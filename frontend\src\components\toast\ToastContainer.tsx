/**
 * Enhanced Toast Container - Enterprise-grade notification positioning and management component
 * Features: Comprehensive toast container with advanced positioning, stacking management, and
 * collision detection, detailed container customization with responsive layouts and dynamic sizing,
 * advanced notification orchestration with queue management and priority handling, ACE Social's
 * notification system integration with seamless container lifecycle management, container interaction
 * features including keyboard navigation and focus management, notification state management with
 * real-time updates and animation coordination, real-time container updates with live positioning
 * changes and dynamic styling, and seamless ACE Social platform integration with advanced container
 * orchestration and comprehensive accessibility compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import * as React from 'react';
import {
  memo,
  forwardRef,
  useImperativeHandle,
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef
} from 'react';
import {
  Box,
  Portal,
  useTheme,
  useMediaQuery,
  Typography,
  alpha,
  IconButton,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Notifications as NotificationsIcon,
  NotificationsOff as NotificationsOffIcon
} from '@mui/icons-material';
import { TransitionGroup } from 'react-transition-group';
import ProductionToast from './ProductionToast';
import { useEnhancedToast } from '../../contexts/EnhancedToastContext';
import { ToastPosition, ToastState } from '../../types/toast';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
} as const;

// Container positions
const CONTAINER_POSITIONS = {
  TOP_LEFT: 'top-left',
  TOP_CENTER: 'top-center',
  TOP_RIGHT: 'top-right',
  BOTTOM_LEFT: 'bottom-left',
  BOTTOM_CENTER: 'bottom-center',
  BOTTOM_RIGHT: 'bottom-right'
} as const;

// Animation types
const ANIMATION_TYPES = {
  SLIDE: 'SLIDE',
  FADE: 'FADE',
  ZOOM: 'ZOOM'
} as const;



/**
 * Enhanced Toast Container Props Interface
 * Comprehensive interface for toast container configuration
 */
interface ToastContainerProps {
  /** Container position for toast placement */
  position?: ToastPosition;
  /** Maximum visible toasts */
  maxVisible?: number;
  /** Maximum queue size */
  maxQueue?: number;
  /** Stack spacing between toasts */
  stackSpacing?: number;
  /** Enable keyboard navigation */
  enableKeyboardNavigation?: boolean;
  /** Enable toast stacking */
  enableStacking?: boolean;
  /** Enable accessibility features */
  enableAccessibility?: boolean;
  /** Enable analytics tracking */
  enableAnalytics?: boolean;
  /** Enable collision detection */
  enableCollisionDetection?: boolean;
  /** Enable responsive positioning */
  enableResponsivePositioning?: boolean;
  /** Animation type for container */
  animationType?: keyof typeof ANIMATION_TYPES;
  /** Enable glass morphism styling */
  enableGlassMorphism?: boolean;
  /** Show queue indicator */
  showQueueIndicator?: boolean;
  /** Enable batch operations */
  enableBatchOperations?: boolean;
  /** Callback for container interaction tracking */
  // eslint-disable-next-line no-unused-vars
  onContainerInteraction?: (interaction: string, data: unknown) => void;
  /** Callback for analytics tracking */
  // eslint-disable-next-line no-unused-vars
  onAnalyticsTrack?: (event: string, data: unknown) => void;
}

/**
 * Container Component Reference Interface
 * Methods exposed through useImperativeHandle for external control
 */
interface ToastContainerRef {
  clearAll: () => void;
  pauseQueue: () => void;
  resumeQueue: () => void;
  getContainerMetrics: () => { visible: number; queued: number; total: number };
  // eslint-disable-next-line no-unused-vars
  updatePosition: (newPosition: ToastPosition) => void;
  exportContainerState: () => unknown;
}

/**
 * Enhanced Toast Container - Comprehensive notification positioning with advanced features
 * Implements detailed container management and enterprise-grade positioning capabilities
 */
const ToastContainer = memo(forwardRef<ToastContainerRef, ToastContainerProps>(({
  position = CONTAINER_POSITIONS.BOTTOM_RIGHT as ToastPosition,
  maxVisible = 5,
  maxQueue = 20,
  stackSpacing = 8,
  enableKeyboardNavigation = true,
  enableStacking = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableCollisionDetection = true,
  enableResponsivePositioning = true,
  animationType = ANIMATION_TYPES.SLIDE,

  enableGlassMorphism = true,
  showQueueIndicator = true,
  enableBatchOperations = true,
  onContainerInteraction,
  onAnalyticsTrack
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const containerRef = useRef<HTMLDivElement>(null);
  const queueRef = useRef<ToastState[]>([]);

  // Enhanced toast context
  const {
    toasts,
    dismissToast,
    options,
  } = useEnhancedToast();

  // Enhanced state management
  const [visibleToasts, setVisibleToasts] = useState<ToastState[]>([]);
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const [isQueueExpanded, setIsQueueExpanded] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [containerAnalytics, setContainerAnalytics] = useState({
    totalRendered: 0,
    averageDisplayTime: 0,
    positionChanges: 0,
    lastActivity: new Date().toISOString()
  });
  const [collisionDetected, setCollisionDetected] = useState(false);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: enableGlassMorphism
      ? `linear-gradient(135deg,
          ${alpha(theme.palette.background.paper, 0.95)} 0%,
          ${alpha(theme.palette.background.default, 0.85)} 100%)`
      : theme.palette.background.paper,
    backdropFilter: enableGlassMorphism ? 'blur(15px)' : 'none',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme, enableGlassMorphism]);

  // Enhanced utility functions
  const handleClearAll = useCallback(() => {
    visibleToasts.forEach(toast => dismissToast(toast.id));

    if (enableAccessibility) {
      announceToScreenReader('All notifications cleared');
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_cleared_all', {
        totalCleared: visibleToasts.length,
        timestamp: new Date().toISOString()
      });
    }
  }, [visibleToasts, dismissToast, enableAccessibility, announceToScreenReader, enableAnalytics, onAnalyticsTrack]);

  const handlePauseQueue = useCallback(() => {
    setIsPaused(true);

    if (enableAccessibility) {
      announceToScreenReader('Notification queue paused');
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_queue_paused', {
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAccessibility, announceToScreenReader, enableAnalytics, onAnalyticsTrack]);

  const handleResumeQueue = useCallback(() => {
    setIsPaused(false);

    if (enableAccessibility) {
      announceToScreenReader('Notification queue resumed');
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_queue_resumed', {
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAccessibility, announceToScreenReader, enableAnalytics, onAnalyticsTrack]);

  const handleUpdatePosition = useCallback((newPosition: ToastPosition) => {
    setContainerAnalytics(prev => ({
      ...prev,
      positionChanges: prev.positionChanges + 1
    }));

    if (enableAccessibility) {
      announceToScreenReader(`Container position changed to ${newPosition}`);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_position_changed', {
        oldPosition: position,
        newPosition,
        timestamp: new Date().toISOString()
      });
    }
  }, [position, enableAccessibility, announceToScreenReader, enableAnalytics, onAnalyticsTrack]);

  const handleExportContainerState = useCallback(() => {
    const state = {
      analytics: containerAnalytics,
      visibleToasts: visibleToasts.length,
      queuedToasts: toasts.length - visibleToasts.length,
      position,
      dimensions: containerDimensions,
      settings: {
        maxVisible,
        maxQueue,
        enableStacking,
        enableKeyboardNavigation,
        enableAccessibility
      },
      exportedAt: new Date().toISOString()
    };

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_state_exported', {
        timestamp: new Date().toISOString()
      });
    }

    return state;
  }, [
    containerAnalytics,
    visibleToasts.length,
    toasts.length,
    position,
    containerDimensions,
    maxVisible,
    maxQueue,
    enableStacking,
    enableKeyboardNavigation,
    enableAccessibility,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    clearAll: handleClearAll,
    pauseQueue: handlePauseQueue,
    resumeQueue: handleResumeQueue,
    getContainerMetrics: () => ({
      visible: visibleToasts.length,
      queued: toasts.length - visibleToasts.length,
      total: toasts.length
    }),
    updatePosition: handleUpdatePosition,
    exportContainerState: handleExportContainerState
  }), [
    handleClearAll,
    handlePauseQueue,
    handleResumeQueue,
    visibleToasts.length,
    toasts.length,
    handleUpdatePosition,
    handleExportContainerState
  ]);

  // Enhanced effects for component lifecycle
  useEffect(() => {
    if (enableAccessibility) {
      announceToScreenReader('Toast notification container initialized');
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_initialized', {
        position,
        maxVisible,
        enableStacking,
        timestamp: new Date().toISOString()
      });
    }
  }, [
    enableAccessibility,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack,
    position,
    maxVisible,
    enableStacking
  ]);

  // Filter and sort toasts for display with enhanced logic
  useEffect(() => {
    if (isPaused) return;

    const filtered = toasts
      .filter(toast => toast.isVisible && !toast.isExiting)
      .sort((a, b) => {
        // Sort by priority first, then by creation time
        const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 };
        const aPriority = priorityOrder[a.config.priority || 'normal'];
        const bPriority = priorityOrder[b.config.priority || 'normal'];

        if (aPriority !== bPriority) {
          return bPriority - aPriority; // Higher priority first
        }

        return b.createdAt.getTime() - a.createdAt.getTime(); // Newer first
      })
      .slice(0, maxVisible);

    setVisibleToasts(filtered);

    // Update analytics
    setContainerAnalytics(prev => ({
      ...prev,
      totalRendered: prev.totalRendered + (filtered.length - visibleToasts.length),
      lastActivity: new Date().toISOString()
    }));

    // Queue management
    queueRef.current = toasts.slice(maxVisible);
  }, [toasts, maxVisible, isPaused, visibleToasts.length]);

  // Enhanced container positioning with collision detection
  const getContainerPosition = useCallback(() => {
    const baseSpacing = isMobile ? 16 : 24;
    const collisionOffset = collisionDetected ? 20 : 0;
    const spacing = baseSpacing + collisionOffset;

    const mobileAdjustment = isMobile ? {
      left: spacing,
      right: spacing,
      width: `calc(100% - ${spacing * 2}px)`
    } : {};

    const responsiveAdjustment = enableResponsivePositioning ? {
      maxWidth: isMobile ? '100%' : '400px',
      minWidth: isMobile ? '280px' : '320px'
    } : {};

    switch (position) {
      case CONTAINER_POSITIONS.TOP_LEFT:
        return {
          top: spacing,
          left: spacing,
          ...mobileAdjustment,
          ...responsiveAdjustment,
        };
      case CONTAINER_POSITIONS.TOP_CENTER:
        return {
          top: spacing,
          left: '50%',
          transform: 'translateX(-50%)',
          ...mobileAdjustment,
          ...responsiveAdjustment,
        };
      case CONTAINER_POSITIONS.TOP_RIGHT:
        return {
          top: spacing,
          right: spacing,
          ...mobileAdjustment,
          ...responsiveAdjustment,
        };
      case CONTAINER_POSITIONS.BOTTOM_LEFT:
        return {
          bottom: spacing,
          left: spacing,
          ...mobileAdjustment,
          ...responsiveAdjustment,
        };
      case CONTAINER_POSITIONS.BOTTOM_CENTER:
        return {
          bottom: spacing,
          left: '50%',
          transform: 'translateX(-50%)',
          ...mobileAdjustment,
          ...responsiveAdjustment,
        };
      case CONTAINER_POSITIONS.BOTTOM_RIGHT:
      default:
        return {
          bottom: spacing,
          right: spacing,
          ...mobileAdjustment,
          ...responsiveAdjustment,
        };
    }
  }, [
    isMobile,
    collisionDetected,
    enableResponsivePositioning,
    position
  ]);

  // Enhanced collision detection
  useEffect(() => {
    if (!enableCollisionDetection) return;

    const checkCollisions = () => {
      const container = containerRef.current;
      if (!container) return;

      const rect = container.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      const hasCollision =
        rect.bottom > viewportHeight - 20 ||
        rect.top < 20 ||
        rect.right > viewportWidth - 20 ||
        rect.left < 20;

      setCollisionDetected(hasCollision);
    };

    checkCollisions();
    window.addEventListener('resize', checkCollisions);
    window.addEventListener('scroll', checkCollisions);

    return () => {
      window.removeEventListener('resize', checkCollisions);
      window.removeEventListener('scroll', checkCollisions);
    };
  }, [enableCollisionDetection, visibleToasts.length]);

  // Enhanced toast action handlers
  const handleToastAction = useCallback((toastId: string, actionIndex?: number) => {
    const toast = toasts.find(t => t.id === toastId);
    if (toast?.config.onAction) {
      toast.config.onAction();
    }

    if (onContainerInteraction) {
      onContainerInteraction('toast_action', {
        toastId,
        actionIndex,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_toast_action', {
        toastId,
        actionIndex,
        timestamp: new Date().toISOString()
      });
    }

    dismissToast(toastId);
  }, [toasts, dismissToast, onContainerInteraction, enableAnalytics, onAnalyticsTrack]);

  const handleToastUndo = useCallback((toastId: string) => {
    const toast = toasts.find(t => t.id === toastId);
    if (toast?.config.onUndo) {
      toast.config.onUndo();
    }

    if (onContainerInteraction) {
      onContainerInteraction('toast_undo', {
        toastId,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_toast_undo', {
        toastId,
        timestamp: new Date().toISOString()
      });
    }

    dismissToast(toastId);
  }, [toasts, dismissToast, onContainerInteraction, enableAnalytics, onAnalyticsTrack]);

  const handleToastInteraction = useCallback((toastId: string, interaction: string) => {
    if (onContainerInteraction) {
      onContainerInteraction('toast_interaction', {
        toastId,
        interaction,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('container_toast_interaction', {
        toastId,
        interaction,
        timestamp: new Date().toISOString()
      });
    }
  }, [onContainerInteraction, enableAnalytics, onAnalyticsTrack]);

  // Enhanced render methods
  const renderQueueIndicator = useCallback(() => {
    if (!showQueueIndicator || toasts.length <= maxVisible) return null;

    const queuedCount = toasts.length - maxVisible;

    return (
      <Box
        sx={{
          position: 'absolute',
          bottom: position.includes('bottom') ? '100%' : 'auto',
          top: position.includes('top') ? '100%' : 'auto',
          left: '50%',
          transform: 'translateX(-50%)',
          mt: position.includes('top') ? 1 : 0,
          mb: position.includes('bottom') ? 1 : 0,
          px: 2,
          py: 0.5,
          pointerEvents: 'auto',
          cursor: 'pointer',
          ...glassMorphismStyles,
        }}
        onClick={() => setIsQueueExpanded(!isQueueExpanded)}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Badge
            badgeContent={queuedCount}
            color="primary"
            sx={{
              '& .MuiBadge-badge': {
                backgroundColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.WHITE
              }
            }}
          >
            <NotificationsIcon fontSize="small" />
          </Badge>
          <Typography
            variant="caption"
            sx={{
              color: theme.palette.text.secondary,
              fontSize: '0.7rem',
            }}
          >
            {queuedCount} more notification{queuedCount !== 1 ? 's' : ''}
          </Typography>
          <IconButton size="small" sx={{ p: 0.5 }}>
            {isQueueExpanded ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
          </IconButton>
        </Box>
      </Box>
    );
  }, [
    showQueueIndicator,
    toasts.length,
    maxVisible,
    position,
    glassMorphismStyles,
    isQueueExpanded,
    theme
  ]);

  const renderContainerControls = useCallback(() => {
    if (!enableBatchOperations || visibleToasts.length === 0) return null;

    return (
      <Box
        sx={{
          position: 'absolute',
          top: position.includes('top') ? '100%' : 'auto',
          bottom: position.includes('bottom') ? '100%' : 'auto',
          right: 0,
          mt: position.includes('bottom') ? 1 : 0,
          mb: position.includes('top') ? 1 : 0,
          display: 'flex',
          gap: 1,
        }}
      >
        <Tooltip title="Clear all notifications">
          <IconButton
            size="small"
            onClick={handleClearAll}
            sx={{
              backgroundColor: alpha(theme.palette.background.paper, 0.9),
              backdropFilter: 'blur(8px)',
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
              },
            }}
          >
            <ClearIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Tooltip title={isPaused ? 'Resume notifications' : 'Pause notifications'}>
          <IconButton
            size="small"
            onClick={isPaused ? handleResumeQueue : handlePauseQueue}
            sx={{
              backgroundColor: alpha(theme.palette.background.paper, 0.9),
              backdropFilter: 'blur(8px)',
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
              },
            }}
          >
            {isPaused ? <NotificationsIcon fontSize="small" /> : <NotificationsOffIcon fontSize="small" />}
          </IconButton>
        </Tooltip>
      </Box>
    );
  }, [
    enableBatchOperations,
    visibleToasts.length,
    position,
    handleClearAll,
    isPaused,
    handleResumeQueue,
    handlePauseQueue,
    theme
  ]);

  // Enhanced stacking calculation with collision detection
  const getStackedToasts = useCallback(() => {
    if (!enableStacking) {
      return visibleToasts.map((toast, index) => ({
        toast,
        stackIndex: 0,
        offset: index * (stackSpacing + 80), // Approximate toast height + spacing
      }));
    }

    return visibleToasts.map((toast, index) => ({
      toast,
      stackIndex: index,
      offset: 0, // Stacking is handled in the toast component
    }));
  }, [enableStacking, visibleToasts, stackSpacing]);

  // Handle container resize for responsive positioning
  useEffect(() => {
    const updateDimensions = () => {
      setContainerDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);



  // Enhanced keyboard navigation
  const handleContainerKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (!enableKeyboardNavigation || visibleToasts.length === 0) return;

    switch (event.key) {
      case 'Escape':
        event.preventDefault();
        if (enableBatchOperations) {
          handleClearAll();
        } else {
          visibleToasts.forEach(toast => dismissToast(toast.id));
        }
        break;
      case 'ArrowUp':
      case 'ArrowDown':
        event.preventDefault();
        // Focus management could be implemented here
        break;
      case 'c':
      case 'C':
        if (event.ctrlKey && enableBatchOperations) {
          event.preventDefault();
          handleClearAll();
        }
        break;
      case 'p':
      case 'P':
        if (event.ctrlKey) {
          event.preventDefault();
          if (isPaused) {
            handleResumeQueue();
          } else {
            handlePauseQueue();
          }
        }
        break;
    }
  }, [
    enableKeyboardNavigation,
    visibleToasts,
    enableBatchOperations,
    handleClearAll,
    dismissToast,
    isPaused,
    handleResumeQueue,
    handlePauseQueue
  ]);

  // Don't render if no toasts and no queue indicator needed
  if (visibleToasts.length === 0 && (!showQueueIndicator || toasts.length === 0)) {
    return null;
  }

  const stackedToasts = getStackedToasts();

  return (
    <Portal>
      <Box
        ref={containerRef}
        role="region"
        aria-label="Notifications"
        aria-live="polite"
        aria-atomic="false"
        tabIndex={enableKeyboardNavigation ? 0 : -1}
        onKeyDown={handleContainerKeyDown}
        sx={{
          position: 'fixed',
          zIndex: theme.zIndex.snackbar,
          pointerEvents: 'none',
          outline: 'none',
          '&:focus-visible': {
            outline: `2px solid ${ACE_COLORS.PURPLE}`,
            outlineOffset: '2px',
          },
          ...getContainerPosition(),
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: position.includes('top') ? 'column' : 'column-reverse',
            gap: enableStacking ? 0 : `${stackSpacing}px`,
            alignItems: position.includes('center') ? 'center' : 'stretch',
            maxHeight: '100vh',
            overflow: 'hidden',
          }}
        >
          <TransitionGroup component={null}>
            {stackedToasts.map(({ toast, stackIndex, offset }) => (
              <Box
                key={toast.id}
                sx={{
                  pointerEvents: 'auto',
                  marginBottom: enableStacking ? 0 : undefined,
                  transform: !enableStacking ? `translateY(${offset}px)` : undefined,
                  transition: theme.transitions.create('transform', {
                    duration: options.animationDuration || 300,
                    easing: theme.transitions.easing.easeInOut,
                  }),
                }}
              >
                <ProductionToast
                  toast={toast}
                  onDismiss={dismissToast}
                  onAction={handleToastAction}
                  onUndo={handleToastUndo}
                  onInteraction={handleToastInteraction}
                  {...(onAnalyticsTrack && { onAnalyticsTrack })}
                  position={position}
                  stackIndex={stackIndex}
                  animationDuration={options.animationDuration || 300}
                  enableKeyboardNavigation={enableKeyboardNavigation}
                  enableAnalytics={enableAnalytics}
                  animationType={animationType}
                  enableGlassMorphism={enableGlassMorphism}
                />
              </Box>
            ))}
          </TransitionGroup>
        </Box>

        {/* Enhanced queue indicator */}
        {renderQueueIndicator()}

        {/* Container controls */}
        {renderContainerControls()}

        {/* Collision detection indicator */}
        {collisionDetected && enableCollisionDetection && (
          <Box
            sx={{
              position: 'absolute',
              top: -10,
              right: -10,
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: ACE_COLORS.YELLOW,
              boxShadow: `0 0 8px ${alpha(ACE_COLORS.YELLOW, 0.5)}`,
              animation: 'pulse 2s infinite',
              '@keyframes pulse': {
                '0%': { opacity: 1 },
                '50%': { opacity: 0.5 },
                '100%': { opacity: 1 },
              },
            }}
          />
        )}
      </Box>
    </Portal>
  );
}));

ToastContainer.displayName = 'ToastContainer';

export default ToastContainer;

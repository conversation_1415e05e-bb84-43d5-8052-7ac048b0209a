/**
 * Enhanced Charts Fallback Component - Enterprise-grade error fallback with intelligent recovery
 * Features: Subscription-based feature gating, comprehensive error categorization, intelligent retry mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced error handling capabilities and interactive recovery exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Alert,
  AlertTitle,
  Button,
  Card,
  CardContent,
  Chip,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  alpha,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  BarChart as ChartIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Memory as MemoryIcon,
  NetworkCheck as NetworkIcon,
  BugReport as BugIcon,
  Security as SecurityIcon,
  Upgrade as UpgradeIcon,
  Analytics as AnalyticsIcon,
  Help as HelpIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
// @ts-expect-error - AuthContext is a JSX file without TypeScript declarations
import { useAuth } from '../../contexts/AuthContext';
// @ts-expect-error - SubscriptionContext is a JSX file without TypeScript declarations
import { useSubscription } from '../../contexts/SubscriptionContext';
// @ts-expect-error - useNotification is a JS file without TypeScript declarations
import { useNotification } from '../../hooks/useNotification';
import { useAccessibility } from '../../hooks/useAccessibility';

// Enhanced component imports
// @ts-expect-error - ErrorBoundary is a JSX file without TypeScript declarations
import ErrorBoundary from '../common/ErrorBoundary';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Error categories for intelligent handling
const ERROR_CATEGORIES = {
  MEMORY: 'memory',
  NETWORK: 'network',
  RENDERING: 'rendering',
  DATA: 'data',
  PERMISSION: 'permission',
  SUBSCRIPTION: 'subscription',
  UNKNOWN: 'unknown'
};

// Chart types for specific fallback messages
const CHART_TYPES = {
  BAR: 'bar',
  LINE: 'line',
  PIE: 'pie',
  AREA: 'area',
  SCATTER: 'scatter',
  HEATMAP: 'heatmap',
  TREEMAP: 'treemap',
  GAUGE: 'gauge'
};

// Recovery strategies
const RECOVERY_STRATEGIES = {
  RETRY: 'retry',
  REFRESH: 'refresh',
  FALLBACK_DATA: 'fallback_data',
  UPGRADE: 'upgrade',
  CONTACT_SUPPORT: 'contact_support'
};

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  RETRY_DELAY: 1000,
  MAX_RETRIES: 3,
  MEMORY_WARNING: 50 * 1024 * 1024, // 50MB
  MEMORY_CRITICAL: 100 * 1024 * 1024, // 100MB
  NETWORK_TIMEOUT: 10000 // 10 seconds
};

// ===========================
// TYPE DEFINITIONS (JSDoc)
// ===========================

/**
 * @typedef {Object} ErrorInfo
 * @property {string} message - Error message
 * @property {string} stack - Error stack trace
 * @property {string} category - Error category
 * @property {number} timestamp - Error timestamp
 * @property {string} [code] - Error code
 * @property {Object} [metadata] - Additional error metadata
 */

/**
 * @typedef {Object} ChartConfig
 * @property {string} type - Chart type
 * @property {string} title - Chart title
 * @property {Object} data - Chart data
 * @property {Object} [options] - Chart options
 * @property {boolean} [isRequired] - Whether chart is required
 */

/**
 * @typedef {Object} RecoveryAction
 * @property {string} id - Action ID
 * @property {string} label - Action label
 * @property {string} description - Action description
 * @property {string} strategy - Recovery strategy
 * @property {Function} handler - Action handler function
 * @property {boolean} [requiresSubscription] - Whether action requires subscription
 * @property {string} [subscriptionTier] - Required subscription tier
 */

/**
 * @typedef {Object} SubscriptionFeatures
 * @property {string} planId - Subscription plan ID
 * @property {string} planName - Subscription plan name
 * @property {boolean} hasAdvancedCharts - Whether plan includes advanced charts
 * @property {boolean} hasErrorAnalytics - Whether plan includes error analytics
 * @property {boolean} hasCustomFallbacks - Whether plan includes custom fallbacks
 * @property {boolean} hasPrioritySupport - Whether plan includes priority support
 * @property {number} maxRetries - Maximum retry attempts
 * @property {string} trackingLevel - Error tracking level
 */

/**
 * @typedef {Object} ComponentState
 * @property {boolean} loading - Loading state
 * @property {boolean} retrying - Retry state
 * @property {boolean} showDetails - Show error details
 * @property {boolean} showRecovery - Show recovery options
 * @property {boolean} showUpgradeDialog - Show upgrade dialog
 * @property {number} retryCount - Current retry count
 * @property {number} lastRetryTime - Last retry timestamp
 * @property {Array<string>} recoveryHistory - Recovery action history
 * @property {Object} performanceMetrics - Performance metrics
 * @property {string} [lastError] - Last error message
 */

/**
 * @typedef {Object} NotificationState
 * @property {boolean} open - Notification open state
 * @property {string} message - Notification message
 * @property {'success'|'error'|'warning'|'info'} severity - Notification severity
 * @property {Object} [action] - Notification action
 */

/**
 * @typedef {Object} ChartsFallbackProps
 * @property {Function} [onRetry] - Retry callback function
 * @property {ErrorInfo} [error] - Error information
 * @property {ChartConfig} [chartConfig] - Chart configuration
 * @property {string} [chartType] - Chart type
 * @property {boolean} [showRecoveryOptions] - Show recovery options
 * @property {boolean} [enableAnalytics] - Enable error analytics
 * @property {Function} [onUpgrade] - Upgrade callback
 * @property {Function} [onContactSupport] - Contact support callback
 * @property {string} [ariaLabel] - ARIA label
 * @property {string} [ariaDescription] - ARIA description
 * @property {string} [testId] - Test ID
 * @property {string} [className] - CSS class name
 * @property {Object} [style] - Inline styles
 */

/**
 * @typedef {Object} ChartsFallbackHandle
 * @property {Function} retry - Retry function
 * @property {Function} reset - Reset function
 * @property {Function} getErrorInfo - Get error information
 * @property {Function} getPerformanceMetrics - Get performance metrics
 * @property {Function} focus - Focus function
 * @property {Function} announce - Screen reader announcement
 */

/**
 * Enhanced ChartsFallback Component - Enterprise-grade error fallback with intelligent recovery
 * Features: Subscription-based feature gating, comprehensive error categorization, intelligent retry mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced error handling capabilities and interactive recovery exploration
 *
 * @component
 * @param {ChartsFallbackProps} props - Component props
 * @returns {React.Component} Enhanced charts fallback component
 */
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
const ChartsFallback = memo(forwardRef((props, ref) => {
  const {
    onRetry,
    error,
    chartConfig,
    chartType = CHART_TYPES.BAR,
    showRecoveryOptions = true,
    enableAnalytics = true,
    onUpgrade,
    onContactSupport,
    ariaLabel,
    ariaDescription,
    testId = 'charts-fallback',
    className = '',
    style = {}
  } = props;

  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { hasFeature } = useAuth();
  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  // @ts-expect-error - Intentionally unused for future responsive features

  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced state management with comprehensive error handling
  const [state, setState] = useState(/** @type {ComponentState} */ ({
    loading: subscriptionLoading,
    retrying: false,
    showDetails: false,
    showRecovery: false,
    showUpgradeDialog: false,
    retryCount: 0,
    lastRetryTime: 0,
    recoveryHistory: [],
    performanceMetrics: {
      errorCount: 0,
      lastErrorTime: null,
      recoveryAttempts: 0,
      successfulRecoveries: 0
    },
    lastError: null
  }));

  const [notification, setNotification] = useState(/** @type {NotificationState} */ ({
    open: false,
    message: '',
    severity: 'info'
  }));

  // Enhanced refs for comprehensive tracking
  const containerRef = useRef(null);
  const retryButtonRef = useRef(null);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   * @returns {SubscriptionFeatures} Subscription features object
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Simple plan-based features
    const features = {
      creator: {
        hasAdvancedCharts: false,
        hasErrorAnalytics: false,
        hasCustomFallbacks: false,
        hasPrioritySupport: false,
        maxRetries: 3,
        trackingLevel: 'basic'
      },
      accelerator: {
        hasAdvancedCharts: true,
        hasErrorAnalytics: true,
        hasCustomFallbacks: false,
        hasPrioritySupport: false,
        maxRetries: 5,
        trackingLevel: 'advanced'
      },
      dominator: {
        hasAdvancedCharts: true,
        hasErrorAnalytics: true,
        hasCustomFallbacks: true,
        hasPrioritySupport: true,
        maxRetries: 10,
        trackingLevel: 'ai-powered'
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      ...currentFeatures
    };
  }, [subscription?.plan_id, subscription?.plan_name]);

  /**
   * Categorize error based on message and context - Production Ready
   * @param {ErrorInfo} errorInfo - Error information
   * @returns {string} Error category
   */
  const categorizeError = useCallback((errorInfo) => {
    if (!errorInfo || !errorInfo.message) return ERROR_CATEGORIES.UNKNOWN;

    const message = errorInfo.message.toLowerCase();

    if (message.includes('memory') || message.includes('heap')) {
      return ERROR_CATEGORIES.MEMORY;
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return ERROR_CATEGORIES.NETWORK;
    }
    if (message.includes('render') || message.includes('canvas') || message.includes('webgl')) {
      return ERROR_CATEGORIES.RENDERING;
    }
    if (message.includes('data') || message.includes('parse') || message.includes('format')) {
      return ERROR_CATEGORIES.DATA;
    }
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return ERROR_CATEGORIES.PERMISSION;
    }
    if (message.includes('subscription') || message.includes('plan') || message.includes('upgrade')) {
      return ERROR_CATEGORIES.SUBSCRIPTION;
    }

    return ERROR_CATEGORIES.UNKNOWN;
  }, []);

  /**
   * Get error-specific icon - Production Ready
   * @param {string} category - Error category
   * @returns {React.Component} Error icon component
   */
  const getErrorIcon = useCallback((category) => {
    const iconProps = { sx: { fontSize: 64, mb: 2 } };

    switch (category) {
      case ERROR_CATEGORIES.MEMORY:
        return <MemoryIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.YELLOW }} />;
      case ERROR_CATEGORIES.NETWORK:
        return <NetworkIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.PURPLE }} />;
      case ERROR_CATEGORIES.RENDERING:
        return <BugIcon {...iconProps} sx={{ ...iconProps.sx, color: 'error.main' }} />;
      case ERROR_CATEGORIES.PERMISSION:
        return <SecurityIcon {...iconProps} sx={{ ...iconProps.sx, color: 'warning.main' }} />;
      case ERROR_CATEGORIES.SUBSCRIPTION:
        return <UpgradeIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.PURPLE }} />;
      default:
        return <ChartIcon {...iconProps} sx={{ ...iconProps.sx, color: 'text.secondary' }} />;
    }
  }, []);

  /**
   * Get category-specific error message - Production Ready
   * @param {string} category - Error category
   * @param {string} chartType - Chart type
   * @returns {Object} Error message object with title and description
   */
  const getErrorMessage = useCallback((category, chartType) => {
    const chartName = chartType ? `${chartType} chart` : 'chart';

    const messages = {
      [ERROR_CATEGORIES.MEMORY]: {
        title: 'Memory Limit Reached',
        description: `The ${chartName} couldn't be loaded due to insufficient memory. Try closing other browser tabs or refreshing the page.`
      },
      [ERROR_CATEGORIES.NETWORK]: {
        title: 'Network Connection Issue',
        description: `Unable to load ${chartName} data due to network connectivity issues. Please check your connection and try again.`
      },
      [ERROR_CATEGORIES.RENDERING]: {
        title: 'Rendering Error',
        description: `The ${chartName} encountered a rendering error. This may be due to browser compatibility or data format issues.`
      },
      [ERROR_CATEGORIES.DATA]: {
        title: 'Data Processing Error',
        description: `Unable to process the data for the ${chartName}. The data format may be invalid or corrupted.`
      },
      [ERROR_CATEGORIES.PERMISSION]: {
        title: 'Access Restricted',
        description: `You don't have permission to view this ${chartName}. Contact your administrator for access.`
      },
      [ERROR_CATEGORIES.SUBSCRIPTION]: {
        title: 'Subscription Required',
        description: `Advanced ${chartName} features require a higher subscription plan. Upgrade to access this functionality.`
      },
      [ERROR_CATEGORIES.UNKNOWN]: {
        title: 'Charts Unavailable',
        description: `The ${chartName} component couldn't be loaded. This helps keep the application running smoothly.`
      }
    };

    return messages[category] || messages[ERROR_CATEGORIES.UNKNOWN];
  }, []);

  /**
   * Enhanced retry handler with intelligent backoff - Production Ready
   * @returns {Promise<void>}
   */
  const handleRetry = useCallback(async () => {
    if (state.retryCount >= subscriptionFeatures.maxRetries) {
      const errorMessage = `Maximum retry attempts (${subscriptionFeatures.maxRetries}) reached`;
      setState(prev => ({ ...prev, lastError: errorMessage }));
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
      return;
    }

    try {
      setState(prev => ({
        ...prev,
        retrying: true,
        retryCount: prev.retryCount + 1,
        lastRetryTime: Date.now(),
        performanceMetrics: {
          ...prev.performanceMetrics,
          recoveryAttempts: prev.performanceMetrics.recoveryAttempts + 1
        }
      }));

      announceToScreenReader('Retrying chart loading...');

      // Intelligent retry delay based on attempt count
      const delay = PERFORMANCE_THRESHOLDS.RETRY_DELAY * Math.pow(2, state.retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));

      if (onRetry) {
        await onRetry();
      } else {
        // Fallback to page reload
        window.location.reload();
      }

      setState(prev => ({
        ...prev,
        performanceMetrics: {
          ...prev.performanceMetrics,
          successfulRecoveries: prev.performanceMetrics.successfulRecoveries + 1
        }
      }));

      showSuccessNotification('Chart loading retry initiated');
      announceToScreenReader('Chart loading retry successful');

      // Track analytics
      if (enableAnalytics && window.analytics) {
        window.analytics.track('Charts Fallback Retry', {
          retryCount: state.retryCount + 1,
          errorCategory: categorizeError(error),
          chartType,
          subscriptionPlan: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }

    } catch (retryError) {
      console.error('Retry failed:', retryError);
      const errorMessage = 'Retry failed. Please try again later.';
      setState(prev => ({ ...prev, lastError: errorMessage }));
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
    } finally {
      setState(prev => ({ ...prev, retrying: false }));
    }
  }, [
    state.retryCount,
    subscriptionFeatures.maxRetries,
    onRetry,
    enableAnalytics,
    categorizeError,
    error,
    chartType,
    subscriptionFeatures.planId,
    showErrorNotification,
    showSuccessNotification,
    announceToScreenReader
  ]);

  /**
   * Enhanced upgrade prompt handler - Production Ready
   */
  const handleUpgradePrompt = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: true }));

    // Track upgrade prompt analytics
    if (enableAnalytics && window.analytics) {
      window.analytics.track('Charts Fallback Upgrade Prompt Shown', {
        currentPlan: subscriptionFeatures.planId,
        errorCategory: categorizeError(error),
        chartType,
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAnalytics, subscriptionFeatures.planId, categorizeError, error, chartType]);

  /**
   * Get recovery actions based on error category and subscription - Production Ready
   * @param {string} category - Error category
   * @returns {Array<RecoveryAction>} Array of recovery actions
   */
  const getRecoveryActions = useCallback((category) => {
    const baseActions = [
      {
        id: 'retry',
        label: 'Retry Loading',
        description: 'Attempt to load the chart again',
        strategy: RECOVERY_STRATEGIES.RETRY,
        handler: handleRetry,
        requiresSubscription: false
      },
      {
        id: 'refresh',
        label: 'Refresh Page',
        description: 'Reload the entire page to clear any issues',
        strategy: RECOVERY_STRATEGIES.REFRESH,
        handler: () => window.location.reload(),
        requiresSubscription: false
      }
    ];

    // Add category-specific actions
    if (category === ERROR_CATEGORIES.MEMORY) {
      baseActions.push({
        id: 'clear_cache',
        label: 'Clear Browser Cache',
        description: 'Clear browser cache to free up memory',
        strategy: RECOVERY_STRATEGIES.FALLBACK_DATA,
        handler: () => {
          if ('caches' in window) {
            caches.keys().then(names => {
              names.forEach(name => caches.delete(name));
            });
          }
          showSuccessNotification('Browser cache cleared');
        },
        requiresSubscription: false
      });
    }

    if (category === ERROR_CATEGORIES.SUBSCRIPTION) {
      baseActions.push({
        id: 'upgrade',
        label: 'Upgrade Plan',
        description: 'Upgrade to access advanced chart features',
        strategy: RECOVERY_STRATEGIES.UPGRADE,
        handler: handleUpgradePrompt,
        requiresSubscription: false
      });
    }

    // Add support action for higher tiers
    if (subscriptionFeatures.hasPrioritySupport) {
      baseActions.push({
        id: 'contact_support',
        label: 'Contact Support',
        description: 'Get priority support for this issue',
        strategy: RECOVERY_STRATEGIES.CONTACT_SUPPORT,
        handler: () => {
          if (onContactSupport) {
            onContactSupport();
          } else {
            window.open('mailto:<EMAIL>?subject=Chart Loading Issue', '_blank');
          }
        },
        requiresSubscription: true,
        subscriptionTier: 'dominator'
      });
    }

    return baseActions;
  }, [handleRetry, handleUpgradePrompt, subscriptionFeatures.hasPrioritySupport, onContactSupport, showSuccessNotification]);

  /**
   * Enhanced imperative handle for parent component access - Production Ready
   */
  useImperativeHandle(ref, () => ({
    retry: handleRetry,
    reset: () => {
      setState(prev => ({
        ...prev,
        retryCount: 0,
        lastRetryTime: 0,
        recoveryHistory: [],
        lastError: null,
        showDetails: false,
        showRecovery: false
      }));
      announceToScreenReader('Charts fallback reset');
    },
    getErrorInfo: () => ({
      error,
      category: categorizeError(error),
      retryCount: state.retryCount,
      lastRetryTime: state.lastRetryTime
    }),
    getPerformanceMetrics: () => state.performanceMetrics,
    focus: () => {
      if (containerRef.current) {
        setFocusToElement(containerRef.current);
      }
    },
    announce: (message) => announceToScreenReader(message)
  }), [
    handleRetry,
    error,
    categorizeError,
    state.retryCount,
    state.lastRetryTime,
    state.performanceMetrics,
    setFocusToElement,
    announceToScreenReader
  ]);

  // Determine error category and message
  const errorCategory = categorizeError(error);
  const errorMessage = getErrorMessage(errorCategory, chartType);
  const recoveryActions = getRecoveryActions(errorCategory);
  const errorIcon = getErrorIcon(errorCategory);

  // Render the component
  return (
    <ErrorBoundary>
      <Card
        ref={containerRef}
        sx={{
          minHeight: 300,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          backgroundColor: theme.palette.background.default,
          position: 'relative',
          overflow: 'hidden'
        }}
        role="alert"
        aria-label={ariaLabel || `Chart loading error: ${errorMessage.title}`}
        aria-description={ariaDescription || errorMessage.description}
        data-testid={testId}
        className={className}
        style={style}
      >
        {/* Loading overlay */}
        {state.retrying && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1
            }}
          >
            <Box sx={{ textAlign: 'center' }}>
              <CircularProgress sx={{ color: ACE_COLORS.PURPLE, mb: 2 }} />
              <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
                Retrying... (Attempt {state.retryCount + 1}/{subscriptionFeatures.maxRetries})
              </Typography>
            </Box>
          </Box>
        )}

        <CardContent sx={{ textAlign: 'center', maxWidth: 500, width: '100%' }}>
          {/* Error Icon */}
          {errorIcon}

          {/* Error Title */}
          <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
            {errorMessage.title}
          </Typography>

          {/* Subscription Badge */}
          {errorCategory === ERROR_CATEGORIES.SUBSCRIPTION && (
            <Chip
              label={`${subscriptionFeatures.planName} Plan`}
              size="small"
              sx={{
                mb: 2,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                fontWeight: 600
              }}
            />
          )}

          {/* Error Description */}
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
            {errorMessage.description}
          </Typography>

          {/* Error Details */}
          {error && (
            <Alert
              severity={errorCategory === ERROR_CATEGORIES.SUBSCRIPTION ? 'warning' : 'info'}
              sx={{ mb: 3, textAlign: 'left' }}
              action={
                <IconButton
                  size="small"
                  onClick={() => setState(prev => ({ ...prev, showDetails: !prev.showDetails }))}
                  sx={{ color: 'inherit' }}
                >
                  {state.showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              }
            >
              <AlertTitle>Error Details</AlertTitle>
              <Typography variant="caption">
                {error.message || 'Resource loading failed'}
              </Typography>

              <Collapse in={state.showDetails}>
                <Box sx={{ mt: 1, pt: 1, borderTop: 1, borderColor: 'divider' }}>
                  <Typography variant="caption" component="div">
                    <strong>Category:</strong> {errorCategory}
                  </Typography>
                  <Typography variant="caption" component="div">
                    <strong>Chart Type:</strong> {chartType}
                  </Typography>
                  <Typography variant="caption" component="div">
                    <strong>Retry Count:</strong> {state.retryCount}/{subscriptionFeatures.maxRetries}
                  </Typography>
                  {subscriptionFeatures.hasErrorAnalytics && (
                    <Typography variant="caption" component="div">
                      <strong>Error ID:</strong> {error.code || 'N/A'}
                    </Typography>
                  )}
                </Box>
              </Collapse>
            </Alert>
          )}

          {/* Primary Actions */}
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', mb: 2 }}>
            <Button
              ref={retryButtonRef}
              variant="contained"
              startIcon={state.retrying ? <CircularProgress size={16} /> : <RefreshIcon />}
              onClick={handleRetry}
              disabled={state.retrying || state.retryCount >= subscriptionFeatures.maxRetries}
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.WHITE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                },
                '&:disabled': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.3)
                }
              }}
            >
              {state.retrying ? 'Retrying...' : 'Retry Loading'}
            </Button>

            {showRecoveryOptions && (
              <Button
                variant="outlined"
                startIcon={<HelpIcon />}
                onClick={() => setState(prev => ({ ...prev, showRecovery: !prev.showRecovery }))}
                sx={{
                  borderColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    borderColor: ACE_COLORS.PURPLE,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                  }
                }}
              >
                Recovery Options
              </Button>
            )}
          </Box>

          {/* Recovery Options */}
          <Collapse in={state.showRecovery}>
            <Card sx={{ backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05), border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ mb: 2, color: ACE_COLORS.DARK, fontWeight: 600 }}>
                  Recovery Actions
                </Typography>

                <List dense>
                  {recoveryActions.map((action) => (
                    <ListItem
                      key={action.id}
                      button
                      onClick={action.handler}
                      disabled={action.requiresSubscription && !subscriptionFeatures.hasPrioritySupport}
                      sx={{
                        borderRadius: 1,
                        mb: 1,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                        }
                      }}
                    >
                      <ListItemIcon>
                        {action.strategy === RECOVERY_STRATEGIES.RETRY && <RefreshIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.REFRESH && <RefreshIcon sx={{ color: ACE_COLORS.YELLOW }} />}
                        {action.strategy === RECOVERY_STRATEGIES.UPGRADE && <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.CONTACT_SUPPORT && <HelpIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.FALLBACK_DATA && <AnalyticsIcon sx={{ color: ACE_COLORS.YELLOW }} />}
                      </ListItemIcon>
                      <ListItemText
                        primary={action.label}
                        secondary={action.description}
                        primaryTypographyProps={{
                          variant: 'body2',
                          fontWeight: 500,
                          color: ACE_COLORS.DARK
                        }}
                        secondaryTypographyProps={{
                          variant: 'caption',
                          color: 'text.secondary'
                        }}
                      />
                      {action.requiresSubscription && !subscriptionFeatures.hasPrioritySupport && (
                        <Chip
                          label="Upgrade Required"
                          size="small"
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2),
                            color: ACE_COLORS.DARK,
                            fontSize: '0.7rem'
                          }}
                        />
                      )}
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Collapse>

          {/* Performance Tips */}
          <Box sx={{ mt: 3, p: 2, backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1), borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontWeight: 500 }}>
              💡 Performance Tips:
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
              {errorCategory === ERROR_CATEGORIES.MEMORY && 'Close other browser tabs to free up memory'}
              {errorCategory === ERROR_CATEGORIES.NETWORK && 'Check your internet connection and try again'}
              {errorCategory === ERROR_CATEGORIES.RENDERING && 'Try using a different browser or update your current one'}
              {errorCategory === ERROR_CATEGORIES.SUBSCRIPTION && 'Upgrade your plan to access advanced chart features'}
              {errorCategory === ERROR_CATEGORIES.UNKNOWN && 'Refresh the page or contact support if the issue persists'}
            </Typography>
          </Box>
        </CardContent>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
            Upgrade Your Plan
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Unlock advanced chart features and enhanced error recovery with a higher tier plan:
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Accelerator Plan Features:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ ml: 2 }}>
                <li>Advanced chart types and customization</li>
                <li>Enhanced error analytics and reporting</li>
                <li>Increased retry attempts (5 vs 3)</li>
                <li>Priority chart loading</li>
              </Typography>
            </Box>

            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Dominator Plan Features:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ ml: 2 }}>
                <li>All Accelerator features</li>
                <li>Custom fallback configurations</li>
                <li>Priority support and assistance</li>
                <li>Maximum retry attempts (10)</li>
                <li>AI-powered error resolution</li>
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
              color="inherit"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setState(prev => ({ ...prev, showUpgradeDialog: false }));
                if (onUpgrade) onUpgrade();
              }}
              variant="contained"
              sx={{ backgroundColor: ACE_COLORS.PURPLE }}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Card>
    </ErrorBoundary>
  );
}));
/* eslint-enable no-unused-vars, @typescript-eslint/no-unused-vars */

// Enhanced PropTypes validation for comprehensive type checking
ChartsFallback.propTypes = {
  /** Retry callback function */
  onRetry: PropTypes.func,

  /** Error information object */
  error: PropTypes.shape({
    message: PropTypes.string,
    stack: PropTypes.string,
    code: PropTypes.string,
    metadata: PropTypes.object
  }),

  /** Chart configuration object */
  chartConfig: PropTypes.shape({
    type: PropTypes.string,
    title: PropTypes.string,
    data: PropTypes.object,
    options: PropTypes.object,
    isRequired: PropTypes.bool
  }),

  /** Chart type for specific fallback messages */
  chartType: PropTypes.oneOf([
    'bar', 'line', 'pie', 'area', 'scatter', 'heatmap', 'treemap', 'gauge'
  ]),

  /** Whether to show recovery options */
  showRecoveryOptions: PropTypes.bool,

  /** Whether to enable error analytics */
  enableAnalytics: PropTypes.bool,

  /** Upgrade callback function */
  onUpgrade: PropTypes.func,

  /** Contact support callback function */
  onContactSupport: PropTypes.func,

  /** ARIA label for accessibility */
  ariaLabel: PropTypes.string,

  /** ARIA description for accessibility */
  ariaDescription: PropTypes.string,

  /** Test ID for testing */
  testId: PropTypes.string,

  /** CSS class name */
  className: PropTypes.string,

  /** Inline styles */
  style: PropTypes.object
};

// Default props for comprehensive fallback behavior
ChartsFallback.defaultProps = {
  onRetry: null,
  error: null,
  chartConfig: null,
  chartType: 'bar',
  showRecoveryOptions: true,
  enableAnalytics: true,
  onUpgrade: null,
  onContactSupport: null,
  ariaLabel: '',
  ariaDescription: '',
  testId: 'charts-fallback',
  className: '',
  style: {}
};

// Enhanced display name for debugging and development tools
ChartsFallback.displayName = 'ChartsFallback';

export default ChartsFallback;

/**
 * Enhanced ACE Social Transaction Management - Enterprise-grade comprehensive transaction management component
 * Features: Comprehensive transaction management with real-time transaction processing, automated transaction
 * categorization, and transaction dispute resolution for ACE Social financial management, detailed transaction
 * management dashboard with live transaction monitoring and transaction status tracking, advanced management
 * features with interactive transaction tables and transaction drill-down capabilities, ACE Social's financial
 * system integration with seamless data aggregation from payment monitoring and billing systems, management
 * interaction features including real-time transaction alerts and transaction filtering capabilities,
 * management state management with transaction data caching and real-time transaction updates, and real-time
 * transaction monitoring with live transaction processing status and automated transaction anomaly detection
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Grid,
  Stack,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as ExportIcon,
  Visibility as ViewIcon,
  Refresh as RefreshIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Analytics as AnalyticsIcon,
  MonetizationOn as MoneyIcon,
  Receipt as ReceiptIcon,
  CreditCard as CreditCardIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

// API
import api from '../../api';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Transaction management constants
const TRANSACTION_STATUSES = {
  COMPLETED: 'completed',
  PENDING: 'pending',
  FAILED: 'failed',
  REFUNDED: 'refunded',
  DISPUTED: 'disputed',
  PROCESSING: 'processing'
};

const TRANSACTION_TYPES = {
  SUBSCRIPTION: 'subscription',
  ADDON: 'addon',
  APPSUMO: 'appsumo',
  REFUND: 'refund',
  UPGRADE: 'upgrade',
  DOWNGRADE: 'downgrade'
};

const MANAGEMENT_VIEWS = {
  TABLE: 'table',
  ANALYTICS: 'analytics',
  DISPUTES: 'disputes',
  REFUNDS: 'refunds'
};

const SORT_OPTIONS = {
  DATE_DESC: 'date_desc',
  DATE_ASC: 'date_asc',
  AMOUNT_DESC: 'amount_desc',
  AMOUNT_ASC: 'amount_asc',
  STATUS: 'status',
  TYPE: 'type'
};

// Transaction management analytics events
const MANAGEMENT_ANALYTICS_EVENTS = {
  TRANSACTION_VIEWED: 'transaction_viewed',
  TRANSACTION_FILTERED: 'transaction_filtered',
  TRANSACTION_EXPORTED: 'transaction_exported',
  TRANSACTION_SEARCHED: 'transaction_searched',
  BULK_ACTION_PERFORMED: 'transaction_bulk_action',
  DISPUTE_INITIATED: 'transaction_dispute_initiated'
};

/**
 * Enhanced Transaction Management - Comprehensive transaction management with advanced features
 * Implements detailed transaction management and enterprise-grade transaction capabilities
 */

const EnhancedTransactionManagement = memo(forwardRef(({
  data = {},
  loading = false,
  error,
  className,
  enableAdvancedFeatures = true,
  enableRealTimeUpdates = true,
  enableBulkOperations = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableExportOptions = true,
  enableDisputeManagement = true,
  defaultView = MANAGEMENT_VIEWS.TABLE,
  defaultSortOption = SORT_OPTIONS.DATE_DESC,
  autoRefreshInterval = 60000, // 1 minute
  maxDisplayTransactions = 1000,
  onTransactionView,
  onTransactionEdit,
  onTransactionRefund,
  onDisputeInitiate,
  onBulkAction,
  onDataExport,
  onAnalyticsTrack,
  onRefresh,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const managementRef = useRef(null);
  const tableRef = useRef(null);
  const autoRefreshTimeoutRef = useRef(null);

  // Core state management
  const [managementLoading, setManagementLoading] = useState(false);
  const [managementError, setManagementError] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [currentView, setCurrentView] = useState(defaultView);
  const [sortOption, setSortOption] = useState(defaultSortOption);

  // Enhanced state management
  const [selectedTransactions, setSelectedTransactions] = useState(new Set());
  const [managementAnalytics, setManagementAnalytics] = useState({
    transactionsViewed: 0,
    transactionsFiltered: 0,
    transactionsExported: 0,
    bulkActionsPerformed: 0,
    disputesInitiated: 0
  });

  // Filters
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    transaction_type: '',
    date_start: '',
    date_end: '',
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    refreshTransactions: () => handleRefresh(),
    exportData: () => handleExport(),
    clearSelection: () => setSelectedTransactions(new Set()),
    focusManagement: () => managementRef.current?.focus(),

    // Navigation methods
    changeView: (view) => handleViewChange(view),
    changePage: (newPage) => setPage(newPage),
    changeRowsPerPage: (rows) => handleRowsPerPageChange(rows),

    // Data methods
    getTransactions: () => transactions,
    getSelectedTransactions: () => Array.from(selectedTransactions),
    getTotalCount: () => totalCount,
    getManagementAnalytics: () => managementAnalytics,

    // State methods
    isLoading: () => managementLoading,
    hasError: () => !!managementError,
    getCurrentView: () => currentView,

    // Filter methods
    applyFilter: (filterType, value) => handleFilterChange(filterType, value),
    clearFilters: () => handleClearFilters(),
    search: (term) => handleSearch(term),

    // Transaction methods
    viewTransaction: (transactionId) => handleTransactionView(transactionId),
    selectTransaction: (transactionId) => handleTransactionSelect(transactionId),

    // Bulk methods
    selectAll: () => handleSelectAll(),
    performBulkAction: (action) => handleBulkAction(action),

    // Analytics methods
    getTransactionInsights: () => generateTransactionInsights(),

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    generateReport: () => generateManagementReport(),
    optimizeTable: () => optimizeTableLayout()
  }), [
    transactions,
    selectedTransactions,
    totalCount,
    managementAnalytics,
    managementLoading,
    managementError,
    currentView,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(MANAGEMENT_ANALYTICS_EVENTS.TRANSACTION_FILTERED, {
        view: currentView,
        timestamp: new Date().toISOString(),
        transactionCount: transactions.length
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Transaction management interface loaded with ${currentView} view`);
    }
  }, [currentView, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader, transactions.length]);

  // Auto-refresh effect
  useEffect(() => {
    if (enableRealTimeUpdates && autoRefreshInterval > 0) {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }

      autoRefreshTimeoutRef.current = setTimeout(() => {
        if (onRefresh) {
          onRefresh();
        } else {
          loadTransactions();
        }
      }, autoRefreshInterval);

      return () => {
        if (autoRefreshTimeoutRef.current) {
          clearTimeout(autoRefreshTimeoutRef.current);
        }
      };
    }
  }, [enableRealTimeUpdates, autoRefreshInterval, onRefresh]);

  // Initialize data loading
  useEffect(() => {
    loadTransactions();
  }, [page, rowsPerPage]);

  // Enhanced handler functions
  const handleViewChange = useCallback((newView) => {
    setCurrentView(newView);

    if (enableAccessibility) {
      announceToScreenReader(`Switched to ${newView} view`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleRefresh = useCallback(() => {
    setManagementLoading(true);
    setManagementError(null);

    if (onRefresh) {
      onRefresh();
    } else {
      loadTransactions();
    }

    if (enableAccessibility) {
      announceToScreenReader('Transaction data refreshed');
    }
  }, [onRefresh, enableAccessibility, announceToScreenReader]);

  const handleTransactionView = useCallback((transactionId) => {
    setManagementAnalytics(prev => ({
      ...prev,
      transactionsViewed: prev.transactionsViewed + 1
    }));

    if (onTransactionView) {
      onTransactionView(transactionId);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(MANAGEMENT_ANALYTICS_EVENTS.TRANSACTION_VIEWED, {
        transactionId,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Viewing transaction ${transactionId}`);
    }
  }, [onTransactionView, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleTransactionSelect = useCallback((transactionId) => {
    setSelectedTransactions(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(transactionId)) {
        newSelection.delete(transactionId);
      } else {
        newSelection.add(transactionId);
      }
      return newSelection;
    });
  }, []);

  const handleSelectAll = useCallback(() => {
    if (selectedTransactions.size === transactions.length) {
      setSelectedTransactions(new Set());
    } else {
      setSelectedTransactions(new Set(transactions.map(t => t.id)));
    }
  }, [selectedTransactions.size, transactions]);

  const handleBulkAction = useCallback((action) => {
    if (!enableBulkOperations) return;

    setManagementAnalytics(prev => ({
      ...prev,
      bulkActionsPerformed: prev.bulkActionsPerformed + 1
    }));

    if (onBulkAction) {
      onBulkAction(action, Array.from(selectedTransactions));
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(MANAGEMENT_ANALYTICS_EVENTS.BULK_ACTION_PERFORMED, {
        action,
        transactionCount: selectedTransactions.size,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Bulk action ${action} performed on ${selectedTransactions.size} transactions`);
    }
  }, [enableBulkOperations, selectedTransactions, onBulkAction, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleExport = useCallback(() => {
    if (!enableExportOptions) return;

    const exportData = {
      transactions,
      filters,
      totalCount,
      exportedAt: new Date().toISOString()
    };

    setManagementAnalytics(prev => ({
      ...prev,
      transactionsExported: prev.transactionsExported + 1
    }));

    if (onDataExport) {
      onDataExport(exportData);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(MANAGEMENT_ANALYTICS_EVENTS.TRANSACTION_EXPORTED, {
        transactionCount: transactions.length,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Transaction data exported successfully');
    }
  }, [enableExportOptions, transactions, filters, totalCount, onDataExport, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleRowsPerPageChange = useCallback((rows) => {
    setRowsPerPage(parseInt(rows, 10));
    setPage(0);
  }, []);

  const loadTransactions = useCallback(async () => {
    try {
      setManagementLoading(true);
      setManagementError(null);

      const params = {
        page: page + 1,
        page_size: rowsPerPage,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        ),
      };

      const response = await api.get('/api/admin/finance/transactions', { params });
      setTransactions(response.data);
      setTotalCount(parseInt(response.headers['x-total-count'] || '0'));

      if (enableAccessibility) {
        announceToScreenReader(`Loaded ${response.data.length} transactions`);
      }
    } catch (err) {
      console.error('Error loading transactions:', err);
      setManagementError(err.response?.data?.detail || 'Failed to load transactions');

      if (enableAccessibility) {
        announceToScreenReader('Failed to load transactions');
      }
    } finally {
      setManagementLoading(false);
    }
  }, [page, rowsPerPage, filters, enableAccessibility, announceToScreenReader]);

  // Enhanced utility functions
  const handleFilterChange = useCallback((field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));

    setManagementAnalytics(prev => ({
      ...prev,
      transactionsFiltered: prev.transactionsFiltered + 1
    }));

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(MANAGEMENT_ANALYTICS_EVENTS.TRANSACTION_FILTERED, {
        field,
        value,
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAnalytics, onAnalyticsTrack]);

  const handleSearch = useCallback((searchTerm) => {
    if (searchTerm !== undefined) {
      setFilters(prev => ({ ...prev, search: searchTerm }));
    }

    setPage(0);
    loadTransactions();

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(MANAGEMENT_ANALYTICS_EVENTS.TRANSACTION_SEARCHED, {
        searchTerm: searchTerm || filters.search,
        timestamp: new Date().toISOString()
      });
    }
  }, [filters.search, loadTransactions, enableAnalytics, onAnalyticsTrack]);

  const handleClearFilters = useCallback(() => {
    setFilters({
      search: '',
      status: '',
      transaction_type: '',
      date_start: '',
      date_end: '',
    });
    setPage(0);

    if (enableAccessibility) {
      announceToScreenReader('All filters cleared');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const generateTransactionInsights = useCallback(() => {
    if (!transactions.length) return [];

    const insights = [];

    // Calculate key insights
    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
    const completedTransactions = transactions.filter(t => t.status === TRANSACTION_STATUSES.COMPLETED);
    const failedTransactions = transactions.filter(t => t.status === TRANSACTION_STATUSES.FAILED);

    insights.push({
      type: 'success_rate',
      title: 'Transaction Success Rate',
      value: formatPercentage(completedTransactions.length / transactions.length),
      trend: completedTransactions.length / transactions.length >= 0.95 ? 'positive' : 'negative',
      recommendation: completedTransactions.length / transactions.length < 0.95 ? 'Investigate payment issues' : 'Excellent success rate'
    });

    insights.push({
      type: 'total_volume',
      title: 'Total Transaction Volume',
      value: formatCurrency(totalAmount),
      trend: 'neutral',
      recommendation: 'Monitor for unusual patterns'
    });

    return insights;
  }, [transactions]);

  const generateManagementReport = useCallback(() => {
    const insights = generateTransactionInsights();

    return {
      summary: insights,
      transactions,
      analytics: managementAnalytics,
      filters,
      totalCount,
      generatedAt: new Date().toISOString()
    };
  }, [generateTransactionInsights, transactions, managementAnalytics, filters, totalCount]);

  const optimizeTableLayout = useCallback(() => {
    // Implement table layout optimization logic
    const optimizedLayout = {
      columnOrder: ['date', 'user', 'type', 'amount', 'status', 'actions'],
      responsive: isMobile ? 'mobile' : 'desktop',
      recommendations: [
        'Hide less important columns on mobile',
        'Add sorting indicators',
        'Implement virtual scrolling for large datasets'
      ]
    };

    return optimizedLayout;
  }, [isMobile]);

  const formatCurrency = useCallback((amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }, []);

  const formatPercentage = useCallback((value) => {
    return `${(value * 100).toFixed(1)}%`;
  }, []);

  const getStatusColor = useCallback((status) => {
    switch (status) {
      case TRANSACTION_STATUSES.COMPLETED:
        return 'success';
      case TRANSACTION_STATUSES.PENDING:
        return 'warning';
      case TRANSACTION_STATUSES.FAILED:
        return 'error';
      case TRANSACTION_STATUSES.REFUNDED:
        return 'info';
      case TRANSACTION_STATUSES.DISPUTED:
        return 'warning';
      case TRANSACTION_STATUSES.PROCESSING:
        return 'primary';
      default:
        return 'default';
    }
  }, []);

  const getTypeColor = useCallback((type) => {
    switch (type) {
      case TRANSACTION_TYPES.SUBSCRIPTION:
        return 'primary';
      case TRANSACTION_TYPES.ADDON:
        return 'secondary';
      case TRANSACTION_TYPES.APPSUMO:
        return 'success';
      case TRANSACTION_TYPES.REFUND:
        return 'error';
      case TRANSACTION_TYPES.UPGRADE:
        return 'info';
      case TRANSACTION_TYPES.DOWNGRADE:
        return 'warning';
      default:
        return 'default';
    }
  }, []);

  return (
    <Box
      ref={managementRef}
      className={className}
      sx={glassMorphismStyles}
      {...props}
    >
      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title="Transaction Filters"
          action={
            <Box display="flex" gap={1}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                disabled={managementLoading || loading}
                sx={{
                  borderColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    borderColor: ACE_COLORS.YELLOW,
                    color: ACE_COLORS.YELLOW,
                    backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1)
                  }
                }}
              >
                Refresh
              </Button>
              {enableExportOptions && (
                <Button
                  variant="contained"
                  startIcon={<ExportIcon />}
                  onClick={handleExport}
                  disabled={!transactions.length}
                  sx={{
                    background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE} 30%, ${ACE_COLORS.YELLOW} 90%)`,
                    '&:hover': {
                      background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE} 60%, ${ACE_COLORS.YELLOW} 100%)`,
                    }
                  }}
                >
                  Export
                </Button>
              )}
            </Box>
          }
        />
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                size="small"
                label="Search"
                placeholder="User email, transaction ID..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  endAdornment: (
                    <IconButton size="small" onClick={handleSearch}>
                      <SearchIcon />
                    </IconButton>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                  <MenuItem value="refunded">Refunded</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={filters.transaction_type}
                  label="Type"
                  onChange={(e) => handleFilterChange('transaction_type', e.target.value)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="subscription">Subscription</MenuItem>
                  <MenuItem value="addon">Add-on</MenuItem>
                  <MenuItem value="appsumo">AppSumo</MenuItem>
                  <MenuItem value="refund">Refund</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                size="small"
                type="date"
                label="Start Date"
                value={filters.date_start}
                onChange={(e) => handleFilterChange('date_start', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                size="small"
                type="date"
                label="End Date"
                value={filters.date_end}
                onChange={(e) => handleFilterChange('date_end', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={1}>
              <Box display="flex" gap={1}>
                <Button
                  variant="contained"
                  onClick={handleSearch}
                  disabled={loading}
                  fullWidth
                >
                  Search
                </Button>
              </Box>
            </Grid>
          </Grid>
          {Object.values(filters).some(value => value !== '') && (
            <Box mt={2}>
              <Button size="small" onClick={handleClearFilters}>
                Clear Filters
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {(managementError || error) && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => {
          setManagementError(null);
          if (enableAccessibility) {
            announceToScreenReader('Error message dismissed');
          }
        }}>
          {managementError || error}
        </Alert>
      )}

      {/* Transactions Table */}
      <Card>
        <CardHeader 
          title="Transactions"
          subheader={`${totalCount} total transactions`}
        />
        <CardContent sx={{ p: 0 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Payment Method</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {(managementLoading || loading) ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                      <CircularProgress sx={{ color: ACE_COLORS.PURPLE }} />
                    </TableCell>
                  </TableRow>
                ) : transactions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                      <Typography variant="body2" color="text.secondary">
                        No transactions found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  transactions.map((transaction) => (
                    <TableRow key={transaction.id} hover>
                      <TableCell>
                        <Typography variant="body2">
                          {format(new Date(transaction.transaction_date), 'MMM dd, yyyy')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {format(new Date(transaction.transaction_date), 'HH:mm')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {transaction.user_email || 'Unknown'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {transaction.user_id.slice(-8)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.transaction_type}
                          color={getTypeColor(transaction.transaction_type)}
                          size="small"
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {transaction.description}
                        </Typography>
                        {transaction.plan_id && (
                          <Typography variant="caption" color="text.secondary">
                            Plan: {transaction.plan_id}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(transaction.amount)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {transaction.currency}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.status}
                          color={getStatusColor(transaction.status)}
                          size="small"
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                          {transaction.payment_method.replace('_', ' ')}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleTransactionView(transaction.id)}
                            sx={{
                              color: ACE_COLORS.PURPLE,
                              '&:hover': {
                                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                              }
                            }}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
            rowsPerPageOptions={[10, 25, 50, 100]}
          />
        </CardContent>
      </Card>
    </Box>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedTransactionManagement.propTypes = {
  // Core props
  data: PropTypes.shape({
    transactions: PropTypes.array,
    totalCount: PropTypes.number,
    analytics: PropTypes.object
  }),
  loading: PropTypes.bool,
  error: PropTypes.string,

  // Enhanced props
  className: PropTypes.string,
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeUpdates: PropTypes.bool,
  enableBulkOperations: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableExportOptions: PropTypes.bool,
  enableDisputeManagement: PropTypes.bool,

  // Configuration
  defaultView: PropTypes.oneOf(Object.values(MANAGEMENT_VIEWS)),
  defaultSortOption: PropTypes.oneOf(Object.values(SORT_OPTIONS)),
  autoRefreshInterval: PropTypes.number,
  maxDisplayTransactions: PropTypes.number,

  // Callback props
  onTransactionView: PropTypes.func,
  onTransactionEdit: PropTypes.func,
  onTransactionRefund: PropTypes.func,
  onDisputeInitiate: PropTypes.func,
  onBulkAction: PropTypes.func,
  onDataExport: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onRefresh: PropTypes.func
};

// Default props
EnhancedTransactionManagement.defaultProps = {
  data: {},
  loading: false,
  error: null,
  className: '',
  enableAdvancedFeatures: true,
  enableRealTimeUpdates: true,
  enableBulkOperations: true,
  enableAccessibility: true,
  enableAnalytics: true,
  enableExportOptions: true,
  enableDisputeManagement: true,
  defaultView: MANAGEMENT_VIEWS.TABLE,
  defaultSortOption: SORT_OPTIONS.DATE_DESC,
  autoRefreshInterval: 60000,
  maxDisplayTransactions: 1000,
  onTransactionView: null,
  onTransactionEdit: null,
  onTransactionRefund: null,
  onDisputeInitiate: null,
  onBulkAction: null,
  onDataExport: null,
  onAnalyticsTrack: null,
  onRefresh: null
};

// Display name for debugging
EnhancedTransactionManagement.displayName = 'EnhancedTransactionManagement';

export default EnhancedTransactionManagement;

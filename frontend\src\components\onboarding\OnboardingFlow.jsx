/**
 * Enhanced Onboarding Flow - Enterprise-grade onboarding flow management component
 * Features: Comprehensive onboarding flow management, multi-step guided workflows, subscription-free access,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced onboarding capabilities and interactive flow exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Stepper,
  Step,
  StepLabel,

  Typography,
  Paper,
  Fade,
  useMediaQuery,
  LinearProgress,
  Stack,
  Snackbar,
  Alert,
  alpha,
  Chip
} from '@mui/material';
import {
  PersonOutline as PersonIcon,
  BusinessOutline as BusinessIcon,
  ConnectWithoutContact as ConnectIcon,
  RocketLaunch as LaunchIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { useAccessibility } from '../../hooks/useAccessibility';
import AccessibleButton from '../common/AccessibleButton';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Onboarding flow display modes with enhanced configurations
const ONBOARDING_FLOW_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Flows',
    description: 'Basic onboarding flow management interface',
    features: ['basic_flows', 'analytics_flows', 'ai_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Flows',
    description: 'Comprehensive onboarding flow management',
    features: ['detailed_flows', 'flow_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Flows',
    description: 'AI-powered onboarding flow management and optimization',
    features: ['ai_assisted', 'ai_optimization', 'flow_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Flows',
    description: 'Advanced onboarding flow analytics and insights',
    features: ['analytics_flows', 'flow_insights']
  }
};

/**
 * Enhanced Onboarding Flow Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Function} [props.onComplete] - Completion callback
 * @param {Function} [props.onSkip] - Skip callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights (no restrictions)
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onFlowAction] - Flow action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-onboarding-flow'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const OnboardingFlow = memo(forwardRef(({
  onComplete,
  onSkip,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onFlowAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-onboarding-flow',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announce, announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { user, updateUserProfile } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const onboardingRef = useRef(null);
  const [activeStep, setActiveStep] = useState(0);
  const [completed, setCompleted] = useState(new Set());
  const [userData, setUserData] = useState({
    businessType: '',
    goals: [],
    socialPlatforms: [],
    experience: '',
  });

  // Enhanced state management
  const [onboardingMode, setOnboardingMode] = useState('compact');
  const [onboardingHistory, setOnboardingHistory] = useState([]);
  const [onboardingAnalytics, setOnboardingAnalytics] = useState(null);
  const [onboardingInsights, setOnboardingInsights] = useState(null);
  const [customFlowConfigs, setCustomFlowConfigs] = useState([]);
  const [onboardingPreferences, setOnboardingPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    flowSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [onboardingDrawerOpen, setOnboardingDrawerOpen] = useState(false);
  const [selectedFlowType, setSelectedFlowType] = useState(null);
  const [onboardingStats, setOnboardingStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastOnboardingCheck, setLastOnboardingCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with full feature access - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // All features are available to all users (no plan-based limitations)
    const features = {
      creator: {
        maxFlowTypes: -1,
        maxFlowPerDay: -1,
        hasAdvancedFlow: true,
        hasFlowAnalytics: true,
        hasCustomFlow: true,
        hasFlowInsights: true,
        hasFlowHistory: true,
        hasAIAssistance: true,
        hasFlowExport: true,
        hasFlowScheduling: true,
        hasFlowAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxFlowTypes: -1,
        maxFlowPerDay: -1,
        hasAdvancedFlow: true,
        hasFlowAnalytics: true,
        hasCustomFlow: true,
        hasFlowInsights: true,
        hasFlowHistory: true,
        hasAIAssistance: true,
        hasFlowExport: true,
        hasFlowScheduling: true,
        hasFlowAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxFlowTypes: -1,
        maxFlowPerDay: -1,
        hasAdvancedFlow: true,
        hasFlowAnalytics: true,
        hasCustomFlow: true,
        hasFlowInsights: true,
        hasFlowHistory: true,
        hasAIAssistance: true,
        hasFlowExport: true,
        hasFlowScheduling: true,
        hasFlowAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxFlowTypes === -1 || currentUsage < currentFeatures.maxFlowTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'main',
      'aria-label': ariaLabel || `Onboarding flow with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Onboarding flow interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive onboarding API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getOnboardingHistory: () => onboardingHistory,
    getOnboardingAnalytics: () => onboardingAnalytics,
    getOnboardingInsights: () => onboardingInsights,
    refreshOnboarding: () => {
      fetchOnboardingAnalytics();
      if (onRefresh) onRefresh();
    },

    // Onboarding methods
    focusOnboarding: () => {
      if (onboardingRef.current) {
        onboardingRef.current.focus();
      }
    },
    getCurrentStep: () => activeStep,
    getTotalSteps: () => steps.length,
    goToStep: (stepIndex) => setActiveStep(stepIndex),
    nextStep: () => handleNext(),
    previousStep: () => handleBack(),
    skipStep: () => handleSkipStep(),
    completeOnboarding: () => handleComplete(),
    skipOnboarding: () => handleSkipOnboarding(),
    openOnboardingDrawer: () => setOnboardingDrawerOpen(true),
    closeOnboardingDrawer: () => setOnboardingDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportOnboardingData: () => {
      if (onExport) {
        onExport(onboardingHistory, onboardingAnalytics);
      }
    },

    // Accessibility methods
    announceOnboarding: (message) => announceToScreenReader(message),
    focusOnboardingField: () => setFocusToElement('onboarding-flow-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => onboardingMode,
    getOnboardingStats: () => onboardingStats,
    getSelectedFlowType: () => selectedFlowType,
    getCustomFlowConfigs: () => customFlowConfigs,
    addCustomFlowConfig,
    handleOnboardingModeChange,
    updateOnboardingPreferences,
    handleFlowTypeSelection,
    validateFlowConfig,
    getOnboardingDrawerOpen: () => onboardingDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    triggerFlowAction: (action, data) => {
      if (onFlowAction) {
        onFlowAction(action, data);
      }
    }
  }), [
    onboardingHistory,
    onboardingAnalytics,
    onboardingInsights,
    onboardingStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    onboardingMode,
    activeStep,
    selectedFlowType,
    customFlowConfigs,
    showAnalytics,
    fullscreenMode,
    onboardingDrawerOpen,
    onFlowAction,
    addCustomFlowConfig,
    fetchOnboardingAnalytics,
    handleBack,
    handleComplete,
    handleFlowTypeSelection,
    handleNext,
    handleOnboardingModeChange,
    handleSkipOnboarding,
    handleSkipStep,
    steps.length,
    updateOnboardingPreferences,
    validateFlowConfig
  ]);

  // Fetch onboarding analytics with enhanced error handling and retry logic
  const fetchOnboardingAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/onboarding/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setOnboardingAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (onboardingPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Onboarding analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch onboarding analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load onboarding analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, onboardingPreferences.showAnalytics]);

  // Handle onboarding mode switching
  const handleOnboardingModeChange = useCallback((newMode) => {
    if (ONBOARDING_FLOW_MODES[newMode.toUpperCase()]) {
      setOnboardingMode(newMode);
      announceToScreenReader(`Onboarding mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setOnboardingHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (onboardingPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} onboarding mode`);
      }
    }
  }, [announceToScreenReader, user?.id, onboardingPreferences.showAnalytics, showSuccess]);

  // Handle custom flow config management
  const addCustomFlowConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: user?.id
    };

    setCustomFlowConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setOnboardingHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (onboardingPreferences.showAnalytics) {
      showSuccess(`Custom flow config "${configData.name}" created`);
    }
  }, [user?.id, onboardingPreferences.showAnalytics, showSuccess]);

  // Handle onboarding preferences updates
  const updateOnboardingPreferences = useCallback((newPreferences) => {
    setOnboardingPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setOnboardingHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (onboardingPreferences.showAnalytics) {
      showSuccess('Onboarding preferences updated');
    }
  }, [user?.id, onboardingPreferences.showAnalytics, showSuccess]);

  // Handle flow type selection
  const handleFlowTypeSelection = useCallback((flowType) => {
    setSelectedFlowType(flowType);

    // Track flow type selection
    const typeRecord = {
      id: Date.now(),
      type: 'flow_type_selected',
      flowType,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setOnboardingHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (onboardingPreferences.showAnalytics) {
      announceToScreenReader(`Selected flow type: ${flowType}`);
    }
  }, [user?.id, onboardingPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateFlowConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.steps?.length) {
      errors.steps = 'At least one step is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Onboarding steps configuration
  const steps = useMemo(() => [
    {
      id: 'welcome',
      label: 'Welcome',
      title: 'Welcome to Your Social Media Success Journey!',
      description: 'Let&apos;s get you set up for maximum impact in just a few minutes.',
      icon: <PersonIcon />,
      optional: false,
    },
    {
      id: 'business',
      label: 'Business Setup',
      title: 'Tell us about your business',
      description: 'This helps us personalize your experience and recommendations.',
      icon: <BusinessIcon />,
      optional: false,
    },
    {
      id: 'platforms',
      label: 'Connect Platforms',
      title: 'Connect your social media accounts',
      description: 'Connect the platforms where you want to grow your presence.',
      icon: <ConnectIcon />,
      optional: true,
    },
    {
      id: 'launch',
      label: 'Ready to Launch',
      title: 'You&apos;re all set!',
      description: 'Your personalized dashboard is ready. Let&apos;s start creating amazing content!',
      icon: <LaunchIcon />,
      optional: false,
    },
  ], []);

  // Calculate progress percentage
  const progress = ((activeStep + 1) / steps.length) * 100;

  // Initial data loading
  useEffect(() => {
    fetchOnboardingAnalytics();
    fetchOnboardingInsights();
  }, [fetchOnboardingAnalytics, fetchOnboardingInsights]);

  // Fetch onboarding insights
  const fetchOnboardingInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/onboarding/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setOnboardingInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch onboarding insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && activeStep >= 0) {
      // Optimize onboarding management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchOnboardingAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, activeStep, fetchOnboardingAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastOnboardingCheck(Date.now());

          if (wasUnavailable && onboardingPreferences.showAnalytics) {
            showSuccess("Connection restored - Onboarding features available");
          }
        } else {
          setBackendAvailable(false);
          if (onboardingPreferences.showAnalytics) {
            showError("Backend service unavailable - Some onboarding features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastOnboardingCheck;
          if (timeSinceLastCheck > 60000 && onboardingPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Onboarding may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastOnboardingCheck, onboardingPreferences.showAnalytics, showSuccess, showError]);

  // Generate AI suggestions when onboarding changes
  useEffect(() => {
    if (enableAIInsights && onboardingPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, onboardingPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/onboarding/ai-suggestions', {
        params: {
          context: activeStep,
          totalSteps: steps.length
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (onboardingPreferences.showAnalytics) {
        showSuccess('AI suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [activeStep, steps.length, onboardingPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when onboarding changes
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchOnboardingStats();
    }
  }, [enableAdvancedFeatures, fetchOnboardingStats]);

  // Fetch onboarding stats function
  const fetchOnboardingStats = useCallback(async () => {
    try {
      const response = await api.get('/api/onboarding/stats');
      setOnboardingStats(response.data);
    } catch (error) {
      console.error('Failed to fetch onboarding stats:', error);
    }
  }, []);

  // Handle step navigation
  const handleNext = useCallback(() => {
    const newCompleted = new Set(completed);
    newCompleted.add(activeStep);
    setCompleted(newCompleted);

    if (activeStep < steps.length - 1) {
      setActiveStep(activeStep + 1);
      announce(`Step ${activeStep + 2} of ${steps.length}: ${steps[activeStep + 1].title}`, 'polite');
    } else {
      handleComplete();
    }
  }, [completed, activeStep, steps, announce, handleComplete]);

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
      announce(`Step ${activeStep} of ${steps.length}: ${steps[activeStep - 1].title}`, 'polite');
    }
  }, [activeStep, steps, announce]);

  const handleSkipStep = useCallback(() => {
    if (steps[activeStep].optional) {
      handleNext();
    }
  }, [steps, activeStep, handleNext]);

  const handleComplete = useCallback(async () => {
    try {
      // Save onboarding data
      await updateUserProfile({
        ...userData,
        onboardingCompleted: true,
        onboardingCompletedAt: new Date().toISOString(),
      });

      announce('Onboarding completed successfully! Welcome to your dashboard.', 'assertive');

      if (onComplete) {
        onComplete(userData);
      }
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
      announce('There was an error completing onboarding. Please try again.', 'assertive');
    }
  }, [userData, updateUserProfile, announce, onComplete]);

  const handleSkipOnboarding = useCallback(() => {
    announce('Onboarding skipped. You can access the setup wizard anytime from settings.', 'polite');

    if (onSkip) {
      onSkip();
    }
  }, [announce, onSkip]);

  // Render step content based on current step
  const renderStepContent = (stepIndex) => {
    const step = steps[stepIndex];

    switch (step.id) {
      case 'welcome':
        return (
          <WelcomeStep
            user={user}
            onNext={handleNext}
            onSkip={handleSkipOnboarding}
          />
        );
      
      case 'business':
        return (
          <BusinessSetupStep
            userData={userData}
            onUpdate={setUserData}
            onNext={handleNext}
            onBack={handleBack}
          />
        );
      
      case 'platforms':
        return (
          <PlatformConnectionStep
            userData={userData}
            onUpdate={setUserData}
            onNext={handleNext}
            onBack={handleBack}
            onSkip={handleSkipStep}
          />
        );
      
      case 'launch':
        return (
          <LaunchStep
            userData={userData}
            onComplete={handleComplete}
            onBack={handleBack}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <Box
      {...getAccessibilityProps()}
      ref={onboardingRef}
      sx={{
        ...sx,
        ...customization,
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${alpha(ACE_COLORS.PURPLE, 0.1)} 0%, ${alpha(ACE_COLORS.YELLOW, 0.1)} 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: { xs: 2, md: 4 },
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      <Paper
        elevation={8}
        sx={{
          width: '100%',
          maxWidth: 800,
          borderRadius: 3,
          overflow: 'hidden',
        }}
      >
        {/* Progress Header */}
        <Box
          sx={{
            p: 3,
            background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.DARK} 100%)`,
            color: 'white',
          }}
        >
          <Typography variant="h5" component="h1" gutterBottom>
            Getting Started
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{
                flex: 1,
                height: 8,
                borderRadius: 4,
                backgroundColor: 'rgba(255,255,255,0.2)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: 'white',
                },
              }}
            />
            <Typography variant="body2" sx={{ minWidth: 60 }}>
              {Math.round(progress)}%
            </Typography>
          </Box>

          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            Step {activeStep + 1} of {steps.length}
          </Typography>
        </Box>

        {/* Stepper Navigation (Desktop) */}
        {!isMobile && (
          <Box sx={{ p: 3, borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
            <Stepper activeStep={activeStep} alternativeLabel>
              {steps.map((step, index) => (
                <Step key={step.id} completed={completed.has(index)}>
                  <StepLabel
                    icon={
                      completed.has(index) ? (
                        <CheckIcon color="success" />
                      ) : (
                        step.icon
                      )
                    }
                  >
                    {step.label}
                    {step.optional && (
                      <Chip
                        label="Optional"
                        size="small"
                        variant="outlined"
                        sx={{ ml: 1, fontSize: '0.7rem' }}
                      />
                    )}
                  </StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
        )}

        {/* Step Content */}
        <Box sx={{ p: { xs: 2, md: 4 } }}>
          <Fade in={true} timeout={500}>
            <Box>
              {renderStepContent(activeStep)}
            </Box>
          </Fade>
        </Box>

        {/* Mobile Step Indicator */}
        {isMobile && (
          <Box
            sx={{
              p: 2,
              borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
              display: 'flex',
              justifyContent: 'center',
              gap: 1,
            }}
          >
            {steps.map((_, index) => (
              <Box
                key={index}
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: index <= activeStep
                    ? ACE_COLORS.PURPLE
                    : alpha(ACE_COLORS.DARK, 0.3),
                  transition: 'background-color 0.3s ease',
                }}
              />
            ))}
          </Box>
        )}
      </Paper>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying onboarding sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
OnboardingFlow.propTypes = {
  // Core props
  onComplete: PropTypes.func,
  onSkip: PropTypes.func,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onFlowAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

OnboardingFlow.displayName = 'OnboardingFlow';

// Individual step components would be implemented here
const WelcomeStep = ({ user, onNext, onSkip }) => (
  <Box textAlign="center">
    <Typography variant="h4" gutterBottom>
      Welcome, {user?.firstName || 'there'}! 👋
    </Typography>
    <Typography variant="body1" color="text.secondary" paragraph>
      We&apos;re excited to help you grow your social media presence. This quick setup will personalize your experience.
    </Typography>
    <Stack direction="row" spacing={2} justifyContent="center" sx={{ mt: 4 }}>
      <AccessibleButton
        variant="outlined"
        onClick={onSkip}
        ariaLabel="Skip onboarding and go directly to dashboard"
      >
        Skip Setup
      </AccessibleButton>
      <AccessibleButton
        variant="contained"
        onClick={onNext}
        ariaLabel="Start the onboarding process"
      >
        Let&apos;s Get Started
      </AccessibleButton>
    </Stack>
  </Box>
);

// Additional step components would be implemented similarly...
const BusinessSetupStep = ({ userData, onUpdate, onNext, onBack }) => {
  const handleBusinessTypeChange = (type) => {
    if (onUpdate) {
      onUpdate({ ...userData, businessType: type });
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>Business Setup</Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Current business type: {userData?.businessType || 'Not selected'}
      </Typography>
      <Stack spacing={2} sx={{ mb: 4 }}>
        <AccessibleButton
          variant={userData?.businessType === 'startup' ? 'contained' : 'outlined'}
          onClick={() => handleBusinessTypeChange('startup')}
        >
          Startup
        </AccessibleButton>
        <AccessibleButton
          variant={userData?.businessType === 'enterprise' ? 'contained' : 'outlined'}
          onClick={() => handleBusinessTypeChange('enterprise')}
        >
          Enterprise
        </AccessibleButton>
      </Stack>
      <Stack direction="row" spacing={2} justifyContent="space-between" sx={{ mt: 4 }}>
        <AccessibleButton variant="outlined" onClick={onBack}>
          Back
        </AccessibleButton>
        <AccessibleButton variant="contained" onClick={onNext}>
          Continue
        </AccessibleButton>
      </Stack>
    </Box>
  );
};

const PlatformConnectionStep = ({ userData, onUpdate, onNext, onBack, onSkip }) => {
  const handlePlatformToggle = (platform) => {
    if (onUpdate) {
      const currentPlatforms = userData?.socialPlatforms || [];
      const updatedPlatforms = currentPlatforms.includes(platform)
        ? currentPlatforms.filter(p => p !== platform)
        : [...currentPlatforms, platform];
      onUpdate({ ...userData, socialPlatforms: updatedPlatforms });
    }
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom>Connect Platforms</Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Selected platforms: {userData?.socialPlatforms?.join(', ') || 'None'}
      </Typography>
      <Stack spacing={2} sx={{ mb: 4 }}>
        {['Facebook', 'Twitter', 'LinkedIn', 'Instagram'].map((platform) => (
          <AccessibleButton
            key={platform}
            variant={userData?.socialPlatforms?.includes(platform) ? 'contained' : 'outlined'}
            onClick={() => handlePlatformToggle(platform)}
          >
            {platform}
          </AccessibleButton>
        ))}
      </Stack>
      <Stack direction="row" spacing={2} justifyContent="space-between" sx={{ mt: 4 }}>
        <AccessibleButton variant="outlined" onClick={onBack}>
          Back
        </AccessibleButton>
        <Stack direction="row" spacing={2}>
          <AccessibleButton variant="text" onClick={onSkip}>
            Skip for Now
          </AccessibleButton>
          <AccessibleButton variant="contained" onClick={onNext}>
            Continue
          </AccessibleButton>
        </Stack>
      </Stack>
    </Box>
  );
};

const LaunchStep = ({ userData, onComplete, onBack }) => (
  <Box textAlign="center">
    <Typography variant="h4" gutterBottom>
      🚀 You&apos;re Ready to Launch!
    </Typography>
    <Typography variant="body1" color="text.secondary" paragraph>
      Your personalized dashboard is ready. Let&apos;s start creating amazing content!
    </Typography>
    {userData && (
      <Box sx={{ mb: 3, p: 2, bgcolor: alpha('#4E40C5', 0.1), borderRadius: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Setup Summary:
        </Typography>
        <Typography variant="body2">
          Business Type: {userData.businessType || 'Not specified'}
        </Typography>
        <Typography variant="body2">
          Platforms: {userData.socialPlatforms?.length || 0} connected
        </Typography>
      </Box>
    )}
    <Stack direction="row" spacing={2} justifyContent="center" sx={{ mt: 4 }}>
      <AccessibleButton variant="outlined" onClick={onBack}>
        Back
      </AccessibleButton>
      <AccessibleButton variant="contained" onClick={onComplete}>
        Go to Dashboard
      </AccessibleButton>
    </Stack>
  </Box>
);

export default OnboardingFlow;

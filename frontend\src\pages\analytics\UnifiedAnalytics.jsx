// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useCallback, Suspense } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Divider,
  Button,
  IconButton,
  CircularProgress,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Drawer,
  useTheme,
  useMediaQuery,
} from '@mui/material';
// import { DateRangePicker } from '@mui/x-date-pickers';
import {
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Campaign as CampaignIcon,
  ContentPaste as ContentIcon,
  Public as PublicIcon,
  CompareArrows as CompareArrowsIcon,
  Lightbulb as LightbulbIcon,
  Close as CloseIcon,
  Save as SaveIcon,
  Download as DownloadIcon,
  SentimentSatisfiedAlt as SentimentSatisfiedAltIcon,
} from '@mui/icons-material';

import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

// Lazy import analytics components to reduce initial bundle size

const PerformanceOverview = React.lazy(() => import('../../components/analytics/PerformanceOverview'));
const ContentPerformanceTable = React.lazy(() => import('../../components/analytics/ContentPerformanceTable'));
const AudienceDemographics = React.lazy(() => import('../../components/analytics/AudienceDemographics'));
const CampaignPerformance = React.lazy(() => import('../../components/analytics/CampaignPerformance'));
const SocialMediaMetrics = React.lazy(() => import('../../components/analytics/SocialMediaMetrics'));
const ICPPerformanceMetrics = React.lazy(() => import('../../components/analytics/ICPPerformanceMetrics'));
const QuickInsights = React.lazy(() => import('../../components/analytics/QuickInsights'));
const AIResponseManagementAnalytics = React.lazy(() => import('../../components/analytics/AIResponseManagementAnalytics'));
const CompetitorInsightsAnalytics = React.lazy(() => import('../../components/analytics/CompetitorInsightsAnalytics'));

/**
 * Unified Analytics page that combines all analytics views into a single page with tabs
 *
 * @component
 */
const UnifiedAnalytics = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  const { showSuccessNotification, showErrorNotification } = useNotification();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Get the tab from the URL or default to 'overview'
  const getTabFromUrl = () => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab');
    return tab || 'overview';
  };

  // State for active tab
  const [activeTab, setActiveTab] = useState(getTabFromUrl());

  // State for filter drawer
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);

  // State for filters
  const [filters, setFilters] = useState({
    dateRange: [
      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      new Date(), // Today
    ],
    platform: 'all',
    campaign: 'all',
    contentType: 'all',
    icp: 'all',
  });

  // State for loading indicators
  const [loading, setLoading] = useState({
    overview: true,
    content: true,
    audience: true,
    campaigns: true,
    social: true,
    icp: true,
    aiResponse: true,
    competitorInsights: true,
  });

  // State for analytics data
  const [analyticsData, setAnalyticsData] = useState({
    overview: null,
    content: null,
    audience: null,
    campaigns: null,
    social: null,
    icp: null,
    aiResponse: null,
    competitorInsights: null,
  });

  // State for available filters
  const [availableFilters, setAvailableFilters] = useState({
    platforms: [],
    campaigns: [],
    contentTypes: [],
    icps: [],
  });

  // Fetch analytics data based on filters
  useEffect(() => {
    fetchAnalyticsData();

    // Update the URL with the active tab
    const searchParams = new URLSearchParams(location.search);
    searchParams.set('tab', activeTab);
    navigate({
      pathname: location.pathname,
      search: searchParams.toString(),
    }, { replace: true });
  }, [activeTab, filters, fetchAnalyticsData, location.pathname, location.search, navigate]);

  // Fetch available filters
  useEffect(() => {
    fetchAvailableFilters();
  }, [fetchAvailableFilters]);

  // Fetch analytics data
  const fetchAnalyticsData = useCallback(async () => {
    // Set loading state for the active tab
    setLoading(prev => ({
      ...prev,
      [activeTab]: true,
    }));

    try {
      // Format date range for API
      const startDate = filters.dateRange[0].toISOString();
      const endDate = filters.dateRange[1].toISOString();

      // Build query parameters
      const queryParams = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
        platform: filters.platform !== 'all' ? filters.platform : '',
        campaign: filters.campaign !== 'all' ? filters.campaign : '',
        content_type: filters.contentType !== 'all' ? filters.contentType : '',
        icp: filters.icp !== 'all' ? filters.icp : '',
      }).toString();

      // Fetch data based on active tab
      let response;

      switch (activeTab) {
        case 'overview':
          response = await api.get(`/api/analytics/overview?${queryParams}`);
          setAnalyticsData(prev => ({
            ...prev,
            overview: response.data,
          }));
          break;

        case 'content':
          response = await api.get(`/api/analytics/content-performance?${queryParams}`);
          setAnalyticsData(prev => ({
            ...prev,
            content: response.data,
          }));
          break;

        case 'audience':
          response = await api.get(`/api/analytics/audience?${queryParams}`);
          setAnalyticsData(prev => ({
            ...prev,
            audience: response.data,
          }));
          break;

        case 'campaigns':
          response = await api.get(`/api/analytics/campaigns?${queryParams}`);
          setAnalyticsData(prev => ({
            ...prev,
            campaigns: response.data,
          }));
          break;

        case 'social':
          response = await api.get(`/api/analytics/social-media?${queryParams}`);
          setAnalyticsData(prev => ({
            ...prev,
            social: response.data,
          }));
          break;

        case 'icp':
          response = await api.get(`/api/analytics/icp-performance?${queryParams}`);
          setAnalyticsData(prev => ({
            ...prev,
            icp: response.data,
          }));
          break;

        case 'aiResponse':
          response = await api.get(`/api/ai-feedback/analytics?${queryParams}`);
          setAnalyticsData(prev => ({
            ...prev,
            aiResponse: response.data,
          }));
          break;

        case 'competitorInsights':
          response = await api.get(`/api/competitor-insights/dashboard?${queryParams}`);
          setAnalyticsData(prev => ({
            ...prev,
            competitorInsights: response.data,
          }));
          break;

        default:
          break;
      }

      showSuccessNotification('Analytics data loaded successfully');
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      showErrorNotification('Failed to load analytics data');
    } finally {
      setLoading(prev => ({
        ...prev,
        [activeTab]: false,
      }));
    }
  }, [activeTab, filters, showErrorNotification, showSuccessNotification]);

  // Fetch available filters
  const fetchAvailableFilters = useCallback(async () => {
    try {
      // Fetch platforms
      const platformsResponse = await api.get('/api/social-media/platforms');

      // Fetch campaigns
      const campaignsResponse = await api.get('/api/campaigns');

      // Fetch content types
      const contentTypesResponse = await api.get('/api/content/types');

      // Fetch ICPs
      const icpsResponse = await api.get('/api/icps');

      setAvailableFilters({
        platforms: platformsResponse.data,
        campaigns: campaignsResponse.data,
        contentTypes: contentTypesResponse.data,
        icps: icpsResponse.data,
      });
    } catch (error) {
      console.error('Error fetching available filters:', error);
      showErrorNotification('Failed to load filter options');
    }
  }, [showErrorNotification]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle filter change
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value,
    }));
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchAnalyticsData();
  };

  // Handle export data
  const handleExportData = () => {
    // In a real implementation, this would export the data to CSV or PDF
    showSuccessNotification('Data exported successfully');
  };

  // Handle save filters
  const handleSaveFilters = () => {
    // In a real implementation, this would save the filters to user preferences
    showSuccessNotification('Filters saved successfully');
  };

  // Loading component for lazy-loaded components
  const ComponentLoader = () => (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
      <CircularProgress />
    </Box>
  );

  // Render the active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <Suspense fallback={<ComponentLoader />}>
            <PerformanceOverview
              data={analyticsData.overview}
              loading={loading.overview}
              onRefresh={handleRefresh}
            />
          </Suspense>
        );

      case 'content':
        return (
          <Suspense fallback={<ComponentLoader />}>
            <ContentPerformanceTable
              data={analyticsData.content}
              loading={loading.content}
              onRefresh={handleRefresh}
            />
          </Suspense>
        );

      case 'audience':
        return (
          <Suspense fallback={<ComponentLoader />}>
            <AudienceDemographics
              data={analyticsData.audience}
              loading={loading.audience}
              onRefresh={handleRefresh}
            />
          </Suspense>
        );

      case 'campaigns':
        return (
          <Suspense fallback={<ComponentLoader />}>
            <CampaignPerformance
              data={analyticsData.campaigns}
              loading={loading.campaigns}
              onRefresh={handleRefresh}
            />
          </Suspense>
        );

      case 'social':
        return (
          <Suspense fallback={<ComponentLoader />}>
            <SocialMediaMetrics
              data={analyticsData.social}
              loading={loading.social}
              onRefresh={handleRefresh}
            />
          </Suspense>
        );

      case 'icp':
        return (
          <Suspense fallback={<ComponentLoader />}>
            <ICPPerformanceMetrics
              data={analyticsData.icp}
              loading={loading.icp}
              onRefresh={handleRefresh}
            />
          </Suspense>
        );

      case 'aiResponse':
        return (
          <Suspense fallback={<ComponentLoader />}>
            <AIResponseManagementAnalytics
              data={analyticsData.aiResponse}
              loading={loading.aiResponse}
              onRefresh={handleRefresh}
            />
          </Suspense>
        );

      case 'competitorInsights':
        return (
          <Suspense fallback={<ComponentLoader />}>
            <CompetitorInsightsAnalytics
              data={analyticsData.competitorInsights}
              loading={loading.competitorInsights}
              onRefresh={handleRefresh}
            />
          </Suspense>
        );

      default:
        return (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h6">Tab content not found</Typography>
          </Box>
        );
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Analytics</Typography>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            color="secondary"
            component={Link}
            to="/analytics/sentiment-analysis"
            startIcon={<SentimentSatisfiedAltIcon />}
            sx={{ mr: 1 }}
          >
            Sentiment Analysis
          </Button>

          <Button
            variant="outlined"
            color="secondary"
            component={Link}
            to="/analytics/ab-testing"
            startIcon={<CompareArrowsIcon />}
          >
            A/B Testing
          </Button>

          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setFilterDrawerOpen(true)}
          >
            Filters
          </Button>

          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleExportData}
          >
            Export
          </Button>

          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
          >
            Refresh
          </Button>
        </Box>
      </Box>


      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12}>
          <Suspense fallback={<ComponentLoader />}>
            <QuickInsights
              data={analyticsData}
              onRefresh={handleRefresh}
              loading={Object.values(loading).some(Boolean)}
            />
          </Suspense>
        </Grid>
      </Grid>


      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant={isMobile ? 'scrollable' : 'fullWidth'}
          scrollButtons={isMobile ? 'auto' : false}
          aria-label="analytics tabs"
        >
          <Tab
            icon={<TrendingUpIcon />}
            label="Overview"
            value="overview"
            iconPosition="start"
          />
          <Tab
            icon={<ContentIcon />}
            label="Content"
            value="content"
            iconPosition="start"
          />
          <Tab
            icon={<PeopleIcon />}
            label="Audience"
            value="audience"
            iconPosition="start"
          />
          <Tab
            icon={<CampaignIcon />}
            label="Campaigns"
            value="campaigns"
            iconPosition="start"
          />
          <Tab
            icon={<PublicIcon />}
            label="Social Media"
            value="social"
            iconPosition="start"
          />
          <Tab
            icon={<CompareArrowsIcon />}
            label="ICP Performance"
            value="icp"
            iconPosition="start"
          />
          <Tab
            icon={<SentimentSatisfiedAltIcon />}
            label="AI Response Management"
            value="aiResponse"
            iconPosition="start"
          />
          <Tab
            icon={<LightbulbIcon />}
            label="Competitor Insights"
            value="competitorInsights"
            iconPosition="start"
          />
        </Tabs>
      </Paper>


      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {renderTabContent()}
      </Box>


      <Drawer
        anchor="right"
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        PaperProps={{
          sx: { width: { xs: '100%', sm: 400 } },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              <FilterListIcon sx={{ verticalAlign: 'middle', mr: 1 }} />
              Filters
            </Typography>

            <IconButton onClick={() => setFilterDrawerOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>

          <Divider sx={{ mb: 3 }} />

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>

            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Date Range
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Date range filtering temporarily disabled
              </Typography>
              {/* <DateRangePicker
                startText="Start Date"
                endText="End Date"
                value={filters.dateRange}
                onChange={(newValue) => handleFilterChange('dateRange', newValue)}
                renderInput={(startProps, endProps) => (
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField {...startProps} fullWidth size="small" />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField {...endProps} fullWidth size="small" />
                    </Grid>
                  </Grid>
                )}
              /> */}
            </Box>


            <FormControl fullWidth>
              <InputLabel id="platform-filter-label">Platform</InputLabel>
              <Select
                labelId="platform-filter-label"
                id="platform-filter"
                value={filters.platform}
                label="Platform"
                onChange={(e) => handleFilterChange('platform', e.target.value)}
                size="small"
              >
                <MenuItem value="all">All Platforms</MenuItem>
                {availableFilters.platforms.map((platform) => (
                  <MenuItem key={platform.id} value={platform.id}>
                    {platform.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>


            <FormControl fullWidth>
              <InputLabel id="campaign-filter-label">Campaign</InputLabel>
              <Select
                labelId="campaign-filter-label"
                id="campaign-filter"
                value={filters.campaign}
                label="Campaign"
                onChange={(e) => handleFilterChange('campaign', e.target.value)}
                size="small"
              >
                <MenuItem value="all">All Campaigns</MenuItem>
                {availableFilters.campaigns.map((campaign) => (
                  <MenuItem key={campaign.id} value={campaign.id}>
                    {campaign.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>


            <FormControl fullWidth>
              <InputLabel id="content-type-filter-label">Content Type</InputLabel>
              <Select
                labelId="content-type-filter-label"
                id="content-type-filter"
                value={filters.contentType}
                label="Content Type"
                onChange={(e) => handleFilterChange('contentType', e.target.value)}
                size="small"
              >
                <MenuItem value="all">All Content Types</MenuItem>
                {availableFilters.contentTypes.map((type) => (
                  <MenuItem key={type.id} value={type.id}>
                    {type.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>


            <FormControl fullWidth>
              <InputLabel id="icp-filter-label">ICP</InputLabel>
              <Select
                labelId="icp-filter-label"
                id="icp-filter"
                value={filters.icp}
                label="ICP"
                onChange={(e) => handleFilterChange('icp', e.target.value)}
                size="small"
              >
                <MenuItem value="all">All ICPs</MenuItem>
                {availableFilters.icps.map((icp) => (
                  <MenuItem key={icp.id} value={icp.id}>
                    {icp.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
              <Button
                variant="outlined"
                onClick={() => {
                  setFilters({
                    dateRange: [
                      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                      new Date(),
                    ],
                    platform: 'all',
                    campaign: 'all',
                    contentType: 'all',
                    icp: 'all',
                  });
                }}
              >
                Reset
              </Button>

              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSaveFilters}
              >
                Save Filters
              </Button>
            </Box>
          </Box>
        </Box>
      </Drawer>
    </Box>
  );
};

export default UnifiedAnalytics;

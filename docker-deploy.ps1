# @since 2024-1-1 to 2025-25-7
# ACE Social Platform - Docker Desktop Deployment Script
# Windows PowerShell version for Docker Desktop
# Version: 1.0.0

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("development", "dev", "production", "prod", "staging")]
    [string]$Environment = "development",
    
    [Parameter(Mandatory=$false)]
    [switch]$Build,
    
    [Parameter(Mandatory=$false)]
    [switch]$Clean,
    
    [Parameter(Mandatory=$false)]
    [switch]$Logs
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"

function Write-Log {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Error-Log {
    param([string]$Message)
    Write-Log "ERROR: $Message" -Color $Red
}

function Write-Success {
    param([string]$Message)
    Write-Log "SUCCESS: $Message" -Color $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Log "WARNING: $Message" -Color $Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Log "INFO: $Message" -Color $Blue
}

# Configuration
$ComposeFile = ""
switch ($Environment) {
    { $_ -in "development", "dev" } { $ComposeFile = "docker-compose.yml" }
    "staging" { $ComposeFile = "docker-compose.staging.yml" }
    { $_ -in "production", "prod" } { $ComposeFile = "docker-compose.prod.yml" }
    default { $ComposeFile = "docker-compose.yml" }
}

Write-Info "🚀 Starting Docker deployment for ACE Social Platform..."
Write-Info "📋 Environment: $Environment"
Write-Info "📄 Compose file: $ComposeFile"

# Pre-deployment checks
Write-Info "🔍 Running pre-deployment checks..."

# Check if Docker Desktop is running
try {
    $dockerInfo = docker info 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Error-Log "Docker Desktop is not running. Please start Docker Desktop first."
        exit 1
    }
    Write-Success "Docker Desktop is running"
} catch {
    Write-Error-Log "Docker is not installed or not accessible. Please install Docker Desktop."
    exit 1
}

# Check if docker-compose is available
try {
    docker-compose --version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Error-Log "docker-compose is not available. Please ensure Docker Desktop is properly installed."
        exit 1
    }
    Write-Success "docker-compose is available"
} catch {
    Write-Error-Log "docker-compose command failed."
    exit 1
}

# Check if compose file exists
if (-not (Test-Path $ComposeFile)) {
    Write-Error-Log "Compose file $ComposeFile not found."
    exit 1
}
Write-Success "Compose file found: $ComposeFile"

# Clean up if requested
if ($Clean) {
    Write-Info "🧹 Cleaning up existing containers and images..."
    docker-compose -f $ComposeFile down --volumes --remove-orphans
    docker system prune -f
    Write-Success "Cleanup completed"
}

# Environment setup for production
if ($Environment -in "production", "prod", "staging") {
    Write-Info "🔐 Setting up environment variables for $Environment..."
    
    # Set default environment variables if not already set
    if (-not $env:SECRET_KEY) {
        $env:SECRET_KEY = "aceo-production-secret-key-$(Get-Random)"
        Write-Warning "SECRET_KEY not set, using generated key"
    }
    
    if (-not $env:MONGODB_URL) {
        $env:MONGODB_URL = "***************************************************"
        Write-Warning "MONGODB_URL not set, using default"
    }
    
    if (-not $env:REDIS_URL) {
        $env:REDIS_URL = "redis://:redis123@redis:6379/0"
        Write-Warning "REDIS_URL not set, using default"
    }
    
    if (-not $env:JWT_SECRET_KEY) {
        $env:JWT_SECRET_KEY = "jwt-secret-$(Get-Random)"
        Write-Warning "JWT_SECRET_KEY not set, using generated key"
    }
    
    Write-Success "Environment variables configured"
}

# Build images if requested or if they don't exist
if ($Build) {
    Write-Info "🏗️ Building Docker images..."
    docker-compose -f $ComposeFile build --no-cache
    if ($LASTEXITCODE -ne 0) {
        Write-Error-Log "Docker build failed"
        exit 1
    }
    Write-Success "Docker images built successfully"
}

# Start services
Write-Info "🚀 Starting services..."
docker-compose -f $ComposeFile up -d
if ($LASTEXITCODE -ne 0) {
    Write-Error-Log "Failed to start services"
    exit 1
}

# Wait for services to start
Write-Info "⏳ Waiting for services to start..."
Start-Sleep -Seconds 30

# Health checks
Write-Info "🏥 Performing health checks..."

# Check backend health
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/health-minimal" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Success "Backend service is healthy"
    } else {
        Write-Warning "Backend service returned status: $($response.StatusCode)"
    }
} catch {
    Write-Warning "Backend service health check failed: $($_.Exception.Message)"
}

# Display service status
Write-Info "📊 Service Status:"
docker-compose -f $ComposeFile ps

# Display useful information
Write-Info "📋 Deployment Information:"
Write-Host ""
Write-Host "🌐 Application URLs:" -ForegroundColor $Cyan
Write-Host "   Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "   API Documentation: http://localhost:8000/docs" -ForegroundColor White
Write-Host "   Health Check: http://localhost:8000/health-minimal" -ForegroundColor White

if ($Environment -in "development", "dev") {
    Write-Host "   Frontend: http://localhost:3001" -ForegroundColor White
}

if ($Environment -in "production", "prod") {
    Write-Host "   Prometheus: http://localhost:9090" -ForegroundColor White
    Write-Host "   Grafana: http://localhost:3001" -ForegroundColor White
}

Write-Host ""
Write-Host "🔧 Useful Commands:" -ForegroundColor $Cyan
Write-Host "   View logs: docker-compose -f $ComposeFile logs -f" -ForegroundColor White
Write-Host "   Stop services: docker-compose -f $ComposeFile down" -ForegroundColor White
Write-Host "   Restart services: docker-compose -f $ComposeFile restart" -ForegroundColor White
Write-Host "   Execute backend shell: docker-compose -f $ComposeFile exec backend bash" -ForegroundColor White

# Show logs if requested
if ($Logs) {
    Write-Info "📜 Showing service logs..."
    docker-compose -f $ComposeFile logs -f
}

Write-Success "Docker deployment completed successfully!"
Write-Info "🎉 ACE Social Platform is now running in Docker Desktop!"

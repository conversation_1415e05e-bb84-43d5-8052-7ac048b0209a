/**
 * Enhanced Logger Utility for ACE Social Platform
 * 
 * Features:
 * - Environment-aware logging levels
 * - Analytics integration for production tracking
 * - Structured logging with context
 * - Performance monitoring integration
 * - Error tracking and reporting
 */

// Logger configuration
const CONFIG = {
  // Environment detection
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  
  // Log levels
  LEVELS: {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
    CRITICAL: 4
  },
  
  // Current log level (can be configured via environment)
  CURRENT_LEVEL: process.env.REACT_APP_LOG_LEVEL || 
                 (process.env.NODE_ENV === 'production' ? 'INFO' : 'DEBUG'),
  
  // Analytics integration
  ANALYTICS_ENABLED: process.env.REACT_APP_ANALYTICS_ENABLED !== 'false',
  
  // Performance tracking
  PERFORMANCE_TRACKING: process.env.REACT_APP_PERFORMANCE_TRACKING !== 'false'
};

/**
 * Enhanced Logger Class
 */
class Logger {
  constructor() {
    this.context = {};
    this.startTime = Date.now();
    this.logCount = {
      debug: 0,
      info: 0,
      warn: 0,
      error: 0,
      critical: 0
    };
  }

  /**
   * Set logging context for all subsequent logs
   * @param {Object} context - Context object
   */
  setContext(context) {
    this.context = { ...this.context, ...context };
  }

  /**
   * Clear logging context
   */
  clearContext() {
    this.context = {};
  }

  /**
   * Check if log level should be output
   * @private
   */
  _shouldLog(level) {
    const currentLevelNum = CONFIG.LEVELS[CONFIG.CURRENT_LEVEL.toUpperCase()] || CONFIG.LEVELS.INFO;
    const logLevelNum = CONFIG.LEVELS[level.toUpperCase()] || CONFIG.LEVELS.INFO;
    return logLevelNum >= currentLevelNum;
  }

  /**
   * Format log message with context
   * @private
   */
  _formatMessage(level, message, data = {}) {
    const timestamp = new Date().toISOString();
    const contextStr = Object.keys(this.context).length > 0 ? 
                      ` [${Object.entries(this.context).map(([k, v]) => `${k}:${v}`).join(', ')}]` : '';
    
    return {
      timestamp,
      level: level.toUpperCase(),
      message: `[AI Insights]${contextStr} ${message}`,
      data: { ...this.context, ...data },
      uptime: Date.now() - this.startTime
    };
  }

  /**
   * Send analytics event for production tracking
   * @private
   */
  _trackAnalytics(level, message, data) {
    if (!CONFIG.ANALYTICS_ENABLED || !CONFIG.IS_PRODUCTION) return;

    try {
      // Google Analytics 4 integration
      if (window.gtag) {
        window.gtag('event', 'ai_insights_log', {
          event_category: 'ai_insights',
          event_label: level,
          custom_parameters: {
            message: message.substring(0, 100), // Limit message length
            level,
            component: data.component || 'unknown',
            user_plan: data.user_plan || 'unknown'
          }
        });
      }

      // Custom analytics integration
      if (window.analytics && typeof window.analytics.track === 'function') {
        window.analytics.track('AI Insights Log Event', {
          level,
          message: message.substring(0, 100),
          component: data.component || 'unknown',
          timestamp: new Date().toISOString(),
          ...data
        });
      }
    } catch (error) {
      // Silently fail analytics to avoid breaking the application
      console.warn('Analytics tracking failed:', error);
    }
  }

  /**
   * Debug level logging
   */
  debug(message, data = {}) {
    if (!this._shouldLog('DEBUG')) return;

    this.logCount.debug++;
    const formatted = this._formatMessage('DEBUG', message, data);

    if (CONFIG.IS_DEVELOPMENT) {
      console.log(formatted.message, formatted.data);
    }
  }

  /**
   * Info level logging
   */
  info(message, data = {}) {
    if (!this._shouldLog('INFO')) return;

    this.logCount.info++;
    const formatted = this._formatMessage('INFO', message, data);

    console.info(formatted.message, formatted.data);
    this._trackAnalytics('info', message, data);
  }

  /**
   * Warning level logging
   */
  warn(message, data = {}) {
    if (!this._shouldLog('WARN')) return;

    this.logCount.warn++;
    const formatted = this._formatMessage('WARN', message, data);

    console.warn(formatted.message, formatted.data);
    this._trackAnalytics('warn', message, data);
  }

  /**
   * Error level logging
   */
  error(message, error = null, data = {}) {
    if (!this._shouldLog('ERROR')) return;

    this.logCount.error++;
    
    // Extract error information
    const errorData = error ? {
      errorName: error.name,
      errorMessage: error.message,
      errorStack: CONFIG.IS_DEVELOPMENT ? error.stack : undefined,
      ...data
    } : data;

    const formatted = this._formatMessage('ERROR', message, errorData);

    console.error(formatted.message, formatted.data);
    this._trackAnalytics('error', message, errorData);

    // Send error to error tracking service in production
    if (CONFIG.IS_PRODUCTION && error) {
      this._trackError(error, message, data);
    }
  }

  /**
   * Critical level logging
   */
  critical(message, error = null, data = {}) {
    this.logCount.critical++;
    
    const errorData = error ? {
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      ...data
    } : data;

    const formatted = this._formatMessage('CRITICAL', message, errorData);

    console.error('🚨 CRITICAL:', formatted.message, formatted.data);
    this._trackAnalytics('critical', message, errorData);

    // Always send critical errors to tracking
    if (error) {
      this._trackError(error, message, data);
    }
  }

  /**
   * Track errors to external service
   * @private
   */
  _trackError(error, message, data) {
    try {
      // Sentry integration (if available)
      if (window.Sentry && typeof window.Sentry.captureException === 'function') {
        window.Sentry.captureException(error, {
          tags: {
            component: 'ai_insights',
            level: 'error'
          },
          extra: {
            message,
            ...data
          }
        });
      }

      // Custom error tracking
      if (window.errorTracker && typeof window.errorTracker.track === 'function') {
        window.errorTracker.track(error, {
          component: 'ai_insights',
          message,
          ...data
        });
      }
    } catch (trackingError) {
      console.warn('Error tracking failed:', trackingError);
    }
  }

  /**
   * Performance timing utility
   */
  time(label) {
    if (!CONFIG.PERFORMANCE_TRACKING) return () => {};

    const startTime = performance.now();
    this.debug(`Timer started: ${label}`);

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.info(`Timer completed: ${label}`, {
        duration: Math.round(duration * 100) / 100,
        component: 'performance'
      });

      return duration;
    };
  }

  /**
   * Get logger statistics
   */
  getStats() {
    return {
      uptime: Date.now() - this.startTime,
      logCounts: { ...this.logCount },
      totalLogs: Object.values(this.logCount).reduce((sum, count) => sum + count, 0),
      context: { ...this.context },
      config: {
        level: CONFIG.CURRENT_LEVEL,
        analyticsEnabled: CONFIG.ANALYTICS_ENABLED,
        performanceTracking: CONFIG.PERFORMANCE_TRACKING,
        environment: process.env.NODE_ENV
      }
    };
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext) {
    const childLogger = new Logger();
    childLogger.context = { ...this.context, ...additionalContext };
    childLogger.startTime = this.startTime;
    childLogger.logCount = { ...this.logCount };
    return childLogger;
  }
}

// Create and export singleton logger instance
const logger = new Logger();

// Export both the instance and the class
export { logger, Logger };
export default logger;

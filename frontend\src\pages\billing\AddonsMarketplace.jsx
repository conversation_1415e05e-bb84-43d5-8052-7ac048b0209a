// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  Paper,
  Badge,
  Fab
} from '@mui/material';
import {
  Add as AddIcon,
  ShoppingCart as ShoppingCartIcon,
  Star as StarIcon,
  Analytics as AnalyticsIcon,
  AutoAwesome as AutoAwesomeIcon,
  Speed as SpeedIcon,
  Psychology as PsychologyIcon,
  Article as TemplateIcon,
  SentimentSatisfiedAlt as SentimentSatisfiedAltIcon,
  Assessment as AssessmentIcon,
  Storage as BatchPredictionIcon,
  Group as GroupIcon,
  Palette as BrandingWatermarkIcon,
  SupportAgent as SupportAgentIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';

import { useAuth } from '../../hooks/useAuth';
import { useNotification } from '../../hooks/useNotification';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useAddons } from '../../hooks/useAddons';
import ShoppingCart from '../../components/cart/ShoppingCart';
import api from '../../api';


const AddonsMarketplace = ({ isEmbedded = false, onError }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { subscription, featureLimits, usage } = useSubscription();
  const {
    catalog,
    error: addonsError
  } = useAddons();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [cart, setCart] = useState([]);
  const [cartOpen, setCartOpen] = useState(false);


  // Production-ready addons aligned with ACEO features
  const productionAddons = [
    // Content Enhancement Add-ons
    {
      id: 'extra-regeneration-credits',
      name: 'Extra Regeneration Credits',
      description: 'Boost your content optimization with additional regeneration credits for perfect posts',
      price: 9.99,
      category: 'Content Enhancement',
      icon: AutoAwesomeIcon,
      features: [
        '+100 regeneration credits per month',
        'Priority regeneration processing',
        'Advanced content optimization',
        'Style consistency improvements',
        'Rollover unused credits'
      ],
      is_popular: true,
      required_plan: 'creator',
      usage_increase: { regeneration_credits: 100 },
      business_value: 'Perfect your content with unlimited refinements'
    },
    {
      id: 'premium-content-templates',
      name: 'Premium Content Templates',
      description: 'Access exclusive, high-converting content templates designed by marketing experts',
      price: 14.99,
      category: 'Content Enhancement',
      icon: TemplateIcon,
      features: [
        '50+ premium templates',
        'Industry-specific templates',
        'Conversion-optimized formats',
        'Seasonal campaign templates',
        'A/B tested variations'
      ],
      is_popular: false,
      required_plan: 'creator',
      business_value: 'Save hours with proven, high-converting templates'
    },
    {
      id: 'ai-content-boost-pro',
      name: 'AI Content Boost Pro',
      description: 'Unlock advanced AI models and premium content generation capabilities',
      price: 24.99,
      category: 'Content Enhancement',
      icon: PsychologyIcon,
      features: [
        'GPT-4 Turbo access',
        'Claude 3 integration',
        'Advanced prompt engineering',
        'Multi-language content generation',
        'Brand voice fine-tuning',
        'Content personalization'
      ],
      is_popular: true,
      required_plan: 'accelerator',
      business_value: 'Generate premium content with cutting-edge AI'
    },

    // Analytics & Insights Add-ons
    {
      id: 'competitor-insights-pro',
      name: 'Competitor Insights Pro',
      description: 'Advanced competitor analysis with real-time tracking and strategic recommendations',
      price: 34.99,
      category: 'Analytics & Insights',
      icon: AnalyticsIcon,
      features: [
        'Track 20+ competitors',
        'Real-time performance alerts',
        'Content gap analysis',
        'Engagement trend predictions',
        'Strategic recommendations',
        'Competitive benchmarking reports'
      ],
      is_popular: true,
      required_plan: 'accelerator',
      usage_increase: { competitor_tracking: 20 },
      business_value: 'Stay ahead with comprehensive competitor intelligence'
    },
    {
      id: 'advanced-sentiment-analytics',
      name: 'Advanced Sentiment Analytics',
      description: 'Deep sentiment analysis with emotion detection and brand perception tracking',
      price: 19.99,
      category: 'Analytics & Insights',
      icon: SentimentSatisfiedAltIcon,
      features: [
        'Emotion detection (joy, anger, fear, etc.)',
        'Brand perception scoring',
        'Sentiment trend forecasting',
        'Crisis detection alerts',
        'Audience mood analysis',
        'Sentiment-driven content suggestions'
      ],
      is_popular: false,
      required_plan: 'accelerator',
      business_value: 'Understand audience emotions and protect brand reputation'
    },
    {
      id: 'custom-reporting-suite',
      name: 'Custom Reporting Suite',
      description: 'Create professional, branded reports with advanced analytics and insights',
      price: 29.99,
      category: 'Analytics & Insights',
      icon: AssessmentIcon,
      features: [
        'White-label report templates',
        'Custom branding options',
        'Automated report scheduling',
        'Client portal access',
        'PDF and PowerPoint export',
        'Interactive dashboards'
      ],
      is_popular: false,
      required_plan: 'dominator',
      business_value: 'Impress clients with professional, branded reports'
    },

    // Automation Add-ons
    {
      id: 'workflow-automation-pro',
      name: 'Workflow Automation Pro',
      description: 'Advanced automation workflows to streamline your content creation process',
      price: 39.99,
      category: 'Automation',
      icon: SpeedIcon,
      features: [
        'Custom workflow builder',
        'Multi-platform automation',
        'Conditional logic triggers',
        'Bulk content operations',
        'Auto-response management',
        'Performance-based scheduling'
      ],
      is_popular: true,
      required_plan: 'accelerator',
      business_value: 'Automate repetitive tasks and scale your content strategy'
    },
    {
      id: 'bulk-content-manager',
      name: 'Bulk Content Manager',
      description: 'Efficiently manage and schedule large volumes of content across platforms',
      price: 22.99,
      category: 'Automation',
      icon: BatchPredictionIcon,
      features: [
        'Bulk upload and editing',
        'CSV import/export',
        'Mass scheduling tools',
        'Batch optimization',
        'Content library management',
        'Duplicate detection'
      ],
      is_popular: false,
      required_plan: 'accelerator',
      usage_increase: { bulk_operations: 1000 },
      business_value: 'Handle large content volumes with ease'
    },

    // Team & Collaboration Add-ons
    {
      id: 'additional-team-seats',
      name: 'Additional Team Seats',
      description: 'Expand your team with additional user accounts and collaboration features',
      price: 15.99,
      category: 'Team & Collaboration',
      icon: GroupIcon,
      features: [
        '+5 additional team members',
        'Advanced role permissions',
        'Team activity tracking',
        'Collaborative workflows',
        'Shared content libraries',
        'Team performance analytics'
      ],
      is_popular: true,
      required_plan: 'accelerator',
      usage_increase: { team_members: 5 },
      business_value: 'Scale your team and improve collaboration'
    },
    {
      id: 'white-label-platform',
      name: 'White-Label Platform',
      description: 'Rebrand the platform with your logo and colors for client presentations',
      price: 99.99,
      category: 'Team & Collaboration',
      icon: BrandingWatermarkIcon,
      features: [
        'Custom branding and logos',
        'White-label client portals',
        'Custom domain support',
        'Branded email notifications',
        'Client onboarding flows',
        'Agency management tools'
      ],
      is_popular: false,
      required_plan: 'dominator',
      business_value: 'Present a professional, branded experience to clients'
    },

    // Support & Training Add-ons
    {
      id: 'priority-support-pro',
      name: 'Priority Support Pro',
      description: '24/7 dedicated support with guaranteed response times and phone access',
      price: 49.99,
      category: 'Support & Training',
      icon: SupportAgentIcon,
      features: [
        '24/7 priority support',
        '1-hour response guarantee',
        'Dedicated account manager',
        'Phone and video support',
        'Custom training sessions',
        'Implementation assistance'
      ],
      is_popular: false,
      required_plan: 'accelerator',
      business_value: 'Get expert help when you need it most'
    },
    {
      id: 'custom-onboarding',
      name: 'Custom Onboarding & Training',
      description: 'Personalized onboarding and training program for your team',
      price: 199.99,
      category: 'Support & Training',
      icon: SchoolIcon,
      features: [
        'Personalized onboarding plan',
        '1-on-1 training sessions',
        'Custom workflow setup',
        'Best practices consultation',
        'Team training workshops',
        '30-day success guarantee'
      ],
      is_popular: false,
      required_plan: 'dominator',
      business_value: 'Maximize ROI with expert guidance and training'
    }
  ];

  // Use the catalog from useAddons hook, fallback to production addons if needed
  const addons = catalog.length > 0 ? catalog : productionAddons;

  // Handle errors from the addons hook
  useEffect(() => {
    if (addonsError) {
      setError(addonsError);
      if (onError) onError(addonsError);
    }
  }, [addonsError, onError]);

  // Load cart from localStorage
  useEffect(() => {
    const savedCart = localStorage.getItem('addonCart');
    if (savedCart) {
      try {
        setCart(JSON.parse(savedCart));
      } catch (error) {
        console.error('Error loading cart:', error);
      }
    }
  }, []);

  // Save cart to localStorage
  useEffect(() => {
    localStorage.setItem('addonCart', JSON.stringify(cart));
  }, [cart]);

  const addToCart = (addon) => {
    const existingItem = cart.find(item => item.id === addon.id);

    if (existingItem) {
      setCart(cart.map(item =>
        item.id === addon.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      // Ensure all necessary fields are preserved when adding to cart
      const cartItem = {
        ...addon,
        quantity: 1,
        // Explicitly preserve price field for cart calculations
        price: addon.pricing?.basic?.price || addon.price
      };
      setCart([...cart, cartItem]);
      console.log('Added to cart:', cartItem); // Debug log
    }

    showSuccessNotification(`${addon.name} added to cart`);
  };



  const removeFromCart = (addonId) => {
    setCart(cart.filter(item => item.id !== addonId));
  };

  const updateCartQuantity = (addonId, quantity) => {
    if (quantity <= 0) {
      removeFromCart(addonId);
      return;
    }
    
    setCart(cart.map(item =>
      item.id === addonId
        ? { ...item, quantity }
        : item
    ));
  };

  // Format currency consistently
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getCartItemCount = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const isAddonAvailable = (addon) => {
    const userPlan = user?.subscription?.plan_id || 'creator';
    const planHierarchy = { creator: 1, accelerator: 2, dominator: 3 };

    return planHierarchy[userPlan] >= planHierarchy[addon.required_plan];
  };

  // Check if user already has similar functionality
  const isAddonRedundant = (addon) => {
    if (!subscription || !featureLimits) return false;

    // Check for redundant add-ons based on current plan features
    const redundancyChecks = {
      'extra-regeneration-credits': () => featureLimits.regeneration_credits >= 500,
      'additional-team-seats': () => featureLimits.user_accounts >= 20,
      'white-label-platform': () => featureLimits.white_label_ai_responses === true,
      'ai-content-boost-pro': () => featureLimits.multi_language_ai_support === true
    };

    const checkFunction = redundancyChecks[addon.id];
    return checkFunction ? checkFunction() : false;
  };

  // Get upgrade recommendation for addon
  const getUpgradeRecommendation = (addon) => {
    if (isAddonAvailable(addon)) return null;

    const userPlan = user?.subscription?.plan_id || 'creator';
    const requiredPlan = addon.required_plan;

    const planNames = {
      creator: 'Creator',
      accelerator: 'Accelerator',
      dominator: 'Dominator'
    };

    return {
      message: `Upgrade to ${planNames[requiredPlan]} plan to access this add-on`,
      requiredPlan,
      currentPlan: userPlan
    };
  };

  // Calculate potential usage increase
  const getUsageBenefit = (addon) => {
    if (!addon.usage_increase || !usage) return null;

    const benefits = [];
    Object.entries(addon.usage_increase).forEach(([key, increase]) => {
      const current = usage[`${key}_used`] || 0;
      const limit = featureLimits?.[key] || 0;
      const newLimit = limit + increase;

      benefits.push({
        feature: key.replace('_', ' '),
        current,
        currentLimit: limit,
        newLimit,
        increase
      });
    });

    return benefits;
  };

  const handleCheckout = async (cartItems, total) => {
    if (!cartItems || cartItems.length === 0) {
      showErrorNotification('Your cart is empty');
      return;
    }

    try {
      setLoading(true);

      // Prepare checkout data for Lemon Squeezy integration
      const checkoutData = {
        items: cartItems.map(item => ({
          addon_id: item.id,
          quantity: item.quantity,
          price: item.price
        })),
        total_amount: total,
        user_id: user?.id,
        subscription_id: subscription?.id
      };

      console.log('Checkout initiated:', checkoutData);
      showSuccessNotification('Initiating secure checkout...');

      // In production, this would create a Lemon Squeezy checkout session
      const response = await api.post('/api/billing/addons/checkout', checkoutData);

      if (response.data.checkout_url) {
        // Close cart drawer before redirecting to Lemon Squeezy
        setCartOpen(false);
        showSuccessNotification('Redirecting to secure payment...');

        // Redirect to Lemon Squeezy checkout
        window.location.href = response.data.checkout_url;
      } else {
        // Fallback: Show error and keep cart open for retry
        showErrorNotification('Checkout service temporarily unavailable. Please try again.');
      }

    } catch (error) {
      console.error('Checkout error:', error);
      showErrorNotification('Failed to initiate checkout. Please try again.');
      // Keep cart open so user can retry
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: isEmbedded ? 0 : 3 }}>
      {!isEmbedded && (
        <>
          <Helmet>
            <title>Add-ons Marketplace | ACEO</title>
            <meta name="description" content="Enhance your subscription with powerful add-ons" />
          </Helmet>
          
          <Box sx={{ textAlign: "center", mb: 4 }}>
            <Typography variant="h4" gutterBottom>
              Add-ons Marketplace
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ maxWidth: 700, mx: "auto" }}
            >
              Enhance your subscription with powerful add-ons designed to supercharge your content creation workflow
            </Typography>
          </Box>
        </>
      )}

      {/* Cart Summary */}
      {cart.length > 0 && (
        <Paper sx={{ p: 2, mb: 3, backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Cart ({getCartItemCount()} items) - {formatCurrency(getCartTotal())}
            </Typography>
            <Button
              variant="contained"
              onClick={() => setCartOpen(true)}
              startIcon={<ShoppingCartIcon />}
              sx={{ px: 3 }}
            >
              View Cart & Checkout
            </Button>
          </Box>
        </Paper>
      )}

      <Grid container spacing={3}>
        {addons.map((addon) => {
          const IconComponent = addon.icon;
          const isAvailable = isAddonAvailable(addon);
          const isInCart = cart.some(item => item.id === addon.id);
          const isRedundant = isAddonRedundant(addon);
          const upgradeRecommendation = getUpgradeRecommendation(addon);
          const usageBenefits = getUsageBenefit(addon);

          return (
            <Grid item xs={12} sm={6} md={4} key={addon.id}>
              <Card
                elevation={3}
                sx={{
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  position: "relative",
                  opacity: isAvailable ? 1 : 0.7,
                  transition: "transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out",
                  "&:hover": isAvailable ? {
                    transform: "translateY(-4px)",
                    boxShadow: 6,
                  } : {},
                  ...(addon.is_popular && {
                    border: `2px solid ${theme.palette.primary.main}`,
                  }),
                }}
              >
                {addon.is_popular && (
                  <Box
                    sx={{
                      position: "absolute",
                      top: -12,
                      left: "50%",
                      transform: "translateX(-50%)",
                      zIndex: 1,
                    }}
                  >
                    <Chip
                      label="Popular"
                      color="primary"
                      size="small"
                      icon={<StarIcon />}
                      sx={{ fontWeight: "bold", boxShadow: 2 }}
                    />
                  </Box>
                )}

                {!isAvailable && (
                  <Box
                    sx={{
                      position: "absolute",
                      top: 16,
                      right: 16,
                      zIndex: 1,
                    }}
                  >
                    <Chip
                      label={upgradeRecommendation ? `${upgradeRecommendation.requiredPlan} Required` : "Upgrade Required"}
                      color="warning"
                      size="small"
                      sx={{ fontWeight: "bold" }}
                    />
                  </Box>
                )}

                {isRedundant && (
                  <Box
                    sx={{
                      position: "absolute",
                      top: 16,
                      left: 16,
                      zIndex: 1,
                    }}
                  >
                    <Chip
                      label="Already Included"
                      color="success"
                      size="small"
                      sx={{ fontWeight: "bold" }}
                    />
                  </Box>
                )}

                <CardContent sx={{ flexGrow: 1, pt: addon.is_popular ? 4 : 3 }}>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: 1,
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        mr: 2,
                      }}
                    >
                      <IconComponent />
                    </Box>
                    <Box>
                      <Typography variant="h6" component="h3" fontWeight="bold">
                        {addon.name}
                      </Typography>
                      <Chip
                        label={addon.category}
                        size="small"
                        variant="outlined"
                        sx={{ mt: 0.5 }}
                      />
                    </Box>
                  </Box>

                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mb: 2, minHeight: 40 }}
                  >
                    {addon.description}
                  </Typography>

                  <Typography
                    variant="h5"
                    component="div"
                    fontWeight="bold"
                    color="primary"
                    sx={{ mb: 2 }}
                  >
                    {formatCurrency(addon.price)}
                    <Typography
                      variant="body2"
                      component="span"
                      color="text.secondary"
                      sx={{ ml: 1 }}
                    >
                      /month
                    </Typography>
                  </Typography>

                  {/* Business Value */}
                  {addon.business_value && (
                    <Box sx={{ mb: 2, p: 1.5, backgroundColor: alpha(theme.palette.success.main, 0.1), borderRadius: 1 }}>
                      <Typography variant="caption" color="success.main" fontWeight="bold">
                        💡 Business Value
                      </Typography>
                      <Typography variant="body2" color="text.primary" sx={{ mt: 0.5 }}>
                        {addon.business_value}
                      </Typography>
                    </Box>
                  )}

                  {/* Usage Benefits */}
                  {usageBenefits && usageBenefits.length > 0 && (
                    <Box sx={{ mb: 2, p: 1.5, backgroundColor: alpha(theme.palette.primary.main, 0.1), borderRadius: 1 }}>
                      <Typography variant="caption" color="primary.main" fontWeight="bold">
                        📈 Usage Increase
                      </Typography>
                      {usageBenefits.map((benefit, index) => (
                        <Typography key={index} variant="body2" color="text.primary" sx={{ mt: 0.5 }}>
                          {benefit.feature}: {benefit.currentLimit} → {benefit.newLimit} (+{benefit.increase})
                        </Typography>
                      ))}
                    </Box>
                  )}

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Features:
                    </Typography>
                    {addon.features.slice(0, 3).map((feature, index) => (
                      <Typography
                        key={index}
                        variant="body2"
                        color="text.secondary"
                        sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}
                      >
                        • {feature}
                      </Typography>
                    ))}
                    {addon.features.length > 3 && (
                      <Typography variant="caption" color="text.secondary">
                        +{addon.features.length - 3} more features
                      </Typography>
                    )}
                  </Box>
                </CardContent>

                <CardActions sx={{ p: 2, pt: 0 }}>
                  {isRedundant ? (
                    <Button
                      variant="outlined"
                      fullWidth
                      size="large"
                      disabled
                      sx={{ py: 1.5, color: 'success.main', borderColor: 'success.main' }}
                    >
                      Already Included in Your Plan
                    </Button>
                  ) : isAvailable ? (
                    <Button
                      variant={isInCart ? "outlined" : "contained"}
                      fullWidth
                      size="large"
                      startIcon={isInCart ? <ShoppingCartIcon /> : <AddIcon />}
                      onClick={() => addToCart(addon)}
                      sx={{ py: 1.5, fontWeight: "bold" }}
                    >
                      {isInCart ? 'In Cart' : 'Add to Cart'}
                    </Button>
                  ) : (
                    <Box sx={{ width: '100%' }}>
                      {upgradeRecommendation && (
                        <Typography variant="caption" color="warning.main" sx={{ display: 'block', mb: 1, textAlign: 'center' }}>
                          {upgradeRecommendation.message}
                        </Typography>
                      )}
                      <Button
                        variant="outlined"
                        fullWidth
                        size="large"
                        onClick={() => navigate('/billing/plans')}
                        sx={{ py: 1.5, color: 'warning.main', borderColor: 'warning.main' }}
                      >
                        Upgrade Plan
                      </Button>
                    </Box>
                  )}
                </CardActions>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Floating Cart Button */}
      {cart.length > 0 && (
        <Fab
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: 1000,
          }}
          onClick={() => setCartOpen(true)}
        >
          <Badge badgeContent={getCartItemCount()} color="error">
            <ShoppingCartIcon />
          </Badge>
        </Fab>
      )}

      {/* Shopping Cart Drawer */}
      <ShoppingCart
        open={cartOpen}
        onClose={() => setCartOpen(false)}
        cart={cart}
        onUpdateQuantity={updateCartQuantity}
        onRemoveItem={removeFromCart}
        onClearCart={() => setCart([])}
        onCheckout={handleCheckout}
        loading={loading}
      />
    </Box>
  );
};

export default AddonsMarketplace;

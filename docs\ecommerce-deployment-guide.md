# E-commerce Integration Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the e-commerce integration feature to production, following the 5-phase deployment approach with comprehensive testing and monitoring.

## Pre-Deployment Checklist

### Code Quality
- [ ] All Pylance errors resolved
- [ ] Unit tests passing (>90% coverage)
- [ ] Integration tests passing
- [ ] Frontend tests passing
- [ ] Security audit completed
- [ ] Performance testing completed

### Infrastructure
- [ ] MongoDB collections indexed
- [ ] Redis caching configured
- [ ] Background job processing setup
- [ ] Webhook endpoints configured
- [ ] SSL certificates valid
- [ ] CDN configuration updated

### Configuration
- [ ] Environment variables set
- [ ] Feature flags configured
- [ ] Add-on definitions loaded
- [ ] API rate limits configured
- [ ] Monitoring alerts setup

## Phase 1: Environment Setup

### 1.1 Database Preparation

#### MongoDB Collections
Create and index the required collections:

```javascript
// Create collections
db.createCollection("ecommerce_stores")
db.createCollection("synced_products")
db.createCollection("ecommerce_sync_logs")
db.createCollection("ecommerce_webhooks")
db.createCollection("ecommerce_analytics")

// Create indexes for performance
db.ecommerce_stores.createIndex({ "user_id": 1 })
db.ecommerce_stores.createIndex({ "platform": 1, "status": 1 })
db.ecommerce_stores.createIndex({ "last_sync_at": 1 })

db.synced_products.createIndex({ "user_id": 1, "store_id": 1 })
db.synced_products.createIndex({ "external_product_id": 1, "platform": 1 })
db.synced_products.createIndex({ "category": 1 })
db.synced_products.createIndex({ "tags": 1 })
db.synced_products.createIndex({ "last_synced_at": 1 })

db.ecommerce_sync_logs.createIndex({ "user_id": 1, "store_id": 1 })
db.ecommerce_sync_logs.createIndex({ "started_at": 1 })
db.ecommerce_sync_logs.createIndex({ "status": 1 })

db.ecommerce_webhooks.createIndex({ "store_id": 1 })
db.ecommerce_webhooks.createIndex({ "received_at": 1 })
db.ecommerce_webhooks.createIndex({ "status": 1 })
```

#### Add-on Configuration
Load e-commerce add-on definitions:

```javascript
db.addon_catalog.insertMany([
  {
    "addon_id": "ecommerce_store_connections",
    "name": "E-commerce Store Connections",
    "description": "Connect multiple e-commerce stores",
    "category": "ecommerce",
    "base_price": 19.99,
    "currency": "USD",
    "features": ["store_connections", "product_sync", "webhook_support"],
    "default_credits": 5,
    "is_active": true
  },
  {
    "addon_id": "product_content_generation",
    "name": "Product Content Generation Credits",
    "description": "AI-powered content generation for products",
    "category": "ecommerce",
    "base_price": 29.99,
    "currency": "USD",
    "features": ["product_content_generation", "product_image_generation"],
    "default_credits": 100,
    "is_active": true
  },
  {
    "addon_id": "ecommerce_icp_generation",
    "name": "E-commerce ICP Generation",
    "description": "Generate customer personas from product data",
    "category": "ecommerce",
    "base_price": 39.99,
    "currency": "USD",
    "features": ["ecommerce_icp_generation", "product_analysis"],
    "default_credits": 50,
    "is_active": true
  },
  {
    "addon_id": "ecommerce_campaigns",
    "name": "E-commerce Campaign Management",
    "description": "Advanced campaign management for products",
    "category": "ecommerce",
    "base_price": 49.99,
    "currency": "USD",
    "features": ["ecommerce_campaigns", "product_targeting"],
    "default_credits": 25,
    "is_active": true
  }
])
```

### 1.2 Environment Configuration

#### Backend Environment Variables
```bash
# E-commerce Integration
ECOMMERCE_INTEGRATION_ENABLED=true

# Shopify Configuration
SHOPIFY_API_KEY=your_production_shopify_api_key
SHOPIFY_API_SECRET=your_production_shopify_api_secret

# Webhook Configuration
WEBHOOK_BASE_URL=https://your-domain.com
WEBHOOK_SECRET_KEY=your_webhook_secret

# Background Processing
SYNC_SCHEDULER_ENABLED=true
SYNC_INTERVAL_SECONDS=3600
MAX_CONCURRENT_SYNCS=5

# Rate Limiting
SHOPIFY_RATE_LIMIT_PER_SECOND=2
WOOCOMMERCE_RATE_LIMIT_PER_SECOND=10

# Monitoring
ECOMMERCE_METRICS_ENABLED=true
```

#### Frontend Environment Variables
```bash
# Feature Flags
REACT_APP_ECOMMERCE_ENABLED=true
REACT_APP_ECOMMERCE_PLATFORMS=shopify,woocommerce

# API Configuration
REACT_APP_API_BASE_URL=https://api.your-domain.com
```

### 1.3 Infrastructure Setup

#### Load Balancer Configuration
```nginx
# Add e-commerce webhook endpoints
location /api/ecommerce/webhooks/ {
    proxy_pass http://backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Increase timeout for webhook processing
    proxy_read_timeout 30s;
    proxy_connect_timeout 10s;
}
```

#### Background Job Processing
```yaml
# Docker Compose addition for sync scheduler
ecommerce-sync-scheduler:
  build: ./backend
  command: python -m app.services.ecommerce_sync_scheduler
  environment:
    - MONGODB_URL=${MONGODB_URL}
    - REDIS_URL=${REDIS_URL}
    - SYNC_SCHEDULER_ENABLED=true
  depends_on:
    - mongodb
    - redis
  restart: unless-stopped
```

## Phase 2: Load Testing

### 2.1 Test Scenarios

#### Store Connection Load Test
```python
# Test concurrent store connections
import asyncio
import aiohttp

async def test_store_connections(concurrent_users=100):
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(concurrent_users):
            task = asyncio.create_task(
                connect_test_store(session, f"test-store-{i}")
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        
        print(f"Store connections: {success_count}/{concurrent_users} successful")
        return success_count / concurrent_users >= 0.95  # 95% success rate
```

#### Product Sync Load Test
```python
# Test product synchronization under load
async def test_product_sync_load(stores=50, products_per_store=1000):
    start_time = time.time()
    
    # Simulate concurrent product syncs
    tasks = []
    for store_id in range(stores):
        task = asyncio.create_task(
            sync_store_products(f"store-{store_id}", products_per_store)
        )
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Verify performance requirements
    assert duration < 300  # Complete within 5 minutes
    assert all(not isinstance(r, Exception) for r in results)
    
    print(f"Synced {stores * products_per_store} products in {duration:.2f}s")
```

#### Content Generation Load Test
```python
# Test AI content generation performance
async def test_content_generation_load(concurrent_requests=200):
    async with aiohttp.ClientSession() as session:
        start_time = time.time()
        
        tasks = []
        for i in range(concurrent_requests):
            task = asyncio.create_task(
                generate_product_content(session, f"product-{i}")
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        avg_response_time = (end_time - start_time) / concurrent_requests
        
        # Verify response time requirement
        assert avg_response_time < 2.0  # <2 second average response time
        
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        assert success_count / concurrent_requests >= 0.95
```

### 2.2 Performance Benchmarks

Target performance metrics:
- **Store Connections**: 100 concurrent connections, 95% success rate
- **Product Sync**: 1000+ products in <200ms average response time
- **Content Generation**: 200 concurrent requests, <2s average response time
- **Database Queries**: <100ms for product searches
- **Memory Usage**: <2GB per service instance
- **CPU Usage**: <70% under normal load

## Phase 3: Security Audit

### 3.1 Security Checklist

#### Authentication & Authorization
- [ ] OAuth flows properly implemented
- [ ] Webhook signature verification
- [ ] API rate limiting enabled
- [ ] User data isolation verified
- [ ] Feature access controls tested

#### Data Protection
- [ ] Credential encryption verified
- [ ] PII data handling compliant
- [ ] Database access restricted
- [ ] API endpoint security tested
- [ ] HTTPS enforcement verified

#### Vulnerability Assessment
```bash
# Run security scans
npm audit --audit-level moderate
safety check --json
bandit -r backend/app/services/ecommerce/
```

### 3.2 Penetration Testing

#### API Security Testing
```python
# Test API endpoint security
def test_api_security():
    # Test unauthorized access
    response = requests.get("/api/ecommerce/stores")
    assert response.status_code == 401
    
    # Test SQL injection protection
    response = requests.get("/api/ecommerce/stores?id='; DROP TABLE users; --")
    assert response.status_code != 500
    
    # Test XSS protection
    response = requests.post("/api/ecommerce/stores/connect", 
                           json={"store_name": "<script>alert('xss')</script>"})
    assert "<script>" not in response.text
```

## Phase 4: Gradual Rollout

### 4.1 Phase 4a: Creator Plan Users (25%)

#### Feature Flag Configuration
```python
# Enable for Creator plan users only
ECOMMERCE_ROLLOUT_CONFIG = {
    "enabled_plans": ["creator"],
    "rollout_percentage": 25,
    "max_stores_per_user": 3,
    "max_products_per_store": 1000
}
```

#### Monitoring Setup
```python
# Monitor key metrics during rollout
ROLLOUT_METRICS = [
    "ecommerce.store_connections.count",
    "ecommerce.sync.success_rate",
    "ecommerce.content_generation.usage",
    "ecommerce.api.response_time",
    "ecommerce.errors.rate"
]
```

### 4.2 Phase 4b: Accelerator/Dominator Plans (50%)

#### Expanded Rollout
```python
# Expand to higher-tier plans
ECOMMERCE_ROLLOUT_CONFIG = {
    "enabled_plans": ["creator", "accelerator", "dominator"],
    "rollout_percentage": 50,
    "max_stores_per_user": 10,
    "max_products_per_store": 5000
}
```

### 4.3 Phase 4c: Full Rollout (100%)

#### Complete Deployment
```python
# Enable for all plans
ECOMMERCE_ROLLOUT_CONFIG = {
    "enabled_plans": ["all"],
    "rollout_percentage": 100,
    "max_stores_per_user": 25,
    "max_products_per_store": 10000
}
```

## Phase 5: Ongoing Monitoring

### 5.1 Monitoring Dashboard

#### Key Metrics
- Store connection success rate
- Product sync performance
- Content generation usage
- API response times
- Error rates by endpoint
- User adoption metrics
- Revenue impact

#### Alerting Rules
```yaml
# Prometheus alerting rules
groups:
  - name: ecommerce
    rules:
      - alert: EcommerceHighErrorRate
        expr: rate(ecommerce_errors_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate in e-commerce integration"
      
      - alert: EcommerceSyncFailure
        expr: rate(ecommerce_sync_failures_total[10m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High sync failure rate"
      
      - alert: EcommerceSlowResponse
        expr: histogram_quantile(0.95, rate(ecommerce_request_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Slow e-commerce API responses"
```

### 5.2 Rollback Procedures

#### 4-Level Rollback Strategy

**Level 1: Feature Flag Disable**
```python
# Immediate rollback via feature flag
ECOMMERCE_INTEGRATION_ENABLED = False
```

**Level 2: Traffic Routing**
```nginx
# Route e-commerce traffic to maintenance page
location /api/ecommerce/ {
    return 503 "E-commerce features temporarily unavailable";
}
```

**Level 3: Service Isolation**
```yaml
# Disable e-commerce services
ecommerce-service:
  deploy:
    replicas: 0
```

**Level 4: Database Rollback**
```javascript
// Rollback database changes if necessary
db.ecommerce_stores.updateMany({}, {$set: {status: "maintenance"}})
```

#### Automatic Rollback Triggers
- Error rate >5% for 5 minutes
- Response time >5 seconds for 3 minutes
- Sync failure rate >20% for 10 minutes
- Memory usage >90% for 5 minutes

### 5.3 Success Metrics

#### Target KPIs
- **Conversion Rate**: 15% of eligible users connect stores
- **Revenue Increase**: 40% increase in add-on revenue
- **Performance**: <200ms average response time
- **Reliability**: <5% error rate
- **Support Impact**: <5% increase in support tickets

#### Monitoring Schedule
- **First 24 hours**: Continuous monitoring
- **First week**: Hourly metric reviews
- **First month**: Daily performance reports
- **Ongoing**: Weekly business metric reviews

## Post-Deployment

### Documentation Updates
- [ ] API documentation updated
- [ ] User guides published
- [ ] Support team trained
- [ ] Knowledge base updated

### Feedback Collection
- [ ] User feedback surveys
- [ ] Support ticket analysis
- [ ] Performance metric review
- [ ] Business impact assessment

### Continuous Improvement
- [ ] Performance optimization
- [ ] Feature enhancement planning
- [ ] User experience improvements
- [ ] Platform expansion roadmap

## Troubleshooting

### Common Deployment Issues

#### Database Connection Issues
```bash
# Check MongoDB connectivity
mongosh --eval "db.adminCommand('ping')"

# Verify indexes
mongosh --eval "db.ecommerce_stores.getIndexes()"
```

#### Service Discovery Issues
```bash
# Check service health
curl -f http://localhost:8000/health/ecommerce

# Verify environment variables
env | grep ECOMMERCE
```

#### Performance Issues
```bash
# Monitor resource usage
docker stats

# Check application logs
docker logs -f ecommerce-service
```

### Emergency Contacts
- **Technical Lead**: [contact information]
- **DevOps Team**: [contact information]
- **Product Manager**: [contact information]
- **On-call Engineer**: [contact information]

/**
 * Enhanced ACE Social Revenue Analytics - Enterprise-grade comprehensive revenue analytics component
 * Features: Comprehensive revenue analytics with advanced revenue tracking, revenue forecasting models,
 * and revenue segmentation analysis for ACE Social financial management, detailed revenue analytics
 * dashboard with real-time revenue visualization and revenue trend analysis, advanced analytics
 * features with interactive revenue charts and revenue drill-down capabilities, ACE Social's
 * financial system integration with seamless data aggregation from financial dashboard and payment
 * monitoring, analytics interaction features including real-time revenue alerts and revenue
 * filtering capabilities, analytics state management with revenue data caching and real-time
 * revenue updates, and real-time revenue monitoring with live revenue tracking and automated
 * revenue anomaly detection
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  Stack,
  Paper,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  ScatterChart,
  Scatter,
  ComposedChart
} from 'recharts';
import {
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  FilterList as FilterIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Analytics as AnalyticsIcon,
  MonetizationOn as MoneyIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';

// API
import api from '../../api';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Revenue analytics constants
const ANALYTICS_VIEWS = {
  OVERVIEW: 'overview',
  TRENDS: 'trends',
  SEGMENTATION: 'segmentation',
  FORECASTING: 'forecasting',
  COMPARISON: 'comparison'
};

const TIME_PERIODS = {
  LAST_7_DAYS: 'last_7_days',
  LAST_30_DAYS: 'last_30_days',
  LAST_90_DAYS: 'last_90_days',
  LAST_YEAR: 'last_year',
  CUSTOM: 'custom'
};

const TIMEFRAMES = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  YEARLY: 'yearly'
};

const REVENUE_TYPES = {
  TOTAL: 'total',
  SUBSCRIPTION: 'subscription',
  ADDON: 'addon',
  APPSUMO: 'appsumo',
  UPGRADE: 'upgrade'
};

// Revenue analytics events
const ANALYTICS_EVENTS = {
  VIEW_CHANGED: 'revenue_analytics_view_changed',
  METRIC_CLICKED: 'revenue_metric_clicked',
  DATA_EXPORTED: 'revenue_data_exported',
  FILTER_APPLIED: 'revenue_filter_applied',
  FORECAST_GENERATED: 'revenue_forecast_generated',
  COMPARISON_TOGGLED: 'revenue_comparison_toggled'
};

/**
 * Enhanced Revenue Analytics - Comprehensive revenue analytics with advanced features
 * Implements detailed revenue analytics management and enterprise-grade analytics capabilities
 */

const EnhancedRevenueAnalytics = memo(forwardRef(({
  dateRange = {
    start: startOfMonth(subMonths(new Date(), 1)),
    end: endOfMonth(subMonths(new Date(), 1))
  },
  onDateRangeChange,
  data = {},
  loading = false,
  error,
  className,
  enableAdvancedFeatures = true,
  enableRealTimeUpdates = true,
  enableForecasting = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableExportOptions = true,
  enableInteractiveCharts = true,
  defaultView = ANALYTICS_VIEWS.OVERVIEW,
  defaultTimePeriod = TIME_PERIODS.LAST_30_DAYS,
  autoRefreshInterval = 300000, // 5 minutes
  maxDataPoints = 1000,
  onViewChange,
  onMetricClick,
  onDataExport,
  onAnalyticsTrack,
  onForecastGenerate,
  onRefresh,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const analyticsRef = useRef(null);
  const chartRefs = useRef({});
  const autoRefreshTimeoutRef = useRef(null);

  // Core state management
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [analyticsError, setAnalyticsError] = useState(null);
  const [metrics, setMetrics] = useState(null);
  const [timeframe, setTimeframe] = useState(TIMEFRAMES.MONTHLY);
  const [currentView, setCurrentView] = useState(defaultView);

  // Enhanced state management
  const [revenueData, setRevenueData] = useState([]);
  const [forecastData, setForecastData] = useState([]);
  const [comparisonData, setComparisonData] = useState([]);
  const [analyticsState, setAnalyticsState] = useState({
    lastUpdated: null,
    viewChanges: 0,
    metricClicks: 0,
    dataExports: 0,
    forecastsGenerated: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    refreshAnalytics: () => handleRefresh(),
    exportData: () => handleExport(),
    resetFilters: () => handleResetFilters(),
    focusAnalytics: () => analyticsRef.current?.focus(),

    // Navigation methods
    changeView: (view) => handleViewChange(view),
    changeTimeframe: (frame) => handleTimeframeChange(frame),
    setDateRange: (range) => handleDateRangeChange(range),

    // Data methods
    getMetrics: () => metrics,
    getRevenueData: () => revenueData,
    getForecastData: () => forecastData,
    getAnalyticsState: () => analyticsState,

    // State methods
    isLoading: () => analyticsLoading,
    hasError: () => !!analyticsError,
    getCurrentView: () => currentView,

    // Chart methods
    focusChart: (chartType) => chartRefs.current[chartType]?.focus(),
    downloadChart: (chartType) => downloadChartData(chartType),

    // Analytics methods
    generateForecast: () => handleGenerateForecast(),
    getRevenueInsights: () => generateRevenueInsights(),

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    generateReport: () => generateAnalyticsReport(),
    optimizeCharts: () => optimizeChartLayout()
  }), [
    metrics,
    revenueData,
    forecastData,
    analyticsState,
    analyticsLoading,
    analyticsError,
    currentView,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(ANALYTICS_EVENTS.VIEW_CHANGED, {
        view: currentView,
        timestamp: new Date().toISOString(),
        dataPoints: Object.keys(metrics || {}).length
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Revenue analytics view changed to ${currentView}`);
    }
  }, [currentView, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader, metrics]);

  // Auto-refresh effect
  useEffect(() => {
    if (enableRealTimeUpdates && autoRefreshInterval > 0) {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }

      autoRefreshTimeoutRef.current = setTimeout(() => {
        if (onRefresh) {
          onRefresh();
        } else {
          loadRevenueMetrics();
        }
      }, autoRefreshInterval);

      return () => {
        if (autoRefreshTimeoutRef.current) {
          clearTimeout(autoRefreshTimeoutRef.current);
        }
      };
    }
  }, [enableRealTimeUpdates, autoRefreshInterval, onRefresh]);

  // Initialize data loading
  useEffect(() => {
    if (dateRange.start && dateRange.end) {
      loadRevenueMetrics();
    }
  }, [dateRange]);

  // Enhanced handler functions
  const handleViewChange = useCallback((newView) => {
    setCurrentView(newView);

    setAnalyticsState(prev => ({
      ...prev,
      viewChanges: prev.viewChanges + 1
    }));

    if (onViewChange) {
      onViewChange(newView);
    }

    if (enableAccessibility) {
      announceToScreenReader(`Switched to ${newView} view`);
    }
  }, [onViewChange, enableAccessibility, announceToScreenReader]);

  const handleTimeframeChange = useCallback((newTimeframe) => {
    setTimeframe(newTimeframe);

    if (enableAccessibility) {
      announceToScreenReader(`Timeframe changed to ${newTimeframe}`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleDateRangeChange = useCallback((field, value) => {
    const newRange = typeof field === 'object' ? field : { ...dateRange, [field]: value };

    if (onDateRangeChange) {
      onDateRangeChange(newRange);
    }

    if (enableAccessibility) {
      announceToScreenReader('Date range updated');
    }
  }, [dateRange, onDateRangeChange, enableAccessibility, announceToScreenReader]);

  const handleMetricClick = useCallback((metricType, value) => {
    setAnalyticsState(prev => ({
      ...prev,
      metricClicks: prev.metricClicks + 1
    }));

    if (onMetricClick) {
      onMetricClick(metricType, value);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(ANALYTICS_EVENTS.METRIC_CLICKED, {
        metricType,
        value,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Clicked ${metricType} metric`);
    }
  }, [onMetricClick, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleRefresh = useCallback(() => {
    setAnalyticsLoading(true);
    setAnalyticsError(null);

    setAnalyticsState(prev => ({
      ...prev,
      lastUpdated: new Date().toISOString()
    }));

    if (onRefresh) {
      onRefresh();
    } else {
      loadRevenueMetrics();
    }

    if (enableAccessibility) {
      announceToScreenReader('Revenue analytics data refreshed');
    }
  }, [onRefresh, enableAccessibility, announceToScreenReader]);

  const handleExport = useCallback(() => {
    if (!enableExportOptions) return;

    const exportData = {
      metrics,
      revenueData,
      forecastData,
      dateRange,
      timeframe,
      exportedAt: new Date().toISOString()
    };

    if (onDataExport) {
      onDataExport(exportData);
    }

    setAnalyticsState(prev => ({
      ...prev,
      dataExports: prev.dataExports + 1
    }));

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(ANALYTICS_EVENTS.DATA_EXPORTED, {
        dataPoints: Object.keys(metrics || {}).length,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Revenue analytics data exported successfully');
    }
  }, [enableExportOptions, metrics, revenueData, forecastData, dateRange, timeframe, onDataExport, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleResetFilters = useCallback(() => {
    setTimeframe(TIMEFRAMES.MONTHLY);
    setCurrentView(defaultView);

    const defaultRange = {
      start: startOfMonth(subMonths(new Date(), 1)),
      end: endOfMonth(subMonths(new Date(), 1))
    };

    if (onDateRangeChange) {
      onDateRangeChange(defaultRange);
    }

    if (enableAccessibility) {
      announceToScreenReader('All filters reset');
    }
  }, [defaultView, onDateRangeChange, enableAccessibility, announceToScreenReader]);

  const loadRevenueMetrics = useCallback(async () => {
    try {
      setAnalyticsLoading(true);
      setAnalyticsError(null);

      const response = await api.get('/api/admin/finance/revenue/metrics', {
        params: {
          period_start: dateRange.start.toISOString(),
          period_end: dateRange.end.toISOString(),
        },
      });

      setMetrics(response.data);

      // Generate mock revenue data for charts
      const mockRevenueData = generateMockRevenueData(response.data);
      setRevenueData(mockRevenueData);

      if (enableAccessibility) {
        announceToScreenReader('Revenue metrics loaded successfully');
      }
    } catch (err) {
      console.error('Error loading revenue metrics:', err);
      setAnalyticsError(err.response?.data?.detail || 'Failed to load revenue metrics');

      if (enableAccessibility) {
        announceToScreenReader('Failed to load revenue metrics');
      }
    } finally {
      setAnalyticsLoading(false);
    }
  }, [dateRange, enableAccessibility, announceToScreenReader]);

  // Enhanced utility functions
  const generateMockRevenueData = useCallback((metricsData) => {
    // Generate mock data for demonstration
    const data = [];
    const months = 12;

    for (let i = 0; i < months; i++) {
      const date = subMonths(new Date(), months - i - 1);
      data.push({
        month: format(date, 'MMM yyyy'),
        total: Math.random() * 50000 + 20000,
        subscription: Math.random() * 30000 + 15000,
        addon: Math.random() * 10000 + 3000,
        appsumo: Math.random() * 5000 + 1000
      });
    }

    return data;
  }, []);

  const generateRevenueInsights = useCallback(() => {
    if (!metrics) return [];

    const insights = [];

    // Calculate key insights
    const totalRevenue = metrics.total_revenue || 0;
    const growthRate = metrics.growth_rate || 0;
    const subscriptionRevenue = metrics.subscription_revenue || 0;

    insights.push({
      type: 'growth',
      title: 'Revenue Growth',
      value: formatPercentage(growthRate),
      trend: growthRate >= 0 ? 'positive' : 'negative',
      recommendation: growthRate < 0.05 ? 'Focus on customer acquisition' : 'Maintain current strategy'
    });

    insights.push({
      type: 'subscription_ratio',
      title: 'Subscription Revenue Ratio',
      value: formatPercentage(subscriptionRevenue / totalRevenue),
      trend: (subscriptionRevenue / totalRevenue) >= 0.8 ? 'positive' : 'neutral',
      recommendation: (subscriptionRevenue / totalRevenue) < 0.8 ? 'Increase subscription focus' : 'Excellent subscription ratio'
    });

    return insights;
  }, [metrics]);

  const handleGenerateForecast = useCallback(() => {
    if (!enableForecasting) return;

    const currentRevenue = metrics?.total_revenue || 0;
    const growthRate = metrics?.growth_rate || 0.05;

    const forecast = [];
    for (let i = 1; i <= 12; i++) {
      const projectedRevenue = currentRevenue * Math.pow(1 + growthRate, i);
      forecast.push({
        month: format(new Date(Date.now() + i * 30 * 24 * 60 * 60 * 1000), 'MMM yyyy'),
        projected_revenue: projectedRevenue,
        confidence: Math.max(0.5, 1 - (i * 0.05))
      });
    }

    setForecastData(forecast);
    setAnalyticsState(prev => ({
      ...prev,
      forecastsGenerated: prev.forecastsGenerated + 1
    }));

    if (onForecastGenerate) {
      onForecastGenerate(forecast);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(ANALYTICS_EVENTS.FORECAST_GENERATED, {
        forecastPeriod: 12,
        timestamp: new Date().toISOString()
      });
    }

    return forecast;
  }, [enableForecasting, metrics, onForecastGenerate, enableAnalytics, onAnalyticsTrack]);

  const generateAnalyticsReport = useCallback(() => {
    const insights = generateRevenueInsights();
    const forecast = forecastData;

    return {
      summary: insights,
      metrics,
      revenueData,
      forecast,
      dateRange,
      timeframe,
      generatedAt: new Date().toISOString()
    };
  }, [generateRevenueInsights, forecastData, metrics, revenueData, dateRange, timeframe]);

  const optimizeChartLayout = useCallback(() => {
    // Implement chart layout optimization logic
    const optimizedLayout = {
      priority: ['total_revenue', 'subscription_revenue', 'growth_rate', 'mrr'],
      responsive: isMobile ? 'mobile' : 'desktop',
      recommendations: [
        'Move key metrics to top for better visibility',
        'Combine related charts for better insights',
        'Add real-time updates for critical metrics'
      ]
    };

    return optimizedLayout;
  }, [isMobile]);

  const downloadChartData = useCallback((chartType) => {
    let chartData;

    switch (chartType) {
      case 'revenue':
        chartData = revenueData;
        break;
      case 'forecast':
        chartData = forecastData;
        break;
      default:
        chartData = metrics;
    }

    // Create and download CSV
    const csv = convertToCSV(chartData);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `revenue_analytics_${chartType}_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  }, [revenueData, forecastData, metrics]);

  const convertToCSV = useCallback((data) => {
    if (!Array.isArray(data)) return '';

    const headers = Object.keys(data[0] || {});
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => row[header] || '').join(','))
    ].join('\n');

    return csvContent;
  }, []);

  const formatCurrency = useCallback((amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }, []);

  const formatPercentage = useCallback((value) => {
    return `${(value * 100).toFixed(1)}%`;
  }, []);

  const setQuickRange = useCallback((months) => {
    const end = endOfMonth(new Date());
    const start = startOfMonth(subMonths(end, months - 1));
    handleDateRangeChange({ start, end });
  }, [handleDateRangeChange]);

  return (
    <Box
      ref={analyticsRef}
      className={className}
      sx={glassMorphismStyles}
      {...props}
    >
      {/* Controls */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <DatePicker
            label="Start Date"
            value={dateRange.start}
            onChange={(value) => handleDateRangeChange('start', value)}
            slotProps={{ textField: { fullWidth: true, size: 'small' } }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <DatePicker
            label="End Date"
            value={dateRange.end}
            onChange={(value) => handleDateRangeChange('end', value)}
            slotProps={{ textField: { fullWidth: true, size: 'small' } }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>Timeframe</InputLabel>
            <Select
              value={timeframe}
              label="Timeframe"
              onChange={(e) => handleTimeframeChange(e.target.value)}
            >
              <MenuItem value={TIMEFRAMES.DAILY}>Daily</MenuItem>
              <MenuItem value={TIMEFRAMES.WEEKLY}>Weekly</MenuItem>
              <MenuItem value={TIMEFRAMES.MONTHLY}>Monthly</MenuItem>
              <MenuItem value={TIMEFRAMES.QUARTERLY}>Quarterly</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Box display="flex" gap={1}>
            <Button
              size="small"
              onClick={() => setQuickRange(3)}
              sx={{
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              3M
            </Button>
            <Button
              size="small"
              onClick={() => setQuickRange(6)}
              sx={{
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              6M
            </Button>
            <Button
              size="small"
              onClick={() => setQuickRange(12)}
              sx={{
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              1Y
            </Button>
          </Box>
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end" gap={2}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleRefresh}
              disabled={analyticsLoading || loading}
              sx={{
                borderColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  borderColor: ACE_COLORS.YELLOW,
                  color: ACE_COLORS.YELLOW,
                  backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1)
                }
              }}
            >
              Refresh
            </Button>
            {enableExportOptions && (
              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                onClick={handleExport}
                disabled={!metrics}
                sx={{
                  background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE} 30%, ${ACE_COLORS.YELLOW} 90%)`,
                  '&:hover': {
                    background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE} 60%, ${ACE_COLORS.YELLOW} 100%)`,
                  }
                }}
              >
                Export
              </Button>
            )}
          </Box>
        </Grid>
      </Grid>

      {(analyticsError || error) && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => {
          setAnalyticsError(null);
          if (enableAccessibility) {
            announceToScreenReader('Error message dismissed');
          }
        }}>
          {analyticsError || error}
        </Alert>
      )}

      {(analyticsLoading || loading) ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : metrics ? (
        <Grid container spacing={3}>
          {/* Revenue Summary Cards */}
          <Grid item xs={12} md={6} lg={3}>
            <Card
              sx={{
                cursor: 'pointer',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: `0 4px 20px ${alpha(ACE_COLORS.PURPLE, 0.3)}`
                }
              }}
              onClick={() => handleMetricClick('total_revenue', metrics.total_revenue)}
            >
              <CardContent>
                <Typography color="text.secondary" gutterBottom variant="body2">
                  Total Revenue
                </Typography>
                <Typography variant="h4" component="div" sx={{ color: ACE_COLORS.PURPLE }}>
                  {formatCurrency(metrics.total_revenue)}
                </Typography>
                {metrics.growth_rate !== null && (
                  <Typography
                    variant="body2"
                    color={metrics.growth_rate >= 0 ? "success.main" : "error.main"}
                  >
                    {metrics.growth_rate >= 0 ? '+' : ''}{formatPercentage(metrics.growth_rate)} vs previous period
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom variant="body2">
                  Subscription Revenue
                </Typography>
                <Typography variant="h4" component="div">
                  {formatCurrency(metrics.subscription_revenue)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {((metrics.subscription_revenue / metrics.total_revenue) * 100).toFixed(1)}% of total
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom variant="body2">
                  Add-on Revenue
                </Typography>
                <Typography variant="h4" component="div">
                  {formatCurrency(metrics.addon_revenue)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {((metrics.addon_revenue / metrics.total_revenue) * 100).toFixed(1)}% of total
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Card>
              <CardContent>
                <Typography color="text.secondary" gutterBottom variant="body2">
                  Average Transaction
                </Typography>
                <Typography variant="h4" component="div">
                  {formatCurrency(metrics.average_transaction_value)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {metrics.transaction_count} transactions
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Revenue Breakdown Chart */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardHeader title="Revenue Breakdown" />
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart
                    data={[
                      { name: 'Subscription', value: metrics.subscription_revenue },
                      { name: 'Add-ons', value: metrics.addon_revenue },
                      { name: 'AppSumo', value: metrics.appsumo_revenue },
                      { name: 'Upgrades', value: metrics.upgrade_revenue },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                    <Tooltip formatter={(value) => formatCurrency(value)} />
                    <Bar dataKey="value" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Monthly Revenue by Plan */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardHeader title="Revenue by Plan" />
              <CardContent>
                <Box>
                  {Object.entries(metrics.revenue_by_plan || {}).map(([plan, revenue]) => (
                    <Box key={plan} display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                        {plan}
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(revenue)}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Key Metrics */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Key Metrics" />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h5" color="primary">
                        {formatCurrency(metrics.mrr)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Monthly Recurring Revenue
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h5" color="primary">
                        {formatCurrency(metrics.arr)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Annual Recurring Revenue
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h5" color="primary">
                        {formatPercentage(metrics.successful_transactions / metrics.transaction_count)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Success Rate
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box textAlign="center">
                      <Typography variant="h5" color="primary">
                        {formatCurrency(metrics.refund_amount)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Refunds
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      ) : (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Typography variant="body1" color="text.secondary">
            Select a date range to view revenue analytics
          </Typography>
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedRevenueAnalytics.propTypes = {
  // Core props
  dateRange: PropTypes.shape({
    start: PropTypes.instanceOf(Date),
    end: PropTypes.instanceOf(Date)
  }),
  onDateRangeChange: PropTypes.func,
  data: PropTypes.shape({
    metrics: PropTypes.object,
    revenueData: PropTypes.array,
    forecastData: PropTypes.array
  }),
  loading: PropTypes.bool,
  error: PropTypes.string,

  // Enhanced props
  className: PropTypes.string,
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeUpdates: PropTypes.bool,
  enableForecasting: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableExportOptions: PropTypes.bool,
  enableInteractiveCharts: PropTypes.bool,

  // Configuration
  defaultView: PropTypes.oneOf(Object.values(ANALYTICS_VIEWS)),
  defaultTimePeriod: PropTypes.oneOf(Object.values(TIME_PERIODS)),
  autoRefreshInterval: PropTypes.number,
  maxDataPoints: PropTypes.number,

  // Callback props
  onViewChange: PropTypes.func,
  onMetricClick: PropTypes.func,
  onDataExport: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onForecastGenerate: PropTypes.func,
  onRefresh: PropTypes.func
};

// Default props
EnhancedRevenueAnalytics.defaultProps = {
  dateRange: {
    start: startOfMonth(subMonths(new Date(), 1)),
    end: endOfMonth(subMonths(new Date(), 1))
  },
  onDateRangeChange: null,
  data: {},
  loading: false,
  error: null,
  className: '',
  enableAdvancedFeatures: true,
  enableRealTimeUpdates: true,
  enableForecasting: true,
  enableAccessibility: true,
  enableAnalytics: true,
  enableExportOptions: true,
  enableInteractiveCharts: true,
  defaultView: ANALYTICS_VIEWS.OVERVIEW,
  defaultTimePeriod: TIME_PERIODS.LAST_30_DAYS,
  autoRefreshInterval: 300000,
  maxDataPoints: 1000,
  onViewChange: null,
  onMetricClick: null,
  onDataExport: null,
  onAnalyticsTrack: null,
  onForecastGenerate: null,
  onRefresh: null
};

// Display name for debugging
EnhancedRevenueAnalytics.displayName = 'EnhancedRevenueAnalytics';

export default EnhancedRevenueAnalytics;

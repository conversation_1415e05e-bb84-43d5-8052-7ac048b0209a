import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  CircularProgress,
  Grid,
  Card,
  CardContent,
  CardActions,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import api from '../api';

const SystemSettings = () => {
  const [settings, setSettings] = useState({
    maintenance_mode: false,
    allow_signups: true,
    default_user_limits: {
      max_campaigns: 5,
      max_content_per_campaign: 20,
      max_images_per_content: 3,
    },
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await api.get('/api/admin/settings');
      setSettings(response.data);
      setError('');
    } catch (error) {
      console.error('Error fetching settings:', error);
      setError('Failed to fetch system settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      await api.put('/api/admin/settings', settings);
      setSuccess('Settings saved successfully');
      setError('');
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setError('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleSettingChange = (path, value) => {
    setSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current = newSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newSettings;
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          System Settings
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchSettings}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSaveSettings}
            disabled={saving}
          >
            {saving ? <CircularProgress size={20} /> : 'Save Settings'}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* General Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                General Settings
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.maintenance_mode}
                      onChange={(e) => handleSettingChange('maintenance_mode', e.target.checked)}
                    />
                  }
                  label="Maintenance Mode"
                />
                <Typography variant="body2" color="text.secondary">
                  When enabled, the application will show a maintenance page to users.
                </Typography>

                <Divider />

                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.allow_signups}
                      onChange={(e) => handleSettingChange('allow_signups', e.target.checked)}
                    />
                  }
                  label="Allow New Signups"
                />
                <Typography variant="body2" color="text.secondary">
                  When disabled, new users cannot register for accounts.
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* User Limits */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <StorageIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Default User Limits
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                <TextField
                  label="Max Campaigns"
                  type="number"
                  value={settings.default_user_limits?.max_campaigns || 5}
                  onChange={(e) => handleSettingChange('default_user_limits.max_campaigns', parseInt(e.target.value))}
                  fullWidth
                  size="small"
                />
                
                <TextField
                  label="Max Content per Campaign"
                  type="number"
                  value={settings.default_user_limits?.max_content_per_campaign || 20}
                  onChange={(e) => handleSettingChange('default_user_limits.max_content_per_campaign', parseInt(e.target.value))}
                  fullWidth
                  size="small"
                />
                
                <TextField
                  label="Max Images per Content"
                  type="number"
                  value={settings.default_user_limits?.max_images_per_content || 3}
                  onChange={(e) => handleSettingChange('default_user_limits.max_images_per_content', parseInt(e.target.value))}
                  fullWidth
                  size="small"
                />
                
                <Typography variant="body2" color="text.secondary">
                  These limits apply to new users by default.
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Information */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <NotificationsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                System Information
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Application Version
                    </Typography>
                    <Typography variant="h6">
                      1.0.0
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Database Status
                    </Typography>
                    <Typography variant="h6" color="success.main">
                      Connected
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      API Status
                    </Typography>
                    <Typography variant="h6" color="success.main">
                      Operational
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Last Updated
                    </Typography>
                    <Typography variant="h6">
                      {new Date().toLocaleDateString()}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SystemSettings;

/**
 * @fileoverview Utility helper functions for the ACE Social platform
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 */

/**
 * Announce text to screen readers for accessibility
 * @param {string} message - Message to announce
 */
export const announceToScreenReader = (message) => {
  try {
    if (typeof window === 'undefined' || typeof document === 'undefined') return;
    if (!message || typeof message !== 'string') return;

    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.style.position = 'absolute';
    announcement.style.left = '-10000px';
    announcement.style.width = '1px';
    announcement.style.height = '1px';
    announcement.style.overflow = 'hidden';

    if (document.body) {
      document.body.appendChild(announcement);
      announcement.textContent = message;

      setTimeout(() => {
        try {
          if (document.body && document.body.contains(announcement)) {
            document.body.removeChild(announcement);
          }
        } catch (error) {
          console.warn('Error removing screen reader announcement:', error);
        }
      }, 1000);
    }
  } catch (error) {
    console.warn('Error in announceToScreenReader:', error);
  }
};

/**
 * Debounce function to limit function calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  if (typeof func !== 'function') {
    console.warn('debounce: first argument must be a function');
    return () => {};
  }

  if (typeof wait !== 'number' || wait < 0) {
    wait = 300; // Default wait time
  }

  let timeout;
  return function executedFunction(...args) {
    try {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    } catch (error) {
      console.warn('Error in debounced function:', error);
    }
  };
};

/**
 * Truncate text to specified length
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @param {string} suffix - Suffix to add (default: '...')
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength, suffix = '...') => {
  if (!text || typeof text !== 'string') return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - suffix.length) + suffix;
};

/**
 * Optimize image URL for performance
 * @param {string} url - Original image URL
 * @param {Object} dimensions - Target dimensions
 * @returns {Promise<string>} Optimized image URL
 */
export const optimizeImageUrl = async (url, dimensions = {}) => {
  if (!url) return '';
  
  try {
    // Basic optimization - in a real implementation, this would use a service like Cloudinary
    const { width, height, quality = 80 } = dimensions;

    // For now, just return the original URL with query parameters for future optimization
    // In production, you would integrate with an image optimization service
    const params = new URLSearchParams();
    if (width) params.append('w', width.toString());
    if (height) params.append('h', height.toString());
    if (quality !== 80) params.append('q', quality.toString());

    const queryString = params.toString();
    return queryString ? `${url}?${queryString}` : url;
  } catch (error) {
    console.warn('Image optimization failed:', error);
    return url;
  }
};

/**
 * Generate responsive image srcset
 * @param {string} url - Base image URL
 * @param {Object} dimensions - Target dimensions
 * @returns {Object} Object with src and srcSet
 */
export const generateImageSrcSet = (url, dimensions = {}) => {
  if (!url) return { src: '', srcSet: '' };
  
  try {
    const { width = 800, height = 600 } = dimensions;

    // Generate different sizes for responsive images
    const sizes = [
      { width: Math.round(width * 0.5), height: Math.round(height * 0.5), suffix: '0.5x' },
      { width: width, height: height, suffix: '1x' },
      { width: Math.round(width * 1.5), height: Math.round(height * 1.5), suffix: '1.5x' },
      { width: Math.round(width * 2), height: Math.round(height * 2), suffix: '2x' }
    ];
    
    // In a real implementation, this would generate optimized URLs for each size
    const srcSet = sizes
      .map(size => `${url} ${size.suffix}`)
      .join(', ');
    
    return {
      src: url,
      srcSet: srcSet
    };
  } catch (error) {
    console.warn('Failed to generate srcSet:', error);
    return { src: url, srcSet: '' };
  }
};

/**
 * Format file size in human readable format
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Generate unique ID
 * @param {string} prefix - Optional prefix
 * @returns {string} Unique ID
 */
export const generateId = (prefix = 'id') => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Generate unique ID (alias for generateId)
 * @param {string} prefix - Optional prefix
 * @returns {string} Unique ID
 */
export const generateUniqueId = (prefix = 'id') => {
  return generateId(prefix);
};

/**
 * Format date to human readable format
 * @param {Date|string|number} date - Date to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date
 */
export const formatDate = (date, options = {}) => {
  if (!date) return '';

  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    const {
      format = 'short', // 'short', 'long', 'relative', 'time', 'datetime'
      locale = 'en-US',
      timezone = undefined
    } = options;

    const formatOptions = {
      timeZone: timezone
    };

    switch (format) {
      case 'short':
        return dateObj.toLocaleDateString(locale, {
          ...formatOptions,
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });

      case 'long':
        return dateObj.toLocaleDateString(locale, {
          ...formatOptions,
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

      case 'time':
        return dateObj.toLocaleTimeString(locale, {
          ...formatOptions,
          hour: '2-digit',
          minute: '2-digit'
        });

      case 'datetime':
        return dateObj.toLocaleString(locale, {
          ...formatOptions,
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });

      case 'relative': {
        const now = new Date();
        const diffMs = now - dateObj;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
        if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
        return `${Math.floor(diffDays / 365)} years ago`;
      }

      default:
        return dateObj.toLocaleDateString(locale, formatOptions);
    }
  } catch (error) {
    console.warn('Date formatting error:', error);
    return '';
  }
};

/**
 * Check if value is empty
 * @param {any} value - Value to check
 * @returns {boolean} True if empty
 */
export const isEmpty = (value) => {
  if (value == null) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * Deep clone object
 * @param {any} obj - Object to clone
 * @returns {any} Cloned object
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

/**
 * Validate competitor data structure
 * @param {Object} competitorData - Competitor data to validate
 * @returns {Object} Validation result with isValid and errors
 */
export const validateCompetitorData = (competitorData) => {
  const errors = [];
  const warnings = [];

  if (!competitorData || typeof competitorData !== 'object') {
    return {
      isValid: false,
      errors: ['Competitor data must be a valid object'],
      warnings: []
    };
  }

  // Required fields validation
  const requiredFields = ['name', 'website'];
  requiredFields.forEach(field => {
    if (!competitorData[field] || competitorData[field].trim() === '') {
      errors.push(`${field} is required`);
    }
  });

  // Name validation
  if (competitorData.name && competitorData.name.length > 100) {
    errors.push('Competitor name must not exceed 100 characters');
  }

  // Website validation
  if (competitorData.website) {
    const urlPattern = /^https?:\/\/.+/;
    if (!urlPattern.test(competitorData.website)) {
      errors.push('Website must be a valid URL starting with http:// or https://');
    }
  }

  // Description validation
  if (competitorData.description && competitorData.description.length > 500) {
    warnings.push('Description is quite long, consider keeping it under 500 characters');
  }

  // Social media handles validation
  if (competitorData.socialMedia) {
    Object.keys(competitorData.socialMedia).forEach(platform => {
      const handle = competitorData.socialMedia[platform];
      if (handle && typeof handle !== 'string') {
        errors.push(`${platform} handle must be a string`);
      }
    });
  }

  // Industry validation
  if (competitorData.industry && typeof competitorData.industry !== 'string') {
    errors.push('Industry must be a string');
  }

  // Size validation
  if (competitorData.size && !['startup', 'small', 'medium', 'large', 'enterprise'].includes(competitorData.size)) {
    warnings.push('Company size should be one of: startup, small, medium, large, enterprise');
  }

  // Founded year validation
  if (competitorData.foundedYear) {
    const currentYear = new Date().getFullYear();
    const year = parseInt(competitorData.foundedYear);
    if (isNaN(year) || year < 1800 || year > currentYear) {
      errors.push('Founded year must be a valid year between 1800 and current year');
    }
  }

  // Location validation
  if (competitorData.location && typeof competitorData.location !== 'string') {
    errors.push('Location must be a string');
  }

  // Tags validation
  if (competitorData.tags) {
    if (!Array.isArray(competitorData.tags)) {
      errors.push('Tags must be an array');
    } else if (competitorData.tags.length > 20) {
      warnings.push('Consider limiting tags to 20 or fewer for better organization');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Format number with locale-specific formatting
 * @param {number} number - Number to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted number
 */
export const formatNumber = (number, options = {}) => {
  if (number == null || isNaN(number)) return '0';

  const {
    locale = 'en-US',
    minimumFractionDigits = 0,
    maximumFractionDigits = 2,
    style = 'decimal',
    currency = 'USD',
    notation = 'standard',
    compactDisplay = 'short'
  } = options;

  try {
    const formatOptions = {
      minimumFractionDigits,
      maximumFractionDigits,
      style,
      notation
    };

    if (style === 'currency') {
      formatOptions.currency = currency;
    }

    if (notation === 'compact') {
      formatOptions.compactDisplay = compactDisplay;
    }

    return new Intl.NumberFormat(locale, formatOptions).format(number);
  } catch (error) {
    console.warn('Number formatting error:', error);
    return number.toString();
  }
};

/**
 * Format percentage with proper symbol and precision
 * @param {number} value - Value to format as percentage (0.5 = 50%)
 * @param {Object} options - Formatting options
 * @returns {string} Formatted percentage
 */
export const formatPercentage = (value, options = {}) => {
  if (value == null || isNaN(value)) return '0%';

  const {
    locale = 'en-US',
    minimumFractionDigits = 0,
    maximumFractionDigits = 1,
    showSign = false
  } = options;

  try {
    const formatOptions = {
      style: 'percent',
      minimumFractionDigits,
      maximumFractionDigits
    };

    if (showSign && value > 0) {
      formatOptions.signDisplay = 'always';
    }

    return new Intl.NumberFormat(locale, formatOptions).format(value);
  } catch (error) {
    console.warn('Percentage formatting error:', error);
    const percentage = (value * 100).toFixed(maximumFractionDigits);
    return `${showSign && value > 0 ? '+' : ''}${percentage}%`;
  }
};

/**
 * Validate content structure and requirements
 * @param {Object} content - Content object to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validation result with isValid, errors, and warnings
 */
export const validateContent = (content, options = {}) => {
  const errors = [];
  const warnings = [];

  if (!content || typeof content !== 'object') {
    return {
      isValid: false,
      errors: ['Content must be a valid object'],
      warnings: []
    };
  }

  const {
    requireTitle = true,
    requireBody = true,
    maxTitleLength = 200,
    maxBodyLength = 5000,
    allowedTypes = ['text', 'image', 'video', 'link'],
    requireTags = false,
    maxTags = 10
  } = options;

  // Title validation
  if (requireTitle) {
    if (!content.title || content.title.trim() === '') {
      errors.push('Title is required');
    } else if (content.title.length > maxTitleLength) {
      errors.push(`Title must not exceed ${maxTitleLength} characters`);
    }
  }

  // Body/content validation
  if (requireBody) {
    if (!content.body && !content.content) {
      errors.push('Content body is required');
    } else {
      const bodyText = content.body || content.content || '';
      if (bodyText.length > maxBodyLength) {
        errors.push(`Content body must not exceed ${maxBodyLength} characters`);
      }
      if (bodyText.length < 10) {
        warnings.push('Content body is quite short, consider adding more detail');
      }
    }
  }

  // Content type validation
  if (content.type && !allowedTypes.includes(content.type)) {
    errors.push(`Content type must be one of: ${allowedTypes.join(', ')}`);
  }

  // Tags validation
  if (requireTags && (!content.tags || content.tags.length === 0)) {
    errors.push('At least one tag is required');
  }

  if (content.tags) {
    if (!Array.isArray(content.tags)) {
      errors.push('Tags must be an array');
    } else {
      if (content.tags.length > maxTags) {
        warnings.push(`Consider limiting tags to ${maxTags} or fewer`);
      }

      // Check for duplicate tags
      const uniqueTags = [...new Set(content.tags)];
      if (uniqueTags.length !== content.tags.length) {
        warnings.push('Duplicate tags detected');
      }
    }
  }

  // URL validation for link content
  if (content.type === 'link' && content.url) {
    const urlPattern = /^https?:\/\/.+/;
    if (!urlPattern.test(content.url)) {
      errors.push('URL must be a valid link starting with http:// or https://');
    }
  }

  // Image validation
  if (content.type === 'image') {
    if (!content.imageUrl && !content.image) {
      errors.push('Image content requires an image URL or image data');
    }

    if (content.imageUrl) {
      const urlPattern = /^https?:\/\/.+/;
      if (!urlPattern.test(content.imageUrl)) {
        errors.push('Image URL must be a valid URL');
      }
    }
  }

  // Scheduling validation
  if (content.scheduledAt) {
    const scheduledDate = new Date(content.scheduledAt);
    const now = new Date();

    if (isNaN(scheduledDate.getTime())) {
      errors.push('Scheduled date must be a valid date');
    } else if (scheduledDate <= now) {
      warnings.push('Scheduled date is in the past');
    }
  }

  // Platform-specific validation
  if (content.platforms && Array.isArray(content.platforms)) {
    const validPlatforms = ['facebook', 'twitter', 'linkedin', 'instagram', 'youtube'];
    const invalidPlatforms = content.platforms.filter(p => !validPlatforms.includes(p));

    if (invalidPlatforms.length > 0) {
      warnings.push(`Unknown platforms: ${invalidPlatforms.join(', ')}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

export default {
  announceToScreenReader,
  debounce,
  truncateText,
  optimizeImageUrl,
  generateImageSrcSet,
  formatFileSize,
  generateId,
  generateUniqueId,
  formatDate,
  formatNumber,
  formatPercentage,
  isEmpty,
  deepClone,
  validateCompetitorData,
  validateContent
};

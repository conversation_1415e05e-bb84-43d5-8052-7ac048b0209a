// @since 2024-1-1 to 2025-25-7
import { memo, Suspense, useCallback, useEffect, useState, useMemo } from "react";
import { Helmet } from "react-helmet-async";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import {
  Box,
  Container,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Breadcrumbs,
  Link,
  Paper,
  Skeleton
} from "@mui/material";
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  ArrowBack as ArrowBackIcon
} from "@mui/icons-material";
import CompetitorForm from "../../components/competitors/CompetitorForm";
import { CompetitorProvider } from "../../contexts/CompetitorContext";
import { useNotification } from "../../hooks/useNotification";
import ErrorBoundary from "../../components/common/ErrorBoundary";

// Enhanced loading component with form skeleton
const LoadingFallback = memo(({ isEditMode }) => (
  <Box sx={{ my: 4 }}>
    <Paper sx={{ p: 3, mb: 3 }}>
      <Skeleton variant="text" width="40%" height={32} sx={{ mb: 2 }} />
      <Skeleton variant="text" width="60%" height={24} sx={{ mb: 3 }} />

      <Box sx={{ display: 'grid', gap: 3 }}>
        <Skeleton variant="rectangular" height={56} />
        <Skeleton variant="rectangular" height={56} />
        <Skeleton variant="rectangular" height={120} />
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Skeleton variant="rectangular" width={120} height={40} />
          <Skeleton variant="rectangular" width={100} height={40} />
        </Box>
      </Box>
    </Paper>

    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        gap: 2
      }}
      role="status"
      aria-live="polite"
      aria-label={`Loading ${isEditMode ? 'edit' : 'add'} competitor form`}
    >
      <CircularProgress size={24} aria-label="Loading data" />
      <Typography variant="body2" color="text.secondary">
        {isEditMode ? 'Loading competitor data...' : 'Preparing form...'}
      </Typography>
    </Box>
  </Box>
));

LoadingFallback.displayName = 'LoadingFallback';

// Error fallback component
const ErrorFallback = memo(({ error, resetError, isEditMode, competitorId }) => (
  <Alert
    severity="error"
    sx={{ my: 3 }}
    action={
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          color="inherit"
          size="small"
          onClick={resetError}
          startIcon={<RefreshIcon />}
          aria-label={`Retry loading ${isEditMode ? 'edit' : 'add'} competitor form`}
        >
          Retry
        </Button>
        <Button
          color="inherit"
          size="small"
          onClick={() => window.history.back()}
          startIcon={<ArrowBackIcon />}
          aria-label="Go back to previous page"
        >
          Go Back
        </Button>
      </Box>
    }
  >
    <Typography variant="body2">
      Failed to load {isEditMode ? 'edit' : 'add'} competitor form
      {competitorId ? ` for ID: ${competitorId}` : ''}: {error?.message || 'Unknown error'}
    </Typography>
  </Alert>
));

ErrorFallback.displayName = 'ErrorFallback';

const CompetitorFormPage = () => {
  const { id: competitorId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { showErrorNotification, showSuccessNotification } = useNotification();
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [competitorName, setCompetitorName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Memoized values
  const isEditMode = useMemo(() => Boolean(competitorId), [competitorId]);

  const formMode = useMemo(() => ({
    isEdit: isEditMode,
    title: isEditMode ? "Edit Competitor" : "Add New Competitor",
    description: isEditMode
      ? "Update competitor information and social media platforms."
      : "Add a new competitor to track and analyze their social media presence.",
    submitText: isEditMode ? "Update Competitor" : "Add Competitor",
    icon: isEditMode ? EditIcon : AddIcon
  }), [isEditMode]);

  // Validate competitor ID for edit mode
  useEffect(() => {
    if (isEditMode && !competitorId) {
      setError(new Error('Competitor ID is required for editing'));
      showErrorNotification('Invalid competitor ID for editing');
    }
  }, [isEditMode, competitorId, showErrorNotification]);

  // Error boundary handler
  const handleError = useCallback((error, errorInfo) => {
    console.error('CompetitorFormPage Error:', error, errorInfo);
    setError(error);
    showErrorNotification(`Failed to load ${formMode.title.toLowerCase()}`);
  }, [showErrorNotification, formMode.title]);

  // Reset error state
  const resetError = useCallback(() => {
    setError(null);
    setRetryCount(prev => prev + 1);
  }, []);

  // Navigation handlers
  const handleNavigateHome = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  const handleNavigateCompetitors = useCallback(() => {
    navigate('/settings?tab=competitors');
  }, [navigate]);

  // Form submission handler
  const handleFormSubmit = useCallback(async () => {
    setIsSubmitting(true);
    try {
      // Form submission will be handled by the CompetitorForm component
      showSuccessNotification(`Competitor ${isEditMode ? 'updated' : 'added'} successfully`);

      // Navigate back to competitors list after successful submission
      setTimeout(() => {
        navigate('/settings?tab=competitors');
      }, 1000);
    } catch (error) {
      console.error('Form submission error:', error);
      showErrorNotification(`Failed to ${isEditMode ? 'update' : 'add'} competitor`);
    } finally {
      setIsSubmitting(false);
    }
  }, [isEditMode, navigate, showSuccessNotification, showErrorNotification]);

  // Handle competitor name update from child component
  const handleCompetitorNameUpdate = useCallback((name) => {
    setCompetitorName(name);
  }, []);

  // SEO and meta tags
  const pageTitle = competitorName && isEditMode
    ? `Edit ${competitorName} | ACE Social`
    : `${formMode.title} | ACE Social`;

  const pageDescription = isEditMode
    ? `Edit competitor information for ${competitorName || 'selected competitor'} including social media platforms and tracking settings.`
    : "Add a new competitor to track and analyze their social media presence, engagement metrics, and content strategy.";

  // Analytics tracking
  useEffect(() => {
    // Track page view
    if (typeof gtag !== 'undefined') {
      gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: pageTitle,
        page_location: window.location.href,
        custom_map: {
          form_mode: isEditMode ? 'edit' : 'add',
          competitor_id: competitorId || null
        }
      });
    }
  }, [pageTitle, isEditMode, competitorId]);

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta name="keywords" content={`${isEditMode ? 'edit' : 'add'} competitor, competitor tracking, social media analysis, competitive intelligence`} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="website" />
        <link rel="canonical" href={`${window.location.origin}${location.pathname}`} />
        {competitorId && <meta name="competitor-id" content={competitorId} />}
        <meta name="form-mode" content={isEditMode ? 'edit' : 'add'} />
      </Helmet>

      <Container
        maxWidth="lg"
        component="main"
        role="main"
        aria-label={`${formMode.title} form`}
      >
        {/* Breadcrumb Navigation */}
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs
            aria-label="breadcrumb navigation"
            sx={{ mb: 2 }}
          >
            <Link
              component="button"
              variant="body2"
              onClick={handleNavigateHome}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
              aria-label="Navigate to dashboard"
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Dashboard
            </Link>
            <Link
              component="button"
              variant="body2"
              onClick={handleNavigateCompetitors}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
              aria-label="Navigate to competitors"
            >
              <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Competitors
            </Link>
            <Typography
              color="text.primary"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <formMode.icon sx={{ mr: 0.5 }} fontSize="inherit" />
              {isEditMode ? (competitorName || 'Edit') : 'Add New'}
            </Typography>
          </Breadcrumbs>
        </Box>

        {/* Page Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <formMode.icon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
            <Typography variant="h4" component="h1" gutterBottom>
              {formMode.title}
            </Typography>
          </Box>
          <Typography variant="body1" color="text.secondary">
            {formMode.description}
          </Typography>

          {/* Form Status Indicators */}
          {isSubmitting && (
            <Alert severity="info" sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={16} />
                <Typography variant="body2">
                  {isEditMode ? 'Updating competitor...' : 'Adding competitor...'}
                </Typography>
              </Box>
            </Alert>
          )}
        </Box>

        {/* Error Display */}
        {error && (
          <ErrorFallback
            error={error}
            resetError={resetError}
            isEditMode={isEditMode}
            competitorId={competitorId}
          />
        )}

        {/* Main Content */}
        {!error && (
          <ErrorBoundary
            onError={handleError}
            fallback={<ErrorFallback error={error} resetError={resetError} isEditMode={isEditMode} competitorId={competitorId} />}
          >
            <CompetitorProvider>
              <Suspense fallback={<LoadingFallback isEditMode={isEditMode} />}>
                <CompetitorForm
                  competitorId={competitorId}
                  isEditMode={isEditMode}
                  onSubmit={handleFormSubmit}
                  onNameUpdate={handleCompetitorNameUpdate}
                  disabled={isSubmitting}
                  key={retryCount} // Force re-render on retry
                />
              </Suspense>
            </CompetitorProvider>
          </ErrorBoundary>
        )}
      </Container>
    </>
  );
};

// Memoize the component for performance
const MemoizedCompetitorFormPage = memo(CompetitorFormPage);
MemoizedCompetitorFormPage.displayName = 'CompetitorFormPage';

export default MemoizedCompetitorFormPage;

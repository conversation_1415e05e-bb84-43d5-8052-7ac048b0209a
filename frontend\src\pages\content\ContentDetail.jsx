// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Divider,
  CircularProgress,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Tab,
  Tabs,
} from '@mui/material';
import { CustomCard, CustomCardContent } from '../../components/common';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Schedule as ScheduleIcon,
  TextFields as TextFieldsIcon,
  Image as ImageIcon,
  BarChart as BarChartIcon,
  Link as LinkIcon,
} from '@mui/icons-material';
import { getContent, deleteContent } from '../../api/content';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import ContentImageWithHeadline from '../../components/content/ContentImageWithHeadline';
import URLAnalytics from '../../components/analytics/URLAnalytics';

const ContentDetail = () => {
  const { contentId } = useParams();
  const navigate = useNavigate();
  const { showSuccess, showError } = useAdvancedToast();

  const [content, setContent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [analyticsTabValue, setAnalyticsTabValue] = useState(0);

  useEffect(() => {
    const fetchContent = async () => {
      setLoading(true);
      try {
        const data = await getContent(contentId);
        setContent(data);
      } catch (error) {
        console.error('Error fetching content:', error);
        showError('Failed to load content details');
      } finally {
        setLoading(false);
      }
    };

    if (contentId) {
      fetchContent();
    }
  }, [contentId, showError]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleAnalyticsTabChange = (event, newValue) => {
    setAnalyticsTabValue(newValue);
  };

  const handleEdit = () => {
    navigate(`/content/edit/${contentId}`);
  };

  const handleSchedule = () => {
    navigate(`/scheduling/schedule/${contentId}`);
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this content?')) {
      try {
        await deleteContent(contentId);
        showSuccess('Content deleted successfully');
        navigate('/content/library');
      } catch (error) {
        console.error('Error deleting content:', error);
        showError('Failed to delete content');
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!content) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5" color="error">
          Content not found
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate('/content/library')}
          sx={{ mt: 2 }}
        >
          Back to Content Library
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton color="primary" onClick={() => navigate('/content/library')} sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          <TextFieldsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Content Details
        </Typography>

        <Box>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleEdit}
            startIcon={<EditIcon />}
            sx={{ mr: 1 }}
          >
            Edit
          </Button>

          <Button
            variant="outlined"
            color="primary"
            onClick={handleSchedule}
            startIcon={<ScheduleIcon />}
            sx={{ mr: 1 }}
          >
            Schedule
          </Button>

          <Button
            variant="outlined"
            color="error"
            onClick={handleDelete}
            startIcon={<DeleteIcon />}
          >
            Delete
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <CustomCard variant="glass">
            <CustomCardContent>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                sx={{ mb: 3 }}
              >
                <Tab label="Content" />
                <Tab label="Images" />
                <Tab label="Analytics" />
              </Tabs>

              {tabValue === 0 && (
                <Box>
                  <Typography variant="h5" gutterBottom>
                    {content.title}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Paper
                    elevation={0}
                    sx={{
                      p: 3,
                      backgroundColor: 'background.default',
                      whiteSpace: 'pre-wrap',
                    }}
                  >
                    {content.text_content}
                  </Paper>

                  {content.tags && content.tags.length > 0 && (
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Hashtags:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {content.tags.map((tag) => (
                          <Chip
                            key={tag}
                            label={`#${tag}`}
                            color="primary"
                            variant="outlined"
                            size="small"
                            sx={{ mr: 1, mb: 1 }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>
              )}

              {tabValue === 1 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    <ImageIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Images
                  </Typography>

                  {content.images && content.images.length > 0 ? (
                    <Grid container spacing={3}>
                      {content.images.map((image, index) => (
                        <Grid item xs={12} sm={6} key={index}>
                          <ContentImageWithHeadline
                            image={image}
                            headline={content.headline}
                            title={content.title}
                            index={index}
                            height={250}
                          />
                        </Grid>
                      ))}
                    </Grid>
                  ) : (
                    <Paper sx={{ p: 3, textAlign: 'center' }}>
                      <Typography variant="body1" color="textSecondary">
                        No images available for this content
                      </Typography>
                    </Paper>
                  )}
                </Box>
              )}

              {tabValue === 2 && (
                <Box>
                  <Tabs
                    value={analyticsTabValue}
                    onChange={handleAnalyticsTabChange}
                    indicatorColor="secondary"
                    textColor="secondary"
                    sx={{ mb: 3 }}
                  >
                    <Tab
                      icon={<BarChartIcon />}
                      iconPosition="start"
                      label="Platform Analytics"
                    />
                    <Tab
                      icon={<LinkIcon />}
                      iconPosition="start"
                      label="URL Analytics"
                    />
                  </Tabs>

                  {analyticsTabValue === 0 && (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        <BarChartIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                        Platform Analytics
                      </Typography>

                      {Object.keys(content.analytics).length > 0 ? (
                        <Grid container spacing={2}>
                          {Object.entries(content.analytics).map(([platform, data]) => (
                            <Grid item xs={12} sm={6} md={4} key={platform}>
                              <Paper sx={{ p: 2 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                  {platform.charAt(0).toUpperCase() + platform.slice(1)}
                                </Typography>
                                <Divider sx={{ mb: 2 }} />
                                <Typography variant="body2">
                                  Views: {data.views}
                                </Typography>
                                <Typography variant="body2">
                                  Likes: {data.likes}
                                </Typography>
                                <Typography variant="body2">
                                  Comments: {data.comments}
                                </Typography>
                                <Typography variant="body2">
                                  Shares: {data.shares}
                                </Typography>
                                <Typography variant="body2">
                                  Engagement Rate: {data.engagement_rate.toFixed(2)}%
                                </Typography>
                              </Paper>
                            </Grid>
                          ))}
                        </Grid>
                      ) : (
                        <Paper sx={{ p: 3, textAlign: 'center' }}>
                          <Typography variant="body1" color="textSecondary">
                            No platform analytics data available for this content
                          </Typography>
                        </Paper>
                      )}
                    </Box>
                  )}

                  {analyticsTabValue === 1 && (
                    <URLAnalytics contentId={contentId} />
                  )}
                </Box>
              )}
            </CustomCardContent>
          </CustomCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <CustomCard variant="glass">
            <CustomCardContent>
              <Typography variant="h6" gutterBottom>
                Content Details
              </Typography>

              <Divider sx={{ mb: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Status
                </Typography>
                <Chip
                  label={content.status.charAt(0).toUpperCase() + content.status.slice(1)}
                  color={
                    content.status === 'published' ? 'success' :
                    content.status === 'scheduled' ? 'primary' :
                    'default'
                  }
                  size="small"
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Platforms
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                  {content.platforms.map((platform, index) => (
                    <Chip
                      key={index}
                      label={platform.charAt(0).toUpperCase() + platform.slice(1)}
                      size="small"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </Box>

              {content.scheduled_for && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Scheduled For
                  </Typography>
                  <Typography variant="body2">
                    {new Date(content.scheduled_for).toLocaleString()}
                  </Typography>
                </Box>
              )}

              {content.published_at && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" color="textSecondary">
                    Published At
                  </Typography>
                  <Typography variant="body2">
                    {new Date(content.published_at).toLocaleString()}
                  </Typography>
                </Box>
              )}

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Created At
                </Typography>
                <Typography variant="body2">
                  {new Date(content.created_at).toLocaleString()}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">
                  Last Updated
                </Typography>
                <Typography variant="body2">
                  {new Date(content.updated_at).toLocaleString()}
                </Typography>
              </Box>
            </CustomCardContent>
          </CustomCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ContentDetail;

/**
 * Enhanced Social Media Messaging Service Tests
 * 
 * Comprehensive test suite for the enhanced social media messaging service
 * covering all four production-ready enhancements: metrics, encryption,
 * deduplication, and WebSocket integration.
 * 
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 */

import { jest } from '@jest/globals';
import enhancedSocialMediaMessaging from '../EnhancedSocialMediaMessaging';
import { socialMediaMetrics } from '../../utils/PrometheusMetricsCollector';
import { messageEncryption } from '../../utils/MessageEncryptionService';
import { messageDeduplication } from '../../utils/MessageDeduplicationService';

// Mock dependencies
jest.mock('axios');
jest.mock('../NetworkMonitor');
jest.mock('../WebSocketService');
jest.mock('react-toastify');

describe('Enhanced Social Media Messaging Service', () => {
  let mockAxios;
  let mockNetworkMonitor;
  let mockWebSocketService;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup axios mock
    mockAxios = require('axios');
    mockAxios.post.mockResolvedValue({
      data: { id: 'msg_123', status: 'sent' },
      headers: {
        'x-rate-limit-remaining': '99',
        'x-rate-limit-reset': new Date(Date.now() + 3600000).toISOString()
      }
    });
    
    // Setup network monitor mock
    mockNetworkMonitor = require('../NetworkMonitor').default;
    mockNetworkMonitor.isOnline = true;
    
    // Reset service state
    enhancedSocialMediaMessaging.messageQueue = [];
    enhancedSocialMediaMessaging.errorCounts = {};
    enhancedSocialMediaMessaging.circuitBreakers = {};
  });

  describe('🚀 Core Functionality', () => {
    test('should send message successfully', async () => {
      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Hello from ACE Social!',
        user_id: 'user_123'
      };

      const result = await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(result).toEqual({
        id: 'msg_123',
        status: 'sent'
      });
      expect(mockAxios.post).toHaveBeenCalledWith('/api/social-media/messages', {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Hello from ACE Social!',
        attachments: [],
        encryption_metadata: null
      });
    });

    test('should queue message when offline', async () => {
      mockNetworkMonitor.isOnline = false;

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Offline message'
      };

      const result = await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(result.status).toBe('sending');
      expect(enhancedSocialMediaMessaging.messageQueue).toHaveLength(1);
      expect(mockAxios.post).not.toHaveBeenCalled();
    });
  });

  describe('📊 Prometheus Metrics Integration', () => {
    test('should record successful message send metrics', async () => {
      const recordSpy = jest.spyOn(socialMediaMetrics, 'recordMessageSend');

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Test message',
        user_id: 'user_123'
      };

      await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(recordSpy).toHaveBeenCalledWith(
        'linkedin',
        'creator', // Default subscription tier
        'success',
        expect.any(Number), // Duration
        expect.objectContaining({
          temp_id: expect.any(String),
          message_id: 'msg_123',
          encrypted: false,
          content_length: 12
        })
      );
    });

    test('should record rate limit violation metrics', async () => {
      const recordRateLimitSpy = jest.spyOn(socialMediaMetrics, 'recordRateLimitViolation');
      
      // Mock rate limit error
      mockAxios.post.mockRejectedValue({
        response: {
          status: 429,
          headers: {
            'x-rate-limit-reset': new Date(Date.now() + 3600000).toISOString()
          }
        }
      });

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Rate limited message'
      };

      await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(recordRateLimitSpy).toHaveBeenCalledWith(
        'linkedin',
        'creator',
        expect.objectContaining({
          resetTime: expect.any(Number)
        })
      );
    });

    test('should record circuit breaker state metrics', async () => {
      const recordCircuitBreakerSpy = jest.spyOn(socialMediaMetrics, 'recordCircuitBreakerState');

      // Trigger circuit breaker by causing multiple failures
      mockAxios.post.mockRejectedValue(new Error('Network error'));

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Failing message'
      };

      // Send multiple messages to trigger circuit breaker
      for (let i = 0; i < 4; i++) {
        await enhancedSocialMediaMessaging.sendMessage(messageData);
      }

      expect(recordCircuitBreakerSpy).toHaveBeenCalledWith(
        'linkedin',
        'open',
        expect.objectContaining({
          errorCount: expect.any(Number),
          resetTime: expect.any(Number)
        })
      );
    });

    test('should record queue processing metrics', async () => {
      const recordQueueSpy = jest.spyOn(socialMediaMetrics, 'recordQueueProcessing');

      // Add messages to queue
      enhancedSocialMediaMessaging.messageQueue = [
        {
          id: 'msg1',
          platform: 'linkedin',
          conversation_id: 'conv_123',
          content: 'Queued message 1',
          nextRetryTime: Date.now() - 1000
        },
        {
          id: 'msg2',
          platform: 'linkedin',
          conversation_id: 'conv_123',
          content: 'Queued message 2',
          nextRetryTime: Date.now() - 1000
        }
      ];

      await enhancedSocialMediaMessaging.processMessageQueue();

      expect(recordQueueSpy).toHaveBeenCalledWith(
        2, // Initial queue size
        expect.any(Number), // Processing time
        2, // Success count
        0  // Failure count
      );
    });
  });

  describe('🔐 Message Encryption', () => {
    test('should encrypt sensitive content automatically', async () => {
      const encryptSpy = jest.spyOn(messageEncryption, 'encryptMessage');
      const shouldEncryptSpy = jest.spyOn(messageEncryption, 'shouldEncrypt').mockReturnValue(true);

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Credit card: 4111-1111-1111-1111', // Sensitive content
        user_id: 'user_123'
      };

      await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(shouldEncryptSpy).toHaveBeenCalledWith(expect.objectContaining({
        content: 'Credit card: 4111-1111-1111-1111'
      }));
      expect(encryptSpy).toHaveBeenCalled();
    });

    test('should force encryption when requested', async () => {
      const encryptSpy = jest.spyOn(messageEncryption, 'encryptMessage');

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Regular message',
        encrypt: true, // Force encryption
        user_id: 'user_123'
      };

      await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(encryptSpy).toHaveBeenCalled();
    });

    test('should handle encryption failures gracefully', async () => {
      jest.spyOn(messageEncryption, 'encryptMessage').mockRejectedValue(new Error('Encryption failed'));
      jest.spyOn(messageEncryption, 'shouldEncrypt').mockReturnValue(true);

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Sensitive content',
        user_id: 'user_123'
      };

      const result = await enhancedSocialMediaMessaging.sendMessage(messageData);

      // Should still send message (fail-open for availability)
      expect(result).toEqual({
        id: 'msg_123',
        status: 'sent'
      });
    });
  });

  describe('🔄 Message Deduplication', () => {
    test('should detect duplicate messages', async () => {
      const isDuplicateSpy = jest.spyOn(messageDeduplication, 'isDuplicate').mockResolvedValue(true);

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Duplicate message',
        user_id: 'user_123'
      };

      const result = await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(isDuplicateSpy).toHaveBeenCalled();
      expect(result.duplicate_detected).toBe(true);
      expect(result.status).toBe('duplicate');
      expect(mockAxios.post).not.toHaveBeenCalled();
    });

    test('should mark messages as sent for deduplication', async () => {
      const markAsSentSpy = jest.spyOn(messageDeduplication, 'markAsSent');
      jest.spyOn(messageDeduplication, 'isDuplicate').mockResolvedValue(false);

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'New message',
        user_id: 'user_123'
      };

      await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(markAsSentSpy).toHaveBeenCalledWith(expect.objectContaining({
        content: 'New message'
      }));
    });

    test('should handle deduplication service failures gracefully', async () => {
      jest.spyOn(messageDeduplication, 'isDuplicate').mockRejectedValue(new Error('Deduplication failed'));

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Message with dedup failure',
        user_id: 'user_123'
      };

      const result = await enhancedSocialMediaMessaging.sendMessage(messageData);

      // Should still send message (fail-open for availability)
      expect(result).toEqual({
        id: 'msg_123',
        status: 'sent'
      });
    });
  });

  describe('🌐 WebSocket Integration', () => {
    test('should initialize WebSocket status monitoring', () => {
      const initSpy = jest.spyOn(enhancedSocialMediaMessaging, 'initWebSocketStatusMonitoring');

      // Create new instance to trigger initialization
      const newService = new (require('../EnhancedSocialMediaMessaging').default.constructor)();

      expect(initSpy).toHaveBeenCalled();
    });

    test('should handle WebSocket platform status updates', () => {
      const message = {
        type: 'platform_status_update',
        data: {
          platform: 'linkedin',
          status: {
            available: true,
            rate_limit_remaining: 95,
            rate_limit_reset: new Date(Date.now() + 3600000).toISOString(),
            response_time: 150
          },
          timestamp: new Date().toISOString()
        }
      };

      enhancedSocialMediaMessaging.handleWebSocketMessage(message);

      expect(enhancedSocialMediaMessaging.platformStatus.linkedin.available).toBe(true);
      expect(enhancedSocialMediaMessaging.platformStatus.linkedin.rateLimitRemaining).toBe(95);
    });

    test('should reset circuit breaker when platform becomes available', () => {
      // Set circuit breaker to open
      enhancedSocialMediaMessaging.circuitBreakers.linkedin = {
        open: true,
        resetTime: Date.now() + 300000
      };

      const resetSpy = jest.spyOn(enhancedSocialMediaMessaging, 'resetCircuitBreaker');

      const message = {
        type: 'platform_status_update',
        data: {
          platform: 'linkedin',
          status: { available: true },
          timestamp: new Date().toISOString()
        }
      };

      enhancedSocialMediaMessaging.handleWebSocketMessage(message);

      expect(resetSpy).toHaveBeenCalledWith('linkedin');
    });
  });

  describe('⚙️ Service Configuration', () => {
    test('should update subscription tier', () => {
      enhancedSocialMediaMessaging.updateSubscriptionTier('accelerator');
      expect(enhancedSocialMediaMessaging.subscriptionTier).toBe('accelerator');
    });

    test('should get comprehensive service status', () => {
      const status = enhancedSocialMediaMessaging.getServiceStatus();

      expect(status).toMatchObject({
        enabled: true,
        subscription_tier: expect.any(String),
        queue_size: expect.any(Number),
        platform_status: expect.any(Object),
        metrics: expect.objectContaining({
          enabled: expect.any(Boolean)
        }),
        encryption: expect.objectContaining({
          enabled: expect.any(Boolean)
        }),
        deduplication: expect.objectContaining({
          enabled: expect.any(Boolean)
        }),
        websocket: expect.objectContaining({
          enabled: expect.any(Boolean)
        })
      });
    });

    test('should configure enhancements', () => {
      const enableSpy = jest.spyOn(socialMediaMetrics, 'enable');
      const disableSpy = jest.spyOn(messageEncryption, 'disable');

      enhancedSocialMediaMessaging.configureEnhancements({
        metrics: true,
        encryption: false
      });

      expect(enableSpy).toHaveBeenCalled();
      expect(disableSpy).toHaveBeenCalled();
    });

    test('should cleanup resources properly', () => {
      const flushSpy = jest.spyOn(socialMediaMetrics, 'flushMetrics').mockResolvedValue(true);

      enhancedSocialMediaMessaging.cleanup();

      expect(flushSpy).toHaveBeenCalled();
      expect(enhancedSocialMediaMessaging.listeners.size).toBe(0);
    });
  });

  describe('🔧 Error Handling & Resilience', () => {
    test('should handle network errors gracefully', async () => {
      mockAxios.post.mockRejectedValue(new Error('Network error'));

      const messageData = {
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Network error message'
      };

      const result = await enhancedSocialMediaMessaging.sendMessage(messageData);

      expect(result.status).toBe('failed');
      expect(enhancedSocialMediaMessaging.messageQueue).toHaveLength(1);
    });

    test('should implement exponential backoff for retries', async () => {
      const queuedMessage = {
        id: 'msg_retry',
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Retry message',
        retryCount: 2,
        nextRetryTime: Date.now() - 1000
      };

      enhancedSocialMediaMessaging.messageQueue = [queuedMessage];
      mockAxios.post.mockRejectedValue(new Error('Retry error'));

      await enhancedSocialMediaMessaging.processMessageQueue();

      const requeuedMessage = enhancedSocialMediaMessaging.messageQueue[0];
      expect(requeuedMessage.retryCount).toBe(3);
      expect(requeuedMessage.nextRetryTime).toBeGreaterThan(Date.now());
    });

    test('should mark messages as permanently failed after max retries', async () => {
      const queuedMessage = {
        id: 'msg_max_retry',
        platform: 'linkedin',
        conversation_id: 'conv_123',
        content: 'Max retry message',
        retryCount: 5, // At max retry limit
        nextRetryTime: Date.now() - 1000
      };

      enhancedSocialMediaMessaging.messageQueue = [queuedMessage];
      mockAxios.post.mockRejectedValue(new Error('Final retry error'));

      const emitSpy = jest.spyOn(enhancedSocialMediaMessaging, 'emit');

      await enhancedSocialMediaMessaging.processMessageQueue();

      expect(enhancedSocialMediaMessaging.messageQueue).toHaveLength(0);
      expect(emitSpy).toHaveBeenCalledWith('messageStatusChanged', expect.objectContaining({
        status: 'failed'
      }));
    });
  });
});

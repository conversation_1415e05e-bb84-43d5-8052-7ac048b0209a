// @since 2024-1-1 to 2025-25-7
import { defineConfig, devices } from '@playwright/test';

/**
 * Accessibility testing configuration
 * WCAG 2.1 AA compliance testing
 */
export default defineConfig({
  testDir: './tests/accessibility',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'test-results/accessibility' }],
    ['json', { outputFile: 'test-results/accessibility-results.json' }]
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },

  projects: [
    {
      name: 'Desktop Accessibility',
      use: { 
        ...devices['Desktop Chrome'],
        // Enable accessibility features
        launchOptions: {
          args: ['--force-prefers-reduced-motion']
        }
      },
    },
    {
      name: 'Mobile Accessibility',
      use: { 
        ...devices['iPhone 12'],
        launchOptions: {
          args: ['--force-prefers-reduced-motion']
        }
      },
    },
    {
      name: 'High Contrast',
      use: { 
        ...devices['Desktop Chrome'],
        colorScheme: 'dark',
        launchOptions: {
          args: ['--force-dark-mode', '--force-prefers-reduced-motion']
        }
      },
    },
  ],

  webServer: {
    command: 'npm run preview',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
});

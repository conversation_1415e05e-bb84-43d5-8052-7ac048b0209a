/**
 * Tests for CompetitorInsightsAnalytics component
 */
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CompetitorInsightsAnalytics from '../CompetitorInsightsAnalytics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn()
  }))
}));

vi.mock('../../hooks/useAuth', () => ({
  useAuth: vi.fn(() => ({
    isAuthenticated: true,
    loading: false
  }))
}));

vi.mock('../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock GlassmorphicCard
vi.mock('../common/GlassmorphicCard', () => ({
  default: ({ children, ...props }) => <div {...props}>{children}</div>
}));

describe('CompetitorInsightsAnalytics', () => {
  const mockApi = require('../../api').default;
  const mockNotification = vi.fn();

  const mockData = {
    overallScore: 85.5,
    overallScoreTrend: 0.05,
    marketPosition: 3,
    marketPositionTrend: -1,
    engagementRate: 4.2,
    engagementRateTrend: 0.08,
    contentPerformanceScore: 78.3,
    contentPerformanceTrend: 2.1,
    followerGrowthRate: 12.5,
    followerGrowthTrend: 0.15,
    avgPostsPerWeek: 8.5,
    postsPerWeekTrend: 1.2,
    competitorAnalysis: {
      topCompetitors: [
        { name: 'Competitor A', score: 92.1 },
        { name: 'Competitor B', score: 88.7 },
        { name: 'Competitor C', score: 84.3 }
      ],
      marketShare: 15.2,
      competitiveAdvantages: [
        'Higher engagement rate',
        'Better content quality',
        'More consistent posting'
      ]
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue({ data: mockData });

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue({
      showErrorNotification: mockNotification
    });

    const { useAuth } = require('../../hooks/useAuth');
    useAuth.mockReturnValue({
      isAuthenticated: true,
      loading: false
    });
  });

  test('renders competitor insights analytics dashboard', async () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics />
      </TestWrapper>
    );

    expect(screen.getByText('Competitor Insights Analytics')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Performance Overview')).toBeInTheDocument();
      expect(screen.getByText('Competitor Comparison')).toBeInTheDocument();
      expect(screen.getByText('Content Analysis')).toBeInTheDocument();
      expect(screen.getByText('Actionable Insights')).toBeInTheDocument();
    });
  });

  test('displays performance metrics correctly', async () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('85.5')).toBeInTheDocument(); // Overall score
    expect(screen.getByText('3')).toBeInTheDocument(); // Market position
    expect(screen.getByText('4.2%')).toBeInTheDocument(); // Engagement rate
    expect(screen.getByText('78.3')).toBeInTheDocument(); // Content performance score
    expect(screen.getByText('12.5%')).toBeInTheDocument(); // Follower growth rate
    expect(screen.getByText('8.5')).toBeInTheDocument(); // Avg posts per week
  });

  test('shows loading state when loading prop is true', () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows loading state during authentication', () => {
    const { useAuth } = require('../../hooks/useAuth');
    useAuth.mockReturnValue({
      isAuthenticated: false,
      loading: true
    });

    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('fetches data automatically when authenticated', async () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/competitors/insights/analytics', {
        params: { days: 30 }
      });
    });
  });

  test('does not fetch data when not authenticated', () => {
    const { useAuth } = require('../../hooks/useAuth');
    useAuth.mockReturnValue({
      isAuthenticated: false,
      loading: false
    });

    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics />
      </TestWrapper>
    );

    expect(mockApi.get).not.toHaveBeenCalled();
  });

  test('handles API error gracefully', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockNotification).toHaveBeenCalledWith('Failed to load competitor insights data');
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledTimes(1);
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh competitor insights data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/competitors/insights/analytics', {
        params: { days: 30 }
      });
    });
  });

  test('handles export menu functionality', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={mockData} />
      </TestWrapper>
    );

    // Open export menu
    const exportButton = screen.getByLabelText('Export competitor insights data');
    await user.click(exportButton);

    await waitFor(() => {
      expect(screen.getByText('Export as CSV')).toBeInTheDocument();
      expect(screen.getByText('Export as PDF')).toBeInTheDocument();
    });

    // Click CSV export
    await user.click(screen.getByText('Export as CSV'));

    expect(consoleSpy).toHaveBeenCalledWith('Exporting data in format:', 'csv');

    consoleSpy.mockRestore();
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={mockData} />
      </TestWrapper>
    );

    // Click on Competitor Comparison tab
    await user.click(screen.getByText('Competitor Comparison'));

    expect(screen.getByText('Detailed competitor comparison analysis will be displayed here')).toBeInTheDocument();

    // Click on Content Analysis tab
    await user.click(screen.getByText('Content Analysis'));

    expect(screen.getByText('Content performance analysis will be shown here')).toBeInTheDocument();

    // Click on Actionable Insights tab
    await user.click(screen.getByText('Actionable Insights'));

    expect(screen.getByText('Strategic recommendations and actionable insights will be displayed here')).toBeInTheDocument();
  });

  test('displays trend indicators correctly', () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={mockData} />
      </TestWrapper>
    );

    // Should show positive trend indicators
    expect(screen.getByText('5.0%')).toBeInTheDocument(); // Overall score trend
    expect(screen.getByText('8.0%')).toBeInTheDocument(); // Engagement rate trend
    expect(screen.getByText('2.1')).toBeInTheDocument(); // Content performance trend
    expect(screen.getByText('15.0%')).toBeInTheDocument(); // Follower growth trend
    expect(screen.getByText('1.2')).toBeInTheDocument(); // Posts per week trend
  });

  test('works with external data prop', () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('85.5')).toBeInTheDocument();
    expect(mockApi.get).not.toHaveBeenCalled(); // Should not fetch when external data provided
  });

  test('works with external loading prop', () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('calls onRefresh prop when provided', async () => {
    const mockOnRefresh = vi.fn();
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={mockData} onRefresh={mockOnRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh competitor insights data');
    await user.click(refreshButton);

    expect(mockOnRefresh).toHaveBeenCalled();
  });

  test('respects days prop for API calls', async () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics days={7} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/competitors/insights/analytics', {
        params: { days: 7 }
      });
    });
  });

  test('handles missing data gracefully', () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={{}} />
      </TestWrapper>
    );

    // Should show default values for missing data
    expect(screen.getByText('0.0')).toBeInTheDocument(); // Default values
  });

  test('disables refresh button during loading', () => {
    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics loading={true} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh competitor insights data');
    expect(refreshButton).toBeDisabled();
  });

  test('updates data when external data prop changes', () => {
    const { rerender } = render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('85.5')).toBeInTheDocument();

    const newData = {
      ...mockData,
      overallScore: 92.3
    };

    rerender(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={newData} />
      </TestWrapper>
    );

    expect(screen.getByText('92.3')).toBeInTheDocument();
  });

  test('handles negative trends correctly', () => {
    const dataWithNegativeTrends = {
      ...mockData,
      overallScoreTrend: -0.03,
      engagementRateTrend: -0.05,
      marketPositionTrend: 1 // Positive trend for market position means worse position
    };

    render(
      <TestWrapper>
        <CompetitorInsightsAnalytics data={dataWithNegativeTrends} />
      </TestWrapper>
    );

    // Should show negative trend indicators
    expect(screen.getByText('3.0%')).toBeInTheDocument(); // Negative overall score trend
    expect(screen.getByText('5.0%')).toBeInTheDocument(); // Negative engagement rate trend
  });
});

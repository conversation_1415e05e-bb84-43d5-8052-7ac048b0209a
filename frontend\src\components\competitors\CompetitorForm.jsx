/**
 * CompetitorForm Component - Enterprise-grade competitor form for ACE Social platform
 * Features: Advanced form validation patterns, intelligent form field generation, dynamic form optimization,
 * advanced form validation, smart field management, adaptive form behaviors, contextual form guidance,
 * accessibility-focused form navigation, responsive form patterns, and production-ready competitor form functionality
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  memo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import { useNavigate, useParams } from 'react-router-dom';
import { useTheme, alpha } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  FormControl,
  FormHelperText,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  Divider,
  Paper,
  Container,
  Stepper,
  Step,
  StepLabel,
  Tooltip,
  Switch,
  InputAdornment,
  LinearProgress,
  Breadcrumbs,
  Link,
  CircularProgress
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Language as LanguageIcon,
  Facebook as FacebookIcon,
  Instagram as InstagramIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  YouTube as YouTubeIcon,
  Pinterest as PinterestIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Check as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';

// Enhanced imports for enterprise features
import { useCompetitors } from '../../contexts/CompetitorContext';
import { useNotification } from '../../hooks/useNotification';
import { useAnalytics } from '../../hooks/useAnalytics';
import { useLocalStorage } from '../../hooks/useLocalStorage';
import { useDebounce } from '../../hooks/useDebounce';


// Enhanced common components
import {
  ErrorBoundary,
  EmptyState,
  FeatureGate,
  ConfirmationDialog
} from '../common';

// Enhanced utility imports
import {
  validateField
} from '../../utils/formUtils';
import {
  announceToScreenReader,
  generateUniqueId
} from '../../utils/helpers';

// ===========================
// CONSTANTS & CONFIGURATIONS
// ===========================

// Form variants
const FORM_VARIANTS = {
  STANDARD: 'standard',
  ENHANCED: 'enhanced',
  MOBILE: 'mobile',
  WIZARD: 'wizard'
};

// Form steps for wizard mode
const FORM_STEPS = {
  BASIC_INFO: {
    index: 0,
    label: 'Basic Information',
    description: 'Enter competitor name and basic details'
  },
  COMPANY_DETAILS: {
    index: 1,
    label: 'Company Details',
    description: 'Industry, size, and target audience information'
  },
  SOCIAL_MEDIA: {
    index: 2,
    label: 'Social Media',
    description: 'Add social media platforms and accounts'
  },
  REVIEW: {
    index: 3,
    label: 'Review',
    description: 'Review and submit competitor information'
  }
};

// Enhanced industry options with categories
const INDUSTRY_OPTIONS = [
  { category: 'Technology', options: ['Software', 'Hardware', 'AI/ML', 'Cybersecurity', 'Cloud Services'] },
  { category: 'Finance', options: ['Banking', 'Insurance', 'Investment', 'Fintech', 'Cryptocurrency'] },
  { category: 'Healthcare', options: ['Medical Devices', 'Pharmaceuticals', 'Telemedicine', 'Health Tech', 'Biotechnology'] },
  { category: 'Education', options: ['EdTech', 'Online Learning', 'K-12', 'Higher Education', 'Corporate Training'] },
  { category: 'Retail', options: ['E-commerce', 'Fashion', 'Consumer Goods', 'Luxury', 'Marketplace'] },
  { category: 'Manufacturing', options: ['Automotive', 'Aerospace', 'Industrial', 'Consumer Products', 'Electronics'] },
  { category: 'Entertainment', options: ['Media', 'Gaming', 'Streaming', 'Music', 'Sports'] },
  { category: 'Food & Beverage', options: ['Restaurants', 'Food Tech', 'Beverages', 'Delivery', 'Catering'] },
  { category: 'Travel', options: ['Airlines', 'Hotels', 'Travel Tech', 'Tourism', 'Transportation'] },
  { category: 'Real Estate', options: ['PropTech', 'Commercial', 'Residential', 'Construction', 'Property Management'] },
  { category: 'Other', options: ['Consulting', 'Legal', 'Marketing', 'Non-profit', 'Government'] }
];

// Enhanced company size options with employee ranges
const COMPANY_SIZE_OPTIONS = [
  { value: 'startup', label: 'Startup', range: '1-10 employees', color: 'primary' },
  { value: 'small', label: 'Small Business', range: '11-50 employees', color: 'secondary' },
  { value: 'medium', label: 'Medium Business', range: '51-200 employees', color: 'info' },
  { value: 'large', label: 'Large Business', range: '201-1000 employees', color: 'warning' },
  { value: 'enterprise', label: 'Enterprise', range: '1000+ employees', color: 'success' }
];

// Enhanced social media platform options with metadata
const PLATFORM_OPTIONS = [
  {
    value: 'linkedin',
    label: 'LinkedIn',
    icon: LinkedInIcon,
    color: '#0077B5',
    urlPattern: /^https?:\/\/(www\.)?linkedin\.com\/(company|in)\/[a-zA-Z0-9-]+\/?$/,
    placeholder: 'https://linkedin.com/company/example'
  },
  {
    value: 'twitter',
    label: 'Twitter',
    icon: TwitterIcon,
    color: '#1DA1F2',
    urlPattern: /^https?:\/\/(www\.)?twitter\.com\/[a-zA-Z0-9_]+\/?$/,
    placeholder: 'https://twitter.com/example'
  },
  {
    value: 'facebook',
    label: 'Facebook',
    icon: FacebookIcon,
    color: '#1877F2',
    urlPattern: /^https?:\/\/(www\.)?facebook\.com\/[a-zA-Z0-9.]+\/?$/,
    placeholder: 'https://facebook.com/example'
  },
  {
    value: 'instagram',
    label: 'Instagram',
    icon: InstagramIcon,
    color: '#E4405F',
    urlPattern: /^https?:\/\/(www\.)?instagram\.com\/[a-zA-Z0-9_.]+\/?$/,
    placeholder: 'https://instagram.com/example'
  },
  {
    value: 'youtube',
    label: 'YouTube',
    icon: YouTubeIcon,
    color: '#FF0000',
    urlPattern: /^https?:\/\/(www\.)?youtube\.com\/(channel|c|user)\/[a-zA-Z0-9_-]+\/?$/,
    placeholder: 'https://youtube.com/c/example'
  },
  {
    value: 'pinterest',
    label: 'Pinterest',
    icon: PinterestIcon,
    color: '#BD081C',
    urlPattern: /^https?:\/\/(www\.)?pinterest\.com\/[a-zA-Z0-9_]+\/?$/,
    placeholder: 'https://pinterest.com/example'
  },
  {
    value: 'tiktok',
    label: 'TikTok',
    icon: LanguageIcon,
    color: '#000000',
    urlPattern: /^https?:\/\/(www\.)?tiktok\.com\/@[a-zA-Z0-9_.]+\/?$/,
    placeholder: 'https://tiktok.com/@example'
  }
];

// Component configuration
const COMPONENT_CONFIG = {
  DEBOUNCE_DELAY: 300,
  AUTO_SAVE_INTERVAL: 30000,
  MAX_SOCIAL_PLATFORMS: 10,
  MIN_NAME_LENGTH: 2,
  MAX_NAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  VALIDATION_DELAY: 500,
  ANIMATION_DURATION: 300
};



// Loading states
const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SAVING: 'saving',
  VALIDATING: 'validating',
  SUCCESS: 'success',
  ERROR: 'error'
};

// ===========================
// UTILITY FUNCTIONS
// ===========================

/**
 * Get platform icon component
 */
const getPlatformIcon = (platform) => {
  const platformConfig = PLATFORM_OPTIONS.find(p => p.value === platform);
  return platformConfig ? platformConfig.icon : LanguageIcon;
};

/**
 * Get platform color
 */
const getPlatformColor = (platform) => {
  const platformConfig = PLATFORM_OPTIONS.find(p => p.value === platform);
  return platformConfig ? platformConfig.color : '#666666';
};

/**
 * Validate social media URL
 */
const validateSocialMediaUrl = (platform, url) => {
  if (!url) return { isValid: true, error: null };

  const platformConfig = PLATFORM_OPTIONS.find(p => p.value === platform);
  if (!platformConfig) return { isValid: false, error: 'Invalid platform' };

  const isValid = platformConfig.urlPattern.test(url);
  return {
    isValid,
    error: isValid ? null : `Invalid ${platformConfig.label} URL format`
  };
};

/**
 * Format competitor data for API
 */
const formatCompetitorDataForAPI = (formData) => {
  return {
    ...formData,
    name: formData.name.trim(),
    description: formData.description?.trim() || '',
    website: formData.website?.trim() || '',
    target_audience: formData.target_audience?.trim() || '',
    social_media: formData.social_media
      .filter(sm => sm.platform && sm.account_name && sm.account_url)
      .map(sm => ({
        ...sm,
        account_name: sm.account_name.trim(),
        account_url: sm.account_url.trim(),
        followers_count: sm.followers_count ? parseInt(sm.followers_count, 10) : 0,
        posts_frequency: sm.posts_frequency?.trim() || ''
      }))
  };
};

/**
 * Generate form validation schema
 */
const generateValidationSchema = (userPlan) => {
  const baseSchema = {
    name: {
      required: true,
      minLength: COMPONENT_CONFIG.MIN_NAME_LENGTH,
      maxLength: COMPONENT_CONFIG.MAX_NAME_LENGTH
    },
    website: {
      pattern: /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?$/,
      message: 'Please enter a valid website URL'
    },
    description: {
      maxLength: COMPONENT_CONFIG.MAX_DESCRIPTION_LENGTH
    }
  };

  // Add advanced validation for higher tier plans
  if (userPlan === 'accelerator' || userPlan === 'dominator') {
    baseSchema.industry = { required: true };
    baseSchema.company_size = { required: true };
  }

  return baseSchema;
};

// ===========================
// MAIN COMPONENT
// ===========================

/**
 * Enhanced enterprise-grade competitor form component with comprehensive form patterns,
 * intelligent form field generation, dynamic form optimization, and production-ready competitor form functionality
 */
const CompetitorForm = memo(({
  // Basic props
  competitorId: propCompetitorId,

  // Enhanced props
  variant = FORM_VARIANTS.STANDARD,
  enableAnalytics = true,
  enableAccessibility = true,
  enableAutoSave = true,
  enableValidation = true,
  enableWizard = false,

  // Callback props
  onSubmit: onSubmitProp,
  onCancel: onCancelProp,
  onFieldChange,
  onValidationChange,
  onError,

  // User context props
  userPlan = 'creator',

  // Testing props
  testId = 'competitor-form',

  // Advanced props
  autoSaveInterval = COMPONENT_CONFIG.AUTO_SAVE_INTERVAL,
  validationDelay = COMPONENT_CONFIG.VALIDATION_DELAY,

  // Accessibility props
  ariaLabel = 'Competitor form',
  announceChanges = true
}) => {
  // ===========================
  // HOOKS & CONTEXT
  // ===========================

  const theme = useTheme();
  const navigate = useNavigate();
  const { id: paramId } = useParams();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Use prop competitorId or URL param
  const competitorId = propCompetitorId || paramId;
  const isEditMode = Boolean(competitorId);

  // Enhanced hooks
  const { showErrorNotification, showSuccessNotification, showInfoNotification } = useNotification();
  const { trackEvent, trackError } = useAnalytics();

  // Competitor context
  const {
    selectedCompetitor,
    loading: competitorsLoading,
    error: competitorsError,
    fetchCompetitor,
    createCompetitor,
    updateCompetitor,
    clearSelectedCompetitor
  } = useCompetitors();

  // Local storage for auto-save
  const [savedFormData, setSavedFormData] = useLocalStorage(
    `competitor-form-${competitorId || 'new'}`,
    null
  );

  // ===========================
  // STATE MANAGEMENT
  // ===========================

  // Core form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    website: '',
    industry: '',
    company_size: '',
    target_audience: '',
    is_active: true,
    social_media: []
  });

  // Enhanced state management
  const [loadingState, setLoadingState] = useState(LOADING_STATES.IDLE);
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});
  const [isValid, setIsValid] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  // Wizard state
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set());

  // UI state
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  // Performance tracking
  const formRef = useRef(null);
  const autoSaveTimeoutRef = useRef(null);
  const validationTimeoutRef = useRef(null);
  const fieldRefs = useRef({});

  // Debounced functions
  const debouncedValidation = useDebounce(validateForm, validationDelay);
  const debouncedAutoSave = useDebounce(autoSaveForm, autoSaveInterval);
  const debouncedTrackEvent = useDebounce(trackEvent, 300);

  // ===========================
  // UTILITY FUNCTIONS
  // ===========================

  /**
   * Handle analytics tracking
   */
  const handleAnalytics = useCallback((eventName, data = {}) => {
    if (!enableAnalytics) return;

    const analyticsData = {
      ...data,
      component: 'CompetitorForm',
      variant,
      userPlan,
      competitorId,
      isEditMode,
      currentStep
    };

    debouncedTrackEvent(eventName, analyticsData);
    onFieldChange?.(eventName, analyticsData);
  }, [enableAnalytics, variant, userPlan, competitorId, isEditMode, currentStep, debouncedTrackEvent, onFieldChange]);

  /**
   * Handle error reporting
   */
  const handleError = useCallback((error, context = {}) => {
    const errorData = {
      error: error.message || error,
      context: {
        ...context,
        component: 'CompetitorForm',
        competitorId,
        currentStep,
        userPlan
      }
    };

    console.error('CompetitorForm Error:', errorData);

    if (enableAnalytics) {
      trackError(errorData);
    }

    setLoadingState(LOADING_STATES.ERROR);
    onError?.(errorData);

    // Show user-friendly error message
    showErrorNotification(
      error.userMessage ||
      error.message ||
      'An unexpected error occurred. Please try again.'
    );
  }, [competitorId, currentStep, userPlan, enableAnalytics, trackError, onError, showErrorNotification]);

  /**
   * Auto-save form data
   */
  const autoSaveForm = useCallback(() => {
    if (!enableAutoSave || !isDirty) return;

    try {
      setSavedFormData(formData);
      handleAnalytics('form_auto_saved');
    } catch (error) {
      console.warn('Auto-save failed:', error);
    }
  }, [enableAutoSave, isDirty, formData, setSavedFormData, handleAnalytics]);

  /**
   * Validate entire form
   */
  const validateForm = useCallback(() => {
    if (!enableValidation) return { isValid: true, errors: {}, warnings: {} };

    const schema = generateValidationSchema(userPlan);
    const newErrors = {};
    const newWarnings = {};

    // Validate basic fields
    Object.entries(schema).forEach(([field, rules]) => {
      const value = formData[field];
      const fieldErrors = validateField(value, rules);

      if (fieldErrors.length > 0) {
        newErrors[field] = fieldErrors[0]; // Show first error
      }
    });

    // Validate social media platforms
    formData.social_media.forEach((platform, index) => {
      if (platform.platform && !platform.account_name) {
        newErrors[`social_media_${index}_account_name`] = 'Account name is required';
      }

      if (platform.platform && !platform.account_url) {
        newErrors[`social_media_${index}_account_url`] = 'Account URL is required';
      }

      if (platform.account_url) {
        const urlValidation = validateSocialMediaUrl(platform.platform, platform.account_url);
        if (!urlValidation.isValid) {
          newErrors[`social_media_${index}_account_url`] = urlValidation.error;
        }
      }

      if (platform.followers_count && (isNaN(Number(platform.followers_count)) || Number(platform.followers_count) < 0)) {
        newErrors[`social_media_${index}_followers_count`] = 'Must be a valid number';
      }
    });

    // Check for warnings
    if (!formData.description) {
      newWarnings.description = 'Adding a description helps with competitor analysis';
    }

    if (formData.social_media.length === 0) {
      newWarnings.social_media = 'Adding social media platforms improves tracking capabilities';
    }

    const formIsValid = Object.keys(newErrors).length === 0;

    setErrors(newErrors);
    setWarnings(newWarnings);
    setIsValid(formIsValid);

    onValidationChange?.({ isValid: formIsValid, errors: newErrors, warnings: newWarnings });

    return { isValid: formIsValid, errors: newErrors, warnings: newWarnings };
  }, [enableValidation, userPlan, formData, onValidationChange]);

  // ===========================
  // EVENT HANDLERS
  // ===========================

  /**
   * Handle form field changes
   */
  const handleChange = useCallback((e) => {
    const { name, value, checked, type } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    setIsDirty(true);
    setUnsavedChanges(true);

    // Clear validation error when field is changed
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }

    // Trigger validation after delay
    if (enableValidation) {
      debouncedValidation();
    }

    // Trigger auto-save
    if (enableAutoSave) {
      debouncedAutoSave();
    }

    handleAnalytics('field_changed', { field: name, value: newValue });
  }, [errors, enableValidation, enableAutoSave, debouncedValidation, debouncedAutoSave, handleAnalytics]);

  // Fetch competitor data if editing
  useEffect(() => {
    if (competitorId) {
      setLoadingState(LOADING_STATES.LOADING);
      fetchCompetitor(competitorId);
    }

    // Clear selected competitor on unmount
    return () => clearSelectedCompetitor();
  }, [competitorId, fetchCompetitor, clearSelectedCompetitor]);
  
  // Populate form data when selectedCompetitor changes
  useEffect(() => {
    if (selectedCompetitor) {
      const competitorData = {
        name: selectedCompetitor.name || '',
        description: selectedCompetitor.description || '',
        website: selectedCompetitor.website || '',
        industry: selectedCompetitor.industry || '',
        company_size: selectedCompetitor.company_size || '',
        target_audience: selectedCompetitor.target_audience || '',
        is_active: selectedCompetitor.is_active !== undefined ? selectedCompetitor.is_active : true,
        social_media: selectedCompetitor.social_media || []
      };

      setFormData(competitorData);
      setLoadingState(LOADING_STATES.SUCCESS);
      setIsDirty(false);
      setUnsavedChanges(false);

      handleAnalytics('competitor_data_loaded', { competitorId });
    }
  }, [selectedCompetitor, competitorId, handleAnalytics]);

  // Load saved form data if available
  useEffect(() => {
    if (!isEditMode && savedFormData && !isDirty) {
      setFormData(savedFormData);
      setIsDirty(true);
      setUnsavedChanges(true);
      showInfoNotification('Restored unsaved changes');
    }
  }, [isEditMode, savedFormData, isDirty, showInfoNotification]);

  /**
   * Handle social media field changes
   */
  const handleSocialMediaChange = useCallback((index, field, value) => {
    const updatedSocialMedia = [...formData.social_media];
    updatedSocialMedia[index] = {
      ...updatedSocialMedia[index],
      [field]: value
    };

    setFormData(prev => ({
      ...prev,
      social_media: updatedSocialMedia
    }));

    setIsDirty(true);
    setUnsavedChanges(true);

    // Clear validation error
    if (errors[`social_media_${index}_${field}`]) {
      setErrors(prev => ({ ...prev, [`social_media_${index}_${field}`]: null }));
    }

    // Trigger validation
    if (enableValidation) {
      debouncedValidation();
    }

    handleAnalytics('social_media_field_changed', { index, field, platform: updatedSocialMedia[index].platform });
  }, [formData.social_media, errors, enableValidation, debouncedValidation, handleAnalytics]);

  /**
   * Add new social media platform
   */
  const handleAddSocialMedia = useCallback(() => {
    if (formData.social_media.length >= COMPONENT_CONFIG.MAX_SOCIAL_PLATFORMS) {
      showErrorNotification(`Maximum ${COMPONENT_CONFIG.MAX_SOCIAL_PLATFORMS} social media platforms allowed`);
      return;
    }

    const newPlatform = {
      id: generateUniqueId(),
      platform: '',
      account_name: '',
      account_url: '',
      followers_count: '',
      posts_frequency: ''
    };

    setFormData(prev => ({
      ...prev,
      social_media: [...prev.social_media, newPlatform]
    }));

    setIsDirty(true);
    setUnsavedChanges(true);

    handleAnalytics('social_media_platform_added', { platformCount: formData.social_media.length + 1 });
  }, [formData.social_media.length, showErrorNotification, handleAnalytics]);

  /**
   * Remove social media platform
   */
  const handleRemoveSocialMedia = useCallback((index) => {
    const updatedSocialMedia = [...formData.social_media];
    const removedPlatform = updatedSocialMedia[index];
    updatedSocialMedia.splice(index, 1);

    setFormData(prev => ({
      ...prev,
      social_media: updatedSocialMedia
    }));

    setIsDirty(true);
    setUnsavedChanges(true);

    // Clear any errors for this platform
    const newErrors = { ...errors };
    Object.keys(newErrors).forEach(key => {
      if (key.startsWith(`social_media_${index}_`)) {
        delete newErrors[key];
      }
    });
    setErrors(newErrors);

    handleAnalytics('social_media_platform_removed', {
      platform: removedPlatform.platform,
      platformCount: updatedSocialMedia.length
    });
  }, [formData.social_media, errors, handleAnalytics]);

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    if (loadingState === LOADING_STATES.SAVING) return;

    setLoadingState(LOADING_STATES.SAVING);
    handleAnalytics('form_submit_started');

    const startTime = performance.now();

    try {
      // Final validation
      const validation = validateForm();
      if (!validation.isValid) {
        setLoadingState(LOADING_STATES.ERROR);
        showErrorNotification('Please fix the errors in the form');
        handleAnalytics('form_submit_validation_failed', { errors: validation.errors });
        return;
      }

      // Format data for API
      const apiData = formatCompetitorDataForAPI(formData);

      let result;
      if (isEditMode) {
        // Update existing competitor
        result = await updateCompetitor(competitorId, apiData);
        handleAnalytics('competitor_updated', { competitorId });
      } else {
        // Create new competitor
        result = await createCompetitor(apiData);
        handleAnalytics('competitor_created', { competitorId: result?.id });
      }

      if (result) {
        setLoadingState(LOADING_STATES.SUCCESS);
        setIsDirty(false);
        setUnsavedChanges(false);

        // Clear auto-saved data
        setSavedFormData(null);

        const submitTime = performance.now() - startTime;
        handleAnalytics('form_submit_success', { submitTime });

        showSuccessNotification(
          isEditMode ? 'Competitor updated successfully' : 'Competitor created successfully'
        );

        // Call custom submit handler or navigate
        if (onSubmitProp) {
          onSubmitProp(result);
        } else {
          navigate(`/competitors/${result.id}`);
        }
      }
    } catch (error) {
      setLoadingState(LOADING_STATES.ERROR);
      handleError(error, { action: 'submitForm' });
    }
  }, [
    loadingState, validateForm, formData, isEditMode, competitorId, updateCompetitor,
    createCompetitor, setSavedFormData, onSubmitProp, navigate, handleAnalytics,
    showErrorNotification, showSuccessNotification, handleError
  ]);

  /**
   * Handle form cancellation
   */
  const handleCancel = useCallback(() => {
    if (unsavedChanges) {
      setConfirmDialogOpen(true);
    } else {
      performCancel();
    }
  }, [unsavedChanges, performCancel]);

  /**
   * Perform actual cancellation
   */
  const performCancel = useCallback(() => {
    setConfirmDialogOpen(false);
    setUnsavedChanges(false);

    handleAnalytics('form_cancelled');

    if (onCancelProp) {
      onCancelProp();
    } else {
      navigate(isEditMode ? `/competitors/${competitorId}` : '/competitors');
    }
  }, [onCancelProp, navigate, isEditMode, competitorId, handleAnalytics]);



  /**
   * Handle next step in wizard
   */
  const handleNextStep = useCallback(() => {
    if (currentStep < Object.keys(FORM_STEPS).length - 1) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(prev => prev + 1);
      handleAnalytics('wizard_next_step', { fromStep: currentStep, toStep: currentStep + 1 });
    }
  }, [currentStep, handleAnalytics]);

  /**
   * Handle previous step in wizard
   */
  const handlePreviousStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
      handleAnalytics('wizard_previous_step', { fromStep: currentStep, toStep: currentStep - 1 });
    }
  }, [currentStep, handleAnalytics]);

  // ===========================
  // EFFECTS
  // ===========================

  // Auto-save effect
  useEffect(() => {
    if (enableAutoSave && isDirty && formData.name) {
      autoSaveTimeoutRef.current = setTimeout(() => {
        autoSaveForm();
      }, autoSaveInterval);
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [enableAutoSave, isDirty, formData.name, autoSaveForm, autoSaveInterval]);

  // Validation effect
  useEffect(() => {
    if (enableValidation && isDirty) {
      validationTimeoutRef.current = setTimeout(() => {
        validateForm();
      }, validationDelay);
    }

    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, [enableValidation, isDirty, formData, validateForm, validationDelay]);

  // Announce changes to screen readers
  useEffect(() => {
    if (announceChanges && enableAccessibility) {
      if (loadingState === LOADING_STATES.SUCCESS) {
        announceToScreenReader(
          isEditMode ? 'Competitor updated successfully' : 'Competitor created successfully'
        );
      } else if (loadingState === LOADING_STATES.ERROR) {
        announceToScreenReader('Form submission failed. Please check for errors.');
      }
    }
  }, [loadingState, isEditMode, announceChanges, enableAccessibility]);

  // Warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (unsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [unsavedChanges]);

  // ===========================
  // COMPUTED VALUES
  // ===========================

  const formProgress = useMemo(() => {
    const totalFields = 7; // Basic fields count
    const completedFields = [
      formData.name,
      formData.description,
      formData.website,
      formData.industry,
      formData.company_size,
      formData.target_audience,
      formData.social_media.length > 0
    ].filter(Boolean).length;

    return Math.round((completedFields / totalFields) * 100);
  }, [formData]);

  const canSubmit = useMemo(() => {
    return isValid && !competitorsLoading && loadingState !== LOADING_STATES.SAVING;
  }, [isValid, competitorsLoading, loadingState]);

  const stepIsValid = useMemo(() => {
    if (!enableWizard) return true;

    switch (currentStep) {
      case FORM_STEPS.BASIC_INFO.index:
        return formData.name && !errors.name;
      case FORM_STEPS.COMPANY_DETAILS.index:
        return true; // Optional fields
      case FORM_STEPS.SOCIAL_MEDIA.index:
        return true; // Optional fields
      case FORM_STEPS.REVIEW.index:
        return isValid;
      default:
        return true;
    }
  }, [enableWizard, currentStep, formData.name, errors.name, isValid]);

  // ===========================
  // EARLY RETURNS
  // ===========================

  // Loading state
  if (competitorsLoading && isEditMode) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Error state
  if (competitorsError) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert
          severity="error"
          action={
            <Button color="inherit" size="small" onClick={() => window.location.reload()}>
              Retry
            </Button>
          }
        >
          {competitorsError || 'Failed to load competitor data'}
        </Alert>
      </Container>
    );
  }

  // ===========================
  // MAIN COMPONENT RENDER
  // ===========================

  return (
    <ErrorBoundary
      fallback={(error, retry) => (
        <Alert
          severity="error"
          action={<Button onClick={retry}>Retry</Button>}
        >
          Failed to load competitor form: {error.message}
        </Alert>
      )}
    >
      <Container
        maxWidth="lg"
        sx={{ py: 3 }}
        data-testid={testId}
        role="main"
        aria-label={ariaLabel}
      >
        {/* Header Section */}
        <Paper
          elevation={2}
          sx={{ p: 3, mb: 3, borderRadius: 2 }}
          role="region"
          aria-labelledby="form-header"
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box>
              <Typography
                id="form-header"
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 600,
                  color: 'primary.main',
                  mb: 1
                }}
              >
                {isEditMode ? 'Edit Competitor' : 'Add New Competitor'}
              </Typography>

              <Typography variant="body1" color="text.secondary">
                {isEditMode
                  ? 'Update competitor information and social media tracking'
                  : 'Add a new competitor to track their social media presence and performance'
                }
              </Typography>
            </Box>

            {/* Progress Indicator */}
            <Box sx={{ minWidth: 120, textAlign: 'right' }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Form Progress
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LinearProgress
                  variant="determinate"
                  value={formProgress}
                  sx={{ width: 80, height: 6, borderRadius: 3 }}
                />
                <Typography variant="body2" color="text.secondary">
                  {formProgress}%
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Breadcrumbs */}
          <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
            <Link color="inherit" href="/competitors">
              Competitors
            </Link>
            <Typography color="text.primary">
              {isEditMode ? 'Edit' : 'Add New'}
            </Typography>
          </Breadcrumbs>

          {/* Feature Access Notice */}
          <FeatureGate featureKey="competitor_management" userPlan={userPlan}>
            {userPlan === 'creator' && (
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  You&apos;re using the Creator plan. Upgrade to Accelerator for advanced validation and auto-suggestions.
                </Typography>
              </Alert>
            )}
          </FeatureGate>
        </Paper>

        {/* Main Form */}
        <Box
          component="form"
          onSubmit={handleSubmit}
          ref={formRef}
          sx={{ width: '100%' }}
          noValidate
        >
          {enableWizard ? (
            // Wizard Mode
            <Paper elevation={2} sx={{ borderRadius: 2 }}>
              <Stepper activeStep={currentStep} orientation="horizontal" sx={{ p: 3 }}>
                {Object.values(FORM_STEPS).map((step) => (
                  <Step key={step.index} completed={completedSteps.has(step.index)}>
                    <StepLabel>
                      <Typography variant="subtitle2">{step.label}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {step.description}
                      </Typography>
                    </StepLabel>
                  </Step>
                ))}
              </Stepper>

              <Box sx={{ p: 3 }}>
                {currentStep === FORM_STEPS.BASIC_INFO.index && (
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom>
                        Basic Information
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Competitor Name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        error={!!errors.name}
                        helperText={errors.name || 'Enter the competitor company name'}
                        required
                        autoFocus
                        inputRef={(ref) => fieldRefs.current.name = ref}
                        aria-describedby="name-helper-text"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Website"
                        name="website"
                        value={formData.website}
                        onChange={handleChange}
                        error={!!errors.website}
                        helperText={errors.website || 'Company website URL'}
                        placeholder="https://example.com"
                        type="url"
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LanguageIcon color="action" />
                            </InputAdornment>
                          )
                        }}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Description"
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        error={!!errors.description}
                        helperText={errors.description || warnings.description || 'Brief description of the competitor'}
                        multiline
                        rows={3}
                        placeholder="Describe what this competitor does, their market position, etc."
                        inputProps={{ maxLength: COMPONENT_CONFIG.MAX_DESCRIPTION_LENGTH }}
                      />
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        {formData.description.length}/{COMPONENT_CONFIG.MAX_DESCRIPTION_LENGTH} characters
                      </Typography>
                    </Grid>
                  </Grid>
                )}

                {currentStep === FORM_STEPS.COMPANY_DETAILS.index && (
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom>
                        Company Details
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <FormControl fullWidth error={!!errors.industry}>
                        <InputLabel>Industry</InputLabel>
                        <Select
                          name="industry"
                          value={formData.industry}
                          onChange={handleChange}
                          label="Industry"
                        >
                          {INDUSTRY_OPTIONS.map((category) => [
                            <MenuItem key={category.category} disabled sx={{ fontWeight: 'bold' }}>
                              {category.category}
                            </MenuItem>,
                            ...category.options.map((option) => (
                              <MenuItem key={option} value={option} sx={{ pl: 3 }}>
                                {option}
                              </MenuItem>
                            ))
                          ])}
                        </Select>
                        {errors.industry && <FormHelperText>{errors.industry}</FormHelperText>}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <FormControl fullWidth error={!!errors.company_size}>
                        <InputLabel>Company Size</InputLabel>
                        <Select
                          name="company_size"
                          value={formData.company_size}
                          onChange={handleChange}
                          label="Company Size"
                        >
                          {COMPANY_SIZE_OPTIONS.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Chip
                                  size="small"
                                  color={option.color}
                                  label={option.label}
                                  sx={{ minWidth: 80 }}
                                />
                                <Typography variant="body2" color="text.secondary">
                                  {option.range}
                                </Typography>
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                        {errors.company_size && <FormHelperText>{errors.company_size}</FormHelperText>}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        label="Target Audience"
                        name="target_audience"
                        value={formData.target_audience}
                        onChange={handleChange}
                        error={!!errors.target_audience}
                        helperText={errors.target_audience || 'Who is their primary audience?'}
                        placeholder="e.g., Small businesses, Millennials, Enterprise clients"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.is_active}
                            onChange={handleChange}
                            name="is_active"
                            color="primary"
                          />
                        }
                        label={
                          <Box>
                            <Typography variant="body1">Active Competitor</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Track this competitor&apos;s social media activity
                            </Typography>
                          </Box>
                        }
                      />
                    </Grid>
                  </Grid>
                )}
              </Box>
            </Paper>
          ) : (
            // Standard Form Mode
            <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
              <Grid container spacing={3}>
                {/* Basic Information Section */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <InfoIcon color="primary" />
                    <Typography variant="h6">
                      Basic Information
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Competitor Name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    error={!!errors.name}
                    helperText={errors.name || 'Enter the competitor company name'}
                    required
                    autoFocus={!isMobile}
                    inputRef={(ref) => fieldRefs.current.name = ref}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <AssessmentIcon color="action" />
                        </InputAdornment>
                      )
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Website"
                    name="website"
                    value={formData.website}
                    onChange={handleChange}
                    error={!!errors.website}
                    helperText={errors.website || 'Company website URL'}
                    placeholder="https://example.com"
                    type="url"
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LanguageIcon color="action" />
                        </InputAdornment>
                      )
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    error={!!errors.description}
                    helperText={errors.description || warnings.description || 'Brief description of the competitor'}
                    multiline
                    rows={3}
                    placeholder="Describe what this competitor does, their market position, etc."
                    inputProps={{ maxLength: COMPONENT_CONFIG.MAX_DESCRIPTION_LENGTH }}
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                    {formData.description.length}/{COMPONENT_CONFIG.MAX_DESCRIPTION_LENGTH} characters
                  </Typography>
                </Grid>

                {/* Company Details Section */}
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <TrendingUpIcon color="primary" />
                    <Typography variant="h6">
                      Company Details
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth error={!!errors.industry}>
                    <InputLabel>Industry</InputLabel>
                    <Select
                      name="industry"
                      value={formData.industry}
                      onChange={handleChange}
                      label="Industry"
                    >
                      {INDUSTRY_OPTIONS.map((category) => [
                        <MenuItem key={category.category} disabled sx={{ fontWeight: 'bold' }}>
                          {category.category}
                        </MenuItem>,
                        ...category.options.map((option) => (
                          <MenuItem key={option} value={option} sx={{ pl: 3 }}>
                            {option}
                          </MenuItem>
                        ))
                      ])}
                    </Select>
                    {errors.industry && <FormHelperText>{errors.industry}</FormHelperText>}
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth error={!!errors.company_size}>
                    <InputLabel>Company Size</InputLabel>
                    <Select
                      name="company_size"
                      value={formData.company_size}
                      onChange={handleChange}
                      label="Company Size"
                    >
                      {COMPANY_SIZE_OPTIONS.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                              size="small"
                              color={option.color}
                              label={option.label}
                              sx={{ minWidth: 80 }}
                            />
                            <Typography variant="body2" color="text.secondary">
                              {option.range}
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.company_size && <FormHelperText>{errors.company_size}</FormHelperText>}
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label="Target Audience"
                    name="target_audience"
                    value={formData.target_audience}
                    onChange={handleChange}
                    error={!!errors.target_audience}
                    helperText={errors.target_audience || 'Who is their primary audience?'}
                    placeholder="e.g., Small businesses, Millennials, Enterprise clients"
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={handleChange}
                        name="is_active"
                        color="primary"
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body1">Active Competitor</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Track this competitor&apos;s social media activity
                        </Typography>
                      </Box>
                    }
                  />
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                </Grid>

                {/* Social Media Platforms Section */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LanguageIcon color="primary" />
                      <Typography variant="h6">
                        Social Media Platforms
                      </Typography>
                      <Chip
                        size="small"
                        label={formData.social_media.length}
                        color="primary"
                        variant="outlined"
                      />
                    </Box>

                    <Button
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={handleAddSocialMedia}
                      disabled={formData.social_media.length >= COMPONENT_CONFIG.MAX_SOCIAL_PLATFORMS}
                      size="small"
                    >
                      Add Platform
                    </Button>
                  </Box>

                  {warnings.social_media && (
                    <Alert severity="info" sx={{ mb: 2 }}>
                      {warnings.social_media}
                    </Alert>
                  )}

                  {formData.social_media.length === 0 ? (
                    <EmptyState
                      icon={LanguageIcon}
                      title="No Social Media Platforms"
                      description="Add social media platforms to track competitor activity across different channels."
                      action={
                        <Button
                          variant="contained"
                          startIcon={<AddIcon />}
                          onClick={handleAddSocialMedia}
                        >
                          Add First Platform
                        </Button>
                      }
                    />
                  ) : (
                    formData.social_media.map((platform, index) => {
                      const PlatformIcon = getPlatformIcon(platform.platform);
                      const platformColor = getPlatformColor(platform.platform);

                      return (
                        <Card
                          key={platform.id || index}
                          sx={{
                            mb: 2,
                            border: `1px solid ${alpha(platformColor, 0.3)}`,
                            '&:hover': {
                              boxShadow: theme.shadows[4]
                            }
                          }}
                        >
                          <CardHeader
                            avatar={
                              <Box
                                sx={{
                                  width: 40,
                                  height: 40,
                                  borderRadius: '50%',
                                  backgroundColor: alpha(platformColor, 0.1),
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              >
                                <PlatformIcon sx={{ color: platformColor }} />
                              </Box>
                            }
                            title={
                              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                {platform.platform ?
                                  PLATFORM_OPTIONS.find(p => p.value === platform.platform)?.label || 'Platform' :
                                  `Platform #${index + 1}`
                                }
                              </Typography>
                            }
                            subheader={platform.account_name || 'No account name set'}
                            action={
                              <Tooltip title="Remove platform">
                                <IconButton
                                  color="error"
                                  onClick={() => handleRemoveSocialMedia(index)}
                                  size="small"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            }
                          />

                          <CardContent>
                            <Grid container spacing={2}>
                              <Grid item xs={12} sm={6} md={3}>
                                <FormControl fullWidth error={!!errors[`social_media_${index}_platform`]}>
                                  <InputLabel>Platform</InputLabel>
                                  <Select
                                    value={platform.platform}
                                    onChange={(e) => handleSocialMediaChange(index, 'platform', e.target.value)}
                                    label="Platform"
                                    required
                                  >
                                    {PLATFORM_OPTIONS.map(option => {
                                      const OptionIcon = option.icon;
                                      return (
                                        <MenuItem key={option.value} value={option.value}>
                                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <OptionIcon sx={{ color: option.color, fontSize: 20 }} />
                                            {option.label}
                                          </Box>
                                        </MenuItem>
                                      );
                                    })}
                                  </Select>
                                  {errors[`social_media_${index}_platform`] && (
                                    <FormHelperText>{errors[`social_media_${index}_platform`]}</FormHelperText>
                                  )}
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  fullWidth
                                  label="Account Name"
                                  value={platform.account_name}
                                  onChange={(e) => handleSocialMediaChange(index, 'account_name', e.target.value)}
                                  error={!!errors[`social_media_${index}_account_name`]}
                                  helperText={errors[`social_media_${index}_account_name`]}
                                  placeholder="@username"
                                  required
                                  InputProps={{
                                    startAdornment: (
                                      <InputAdornment position="start">
                                        @
                                      </InputAdornment>
                                    )
                                  }}
                                />
                              </Grid>

                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  fullWidth
                                  label="Account URL"
                                  value={platform.account_url}
                                  onChange={(e) => handleSocialMediaChange(index, 'account_url', e.target.value)}
                                  error={!!errors[`social_media_${index}_account_url`]}
                                  helperText={errors[`social_media_${index}_account_url`] ||
                                    (platform.platform ? PLATFORM_OPTIONS.find(p => p.value === platform.platform)?.placeholder : '')}
                                  placeholder={platform.platform ?
                                    PLATFORM_OPTIONS.find(p => p.value === platform.platform)?.placeholder :
                                    'https://platform.com/username'}
                                  required
                                  type="url"
                                />
                              </Grid>

                              <Grid item xs={12} sm={6} md={3}>
                                <TextField
                                  fullWidth
                                  label="Followers Count"
                                  value={platform.followers_count}
                                  onChange={(e) => handleSocialMediaChange(index, 'followers_count', e.target.value)}
                                  error={!!errors[`social_media_${index}_followers_count`]}
                                  helperText={errors[`social_media_${index}_followers_count`] || 'Approximate follower count'}
                                  type="number"
                                  inputProps={{ min: 0 }}
                                  InputProps={{
                                    startAdornment: (
                                      <InputAdornment position="start">
                                        <TrendingUpIcon color="action" />
                                      </InputAdornment>
                                    )
                                  }}
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <TextField
                                  fullWidth
                                  label="Posting Frequency"
                                  value={platform.posts_frequency}
                                  onChange={(e) => handleSocialMediaChange(index, 'posts_frequency', e.target.value)}
                                  placeholder="e.g., Daily, Weekly, 3 times per week"
                                  helperText="How often does this competitor post on this platform?"
                                  InputProps={{
                                    startAdornment: (
                                      <InputAdornment position="start">
                                        <ScheduleIcon color="action" />
                                      </InputAdornment>
                                    )
                                  }}
                                />
                              </Grid>
                            </Grid>
                          </CardContent>
                        </Card>
                      );
                    })
                  )}
                </Grid>
              </Grid>
            </Paper>
          )}
        </Box>

        {/* Form Actions */}
        <Paper elevation={1} sx={{ p: 3, mt: 3, borderRadius: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              {enableWizard && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    onClick={handlePreviousStep}
                    disabled={currentStep === 0}
                    startIcon={<ArrowBackIcon />}
                  >
                    Previous
                  </Button>

                  {currentStep < Object.keys(FORM_STEPS).length - 1 ? (
                    <Button
                      variant="contained"
                      onClick={handleNextStep}
                      disabled={!stepIsValid}
                      endIcon={<ArrowForwardIcon />}
                    >
                      Next
                    </Button>
                  ) : null}
                </Box>
              )}
            </Box>

            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              {/* Auto-save indicator */}
              {enableAutoSave && isDirty && (
                <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <SaveIcon fontSize="small" />
                  Auto-saving...
                </Typography>
              )}

              {/* Validation status */}
              {enableValidation && (
                <Tooltip title={isValid ? 'Form is valid' : 'Please fix validation errors'}>
                  <Chip
                    size="small"
                    icon={isValid ? <CheckIcon /> : <WarningIcon />}
                    label={isValid ? 'Valid' : 'Invalid'}
                    color={isValid ? 'success' : 'error'}
                    variant="outlined"
                  />
                </Tooltip>
              )}

              <Button
                variant="outlined"
                onClick={handleCancel}
                disabled={loadingState === LOADING_STATES.SAVING}
                startIcon={<CancelIcon />}
              >
                Cancel
              </Button>

              <LoadingButton
                type="submit"
                variant="contained"
                color="primary"
                loading={loadingState === LOADING_STATES.SAVING}
                disabled={!canSubmit}
                loadingPosition="start"
                startIcon={<SaveIcon />}
                size="large"
              >
                {isEditMode ? 'Update Competitor' : 'Create Competitor'}
              </LoadingButton>
            </Box>
          </Box>

          {/* Form Progress for Standard Mode */}
          {!enableWizard && (
            <Box sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  Form Completion
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {formProgress}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={formProgress}
                sx={{ height: 4, borderRadius: 2 }}
              />
            </Box>
          )}
        </Paper>

        {/* Confirmation Dialog */}
        <ConfirmationDialog
          open={confirmDialogOpen}
          title="Unsaved Changes"
          content="You have unsaved changes. Are you sure you want to leave without saving?"
          confirmText="Leave"
          cancelText="Stay"
          onConfirm={performCancel}
          onCancel={() => setConfirmDialogOpen(false)}
          confirmColor="warning"
        />
      </Container>
    </ErrorBoundary>
  );
});

// ===========================
// PROPTYPES & DISPLAY NAME
// ===========================

CompetitorForm.propTypes = {
  // Basic props
  competitorId: PropTypes.string,

  // Enhanced props
  variant: PropTypes.oneOf(Object.values(FORM_VARIANTS)),
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAutoSave: PropTypes.bool,
  enableValidation: PropTypes.bool,
  enableWizard: PropTypes.bool,

  // Callback props
  onSubmit: PropTypes.func,
  onCancel: PropTypes.func,
  onFieldChange: PropTypes.func,
  onValidationChange: PropTypes.func,
  onError: PropTypes.func,

  // User context props
  userPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),
  userId: PropTypes.string,

  // Testing props
  testId: PropTypes.string,

  // Advanced props
  autoSaveInterval: PropTypes.number,
  validationDelay: PropTypes.number,

  // Accessibility props
  ariaLabel: PropTypes.string,
  announceChanges: PropTypes.bool
};

CompetitorForm.defaultProps = {
  variant: FORM_VARIANTS.STANDARD,
  enableAnalytics: true,
  enableAccessibility: true,
  enableAutoSave: true,
  enableValidation: true,
  enableWizard: false,
  userPlan: 'creator',
  testId: 'competitor-form',
  autoSaveInterval: COMPONENT_CONFIG.AUTO_SAVE_INTERVAL,
  validationDelay: COMPONENT_CONFIG.VALIDATION_DELAY,
  ariaLabel: 'Competitor form',
  announceChanges: true
};

CompetitorForm.displayName = 'CompetitorForm';

export default CompetitorForm;

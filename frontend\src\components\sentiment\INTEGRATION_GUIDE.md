<!-- @since 2024-1-1 to 2025-25-7 -->
# Sentiment Analysis Integration Guide

## 🎯 Overview

This guide shows how to integrate the existing enterprise-grade sentiment analysis components throughout the ACE Social platform, replacing the deprecated `SentimentPreview` component.

## ✅ Migration Complete

- ✅ **Removed**: `frontend/src/components/content/SentimentPreview.jsx` (duplicate component)
- ✅ **Updated**: `ConsolidatedContentGenerator.jsx` to use existing sentiment components
- ✅ **Created**: Central export hub (`index.js`) for easy imports

## 🚀 Quick Start

### Basic Import Pattern
```jsx
// Import individual components
import { SentimentOverviewCards, SentimentDashboard } from '../sentiment';

// Or import specific components
import SentimentOverviewCards from '../sentiment/SentimentOverviewCards';
```

## 📋 Component APIs

### 1. SentimentOverviewCards (Content Preview Replacement)

**Use Case**: Replace old `SentimentPreview` in content creation workflows

```jsx
import { SentimentOverviewCards } from '../sentiment';

<SentimentOverviewCards
  timeRange={1}              // Number of days (1 for current content)
  onCardClick={(type) => {}} // Optional click handler
  refreshTrigger={0}         // Trigger refresh when changed
/>
```

**Props**:
- `timeRange` (number): Days to analyze (default: 30)
- `onCardClick` (function): Click handler for cards
- `refreshTrigger` (number): Increment to trigger refresh

### 2. SentimentDashboard (Full Analysis)

**Use Case**: Comprehensive sentiment analysis dashboard

```jsx
import { SentimentDashboard } from '../sentiment';

<SentimentDashboard />
```

### 3. SentimentDistributionChart (Quick Insights)

**Use Case**: Visual sentiment breakdown

```jsx
import { SentimentDistributionChart } from '../sentiment';

<SentimentDistributionChart
  timeRange={7}              // Days to analyze
  platforms={['all']}        // Platforms to include
  height={400}               // Chart height
  refreshTrigger={0}         // Trigger refresh
/>
```

### 4. KeywordAnalysisWidget (Keyword Insights)

**Use Case**: Top keywords driving sentiment

```jsx
import { KeywordAnalysisWidget } from '../sentiment';

<KeywordAnalysisWidget
  timeRange={7}              // Days to analyze
  topCount={10}              // Number of keywords
  refreshTrigger={0}         // Trigger refresh
/>
```

## 🔧 Integration Examples

### Content Creation Preview
```jsx
// Replace old SentimentPreview usage
const ContentEditor = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={8}>
        {/* Content editing UI */}
      </Grid>
      <Grid item xs={12} md={4}>
        <SentimentOverviewCards
          timeRange={1}
          compact={true}
          showOnlyOverview={true}
        />
      </Grid>
    </Grid>
  );
};
```

### Analytics Dashboard
```jsx
const AnalyticsDashboard = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <SentimentDashboard />
      </Grid>
    </Grid>
  );
};
```

### Quick Sentiment Insights
```jsx
const QuickInsights = () => {
  return (
    <Grid container spacing={2}>
      <Grid item xs={12} md={6}>
        <SentimentDistributionChart 
          timeRange={7}
          height={300}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <KeywordAnalysisWidget 
          timeRange={7}
          topCount={5}
        />
      </Grid>
    </Grid>
  );
};
```

## 🔐 Subscription Integration

All sentiment components include built-in subscription checking:

```jsx
// Components automatically handle subscription access
const { hasFeatureAccess } = useSubscription();

// If user doesn't have access, components show upgrade prompts
if (!hasFeatureAccess('sentiment_analysis')) {
  return <UpgradePrompt />;
}
```

## 🎨 ACE Social Branding

Components use ACE Social brand colors:
- **Primary**: `#4E40C5` (Purple)
- **Success**: `#00D68F` (Green) 
- **Error**: `#FF3D71` (Red)
- **Dark**: `#15110E`
- **Yellow**: `#EBAE1B`

## 📊 Data Format

Components expect data in this format:
```javascript
{
  overall_sentiment: 'positive',
  sentiment_score: 0.75,
  confidence: 0.85,
  total_posts: 150,
  sentiment_distribution: {
    very_positive: 25,
    positive: 45,
    neutral: 50,
    negative: 20,
    very_negative: 10
  }
}
```

## 🧪 Testing

Components include comprehensive test coverage:
```bash
# Run sentiment-specific tests
npm test -- --config=jest.sentiment.config.js

# Run with coverage
npm test -- --config=jest.sentiment.config.js --coverage
```

## 🚀 Performance

- **Memoized**: All components use React.memo and useCallback
- **Lazy Loading**: Components load data on demand
- **Error Boundaries**: Built-in error handling
- **Accessibility**: WCAG 2.1 AA compliant

## 📈 Next Steps

1. **Update Imports**: Replace any remaining `SentimentPreview` imports
2. **Test Integration**: Verify components work in your workflows
3. **Customize**: Adjust props for your specific use cases
4. **Monitor**: Use built-in analytics to track usage

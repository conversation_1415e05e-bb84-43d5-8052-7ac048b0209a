"""
Production readiness service for ACEO add-on system.
Includes comprehensive logging, performance optimization, security validation,
monitoring, documentation, and A/B testing framework.
"""
import logging
import time
import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum
import hashlib
import json

from app.core.monitoring import (
    record_addon_metrics, 
    record_addon_purchase_metrics,
    record_addon_usage_metrics,
    record_addon_recommendation_metrics
)
from app.core.redis import (
    redis_manager, redis_get, redis_set, redis_delete, redis_setex,
    redis_incr, redis_expire, redis_lpush, redis_llen, redis_lrange, redis_incrbyfloat
)
from app.services.addon_error_handler import handle_addon_error, AddonErrorType

logger = logging.getLogger(__name__)


class SecurityLevel(str, Enum):
    """Security validation levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class PerformanceMetric(str, Enum):
    """Performance metrics to track."""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    ERROR_RATE = "error_rate"
    CACHE_HIT_RATE = "cache_hit_rate"
    MEMORY_USAGE = "memory_usage"


@dataclass
class ABTestVariant:
    """A/B test variant configuration."""
    name: str
    weight: float
    config: Dict[str, Any]
    is_control: bool = False


@dataclass
class SecurityValidationResult:
    """Security validation result."""
    is_valid: bool
    security_level: SecurityLevel
    violations: List[str]
    recommendations: List[str]


class AddonProductionReadiness:
    """Production readiness manager for add-on system."""
    
    def __init__(self):
        self.performance_cache = {}
        self.security_validators = self._initialize_security_validators()
        self.ab_tests = {}
        self.feature_flags = {}
        
    def _initialize_security_validators(self) -> Dict[str, Callable]:
        """Initialize security validation functions."""
        return {
            "addon_purchase": self._validate_purchase_security,
            "usage_tracking": self._validate_usage_security,
            "feature_access": self._validate_access_security,
            "data_export": self._validate_export_security
        }
    
    async def validate_security(self, operation: str, user_id: int, 
                              data: Dict[str, Any]) -> SecurityValidationResult:
        """Comprehensive security validation for add-on operations."""
        try:
            violations = []
            recommendations = []
            security_level = SecurityLevel.LOW
            
            # Get validator for operation
            validator = self.security_validators.get(operation)
            if not validator:
                violations.append(f"No security validator found for operation: {operation}")
                return SecurityValidationResult(False, SecurityLevel.CRITICAL, violations, recommendations)
            
            # Run validation
            validation_result = await validator(user_id, data)
            violations.extend(validation_result.get("violations", []))
            recommendations.extend(validation_result.get("recommendations", []))
            security_level = validation_result.get("security_level", SecurityLevel.MEDIUM)
            
            # Additional common validations
            common_violations = await self._validate_common_security(user_id, data)
            violations.extend(common_violations)
            
            # Determine overall security level
            if violations:
                if any("critical" in v.lower() for v in violations):
                    security_level = SecurityLevel.CRITICAL
                elif any("high" in v.lower() for v in violations):
                    security_level = SecurityLevel.HIGH
                else:
                    security_level = SecurityLevel.MEDIUM
            
            is_valid = security_level in [SecurityLevel.LOW, SecurityLevel.MEDIUM]
            
            # Log security events
            if violations:
                logger.warning(f"Security violations for user {user_id}, operation {operation}: {violations}")
            
            return SecurityValidationResult(is_valid, security_level, violations, recommendations)
            
        except Exception as e:
            logger.error(f"Error in security validation: {str(e)}")
            return SecurityValidationResult(
                False, 
                SecurityLevel.CRITICAL, 
                [f"Security validation failed: {str(e)}"], 
                ["Contact security team"]
            )
    
    async def _validate_purchase_security(self, user_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate add-on purchase security."""
        violations = []
        recommendations = []
        
        # Check for suspicious purchase patterns
        purchase_key = f"purchase_pattern:{user_id}"
        recent_purchases = await redis_get(purchase_key) or 0
        recent_purchases = int(recent_purchases)
        
        if recent_purchases > 5:  # More than 5 purchases in last hour
            violations.append("Suspicious purchase frequency detected")
            recommendations.append("Implement purchase rate limiting")
        
        # Validate purchase amount
        amount = data.get("amount", 0)
        if amount > 1000:  # $1000 threshold
            violations.append("High-value purchase requires additional verification")
            recommendations.append("Require additional authentication for high-value purchases")
        
        # Check payment method validation
        if not data.get("payment_method_verified"):
            violations.append("Payment method not verified")
            recommendations.append("Require payment method verification")
        
        return {
            "violations": violations,
            "recommendations": recommendations,
            "security_level": SecurityLevel.MEDIUM if violations else SecurityLevel.LOW
        }
    
    async def _validate_usage_security(self, user_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate usage tracking security."""
        violations = []
        recommendations = []
        
        # Check for usage anomalies
        usage_amount = data.get("amount", 1)
        if usage_amount > 100:  # Unusually high single usage
            violations.append("Anomalous usage amount detected")
            recommendations.append("Implement usage amount validation")
        
        # Validate usage type
        allowed_usage_types = [
            "regeneration_credits", "image_generation", "sentiment_analysis", 
            "auto_replies", "team_seats", "priority_support"
        ]
        
        usage_type = data.get("usage_type")
        if usage_type not in allowed_usage_types:
            violations.append(f"Invalid usage type: {usage_type}")
            recommendations.append("Validate usage types against whitelist")
        
        return {
            "violations": violations,
            "recommendations": recommendations,
            "security_level": SecurityLevel.LOW
        }
    
    async def _validate_access_security(self, user_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate feature access security."""
        violations = []
        recommendations = []
        
        # Check for privilege escalation attempts
        requested_feature = data.get("feature")
        user_plan = data.get("user_plan", "creator")
        
        # Define feature access matrix
        feature_access = {
            "white_label": ["dominator"],
            "priority_support": ["accelerator", "dominator"],
            "advanced_analytics": ["accelerator", "dominator"]
        }
        
        if requested_feature in feature_access:
            allowed_plans = feature_access[requested_feature]
            if user_plan not in allowed_plans:
                violations.append(f"Unauthorized access attempt to {requested_feature}")
                recommendations.append("Implement strict feature access controls")
        
        return {
            "violations": violations,
            "recommendations": recommendations,
            "security_level": SecurityLevel.MEDIUM if violations else SecurityLevel.LOW
        }
    
    async def _validate_export_security(self, user_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data export security."""
        violations = []
        recommendations = []
        
        # Check export permissions
        export_type = data.get("export_type")
        sensitive_exports = ["user_data", "payment_info", "analytics_data"]
        
        if export_type in sensitive_exports:
            violations.append("Sensitive data export requires additional authorization")
            recommendations.append("Implement multi-factor authentication for sensitive exports")
        
        return {
            "violations": violations,
            "recommendations": recommendations,
            "security_level": SecurityLevel.HIGH if violations else SecurityLevel.LOW
        }
    
    async def _validate_common_security(self, user_id: int, data: Dict[str, Any]) -> List[str]:
        """Common security validations."""
        violations = []
        
        # Rate limiting check
        rate_limit_key = f"rate_limit:{user_id}"
        current_requests = await redis_get(rate_limit_key) or 0
        current_requests = int(current_requests) + 1
        
        if current_requests > 100:  # 100 requests per minute
            violations.append("Rate limit exceeded")
        
        await redis_setex(rate_limit_key, 60, str(current_requests))
        
        # Input validation
        for key, value in data.items():
            if isinstance(value, str) and len(value) > 10000:  # 10KB limit
                violations.append(f"Input too large for field: {key}")
        
        return violations
    
    async def optimize_performance(self, operation: str, func: Callable, 
                                 *args, **kwargs) -> Any:
        """Performance optimization wrapper for add-on operations."""
        start_time = time.time()
        cache_key = None
        
        try:
            # Generate cache key for cacheable operations
            if operation in ["get_addon_catalog", "get_user_addons", "get_recommendations"]:
                cache_key = self._generate_cache_key(operation, args, kwargs)
                cached_result = await redis_get(cache_key)
                
                if cached_result:
                    # Cache hit
                    record_addon_metrics("cache_hit", "system", 1)
                    return json.loads(cached_result)
            
            # Execute function
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Cache result if applicable
            if cache_key and result:
                cache_ttl = self._get_cache_ttl(operation)
                await redis_setex(cache_key, cache_ttl, json.dumps(result, default=str))
                record_addon_metrics("cache_miss", "system", 1)
            
            # Record performance metrics
            duration = time.time() - start_time
            record_addon_metrics(f"performance_{operation}", "system", int(duration * 1000))
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            record_addon_metrics(f"error_{operation}", "system", 1)
            
            # Handle error with graceful degradation
            await handle_addon_error(
                AddonErrorType.EXTERNAL_SERVICE_ERROR,
                kwargs.get("user_id", 0),
                operation=operation,
                details={"error": str(e), "duration": duration}
            )
            
            raise
    
    def _generate_cache_key(self, operation: str, args: tuple, kwargs: dict) -> str:
        """Generate cache key for operation."""
        # Create deterministic hash of operation and parameters
        key_data = {
            "operation": operation,
            "args": args,
            "kwargs": {k: v for k, v in kwargs.items() if k != "db"}  # Exclude DB session
        }
        
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        
        return f"addon_cache:{operation}:{key_hash}"
    
    def _get_cache_ttl(self, operation: str) -> int:
        """Get cache TTL for operation."""
        cache_ttls = {
            "get_addon_catalog": 300,      # 5 minutes
            "get_user_addons": 60,         # 1 minute
            "get_recommendations": 600,    # 10 minutes
            "get_usage_analytics": 300     # 5 minutes
        }
        
        return cache_ttls.get(operation, 300)
    
    async def setup_ab_test(self, test_name: str, variants: List[ABTestVariant]) -> bool:
        """Setup A/B test for add-on features."""
        try:
            # Validate variants
            total_weight = sum(variant.weight for variant in variants)
            if abs(total_weight - 1.0) > 0.01:  # Allow small floating point errors
                logger.error(f"A/B test {test_name} variant weights don't sum to 1.0: {total_weight}")
                return False
            
            # Store test configuration
            test_config = {
                "name": test_name,
                "variants": [
                    {
                        "name": variant.name,
                        "weight": variant.weight,
                        "config": variant.config,
                        "is_control": variant.is_control
                    }
                    for variant in variants
                ],
                "created_at": datetime.now(timezone.utc).isoformat(),
                "is_active": True
            }
            
            await redis_set(f"ab_test:{test_name}", json.dumps(test_config))
            self.ab_tests[test_name] = test_config
            
            logger.info(f"A/B test {test_name} setup successfully with {len(variants)} variants")
            return True
            
        except Exception as e:
            logger.error(f"Error setting up A/B test {test_name}: {str(e)}")
            return False
    
    async def get_ab_test_variant(self, test_name: str, user_id: int) -> Optional[Dict[str, Any]]:
        """Get A/B test variant for user."""
        try:
            test_config = self.ab_tests.get(test_name)
            if not test_config:
                # Try to load from Redis
                cached_config = await redis_get(f"ab_test:{test_name}")
                if cached_config:
                    test_config = json.loads(cached_config)
                    self.ab_tests[test_name] = test_config
                else:
                    return None
            
            if not test_config.get("is_active"):
                return None
            
            # Deterministic variant assignment based on user ID
            user_hash = hashlib.md5(f"{test_name}:{user_id}".encode()).hexdigest()
            hash_value = int(user_hash[:8], 16) / (2**32)  # Convert to 0-1 range
            
            # Select variant based on weights
            cumulative_weight = 0
            for variant in test_config["variants"]:
                cumulative_weight += variant["weight"]
                if hash_value <= cumulative_weight:
                    # Record assignment
                    record_addon_metrics(f"ab_test_{test_name}_{variant['name']}", "assignment", 1)
                    return variant
            
            # Fallback to control variant
            control_variant = next((v for v in test_config["variants"] if v.get("is_control")), None)
            return control_variant or test_config["variants"][0]
            
        except Exception as e:
            logger.error(f"Error getting A/B test variant for {test_name}: {str(e)}")
            return None
    
    async def record_ab_test_conversion(self, test_name: str, user_id: int, 
                                      conversion_type: str, value: float = 1.0):
        """Record A/B test conversion event."""
        try:
            variant = await self.get_ab_test_variant(test_name, user_id)
            if not variant:
                return
            
            # Record conversion
            conversion_key = f"ab_conversion:{test_name}:{variant['name']}:{conversion_type}"
            await redis_incrbyfloat(conversion_key, value)
            await redis_expire(conversion_key, 30 * 24 * 3600)  # 30 days
            
            record_addon_metrics(
                f"ab_conversion_{test_name}_{variant['name']}_{conversion_type}", 
                "conversion", 
                int(value)
            )
            
        except Exception as e:
            logger.error(f"Error recording A/B test conversion: {str(e)}")
    
    async def set_feature_flag(self, flag_name: str, enabled: bool, 
                             conditions: Optional[Dict[str, Any]] = None):
        """Set feature flag for gradual rollout."""
        try:
            flag_config = {
                "enabled": enabled,
                "conditions": conditions or {},
                "updated_at": datetime.now(timezone.utc).isoformat()
            }
            
            await redis_set(f"feature_flag:{flag_name}", json.dumps(flag_config))
            self.feature_flags[flag_name] = flag_config
            
            logger.info(f"Feature flag {flag_name} set to {enabled}")
            
        except Exception as e:
            logger.error(f"Error setting feature flag {flag_name}: {str(e)}")
    
    async def is_feature_enabled(self, flag_name: str, user_id: int, 
                                user_plan: str = "creator") -> bool:
        """Check if feature is enabled for user."""
        try:
            flag_config = self.feature_flags.get(flag_name)
            if not flag_config:
                # Try to load from Redis
                cached_config = await redis_get(f"feature_flag:{flag_name}")
                if cached_config:
                    flag_config = json.loads(cached_config)
                    self.feature_flags[flag_name] = flag_config
                else:
                    return False  # Default to disabled
            
            if not flag_config.get("enabled"):
                return False
            
            # Check conditions
            conditions = flag_config.get("conditions", {})
            
            # Plan-based conditions
            if "plans" in conditions:
                if user_plan not in conditions["plans"]:
                    return False
            
            # Percentage rollout
            if "percentage" in conditions:
                user_hash = hashlib.md5(f"{flag_name}:{user_id}".encode()).hexdigest()
                hash_value = int(user_hash[:8], 16) / (2**32) * 100
                if hash_value > conditions["percentage"]:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking feature flag {flag_name}: {str(e)}")
            return False
    
    async def generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report for add-on system."""
        try:
            report = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "overall_status": "healthy",
                "components": {},
                "metrics": {},
                "recommendations": []
            }
            
            # Check component health
            components = ["catalog", "usage_tracking", "marketing", "billing", "error_handling"]
            
            for component in components:
                health_key = f"component_health:{component}"
                health_data = await redis_get(health_key)
                
                if health_data:
                    report["components"][component] = json.loads(health_data)
                else:
                    report["components"][component] = {"status": "unknown", "last_check": None}
            
            # Aggregate metrics
            error_rate = await self._calculate_error_rate()
            response_time = await self._calculate_avg_response_time()
            
            report["metrics"] = {
                "error_rate": error_rate,
                "avg_response_time": response_time,
                "cache_hit_rate": await self._calculate_cache_hit_rate()
            }
            
            # Generate recommendations
            if error_rate > 0.05:  # 5% error rate
                report["recommendations"].append("High error rate detected - investigate error patterns")
                report["overall_status"] = "degraded"
            
            if response_time > 2000:  # 2 seconds
                report["recommendations"].append("High response times - consider performance optimization")
                report["overall_status"] = "degraded"
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating health report: {str(e)}")
            return {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "overall_status": "error",
                "error": str(e)
            }
    
    async def _calculate_error_rate(self) -> float:
        """Calculate current error rate."""
        try:
            total_requests = await redis_get("total_addon_requests") or 0
            total_errors = await redis_get("total_addon_errors") or 0
            
            if int(total_requests) == 0:
                return 0.0
            
            return int(total_errors) / int(total_requests)
        except:
            return 0.0
    
    async def _calculate_avg_response_time(self) -> float:
        """Calculate average response time."""
        try:
            total_time = await redis_get("total_addon_response_time") or 0
            total_requests = await redis_get("total_addon_requests") or 0
            
            if int(total_requests) == 0:
                return 0.0
            
            return float(total_time) / int(total_requests)
        except:
            return 0.0
    
    async def _calculate_cache_hit_rate(self) -> float:
        """Calculate cache hit rate."""
        try:
            cache_hits = await redis_get("addon_cache_hits") or 0
            cache_misses = await redis_get("addon_cache_misses") or 0
            
            total = int(cache_hits) + int(cache_misses)
            if total == 0:
                return 0.0
            
            return int(cache_hits) / total
        except:
            return 0.0


# Global production readiness manager
production_manager = AddonProductionReadiness()


# Decorator for performance monitoring
def monitor_addon_performance(operation: str):
    """Decorator to monitor add-on operation performance."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            return await production_manager.optimize_performance(operation, func, *args, **kwargs)
        return wrapper
    return decorator


# Security validation decorator
def validate_addon_security(operation: str):
    """Decorator to validate add-on operation security."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract user_id and data from arguments
            user_id = kwargs.get("user_id") or (args[0] if args else 0)
            data = kwargs.get("data", {})
            
            # Validate security
            validation_result = await production_manager.validate_security(operation, user_id, data)
            
            if not validation_result.is_valid:
                raise SecurityError(f"Security validation failed: {validation_result.violations}")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


class SecurityError(Exception):
    """Security validation error."""
    pass

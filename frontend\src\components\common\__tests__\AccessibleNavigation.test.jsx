import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import AccessibleNavigation from '../AccessibleNavigation';

// Mock the accessibility hook
const mockAccessibility = {
  announceToScreenReader: vi.fn(),
  setFocusToElement: vi.fn(),
  isHighContrastMode: false,
  isReducedMotion: false
};

vi.mock('../../../hooks/useAccessibility', () => ({
  useAccessibility: () => mockAccessibility,
}));

// Mock react-router-dom hooks
const mockNavigate = vi.fn();
const mockLocation = {
  pathname: '/dashboard'
};

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
  };
});

// Test wrapper with theme and router
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>{children}</ThemeProvider>
    </BrowserRouter>
  );
};

// Mock navigation items
const mockNavigationItems = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: null
  },
  {
    id: 'campaigns',
    label: 'Campaigns',
    path: '/campaigns',
    icon: null,
    children: [
      {
        id: 'create-campaign',
        label: 'Create Campaign',
        path: '/campaigns/create'
      },
      {
        id: 'manage-campaigns',
        label: 'Manage Campaigns',
        path: '/campaigns/manage'
      }
    ]
  },
  {
    id: 'analytics',
    label: 'Analytics',
    path: '/analytics',
    icon: null
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: null
  }
];

describe('AccessibleNavigation', () => {
  const mockProps = {
    navigationItems: mockNavigationItems,
    ariaLabel: 'Main navigation',
    onItemClick: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders accessible navigation correctly', () => {
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    // Should render navigation with proper ARIA label
    expect(screen.getByRole('navigation', { name: 'Main navigation' })).toBeInTheDocument();
    
    // Should render navigation items
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Campaigns')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  test('handles keyboard navigation with arrow keys', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const navigation = screen.getByRole('navigation');
    
    // Focus on navigation
    navigation.focus();
    
    // Navigate with arrow keys
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{ArrowUp}');

    // Should handle keyboard navigation
    expect(navigation).toBeInTheDocument();
  });

  test('handles item selection with Enter and Space keys', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const dashboardItem = screen.getByRole('button', { name: /dashboard/i });
    
    // Focus and press Enter
    dashboardItem.focus();
    await user.keyboard('{Enter}');

    expect(mockProps.onItemClick).toHaveBeenCalledWith(
      expect.objectContaining({ id: 'dashboard' })
    );
  });

  test('handles expandable navigation items', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const campaignsItem = screen.getByRole('button', { name: /campaigns/i });
    
    // Click to expand
    await user.click(campaignsItem);

    // Should show child items
    expect(screen.getByText('Create Campaign')).toBeInTheDocument();
    expect(screen.getByText('Manage Campaigns')).toBeInTheDocument();
  });

  test('handles collapsible navigation items', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const campaignsItem = screen.getByRole('button', { name: /campaigns/i });
    
    // Expand first
    await user.click(campaignsItem);
    expect(screen.getByText('Create Campaign')).toBeInTheDocument();

    // Collapse
    await user.click(campaignsItem);
    
    // Child items should be hidden
    await waitFor(() => {
      expect(screen.queryByText('Create Campaign')).not.toBeVisible();
    });
  });

  test('highlights current page item', () => {
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    // Dashboard should be highlighted as current page
    const dashboardItem = screen.getByRole('button', { name: /dashboard/i });
    expect(dashboardItem).toHaveAttribute('aria-current', 'page');
  });

  test('handles focus management correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const navigation = screen.getByRole('navigation');
    
    // Focus should be manageable
    navigation.focus();
    expect(navigation).toHaveFocus();

    // Tab navigation should work
    await user.tab();
    
    // Should move focus to first navigation item
    const firstItem = screen.getByRole('button', { name: /dashboard/i });
    expect(firstItem).toHaveFocus();
  });

  test('provides proper ARIA attributes', () => {
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    // Navigation should have proper role and label
    const navigation = screen.getByRole('navigation', { name: 'Main navigation' });
    expect(navigation).toBeInTheDocument();

    // Expandable items should have proper ARIA attributes
    const campaignsItem = screen.getByRole('button', { name: /campaigns/i });
    expect(campaignsItem).toHaveAttribute('aria-expanded', 'false');
  });

  test('handles screen reader announcements', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const dashboardItem = screen.getByRole('button', { name: /dashboard/i });
    
    // Click item
    await user.click(dashboardItem);

    // Should announce to screen reader
    expect(mockAccessibility.announceToScreenReader).toHaveBeenCalledWith(
      'Navigated to Dashboard'
    );
  });

  test('handles child item navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    // Expand campaigns
    const campaignsItem = screen.getByRole('button', { name: /campaigns/i });
    await user.click(campaignsItem);

    // Click child item
    const createCampaignItem = screen.getByRole('button', { name: /create campaign/i });
    await user.click(createCampaignItem);

    expect(mockProps.onItemClick).toHaveBeenCalledWith(
      expect.objectContaining({ id: 'create-campaign' })
    );
  });

  test('handles empty navigation items', () => {
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} navigationItems={[]} />
      </TestWrapper>
    );

    // Should render navigation container
    expect(screen.getByRole('navigation')).toBeInTheDocument();
    
    // Should not have any navigation items
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  test('handles navigation without onItemClick callback', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} onItemClick={null} />
      </TestWrapper>
    );

    const dashboardItem = screen.getByRole('button', { name: /dashboard/i });
    
    // Should not throw error when clicking without callback
    await user.click(dashboardItem);
    
    expect(dashboardItem).toBeInTheDocument();
  });

  test('supports custom styling', () => {
    const customSx = { backgroundColor: 'red' };
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} sx={customSx} />
      </TestWrapper>
    );

    // Should apply custom styles
    const navigation = screen.getByRole('navigation');
    expect(navigation).toBeInTheDocument();
  });

  test('handles keyboard shortcuts', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const navigation = screen.getByRole('navigation');
    navigation.focus();

    // Test Home key (should focus first item)
    await user.keyboard('{Home}');
    
    const firstItem = screen.getByRole('button', { name: /dashboard/i });
    expect(firstItem).toHaveFocus();

    // Test End key (should focus last item)
    await user.keyboard('{End}');
    
    const lastItem = screen.getByRole('button', { name: /settings/i });
    expect(lastItem).toHaveFocus();
  });

  test('handles nested navigation levels', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    // Expand campaigns to show nested items
    const campaignsItem = screen.getByRole('button', { name: /campaigns/i });
    await user.click(campaignsItem);

    // Should show nested items with proper indentation
    expect(screen.getByText('Create Campaign')).toBeInTheDocument();
    expect(screen.getByText('Manage Campaigns')).toBeInTheDocument();
  });

  test('maintains focus after item expansion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const campaignsItem = screen.getByRole('button', { name: /campaigns/i });
    
    // Focus and expand
    campaignsItem.focus();
    await user.click(campaignsItem);

    // Focus should remain on the expanded item
    expect(campaignsItem).toHaveFocus();
  });

  test('handles rapid keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const navigation = screen.getByRole('navigation');
    navigation.focus();

    // Rapid arrow key presses
    await user.keyboard('{ArrowDown}{ArrowDown}{ArrowDown}{ArrowUp}{ArrowUp}');

    // Should handle rapid navigation without errors
    expect(navigation).toBeInTheDocument();
  });

  test('provides proper list structure', () => {
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    // Should use proper list structure for screen readers
    expect(screen.getByRole('list')).toBeInTheDocument();
    
    // Should have proper list items
    const listItems = screen.getAllByRole('listitem');
    expect(listItems.length).toBeGreaterThan(0);
  });

  test('handles focus trap within navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleNavigation {...mockProps} />
      </TestWrapper>
    );

    const navigation = screen.getByRole('navigation');
    
    // Focus should be contained within navigation when using keyboard
    navigation.focus();
    await user.keyboard('{Tab}');
    
    // Focus should move to first navigation item
    const firstItem = screen.getByRole('button', { name: /dashboard/i });
    expect(firstItem).toHaveFocus();
  });
});

"""
Admin API routes for metadata removal monitoring and management.

This module provides admin-only endpoints for monitoring metadata removal
operations, viewing statistics, and managing configuration.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from app.core.security import get_current_active_user
from app.models.user import User
from app.services.metadata_removal_monitoring import (
    get_user_metadata_removal_stats,
    get_global_metadata_removal_stats,
    metadata_removal_monitor,
    MetadataRemovalStats
)
from app.services.image_metadata_removal import metadata_remover
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

def require_admin(current_user: User = Depends(get_current_active_user)) -> User:
    """Require admin privileges for accessing these endpoints."""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user

@router.get("/stats/global", response_model=Dict[str, Any])
async def get_global_metadata_removal_stats_endpoint(
    hours: int = Query(24, ge=1, le=168, description="Hours to look back (1-168)"),
    admin_user: User = Depends(require_admin)
):
    """
    Get global metadata removal statistics.
    
    Returns comprehensive statistics for all metadata removal operations
    across the platform for the specified time period.
    """
    try:
        stats = await get_global_metadata_removal_stats(hours)
        
        if not stats:
            return {
                "message": "No metadata removal operations found for the specified period",
                "hours": hours,
                "stats": None
            }
        
        return {
            "hours": hours,
            "stats": {
                "total_operations": stats.total_operations,
                "total_images_processed": stats.total_images_processed,
                "total_processing_time_ms": stats.total_processing_time_ms,
                "average_processing_time_ms": stats.average_processing_time_ms,
                "success_rate": stats.success_rate,
                "cache_hit_rate": stats.cache_hit_rate,
                "total_size_reduction_bytes": stats.total_size_reduction_bytes,
                "average_size_reduction_percent": stats.average_size_reduction_percent,
                "total_ai_tags_removed": stats.total_ai_tags_removed,
                "total_metadata_tags_removed": stats.total_metadata_tags_removed,
                "error_count": stats.error_count,
                "performance_target_met_rate": stats.performance_target_met_rate
            },
            "performance_target_ms": settings.METADATA_REMOVAL_PERFORMANCE_TARGET_MS,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting global metadata removal stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve global statistics: {str(e)}"
        )

@router.get("/stats/user/{user_id}", response_model=Dict[str, Any])
async def get_user_metadata_removal_stats_endpoint(
    user_id: str,
    hours: int = Query(24, ge=1, le=168, description="Hours to look back (1-168)"),
    admin_user: User = Depends(require_admin)
):
    """
    Get metadata removal statistics for a specific user.
    
    Returns comprehensive statistics for metadata removal operations
    performed by the specified user.
    """
    try:
        stats = await get_user_metadata_removal_stats(user_id, hours)
        
        if not stats:
            return {
                "message": f"No metadata removal operations found for user {user_id}",
                "user_id": user_id,
                "hours": hours,
                "stats": None
            }
        
        return {
            "user_id": user_id,
            "hours": hours,
            "stats": {
                "total_operations": stats.total_operations,
                "total_images_processed": stats.total_images_processed,
                "total_processing_time_ms": stats.total_processing_time_ms,
                "average_processing_time_ms": stats.average_processing_time_ms,
                "success_rate": stats.success_rate,
                "cache_hit_rate": stats.cache_hit_rate,
                "total_size_reduction_bytes": stats.total_size_reduction_bytes,
                "average_size_reduction_percent": stats.average_size_reduction_percent,
                "total_ai_tags_removed": stats.total_ai_tags_removed,
                "total_metadata_tags_removed": stats.total_metadata_tags_removed,
                "error_count": stats.error_count,
                "performance_target_met_rate": stats.performance_target_met_rate
            },
            "performance_target_ms": settings.METADATA_REMOVAL_PERFORMANCE_TARGET_MS,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting user metadata removal stats for {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve user statistics: {str(e)}"
        )

@router.get("/alerts", response_model=List[Dict[str, Any]])
async def get_metadata_removal_alerts_endpoint(
    hours: int = Query(1, ge=1, le=24, description="Hours to look back (1-24)"),
    admin_user: User = Depends(require_admin)
):
    """
    Get performance alerts for metadata removal operations.
    
    Returns alerts for operations that exceeded performance targets
    or encountered errors.
    """
    try:
        alerts = await metadata_removal_monitor.get_performance_alerts(hours)
        
        return alerts
        
    except Exception as e:
        logger.error(f"Error getting metadata removal alerts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve alerts: {str(e)}"
        )

@router.get("/performance", response_model=Dict[str, Any])
async def get_metadata_removal_performance_endpoint(
    admin_user: User = Depends(require_admin)
):
    """
    Get current performance metrics for the metadata removal service.
    
    Returns real-time performance metrics from the metadata removal service.
    """
    try:
        performance_metrics = metadata_remover.get_performance_metrics()
        
        return {
            "service_metrics": performance_metrics,
            "configuration": {
                "enabled": settings.METADATA_REMOVAL_ENABLED,
                "preserve_quality": settings.METADATA_REMOVAL_PRESERVE_QUALITY,
                "max_file_size_mb": settings.METADATA_REMOVAL_MAX_FILE_SIZE_MB,
                "cache_ttl": settings.METADATA_REMOVAL_CACHE_TTL,
                "performance_target_ms": settings.METADATA_REMOVAL_PERFORMANCE_TARGET_MS,
                "batch_size_limit": settings.METADATA_REMOVAL_BATCH_SIZE_LIMIT,
                "track_usage": settings.METADATA_REMOVAL_TRACK_USAGE
            },
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting metadata removal performance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve performance metrics: {str(e)}"
        )

@router.get("/config", response_model=Dict[str, Any])
async def get_metadata_removal_config_endpoint(
    admin_user: User = Depends(require_admin)
):
    """
    Get current metadata removal configuration.
    
    Returns the current configuration settings for the metadata removal service.
    """
    try:
        from app.services.image_metadata_removal import get_metadata_removal_config
        
        config = get_metadata_removal_config()
        
        return {
            "configuration": config,
            "environment_settings": {
                "METADATA_REMOVAL_ENABLED": settings.METADATA_REMOVAL_ENABLED,
                "METADATA_REMOVAL_PRESERVE_QUALITY": settings.METADATA_REMOVAL_PRESERVE_QUALITY,
                "METADATA_REMOVAL_MAX_FILE_SIZE_MB": settings.METADATA_REMOVAL_MAX_FILE_SIZE_MB,
                "METADATA_REMOVAL_CACHE_TTL": settings.METADATA_REMOVAL_CACHE_TTL,
                "METADATA_REMOVAL_PERFORMANCE_TARGET_MS": settings.METADATA_REMOVAL_PERFORMANCE_TARGET_MS,
                "METADATA_REMOVAL_BATCH_SIZE_LIMIT": settings.METADATA_REMOVAL_BATCH_SIZE_LIMIT,
                "METADATA_REMOVAL_TRACK_USAGE": settings.METADATA_REMOVAL_TRACK_USAGE
            },
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting metadata removal config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve configuration: {str(e)}"
        )

@router.post("/test", response_model=Dict[str, Any])
async def test_metadata_removal_endpoint(
    test_image_url: str,
    admin_user: User = Depends(require_admin)
):
    """
    Test metadata removal functionality with a specific image URL.
    
    This endpoint allows admins to test the metadata removal service
    with a specific image URL for debugging and validation purposes.
    """
    try:
        from app.services.image_metadata_removal import remove_image_metadata
        
        result = await remove_image_metadata(
            image_url=test_image_url,
            user_id=str(admin_user.id),
            preserve_quality=True,
            track_usage=False  # Don't track usage for admin tests
        )
        
        return {
            "test_image_url": test_image_url,
            "result": result,
            "tested_by": str(admin_user.id),
            "tested_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error testing metadata removal with {test_image_url}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test metadata removal: {str(e)}"
        )

/**
 * Enhanced Workflow Integration Service Tests v2.0.0
 * 
 * Comprehensive test suite for the Enhanced Workflow Integration Service
 * with Enhanced Platform Service v2.0.0 integration.
 * 
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import workflowIntegration, { 
  WorkflowOperationType, 
  WorkflowErrorType, 
  SubscriptionTier,
  integrateWorkflowWithSystem
} from '../workflowIntegration';

// Mock dependencies
vi.mock('../platformService', () => ({
  default: {
    on: vi.fn(),
    generatePlatformFingerprint: vi.fn().mockResolvedValue('mock-platform-fingerprint')
  }
}));

vi.mock('../fingerprint', () => ({
  getDeviceFingerprint: vi.fn().mockResolvedValue('mock-device-fingerprint')
}));

vi.mock('../../utils/PrometheusMetricsCollector', () => ({
  PrometheusMetricsCollector: vi.fn().mockImplementation(() => ({
    recordWorkflowOperation: vi.fn()
  }))
}));

vi.mock('../tokenManager', () => ({
  default: {
    isInitialized: true,
    isTokenValid: vi.fn().mockResolvedValue(true),
    getTokenPayload: vi.fn().mockResolvedValue({
      sub: 'user123',
      email: '<EMAIL>',
      subscription: { plan: 'creator' }
    })
  }
}));

vi.mock('../../api', () => ({
  default: {
    post: vi.fn().mockResolvedValue({
      data: { id: 'mock-service-id', name: 'Test Service' }
    })
  }
}));

vi.mock('../../utils/cache', () => ({
  workflowCache: {
    set: vi.fn(),
    get: vi.fn(),
    delete: vi.fn()
  },
  cacheKey: vi.fn((type, identifier) => `${type}_${identifier}`)
}));

// Mock global objects
global.window = {
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
};

describe('Enhanced Workflow Integration Service v2.0.0', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    test('should initialize successfully with enhanced services', async () => {
      const status = await workflowIntegration.initialize();
      
      expect(status).toBeDefined();
      expect(status.initialized).toBe(true);
      expect(status.config).toBeDefined();
      expect(status.security).toBeDefined();
      expect(status.performance).toBeDefined();
      expect(status.subscriptionTier).toBeDefined();
    });

    test('should handle initialization failure gracefully', async () => {
      // Mock a service failure
      const originalConsoleError = console.error;
      console.error = vi.fn();
      
      // This should not throw but fall back to basic functionality
      try {
        await workflowIntegration.initialize();
      } catch (error) {
        // Should handle gracefully
      }
      
      console.error = originalConsoleError;
    });

    test('should provide comprehensive service status', () => {
      const status = workflowIntegration.getServiceStatus();
      expect(status.initialized).toBeDefined();
      expect(status.config).toBeDefined();
      expect(status.circuitBreaker).toBeDefined();
      expect(status.performance).toBeDefined();
      expect(status.security).toBeDefined();
      expect(status.subscriptionTier).toBeDefined();
      expect(status.sessionId).toBeDefined();
      expect(status.activeWorkflows).toBeDefined();
    });
  });

  describe('Enhanced Security Features', () => {
    beforeEach(async () => {
      await workflowIntegration.initialize();
    });

    test('should validate user authentication', async () => {
      const isValid = await workflowIntegration._validateUserAuthentication('user123');
      expect(isValid).toBe(true);
    });

    test('should validate subscription access for different operations', async () => {
      // Test creator tier access
      const creatorAccess = await workflowIntegration._validateSubscriptionAccess('user123', 'service_creation');
      expect(creatorAccess).toBe(true);

      // Test accelerator-only feature
      const acceleratorAccess = await workflowIntegration._validateSubscriptionAccess('user123', 'campaign_creation');
      expect(acceleratorAccess).toBe(false); // Creator tier should not have access
    });

    test('should validate security context', async () => {
      const isValid = await workflowIntegration._validateSecurityContext();
      expect(isValid).toBe(true);
    });

    test('should generate enhanced cache keys with security context', () => {
      const cacheKey = workflowIntegration.generateCacheKey('service', 'test-service', 'user123');
      expect(cacheKey).toContain('enhanced_workflow_user_user123');
      expect(cacheKey).toContain('service_test-service');
    });
  });

  describe('Enhanced Workflow Operations', () => {
    beforeEach(async () => {
      await workflowIntegration.initialize();
      workflowIntegration.setCorrelationId('test-correlation-id');
    });

    test('should save service to system with enhanced security', async () => {
      const serviceData = {
        name: 'Test Service',
        description: 'Test Description',
        value_proposition: 'Test Value Prop',
        target_industry: 'Technology',
        pricing_model: 'Subscription',
        service_level: 'Premium',
        delivery_timeline: '30 days',
        target_segments: ['Enterprise'],
        key_differentiators: ['Innovation']
      };

      const result = await workflowIntegration.saveServiceToSystem(serviceData, 'user123');
      
      expect(result).toBeDefined();
      expect(result.id).toBe('mock-service-id');
    });

    test('should handle service creation failure with circuit breaker', async () => {
      // Mock API failure
      const api = await import('../../api');
      api.default.post.mockRejectedValueOnce(new Error('API Error'));

      const serviceData = {
        name: 'Test Service',
        description: 'Test Description',
        value_proposition: 'Test Value Prop',
        target_industry: 'Technology',
        pricing_model: 'Subscription',
        service_level: 'Premium',
        delivery_timeline: '30 days',
        target_segments: ['Enterprise'],
        key_differentiators: ['Innovation']
      };

      await expect(workflowIntegration.saveServiceToSystem(serviceData, 'user123'))
        .rejects.toThrow('API Error');
    });

    test('should cache workflow data with enhanced metadata', () => {
      const testData = { test: 'data' };
      const cacheKey = workflowIntegration.cacheWorkflowData('service', 'test-service', testData, 'user123');
      
      expect(cacheKey).toBeDefined();
      expect(cacheKey).toContain('enhanced_workflow_user_user123');
    });
  });

  describe('Circuit Breaker Pattern', () => {
    beforeEach(async () => {
      await workflowIntegration.initialize();
    });

    test('should track failures and open circuit breaker', () => {
      // Simulate multiple failures
      for (let i = 0; i < 6; i++) {
        workflowIntegration._handleCircuitBreakerFailure();
      }
      
      const status = workflowIntegration.getServiceStatus();
      expect(status.circuitBreaker.state).toBe('OPEN');
    });

    test('should reset circuit breaker on success', () => {
      // Open circuit breaker
      for (let i = 0; i < 6; i++) {
        workflowIntegration._handleCircuitBreakerFailure();
      }
      
      // Reset it
      workflowIntegration._resetCircuitBreaker();
      
      const status = workflowIntegration.getServiceStatus();
      expect(status.circuitBreaker.state).toBe('CLOSED');
    });

    test('should prevent operations when circuit breaker is open', () => {
      // Open circuit breaker
      for (let i = 0; i < 6; i++) {
        workflowIntegration._handleCircuitBreakerFailure();
      }
      
      const canExecute = workflowIntegration._canExecuteOperation();
      expect(canExecute).toBe(false);
    });
  });

  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      await workflowIntegration.initialize();
    });

    test('should track performance metrics', () => {
      const status = workflowIntegration.getServiceStatus();
      expect(status.performance).toBeDefined();
      expect(status.performance.workflowsExecuted).toBeDefined();
      expect(status.performance.successfulWorkflows).toBeDefined();
      expect(status.performance.failedWorkflows).toBeDefined();
    });

    test('should record metrics for operations', () => {
      workflowIntegration._recordMetric('test_operation', 'success', {
        duration: 100,
        user_id: 'user123'
      });
      
      // Should not throw and should handle gracefully
    });
  });

  describe('Subscription Tier Integration', () => {
    beforeEach(async () => {
      await workflowIntegration.initialize();
    });

    test('should detect subscription tier from token', async () => {
      const tier = await workflowIntegration._getUserSubscriptionTier('user123');
      expect(tier).toBe('creator');
    });

    test('should validate access based on subscription tier', async () => {
      // Creator tier should have access to basic operations
      const basicAccess = await workflowIntegration._validateSubscriptionAccess('user123', 'service_creation');
      expect(basicAccess).toBe(true);

      // Creator tier should not have access to advanced operations
      const advancedAccess = await workflowIntegration._validateSubscriptionAccess('user123', 'parallel_execution');
      expect(advancedAccess).toBe(false);
    });
  });

  describe('Utility Functions', () => {
    test('should integrate workflow with system using utility function', async () => {
      const workflowData = {
        serviceDefinition: { name: 'Test Service' },
        selectedICP: { name: 'Test ICP' },
        generatedContent: []
      };

      // Mock the completeWorkflowIntegration method
      workflowIntegration.completeWorkflowIntegration = vi.fn().mockResolvedValue({
        service: { id: 'service-123' },
        icp: { id: 'icp-123' },
        campaign: null,
        errors: []
      });

      const result = await integrateWorkflowWithSystem(workflowData, 'user123', 'correlation-123');
      
      expect(result).toBeDefined();
      expect(result.service).toBeDefined();
      expect(result.icp).toBeDefined();
    });
  });

  describe('Cleanup and Resource Management', () => {
    test('should cleanup resources properly', () => {
      workflowIntegration.cleanup();
      // Should not throw and should clean up properly
    });
  });

  describe('Constants and Enums', () => {
    test('should export workflow operation types', () => {
      expect(WorkflowOperationType.SERVICE_CREATION).toBe('service_creation');
      expect(WorkflowOperationType.ICP_CREATION).toBe('icp_creation');
      expect(WorkflowOperationType.CAMPAIGN_CREATION).toBe('campaign_creation');
    });

    test('should export workflow error types', () => {
      expect(WorkflowErrorType.AUTHENTICATION_FAILED).toBe('authentication_failed');
      expect(WorkflowErrorType.SUBSCRIPTION_ACCESS_DENIED).toBe('subscription_access_denied');
      expect(WorkflowErrorType.SECURITY_VALIDATION_FAILED).toBe('security_validation_failed');
    });

    test('should export subscription tiers', () => {
      expect(SubscriptionTier.CREATOR).toBe('creator');
      expect(SubscriptionTier.ACCELERATOR).toBe('accelerator');
      expect(SubscriptionTier.DOMINATOR).toBe('dominator');
    });
  });
});

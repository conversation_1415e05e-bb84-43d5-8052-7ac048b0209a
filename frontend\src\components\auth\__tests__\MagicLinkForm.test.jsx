/**
 * Tests for MagicLinkForm component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import MagicLinkForm from '../MagicLinkForm';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock useAuth hook
const mockRequestMagicLink = vi.fn();
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    requestMagicLink: mockRequestMagicLink
  })
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock;

describe('MagicLinkForm', () => {
  const mockProps = {
    onSuccess: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue('');
    mockRequestMagicLink.mockResolvedValue({ success: true });
  });

  test('renders magic link form correctly', () => {
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Enter your email address and we'll send you a magic link to sign in instantly.')).toBeInTheDocument();
    expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Send Magic Link' })).toBeInTheDocument();
  });

  test('loads initial email from localStorage', () => {
    localStorageMock.getItem.mockReturnValue('<EMAIL>');
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });

  test('uses initialEmail prop over localStorage', () => {
    localStorageMock.getItem.mockReturnValue('<EMAIL>');
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} initialEmail="<EMAIL>" />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });

  test('handles form submission successfully', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockRequestMagicLink).toHaveBeenCalledWith('<EMAIL>');
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith('lastUsedEmail', '<EMAIL>');
    expect(mockProps.onSuccess).toHaveBeenCalledWith('<EMAIL>');
  });

  test('shows success state after successful submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Magic link sent! Check your email.')).toBeInTheDocument();
    });

    expect(screen.getByText('We've sent a secure login link to')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('The link will expire in 15 minutes. If you don't see the email, check your spam folder.')).toBeInTheDocument();
  });

  test('validates required email field', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('Email is required');
    expect(mockRequestMagicLink).not.toHaveBeenCalled();
  });

  test('validates email format', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('Please enter a valid email address');
    expect(mockRequestMagicLink).not.toHaveBeenCalled();
  });

  test('handles API errors gracefully', async () => {
    const user = userEvent.setup();
    mockRequestMagicLink.mockResolvedValue({ 
      success: false, 
      error: 'User not found' 
    });
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('User not found')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('User not found');
  });

  test('handles network errors gracefully', async () => {
    const user = userEvent.setup();
    mockRequestMagicLink.mockRejectedValue(new Error('Network error'));
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('An unexpected error occurred')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('An unexpected error occurred');
  });

  test('shows loading state during submission', async () => {
    const user = userEvent.setup();
    mockRequestMagicLink.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
    expect(screen.getByText('Sending magic link, please wait...')).toBeInTheDocument();
  });

  test('implements rate limiting', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    // First submission
    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Magic link sent! Check your email.')).toBeInTheDocument();
    });

    // Clear the form and try again immediately
    await user.click(screen.getByLabelText('Resend magic link'));

    // Should show rate limiting error
    await waitFor(() => {
      expect(screen.getByText('Please wait before requesting another magic link')).toBeInTheDocument();
    });
  });

  test('shows resend functionality in success state', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} showResend={true} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Magic link sent! Check your email.')).toBeInTheDocument();
    });

    expect(screen.getByText('Didn't receive the email?')).toBeInTheDocument();
    expect(screen.getByLabelText('Resend magic link')).toBeInTheDocument();
  });

  test('hides resend functionality when showResend is false', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} showResend={false} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Magic link sent! Check your email.')).toBeInTheDocument();
    });

    expect(screen.queryByText('Didn't receive the email?')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Resend magic link')).not.toBeInTheDocument();
  });

  test('handles resend functionality', async () => {
    const user = userEvent.setup();
    vi.useFakeTimers();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} showResend={true} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    // First submission
    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Magic link sent! Check your email.')).toBeInTheDocument();
    });

    // Wait for rate limit to pass
    vi.advanceTimersByTime(61000); // 61 seconds

    // Click resend
    const resendButton = screen.getByLabelText('Resend magic link');
    await user.click(resendButton);

    await waitFor(() => {
      expect(mockRequestMagicLink).toHaveBeenCalledTimes(2);
    });

    vi.useRealTimers();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    expect(emailInput).toHaveAttribute('aria-required', 'true');
    expect(emailInput).toHaveAttribute('type', 'email');

    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });
    expect(submitButton).toHaveAttribute('type', 'submit');
  });

  test('handles autoFocus prop correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} autoFocus={true} />
      </TestWrapper>
    );

    let emailInput = screen.getByLabelText('Email Address');
    expect(emailInput).toHaveAttribute('autoFocus');

    rerender(
      <TestWrapper>
        <MagicLinkForm {...mockProps} autoFocus={false} />
      </TestWrapper>
    );

    emailInput = screen.getByLabelText('Email Address');
    expect(emailInput).not.toHaveAttribute('autoFocus');
  });

  test('trims whitespace from email input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MagicLinkForm {...mockProps} />
      </TestWrapper>
    );

    const emailInput = screen.getByLabelText('Email Address');
    const submitButton = screen.getByRole('button', { name: 'Send Magic Link' });

    await user.type(emailInput, '  <EMAIL>  ');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockRequestMagicLink).toHaveBeenCalledWith('<EMAIL>');
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith('lastUsedEmail', '<EMAIL>');
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <MagicLinkForm 
          {...mockProps} 
          data-testid="test-magic-link-form"
          className="custom-class"
        />
      </TestWrapper>
    );

    const form = screen.getByTestId('test-magic-link-form');
    expect(form).toHaveClass('custom-class');
  });
});

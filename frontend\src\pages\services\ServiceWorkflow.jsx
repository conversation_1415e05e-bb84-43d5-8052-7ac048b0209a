// @since 2024-1-1 to 2025-25-7
import { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
  Snackbar,
  useTheme,
  Breadcrumbs,
  Link,
  IconButton,
  Button,
  CircularProgress
} from '@mui/material';
import { 
  Business as BusinessIcon,
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

// Workflow components
import WorkflowProvider, { useWorkflow } from '../../components/services/WorkflowProvider';
import ServiceWorkflowStepper from '../../components/services/ServiceWorkflowStepper';
import WorkflowNavigation from '../../components/services/WorkflowNavigation';
import ErrorBoundary from '../../components/services/ErrorBoundary';

// Performance monitoring utilities
const startTiming = (name, data) => {
  const timingId = `${name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  console.log(`Performance timing started: ${name}`, { timingId, ...data });

  // Store timing data for later analysis
  const timingData = {
    name,
    startTime: performance.now(),
    data,
    timingId
  };

  sessionStorage.setItem(`timing_${timingId}`, JSON.stringify(timingData));
  return timingId;
};

const endTiming = (timingId, data) => {
  try {
    const storedData = sessionStorage.getItem(`timing_${timingId}`);
    if (storedData) {
      const timingData = JSON.parse(storedData);
      const duration = performance.now() - timingData.startTime;

      console.log(`Performance timing ended: ${timingData.name}`, {
        timingId,
        duration: `${duration.toFixed(2)}ms`,
        ...data
      });

      // Clean up
      sessionStorage.removeItem(`timing_${timingId}`);
    }
  } catch (error) {
    console.warn('Error ending timing:', error);
  }
};

// Workflow cache with enhanced functionality
const workflowCache = {
  set: (key, value, ttl = 30 * 60 * 1000) => {
    try {
      const cacheData = {
        value,
        timestamp: Date.now(),
        ttl
      };
      localStorage.setItem(`workflow_cache_${key}`, JSON.stringify(cacheData));
      console.log(`Workflow cache set: ${key}, TTL: ${ttl}ms`);
    } catch (error) {
      console.warn('Error setting cache:', error);
    }
  },

  get: (key) => {
    try {
      const cached = localStorage.getItem(`workflow_cache_${key}`);
      if (!cached) return null;

      const cacheData = JSON.parse(cached);
      const now = Date.now();

      // Check if cache has expired
      if (now - cacheData.timestamp > cacheData.ttl) {
        localStorage.removeItem(`workflow_cache_${key}`);
        return null;
      }

      return cacheData.value;
    } catch (error) {
      console.warn('Error getting cache:', error);
      return null;
    }
  },

  clear: (key) => {
    try {
      localStorage.removeItem(`workflow_cache_${key}`);
    } catch (error) {
      console.warn('Error clearing cache:', error);
    }
  }
};

// Step components
import ServiceDefinitionStep from '../../components/services/steps/ServiceDefinitionStep';
import ICPSelectionStep from '../../components/services/steps/ICPSelectionStep';
import StrategyPlanningStep from '../../components/services/steps/StrategyPlanningStep';
import ContentGenerationStep from '../../components/services/steps/ContentGenerationStep';

/**
 * ServiceWorkflowContainer - Main container for the multi-step service workflow
 * Implements progressive disclosure with Material-UI glass morphism styling
 * Enhanced with better navigation and context
 */
const ServiceWorkflowContainer = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { serviceId } = useParams();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const {
    currentStep,
    steps,
    loading,
    error,
    actions,
    workflowData,
    correlationId
  } = useWorkflow();

  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [loadTimingId, setLoadTimingId] = useState(null);
  const [isEditing, setIsEditing] = useState(Boolean(serviceId));
  const [authError, setAuthError] = useState(null);

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      setAuthError('Authentication required to access service workflow');
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Performance monitoring
  useEffect(() => {
    if (!isAuthenticated || authLoading) return;

    const timingId = startTiming('pageLoad', {
      page: 'service-workflow',
      mode: isEditing ? 'edit' : 'create',
      correlationId,
      userId: user?.id
    });
    setLoadTimingId(timingId);

    return () => {
      if (timingId) {
        endTiming(timingId, { completed: true });
      }
    };
  }, [correlationId, isEditing, isAuthenticated, authLoading, user?.id]);

  // Cleanup timing on unmount
  useEffect(() => {
    return () => {
      if (loadTimingId) {
        endTiming(loadTimingId, { unmounted: true });
      }
    };
  }, [loadTimingId]);

  // Handle step navigation with performance monitoring
  const handleNext = useCallback(async () => {
    if (!isAuthenticated) {
      setNotification({
        open: true,
        message: 'Authentication required to proceed.',
        severity: 'error'
      });
      navigate('/login');
      return;
    }

    const timingId = startTiming('userInteraction', {
      action: 'next_step',
      currentStep,
      correlationId,
      userId: user?.id
    });

    try {
      actions.setLoading(true);

      // Validate current step data before proceeding
      const currentStepData = actions.getCurrentStepData();
      if (!currentStepData) {
        setNotification({
          open: true,
          message: 'Please complete the current step before proceeding.',
          severity: 'warning'
        });
        endTiming(timingId, { success: false, reason: 'incomplete_step' });
        return;
      }

      // Cache current step data with user context
      const cacheKeyName = `step_${currentStep}_${correlationId}_${user?.id}`;
      workflowCache.set(cacheKeyName, {
        ...currentStepData,
        userId: user?.id,
        timestamp: new Date().toISOString()
      }, 15 * 60 * 1000); // 15 minutes

      // Mark current step as completed
      actions.completeStep(currentStep);

      // Move to next step
      actions.nextStep();

      setNotification({
        open: true,
        message: 'Step completed successfully!',
        severity: 'success'
      });

      endTiming(timingId, { success: true });
    } catch (error) {
      console.error('Error proceeding to next step:', error);
      actions.setError('Failed to proceed to next step. Please try again.');
      endTiming(timingId, { success: false, error: error.message });
    } finally {
      actions.setLoading(false);
    }
  }, [currentStep, actions, correlationId, isAuthenticated, user?.id, navigate]);

  const handlePrevious = useCallback(() => {
    actions.previousStep();
  }, [actions]);

  const handleSave = useCallback(async () => {
    if (!isAuthenticated) {
      setNotification({
        open: true,
        message: 'Authentication required to save progress.',
        severity: 'error'
      });
      navigate('/login');
      return;
    }

    const timingId = startTiming('userInteraction', {
      action: 'save_progress',
      correlationId,
      userId: user?.id
    });

    try {
      actions.setLoading(true);

      // Save to cache with user context
      const cacheKeyName = `workflow_progress_${correlationId}_${user?.id}`;
      const saveData = {
        ...workflowData,
        userId: user?.id,
        userEmail: user?.email,
        savedAt: new Date().toISOString(),
        correlationId
      };

      workflowCache.set(cacheKeyName, saveData, 30 * 60 * 1000); // 30 minutes

      // Also save to localStorage as backup with user context
      localStorage.setItem(`serviceWorkflowData_${user?.id}`, JSON.stringify(saveData));

      setNotification({
        open: true,
        message: 'Progress saved successfully!',
        severity: 'success'
      });

      endTiming(timingId, { success: true });
    } catch (error) {
      console.error('Error saving progress:', error);
      setNotification({
        open: true,
        message: 'Failed to save progress. Please try again.',
        severity: 'error'
      });
      endTiming(timingId, { success: false, error: error.message });
    } finally {
      actions.setLoading(false);
    }
  }, [workflowData, actions, correlationId, isAuthenticated, user?.id, user?.email, navigate]);

  const handleBackToServices = () => {
    // Save progress before leaving if there's unsaved data
    if (workflowData && Object.keys(workflowData).length > 0) {
      const cacheKeyName = `workflow_progress_${correlationId}_${user?.id}`;
      workflowCache.set(cacheKeyName, {
        ...workflowData,
        userId: user?.id,
        autoSavedAt: new Date().toISOString(),
        correlationId
      }, 30 * 60 * 1000);
    }
    navigate('/services');
  };

  // Enhanced editing functionality
  const handleToggleEditMode = useCallback(() => {
    setIsEditing(!isEditing);
    setNotification({
      open: true,
      message: isEditing ? 'Switched to create mode' : 'Switched to edit mode',
      severity: 'info'
    });
  }, [isEditing]);

  // Render current step content with user context
  const renderStepContent = () => {
    const stepProps = {
      user,
      isEditing,
      serviceId,
      onToggleEditMode: handleToggleEditMode
    };

    switch (currentStep) {
      case 0:
        return <ServiceDefinitionStep {...stepProps} />;
      case 1:
        return <ICPSelectionStep {...stepProps} />;
      case 2:
        return <StrategyPlanningStep {...stepProps} />;
      case 3:
        return <ContentGenerationStep {...stepProps} />;
      default:
        return <ServiceDefinitionStep {...stepProps} />;
    }
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: theme.spacing(3), display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Show authentication error if any
  if (authError) {
    return (
      <Container maxWidth="lg" sx={{ py: theme.spacing(3) }}>
        <Alert severity="error" sx={{ mb: theme.spacing(3) }}>
          <Typography variant="h6" gutterBottom>Authentication Error</Typography>
          {authError}
        </Alert>
        <Box sx={{ display: 'flex', gap: theme.spacing(2) }}>
          <Button variant="contained" onClick={() => navigate('/login')}>
            Go to Login
          </Button>
          <Button variant="outlined" onClick={() => navigate('/services')}>
            Back to Services
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container
      maxWidth="lg"
      sx={{
        py: { xs: theme.spacing(1), sm: theme.spacing(2), md: theme.spacing(3) },
        px: { xs: theme.spacing(1), sm: theme.spacing(2) },
        minHeight: '100vh',
        // Mobile-first responsive design
        [theme.breakpoints.down('sm')]: {
          px: theme.spacing(0.5),
        },
      }}
    >
      {/* Enhanced Header with Navigation */}
      <Box sx={{
        mb: { xs: theme.spacing(2), sm: theme.spacing(3), md: theme.spacing(4) },
        // Mobile header adjustments
        [theme.breakpoints.down('sm')]: {
          mb: theme.spacing(2),
        },
      }}>
        {/* Breadcrumb Navigation */}
        <Box sx={{ mb: theme.spacing(2) }}>
          <Breadcrumbs aria-label="breadcrumb">
            <Link
              component="button"
              variant="body2"
              onClick={handleBackToServices}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Services
            </Link>
            <Typography color="text.primary">
              {isEditing ? 'Edit Service' : 'Create Service'}
            </Typography>
          </Breadcrumbs>
        </Box>

        {/* Header with Back Button - Mobile Responsive */}
        <Box sx={{
          display: 'flex',
          alignItems: { xs: 'flex-start', sm: 'center' },
          gap: { xs: theme.spacing(1), sm: theme.spacing(2) },
          flexDirection: { xs: 'column', sm: 'row' },
          // Mobile layout adjustments
          [theme.breakpoints.down('sm')]: {
            gap: theme.spacing(1),
          },
        }}>
          <IconButton
            onClick={handleBackToServices}
            sx={{
              alignSelf: { xs: 'flex-start', sm: 'center' },
              '&:focus-visible': {
                outline: `2px solid ${theme.palette.primary.main}`,
                outlineOffset: '2px',
              },
              // Mobile size adjustment
              [theme.breakpoints.down('sm')]: {
                p: theme.spacing(1),
              },
            }}
          >
            <ArrowBackIcon />
          </IconButton>

          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: theme.spacing(1),
                mb: theme.spacing(0.5),
                // Mobile typography adjustments
                [theme.breakpoints.down('sm')]: {
                  fontSize: '1.5rem',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  gap: theme.spacing(0.5),
                },
                [theme.breakpoints.down('xs')]: {
                  fontSize: '1.25rem',
                },
              }}
            >
              <BusinessIcon sx={{
                [theme.breakpoints.down('sm')]: {
                  fontSize: '1.5rem',
                },
              }} />
              <Box component="span" sx={{
                [theme.breakpoints.down('sm')]: {
                  fontSize: 'inherit',
                },
              }}>
                {isEditing ? 'Edit Service' : 'Create New Service'}
              </Box>
            </Typography>
            <Typography
              variant="body1"
              color="textSecondary"
              sx={{
                // Mobile typography adjustments
                [theme.breakpoints.down('sm')]: {
                  fontSize: '0.875rem',
                  lineHeight: 1.4,
                },
              }}
            >
              {isEditing
                ? 'Update your service details and regenerate content'
                : 'Define your service, generate ICPs, plan strategy, and launch campaigns'
              }
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: theme.spacing(3) }}
          onClose={() => actions.setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Workflow Navigation */}
      <WorkflowNavigation
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        nextDisabled={loading}
        previousDisabled={loading}
        saveDisabled={loading}
      />

      {/* Workflow Stepper */}
      <ServiceWorkflowStepper
        activeStep={currentStep}
        steps={steps}
      >
        {renderStepContent()}
      </ServiceWorkflowStepper>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification({ ...notification, open: false })}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

/**
 * ServiceWorkflow - Main component with workflow provider and error boundary
 */
const ServiceWorkflow = () => {
  return (
    <ErrorBoundary fallbackMessage="The service workflow encountered an unexpected error. Please refresh the page or contact support if the issue persists.">
      <WorkflowProvider>
        <ServiceWorkflowContainer />
      </WorkflowProvider>
    </ErrorBoundary>
  );
};

export default ServiceWorkflow;

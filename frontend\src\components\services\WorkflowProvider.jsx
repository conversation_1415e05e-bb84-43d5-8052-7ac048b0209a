/**
 * Enhanced Workflow Provider - Enterprise-grade workflow state management component
 * Features: Comprehensive workflow state management with advanced context capabilities,
 * workflow persistence with localStorage/sessionStorage integration, real-time validation,
 * subscription-based feature gating, analytics tracking, workflow templates and presets,
 * collaboration features, and ACE Social platform integration with advanced workflow
 * capabilities and seamless state management workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  createContext,
  useContext,
  useReducer,
  useCallback,
  useMemo,
  useEffect,
  useState,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';

import { useSubscription } from '../../contexts/SubscriptionContext';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// Enhanced error handler hook
const useEnhancedErrorHandler = (options = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    exponentialBackoff = true,
    onError = () => {}
  } = options;

  const [errorState, setErrorState] = useState({
    error: null,
    isRetrying: false,
    canRetry: false,
    retryCount: 0
  });

  const handleError = useCallback((error) => {
    const canRetry = errorState.retryCount < maxRetries;
    setErrorState({
      error,
      isRetrying: false,
      canRetry,
      retryCount: errorState.retryCount
    });
    onError(error);
  }, [errorState.retryCount, maxRetries, onError]);

  const retry = useCallback(async () => {
    if (!errorState.canRetry) return;

    setErrorState(prev => ({ ...prev, isRetrying: true }));

    const delay = exponentialBackoff
      ? retryDelay * Math.pow(2, errorState.retryCount)
      : retryDelay;

    await new Promise(resolve => setTimeout(resolve, delay));

    setErrorState(prev => ({
      ...prev,
      isRetrying: false,
      retryCount: prev.retryCount + 1,
      canRetry: prev.retryCount + 1 < maxRetries
    }));
  }, [errorState.canRetry, errorState.retryCount, exponentialBackoff, retryDelay, maxRetries]);

  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isRetrying: false,
      canRetry: false,
      retryCount: 0
    });
  }, []);

  const executeWithErrorHandling = useCallback(async (fn) => {
    try {
      return await fn();
    } catch (error) {
      handleError(error);
      throw error;
    }
  }, [handleError]);

  return {
    ...errorState,
    handleError,
    retry,
    clearError,
    executeWithErrorHandling
  };
};

// ACE Social brand colors (for future use)
// const ACE_COLORS = {
//   DARK: '#15110E',
//   PURPLE: '#4E40C5',
//   YELLOW: '#EBAE1B',
//   WHITE: '#FFFFFF'
// };

// Plan-based workflow limitations
const PLAN_LIMITS = {
  1: { // Creator
    maxWorkflowSteps: 4,
    workflowPersistence: false,
    workflowAnalytics: false,
    workflowTemplates: false,
    workflowCollaboration: false,
    advancedValidation: false,
    workflowHistory: false,
    realTimeSync: false,
    workflowExport: false,
    customWorkflows: false
  },
  2: { // Accelerator
    maxWorkflowSteps: 8,
    workflowPersistence: true,
    workflowAnalytics: true,
    workflowTemplates: true,
    workflowCollaboration: false,
    advancedValidation: true,
    workflowHistory: true,
    realTimeSync: true,
    workflowExport: false,
    customWorkflows: true
  },
  3: { // Dominator
    maxWorkflowSteps: 20,
    workflowPersistence: true,
    workflowAnalytics: true,
    workflowTemplates: true,
    workflowCollaboration: true,
    advancedValidation: true,
    workflowHistory: true,
    realTimeSync: true,
    workflowExport: true,
    customWorkflows: true
  }
};

/**
 * Enhanced Workflow Provider - Comprehensive workflow state management with advanced features
 * Implements plan-based feature restrictions and enterprise-grade workflow capabilities
 */

// Enhanced workflow steps configuration with validation and dependencies
// TODO: Move to separate constants file for better Fast Refresh support
const WORKFLOW_STEPS = [
  {
    id: 'service-definition',
    label: 'Service Definition',
    description: 'Define your service details and target market',
    component: 'ServiceDefinitionStep',
    icon: 'BusinessIcon',
    estimatedTime: 10, // minutes
    required: true,
    dependencies: [],
    validation: {
      required: ['serviceName', 'serviceDescription', 'targetMarket'],
      optional: ['pricing', 'timeline']
    },
    planRequirement: 1 // Available from Creator plan
  },
  {
    id: 'icp-selection',
    label: 'ICP Selection',
    description: 'Generate and select your ideal customer profile',
    component: 'ICPSelectionStep',
    icon: 'PersonIcon',
    estimatedTime: 15,
    required: true,
    dependencies: ['service-definition'],
    validation: {
      required: ['selectedICP', 'demographics'],
      optional: ['psychographics', 'behaviors']
    },
    planRequirement: 1
  },
  {
    id: 'strategy-planning',
    label: 'Strategy Planning',
    description: 'Review and approve AI-generated strategy',
    component: 'StrategyPlanningStep',
    icon: 'StrategyIcon',
    estimatedTime: 20,
    required: true,
    dependencies: ['icp-selection'],
    validation: {
      required: ['strategy', 'objectives'],
      optional: ['tactics', 'metrics']
    },
    planRequirement: 1
  },
  {
    id: 'content-generation',
    label: 'Content & Campaign',
    description: 'Generate content and setup campaign',
    component: 'ContentGenerationStep',
    icon: 'CampaignIcon',
    estimatedTime: 25,
    required: true,
    dependencies: ['strategy-planning'],
    validation: {
      required: ['content', 'campaign'],
      optional: ['schedule', 'budget']
    },
    planRequirement: 1
  },
  {
    id: 'analytics-setup',
    label: 'Analytics Setup',
    description: 'Configure tracking and analytics',
    component: 'AnalyticsSetupStep',
    icon: 'AnalyticsIcon',
    estimatedTime: 10,
    required: false,
    dependencies: ['content-generation'],
    validation: {
      required: ['trackingSetup'],
      optional: ['customMetrics', 'dashboards']
    },
    planRequirement: 2 // Available from Accelerator plan
  },
  {
    id: 'collaboration-setup',
    label: 'Team Collaboration',
    description: 'Setup team access and collaboration',
    component: 'CollaborationSetupStep',
    icon: 'TeamIcon',
    estimatedTime: 15,
    required: false,
    dependencies: ['content-generation'],
    validation: {
      required: ['teamMembers'],
      optional: ['permissions', 'notifications']
    },
    planRequirement: 3 // Available from Dominator plan
  }
];

// Workflow templates for common use cases
// TODO: Move to separate constants file for better Fast Refresh support
const WORKFLOW_TEMPLATES = {
  'basic-content': {
    name: 'Basic Content Creation',
    description: 'Simple workflow for content creation',
    steps: ['service-definition', 'icp-selection', 'content-generation'],
    planRequirement: 1
  },
  'full-strategy': {
    name: 'Complete Strategy Development',
    description: 'Comprehensive workflow with strategy planning',
    steps: ['service-definition', 'icp-selection', 'strategy-planning', 'content-generation'],
    planRequirement: 1
  },
  'enterprise-workflow': {
    name: 'Enterprise Workflow',
    description: 'Full workflow with analytics and collaboration',
    steps: ['service-definition', 'icp-selection', 'strategy-planning', 'content-generation', 'analytics-setup', 'collaboration-setup'],
    planRequirement: 3
  }
};

// Enhanced initial workflow state
const createInitialState = (template = null) => ({
  currentStep: 0,
  completedSteps: [],
  workflowData: {
    serviceDefinition: null,
    selectedICP: null,
    strategy: null,
    generatedContent: null,
    campaign: null,
    analytics: null,
    collaboration: null
  },
  loading: false,
  error: null,
  correlationId: null,
  // Enhanced state properties
  stepHistory: [],
  stepValidationErrors: {},
  stepWarnings: {},
  workflowMetrics: {
    startTime: null,
    stepTimes: {},
    totalTime: 0,
    completionRate: 0
  },
  workflowTemplate: template,
  activeSteps: template ? WORKFLOW_TEMPLATES[template]?.steps || WORKFLOW_STEPS.map(s => s.id) : WORKFLOW_STEPS.map(s => s.id),
  persistence: {
    autoSave: true,
    lastSaved: null,
    isDirty: false
  },
  collaboration: {
    activeUsers: [],
    changes: [],
    conflicts: []
  },
  analytics: {
    events: [],
    stepAnalytics: {},
    userBehavior: []
  }
});

// Enhanced workflow actions
const WORKFLOW_ACTIONS = {
  SET_CURRENT_STEP: 'SET_CURRENT_STEP',
  NEXT_STEP: 'NEXT_STEP',
  PREVIOUS_STEP: 'PREVIOUS_STEP',
  COMPLETE_STEP: 'COMPLETE_STEP',
  UPDATE_STEP_DATA: 'UPDATE_STEP_DATA',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  RESET_WORKFLOW: 'RESET_WORKFLOW',
  SET_CORRELATION_ID: 'SET_CORRELATION_ID',
  // Enhanced actions
  VALIDATE_STEP: 'VALIDATE_STEP',
  SET_STEP_VALIDATION_ERRORS: 'SET_STEP_VALIDATION_ERRORS',
  SET_STEP_WARNINGS: 'SET_STEP_WARNINGS',
  ADD_TO_HISTORY: 'ADD_TO_HISTORY',
  UNDO_STEP: 'UNDO_STEP',
  REDO_STEP: 'REDO_STEP',
  UPDATE_METRICS: 'UPDATE_METRICS',
  SET_TEMPLATE: 'SET_TEMPLATE',
  SAVE_WORKFLOW: 'SAVE_WORKFLOW',
  LOAD_WORKFLOW: 'LOAD_WORKFLOW',
  SET_PERSISTENCE_STATE: 'SET_PERSISTENCE_STATE',
  ADD_COLLABORATION_USER: 'ADD_COLLABORATION_USER',
  REMOVE_COLLABORATION_USER: 'REMOVE_COLLABORATION_USER',
  ADD_COLLABORATION_CHANGE: 'ADD_COLLABORATION_CHANGE',
  TRACK_ANALYTICS_EVENT: 'TRACK_ANALYTICS_EVENT',
  UPDATE_STEP_ANALYTICS: 'UPDATE_STEP_ANALYTICS'
};

// Enhanced workflow reducer with comprehensive state management
const workflowReducer = (state, action) => {
  switch (action.type) {
    case WORKFLOW_ACTIONS.SET_CURRENT_STEP: {
      const newStep = action.payload;
      const stepHistory = [...state.stepHistory, {
        action: 'navigate',
        from: state.currentStep,
        to: newStep,
        timestamp: Date.now()
      }];

      return {
        ...state,
        currentStep: newStep,
        stepHistory: stepHistory.slice(-50), // Keep last 50 actions
        error: null,
        persistence: { ...state.persistence, isDirty: true }
      };
    }

    case WORKFLOW_ACTIONS.NEXT_STEP: {
      const nextStep = Math.min(state.currentStep + 1, state.activeSteps.length - 1);
      const stepHistory = [...state.stepHistory, {
        action: 'next',
        from: state.currentStep,
        to: nextStep,
        timestamp: Date.now()
      }];

      return {
        ...state,
        currentStep: nextStep,
        stepHistory: stepHistory.slice(-50),
        error: null,
        persistence: { ...state.persistence, isDirty: true }
      };
    }

    case WORKFLOW_ACTIONS.PREVIOUS_STEP: {
      const prevStep = Math.max(state.currentStep - 1, 0);
      const stepHistory = [...state.stepHistory, {
        action: 'previous',
        from: state.currentStep,
        to: prevStep,
        timestamp: Date.now()
      }];

      return {
        ...state,
        currentStep: prevStep,
        stepHistory: stepHistory.slice(-50),
        error: null,
        persistence: { ...state.persistence, isDirty: true }
      };
    }

    case WORKFLOW_ACTIONS.COMPLETE_STEP: {
      const stepIndex = action.payload;
      const completedSteps = [...state.completedSteps];
      if (!completedSteps.includes(stepIndex)) {
        completedSteps.push(stepIndex);
      }

      const completionRate = (completedSteps.length / state.activeSteps.length) * 100;

      return {
        ...state,
        completedSteps,
        workflowMetrics: {
          ...state.workflowMetrics,
          completionRate,
          stepTimes: {
            ...state.workflowMetrics.stepTimes,
            [stepIndex]: Date.now()
          }
        },
        error: null,
        persistence: { ...state.persistence, isDirty: true }
      };
    }

    case WORKFLOW_ACTIONS.UPDATE_STEP_DATA: {
      const { step, data } = action.payload;
      return {
        ...state,
        workflowData: {
          ...state.workflowData,
          [step]: data
        },
        error: null,
        persistence: { ...state.persistence, isDirty: true }
      };
    }

    case WORKFLOW_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case WORKFLOW_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      };

    case WORKFLOW_ACTIONS.SET_CORRELATION_ID:
      return {
        ...state,
        correlationId: action.payload
      };

    case WORKFLOW_ACTIONS.RESET_WORKFLOW: {
      const template = action.payload?.template || null;
      const initialState = createInitialState(template);
      return {
        ...initialState,
        correlationId: action.payload?.correlationId || generateCorrelationId()
      };
    }

    case WORKFLOW_ACTIONS.VALIDATE_STEP: {
      const { stepIndex, errors, warnings } = action.payload;
      return {
        ...state,
        stepValidationErrors: {
          ...state.stepValidationErrors,
          [stepIndex]: errors || []
        },
        stepWarnings: {
          ...state.stepWarnings,
          [stepIndex]: warnings || []
        }
      };
    }

    case WORKFLOW_ACTIONS.TRACK_ANALYTICS_EVENT: {
      const event = {
        ...action.payload,
        timestamp: Date.now(),
        correlationId: state.correlationId
      };
      return {
        ...state,
        analytics: {
          ...state.analytics,
          events: [...state.analytics.events, event].slice(-1000) // Keep last 1000 events
        }
      };
    }

    default:
      return state;
  }
};

// Generate correlation ID for tracking
const generateCorrelationId = () => {
  return `workflow_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Create enhanced context
const WorkflowContext = createContext();

// Enhanced workflow provider component with comprehensive features
export const WorkflowProvider = memo(forwardRef(({
  children,
  template = null,
  enablePersistence = true,
  enableAnalytics = true,
  autoSave = true,
  persistenceKey = 'ace_social_workflow'
}, ref) => {
  const { updateUsage, getPlanTier } = useSubscription();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Initialize state with template
  const initialState = useMemo(() => createInitialState(template), [template]);
  const [state, dispatch] = useReducer(workflowReducer, {
    ...initialState,
    correlationId: generateCorrelationId()
  });

  // Plan tier and feature access
  const planTier = getPlanTier();
  const planLimits = PLAN_LIMITS[planTier] || PLAN_LIMITS[1];

  // Enhanced error handling
  const errorHandler = useEnhancedErrorHandler({
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
    correlationId: state.correlationId,
    onError: (error) => {
      dispatch({ type: WORKFLOW_ACTIONS.SET_ERROR, payload: error.message });
      announceToScreenReader(`Workflow error: ${error.message}`);
    }
  });

  // Enhanced persistence management
  // const persistenceRef = useRef(null);
  // const analyticsRef = useRef([]);

  // Workflow persistence
  useEffect(() => {
    if (enablePersistence && planLimits.workflowPersistence && state.persistence.isDirty) {
      const timeoutId = setTimeout(() => {
        try {
          const workflowData = {
            ...state,
            timestamp: Date.now(),
            version: '2.0.0'
          };
          localStorage.setItem(persistenceKey, JSON.stringify(workflowData));
          dispatch({ type: WORKFLOW_ACTIONS.SAVE_WORKFLOW });
        } catch (error) {
          console.error('Failed to persist workflow:', error);
        }
      }, autoSave ? 2000 : 0);

      return () => clearTimeout(timeoutId);
    }
  }, [state, enablePersistence, planLimits.workflowPersistence, persistenceKey, autoSave]);

  // Load persisted workflow on mount
  useEffect(() => {
    if (enablePersistence && planLimits.workflowPersistence) {
      try {
        const persistedData = localStorage.getItem(persistenceKey);
        if (persistedData) {
          const workflowData = JSON.parse(persistedData);
          if (workflowData.version === '2.0.0') {
            dispatch({ type: WORKFLOW_ACTIONS.LOAD_WORKFLOW, payload: workflowData });
          }
        }
      } catch (error) {
        console.error('Failed to load persisted workflow:', error);
      }
    }
  }, [enablePersistence, planLimits.workflowPersistence, persistenceKey]);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    getCurrentStep: () => state.currentStep,
    getWorkflowData: () => state.workflowData,
    getCompletedSteps: () => state.completedSteps,
    getWorkflowMetrics: () => state.workflowMetrics,
    navigateToStep: (stepIndex) => actions.setCurrentStep(stepIndex),
    completeCurrentStep: () => actions.completeStep(state.currentStep),
    resetWorkflow: (template) => actions.resetWorkflow(template),
    exportWorkflow: () => actions.exportWorkflow(),
    validateCurrentStep: () => actions.validateStep(state.currentStep),
    getStepValidationErrors: (stepIndex) => state.stepValidationErrors[stepIndex] || [],
    trackEvent: (event) => actions.trackAnalyticsEvent(event),
    getAnalytics: () => state.analytics
  }), [state, actions]);

  // Enhanced action creators with comprehensive functionality
  const setCurrentStep = useCallback(async (step) => {
    if (!planLimits.customWorkflows && step !== state.currentStep + 1 && step !== state.currentStep - 1) {
      announceToScreenReader('Custom navigation is not available in your current plan');
      return;
    }

    dispatch({ type: WORKFLOW_ACTIONS.SET_CURRENT_STEP, payload: step });

    if (enableAnalytics && planLimits.workflowAnalytics) {
      dispatch({
        type: WORKFLOW_ACTIONS.TRACK_ANALYTICS_EVENT,
        payload: {
          type: 'step_navigation',
          stepIndex: step,
          timestamp: Date.now()
        }
      });
    }

    await updateUsage('workflow_navigation', 1, { stepIndex: step, planTier });
    announceToScreenReader(`Navigated to step ${step + 1}: ${WORKFLOW_STEPS[step]?.label}`);
  }, [planLimits.customWorkflows, state.currentStep, enableAnalytics, planLimits.workflowAnalytics, updateUsage, planTier, announceToScreenReader]);

  const nextStep = useCallback(async () => {
    if (state.currentStep >= state.activeSteps.length - 1) {
      announceToScreenReader('Already at the last step');
      return;
    }

    dispatch({ type: WORKFLOW_ACTIONS.NEXT_STEP });

    if (enableAnalytics && planLimits.workflowAnalytics) {
      dispatch({
        type: WORKFLOW_ACTIONS.TRACK_ANALYTICS_EVENT,
        payload: {
          type: 'step_next',
          fromStep: state.currentStep,
          toStep: state.currentStep + 1
        }
      });
    }

    await updateUsage('workflow_next', 1, { currentStep: state.currentStep, planTier });
    announceToScreenReader(`Advanced to step ${state.currentStep + 2}`);
  }, [state.currentStep, state.activeSteps.length, enableAnalytics, planLimits.workflowAnalytics, updateUsage, planTier, announceToScreenReader]);

  const previousStep = useCallback(async () => {
    if (state.currentStep <= 0) {
      announceToScreenReader('Already at the first step');
      return;
    }

    dispatch({ type: WORKFLOW_ACTIONS.PREVIOUS_STEP });

    if (enableAnalytics && planLimits.workflowAnalytics) {
      dispatch({
        type: WORKFLOW_ACTIONS.TRACK_ANALYTICS_EVENT,
        payload: {
          type: 'step_previous',
          fromStep: state.currentStep,
          toStep: state.currentStep - 1
        }
      });
    }

    await updateUsage('workflow_previous', 1, { currentStep: state.currentStep, planTier });
    announceToScreenReader(`Returned to step ${state.currentStep}`);
  }, [state.currentStep, enableAnalytics, planLimits.workflowAnalytics, updateUsage, planTier, announceToScreenReader]);

  // Basic action creators
  const setLoading = useCallback((loading) => {
    dispatch({ type: WORKFLOW_ACTIONS.SET_LOADING, payload: loading });
  }, []);

  const setError = useCallback((error) => {
    dispatch({ type: WORKFLOW_ACTIONS.SET_ERROR, payload: error });
    announceToScreenReader(`Workflow error: ${error}`);
  }, [announceToScreenReader]);

  const resetWorkflow = useCallback(async (template = null) => {
    dispatch({
      type: WORKFLOW_ACTIONS.RESET_WORKFLOW,
      payload: { template, correlationId: generateCorrelationId() }
    });

    if (enableAnalytics && planLimits.workflowAnalytics) {
      dispatch({
        type: WORKFLOW_ACTIONS.TRACK_ANALYTICS_EVENT,
        payload: {
          type: 'workflow_reset',
          template,
          timestamp: Date.now()
        }
      });
    }

    await updateUsage('workflow_reset', 1, { template, planTier });
    announceToScreenReader('Workflow has been reset');
  }, [enableAnalytics, planLimits.workflowAnalytics, updateUsage, planTier, announceToScreenReader]);

  const updateStepData = useCallback(async (step, data) => {
    dispatch({
      type: WORKFLOW_ACTIONS.UPDATE_STEP_DATA,
      payload: { step, data }
    });

    if (enableAnalytics && planLimits.workflowAnalytics) {
      dispatch({
        type: WORKFLOW_ACTIONS.TRACK_ANALYTICS_EVENT,
        payload: {
          type: 'step_data_updated',
          step,
          dataSize: JSON.stringify(data).length
        }
      });
    }

    await updateUsage('workflow_data_update', 1, { step, planTier });
  }, [enableAnalytics, planLimits.workflowAnalytics, updateUsage, planTier]);

  // Utility functions
  const canProceedToStep = useCallback((stepIndex) => {
    // Check plan limits
    if (stepIndex >= planLimits.maxWorkflowSteps) {
      return false;
    }

    // Check step dependencies
    const step = WORKFLOW_STEPS[stepIndex];
    if (step?.dependencies) {
      for (const depId of step.dependencies) {
        const depIndex = WORKFLOW_STEPS.findIndex(s => s.id === depId);
        if (depIndex !== -1 && !state.completedSteps.includes(depIndex)) {
          return false;
        }
      }
    }

    // Check if all previous required steps are completed
    for (let i = 0; i < stepIndex; i++) {
      const prevStep = WORKFLOW_STEPS[i];
      if (prevStep?.required && !state.completedSteps.includes(i)) {
        return false;
      }
    }

    return true;
  }, [state.completedSteps, planLimits.maxWorkflowSteps]);

  const isStepCompleted = useCallback((stepIndex) => {
    return state.completedSteps.includes(stepIndex);
  }, [state.completedSteps]);

  const getCurrentStepData = useCallback(() => {
    const currentStepId = WORKFLOW_STEPS[state.currentStep]?.id;
    return state.workflowData[currentStepId] || null;
  }, [state.currentStep, state.workflowData]);

  // Enhanced error handling actions
  const clearError = useCallback(() => {
    errorHandler.clearError();
    dispatch({ type: WORKFLOW_ACTIONS.SET_ERROR, payload: null });
    announceToScreenReader('Error cleared');
  }, [errorHandler, announceToScreenReader]);

  // Analytics actions
  const trackAnalyticsEvent = useCallback((event) => {
    if (enableAnalytics && planLimits.workflowAnalytics) {
      dispatch({
        type: WORKFLOW_ACTIONS.TRACK_ANALYTICS_EVENT,
        payload: event
      });
    }
  }, [enableAnalytics, planLimits.workflowAnalytics]);

  // Memoized actions object
  const actions = useMemo(() => ({
    setCurrentStep,
    nextStep,
    previousStep,
    updateStepData,
    setLoading,
    setError,
    resetWorkflow,
    canProceedToStep,
    isStepCompleted,
    getCurrentStepData,
    clearError,
    trackAnalyticsEvent,
    // Error state from error handler
    handleError: errorHandler.handleError,
    retryOperation: errorHandler.retry,
    executeWithErrorHandling: errorHandler.executeWithErrorHandling,
    errorInfo: errorHandler.error,
    isRetrying: errorHandler.isRetrying,
    canRetry: errorHandler.canRetry
  }), [
    setCurrentStep,
    nextStep,
    previousStep,
    updateStepData,
    setLoading,
    setError,
    resetWorkflow,
    canProceedToStep,
    isStepCompleted,
    getCurrentStepData,
    clearError,
    trackAnalyticsEvent,
    errorHandler
  ]);

  // Memoized context value for performance
  const contextValue = useMemo(() => ({
    ...state,
    actions,
    steps: WORKFLOW_STEPS,
    templates: WORKFLOW_TEMPLATES,
    planLimits,
    planTier
  }), [state, actions, planLimits, planTier]);

  return (
    <WorkflowContext.Provider value={contextValue}>
      {children}
    </WorkflowContext.Provider>
  );
}));

WorkflowProvider.displayName = 'WorkflowProvider';

WorkflowProvider.propTypes = {
  /** Child components */
  children: PropTypes.node.isRequired,
  /** Workflow template to initialize with */
  template: PropTypes.oneOf(['basic-content', 'full-strategy', 'enterprise-workflow']),
  /** Enable workflow persistence */
  enablePersistence: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Enable collaboration features */
  enableCollaboration: PropTypes.bool,
  /** Enable auto-save functionality */
  autoSave: PropTypes.bool,
  /** Local storage key for persistence */
  persistenceKey: PropTypes.string
};

// Custom hook to use workflow context
// eslint-disable-next-line react-refresh/only-export-components
export const useWorkflow = () => {
  const context = useContext(WorkflowContext);
  if (!context) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  return context;
};

export default WorkflowProvider;

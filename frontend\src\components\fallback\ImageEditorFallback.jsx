/**
 * Enhanced Image Editor Fallback Component - Enterprise-grade image editor error fallback with intelligent recovery
 * Features: Subscription-based feature gating, comprehensive error categorization, intelligent retry mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced image editor error handling capabilities and interactive recovery exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Alert,
  AlertTitle,
  Button,
  Card,
  CardContent,
  Chip,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  alpha,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Image as ImageIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Memory as MemoryIcon,
  NetworkCheck as NetworkIcon,
  BugReport as BugIcon,
  Security as SecurityIcon,
  Upgrade as UpgradeIcon,
  Help as HelpIcon,
  Brush as BrushIcon,
  Download as DownloadIcon,
  ClearAll as ClearIcon,
  BrowserUpdated as BrowserIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
// @ts-expect-error - AuthContext is a JSX file without TypeScript declarations
import { useAuth } from '../../contexts/AuthContext';
// @ts-expect-error - SubscriptionContext is a JSX file without TypeScript declarations
import { useSubscription } from '../../contexts/SubscriptionContext';
// @ts-expect-error - useNotification is a JS file without TypeScript declarations
import { useNotification } from '../../hooks/useNotification';
import { useAccessibility } from '../../hooks/useAccessibility';

// Enhanced component imports
// @ts-expect-error - ErrorBoundary is a JSX file without TypeScript declarations
import ErrorBoundary from '../common/ErrorBoundary';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Error categories for intelligent handling
const ERROR_CATEGORIES = {
  MEMORY: 'memory',
  NETWORK: 'network',
  RENDERING: 'rendering',
  CANVAS: 'canvas',
  PERMISSION: 'permission',
  SUBSCRIPTION: 'subscription',
  BROWSER_COMPATIBILITY: 'browser_compatibility',
  FILE_FORMAT: 'file_format',
  UNKNOWN: 'unknown'
};

// Image editor types for specific fallback messages
const EDITOR_TYPES = {
  BASIC: 'basic_editor',
  ADVANCED: 'advanced_editor',
  FILTERS: 'filters',
  EFFECTS: 'effects',
  CROPPING: 'cropping',
  LAYERS: 'layers'
};

// Recovery strategies
const RECOVERY_STRATEGIES = {
  RETRY: 'retry',
  BASIC_EDITOR: 'basic_editor',
  DOWNLOAD_ORIGINAL: 'download_original',
  CLEAR_CACHE: 'clear_cache',
  BROWSER_CHECK: 'browser_check',
  UPGRADE: 'upgrade',
  CONTACT_SUPPORT: 'contact_support'
};

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  RETRY_DELAY: 1000,
  MAX_RETRIES: 3,
  MEMORY_WARNING: 50 * 1024 * 1024, // 50MB
  MEMORY_CRITICAL: 100 * 1024 * 1024, // 100MB
  NETWORK_TIMEOUT: 10000, // 10 seconds
  MAX_IMAGE_SIZE: 10 * 1024 * 1024, // 10MB
  CANVAS_MAX_SIZE: 4096 // 4K resolution
};

// ===========================
// TYPE DEFINITIONS (JSDoc)
// ===========================

/**
 * @typedef {Object} ErrorInfo
 * @property {string} message - Error message
 * @property {string} stack - Error stack trace
 * @property {string} category - Error category
 * @property {number} timestamp - Error timestamp
 * @property {string} [code] - Error code
 * @property {Object} [metadata] - Additional error metadata
 */

/**
 * @typedef {Object} ImageEditorConfig
 * @property {string} type - Editor type
 * @property {string} title - Editor title
 * @property {Object} image - Image data
 * @property {Object} [options] - Editor options
 * @property {boolean} [hasLayers] - Whether editor supports layers
 * @property {boolean} [hasFilters] - Whether editor supports filters
 * @property {boolean} [hasEffects] - Whether editor supports effects
 */

/**
 * @typedef {Object} RecoveryAction
 * @property {string} id - Action ID
 * @property {string} label - Action label
 * @property {string} description - Action description
 * @property {string} strategy - Recovery strategy
 * @property {Function} handler - Action handler function
 * @property {boolean} [requiresSubscription] - Whether action requires subscription
 * @property {string} [subscriptionTier] - Required subscription tier
 */

/**
 * @typedef {Object} SubscriptionFeatures
 * @property {string} planId - Subscription plan ID
 * @property {string} planName - Subscription plan name
 * @property {boolean} hasAdvancedEditor - Whether plan includes advanced editor
 * @property {boolean} hasFilters - Whether plan includes filters
 * @property {boolean} hasEffects - Whether plan includes effects
 * @property {boolean} hasLayers - Whether plan includes layers
 * @property {boolean} hasErrorAnalytics - Whether plan includes error analytics
 * @property {boolean} hasCustomFallbacks - Whether plan includes custom fallbacks
 * @property {boolean} hasPrioritySupport - Whether plan includes priority support
 * @property {boolean} hasImageExport - Whether plan includes image export
 * @property {number} maxRetries - Maximum retry attempts
 * @property {number} maxImageSize - Maximum image size supported
 * @property {string} trackingLevel - Error tracking level
 */

/**
 * @typedef {Object} ComponentState
 * @property {boolean} loading - Loading state
 * @property {boolean} retrying - Retry state
 * @property {boolean} showDetails - Show error details
 * @property {boolean} showRecovery - Show recovery options
 * @property {boolean} showUpgradeDialog - Show upgrade dialog
 * @property {boolean} showBrowserCheck - Show browser compatibility check
 * @property {number} retryCount - Current retry count
 * @property {number} lastRetryTime - Last retry timestamp
 * @property {Array<string>} recoveryHistory - Recovery action history
 * @property {Object} performanceMetrics - Performance metrics
 * @property {Object} browserInfo - Browser compatibility info
 * @property {string} [lastError] - Last error message
 */

/**
 * @typedef {Object} NotificationState
 * @property {boolean} open - Notification open state
 * @property {string} message - Notification message
 * @property {'success'|'error'|'warning'|'info'} severity - Notification severity
 * @property {Object} [action] - Notification action
 */

/**
 * @typedef {Object} ImageEditorFallbackProps
 * @property {Function} [onRetry] - Retry callback function
 * @property {ErrorInfo} [error] - Error information
 * @property {Object} [image] - Image data object
 * @property {ImageEditorConfig} [editorConfig] - Editor configuration
 * @property {string} [editorType] - Editor type
 * @property {boolean} [showRecoveryOptions] - Show recovery options
 * @property {boolean} [enableAnalytics] - Enable error analytics
 * @property {Function} [onBasicEditor] - Basic editor callback
 * @property {Function} [onDownload] - Download callback
 * @property {Function} [onUpgrade] - Upgrade callback
 * @property {Function} [onContactSupport] - Contact support callback
 * @property {string} [ariaLabel] - ARIA label
 * @property {string} [ariaDescription] - ARIA description
 * @property {string} [testId] - Test ID
 * @property {string} [className] - CSS class name
 * @property {Object} [style] - Inline styles
 */

/**
 * @typedef {Object} ImageEditorFallbackHandle
 * @property {Function} retry - Retry function
 * @property {Function} reset - Reset function
 * @property {Function} switchToBasicEditor - Switch to basic editor function
 * @property {Function} downloadOriginal - Download original function
 * @property {Function} clearCache - Clear cache function
 * @property {Function} checkBrowserCompatibility - Check browser compatibility function
 * @property {Function} getErrorInfo - Get error information
 * @property {Function} getPerformanceMetrics - Get performance metrics
 * @property {Function} focus - Focus function
 * @property {Function} announce - Screen reader announcement
 */

/**
 * Enhanced ImageEditorFallback Component - Enterprise-grade image editor error fallback with intelligent recovery
 * Features: Subscription-based feature gating, comprehensive error categorization, intelligent retry mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced image editor error handling capabilities and interactive recovery exploration
 *
 * @component
 * @param {ImageEditorFallbackProps} props - Component props
 * @returns {React.Component} Enhanced image editor fallback component
 */
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
const ImageEditorFallback = memo(forwardRef((props, ref) => {
  const {
    onRetry,
    error,
    image,
    editorConfig,
    editorType = EDITOR_TYPES.BASIC,
    showRecoveryOptions = true,
    enableAnalytics = true,
    onBasicEditor,
    onDownload,
    onUpgrade,
    onContactSupport,
    ariaLabel,
    ariaDescription,
    testId = 'image-editor-fallback',
    className = '',
    style = {}
  } = props;

  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { hasFeature } = useAuth();
  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  // @ts-expect-error - Intentionally unused for future responsive features

  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced state management with comprehensive error handling
  const [state, setState] = useState(/** @type {ComponentState} */ ({
    loading: subscriptionLoading,
    retrying: false,
    showDetails: false,
    showRecovery: false,
    showUpgradeDialog: false,
    showBrowserCheck: false,
    retryCount: 0,
    lastRetryTime: 0,
    recoveryHistory: [],
    performanceMetrics: {
      errorCount: 0,
      lastErrorTime: null,
      recoveryAttempts: 0,
      successfulRecoveries: 0,
      imageLoadTime: null,
      canvasOperations: 0
    },
    browserInfo: {
      supportsCanvas: !!window.HTMLCanvasElement,
      supportsWebGL: !!window.WebGLRenderingContext,
      supportsFileAPI: !!window.File && !!window.FileReader,
      userAgent: navigator.userAgent,
      memoryInfo: navigator.deviceMemory || 'unknown'
    },
    lastError: null
  }));

  const [notification, setNotification] = useState(/** @type {NotificationState} */ ({
    open: false,
    message: '',
    severity: 'info'
  }));

  // Enhanced refs for comprehensive tracking
  const containerRef = useRef(null);
  const retryButtonRef = useRef(null);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   * @returns {SubscriptionFeatures} Subscription features object
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Simple plan-based features
    const features = {
      creator: {
        hasAdvancedEditor: false,
        hasFilters: false,
        hasEffects: false,
        hasLayers: false,
        hasErrorAnalytics: false,
        hasCustomFallbacks: false,
        hasPrioritySupport: false,
        hasImageExport: true,
        maxRetries: 3,
        maxImageSize: 5 * 1024 * 1024, // 5MB
        trackingLevel: 'basic'
      },
      accelerator: {
        hasAdvancedEditor: true,
        hasFilters: true,
        hasEffects: false,
        hasLayers: false,
        hasErrorAnalytics: true,
        hasCustomFallbacks: false,
        hasPrioritySupport: false,
        hasImageExport: true,
        maxRetries: 5,
        maxImageSize: 10 * 1024 * 1024, // 10MB
        trackingLevel: 'advanced'
      },
      dominator: {
        hasAdvancedEditor: true,
        hasFilters: true,
        hasEffects: true,
        hasLayers: true,
        hasErrorAnalytics: true,
        hasCustomFallbacks: true,
        hasPrioritySupport: true,
        hasImageExport: true,
        maxRetries: 10,
        maxImageSize: 50 * 1024 * 1024, // 50MB
        trackingLevel: 'ai-powered'
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      ...currentFeatures
    };
  }, [subscription?.plan_id, subscription?.plan_name]);

  /**
   * Categorize error based on message and context - Production Ready
   * @param {ErrorInfo} errorInfo - Error information
   * @returns {string} Error category
   */
  const categorizeError = useCallback((errorInfo) => {
    if (!errorInfo || !errorInfo.message) return ERROR_CATEGORIES.UNKNOWN;

    const message = errorInfo.message.toLowerCase();

    if (message.includes('memory') || message.includes('heap') || message.includes('out of memory')) {
      return ERROR_CATEGORIES.MEMORY;
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout') || message.includes('connection')) {
      return ERROR_CATEGORIES.NETWORK;
    }
    if (message.includes('render') || message.includes('webgl') || message.includes('gpu')) {
      return ERROR_CATEGORIES.RENDERING;
    }
    if (message.includes('canvas') || message.includes('context') || message.includes('2d') || message.includes('webgl')) {
      return ERROR_CATEGORIES.CANVAS;
    }
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden') || message.includes('access')) {
      return ERROR_CATEGORIES.PERMISSION;
    }
    if (message.includes('subscription') || message.includes('plan') || message.includes('upgrade') || message.includes('limit')) {
      return ERROR_CATEGORIES.SUBSCRIPTION;
    }
    if (message.includes('browser') || message.includes('unsupported') || message.includes('compatibility')) {
      return ERROR_CATEGORIES.BROWSER_COMPATIBILITY;
    }
    if (message.includes('format') || message.includes('file') || message.includes('image') || message.includes('corrupt')) {
      return ERROR_CATEGORIES.FILE_FORMAT;
    }

    return ERROR_CATEGORIES.UNKNOWN;
  }, []);

  /**
   * Get error-specific icon - Production Ready
   * @param {string} category - Error category
   * @returns {React.Component} Error icon component
   */
  const getErrorIcon = useCallback((category) => {
    const iconProps = { sx: { fontSize: 64, mb: 2 } };

    switch (category) {
      case ERROR_CATEGORIES.MEMORY:
        return <MemoryIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.YELLOW }} />;
      case ERROR_CATEGORIES.NETWORK:
        return <NetworkIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.PURPLE }} />;
      case ERROR_CATEGORIES.RENDERING:
        return <BugIcon {...iconProps} sx={{ ...iconProps.sx, color: 'error.main' }} />;
      case ERROR_CATEGORIES.CANVAS:
        return <BrushIcon {...iconProps} sx={{ ...iconProps.sx, color: 'warning.main' }} />;
      case ERROR_CATEGORIES.PERMISSION:
        return <SecurityIcon {...iconProps} sx={{ ...iconProps.sx, color: 'warning.main' }} />;
      case ERROR_CATEGORIES.SUBSCRIPTION:
        return <UpgradeIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.PURPLE }} />;
      case ERROR_CATEGORIES.BROWSER_COMPATIBILITY:
        return <BrowserIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.YELLOW }} />;
      case ERROR_CATEGORIES.FILE_FORMAT:
        return <ImageIcon {...iconProps} sx={{ ...iconProps.sx, color: 'error.main' }} />;
      default:
        return <ImageIcon {...iconProps} sx={{ ...iconProps.sx, color: 'text.secondary' }} />;
    }
  }, []);

  /**
   * Get category-specific error message - Production Ready
   * @param {string} category - Error category
   * @param {string} editorType - Editor type
   * @returns {Object} Error message object with title and description
   */
  const getErrorMessage = useCallback((category, editorType) => {
    const editorName = editorType ? `${editorType.replace('_', ' ')}` : 'image editor';

    const messages = {
      [ERROR_CATEGORIES.MEMORY]: {
        title: 'Memory Limit Reached',
        description: `The ${editorName} couldn't be loaded due to insufficient memory. Try using a smaller image or closing other browser tabs.`
      },
      [ERROR_CATEGORIES.NETWORK]: {
        title: 'Network Connection Issue',
        description: `Unable to load ${editorName} resources due to network connectivity issues. Please check your connection and try again.`
      },
      [ERROR_CATEGORIES.RENDERING]: {
        title: 'Rendering Error',
        description: `The ${editorName} encountered a rendering error. This may be due to graphics driver or browser compatibility issues.`
      },
      [ERROR_CATEGORIES.CANVAS]: {
        title: 'Canvas Error',
        description: `Unable to initialize the canvas for the ${editorName}. Your browser may not support required canvas features.`
      },
      [ERROR_CATEGORIES.PERMISSION]: {
        title: 'Access Restricted',
        description: `You don't have permission to use the ${editorName}. Contact your administrator for access.`
      },
      [ERROR_CATEGORIES.SUBSCRIPTION]: {
        title: 'Subscription Required',
        description: `Advanced ${editorName} features require a higher subscription plan. Upgrade to access this functionality.`
      },
      [ERROR_CATEGORIES.BROWSER_COMPATIBILITY]: {
        title: 'Browser Compatibility Issue',
        description: `Your browser doesn't support all features required for the ${editorName}. Try updating your browser or using a different one.`
      },
      [ERROR_CATEGORIES.FILE_FORMAT]: {
        title: 'File Format Error',
        description: `The image format is not supported by the ${editorName}. Try using a different image format (JPEG, PNG, WebP).`
      },
      [ERROR_CATEGORIES.UNKNOWN]: {
        title: 'Image Editor Unavailable',
        description: `The ${editorName} couldn't be loaded. You can still use basic image features.`
      }
    };

    return messages[category] || messages[ERROR_CATEGORIES.UNKNOWN];
  }, []);

  /**
   * Enhanced retry handler with intelligent backoff - Production Ready
   * @returns {Promise<void>}
   */
  const handleRetry = useCallback(async () => {
    if (state.retryCount >= subscriptionFeatures.maxRetries) {
      const errorMessage = `Maximum retry attempts (${subscriptionFeatures.maxRetries}) reached`;
      setState(prev => ({ ...prev, lastError: errorMessage }));
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
      return;
    }

    try {
      setState(prev => ({
        ...prev,
        retrying: true,
        retryCount: prev.retryCount + 1,
        lastRetryTime: Date.now(),
        performanceMetrics: {
          ...prev.performanceMetrics,
          recoveryAttempts: prev.performanceMetrics.recoveryAttempts + 1
        }
      }));

      announceToScreenReader('Retrying image editor loading...');

      // Intelligent retry delay based on attempt count
      const delay = PERFORMANCE_THRESHOLDS.RETRY_DELAY * Math.pow(2, state.retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));

      if (onRetry) {
        await onRetry();
      } else {
        // Fallback to page reload
        window.location.reload();
      }

      setState(prev => ({
        ...prev,
        performanceMetrics: {
          ...prev.performanceMetrics,
          successfulRecoveries: prev.performanceMetrics.successfulRecoveries + 1
        }
      }));

      showSuccessNotification('Image editor loading retry initiated');
      announceToScreenReader('Image editor loading retry successful');

      // Track analytics
      if (enableAnalytics && window.analytics) {
        window.analytics.track('Image Editor Fallback Retry', {
          retryCount: state.retryCount + 1,
          errorCategory: categorizeError(error),
          editorType,
          subscriptionPlan: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }

    } catch (retryError) {
      console.error('Retry failed:', retryError);
      const errorMessage = 'Retry failed. Please try again later.';
      setState(prev => ({ ...prev, lastError: errorMessage }));
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
    } finally {
      setState(prev => ({ ...prev, retrying: false }));
    }
  }, [
    state.retryCount,
    subscriptionFeatures.maxRetries,
    onRetry,
    enableAnalytics,
    categorizeError,
    error,
    editorType,
    subscriptionFeatures.planId,
    showErrorNotification,
    showSuccessNotification,
    announceToScreenReader
  ]);

  /**
   * Handle switch to basic editor - Production Ready
   */
  const handleBasicEditor = useCallback(() => {
    try {
      announceToScreenReader('Switching to basic image editor...');

      if (onBasicEditor) {
        onBasicEditor();
      } else {
        // Fallback basic editor functionality
        showSuccessNotification('Switched to basic editor mode');
      }

      setState(prev => ({
        ...prev,
        recoveryHistory: [...prev.recoveryHistory, 'basic_editor']
      }));

      announceToScreenReader('Switched to basic image editor');
    } catch (basicEditorError) {
      console.error('Basic editor switch failed:', basicEditorError);
      showErrorNotification('Failed to switch to basic editor');
      announceToScreenReader('Basic editor switch failed');
    }
  }, [onBasicEditor, showErrorNotification, showSuccessNotification, announceToScreenReader]);

  /**
   * Handle download original image - Production Ready
   */
  const handleDownloadOriginal = useCallback(() => {
    try {
      announceToScreenReader('Downloading original image...');

      if (onDownload) {
        onDownload(image);
      } else if (image && image.url) {
        // Fallback download functionality
        const link = document.createElement('a');
        link.href = image.url;
        link.download = image.name || 'image';
        link.click();
      }

      setState(prev => ({
        ...prev,
        recoveryHistory: [...prev.recoveryHistory, 'download_original']
      }));

      showSuccessNotification('Original image download initiated');
      announceToScreenReader('Original image download started');
    } catch (downloadError) {
      console.error('Download failed:', downloadError);
      showErrorNotification('Failed to download original image');
      announceToScreenReader('Image download failed');
    }
  }, [onDownload, image, showErrorNotification, showSuccessNotification, announceToScreenReader]);

  /**
   * Handle clear cache - Production Ready
   */
  const handleClearCache = useCallback(() => {
    try {
      announceToScreenReader('Clearing browser cache...');

      // Clear various browser caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name));
        });
      }

      // Clear localStorage related to image editor
      Object.keys(localStorage).forEach(key => {
        if (key.includes('image') || key.includes('editor') || key.includes('canvas')) {
          localStorage.removeItem(key);
        }
      });

      setState(prev => ({
        ...prev,
        recoveryHistory: [...prev.recoveryHistory, 'clear_cache']
      }));

      showSuccessNotification('Browser cache cleared');
      announceToScreenReader('Browser cache has been cleared');
    } catch (cacheError) {
      console.error('Cache clear failed:', cacheError);
      showErrorNotification('Failed to clear cache');
      announceToScreenReader('Cache clearing failed');
    }
  }, [showErrorNotification, showSuccessNotification, announceToScreenReader]);

  /**
   * Handle browser compatibility check - Production Ready
   */
  const handleBrowserCheck = useCallback(() => {
    setState(prev => ({ ...prev, showBrowserCheck: true }));
    announceToScreenReader('Showing browser compatibility information');
  }, [announceToScreenReader]);

  /**
   * Enhanced upgrade prompt handler - Production Ready
   */
  const handleUpgradePrompt = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: true }));

    // Track upgrade prompt analytics
    if (enableAnalytics && window.analytics) {
      window.analytics.track('Image Editor Fallback Upgrade Prompt Shown', {
        currentPlan: subscriptionFeatures.planId,
        errorCategory: categorizeError(error),
        editorType,
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAnalytics, subscriptionFeatures.planId, categorizeError, error, editorType]);

  /**
   * Get recovery actions based on error category and subscription - Production Ready
   * @param {string} category - Error category
   * @returns {Array<RecoveryAction>} Array of recovery actions
   */
  const getRecoveryActions = useCallback((category) => {
    const baseActions = [
      {
        id: 'retry',
        label: 'Retry Loading',
        description: 'Attempt to load the image editor again',
        strategy: RECOVERY_STRATEGIES.RETRY,
        handler: handleRetry,
        requiresSubscription: false
      },
      {
        id: 'basic_editor',
        label: 'Use Basic Editor',
        description: 'Switch to a simplified image editor',
        strategy: RECOVERY_STRATEGIES.BASIC_EDITOR,
        handler: handleBasicEditor,
        requiresSubscription: false
      }
    ];

    // Add category-specific actions
    if (image && image.url) {
      baseActions.push({
        id: 'download_original',
        label: 'Download Original',
        description: 'Download the original image file',
        strategy: RECOVERY_STRATEGIES.DOWNLOAD_ORIGINAL,
        handler: handleDownloadOriginal,
        requiresSubscription: false
      });
    }

    if (category === ERROR_CATEGORIES.MEMORY || category === ERROR_CATEGORIES.CANVAS) {
      baseActions.push({
        id: 'clear_cache',
        label: 'Clear Cache',
        description: 'Clear browser cache to free up memory',
        strategy: RECOVERY_STRATEGIES.CLEAR_CACHE,
        handler: handleClearCache,
        requiresSubscription: false
      });
    }

    if (category === ERROR_CATEGORIES.BROWSER_COMPATIBILITY) {
      baseActions.push({
        id: 'browser_check',
        label: 'Check Browser',
        description: 'View browser compatibility information',
        strategy: RECOVERY_STRATEGIES.BROWSER_CHECK,
        handler: handleBrowserCheck,
        requiresSubscription: false
      });
    }

    if (category === ERROR_CATEGORIES.SUBSCRIPTION) {
      baseActions.push({
        id: 'upgrade',
        label: 'Upgrade Plan',
        description: 'Upgrade to access advanced image editor features',
        strategy: RECOVERY_STRATEGIES.UPGRADE,
        handler: handleUpgradePrompt,
        requiresSubscription: false
      });
    }

    // Add support action for higher tiers
    if (subscriptionFeatures.hasPrioritySupport) {
      baseActions.push({
        id: 'contact_support',
        label: 'Contact Support',
        description: 'Get priority support for this image editor issue',
        strategy: RECOVERY_STRATEGIES.CONTACT_SUPPORT,
        handler: () => {
          if (onContactSupport) {
            onContactSupport();
          } else {
            window.open('mailto:<EMAIL>?subject=Image Editor Loading Issue', '_blank');
          }
        },
        requiresSubscription: true,
        subscriptionTier: 'dominator'
      });
    }

    return baseActions;
  }, [
    handleRetry,
    handleBasicEditor,
    handleDownloadOriginal,
    handleClearCache,
    handleBrowserCheck,
    handleUpgradePrompt,
    image,
    subscriptionFeatures.hasPrioritySupport,
    onContactSupport
  ]);

  /**
   * Enhanced imperative handle for parent component access - Production Ready
   */
  useImperativeHandle(ref, () => ({
    retry: handleRetry,
    reset: () => {
      setState(prev => ({
        ...prev,
        retryCount: 0,
        lastRetryTime: 0,
        recoveryHistory: [],
        lastError: null,
        showDetails: false,
        showRecovery: false,
        showBrowserCheck: false
      }));
      announceToScreenReader('Image editor fallback reset');
    },
    switchToBasicEditor: handleBasicEditor,
    downloadOriginal: handleDownloadOriginal,
    clearCache: handleClearCache,
    checkBrowserCompatibility: handleBrowserCheck,
    getErrorInfo: () => ({
      error,
      category: categorizeError(error),
      retryCount: state.retryCount,
      lastRetryTime: state.lastRetryTime,
      browserInfo: state.browserInfo
    }),
    getPerformanceMetrics: () => state.performanceMetrics,
    focus: () => {
      if (containerRef.current) {
        setFocusToElement(containerRef.current);
      }
    },
    announce: (message) => announceToScreenReader(message)
  }), [
    handleRetry,
    handleBasicEditor,
    handleDownloadOriginal,
    handleClearCache,
    handleBrowserCheck,
    error,
    categorizeError,
    state.retryCount,
    state.lastRetryTime,
    state.browserInfo,
    state.performanceMetrics,
    setFocusToElement,
    announceToScreenReader
  ]);

  // Determine error category and message
  const errorCategory = categorizeError(error);
  const errorMessage = getErrorMessage(errorCategory, editorType);
  const recoveryActions = getRecoveryActions(errorCategory);
  const errorIcon = getErrorIcon(errorCategory);

  // Render the component
  return (
    <ErrorBoundary>
      <Card
        ref={containerRef}
        sx={{
          minHeight: 300,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          backgroundColor: theme.palette.background.default,
          position: 'relative',
          overflow: 'hidden'
        }}
        role="alert"
        aria-label={ariaLabel || `Image editor loading error: ${errorMessage.title}`}
        aria-description={ariaDescription || errorMessage.description}
        data-testid={testId}
        className={className}
        style={style}
      >
        {/* Loading overlay */}
        {state.retrying && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1
            }}
          >
            <Box sx={{ textAlign: 'center' }}>
              <CircularProgress sx={{ color: ACE_COLORS.PURPLE, mb: 2 }} />
              <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
                Retrying... (Attempt {state.retryCount + 1}/{subscriptionFeatures.maxRetries})
              </Typography>
            </Box>
          </Box>
        )}

        <CardContent sx={{ textAlign: 'center', maxWidth: 500, width: '100%' }}>
          {/* Error Icon */}
          {errorIcon}

          {/* Error Title */}
          <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
            {errorMessage.title}
          </Typography>

          {/* Subscription Badge */}
          {errorCategory === ERROR_CATEGORIES.SUBSCRIPTION && (
            <Chip
              label={`${subscriptionFeatures.planName} Plan`}
              size="small"
              sx={{
                mb: 2,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                fontWeight: 600
              }}
            />
          )}

          {/* Error Description */}
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
            {errorMessage.description}
          </Typography>

          {/* Error Details */}
          {error && (
            <Alert
              severity={errorCategory === ERROR_CATEGORIES.SUBSCRIPTION ? 'warning' : 'info'}
              sx={{ mb: 3, textAlign: 'left' }}
              action={
                <IconButton
                  size="small"
                  onClick={() => setState(prev => ({ ...prev, showDetails: !prev.showDetails }))}
                  sx={{ color: 'inherit' }}
                >
                  {state.showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              }
            >
              <AlertTitle>Error Details</AlertTitle>
              <Typography variant="caption">
                {error.message || 'Image editor loading failed'}
              </Typography>

              <Collapse in={state.showDetails}>
                <Box sx={{ mt: 1, pt: 1, borderTop: 1, borderColor: 'divider' }}>
                  <Typography variant="caption" component="div">
                    <strong>Category:</strong> {errorCategory}
                  </Typography>
                  <Typography variant="caption" component="div">
                    <strong>Editor Type:</strong> {editorType}
                  </Typography>
                  <Typography variant="caption" component="div">
                    <strong>Retry Count:</strong> {state.retryCount}/{subscriptionFeatures.maxRetries}
                  </Typography>
                  {subscriptionFeatures.hasErrorAnalytics && (
                    <Typography variant="caption" component="div">
                      <strong>Error ID:</strong> {error.code || 'N/A'}
                    </Typography>
                  )}
                  <Typography variant="caption" component="div">
                    <strong>Canvas Support:</strong> {state.browserInfo.supportsCanvas ? 'Yes' : 'No'}
                  </Typography>
                  <Typography variant="caption" component="div">
                    <strong>WebGL Support:</strong> {state.browserInfo.supportsWebGL ? 'Yes' : 'No'}
                  </Typography>
                </Box>
              </Collapse>
            </Alert>
          )}

          {/* Primary Actions */}
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', mb: 2, flexWrap: 'wrap' }}>
            <Button
              ref={retryButtonRef}
              variant="contained"
              startIcon={state.retrying ? <CircularProgress size={16} /> : <RefreshIcon />}
              onClick={handleRetry}
              disabled={state.retrying || state.retryCount >= subscriptionFeatures.maxRetries}
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.WHITE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                },
                '&:disabled': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.3)
                }
              }}
            >
              {state.retrying ? 'Retrying...' : 'Retry Loading'}
            </Button>

            <Button
              variant="outlined"
              startIcon={<BrushIcon />}
              onClick={handleBasicEditor}
              sx={{
                borderColor: ACE_COLORS.YELLOW,
                color: ACE_COLORS.DARK,
                '&:hover': {
                  borderColor: ACE_COLORS.YELLOW,
                  backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1)
                }
              }}
            >
              Basic Editor
            </Button>

            {showRecoveryOptions && (
              <Button
                variant="outlined"
                startIcon={<HelpIcon />}
                onClick={() => setState(prev => ({ ...prev, showRecovery: !prev.showRecovery }))}
                sx={{
                  borderColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    borderColor: ACE_COLORS.PURPLE,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                  }
                }}
              >
                More Options
              </Button>
            )}
          </Box>

          {/* Recovery Options */}
          <Collapse in={state.showRecovery}>
            <Card sx={{ backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05), border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ mb: 2, color: ACE_COLORS.DARK, fontWeight: 600 }}>
                  Recovery Actions
                </Typography>

                <List dense>
                  {recoveryActions.map((action) => (
                    <ListItem
                      key={action.id}
                      button
                      onClick={action.handler}
                      disabled={action.requiresSubscription && !subscriptionFeatures.hasPrioritySupport && action.id === 'contact_support'}
                      sx={{
                        borderRadius: 1,
                        mb: 1,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                        }
                      }}
                    >
                      <ListItemIcon>
                        {action.strategy === RECOVERY_STRATEGIES.RETRY && <RefreshIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.BASIC_EDITOR && <BrushIcon sx={{ color: ACE_COLORS.YELLOW }} />}
                        {action.strategy === RECOVERY_STRATEGIES.DOWNLOAD_ORIGINAL && <DownloadIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.CLEAR_CACHE && <ClearIcon sx={{ color: ACE_COLORS.YELLOW }} />}
                        {action.strategy === RECOVERY_STRATEGIES.BROWSER_CHECK && <BrowserIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.UPGRADE && <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.CONTACT_SUPPORT && <HelpIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                      </ListItemIcon>
                      <ListItemText
                        primary={action.label}
                        secondary={action.description}
                        primaryTypographyProps={{
                          variant: 'body2',
                          fontWeight: 500,
                          color: ACE_COLORS.DARK
                        }}
                        secondaryTypographyProps={{
                          variant: 'caption',
                          color: 'text.secondary'
                        }}
                      />
                      {action.requiresSubscription && !subscriptionFeatures.hasPrioritySupport && action.id === 'contact_support' && (
                        <Chip
                          label="Upgrade Required"
                          size="small"
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2),
                            color: ACE_COLORS.DARK,
                            fontSize: '0.7rem'
                          }}
                        />
                      )}
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Collapse>

          {/* Performance Tips */}
          <Box sx={{ mt: 3, p: 2, backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1), borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontWeight: 500 }}>
              💡 Performance Tips:
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
              {errorCategory === ERROR_CATEGORIES.MEMORY && 'Close other browser tabs or use a smaller image'}
              {errorCategory === ERROR_CATEGORIES.NETWORK && 'Check your internet connection and try again'}
              {errorCategory === ERROR_CATEGORIES.CANVAS && 'Try updating your browser or graphics drivers'}
              {errorCategory === ERROR_CATEGORIES.BROWSER_COMPATIBILITY && 'Update your browser or try a different one'}
              {errorCategory === ERROR_CATEGORIES.FILE_FORMAT && 'Try converting your image to JPEG or PNG format'}
              {errorCategory === ERROR_CATEGORIES.SUBSCRIPTION && 'Upgrade your plan to access advanced image editing features'}
              {errorCategory === ERROR_CATEGORIES.UNKNOWN && 'Try the basic editor or contact support if the issue persists'}
            </Typography>
          </Box>
        </CardContent>

        {/* Browser Compatibility Dialog */}
        <Dialog
          open={state.showBrowserCheck}
          onClose={() => setState(prev => ({ ...prev, showBrowserCheck: false }))}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
            Browser Compatibility Check
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Your browser compatibility status:
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Required Features:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    {state.browserInfo.supportsCanvas ? <CheckCircleIcon sx={{ color: 'success.main' }} /> : <ErrorIcon sx={{ color: 'error.main' }} />}
                  </ListItemIcon>
                  <ListItemText primary="HTML5 Canvas Support" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {state.browserInfo.supportsWebGL ? <CheckCircleIcon sx={{ color: 'success.main' }} /> : <WarningIcon sx={{ color: 'warning.main' }} />}
                  </ListItemIcon>
                  <ListItemText primary="WebGL Support (for advanced features)" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    {state.browserInfo.supportsFileAPI ? <CheckCircleIcon sx={{ color: 'success.main' }} /> : <ErrorIcon sx={{ color: 'error.main' }} />}
                  </ListItemIcon>
                  <ListItemText primary="File API Support" />
                </ListItem>
              </List>
            </Box>

            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                System Information:
              </Typography>
              <Typography variant="body2" component="div">
                <strong>Browser:</strong> {state.browserInfo.userAgent.split(' ')[0]}
              </Typography>
              <Typography variant="body2" component="div">
                <strong>Memory:</strong> {state.browserInfo.memoryInfo} GB
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setState(prev => ({ ...prev, showBrowserCheck: false }))}
              color="inherit"
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
            Upgrade Your Plan
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Unlock advanced image editing features with a higher tier plan:
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Accelerator Plan Features:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ ml: 2 }}>
                <li>Advanced image editor with filters</li>
                <li>Enhanced error analytics and reporting</li>
                <li>Larger image size support (10MB vs 5MB)</li>
                <li>Increased retry attempts (5 vs 3)</li>
                <li>Priority image processing</li>
              </Typography>
            </Box>

            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Dominator Plan Features:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ ml: 2 }}>
                <li>All Accelerator features</li>
                <li>Advanced effects and layer support</li>
                <li>Custom fallback configurations</li>
                <li>Priority support and assistance</li>
                <li>Maximum image size (50MB)</li>
                <li>AI-powered error resolution</li>
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
              color="inherit"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setState(prev => ({ ...prev, showUpgradeDialog: false }));
                if (onUpgrade) onUpgrade();
              }}
              variant="contained"
              sx={{ backgroundColor: ACE_COLORS.PURPLE }}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Card>
    </ErrorBoundary>
  );
}));
/* eslint-enable no-unused-vars, @typescript-eslint/no-unused-vars */

// Enhanced PropTypes validation for comprehensive type checking
ImageEditorFallback.propTypes = {
  /** Retry callback function */
  onRetry: PropTypes.func,

  /** Error information object */
  error: PropTypes.shape({
    message: PropTypes.string,
    stack: PropTypes.string,
    code: PropTypes.string,
    metadata: PropTypes.object
  }),

  /** Image data object */
  image: PropTypes.shape({
    url: PropTypes.string,
    name: PropTypes.string,
    size: PropTypes.number,
    type: PropTypes.string
  }),

  /** Editor configuration object */
  editorConfig: PropTypes.shape({
    type: PropTypes.string,
    title: PropTypes.string,
    image: PropTypes.object,
    options: PropTypes.object,
    hasLayers: PropTypes.bool,
    hasFilters: PropTypes.bool,
    hasEffects: PropTypes.bool
  }),

  /** Editor type for specific fallback messages */
  editorType: PropTypes.oneOf([
    'basic_editor', 'advanced_editor', 'filters', 'effects', 'cropping', 'layers'
  ]),

  /** Whether to show recovery options */
  showRecoveryOptions: PropTypes.bool,

  /** Whether to enable error analytics */
  enableAnalytics: PropTypes.bool,

  /** Basic editor callback function */
  onBasicEditor: PropTypes.func,

  /** Download callback function */
  onDownload: PropTypes.func,

  /** Upgrade callback function */
  onUpgrade: PropTypes.func,

  /** Contact support callback function */
  onContactSupport: PropTypes.func,

  /** ARIA label for accessibility */
  ariaLabel: PropTypes.string,

  /** ARIA description for accessibility */
  ariaDescription: PropTypes.string,

  /** Test ID for testing */
  testId: PropTypes.string,

  /** CSS class name */
  className: PropTypes.string,

  /** Inline styles */
  style: PropTypes.object
};

// Default props for comprehensive fallback behavior
ImageEditorFallback.defaultProps = {
  onRetry: null,
  error: null,
  image: null,
  editorConfig: null,
  editorType: 'basic_editor',
  showRecoveryOptions: true,
  enableAnalytics: true,
  onBasicEditor: null,
  onDownload: null,
  onUpgrade: null,
  onContactSupport: null,
  ariaLabel: '',
  ariaDescription: '',
  testId: 'image-editor-fallback',
  className: '',
  style: {}
};

// Enhanced display name for debugging and development tools
ImageEditorFallback.displayName = 'ImageEditorFallback';

export default ImageEditorFallback;

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import UnifiedCompetitorsPage from '../UnifiedCompetitorsPage';
import CompetitorDetailPage from '../CompetitorDetailPage';
import CompetitorFormPage from '../CompetitorFormPage';
import CompetitorComparisonPage from '../CompetitorComparisonPage';
import CompetitorsListPage from '../CompetitorsListPage';
import { NotificationProvider } from '../../../contexts/NotificationContext';
import { CompetitorProvider } from '../../../contexts/CompetitorContext';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock child components to focus on page-level accessibility
jest.mock('../../../components/competitors/CompetitorList', () => {
  return function MockCompetitorList() {
    return (
      <div role="list" aria-label="Competitors list">
        <div role="listitem" tabIndex={0}>
          <h3>TechCorp</h3>
          <p>Technology company competitor</p>
          <button aria-label="View TechCorp details">View Details</button>
        </div>
        <div role="listitem" tabIndex={0}>
          <h3>InnovateCo</h3>
          <p>Innovation company competitor</p>
          <button aria-label="View InnovateCo details">View Details</button>
        </div>
      </div>
    );
  };
});

jest.mock('../../../components/competitors/CompetitorComparison', () => {
  return function MockCompetitorComparison() {
    return (
      <div role="region" aria-label="Competitor comparison">
        <h2>Competitor Comparison</h2>
        <div role="img" aria-label="Comparison chart showing engagement rates">
          <canvas aria-describedby="chart-description">Chart</canvas>
          <div id="chart-description" className="sr-only">
            Bar chart comparing engagement rates between selected competitors
          </div>
        </div>
        <table role="table" aria-label="Comparison data table">
          <thead>
            <tr>
              <th scope="col">Competitor</th>
              <th scope="col">Engagement Rate</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>TechCorp</td>
              <td>3.5%</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  };
});

jest.mock('../../../components/competitors/CompetitorDetail', () => {
  return function MockCompetitorDetail() {
    return (
      <div role="main" aria-label="Competitor details">
        <header>
          <h1>TechCorp Details</h1>
        </header>
        <section aria-labelledby="metrics-heading">
          <h2 id="metrics-heading">Performance Metrics</h2>
          <dl>
            <dt>Followers</dt>
            <dd>10,000</dd>
            <dt>Engagement Rate</dt>
            <dd>3.5%</dd>
          </dl>
        </section>
        <nav aria-label="Competitor actions">
          <button aria-label="Edit competitor information">Edit</button>
          <button aria-label="Delete competitor">Delete</button>
        </nav>
      </div>
    );
  };
});

jest.mock('../../../components/competitors/CompetitorForm', () => {
  return function MockCompetitorForm({ isEditMode }) {
    return (
      <form role="form" aria-label={`${isEditMode ? 'Edit' : 'Add'} competitor form`}>
        <fieldset>
          <legend>{isEditMode ? 'Edit' : 'Add'} Competitor Information</legend>
          
          <div>
            <label htmlFor="competitor-name">
              Competitor Name <span aria-label="required">*</span>
            </label>
            <input
              id="competitor-name"
              type="text"
              required
              aria-describedby="name-help"
            />
            <div id="name-help" className="help-text">
              Enter the official name of the competitor company
            </div>
          </div>

          <div>
            <label htmlFor="competitor-website">Website URL</label>
            <input
              id="competitor-website"
              type="url"
              aria-describedby="website-help"
            />
            <div id="website-help" className="help-text">
              Enter the competitor's main website URL
            </div>
          </div>

          <fieldset>
            <legend>Social Media Platforms</legend>
            <div role="group" aria-labelledby="platforms-legend">
              <input type="checkbox" id="linkedin" value="linkedin" />
              <label htmlFor="linkedin">LinkedIn</label>
              
              <input type="checkbox" id="twitter" value="twitter" />
              <label htmlFor="twitter">Twitter</label>
            </div>
          </fieldset>
        </fieldset>

        <div role="group" aria-label="Form actions">
          <button type="submit" aria-describedby="submit-help">
            {isEditMode ? 'Update' : 'Create'} Competitor
          </button>
          <button type="button" aria-label="Cancel and return to competitors list">
            Cancel
          </button>
        </div>
        
        <div id="submit-help" className="help-text">
          {isEditMode ? 'Update' : 'Create'} the competitor with the provided information
        </div>
      </form>
    );
  };
});

// Test wrapper
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <NotificationProvider>
          <CompetitorProvider>
            {children}
          </CompetitorProvider>
        </NotificationProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Competitors Pages Accessibility Tests', () => {
  describe('WCAG 2.1 AA Compliance', () => {
    test('UnifiedCompetitorsPage should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('CompetitorsListPage should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <CompetitorsListPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('CompetitorComparisonPage should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <CompetitorComparisonPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('CompetitorDetailPage should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <CompetitorDetailPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('CompetitorFormPage should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <CompetitorFormPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Semantic HTML and ARIA', () => {
    test('uses proper heading hierarchy', async () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Check main heading
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('Competitor Analysis');

      // Check that headings follow proper hierarchy
      const allHeadings = screen.getAllByRole('heading');
      expect(allHeadings.length).toBeGreaterThan(0);
    });

    test('has proper landmark roles', async () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Check for main landmark
      expect(screen.getByRole('main')).toBeInTheDocument();

      // Check for navigation (breadcrumbs)
      expect(screen.getByLabelText(/breadcrumb navigation/i)).toBeInTheDocument();

      // Check for tablist
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    test('provides proper ARIA labels for interactive elements', async () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Check buttons have proper labels
      expect(screen.getByLabelText(/add new competitor/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/navigate to dashboard/i)).toBeInTheDocument();

      // Check tabs have proper labels
      expect(screen.getByLabelText(/switch to competitor list tab/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/switch to compare competitors tab/i)).toBeInTheDocument();
    });

    test('uses proper form labeling in CompetitorFormPage', async () => {
      render(
        <TestWrapper>
          <CompetitorFormPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('form')).toBeInTheDocument();
      });

      // Check form has proper label
      expect(screen.getByLabelText(/add competitor form/i)).toBeInTheDocument();

      // Check fieldsets have legends
      expect(screen.getByText(/add competitor information/i)).toBeInTheDocument();

      // Check inputs have proper labels
      expect(screen.getByLabelText(/competitor name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/website url/i)).toBeInTheDocument();

      // Check required field indicators
      expect(screen.getByLabelText(/required/i)).toBeInTheDocument();
    });
  });

  describe('Keyboard Navigation', () => {
    test('supports tab navigation through all interactive elements', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Test tab navigation
      await user.tab();
      expect(document.activeElement).toHaveAttribute('aria-label', 'Navigate to dashboard');

      await user.tab();
      expect(document.activeElement).toHaveAttribute('aria-label', 'Add new competitor');
    });

    test('supports arrow key navigation in tabs', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tablist')).toBeInTheDocument();
      });

      // Focus on first tab
      const listTab = screen.getByLabelText(/switch to competitor list tab/i);
      listTab.focus();

      // Use arrow keys to navigate
      await user.keyboard('{ArrowRight}');
      expect(document.activeElement).toHaveAttribute('aria-label', expect.stringContaining('Compare Competitors'));
    });

    test('supports Enter and Space key activation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tablist')).toBeInTheDocument();
      });

      // Focus on Compare tab and activate with Enter
      const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
      compareTab.focus();
      
      await user.keyboard('{Enter}');
      
      // Tab should be activated
      expect(compareTab).toHaveAttribute('aria-selected', 'true');
    });
  });

  describe('Screen Reader Support', () => {
    test('provides descriptive text for data visualizations', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tablist')).toBeInTheDocument();
      });

      // Switch to comparison tab
      const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
      await user.click(compareTab);

      await waitFor(() => {
        expect(screen.getByRole('img', { name: /comparison chart/i })).toBeInTheDocument();
      });

      // Check for chart description
      expect(screen.getByText(/bar chart comparing engagement rates/i)).toBeInTheDocument();
    });

    test('provides status updates for form validation', async () => {
      render(
        <TestWrapper>
          <CompetitorFormPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('form')).toBeInTheDocument();
      });

      // Check for help text
      expect(screen.getByText(/enter the official name of the competitor company/i)).toBeInTheDocument();
      expect(screen.getByText(/enter the competitor's main website url/i)).toBeInTheDocument();
    });

    test('announces dynamic content changes', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tablist')).toBeInTheDocument();
      });

      // Switch tabs and verify content changes are announced
      const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
      await user.click(compareTab);

      // Check that tab panel content is properly labeled
      expect(screen.getByRole('tabpanel')).toHaveAttribute('aria-label', expect.stringContaining('content'));
    });
  });

  describe('Focus Management', () => {
    test('manages focus properly when switching tabs', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tablist')).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
      await user.click(compareTab);

      // Focus should be managed properly
      expect(compareTab).toHaveAttribute('aria-selected', 'true');
    });

    test('provides visible focus indicators', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Tab to first interactive element
      await user.tab();
      
      // Check that focused element has visible focus indicator
      const focusedElement = document.activeElement;
      expect(focusedElement).toHaveAttribute('tabindex');
    });
  });

  describe('Responsive Design Accessibility', () => {
    test('maintains accessibility on mobile viewports', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { container } = render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('maintains minimum touch target sizes', async () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Check that interactive elements meet minimum size requirements (44px)
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        const rect = button.getBoundingClientRect();
        expect(Math.min(rect.width, rect.height)).toBeGreaterThanOrEqual(44);
      });
    });
  });
});

/**
 * Enhanced ICPSelector Component - Enterprise-grade ICP selection with intelligent analytics
 * Features: Subscription-based feature gating, comprehensive error handling, intelligent selection mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced ICP selection capabilities and interactive profile exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  CircularProgress,

  Chip,
  TextField,
  Alert,
  AlertTitle,
  Skeleton,
  alpha,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TablePagination,
  InputAdornment
} from '@mui/material';
import {
  Person as PersonIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,
  Search as SearchIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import ErrorBoundary from "../common/ErrorBoundary";
import api from '../../api';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// ICP selection modes with enhanced configurations
const SELECTION_MODES = {
  SINGLE: {
    id: 'single',
    name: 'Single Selection',
    description: 'Select one ICP at a time',
    subscriptionLimits: {
      creator: { available: true, maxSelections: 1 },
      accelerator: { available: true, maxSelections: 1 },
      dominator: { available: true, maxSelections: 1 }
    }
  },
  MULTIPLE: {
    id: 'multiple',
    name: 'Multiple Selection',
    description: 'Select multiple ICPs for comparison',
    subscriptionLimits: {
      creator: { available: false, maxSelections: 0 },
      accelerator: { available: true, maxSelections: 3 },
      dominator: { available: true, maxSelections: 10 }
    }
  },
  BULK: {
    id: 'bulk',
    name: 'Bulk Selection',
    description: 'Advanced bulk operations with filters',
    subscriptionLimits: {
      creator: { available: false, maxSelections: 0 },
      accelerator: { available: false, maxSelections: 0 },
      dominator: { available: true, maxSelections: -1 }
    }
  }
};



/**
 * Enhanced ICPSelector Component - Enterprise-grade ICP selection with intelligent analytics
 * Features: Subscription-based feature gating, comprehensive error handling, intelligent selection mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced ICP selection capabilities and interactive profile exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {Array} [props.selectedICPs=[]] - Currently selected ICPs
 * @param {Function} [props.onSelectionChange] - Selection change callback
 * @param {string} [props.selectionMode='single'] - Selection mode (single, multiple, bulk)
 * @param {string} [props.viewMode='list'] - View mode (list, grid, compact, detailed)
 * @param {boolean} [props.showPreview=true] - Show ICP preview
 * @param {boolean} [props.enableFilters=true] - Enable filtering
 * @param {boolean} [props.enableSearch=true] - Enable search
 * @param {boolean} [props.enableExport=false] - Enable export functionality
 * @param {boolean} [props.realTimeUpdates=true] - Enable real-time updates
 * @param {string} [props.serviceId] - Service ID filter
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */
const ICPSelector = memo(forwardRef(({
  selectedICPs = [],
  onSelectionChange,
  selectionMode = 'single',
  enableSearch = true,
  enableExport = false,
  realTimeUpdates = true,
  serviceId,
  customization = {},
  className = '',
  style = {},
  testId = 'icp-selector',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced refs for comprehensive tracking
  const containerRef = useRef(null);
  const searchInputRef = useRef(null);

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: subscriptionLoading,
    refreshing: false,
    showFilters: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showCompareDialog: false,
    animationKey: 0,
    errors: {},
    // ICP data state
    icps: [],
    filteredICPs: [],
    searchQuery: '',
    selectedFilters: {},
    sortBy: 'name',
    sortOrder: 'asc',
    currentPage: 0,
    rowsPerPage: 10
  });



  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const selectionLimits = SELECTION_MODES[selectionMode.toUpperCase()]?.subscriptionLimits[planId] ||
                           SELECTION_MODES.SINGLE.subscriptionLimits[planId];

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasMultipleSelection: selectionLimits.available && selectionMode !== 'single',
      hasBulkOperations: planId === 'dominator' && selectionMode === 'bulk',
      hasAdvancedFilters: planId !== 'creator',
      hasExport: enableExport && planId !== 'creator',
      hasRealTime: realTimeUpdates && planId !== 'creator',
      hasAIRecommendations: planId === 'dominator',
      maxSelections: selectionLimits.maxSelections,
      refreshInterval: planId === 'dominator' ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, selectionMode, enableExport, realTimeUpdates]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || 'ICP Selection Interface',
      'aria-description': ariaDescription || 'Select and manage Ideal Customer Profiles for content generation',
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, realTimeUpdates]);

  /**
   * Enhanced ICP loading with subscription integration - Production Ready
   */
  const loadICPs = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, errors: {} }));

      // Determine API endpoint based on serviceId
      const endpoint = serviceId
        ? `/api/icps/service/${serviceId}`
        : '/api/icps';

      const response = await api.get(endpoint);

      // Transform API response to match expected format
      const transformedICPs = response.data.map(icp => ({
        id: icp.id || icp._id,
        name: icp.name,
        description: icp.description,
        demographics: {
          industry: icp.demographics?.industry || 'Not specified',
          company_size: icp.demographics?.company_size || 'Not specified',
          location: icp.demographics?.location || [],
          annual_revenue: icp.demographics?.annual_revenue || 'Not specified',
          employee_count: icp.demographics?.employee_count || 'Not specified'
        },
        decision_maker: {
          title: icp.decision_maker?.title || 'Not specified',
          department: icp.decision_maker?.department || 'Not specified',
          age_range: icp.decision_maker?.age_range || 'Not specified'
        },
        pain_points: icp.pain_points || [],
        goals: icp.goals || [],
        content_preferences: icp.content_preferences || [],
        confidence_score: icp.confidence_score || 85,
        generation_type: icp.generation_type || 'demographic',
        created_at: icp.created_at,
        _originalData: icp
      }));

      setState(prev => ({
        ...prev,
        icps: transformedICPs,
        filteredICPs: transformedICPs,
        loading: false
      }));

      const successMessage = `Loaded ${transformedICPs.length} ICPs successfully`;
      showSuccessNotification(successMessage);
      announceToScreenReader(successMessage);

      // Track analytics
      if (window.analytics) {
        window.analytics.track('ICP Selection Load Success', {
          icpCount: transformedICPs.length,
          serviceId,
          subscriptionPlan: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('Error loading ICPs:', error);
      const errorMessage = error.response?.status === 404
        ? 'No ICPs found for this service'
        : error.response?.status === 403
        ? 'Access denied to ICPs'
        : error.response?.data?.detail || 'Failed to load ICPs';

      setState(prev => ({
        ...prev,
        loading: false,
        errors: { load: errorMessage }
      }));

      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);

      // Track analytics
      if (window.analytics) {
        window.analytics.track('ICP Selection Load Error', {
          errorMessage,
          serviceId,
          subscriptionPlan: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }
    }
  }, [serviceId, showSuccessNotification, showErrorNotification, announceToScreenReader, subscriptionFeatures.planId]);

  /**
   * Enhanced ICP selection handler - Production Ready
   */
  const handleICPSelection = useCallback((icpId, isSelected = null) => {
    const icp = state.icps.find(i => i.id === icpId);
    if (!icp) return;

    let newSelection = [...selectedICPs];

    if (selectionMode === 'single') {
      newSelection = isSelected !== false ? [icp] : [];
    } else {
      const isCurrentlySelected = selectedICPs.some(selected => selected.id === icpId);
      const shouldSelect = isSelected !== null ? isSelected : !isCurrentlySelected;

      if (shouldSelect) {
        // Check subscription limits
        if (newSelection.length >= subscriptionFeatures.maxSelections && subscriptionFeatures.maxSelections !== -1) {
          const errorMessage = `Your ${subscriptionFeatures.planName} plan allows up to ${subscriptionFeatures.maxSelections} ICP selections. Upgrade for more.`;
          showErrorNotification(errorMessage);
          announceToScreenReader(errorMessage);
          setState(prev => ({ ...prev, showUpgradeDialog: true }));
          return;
        }
        newSelection.push(icp);
      } else {
        newSelection = newSelection.filter(selected => selected.id !== icpId);
      }
    }

    if (onSelectionChange) {
      onSelectionChange(newSelection);
    }

    const message = newSelection.length === 0
      ? 'All ICPs deselected'
      : `${newSelection.length} ICP${newSelection.length > 1 ? 's' : ''} selected`;

    announceToScreenReader(message);

    // Track analytics
    if (window.analytics) {
      window.analytics.track('ICP Selection Changed', {
        selectionCount: newSelection.length,
        selectionMode,
        icpId,
        subscriptionPlan: subscriptionFeatures.planId,
        timestamp: new Date().toISOString()
      });
    }
  }, [
    state.icps,
    selectedICPs,
    selectionMode,
    subscriptionFeatures.maxSelections,
    subscriptionFeatures.planName,
    subscriptionFeatures.planId,
    onSelectionChange,
    showErrorNotification,
    announceToScreenReader
  ]);

  /**
   * Enhanced search and filter functionality - Production Ready
   */
  const handleSearch = useCallback((query) => {
    setState(prev => ({ ...prev, searchQuery: query }));

    const filtered = state.icps.filter(icp => {
      const searchMatch = !query ||
        icp.name.toLowerCase().includes(query.toLowerCase()) ||
        icp.description.toLowerCase().includes(query.toLowerCase()) ||
        icp.demographics.industry.toLowerCase().includes(query.toLowerCase());

      // Apply additional filters if any
      const filterMatch = Object.keys(state.selectedFilters).every(filterKey => {
        const filterValue = state.selectedFilters[filterKey];
        if (!filterValue) return true;

        switch (filterKey) {
          case 'industry':
            return icp.demographics.industry === filterValue;
          case 'company_size':
            return icp.demographics.company_size === filterValue;
          case 'confidence_score':
            return icp.confidence_score >= parseInt(filterValue);
          default:
            return true;
        }
      });

      return searchMatch && filterMatch;
    });

    setState(prev => ({ ...prev, filteredICPs: filtered, currentPage: 0 }));
    announceToScreenReader(`${filtered.length} ICPs found`);
  }, [state.icps, state.selectedFilters, announceToScreenReader]);

  // Load ICPs on component mount
  useEffect(() => {
    loadICPs();
  }, [loadICPs]);

  // Update filtered ICPs when search query or filters change
  useEffect(() => {
    handleSearch(state.searchQuery);
  }, [state.searchQuery, handleSearch]);

  // Real-time updates effect
  useEffect(() => {
    if (!subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      loadICPs();
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, loadICPs]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    loadICPs,
    getSelectedICPs: () => selectedICPs,
    clearSelection: () => {
      if (onSelectionChange) {
        onSelectionChange([]);
      }
      announceToScreenReader('All selections cleared');
    },
    selectAll: () => {
      const maxSelections = subscriptionFeatures.maxSelections;
      const icpsToSelect = maxSelections === -1 ? state.filteredICPs : state.filteredICPs.slice(0, maxSelections);

      if (onSelectionChange) {
        onSelectionChange(icpsToSelect);
      }
      announceToScreenReader(`${icpsToSelect.length} ICPs selected`);
    },
    search: handleSearch,
    focus: () => setFocusToElement(containerRef.current),
    announce: (message) => announceToScreenReader(message)
  }), [
    loadICPs,
    selectedICPs,
    onSelectionChange,
    subscriptionFeatures.maxSelections,
    state.filteredICPs,
    handleSearch,
    setFocusToElement,
    announceToScreenReader
  ]);

  // Main render condition checks
  if (state.loading && state.icps.length === 0) {
    return (
      <ErrorBoundary
        fallback={
          <Box sx={{ p: 1, textAlign: 'center' }}>
            <Typography variant="caption" color="error">
              ICP selector unavailable
            </Typography>
          </Box>
        }
      >
        <Box
          sx={{
            ...customization,
            ...style
          }}
          className={className}
          data-testid={testId}
          {...getAccessibilityProps()}
        >
          <Skeleton variant="rectangular" height={56} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" height={200} />
        </Box>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Box sx={{ p: 1, textAlign: 'center' }}>
          <Typography variant="caption" color="error">
            ICP selector error
          </Typography>
        </Box>
      }
    >
      <Box
        ref={containerRef}
        sx={{
          ...customization,
          ...style
        }}
        className={className}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        <Typography variant="h4" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
          <PersonIcon sx={{ mr: 1, verticalAlign: 'middle', color: ACE_COLORS.PURPLE }} />
          ICP Selection
        </Typography>

        {/* Subscription Badge */}
        <Chip
          label={`${subscriptionFeatures.planName} Plan - ${subscriptionFeatures.maxSelections === -1 ? 'Unlimited' : subscriptionFeatures.maxSelections} Selections`}
          size="small"
          sx={{
            mb: 3,
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
            color: ACE_COLORS.PURPLE,
            fontWeight: 600
          }}
        />

        {/* Search and Filters */}
        {enableSearch && (
          <Card sx={{ mb: 3, border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={8}>
                  <TextField
                    ref={searchInputRef}
                    fullWidth
                    placeholder="Search ICPs by name, description, or industry..."
                    value={state.searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon sx={{ color: ACE_COLORS.PURPLE }} />
                        </InputAdornment>
                      )
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        '&.Mui-focused fieldset': {
                          borderColor: ACE_COLORS.PURPLE,
                        },
                      },
                      '& .MuiInputLabel-root.Mui-focused': {
                        color: ACE_COLORS.PURPLE,
                      },
                    }}
                  />
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="outlined"
                      onClick={() => setState(prev => ({ ...prev, refreshing: true }))}
                      disabled={state.refreshing}
                      startIcon={state.refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
                      sx={{
                        borderColor: ACE_COLORS.PURPLE,
                        color: ACE_COLORS.PURPLE,
                        '&:hover': {
                          borderColor: ACE_COLORS.PURPLE,
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                        }
                      }}
                    >
                      Refresh
                    </Button>


                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Selection Summary */}
        {selectedICPs.length > 0 && (
          <Alert
            severity="info"
            sx={{
              mb: 3,
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
            }}
          >
            <AlertTitle>Selection Summary</AlertTitle>
            {selectedICPs.length} ICP{selectedICPs.length > 1 ? 's' : ''} selected for content generation.
            {subscriptionFeatures.maxSelections !== -1 && (
              <Typography variant="caption" sx={{ display: 'block', mt: 1 }}>
                {subscriptionFeatures.maxSelections - selectedICPs.length} selections remaining.
              </Typography>
            )}
          </Alert>
        )}

        {/* ICP List */}
        {state.filteredICPs.length > 0 ? (
          <Grid container spacing={2}>
            {state.filteredICPs
              .slice(state.currentPage * state.rowsPerPage, (state.currentPage + 1) * state.rowsPerPage)
              .map((icp) => {
                const isSelected = selectedICPs.some(selected => selected.id === icp.id);

                return (
                  <Grid item xs={12} key={icp.id}>
                    <Card
                      sx={{
                        borderColor: isSelected ? ACE_COLORS.PURPLE : alpha(ACE_COLORS.PURPLE, 0.2),
                        borderWidth: isSelected ? 2 : 1,
                        borderStyle: 'solid',
                        backgroundColor: isSelected ? alpha(ACE_COLORS.PURPLE, 0.02) : 'background.paper',
                        transition: 'all 0.2s ease-in-out',
                        cursor: 'pointer',
                        '&:hover': {
                          borderColor: ACE_COLORS.PURPLE,
                          boxShadow: `0 4px 12px ${alpha(ACE_COLORS.PURPLE, 0.15)}`
                        }
                      }}
                      onClick={() => handleICPSelection(icp.id)}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                              <PersonIcon sx={{ mr: 1, verticalAlign: 'middle', color: ACE_COLORS.PURPLE }} />
                              {icp.name}
                            </Typography>
                            <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                              {icp.description}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                              <Chip
                                label={icp.demographics.industry}
                                size="small"
                                sx={{ backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1), color: ACE_COLORS.DARK }}
                              />
                              <Chip
                                label={icp.demographics.company_size}
                                size="small"
                                sx={{ backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1), color: ACE_COLORS.PURPLE }}
                              />
                              <Chip
                                label={`${icp.confidence_score}% confidence`}
                                size="small"
                                color={icp.confidence_score >= 90 ? 'success' : icp.confidence_score >= 70 ? 'warning' : 'error'}
                                variant="outlined"
                              />
                            </Box>
                          </Box>

                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {isSelected && (
                              <CheckCircleIcon sx={{ color: ACE_COLORS.PURPLE }} />
                            )}
                            <Button
                              variant={isSelected ? "contained" : "outlined"}
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleICPSelection(icp.id);
                              }}
                              sx={{
                                backgroundColor: isSelected ? ACE_COLORS.PURPLE : 'transparent',
                                borderColor: ACE_COLORS.PURPLE,
                                color: isSelected ? ACE_COLORS.WHITE : ACE_COLORS.PURPLE,
                                '&:hover': {
                                  backgroundColor: isSelected ? alpha(ACE_COLORS.PURPLE, 0.8) : alpha(ACE_COLORS.PURPLE, 0.1),
                                  borderColor: ACE_COLORS.PURPLE
                                }
                              }}
                            >
                              {isSelected ? 'Selected' : 'Select'}
                            </Button>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })}
          </Grid>
        ) : (
          <Alert severity="info" sx={{ mt: 2 }}>
            <AlertTitle>No ICPs Found</AlertTitle>
            {state.searchQuery ?
              'No ICPs match your search criteria. Try adjusting your search terms.' :
              'No ICPs available. Generate ICPs first to use targeted content creation.'
            }
          </Alert>
        )}

        {/* Pagination */}
        {state.filteredICPs.length > state.rowsPerPage && (
          <TablePagination
            component="div"
            count={state.filteredICPs.length}
            page={state.currentPage}
            onPageChange={(event, newPage) => setState(prev => ({ ...prev, currentPage: newPage }))}
            rowsPerPage={state.rowsPerPage}
            onRowsPerPageChange={(event) => setState(prev => ({
              ...prev,
              rowsPerPage: parseInt(event.target.value, 10),
              currentPage: 0
            }))}
            sx={{ mt: 2 }}
          />
        )}

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span">
                Upgrade Required
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Typography variant="body1" gutterBottom>
              Your current {subscriptionFeatures.planName} plan has reached its ICP selection limit.
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Upgrade to Accelerator or Dominator plan for more ICP selections and advanced features.
            </Typography>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
              sx={{ color: theme.palette.text.secondary }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={() => window.open('/pricing', '_blank')}
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': { backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8) }
              }}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
ICPSelector.propTypes = {
  // Core props
  selectedICPs: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
      name: PropTypes.string.isRequired,
      description: PropTypes.string,
      demographics: PropTypes.object,
      decision_maker: PropTypes.object,
      confidence_score: PropTypes.number
    })
  ),
  onSelectionChange: PropTypes.func,

  // Selection configuration props
  selectionMode: PropTypes.oneOf(['single', 'multiple', 'bulk']),
  enableSearch: PropTypes.bool,
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,

  // Data props
  serviceId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),

  // Styling props
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,

  // Accessibility props
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

ICPSelector.defaultProps = {
  selectedICPs: [],
  selectionMode: 'single',
  enableSearch: true,
  enableExport: false,
  realTimeUpdates: true,
  customization: {},
  className: '',
  style: {},
  testId: 'icp-selector'
};

// Display name for debugging
ICPSelector.displayName = 'ICPSelector';

export default ICPSelector;

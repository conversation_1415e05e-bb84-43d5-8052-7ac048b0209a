// @since 2024-1-1 to 2025-25-7
import { Box, Typography, Alert, Button, Paper, Chip, IconButton, Tooltip } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import {
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  ErrorOutline as ErrorIcon,
  Info as InfoIcon,
  Support as SupportIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Fallback component for UnifiedSettingsPage when dynamic import fails
 */
const UnifiedSettingsPageFallback = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const handleRetry = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    navigate('/dashboard');
  };

  const handleContactSupport = () => {
    // Use ACE Social support contact
    window.open('mailto:<EMAIL>?subject=Settings Page Issue', '_blank');
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      {/* Enhanced header with status indicators */}
      <Paper sx={{ p: 3, mb: 3, textAlign: 'center', backgroundColor: 'error.light', borderRadius: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 2 }}>
          <ErrorIcon sx={{ fontSize: 48, color: 'error.main', mr: 1 }} />
          <SettingsIcon sx={{ fontSize: 64, color: 'error.main' }} />
        </Box>

        <Typography variant="h4" gutterBottom color="error.dark">
          Settings Temporarily Unavailable
          {user?.name && (
            <Typography component="span" variant="h6" color="error.main" sx={{ ml: 1, display: 'block' }}>
              Sorry, {user.name}
            </Typography>
          )}
        </Typography>

        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
          <Chip
            label="Service Disruption"
            color="error"
            size="small"
            icon={<ErrorIcon />}
          />
          <Chip
            label="Temporary Issue"
            color="warning"
            size="small"
            variant="outlined"
          />
        </Box>

        <Typography variant="body1" color="error.dark" sx={{ mb: 2 }}>
          We&apos;re having trouble loading the settings page. This is usually a temporary issue.
          {isAuthenticated ? ' Your account data is safe and secure.' : ' Please try logging in again.'}
        </Typography>

        <Typography variant="body2" color="text.secondary">
          Our ACE Social team has been notified and is working to resolve this quickly.
          {user?.email && ` We'll send updates to ${user.email} if needed.`}
        </Typography>
      </Paper>
      
      {/* Enhanced troubleshooting section */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <InfoIcon sx={{ mr: 1, color: 'info.main' }} />
          <Typography variant="h6" color="info.main">
            Troubleshooting Steps
          </Typography>
        </Box>

        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>What you can try{user?.name ? `, ${user.name}` : ''}:</strong>
          </Typography>
          <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
            <li>Refresh the page using the button below</li>
            <li>Clear your browser cache and cookies</li>
            <li>Try again in a few moments</li>
            <li>Check your internet connection</li>
            {!isAuthenticated && <li>Try logging in again</li>}
            <li>Contact ACE Social support if the issue persists</li>
          </ul>
        </Alert>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
          <Chip
            label="Quick Fix"
            color="success"
            size="small"
            icon={<RefreshIcon />}
          />
          <Chip
            label="Usually Resolves in < 5 minutes"
            color="info"
            size="small"
            variant="outlined"
          />
        </Box>
      </Paper>

      {/* Enhanced action buttons */}
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          onClick={handleRetry}
          startIcon={<RefreshIcon />}
          size="large"
        >
          Retry Loading
        </Button>

        <Button
          variant="outlined"
          onClick={handleGoHome}
          startIcon={<HomeIcon />}
          size="large"
        >
          Go to Dashboard
        </Button>

        <Tooltip title="Contact ACE Social support team">
          <IconButton
            color="primary"
            onClick={handleContactSupport}
            size="large"
          >
            <SupportIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Additional help section */}
      <Box sx={{ mt: 4, p: 2, backgroundColor: 'background.default', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary" align="center">
          💡 <strong>Pro Tip:</strong> You can access individual settings pages directly from the main navigation menu.
          {isAuthenticated && user?.subscription?.plan_name &&
            ` Your ${user.subscription.plan_name} plan includes priority support.`
          }
        </Typography>
      </Box>

      {/* Authentication status indicator */}
      {!isAuthenticated && (
        <Box sx={{ mt: 2, p: 2, backgroundColor: 'warning.light', borderRadius: 1, border: '1px solid', borderColor: 'warning.main' }}>
          <Typography variant="body2" color="warning.dark" align="center">
            ⚠️ <strong>Note:</strong> You are not currently logged in. Some features may be limited.
            <Button
              variant="text"
              size="small"
              onClick={() => navigate('/login')}
              sx={{ ml: 1 }}
            >
              Log In
            </Button>
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default UnifiedSettingsPageFallback;
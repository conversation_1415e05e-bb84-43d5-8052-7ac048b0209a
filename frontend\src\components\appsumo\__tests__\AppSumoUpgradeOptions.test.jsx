/**
 * Tests for AppSumoUpgradeOptions component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import AppSumoUpgradeOptions from '../AppSumoUpgradeOptions';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock hooks
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn(),
    showSuccessNotification: vi.fn()
  }))
}));

// Mock GlassmorphicCard
vi.mock('../../common/GlassmorphicCard', () => ({
  default: ({ children, variant, ...props }) => (
    <div data-testid={`glassmorphic-card-${variant}`} {...props}>
      {children}
    </div>
  )
}));

describe('AppSumoUpgradeOptions', () => {
  const mockUpgradeOptions = [
    {
      tier_type: 'tier2',
      name: 'Double',
      description: 'Perfect for small teams with enhanced features',
      price: 59.00,
      features: [
        'Everything in Single tier',
        '2x Content Generation Credits',
        'Advanced Analytics',
        'Priority Support'
      ]
    },
    {
      tier_type: 'tier3',
      name: 'Triple',
      description: 'Ideal for growing businesses with premium features',
      price: 118.00,
      features: [
        'Everything in Double tier',
        '3x Content Generation Credits',
        'White-label Options',
        'Custom Integrations',
        'Dedicated Account Manager'
      ]
    }
  ];

  const mockAddons = [
    {
      id: 'addon1',
      name: 'Extra Credits Pack',
      description: 'Additional content generation credits for heavy users',
      price: 29.99
    },
    {
      id: 'addon2',
      name: 'Premium Analytics',
      description: 'Advanced analytics and reporting features',
      price: 19.99
    }
  ];

  const mockProps = {
    currentTier: 'tier1',
    onUpgrade: vi.fn(),
    onRefresh: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders upgrade options component', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      if (url.includes('addons')) {
        return Promise.resolve({ data: mockAddons });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Enhance Your AppSumo Plan')).toBeInTheDocument();
    });

    expect(screen.getByText('Available Tier Upgrades')).toBeInTheDocument();
    expect(screen.getByText('Available Add-ons')).toBeInTheDocument();
  });

  test('shows loading state correctly', () => {
    const api = require('../../../api');
    api.default.get.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows error state when API fails', async () => {
    const api = await import('../../../api');
    api.default.get.mockRejectedValue(new Error('API Error'));
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load upgrade options')).toBeInTheDocument();
    });

    expect(screen.getByLabelText('Retry loading upgrade options')).toBeInTheDocument();
  });

  test('shows empty state when no options available', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: [] });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No Upgrade Options Available')).toBeInTheDocument();
    });

    expect(screen.getByText('You\'re already on our highest AppSumo tier or there are no additional upgrades available at this time.')).toBeInTheDocument();
  });

  test('displays upgrade options with correct data', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Double Tier')).toBeInTheDocument();
      expect(screen.getByText('Triple Tier')).toBeInTheDocument();
      expect(screen.getByText('$59.00')).toBeInTheDocument();
      expect(screen.getByText('$118.00')).toBeInTheDocument();
    });

    expect(screen.getByText('Perfect for small teams with enhanced features')).toBeInTheDocument();
    expect(screen.getByText('2x Content Generation Credits')).toBeInTheDocument();
  });

  test('displays addons when showAddons is true', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: [] });
      }
      if (url.includes('addons')) {
        return Promise.resolve({ data: mockAddons });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} showAddons={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Available Add-ons')).toBeInTheDocument();
      expect(screen.getByText('Extra Credits Pack')).toBeInTheDocument();
      expect(screen.getByText('Premium Analytics')).toBeInTheDocument();
    });
  });

  test('hides addons when showAddons is false', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} showAddons={false} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Available Tier Upgrades')).toBeInTheDocument();
    });

    expect(screen.queryByText('Available Add-ons')).not.toBeInTheDocument();
    // API should not be called for addons
    expect(api.default.get).toHaveBeenCalledWith('/api/appsumo/upgrade-options?current_tier=tier1');
    expect(api.default.get).not.toHaveBeenCalledWith('/api/billing/addons');
  });

  test('handles upgrade button click with onUpgrade callback', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Double Tier')).toBeInTheDocument();
    });

    const upgradeButtons = screen.getAllByText('Upgrade Now');
    await user.click(upgradeButtons[0]);

    expect(mockProps.onUpgrade).toHaveBeenCalledWith('tier2');
  });

  test('shows loading state on upgrade button during upgrade', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      return Promise.resolve({ data: [] });
    });

    // Mock onUpgrade to return a promise that doesn't resolve immediately
    const onUpgrade = vi.fn(() => new Promise(() => {}));
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} onUpgrade={onUpgrade} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Double Tier')).toBeInTheDocument();
    });

    const upgradeButtons = screen.getAllByText('Upgrade Now');
    await user.click(upgradeButtons[0]);

    // Should show loading spinner
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockUpgradeOptions });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Refresh upgrade options')).toBeInTheDocument();
    });

    const refreshButton = screen.getByLabelText('Refresh upgrade options');
    await user.click(refreshButton);

    expect(mockProps.onRefresh).toHaveBeenCalled();
    expect(api.default.get).toHaveBeenCalledTimes(3); // Initial + refresh calls
  });

  test('handles retry functionality in error state', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockRejectedValue(new Error('Network error'));
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Retry loading upgrade options')).toBeInTheDocument();
    });

    const retryButton = screen.getByLabelText('Retry loading upgrade options');
    await user.click(retryButton);

    expect(mockProps.onRefresh).toHaveBeenCalled();
  });

  test('applies correct accessibility attributes', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      // Check ARIA labels
      expect(screen.getByLabelText('Refresh upgrade options')).toBeInTheDocument();

      // Check tooltips
      expect(screen.getByTitle('Refresh Options')).toBeInTheDocument();
    });
  });

  test('handles different current tiers correctly', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} currentTier="tier2" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(api.default.get).toHaveBeenCalledWith('/api/appsumo/upgrade-options?current_tier=tier2');
    });
  });

  test('formats prices correctly', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('$59.00')).toBeInTheDocument();
      expect(screen.getByText('$118.00')).toBeInTheDocument();
      expect(screen.getAllByText('one-time')).toHaveLength(2);
    });
  });

  test('renders upgrade options without onUpgrade callback as links', async () => {
    const api = await import('../../../api');
    api.default.get.mockImplementation((url) => {
      if (url.includes('upgrade-options')) {
        return Promise.resolve({ data: mockUpgradeOptions });
      }
      return Promise.resolve({ data: [] });
    });
    
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions currentTier="tier1" />
      </TestWrapper>
    );

    await waitFor(() => {
      const upgradeButtons = screen.getAllByText('Upgrade Now');
      expect(upgradeButtons[0]).toHaveAttribute('href', '/appsumo/upgrade?tier=tier2');
    });
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <AppSumoUpgradeOptions 
          {...mockProps} 
          data-testid="test-upgrade-options"
          aria-label="Custom AppSumo Upgrade Options"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('glassmorphic-card-glass');
    expect(component).toHaveAttribute('data-testid', 'test-upgrade-options');
    expect(component).toHaveAttribute('aria-label', 'Custom AppSumo Upgrade Options');
  });
});

/**
 * CompetitorDetail Component Test Suite
 * Comprehensive testing for enterprise-grade competitor detail component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';

// Component under test
import CompetitorDetail from '../CompetitorDetail';

// Mock contexts and hooks
import { CompetitorProvider } from '../../../contexts/CompetitorContext';
import { NotificationProvider } from '../../../contexts/NotificationContext';

// Test utilities
import { createMockCompetitor } from '../../../__mocks__/competitorMocks';
import { axe, toHaveNoViolations } from 'jest-axe';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock external dependencies
vi.mock('../../../api/competitor-analytics', () => ({
  getCompetitorInsights: vi.fn(),
  getCompetitorMetrics: vi.fn(),
  exportCompetitorData: vi.fn(),
  subscribeToCompetitorAlerts: vi.fn(),
  unsubscribeFromCompetitorAlerts: vi.fn()
}));

vi.mock('../../../hooks/useAnalytics', () => ({
  useAnalytics: () => ({
    trackEvent: vi.fn(),
    trackError: vi.fn(),
    trackPerformance: vi.fn()
  })
}));

vi.mock('../../../hooks/useLocalStorage', () => ({
  useLocalStorage: (key, defaultValue) => [defaultValue, vi.fn()]
}));

vi.mock('../../../hooks/useDebounce', () => ({
  useDebounce: (fn) => fn
}));

vi.mock('../../../hooks/useFeatureAccess', () => ({
  useFeatureAccess: () => ({
    hasFeature: vi.fn().mockReturnValue(true),
    checkFeatureAccess: vi.fn().mockReturnValue(true)
  })
}));

// Mock child components
vi.mock('../SocialMediaCard', () => ({
  default: ({ platform, enhanced, showMetrics, onAnalyze }) => (
    <div data-testid={`social-media-card-${platform.platform}`}>
      Social Media Card - {platform.platform}
      {enhanced && <span data-testid="enhanced">Enhanced</span>}
      {showMetrics && <span data-testid="show-metrics">Show Metrics</span>}
      {onAnalyze && <button onClick={onAnalyze}>Analyze</button>}
    </div>
  )
}));

vi.mock('../CompetitorPostsList', () => ({
  default: ({ competitorId, enhanced, showAnalytics, userPlan }) => (
    <div data-testid="competitor-posts-list">
      Posts List - {competitorId}
      {enhanced && <span data-testid="enhanced">Enhanced</span>}
      {showAnalytics && <span data-testid="show-analytics">Show Analytics</span>}
      <span data-testid="user-plan">{userPlan}</span>
    </div>
  )
}));

vi.mock('../CompetitorAnalysisView', () => ({
  default: ({ competitor, onAnalyze, variant, enableAnalytics, userPlan }) => (
    <div data-testid="competitor-analysis-view">
      Analysis View - {competitor.name}
      <button onClick={onAnalyze}>Analyze</button>
      <span data-testid="variant">{variant}</span>
      <span data-testid="enable-analytics">{enableAnalytics.toString()}</span>
      <span data-testid="user-plan">{userPlan}</span>
    </div>
  )
}));

// Test theme
const theme = createTheme({
  palette: {
    primary: { main: '#4E40C5' },
    secondary: { main: '#EBAE1B' }
  }
});

// Test wrapper component
const TestWrapper = ({ children, competitorContextValue = {} }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      <NotificationProvider>
        <CompetitorProvider value={competitorContextValue}>
          {children}
        </CompetitorProvider>
      </NotificationProvider>
    </ThemeProvider>
  </BrowserRouter>
);

// Mock data
const mockCompetitor = createMockCompetitor({
  id: 'test-competitor-1',
  name: 'Test Competitor',
  description: 'A test competitor for unit testing',
  industry: 'Technology',
  website: 'https://example.com',
  is_active: true,
  social_media: [
    { platform: 'linkedin', url: 'https://linkedin.com/company/test' },
    { platform: 'twitter', url: 'https://twitter.com/test' }
  ],
  posts: [
    { id: 'post-1', content: 'Test post 1' },
    { id: 'post-2', content: 'Test post 2' }
  ]
});

const defaultContextValue = {
  selectedCompetitor: mockCompetitor,
  loading: false,
  error: null,
  fetchCompetitor: vi.fn(),
  deleteCompetitor: vi.fn(),
  syncCompetitor: vi.fn(),
  analyzeCompetitor: vi.fn()
};

describe('CompetitorDetail Component', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
  });

  describe('Rendering and Basic Functionality', () => {
    test('renders without crashing', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByText('Test Competitor')).toBeInTheDocument();
    });

    test('displays loading state correctly', () => {
      const loadingContextValue = {
        ...defaultContextValue,
        loading: true,
        selectedCompetitor: null
      };

      render(
        <TestWrapper competitorContextValue={loadingContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
    });

    test('displays error state correctly', () => {
      const errorContextValue = {
        ...defaultContextValue,
        error: 'Failed to load competitor',
        selectedCompetitor: null
      };

      render(
        <TestWrapper competitorContextValue={errorContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByText('Failed to load competitor')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });

    test('shows competitor not found when no competitor data', () => {
      const noCompetitorContextValue = {
        ...defaultContextValue,
        selectedCompetitor: null,
        loading: false,
        error: null
      };

      render(
        <TestWrapper competitorContextValue={noCompetitorContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByText('Competitor Not Found')).toBeInTheDocument();
      expect(screen.getByText('Back to Competitors')).toBeInTheDocument();
    });
  });

  describe('Competitor Information Display', () => {
    test('displays competitor basic information correctly', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByText('Test Competitor')).toBeInTheDocument();
      expect(screen.getByText('A test competitor for unit testing')).toBeInTheDocument();
      expect(screen.getByText('Technology')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
    });

    test('displays social media platforms', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByTestId('social-media-card-linkedin')).toBeInTheDocument();
      expect(screen.getByTestId('social-media-card-twitter')).toBeInTheDocument();
    });

    test('displays health score and quick stats', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByText('Health Score')).toBeInTheDocument();
      expect(screen.getByText('Platforms')).toBeInTheDocument();
      expect(screen.getByText('Total Posts')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    test('renders all tabs correctly', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByText('Overview')).toBeInTheDocument();
      expect(screen.getByText('Metrics')).toBeInTheDocument();
      expect(screen.getByText('Posts')).toBeInTheDocument();
      expect(screen.getByText('Analysis')).toBeInTheDocument();
    });

    test('handles tab changes correctly', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      const postsTab = screen.getByText('Posts');
      await user.click(postsTab);

      expect(screen.getByTestId('competitor-posts-list')).toBeInTheDocument();
    });

    test('shows insights tab when feature is available', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" userPlan="dominator" />
        </TestWrapper>
      );

      expect(screen.getByText('Insights')).toBeInTheDocument();
    });
  });

  describe('Action Buttons', () => {
    test('sync button triggers sync action', async () => {
      const mockSyncCompetitor = vi.fn().mockResolvedValue();
      const contextValue = {
        ...defaultContextValue,
        syncCompetitor: mockSyncCompetitor
      };

      render(
        <TestWrapper competitorContextValue={contextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      const syncButton = screen.getByText('Sync');
      await user.click(syncButton);

      // Should open confirmation dialog
      expect(screen.getByText('Sync Competitor Data')).toBeInTheDocument();
      
      const confirmButton = screen.getByRole('button', { name: /sync/i });
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockSyncCompetitor).toHaveBeenCalledWith('test-competitor-1');
      });
    });

    test('analyze button triggers analyze action', async () => {
      const mockAnalyzeCompetitor = vi.fn().mockResolvedValue();
      const contextValue = {
        ...defaultContextValue,
        analyzeCompetitor: mockAnalyzeCompetitor
      };

      render(
        <TestWrapper competitorContextValue={contextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      const analyzeButton = screen.getByText('Analyze');
      await user.click(analyzeButton);

      // Should open confirmation dialog
      expect(screen.getByText('Analyze Competitor')).toBeInTheDocument();
    });

    test('export button is available when enabled', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" enableExport={true} />
        </TestWrapper>
      );

      expect(screen.getByLabelText('Export competitor data')).toBeInTheDocument();
    });

    test('delete button triggers delete confirmation', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      const moreButton = screen.getByLabelText('More actions');
      await user.click(moreButton);

      const deleteButton = screen.getByText('Delete Competitor');
      await user.click(deleteButton);

      expect(screen.getByText('Delete Competitor')).toBeInTheDocument();
      expect(screen.getByText(/Are you sure you want to delete/)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has no accessibility violations', async () => {
      const { container } = render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('supports keyboard navigation', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      // Test tab navigation
      await user.tab();
      expect(document.activeElement).toHaveAttribute('aria-label', 'Sync competitor data');

      await user.tab();
      expect(document.activeElement).toHaveAttribute('aria-label', 'Analyze competitor');
    });

    test('has proper ARIA labels and roles', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Competitor detail view');
      expect(screen.getByRole('region')).toHaveAttribute('aria-labelledby', 'competitor-header');
    });
  });

  describe('Plan Integration', () => {
    test('respects plan features for export', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" userPlan="creator" enableExport={true} />
        </TestWrapper>
      );

      const exportButton = screen.getByLabelText('Export competitor data');
      expect(exportButton).toBeInTheDocument();
    });

    test('shows feature gates for premium features', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" userPlan="creator" />
        </TestWrapper>
      );

      // Should show feature gate for analyze
      const analyzeButton = screen.getByLabelText('Analyze competitor');
      expect(analyzeButton).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('handles API errors gracefully', async () => {
      const errorContextValue = {
        ...defaultContextValue,
        syncCompetitor: vi.fn().mockRejectedValue(new Error('API Error'))
      };

      render(
        <TestWrapper competitorContextValue={errorContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      const syncButton = screen.getByText('Sync');
      await user.click(syncButton);

      const confirmButton = screen.getByRole('button', { name: /sync/i });
      await user.click(confirmButton);

      // Should handle error gracefully
      await waitFor(() => {
        expect(screen.queryByText('Sync Competitor Data')).not.toBeInTheDocument();
      });
    });
  });

  describe('Performance and Optimization', () => {
    test('memoizes expensive calculations', () => {
      const { rerender } = render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      // Re-render with same props should not cause unnecessary recalculations
      rerender(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorDetail competitorId="test-competitor-1" />
        </TestWrapper>
      );

      // Component should render without issues
      expect(screen.getByText('Test Competitor')).toBeInTheDocument();
    });
  });
});

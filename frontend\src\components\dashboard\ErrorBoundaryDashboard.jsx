/**
 * Enhanced ErrorBoundaryDashboard Component - Enterprise-grade error handling
 * Features: Plan-based error analytics limitations, real-time error tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error recovery mechanisms, and ACE Social platform integration
 * with advanced error analytics, user experience monitoring, and predictive error prevention
 @since 2024-1-1 to 2025-25-7
*/

import { Component } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Alert,
  Card,
  CardContent,

  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  alpha,
  Stack,
  CircularProgress,
  Paper,
  Grid,
  Snackbar
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  BugReport as BugReportIcon,
  ContentCopy as CopyIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  Upgrade as UpgradeIcon,
  Feedback as FeedbackIcon,
  Support as SupportIcon,
  Home as HomeIcon
} from '@mui/icons-material';



/**
 * Enhanced error boundary configurations with plan-based features - Production Ready
 */
const ERROR_TYPES = {
  COMPONENT_ERROR: 'component_error',
  NETWORK_ERROR: 'network_error',
  PERMISSION_ERROR: 'permission_error',
  SUBSCRIPTION_ERROR: 'subscription_error',
  CRITICAL_SYSTEM_ERROR: 'critical_system_error',
  PERFORMANCE_ERROR: 'performance_error',
  SECURITY_ERROR: 'security_error',
  STORAGE_ERROR: 'storage_error'
};

const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * Enhanced ErrorBoundaryDashboard Class Component with Enterprise Features
 */
class ErrorBoundaryDashboard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      correlationId: null,
      retryCount: 0,
      showDetails: false,
      errorType: ERROR_TYPES.COMPONENT_ERROR,
      errorSeverity: ERROR_SEVERITY.MEDIUM,
      showUpgradeDialog: false,
      showFeedbackDialog: false,
      feedbackSubmitted: false,
      errorAnalytics: {
        errorCount: 0,
        lastErrorTime: null,
        errorPattern: [],
        userImpact: 'low',
        recoverySuccess: false
      },
      userFeedback: {
        rating: null,
        description: '',
        contactInfo: ''
      },
      systemInfo: {
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        memory: performance.memory ? {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        } : null
      },
      recoveryAttempts: [],
      isRecovering: false,
      showSuccessMessage: false
    };

    // Bind methods
    this.handleRetry = this.handleRetry.bind(this);
    this.handleCopyError = this.handleCopyError.bind(this);
    this.toggleDetails = this.toggleDetails.bind(this);
    this.handleUpgrade = this.handleUpgrade.bind(this);
    this.handleFeedback = this.handleFeedback.bind(this);
    this.categorizeError = this.categorizeError.bind(this);
    this.assessSeverity = this.assessSeverity.bind(this);
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      correlationId: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      errorAnalytics: {
        errorCount: 1,
        lastErrorTime: new Date().toISOString(),
        errorPattern: [error.message],
        userImpact: 'medium',
        recoverySuccess: false
      }
    };
  }

  componentDidCatch(error, errorInfo) {
    // Enhanced error logging and categorization
    console.error('Enhanced Dashboard Error Boundary caught an error:', error, errorInfo);

    const errorType = this.categorizeError(error);
    const errorSeverity = this.assessSeverity(error, errorInfo);

    this.setState(prevState => ({
      error,
      errorInfo,
      errorType,
      errorSeverity,
      errorAnalytics: {
        ...prevState.errorAnalytics,
        errorCount: prevState.errorAnalytics.errorCount + 1,
        lastErrorTime: new Date().toISOString(),
        errorPattern: [...prevState.errorAnalytics.errorPattern, error.message].slice(-10), // Keep last 10 errors
        userImpact: this.calculateUserImpact(error, errorInfo)
      }
    }));

    // Enhanced error reporting
    this.reportError(error, errorInfo, errorType, errorSeverity);
  }

  /**
   * Enhanced error categorization - Production Ready
   */
  categorizeError(error) {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('network') || message.includes('fetch') || message.includes('xhr')) {
      return ERROR_TYPES.NETWORK_ERROR;
    }
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return ERROR_TYPES.PERMISSION_ERROR;
    }
    if (message.includes('subscription') || message.includes('plan') || message.includes('limit')) {
      return ERROR_TYPES.SUBSCRIPTION_ERROR;
    }
    if (message.includes('security') || message.includes('csrf') || message.includes('xss')) {
      return ERROR_TYPES.SECURITY_ERROR;
    }
    if (message.includes('storage') || message.includes('quota') || message.includes('disk')) {
      return ERROR_TYPES.STORAGE_ERROR;
    }
    if (message.includes('performance') || message.includes('timeout') || message.includes('slow')) {
      return ERROR_TYPES.PERFORMANCE_ERROR;
    }
    if (stack.includes('critical') || message.includes('fatal') || message.includes('system')) {
      return ERROR_TYPES.CRITICAL_SYSTEM_ERROR;
    }

    return ERROR_TYPES.COMPONENT_ERROR;
  }

  /**
   * Enhanced error severity assessment - Production Ready
   */
  assessSeverity(error, errorInfo) {
    const message = error.message.toLowerCase();
    const componentStack = errorInfo?.componentStack?.toLowerCase() || '';

    // Critical severity indicators
    if (message.includes('fatal') || message.includes('critical') ||
        message.includes('system') || componentStack.includes('app')) {
      return ERROR_SEVERITY.CRITICAL;
    }

    // High severity indicators
    if (message.includes('security') || message.includes('unauthorized') ||
        message.includes('payment') || message.includes('billing')) {
      return ERROR_SEVERITY.HIGH;
    }

    // Medium severity indicators
    if (message.includes('network') || message.includes('timeout') ||
        message.includes('performance') || componentStack.includes('dashboard')) {
      return ERROR_SEVERITY.MEDIUM;
    }

    // Default to low severity
    return ERROR_SEVERITY.LOW;
  }

  /**
   * Calculate user impact based on error context - Production Ready
   */
  calculateUserImpact(error, errorInfo) {
    const componentStack = errorInfo?.componentStack?.toLowerCase() || '';

    if (componentStack.includes('app') || componentStack.includes('main')) {
      return 'critical';
    }
    if (componentStack.includes('dashboard') || componentStack.includes('content')) {
      return 'high';
    }
    if (componentStack.includes('widget') || componentStack.includes('card')) {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Enhanced error reporting with analytics - Production Ready
   */
  reportError = (error, errorInfo, errorType, errorSeverity) => {
    const { correlationId, systemInfo, errorAnalytics } = this.state;
    const { onError, enableAnalytics = true } = this.props;

    const enhancedErrorReport = {
      correlationId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo?.componentStack,
      errorType,
      errorSeverity,
      timestamp: new Date().toISOString(),
      systemInfo,
      errorAnalytics,
      userContext: {
        retryCount: this.state.retryCount,
        sessionDuration: Date.now() - (window.sessionStartTime || Date.now()),
        previousErrors: this.state.errorAnalytics.errorPattern
      },
      environmentInfo: {
        nodeEnv: process.env.NODE_ENV,
        buildVersion: process.env.REACT_APP_VERSION || 'unknown',
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onlineStatus: navigator.onLine
      }
    };

    // Send to parent component
    if (onError) {
      onError(enhancedErrorReport);
    }

    // Enhanced logging for development
    console.group('🚨 Enhanced Error Boundary Report');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Error Type:', errorType);
    console.error('Error Severity:', errorSeverity);
    console.error('Full Report:', enhancedErrorReport);
    console.groupEnd();

    // Analytics tracking
    if (enableAnalytics && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: errorSeverity === ERROR_SEVERITY.CRITICAL,
        error_type: errorType,
        correlation_id: correlationId
      });
    }

    // In production, send to error tracking service
    if (process.env.NODE_ENV === 'production') {
      // Enhanced error tracking with context
      try {
        // Example: Send to Sentry with enhanced context
        if (window.Sentry) {
          window.Sentry.withScope((scope) => {
            scope.setTag('errorType', errorType);
            scope.setTag('errorSeverity', errorSeverity);
            scope.setTag('correlationId', correlationId);
            scope.setContext('errorAnalytics', errorAnalytics);
            scope.setContext('systemInfo', systemInfo);
            window.Sentry.captureException(error);
          });
        }

        // Example: Send to custom error tracking API
        fetch('/api/errors', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(enhancedErrorReport)
        }).catch(reportingError => {
          console.error('Failed to report error:', reportingError);
        });
      } catch (reportingError) {
        console.error('Error reporting failed:', reportingError);
      }
    }
  };

  /**
   * Enhanced retry mechanism with recovery tracking - Production Ready
   */
  handleRetry = async () => {
    const { onRetry, enableAnalytics = true } = this.props;
    const retryAttempt = {
      timestamp: new Date().toISOString(),
      retryCount: this.state.retryCount + 1,
      errorType: this.state.errorType,
      recoveryMethod: 'manual_retry'
    };

    this.setState(prevState => ({
      isRecovering: true,
      recoveryAttempts: [...prevState.recoveryAttempts, retryAttempt]
    }));

    try {
      // Simulate recovery delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Call parent retry function if provided
      if (onRetry) {
        await onRetry();
      }

      // Reset error state
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        correlationId: null,
        retryCount: prevState.retryCount + 1,
        showDetails: false,
        isRecovering: false,
        showSuccessMessage: true,
        errorAnalytics: {
          ...prevState.errorAnalytics,
          recoverySuccess: true
        }
      }));

      // Track successful recovery
      if (enableAnalytics && window.gtag) {
        window.gtag('event', 'error_recovery_success', {
          error_type: this.state.errorType,
          retry_count: this.state.retryCount + 1,
          correlation_id: this.state.correlationId
        });
      }

      // Hide success message after delay
      setTimeout(() => {
        this.setState({ showSuccessMessage: false });
      }, 3000);

    } catch (recoveryError) {
      console.error('Recovery failed:', recoveryError);

      this.setState(prevState => ({
        isRecovering: false,
        errorAnalytics: {
          ...prevState.errorAnalytics,
          recoverySuccess: false
        }
      }));

      // Track failed recovery
      if (enableAnalytics && window.gtag) {
        window.gtag('event', 'error_recovery_failed', {
          error_type: this.state.errorType,
          retry_count: this.state.retryCount + 1,
          correlation_id: this.state.correlationId
        });
      }
    }
  };

  /**
   * Enhanced error copying with comprehensive details - Production Ready
   */
  handleCopyError = () => {
    const { error, errorInfo, correlationId, errorType, errorSeverity, systemInfo, errorAnalytics } = this.state;
    const enhancedErrorText = `
=== ACE Social Dashboard Error Report ===
Error ID: ${correlationId}
Type: ${errorType}
Severity: ${errorSeverity}
Message: ${error?.message || 'Unknown error'}
Timestamp: ${new Date().toISOString()}
URL: ${window.location.href}

=== System Information ===
User Agent: ${systemInfo.userAgent}
Viewport: ${systemInfo.viewport.width}x${systemInfo.viewport.height}
Memory Usage: ${systemInfo.memory ? `${Math.round(systemInfo.memory.used / 1024 / 1024)}MB` : 'N/A'}
Online Status: ${navigator.onLine ? 'Online' : 'Offline'}

=== Error Analytics ===
Error Count: ${errorAnalytics.errorCount}
User Impact: ${errorAnalytics.userImpact}
Recovery Success: ${errorAnalytics.recoverySuccess ? 'Yes' : 'No'}

=== Technical Details ===
Stack Trace:
${error?.stack || 'No stack trace available'}

Component Stack:
${errorInfo?.componentStack || 'No component stack available'}

=== Support Information ===
For support, please include this Error ID: ${correlationId}
Contact: <EMAIL>
    `.trim();

    navigator.clipboard.writeText(enhancedErrorText).then(() => {
      console.log('Enhanced error details copied to clipboard');
      // Show success feedback
      this.setState({ showSuccessMessage: true });
      setTimeout(() => {
        this.setState({ showSuccessMessage: false });
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy error details:', err);
    });
  };

  /**
   * Enhanced details toggle with analytics - Production Ready
   */
  toggleDetails = () => {
    const { enableAnalytics = true } = this.props;

    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));

    // Track details interaction
    if (enableAnalytics && window.gtag) {
      window.gtag('event', 'error_details_toggle', {
        error_type: this.state.errorType,
        correlation_id: this.state.correlationId,
        action: this.state.showDetails ? 'close' : 'open'
      });
    }
  };

  /**
   * Handle plan upgrade for enhanced error features - Production Ready
   */
  handleUpgrade = () => {
    const { onUpgrade } = this.props;

    this.setState({ showUpgradeDialog: true });

    if (onUpgrade) {
      onUpgrade();
    }

    // Track upgrade interaction
    if (window.gtag) {
      window.gtag('event', 'error_boundary_upgrade_click', {
        error_type: this.state.errorType,
        correlation_id: this.state.correlationId
      });
    }
  };

  /**
   * Handle user feedback collection - Production Ready
   */
  handleFeedback = (feedbackData) => {
    const { onFeedback } = this.props;

    this.setState({
      userFeedback: feedbackData,
      feedbackSubmitted: true,
      showFeedbackDialog: false
    });

    if (onFeedback) {
      onFeedback({
        ...feedbackData,
        correlationId: this.state.correlationId,
        errorType: this.state.errorType,
        errorSeverity: this.state.errorSeverity
      });
    }

    // Track feedback submission
    if (window.gtag) {
      window.gtag('event', 'error_feedback_submitted', {
        error_type: this.state.errorType,
        correlation_id: this.state.correlationId,
        rating: feedbackData.rating
      });
    }
  };

  /**
   * Enhanced render method with enterprise-grade error UI - Production Ready
   */
  render() {
    const {
      hasError,
      error,
      errorInfo,
      correlationId,
      retryCount,
      showDetails,
      errorType,
      errorSeverity,
      feedbackSubmitted,
      isRecovering,
      showSuccessMessage,
      errorAnalytics
    } = this.state;

    const {
      children,
      fallback,
      showRetryButton = true,
      showErrorDetails = true,
      enableUpgrade = true,
      enableFeedback = true,
      enableAnalytics = true,
      minHeight = 400,
      maxWidth = 800
    } = this.props;

    // ACE Social brand colors
    const aceColors = {
      primary: '#4E40C5',
      dark: '#15110E',
      yellow: '#EBAE1B',
      white: '#FFFFFF'
    };

    if (hasError) {
      // Custom fallback UI if provided
      if (fallback) {
        return fallback(error, this.handleRetry, correlationId);
      }

      // Enhanced error severity styling
      const getSeverityColor = () => {
        switch (errorSeverity) {
          case ERROR_SEVERITY.CRITICAL: return '#dc2626';
          case ERROR_SEVERITY.HIGH: return '#ea580c';
          case ERROR_SEVERITY.MEDIUM: return '#d97706';
          case ERROR_SEVERITY.LOW: return '#65a30d';
          default: return '#6b7280';
        }
      };

      const getSeverityIcon = () => {
        switch (errorSeverity) {
          case ERROR_SEVERITY.CRITICAL: return <ErrorIcon sx={{ fontSize: 48, color: getSeverityColor() }} />;
          case ERROR_SEVERITY.HIGH: return <WarningIcon sx={{ fontSize: 48, color: getSeverityColor() }} />;
          case ERROR_SEVERITY.MEDIUM: return <InfoIcon sx={{ fontSize: 48, color: getSeverityColor() }} />;
          case ERROR_SEVERITY.LOW: return <InfoIcon sx={{ fontSize: 48, color: getSeverityColor() }} />;
          default: return <ErrorIcon sx={{ fontSize: 48, color: getSeverityColor() }} />;
        }
      };

      // Enhanced error UI with ACE Social branding
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight,
            p: 3,
            textAlign: 'center',
            background: `linear-gradient(135deg,
              ${alpha('#fff5f5', 0.8)} 0%,
              ${alpha('#fef2f2', 0.6)} 100%)`,
            backdropFilter: 'blur(10px)'
          }}
          role="alert"
          aria-live="assertive"
          aria-label="Error occurred in dashboard"
        >
          {/* Success Message Snackbar */}
          {showSuccessMessage && (
            <Snackbar
              open={showSuccessMessage}
              autoHideDuration={3000}
              anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
              <Alert severity="success" sx={{ width: '100%' }}>
                Action completed successfully!
              </Alert>
            </Snackbar>
          )}

          <Card
            sx={{
              maxWidth,
              width: '100%',
              background: `linear-gradient(135deg,
                ${alpha(aceColors.white, 0.95)} 0%,
                ${alpha(aceColors.white, 0.85)} 100%)`,
              backdropFilter: 'blur(20px)',
              border: `2px solid ${getSeverityColor()}`,
              borderRadius: 3,
              boxShadow: `0 8px 32px ${alpha(getSeverityColor(), 0.2)}`,
              overflow: 'hidden'
            }}
          >
            <CardContent sx={{ p: { xs: 3, sm: 4, md: 5 } }}>
              {/* Error Header */}
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 3 }}>
                {getSeverityIcon()}
                <Box sx={{ ml: 2, textAlign: 'left' }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, color: aceColors.dark, mb: 0.5 }}>
                    {errorSeverity === ERROR_SEVERITY.CRITICAL ? 'Critical Error' :
                     errorSeverity === ERROR_SEVERITY.HIGH ? 'System Error' :
                     errorSeverity === ERROR_SEVERITY.MEDIUM ? 'Dashboard Error' : 'Minor Issue'}
                  </Typography>
                  <Chip
                    label={errorType.replace('_', ' ').toUpperCase()}
                    size="small"
                    sx={{
                      bgcolor: getSeverityColor(),
                      color: 'white',
                      fontWeight: 600
                    }}
                  />
                </Box>
              </Box>

              {/* Error Message */}
              <Typography variant="h6" color="text.primary" sx={{ mb: 3, lineHeight: 1.6 }}>
                We&apos;re sorry, but something went wrong while loading the dashboard.
                Our team has been notified and is working to fix this issue.
              </Typography>

              {/* Error Analytics Display */}
              {enableAnalytics && (
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: alpha(aceColors.primary, 0.05) }}>
                      <Typography variant="caption" color="text.secondary">Error Count</Typography>
                      <Typography variant="h6" sx={{ color: aceColors.primary, fontWeight: 600 }}>
                        {errorAnalytics.errorCount}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: alpha(aceColors.yellow, 0.05) }}>
                      <Typography variant="caption" color="text.secondary">User Impact</Typography>
                      <Typography variant="h6" sx={{ color: aceColors.dark, fontWeight: 600 }}>
                        {errorAnalytics.userImpact}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: alpha(getSeverityColor(), 0.05) }}>
                      <Typography variant="caption" color="text.secondary">Severity</Typography>
                      <Typography variant="h6" sx={{ color: getSeverityColor(), fontWeight: 600 }}>
                        {errorSeverity}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Paper sx={{ p: 2, textAlign: 'center', bgcolor: alpha(aceColors.primary, 0.05) }}>
                      <Typography variant="caption" color="text.secondary">Retry Count</Typography>
                      <Typography variant="h6" sx={{ color: aceColors.primary, fontWeight: 600 }}>
                        {retryCount}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              )}

              {/* Error ID and Status Chips */}
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', mb: 3, flexWrap: 'wrap' }}>
                <Chip
                  label={`Error ID: ${correlationId}`}
                  size="medium"
                  variant="outlined"
                  sx={{
                    borderColor: getSeverityColor(),
                    color: getSeverityColor(),
                    fontWeight: 600
                  }}
                  icon={<BugReportIcon />}
                />
                {retryCount > 0 && (
                  <Chip
                    label={`Retry attempts: ${retryCount}`}
                    size="medium"
                    variant="outlined"
                    color="warning"
                    icon={<RefreshIcon />}
                  />
                )}
                {feedbackSubmitted && (
                  <Chip
                    label="Feedback submitted"
                    size="medium"
                    variant="filled"
                    color="success"
                    icon={<SuccessIcon />}
                  />
                )}
              </Box>

              {/* Enhanced Action Buttons */}
              <Stack spacing={2} direction={{ xs: 'column', sm: 'row' }} sx={{ justifyContent: 'center', mb: 3 }}>
                {showRetryButton && (
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={isRecovering ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
                    onClick={this.handleRetry}
                    disabled={isRecovering}
                    sx={{
                      bgcolor: aceColors.primary,
                      '&:hover': { bgcolor: '#3d2f9f' },
                      fontWeight: 600,
                      px: 4,
                      py: 1.5,
                      minWidth: 160
                    }}
                  >
                    {isRecovering ? 'Recovering...' : 'Try Again'}
                  </Button>
                )}

                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<HomeIcon />}
                  onClick={() => window.location.href = '/dashboard'}
                  sx={{
                    borderColor: aceColors.primary,
                    color: aceColors.primary,
                    '&:hover': {
                      borderColor: '#3d2f9f',
                      bgcolor: alpha(aceColors.primary, 0.05)
                    },
                    fontWeight: 600,
                    px: 4,
                    py: 1.5,
                    minWidth: 160
                  }}
                >
                  Go to Dashboard
                </Button>

                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<RefreshIcon />}
                  onClick={() => window.location.reload()}
                  sx={{
                    borderColor: aceColors.yellow,
                    color: aceColors.dark,
                    '&:hover': {
                      borderColor: '#d97706',
                      bgcolor: alpha(aceColors.yellow, 0.05)
                    },
                    fontWeight: 600,
                    px: 4,
                    py: 1.5,
                    minWidth: 160
                  }}
                >
                  Reload Page
                </Button>
              </Stack>

              {/* Secondary Actions */}
              <Stack spacing={1} direction={{ xs: 'column', sm: 'row' }} sx={{ justifyContent: 'center', mb: 3 }}>
                <Button
                  variant="text"
                  size="medium"
                  startIcon={<CopyIcon />}
                  onClick={this.handleCopyError}
                  sx={{ color: aceColors.primary }}
                >
                  Copy Error Details
                </Button>

                {enableFeedback && !feedbackSubmitted && (
                  <Button
                    variant="text"
                    size="medium"
                    startIcon={<FeedbackIcon />}
                    onClick={() => this.setState({ showFeedbackDialog: true })}
                    sx={{ color: aceColors.primary }}
                  >
                    Send Feedback
                  </Button>
                )}

                {enableUpgrade && errorSeverity !== ERROR_SEVERITY.LOW && (
                  <Button
                    variant="text"
                    size="medium"
                    startIcon={<UpgradeIcon />}
                    onClick={this.handleUpgrade}
                    sx={{ color: aceColors.yellow }}
                  >
                    Upgrade for Better Support
                  </Button>
                )}
              </Stack>

              {/* Enhanced Error Details Accordion */}
              {showErrorDetails && error && (
                <Accordion
                  expanded={showDetails}
                  onChange={this.toggleDetails}
                  sx={{
                    mt: 2,
                    border: `1px solid ${alpha(getSeverityColor(), 0.2)}`,
                    borderRadius: 2,
                    '&:before': { display: 'none' }
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="error-details-content"
                    id="error-details-header"
                    sx={{
                      bgcolor: alpha(getSeverityColor(), 0.05),
                      '&:hover': { bgcolor: alpha(getSeverityColor(), 0.1) }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BugReportIcon fontSize="small" sx={{ color: getSeverityColor() }} />
                      <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                        Technical Details & Diagnostics
                      </Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 3 }}>
                    <Box sx={{ textAlign: 'left' }}>
                      {/* Error Message */}
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1, color: getSeverityColor(), fontWeight: 600 }}>
                          Error Message:
                        </Typography>
                        <Alert severity="error" sx={{ mb: 2 }}>
                          <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                            {error.message}
                          </Typography>
                        </Alert>
                      </Box>

                      {/* System Information */}
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1, color: aceColors.primary, fontWeight: 600 }}>
                          System Information:
                        </Typography>
                        <Paper sx={{ p: 2, bgcolor: alpha(aceColors.primary, 0.02) }}>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="caption" color="text.secondary">Browser:</Typography>
                              <Typography variant="body2">{navigator.userAgent.split(' ')[0]}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="caption" color="text.secondary">Platform:</Typography>
                              <Typography variant="body2">{navigator.platform}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="caption" color="text.secondary">Language:</Typography>
                              <Typography variant="body2">{navigator.language}</Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="caption" color="text.secondary">Online Status:</Typography>
                              <Typography variant="body2">{navigator.onLine ? 'Online' : 'Offline'}</Typography>
                            </Grid>
                          </Grid>
                        </Paper>
                      </Box>

                      {/* Stack Trace */}
                      {error.stack && (
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1, color: getSeverityColor(), fontWeight: 600 }}>
                            Stack Trace:
                          </Typography>
                          <Paper
                            sx={{
                              bgcolor: alpha(aceColors.dark, 0.02),
                              p: 2,
                              borderRadius: 2,
                              overflow: 'auto',
                              maxHeight: 300,
                              border: `1px solid ${alpha(aceColors.dark, 0.1)}`
                            }}
                          >
                            <Typography
                              component="pre"
                              sx={{
                                fontSize: '0.75rem',
                                fontFamily: 'monospace',
                                whiteSpace: 'pre-wrap',
                                color: aceColors.dark,
                                lineHeight: 1.4
                              }}
                            >
                              {error.stack}
                            </Typography>
                          </Paper>
                        </Box>
                      )}

                      {/* Component Stack */}
                      {errorInfo?.componentStack && (
                        <Box sx={{ mb: 3 }}>
                          <Typography variant="subtitle2" sx={{ mb: 1, color: getSeverityColor(), fontWeight: 600 }}>
                            Component Stack:
                          </Typography>
                          <Paper
                            sx={{
                              bgcolor: alpha(aceColors.dark, 0.02),
                              p: 2,
                              borderRadius: 2,
                              overflow: 'auto',
                              maxHeight: 300,
                              border: `1px solid ${alpha(aceColors.dark, 0.1)}`
                            }}
                          >
                            <Typography
                              component="pre"
                              sx={{
                                fontSize: '0.75rem',
                                fontFamily: 'monospace',
                                whiteSpace: 'pre-wrap',
                                color: aceColors.dark,
                                lineHeight: 1.4
                              }}
                            >
                              {errorInfo.componentStack}
                            </Typography>
                          </Paper>
                        </Box>
                      )}

                      {/* Recovery Suggestions */}
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1, color: aceColors.primary, fontWeight: 600 }}>
                          Recovery Suggestions:
                        </Typography>
                        <Alert severity="info" sx={{ bgcolor: alpha(aceColors.primary, 0.05) }}>
                          <Typography variant="body2">
                            • Try refreshing the page or clearing your browser cache<br/>
                            • Check your internet connection and try again<br/>
                            • If the problem persists, contact support with Error ID: {correlationId}<br/>
                            • Consider upgrading your plan for priority support and enhanced error recovery
                          </Typography>
                        </Alert>
                      </Box>
                    </Box>
                  </AccordionDetails>
                </Accordion>
              )}

              {/* Support Information */}
              <Box sx={{ mt: 3, p: 2, bgcolor: alpha(aceColors.primary, 0.02), borderRadius: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mb: 1 }}>
                  <SupportIcon sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                  Need immediate assistance? Contact our support team
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ textAlign: 'center', display: 'block' }}>
                  Email: <EMAIL> | Error ID: {correlationId}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      );
    }

    return children;
  }
}

/**
 * Comprehensive PropTypes validation - Production Ready
 */
ErrorBoundaryDashboard.propTypes = {
  /** Child components to render */
  children: PropTypes.node.isRequired,

  /** Custom fallback UI function */
  fallback: PropTypes.func,

  /** Visual variant of the error boundary */
  variant: PropTypes.oneOf(['default', 'compact', 'detailed']),

  /** Minimum height of the error boundary */
  minHeight: PropTypes.number,

  /** Maximum width of the error boundary */
  maxWidth: PropTypes.number,

  /** Enable analytics functionality */
  enableAnalytics: PropTypes.bool,

  /** Enable upgrade functionality */
  enableUpgrade: PropTypes.bool,

  /** Enable feedback functionality */
  enableFeedback: PropTypes.bool,

  /** Whether to show retry button */
  showRetryButton: PropTypes.bool,

  /** Whether to show error details */
  showErrorDetails: PropTypes.bool,

  /** Callback when error occurs */
  onError: PropTypes.func,

  /** Callback when retry is attempted */
  onRetry: PropTypes.func,

  /** Callback when upgrade is requested */
  onUpgrade: PropTypes.func,

  /** Callback when feedback is submitted */
  onFeedback: PropTypes.func,

  /** Additional CSS class name */
  className: PropTypes.string,

  /** Test ID for testing */
  'data-testid': PropTypes.string
};

/**
 * Default props with enterprise-grade defaults - Production Ready
 */
ErrorBoundaryDashboard.defaultProps = {
  fallback: null,
  variant: 'default',
  minHeight: 400,
  maxWidth: 800,
  enableAnalytics: true,
  enableUpgrade: true,
  enableFeedback: true,
  showRetryButton: true,
  showErrorDetails: true,
  onError: null,
  onRetry: null,
  onUpgrade: null,
  onFeedback: null,
  className: '',
  'data-testid': 'error-boundary-dashboard'
};

/**
 * Display name for debugging - Production Ready
 */
ErrorBoundaryDashboard.displayName = 'ErrorBoundaryDashboard';

export default ErrorBoundaryDashboard;

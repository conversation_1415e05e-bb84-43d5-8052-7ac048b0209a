import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import UnifiedCompetitorsPage from '../UnifiedCompetitorsPage';
import { NotificationProvider } from '../../../contexts/NotificationContext';

// Mock the child components
jest.mock('../CompetitorsListPage', () => {
  return function MockCompetitorsListPage({ isEmbedded, ...props }) {
    return (
      <div data-testid="competitors-list-page">
        <p>Competitors List Page</p>
        <p>Embedded: {isEmbedded ? 'true' : 'false'}</p>
      </div>
    );
  };
});

jest.mock('../CompetitorComparisonPage', () => {
  return function MockCompetitorComparisonPage({ isEmbedded, ...props }) {
    return (
      <div data-testid="competitor-comparison-page">
        <p>Competitor Comparison Page</p>
        <p>Embedded: {isEmbedded ? 'true' : 'false'}</p>
      </div>
    );
  };
});

// Mock useNotification hook
jest.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showErrorNotification: jest.fn(),
    showSuccessNotification: jest.fn()
  })
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
const mockLocation = {
  pathname: '/competitors',
  search: '?view=list'
};

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation
}));

// Test wrapper component
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <NotificationProvider>
          {children}
        </NotificationProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('UnifiedCompetitorsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders with default props', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByLabelText(/unified competitors dashboard/i)).toBeInTheDocument();
    });

    test('renders breadcrumb navigation when not embedded', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage isEmbedded={false} />
        </TestWrapper>
      );

      expect(screen.getByLabelText(/breadcrumb navigation/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/navigate to dashboard/i)).toBeInTheDocument();
    });

    test('does not render breadcrumb navigation when embedded', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage isEmbedded={true} />
        </TestWrapper>
      );

      expect(screen.queryByLabelText(/breadcrumb navigation/i)).not.toBeInTheDocument();
    });

    test('renders page header when showHeader is true', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage showHeader={true} />
        </TestWrapper>
      );

      expect(screen.getByText('Competitor Analysis')).toBeInTheDocument();
      expect(screen.getByLabelText(/add new competitor/i)).toBeInTheDocument();
    });

    test('does not render page header when showHeader is false', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage showHeader={false} />
        </TestWrapper>
      );

      expect(screen.queryByText('Competitor Analysis')).not.toBeInTheDocument();
    });

    test('shows compact mode alert when compactMode is true', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage compactMode={true} />
        </TestWrapper>
      );

      expect(screen.getByText(/viewing competitors in compact mode/i)).toBeInTheDocument();
    });

    test('shows user welcome chip when embedded with user', () => {
      const user = { name: 'John Doe' };
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage isEmbedded={true} user={user} />
        </TestWrapper>
      );

      expect(screen.getByText('Welcome, John Doe')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    test('renders tab navigation with correct tabs', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      expect(screen.getByLabelText(/competitor navigation tabs/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/switch to competitor list tab/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/switch to compare competitors tab/i)).toBeInTheDocument();
    });

    test('switches to compare tab when clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
      await user.click(compareTab);

      expect(mockNavigate).toHaveBeenCalledWith(
        expect.objectContaining({
          search: expect.stringContaining('view=compare')
        }),
        { replace: true }
      );
    });

    test('displays correct content based on active tab', async () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      // Should show list page by default
      expect(screen.getByTestId('competitors-list-page')).toBeInTheDocument();
      expect(screen.queryByTestId('competitor-comparison-page')).not.toBeInTheDocument();
    });

    test('shows tab descriptions for screen readers', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      expect(screen.getByText('View and manage all competitors')).toBeInTheDocument();
      expect(screen.getByText('Compare competitor performance')).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    test('navigates to dashboard when home breadcrumb is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage isEmbedded={false} />
        </TestWrapper>
      );

      const homeLink = screen.getByLabelText(/navigate to dashboard/i);
      await user.click(homeLink);

      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });

    test('navigates to add competitor when add button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      const addButton = screen.getByLabelText(/add new competitor/i);
      await user.click(addButton);

      expect(mockNavigate).toHaveBeenCalledWith('/settings?tab=competitors&action=new');
    });

    test('disables add button when not authenticated', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage isAuthenticated={false} />
        </TestWrapper>
      );

      const addButton = screen.getByLabelText(/add new competitor/i);
      expect(addButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    test('displays error fallback when error occurs', () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      // Verify error boundary structure exists
      expect(screen.getByRole('main')).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels and roles', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByLabelText(/unified competitors dashboard/i)).toBeInTheDocument();
      expect(screen.getByRole('tabpanel')).toBeInTheDocument();
    });

    test('supports keyboard navigation for tabs', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      const tabList = screen.getByLabelText(/competitor navigation tabs/i);
      expect(tabList).toBeInTheDocument();

      // Test tab navigation
      const listTab = screen.getByLabelText(/switch to competitor list tab/i);
      listTab.focus();
      
      await user.keyboard('{ArrowRight}');
      expect(document.activeElement).toHaveAttribute('aria-label', expect.stringContaining('Compare Competitors'));
    });

    test('provides proper tab panel labeling', () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      const tabPanel = screen.getByRole('tabpanel');
      expect(tabPanel).toHaveAttribute('aria-labelledby', expect.stringContaining('tab-list'));
      expect(tabPanel).toHaveAttribute('aria-label', expect.stringContaining('content'));
    });
  });

  describe('Responsive Design', () => {
    test('uses fullWidth tabs on mobile', () => {
      // Mock mobile breakpoint
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      const tabs = screen.getByLabelText(/competitor navigation tabs/i);
      expect(tabs).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    test('component is memoized', () => {
      const { rerender } = render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      // Re-render with same props should not cause unnecessary re-renders
      rerender(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      // Component should be wrapped with memo
      expect(UnifiedCompetitorsPage.displayName).toBe('UnifiedCompetitorsPage');
    });

    test('handles prop changes efficiently', () => {
      const { rerender } = render(
        <TestWrapper>
          <UnifiedCompetitorsPage isEmbedded={false} />
        </TestWrapper>
      );

      // Change props and verify re-render
      rerender(
        <TestWrapper>
          <UnifiedCompetitorsPage isEmbedded={true} />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('URL State Management', () => {
    test('updates URL when tab changes', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
      await user.click(compareTab);

      expect(mockNavigate).toHaveBeenCalledWith(
        expect.objectContaining({
          search: expect.stringContaining('view=compare')
        }),
        { replace: true }
      );
    });

    test('initializes with correct tab from URL', () => {
      // Mock URL with compare view
      mockLocation.search = '?view=compare';
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      // Should initialize with compare tab active
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });
});

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
  CircularProgress,
  Divider,
  TextField,
  MenuItem,
  Grid,
  useTheme,
  <PERSON>ert,
  Container,
  AlertTitle
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import { useTeam } from '../../contexts/TeamContext';
import { useAuth } from '../../contexts/AuthContext';
import PageHeader from '../../components/common/PageHeader';
import TeamInvitationEmailPreview from '../../components/teams/TeamInvitationEmailPreview';

const TeamInvitePage = () => {
  const theme = useTheme();
  const { teamId } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const { fetchTeam, inviteToTeam, loading } = useTeam();

  const [team, setTeam] = useState(null);
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('member');
  const [message, setMessage] = useState('');
  const [inviteLoading, setInviteLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Load team data with authentication check
  useEffect(() => {
    const loadTeam = async () => {
      if (!isAuthenticated || !teamId || authLoading) return;

      const teamData = await fetchTeam(teamId);
      if (teamData) {
        setTeam(teamData);

        // Check if user is owner or admin
        const userRole = teamData.members.find(member => member.user_id === user?.id)?.role;
        if (userRole !== 'owner' && userRole !== 'admin') {
          setError('You do not have permission to invite members to this team. Only owners and admins can send invitations.');
          setTimeout(() => {
            navigate(`/teams/${teamId}`);
          }, 3000);
        }
      } else {
        navigate('/teams');
      }
    };

    loadTeam();
  }, [teamId, fetchTeam, navigate, isAuthenticated, user?.id, authLoading]);

  // Handle invite submission with enhanced error handling
  const handleInvite = async (e) => {
    e.preventDefault();

    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!email.trim()) {
      setError('Email address is required');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      setError('Please enter a valid email address');
      return;
    }

    setInviteLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await inviteToTeam(teamId, {
        email: email.trim(),
        role,
        message: message.trim() || undefined
      });

      if (result) {
        setSuccess(true);
        setEmail('');
        setMessage('');
        setRole('member'); // Reset to default role
      }
    } catch (err) {
      console.error('Error sending invitation:', err);

      if (err.response?.status === 401) {
        setError('Session expired. Please log in again.');
        navigate('/login');
      } else if (err.response?.status === 403) {
        setError('You do not have permission to invite members to this team.');
      } else if (err.response?.status === 409) {
        setError('This user is already a member of the team or has a pending invitation.');
      } else if (err.response?.status === 422) {
        setError('Invalid email address or role. Please check your inputs.');
      } else {
        setError(err.response?.data?.detail || 'Failed to send invitation. Please try again.');
      }
    } finally {
      setInviteLoading(false);
    }
  };

  // Handle back button
  const handleBack = () => {
    navigate(`/teams/${teamId}`);
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  if (loading && !team) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 300,
        p: 3,
        backgroundColor: theme.palette.background.default
      }}>
        <Paper sx={{
          p: 4,
          textAlign: 'center',
          backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[50],
          border: `1px solid ${theme.palette.divider}`
        }}>
          <CircularProgress size={40} sx={{ color: theme.palette.primary.main }} />
          <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
            Loading team details...
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Preparing invitation form for {user?.name || 'user'}
          </Typography>
        </Paper>
      </Box>
    );
  }

  if (!team) {
    return null;
  }

  return (
    <Box sx={{ p: 3 }}>
      <PageHeader
        title={`Invite to ${team.name}`}
        subtitle={
          <Box>
            <Typography variant="body1" color="text.secondary">
              Send invitations to collaborate with your team
            </Typography>
            {user?.name && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Inviting as {user.name} • {team?.members?.find(member => member.user_id === user?.id)?.role || 'Admin'}
              </Typography>
            )}
          </Box>
        }
        action={
          <Button
            variant="outlined"
            onClick={handleBack}
          >
            Back to Team
          </Button>
        }
      />

      <Grid container spacing={3} sx={{ mt: 1 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{
            p: 3,
            backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[900] : theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: 2
          }}>
            <Typography variant="h6" gutterBottom sx={{ color: theme.palette.primary.main }}>
              Send Invitation
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Invite someone to join your team and start collaborating
              {user?.name && ` • Sent by ${user.name}`}
            </Typography>

            <Divider sx={{ mb: 3, backgroundColor: theme.palette.divider }} />

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                <AlertTitle>Invitation Sent Successfully!</AlertTitle>
                The recipient will receive an email with instructions to join {team?.name || 'the team'}.
                {user?.name && ` The invitation was sent by ${user.name}.`}
              </Alert>
            )}

            <form onSubmit={handleInvite}>
              <TextField
                label="Email Address"
                fullWidth
                variant="outlined"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                type="email"
                sx={{ mb: 3 }}
              />

              <TextField
                label="Role"
                select
                fullWidth
                variant="outlined"
                value={role}
                onChange={(e) => setRole(e.target.value)}
                sx={{ mb: 3 }}
              >
                <MenuItem value="admin">Admin</MenuItem>
                <MenuItem value="member">Member</MenuItem>
                <MenuItem value="viewer">Viewer</MenuItem>
              </TextField>

              <TextField
                label="Personal Message (Optional)"
                fullWidth
                variant="outlined"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                multiline
                rows={4}
                sx={{ mb: 3 }}
                placeholder="Add a personal message to your invitation..."
              />

              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                disabled={inviteLoading || !email.trim()}
                startIcon={inviteLoading ? <CircularProgress size={20} /> : <SendIcon />}
              >
                {inviteLoading ? 'Sending...' : 'Send Invitation'}
              </Button>
            </form>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Email Preview
            </Typography>

            <Divider sx={{ mb: 3 }} />

            <Box sx={{ maxHeight: 600, overflow: 'auto' }}>
              <TeamInvitationEmailPreview
                teamName={team.name}
                inviterName={user?.full_name}
                inviterEmail={user?.email}
                message={message}
              />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TeamInvitePage;

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from "react";
import {
  Link as RouterLink,

  useSearchParams,
} from "react-router-dom";
import {
  Box,
  Button,
  TextField,
  Typography,
  Link,

  InputAdornment,
  IconButton,
  CircularProgress,
  Alert,
  Divider,
} from "@mui/material";
import { Visibility, VisibilityOff, LockReset } from "@mui/icons-material";
import { useAuth } from "../../hooks/useAuth";
import { useNotification } from "../../hooks/useNotification";

const ResetPassword = () => {

  const [searchParams] = useSearchParams();
  const { resetPassword } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [token, setToken] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const [tokenError, setTokenError] = useState("");

  // Get token from URL query parameter
  useEffect(() => {
    const tokenFromUrl = searchParams.get("token");
    if (tokenFromUrl) {
      setToken(tokenFromUrl);
    } else {
      setTokenError(
        "Missing password reset token. Please check your email link and try again."
      );
    }
  }, [searchParams]);

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const validateForm = () => {
    if (!token) {
      setTokenError(
        "Missing password reset token. Please check your email link and try again."
      );
      return false;
    }

    if (!password) {
      setError("Password is required");
      return false;
    }

    if (password.length < 8) {
      setError("Password must be at least 8 characters long");
      return false;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError("");

    try {
      const result = await resetPassword(token, password);

      if (result.success) {
        setSuccess(true);
        showSuccessNotification("Password has been reset successfully!");
      } else {
        setError(result.error || "Failed to reset password. Please try again.");
        showErrorNotification(
          result.error || "Failed to reset password. Please try again."
        );
      }
    } catch (err) {
      console.error("Password reset error:", err);
      setError("An unexpected error occurred. Please try again.");
      showErrorNotification("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        width: "100%",
      }}
    >
      <Box sx={{ mb: 3, textAlign: "center" }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Reset Password
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Enter your new password below
        </Typography>
      </Box>

      {tokenError ? (
        <Box sx={{ my: 3 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            {tokenError}
          </Alert>
          <Button
            component={RouterLink}
            to="/forgot-password"
            variant="contained"
            color="primary"
            fullWidth
          >
            Request New Reset Link
          </Button>
        </Box>
      ) : success ? (
        <Box sx={{ my: 3 }}>
          <Alert severity="success" sx={{ mb: 3 }}>
            Your password has been reset successfully!
          </Alert>
          <Typography variant="body2" sx={{ mb: 3 }}>
            You can now log in with your new password.
          </Typography>
          <Button
            component={RouterLink}
            to="/login"
            variant="contained"
            color="primary"
            fullWidth
          >
            Go to Login
          </Button>
        </Box>
      ) : (
        <form onSubmit={handleSubmit}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <TextField
            fullWidth
            label="New Password"
            name="password"
            type={showPassword ? "text" : "password"}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            margin="normal"
            required
            autoFocus
            helperText="Password must be at least 8 characters long"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleTogglePasswordVisibility}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <TextField
            fullWidth
            label="Confirm New Password"
            name="confirmPassword"
            type={showConfirmPassword ? "text" : "password"}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            margin="normal"
            required
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleToggleConfirmPasswordVisibility}
                    edge="end"
                  >
                    {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            size="large"
            disabled={loading}
            startIcon={<LockReset />}
            sx={{ mt: 3, mb: 3 }}
          >
            {loading ? <CircularProgress size={24} /> : "Reset Password"}
          </Button>
        </form>
      )}

      <Divider sx={{ my: 2 }} />

      <Box sx={{ textAlign: "center" }}>
        <Typography variant="body2">
          Remember your password?{" "}
          <Link
            component={RouterLink}
            to="/login"
            variant="body2"
            underline="hover"
            fontWeight="bold"
          >
            Back to Login
          </Link>
        </Typography>
      </Box>
    </Box>
  );
};

export default ResetPassword;

"""
Bulk Import/Export Service for E-commerce Integration.
Provides comprehensive CSV/Excel processing with validation and background jobs.
"""

import logging
import asyncio
import csv
import io
import json
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from bson import ObjectId
import pandas as pd
from pathlib import Path

from app.models.ecommerce import SyncedProduct
from app.models.user import User
from app.db.mongodb import get_database
from app.core.redis import get_redis_client
from app.core.monitoring import monitor_performance, log_audit_event, OperationType
from app.services.job_queue.job_manager import job_manager
from app.core.config import settings

logger = logging.getLogger(__name__)

# Collection names
PRODUCTS_COLLECTION = "synced_products"
BULK_JOBS_COLLECTION = "bulk_jobs"
IMPORT_TEMPLATES_COLLECTION = "import_templates"

# Redis keys
BULK_JOB_STATUS_KEY = "bulk_job:{job_id}"
BULK_JOB_PROGRESS_KEY = "bulk_job_progress:{job_id}"

# File processing limits
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
MAX_RECORDS_PER_FILE = 50000
CHUNK_SIZE = 1000  # Process records in chunks

# Default field mappings
DEFAULT_PRODUCT_MAPPING = {
    "title": ["title", "name", "product_name"],
    "description": ["description", "desc", "product_description"],
    "price": ["price", "cost", "amount"],
    "sku": ["sku", "product_code", "item_code"],
    "inventory_quantity": ["quantity", "stock", "inventory", "qty"],
    "category": ["category", "type", "product_type"],
    "tags": ["tags", "keywords", "labels"],
    "weight": ["weight", "mass"],
    "vendor": ["vendor", "supplier", "brand"],
    "featured_image": ["image", "photo", "picture", "image_url"]
}


class BulkOperationsService:
    """
    Comprehensive bulk import/export service with background processing.
    """
    
    def __init__(self):
        self.redis_client = None
        self.db = None
        
    async def _get_redis_client(self):
        """Get Redis client for caching."""
        if not self.redis_client:
            self.redis_client = await get_redis_client()
        return self.redis_client
    
    async def _get_database(self):
        """Get MongoDB database."""
        if not self.db:
            self.db = await get_database()
        return self.db
    
    @monitor_performance("validate_import_file")
    async def validate_import_file(
        self,
        file_content: bytes,
        file_type: str,
        mapping: Dict[str, str],
        validation_rules: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Validate import file structure and content.
        
        Args:
            file_content: File content as bytes
            file_type: File type (csv, xlsx)
            mapping: Field mapping configuration
            validation_rules: Optional validation rules
            
        Returns:
            Validation result with errors and preview
        """
        try:
            # Check file size
            if len(file_content) > MAX_FILE_SIZE:
                return {
                    "success": False,
                    "error": f"File size exceeds maximum limit of {MAX_FILE_SIZE // (1024*1024)}MB"
                }
            
            # Parse file based on type
            if file_type.lower() == "csv":
                df = pd.read_csv(io.BytesIO(file_content))
            elif file_type.lower() in ["xlsx", "xls"]:
                df = pd.read_excel(io.BytesIO(file_content))
            else:
                return {
                    "success": False,
                    "error": f"Unsupported file type: {file_type}"
                }
            
            # Check record count
            if len(df) > MAX_RECORDS_PER_FILE:
                return {
                    "success": False,
                    "error": f"File contains {len(df)} records. Maximum allowed: {MAX_RECORDS_PER_FILE}"
                }
            
            # Validate headers
            file_headers = df.columns.tolist()
            mapped_fields = list(mapping.keys())
            source_fields = list(mapping.values())
            
            missing_headers = [field for field in source_fields if field not in file_headers]
            if missing_headers:
                return {
                    "success": False,
                    "error": f"Missing required headers: {missing_headers}",
                    "available_headers": file_headers
                }
            
            # Validate data types and content
            validation_errors = []
            preview_data = []
            
            # Get preview (first 10 rows)
            preview_rows = df.head(10)
            for index, row in preview_rows.iterrows():
                row_data = {}
                row_errors = []
                
                for target_field, source_field in mapping.items():
                    value = row.get(source_field)
                    
                    # Apply validation rules
                    if validation_rules and target_field in validation_rules:
                        rule = validation_rules[target_field]
                        
                        # Required field validation
                        if rule.get("required", False) and pd.isna(value):
                            row_errors.append(f"{target_field} is required")
                            continue
                        
                        # Data type validation
                        if not pd.isna(value):
                            if target_field == "price" and rule.get("type") == "decimal":
                                try:
                                    Decimal(str(value))
                                except Exception:
                                    row_errors.append(f"{target_field} must be a valid decimal")

                            elif target_field == "inventory_quantity" and rule.get("type") == "integer":
                                try:
                                    int(float(str(value)))
                                except (ValueError, TypeError):
                                    row_errors.append(f"{target_field} must be a valid integer")
                    
                    row_data[target_field] = value
                
                try:
                    # Handle various index types safely
                    if isinstance(index, (int, float)):
                        row_number = int(index) + 1
                    elif isinstance(index, str) and index.isdigit():
                        row_number = int(index) + 1
                    else:
                        row_number = len(preview_data) + 1
                except (ValueError, TypeError, AttributeError):
                    row_number = len(preview_data) + 1

                preview_data.append({
                    "row_index": row_number,
                    "data": row_data,
                    "errors": row_errors
                })

                if row_errors:
                    validation_errors.extend([
                        f"Row {row_number}: {error}" for error in row_errors
                    ])
            
            # Summary statistics
            stats = {
                "total_rows": len(df),
                "total_columns": len(df.columns),
                "mapped_fields": len(mapped_fields),
                "empty_cells": int(df.isnull().sum().sum()),
                "duplicate_rows": len(df) - len(df.drop_duplicates())
            }
            
            return {
                "success": True,
                "stats": stats,
                "preview": preview_data,
                "validation_errors": validation_errors,
                "has_errors": len(validation_errors) > 0,
                "file_headers": file_headers,
                "mapped_fields": mapped_fields
            }
            
        except Exception as e:
            logger.error(f"Error validating import file: {str(e)}")
            return {
                "success": False,
                "error": f"File validation failed: {str(e)}"
            }
    
    @monitor_performance("create_import_job")
    async def create_import_job(
        self,
        user_id: str,
        store_id: str,
        file_content: bytes,
        file_type: str,
        import_type: str,
        mapping: Dict[str, str],
        validation_rules: Optional[Dict[str, Any]] = None,
        import_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a bulk import job for background processing.
        
        Args:
            user_id: User ID
            store_id: Store ID
            file_content: File content as bytes
            file_type: File type (csv, xlsx)
            import_type: Import type (products, inventory, variants)
            mapping: Field mapping configuration
            validation_rules: Optional validation rules
            import_options: Optional import options
            
        Returns:
            Created job details
        """
        try:
            db = await self._get_database()
            
            # Validate file first
            validation_result = await self.validate_import_file(
                file_content, file_type, mapping, validation_rules
            )
            
            if not validation_result.get("success"):
                return validation_result
            
            if validation_result.get("has_errors"):
                return {
                    "success": False,
                    "error": "File validation failed",
                    "validation_errors": validation_result.get("validation_errors", [])
                }
            
            # Create job document
            job_id = ObjectId()
            job_doc = {
                "_id": job_id,
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id),
                "job_type": "import",
                "import_type": import_type,
                "file_type": file_type,
                "mapping": mapping,
                "validation_rules": validation_rules or {},
                "import_options": import_options or {},
                "status": "pending",
                "total_records": validation_result["stats"]["total_rows"],
                "processed_records": 0,
                "successful_records": 0,
                "failed_records": 0,
                "errors": [],
                "progress_percentage": 0,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "started_at": None,
                "completed_at": None
            }
            
            await db[BULK_JOBS_COLLECTION].insert_one(job_doc)
            
            # Store file content temporarily (in production, use cloud storage)
            redis = await self._get_redis_client()
            if redis:
                file_key = f"import_file:{job_id}"
                await redis.setex(file_key, 3600, file_content)  # 1 hour expiry
            
            # Queue the job for background processing
            await job_manager.enqueue_job(
                job_type="bulk_import",
                job_data={
                    "job_id": str(job_id),
                    "user_id": user_id,
                    "store_id": store_id,
                    "import_type": import_type,
                    "file_type": file_type,
                    "mapping": mapping,
                    "validation_rules": validation_rules,
                    "import_options": import_options
                },
                priority="normal",
                delay_seconds=0
            )
            
            # Log audit event
            log_audit_event(
                operation_type=OperationType.CREATE,
                resource_type="bulk_import_job",
                resource_id=str(job_id),
                user_id=user_id,
                details={
                    "import_type": import_type,
                    "total_records": validation_result["stats"]["total_rows"],
                    "file_type": file_type
                }
            )
            
            return {
                "success": True,
                "job_id": str(job_id),
                "status": "pending",
                "total_records": validation_result["stats"]["total_rows"],
                "estimated_duration_minutes": max(1, validation_result["stats"]["total_rows"] // 100),
                "validation_summary": validation_result["stats"]
            }
            
        except Exception as e:
            logger.error(f"Error creating import job: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to create import job: {str(e)}"
            }


    @monitor_performance("create_export_job")
    async def create_export_job(
        self,
        user_id: str,
        store_id: str,
        export_type: str,
        export_format: str = "csv",
        filters: Optional[Dict[str, Any]] = None,
        fields: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Create a bulk export job for background processing.

        Args:
            user_id: User ID
            store_id: Store ID
            export_type: Export type (products, inventory, variants)
            export_format: Export format (csv, xlsx)
            filters: Optional export filters
            fields: Optional specific fields to export

        Returns:
            Created export job details
        """
        try:
            db = await self._get_database()

            # Count records to export
            filter_criteria: Dict[str, Any] = {
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id)
            }

            if filters:
                # Apply additional filters
                if filters.get("categories"):
                    filter_criteria["category"] = {"$in": filters["categories"]}
                if filters.get("price_range"):
                    price_filter = {}
                    if filters["price_range"].get("min") is not None:
                        price_filter["$gte"] = float(filters["price_range"]["min"])
                    if filters["price_range"].get("max") is not None:
                        price_filter["$lte"] = float(filters["price_range"]["max"])
                    if price_filter:
                        filter_criteria["price"] = price_filter
                if filters.get("in_stock_only"):
                    filter_criteria["inventory_quantity"] = {"$gt": 0}

            total_records = await db[PRODUCTS_COLLECTION].count_documents(filter_criteria)

            if total_records == 0:
                return {
                    "success": False,
                    "error": "No records found matching the export criteria"
                }

            # Create export job
            job_id = ObjectId()
            job_doc = {
                "_id": job_id,
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id),
                "job_type": "export",
                "export_type": export_type,
                "export_format": export_format,
                "filters": filters or {},
                "fields": fields or [],
                "status": "pending",
                "total_records": total_records,
                "processed_records": 0,
                "progress_percentage": 0,
                "download_url": None,
                "expires_at": datetime.now(timezone.utc) + timedelta(days=7),
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc),
                "started_at": None,
                "completed_at": None
            }

            await db[BULK_JOBS_COLLECTION].insert_one(job_doc)

            # Queue the job for background processing
            await job_manager.enqueue_job(
                job_type="bulk_export",
                job_data={
                    "job_id": str(job_id),
                    "user_id": user_id,
                    "store_id": store_id,
                    "export_type": export_type,
                    "export_format": export_format,
                    "filters": filters,
                    "fields": fields
                },
                priority="normal",
                delay_seconds=0
            )

            return {
                "success": True,
                "job_id": str(job_id),
                "status": "pending",
                "total_records": total_records,
                "estimated_duration_minutes": max(1, total_records // 500),
                "expires_at": job_doc["expires_at"].isoformat()
            }

        except Exception as e:
            logger.error(f"Error creating export job: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to create export job: {str(e)}"
            }

    @monitor_performance("get_job_status")
    async def get_job_status(self, user_id: str, job_id: str) -> Dict[str, Any]:
        """
        Get status of a bulk operation job.

        Args:
            user_id: User ID
            job_id: Job ID

        Returns:
            Job status and progress
        """
        try:
            db = await self._get_database()

            job = await db[BULK_JOBS_COLLECTION].find_one({
                "_id": ObjectId(job_id),
                "user_id": ObjectId(user_id)
            })

            if not job:
                return {
                    "success": False,
                    "error": "Job not found or access denied"
                }

            # Get real-time progress from Redis if available
            redis = await self._get_redis_client()
            if redis:
                progress_key = BULK_JOB_PROGRESS_KEY.format(job_id=job_id)
                progress_data = await redis.get(progress_key)
                if progress_data:
                    progress_info = json.loads(progress_data)
                    job.update(progress_info)

            return {
                "success": True,
                "job_id": str(job["_id"]),
                "job_type": job.get("job_type"),
                "status": job.get("status"),
                "total_records": job.get("total_records", 0),
                "processed_records": job.get("processed_records", 0),
                "successful_records": job.get("successful_records", 0),
                "failed_records": job.get("failed_records", 0),
                "progress_percentage": job.get("progress_percentage", 0),
                "errors": job.get("errors", []),
                "download_url": job.get("download_url"),
                "expires_at": job.get("expires_at"),
                "created_at": job.get("created_at"),
                "started_at": job.get("started_at"),
                "completed_at": job.get("completed_at"),
                "estimated_completion": self._estimate_completion_time(job)
            }

        except Exception as e:
            logger.error(f"Error getting job status: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to get job status: {str(e)}"
            }

    def _estimate_completion_time(self, job: Dict[str, Any]) -> Optional[str]:
        """Estimate job completion time based on progress."""
        try:
            if job.get("status") in ["completed", "failed", "cancelled"]:
                return None

            processed = job.get("processed_records", 0)
            total = job.get("total_records", 0)
            started_at = job.get("started_at")

            if not started_at or processed == 0 or total == 0:
                return None

            elapsed = datetime.now(timezone.utc) - started_at
            rate = processed / elapsed.total_seconds()  # records per second

            if rate > 0:
                remaining_records = total - processed
                remaining_seconds = remaining_records / rate
                completion_time = datetime.now(timezone.utc) + timedelta(seconds=remaining_seconds)
                return completion_time.isoformat()

            return None

        except Exception:
            return None

    @monitor_performance("get_import_templates")
    async def get_import_templates(self, import_type: str) -> Dict[str, Any]:
        """
        Get import templates for different data types.

        Args:
            import_type: Type of import (products, inventory, variants)

        Returns:
            Import templates with field mappings and examples
        """
        try:
            templates = {
                "products": {
                    "name": "Product Import Template",
                    "description": "Template for importing product data",
                    "required_fields": ["title", "price", "sku"],
                    "optional_fields": ["description", "category", "tags", "weight", "vendor"],
                    "field_mapping": DEFAULT_PRODUCT_MAPPING,
                    "validation_rules": {
                        "title": {"required": True, "type": "string", "max_length": 255},
                        "price": {"required": True, "type": "decimal", "min_value": 0},
                        "sku": {"required": True, "type": "string", "max_length": 100},
                        "inventory_quantity": {"type": "integer", "min_value": 0}
                    },
                    "example_data": [
                        {
                            "title": "Sample Product 1",
                            "description": "This is a sample product description",
                            "price": 29.99,
                            "sku": "SAMPLE-001",
                            "inventory_quantity": 100,
                            "category": "Electronics",
                            "tags": "sample, test, product",
                            "weight": 1.5,
                            "vendor": "Sample Vendor"
                        },
                        {
                            "title": "Sample Product 2",
                            "description": "Another sample product",
                            "price": 49.99,
                            "sku": "SAMPLE-002",
                            "inventory_quantity": 50,
                            "category": "Accessories",
                            "tags": "sample, accessory",
                            "weight": 0.8,
                            "vendor": "Sample Vendor"
                        }
                    ]
                },
                "inventory": {
                    "name": "Inventory Update Template",
                    "description": "Template for updating inventory quantities",
                    "required_fields": ["sku", "quantity"],
                    "optional_fields": ["reason", "location"],
                    "field_mapping": {
                        "sku": ["sku", "product_code", "item_code"],
                        "quantity": ["quantity", "stock", "inventory", "qty"],
                        "reason": ["reason", "note", "comment"],
                        "location": ["location", "warehouse", "store"]
                    },
                    "validation_rules": {
                        "sku": {"required": True, "type": "string"},
                        "quantity": {"required": True, "type": "integer", "min_value": 0}
                    },
                    "example_data": [
                        {"sku": "SAMPLE-001", "quantity": 150, "reason": "Restock", "location": "Warehouse A"},
                        {"sku": "SAMPLE-002", "quantity": 75, "reason": "Adjustment", "location": "Warehouse A"}
                    ]
                }
            }

            if import_type not in templates:
                return {
                    "success": False,
                    "error": f"Template not found for import type: {import_type}"
                }

            return {
                "success": True,
                "template": templates[import_type],
                "available_types": list(templates.keys())
            }

        except Exception as e:
            logger.error(f"Error getting import templates: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to get import templates: {str(e)}"
            }


# Create singleton instance
bulk_operations_service = BulkOperationsService()

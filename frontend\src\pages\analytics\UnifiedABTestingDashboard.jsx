// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  Button,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  InputAdornment
} from '@mui/material';

import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { format } from 'date-fns';

import { useNotification } from '../../hooks/useNotification';
import { getAllABTests, getABTestResults, applyWinningVariant } from '../../api/unifiedABTesting';
import ABTestResultsDialog from '../../components/ab-testing/ABTestResultsDialog';

const UnifiedABTestingDashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [loading, setLoading] = useState(true);
  const [tests, setTests] = useState({ brand_profile_tests: [], campaign_tests: [] });
  const [filteredTests, setFilteredTests] = useState({ brand_profile_tests: [], campaign_tests: [] });
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedTest, setSelectedTest] = useState(null);
  const [testResults, setTestResults] = useState(null);
  const [resultsDialogOpen, setResultsDialogOpen] = useState(false);
  const [applyingWinner, setApplyingWinner] = useState(false);

  // Parse URL parameters on component mount
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const typeParam = params.get('type');

    if (typeParam) {
      if (typeParam === 'brand_profile') {
        setTypeFilter('brand_profile');
        setActiveTab(0);
      } else if (typeParam === 'campaign') {
        setTypeFilter('campaign');
        setActiveTab(1);
      }
    }
  }, [location.search]);

  // Fetch all A/B tests
  useEffect(() => {
    const fetchTests = async () => {
      setLoading(true);
      try {
        const data = await getAllABTests();
        setTests(data);
        setFilteredTests(data);
      } catch (error) {
        console.error('Error fetching A/B tests:', error);
        showErrorNotification('Failed to load A/B tests');
      } finally {
        setLoading(false);
      }
    };

    fetchTests();
  }, [showErrorNotification]);

  // Filter tests when search term, status filter, or type filter changes
  useEffect(() => {
    const filterTests = () => {
      // First, filter by test type if needed
      let testsToFilter = { ...tests };

      // If type filter is applied, only include tests of that type
      if (typeFilter === 'brand_profile') {
        testsToFilter = {
          brand_profile_tests: tests.brand_profile_tests,
          campaign_tests: []
        };
      } else if (typeFilter === 'campaign') {
        testsToFilter = {
          brand_profile_tests: [],
          campaign_tests: tests.campaign_tests
        };
      }

      // Then apply search and status filters
      const filtered = {
        brand_profile_tests: testsToFilter.brand_profile_tests.filter(test => {
          const matchesSearch = test.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                               (test.description && test.description.toLowerCase().includes(searchTerm.toLowerCase()));
          const matchesStatus = statusFilter === 'all' ||
                               (statusFilter === 'active' && test.is_active) ||
                               (statusFilter === 'completed' && !test.is_active);
          return matchesSearch && matchesStatus;
        }),
        campaign_tests: testsToFilter.campaign_tests.filter(test => {
          const matchesSearch = test.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                               (test.description && test.description.toLowerCase().includes(searchTerm.toLowerCase()));
          const matchesStatus = statusFilter === 'all' ||
                               (statusFilter === 'active' && test.is_active) ||
                               (statusFilter === 'completed' && !test.is_active);
          return matchesSearch && matchesStatus;
        })
      };
      setFilteredTests(filtered);

      // Update active tab based on type filter
      if (typeFilter === 'brand_profile') {
        setActiveTab(0);
      } else if (typeFilter === 'campaign') {
        setActiveTab(1);
      }
    };

    filterTests();
  }, [tests, searchTerm, statusFilter, typeFilter]);

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);

    // Update type filter to match the selected tab
    let newType = 'all';
    if (newValue === 0) {
      newType = 'brand_profile';
    } else if (newValue === 1) {
      newType = 'campaign';
    }

    setTypeFilter(newType);

    // Update URL with type parameter
    const params = new URLSearchParams(location.search);
    if (newType === 'all') {
      params.delete('type');
    } else {
      params.set('type', newType);
    }
    navigate({ search: params.toString() }, { replace: true });
  };

  // View test results
  const handleViewResults = async (test) => {
    try {
      setSelectedTest(test);
      const results = await getABTestResults(test.type, test.id);
      setTestResults(results);
      setResultsDialogOpen(true);
    } catch (error) {
      console.error('Error fetching test results:', error);
      showErrorNotification('Failed to load test results');
    }
  };

  // Apply winning variant
  const handleApplyWinner = async (testType, testId, variantId) => {
    try {
      setApplyingWinner(true);
      await applyWinningVariant(testType, testId, variantId);
      showSuccessNotification('Winning variant applied successfully');
      setResultsDialogOpen(false);
    } catch (error) {
      console.error('Error applying winning variant:', error);
      showErrorNotification('Failed to apply winning variant');
    } finally {
      setApplyingWinner(false);
    }
  };

  // Create new A/B test
  const handleCreateTest = (type) => {
    if (type === 'brand_profile') {
      // Navigate to brand profile management page
      navigate('/content/branding');
    } else if (type === 'campaign') {
      navigate('/campaigns');
    }
  };

  // Render test table
  const renderTestTable = (testType) => {
    const testsToShow = testType === 'brand_profile' ? filteredTests.brand_profile_tests : filteredTests.campaign_tests;

    if (testsToShow.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            No {testType === 'brand_profile' ? 'brand profile' : 'campaign'} A/B tests found.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            sx={{ mt: 2 }}
            onClick={() => handleCreateTest(testType)}
          >
            Create {testType === 'brand_profile' ? 'Brand Profile' : 'Campaign'} A/B Test
          </Button>
        </Box>
      );
    }

    return (
      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Variants</TableCell>
              <TableCell>Significance</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {testsToShow.map((test) => (
              <TableRow key={test.id}>
                <TableCell>
                  <Typography variant="subtitle2">{test.name}</Typography>
                  {test.description && (
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {test.description}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>{format(new Date(test.created_at), 'MMM d, yyyy')}</TableCell>
                <TableCell>
                  <Chip
                    label={test.is_active ? 'Active' : 'Completed'}
                    color={test.is_active ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>{test.variant_count}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ position: 'relative', display: 'inline-flex', mr: 1 }}>
                      <CircularProgress
                        variant="determinate"
                        value={test.statistical_significance * 100}
                        size={24}
                        thickness={4}
                        color={test.has_winner ? 'success' : 'primary'}
                      />
                      {test.has_winner && (
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <CheckCircleIcon color="success" sx={{ fontSize: 16 }} />
                        </Box>
                      )}
                    </Box>
                    {Math.round(test.statistical_significance * 100)}%
                  </Box>
                </TableCell>
                <TableCell>
                  <Tooltip title="View Results">
                    <IconButton
                      color="primary"
                      onClick={() => handleViewResults(test)}
                    >
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ py: 3 }}>
      <Typography variant="h4" gutterBottom>
        <CompareArrowsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        Unified A/B Testing Dashboard
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Search Tests"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                  startAdornment={
                    <InputAdornment position="start">
                      <FilterListIcon />
                    </InputAdornment>
                  }
                >
                  <MenuItem value="all">All</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Test Type</InputLabel>
                <Select
                  value={typeFilter}
                  onChange={(e) => {
                    const newType = e.target.value;
                    setTypeFilter(newType);

                    // Update URL with type parameter
                    const params = new URLSearchParams(location.search);
                    if (newType === 'all') {
                      params.delete('type');
                    } else {
                      params.set('type', newType);
                    }
                    navigate({ search: params.toString() }, { replace: true });
                  }}
                  label="Test Type"
                  startAdornment={
                    <InputAdornment position="start">
                      <CompareArrowsIcon />
                    </InputAdornment>
                  }
                >
                  <MenuItem value="all">All Tests</MenuItem>
                  <MenuItem value="brand_profile">Brand Profile Tests</MenuItem>
                  <MenuItem value="campaign">Campaign Tests</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => handleCreateTest(activeTab === 0 ? 'brand_profile' : 'campaign')}
              >
                Create {activeTab === 0 ? 'Brand Profile' : 'Campaign'} A/B Test
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="Brand Profile Tests" />
        <Tab label="Campaign Tests" />
      </Tabs>

      <Box sx={{ mt: 2 }}>
        {activeTab === 0 ? renderTestTable('brand_profile') : renderTestTable('campaign')}
      </Box>

      {selectedTest && testResults && (
        <ABTestResultsDialog
          open={resultsDialogOpen}
          onClose={() => setResultsDialogOpen(false)}
          testResults={testResults}
          onApplyWinner={handleApplyWinner}
          applyingWinner={applyingWinner}
        />
      )}
    </Box>
  );
};

export default UnifiedABTestingDashboard;

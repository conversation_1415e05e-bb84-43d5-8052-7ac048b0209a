import axios from 'axios';
import { API_BASE_URL, ENVIRONMENT } from '../config.js';

// Check if localhost bypass is enabled
const isLocalhostBypass = ENVIRONMENT === 'development' &&
  (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

// Create axios instance with default configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  // Add retry configuration for stability
  retry: 3,
  retryDelay: 1000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle network errors with retry
    if (!error.response && !originalRequest._retry) {
      originalRequest._retry = true;
      console.warn('Network error, retrying...', error.message);

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000));
      return api(originalRequest);
    }

    // Handle 401 errors (but not in localhost bypass mode)
    if (error.response?.status === 401 && !isLocalhostBypass) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      window.location.href = '/login';
    }

    // Handle 500 errors with user-friendly message
    if (error.response?.status >= 500) {
      console.error('Server error:', error.response.data);
    }

    return Promise.reject(error);
  }
);

export default api;

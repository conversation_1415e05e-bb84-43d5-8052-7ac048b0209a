// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import { Box, Container, Paper, Typography, Tabs, Tab, CircularProgress, Alert, AlertTitle, Button, Breadcrumbs, Link, Tooltip, IconButton } from '@mui/material';
import { Settings as SettingsIcon, Notifications as NotificationsIcon, Add as AddIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useSnackbar } from '../../contexts/SnackbarContext';
import {
  getNotificationRules,
  getNotificationRule,
  createNotificationRule,
  updateNotificationRule
} from '../../api/notificationRules';
import NotificationRulesList from '../../components/settings/NotificationRulesList';
import NotificationRuleForm from '../../components/settings/NotificationRuleForm';

const NotificationRulesPage = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useSnackbar();
  
  // State
  const [loading, setLoading] = useState(true);
  const [rules, setRules] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedRuleId, setSelectedRuleId] = useState(null);
  const [selectedRule, setSelectedRule] = useState(null);
  
  // Navigation handlers for production-ready user experience
  const handleNavigateToSettings = useCallback(() => {
    navigate('/settings');
  }, [navigate]);

  const handleNavigateToNotifications = useCallback(() => {
    navigate('/notifications');
  }, [navigate]);

  // Fetch rules with proper error handling and authentication
  const fetchRules = useCallback(async () => {
    try {
      setLoading(true);
      const data = await getNotificationRules();
      setRules(data);
    } catch (error) {
      console.error('Error fetching notification rules:', error);
      showErrorNotification('Failed to fetch notification rules');
    } finally {
      setLoading(false);
    }
  }, [showErrorNotification]);

  // Authentication check with redirect
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      showErrorNotification('Authentication required to access notification rules');
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate, showErrorNotification]);

  // Fetch rules on mount (only if authenticated)
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      fetchRules();
    }
  }, [fetchRules, isAuthenticated, authLoading]);
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    
    // Reset selected rule when switching to list view
    if (newValue === 0) {
      setSelectedRuleId(null);
      setSelectedRule(null);
    }
  };
  
  // Create rule with authentication check
  const handleCreateRule = () => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to create notification rules');
      navigate('/login');
      return;
    }

    setSelectedRuleId(null);
    setSelectedRule(null);
    setActiveTab(1);
  };
  
  // Edit rule with enhanced error handling
  const handleEditRule = async (ruleId) => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to edit notification rules');
      navigate('/login');
      return;
    }

    try {
      setLoading(true);
      const rule = await getNotificationRule(ruleId);
      setSelectedRuleId(ruleId);
      setSelectedRule(rule);
      setActiveTab(1);
    } catch (error) {
      console.error('Error fetching notification rule:', error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to edit this notification rule.');
      } else if (error.response?.status === 404) {
        showErrorNotification('Notification rule not found. It may have been deleted.');
      } else {
        showErrorNotification('Failed to fetch notification rule. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Submit rule form with enhanced error handling
  const handleSubmitRuleForm = async (formData) => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to save notification rules');
      navigate('/login');
      return;
    }

    try {
      setLoading(true);

      if (selectedRuleId) {
        // Update existing rule
        const updatedRule = await updateNotificationRule(selectedRuleId, formData);

        // Update rules list
        setRules(prevRules => prevRules.map(rule =>
          rule.id === updatedRule.id ? updatedRule : rule
        ));

        showSuccessNotification(`Notification rule "${updatedRule.name || 'Unnamed'}" updated successfully`);
      } else {
        // Create new rule
        const newRule = await createNotificationRule(formData);

        // Add to rules list
        setRules(prevRules => [...prevRules, newRule]);

        showSuccessNotification(`Notification rule "${newRule.name || 'Unnamed'}" created successfully`);
      }

      // Switch back to list view
      setActiveTab(0);
      setSelectedRuleId(null);
      setSelectedRule(null);
    } catch (error) {
      console.error('Error saving notification rule:', error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to save notification rules.');
      } else if (error.response?.status === 422) {
        showErrorNotification('Invalid rule data. Please check your inputs and try again.');
      } else {
        showErrorNotification('Failed to save notification rule. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Cancel rule form
  const handleCancelRuleForm = () => {
    setActiveTab(0);
    setSelectedRuleId(null);
    setSelectedRule(null);
  };
  
  // Rule updated
  const handleRuleUpdated = (updatedRule) => {
    setRules(prevRules => prevRules.map(rule => 
      rule.id === updatedRule.id ? updatedRule : rule
    ));
  };
  
  // Rule deleted
  const handleRuleDeleted = (ruleId) => {
    setRules(prevRules => prevRules.filter(rule => rule.id !== ruleId));
  };
  
  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <Container maxWidth="lg">
      {/* Enhanced header with navigation and user context */}
      <Box sx={{ mb: 4 }}>
        {/* Breadcrumb navigation */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            component="button"
            variant="body2"
            onClick={handleNavigateToSettings}
            sx={{
              display: 'flex',
              alignItems: 'center',
              textDecoration: 'none',
              color: 'primary.main',
              '&:hover': {
                textDecoration: 'underline',
              },
            }}
          >
            <SettingsIcon sx={{ mr: 0.5, fontSize: 16 }} />
            Settings
          </Link>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <NotificationsIcon sx={{ mr: 0.5, fontSize: 16 }} />
            Notification Rules
          </Typography>
        </Breadcrumbs>

        {/* Page header with actions */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Notification Rules
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Customize when and how you receive notifications for {user?.email || 'your account'}.
              Create rules to filter notifications based on content type, priority, and timing.
              {user?.name && ` Welcome back, ${user.name}!`}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Back to Settings">
              <Button
                variant="outlined"
                size="small"
                onClick={handleNavigateToSettings}
                startIcon={<ArrowBackIcon />}
              >
                Back
              </Button>
            </Tooltip>

            <Tooltip title="Go to Notifications">
              <Button
                variant="outlined"
                size="small"
                onClick={handleNavigateToNotifications}
                startIcon={<NotificationsIcon />}
              >
                View Notifications
              </Button>
            </Tooltip>

            <Tooltip title="Create New Rule">
              <Button
                variant="contained"
                size="small"
                onClick={handleCreateRule}
                startIcon={<AddIcon />}
              >
                New Rule
              </Button>
            </Tooltip>

            <Tooltip title="Refresh Rules">
              <IconButton
                onClick={fetchRules}
                disabled={loading}
                color="primary"
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* User context and statistics */}
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Active Rules:</strong> {rules.length} |
            <strong> User:</strong> {user?.name || user?.email || 'Current User'} |
            <strong> Account Type:</strong> {user?.subscription?.plan_name || 'Free'} |
            <strong> Last Updated:</strong> {new Date().toLocaleDateString()}
          </Typography>
        </Alert>
      </Box>
      
      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label="My Rules" />
          <Tab label={selectedRuleId ? 'Edit Rule' : 'Create Rule'} />
        </Tabs>
      </Paper>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {activeTab === 0 && (
            <NotificationRulesList
              rules={rules}
              onRuleUpdated={handleRuleUpdated}
              onRuleDeleted={handleRuleDeleted}
              onCreateRule={handleCreateRule}
              onEditRule={handleEditRule}
            />
          )}
          
          {activeTab === 1 && (
            <NotificationRuleForm
              initialData={selectedRule}
              onSubmit={handleSubmitRuleForm}
              onCancel={handleCancelRuleForm}
            />
          )}
        </>
      )}
    </Container>
  );
};

export default NotificationRulesPage;

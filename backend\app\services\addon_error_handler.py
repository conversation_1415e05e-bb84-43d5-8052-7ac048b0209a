"""
Comprehensive error handling and edge case management for ACEO add-ons.
Handles expiration, conflicts, refunds, failed payments, and graceful degradation.
"""
import logging
from typing import Dict, List, Optional, Any, Tuple, Callable
from datetime import datetime, timezone, timedelta
from enum import Enum
from dataclasses import dataclass

from app.db.mongodb import get_database
from app.models.user import User
# Models are now handled as dictionaries for MongoDB
from app.services.addon_catalog import addon_catalog
from app.services.addon_usage_tracking import usage_tracker
# Note: These functions don't exist yet, using placeholders
# from app.services.notification import send_addon_notification
# from app.services.email_service import send_addon_expiry_email
from app.core.monitoring import record_addon_metrics, record_error_metrics
from app.core.redis import (
    redis_manager, redis_get, redis_set, redis_delete, redis_setex,
    redis_incr, redis_expire, redis_set_nx_ex
)

logger = logging.getLogger(__name__)


class AddonErrorType(str, Enum):
    """Types of add-on errors."""
    EXPIRY_CONFLICT = "expiry_conflict"
    PAYMENT_FAILED = "payment_failed"
    REFUND_PROCESSING = "refund_processing"
    USAGE_CONFLICT = "usage_conflict"
    TIMEZONE_MISMATCH = "timezone_mismatch"
    CONCURRENT_MODIFICATION = "concurrent_modification"
    INVALID_STATE = "invalid_state"
    EXTERNAL_SERVICE_ERROR = "external_service_error"


class GracefulDegradationLevel(str, Enum):
    """Levels of graceful degradation."""
    FULL_SERVICE = "full_service"
    LIMITED_SERVICE = "limited_service"
    EMERGENCY_MODE = "emergency_mode"
    SERVICE_UNAVAILABLE = "service_unavailable"


@dataclass
class ErrorContext:
    """Error context information."""
    user_id: int
    addon_id: Optional[str]
    operation: str
    error_type: AddonErrorType
    details: Dict[str, Any]
    timestamp: datetime
    correlation_id: Optional[str] = None


class AddonErrorHandler:
    """Comprehensive error handler for add-on operations."""
    
    def __init__(self):
        self.degradation_level = GracefulDegradationLevel.FULL_SERVICE
        self.error_thresholds = {
            "payment_failures": 5,  # per hour
            "service_errors": 10,   # per hour
            "concurrent_conflicts": 3  # per minute
        }
        self.recovery_strategies = self._initialize_recovery_strategies()
    
    def _initialize_recovery_strategies(self) -> Dict[AddonErrorType, Callable]:
        """Initialize recovery strategies for different error types."""
        return {
            AddonErrorType.EXPIRY_CONFLICT: self._handle_expiry_conflict,
            AddonErrorType.PAYMENT_FAILED: self._handle_payment_failure,
            AddonErrorType.REFUND_PROCESSING: self._handle_refund_processing,
            AddonErrorType.USAGE_CONFLICT: self._handle_usage_conflict,
            AddonErrorType.TIMEZONE_MISMATCH: self._handle_timezone_mismatch,
            AddonErrorType.CONCURRENT_MODIFICATION: self._handle_concurrent_modification,
            AddonErrorType.INVALID_STATE: self._handle_invalid_state,
            AddonErrorType.EXTERNAL_SERVICE_ERROR: self._handle_external_service_error
        }
    
    async def handle_error(self, error_context: ErrorContext) -> Dict[str, Any]:
        """
        Handle add-on errors with appropriate recovery strategies.
        
        Returns:
            Dict with recovery result and next steps
        """
        try:
            # Log error for monitoring
            record_error_metrics(error_context.error_type.value, "addon_error")
            
            # Check if we need to degrade service
            await self._check_degradation_triggers(error_context)
            
            # Get recovery strategy
            recovery_strategy = self.recovery_strategies.get(error_context.error_type)
            
            if not recovery_strategy:
                return await self._handle_unknown_error(error_context)
            
            # Execute recovery strategy
            recovery_result = await recovery_strategy(error_context)
            
            # Update error metrics
            record_addon_metrics(
                f"error_recovery_{error_context.error_type.value}",
                "system",
                1 if recovery_result.get("success") else 0
            )
            
            return recovery_result
            
        except Exception as e:
            logger.error(f"Error in error handler: {str(e)}")
            return {
                "success": False,
                "error": "Error handler failed",
                "fallback_action": "contact_support",
                "user_message": "We're experiencing technical difficulties. Please contact support."
            }
    
    async def _handle_expiry_conflict(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle add-on expiry conflicts and overlapping periods."""
        try:
            db = await get_database()
            user_id = context.user_id
            addon_id = context.addon_id

            # Get conflicting add-ons
            user_addons_collection = db["user_addons"]
            conflicting_addons = await user_addons_collection.find({
                "user_id": user_id,
                "addon_id": addon_id,
                "is_active": True
            }).to_list(length=None)

            if len(conflicting_addons) <= 1:
                return {"success": True, "action": "no_conflict_found"}

            # Sort by purchase date
            conflicting_addons.sort(key=lambda x: x.get("purchased_at", datetime.min.replace(tzinfo=timezone.utc)))

            # Keep the most recent, consolidate credits from others
            primary_addon = conflicting_addons[-1]
            total_credits = sum(addon.get("credits_remaining", 0) for addon in conflicting_addons)

            # Update primary add-on with consolidated credits
            await user_addons_collection.update_one(
                {"_id": primary_addon["_id"]},
                {
                    "$set": {
                        "credits_remaining": total_credits,
                        "total_credits": max(primary_addon.get("total_credits", 0), total_credits),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # Deactivate other add-ons
            for addon in conflicting_addons[:-1]:
                await user_addons_collection.update_one(
                    {"_id": addon["_id"]},
                    {
                        "$set": {
                            "is_active": False,
                            "deactivated_reason": "consolidated_due_to_conflict",
                            "deactivated_at": datetime.now(timezone.utc),
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )

            # Notify user (if notification service is available)
            try:
                await send_addon_notification(
                    user_id,
                    "Add-on Credits Consolidated",
                    f"We've consolidated your {addon_id} credits. You now have {total_credits} credits available.",
                    "info"
                )
            except Exception as e:
                logger.warning(f"Failed to send consolidation notification: {str(e)}")

            return {
                "success": True,
                "action": "consolidated_credits",
                "total_credits": total_credits,
                "primary_addon_id": str(primary_addon["_id"])
            }
            
        except Exception as e:
            logger.error(f"Error handling expiry conflict: {str(e)}")
            return {
                "success": False,
                "error": "Failed to resolve expiry conflict",
                "fallback_action": "manual_review"
            }
    
    async def _handle_payment_failure(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle failed payments for add-on purchases."""
        try:
            user_id = context.user_id
            payment_details = context.details
            
            # Check if this is a retry or first failure
            failure_key = f"payment_failure:{user_id}:{payment_details.get('transaction_id')}"
            failure_count = await redis_get(failure_key) or 0
            failure_count = int(failure_count) + 1

            await redis_setex(failure_key, 3600, str(failure_count))  # 1 hour expiry
            
            if failure_count <= 3:
                # Attempt automatic retry with exponential backoff
                retry_delay = 2 ** failure_count  # 2, 4, 8 seconds
                
                return {
                    "success": False,
                    "action": "retry_payment",
                    "retry_delay": retry_delay,
                    "retry_count": failure_count,
                    "user_message": f"Payment failed. Retrying in {retry_delay} seconds..."
                }
            else:
                # Too many failures, require manual intervention
                await send_addon_notification(
                    user_id,
                    "Payment Failed",
                    "We couldn't process your payment. Please update your payment method.",
                    "error"
                )
                
                return {
                    "success": False,
                    "action": "require_payment_update",
                    "user_message": "Payment failed. Please update your payment method.",
                    "redirect_url": "/billing/payment-methods"
                }
                
        except Exception as e:
            logger.error(f"Error handling payment failure: {str(e)}")
            return {
                "success": False,
                "error": "Failed to handle payment failure",
                "fallback_action": "contact_support"
            }
    
    async def _handle_refund_processing(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle add-on refund processing and credit adjustments."""
        try:
            db = await get_database()
            user_id = context.user_id
            addon_id = context.addon_id
            refund_details = context.details

            # Find the add-on to refund
            user_addons_collection = db["user_addons"]
            addon = await user_addons_collection.find_one({
                "user_id": user_id,
                "addon_id": addon_id,
                "_id": refund_details.get("addon_instance_id")
            })

            if not addon:
                return {
                    "success": False,
                    "error": "Add-on not found for refund",
                    "action": "verify_addon_details"
                }

            # Calculate refund amount based on unused credits
            total_credits = addon.get("total_credits", 0)
            credits_used = addon.get("credits_used", 0)
            usage_percentage = credits_used / total_credits if total_credits > 0 else 1
            refund_percentage = max(0, 1 - usage_percentage)
            refund_amount = addon.get("purchase_price", 0) * refund_percentage

            # Create refund record
            refunds_collection = db["addon_refunds"]
            refund_data = {
                "user_id": user_id,
                "user_addon_id": addon["_id"],
                "addon_id": addon_id,
                "refund_amount": refund_amount,
                "refund_reason": refund_details.get("reason", "user_request"),
                "processed_at": datetime.now(timezone.utc),
                "refund_percentage": refund_percentage,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            await refunds_collection.insert_one(refund_data)

            # Mark add-on as refunded
            await user_addons_collection.update_one(
                {"_id": addon["_id"]},
                {
                    "$set": {
                        "is_refunded": True,
                        "is_active": False,
                        "refunded_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            # Process actual refund through payment processor
            # This would integrate with Lemon Squeezy refund API

            # Send notification (if notification service is available)
            try:
                await send_addon_notification(
                    user_id,
                    "Refund Processed",
                    f"Your refund of ${refund_amount:.2f} has been processed.",
                    "success"
                )
            except Exception as e:
                logger.warning(f"Failed to send refund notification: {str(e)}")
            
            return {
                "success": True,
                "action": "refund_processed",
                "refund_amount": refund_amount,
                "refund_percentage": refund_percentage
            }
            
        except Exception as e:
            logger.error(f"Error processing refund: {str(e)}")
            return {
                "success": False,
                "error": "Failed to process refund",
                "fallback_action": "manual_refund_review"
            }
    
    async def _handle_usage_conflict(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle usage conflicts and concurrent access issues."""
        try:
            user_id = context.user_id
            usage_details = context.details
            
            # Implement optimistic locking for usage tracking
            lock_key = f"usage_lock:{user_id}:{usage_details.get('addon_id')}"
            
            # Try to acquire lock
            lock_acquired = await redis_set_nx_ex(lock_key, "locked", 30)
            
            if not lock_acquired:
                return {
                    "success": False,
                    "action": "retry_with_backoff",
                    "retry_delay": 1,
                    "user_message": "Processing your request, please wait..."
                }
            
            try:
                # Re-check usage and process
                current_usage = await usage_tracker.get_addon_status(str(user_id))

                # Process the usage with current state
                usage_type = usage_details.get("usage_type")
                if usage_type and hasattr(usage_tracker, 'UsageType'):
                    # Ensure usage_type is a valid UsageType enum
                    from app.services.addon_usage_tracking import UsageType
                    if isinstance(usage_type, str):
                        try:
                            usage_type = UsageType(usage_type)
                        except ValueError:
                            usage_type = UsageType.REGENERATION_CREDITS  # Default fallback

                    result = await usage_tracker.track_usage(
                        str(user_id),
                        usage_type,
                        usage_details.get("amount", 1),
                        usage_details.get("metadata", {})
                    )
                else:
                    # Skip usage tracking if no valid usage type
                    result = {"success": True, "message": "Usage tracking skipped - no valid usage type"}
                
                return {
                    "success": result.get("success", False),
                    "action": "usage_processed",
                    "credits_remaining": result.get("credits_remaining", 0)
                }
                
            finally:
                # Release lock
                await redis_delete(lock_key)
                
        except Exception as e:
            logger.error(f"Error handling usage conflict: {str(e)}")
            return {
                "success": False,
                "error": "Failed to resolve usage conflict",
                "fallback_action": "retry_later"
            }
    
    async def _handle_timezone_mismatch(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle timezone-related issues in add-on operations."""
        try:
            # Normalize all timestamps to UTC
            timezone_details = context.details
            
            # Convert timestamps to UTC if they aren't already
            for key, value in timezone_details.items():
                if isinstance(value, datetime) and value.tzinfo is None:
                    # Assume local timezone and convert to UTC
                    timezone_details[key] = value.replace(tzinfo=timezone.utc)
            
            return {
                "success": True,
                "action": "timezone_normalized",
                "normalized_data": timezone_details
            }
            
        except Exception as e:
            logger.error(f"Error handling timezone mismatch: {str(e)}")
            return {
                "success": False,
                "error": "Failed to handle timezone mismatch",
                "fallback_action": "use_utc_default"
            }
    
    async def _handle_concurrent_modification(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle concurrent modification conflicts."""
        try:
            # Implement retry with exponential backoff
            retry_count = context.details.get("retry_count", 0)
            max_retries = 3
            
            if retry_count >= max_retries:
                return {
                    "success": False,
                    "action": "max_retries_exceeded",
                    "user_message": "Unable to complete operation due to high system load. Please try again later."
                }
            
            retry_delay = (2 ** retry_count) * 0.1  # 0.1, 0.2, 0.4 seconds
            
            return {
                "success": False,
                "action": "retry_with_backoff",
                "retry_delay": retry_delay,
                "retry_count": retry_count + 1
            }
            
        except Exception as e:
            logger.error(f"Error handling concurrent modification: {str(e)}")
            return {
                "success": False,
                "error": "Failed to handle concurrent modification",
                "fallback_action": "serialize_operations"
            }
    
    async def _handle_invalid_state(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle invalid add-on states."""
        try:
            db = await get_database()
            user_id = context.user_id
            addon_id = context.addon_id

            # Find and fix invalid states
            user_addons_collection = db["user_addons"]
            invalid_addons = await user_addons_collection.find({
                "user_id": user_id,
                "addon_id": addon_id
            }).to_list(length=None)

            fixed_count = 0
            for addon in invalid_addons:
                updates = {}

                # Fix common invalid states
                if addon.get("credits_remaining", 0) < 0:
                    updates["credits_remaining"] = 0
                    fixed_count += 1

                if addon.get("credits_used", 0) > addon.get("total_credits", 0):
                    updates["credits_used"] = addon.get("total_credits", 0)
                    fixed_count += 1

                expires_at = addon.get("expires_at")
                if (addon.get("is_active") and expires_at and
                    expires_at < datetime.now(timezone.utc)):
                    updates["is_active"] = False
                    updates["expired_at"] = expires_at
                    fixed_count += 1

                # Apply updates if any
                if updates:
                    updates["updated_at"] = datetime.now(timezone.utc)
                    await user_addons_collection.update_one(
                        {"_id": addon["_id"]},
                        {"$set": updates}
                    )
            
            return {
                "success": True,
                "action": "state_corrected",
                "fixes_applied": fixed_count
            }
            
        except Exception as e:
            logger.error(f"Error handling invalid state: {str(e)}")
            return {
                "success": False,
                "error": "Failed to handle invalid state",
                "fallback_action": "manual_state_review"
            }
    
    async def _handle_external_service_error(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle external service errors (Lemon Squeezy, OpenAI, etc.)."""
        try:
            service_name = context.details.get("service", "unknown")
            
            # Implement circuit breaker pattern
            circuit_key = f"circuit_breaker:{service_name}"
            failure_count = await redis_get(circuit_key) or 0
            failure_count = int(failure_count) + 1

            await redis_setex(circuit_key, 300, str(failure_count))  # 5 minutes
            
            if failure_count >= 5:
                # Circuit breaker open - use fallback
                return {
                    "success": False,
                    "action": "circuit_breaker_open",
                    "fallback_mode": True,
                    "user_message": f"{service_name} is temporarily unavailable. Using fallback mode."
                }
            
            # Service still available, retry with backoff
            return {
                "success": False,
                "action": "retry_external_service",
                "retry_delay": min(2 ** failure_count, 30),  # Max 30 seconds
                "service": service_name
            }
            
        except Exception as e:
            logger.error(f"Error handling external service error: {str(e)}")
            return {
                "success": False,
                "error": "Failed to handle external service error",
                "fallback_action": "enable_offline_mode"
            }
    
    async def _handle_unknown_error(self, context: ErrorContext) -> Dict[str, Any]:
        """Handle unknown error types."""
        logger.error(f"Unknown error type: {context.error_type}")
        
        return {
            "success": False,
            "action": "unknown_error",
            "error": f"Unknown error type: {context.error_type}",
            "fallback_action": "contact_support",
            "user_message": "An unexpected error occurred. Please contact support."
        }
    
    async def _check_degradation_triggers(self, context: ErrorContext):
        """Check if we need to degrade service level."""
        try:
            # Check error rates
            error_key = f"error_rate:{context.error_type.value}"
            current_errors = await redis_get(error_key) or 0
            current_errors = int(current_errors) + 1

            await redis_setex(error_key, 3600, str(current_errors))  # 1 hour window
            
            # Determine if we need to degrade
            threshold = self.error_thresholds.get(context.error_type.value, 10)
            
            if current_errors >= threshold:
                if self.degradation_level == GracefulDegradationLevel.FULL_SERVICE:
                    self.degradation_level = GracefulDegradationLevel.LIMITED_SERVICE
                    logger.warning(f"Degrading to limited service due to {context.error_type.value} errors")
                elif self.degradation_level == GracefulDegradationLevel.LIMITED_SERVICE:
                    self.degradation_level = GracefulDegradationLevel.EMERGENCY_MODE
                    logger.error(f"Degrading to emergency mode due to {context.error_type.value} errors")
                    
        except Exception as e:
            logger.error(f"Error checking degradation triggers: {str(e)}")


# Global error handler instance
addon_error_handler = AddonErrorHandler()


async def handle_addon_error(error_type: AddonErrorType, user_id: int, 
                           addon_id: Optional[str] = None, operation: str = "unknown",
                           details: Optional[Dict[str, Any]] = None,
                           correlation_id: Optional[str] = None) -> Dict[str, Any]:
    """Handle add-on errors with comprehensive recovery strategies."""
    error_context = ErrorContext(
        user_id=user_id,
        addon_id=addon_id,
        operation=operation,
        error_type=error_type,
        details=details or {},
        timestamp=datetime.now(timezone.utc),
        correlation_id=correlation_id
    )
    
    return await addon_error_handler.handle_error(error_context)


def get_degradation_level() -> GracefulDegradationLevel:
    """Get current service degradation level."""
    return addon_error_handler.degradation_level


async def reset_degradation_level():
    """Reset service degradation level to full service."""
    addon_error_handler.degradation_level = GracefulDegradationLevel.FULL_SERVICE
    logger.info("Service degradation level reset to full service")


# Placeholder functions for missing notification services
# TODO: Implement these functions in the appropriate services

async def send_addon_notification(user_id: int, title: str, message: str, notification_type: str = "info", **kwargs):
    """
    Placeholder for addon notification function.
    TODO: Implement this in app.services.notification
    """
    logger.info(f"PLACEHOLDER: Would send addon notification to user {user_id}: {title} - {message} (type: {notification_type})")
    # For now, just log the notification instead of failing
    return True

async def send_addon_expiry_email(user_id: int, addon_id: str, expiry_date: str, **kwargs):
    """
    Placeholder for addon expiry email function.
    TODO: Implement this in app.services.email_service
    """
    logger.info(f"PLACEHOLDER: Would send addon expiry email to user {user_id} for addon {addon_id} expiring {expiry_date}")
    # For now, just log the email instead of failing
    return True

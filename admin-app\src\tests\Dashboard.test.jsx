/**
 * Comprehensive test suite for the Enhanced Admin Dashboard
 * 
 * Tests cover:
 * - Component rendering and data display
 * - Real-time data updates
 * - User interactions and navigation
 * - Error handling and loading states
 * - Accessibility compliance
 * - Performance metrics
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

import Dashboard from '../pages/Dashboard';
import { AuthProvider } from '../contexts/AuthContext';
import * as apiManagementService from '../services/apiManagement';

// Mock services
vi.mock('../api', () => ({
  default: {
    get: vi.fn()
  }
}));

vi.mock('../services/apiManagement', () => ({
  apiManagementService: {
    getRealTimeMetrics: vi.fn(),
    getApiRegistry: vi.fn(),
    getUsageTrends: vi.fn()
  }
}));

// Mock theme
const mockTheme = createTheme({
  palette: {
    primary: { main: '#6C4BFA' },
    secondary: { main: '#00E4BC' },
    error: { main: '#FF3D71' },
    warning: { main: '#FFAA00' },
    info: { main: '#4590FF' },
    success: { main: '#00D68F' }
  },
  spacing: 8
});

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={mockTheme}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </ThemeProvider>
  </BrowserRouter>
);

// Mock data
const mockStats = {
  total_users: 1250,
  active_users: 890,
  new_users_today: 15,
  total_coupons: 45,
  total_appsumo_codes: 120,
  active_subscriptions: 340,
  coupons_used_today: 8
};

const mockApiMetrics = {
  timestamp: new Date().toISOString(),
  requests_last_hour: 2500,
  successful_requests: 2375,
  failed_requests: 125,
  success_rate: 95.0,
  error_rate: 5.0,
  avg_response_time_ms: 185.5,
  unique_users_last_hour: 145,
  api_status: {
    active: 25,
    disabled: 2,
    maintenance: 1,
    total: 28
  },
  top_endpoints: [
    {
      endpoint: 'GET /api/content',
      requests: 850,
      avg_response_time: 120.5,
      error_rate: 2.1
    },
    {
      endpoint: 'POST /api/auth/login',
      requests: 420,
      avg_response_time: 95.2,
      error_rate: 1.8
    }
  ]
};

describe('Enhanced Admin Dashboard', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup default mock responses
    const mockApi = require('../api').default;
    mockApi.get.mockImplementation((url) => {
      switch (url) {
        case '/api/admin/stats':
          return Promise.resolve({ data: mockStats });
        case '/api/admin/analytics/users':
          return Promise.resolve({ data: { total_users: 1250 } });
        case '/api/admin/finance/dashboard':
          return Promise.resolve({ data: { current_mrr: 15000 } });
        case '/api/admin/system/health':
          return Promise.resolve({ data: { status: 'healthy' } });
        default:
          return Promise.reject(new Error('Not found'));
      }
    });
    
    apiManagementService.apiManagementService.getRealTimeMetrics.mockResolvedValue(mockApiMetrics);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Component Rendering', () => {
    it('renders dashboard header with title and controls', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Comprehensive platform monitoring and management center')).toBeInTheDocument();
        expect(screen.getByText('Auto Refresh')).toBeInTheDocument();
        expect(screen.getByText('Refresh')).toBeInTheDocument();
      });
    });

    it('renders navigation tabs correctly', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Overview')).toBeInTheDocument();
        expect(screen.getByText('API Health')).toBeInTheDocument();
        expect(screen.getByText('Performance')).toBeInTheDocument();
        expect(screen.getByText('Security')).toBeInTheDocument();
      });
    });

    it('displays key metrics cards with correct data', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Total Users')).toBeInTheDocument();
        expect(screen.getByText('1.3K')).toBeInTheDocument(); // Formatted number
        expect(screen.getByText('Active APIs')).toBeInTheDocument();
        expect(screen.getByText('25')).toBeInTheDocument();
        expect(screen.getByText('Response Time')).toBeInTheDocument();
        expect(screen.getByText('186ms')).toBeInTheDocument();
      });
    });

    it('renders performance chart component', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Performance Trends')).toBeInTheDocument();
        expect(screen.getByText('Real-time system metrics over the last 24 hours')).toBeInTheDocument();
      });
    });

    it('displays API health status card', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('API Health Status')).toBeInTheDocument();
        expect(screen.getByText('Active APIs')).toBeInTheDocument();
        expect(screen.getByText('Error Rate')).toBeInTheDocument();
        expect(screen.getByText('Top Endpoints (Last Hour)')).toBeInTheDocument();
      });
    });
  });

  describe('User Interactions', () => {
    it('handles tab navigation correctly', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        const apiHealthTab = screen.getByText('API Health');
        fireEvent.click(apiHealthTab);
        
        expect(screen.getByText('API Health Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Open API Management')).toBeInTheDocument();
      });
    });

    it('toggles auto-refresh functionality', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        const autoRefreshSwitch = screen.getByRole('checkbox', { name: /auto refresh/i });
        expect(autoRefreshSwitch).toBeChecked();
        
        fireEvent.click(autoRefreshSwitch);
        expect(autoRefreshSwitch).not.toBeChecked();
      });
    });

    it('handles manual refresh button click', async () => {
      const mockApi = require('../api').default;
      
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        const refreshButton = screen.getByText('Refresh');
        fireEvent.click(refreshButton);
        
        // Verify API calls were made
        expect(mockApi.get).toHaveBeenCalledWith('/api/admin/stats');
        expect(apiManagementService.apiManagementService.getRealTimeMetrics).toHaveBeenCalled();
      });
    });

    it('navigates to performance tab and displays chart', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        const performanceTab = screen.getByText('Performance');
        fireEvent.click(performanceTab);
        
        expect(screen.getByText('Performance Analytics')).toBeInTheDocument();
        expect(screen.getByText('Detailed system performance metrics and trends')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('displays error message when API calls fail', async () => {
      const mockApi = require('../api').default;
      mockApi.get.mockRejectedValue(new Error('Network error'));
      
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/Failed to load dashboard data/)).toBeInTheDocument();
      });
    });

    it('handles partial API failures gracefully', async () => {
      const mockApi = require('../api').default;
      mockApi.get.mockImplementation((url) => {
        if (url === '/api/admin/stats') {
          return Promise.resolve({ data: mockStats });
        }
        return Promise.reject(new Error('Service unavailable'));
      });
      
      apiManagementService.apiManagementService.getRealTimeMetrics.mockRejectedValue(new Error('API service down'));
      
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        // Should still display basic stats
        expect(screen.getByText('Total Users')).toBeInTheDocument();
        expect(screen.getByText('1.3K')).toBeInTheDocument();
        
        // Should show warning about unavailable features
        expect(screen.getByText(/Some features may be unavailable/)).toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading spinner during initial data fetch', () => {
      const mockApi = require('../api').default;
      mockApi.get.mockImplementation(() => new Promise(() => {})); // Never resolves
      
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('shows skeleton loaders for individual cards during loading', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      // Initially should show loading state
      const skeletons = screen.getAllByTestId(/skeleton/i);
      expect(skeletons.length).toBeGreaterThan(0);
    });
  });

  describe('Data Formatting', () => {
    it('formats large numbers correctly', async () => {
      const mockApi = require('../api').default;
      mockApi.get.mockResolvedValue({
        data: { ...mockStats, total_users: 1500000 }
      });
      
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('1.5M')).toBeInTheDocument();
      });
    });

    it('formats percentages correctly', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('5.0%')).toBeInTheDocument(); // Error rate
      });
    });

    it('formats response times correctly', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('186ms')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels for interactive elements', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        const refreshButton = screen.getByRole('button', { name: /refresh/i });
        expect(refreshButton).toBeInTheDocument();
        
        const autoRefreshSwitch = screen.getByRole('checkbox', { name: /auto refresh/i });
        expect(autoRefreshSwitch).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation for tabs', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        const tabList = screen.getByRole('tablist');
        expect(tabList).toBeInTheDocument();
        
        const tabs = within(tabList).getAllByRole('tab');
        expect(tabs).toHaveLength(4);
        
        tabs.forEach(tab => {
          expect(tab).toHaveAttribute('tabindex');
        });
      });
    });

    it('provides proper heading hierarchy', async () => {
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        const mainHeading = screen.getByRole('heading', { level: 4, name: /admin dashboard/i });
        expect(mainHeading).toBeInTheDocument();
      });
    });
  });

  describe('Performance', () => {
    it('renders within acceptable time limits', async () => {
      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render within 2 seconds (2000ms)
      expect(renderTime).toBeLessThan(2000);
    });

    it('handles large datasets efficiently', async () => {
      const largeDataset = {
        ...mockStats,
        total_users: 1000000,
        active_users: 750000
      };
      
      const mockApi = require('../api').default;
      mockApi.get.mockResolvedValue({ data: largeDataset });
      
      render(
        <TestWrapper>
          <Dashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('1.0M')).toBeInTheDocument();
        expect(screen.getByText('750.0K')).toBeInTheDocument();
      });
    });
  });
});

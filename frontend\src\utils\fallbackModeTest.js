/**
 * Fallback Mode Test Utility
 * Tests and validates fallback mode functionality
 */

import { 
  isWebSocketSupported, 
  shouldUseFallbackMode, 
  getPlatformConfig,
  logEnvironmentInfo 
} from './environmentDetection';

/**
 * Test fallback mode functionality
 */
export const testFallbackMode = () => {
  // Safety check for browser environment
  if (typeof window === 'undefined') {
    console.log('=== Fallback Mode Test (Server-side) ===');
    return { testPassed: true, note: 'Server-side environment detected' };
  }

  console.log('=== Fallback Mode Test ===');

  try {
    // Log environment information
    logEnvironmentInfo();

    const config = getPlatformConfig();
    const webSocketSupported = isWebSocketSupported();
    const useFallback = shouldUseFallbackMode();
  
  console.log('Environment Test Results:', {
    hostname: window.location.hostname,
    webSocketSupported,
    useFallback,
    config
  });
  
  // Test WebSocket availability
  if (typeof WebSocket === 'undefined') {
    console.warn('❌ WebSocket not available in browser');
  } else {
    console.log('✅ WebSocket available in browser');
  }
  
  // Test serverless platform detection
  const serverlessPlatforms = [
    'vercel.app',
    'vercel.com',
    'netlify.app',
    'netlify.com',
    'railway.app',
    'render.com'
  ];
  
  const isServerless = serverlessPlatforms.some(platform => 
    window.location.hostname.includes(platform)
  );
  
  if (isServerless) {
    console.log('✅ Serverless platform detected - fallback mode should be active');
    if (useFallback) {
      console.log('✅ Fallback mode correctly activated');
    } else {
      console.error('❌ Fallback mode should be active but is not');
    }
  } else {
    console.log('✅ Traditional hosting detected - WebSocket should be available');
    if (!useFallback) {
      console.log('✅ WebSocket mode correctly activated');
    } else {
      console.warn('⚠️ Fallback mode active on traditional hosting');
    }
  }
  
  // Test configuration values
  console.log('Configuration Test:', {
    pollInterval: config.pollInterval,
    enableMetrics: config.enableMetrics,
    enableRealTimeUpdates: config.enableRealTimeUpdates
  });
  
    return {
      webSocketSupported,
      useFallback,
      isServerless,
      config,
      testPassed: isServerless ? useFallback : !useFallback
    };
  } catch (error) {
    console.error('❌ Fallback mode test failed:', error);
    return {
      testPassed: false,
      error: error.message
    };
  }
};

/**
 * Test metrics endpoint availability
 */
export const testMetricsEndpoint = async () => {
  console.log('=== Metrics Endpoint Test ===');
  
  try {
    const response = await fetch('/api/metrics/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Fallback-Mode': shouldUseFallbackMode() ? 'true' : 'false'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Metrics endpoint available:', data);
      return true;
    } else {
      console.warn('⚠️ Metrics endpoint returned:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Metrics endpoint test failed:', error);
    return false;
  }
};

/**
 * Test platform config status endpoint
 */
export const testPlatformConfigEndpoint = async () => {
  console.log('=== Platform Config Endpoint Test ===');
  
  try {
    const response = await fetch('/api/platform-config/status', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Fallback-Mode': shouldUseFallbackMode() ? 'true' : 'false'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Platform config endpoint available:', data);
      return true;
    } else {
      console.warn('⚠️ Platform config endpoint returned:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Platform config endpoint test failed:', error);
    return false;
  }
};

/**
 * Run comprehensive fallback mode tests
 */
export const runFallbackTests = async () => {
  console.log('🧪 Running Comprehensive Fallback Mode Tests...');
  
  const results = {
    environmentTest: testFallbackMode(),
    metricsTest: await testMetricsEndpoint(),
    platformConfigTest: await testPlatformConfigEndpoint()
  };
  
  console.log('📊 Test Results Summary:', results);
  
  const allTestsPassed = results.environmentTest.testPassed && 
                        results.metricsTest && 
                        results.platformConfigTest;
  
  if (allTestsPassed) {
    console.log('🎉 All fallback mode tests passed!');
  } else {
    console.error('❌ Some fallback mode tests failed');
  }
  
  return results;
};

// Auto-run tests in development mode
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // Run tests after a short delay to ensure environment is ready
  setTimeout(() => {
    try {
      runFallbackTests();
    } catch (error) {
      console.warn('Fallback tests failed to run:', error);
    }
  }, 2000);
}

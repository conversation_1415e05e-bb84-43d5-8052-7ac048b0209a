REM @since 2024-1-1 to 2025-25-7
@echo off
echo 🧹 Cleaning up development environment...

REM Clear Vite cache
if exist "node_modules\.vite" (
    echo Clearing Vite cache...
    rmdir /s /q "node_modules\.vite"
    echo ✅ Cleared Vite cache
) else (
    echo ℹ️  Vite cache not found
)

REM Clear build artifacts
if exist "dist" (
    echo Clearing build artifacts...
    rmdir /s /q "dist"
    echo ✅ Cleared build artifacts
) else (
    echo ℹ️  Build artifacts not found
)

REM Clear npm cache
echo Clearing npm cache...
npm cache clean --force
echo ✅ Cleared npm cache

REM Install missing dependencies
echo Installing missing dependencies...
npm install cross-env rimraf
echo ✅ Dependencies installed

echo.
echo 🎉 Cleanup complete!
echo.
echo 📋 Next steps:
echo 1. Run: npm run dev:memory
echo 2. If issues persist, try: npm run dev:minimal
echo 3. For extreme cases, try: npm run clean:all
echo.
pause

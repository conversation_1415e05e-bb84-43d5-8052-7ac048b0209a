// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import CampaignCreatorComponent from '../../components/campaigns/CampaignCreator';
import { Box, CircularProgress } from '@mui/material';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

const CampaignCreator = () => {
  const navigate = useNavigate();
  const { icpId } = useParams();
  const { showErrorNotification } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [icp, setIcp] = useState(null);
  
  // Fetch ICP details
  useEffect(() => {
    const fetchIcp = async () => {
      setLoading(true);
      try {
        const response = await api.get(`/api/icps/${icpId}`);
        setIcp(response.data);
      } catch (error) {
        console.error('Error fetching ICP:', error);
        showErrorNotification('Failed to load ICP details');
        navigate('/services');
      } finally {
        setLoading(false);
      }
    };
    
    if (icpId) {
      fetchIcp();
    }
  }, [icpId, showErrorNotification, navigate]);
  
  // Render loading state
  if (loading) {
    return (
      <Box sx={{ py: 3, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return <CampaignCreatorComponent icp={icp} />;
};

export default CampaignCreator;

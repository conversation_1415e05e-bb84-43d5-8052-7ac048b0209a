/**
 * Tests for AddonsMarketplace component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AddonsMarketplace from '../AddonsMarketplace';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useAuth', () => ({
  useAuth: vi.fn(() => ({
    user: { id: 'test-user', subscription: { plan_id: 'basic' } }
  }))
}));

vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn(),
    showSuccessNotification: vi.fn()
  }))
}));

vi.mock('../../api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn()
  }
}));

vi.mock('../ui/GlassmorphicCard', () => ({
  default: ({ children, ...props }) => <div {...props}>{children}</div>
}));

describe('AddonsMarketplace', () => {
  const mockApi = require('../../api').default;
  const mockNotification = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default API responses
    mockApi.get.mockResolvedValue({
      data: [
        {
          id: 'regeneration_booster',
          name: 'Regeneration Booster Pack',
          description: 'Additional regeneration credits',
          category: 'regeneration',
          price_per_unit: 0.75,
          package_options: [
            { quantity: 10, price: 6.99 },
            { quantity: 25, price: 15.99 }
          ],
          is_popular: true
        },
        {
          id: 'image_pack_basic',
          name: 'Basic Image Pack',
          description: 'Additional image generation credits',
          category: 'image',
          price_per_unit: 0.50,
          package_options: [
            { quantity: 10, price: 4.99 }
          ]
        }
      ]
    });

    mockApi.post.mockResolvedValue({
      data: { success: true, message: 'Purchase successful' }
    });

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue({
      showErrorNotification: mockNotification,
      showSuccessNotification: mockNotification
    });
  });

  test('renders marketplace with addons', async () => {
    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    expect(screen.getByText('Add-ons Marketplace')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Regeneration Booster Pack')).toBeInTheDocument();
      expect(screen.getByText('Basic Image Pack')).toBeInTheDocument();
    });
  });

  test('shows loading state initially', () => {
    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('displays popular badge for popular addons', async () => {
    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Most Popular')).toBeInTheDocument();
    });
  });

  test('opens purchase dialog when purchase button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Regeneration Booster Pack')).toBeInTheDocument();
    });

    const purchaseButtons = screen.getAllByText('Purchase');
    await user.click(purchaseButtons[0]);

    expect(screen.getByText('Purchase Regeneration Booster Pack')).toBeInTheDocument();
    expect(screen.getByText('Select Package')).toBeInTheDocument();
  });

  test('handles purchase flow correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Regeneration Booster Pack')).toBeInTheDocument();
    });

    // Open purchase dialog
    const purchaseButtons = screen.getAllByText('Purchase');
    await user.click(purchaseButtons[0]);

    // Select a package
    const selectElement = screen.getByRole('combobox');
    await user.click(selectElement);
    
    await waitFor(() => {
      expect(screen.getByText('10 credits')).toBeInTheDocument();
    });
    
    await user.click(screen.getByText('10 credits'));

    // Complete purchase
    const finalPurchaseButton = screen.getByRole('button', { name: /purchase/i });
    await user.click(finalPurchaseButton);

    await waitFor(() => {
      expect(mockApi.post).toHaveBeenCalledWith('/api/billing/purchase-addon', {
        addon_id: 'regeneration_booster',
        package_option: 0
      });
    });
  });

  test('handles API error gracefully', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockNotification).toHaveBeenCalledWith('Failed to load add-ons');
    });
  });

  test('shows fallback addons when API returns empty', async () => {
    mockApi.get.mockResolvedValue({ data: [] });

    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Regeneration Booster Pack')).toBeInTheDocument();
      expect(screen.getByText('Basic Image Pack')).toBeInTheDocument();
    });
  });

  test('displays correct pricing information', async () => {
    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('$6.99')).toBeInTheDocument();
      expect(screen.getByText('$4.99')).toBeInTheDocument();
    });
  });

  test('handles purchase error correctly', async () => {
    const user = userEvent.setup();
    mockApi.post.mockResolvedValue({
      data: { success: false, message: 'Payment failed' }
    });

    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Regeneration Booster Pack')).toBeInTheDocument();
    });

    // Open purchase dialog and complete flow
    const purchaseButtons = screen.getAllByText('Purchase');
    await user.click(purchaseButtons[0]);

    const selectElement = screen.getByRole('combobox');
    await user.click(selectElement);
    await user.click(screen.getByText('10 credits'));

    const finalPurchaseButton = screen.getByRole('button', { name: /purchase/i });
    await user.click(finalPurchaseButton);

    await waitFor(() => {
      expect(mockNotification).toHaveBeenCalledWith('Payment failed');
    });
  });

  test('closes purchase dialog when cancel is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AddonsMarketplace />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Regeneration Booster Pack')).toBeInTheDocument();
    });

    // Open purchase dialog
    const purchaseButtons = screen.getAllByText('Purchase');
    await user.click(purchaseButtons[0]);

    expect(screen.getByText('Purchase Regeneration Booster Pack')).toBeInTheDocument();

    // Close dialog
    await user.click(screen.getByText('Cancel'));

    expect(screen.queryByText('Purchase Regeneration Booster Pack')).not.toBeInTheDocument();
  });
});

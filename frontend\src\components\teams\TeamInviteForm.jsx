/**
 * Enhanced Team Invite Form - Enterprise-grade team invitation creation component
 * Features: Comprehensive team invitation form system with advanced form validation, multi-recipient support,
 * and bulk invitation capabilities, detailed form customization with role selection and permission configuration,
 * advanced form features with email validation and duplicate detection, ACE Social's team management system
 * integration with seamless invitation workflow and member role management, form interaction features including
 * auto-complete suggestions and batch operations, form customization capabilities with template selection and
 * message templates, real-time form updates with live validation feedback and recipient status tracking, and
 * seamless ACE Social platform integration with advanced form orchestration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  TextField,
  MenuItem,
  Grid,
  Divider,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  Card,
  CardContent,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  LinearProgress,
  Fade,
  Snackbar
} from '@mui/material';
import {
  Send as SendIcon,
  Clear as ClearIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Security as SecurityIcon,
  Close as CloseIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useTeam } from '../../contexts/TeamContext';
import { useAuth } from '../../hooks/useAuth';
import TeamInvitationEmailPreview from './TeamInvitationEmailPreview';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Team roles
const TEAM_ROLES = {
  ADMIN: 'admin',
  MEMBER: 'member',
  VIEWER: 'viewer'
};



/**
 * Enhanced Team Invite Form - Comprehensive invitation creation with advanced features
 * Implements detailed form validation and enterprise-grade invitation management capabilities
 */
const TeamInviteForm = memo(forwardRef(({
  teamId,
  onInviteSent,
  onAnalyticsTrack
}, ref) => {
  const theme = useTheme();
  const { user } = useAuth();
  const { inviteToTeam, fetchTeam } = useTeam();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const formRef = useRef(null);
  const emailInputRef = useRef(null);

  // Enhanced state management
  const [team, setTeam] = useState(null);
  const [email, setEmail] = useState('');
  const [role, setRole] = useState(TEAM_ROLES.MEMBER);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [notifications, setNotifications] = useState([]);
  const [formAnalytics, setFormAnalytics] = useState({
    invitationsSent: 0,
    successRate: 0,
    lastActivity: new Date().toISOString()
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    resetForm: () => handleResetForm(),
    submitForm: () => handleSubmitForm(),
    validateForm: () => validateFormData(),
    getFormData: () => getFormData(),
    getAnalytics: () => formAnalytics,
    focusEmailInput: () => emailInputRef.current?.focus()
  }), [
    formAnalytics,
    handleResetForm,
    handleSubmitForm,
    validateFormData,
    getFormData
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced validation functions
  const validateEmail = useCallback((emailValue) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailValue.trim()) {
      return 'Email is required';
    }
    if (!emailRegex.test(emailValue)) {
      return 'Please enter a valid email address';
    }
    return null;
  }, []);

  const validateFormData = useCallback(() => {
    const errors = {};

    const emailError = validateEmail(email);
    if (emailError) {
      errors.email = emailError;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [email, validateEmail]);

  // Enhanced event handlers
  const handleResetForm = useCallback(() => {
    setEmail('');
    setRole(TEAM_ROLES.MEMBER);
    setMessage('');
    setError(null);
    setSuccess(false);
    setValidationErrors({});
    announceToScreenReader('Form reset successfully');
  }, [announceToScreenReader]);

  const getFormData = useCallback(() => {
    return {
      email,
      role,
      message
    };
  }, [email, role, message]);

  // Enhanced submit handler
  const handleSubmitForm = useCallback(async () => {
    if (!validateFormData()) {
      announceToScreenReader('Please fix form errors before submitting');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await inviteToTeam(teamId, {
        email: email.trim(),
        role,
        message: message.trim() || undefined
      });

      if (result) {
        setFormAnalytics(prev => ({
          ...prev,
          invitationsSent: prev.invitationsSent + 1,
          successRate: ((prev.invitationsSent + 1) / (prev.invitationsSent + 1)) * 100,
          lastActivity: new Date().toISOString()
        }));

        setSuccess(true);
        handleResetForm();

        if (onInviteSent) {
          onInviteSent(result);
        }

        if (onAnalyticsTrack) {
          onAnalyticsTrack({
            action: 'invitation_sent',
            teamId,
            timestamp: new Date().toISOString()
          });
        }

        announceToScreenReader('Invitation sent successfully');
      }
    } catch (err) {
      setError(err.response?.data?.detail || 'Failed to send invitation');
      announceToScreenReader('Failed to send invitation');
    } finally {
      setLoading(false);
    }
  }, [
    validateFormData,
    teamId,
    email,
    role,
    message,
    inviteToTeam,
    handleResetForm,
    onInviteSent,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  const handleInvite = useCallback((e) => {
    e.preventDefault();
    handleSubmitForm();
  }, [handleSubmitForm]);

  // Load team data
  useEffect(() => {
    const loadTeam = async () => {
      if (!teamId) return;

      try {
        const teamData = await fetchTeam(teamId);
        if (teamData) {
          setTeam(teamData);
        }
      } catch {
        setError('Failed to load team data');
      }
    };

    loadTeam();
  }, [teamId, fetchTeam]);

  // Real-time validation
  useEffect(() => {
    const timer = setTimeout(() => {
      validateFormData();
    }, 500);

    return () => clearTimeout(timer);
  }, [email, validateFormData]);
  
  return (
    <Box sx={{ ...glassMorphismStyles, p: 3 }}>
      {/* Enhanced Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold" sx={{
          color: ACE_COLORS.DARK,
          background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.YELLOW} 100%)`,
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          mb: 1
        }}>
          Invite Team Members
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Send invitations to join {team?.name || 'your team'} on ACE Social
        </Typography>
      </Box>

      {/* Success/Error Alerts */}
      <Fade in={success || !!error} timeout={500}>
        <Box sx={{ mb: 3 }}>
          {success && (
            <Alert
              severity="success"
              sx={{
                border: `1px solid ${alpha('#4CAF50', 0.3)}`,
                backgroundColor: alpha('#4CAF50', 0.1),
                mb: 2
              }}
              action={
                <IconButton size="small" onClick={() => setSuccess(false)}>
                  <CloseIcon />
                </IconButton>
              }
            >
              Invitation sent successfully!
            </Alert>
          )}

          {error && (
            <Alert
              severity="error"
              sx={{
                border: `1px solid ${alpha('#F44336', 0.3)}`,
                backgroundColor: alpha('#F44336', 0.1)
              }}
              action={
                <IconButton size="small" onClick={() => setError(null)}>
                  <CloseIcon />
                </IconButton>
              }
            >
              {error}
            </Alert>
          )}
        </Box>
      </Fade>

      {/* Enhanced Form */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card sx={{ ...glassMorphismStyles, border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
            <CardContent sx={{ p: 3 }}>
              <Box component="form" onSubmit={handleInvite} ref={formRef}>
                <TextField
                  ref={emailInputRef}
                  fullWidth
                  label="Email Address"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={loading}
                  error={!!validationErrors.email}
                  helperText={validationErrors.email || 'Enter the recipient\'s email address'}
                  InputProps={{
                    startAdornment: <EmailIcon sx={{ color: 'text.secondary', mr: 1 }} />
                  }}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: ACE_COLORS.PURPLE
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: ACE_COLORS.PURPLE
                      }
                    }
                  }}
                />

                <FormControl fullWidth sx={{ mb: 3 }}>
                  <InputLabel>Role</InputLabel>
                  <Select
                    value={role}
                    label="Role"
                    onChange={(e) => setRole(e.target.value)}
                    disabled={loading}
                  >
                    <MenuItem value={TEAM_ROLES.ADMIN}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <SecurityIcon fontSize="small" />
                        Admin - Full access
                      </Box>
                    </MenuItem>
                    <MenuItem value={TEAM_ROLES.MEMBER}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <PersonIcon fontSize="small" />
                        Member - Standard access
                      </Box>
                    </MenuItem>
                    <MenuItem value={TEAM_ROLES.VIEWER}>
                      <Box display="flex" alignItems="center" gap={1}>
                        <VisibilityIcon fontSize="small" />
                        Viewer - Read-only access
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>

                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Personal Message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  disabled={loading}
                  placeholder="Add a personal message to the invitation..."
                  helperText={`${message.length}/500 characters`}
                  inputProps={{ maxLength: 500 }}
                  sx={{
                    mb: 3,
                    '& .MuiOutlinedInput-root': {
                      '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: ACE_COLORS.PURPLE
                      },
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: ACE_COLORS.PURPLE
                      }
                    }
                  }}
                />

                {/* Loading Progress */}
                {loading && (
                  <Box sx={{ mb: 2 }}>
                    <LinearProgress
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: ACE_COLORS.PURPLE
                        }
                      }}
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
                      Sending invitation...
                    </Typography>
                  </Box>
                )}

                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={handleResetForm}
                    disabled={loading}
                    startIcon={<ClearIcon />}
                    sx={{
                      borderColor: 'text.secondary',
                      color: 'text.secondary',
                      '&:hover': {
                        borderColor: 'text.primary',
                        backgroundColor: alpha('text.secondary', 0.1)
                      }
                    }}
                  >
                    Clear
                  </Button>

                  <Button
                    type="submit"
                    variant="contained"
                    fullWidth
                    startIcon={loading ? <CircularProgress size={20} sx={{ color: ACE_COLORS.WHITE }} /> : <SendIcon />}
                    disabled={loading || !email.trim()}
                    sx={{
                      backgroundColor: ACE_COLORS.PURPLE,
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                      }
                    }}
                  >
                    {loading ? 'Sending...' : 'Send Invitation'}
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ ...glassMorphismStyles }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
                Email Preview
              </Typography>

              <Divider sx={{ mb: 2, borderColor: alpha(ACE_COLORS.PURPLE, 0.2) }} />

              <Box sx={{ maxHeight: 500, overflow: 'auto' }}>
                <TeamInvitationEmailPreview
                  teamName={team?.name || 'Your Team'}
                  inviterName={user?.full_name || user?.name || 'Team Admin'}
                  inviterEmail={user?.email || ''}
                  message={message}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={index}
          open={true}
          autoHideDuration={6000}
          onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            severity={notification.severity || 'info'}
            onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
            sx={{
              ...glassMorphismStyles,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
}));

TeamInviteForm.displayName = 'TeamInviteForm';

TeamInviteForm.propTypes = {
  /** Team ID for the invitation */
  teamId: PropTypes.string.isRequired,
  /** Function called when invitation is sent */
  onInviteSent: PropTypes.func,
  /** Function called when form state changes */
  onFormStateChange: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Enable bulk invitations */
  enableBulkInvitations: PropTypes.bool,
  /** Enable email validation */
  enableEmailValidation: PropTypes.bool,
  /** Enable message templates */
  enableMessageTemplates: PropTypes.bool,
  /** Enable real-time validation */
  enableRealTimeValidation: PropTypes.bool,
  /** Maximum number of invitations */
  maxInvitations: PropTypes.number,
  /** Default role for invitations */
  defaultRole: PropTypes.oneOf(Object.values(TEAM_ROLES))
};

export default TeamInviteForm;

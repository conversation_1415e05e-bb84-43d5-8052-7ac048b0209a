# E-commerce Integration API Reference

## Overview

This document provides comprehensive API reference for the e-commerce integration endpoints. All endpoints require authentication and follow RESTful conventions.

## Authentication

All API requests must include a valid JWT token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## Base URL

```
https://api.your-domain.com/api/ecommerce
```

## Store Management

### Get User Stores

Retrieve all e-commerce stores connected by the current user.

**Endpoint:** `GET /stores`

**Response:**
```json
[
  {
    "id": "store_id",
    "platform": "shopify",
    "store_name": "My Store",
    "store_url": "https://my-store.myshopify.com",
    "status": "connected",
    "last_sync_at": "2023-12-01T12:00:00Z",
    "total_products": 150,
    "synced_products": 145,
    "sync_enabled": true,
    "created_at": "2023-11-01T10:00:00Z",
    "last_error": null
  }
]
```

**Status Codes:**
- `200`: Success
- `401`: Unauthorized
- `500`: Internal server error

### Connect Store

Connect a new e-commerce store to the platform.

**Endpoint:** `POST /stores/connect`

**Required Feature:** `ecommerce_integration`

**Request Body:**
```json
{
  "platform": "shopify",
  "authorization_code": "auth_code_from_oauth",
  "state": "oauth_state_parameter",
  "redirect_uri": "https://your-app.com/callback",
  "store_url": "https://store.myshopify.com"
}
```

**Response:**
```json
{
  "success": true,
  "store_id": "new_store_id",
  "store_name": "Connected Store",
  "platform": "shopify",
  "status": "connected"
}
```

**Status Codes:**
- `200`: Store connected successfully
- `400`: Invalid request parameters
- `401`: Unauthorized
- `403`: Feature not available
- `500`: Connection failed

### Disconnect Store

Remove a store connection and all associated data.

**Endpoint:** `DELETE /stores/{store_id}`

**Response:**
```json
{
  "success": true,
  "message": "Store disconnected successfully"
}
```

**Status Codes:**
- `200`: Store disconnected
- `404`: Store not found
- `401`: Unauthorized
- `500`: Internal server error

### Sync Store Products

Manually trigger product synchronization for a store.

**Endpoint:** `POST /stores/{store_id}/sync`

**Request Body (Optional):**
```json
{
  "force_full_sync": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Product sync started",
  "sync_type": "incremental"
}
```

**Status Codes:**
- `200`: Sync started
- `404`: Store not found
- `401`: Unauthorized
- `500`: Sync failed to start

## Product Management

### Get Store Products

Retrieve products from a connected store with pagination and filtering.

**Endpoint:** `GET /stores/{store_id}/products`

**Query Parameters:**
- `limit` (optional): Number of products to return (default: 50, max: 100)
- `offset` (optional): Number of products to skip (default: 0)

**Response:**
```json
{
  "products": [
    {
      "id": "product_id",
      "external_product_id": "12345",
      "title": "Product Name",
      "description": "Product description",
      "price": 99.99,
      "compare_at_price": 119.99,
      "currency": "USD",
      "sku": "PROD-SKU",
      "vendor": "Brand Name",
      "category": "Electronics",
      "tags": ["tag1", "tag2"],
      "featured_image": "https://example.com/image.jpg",
      "status": "active",
      "inventory_quantity": 50,
      "last_synced_at": "2023-12-01T12:00:00Z",
      "content_generated": false,
      "content_generation_count": 0
    }
  ],
  "total": 150,
  "limit": 50,
  "offset": 0,
  "has_more": true
}
```

**Status Codes:**
- `200`: Success
- `404`: Store not found
- `401`: Unauthorized
- `500`: Internal server error

### Generate Product ICPs

Generate Ideal Customer Profiles based on product data.

**Endpoint:** `POST /stores/{store_id}/products/generate-icps`

**Required Feature:** `icp_generation`

**Request Body:**
```json
{
  "product_ids": ["product1", "product2"],
  "category": "Electronics",
  "count": 3
}
```

**Response:**
```json
{
  "success": true,
  "icps": [
    {
      "id": "icp_id",
      "name": "Tech Enthusiast",
      "description": "Early adopters of technology products",
      "demographics": {
        "age_range": "25-40",
        "gender": "Mixed",
        "income_range": "$60,000-$120,000",
        "education": "College educated",
        "location": "Urban areas",
        "occupation": "Technology professional"
      },
      "decision_maker": {
        "title": "Primary buyer",
        "department": "Personal",
        "influence_level": "High",
        "decision_criteria": ["Innovation", "Quality", "Reviews"]
      },
      "pain_points": [
        {
          "description": "Keeping up with latest technology",
          "severity": "Medium",
          "frequency": "Regular"
        }
      ],
      "goals": [
        {
          "description": "Stay current with technology trends",
          "priority": "High",
          "timeline": "Ongoing"
        }
      ],
      "objections": [
        {
          "objection": "Price concerns",
          "response": "Emphasize long-term value and ROI"
        }
      ],
      "content_preferences": [
        {
          "type": "Video",
          "platform": "YouTube",
          "engagement_level": "High"
        }
      ],
      "buying_process": "Research extensively, read reviews, compare options",
      "is_ai_generated": true
    }
  ],
  "count": 1
}
```

**Status Codes:**
- `200`: ICPs generated successfully
- `400`: Invalid request parameters
- `401`: Unauthorized
- `403`: Feature not available
- `404`: Store not found
- `500`: Generation failed

## Campaign Management

### Create Product Campaign

Create a new campaign focused on specific products.

**Endpoint:** `POST /campaigns/product-campaign`

**Required Feature:** `campaign_management`

**Request Body:**
```json
{
  "campaign_data": {
    "name": "Holiday Product Campaign",
    "description": "Promote holiday products",
    "start_date": "2023-12-01T00:00:00Z",
    "end_date": "2023-12-31T23:59:59Z",
    "platforms": ["instagram", "facebook"],
    "goals": [
      {
        "type": "conversions",
        "target": 100,
        "metric": "purchases"
      }
    ],
    "icp_id": "target_icp_id",
    "budget": "1000.00",
    "target_roas": 300.0
  },
  "product_ids": ["product1", "product2", "product3"],
  "store_id": "store_id"
}
```

**Response:**
```json
{
  "success": true,
  "campaign_id": "new_campaign_id",
  "campaign_name": "Holiday Product Campaign",
  "products_count": 3
}
```

**Status Codes:**
- `200`: Campaign created successfully
- `400`: Invalid request parameters
- `401`: Unauthorized
- `403`: Feature not available
- `500`: Campaign creation failed

### Generate Campaign Content

Generate AI-powered content for campaign products.

**Endpoint:** `POST /campaigns/{campaign_id}/generate-content`

**Required Feature:** `content_generation`

**Request Body:**
```json
{
  "content_requests": [
    {
      "product_id": "product1",
      "topic": "Promote wireless headphones",
      "platform": "instagram",
      "content_type": "post",
      "tone": "engaging",
      "include_pricing": true,
      "call_to_action": "Shop now",
      "target_audience": "Music lovers aged 20-35"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "generated_content": [
    {
      "id": "content_id",
      "text": "🎵 Experience premium sound with our wireless headphones! Perfect for music lovers who demand quality...",
      "images": ["https://example.com/generated-image.jpg"],
      "platform": "instagram",
      "product_id": "product1",
      "external_product_id": "12345",
      "campaign_id": "campaign_id"
    }
  ],
  "count": 1
}
```

**Status Codes:**
- `200`: Content generated successfully
- `400`: Invalid request parameters
- `401`: Unauthorized
- `403`: Feature not available
- `404`: Campaign not found
- `500`: Content generation failed

## Monitoring

### Get Sync Status

Retrieve synchronization status for all user stores.

**Endpoint:** `GET /sync/status`

**Response:**
```json
{
  "total_stores": 3,
  "syncing": 1,
  "completed": 2,
  "failed": 0,
  "stores": [
    {
      "id": "store1",
      "name": "Store 1",
      "platform": "shopify",
      "status": "sync_completed",
      "last_sync_at": "2023-12-01T12:00:00Z",
      "total_products": 100,
      "synced_products": 100,
      "last_error": null
    },
    {
      "id": "store2",
      "name": "Store 2",
      "platform": "woocommerce",
      "status": "syncing",
      "last_sync_at": "2023-12-01T11:30:00Z",
      "total_products": 250,
      "synced_products": 200,
      "last_error": null
    }
  ]
}
```

**Status Codes:**
- `200`: Success
- `401`: Unauthorized
- `500`: Internal server error

## Webhooks

### Handle Platform Webhooks

Receive and process webhooks from e-commerce platforms.

**Endpoint:** `POST /webhooks/{platform}`

**Supported Platforms:** `shopify`, `woocommerce`

**Headers:**
- `X-Shopify-Hmac-Sha256`: Shopify webhook signature
- `X-WC-Webhook-Signature`: WooCommerce webhook signature

**Request Body:** Platform-specific webhook payload

**Response:**
```json
{
  "status": "ok"
}
```

**Status Codes:**
- `200`: Webhook processed successfully
- `400`: Invalid webhook payload or signature
- `500`: Webhook processing failed

## Error Handling

### Error Response Format

All error responses follow this format:

```json
{
  "detail": "Error description",
  "error_code": "SPECIFIC_ERROR_CODE",
  "timestamp": "2023-12-01T12:00:00Z"
}
```

### Common Error Codes

- `STORE_NOT_FOUND`: Requested store does not exist or is not accessible
- `INSUFFICIENT_CREDITS`: User has insufficient add-on credits
- `SYNC_IN_PROGRESS`: Store sync is already in progress
- `INVALID_PLATFORM`: Unsupported e-commerce platform
- `WEBHOOK_VERIFICATION_FAILED`: Webhook signature verification failed
- `RATE_LIMIT_EXCEEDED`: API rate limit exceeded
- `FEATURE_NOT_AVAILABLE`: Required feature not available in user's plan

## Rate Limiting

API endpoints are rate limited to ensure fair usage:

- **Store Management**: 100 requests per hour per user
- **Product Operations**: 1000 requests per hour per user
- **Content Generation**: 500 requests per hour per user
- **Webhooks**: No rate limit (platform-controlled)

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1701432000
```

## SDKs and Libraries

### JavaScript/Node.js
```javascript
import { EcommerceAPI } from '@ace-social/ecommerce-sdk';

const api = new EcommerceAPI({
  baseURL: 'https://api.your-domain.com',
  apiKey: 'your-api-key'
});

// Connect a store
const store = await api.stores.connect({
  platform: 'shopify',
  authorizationCode: 'code',
  state: 'state',
  redirectUri: 'callback-url'
});
```

### Python
```python
from ace_social_ecommerce import EcommerceClient

client = EcommerceClient(
    base_url='https://api.your-domain.com',
    api_key='your-api-key'
)

# Get stores
stores = client.stores.list()

# Sync products
client.stores.sync_products(store_id='store123')
```

## Support

For API support:
- **Documentation**: [Link to full documentation]
- **Support Email**: <EMAIL>
- **Status Page**: [Link to status page]
- **Rate Limit Issues**: Contact support for rate limit increases

/**
 * API Management Service
 * 
 * Provides comprehensive API management functionality including:
 * - API registry management
 * - Real-time analytics and monitoring
 * - Error pattern detection
 * - Performance insights
 * - User behavior analytics
 * - Bulk operations
 */

import api from '../api/index.js';

class ApiManagementService {
  constructor() {
    this.baseUrl = '/api/admin/api-management';
    this.cache = new Map();
    this.cacheTimeout = 60000; // 1 minute cache
  }

  /**
   * Get cached data if available and not expired
   */
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * Set cached data with timestamp
   */
  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Clear cache for specific key or all cache
   */
  clearCache(key = null) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get complete API registry with status and metrics
   */
  async getApiRegistry(useCache = true) {
    const cacheKey = 'api-registry';
    
    if (useCache) {
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached;
    }

    try {
      const response = await api.get(`${this.baseUrl}/registry`, {
        params: { use_cache: useCache }
      });
      
      const data = response.data;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to get API registry:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load API registry');
    }
  }

  /**
   * Get real-time API metrics
   */
  async getRealTimeMetrics() {
    try {
      const response = await api.get(`${this.baseUrl}/analytics/realtime`);
      return response.data;
    } catch (error) {
      console.error('Failed to get real-time metrics:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load real-time metrics');
    }
  }

  /**
   * Get API usage trends over time
   */
  async getUsageTrends(hours = 24, intervalMinutes = 60) {
    const cacheKey = `usage-trends-${hours}-${intervalMinutes}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get(`${this.baseUrl}/analytics/trends`, {
        params: {
          hours,
          interval_minutes: intervalMinutes
        }
      });
      
      const data = response.data;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to get usage trends:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load usage trends');
    }
  }

  /**
   * Get error patterns and anomalies
   */
  async getErrorPatterns(hours = 24) {
    try {
      const response = await api.get(`${this.baseUrl}/analytics/errors`, {
        params: { hours }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get error patterns:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load error patterns');
    }
  }

  /**
   * Get performance insights and recommendations
   */
  async getPerformanceInsights(hours = 24) {
    try {
      const response = await api.get(`${this.baseUrl}/analytics/insights`, {
        params: { hours }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get performance insights:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load performance insights');
    }
  }

  /**
   * Get user behavior analytics
   */
  async getUserBehavior(hours = 24) {
    try {
      const response = await api.get(`${this.baseUrl}/analytics/users`, {
        params: { hours }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get user behavior analytics:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load user behavior analytics');
    }
  }

  /**
   * Update status of a specific API endpoint
   */
  async updateApiStatus(method, path, status, reason = null) {
    try {
      // Ensure path is properly encoded
      const encodedPath = path.startsWith('/') ? path.slice(1) : path;
      
      const response = await api.put(
        `${this.baseUrl}/status/${method}/${encodedPath}`,
        {
          status,
          reason
        }
      );
      
      // Clear relevant cache
      this.clearCache('api-registry');
      
      return response.data;
    } catch (error) {
      console.error('Failed to update API status:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update API status');
    }
  }

  /**
   * Bulk update API endpoint statuses
   */
  async bulkUpdateApiStatus(endpoints, reason = null) {
    try {
      const response = await api.post(`${this.baseUrl}/bulk/status`, {
        endpoints,
        reason
      });
      
      // Clear relevant cache
      this.clearCache('api-registry');
      
      return response.data;
    } catch (error) {
      console.error('Failed to bulk update API status:', error);
      throw new Error(error.response?.data?.detail || 'Failed to perform bulk update');
    }
  }

  /**
   * Trigger health check for API endpoints
   */
  async triggerHealthCheck(endpoints = null) {
    try {
      const params = {};
      if (endpoints && endpoints.length > 0) {
        params.endpoints = endpoints;
      }

      const response = await api.post(`${this.baseUrl}/health/check`, null, {
        params
      });
      
      // Clear relevant cache
      this.clearCache('api-registry');
      
      return response.data;
    } catch (error) {
      console.error('Failed to trigger health check:', error);
      throw new Error(error.response?.data?.detail || 'Failed to perform health check');
    }
  }

  /**
   * Export API data in various formats
   */
  async exportApiData(format = 'json', options = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/export`, {
        format,
        include_metrics: options.includeMetrics || true,
        include_errors: options.includeErrors || false,
        date_range_hours: options.dateRangeHours || 24
      });
      
      return response.data;
    } catch (error) {
      console.error('Failed to export API data:', error);
      throw new Error(error.response?.data?.detail || 'Failed to export API data');
    }
  }

  /**
   * Get API management system information
   */
  async getSystemInfo() {
    try {
      const response = await api.get(this.baseUrl);
      return response.data;
    } catch (error) {
      console.error('Failed to get system info:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load system information');
    }
  }

  /**
   * Search APIs by various criteria
   */
  async searchApis(query, filters = {}) {
    try {
      const apis = await this.getApiRegistry();
      
      let filteredApis = apis;
      
      // Apply text search
      if (query) {
        const searchTerm = query.toLowerCase();
        filteredApis = filteredApis.filter(api => 
          api.path.toLowerCase().includes(searchTerm) ||
          api.method.toLowerCase().includes(searchTerm) ||
          api.name.toLowerCase().includes(searchTerm) ||
          api.description.toLowerCase().includes(searchTerm) ||
          api.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }
      
      // Apply status filter
      if (filters.status) {
        filteredApis = filteredApis.filter(api => api.status === filters.status);
      }
      
      // Apply health status filter
      if (filters.healthStatus) {
        filteredApis = filteredApis.filter(api => api.health_status === filters.healthStatus);
      }
      
      // Apply method filter
      if (filters.method) {
        filteredApis = filteredApis.filter(api => api.method === filters.method);
      }
      
      // Apply admin-only filter
      if (filters.adminOnly !== undefined) {
        filteredApis = filteredApis.filter(api => api.is_admin_only === filters.adminOnly);
      }
      
      // Apply response time filter
      if (filters.maxResponseTime) {
        filteredApis = filteredApis.filter(api => api.response_time_ms <= filters.maxResponseTime);
      }
      
      // Apply error rate filter
      if (filters.maxErrorRate) {
        filteredApis = filteredApis.filter(api => api.error_rate <= filters.maxErrorRate);
      }
      
      return filteredApis;
    } catch (error) {
      console.error('Failed to search APIs:', error);
      throw new Error('Failed to search APIs');
    }
  }

  /**
   * Get API statistics summary
   */
  async getApiStatistics() {
    try {
      const apis = await this.getApiRegistry();
      const realTimeMetrics = await this.getRealTimeMetrics();
      
      const stats = {
        total_apis: apis.length,
        active_apis: apis.filter(api => api.status === 'active').length,
        disabled_apis: apis.filter(api => api.status === 'disabled').length,
        maintenance_apis: apis.filter(api => api.status === 'maintenance').length,
        healthy_apis: apis.filter(api => api.health_status === 'healthy').length,
        degraded_apis: apis.filter(api => api.health_status === 'degraded').length,
        down_apis: apis.filter(api => api.health_status === 'down').length,
        admin_only_apis: apis.filter(api => api.is_admin_only).length,
        public_apis: apis.filter(api => !api.requires_auth).length,
        avg_response_time: apis.reduce((sum, api) => sum + api.response_time_ms, 0) / apis.length,
        avg_success_rate: apis.reduce((sum, api) => sum + api.success_rate, 0) / apis.length,
        total_requests: apis.reduce((sum, api) => sum + api.total_requests, 0),
        total_failed_requests: apis.reduce((sum, api) => sum + api.failed_requests, 0),
        realtime_metrics: realTimeMetrics
      };
      
      return stats;
    } catch (error) {
      console.error('Failed to get API statistics:', error);
      throw new Error('Failed to load API statistics');
    }
  }

  /**
   * Get trending APIs based on usage
   */
  async getTrendingApis(limit = 10) {
    try {
      const apis = await this.getApiRegistry();
      
      // Sort by total requests and recent activity
      const trending = apis
        .filter(api => api.total_requests > 0)
        .sort((a, b) => {
          // Weight recent activity more heavily
          const aScore = a.total_requests + (a.success_rate / 100) * 1000;
          const bScore = b.total_requests + (b.success_rate / 100) * 1000;
          return bScore - aScore;
        })
        .slice(0, limit);
      
      return trending;
    } catch (error) {
      console.error('Failed to get trending APIs:', error);
      throw new Error('Failed to load trending APIs');
    }
  }
}

// Create and export singleton instance
export const apiManagementService = new ApiManagementService();
export default apiManagementService;

import {
  Box,
  Typography,
  LinearProgress,
  Chip,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import PropTypes from 'prop-types';

const UsageIndicator = ({
  current,
  total,
  label = "Usage",
  showAlert = true,
  size = "medium"
}) => {
  // Handle unlimited usage
  if (total === -1) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <CheckCircleIcon color="success" fontSize="small" />
        <Typography variant="body2" color="success.main">
          {label}: Unlimited
        </Typography>
      </Box>
    );
  }

  // Calculate percentage
  const percentage = total > 0 ? Math.min((current / total) * 100, 100) : 0;
  const remaining = Math.max(total - current, 0);

  // Determine status and color
  let status = 'normal';
  let color = 'primary';
  let alertSeverity = 'info';
  let icon = null;

  if (percentage >= 95) {
    status = 'critical';
    color = 'error';
    alertSeverity = 'error';
    icon = <ErrorIcon fontSize="small" />;
  } else if (percentage >= 75) {
    status = 'warning';
    color = 'warning';
    alertSeverity = 'warning';
    icon = <WarningIcon fontSize="small" />;
  } else {
    icon = <CheckCircleIcon fontSize="small" />;
  }

  const getProgressColor = () => {
    if (percentage >= 95) return 'error';
    if (percentage >= 75) return 'warning';
    return 'primary';
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const getAlertMessage = () => {
    if (percentage >= 95) {
      return `You've used ${percentage.toFixed(1)}% of your ${label.toLowerCase()}. Consider upgrading to avoid interruptions.`;
    }
    if (percentage >= 75) {
      return `You've used ${percentage.toFixed(1)}% of your ${label.toLowerCase()}. You may want to consider upgrading soon.`;
    }
    return null;
  };

  return (
    <Box sx={{ width: '100%' }}>
      {/* Usage Progress */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        {icon}
        <Typography 
          variant={size === 'small' ? 'caption' : 'body2'} 
          sx={{ flexGrow: 1 }}
        >
          {label}
        </Typography>
        <Chip
          label={`${formatNumber(current)} / ${formatNumber(total)}`}
          size={size === 'small' ? 'small' : 'medium'}
          color={color}
          variant={percentage >= 75 ? 'filled' : 'outlined'}
        />
      </Box>

      {/* Progress Bar */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <LinearProgress
          variant="determinate"
          value={percentage}
          color={getProgressColor()}
          sx={{
            flexGrow: 1,
            height: size === 'small' ? 4 : 6,
            borderRadius: 2,
            backgroundColor: 'rgba(0, 0, 0, 0.1)'
          }}
        />
        <Typography 
          variant={size === 'small' ? 'caption' : 'body2'} 
          color="text.secondary"
          sx={{ minWidth: 40, textAlign: 'right' }}
        >
          {percentage.toFixed(0)}%
        </Typography>
      </Box>

      {/* Remaining Credits */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
        <Typography variant="caption" color="text.secondary">
          {remaining} remaining
        </Typography>
        {percentage >= 75 && (
          <Typography variant="caption" color={color + '.main'}>
            {status === 'critical' ? 'Almost depleted' : 'Running low'}
          </Typography>
        )}
      </Box>

      {/* Alert for high usage */}
      {showAlert && percentage >= 75 && (
        <Alert 
          severity={alertSeverity} 
          sx={{ mt: 1, fontSize: '0.75rem' }}
          variant="outlined"
        >
          {getAlertMessage()}
        </Alert>
      )}
    </Box>
  );
};

// Compact version for inline display
export const CompactUsageIndicator = ({ current, total, label }) => {
  if (total === -1) {
    return (
      <Tooltip title={`${label}: Unlimited`}>
        <Chip 
          label="∞" 
          size="small" 
          color="success" 
          variant="outlined"
        />
      </Tooltip>
    );
  }

  const percentage = total > 0 ? Math.min((current / total) * 100, 100) : 0;
  const remaining = Math.max(total - current, 0);

  let color = 'primary';
  if (percentage >= 95) color = 'error';
  else if (percentage >= 75) color = 'warning';

  return (
    <Tooltip title={`${label}: ${current}/${total} used (${remaining} remaining)`}>
      <Chip 
        label={`${current}/${total}`}
        size="small" 
        color={color}
        variant={percentage >= 75 ? 'filled' : 'outlined'}
      />
    </Tooltip>
  );
};

// Usage ring indicator for dashboard widgets
export const RingUsageIndicator = ({ current, total, label, size = 60 }) => {
  if (total === -1) {
    return (
      <Box 
        sx={{ 
          width: size, 
          height: size, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          border: '2px solid',
          borderColor: 'success.main',
          borderRadius: '50%'
        }}
      >
        <Typography variant="caption" color="success.main" fontWeight="bold">
          ∞
        </Typography>
      </Box>
    );
  }

  const percentage = total > 0 ? Math.min((current / total) * 100, 100) : 0;
  const circumference = 2 * Math.PI * (size / 2 - 4);
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  let color = '#1976d2'; // primary
  if (percentage >= 95) color = '#d32f2f'; // error
  else if (percentage >= 75) color = '#ed6c02'; // warning

  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <svg width={size} height={size}>
        <circle
          cx={size / 2}
          cy={size / 2}
          r={size / 2 - 4}
          stroke="rgba(0, 0, 0, 0.1)"
          strokeWidth="3"
          fill="transparent"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={size / 2 - 4}
          stroke={color}
          strokeWidth="3"
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
          style={{
            transition: 'stroke-dashoffset 0.3s ease-in-out'
          }}
        />
      </svg>
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column'
        }}
      >
        <Typography variant="caption" fontWeight="bold" color={color}>
          {percentage.toFixed(0)}%
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.6rem' }}>
          {label}
        </Typography>
      </Box>
    </Box>
  );
};

// PropTypes for type checking
UsageIndicator.propTypes = {
  current: PropTypes.number.isRequired,
  total: PropTypes.number.isRequired,
  label: PropTypes.string,
  showAlert: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium', 'large'])
};

UsageIndicator.defaultProps = {
  label: "Usage",
  showAlert: true,
  size: "medium"
};

CompactUsageIndicator.propTypes = {
  current: PropTypes.number.isRequired,
  total: PropTypes.number.isRequired,
  label: PropTypes.string.isRequired
};

RingUsageIndicator.propTypes = {
  current: PropTypes.number.isRequired,
  total: PropTypes.number.isRequired,
  label: PropTypes.string.isRequired,
  size: PropTypes.number
};

RingUsageIndicator.defaultProps = {
  size: 60
};

export default UsageIndicator;

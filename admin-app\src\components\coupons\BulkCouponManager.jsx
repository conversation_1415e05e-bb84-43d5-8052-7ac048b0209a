/**
 * Enhanced ACE Social Bulk Coupon Manager - Enterprise-grade bulk coupon management component
 * Features: Comprehensive bulk coupon management with advanced coupon generation, validation,
 * and distribution capabilities for ACE Social promotional campaigns, detailed coupon analytics
 * with usage tracking and redemption rate analysis, advanced coupon management features with
 * bulk operations and expiration handling, ACE Social's coupon system integration with seamless
 * coupon lifecycle management, coupon interaction features including bulk generation and
 * validation workflows, coupon state management with real-time validation updates and usage
 * tracking, and real-time coupon monitoring with live usage displays and automatic optimization
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Grid,
  Card,
  CardContent,
  Checkbox,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Collapse,
  Divider,
  Badge,
  Snackbar,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide,
  Switch,
  Tabs,
  Tab,
  Stack
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  ContentCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
  Timeline as TimelineIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Edit as EditIcon,
  MoreVert as MoreVertIcon,
  Sync as SyncIcon,
  NotificationsActive as NotificationsIcon,
  Speed as SpeedIcon,
  Verified as VerifiedIcon,
  Block as BlockIcon,
  History as HistoryIcon,
  LocalOffer as CouponIcon,
  Campaign as CampaignIcon,
  MonetizationOn as MoneyIcon,
  Schedule as ScheduleIcon,
  Group as GroupIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { validateFormData, generateCouponCode, exportToCSV, copyToClipboard } from '../../utils/couponHelpers';
import api from '../../api';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Bulk coupon management constants
const OPERATION_MODES = {
  GENERATE: 'generate',
  MANAGE: 'manage',
  ANALYZE: 'analyze'
};

const BULK_OPERATIONS = {
  ACTIVATE: 'activate',
  DEACTIVATE: 'deactivate',
  DELETE: 'delete',
  EXPORT: 'export',
  DUPLICATE: 'duplicate',
  ARCHIVE: 'archive'
};

const GENERATION_STRATEGIES = {
  SEQUENTIAL: 'sequential',
  RANDOM: 'random',
  PATTERN: 'pattern',
  CUSTOM: 'custom'
};

const VALIDATION_LEVELS = {
  BASIC: 'basic',
  ADVANCED: 'advanced',
  STRICT: 'strict'
};

// Coupon analytics events
const COUPON_ANALYTICS_EVENTS = {
  BULK_GENERATION_STARTED: 'bulk_generation_started',
  BULK_GENERATION_COMPLETED: 'bulk_generation_completed',
  BULK_OPERATION_EXECUTED: 'bulk_operation_executed',
  COUPON_VALIDATED: 'coupon_validated',
  EXPORT_INITIATED: 'export_initiated',
  ANALYTICS_VIEWED: 'analytics_viewed'
};

/**
 * Enhanced Bulk Coupon Manager - Comprehensive bulk coupon management with advanced features
 * Implements detailed coupon generation and enterprise-grade management capabilities
 */
const EnhancedBulkCouponManager = memo(forwardRef(({
  open,
  onClose,
  coupons = [],
  onCouponsUpdated,
  mode = OPERATION_MODES.GENERATE,
  enableAdvancedFeatures = true,
  enableRealTimeValidation = true,
  enableAnalytics = true,
  enableAccessibility = true,
  enableBulkOperations = true,
  maxCouponsPerBatch = 1000,
  batchSize = 50,
  validationLevel = VALIDATION_LEVELS.ADVANCED,
  generationStrategy = GENERATION_STRATEGIES.RANDOM,
  onGenerationStart,
  onGenerationProgress,
  onGenerationComplete,
  onBulkOperationComplete,
  onAnalyticsTrack,
  onValidationResult,
  className,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const dialogRef = useRef(null);
  const formRef = useRef(null);
  const stepperRef = useRef(null);

  // Core state management
  const [activeStep, setActiveStep] = useState(0);
  const [operation, setOperation] = useState(BULK_OPERATIONS.ACTIVATE);
  const [selectedCoupons, setSelectedCoupons] = useState([]);
  const [formData, setFormData] = useState({
    prefix: '',
    count: 10,
    name: '',
    description: '',
    discount_type: 'percentage',
    discount_value: 0,
    applicable_to: 'all',
    minimum_purchase_amount: 0,
    max_redemptions_per_user: 1,
    start_date: new Date(),
    end_date: null,
    generation_strategy: generationStrategy,
    validation_level: validationLevel
  });
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});
  const [loading, setLoading] = useState(false);
  const [generatedCoupons, setGeneratedCoupons] = useState([]);
  const [progress, setProgress] = useState(0);
  const [showPreview, setShowPreview] = useState(false);

  // Enhanced state management
  const [currentTab, setCurrentTab] = useState(0);
  const [validationResults, setValidationResults] = useState({});
  const [bulkAnalytics, setBulkAnalytics] = useState({
    totalProcessed: 0,
    successCount: 0,
    errorCount: 0,
    startTime: null,
    endTime: null,
    averageProcessingTime: 0
  });
  const [operationHistory, setOperationHistory] = useState([]);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [realTimeValidation, setRealTimeValidation] = useState(enableRealTimeValidation);
  const [processingStatus, setProcessingStatus] = useState('idle'); // idle, processing, completed, error

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    startGeneration: () => handleGenerate(),
    executeBulkOperation: (op) => handleBulkOperation(op),
    resetForm: () => handleReset(),
    validateCoupons: () => handleValidation(),

    // Navigation methods
    nextStep: () => handleNextStep(),
    previousStep: () => handlePreviousStep(),
    goToStep: (step) => setActiveStep(step),

    // Data methods
    getFormData: () => formData,
    setFormData: (data) => setFormData(data),
    getSelectedCoupons: () => selectedCoupons,
    getGeneratedCoupons: () => generatedCoupons,

    // Analytics methods
    getBulkAnalytics: () => bulkAnalytics,
    getOperationHistory: () => operationHistory,
    resetAnalytics: () => setBulkAnalytics({
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      startTime: null,
      endTime: null,
      averageProcessingTime: 0
    }),

    // Accessibility methods
    focusDialog: () => dialogRef.current?.focus(),
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    exportResults: () => handleExport(),
    copyToClipboard: () => handleCopyToClipboard(),
    toggleAdvancedOptions: () => setShowAdvancedOptions(!showAdvancedOptions),
    validateConfiguration: () => validateConfiguration()
  }), [
    formData,
    selectedCoupons,
    generatedCoupons,
    bulkAnalytics,
    operationHistory,
    showAdvancedOptions,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  const steps = useMemo(() => mode === OPERATION_MODES.GENERATE ? [
    {
      label: 'Configure Generation',
      description: 'Set up coupon generation parameters',
      icon: <SettingsIcon />
    },
    {
      label: 'Generate Coupons',
      description: 'Create coupons with specified settings',
      icon: <CouponIcon />
    },
    {
      label: 'Review & Export',
      description: 'Review generated coupons and export',
      icon: <DownloadIcon />
    }
  ] : [
    {
      label: 'Select Operation',
      description: 'Choose bulk operation to perform',
      icon: <SettingsIcon />
    },
    {
      label: 'Select Coupons',
      description: 'Choose coupons for bulk operation',
      icon: <CheckCircleIcon />
    },
    {
      label: 'Execute Operation',
      description: 'Perform the selected operation',
      icon: <VerifiedIcon />
    }
  ], [mode]);

  const bulkOperations = useMemo(() => [
    {
      value: BULK_OPERATIONS.ACTIVATE,
      label: 'Activate Coupons',
      description: 'Enable selected coupons for use',
      icon: <CheckCircleIcon />,
      color: 'success'
    },
    {
      value: BULK_OPERATIONS.DEACTIVATE,
      label: 'Deactivate Coupons',
      description: 'Disable selected coupons',
      icon: <BlockIcon />,
      color: 'warning'
    },
    {
      value: BULK_OPERATIONS.DELETE,
      label: 'Delete Coupons',
      description: 'Permanently remove selected coupons',
      icon: <DeleteIcon />,
      color: 'error'
    },
    {
      value: BULK_OPERATIONS.EXPORT,
      label: 'Export Coupons',
      description: 'Export selected coupons to CSV',
      icon: <DownloadIcon />,
      color: 'info'
    },
    {
      value: BULK_OPERATIONS.DUPLICATE,
      label: 'Duplicate Coupons',
      description: 'Create copies of selected coupons',
      icon: <CopyIcon />,
      color: 'primary'
    },
    {
      value: BULK_OPERATIONS.ARCHIVE,
      label: 'Archive Coupons',
      description: 'Move selected coupons to archive',
      icon: <HistoryIcon />,
      color: 'secondary'
    }
  ], []);

  // Enhanced analytics tracking
  useEffect(() => {
    if (open && enableAnalytics) {
      const startTime = new Date().toISOString();
      setBulkAnalytics(prev => ({
        ...prev,
        startTime
      }));

      if (onAnalyticsTrack) {
        onAnalyticsTrack(COUPON_ANALYTICS_EVENTS.BULK_GENERATION_STARTED, {
          mode,
          timestamp: startTime,
          couponsCount: coupons.length
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Bulk coupon manager opened in ${mode} mode`);
      }
    }
  }, [open, mode, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader, coupons.length]);

  // Real-time validation effect
  useEffect(() => {
    if (realTimeValidation && open) {
      const timeoutId = setTimeout(() => {
        validateConfiguration();
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [formData, realTimeValidation, open]);

  // Form validation rules
  const validationRules = useMemo(() => [
    { key: 'name', label: 'Name', type: 'string', minLength: 3, maxLength: 100, required: true },
    { key: 'description', label: 'Description', type: 'string', minLength: 10, maxLength: 500, required: true },
    { key: 'count', label: 'Count', type: 'number', min: 1, max: maxCouponsPerBatch, required: true },
    { key: 'discount_value', label: 'Discount Value', type: 'number', min: 0, required: true },
    { key: 'prefix', label: 'Prefix', type: 'string', maxLength: 10, pattern: /^[A-Z0-9]*$/ }
  ], [maxCouponsPerBatch]);

  // Enhanced validation function
  const validateConfiguration = useCallback(() => {
    const newErrors = {};
    const newWarnings = {};

    // Validate form data against rules
    validationRules.forEach(rule => {
      const value = formData[rule.key];

      if (rule.required && (!value || value === '')) {
        newErrors[rule.key] = `${rule.label} is required`;
      } else if (value) {
        if (rule.type === 'string') {
          if (rule.minLength && value.length < rule.minLength) {
            newErrors[rule.key] = `${rule.label} must be at least ${rule.minLength} characters`;
          }
          if (rule.maxLength && value.length > rule.maxLength) {
            newErrors[rule.key] = `${rule.label} must be no more than ${rule.maxLength} characters`;
          }
          if (rule.pattern && !rule.pattern.test(value)) {
            newErrors[rule.key] = `${rule.label} contains invalid characters`;
          }
        } else if (rule.type === 'number') {
          const numValue = Number(value);
          if (isNaN(numValue)) {
            newErrors[rule.key] = `${rule.label} must be a valid number`;
          } else {
            if (rule.min !== undefined && numValue < rule.min) {
              newErrors[rule.key] = `${rule.label} must be at least ${rule.min}`;
            }
            if (rule.max !== undefined && numValue > rule.max) {
              newErrors[rule.key] = `${rule.label} must be no more than ${rule.max}`;
            }
          }
        }
      }
    });

    // Additional business logic validation
    if (formData.count > 500) {
      newWarnings.count = 'Generating more than 500 coupons may take longer to process';
    }

    if (formData.discount_value > 90 && formData.discount_type === 'percentage') {
      newWarnings.discount_value = 'High percentage discounts may impact profitability';
    }

    if (formData.end_date && formData.start_date && formData.end_date <= formData.start_date) {
      newErrors.end_date = 'End date must be after start date';
    }

    setErrors(newErrors);
    setWarnings(newWarnings);

    const isValid = Object.keys(newErrors).length === 0;

    if (onValidationResult) {
      onValidationResult({
        isValid,
        errors: newErrors,
        warnings: newWarnings
      });
    }

    return isValid;
  }, [formData, validationRules, onValidationResult]);

  // Enhanced form field change handler
  const handleFieldChange = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Clear warning for this field
    if (warnings[field]) {
      setWarnings(prev => ({ ...prev, [field]: null }));
    }

    // Track field changes for analytics
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('field_changed', {
        field,
        value: typeof value === 'string' ? value.substring(0, 50) : value,
        timestamp: new Date().toISOString()
      });
    }
  }, [errors, warnings, enableAnalytics, onAnalyticsTrack]);

  // Enhanced step navigation
  const handleNextStep = useCallback(() => {
    if (activeStep < steps.length - 1) {
      if (validateConfiguration()) {
        setActiveStep(prev => prev + 1);

        if (enableAccessibility) {
          announceToScreenReader(`Moved to step ${activeStep + 2}: ${steps[activeStep + 1].label}`);
        }
      }
    }
  }, [activeStep, steps.length, validateConfiguration, enableAccessibility, announceToScreenReader, steps]);

  const handlePreviousStep = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep(prev => prev - 1);

      if (enableAccessibility) {
        announceToScreenReader(`Moved to step ${activeStep}: ${steps[activeStep - 1].label}`);
      }
    }
  }, [activeStep, enableAccessibility, announceToScreenReader, steps]);

  // Enhanced generation handler
  const handleGenerate = useCallback(async () => {
    if (!validateConfiguration()) {
      return;
    }

    setLoading(true);
    setProgress(0);
    setProcessingStatus('processing');

    const startTime = Date.now();
    setBulkAnalytics(prev => ({
      ...prev,
      startTime: new Date().toISOString(),
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0
    }));

    if (onGenerationStart) {
      onGenerationStart(formData);
    }

    if (enableAccessibility) {
      announceToScreenReader(`Starting generation of ${formData.count} coupons`);
    }

    try {
      const generatedCoupons = [];
      const totalBatches = Math.ceil(formData.count / batchSize);

      for (let batch = 0; batch < totalBatches; batch++) {
        const batchStart = batch * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, formData.count);
        const batchCoupons = [];

        // Generate coupons for this batch
        for (let i = batchStart; i < batchEnd; i++) {
          const code = generateCouponCode(formData.prefix, 8);
          batchCoupons.push({
            code,
            name: `${formData.name} #${i + 1}`,
            description: formData.description,
            discount_type: formData.discount_type,
            discount_value: formData.discount_value,
            applicable_to: formData.applicable_to,
            minimum_purchase_amount: formData.minimum_purchase_amount,
            max_redemptions_per_user: formData.max_redemptions_per_user,
            start_date: formData.start_date.toISOString(),
            end_date: formData.end_date ? formData.end_date.toISOString() : null,
            is_active: true,
            generation_strategy: formData.generation_strategy,
            validation_level: formData.validation_level
          });
        }

        // Send batch to API
        const response = await api.post('/api/coupons/bulk-generate', {
          coupons: batchCoupons,
          metadata: {
            batch_number: batch + 1,
            total_batches: totalBatches,
            generation_strategy: formData.generation_strategy
          }
        });

        generatedCoupons.push(...response.data.coupons);

        // Update progress
        const progressPercent = ((batch + 1) / totalBatches) * 100;
        setProgress(progressPercent);

        // Update analytics
        setBulkAnalytics(prev => ({
          ...prev,
          totalProcessed: generatedCoupons.length,
          successCount: generatedCoupons.length
        }));

        if (onGenerationProgress) {
          onGenerationProgress({
            batch: batch + 1,
            totalBatches,
            progress: progressPercent,
            generatedCount: generatedCoupons.length
          });
        }

        // Small delay for UX and to prevent overwhelming the server
        if (batch < totalBatches - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      setGeneratedCoupons(generatedCoupons);
      setProcessingStatus('completed');
      setActiveStep(steps.length - 1);

      // Final analytics update
      setBulkAnalytics(prev => ({
        ...prev,
        endTime: new Date().toISOString(),
        averageProcessingTime: processingTime / generatedCoupons.length
      }));

      // Add to operation history
      setOperationHistory(prev => [...prev, {
        id: Date.now(),
        type: 'generation',
        timestamp: new Date().toISOString(),
        details: {
          count: generatedCoupons.length,
          processingTime,
          strategy: formData.generation_strategy
        }
      }]);

      // Notify parent component
      if (onCouponsUpdated) {
        onCouponsUpdated(generatedCoupons);
      }

      if (onGenerationComplete) {
        onGenerationComplete({
          coupons: generatedCoupons,
          analytics: bulkAnalytics,
          processingTime
        });
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(COUPON_ANALYTICS_EVENTS.BULK_GENERATION_COMPLETED, {
          count: generatedCoupons.length,
          processingTime,
          strategy: formData.generation_strategy
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Successfully generated ${generatedCoupons.length} coupons`);
      }

    } catch (error) {
      console.error('Error generating coupons:', error);
      setErrors({
        generation: error.response?.data?.detail || 'Failed to generate coupons'
      });
      setProcessingStatus('error');

      setBulkAnalytics(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }));

      if (enableAccessibility) {
        announceToScreenReader('Coupon generation failed. Please check the error message and try again.');
      }
    } finally {
      setLoading(false);
    }
  }, [
    validateConfiguration,
    formData,
    batchSize,
    steps.length,
    onGenerationStart,
    onGenerationProgress,
    onGenerationComplete,
    onCouponsUpdated,
    enableAnalytics,
    enableAccessibility,
    onAnalyticsTrack,
    announceToScreenReader,
    bulkAnalytics
  ]);

  // Enhanced coupon selection handlers
  const handleCouponSelect = useCallback((couponId) => {
    setSelectedCoupons(prev => {
      const newSelection = prev.includes(couponId)
        ? prev.filter(id => id !== couponId)
        : [...prev, couponId];

      if (enableAccessibility) {
        const action = prev.includes(couponId) ? 'deselected' : 'selected';
        announceToScreenReader(`Coupon ${action}. ${newSelection.length} of ${coupons.length} coupons selected.`);
      }

      return newSelection;
    });
  }, [coupons.length, enableAccessibility, announceToScreenReader]);

  const handleSelectAll = useCallback(() => {
    const newSelection = selectedCoupons.length === coupons.length ? [] : coupons.map(c => c.id);
    setSelectedCoupons(newSelection);

    if (enableAccessibility) {
      const action = newSelection.length === 0 ? 'deselected all' : 'selected all';
      announceToScreenReader(`${action} coupons. ${newSelection.length} coupons selected.`);
    }
  }, [selectedCoupons.length, coupons, enableAccessibility, announceToScreenReader]);

  // Enhanced bulk operation handler
  const handleBulkOperation = useCallback(async (operationType = operation) => {
    if (selectedCoupons.length === 0) {
      setErrors({ selection: 'Please select at least one coupon' });
      return;
    }

    setLoading(true);
    setProcessingStatus('processing');

    const startTime = Date.now();
    setBulkAnalytics(prev => ({
      ...prev,
      startTime: new Date().toISOString(),
      totalProcessed: selectedCoupons.length
    }));

    if (enableAccessibility) {
      announceToScreenReader(`Starting ${operationType} operation on ${selectedCoupons.length} coupons`);
    }

    try {
      let response;

      switch (operationType) {
        case BULK_OPERATIONS.ACTIVATE:
          response = await api.post('/api/coupons/bulk-activate', {
            coupon_ids: selectedCoupons,
          });
          break;
        case BULK_OPERATIONS.DEACTIVATE:
          response = await api.post('/api/coupons/bulk-deactivate', {
            coupon_ids: selectedCoupons,
          });
          break;
        case BULK_OPERATIONS.DELETE:
          response = await api.post('/api/coupons/bulk-delete', {
            coupon_ids: selectedCoupons,
          });
          break;
        case BULK_OPERATIONS.DUPLICATE:
          response = await api.post('/api/coupons/bulk-duplicate', {
            coupon_ids: selectedCoupons,
          });
          break;
        case BULK_OPERATIONS.ARCHIVE:
          response = await api.post('/api/coupons/bulk-archive', {
            coupon_ids: selectedCoupons,
          });
          break;
        case BULK_OPERATIONS.EXPORT:
          handleExportSelected();
          return;
        default:
          throw new Error('Invalid operation');
      }

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      setProcessingStatus('completed');

      // Update analytics
      setBulkAnalytics(prev => ({
        ...prev,
        endTime: new Date().toISOString(),
        successCount: selectedCoupons.length,
        averageProcessingTime: processingTime / selectedCoupons.length
      }));

      // Add to operation history
      setOperationHistory(prev => [...prev, {
        id: Date.now(),
        type: operationType,
        timestamp: new Date().toISOString(),
        details: {
          count: selectedCoupons.length,
          processingTime
        }
      }]);

      if (onCouponsUpdated) {
        onCouponsUpdated(response.data);
      }

      if (onBulkOperationComplete) {
        onBulkOperationComplete({
          operation: operationType,
          affectedCoupons: selectedCoupons.length,
          result: response.data,
          processingTime
        });
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(COUPON_ANALYTICS_EVENTS.BULK_OPERATION_EXECUTED, {
          operation: operationType,
          count: selectedCoupons.length,
          processingTime
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Successfully completed ${operationType} operation on ${selectedCoupons.length} coupons`);
      }

      handleClose();

    } catch (error) {
      console.error('Error executing bulk operation:', error);
      setErrors({
        operation: error.response?.data?.detail || `Failed to ${operationType} coupons`
      });
      setProcessingStatus('error');

      setBulkAnalytics(prev => ({
        ...prev,
        errorCount: prev.errorCount + 1
      }));

      if (enableAccessibility) {
        announceToScreenReader(`${operationType} operation failed. Please check the error message and try again.`);
      }
    } finally {
      setLoading(false);
    }
  }, [
    selectedCoupons,
    operation,
    onCouponsUpdated,
    onBulkOperationComplete,
    enableAnalytics,
    enableAccessibility,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  // Enhanced utility functions
  const handleExport = useCallback(() => {
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'name', label: 'Name' },
      { key: 'discount_type', label: 'Type' },
      { key: 'discount_value', label: 'Value' },
      { key: 'created_at', label: 'Created At', type: 'date' },
      { key: 'is_active', label: 'Active', type: 'boolean' }
    ];

    exportToCSV(
      generatedCoupons,
      `bulk-coupons-${Date.now()}`,
      exportColumns
    );

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(COUPON_ANALYTICS_EVENTS.EXPORT_INITIATED, {
        count: generatedCoupons.length,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Exported ${generatedCoupons.length} coupons to CSV file`);
    }
  }, [generatedCoupons, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleExportSelected = useCallback(() => {
    const selectedCouponData = coupons.filter(c => selectedCoupons.includes(c.id));
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'name', label: 'Name' },
      { key: 'discount_type', label: 'Type' },
      { key: 'discount_value', label: 'Value' },
      { key: 'is_active', label: 'Active', type: 'boolean' },
      { key: 'redemption_count', label: 'Redemptions' },
    ];

    exportToCSV(
      selectedCouponData,
      `selected-coupons-${Date.now()}`,
      exportColumns
    );

    if (enableAccessibility) {
      announceToScreenReader(`Exported ${selectedCouponData.length} selected coupons to CSV file`);
    }
  }, [coupons, selectedCoupons, enableAccessibility, announceToScreenReader]);

  const handleCopyToClipboard = useCallback(async () => {
    const codesList = generatedCoupons.map(coupon => coupon.code).join('\n');
    const success = await copyToClipboard(codesList);

    if (success && enableAccessibility) {
      announceToScreenReader(`Copied ${generatedCoupons.length} coupon codes to clipboard`);
    }
  }, [generatedCoupons, enableAccessibility, announceToScreenReader]);

  const handleReset = useCallback(() => {
    setActiveStep(0);
    setOperation(BULK_OPERATIONS.ACTIVATE);
    setSelectedCoupons([]);
    setFormData({
      prefix: '',
      count: 10,
      name: '',
      description: '',
      discount_type: 'percentage',
      discount_value: 0,
      applicable_to: 'all',
      minimum_purchase_amount: 0,
      max_redemptions_per_user: 1,
      start_date: new Date(),
      end_date: null,
      generation_strategy: generationStrategy,
      validation_level: validationLevel
    });
    setErrors({});
    setWarnings({});
    setGeneratedCoupons([]);
    setProgress(0);
    setShowPreview(false);
    setProcessingStatus('idle');
    setBulkAnalytics({
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      startTime: null,
      endTime: null,
      averageProcessingTime: 0
    });
  }, [generationStrategy, validationLevel]);

  const handleClose = useCallback(() => {
    handleReset();
    onClose();
  }, [handleReset, onClose]);

  const handleValidation = useCallback(() => {
    return validateConfiguration();
  }, [validateConfiguration]);



  return (
    <Dialog
      ref={dialogRef}
      open={open}
      onClose={handleClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: {
          ...glassMorphismStyles,
          minHeight: 700,
          maxHeight: '90vh',
          overflow: 'hidden'
        }
      }}
      aria-labelledby="bulk-coupon-manager-title"
      aria-describedby="bulk-coupon-manager-description"
      className={className}
      {...props}
    >
      {/* Enhanced Dialog Title */}
      <DialogTitle
        id="bulk-coupon-manager-title"
        sx={{
          background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${alpha(ACE_COLORS.PURPLE, 0.8)} 100%)`,
          color: ACE_COLORS.WHITE,
          py: 3
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" gap={2}>
            <CouponIcon sx={{ fontSize: 32 }} />
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                {mode === OPERATION_MODES.GENERATE ? 'Bulk Coupon Generator' :
                 mode === OPERATION_MODES.ANALYZE ? 'Coupon Analytics' : 'Bulk Coupon Manager'}
              </Typography>
              <Typography variant="subtitle2" sx={{ opacity: 0.9 }}>
                {mode === OPERATION_MODES.GENERATE ? 'Generate multiple coupons efficiently' :
                 mode === OPERATION_MODES.ANALYZE ? 'Analyze coupon performance and usage' :
                 'Manage multiple coupons with bulk operations'}
              </Typography>
            </Box>
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            {/* Processing status indicator */}
            {processingStatus !== 'idle' && (
              <Chip
                icon={processingStatus === 'processing' ? <CircularProgress size={16} /> :
                      processingStatus === 'completed' ? <CheckCircleIcon /> : <ErrorIcon />}
                label={processingStatus.charAt(0).toUpperCase() + processingStatus.slice(1)}
                size="small"
                sx={{
                  bgcolor: alpha(ACE_COLORS.WHITE, 0.2),
                  color: ACE_COLORS.WHITE,
                  '& .MuiChip-icon': { color: ACE_COLORS.WHITE }
                }}
              />
            )}

            {/* Analytics toggle */}
            {enableAnalytics && (
              <Tooltip title="View Analytics">
                <IconButton
                  onClick={() => setCurrentTab(currentTab === 1 ? 0 : 1)}
                  sx={{ color: ACE_COLORS.WHITE }}
                >
                  <AnalyticsIcon />
                </IconButton>
              </Tooltip>
            )}

            {/* Advanced options toggle */}
            <Tooltip title="Advanced Options">
              <IconButton
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                sx={{ color: ACE_COLORS.WHITE }}
              >
                <SettingsIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Close">
              <IconButton onClick={handleClose} sx={{ color: ACE_COLORS.WHITE }}>
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Progress indicator for current step */}
        <Box sx={{ mt: 2 }}>
          <LinearProgress
            variant="determinate"
            value={(activeStep / (steps.length - 1)) * 100}
            sx={{
              height: 4,
              borderRadius: 2,
              backgroundColor: alpha(ACE_COLORS.WHITE, 0.3),
              '& .MuiLinearProgress-bar': {
                backgroundColor: ACE_COLORS.YELLOW
              }
            }}
          />
          <Typography variant="caption" sx={{ mt: 1, opacity: 0.9 }}>
            Step {activeStep + 1} of {steps.length}: {steps[activeStep]?.label}
          </Typography>
        </Box>
      </DialogTitle>

      {/* Enhanced Dialog Content */}
      <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
        {/* Tab Navigation */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={currentTab}
            onChange={(e, newValue) => setCurrentTab(newValue)}
            sx={{
              '& .MuiTab-root': {
                minHeight: 48,
                textTransform: 'none',
                fontWeight: 'medium'
              }
            }}
          >
            <Tab
              label="Main Operation"
              icon={<CampaignIcon />}
              iconPosition="start"
            />
            {enableAnalytics && (
              <Tab
                label="Analytics"
                icon={<AnalyticsIcon />}
                iconPosition="start"
              />
            )}
          </Tabs>
        </Box>

        {/* Main Operation Tab */}
        <Fade in={currentTab === 0}>
          <Box sx={{ display: currentTab === 0 ? 'block' : 'none', p: 3 }}>
            {/* Error and Warning Alerts */}
            {Object.keys(errors).length > 0 && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Please fix the following errors:
                </Typography>
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  {Object.entries(errors).map(([field, message]) => (
                    <li key={field}>{message}</li>
                  ))}
                </ul>
              </Alert>
            )}

            {Object.keys(warnings).length > 0 && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Warnings:
                </Typography>
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  {Object.entries(warnings).map(([field, message]) => (
                    <li key={field}>{message}</li>
                  ))}
                </ul>
              </Alert>
            )}

            {/* Enhanced Stepper */}
            <Stepper
              activeStep={activeStep}
              orientation={isMobile ? "vertical" : "horizontal"}
              sx={{ mb: 4 }}
            >
              {steps.map((step, index) => (
                <Step key={index}>
                  <StepLabel
                    icon={step.icon}
                    sx={{
                      '& .MuiStepLabel-label': {
                        fontWeight: activeStep === index ? 'bold' : 'normal'
                      }
                    }}
                  >
                    <Box>
                      <Typography variant="body1">{step.label}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {step.description}
                      </Typography>
                    </Box>
                  </StepLabel>
                </Step>
              ))}
            </Stepper>

            {/* Step Content */}
            <Box sx={{ minHeight: 400 }}>
              {mode === OPERATION_MODES.GENERATE ? (
                <>
                  {/* Generate Mode Steps */}
                  {activeStep === 0 && (
                    <Fade in={true}>
                      <Card variant="outlined" sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SettingsIcon color="primary" />
                          Configure Generation Settings
                        </Typography>

                        <Grid container spacing={3} sx={{ mt: 1 }}>
                          <Grid item xs={12} md={6}>
                            <TextField
                              label="Prefix (Optional)"
                              value={formData.prefix}
                              onChange={(e) => handleFieldChange('prefix', e.target.value.toUpperCase())}
                              fullWidth
                              error={!!errors.prefix}
                              helperText={errors.prefix || "Code prefix (e.g., SUMMER)"}
                              inputProps={{ maxLength: 10 }}
                              InputProps={{
                                startAdornment: <CouponIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              }}
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <TextField
                              label="Number of Coupons"
                              type="number"
                              value={formData.count}
                              onChange={(e) => handleFieldChange('count', parseInt(e.target.value) || 0)}
                              error={!!errors.count}
                              helperText={errors.count || `Generate 1-${maxCouponsPerBatch} coupons`}
                              inputProps={{ min: 1, max: maxCouponsPerBatch }}
                              fullWidth
                              required
                              InputProps={{
                                startAdornment: <GroupIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              }}
                            />
                          </Grid>

                          <Grid item xs={12}>
                            <TextField
                              label="Campaign Name"
                              value={formData.name}
                              onChange={(e) => handleFieldChange('name', e.target.value)}
                              error={!!errors.name}
                              helperText={errors.name || 'Name for this coupon campaign'}
                              fullWidth
                              required
                              InputProps={{
                                startAdornment: <CampaignIcon sx={{ mr: 1, color: 'text.secondary' }} />
                              }}
                            />
                          </Grid>

                          <Grid item xs={12}>
                            <TextField
                              label="Description"
                              value={formData.description}
                              onChange={(e) => handleFieldChange('description', e.target.value)}
                              error={!!errors.description}
                              helperText={errors.description || 'Description for all generated coupons'}
                              fullWidth
                              multiline
                              rows={3}
                              required
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <FormControl fullWidth>
                              <InputLabel>Discount Type</InputLabel>
                              <Select
                                value={formData.discount_type}
                                onChange={(e) => handleFieldChange('discount_type', e.target.value)}
                                label="Discount Type"
                              >
                                <MenuItem value="percentage">Percentage</MenuItem>
                                <MenuItem value="fixed">Fixed Amount</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <TextField
                              label="Discount Value"
                              type="number"
                              value={formData.discount_value}
                              onChange={(e) => handleFieldChange('discount_value', parseFloat(e.target.value) || 0)}
                              error={!!errors.discount_value}
                              helperText={errors.discount_value || (formData.discount_type === 'percentage' ? 'Percentage (0-100)' : 'Amount in dollars')}
                              fullWidth
                              required
                              InputProps={{
                                startAdornment: formData.discount_type === 'percentage' ? '%' : '$'
                              }}
                            />
                          </Grid>
                        </Grid>

                        {/* Advanced Options */}
                        <Collapse in={showAdvancedOptions}>
                          <Divider sx={{ my: 3 }} />
                          <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <SecurityIcon color="primary" />
                            Advanced Options
                          </Typography>

                          <Grid container spacing={3} sx={{ mt: 1 }}>
                            <Grid item xs={12} md={6}>
                              <FormControl fullWidth>
                                <InputLabel>Generation Strategy</InputLabel>
                                <Select
                                  value={formData.generation_strategy}
                                  onChange={(e) => handleFieldChange('generation_strategy', e.target.value)}
                                  label="Generation Strategy"
                                >
                                  <MenuItem value={GENERATION_STRATEGIES.RANDOM}>Random</MenuItem>
                                  <MenuItem value={GENERATION_STRATEGIES.SEQUENTIAL}>Sequential</MenuItem>
                                  <MenuItem value={GENERATION_STRATEGIES.PATTERN}>Pattern-based</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControl fullWidth>
                                <InputLabel>Validation Level</InputLabel>
                                <Select
                                  value={formData.validation_level}
                                  onChange={(e) => handleFieldChange('validation_level', e.target.value)}
                                  label="Validation Level"
                                >
                                  <MenuItem value={VALIDATION_LEVELS.BASIC}>Basic</MenuItem>
                                  <MenuItem value={VALIDATION_LEVELS.ADVANCED}>Advanced</MenuItem>
                                  <MenuItem value={VALIDATION_LEVELS.STRICT}>Strict</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>
                          </Grid>
                        </Collapse>

                        {/* Preview */}
                        <Box sx={{ mt: 3, p: 2, bgcolor: alpha(ACE_COLORS.PURPLE, 0.05), borderRadius: 1 }}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Code Preview:
                          </Typography>
                          <Typography variant="h6" sx={{ fontFamily: 'monospace', color: ACE_COLORS.PURPLE }}>
                            {formData.prefix ? formData.prefix + '-' : ''}XXXXXXXX
                          </Typography>
                        </Box>
                      </Card>
                    </Fade>
                  )}

                  {/* Generation Progress Step */}
                  {activeStep === 1 && (
                    <Fade in={true}>
                      <Card variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                          <CouponIcon color="primary" />
                          Generating {formData.count} Coupons
                        </Typography>

                        <Box sx={{ my: 4 }}>
                          <CircularProgress
                            variant="determinate"
                            value={progress}
                            size={80}
                            thickness={4}
                            sx={{ color: ACE_COLORS.PURPLE }}
                          />
                          <Typography variant="h4" sx={{ mt: 2, fontWeight: 'bold', color: ACE_COLORS.PURPLE }}>
                            {Math.round(progress)}%
                          </Typography>
                        </Box>

                        <LinearProgress
                          variant="determinate"
                          value={progress}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2),
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: ACE_COLORS.PURPLE
                            }
                          }}
                        />

                        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                          Processing batch {Math.ceil((progress / 100) * Math.ceil(formData.count / batchSize))} of {Math.ceil(formData.count / batchSize)}
                        </Typography>
                      </Card>
                    </Fade>
                  )}

                  {/* Results Step */}
                  {activeStep === 2 && (
                    <Fade in={true}>
                      <Card variant="outlined" sx={{ p: 3 }}>
                        <Alert severity="success" sx={{ mb: 3 }}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <CheckCircleIcon />
                            <Typography variant="subtitle1">
                              Successfully generated {generatedCoupons.length} coupons
                            </Typography>
                          </Box>
                        </Alert>

                        <Grid container spacing={2} sx={{ mb: 3 }}>
                          <Grid item xs={12} sm={6} md={3}>
                            <Button
                              variant="contained"
                              fullWidth
                              startIcon={<DownloadIcon />}
                              onClick={handleExport}
                              sx={{
                                bgcolor: ACE_COLORS.PURPLE,
                                '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
                              }}
                            >
                              Export CSV
                            </Button>
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <Button
                              variant="outlined"
                              fullWidth
                              startIcon={<CopyIcon />}
                              onClick={handleCopyToClipboard}
                              sx={{
                                borderColor: ACE_COLORS.PURPLE,
                                color: ACE_COLORS.PURPLE
                              }}
                            >
                              Copy Codes
                            </Button>
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <Button
                              variant="outlined"
                              fullWidth
                              startIcon={showPreview ? <VisibilityOffIcon /> : <VisibilityIcon />}
                              onClick={() => setShowPreview(!showPreview)}
                            >
                              {showPreview ? 'Hide' : 'Show'} Preview
                            </Button>
                          </Grid>
                          <Grid item xs={12} sm={6} md={3}>
                            <Button
                              variant="outlined"
                              fullWidth
                              startIcon={<AnalyticsIcon />}
                              onClick={() => setCurrentTab(1)}
                              disabled={!enableAnalytics}
                            >
                              View Analytics
                            </Button>
                          </Grid>
                        </Grid>

                        <Collapse in={showPreview}>
                          <TableContainer component={Paper} sx={{ maxHeight: 400, mb: 2 }}>
                            <Table stickyHeader>
                              <TableHead>
                                <TableRow>
                                  <TableCell>Code</TableCell>
                                  <TableCell>Name</TableCell>
                                  <TableCell>Discount</TableCell>
                                  <TableCell>Status</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {generatedCoupons.slice(0, 20).map((coupon) => (
                                  <TableRow key={coupon.code}>
                                    <TableCell sx={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                                      {coupon.code}
                                    </TableCell>
                                    <TableCell>{coupon.name}</TableCell>
                                    <TableCell>
                                      <Chip
                                        label={coupon.discount_type === 'percentage' ?
                                          `${coupon.discount_value}%` :
                                          `$${coupon.discount_value}`}
                                        color="primary"
                                        size="small"
                                      />
                                    </TableCell>
                                    <TableCell>
                                      <Chip
                                        label="Active"
                                        color="success"
                                        size="small"
                                      />
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </TableContainer>

                          {generatedCoupons.length > 20 && (
                            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                              Showing first 20 of {generatedCoupons.length} generated coupons
                            </Typography>
                          )}
                        </Collapse>

                        <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
                          Coupons have been saved to the database and are ready for distribution.
                        </Typography>
                      </Card>
                    </Fade>
                  )}
                </>
              ) : (
                <>
                  {/* Manage Mode Steps - Simplified for now */}
                  <Typography variant="h6" sx={{ textAlign: 'center', py: 4 }}>
                    Bulk Management Mode - Coming Soon
                  </Typography>
                </>
              )}
            </Box>
          </Box>
        </Fade>

        {/* Analytics Tab */}
        {enableAnalytics && (
          <Fade in={currentTab === 1}>
            <Box sx={{ display: currentTab === 1 ? 'block' : 'none', p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AnalyticsIcon color="primary" />
                Bulk Operation Analytics
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Processing Statistics
                    </Typography>
                    <Typography variant="h4" color="primary">
                      {bulkAnalytics.totalProcessed}
                    </Typography>
                    <Typography variant="body2">
                      Total Processed
                    </Typography>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Success Rate
                    </Typography>
                    <Typography variant="h4" color="success.main">
                      {bulkAnalytics.totalProcessed > 0 ?
                        Math.round((bulkAnalytics.successCount / bulkAnalytics.totalProcessed) * 100) : 0}%
                    </Typography>
                    <Typography variant="body2">
                      {bulkAnalytics.successCount} successful
                    </Typography>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Operation History
                    </Typography>
                    {operationHistory.length > 0 ? (
                      <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                        {operationHistory.map((op) => (
                          <Box key={op.id} sx={{ py: 1, borderBottom: '1px solid', borderColor: 'divider' }}>
                            <Typography variant="body2">
                              {op.type} - {op.details.count} items - {new Date(op.timestamp).toLocaleString()}
                            </Typography>
                          </Box>
                        ))}
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No operations performed yet
                      </Typography>
                    )}
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </Fade>
        )}
      </DialogContent>

      {/* Enhanced Dialog Actions */}
      <DialogActions
        sx={{
          p: 3,
          background: `linear-gradient(135deg, ${alpha(ACE_COLORS.PURPLE, 0.05)} 0%, ${alpha(ACE_COLORS.YELLOW, 0.05)} 100%)`,
          borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
          {/* Left side - Step info */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Step {activeStep + 1} of {steps.length}
            </Typography>

            {processingStatus !== 'idle' && (
              <Chip
                icon={processingStatus === 'processing' ? <CircularProgress size={16} /> :
                      processingStatus === 'completed' ? <CheckCircleIcon /> : <ErrorIcon />}
                label={processingStatus.charAt(0).toUpperCase() + processingStatus.slice(1)}
                size="small"
                color={processingStatus === 'completed' ? 'success' :
                       processingStatus === 'error' ? 'error' : 'default'}
              />
            )}
          </Box>

          {/* Right side - Action buttons */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              onClick={handleClose}
              disabled={loading}
            >
              {activeStep === steps.length - 1 ? 'Close' : 'Cancel'}
            </Button>

            {activeStep > 0 && activeStep < steps.length - 1 && (
              <Button
                onClick={handlePreviousStep}
                disabled={loading}
                startIcon={<RefreshIcon />}
              >
                Back
              </Button>
            )}

            {activeStep < steps.length - 1 && (
              <Button
                onClick={mode === OPERATION_MODES.GENERATE && activeStep === 0 ? handleGenerate : handleNextStep}
                variant="contained"
                disabled={loading || Object.keys(errors).length > 0}
                sx={{
                  bgcolor: ACE_COLORS.PURPLE,
                  '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
                }}
                startIcon={loading ? <CircularProgress size={16} /> :
                          mode === OPERATION_MODES.GENERATE && activeStep === 0 ? <CouponIcon /> :
                          <CheckCircleIcon />}
              >
                {loading ? 'Processing...' :
                 mode === OPERATION_MODES.GENERATE && activeStep === 0 ? 'Generate Coupons' :
                 'Next Step'}
              </Button>
            )}
          </Box>
        </Box>
      </DialogActions>
    </Dialog>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedBulkCouponManager.propTypes = {
  // Core props
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  coupons: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    code: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    is_active: PropTypes.bool,
    redemption_count: PropTypes.number
  })),
  onCouponsUpdated: PropTypes.func,

  // Mode and configuration
  mode: PropTypes.oneOf(Object.values(OPERATION_MODES)),
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeValidation: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableBulkOperations: PropTypes.bool,

  // Generation settings
  maxCouponsPerBatch: PropTypes.number,
  batchSize: PropTypes.number,
  validationLevel: PropTypes.oneOf(Object.values(VALIDATION_LEVELS)),
  generationStrategy: PropTypes.oneOf(Object.values(GENERATION_STRATEGIES)),

  // Callback props
  onGenerationStart: PropTypes.func,
  onGenerationProgress: PropTypes.func,
  onGenerationComplete: PropTypes.func,
  onBulkOperationComplete: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onValidationResult: PropTypes.func,

  // Standard props
  className: PropTypes.string
};

// Default props
EnhancedBulkCouponManager.defaultProps = {
  coupons: [],
  onCouponsUpdated: null,
  mode: OPERATION_MODES.GENERATE,
  enableAdvancedFeatures: true,
  enableRealTimeValidation: true,
  enableAnalytics: true,
  enableAccessibility: true,
  enableBulkOperations: true,
  maxCouponsPerBatch: 1000,
  batchSize: 50,
  validationLevel: VALIDATION_LEVELS.ADVANCED,
  generationStrategy: GENERATION_STRATEGIES.RANDOM,
  onGenerationStart: null,
  onGenerationProgress: null,
  onGenerationComplete: null,
  onBulkOperationComplete: null,
  onAnalyticsTrack: null,
  onValidationResult: null,
  className: ''
};

// Display name for debugging
EnhancedBulkCouponManager.displayName = 'EnhancedBulkCouponManager';

export default EnhancedBulkCouponManager;

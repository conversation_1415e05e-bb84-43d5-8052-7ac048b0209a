import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ChangeTracker from '../ChangeTracker';

// Mock the collaboration context
const mockCollaborationContext = {
  isConnected: true,
  activeUsers: [
    {
      user_id: 'user1',
      user_info: { name: '<PERSON>' }
    },
    {
      user_id: 'user2',
      user_info: { name: '<PERSON>' }
    }
  ]
};

vi.mock('../../../contexts/CollaborationContext', () => ({
  useCollaboration: () => mockCollaborationContext,
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
      warning: {
        main: '#FF9800',
      },
      success: {
        main: '#4CAF50',
      },
      error: {
        main: '#F44336',
      },
      info: {
        main: '#2196F3',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ChangeTracker', () => {
  const mockProps = {
    contentId: 'test-content-id'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders change tracker correctly', () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Change History')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /collapse/i })).toBeInTheDocument();
  });

  test('shows loading state initially', () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Loading changes...')).toBeInTheDocument();
  });

  test('displays changes after loading', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    // Wait for changes to load
    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for mock changes
    expect(screen.getByText('Updated title')).toBeInTheDocument();
    expect(screen.getByText('Added description')).toBeInTheDocument();
  });

  test('shows change details correctly', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for change details
    expect(screen.getByText('From: Old Title')).toBeInTheDocument();
    expect(screen.getByText('To: New Title')).toBeInTheDocument();
    expect(screen.getByText('Added: Added description')).toBeInTheDocument();
  });

  test('displays user information correctly', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for user names
    expect(screen.getByText(/John Doe/)).toBeInTheDocument();
    expect(screen.getByText(/Jane Smith/)).toBeInTheDocument();
  });

  test('shows relative timestamps', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for relative timestamps
    expect(screen.getByText(/ago/)).toBeInTheDocument();
  });

  test('displays change type chips correctly', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for change type chips
    expect(screen.getByText('EDIT')).toBeInTheDocument();
    expect(screen.getByText('ADD')).toBeInTheDocument();
  });

  test('handles expand/collapse functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    const collapseButton = screen.getByRole('button', { name: /collapse/i });
    
    // Initially expanded, should show changes
    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });
    expect(screen.getByText('Updated title')).toBeInTheDocument();

    // Click to collapse
    await user.click(collapseButton);
    
    // Changes should be hidden
    expect(screen.queryByText('Updated title')).not.toBeInTheDocument();

    // Click to expand again
    await user.click(collapseButton);
    
    // Changes should be visible again
    expect(screen.getByText('Updated title')).toBeInTheDocument();
  });

  test('displays active users section', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Currently editing:')).toBeInTheDocument();
  });

  test('shows user avatars for active users', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for user avatars (should show initials)
    const avatars = screen.getAllByRole('img');
    expect(avatars.length).toBeGreaterThan(0);
  });

  test('displays change count badge', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Should show badge with change count
    const badge = screen.getByText('2'); // Mock data has 2 changes
    expect(badge).toBeInTheDocument();
  });

  test('handles empty changes state', () => {
    // Mock empty changes
    const emptyMockProps = {
      contentId: 'empty-content-id'
    };

    render(
      <TestWrapper>
        <ChangeTracker {...emptyMockProps} />
      </TestWrapper>
    );

    // Should show loading initially, then empty state
    expect(screen.getByText('Loading changes...')).toBeInTheDocument();
  });

  test('handles missing content ID', () => {
    render(
      <TestWrapper>
        <ChangeTracker contentId="" />
      </TestWrapper>
    );

    // Should not attempt to fetch changes
    expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
  });

  test('displays correct change icons', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for change type icons in chips
    const editChip = screen.getByText('EDIT').closest('.MuiChip-root');
    const addChip = screen.getByText('ADD').closest('.MuiChip-root');
    
    expect(editChip).toBeInTheDocument();
    expect(addChip).toBeInTheDocument();
  });

  test('handles user initials generation', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for user initials in avatars
    expect(screen.getByText('JD')).toBeInTheDocument(); // John Doe
    expect(screen.getByText('JS')).toBeInTheDocument(); // Jane Smith
  });

  test('has proper accessibility attributes', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for proper ARIA labels and roles
    expect(screen.getByRole('button', { name: /collapse/i })).toBeInTheDocument();
    expect(screen.getByRole('list')).toBeInTheDocument();
    
    // Check for avatars with alt text
    const avatars = screen.getAllByRole('img');
    avatars.forEach(avatar => {
      expect(avatar).toHaveAttribute('alt');
    });
  });

  test('handles real-time connection status', () => {
    // Test with disconnected state
    const disconnectedContext = {
      ...mockCollaborationContext,
      isConnected: false
    };

    vi.mocked(require('../../../contexts/CollaborationContext').useCollaboration).mockReturnValue(disconnectedContext);

    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    // Component should still render but not attempt real-time updates
    expect(screen.getByText('Change History')).toBeInTheDocument();
  });

  test('handles no active users', async () => {
    const noUsersContext = {
      ...mockCollaborationContext,
      activeUsers: []
    };

    vi.mocked(require('../../../contexts/CollaborationContext').useCollaboration).mockReturnValue(noUsersContext);

    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Should not show active users section
    expect(screen.queryByText('Currently editing:')).not.toBeInTheDocument();
  });

  test('displays change field names correctly', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check that field names are extracted correctly from nested paths
    expect(screen.getByText('Updated title')).toBeInTheDocument(); // from 'content.title'
    expect(screen.getByText('Added description')).toBeInTheDocument(); // from 'content.description'
  });

  test('handles different change types', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check for different change type handling
    expect(screen.getByText('Updated title')).toBeInTheDocument(); // edit type
    expect(screen.getByText('Added description')).toBeInTheDocument(); // add type
  });

  test('shows proper styling for different change types', async () => {
    render(
      <TestWrapper>
        <ChangeTracker {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByText('Loading changes...')).not.toBeInTheDocument();
    });

    // Check that change items have proper styling based on type
    const changeItems = screen.getAllByRole('listitem');
    expect(changeItems.length).toBeGreaterThan(0);
    
    // Each change item should have proper border styling
    changeItems.forEach(item => {
      expect(item).toHaveStyle('border-left: 4px solid');
    });
  });
});

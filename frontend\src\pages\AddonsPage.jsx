import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Alert,
  Skeleton,
  Tab,
  Tabs,
  Badge,
  LinearProgress,

  IconButton,
  Tooltip
} from '@mui/material';
import {
  Extension as ExtensionIcon,

  Refresh as RefreshIcon,
  ShoppingCart as ShoppingCartIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon
} from '@mui/icons-material';

import { useAddons } from '../hooks/useAddons';

import UsageIndicator from '../components/addons/UsageIndicator';
import UpgradeModal from '../components/addons/UpgradeModal';
import PageHeader from '../components/PageHeader';
import { useMessage } from '../hooks/useMessage';

const AddonsPage = () => {

  const { showMessage } = useMessage();
  const {
    catalog,
    userAddons,
    recommendations,
    loading,
    error,
    purchaseAddon,
    refreshData,
    getUsageAlerts
  } = useAddons();

  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedAddon, setSelectedAddon] = useState(null);
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false);
  const [usageAlerts, setUsageAlerts] = useState([]);

  useEffect(() => {
    const loadUsageAlerts = async () => {
      try {
        const alerts = await getUsageAlerts();
        setUsageAlerts(alerts);
      } catch (error) {
        console.error('Error loading usage alerts:', error);
      }
    };

    loadUsageAlerts();
  }, [getUsageAlerts]);

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const handlePurchaseAddon = async (addonId, variant = 'basic') => {
    try {
      const result = await purchaseAddon(addonId, variant);
      if (result.success) {
        showMessage('Add-on purchased successfully!', 'success');
        await refreshData();
      } else {
        showMessage(result.error || 'Purchase failed', 'error');
      }
    } catch {
      showMessage('Purchase failed. Please try again.', 'error');
    }
  };

  const handleUpgradeClick = (addon) => {
    setSelectedAddon(addon);
    setUpgradeModalOpen(true);
  };

  const renderAddonCard = (addon, isOwned = false) => {
    const userAddon = userAddons.find(ua => ua.addon_id === addon.id);
    const isActive = userAddon?.status === 'active';


    return (
      <Grid item xs={12} sm={6} md={4} key={addon.id}>
        <Card 
          sx={{ 
            height: '100%', 
            display: 'flex', 
            flexDirection: 'column',
            position: 'relative',
            '&:hover': {
              boxShadow: 6,
              transform: 'translateY(-2px)',
              transition: 'all 0.3s ease-in-out'
            }
          }}
        >
          {addon.is_popular && (
            <Chip
              label="Popular"
              color="primary"
              size="small"
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                zIndex: 1
              }}
            />
          )}
          
          {isActive && (
            <Chip
              label="Active"
              color="success"
              size="small"
              icon={<CheckCircleIcon />}
              sx={{
                position: 'absolute',
                top: 8,
                left: 8,
                zIndex: 1
              }}
            />
          )}

          <CardContent sx={{ flexGrow: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <ExtensionIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6" component="h2">
                {addon.name}
              </Typography>
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {addon.description}
            </Typography>

            {/* Usage indicator for owned add-ons */}
            {isActive && (
              <Box sx={{ mb: 2 }}>
                <UsageIndicator
                  current={userAddon.credits_used}
                  total={userAddon.total_credits}
                  label="Credits Used"
                />
              </Box>
            )}

            {/* Features list */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Features:
              </Typography>
              {addon.features?.slice(0, 3).map((feature, index) => (
                <Typography key={index} variant="body2" sx={{ fontSize: '0.875rem', mb: 0.5 }}>
                  • {feature}
                </Typography>
              ))}
            </Box>

            {/* Pricing */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="h6" color="primary.main">
                ${addon.pricing?.basic?.price || 'N/A'}
                {addon.pricing?.basic?.billing_cycle && (
                  <Typography component="span" variant="body2" color="text.secondary">
                    /{addon.pricing.basic.billing_cycle}
                  </Typography>
                )}
              </Typography>
            </Box>
          </CardContent>

          <CardActions sx={{ p: 2, pt: 0 }}>
            {isActive ? (
              <Button
                fullWidth
                variant="outlined"
                startIcon={<InfoIcon />}
                onClick={() => handleUpgradeClick(addon)}
              >
                Manage
              </Button>
            ) : isOwned ? (
              <Button
                fullWidth
                variant="outlined"
                disabled
              >
                Expired
              </Button>
            ) : (
              <Button
                fullWidth
                variant="contained"
                startIcon={<ShoppingCartIcon />}
                onClick={() => handlePurchaseAddon(addon.id)}
                disabled={loading}
              >
                Purchase
              </Button>
            )}
          </CardActions>
        </Card>
      </Grid>
    );
  };

  const renderUsageAlerts = () => {
    if (usageAlerts.length === 0) return null;

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Usage Alerts
        </Typography>
        {usageAlerts.map((alert, index) => (
          <Alert
            key={index}
            severity={alert.alert_type === 'critical' ? 'error' : 'warning'}
            sx={{ mb: 1 }}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={() => handleUpgradeClick({ id: alert.addon_id, name: alert.addon_name })}
              >
                Upgrade
              </Button>
            }
          >
            {alert.message}
          </Alert>
        ))}
      </Box>
    );
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 0: // All Add-ons
        return (
          <Grid container spacing={3}>
            {catalog.map(addon => renderAddonCard(addon))}
          </Grid>
        );
      
      case 1: { // My Add-ons
        const myAddonIds = userAddons.map(ua => ua.addon_id);
        const myAddons = catalog.filter(addon => myAddonIds.includes(addon.id));

        return (
          <Grid container spacing={3}>
            {myAddons.length > 0 ? (
              myAddons.map(addon => renderAddonCard(addon, true))
            ) : (
              <Grid item xs={12}>
                <Alert severity="info">
                  You haven&apos;t purchased any add-ons yet. Browse the marketplace to enhance your ACE Social experience!
                </Alert>
              </Grid>
            )}
          </Grid>
        );
      }
      
      case 2: // Recommended
        return (
          <Grid container spacing={3}>
            {recommendations.length > 0 ? (
              recommendations.map(rec => renderAddonCard(rec.addon))
            ) : (
              <Grid item xs={12}>
                <Alert severity="info">
                  No recommendations available at the moment. Check back later!
                </Alert>
              </Grid>
            )}
          </Grid>
        );
      
      default:
        return null;
    }
  };

  if (loading && catalog.length === 0) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <PageHeader
          title="Add-ons Marketplace"
          subtitle="Enhance your ACE Social experience with powerful add-ons"
          icon={<ExtensionIcon />}
        />
        <Grid container spacing={3}>
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <Grid item xs={12} sm={6} md={4} key={item}>
              <Card sx={{ height: 300 }}>
                <CardContent>
                  <Skeleton variant="text" width="60%" height={32} />
                  <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
                  <Skeleton variant="text" width="100%" height={20} />
                  <Skeleton variant="text" width="80%" height={20} />
                  <Skeleton variant="rectangular" width="100%" height={60} sx={{ mt: 2 }} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <PageHeader
        title="Add-ons Marketplace"
        subtitle="Enhance your ACE Social experience with powerful add-ons"
        icon={<ExtensionIcon />}
        action={
          <Tooltip title="Refresh add-ons data">
            <IconButton onClick={refreshData} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        }
      />

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Usage Alerts */}
      {renderUsageAlerts()}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={selectedTab} onChange={handleTabChange}>
          <Tab 
            label="All Add-ons" 
            icon={<Badge badgeContent={catalog.length} color="primary" />}
          />
          <Tab 
            label="My Add-ons" 
            icon={<Badge badgeContent={userAddons.filter(ua => ua.status === 'active').length} color="success" />}
          />
          <Tab 
            label="Recommended" 
            icon={<Badge badgeContent={recommendations.length} color="secondary" />}
          />
        </Tabs>
      </Box>

      {/* Loading indicator */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Tab content */}
      {renderTabContent()}

      {/* Upgrade Modal */}
      <UpgradeModal
        open={upgradeModalOpen}
        onClose={() => setUpgradeModalOpen(false)}
        addon={selectedAddon}
        onPurchase={handlePurchaseAddon}
      />
    </Container>
  );
};

export default AddonsPage;

<!-- @since 2024-1-1 to 2025-25-7 -->
# ACE Social Platform - Build Documentation

> **Version**: 2.0.0  
> **Last Updated**: 2025-01-06  
> **Environment**: Production-Ready

## 📋 Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Build System Architecture](#build-system-architecture)
- [Environment Configuration](#environment-configuration)
- [Build Commands](#build-commands)
- [Docker Deployment](#docker-deployment)
- [Testing & Validation](#testing--validation)
- [Performance Optimization](#performance-optimization)
- [CI/CD Pipeline](#cicd-pipeline)
- [Troubleshooting](#troubleshooting)
- [Security Guidelines](#security-guidelines)

## 🎯 Overview

The ACE Social Platform uses a modern, standardized build system designed for:

- **Multi-environment support** (development, staging, production)
- **Containerized deployment** with Docker
- **Automated testing** and validation
- **Performance optimization** with bundle analysis
- **Security-first** configuration
- **Cross-platform compatibility** (Windows, macOS, Linux)

### Build Targets

| Target | Description | Output Size | Build Time |
|--------|-------------|-------------|------------|
| Development | Fast builds with HMR | ~15MB | ~30s |
| Staging | Optimized with source maps | ~8MB | ~2min |
| Production | Fully optimized | <2MB | ~3min |

## 🔧 Prerequisites

### Required Software

| Software | Version | Purpose |
|----------|---------|---------|
| **Node.js** | ≥18.0.0 | Frontend build & package management |
| **npm** | ≥8.0.0 | Package manager |
| **Python** | ≥3.8 | Backend runtime |
| **Docker** | ≥20.0 | Containerization |
| **Docker Compose** | ≥2.0 | Multi-container orchestration |

### System Requirements

- **RAM**: Minimum 8GB (16GB recommended for development)
- **Storage**: 10GB free space
- **Network**: Stable internet for dependency downloads

### Verification

```bash
# Check all prerequisites
node --version    # Should be ≥18.0.0
npm --version     # Should be ≥8.0.0
python --version  # Should be ≥3.8
docker --version  # Should be ≥20.0
docker-compose --version  # Should be ≥2.0
```

## 🚀 Quick Start

### 1. Clone & Setup

```bash
# Clone repository
git clone https://github.com/Tayyabjv1/Social-media-Platform.git
cd Social-media-Platform

# Install all dependencies
npm run install:all

# Verify build configuration
npm run verify
```

### 2. Development Mode

```bash
# Start all services in development mode
npm run dev

# Or start individual services
npm run dev:frontend    # Frontend only (port 3000)
npm run dev:backend     # Backend only (port 8000)
npm run dev:admin       # Admin panel (port 3001)
```

### 3. Production Build

```bash
# Build for production
npm run build:production

# Deploy with Docker
npm run start:prod
```

## 🏗️ Build System Architecture

### Multi-Stage Build Process

The build system follows a multi-stage approach for optimal performance and security:

1. **Dependency Resolution**: Install and cache dependencies
2. **Code Compilation**: TypeScript → JavaScript, SCSS → CSS
3. **Bundle Optimization**: Tree shaking, minification, compression
4. **Asset Processing**: Image optimization, font subsetting
5. **Docker Packaging**: Multi-stage container build
6. **Validation**: Security scans, performance checks

### File Structure

```
├── frontend/           # React frontend application
│   ├── src/           # Source code
│   ├── dist/          # Build output
│   ├── Dockerfile.*   # Container configurations
│   └── vite.config.*  # Build configurations
├── backend/           # Python FastAPI backend
│   ├── app/          # Application code
│   ├── requirements.txt
│   └── Dockerfile.*  # Container configurations
├── admin-app/        # Admin panel
├── scripts/          # Build automation scripts
├── docker-compose.*  # Environment configurations
└── BUILD.md         # This documentation
```

## ⚙️ Environment Configuration

### Environment Files

Create environment-specific configuration files:

```bash
# Development
.env.development

# Staging
.env.staging

# Production
.env.production
```

### Required Environment Variables

```bash
# Application
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=your_secure_secret_key

# Database
MONGODB_URL=mongodb://localhost:27017/ace_social
REDIS_URL=redis://localhost:6379/0

# API Keys
OPENAI_API_KEY=your_openai_api_key
SENDGRID_API_KEY=your_sendgrid_api_key
LEMON_SQUEEZY_API_KEY=your_lemon_squeezy_api_key

# Security
CORS_ORIGINS=["https://yourdomain.com"]
```

## 🔨 Build Commands

### Core Commands

```bash
# Install all dependencies
npm run install:all

# Development
npm run dev                    # All services
npm run dev:frontend          # Frontend only
npm run dev:backend           # Backend only
npm run dev:admin             # Admin panel only

# Building
npm run build                 # All components
npm run build:frontend        # Frontend only
npm run build:backend         # Backend only
npm run build:admin           # Admin panel only
npm run build:production      # Optimized production build

# Docker
npm run build:docker          # Build Docker images
npm run start                 # Start with Docker Compose
npm run start:dev             # Development environment
npm run start:prod            # Production environment
npm run stop                  # Stop all services

# Testing
npm run test                  # All tests
npm run test:frontend         # Frontend tests
npm run test:backend          # Backend tests
npm run lint                  # Code linting

# Utilities
npm run clean                 # Clean build artifacts
npm run verify                # Verify build configuration
npm run health                # Health check all services
```

### Advanced Commands

```bash
# Performance
npm run build:analyze         # Bundle analysis
npm run performance:audit     # Performance audit

# Security
npm run security:scan         # Security vulnerability scan
npm run security:audit        # Security audit

# Deployment
npm run deploy:staging        # Deploy to staging
npm run deploy:production     # Deploy to production
```

## 🐳 Docker Deployment

### Development Environment

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Environment

```bash
# Build and start production
docker-compose -f docker-compose.prod.yml up -d

# Monitor services
docker-compose ps
docker-compose logs -f backend
```

### Container Health Checks

All containers include health checks:

- **Backend**: HTTP health endpoint
- **Frontend**: Nginx status
- **MongoDB**: Database ping
- **Redis**: Redis ping

## 🧪 Testing & Validation

### Test Types

1. **Unit Tests**: Component and function testing
2. **Integration Tests**: API and database testing
3. **E2E Tests**: Full user workflow testing
4. **Performance Tests**: Load and stress testing
5. **Security Tests**: Vulnerability scanning

### Running Tests

```bash
# All tests
npm run test

# Specific test types
npm run test:unit
npm run test:integration
npm run test:e2e
npm run test:performance
npm run test:security

# Coverage reports
npm run test:coverage
```

### Build Validation

```bash
# Comprehensive build validation
npm run verify

# Specific validations
npm run verify:dependencies
npm run verify:security
npm run verify:performance
npm run verify:docker
```

## 📊 Performance Optimization

### Bundle Size Targets

- **Frontend**: <2MB gzipped
- **Backend**: <50MB container
- **Admin**: <1MB gzipped

### Optimization Techniques

1. **Code Splitting**: Dynamic imports for route-based splitting
2. **Tree Shaking**: Remove unused code
3. **Minification**: Compress JavaScript and CSS
4. **Image Optimization**: WebP conversion and compression
5. **Caching**: Aggressive caching strategies

### Performance Monitoring

```bash
# Bundle analysis
npm run build:analyze

# Performance audit
npm run performance:audit

# Lighthouse CI
npm run lighthouse:ci
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

The platform includes automated CI/CD pipelines:

1. **Pull Request Checks**
   - Code linting
   - Unit tests
   - Security scans
   - Build verification

2. **Staging Deployment**
   - Integration tests
   - Performance tests
   - Automated deployment

3. **Production Deployment**
   - Manual approval
   - Blue-green deployment
   - Health checks
   - Rollback capability

### Pipeline Configuration

See `.github/workflows/` for complete pipeline definitions.

## 🔒 Security Guidelines

### Build Security

1. **Dependency Scanning**: Automated vulnerability checks
2. **Secret Management**: Environment-based secrets
3. **Container Security**: Non-root users, minimal images
4. **Code Analysis**: Static security analysis

### Security Commands

```bash
# Security audit
npm audit
npm run security:scan

# Container security
docker scan ace-social:latest

# Dependency updates
npm update
npm run security:update
```

## 🐛 Troubleshooting

### Common Issues

#### Build Failures

```bash
# Clear caches and rebuild
npm run clean
npm run install:all
npm run build
```

#### Memory Issues

```bash
# Increase Node.js memory
export NODE_OPTIONS="--max-old-space-size=8192"
npm run build
```

#### Docker Issues

```bash
# Clean Docker system
docker system prune -af
docker volume prune -f

# Rebuild containers
npm run build:docker
```

### Performance Issues

1. **Slow builds**: Enable build caching
2. **Large bundles**: Run bundle analysis
3. **Memory errors**: Increase Node.js memory limit

### Getting Help

- **Documentation**: Check this BUILD.md file
- **Issues**: Create GitHub issue with build logs
- **Logs**: Check `logs/` directory for detailed logs

---

**Last Updated**: 2025-01-06  
**Maintainer**: ACE Social Platform Team  
**Version**: 2.0.0

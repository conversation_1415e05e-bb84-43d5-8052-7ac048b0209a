// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  Button,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  AlertTitle,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Public as PublicIcon,
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../contexts/NotificationContext';
import api from '../../api';
import RecommendationCard from '../../components/scheduling/RecommendationCard';
import RecommendationDetails from '../../components/scheduling/RecommendationDetails';

/**
 * Smart Scheduling Recommendations Page
 * 
 * Displays AI-powered recommendations for optimal posting times based on:
 * - Historical engagement data
 * - ICP characteristics
 * - Audience location data
 * 
 * Features:
 * - Filter recommendations by platform
 * - View detailed reasoning for each recommendation
 * - One-click scheduling for recommended times
 * - Visual indicators for recommendation confidence
 */
const SmartSchedulingRecommendationsPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  // State for recommendations
  const [recommendations, setRecommendations] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedPlatform, setSelectedPlatform] = useState('all');
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedRecommendation, setSelectedRecommendation] = useState(null);
  const [showDetailPanel, setShowDetailPanel] = useState(false);
  const [error, setError] = useState(null);
  const [connectedAccounts, setConnectedAccounts] = useState([]);
  
  // Tabs for different time periods
  const timePeriodTabs = useMemo(() => [
    { label: 'This Week', value: 'this_week' },
    { label: 'Next Week', value: 'next_week' },
    { label: 'This Month', value: 'this_month' },
  ], []);
  
  // Platform options based on connected accounts
  const platformOptions = useMemo(() => {
    const options = [{ label: 'All Platforms', value: 'all' }];

    // Add connected platforms
    connectedAccounts.forEach(account => {
      if (!options.find(opt => opt.value === account.platform)) {
        options.push({
          label: account.platform.charAt(0).toUpperCase() + account.platform.slice(1),
          value: account.platform
        });
      }
    });

    // If no connected accounts, show default platforms
    if (connectedAccounts.length === 0) {
      options.push(
        { label: 'LinkedIn', value: 'linkedin' },
        { label: 'Twitter', value: 'twitter' },
        { label: 'Facebook', value: 'facebook' },
        { label: 'Instagram', value: 'instagram' }
      );
    }

    return options;
  }, [connectedAccounts]);
  
  // Authentication check
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [isAuthenticated, navigate]);

  // Load connected accounts
  useEffect(() => {
    if (!isAuthenticated) return;

    const fetchConnectedAccounts = async () => {
      try {
        const response = await api.get('/api/social-media/accounts');
        setConnectedAccounts(response.data.accounts || []);
      } catch (error) {
        console.error('Error fetching connected accounts:', error);
        if (error.response?.status === 401) {
          navigate('/login');
          return;
        }
        // Don't show error notification for accounts as it's not critical
      }
    };

    fetchConnectedAccounts();
  }, [isAuthenticated, navigate]);

  // Function to fetch recommendations from API
  const fetchRecommendations = useCallback(async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();

      if (selectedPlatform !== 'all') {
        params.append('platform', selectedPlatform);
      }

      // Add time period from selected tab
      params.append('time_period', timePeriodTabs[selectedTab].value);

      // Add user timezone if available
      if (user?.timezone) {
        params.append('timezone', user.timezone);
      }

      // Fetch recommendations
      const response = await api.get(`/api/posting-time/recommendations?${params.toString()}`);

      // Handle different response formats
      if (response.data && response.data.combined_recommendations) {
        setRecommendations(response.data);
        showSuccessNotification('Scheduling recommendations loaded successfully');
      } else {
        setRecommendations({
          combined_recommendations: {},
          message: 'No recommendations available for the selected criteria. Try connecting more social media accounts or publishing more content.'
        });
      }
    } catch (error) {
      console.error('Error fetching scheduling recommendations:', error);

      if (error.response?.status === 401) {
        navigate('/login');
        return;
      } else if (error.response?.status === 404) {
        setError('Scheduling recommendations service is not available');
      } else if (error.response?.status === 400) {
        setError(error.response?.data?.detail || 'Invalid request parameters');
      } else if (error.response?.status >= 500) {
        setError('Service temporarily unavailable. Please try again later.');
      } else {
        setError(error.response?.data?.detail || 'Failed to load scheduling recommendations');
      }

      showErrorNotification(error.response?.data?.detail || 'Failed to load scheduling recommendations');
    } finally {
      setLoading(false);
    }
  }, [selectedPlatform, selectedTab, timePeriodTabs, user?.timezone, isAuthenticated, navigate, showSuccessNotification, showErrorNotification]);

  // Load recommendations on component mount and when filters change
  useEffect(() => {
    fetchRecommendations();
  }, [fetchRecommendations]);
  
  // Function to get color based on confidence level
  const getConfidenceColor = (confidence) => {
    switch (confidence.toLowerCase()) {
      case 'high':
        return theme.palette.success.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.info.main;
      default:
        return theme.palette.primary.main;
    }
  };
  
  // Handle platform filter change
  const handlePlatformChange = (event) => {
    setSelectedPlatform(event.target.value);
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
    // Fetch new recommendations for the selected time period
    fetchRecommendations();
  };
  
  // Handle recommendation selection
  const handleRecommendationSelect = (recommendation) => {
    setSelectedRecommendation(recommendation);
    setShowDetailPanel(true);
  };
  
  // Handle scheduling content for a recommended time
  const handleScheduleContent = (recommendation) => {
    // Get day of week and hour from recommendation
    const { day_of_week, hour } = recommendation;
    
    // Calculate target date
    const now = new Date();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    
    // Calculate days to add to get to the target day
    let daysToAdd = day_of_week - currentDay;
    if (daysToAdd <= 0) {
      daysToAdd += 7; // Move to next week if day has passed
    }
    
    // Create target date
    const targetDate = new Date(now);
    targetDate.setDate(now.getDate() + daysToAdd);
    targetDate.setHours(hour, 0, 0, 0);
    
    // Format date for URL
    const formattedDate = targetDate.toISOString();
    
    // Navigate to content creation with scheduled time
    navigate('/content/generator', { 
      state: { 
        scheduledDate: formattedDate,
        platform: recommendation.platform === 'all' ? null : recommendation.platform,
        optimalTime: true,
      } 
    });
    
    showSuccessNotification('Scheduling time selected. Create your content now.');
  };
  
  // Handle back button in detail panel
  const handleBackToList = () => {
    setShowDetailPanel(false);
  };
  
  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1">
          Smart Scheduling Recommendations
        </Typography>
        
        <Button 
          variant="outlined" 
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/scheduling')}
        >
          Back to Calendar
        </Button>
      </Box>
      
      <Typography variant="body1" paragraph>
        Get AI-powered recommendations for the best times to post on social media based on your audience&apos;s behavior,
        ICP characteristics, and historical engagement data.
      </Typography>

      {/* Connected Accounts Warning */}
      {connectedAccounts.length === 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <AlertTitle>No Connected Social Media Accounts</AlertTitle>
          You don&apos;t have any connected social media accounts yet. Connect accounts to get personalized posting time recommendations.
          <Button
            variant="outlined"
            size="small"
            sx={{ mt: 1, ml: 1 }}
            onClick={() => navigate('/profile/social-media')}
          >
            Connect Accounts
          </Button>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <AlertTitle>Error</AlertTitle>
          {error}
        </Alert>
      )}
      
      {/* Filters and Tabs */}
      <Paper sx={{ mb: 3, p: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined" size="small">
              <InputLabel id="platform-filter-label">Platform</InputLabel>
              <Select
                labelId="platform-filter-label"
                id="platform-filter"
                value={selectedPlatform}
                onChange={handlePlatformChange}
                label="Platform"
                startAdornment={<PublicIcon sx={{ mr: 1 }} />}
              >
                {platformOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Tabs
              value={selectedTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant={isMobile ? "fullWidth" : "standard"}
              aria-label="time period tabs"
            >
              {timePeriodTabs.map((tab) => (
                <Tab key={tab.value} label={tab.label} />
              ))}
            </Tabs>
          </Grid>
          
          <Grid item xs={12} md={2} sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Tooltip title="Refresh recommendations">
              <IconButton onClick={fetchRecommendations} color="primary">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Grid>
        </Grid>
      </Paper>
      
      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Recommendations List */}
        <Grid item xs={12} md={showDetailPanel ? (isTablet ? 12 : 6) : 12}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : recommendations ? (
            <Box>
              {Object.entries(recommendations.combined_recommendations || {}).length > 0 ? (
                <Grid container spacing={2}>
                  {Object.entries(recommendations.combined_recommendations || {}).map(([day, timeSlots]) => (
                    <Grid item xs={12} sm={6} md={showDetailPanel ? 12 : 4} lg={showDetailPanel ? 6 : 4} key={day}>
                      <Card 
                        elevation={2}
                        sx={{ 
                          height: '100%',
                          transition: 'all 0.2s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 4,
                          }
                        }}
                      >
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            {day}
                          </Typography>
                          
                          <Divider sx={{ mb: 2 }} />
                          
                          {timeSlots.length > 0 ? (
                            timeSlots.map((slot, index) => (
                              <RecommendationCard
                                key={index}
                                recommendation={slot}
                                day={day}
                                onSelect={() => handleRecommendationSelect({ ...slot, day })}
                                onSchedule={() => handleScheduleContent({ ...slot, day_of_week: getDayOfWeek(day), hour: slot.start_hour })}
                                getConfidenceColor={getConfidenceColor}
                              />
                            ))
                          ) : (
                            <Alert severity="info">
                              No recommended time slots available for {day}.
                            </Alert>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert severity="info">
                  <AlertTitle>No recommendations available</AlertTitle>
                  {recommendations?.message ||
                   'We don\'t have enough data to provide recommendations for the selected filters.'}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      To get personalized recommendations:
                    </Typography>
                    <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                      <li>Connect more social media accounts</li>
                      <li>Publish content regularly to build historical data</li>
                      <li>Try selecting a different platform or time period</li>
                      <li>Ensure your content has engagement data</li>
                    </ul>
                    <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => navigate('/profile/social-media')}
                      >
                        Connect Accounts
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => navigate('/content/generator')}
                      >
                        Create Content
                      </Button>
                    </Box>
                  </Box>
                </Alert>
              )}
            </Box>
          ) : (
            <Alert severity="error">
              <AlertTitle>Error loading recommendations</AlertTitle>
              Failed to load scheduling recommendations. Please try again later.
            </Alert>
          )}
        </Grid>
        
        {/* Detail Panel */}
        {showDetailPanel && selectedRecommendation && (
          <Grid item xs={12} md={isTablet ? 12 : 6}>
            <RecommendationDetails
              recommendation={selectedRecommendation}
              onBack={handleBackToList}
              onSchedule={() => handleScheduleContent({
                ...selectedRecommendation,
                day_of_week: getDayOfWeek(selectedRecommendation.day),
                hour: selectedRecommendation.start_hour
              })}
              getConfidenceColor={getConfidenceColor}
            />
          </Grid>
        )}
      </Grid>
      
      {/* Explanation Section */}
      <Paper sx={{ mt: 4, p: 3 }}>
        <Typography variant="h6" gutterBottom>
          How Recommendations Are Generated
        </Typography>
        
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <TrendingUpIcon sx={{ mr: 2, color: theme.palette.primary.main }} />
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Historical Engagement
                </Typography>
                <Typography variant="body2">
                  Analysis of your past content performance to identify patterns in engagement rates across different days and times.
                </Typography>
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <PeopleIcon sx={{ mr: 2, color: theme.palette.secondary.main }} />
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  ICP Characteristics
                </Typography>
                <Typography variant="body2">
                  Behavior patterns of your target audience based on your defined Ideal Customer Profiles (ICPs).
                </Typography>
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
              <PublicIcon sx={{ mr: 2, color: theme.palette.success.main }} />
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Location Data
                </Typography>
                <Typography variant="body2">
                  Time zones where your audience is located, ensuring your content reaches them when they&apos;re most active.
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
        
        <Alert severity="info" sx={{ mt: 3 }}>
          <AlertTitle>Improving Recommendations</AlertTitle>
          The more content you publish, the more accurate these recommendations will become. Our AI continuously learns from your content performance to refine future recommendations.
        </Alert>
      </Paper>
    </Box>
  );
};

// Helper function to convert day name to day of week number
const getDayOfWeek = (dayName) => {
  const days = {
    'Monday': 1,
    'Tuesday': 2,
    'Wednesday': 3,
    'Thursday': 4,
    'Friday': 5,
    'Saturday': 6,
    'Sunday': 0
  };
  return days[dayName] || 0;
};

export default SmartSchedulingRecommendationsPage;

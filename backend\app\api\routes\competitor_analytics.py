"""
API routes for competitor social media analytics.
"""
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel, Field

from app.models.user import User
from app.api.deps import get_current_active_user
from app.services.competitor_analytics import (
    CompetitorAnalyticsService,
    CompetitorMetrics,
    CompetitorComparison
)


logger = logging.getLogger(__name__)

router = APIRouter()

# Response models
class CompetitorMetricsResponse(BaseModel):
    """Response model for competitor metrics."""
    platform: str
    account_name: str
    followers_count: int
    following_count: int
    posts_count: int
    engagement_rate: float
    avg_likes_per_post: float
    avg_comments_per_post: float
    avg_shares_per_post: float
    posting_frequency: str
    last_post_date: Optional[datetime]
    top_content_types: List[str]
    peak_posting_times: List[str]
    hashtag_usage: List[str]
    last_updated: datetime

class CompetitorComparisonResponse(BaseModel):
    """Response model for competitor comparison."""
    competitor_id: str
    competitor_name: str
    metrics: List[CompetitorMetricsResponse]
    overall_score: float
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]

class AnalyticsRefreshRequest(BaseModel):
    """Request model for refreshing analytics."""
    competitor_ids: List[str]
    platforms: Optional[List[str]] = None
    force_refresh: bool = False

class BenchmarkResponse(BaseModel):
    """Response model for industry benchmarks."""
    platform: str
    industry_avg_engagement: float
    industry_avg_followers: int
    industry_avg_posting_frequency: str
    top_performing_content_types: List[str]
    recommended_posting_times: List[str]

@router.get("/competitors/{competitor_id}/metrics", response_model=List[CompetitorMetricsResponse])
async def get_competitor_metrics(
    competitor_id: str,
    platforms: Optional[List[str]] = Query(None, description="Platforms to analyze"),
    refresh: bool = Query(False, description="Force refresh data from APIs"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get social media metrics for a specific competitor.
    
    Args:
        competitor_id: The competitor's ID
        platforms: Optional list of platforms to analyze
        refresh: Whether to force refresh data from social media APIs
        
    Returns:
        List of competitor metrics for each platform
    """
    try:
        analytics_service = CompetitorAnalyticsService()
        
        if refresh:
            # Force refresh from APIs
            metrics = await analytics_service.fetch_competitor_metrics(
                competitor_id, platforms
            )
        else:
            # Try to get cached data first
            cached_metrics = await analytics_service._get_cached_metrics(
                competitor_id, platforms
            )
            
            if cached_metrics and not analytics_service._is_data_stale(cached_metrics):
                metrics = cached_metrics
            else:
                # Fetch fresh data
                metrics = await analytics_service.fetch_competitor_metrics(
                    competitor_id, platforms
                )
        
        # Convert to response format
        response_metrics = [
            CompetitorMetricsResponse(
                platform=m.platform,
                account_name=m.account_name,
                followers_count=m.followers_count,
                following_count=m.following_count,
                posts_count=m.posts_count,
                engagement_rate=m.engagement_rate,
                avg_likes_per_post=m.avg_likes_per_post,
                avg_comments_per_post=m.avg_comments_per_post,
                avg_shares_per_post=m.avg_shares_per_post,
                posting_frequency=m.posting_frequency,
                last_post_date=m.last_post_date,
                top_content_types=m.top_content_types,
                peak_posting_times=m.peak_posting_times,
                hashtag_usage=m.hashtag_usage,
                last_updated=m.last_updated
            )
            for m in metrics
        ]
        
        return response_metrics
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting competitor metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch competitor metrics")

@router.post("/competitors/compare", response_model=List[CompetitorComparisonResponse])
async def compare_competitors(
    competitor_ids: List[str],
    platforms: Optional[List[str]] = Query(None, description="Platforms to compare"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Compare multiple competitors across social media platforms.
    
    Args:
        competitor_ids: List of competitor IDs to compare
        platforms: Optional list of platforms to compare
        
    Returns:
        List of competitor comparison results
    """
    try:
        if len(competitor_ids) < 2:
            raise HTTPException(
                status_code=400, 
                detail="At least 2 competitors required for comparison"
            )
        
        if len(competitor_ids) > 10:
            raise HTTPException(
                status_code=400, 
                detail="Maximum 10 competitors allowed for comparison"
            )
        
        analytics_service = CompetitorAnalyticsService()
        comparisons = await analytics_service.compare_competitors(
            competitor_ids, platforms
        )
        
        # Convert to response format
        response_comparisons = [
            CompetitorComparisonResponse(
                competitor_id=c.competitor_id,
                competitor_name=c.competitor_name,
                metrics=[
                    CompetitorMetricsResponse(
                        platform=m.platform,
                        account_name=m.account_name,
                        followers_count=m.followers_count,
                        following_count=m.following_count,
                        posts_count=m.posts_count,
                        engagement_rate=m.engagement_rate,
                        avg_likes_per_post=m.avg_likes_per_post,
                        avg_comments_per_post=m.avg_comments_per_post,
                        avg_shares_per_post=m.avg_shares_per_post,
                        posting_frequency=m.posting_frequency,
                        last_post_date=m.last_post_date,
                        top_content_types=m.top_content_types,
                        peak_posting_times=m.peak_posting_times,
                        hashtag_usage=m.hashtag_usage,
                        last_updated=m.last_updated
                    )
                    for m in c.metrics
                ],
                overall_score=c.overall_score,
                strengths=c.strengths,
                weaknesses=c.weaknesses,
                recommendations=c.recommendations
            )
            for c in comparisons
        ]
        
        return response_comparisons
        
    except Exception as e:
        logger.error(f"Error comparing competitors: {e}")
        raise HTTPException(status_code=500, detail="Failed to compare competitors")

@router.post("/competitors/refresh")
async def refresh_competitor_analytics(
    request: AnalyticsRefreshRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """
    Refresh competitor analytics data from social media APIs.
    
    This endpoint triggers a background task to refresh data for multiple competitors.
    """
    try:
        analytics_service = CompetitorAnalyticsService()
        
        # Add background task to refresh data
        background_tasks.add_task(
            refresh_competitor_data_task,
            analytics_service,
            request.competitor_ids,
            request.platforms,
            request.force_refresh
        )
        
        return {
            "message": "Analytics refresh started",
            "competitor_ids": request.competitor_ids,
            "platforms": request.platforms,
            "estimated_completion": "5-10 minutes"
        }
        
    except Exception as e:
        logger.error(f"Error starting analytics refresh: {e}")
        raise HTTPException(status_code=500, detail="Failed to start analytics refresh")

@router.get("/benchmarks/{platform}", response_model=BenchmarkResponse)
async def get_industry_benchmarks(
    platform: str,
    industry: Optional[str] = Query(None, description="Industry category"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get industry benchmarks for a specific social media platform.
    
    Args:
        platform: The social media platform
        industry: Optional industry category for more specific benchmarks
        
    Returns:
        Industry benchmark data for the platform
    """
    try:
        analytics_service = CompetitorAnalyticsService()
        benchmarks = await analytics_service.get_industry_benchmarks(platform, industry)
        
        return BenchmarkResponse(
            platform=platform,
            industry_avg_engagement=benchmarks.get("avg_engagement", 0.0),
            industry_avg_followers=benchmarks.get("avg_followers", 0),
            industry_avg_posting_frequency=benchmarks.get("avg_posting_frequency", "unknown"),
            top_performing_content_types=benchmarks.get("top_content_types", []),
            recommended_posting_times=benchmarks.get("recommended_posting_times", [])
        )
        
    except Exception as e:
        logger.error(f"Error getting industry benchmarks: {e}")
        raise HTTPException(status_code=500, detail="Failed to get industry benchmarks")

@router.get("/competitors/{competitor_id}/insights")
async def get_competitor_insights(
    competitor_id: str,
    timeframe: str = Query("30d", description="Timeframe for insights (7d, 30d, 90d)"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get AI-powered insights for a specific competitor.
    
    Args:
        competitor_id: The competitor's ID
        timeframe: Timeframe for analysis
        
    Returns:
        AI-generated insights and recommendations
    """
    try:
        analytics_service: CompetitorAnalyticsService = CompetitorAnalyticsService()
        insights = await analytics_service.generate_competitor_insights(
            competitor_id, timeframe
        )
        
        return {
            "competitor_id": competitor_id,
            "timeframe": timeframe,
            "insights": insights.get("insights", []),
            "trends": insights.get("trends", []),
            "opportunities": insights.get("opportunities", []),
            "threats": insights.get("threats", []),
            "action_items": insights.get("action_items", []),
            "generated_at": datetime.utcnow()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating competitor insights: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate insights")

@router.get("/competitors/{competitor_id}/content-analysis")
async def analyze_competitor_content(
    competitor_id: str,
    platform: str = Query(..., description="Platform to analyze"),
    limit: int = Query(50, description="Number of recent posts to analyze"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Analyze competitor's content strategy and performance.
    
    Args:
        competitor_id: The competitor's ID
        platform: Platform to analyze
        limit: Number of recent posts to analyze
        
    Returns:
        Content analysis results
    """
    try:
        analytics_service: CompetitorAnalyticsService = CompetitorAnalyticsService()
        analysis = await analytics_service.analyze_competitor_content(
            competitor_id, platform, limit
        )
        
        return {
            "competitor_id": competitor_id,
            "platform": platform,
            "posts_analyzed": analysis.get("posts_count", 0),
            "content_themes": analysis.get("content_themes", []),
            "top_performing_posts": analysis.get("top_posts", []),
            "content_calendar_patterns": analysis.get("calendar_patterns", {}),
            "hashtag_strategy": analysis.get("hashtag_strategy", {}),
            "engagement_patterns": analysis.get("engagement_patterns", {}),
            "recommendations": analysis.get("recommendations", []),
            "analyzed_at": datetime.utcnow()
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error analyzing competitor content: {e}")
        raise HTTPException(status_code=500, detail="Failed to analyze content")

# Background task functions
async def refresh_competitor_data_task(
    analytics_service: CompetitorAnalyticsService,
    competitor_ids: List[str],
    platforms: Optional[List[str]],
    force_refresh: bool
):
    """Background task to refresh competitor data."""
    try:
        for competitor_id in competitor_ids:
            await analytics_service.fetch_competitor_metrics(
                competitor_id, platforms
            )
            logger.info(f"Refreshed analytics for competitor {competitor_id}")
            
    except Exception as e:
        logger.error(f"Error in background refresh task: {e}")

@router.get("/health")
async def health_check():
    """Health check endpoint for competitor analytics service."""
    return {
        "status": "healthy",
        "service": "competitor_analytics",
        "timestamp": datetime.utcnow(),
        "features": [
            "competitor_metrics",
            "competitor_comparison", 
            "industry_benchmarks",
            "content_analysis",
            "ai_insights"
        ]
    }

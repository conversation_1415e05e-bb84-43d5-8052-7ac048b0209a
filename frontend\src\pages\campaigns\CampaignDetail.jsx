// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Divider,
  CircularProgress,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  ListItemSecondaryAction,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CampaignIcon from '@mui/icons-material/Campaign';
import EditIcon from '@mui/icons-material/Edit';
import PauseIcon from '@mui/icons-material/Pause';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ImageIcon from '@mui/icons-material/Image';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import BarChartIcon from '@mui/icons-material/BarChart';
import PersonIcon from '@mui/icons-material/Person';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';
import CampaignImageManipulator from '../../components/campaigns/CampaignImageManipulator';

const CampaignDetail = () => {
  const navigate = useNavigate();
  const { campaignId } = useParams();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [loading, setLoading] = useState(true);
  const [campaign, setCampaign] = useState(null);
  const [icp, setIcp] = useState(null);
  const [contents, setContents] = useState([]);
  const [tabValue, setTabValue] = useState(0);

  // Fetch campaign details, ICP, and content
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch campaign details
        const campaignResponse = await api.get(`/api/campaigns/${campaignId}`);
        setCampaign(campaignResponse.data);

        // Fetch ICP details
        const icpResponse = await api.get(`/api/icps/${campaignResponse.data.icp_id}`);
        setIcp(icpResponse.data);

        // Fetch campaign content
        if (campaignResponse.data.content_ids && campaignResponse.data.content_ids.length > 0) {
          const contentPromises = campaignResponse.data.content_ids.map(contentId =>
            api.get(`/api/content/${contentId}`)
          );

          const contentResponses = await Promise.all(contentPromises);
          setContents(contentResponses.map(response => response.data));
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        showErrorNotification('Failed to load campaign details');
      } finally {
        setLoading(false);
      }
    };

    if (campaignId) {
      fetchData();
    }
  }, [campaignId, showErrorNotification]);

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Handle toggling campaign status
  const handleToggleCampaignStatus = async () => {
    const newStatus = campaign.status === 'active' ? 'paused' : 'active';

    try {
      await api.put(`/api/campaigns/${campaign.id}`, { status: newStatus });

      // Update local state
      setCampaign({
        ...campaign,
        status: newStatus
      });

      showSuccessNotification(`Campaign ${newStatus === 'active' ? 'activated' : 'paused'} successfully`);
    } catch (error) {
      console.error('Error updating campaign status:', error);
      showErrorNotification('Failed to update campaign status');
    }
  };

  // Calculate campaign progress
  const calculateProgress = () => {
    if (!campaign || !campaign.start_date || !campaign.end_date) return 0;

    const start = new Date(campaign.start_date).getTime();
    const end = new Date(campaign.end_date).getTime();
    const now = new Date().getTime();

    if (now <= start) return 0;
    if (now >= end) return 100;

    return Math.round(((now - start) / (end - start)) * 100);
  };

  // Filter content based on status
  const getFilteredContent = () => {
    if (!contents || contents.length === 0) return [];

    switch (tabValue) {
      case 0: // All
        return contents;
      case 1: // Published
        return contents.filter(content => content.status === 'published');
      case 2: // Scheduled
        return contents.filter(content => content.status === 'scheduled');
      case 3: // Draft
        return contents.filter(content => content.status === 'draft');
      default:
        return contents;
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ py: 3, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  // If campaign not found
  if (!campaign) {
    return (
      <Box sx={{ py: 3 }}>
        <Typography variant="h5" color="error" align="center">
          Campaign not found
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate('/campaigns')}
            startIcon={<ArrowBackIcon />}
          >
            Back to Campaigns
          </Button>
        </Box>
      </Box>
    );
  }

  const progress = calculateProgress();
  const filteredContent = getFilteredContent();

  return (
    <Box sx={{ py: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton
          color="primary"
          onClick={() => navigate('/campaigns')}
          sx={{ mr: 1 }}
        >
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          <CampaignIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          {campaign.name}
        </Typography>

        <Box>
          <Tooltip title="Edit Campaign">
            <Button
              variant="outlined"
              color="primary"
              onClick={() => navigate(`/campaigns/${campaign.id}/edit`)}
              startIcon={<EditIcon />}
              sx={{ mr: 1 }}
            >
              Edit
            </Button>
          </Tooltip>

          {campaign.status !== 'completed' && campaign.status !== 'draft' && (
            <Button
              variant="contained"
              color={campaign.status === 'active' ? 'warning' : 'success'}
              onClick={handleToggleCampaignStatus}
              startIcon={campaign.status === 'active' ? <PauseIcon /> : <PlayArrowIcon />}
            >
              {campaign.status === 'active' ? 'Pause Campaign' : 'Activate Campaign'}
            </Button>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Campaign Overview
                </Typography>
                <Chip
                  label={campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                  color={
                    campaign.status === 'active' ? 'success' :
                    campaign.status === 'paused' ? 'warning' :
                    campaign.status === 'completed' ? 'info' : 'default'
                  }
                />
              </Box>

              <Typography variant="body1" paragraph>
                {campaign.description}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Date Range
                    </Typography>
                    <Typography variant="body2">
                      {new Date(campaign.start_date).toLocaleDateString()} - {campaign.end_date ? new Date(campaign.end_date).toLocaleDateString() : 'Ongoing'}
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Platforms
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                      {campaign.platforms.map((platform, index) => (
                        <Chip
                          key={index}
                          label={platform.charAt(0).toUpperCase() + platform.slice(1)}
                          size="small"
                        />
                      ))}
                    </Box>
                  </Paper>
                </Grid>
              </Grid>

              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                  <Typography variant="body2" color="textSecondary">
                    Campaign Progress
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {progress}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={progress}
                  color={
                    campaign.status === 'active' ? 'success' :
                    campaign.status === 'paused' ? 'warning' :
                    'primary'
                  }
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              {campaign.goals && campaign.goals.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Campaign Goals
                  </Typography>
                  <Grid container spacing={2}>
                    {campaign.goals.map((goal, index) => (
                      <Grid item xs={12} sm={6} key={index}>
                        <Paper sx={{ p: 2 }}>
                          <Typography variant="subtitle2">
                            {goal.goal_type.charAt(0).toUpperCase() + goal.goal_type.slice(1)}
                          </Typography>
                          <Typography variant="body2" paragraph>
                            {goal.description}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                            <Typography variant="caption" color="textSecondary">
                              Target: {goal.target_metric}
                            </Typography>
                            <Typography variant="caption" fontWeight="bold">
                              {goal.target_value}
                            </Typography>
                          </Box>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Campaign Content
              </Typography>

              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                sx={{ mb: 2 }}
              >
                <Tab label={`All (${contents.length})`} />
                <Tab label={`Published (${contents.filter(c => c.status === 'published').length})`} />
                <Tab label={`Scheduled (${contents.filter(c => c.status === 'scheduled').length})`} />
                <Tab label={`Draft (${contents.filter(c => c.status === 'draft').length})`} />
                <Tab
                  icon={<AutoAwesomeIcon />}
                  iconPosition="start"
                  label="Image Manipulation"
                />
              </Tabs>

              {tabValue === 4 ? (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Image Manipulation
                  </Typography>
                  <CampaignImageManipulator
                    campaignId={campaign.id}
                    campaignName={campaign.name}
                    onImagesGenerated={(images) => {
                      // Handle generated images if needed
                      showSuccessNotification(`${images.length} images generated successfully for campaign: ${campaign.name}`);
                    }}
                  />
                </Box>
              ) : filteredContent.length === 0 ? (
                <Paper sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1" color="textSecondary">
                    No content found in this category
                  </Typography>
                </Paper>
              ) : (
                <List>
                  {filteredContent.map((content) => (
                    <Paper key={content.id} sx={{ mb: 2 }}>
                      <ListItem alignItems="flex-start">
                        <ListItemAvatar>
                          {content.images && content.images.length > 0 ? (
                            <Avatar
                              variant="rounded"
                              src={content.images[0].url}
                              alt={content.title}
                              sx={{ width: 80, height: 80, mr: 1 }}
                            />
                          ) : (
                            <Avatar
                              variant="rounded"
                              sx={{ width: 80, height: 80, mr: 1, bgcolor: 'primary.light' }}
                            >
                              <ImageIcon />
                            </Avatar>
                          )}
                        </ListItemAvatar>
                        <ListItemText
                          primary={content.title}
                          secondary={
                            <>
                              <Typography
                                component="span"
                                variant="body2"
                                color="textPrimary"
                                sx={{ display: 'inline', mr: 1 }}
                              >
                                {content.text_content.substring(0, 100)}...
                              </Typography>
                              <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                <Chip
                                  label={content.status.charAt(0).toUpperCase() + content.status.slice(1)}
                                  size="small"
                                  color={
                                    content.status === 'published' ? 'success' :
                                    content.status === 'scheduled' ? 'primary' :
                                    'default'
                                  }
                                />
                                {content.platforms.map((platform, i) => (
                                  <Chip
                                    key={i}
                                    label={platform.charAt(0).toUpperCase() + platform.slice(1)}
                                    size="small"
                                    variant="outlined"
                                  />
                                ))}
                                {content.scheduled_for && (
                                  <Chip
                                    icon={<CalendarMonthIcon />}
                                    label={new Date(content.scheduled_for).toLocaleString()}
                                    size="small"
                                    variant="outlined"
                                    color="primary"
                                  />
                                )}
                              </Box>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Tooltip title="View Content">
                            <IconButton
                              edge="end"
                              onClick={() => navigate(`/content/${content.id}`)}
                            >
                              <VisibilityIcon />
                            </IconButton>
                          </Tooltip>
                        </ListItemSecondaryAction>
                      </ListItem>
                    </Paper>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          {icp && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Target ICP
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1">
                    {icp.name}
                  </Typography>
                  <Button
                    variant="text"
                    size="small"
                    onClick={() => navigate(`/icps/${icp.id}`)}
                  >
                    View Details
                  </Button>
                </Box>

                <Typography variant="body2" color="textSecondary" paragraph>
                  {icp.description}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" gutterBottom>
                  Demographics
                </Typography>
                <Grid container spacing={1} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Typography variant="caption" color="textSecondary">
                      Industry:
                    </Typography>
                    <Typography variant="body2">
                      {icp.demographics.industry}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="caption" color="textSecondary">
                      Company Size:
                    </Typography>
                    <Typography variant="body2">
                      {icp.demographics.company_size}
                    </Typography>
                  </Grid>
                </Grid>

                <Typography variant="subtitle2" gutterBottom>
                  Decision Maker
                </Typography>
                <Grid container spacing={1} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Typography variant="caption" color="textSecondary">
                      Title:
                    </Typography>
                    <Typography variant="body2">
                      {icp.decision_maker.title}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="caption" color="textSecondary">
                      Department:
                    </Typography>
                    <Typography variant="body2">
                      {icp.decision_maker.department}
                    </Typography>
                  </Grid>
                </Grid>

                {icp.pain_points && icp.pain_points.length > 0 && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      Pain Points
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                      {icp.pain_points.map((pain, index) => (
                        <Chip
                          key={index}
                          label={pain.description}
                          size="small"
                          color={pain.severity === 'High' ? 'error' : 'default'}
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </>
                )}

                {icp.content_preferences && icp.content_preferences.length > 0 && (
                  <>
                    <Typography variant="subtitle2" gutterBottom>
                      Content Preferences
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {icp.content_preferences.map((pref, index) => (
                        <Chip
                          key={index}
                          label={`${pref.content_type} (${pref.tone})`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </>
                )}
              </CardContent>
            </Card>
          )}

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <BarChartIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Campaign Performance
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {campaign.performance?.total_impressions || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Total Impressions
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {campaign.performance?.total_engagements || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Total Engagements
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {campaign.performance?.engagement_rate?.toFixed(2) || 0}%
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Engagement Rate
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {campaign.performance?.clicks || 0}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Clicks
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>

              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Typography variant="caption" color="textSecondary">
                  Last updated: {campaign.performance?.last_updated ?
                    new Date(campaign.performance.last_updated).toLocaleString() :
                    'Never'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CampaignDetail;

"""
Monitoring and Analytics Service for Image Metadata Removal

This service provides comprehensive monitoring, analytics, and alerting
for the image metadata removal feature in the ACEO platform.
"""

import logging
import time
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, asdict
import json

from app.core.config import settings
from app.core.monitoring import record_addon_metrics
from app.services.cache_service import CacheService
from app.utils.redis_cache import get_redis_client
from app.services.addon_usage_tracking import track_addon_usage, UsageType

logger = logging.getLogger(__name__)

@dataclass
class MetadataRemovalMetrics:
    """Metrics for metadata removal operations."""
    timestamp: datetime
    operation_type: str  # "single", "batch", "job_handler"
    user_id: str
    image_count: int
    processing_time_ms: float
    success: bool
    original_size_bytes: Optional[int] = None
    processed_size_bytes: Optional[int] = None
    size_reduction_percent: Optional[float] = None
    ai_tags_removed: Optional[int] = None
    total_metadata_tags_removed: Optional[int] = None
    error_message: Optional[str] = None
    cache_hit: bool = False

@dataclass
class MetadataRemovalStats:
    """Aggregated statistics for metadata removal."""
    total_operations: int
    total_images_processed: int
    total_processing_time_ms: float
    average_processing_time_ms: float
    success_rate: float
    cache_hit_rate: float
    total_size_reduction_bytes: int
    average_size_reduction_percent: float
    total_ai_tags_removed: int
    total_metadata_tags_removed: int
    error_count: int
    performance_target_met_rate: float

class MetadataRemovalMonitor:
    """
    Comprehensive monitoring service for metadata removal operations.
    """
    
    def __init__(self):
        self.cache_service = CacheService(namespace="metadata_monitoring")
        self.metrics_cache_ttl = 3600  # 1 hour
        self.stats_cache_ttl = 300     # 5 minutes
        
    async def record_operation(self, metrics: MetadataRemovalMetrics) -> None:
        """
        Record a metadata removal operation for monitoring and analytics.
        
        Args:
            metrics: Metrics data for the operation
        """
        try:
            # Store individual metric
            await self._store_metric(metrics)
            
            # Update aggregated statistics
            await self._update_aggregated_stats(metrics)
            
            # Record Prometheus metrics
            await self._record_prometheus_metrics(metrics)
            
            # Check for performance alerts
            await self._check_performance_alerts(metrics)
            
            # Track usage for billing
            if settings.METADATA_REMOVAL_TRACK_USAGE and metrics.success:
                await self._track_usage_for_billing(metrics)
                
        except Exception as e:
            logger.error(f"Error recording metadata removal metrics: {str(e)}")
    
    async def get_user_stats(
        self, 
        user_id: str, 
        hours: int = 24
    ) -> Optional[MetadataRemovalStats]:
        """
        Get metadata removal statistics for a specific user.
        
        Args:
            user_id: User ID to get stats for
            hours: Number of hours to look back
            
        Returns:
            User statistics or None if no data
        """
        try:
            cache_key = f"user_stats:{user_id}:{hours}h"
            cached_stats = await self.cache_service.get(cache_key)
            
            if cached_stats:
                return MetadataRemovalStats(**cached_stats)
            
            # Calculate stats from stored metrics
            stats = await self._calculate_user_stats(user_id, hours)
            
            if stats:
                # Cache the results
                await self.cache_service.set(
                    cache_key, 
                    asdict(stats), 
                    ttl=self.stats_cache_ttl
                )
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting user stats for {user_id}: {str(e)}")
            return None
    
    async def get_global_stats(self, hours: int = 24) -> Optional[MetadataRemovalStats]:
        """
        Get global metadata removal statistics.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Global statistics or None if no data
        """
        try:
            cache_key = f"global_stats:{hours}h"
            cached_stats = await self.cache_service.get(cache_key)
            
            if cached_stats:
                return MetadataRemovalStats(**cached_stats)
            
            # Calculate stats from stored metrics
            stats = await self._calculate_global_stats(hours)
            
            if stats:
                # Cache the results
                await self.cache_service.set(
                    cache_key, 
                    asdict(stats), 
                    ttl=self.stats_cache_ttl
                )
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting global stats: {str(e)}")
            return None
    
    async def get_performance_alerts(self, hours: int = 1) -> List[Dict[str, Any]]:
        """
        Get performance alerts for metadata removal operations.
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            List of performance alerts
        """
        try:
            cache_key = f"performance_alerts:{hours}h"
            cached_alerts = await self.cache_service.get(cache_key)
            
            if cached_alerts:
                return cached_alerts
            
            alerts = await self._get_performance_alerts(hours)
            
            # Cache the results for a shorter time
            await self.cache_service.set(
                cache_key, 
                alerts, 
                ttl=60  # 1 minute cache
            )
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error getting performance alerts: {str(e)}")
            return []
    
    async def _store_metric(self, metrics: MetadataRemovalMetrics) -> None:
        """Store individual metric in Redis."""
        try:
            redis_cache = await get_redis_client()
            if not redis_cache:
                return

            # Create metric key with timestamp for easy pattern matching
            metric_key = f"metadata_metric:{metrics.user_id}:{int(metrics.timestamp.timestamp())}"

            # Store metric data using the cache interface
            await redis_cache.set(
                metric_key,
                asdict(metrics),
                ttl=self.metrics_cache_ttl,
                serialize_json=True
            )

        except Exception as e:
            logger.error(f"Error storing metric: {str(e)}")
    
    async def _update_aggregated_stats(self, metrics: MetadataRemovalMetrics) -> None:
        """Update aggregated statistics."""
        try:
            # Update user aggregated stats
            await self._update_user_aggregated_stats(metrics)
            
            # Update global aggregated stats
            await self._update_global_aggregated_stats(metrics)
            
        except Exception as e:
            logger.error(f"Error updating aggregated stats: {str(e)}")

    async def _update_user_aggregated_stats(self, metrics: MetadataRemovalMetrics) -> None:
        """Update user-specific aggregated statistics."""
        try:
            # For now, we'll rely on the real-time calculation from stored metrics
            # In a production system, you might want to maintain running totals
            # metrics parameter is reserved for future use
            _ = metrics  # Acknowledge the parameter
        except Exception as e:
            logger.error(f"Error updating user aggregated stats: {str(e)}")

    async def _update_global_aggregated_stats(self, metrics: MetadataRemovalMetrics) -> None:
        """Update global aggregated statistics."""
        try:
            # For now, we'll rely on the real-time calculation from stored metrics
            # In a production system, you might want to maintain running totals
            # metrics parameter is reserved for future use
            _ = metrics  # Acknowledge the parameter
        except Exception as e:
            logger.error(f"Error updating global aggregated stats: {str(e)}")
    
    async def _record_prometheus_metrics(self, metrics: MetadataRemovalMetrics) -> None:
        """Record metrics in Prometheus format."""
        try:
            # Record operation count - using simplified format
            record_addon_metrics(
                f"metadata_removal_{metrics.operation_type}_operations",
                "system",
                1
            )

            # Record success/failure
            if metrics.success:
                record_addon_metrics("metadata_removal_success", "system", 1)
            else:
                record_addon_metrics("metadata_removal_errors", "system", 1)

            # Record cache hits
            if metrics.cache_hit:
                record_addon_metrics("metadata_removal_cache_hits", "system", 1)

            # Record AI tags removed
            if metrics.ai_tags_removed is not None:
                record_addon_metrics("metadata_removal_ai_tags", "system", metrics.ai_tags_removed)

        except Exception as e:
            logger.error(f"Error recording Prometheus metrics: {str(e)}")
    
    async def _check_performance_alerts(self, metrics: MetadataRemovalMetrics) -> None:
        """Check for performance alerts and log warnings."""
        try:
            performance_target = settings.METADATA_REMOVAL_PERFORMANCE_TARGET_MS
            
            # Check if processing time exceeds target
            if metrics.processing_time_ms > performance_target:
                alert_data = {
                    "type": "performance_degradation",
                    "user_id": metrics.user_id,
                    "processing_time_ms": metrics.processing_time_ms,
                    "target_ms": performance_target,
                    "operation_type": metrics.operation_type,
                    "timestamp": metrics.timestamp.isoformat()
                }
                
                logger.warning(f"Performance alert: {json.dumps(alert_data)}")
                
                # Store alert for retrieval
                await self._store_performance_alert(alert_data)
            
            # Check for errors
            if not metrics.success:
                alert_data = {
                    "type": "operation_failure",
                    "user_id": metrics.user_id,
                    "error_message": metrics.error_message,
                    "operation_type": metrics.operation_type,
                    "timestamp": metrics.timestamp.isoformat()
                }
                
                logger.error(f"Operation failure alert: {json.dumps(alert_data)}")
                await self._store_performance_alert(alert_data)
                
        except Exception as e:
            logger.error(f"Error checking performance alerts: {str(e)}")

    async def _calculate_user_stats(self, user_id: str, hours: int) -> Optional[MetadataRemovalStats]:
        """Calculate statistics for a specific user."""
        try:
            redis_cache = await get_redis_client()
            if not redis_cache:
                return None

            # For now, use a simplified approach with pattern matching
            # In production, you might want to use a more efficient indexing strategy
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            # Get all metric keys for this user using pattern matching
            pattern = f"metadata_metric:{user_id}:*"
            all_keys = await redis_cache.keys(pattern)

            metrics_data = []
            for key in all_keys:
                metric_data = await redis_cache.get(key)
                if metric_data:
                    try:
                        # Data should already be deserialized by the cache
                        if isinstance(metric_data, dict):
                            metric = metric_data
                        else:
                            metric = json.loads(metric_data)

                        metric_time = datetime.fromisoformat(metric['timestamp'].replace('Z', '+00:00'))
                        if metric_time >= cutoff_time:
                            metrics_data.append(metric)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue

            if not metrics_data:
                return None

            return self._calculate_stats_from_metrics(metrics_data)

        except Exception as e:
            logger.error(f"Error calculating user stats: {str(e)}")
            return None

    async def _calculate_global_stats(self, hours: int) -> Optional[MetadataRemovalStats]:
        """Calculate global statistics."""
        try:
            redis_cache = await get_redis_client()
            if not redis_cache:
                return None

            # Get global metrics from the last N hours using pattern matching
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            # Get all metric keys using pattern matching
            pattern = "metadata_metric:*"
            all_keys = await redis_cache.keys(pattern)

            metrics_data = []
            for key in all_keys:
                metric_data = await redis_cache.get(key)
                if metric_data:
                    try:
                        # Data should already be deserialized by the cache
                        if isinstance(metric_data, dict):
                            metric = metric_data
                        else:
                            metric = json.loads(metric_data)

                        metric_time = datetime.fromisoformat(metric['timestamp'].replace('Z', '+00:00'))
                        if metric_time >= cutoff_time:
                            metrics_data.append(metric)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue

            if not metrics_data:
                return None

            return self._calculate_stats_from_metrics(metrics_data)

        except Exception as e:
            logger.error(f"Error calculating global stats: {str(e)}")
            return None

    def _calculate_stats_from_metrics(self, metrics_data: List[Dict[str, Any]]) -> MetadataRemovalStats:
        """Calculate statistics from a list of metrics."""
        total_operations = len(metrics_data)
        total_images_processed = sum(m.get('image_count', 0) for m in metrics_data)
        total_processing_time_ms = sum(m.get('processing_time_ms', 0) for m in metrics_data)
        successful_operations = sum(1 for m in metrics_data if m.get('success', False))
        cache_hits = sum(1 for m in metrics_data if m.get('cache_hit', False))

        # Size reduction calculations
        size_reductions = [m.get('size_reduction_percent', 0) for m in metrics_data if m.get('size_reduction_percent') is not None]
        total_size_reduction_bytes = sum(
            (m.get('original_size_bytes', 0) - m.get('processed_size_bytes', 0))
            for m in metrics_data
            if m.get('original_size_bytes') and m.get('processed_size_bytes')
        )

        # AI tags and metadata tags
        total_ai_tags_removed = sum(m.get('ai_tags_removed', 0) for m in metrics_data if m.get('ai_tags_removed'))
        total_metadata_tags_removed = sum(m.get('total_metadata_tags_removed', 0) for m in metrics_data if m.get('total_metadata_tags_removed'))

        # Performance target calculations
        performance_target = settings.METADATA_REMOVAL_PERFORMANCE_TARGET_MS
        operations_meeting_target = sum(
            1 for m in metrics_data
            if m.get('processing_time_ms', float('inf')) <= performance_target
        )

        return MetadataRemovalStats(
            total_operations=total_operations,
            total_images_processed=total_images_processed,
            total_processing_time_ms=total_processing_time_ms,
            average_processing_time_ms=total_processing_time_ms / total_operations if total_operations > 0 else 0,
            success_rate=successful_operations / total_operations if total_operations > 0 else 0,
            cache_hit_rate=cache_hits / total_operations if total_operations > 0 else 0,
            total_size_reduction_bytes=total_size_reduction_bytes,
            average_size_reduction_percent=sum(size_reductions) / len(size_reductions) if size_reductions else 0,
            total_ai_tags_removed=total_ai_tags_removed,
            total_metadata_tags_removed=total_metadata_tags_removed,
            error_count=total_operations - successful_operations,
            performance_target_met_rate=operations_meeting_target / total_operations if total_operations > 0 else 0
        )

    async def _track_usage_for_billing(self, metrics: MetadataRemovalMetrics) -> None:
        """Track usage for add-on billing."""
        try:
            await track_addon_usage(
                user_id=metrics.user_id,
                usage_type=UsageType.IMAGE_GENERATION,  # Use existing image generation usage type
                amount=metrics.image_count,
                metadata={
                    "operation": "metadata_removal",
                    "operation_type": metrics.operation_type,
                    "processing_time_ms": metrics.processing_time_ms,
                    "ai_tags_removed": metrics.ai_tags_removed,
                    "timestamp": metrics.timestamp.isoformat()
                }
            )
        except Exception as e:
            logger.warning(f"Failed to track usage for billing: {str(e)}")

    async def _store_performance_alert(self, alert_data: Dict[str, Any]) -> None:
        """Store performance alert for later retrieval."""
        try:
            redis_cache = await get_redis_client()
            if not redis_cache:
                return

            alert_key = f"metadata_alert:{int(time.time())}"
            await redis_cache.set(
                alert_key,
                alert_data,
                ttl=3600,  # Store alerts for 1 hour
                serialize_json=True
            )

        except Exception as e:
            logger.error(f"Error storing performance alert: {str(e)}")

    async def _get_performance_alerts(self, hours: int) -> List[Dict[str, Any]]:
        """Get performance alerts from the last N hours."""
        try:
            redis_cache = await get_redis_client()
            if not redis_cache:
                return []

            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            # Get all alert keys using pattern matching
            pattern = "metadata_alert:*"
            alert_keys = await redis_cache.keys(pattern)
            alerts = []

            for key in alert_keys:
                alert_data = await redis_cache.get(key)
                if alert_data:
                    try:
                        # Data should already be deserialized by the cache
                        if isinstance(alert_data, dict):
                            alert = alert_data
                        else:
                            alert = json.loads(alert_data)

                        alert_time = datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00'))
                        if alert_time >= cutoff_time:
                            alerts.append(alert)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue

            return alerts

        except Exception as e:
            logger.error(f"Error getting performance alerts: {str(e)}")
            return []


# Global monitor instance
metadata_removal_monitor = MetadataRemovalMonitor()


async def record_metadata_removal_operation(
    operation_type: str,
    user_id: str,
    image_count: int,
    processing_time_ms: float,
    success: bool,
    **kwargs
) -> None:
    """
    Convenience function to record a metadata removal operation.
    
    Args:
        operation_type: Type of operation (single, batch, job_handler)
        user_id: User ID
        image_count: Number of images processed
        processing_time_ms: Processing time in milliseconds
        success: Whether the operation was successful
        **kwargs: Additional metric data
    """
    metrics = MetadataRemovalMetrics(
        timestamp=datetime.now(timezone.utc),
        operation_type=operation_type,
        user_id=user_id,
        image_count=image_count,
        processing_time_ms=processing_time_ms,
        success=success,
        **kwargs
    )
    
    await metadata_removal_monitor.record_operation(metrics)


async def get_user_metadata_removal_stats(user_id: str, hours: int = 24) -> Optional[MetadataRemovalStats]:
    """Get metadata removal statistics for a user."""
    return await metadata_removal_monitor.get_user_stats(user_id, hours)


async def get_global_metadata_removal_stats(hours: int = 24) -> Optional[MetadataRemovalStats]:
    """Get global metadata removal statistics."""
    return await metadata_removal_monitor.get_global_stats(hours)

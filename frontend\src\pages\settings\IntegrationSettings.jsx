// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from "react";
import platformService from "../../services/platformService";
import { Helmet } from "react-helmet-async";
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Divider,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  AlertTitle,
  Switch,
  FormControlLabel,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Chip,
} from "@mui/material";
import {
  Refresh as RefreshIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Check as CheckIcon,
  Link as LinkIcon,
  CreditCard as CreditCardIcon,
  Email as EmailIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  Pinterest as PinterestIcon,
  Chat as ThreadsIcon,
  MusicVideo as TikTokIcon,
  OpenInNew as OpenInNewIcon,
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useNotification } from "../../hooks/useNotification";
import api from "../../api";
import CustomerPortalButton from "../../components/billing/CustomerPortalButton";

// Tab panel component
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`integration-tabpanel-${index}`}
      aria-labelledby={`integration-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

/**
 * IntegrationSettings component provides a comprehensive interface for managing
 * social media connections, payment methods, and email notification preferences.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isEmbedded - Whether the component is embedded in another page
 */
const IntegrationSettings = ({ isEmbedded = false }) => {
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // State for tabs
  const [activeTab, setActiveTab] = useState(0);

  // State for loading
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // State for social media accounts
  const [socialAccounts, setSocialAccounts] = useState([]);

  // State for payment methods
  const [paymentMethods, setPaymentMethods] = useState([]);

  // State for email settings
  const [emailSettings, setEmailSettings] = useState({
    notifyContentScheduled: true,
    notifyContentPublished: true,
    notifyPaymentSuccess: true,
    notifyPaymentFailure: true,
    weeklyAnalyticsReport: true,
    marketingEmails: false,
    notificationEmail: '',
  });

  // State for Snackbar notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Snackbar handlers
  const showSnackbar = useCallback((message, severity = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  }, []);

  const handleCloseSnackbar = useCallback((event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  // State for dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogPlatform, setDialogPlatform] = useState("");

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Navigation handlers for production-ready user experience
  const handleNavigateToSettings = useCallback(() => {
    navigate('/settings');
  }, [navigate]);

  const handleNavigateToIntegrations = useCallback(() => {
    navigate('/integrations');
  }, [navigate]);

  const handleNavigateToProfile = useCallback(() => {
    if (user?.id) {
      navigate(`/profile/${user.id}`);
    }
  }, [navigate, user?.id]);

  // User authentication check with redirect
  const handleAuthenticationRequired = useCallback(() => {
    if (!isAuthenticated) {
      showErrorNotification('Please log in to manage integrations');
      navigate('/login');
      return false;
    }
    return true;
  }, [isAuthenticated, showErrorNotification, navigate]);

  // Function to fetch all integration data
  const fetchData = useCallback(async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      // Fetch social media accounts
      const socialResponse = await api.get("/api/social-media/accounts");
      setSocialAccounts(socialResponse.data.accounts || []);

      // Fetch payment methods
      const paymentResponse = await api.get("/api/billing/payment-methods");
      setPaymentMethods(paymentResponse.data || []);

      // Fetch email settings (in a real app, this would be an API call)
      // For now, we'll use the default state

      if (isRefresh) {
        showSuccessNotification("Integration data refreshed");
      }
    } catch (error) {
      console.error("Error fetching integration data:", error);
      showErrorNotification("Failed to load integration data");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [showErrorNotification, showSuccessNotification]);

  // Authentication check with redirect
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      showErrorNotification('Authentication required to access integration settings');
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate, showErrorNotification]);

  // Fetch data on component mount
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      fetchData();
    }
  }, [fetchData, isAuthenticated, authLoading]);

  // Function to handle refresh
  const handleRefresh = () => {
    fetchData(true);
  };

  // Function to connect social media account with authentication check
  const handleConnectSocialMedia = (platform) => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to connect social media accounts');
      navigate('/login');
      return;
    }

    setDialogPlatform(platform);
    setDialogOpen(true);
  };

  // Function to disconnect social media account with enhanced error handling
  const handleDisconnectSocialMedia = async (platform) => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to disconnect social media accounts');
      navigate('/login');
      return;
    }

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to disconnect your ${platform} account? This will stop all scheduled posts and analytics tracking for this platform.`
    );

    if (!confirmed) return;

    try {
      await api.delete(`/api/social-media/accounts/${platform.toLowerCase()}`);
      showSuccessNotification(`${platform} account disconnected successfully`);
      showSnackbar(`${platform} integration removed`, "success");
      fetchData(true);
    } catch (error) {
      console.error(`Error disconnecting ${platform} account:`, error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to disconnect this account.');
      } else if (error.response?.status === 404) {
        showErrorNotification(`${platform} account not found. It may have already been disconnected.`);
      } else {
        showErrorNotification(`Failed to disconnect ${platform} account. Please try again.`);
      }

      showSnackbar(`Failed to disconnect ${platform}. Please try again.`, "error");
    }
  };

  // Function to handle dialog close
  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  // Function to handle dialog confirm with enhanced error handling
  const handleDialogConfirm = async () => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to connect social media accounts');
      navigate('/login');
      setDialogOpen(false);
      return;
    }

    try {
      // Get authorization URL
      const response = await api.get(
        `/api/social-media/connect/${dialogPlatform.toLowerCase()}`
      );

      if (!response.data?.authorization_url) {
        throw new Error('No authorization URL received');
      }

      // Open the authorization URL in a new window
      const authWindow = window.open(response.data.authorization_url, "_blank", "width=600,height=700");

      if (!authWindow) {
        showErrorNotification('Please allow popups for this site to connect your social media accounts');
        return;
      }

      setDialogOpen(false);
      showSuccessNotification(
        `Please complete the ${dialogPlatform} authorization in the new window`
      );
      showSnackbar(`Opening ${dialogPlatform} authorization...`, "info");

      // Optional: Listen for the window to close and refresh data
      const checkClosed = setInterval(() => {
        if (authWindow.closed) {
          clearInterval(checkClosed);
          setTimeout(() => {
            fetchData(true);
            showSnackbar(`Checking ${dialogPlatform} connection status...`, "info");
          }, 2000);
        }
      }, 1000);

    } catch (error) {
      console.error(`Error connecting to ${dialogPlatform}:`, error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to connect social media accounts.');
      } else if (error.response?.status === 429) {
        showErrorNotification('Too many connection attempts. Please try again later.');
      } else {
        showErrorNotification(`Failed to connect to ${dialogPlatform}. Please try again.`);
      }

      showSnackbar(`Failed to connect to ${dialogPlatform}`, "error");
      setDialogOpen(false);
    }
  };

  // Function to handle email settings change with enhanced error handling
  const handleEmailSettingChange = (setting) => async (event) => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to update email settings');
      navigate('/login');
      return;
    }

    const newValue = event.target.checked;

    // Optimistically update the UI
    const previousSettings = emailSettings;
    setEmailSettings({
      ...emailSettings,
      [setting]: newValue,
    });

    // Save to API and show enhanced notifications
    try {
      if (user?.id) {
        const updatedSettings = {
          ...emailSettings,
          [setting]: newValue,
        };

        await api.put(`/api/users/${user.id}/email-settings`, updatedSettings);
        showSuccessNotification(`Email setting "${setting}" updated successfully`);
        showSnackbar("Settings saved and synced to your account", "success");
      } else {
        showSnackbar("Settings updated locally", "info");
      }
    } catch (error) {
      console.error("Error saving email settings:", error);

      // Revert the optimistic update on error
      setEmailSettings(previousSettings);

      // Provide specific error feedback
      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to update email settings.');
      } else {
        showErrorNotification(`Failed to save email setting "${setting}". Please try again.`);
      }

      showSnackbar("Failed to sync settings. Please try again.", "error");
    }
  };

  // Function to add payment method - opens the customer portal
  const handleAddPaymentMethod = async () => {
    try {
      const response = await api.get("/api/billing/customer-portal");
      if (response && response.data && response.data.customer_portal_url) {
        window.open(response.data.customer_portal_url, "_blank");
      } else {
        showErrorNotification(
          "Failed to access customer portal. Please try again later."
        );
      }
    } catch (error) {
      console.error("Error accessing customer portal:", error);
      showErrorNotification(
        "Failed to access customer portal. Please try again later."
      );
    }
  };

  // Function to remove payment method
  const handleRemovePaymentMethod = async (paymentMethodId) => {
    try {
      await api.delete(`/api/billing/payment-methods/${paymentMethodId}`);
      showSuccessNotification("Payment method removed successfully");
      fetchData(true);
    } catch (error) {
      console.error("Error removing payment method:", error);
      showErrorNotification("Failed to remove payment method");
    }
  };

  // Function to set default payment method
  const handleSetDefaultPaymentMethod = async (paymentMethodId) => {
    try {
      await api.post(`/api/billing/payment-methods/${paymentMethodId}/default`);
      showSuccessNotification("Default payment method updated");
      fetchData(true);
    } catch (error) {
      console.error("Error setting default payment method:", error);
      showErrorNotification("Failed to update default payment method");
    }
  };

  // Get platform icon
  const getPlatformIcon = (platform) => {
    switch (platform.toLowerCase()) {
      case "facebook":
        return <FacebookIcon />;
      case "twitter":
        return <TwitterIcon />;
      case "linkedin":
        return <LinkedInIcon />;
      case "instagram":
        return <InstagramIcon />;
      case "pinterest":
        return <PinterestIcon />;
      case "threads":
        return <ThreadsIcon />;
      case "tiktok":
        return <TikTokIcon />;
      default:
        return <LinkIcon />;
    }
  };

  // Get platform color using centralized service
  const getPlatformColor = (platform) => {
    try {
      return platformService.getPlatformColor(platform);
    } catch (error) {
      console.warn(`Failed to get platform color for ${platform}:`, error);
      return "#757575";
    }
  };

  return (
    <>
      {!isEmbedded && (
        <Helmet>
          <title>Integration Settings</title>
        </Helmet>
      )}

      {/* Authentication loading state */}
      {authLoading && (
        <Container maxWidth="sm" sx={{ mt: 4, textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Container>
      )}

      {/* Authentication check */}
      {!authLoading && !isAuthenticated && (
        <Container maxWidth="sm" sx={{ mt: 4 }}>
          <Alert severity="warning" sx={{ mb: 2 }}>
            <AlertTitle>Authentication Required</AlertTitle>
            <Typography variant="body2" paragraph>
              Please log in to access integration settings and manage your social media connections.
            </Typography>
            <Button
              variant="contained"
              onClick={handleAuthenticationRequired}
              sx={{ mt: 1 }}
            >
              Go to Login
            </Button>
          </Alert>
        </Container>
      )}

      {/* Don't render main content if not authenticated */}
      {!authLoading && !isAuthenticated && null}

      {/* Main content - only render if authenticated */}
      {!authLoading && isAuthenticated && (
        <Container
          maxWidth={isEmbedded ? false : "lg"}
          disableGutters={isEmbedded}
        >
        {!isEmbedded && (
          <Box
            sx={{
              mb: 4,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Typography variant="h4" component="h1">
              Integration Settings
            </Typography>

            <Box sx={{ display: 'flex', gap: 1 }}>
              {/* Navigation buttons */}
              <Tooltip title="Go to Main Settings">
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleNavigateToSettings}
                  startIcon={<CheckIcon />}
                >
                  Settings
                </Button>
              </Tooltip>

              <Tooltip title="View Full Integrations Page">
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleNavigateToIntegrations}
                  startIcon={<LinkIcon />}
                >
                  Full Page
                </Button>
              </Tooltip>

              {user?.id && (
                <Tooltip title="Go to Profile">
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={handleNavigateToProfile}
                    startIcon={<CheckIcon />}
                  >
                    Profile
                  </Button>
                </Tooltip>
              )}

              <Tooltip title="Refresh data">
                <IconButton
                  onClick={handleRefresh}
                  disabled={loading || refreshing}
                  color="primary"
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        )}

        <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
          <Tooltip title="Refresh data">
            <IconButton
              onClick={handleRefresh}
              disabled={loading || refreshing}
              color="primary"
              size="small"
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>

        <Paper sx={{ mb: 4 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab icon={<LinkIcon />} label="Social Media" />
            <Tab icon={<CreditCardIcon />} label="Payment Methods" />
            <Tab icon={<EmailIcon />} label="Email Settings" />
          </Tabs>

          {/* Social Media Tab */}
          <TabPanel value={activeTab} index={0}>
            {loading ? (
              <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Alert severity="info" sx={{ mb: 3 }}>
                  <AlertTitle>Connect Your Social Media Accounts</AlertTitle>
                  Connect your social media accounts to enable automatic posting
                  and analytics tracking.
                </Alert>

                <Grid container spacing={3}>
                  {/* Facebook */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader
                        avatar={
                          <Box sx={{
                            color: getPlatformColor("facebook"),
                            display: 'flex',
                            alignItems: 'center'
                          }}>
                            {getPlatformIcon("facebook")}
                          </Box>
                        }
                        title="Facebook"
                        subheader={
                          socialAccounts.some(
                            (acc) => acc.platform === "facebook"
                          )
                            ? "Connected"
                            : "Not connected"
                        }
                        action={
                          socialAccounts.some(
                            (acc) => acc.platform === "facebook"
                          ) && (
                            <Chip
                              label="Connected"
                              color="success"
                              size="small"
                              icon={<CheckIcon />}
                            />
                          )
                        }
                      />
                      <CardContent>
                        {socialAccounts.some(
                          (acc) => acc.platform === "facebook"
                        ) ? (
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              gutterBottom
                            >
                              Connected Account:
                            </Typography>
                            <Typography variant="body1">
                              {
                                socialAccounts.find(
                                  (acc) => acc.platform === "facebook"
                                )?.account_name
                              }
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Connect your Facebook account to schedule posts and
                            track performance.
                          </Typography>
                        )}
                      </CardContent>
                      <CardActions>
                        {socialAccounts.some(
                          (acc) => acc.platform === "facebook"
                        ) ? (
                          <Button
                            color="error"
                            onClick={() =>
                              handleDisconnectSocialMedia("facebook")
                            }
                            startIcon={<DeleteIcon />}
                          >
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            color="primary"
                            onClick={() => handleConnectSocialMedia("Facebook")}
                            startIcon={<AddIcon />}
                          >
                            Connect
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>

                  {/* Twitter */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader
                        avatar={<TwitterIcon style={{ color: "#1DA1F2" }} />}
                        title="Twitter"
                        subheader={
                          socialAccounts.some(
                            (acc) => acc.platform === "twitter"
                          )
                            ? "Connected"
                            : "Not connected"
                        }
                        action={
                          socialAccounts.some(
                            (acc) => acc.platform === "twitter"
                          ) && (
                            <Chip
                              label="Connected"
                              color="success"
                              size="small"
                              icon={<CheckIcon />}
                            />
                          )
                        }
                      />
                      <CardContent>
                        {socialAccounts.some(
                          (acc) => acc.platform === "twitter"
                        ) ? (
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              gutterBottom
                            >
                              Connected Account:
                            </Typography>
                            <Typography variant="body1">
                              {
                                socialAccounts.find(
                                  (acc) => acc.platform === "twitter"
                                )?.account_name
                              }
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Connect your Twitter account to schedule tweets and
                            track engagement.
                          </Typography>
                        )}
                      </CardContent>
                      <CardActions>
                        {socialAccounts.some(
                          (acc) => acc.platform === "twitter"
                        ) ? (
                          <Button
                            color="error"
                            onClick={() =>
                              handleDisconnectSocialMedia("twitter")
                            }
                            startIcon={<DeleteIcon />}
                          >
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            color="primary"
                            onClick={() => handleConnectSocialMedia("Twitter")}
                            startIcon={<AddIcon />}
                          >
                            Connect
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>

                  {/* LinkedIn */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader
                        avatar={<LinkedInIcon style={{ color: "#0077B5" }} />}
                        title="LinkedIn"
                        subheader={
                          socialAccounts.some(
                            (acc) => acc.platform === "linkedin"
                          )
                            ? "Connected"
                            : "Not connected"
                        }
                        action={
                          socialAccounts.some(
                            (acc) => acc.platform === "linkedin"
                          ) && (
                            <Chip
                              label="Connected"
                              color="success"
                              size="small"
                              icon={<CheckIcon />}
                            />
                          )
                        }
                      />
                      <CardContent>
                        {socialAccounts.some(
                          (acc) => acc.platform === "linkedin"
                        ) ? (
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              gutterBottom
                            >
                              Connected Account:
                            </Typography>
                            <Typography variant="body1">
                              {
                                socialAccounts.find(
                                  (acc) => acc.platform === "linkedin"
                                )?.account_name
                              }
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Connect your LinkedIn account to schedule posts and
                            analyze professional engagement.
                          </Typography>
                        )}
                      </CardContent>
                      <CardActions>
                        {socialAccounts.some(
                          (acc) => acc.platform === "linkedin"
                        ) ? (
                          <Button
                            color="error"
                            onClick={() =>
                              handleDisconnectSocialMedia("linkedin")
                            }
                            startIcon={<DeleteIcon />}
                          >
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            color="primary"
                            onClick={() => handleConnectSocialMedia("LinkedIn")}
                            startIcon={<AddIcon />}
                          >
                            Connect
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>

                  {/* Instagram */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader
                        avatar={<InstagramIcon style={{ color: "#E1306C" }} />}
                        title="Instagram"
                        subheader={
                          socialAccounts.some(
                            (acc) => acc.platform === "instagram"
                          )
                            ? "Connected"
                            : "Not connected"
                        }
                        action={
                          socialAccounts.some(
                            (acc) => acc.platform === "instagram"
                          ) && (
                            <Chip
                              label="Connected"
                              color="success"
                              size="small"
                              icon={<CheckIcon />}
                            />
                          )
                        }
                      />
                      <CardContent>
                        {socialAccounts.some(
                          (acc) => acc.platform === "instagram"
                        ) ? (
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              gutterBottom
                            >
                              Connected Account:
                            </Typography>
                            <Typography variant="body1">
                              {
                                socialAccounts.find(
                                  (acc) => acc.platform === "instagram"
                                )?.account_name
                              }
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Connect your Instagram account to schedule posts and
                            track visual content performance.
                          </Typography>
                        )}
                      </CardContent>
                      <CardActions>
                        {socialAccounts.some(
                          (acc) => acc.platform === "instagram"
                        ) ? (
                          <Button
                            color="error"
                            onClick={() =>
                              handleDisconnectSocialMedia("instagram")
                            }
                            startIcon={<DeleteIcon />}
                          >
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            color="primary"
                            onClick={() =>
                              handleConnectSocialMedia("Instagram")
                            }
                            startIcon={<AddIcon />}
                          >
                            Connect
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>

                  {/* Pinterest */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader
                        avatar={<PinterestIcon style={{ color: "#BD081C" }} />}
                        title="Pinterest"
                        subheader={
                          socialAccounts.some(
                            (acc) => acc.platform === "pinterest"
                          )
                            ? "Connected"
                            : "Not connected"
                        }
                        action={
                          socialAccounts.some(
                            (acc) => acc.platform === "pinterest"
                          ) && (
                            <Chip
                              label="Connected"
                              color="success"
                              size="small"
                              icon={<CheckIcon />}
                            />
                          )
                        }
                      />
                      <CardContent>
                        {socialAccounts.some(
                          (acc) => acc.platform === "pinterest"
                        ) ? (
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              gutterBottom
                            >
                              Connected Account:
                            </Typography>
                            <Typography variant="body1">
                              {
                                socialAccounts.find(
                                  (acc) => acc.platform === "pinterest"
                                )?.account_name
                              }
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Connect your Pinterest account to schedule pins and
                            track engagement with visual content.
                          </Typography>
                        )}
                      </CardContent>
                      <CardActions>
                        {socialAccounts.some(
                          (acc) => acc.platform === "pinterest"
                        ) ? (
                          <Button
                            color="error"
                            onClick={() =>
                              handleDisconnectSocialMedia("pinterest")
                            }
                            startIcon={<DeleteIcon />}
                          >
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            color="primary"
                            onClick={() =>
                              handleConnectSocialMedia("Pinterest")
                            }
                            startIcon={<AddIcon />}
                          >
                            Connect
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>

                  {/* Threads */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader
                        avatar={<ThreadsIcon style={{ color: "#000000" }} />}
                        title="Threads"
                        subheader={
                          socialAccounts.some(
                            (acc) => acc.platform === "threads"
                          )
                            ? "Connected"
                            : "Not connected"
                        }
                        action={
                          socialAccounts.some(
                            (acc) => acc.platform === "threads"
                          ) && (
                            <Chip
                              label="Connected"
                              color="success"
                              size="small"
                              icon={<CheckIcon />}
                            />
                          )
                        }
                      />
                      <CardContent>
                        {socialAccounts.some(
                          (acc) => acc.platform === "threads"
                        ) ? (
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              gutterBottom
                            >
                              Connected Account:
                            </Typography>
                            <Typography variant="body1">
                              {
                                socialAccounts.find(
                                  (acc) => acc.platform === "threads"
                                )?.account_name
                              }
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Connect your Threads account to schedule posts and
                            engage with text-based conversations.
                          </Typography>
                        )}
                      </CardContent>
                      <CardActions>
                        {socialAccounts.some(
                          (acc) => acc.platform === "threads"
                        ) ? (
                          <Button
                            color="error"
                            onClick={() =>
                              handleDisconnectSocialMedia("threads")
                            }
                            startIcon={<DeleteIcon />}
                          >
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            color="primary"
                            onClick={() => handleConnectSocialMedia("Threads")}
                            startIcon={<AddIcon />}
                          >
                            Connect
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>

                  {/* TikTok */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardHeader
                        avatar={<TikTokIcon style={{ color: "#000000" }} />}
                        title="TikTok"
                        subheader={
                          socialAccounts.some(
                            (acc) => acc.platform === "tiktok"
                          )
                            ? "Connected"
                            : "Not connected"
                        }
                        action={
                          socialAccounts.some(
                            (acc) => acc.platform === "tiktok"
                          ) && (
                            <Chip
                              label="Connected"
                              color="success"
                              size="small"
                              icon={<CheckIcon />}
                            />
                          )
                        }
                      />
                      <CardContent>
                        {socialAccounts.some(
                          (acc) => acc.platform === "tiktok"
                        ) ? (
                          <Box>
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              gutterBottom
                            >
                              Connected Account:
                            </Typography>
                            <Typography variant="body1">
                              {
                                socialAccounts.find(
                                  (acc) => acc.platform === "tiktok"
                                )?.account_name
                              }
                            </Typography>
                          </Box>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Connect your TikTok account to schedule videos and
                            analyze short-form video performance.
                          </Typography>
                        )}
                      </CardContent>
                      <CardActions>
                        {socialAccounts.some(
                          (acc) => acc.platform === "tiktok"
                        ) ? (
                          <Button
                            color="error"
                            onClick={() =>
                              handleDisconnectSocialMedia("tiktok")
                            }
                            startIcon={<DeleteIcon />}
                          >
                            Disconnect
                          </Button>
                        ) : (
                          <Button
                            color="primary"
                            onClick={() => handleConnectSocialMedia("TikTok")}
                            startIcon={<AddIcon />}
                          >
                            Connect
                          </Button>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>
                </Grid>
              </>
            )}
          </TabPanel>

          {/* Payment Methods Tab */}
          <TabPanel value={activeTab} index={1}>
            {loading ? (
              <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Alert severity="info" sx={{ mb: 4 }}>
                  <AlertTitle>Secure Payment Processing</AlertTitle>
                  <Typography paragraph>
                    Your payment methods are securely managed through our
                    payment provider, Lemon Squeezy. Your payment information is
                    never stored on our servers and is handled according to the
                    highest security standards.
                  </Typography>
                </Alert>

                {/* Main payment methods card with instructions */}
                <Card sx={{ mb: 4 }}>
                  <CardContent>
                    <Box sx={{ textAlign: "center", mb: 3 }}>
                      <CreditCardIcon
                        sx={{ fontSize: 60, color: "primary.main", mb: 2 }}
                      />
                      <Typography variant="h5" gutterBottom>
                        Manage Your Payment Methods
                      </Typography>
                      <Typography
                        variant="body1"
                        color="text.secondary"
                        paragraph
                      >
                        Add, update, or remove payment methods for your
                        subscription and add-on purchases.
                      </Typography>
                    </Box>

                    <Box sx={{ mb: 4 }}>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{ borderBottom: 1, borderColor: "divider", pb: 1 }}
                      >
                        How to Add a Payment Method
                      </Typography>

                      <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid
                          item
                          xs={12}
                          md={1}
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Chip
                            label="1"
                            color="primary"
                            sx={{ height: 30, width: 30, borderRadius: "50%" }}
                          />
                        </Grid>
                        <Grid item xs={12} md={11}>
                          <Typography variant="body1">
                            Click the <strong>&quot;Access Customer Portal&quot;</strong>{" "}
                            button below to open the Lemon Squeezy customer
                            portal.
                          </Typography>
                        </Grid>

                        <Grid
                          item
                          xs={12}
                          md={1}
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Chip
                            label="2"
                            color="primary"
                            sx={{ height: 30, width: 30, borderRadius: "50%" }}
                          />
                        </Grid>
                        <Grid item xs={12} md={11}>
                          <Typography variant="body1">
                            In the customer portal, navigate to the{" "}
                            <strong>&quot;Payment Methods&quot;</strong> section.
                          </Typography>
                        </Grid>

                        <Grid
                          item
                          xs={12}
                          md={1}
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Chip
                            label="3"
                            color="primary"
                            sx={{ height: 30, width: 30, borderRadius: "50%" }}
                          />
                        </Grid>
                        <Grid item xs={12} md={11}>
                          <Typography variant="body1">
                            Click <strong>&quot;Add Payment Method&quot;</strong> and
                            enter your card details in the secure form.
                          </Typography>
                        </Grid>

                        <Grid
                          item
                          xs={12}
                          md={1}
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                          }}
                        >
                          <Chip
                            label="4"
                            color="primary"
                            sx={{ height: 30, width: 30, borderRadius: "50%" }}
                          />
                        </Grid>
                        <Grid item xs={12} md={11}>
                          <Typography variant="body1">
                            To set a payment method as default, select the{" "}
                            <strong>&quot;Make Default&quot;</strong> option next to the
                            payment method.
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>

                    <Box
                      sx={{ display: "flex", justifyContent: "center", mt: 2 }}
                    >
                      <CustomerPortalButton
                        label="Access Customer Portal"
                        size="large"
                        startIcon={<OpenInNewIcon />}
                        tooltipText="Opens Lemon Squeezy in a new window"
                        sx={{ px: 4, py: 1.5 }}
                      />
                    </Box>
                  </CardContent>
                </Card>

                {/* Current payment methods section */}
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Your Current Payment Methods
                  </Typography>
                </Box>

                {paymentMethods.length === 0 ? (
                  <Alert severity="warning" sx={{ mb: 3 }}>
                    <AlertTitle>No Payment Methods Found</AlertTitle>
                    <Typography paragraph>
                      You don&apos;t have any payment methods set up yet. Add a
                      payment method through the customer portal to manage your
                      subscription.
                    </Typography>
                    <CustomerPortalButton
                      label="Add Payment Method"
                      startIcon={<AddIcon />}
                      size="medium"
                      sx={{ mt: 1 }}
                    />
                  </Alert>
                ) : (
                  <Grid container spacing={3}>
                    {paymentMethods.map((method) => (
                      <Grid item xs={12} md={6} key={method.id}>
                        <Card>
                          <CardHeader
                            avatar={<CreditCardIcon color="primary" />}
                            title={`${method.brand.toUpperCase()} •••• ${
                              method.last4
                            }`}
                            subheader={`Expires ${method.exp_month}/${method.exp_year}`}
                            action={
                              method.is_default && (
                                <Chip
                                  label="Default"
                                  color="primary"
                                  size="small"
                                  icon={<CheckIcon />}
                                />
                              )
                            }
                          />
                          <CardActions>
                            {!method.is_default && (
                              <Button
                                color="primary"
                                onClick={() =>
                                  handleSetDefaultPaymentMethod(method.id)
                                }
                              >
                                Set as Default
                              </Button>
                            )}
                            <Button
                              color="error"
                              onClick={() =>
                                handleRemovePaymentMethod(method.id)
                              }
                              startIcon={<DeleteIcon />}
                              disabled={method.is_default}
                            >
                              Remove
                            </Button>
                          </CardActions>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                )}

                {/* Quick add payment method button */}
                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={handleAddPaymentMethod}
                    startIcon={<AddIcon />}
                    size="large"
                  >
                    Quick Add Payment Method
                  </Button>
                  <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                    Opens customer portal in a new window
                  </Typography>
                </Box>
              </>
            )}
          </TabPanel>

          {/* Email Settings Tab */}
          <TabPanel value={activeTab} index={2}>
            {loading ? (
              <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Alert severity="info" sx={{ mb: 3 }}>
                  <AlertTitle>Manage Email Notifications</AlertTitle>
                  Customize which email notifications you receive from the
                  platform.
                </Alert>

                <Card>
                  <CardHeader
                    title="Email Notification Settings"
                    subheader="Choose which emails you want to receive"
                  />
                  <CardContent>
                    {/* Email Address Configuration */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="h6" gutterBottom>
                        Email Address
                      </Typography>
                      <TextField
                        fullWidth
                        label="Primary Email Address"
                        value={user?.email || ''}
                        disabled
                        helperText="This is your account email address. To change it, update your profile settings."
                        sx={{ mb: 2 }}
                      />
                      <TextField
                        fullWidth
                        label="Notification Email (Optional)"
                        value={emailSettings.notificationEmail || ''}
                        onChange={(e) => setEmailSettings(prev => ({
                          ...prev,
                          notificationEmail: e.target.value
                        }))}
                        placeholder="Enter alternative email for notifications"
                        helperText="Leave blank to use your primary email address"
                      />
                    </Box>

                    <Divider sx={{ my: 3 }} />

                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={emailSettings.notifyContentScheduled}
                              onChange={handleEmailSettingChange(
                                "notifyContentScheduled"
                              )}
                              color="primary"
                            />
                          }
                          label="Content scheduled notifications"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={emailSettings.notifyContentPublished}
                              onChange={handleEmailSettingChange(
                                "notifyContentPublished"
                              )}
                              color="primary"
                            />
                          }
                          label="Content published notifications"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={emailSettings.notifyPaymentSuccess}
                              onChange={handleEmailSettingChange(
                                "notifyPaymentSuccess"
                              )}
                              color="primary"
                            />
                          }
                          label="Payment success notifications"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={emailSettings.notifyPaymentFailure}
                              onChange={handleEmailSettingChange(
                                "notifyPaymentFailure"
                              )}
                              color="primary"
                            />
                          }
                          label="Payment failure notifications"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={emailSettings.weeklyAnalyticsReport}
                              onChange={handleEmailSettingChange(
                                "weeklyAnalyticsReport"
                              )}
                              color="primary"
                            />
                          }
                          label="Weekly analytics reports"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={emailSettings.marketingEmails}
                              onChange={handleEmailSettingChange(
                                "marketingEmails"
                              )}
                              color="primary"
                            />
                          }
                          label="Marketing and promotional emails"
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </>
            )}
          </TabPanel>
        </Paper>

        {/* Connect Social Media Dialog */}
        <Dialog
        open={dialogOpen}
        onClose={handleDialogClose}
        aria-labelledby="connect-dialog-title"
        aria-describedby="connect-dialog-description"
      >
        <DialogTitle id="connect-dialog-title">
          Connect {dialogPlatform}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="connect-dialog-description">
            You will be redirected to {dialogPlatform} to authorize access to
            your account. This will allow the application to post content and
            retrieve analytics on your behalf.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleDialogConfirm}
            color="primary"
            variant="contained"
          >
            Connect
          </Button>
        </DialogActions>
      </Dialog>
      </Container>
      )}

      {/* Enhanced Snackbar for user feedback */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default IntegrationSettings;

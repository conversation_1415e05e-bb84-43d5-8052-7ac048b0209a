#!/usr/bin/env node
// @since 2024-1-1 to 2025-25-7

/**
 * ACE Social Platform - Enhanced Build Verification Script
 * Version: 2.0.0
 *
 * Comprehensive validation of build components, performance checks,
 * and build quality metrics for production readiness.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Script metadata
const SCRIPT_VERSION = '2.0.0';
const SCRIPT_NAME = 'ACE Social Build Verification';

// Colors for console output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    purple: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
};

// Build quality thresholds
const QUALITY_THRESHOLDS = {
    maxBundleSize: 2 * 1024 * 1024, // 2MB
    maxBuildTime: 300, // 5 minutes
    minTestCoverage: 80, // 80%
    maxVulnerabilities: 0,
    maxLintErrors: 0
};

// Validation results
let validationResults = {
    passed: 0,
    failed: 0,
    warnings: 0,
    details: []
};

function log(message, color = 'reset') {
    const timestamp = new Date().toISOString();
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logHeader(message) {
    console.log(`${colors.white}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.white}${message}${colors.reset}`);
    console.log(`${colors.white}${'='.repeat(60)}${colors.reset}`);
}

function logSection(message) {
    console.log(`${colors.blue}${'─'.repeat(40)}${colors.reset}`);
    console.log(`${colors.blue}${message}${colors.reset}`);
    console.log(`${colors.blue}${'─'.repeat(40)}${colors.reset}`);
}

function addResult(category, name, passed, message = '', severity = 'error') {
    const result = { category, name, passed, message, severity };
    validationResults.details.push(result);

    if (passed) {
        validationResults.passed++;
        log(`✅ ${name}`, 'green');
    } else {
        if (severity === 'warning') {
            validationResults.warnings++;
            log(`⚠️  ${name}: ${message}`, 'yellow');
        } else {
            validationResults.failed++;
            log(`❌ ${name}: ${message}`, 'red');
        }
    }

    return passed;
}

// Utility functions
function executeCommand(command, options = {}) {
    try {
        const result = execSync(command, {
            encoding: 'utf8',
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options
        });
        return { success: true, output: result.trim() };
    } catch (error) {
        return { success: false, error: error.message, output: error.stdout || '' };
    }
}

function getFileSize(filePath) {
    try {
        const stats = fs.statSync(filePath);
        return stats.size;
    } catch (error) {
        return 0;
    }
}

function getDirectorySize(dirPath) {
    try {
        let totalSize = 0;
        const files = fs.readdirSync(dirPath, { withFileTypes: true });

        for (const file of files) {
            const fullPath = path.join(dirPath, file.name);
            if (file.isDirectory()) {
                totalSize += getDirectorySize(fullPath);
            } else {
                totalSize += getFileSize(fullPath);
            }
        }

        return totalSize;
    } catch (error) {
        return 0;
    }
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function checkFile(filePath, description, category = 'files') {
    const exists = fs.existsSync(filePath);
    return addResult(category, description, exists, exists ? '' : `Missing file: ${filePath}`);
}

function checkDirectory(dirPath, description, category = 'directories') {
    const exists = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
    return addResult(category, description, exists, exists ? '' : `Missing directory: ${dirPath}`);
}

function checkCommand(command, description, category = 'prerequisites') {
    const result = executeCommand(`${command} --version`, { silent: true });
    return addResult(category, description, result.success,
        result.success ? '' : `Command not found or failed: ${command}`);
}

// Validation functions
function validatePrerequisites() {
    logSection('Validating Prerequisites');

    const prerequisites = [
        { command: 'node', description: 'Node.js' },
        { command: 'npm', description: 'npm' },
        { command: 'python', description: 'Python' },
        { command: 'docker', description: 'Docker' },
        { command: 'docker-compose', description: 'Docker Compose' }
    ];

    let allPresent = true;
    prerequisites.forEach(({ command, description }) => {
        if (!checkCommand(command, description)) {
            allPresent = false;
        }
    });

    // Check Docker daemon
    const dockerInfo = executeCommand('docker info', { silent: true });
    addResult('prerequisites', 'Docker Daemon Running', dockerInfo.success,
        dockerInfo.success ? '' : 'Docker daemon is not running');

    return allPresent && dockerInfo.success;
}

function validateProjectStructure() {
    logSection('Validating Project Structure');

    const requiredFiles = [
        { path: 'package.json', description: 'Root package.json' },
        { path: 'frontend/package.json', description: 'Frontend package.json' },
        { path: 'backend/requirements.txt', description: 'Backend requirements.txt' },
        { path: 'admin-app/package.json', description: 'Admin package.json' },
        { path: 'docker-compose.yml', description: 'Docker Compose config' },
        { path: 'BUILD.md', description: 'Build documentation' }
    ];

    const requiredDirectories = [
        { path: 'frontend/src', description: 'Frontend source directory' },
        { path: 'backend/app', description: 'Backend app directory' },
        { path: 'admin-app/src', description: 'Admin source directory' },
        { path: 'scripts', description: 'Scripts directory' }
    ];

    let allPresent = true;

    requiredFiles.forEach(({ path, description }) => {
        if (!checkFile(path, description)) {
            allPresent = false;
        }
    });

    requiredDirectories.forEach(({ path, description }) => {
        if (!checkDirectory(path, description)) {
            allPresent = false;
        }
    });

    return allPresent;
}

function validatePackageJson(filePath, description) {
    try {
        const packageJson = JSON.parse(fs.readFileSync(filePath, 'utf8'));

        // Check for required scripts based on package type
        let requiredScripts = [];

        if (filePath === 'package.json') {
            requiredScripts = [
                'build', 'build:frontend', 'build:backend', 'build:docker', 'build:production',
                'dev', 'dev:frontend', 'dev:backend',
                'start', 'start:dev', 'start:prod', 'stop',
                'test', 'test:frontend', 'test:backend',
                'clean', 'install:all'
            ];
        } else if (filePath.includes('frontend')) {
            requiredScripts = ['dev', 'build', 'build:production', 'test', 'lint', 'type-check'];
        } else if (filePath.includes('admin')) {
            requiredScripts = ['dev', 'build', 'test', 'lint'];
        }

        let allScriptsPresent = true;
        const missingScripts = [];

        requiredScripts.forEach(script => {
            if (!packageJson.scripts || !packageJson.scripts[script]) {
                missingScripts.push(script);
                allScriptsPresent = false;
            }
        });

        addResult('package-scripts', `${description} scripts`, allScriptsPresent,
            allScriptsPresent ? '' : `Missing scripts: ${missingScripts.join(', ')}`);

        // Check for security vulnerabilities in dependencies
        if (packageJson.dependencies || packageJson.devDependencies) {
            const auditResult = executeCommand('npm audit --json', { silent: true, cwd: path.dirname(filePath) });
            if (auditResult.success) {
                try {
                    const auditData = JSON.parse(auditResult.output);
                    const vulnerabilities = auditData.metadata?.vulnerabilities || {};
                    const totalVulns = Object.values(vulnerabilities).reduce((sum, count) => sum + count, 0);

                    addResult('security', `${description} security audit`, totalVulns <= QUALITY_THRESHOLDS.maxVulnerabilities,
                        totalVulns > 0 ? `Found ${totalVulns} vulnerabilities` : '',
                        totalVulns > 0 ? 'warning' : 'error');
                } catch (e) {
                    addResult('security', `${description} security audit`, false, 'Failed to parse audit results', 'warning');
                }
            }
        }

        return allScriptsPresent;
    } catch (error) {
        addResult('package-scripts', `${description} validation`, false, `Error reading package.json: ${error.message}`);
        return false;
    }
}

function validateDockerConfiguration() {
    logSection('Validating Docker Configuration');

    const dockerFiles = [
        { path: 'docker-compose.yml', description: 'Main Docker Compose' },
        { path: 'docker-compose.dev.yml', description: 'Development Docker Compose' },
        { path: 'docker-compose.prod.yml', description: 'Production Docker Compose' },
        { path: 'docker-compose.staging.yml', description: 'Staging Docker Compose' },
        { path: 'Dockerfile', description: 'Main Dockerfile' },
        { path: 'frontend/Dockerfile.dev', description: 'Frontend Dev Dockerfile' },
        { path: 'frontend/Dockerfile.prod', description: 'Frontend Prod Dockerfile' }
    ];

    let allPresent = true;
    dockerFiles.forEach(({ path, description }) => {
        if (!checkFile(path, description, 'docker')) {
            allPresent = false;
        }
    });

    // Validate Docker Compose syntax
    const composeFiles = ['docker-compose.yml', 'docker-compose.dev.yml', 'docker-compose.prod.yml'];
    composeFiles.forEach(file => {
        if (fs.existsSync(file)) {
            const validateResult = executeCommand(`docker-compose -f ${file} config`, { silent: true });
            addResult('docker', `${file} syntax validation`, validateResult.success,
                validateResult.success ? '' : 'Invalid Docker Compose syntax');
        }
    });

    return allPresent;
}

function validateBuildScripts() {
    logSection('Validating Build Scripts');

    const buildScripts = [
        { path: 'scripts/build.sh', description: 'Unix build script' },
        { path: 'scripts/build.bat', description: 'Windows build script' },
        { path: 'scripts/verify-build.js', description: 'Build verification script' }
    ];

    let allPresent = true;
    buildScripts.forEach(({ path, description }) => {
        if (!checkFile(path, description, 'build-scripts')) {
            allPresent = false;
        } else {
            // Check if script is executable (Unix only)
            if (path.endsWith('.sh') && process.platform !== 'win32') {
                try {
                    const stats = fs.statSync(path);
                    const isExecutable = !!(stats.mode & parseInt('111', 8));
                    addResult('build-scripts', `${description} executable`, isExecutable,
                        isExecutable ? '' : 'Script is not executable');
                } catch (error) {
                    addResult('build-scripts', `${description} permissions`, false, error.message);
                }
            }
        }
    });

    return allPresent;
}

function validateBuildOutput() {
    logSection('Validating Build Output');

    let hasValidOutput = true;

    // Check frontend build output
    if (fs.existsSync('frontend/dist')) {
        const distSize = getDirectorySize('frontend/dist');
        const sizeValid = distSize > 0 && distSize <= QUALITY_THRESHOLDS.maxBundleSize;

        addResult('build-output', 'Frontend build output', distSize > 0,
            distSize === 0 ? 'Build output is empty' : `Size: ${formatBytes(distSize)}`);

        addResult('build-output', 'Frontend bundle size', sizeValid,
            sizeValid ? '' : `Bundle size ${formatBytes(distSize)} exceeds ${formatBytes(QUALITY_THRESHOLDS.maxBundleSize)}`,
            'warning');

        // Check for critical files
        const criticalFiles = ['index.html', 'assets'];
        criticalFiles.forEach(file => {
            const filePath = path.join('frontend/dist', file);
            checkFile(filePath, `Frontend ${file}`, 'build-output');
        });

        if (distSize === 0) hasValidOutput = false;
    } else {
        addResult('build-output', 'Frontend build output', false, 'frontend/dist directory not found');
        hasValidOutput = false;
    }

    // Check admin build output
    if (fs.existsSync('admin-app/dist')) {
        const distSize = getDirectorySize('admin-app/dist');
        addResult('build-output', 'Admin build output', distSize > 0,
            distSize === 0 ? 'Build output is empty' : `Size: ${formatBytes(distSize)}`);

        if (distSize === 0) hasValidOutput = false;
    } else {
        addResult('build-output', 'Admin build output', false, 'admin-app/dist directory not found');
        hasValidOutput = false;
    }

    // Check backend installation
    const backendCheck = executeCommand('cd backend && python -c "import app; print(\'Success\')"', { silent: true });
    addResult('build-output', 'Backend installation', backendCheck.success,
        backendCheck.success ? '' : 'Backend import failed');

    if (!backendCheck.success) hasValidOutput = false;

    return hasValidOutput;
}

function validatePerformance() {
    logSection('Validating Performance Metrics');

    let performanceGood = true;

    // Check build artifacts size
    const frontendSize = fs.existsSync('frontend/dist') ? getDirectorySize('frontend/dist') : 0;
    const adminSize = fs.existsSync('admin-app/dist') ? getDirectorySize('admin-app/dist') : 0;
    const totalSize = frontendSize + adminSize;

    addResult('performance', 'Total bundle size', totalSize <= QUALITY_THRESHOLDS.maxBundleSize,
        `Total: ${formatBytes(totalSize)}, Limit: ${formatBytes(QUALITY_THRESHOLDS.maxBundleSize)}`,
        totalSize > QUALITY_THRESHOLDS.maxBundleSize ? 'warning' : 'info');

    // Check for source maps in production builds
    if (fs.existsSync('frontend/dist')) {
        const hasSourceMaps = fs.readdirSync('frontend/dist/assets', { withFileTypes: true })
            .some(file => file.name.endsWith('.map'));

        addResult('performance', 'Source maps in production', !hasSourceMaps,
            hasSourceMaps ? 'Source maps found in production build' : '',
            'warning');
    }

    // Check for unminified files
    if (fs.existsSync('frontend/dist/assets')) {
        const unminifiedFiles = fs.readdirSync('frontend/dist/assets')
            .filter(file => file.endsWith('.js') && !file.includes('.min.') && file.length > 50);

        addResult('performance', 'Minified assets', unminifiedFiles.length === 0,
            unminifiedFiles.length > 0 ? `Found ${unminifiedFiles.length} potentially unminified files` : '',
            'warning');
    }

    return performanceGood;
}

function validateSecurity() {
    logSection('Validating Security Configuration');

    let securityGood = true;

    // Check for sensitive files that shouldn't be committed
    const sensitiveFiles = ['.env', '.env.local', '.env.production', 'config/secrets.json'];
    sensitiveFiles.forEach(file => {
        if (fs.existsSync(file)) {
            addResult('security', `Sensitive file check: ${file}`, false,
                'Sensitive file found in repository', 'warning');
            securityGood = false;
        }
    });

    // Check for hardcoded secrets in package.json files
    const packageFiles = ['package.json', 'frontend/package.json', 'admin-app/package.json'];
    packageFiles.forEach(file => {
        if (fs.existsSync(file)) {
            const content = fs.readFileSync(file, 'utf8');
            const hasSecrets = /(?:password|secret|key|token).*[:=]\s*["'][^"']{10,}["']/i.test(content);
            addResult('security', `Hardcoded secrets check: ${file}`, !hasSecrets,
                hasSecrets ? 'Potential hardcoded secrets found' : '', 'warning');
        }
    });

    // Check Docker security
    if (fs.existsSync('Dockerfile')) {
        const dockerContent = fs.readFileSync('Dockerfile', 'utf8');
        const usesRootUser = !dockerContent.includes('USER ') || dockerContent.includes('USER root');
        addResult('security', 'Docker non-root user', !usesRootUser,
            usesRootUser ? 'Dockerfile may be running as root user' : '', 'warning');
    }

    return securityGood;
}

function validateEnvironmentConfig() {
    logSection('Validating Environment Configuration');

    let configValid = true;

    // Check for environment template files
    const envTemplates = ['.env.example', '.env.template'];
    const hasTemplate = envTemplates.some(template => fs.existsSync(template));
    addResult('environment', 'Environment template', hasTemplate,
        hasTemplate ? '' : 'No environment template found', 'warning');

    // Check for environment-specific configs
    const envConfigs = ['.env.development', '.env.staging', '.env.production'];
    envConfigs.forEach(config => {
        if (fs.existsSync(config)) {
            const content = fs.readFileSync(config, 'utf8');
            const hasPlaceholders = /your_.*_here|change_this|placeholder/i.test(content);
            addResult('environment', `${config} configuration`, !hasPlaceholders,
                hasPlaceholders ? 'Contains placeholder values' : '', 'warning');
        }
    });

    return configValid;
}

function validateCodeQuality() {
    logSection('Validating Code Quality');

    let qualityGood = true;

    // Check for linting configuration
    const lintConfigs = ['.eslintrc.js', '.eslintrc.json', 'eslint.config.js'];
    const hasLintConfig = lintConfigs.some(config => fs.existsSync(config) || fs.existsSync(`frontend/${config}`));
    addResult('code-quality', 'ESLint configuration', hasLintConfig,
        hasLintConfig ? '' : 'No ESLint configuration found');

    // Check for TypeScript configuration
    const tsConfigs = ['tsconfig.json', 'frontend/tsconfig.json'];
    const hasTsConfig = tsConfigs.some(config => fs.existsSync(config));
    addResult('code-quality', 'TypeScript configuration', hasTsConfig,
        hasTsConfig ? '' : 'No TypeScript configuration found');

    // Check for test configuration
    const testConfigs = ['jest.config.js', 'vitest.config.js', 'frontend/vitest.config.js'];
    const hasTestConfig = testConfigs.some(config => fs.existsSync(config));
    addResult('code-quality', 'Test configuration', hasTestConfig,
        hasTestConfig ? '' : 'No test configuration found');

    // Run linting if available
    if (hasLintConfig) {
        const lintResult = executeCommand('cd frontend && npm run lint', { silent: true });
        addResult('code-quality', 'Frontend linting', lintResult.success,
            lintResult.success ? '' : 'Linting errors found', 'warning');
    }

    return qualityGood;
}

function generateBuildQualityReport() {
    logSection('Generating Build Quality Report');

    const report = {
        timestamp: new Date().toISOString(),
        version: SCRIPT_VERSION,
        summary: {
            passed: validationResults.passed,
            failed: validationResults.failed,
            warnings: validationResults.warnings,
            total: validationResults.passed + validationResults.failed + validationResults.warnings
        },
        categories: {},
        recommendations: []
    };

    // Group results by category
    validationResults.details.forEach(result => {
        if (!report.categories[result.category]) {
            report.categories[result.category] = {
                passed: 0,
                failed: 0,
                warnings: 0,
                details: []
            };
        }

        if (result.passed) {
            report.categories[result.category].passed++;
        } else if (result.severity === 'warning') {
            report.categories[result.category].warnings++;
        } else {
            report.categories[result.category].failed++;
        }

        report.categories[result.category].details.push(result);
    });

    // Generate recommendations
    if (report.categories['build-output']?.failed > 0) {
        report.recommendations.push('Run build process to generate required output files');
    }

    if (report.categories['security']?.failed > 0 || report.categories['security']?.warnings > 0) {
        report.recommendations.push('Review and fix security issues before deployment');
    }

    if (report.categories['performance']?.warnings > 0) {
        report.recommendations.push('Optimize bundle size and performance metrics');
    }

    if (report.categories['code-quality']?.failed > 0) {
        report.recommendations.push('Fix linting errors and improve code quality');
    }

    // Save report to file
    const reportPath = `logs/build/verification_report_${Date.now()}.json`;
    if (!fs.existsSync('logs/build')) {
        fs.mkdirSync('logs/build', { recursive: true });
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    log(`📊 Build quality report saved to: ${reportPath}`, 'blue');

    return report;
}

function generateBuildReport() {
    logHeader(`${SCRIPT_NAME} v${SCRIPT_VERSION} - Build Verification Report`);

    const validationSuite = [
        { name: 'Prerequisites', fn: validatePrerequisites },
        { name: 'Project Structure', fn: validateProjectStructure },
        { name: 'Package Configuration', fn: () => {
            let allValid = true;
            allValid &= validatePackageJson('package.json', 'Root package.json');
            allValid &= validatePackageJson('frontend/package.json', 'Frontend package.json');
            allValid &= validatePackageJson('admin-app/package.json', 'Admin package.json');
            return allValid;
        }},
        { name: 'Docker Configuration', fn: validateDockerConfiguration },
        { name: 'Build Scripts', fn: validateBuildScripts },
        { name: 'Build Output', fn: validateBuildOutput },
        { name: 'Performance Metrics', fn: validatePerformance },
        { name: 'Security Configuration', fn: validateSecurity },
        { name: 'Environment Configuration', fn: validateEnvironmentConfig },
        { name: 'Code Quality', fn: validateCodeQuality }
    ];

    log('🔍 Running comprehensive build validation...', 'blue');

    validationSuite.forEach(({ name, fn }) => {
        try {
            fn();
        } catch (error) {
            addResult('system', name, false, `Validation error: ${error.message}`);
        }
    });

    // Generate quality report
    const qualityReport = generateBuildQualityReport();

    // Display summary
    logHeader('Validation Summary');

    const totalChecks = validationResults.passed + validationResults.failed + validationResults.warnings;
    const successRate = totalChecks > 0 ? Math.round((validationResults.passed / totalChecks) * 100) : 0;

    log(`📊 Total Checks: ${totalChecks}`, 'blue');
    log(`✅ Passed: ${validationResults.passed}`, 'green');
    log(`❌ Failed: ${validationResults.failed}`, 'red');
    log(`⚠️  Warnings: ${validationResults.warnings}`, 'yellow');
    log(`📈 Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');

    // Category breakdown
    logSection('Category Breakdown');
    Object.entries(qualityReport.categories).forEach(([category, stats]) => {
        const total = stats.passed + stats.failed + stats.warnings;
        const rate = total > 0 ? Math.round((stats.passed / total) * 100) : 0;
        log(`${category}: ${stats.passed}/${total} (${rate}%) - ${stats.failed} failed, ${stats.warnings} warnings`,
            rate >= 90 ? 'green' : rate >= 70 ? 'yellow' : 'red');
    });

    // Recommendations
    if (qualityReport.recommendations.length > 0) {
        logSection('Recommendations');
        qualityReport.recommendations.forEach((rec, index) => {
            log(`${index + 1}. ${rec}`, 'yellow');
        });
    }

    // Final verdict
    const buildReady = validationResults.failed === 0;
    const productionReady = buildReady && validationResults.warnings <= 2;

    if (productionReady) {
        logHeader('🎉 Build is Production Ready!');
        log('✅ All critical checks passed', 'green');
        log('✅ Minimal warnings detected', 'green');
        log('✅ Ready for deployment', 'green');

        logSection('Next Steps');
        log('1. Install dependencies: npm run install:all', 'cyan');
        log('2. Run tests: npm run test', 'cyan');
        log('3. Build for production: npm run build:production', 'cyan');
        log('4. Deploy: npm run start:prod', 'cyan');
        log('5. Monitor: npm run logs', 'cyan');
    } else if (buildReady) {
        logHeader('⚠️  Build is Ready with Warnings');
        log('✅ All critical checks passed', 'green');
        log('⚠️  Some warnings detected - review before production', 'yellow');

        logSection('Next Steps');
        log('1. Review and fix warnings above', 'yellow');
        log('2. Install dependencies: npm run install:all', 'cyan');
        log('3. Start development: npm run dev', 'cyan');
        log('4. Run tests: npm run test', 'cyan');
    } else {
        logHeader('❌ Build is Not Ready');
        log('❌ Critical issues detected', 'red');
        log('🔧 Fix the errors above before proceeding', 'red');

        logSection('Next Steps');
        log('1. Fix all failed checks above', 'red');
        log('2. Re-run verification: npm run verify', 'yellow');
        log('3. Check BUILD.md for detailed instructions', 'cyan');
    }

    log(`\n📄 Detailed report: ${qualityReport.timestamp}`, 'blue');

    return buildReady;
}

// CLI interface
function showHelp() {
    console.log(`
${SCRIPT_NAME} v${SCRIPT_VERSION}

USAGE:
    node scripts/verify-build.js [OPTIONS]

OPTIONS:
    --help, -h          Show this help message
    --verbose, -v       Enable verbose output
    --category <name>   Run specific category only
    --json              Output results in JSON format
    --no-color          Disable colored output

CATEGORIES:
    prerequisites       Check system prerequisites
    structure          Check project structure
    packages           Check package configurations
    docker             Check Docker configurations
    scripts            Check build scripts
    output             Check build output
    performance        Check performance metrics
    security           Check security configuration
    environment        Check environment configuration
    quality            Check code quality

EXAMPLES:
    node scripts/verify-build.js                    # Full verification
    node scripts/verify-build.js --category docker  # Docker checks only
    node scripts/verify-build.js --json             # JSON output
    node scripts/verify-build.js --verbose          # Verbose output
`);
}

// Parse command line arguments
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {
        verbose: false,
        category: null,
        json: false,
        noColor: false,
        help: false
    };

    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        switch (arg) {
            case '--help':
            case '-h':
                options.help = true;
                break;
            case '--verbose':
            case '-v':
                options.verbose = true;
                break;
            case '--category':
                options.category = args[++i];
                break;
            case '--json':
                options.json = true;
                break;
            case '--no-color':
                options.noColor = true;
                break;
        }
    }

    return options;
}

// Main execution
if (require.main === module) {
    const options = parseArgs();

    if (options.help) {
        showHelp();
        process.exit(0);
    }

    // Disable colors if requested
    if (options.noColor) {
        Object.keys(colors).forEach(key => {
            colors[key] = '';
        });
    }

    try {
        const success = generateBuildReport();

        if (options.json) {
            const report = generateBuildQualityReport();
            console.log(JSON.stringify(report, null, 2));
        }

        process.exit(success ? 0 : 1);
    } catch (error) {
        log(`❌ Verification failed: ${error.message}`, 'red');
        if (options.verbose) {
            console.error(error.stack);
        }
        process.exit(1);
    }
}

module.exports = {
    generateBuildReport,
    validatePrerequisites,
    validateProjectStructure,
    validateDockerConfiguration,
    validateBuildOutput,
    validatePerformance,
    validateSecurity,
    validateEnvironmentConfig,
    validateCodeQuality
};

"""
Real-time Inventory Tracking Service for E-commerce Integration.
Provides comprehensive inventory management with WebSocket support, alerts, and analytics.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from bson import ObjectId

from app.models.ecommerce import (
    SyncedProduct, InventoryAlert, InventoryHistory, InventorySnapshot,
    InventoryChangeType
)
from app.models.user import User, PyObjectId
from app.db.mongodb import get_database
from app.core.redis import get_redis_client
from app.core.websocket import websocket_manager
from app.core.monitoring import monitor_performance, log_audit_event, OperationType
from app.services.notifications.notification_service import notification_service
# Simple error handler for service errors
def handle_service_error(error: Exception, operation: str, context: dict) -> dict:
    """Handle service errors and return standardized error response."""
    return {
        "success": False,
        "error": str(error),
        "operation": operation,
        "context": context
    }
from app.core.config import settings

logger = logging.getLogger(__name__)

# Collection names
PRODUCTS_COLLECTION = "synced_products"
INVENTORY_ALERTS_COLLECTION = "inventory_alerts"
INVENTORY_HISTORY_COLLECTION = "inventory_history"
INVENTORY_SNAPSHOTS_COLLECTION = "inventory_snapshots"

# Redis keys
INVENTORY_CACHE_KEY = "inventory:{store_id}:{product_id}"
INVENTORY_ALERTS_KEY = "inventory_alerts:{user_id}"
INVENTORY_CHANGES_KEY = "inventory_changes:{store_id}"


class InventoryTrackingService:
    """
    Real-time inventory tracking service with WebSocket support.
    """
    
    def __init__(self):
        self.redis_client = None
        self.db = None
        self.active_connections: Set[str] = set()
        
    async def _get_redis_client(self):
        """Get Redis client for caching."""
        if not self.redis_client:
            self.redis_client = await get_redis_client()
        return self.redis_client
    
    async def _get_database(self):
        """Get MongoDB database."""
        if not self.db:
            self.db = await get_database()
        return self.db
    
    @monitor_performance("inventory_update")
    async def update_inventory(
        self,
        user_id: str,
        store_id: str,
        product_id: str,
        new_quantity: int,
        change_type: InventoryChangeType = InventoryChangeType.MANUAL,
        variant_id: Optional[str] = None,
        reason: Optional[str] = None,
        reference_id: Optional[str] = None,
        changed_by: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update product inventory with real-time notifications.
        
        Args:
            user_id: User making the change
            store_id: Store ID
            product_id: Product ID
            new_quantity: New inventory quantity
            change_type: Type of inventory change
            variant_id: Optional variant ID
            reason: Reason for change
            reference_id: External reference
            changed_by: User who made the change
            
        Returns:
            Update result with change details
        """
        try:
            db = await self._get_database()
            redis = await self._get_redis_client()
            
            # Get current product
            product = await db[PRODUCTS_COLLECTION].find_one({
                "_id": ObjectId(product_id),
                "store_id": ObjectId(store_id),
                "user_id": ObjectId(user_id)
            })
            
            if not product:
                raise ValueError(f"Product {product_id} not found")
            
            previous_quantity = product.get("inventory_quantity", 0)
            change_amount = new_quantity - previous_quantity
            
            # Update product inventory
            await db[PRODUCTS_COLLECTION].update_one(
                {"_id": ObjectId(product_id)},
                {
                    "$set": {
                        "inventory_quantity": new_quantity,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            # Record inventory history
            history_record = InventoryHistory(
                user_id=PyObjectId(user_id),
                store_id=PyObjectId(store_id),
                product_id=PyObjectId(product_id),
                variant_id=variant_id,
                change_type=change_type,
                previous_quantity=previous_quantity,
                new_quantity=new_quantity,
                change_amount=change_amount,
                reason=reason,
                reference_id=reference_id,
                changed_by=changed_by,
                external_sync=change_type == InventoryChangeType.SYNC,
                sync_timestamp=datetime.now(timezone.utc) if change_type == InventoryChangeType.SYNC else None
            )
            
            await db[INVENTORY_HISTORY_COLLECTION].insert_one(
                history_record.model_dump(by_alias=True)
            )
            
            # Update cache
            cache_key = INVENTORY_CACHE_KEY.format(
                store_id=store_id, 
                product_id=product_id
            )
            if redis:
                await redis.setex(cache_key, 3600, str(new_quantity))
            
            # Check for alerts
            await self._check_inventory_alerts(
                user_id, store_id, product_id, new_quantity, variant_id
            )
            
            # Send real-time update
            await self._broadcast_inventory_update(
                user_id, store_id, product_id, {
                    "previous_quantity": previous_quantity,
                    "new_quantity": new_quantity,
                    "change_amount": change_amount,
                    "change_type": change_type.value,
                    "variant_id": variant_id,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
            
            # Log audit event
            log_audit_event(
                operation_type=OperationType.UPDATE,
                resource_type="inventory",
                resource_id=product_id,
                user_id=user_id,
                details={
                    "store_id": store_id,
                    "previous_quantity": previous_quantity,
                    "new_quantity": new_quantity,
                    "change_type": change_type.value
                }
            )
            
            return {
                "success": True,
                "product_id": product_id,
                "previous_quantity": previous_quantity,
                "new_quantity": new_quantity,
                "change_amount": change_amount,
                "history_id": str(history_record.id)
            }
            
        except Exception as e:
            logger.error(f"Error updating inventory for product {product_id}: {str(e)}")
            return handle_service_error(e, "inventory_update", {
                "product_id": product_id,
                "store_id": store_id,
                "user_id": user_id
            })
    
    async def _check_inventory_alerts(
        self,
        user_id: str,
        store_id: str,
        product_id: str,
        current_quantity: int,
        variant_id: Optional[str] = None
    ):
        """Check and trigger inventory alerts if thresholds are met."""
        try:
            db = await self._get_database()
            
            # Get active alerts for this product
            alert_filter = {
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id),
                "product_id": ObjectId(product_id),
                "is_active": True
            }
            
            if variant_id:
                alert_filter["variant_id"] = variant_id
            
            alerts = await db[INVENTORY_ALERTS_COLLECTION].find(alert_filter).to_list(None)
            
            for alert_data in alerts:
                alert = InventoryAlert(**alert_data)
                should_trigger = False
                
                # Check alert conditions
                if alert.alert_type == "low_stock" and current_quantity <= alert.threshold:
                    should_trigger = True
                elif alert.alert_type == "out_of_stock" and current_quantity == 0:
                    should_trigger = True
                elif alert.alert_type == "high_demand" and current_quantity >= alert.threshold:
                    should_trigger = True
                
                if should_trigger:
                    await self._trigger_inventory_alert(alert, current_quantity)
                    
        except Exception as e:
            logger.error(f"Error checking inventory alerts: {str(e)}")
    
    async def _trigger_inventory_alert(self, alert: InventoryAlert, current_quantity: int):
        """Trigger an inventory alert and send notifications."""
        try:
            db = await self._get_database()
            
            # Update alert status
            await db[INVENTORY_ALERTS_COLLECTION].update_one(
                {"_id": alert.id},
                {
                    "$set": {
                        "last_triggered": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    },
                    "$inc": {"trigger_count": 1}
                }
            )
            
            # Send notifications
            if alert.email_enabled:
                await self._send_alert_email(alert, current_quantity)
            
            if alert.webhook_enabled and alert.webhook_url:
                await self._send_alert_webhook(alert, current_quantity)
            
            # Send real-time notification
            await self._broadcast_alert_notification(alert, current_quantity)
            
        except Exception as e:
            logger.error(f"Error triggering inventory alert {alert.id}: {str(e)}")
    
    async def _send_alert_email(self, alert: InventoryAlert, current_quantity: int):
        """Send email notification for inventory alert."""
        try:
            # Get product details
            db = await self._get_database()
            product = await db[PRODUCTS_COLLECTION].find_one({"_id": alert.product_id})
            
            if not product:
                return
            
            # Send email notification
            await notification_service.send_email(
                user_id=str(alert.user_id),
                subject=f"Inventory Alert: {product.get('title', 'Product')}",
                template="inventory_alert",
                context={
                    "product_title": product.get("title", "Product"),
                    "alert_type": alert.alert_type,
                    "current_quantity": current_quantity,
                    "threshold": alert.threshold,
                    "store_name": product.get("store_name", "Store")
                }
            )
            
        except Exception as e:
            logger.error(f"Error sending alert email: {str(e)}")
    
    async def _send_alert_webhook(self, alert: InventoryAlert, current_quantity: int):
        """Send webhook notification for inventory alert."""
        try:
            import aiohttp
            
            webhook_data = {
                "alert_id": str(alert.id),
                "alert_type": alert.alert_type,
                "product_id": str(alert.product_id),
                "store_id": str(alert.store_id),
                "current_quantity": current_quantity,
                "threshold": alert.threshold,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            if alert.webhook_url:
                async with aiohttp.ClientSession() as session:
                    await session.post(
                        alert.webhook_url,
                        json=webhook_data,
                        timeout=aiohttp.ClientTimeout(total=10)
                    )
                
        except Exception as e:
            logger.error(f"Error sending alert webhook: {str(e)}")
    
    async def _broadcast_inventory_update(
        self,
        user_id: str,
        store_id: str,
        product_id: str,
        update_data: Dict[str, Any]
    ):
        """Broadcast inventory update via WebSocket."""
        try:
            message = {
                "type": "inventory_update",
                "store_id": store_id,
                "product_id": product_id,
                "data": update_data
            }
            
            # Send to user's connections
            await websocket_manager.send_to_user(user_id, message)
            
        except Exception as e:
            logger.error(f"Error broadcasting inventory update: {str(e)}")
    
    async def _broadcast_alert_notification(self, alert: InventoryAlert, current_quantity: int):
        """Broadcast alert notification via WebSocket."""
        try:
            message = {
                "type": "inventory_alert",
                "alert_id": str(alert.id),
                "alert_type": alert.alert_type,
                "product_id": str(alert.product_id),
                "store_id": str(alert.store_id),
                "current_quantity": current_quantity,
                "threshold": alert.threshold,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Send to user's connections
            await websocket_manager.send_to_user(str(alert.user_id), message)
            
        except Exception as e:
            logger.error(f"Error broadcasting alert notification: {str(e)}")


    @monitor_performance("create_inventory_alert")
    async def create_inventory_alert(
        self,
        user_id: str,
        store_id: str,
        product_id: str,
        alert_type: str,
        threshold: int,
        variant_id: Optional[str] = None,
        email_enabled: bool = True,
        webhook_enabled: bool = False,
        webhook_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a new inventory alert.

        Args:
            user_id: User ID
            store_id: Store ID
            product_id: Product ID
            alert_type: Type of alert (low_stock, out_of_stock, high_demand)
            threshold: Inventory threshold
            variant_id: Optional variant ID
            email_enabled: Whether to send email notifications
            webhook_enabled: Whether to send webhook notifications
            webhook_url: Webhook URL for notifications

        Returns:
            Created alert details
        """
        try:
            db = await self._get_database()

            # Check if alert already exists
            existing_alert = await db[INVENTORY_ALERTS_COLLECTION].find_one({
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id),
                "product_id": ObjectId(product_id),
                "alert_type": alert_type,
                "variant_id": variant_id,
                "is_active": True
            })

            if existing_alert:
                raise ValueError(f"Alert of type {alert_type} already exists for this product")

            # Create new alert
            alert = InventoryAlert(
                user_id=PyObjectId(user_id),
                store_id=PyObjectId(store_id),
                product_id=PyObjectId(product_id),
                variant_id=variant_id,
                alert_type=alert_type,
                threshold=threshold,
                email_enabled=email_enabled,
                webhook_enabled=webhook_enabled,
                webhook_url=webhook_url,
                last_triggered=None
            )

            result = await db[INVENTORY_ALERTS_COLLECTION].insert_one(
                alert.model_dump(by_alias=True)
            )

            # Cache alert for quick access
            redis = await self._get_redis_client()
            if redis:
                alerts_key = INVENTORY_ALERTS_KEY.format(user_id=user_id)
                redis.sadd(alerts_key, str(result.inserted_id))
                redis.expire(alerts_key, 3600)

            return {
                "success": True,
                "alert_id": str(result.inserted_id),
                "alert": alert.model_dump()
            }

        except Exception as e:
            logger.error(f"Error creating inventory alert: {str(e)}")
            return handle_service_error(e, "create_inventory_alert", {
                "user_id": user_id,
                "product_id": product_id,
                "alert_type": alert_type
            })

    @monitor_performance("get_inventory_history")
    async def get_inventory_history(
        self,
        user_id: str,
        store_id: str,
        product_id: Optional[str] = None,
        variant_id: Optional[str] = None,
        change_types: Optional[List[str]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get inventory change history with filtering options.

        Args:
            user_id: User ID
            store_id: Store ID
            product_id: Optional product ID filter
            variant_id: Optional variant ID filter
            change_types: Optional change types filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Number of records to return
            offset: Number of records to skip

        Returns:
            Inventory history records
        """
        try:
            db = await self._get_database()

            # Build filter
            filter_criteria: Dict[str, Any] = {
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id)
            }

            if product_id:
                filter_criteria["product_id"] = ObjectId(product_id)

            if variant_id:
                filter_criteria["variant_id"] = variant_id

            if change_types:
                filter_criteria["change_type"] = {"$in": change_types}

            if start_date or end_date:
                date_filter: Dict[str, Any] = {}
                if start_date:
                    date_filter["$gte"] = start_date
                if end_date:
                    date_filter["$lte"] = end_date
                filter_criteria["created_at"] = date_filter

            # Get total count
            total_count = await db[INVENTORY_HISTORY_COLLECTION].count_documents(filter_criteria)

            # Get history records
            cursor = db[INVENTORY_HISTORY_COLLECTION].find(filter_criteria)
            cursor = cursor.sort("created_at", -1).skip(offset).limit(limit)

            history_records = await cursor.to_list(None)

            # Enrich with product details
            for record in history_records:
                product = await db[PRODUCTS_COLLECTION].find_one(
                    {"_id": record["product_id"]},
                    {"title": 1, "sku": 1, "featured_image": 1}
                )
                if product:
                    record["product_details"] = {
                        "title": product.get("title"),
                        "sku": product.get("sku"),
                        "featured_image": product.get("featured_image")
                    }

            return {
                "success": True,
                "history": history_records,
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total_count
            }

        except Exception as e:
            logger.error(f"Error getting inventory history: {str(e)}")
            return handle_service_error(e, "get_inventory_history", {
                "user_id": user_id,
                "store_id": store_id
            })

    @monitor_performance("generate_inventory_snapshot")
    async def generate_inventory_snapshot(
        self,
        user_id: str,
        store_id: str,
        snapshot_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Generate daily inventory snapshot for analytics.

        Args:
            user_id: User ID
            store_id: Store ID
            snapshot_date: Date for snapshot (defaults to today)

        Returns:
            Generated snapshot details
        """
        try:
            db = await self._get_database()

            if not snapshot_date:
                snapshot_date = datetime.now(timezone.utc).replace(
                    hour=0, minute=0, second=0, microsecond=0
                )

            # Check if snapshot already exists
            existing_snapshot = await db[INVENTORY_SNAPSHOTS_COLLECTION].find_one({
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id),
                "snapshot_date": snapshot_date
            })

            if existing_snapshot:
                return {
                    "success": True,
                    "snapshot_id": str(existing_snapshot["_id"]),
                    "message": "Snapshot already exists for this date"
                }

            # Get all products for the store
            products = await db[PRODUCTS_COLLECTION].find({
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id)
            }).to_list(None)

            # Calculate metrics
            total_products = len(products)
            total_inventory_value = Decimal('0')
            low_stock_products = 0
            out_of_stock_products = 0
            category_metrics = {}

            for product in products:
                quantity = product.get("inventory_quantity", 0)
                price = Decimal(str(product.get("price", 0)))
                category = product.get("category", "Uncategorized")

                # Calculate inventory value
                total_inventory_value += price * quantity

                # Count stock levels
                if quantity == 0:
                    out_of_stock_products += 1
                elif quantity <= 10:  # Configurable low stock threshold
                    low_stock_products += 1

                # Category metrics
                if category not in category_metrics:
                    category_metrics[category] = {
                        "product_count": 0,
                        "total_quantity": 0,
                        "total_value": Decimal('0')
                    }

                category_metrics[category]["product_count"] += 1
                category_metrics[category]["total_quantity"] += quantity
                category_metrics[category]["total_value"] += price * quantity

            # Get top products by value and quantity
            products_by_value = sorted(
                products,
                key=lambda p: Decimal(str(p.get("price", 0))) * p.get("inventory_quantity", 0),
                reverse=True
            )[:10]

            products_by_quantity = sorted(
                products,
                key=lambda p: p.get("inventory_quantity", 0),
                reverse=True
            )[:10]

            # Get change summary for the day
            start_of_day = snapshot_date
            end_of_day = start_of_day + timedelta(days=1)

            changes = await db[INVENTORY_HISTORY_COLLECTION].find({
                "user_id": ObjectId(user_id),
                "store_id": ObjectId(store_id),
                "created_at": {"$gte": start_of_day, "$lt": end_of_day}
            }).to_list(None)

            total_changes = len(changes)
            net_change = sum(change.get("change_amount", 0) for change in changes)

            # Create snapshot
            snapshot = InventorySnapshot(
                user_id=PyObjectId(user_id),
                store_id=PyObjectId(store_id),
                snapshot_date=snapshot_date,
                total_products=total_products,
                total_inventory_value=total_inventory_value,
                low_stock_products=low_stock_products,
                out_of_stock_products=out_of_stock_products,
                category_metrics={k: {
                    "product_count": v["product_count"],
                    "total_quantity": v["total_quantity"],
                    "total_value": float(v["total_value"])
                } for k, v in category_metrics.items()},
                top_products_by_value=[{
                    "product_id": str(p["_id"]),
                    "title": p.get("title"),
                    "value": float(Decimal(str(p.get("price", 0))) * p.get("inventory_quantity", 0))
                } for p in products_by_value],
                top_products_by_quantity=[{
                    "product_id": str(p["_id"]),
                    "title": p.get("title"),
                    "quantity": p.get("inventory_quantity", 0)
                } for p in products_by_quantity],
                total_changes=total_changes,
                net_change=net_change
            )

            result = await db[INVENTORY_SNAPSHOTS_COLLECTION].insert_one(
                snapshot.model_dump(by_alias=True)
            )

            return {
                "success": True,
                "snapshot_id": str(result.inserted_id),
                "snapshot": snapshot.model_dump()
            }

        except Exception as e:
            logger.error(f"Error generating inventory snapshot: {str(e)}")
            return handle_service_error(e, "generate_inventory_snapshot", {
                "user_id": user_id,
                "store_id": store_id
            })


# Create singleton instance
inventory_service = InventoryTrackingService()

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  TextField,
  Button,
  Divider,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  IconButton
} from '@mui/material';
import AvatarUploader from '../../components/common/AvatarUploader';
import {
  AccountCircle as AccountCircleIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Business as BusinessIcon,
  Email as EmailIcon,
  CalendarToday as CalendarTodayIcon,
  Security as SecurityIcon,
  CardGiftcard as CardGiftcardIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';
import GlassmorphicCard from '../../components/common/GlassmorphicCard';
import UserAddons from '../../components/profile/UserAddons';
import { Link as RouterLink } from 'react-router-dom';
import { AppSumoBadge } from '../../components/appsumo';

/**
 * Profile component displays and allows editing of the user's profile information
 * with advanced features like avatar upload, password change, and account management.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isEmbedded - Whether the component is embedded in another page
 */
const Profile = ({ isEmbedded = false }) => {
  const { user, updateProfile } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // State for tabs
  const [tabValue, setTabValue] = useState(0);

  // State for profile data
  const [profileData, setProfileData] = useState({
    full_name: '',
    email: '',
    company_name: '',
  });

  // State for editing
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // State for password change
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordError, setPasswordError] = useState(null);
  const [passwordSuccess, setPasswordSuccess] = useState(false);

  // State for avatar upload
  const [avatarLoading, setAvatarLoading] = useState(false);

  // Load profile data when user changes
  useEffect(() => {
    if (user) {
      setProfileData({
        full_name: user.full_name || '',
        email: user.email || '',
        company_name: user.company_name || '',
      });
    }
  }, [user]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle profile data change
  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Reset status messages
    setError(null);
    setSuccess(false);
  };

  // Handle password data change
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Reset status messages
    setPasswordError(null);
    setPasswordSuccess(false);
  };

  // Toggle password visibility
  const togglePasswordVisibility = (field) => {
    setShowPassword((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  // Handle save profile
  const handleSaveProfile = async () => {
    // Validate required fields
    if (!profileData.full_name.trim()) {
      setError('Full name is required');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const response = await updateProfile({
        full_name: profileData.full_name.trim(),
        company_name: profileData.company_name.trim(),
      });

      if (response.success) {
        setSuccess(true);
        setIsEditing(false);
        showSuccessNotification('Profile updated successfully');
      } else {
        setError(response.error || 'Failed to update profile');
        showErrorNotification('Failed to update profile');
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      if (err.response?.status === 400) {
        setError(err.response?.data?.detail || 'Invalid profile data');
      } else if (err.response?.status === 401) {
        setError('Authentication required. Please log in again.');
      } else if (err.response?.status === 422) {
        setError('Profile validation failed. Please check your input.');
      } else {
        setError(err.response?.data?.detail || 'An unexpected error occurred. Please try again.');
      }
      showErrorNotification('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  // Handle change password
  const handleChangePassword = async () => {
    // Validate passwords
    if (passwordData.new_password !== passwordData.confirm_password) {
      setPasswordError('New passwords do not match');
      return;
    }

    if (passwordData.new_password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return;
    }

    // Additional password validation
    if (passwordData.current_password === passwordData.new_password) {
      setPasswordError('New password must be different from current password');
      return;
    }

    setPasswordLoading(true);
    setPasswordError(null);
    setPasswordSuccess(false);

    try {
      // Use the auth endpoint for password change since /api/users/change-password doesn't exist
      // We'll create a custom implementation using the existing auth system
      const response = await api.post('/api/auth/change-password', {
        current_password: passwordData.current_password,
        new_password: passwordData.new_password,
      });

      setPasswordSuccess(true);
      showSuccessNotification('Password changed successfully');

      // Reset password fields
      setPasswordData({
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
    } catch (err) {
      console.error('Error changing password:', err);
      if (err.response?.status === 400) {
        setPasswordError(err.response?.data?.detail || 'Invalid current password');
      } else if (err.response?.status === 401) {
        setPasswordError('Current password is incorrect');
      } else if (err.response?.status === 422) {
        setPasswordError('Password validation failed');
      } else {
        setPasswordError(err.response?.data?.detail || 'Failed to change password');
      }
      showErrorNotification('Failed to change password');
    } finally {
      setPasswordLoading(false);
    }
  };

  // Handle avatar change
  const handleAvatarChange = async (file) => {
    if (!file) return;

    // Upload avatar
    setAvatarLoading(true);

    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await api.post('/api/users/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Update user profile with new avatar URLs
      // Handle different response formats from the backend
      const avatarUrl = response.data.avatar_url || response.data.default_url || response.data.avatar;
      const avatarUrls = response.data.avatar_urls || response.data.urls || null;

      await updateProfile({
        avatar: avatarUrl,
        avatar_urls: avatarUrls
      });

      showSuccessNotification('Avatar updated successfully');
    } catch (err) {
      console.error('Error uploading avatar:', err);
      if (err.response?.status === 400) {
        showErrorNotification(err.response?.data?.detail || 'Invalid file type or size');
      } else if (err.response?.status === 413) {
        showErrorNotification('File too large. Please choose a smaller image.');
      } else {
        showErrorNotification(err.response?.data?.detail || 'Failed to upload avatar');
      }
    } finally {
      setAvatarLoading(false);
    }
  };

  // Handle remove avatar
  const handleRemoveAvatar = async () => {
    setAvatarLoading(true);

    try {
      await api.delete('/api/users/avatar');

      // Update user profile
      await updateProfile({
        avatar: null,
      });

      showSuccessNotification('Avatar removed successfully');
    } catch (err) {
      console.error('Error removing avatar:', err);
      if (err.response?.status === 404) {
        showErrorNotification('No avatar to remove');
      } else {
        showErrorNotification(err.response?.data?.detail || 'Failed to remove avatar');
      }
    } finally {
      setAvatarLoading(false);
    }
  };

  return (
    <Box sx={{ p: isEmbedded ? 0 : 3 }}>
      {!isEmbedded && (
        <Typography variant="h4" gutterBottom>
          <AccountCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          My Profile
        </Typography>
      )}

      <Grid container spacing={3}>
        {/* Profile sidebar - only shown when not embedded */}
        {!isEmbedded && (
          <Grid item xs={12} md={4}>
            <GlassmorphicCard variant="glass" sx={{ mb: 3 }}>
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <AvatarUploader
                  onUpload={handleAvatarChange}
                  onRemove={handleRemoveAvatar}
                  currentAvatar={user?.avatar}
                  loading={avatarLoading}
                  size={100}
                  name={user?.full_name}
                />

                <Typography variant="h6">{user?.full_name}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                    {user?.email}
                  </Typography>

                  {/* Show AppSumo badge if user has AppSumo lifetime subscription */}
                  {user?.subscription?.is_appsumo_lifetime && (
                    <AppSumoBadge
                      variant="chip"
                      size="small"
                      tierName={user?.subscription?.plan_name?.replace(' Plan', '') || ''}
                    />
                  )}
                </Box>
              </Box>

              <Divider />

              <Box sx={{ p: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <BusinessIcon fontSize="small" sx={{ mr: 1 }} />
                  {user?.company_name || 'No company specified'}
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <EmailIcon fontSize="small" sx={{ mr: 1 }} />
                  {user?.email}
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CalendarTodayIcon fontSize="small" sx={{ mr: 1 }} />
                  Member since: {user?.created_at ? format(new Date(user.created_at), 'MMMM yyyy') : 'N/A'}
                </Typography>

                {/* Show AppSumo subscription info if applicable */}
                {user?.subscription?.is_appsumo_lifetime && (
                  <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', color: '#FF8C00' }}>
                    <CardGiftcardIcon fontSize="small" sx={{ mr: 1, color: '#FF8C00' }} />
                    AppSumo Lifetime Member
                  </Typography>
                )}
              </Box>
            </GlassmorphicCard>

            <GlassmorphicCard variant="glass">
              <Box sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Quick Links
                </Typography>

                <Button
                  component={RouterLink}
                  to="/settings?tab=general"
                  variant="outlined"
                  fullWidth
                  sx={{ mb: 1 }}
                >
                  Account Settings
                </Button>

                <Button
                  component={RouterLink}
                  to="/profile/social-media"
                  variant="outlined"
                  fullWidth
                  sx={{ mb: 1 }}
                >
                  Social Media Accounts
                </Button>

                <Button
                  component={RouterLink}
                  to="/billing?view=plans"
                  variant="outlined"
                  fullWidth
                >
                  Subscription & Billing
                </Button>
              </Box>
            </GlassmorphicCard>
          </Grid>
        )}

        {/* Profile content */}
        <Grid item xs={12} md={isEmbedded ? 12 : 8}>
          {/* Show avatar uploader when embedded */}
          {isEmbedded && (
            <GlassmorphicCard variant="glass" sx={{ mb: 3 }}>
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  <AccountCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Profile Picture
                </Typography>
                <AvatarUploader
                  onUpload={handleAvatarChange}
                  onRemove={handleRemoveAvatar}
                  currentAvatar={user?.avatar}
                  loading={avatarLoading}
                  size={120}
                  name={user?.full_name}
                />
              </Box>
            </GlassmorphicCard>
          )}

          {/* User's add-ons - only shown when not embedded */}
          {!isEmbedded && (
            <>
              <UserAddons isEmbedded={isEmbedded} />
              <Box sx={{ mt: 3 }} />
            </>
          )}

          <GlassmorphicCard variant="glass">
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab icon={<AccountCircleIcon />} label="Profile Information" />
              <Tab icon={<SecurityIcon />} label="Security" />
            </Tabs>

            <Divider />

            {/* Profile Information Tab */}
            {tabValue === 0 && (
              <Box sx={{ p: 3 }}>
                {success && (
                  <Alert severity="success" sx={{ mb: 3 }}>
                    Profile updated successfully!
                  </Alert>
                )}

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      label="Full Name"
                      name="full_name"
                      value={profileData.full_name}
                      onChange={handleProfileChange}
                      fullWidth
                      disabled={!isEditing || loading}
                      required
                      error={isEditing && !profileData.full_name.trim()}
                      helperText={isEditing && !profileData.full_name.trim() ? 'Full name is required' : ''}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      label="Email Address"
                      name="email"
                      value={profileData.email}
                      fullWidth
                      disabled={true} // Email cannot be changed
                      helperText="Email address cannot be changed"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      label="Company Name"
                      name="company_name"
                      value={profileData.company_name}
                      onChange={handleProfileChange}
                      fullWidth
                      disabled={!isEditing || loading}
                    />
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                  {isEditing ? (
                    <>
                      <Button
                        variant="outlined"
                        onClick={() => {
                          setIsEditing(false);
                          // Reset form to original values
                          setProfileData({
                            full_name: user?.full_name || '',
                            email: user?.email || '',
                            company_name: user?.company_name || '',
                          });
                        }}
                        sx={{ mr: 1 }}
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleSaveProfile}
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                      >
                        Save Changes
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => setIsEditing(true)}
                      startIcon={<EditIcon />}
                    >
                      Edit Profile
                    </Button>
                  )}
                </Box>
              </Box>
            )}

            {/* Security Tab */}
            {tabValue === 1 && (
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Change Password
                </Typography>

                {passwordSuccess && (
                  <Alert severity="success" sx={{ mb: 3 }}>
                    Password changed successfully!
                  </Alert>
                )}

                {passwordError && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {passwordError}
                  </Alert>
                )}

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      label="Current Password"
                      name="current_password"
                      type={showPassword.current ? 'text' : 'password'}
                      value={passwordData.current_password}
                      onChange={handlePasswordChange}
                      fullWidth
                      required
                      disabled={passwordLoading}
                      InputProps={{
                        endAdornment: (
                          <IconButton
                            onClick={() => togglePasswordVisibility('current')}
                            edge="end"
                          >
                            {showPassword.current ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      label="New Password"
                      name="new_password"
                      type={showPassword.new ? 'text' : 'password'}
                      value={passwordData.new_password}
                      onChange={handlePasswordChange}
                      fullWidth
                      required
                      disabled={passwordLoading}
                      helperText="Password must be at least 8 characters long"
                      InputProps={{
                        endAdornment: (
                          <IconButton
                            onClick={() => togglePasswordVisibility('new')}
                            edge="end"
                          >
                            {showPassword.new ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      label="Confirm New Password"
                      name="confirm_password"
                      type={showPassword.confirm ? 'text' : 'password'}
                      value={passwordData.confirm_password}
                      onChange={handlePasswordChange}
                      fullWidth
                      required
                      disabled={passwordLoading}
                      error={passwordData.confirm_password && passwordData.new_password !== passwordData.confirm_password}
                      helperText={
                        passwordData.confirm_password && passwordData.new_password !== passwordData.confirm_password
                          ? 'Passwords do not match'
                          : ''
                      }
                      InputProps={{
                        endAdornment: (
                          <IconButton
                            onClick={() => togglePasswordVisibility('confirm')}
                            edge="end"
                          >
                            {showPassword.confirm ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        ),
                      }}
                    />
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleChangePassword}
                    disabled={
                      passwordLoading ||
                      !passwordData.current_password ||
                      !passwordData.new_password ||
                      !passwordData.confirm_password ||
                      passwordData.new_password !== passwordData.confirm_password ||
                      passwordData.new_password.length < 8
                    }
                    startIcon={passwordLoading ? <CircularProgress size={20} /> : <SaveIcon />}
                  >
                    Change Password
                  </Button>
                </Box>
              </Box>
            )}
          </GlassmorphicCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Profile;

/**
 * Enhanced ACE Social Coupon Performance Tracker - Enterprise-grade performance tracking component
 * Features: Comprehensive performance tracking with advanced metrics visualization, real-time
 * monitoring, and conversion analysis capabilities for ACE Social promotional campaigns, detailed
 * performance dashboard with usage statistics and conversion rate tracking, advanced tracking
 * features with real-time data visualization and trend analysis, ACE Social's analytics system
 * integration with seamless data aggregation and performance monitoring, tracking interaction
 * features including data filtering and metric comparison, tracking state management with
 * real-time data updates and metric caching, and real-time performance monitoring with live
 * metric displays and automatic tracking optimization
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  useState,
  useMemo,
  useEffect,
  useCallback,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Skeleton,
  LinearProgress,
  Paper,
  Stack,
  Grid,
  Divider,
  Button,
  ButtonGroup,
  Badge,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide,
  Collapse
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Timeline as TimelineIcon,
  LocalOffer as CouponIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  Insights as InsightsIcon,
  Compare as CompareIcon,
  ShowChart as ChartIcon,
  PieChart as PieChartIcon,
  BarChart as BarChartIcon,
  Schedule as ScheduleIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Settings as SettingsIcon,
  MonetizationOn as MoneyIcon,
  Group as GroupIcon,
  Star as StarIcon
} from '@mui/icons-material';
import {
  formatDate,
  formatDateTime,
  getRelativeTime,
  exportToCSV,
  debounce,
  formatDiscount,
  getCouponStatus,
  formatCurrency,
} from '../../utils/couponHelpers';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Performance tracking constants
const TRACKING_VIEWS = {
  OVERVIEW: 'overview',
  DETAILED: 'detailed',
  ANALYTICS: 'analytics',
  COMPARISON: 'comparison'
};

const PERFORMANCE_METRICS = {
  REDEMPTIONS: 'redemptions',
  CONVERSION_RATE: 'conversion_rate',
  REVENUE_IMPACT: 'revenue_impact',
  USAGE_RATE: 'usage_rate'
};

const TIME_PERIODS = {
  LAST_7_DAYS: 'last_7_days',
  LAST_30_DAYS: 'last_30_days',
  LAST_90_DAYS: 'last_90_days',
  LAST_YEAR: 'last_year'
};

// Performance analytics events
const TRACKING_ANALYTICS_EVENTS = {
  VIEW_CHANGED: 'tracking_view_changed',
  METRIC_CLICKED: 'tracking_metric_clicked',
  DATA_EXPORTED: 'tracking_data_exported',
  FILTER_APPLIED: 'tracking_filter_applied',
  REFRESH_TRIGGERED: 'tracking_refresh_triggered',
  PERFORMANCE_ALERT: 'tracking_performance_alert'
};

/**
 * Enhanced Coupon Performance Tracker - Comprehensive performance tracking with advanced features
 * Implements detailed performance tracking management and enterprise-grade tracking capabilities
 */
const EnhancedCouponPerformanceTracker = memo(forwardRef(({
  data,
  loading = false,
  error,
  onRefresh,
  className,
  enableAdvancedFeatures = true,
  enableRealTimeTracking = true,
  enableAnalytics = true,
  enableAccessibility = true,
  enableExportOptions = true,
  enableComparison = true,
  defaultView = TRACKING_VIEWS.OVERVIEW,
  defaultTimePeriod = TIME_PERIODS.LAST_30_DAYS,
  autoRefreshInterval = 300000, // 5 minutes
  maxDisplayCoupons = 1000,
  onViewChange,
  onMetricClick,
  onDataExport,
  onAnalyticsTrack,
  onFilterChange,
  onPerformanceAlert,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const tableRef = useRef(null);
  const chartRef = useRef(null);
  const exportRef = useRef(null);
  const autoRefreshTimeoutRef = useRef(null);

  // Core state management
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    performance: 'all',
    timePeriod: defaultTimePeriod
  });
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);

  // Enhanced state management
  const [currentView, setCurrentView] = useState(defaultView);
  const [selectedMetrics, setSelectedMetrics] = useState([PERFORMANCE_METRICS.REDEMPTIONS]);
  const [comparisonMode, setComparisonMode] = useState(false);
  const [selectedCoupons, setSelectedCoupons] = useState([]);
  const [sortConfig, setSortConfig] = useState({ field: 'metrics.redemptions', direction: 'desc' });
  const [trackingAnalytics, setTrackingAnalytics] = useState({
    viewChanges: 0,
    metricClicks: 0,
    dataExports: 0,
    filterApplications: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    refreshData: () => handleRefresh(),
    exportData: () => handleExport(),
    resetFilters: () => handleResetFilters(),
    focusTable: () => tableRef.current?.focus(),

    // Navigation methods
    changeView: (view) => handleViewChange(view),
    setTimePeriod: (period) => handleTimePeriodChange(period),
    toggleComparison: () => setComparisonMode(!comparisonMode),

    // Data methods
    getFilteredData: () => filteredCoupons,
    getSelectedCoupons: () => selectedCoupons,
    getTrackingAnalytics: () => trackingAnalytics,
    getCurrentMetrics: () => selectedMetrics,

    // State methods
    isLoading: () => loading,
    hasError: () => !!error,
    getRowCount: () => filteredCoupons.length,
    getCurrentPage: () => page,

    // Selection methods
    selectCoupon: (couponId) => handleCouponSelect(couponId),
    selectAllCoupons: () => handleSelectAll(),
    clearSelection: () => setSelectedCoupons([]),

    // Analytics methods
    getPerformanceInsights: () => generatePerformanceInsights(),
    getTopPerformers: () => getTopPerformingCoupons(),
    getLowPerformers: () => getLowPerformingCoupons(),

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    generateReport: () => generatePerformanceReport(),
    scheduleAlert: (config) => schedulePerformanceAlert(config),
    optimizePerformance: () => optimizeTrackingPerformance()
  }), [
    filteredCoupons,
    selectedCoupons,
    trackingAnalytics,
    selectedMetrics,
    loading,
    error,
    page,
    comparisonMode,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(TRACKING_ANALYTICS_EVENTS.VIEW_CHANGED, {
        view: currentView,
        timestamp: new Date().toISOString(),
        couponsCount: data?.coupons?.length || 0
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Performance tracker view changed to ${currentView}`);
    }
  }, [currentView, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader, data?.coupons?.length]);

  // Auto-refresh effect
  useEffect(() => {
    if (enableRealTimeTracking && autoRefreshInterval > 0) {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }

      autoRefreshTimeoutRef.current = setTimeout(() => {
        if (onRefresh) {
          onRefresh();
        }
      }, autoRefreshInterval);

      return () => {
        if (autoRefreshTimeoutRef.current) {
          clearTimeout(autoRefreshTimeoutRef.current);
        }
      };
    }
  }, [enableRealTimeTracking, autoRefreshInterval, onRefresh]);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((term) => {
      setSearchTerm(term);
      setPage(0);

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack('search_performed', {
          searchTerm: term.substring(0, 50), // Limit for privacy
          timestamp: new Date().toISOString()
        });
      }
    }, 300),
    [enableAnalytics, onAnalyticsTrack]
  );

  // Enhanced performance metrics calculation
  const couponsWithMetrics = useMemo(() => {
    if (!data?.coupons || !Array.isArray(data.coupons)) {
      return [];
    }

    return data.coupons.map(coupon => {
      const redemptions = coupon.redemption_count || 0;
      const maxRedemptions = coupon.max_redemptions || Infinity;
      const usageRate = maxRedemptions === Infinity ? redemptions : (redemptions / maxRedemptions) * 100;

      // Enhanced revenue impact calculation
      const avgOrderValue = 100; // Assume $100 average order
      const discountAmount = coupon.discount_type === 'percentage' ?
        (coupon.discount_value / 100) * avgOrderValue :
        coupon.discount_value;
      const revenueImpact = redemptions * discountAmount;

      // Enhanced performance rating with more sophisticated logic
      let performanceRating = 'low';
      let performanceScore = 0;

      // Calculate performance score based on multiple factors
      const redemptionScore = Math.min(redemptions / 100, 1) * 40; // Max 40 points
      const usageRateScore = Math.min(usageRate / 100, 1) * 30; // Max 30 points
      const revenueScore = Math.min(revenueImpact / 1000, 1) * 30; // Max 30 points

      performanceScore = redemptionScore + usageRateScore + revenueScore;

      if (performanceScore >= 70) {
        performanceRating = 'high';
      } else if (performanceScore >= 40) {
        performanceRating = 'medium';
      }

      // Calculate conversion rate (simplified)
      const conversionRate = redemptions > 0 ? (redemptions / (redemptions + 100)) * 100 : 0;

      // Calculate trend (simplified - would need historical data in real implementation)
      const trend = Math.random() > 0.5 ? 'up' : 'down';
      const trendPercentage = (Math.random() * 20 - 10).toFixed(1); // -10% to +10%

      return {
        ...coupon,
        metrics: {
          usageRate,
          revenueImpact,
          performanceRating,
          performanceScore,
          redemptions,
          conversionRate,
          trend,
          trendPercentage,
          avgOrderValue,
          discountAmount
        },
      };
    });
  }, [data?.coupons]);

  // Enhanced handler functions
  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();

      if (enableAccessibility) {
        announceToScreenReader('Performance data refreshed');
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(TRACKING_ANALYTICS_EVENTS.REFRESH_TRIGGERED, {
          timestamp: new Date().toISOString(),
          view: currentView
        });
      }
    }
  }, [onRefresh, enableAccessibility, enableAnalytics, onAnalyticsTrack, announceToScreenReader, currentView]);

  const handleViewChange = useCallback((newView) => {
    setCurrentView(newView);

    setTrackingAnalytics(prev => ({
      ...prev,
      viewChanges: prev.viewChanges + 1
    }));

    if (onViewChange) {
      onViewChange(newView);
    }

    if (enableAccessibility) {
      announceToScreenReader(`Switched to ${newView} view`);
    }
  }, [onViewChange, enableAccessibility, announceToScreenReader]);

  const handleMetricClick = useCallback((metric, coupon) => {
    setTrackingAnalytics(prev => ({
      ...prev,
      metricClicks: prev.metricClicks + 1
    }));

    if (onMetricClick) {
      onMetricClick(metric, coupon);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(TRACKING_ANALYTICS_EVENTS.METRIC_CLICKED, {
        metric,
        couponId: coupon.id,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Selected ${metric} metric for coupon ${coupon.code}`);
    }
  }, [onMetricClick, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleCouponSelect = useCallback((couponId) => {
    setSelectedCoupons(prev => {
      const isSelected = prev.includes(couponId);
      const newSelection = isSelected
        ? prev.filter(id => id !== couponId)
        : [...prev, couponId];

      if (enableAccessibility) {
        announceToScreenReader(`Coupon ${isSelected ? 'deselected' : 'selected'}`);
      }

      return newSelection;
    });
  }, [enableAccessibility, announceToScreenReader]);

  const handleSelectAll = useCallback(() => {
    const allIds = filteredCoupons.map(coupon => coupon.id);
    setSelectedCoupons(allIds);

    if (enableAccessibility) {
      announceToScreenReader(`Selected all ${allIds.length} coupons`);
    }
  }, [filteredCoupons, enableAccessibility, announceToScreenReader]);

  // Enhanced utility functions
  const generatePerformanceInsights = useCallback(() => {
    if (!couponsWithMetrics.length) return null;

    const totalRedemptions = couponsWithMetrics.reduce((sum, coupon) => sum + coupon.metrics.redemptions, 0);
    const avgPerformanceScore = couponsWithMetrics.reduce((sum, coupon) => sum + coupon.metrics.performanceScore, 0) / couponsWithMetrics.length;
    const highPerformers = couponsWithMetrics.filter(coupon => coupon.metrics.performanceRating === 'high').length;

    return {
      totalRedemptions,
      avgPerformanceScore: avgPerformanceScore.toFixed(1),
      highPerformersCount: highPerformers,
      highPerformersPercentage: ((highPerformers / couponsWithMetrics.length) * 100).toFixed(1)
    };
  }, [couponsWithMetrics]);

  const getTopPerformingCoupons = useCallback((limit = 5) => {
    return [...couponsWithMetrics]
      .sort((a, b) => b.metrics.performanceScore - a.metrics.performanceScore)
      .slice(0, limit);
  }, [couponsWithMetrics]);

  const getLowPerformingCoupons = useCallback((limit = 5) => {
    return [...couponsWithMetrics]
      .sort((a, b) => a.metrics.performanceScore - b.metrics.performanceScore)
      .slice(0, limit);
  }, [couponsWithMetrics]);

  const handleResetFilters = useCallback(() => {
    setFilters({
      status: 'all',
      type: 'all',
      performance: 'all',
      timePeriod: defaultTimePeriod
    });
    setSearchTerm('');
    setPage(0);

    if (enableAccessibility) {
      announceToScreenReader('All filters reset');
    }
  }, [defaultTimePeriod, enableAccessibility, announceToScreenReader]);

  const handleTimePeriodChange = useCallback((period) => {
    setFilters(prev => ({ ...prev, timePeriod: period }));

    if (enableAccessibility) {
      announceToScreenReader(`Time period changed to ${period}`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  // Enhanced filter and search coupons
  const filteredCoupons = useMemo(() => {
    let filtered = [...couponsWithMetrics];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(coupon =>
        coupon.code?.toLowerCase().includes(term) ||
        coupon.name?.toLowerCase().includes(term) ||
        coupon.description?.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(coupon => {
        const status = getCouponStatus(coupon);
        return status.status === filters.status;
      });
    }

    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(coupon => coupon.discount_type === filters.type);
    }

    // Apply performance filter
    if (filters.performance !== 'all') {
      filtered = filtered.filter(coupon => coupon.metrics.performanceRating === filters.performance);
    }

    // Enhanced sorting based on sortConfig
    if (sortConfig.field) {
      filtered.sort((a, b) => {
        const aValue = sortConfig.field.includes('.')
          ? sortConfig.field.split('.').reduce((obj, key) => obj?.[key], a)
          : a[sortConfig.field];
        const bValue = sortConfig.field.includes('.')
          ? sortConfig.field.split('.').reduce((obj, key) => obj?.[key], b)
          : b[sortConfig.field];

        if (sortConfig.direction === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }

    // Limit results for performance
    if (maxDisplayCoupons && filtered.length > maxDisplayCoupons) {
      filtered = filtered.slice(0, maxDisplayCoupons);
    }

    return filtered;
  }, [couponsWithMetrics, searchTerm, filters, sortConfig, maxDisplayCoupons]);

  // Enhanced available types calculation
  const availableTypes = useMemo(() => {
    if (!data?.coupons) return [];
    const types = [...new Set(data.coupons.map(c => c.discount_type).filter(Boolean))];
    return types.sort();
  }, [data?.coupons]);

  // Enhanced pagination handlers
  const handleChangePage = useCallback((event, newPage) => {
    setPage(newPage);

    if (enableAccessibility) {
      announceToScreenReader(`Moved to page ${newPage + 1}`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleChangeRowsPerPage = useCallback((event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setPage(0);

    if (enableAccessibility) {
      announceToScreenReader(`Changed to show ${newRowsPerPage} rows per page`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  // Enhanced search handler
  const handleSearchChange = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, [debouncedSearch]);

  // Enhanced filter change handler
  const handleFilterChange = useCallback((filterType, value) => {
    setFilters(prev => ({ ...prev, [filterType]: value }));
    setPage(0);
    setFilterMenuAnchor(null);

    setTrackingAnalytics(prev => ({
      ...prev,
      filterApplications: prev.filterApplications + 1
    }));

    if (onFilterChange) {
      onFilterChange(filterType, value);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(TRACKING_ANALYTICS_EVENTS.FILTER_APPLIED, {
        filterType,
        value,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Applied ${filterType} filter: ${value}`);
    }
  }, [onFilterChange, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced export handler
  const handleExport = useCallback(() => {
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'name', label: 'Name' },
      { key: 'discount_type', label: 'Type' },
      { key: 'discount_value', label: 'Value', type: 'discount' },
      { key: 'metrics.redemptions', label: 'Redemptions' },
      { key: 'metrics.usageRate', label: 'Usage Rate (%)' },
      { key: 'metrics.revenueImpact', label: 'Revenue Impact', type: 'currency' },
      { key: 'metrics.performanceRating', label: 'Performance' },
      { key: 'metrics.performanceScore', label: 'Performance Score' },
      { key: 'metrics.conversionRate', label: 'Conversion Rate (%)' },
      { key: 'metrics.trend', label: 'Trend' },
      { key: 'metrics.trendPercentage', label: 'Trend %' },
      { key: 'created_at', label: 'Created At', type: 'date' },
    ];

    const exportData = selectedCoupons.length > 0
      ? filteredCoupons.filter(coupon => selectedCoupons.includes(coupon.id))
      : filteredCoupons;

    exportToCSV(
      exportData,
      `coupon-performance-${formatDate(new Date())}`,
      exportColumns
    );

    setTrackingAnalytics(prev => ({
      ...prev,
      dataExports: prev.dataExports + 1
    }));

    if (onDataExport) {
      onDataExport(exportData);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(TRACKING_ANALYTICS_EVENTS.DATA_EXPORTED, {
        exportedCount: exportData.length,
        selectedOnly: selectedCoupons.length > 0,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Exported ${exportData.length} coupon performance records`);
    }
  }, [filteredCoupons, selectedCoupons, onDataExport, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced performance color function
  const getPerformanceColor = useCallback((rating) => {
    switch (rating) {
      case 'high':
        return 'success';
      case 'medium':
        return 'warning';
      case 'low':
        return 'error';
      default:
        return 'default';
    }
  }, []);

  // Enhanced performance icon function
  const getPerformanceIcon = useCallback((rating, trend) => {
    if (rating === 'high') {
      return trend === 'up' ? <TrendingUpIcon /> : <StarIcon />;
    } else if (rating === 'low') {
      return <TrendingDownIcon />;
    }
    return null;
  }, []);

  // Calculate paginated coupons
  const paginatedCoupons = useMemo(() => {
    return filteredCoupons.slice(
      page * rowsPerPage,
      page * rowsPerPage + rowsPerPage
    );
  }, [filteredCoupons, page, rowsPerPage]);

  // Render loading skeleton
  if (loading && !data?.coupons) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent>
            <Skeleton variant="text" width="40%" height={32} sx={{ mb: 2 }} />
            {[1, 2, 3, 4, 5].map((item) => (
              <Skeleton key={item} variant="rectangular" height={60} sx={{ mb: 1 }} />
            ))}
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box className={className} {...props}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  // Render empty state
  if (!data?.coupons || data.coupons.length === 0) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <CouponIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Coupons Found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Performance data will appear here once you have active coupons.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box className={className} {...props}>
      <Card variant="glass">
        <CardContent>
          {/* Header */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TimelineIcon />
              Coupon Performance Tracker
            </Typography>
            <Box display="flex" gap={1}>
              <Tooltip title="Export Data">
                <IconButton onClick={handleExport} disabled={filteredCoupons.length === 0}>
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Filters">
                <IconButton onClick={(e) => setFilterMenuAnchor(e.currentTarget)}>
                  <FilterIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Search and Filters */}
          <Box display="flex" gap={2} mb={3}>
            <TextField
              placeholder="Search by code, name, or description..."
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ flexGrow: 1 }}
            />
          </Box>

          {/* Results Summary */}
          <Typography variant="body2" color="text.secondary" mb={2}>
            Showing {paginatedCoupons.length} of {filteredCoupons.length} coupons
          </Typography>

          {/* Performance Table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Code</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Discount</TableCell>
                  <TableCell>Redemptions</TableCell>
                  <TableCell>Usage Rate</TableCell>
                  <TableCell>Revenue Impact</TableCell>
                  <TableCell>Performance</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedCoupons.map((coupon) => {
                  const status = getCouponStatus(coupon);
                  return (
                    <TableRow key={coupon.id} hover>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace" fontWeight="medium">
                          {coupon.code}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {coupon.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {coupon.description?.substring(0, 50)}...
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {formatDiscount(coupon)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body2" fontWeight="medium">
                            {coupon.metrics.redemptions}
                          </Typography>
                          {coupon.max_redemptions && (
                            <Typography variant="caption" color="text.secondary">
                              / {coupon.max_redemptions}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min(coupon.metrics.usageRate, 100)}
                            sx={{ width: 80, height: 6, borderRadius: 3, mb: 0.5 }}
                            color={getPerformanceColor(coupon.metrics.performanceRating)}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {coupon.metrics.usageRate.toFixed(1)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(coupon.metrics.revenueImpact)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={coupon.metrics.performanceRating.charAt(0).toUpperCase() + coupon.metrics.performanceRating.slice(1)}
                          color={getPerformanceColor(coupon.metrics.performanceRating)}
                          size="small"
                          icon={coupon.metrics.performanceRating === 'high' ? <TrendingUpIcon /> : 
                                coupon.metrics.performanceRating === 'low' ? <TrendingDownIcon /> : undefined}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                          color={status.color}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small">
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={filteredCoupons.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[10, 25, 50, 100]}
          />
        </CardContent>
      </Card>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterMenuAnchor}
        open={Boolean(filterMenuAnchor)}
        onClose={() => setFilterMenuAnchor(null)}
      >
        <MenuItem disabled>
          <Typography variant="subtitle2">Filter Options</Typography>
        </MenuItem>
        
        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              label="Status"
            >
              <MenuItem value="all">All Statuses</MenuItem>
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
              <MenuItem value="expired">Expired</MenuItem>
              <MenuItem value="exhausted">Exhausted</MenuItem>
            </Select>
          </FormControl>
        </MenuItem>

        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Type</InputLabel>
            <Select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              label="Type"
            >
              <MenuItem value="all">All Types</MenuItem>
              {availableTypes.map((type) => (
                <MenuItem key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </MenuItem>

        <MenuItem>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Performance</InputLabel>
            <Select
              value={filters.performance}
              onChange={(e) => handleFilterChange('performance', e.target.value)}
              label="Performance"
            >
              <MenuItem value="all">All Performance</MenuItem>
              <MenuItem value="high">High</MenuItem>
              <MenuItem value="medium">Medium</MenuItem>
              <MenuItem value="low">Low</MenuItem>
            </Select>
          </FormControl>
        </MenuItem>
      </Menu>
    </Box>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedCouponPerformanceTracker.propTypes = {
  // Core props
  data: PropTypes.shape({
    coupons: PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      code: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      description: PropTypes.string,
      discount_type: PropTypes.oneOf(['percentage', 'fixed_amount', 'free_trial_extension', 'free_addon']).isRequired,
      discount_value: PropTypes.number.isRequired,
      redemption_count: PropTypes.number,
      max_redemptions: PropTypes.number,
      is_active: PropTypes.bool,
      created_at: PropTypes.string
    }))
  }),
  loading: PropTypes.bool,
  error: PropTypes.string,
  onRefresh: PropTypes.func,

  // Enhanced props
  className: PropTypes.string,
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeTracking: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableExportOptions: PropTypes.bool,
  enableComparison: PropTypes.bool,

  // Configuration
  defaultView: PropTypes.oneOf(Object.values(TRACKING_VIEWS)),
  defaultTimePeriod: PropTypes.oneOf(Object.values(TIME_PERIODS)),
  autoRefreshInterval: PropTypes.number,
  maxDisplayCoupons: PropTypes.number,

  // Callback props
  onViewChange: PropTypes.func,
  onMetricClick: PropTypes.func,
  onDataExport: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onFilterChange: PropTypes.func,
  onPerformanceAlert: PropTypes.func
};

// Default props
EnhancedCouponPerformanceTracker.defaultProps = {
  data: { coupons: [] },
  loading: false,
  error: null,
  onRefresh: null,
  className: '',
  enableAdvancedFeatures: true,
  enableRealTimeTracking: true,
  enableAnalytics: true,
  enableAccessibility: true,
  enableExportOptions: true,
  enableComparison: true,
  defaultView: TRACKING_VIEWS.OVERVIEW,
  defaultTimePeriod: TIME_PERIODS.LAST_30_DAYS,
  autoRefreshInterval: 300000,
  maxDisplayCoupons: 1000,
  onViewChange: null,
  onMetricClick: null,
  onDataExport: null,
  onAnalyticsTrack: null,
  onFilterChange: null,
  onPerformanceAlert: null
};

// Display name for debugging
EnhancedCouponPerformanceTracker.displayName = 'EnhancedCouponPerformanceTracker';

export default EnhancedCouponPerformanceTracker;

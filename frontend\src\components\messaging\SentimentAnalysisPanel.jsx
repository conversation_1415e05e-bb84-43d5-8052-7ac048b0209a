/**
 * Enhanced Sentiment Analysis Panel - Enterprise-grade sentiment analysis component
 * Features: Plan-based sentiment limitations, real-time sentiment tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced sentiment analysis capabilities and interactive sentiment exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from "react";
import PropTypes from 'prop-types';
import {
  Box,
  Paper,
  Typography,
  Chip,
  LinearProgress,
  Divider,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Collapse,
  Alert,
  CircularProgress,
  useMediaQuery,
  alpha,
  Snackbar
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Refresh as RefreshIcon,
  Psychology as PsychologyIcon,
  Speed as SpeedIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { Line<PERSON><PERSON>, Line, <PERSON>Axis, <PERSON>Axis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';

import ConversationSentimentIndicator from './ConversationSentimentIndicator';
import { useAuth } from '../../contexts/AuthContext';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Sentiment display modes with enhanced configurations
const SENTIMENT_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Sentiment',
    description: 'Basic sentiment analysis interface',
    subscriptionLimits: {
      creator: { available: true, maxSentimentTypes: 3, features: ['basic_sentiment'] },
      accelerator: { available: true, maxSentimentTypes: 10, features: ['basic_sentiment', 'analytics_sentiment'] },
      dominator: { available: true, maxSentimentTypes: -1, features: ['basic_sentiment', 'analytics_sentiment', 'ai_insights'] }
    }
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Sentiment',
    description: 'Comprehensive sentiment analysis',
    subscriptionLimits: {
      creator: { available: false, maxSentimentTypes: 0, features: [] },
      accelerator: { available: true, maxSentimentTypes: 10, features: ['detailed_sentiment', 'sentiment_analytics'] },
      dominator: { available: true, maxSentimentTypes: -1, features: ['detailed_sentiment', 'sentiment_analytics', 'real_time_insights'] }
    }
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Sentiment',
    description: 'AI-powered sentiment analysis and optimization',
    subscriptionLimits: {
      creator: { available: false, maxSentimentTypes: 0, features: [] },
      accelerator: { available: false, maxSentimentTypes: 0, features: [] },
      dominator: { available: true, maxSentimentTypes: -1, features: ['ai_assisted', 'ai_optimization', 'sentiment_insights'] }
    }
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Sentiment',
    description: 'Advanced sentiment analytics and insights',
    subscriptionLimits: {
      creator: { available: false, maxSentimentTypes: 0, features: [] },
      accelerator: { available: false, maxSentimentTypes: 0, features: [] },
      dominator: { available: true, maxSentimentTypes: -1, features: ['analytics_sentiment', 'sentiment_insights'] }
    }
  }
};

/**
 * Enhanced Sentiment Analysis Panel Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {string} props.conversationId - ID of the conversation to analyze
 * @param {Function} [props.onSentimentUpdate] - Callback when sentiment is updated
 * @param {boolean} [props.expanded=false] - Whether panel is expanded by default
 * @param {boolean} [props.showTrends=true] - Whether to show sentiment trends
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=false] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onUpgrade] - Upgrade callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-sentiment-analysis-panel'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const SentimentAnalysisPanel = memo(forwardRef(({
  conversationId,
  onSentimentUpdate,
  expanded = false,
  showTrends = true,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = false,
  onExport,
  onRefresh,
  onUpgrade,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-sentiment-analysis-panel',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useAccessibility();
  const { user } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const sentimentRef = useRef(null);
  const [sentimentData, setSentimentData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isExpanded, setIsExpanded] = useState(expanded);
  const [refreshing, setRefreshing] = useState(false);

  // Enhanced state management
  const [sentimentMode, setSentimentMode] = useState('compact');
  const [sentimentHistory, setSentimentHistory] = useState([]);
  const [sentimentAnalytics, setSentimentAnalytics] = useState(null);
  const [sentimentInsights, setSentimentInsights] = useState(null);
  const [customSentiments, setCustomSentiments] = useState([]);
  const [sentimentPreferences, setSentimentPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: false,
    sentimentSuggestions: true
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [sentimentDrawerOpen, setSentimentDrawerOpen] = useState(false);
  const [selectedSentimentType, setSelectedSentimentType] = useState(null);
  const [sentimentStats, setSentimentStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastSentimentCheck, setLastSentimentCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Apply feature overrides based on props
    const advancedFeaturesEnabled = enableAdvancedFeatures && subscription?.plan_id !== 'creator';
    const aiInsightsEnabled = enableAIInsights && subscription?.plan_id === 'dominator';

    const features = {
      creator: {
        maxSentimentTypes: 3,
        maxSentimentsPerConversation: 10,
        hasAdvancedSentiment: false,
        hasSentimentAnalytics: false,
        hasCustomSentiments: false,
        hasSentimentInsights: false,
        hasSentimentHistory: false,
        hasAIAssistance: false,
        hasSentimentExport: false,
        hasSentimentScheduling: false,
        hasSentimentAutomation: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxSentimentTypes: 10,
        maxSentimentsPerConversation: 50,
        hasAdvancedSentiment: true,
        hasSentimentAnalytics: true,
        hasCustomSentiments: true,
        hasSentimentInsights: false,
        hasSentimentHistory: true,
        hasAIAssistance: false,
        hasSentimentExport: true,
        hasSentimentScheduling: true,
        hasSentimentAutomation: false,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxSentimentTypes: -1,
        maxSentimentsPerConversation: -1,
        hasAdvancedSentiment: true,
        hasSentimentAnalytics: true,
        hasCustomSentiments: true,
        hasSentimentInsights: true,
        hasSentimentHistory: true,
        hasAIAssistance: true,
        hasSentimentExport: true,
        hasSentimentScheduling: true,
        hasSentimentAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'ai-powered',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    // Apply feature overrides
    const enhancedFeatures = {
      ...currentFeatures,
      hasAdvancedSentiment: currentFeatures.hasAdvancedSentiment && advancedFeaturesEnabled,
      hasSentimentInsights: currentFeatures.hasSentimentInsights && aiInsightsEnabled,
      hasSentimentAnalytics: currentFeatures.hasSentimentAnalytics && advancedFeaturesEnabled
    };

    return {
      ...enhancedFeatures,
      hasFeatureAccess: (feature) => enhancedFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = enhancedFeatures[feature] === true;
        const withinLimits = enhancedFeatures.maxSentimentTypes === -1 || currentUsage < enhancedFeatures.maxSentimentTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription, enableAdvancedFeatures, enableAIInsights]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Sentiment analysis with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Sentiment interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive sentiment API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getSentimentHistory: () => sentimentHistory,
    getSentimentAnalytics: () => sentimentAnalytics,
    getSentimentInsights: () => sentimentInsights,
    refreshSentiment: () => {
      fetchSentimentAnalytics();
      fetchSentimentAnalysis();
      if (onRefresh) onRefresh();
    },

    // Sentiment methods
    focusSentiment: () => {
      if (sentimentRef.current) {
        sentimentRef.current.focus();
      }
    },
    expandPanel: () => setIsExpanded(true),
    collapsePanel: () => setIsExpanded(false),
    togglePanel: () => setIsExpanded(prev => !prev),
    openSentimentDrawer: () => setSentimentDrawerOpen(true),
    closeSentimentDrawer: () => setSentimentDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportSentimentData: () => {
      if (subscriptionFeatures.hasExport && onExport) {
        onExport(sentimentHistory, sentimentAnalytics);
      }
    },

    // Accessibility methods
    announceSentiment: (message) => announceToScreenReader(message),
    focusSentimentField: () => setFocusToElement('sentiment-analysis-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getCurrentStep: () => activeStep,
    goToStep: (step) => setActiveStep(step),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => sentimentMode,
    getSentimentStats: () => sentimentStats,

    // Enhanced methods
    changeSentimentMode: (mode) => handleSentimentModeChange(mode),
    showUpgradePrompt: (feature) => handleUpgradePrompt(feature),
    addCustomSentiment: (sentiment) => addCustomSentiment(sentiment),
    updatePreferences: (prefs) => updateSentimentPreferences(prefs),
    selectSentimentType: (type) => handleSentimentTypeSelection(type),
    generateAI: () => generateAISuggestions(),
    validateSentiment: (sentiment) => validateSentiment(sentiment),
    fetchStats: () => fetchSentimentStats(),
    getSelectedType: () => selectedSentimentType,
    getCustomSentiments: () => customSentiments,
    getSentimentPreferences: () => sentimentPreferences,
    isDrawerOpen: () => sentimentDrawerOpen,
    isAnalyticsVisible: () => showAnalytics,
    isFullscreenActive: () => fullscreenMode
  }), [
    sentimentHistory,
    sentimentAnalytics,
    sentimentInsights,
    sentimentStats,
    subscriptionFeatures,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    activeStep,
    validationErrors,
    sentimentMode,
    fetchSentimentAnalytics,
    fetchSentimentAnalysis,
    addCustomSentiment,
    customSentiments,
    fetchSentimentStats,
    fullscreenMode,
    generateAISuggestions,
    handleSentimentModeChange,
    handleSentimentTypeSelection,
    handleUpgradePrompt,
    selectedSentimentType,
    sentimentDrawerOpen,
    sentimentPreferences,
    showAnalytics,
    updateSentimentPreferences,
    validateSentiment
  ]);

  // Fetch sentiment analytics with enhanced error handling and retry logic
  const fetchSentimentAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/sentiment/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setSentimentAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (sentimentPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Sentiment analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch sentiment analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load sentiment analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, sentimentPreferences.showAnalytics]);

  // Fetch sentiment insights
  const fetchSentimentInsights = useCallback(async () => {
    if (!subscriptionFeatures.hasSentimentInsights) return;

    await handleApiRequest(
      async () => {
        const response = await api.get('/api/sentiment/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setSentimentInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch sentiment insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest, subscriptionFeatures.hasSentimentInsights]);

  useEffect(() => {
    if (conversationId && subscriptionFeatures.hasFeatureAccess('sentiment_analysis')) {
      fetchSentimentAnalysis();
    }
  }, [conversationId, subscriptionFeatures, fetchSentimentAnalysis]);

  // Initial data loading
  useEffect(() => {
    if (subscriptionFeatures.hasSentimentAnalytics) {
      fetchSentimentAnalytics();
    }
    if (subscriptionFeatures.hasSentimentInsights) {
      fetchSentimentInsights();
    }
  }, [subscriptionFeatures.hasSentimentAnalytics, subscriptionFeatures.hasSentimentInsights, fetchSentimentAnalytics, fetchSentimentInsights]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && sentimentData) {
      // Optimize sentiment analysis based on real-time data
      const optimizationTimer = setTimeout(() => {
        if (subscriptionFeatures.hasSentimentAnalytics) {
          fetchSentimentAnalytics();
        }
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, sentimentData, subscriptionFeatures.hasSentimentAnalytics, fetchSentimentAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastSentimentCheck(Date.now());

          if (wasUnavailable && sentimentPreferences.showAnalytics) {
            showSuccess("Connection restored - Sentiment features available");
          }
        } else {
          setBackendAvailable(false);
          if (sentimentPreferences.showAnalytics) {
            showError("Backend service unavailable - Some sentiment features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastSentimentCheck;
          if (timeSinceLastCheck > 60000 && sentimentPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Sentiment analysis may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastSentimentCheck, sentimentPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when sentiment data changes
  useEffect(() => {
    if (sentimentData && subscriptionFeatures.hasSentimentAnalytics) {
      fetchSentimentStats();
    }
  }, [sentimentData, subscriptionFeatures.hasSentimentAnalytics, fetchSentimentStats]);

  // Generate AI suggestions when sentiment data changes
  useEffect(() => {
    if (sentimentData && subscriptionFeatures.hasAIAssistance && sentimentPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [sentimentData, subscriptionFeatures.hasAIAssistance, sentimentPreferences.aiAssistance, generateAISuggestions]);

  const fetchSentimentAnalysis = useCallback(async (forceRefresh = false) => {
    if (!conversationId || !subscriptionFeatures.hasFeatureAccess('sentiment_analysis')) return;

    try {
      setLoading(true);
      setError(null);
      
      if (forceRefresh) {
        setRefreshing(true);
      }

      const response = await api.get(`/inbox/sentiment/conversation/${conversationId}/sentiment`, {
        params: {
          include_history: showTrends,
          analyze_intent: true
        }
      });

      setSentimentData(response.data);
      
      if (onSentimentUpdate) {
        onSentimentUpdate(response.data);
      }

      if (forceRefresh) {
        showSuccessNotification('Sentiment analysis updated');
      }

    } catch (err) {
      console.error('Error fetching sentiment analysis:', err);
      setError(err.response?.data?.detail || 'Failed to analyze conversation sentiment');
      showErrorNotification('Failed to load sentiment analysis');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [conversationId, subscriptionFeatures, showTrends, onSentimentUpdate, showSuccessNotification, showErrorNotification]);

  const handleRefresh = useCallback(() => {
    fetchSentimentAnalysis(true);

    // Track refresh action
    const refreshRecord = {
      id: Date.now(),
      type: 'sentiment_refresh',
      conversationId,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setSentimentHistory(prev => [refreshRecord, ...prev.slice(0, 99)]);

    if (sentimentPreferences.showAnalytics) {
      announceToScreenReader('Sentiment analysis refreshed');
    }
  }, [fetchSentimentAnalysis, conversationId, user?.id, sentimentPreferences.showAnalytics, announceToScreenReader]);

  const handleToggleExpanded = useCallback(() => {
    setIsExpanded(!isExpanded);

    // Track expansion toggle
    const toggleRecord = {
      id: Date.now(),
      type: 'panel_toggle',
      expanded: !isExpanded,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setSentimentHistory(prev => [toggleRecord, ...prev.slice(0, 99)]);

    if (sentimentPreferences.showAnalytics) {
      announceToScreenReader(`Sentiment panel ${!isExpanded ? 'expanded' : 'collapsed'}`);
    }
  }, [isExpanded, user?.id, sentimentPreferences.showAnalytics, announceToScreenReader]);

  // Handle sentiment mode switching
  const handleSentimentModeChange = useCallback((newMode) => {
    if (SENTIMENT_MODES[newMode.toUpperCase()]) {
      setSentimentMode(newMode);
      announceToScreenReader(`Sentiment mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setSentimentHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (sentimentPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} sentiment mode`);
      }
    }
  }, [announceToScreenReader, user?.id, sentimentPreferences.showAnalytics, showSuccess]);

  // Handle upgrade prompts
  const handleUpgradePrompt = useCallback((feature) => {
    if (onUpgrade) {
      onUpgrade(feature);
    } else {
      showError(`${feature} requires a plan upgrade`);
    }

    // Track upgrade prompt
    const upgradeRecord = {
      id: Date.now(),
      type: 'upgrade_prompt',
      feature,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setSentimentHistory(prev => [upgradeRecord, ...prev.slice(0, 99)]);
  }, [onUpgrade, showError, user?.id]);

  // Handle custom sentiment management
  const addCustomSentiment = useCallback((sentiment) => {
    if (subscriptionFeatures.hasCustomSentiments) {
      const newSentiment = {
        id: Date.now(),
        ...sentiment,
        createdAt: new Date().toISOString(),
        userId: user?.id
      };

      setCustomSentiments(prev => [...prev, newSentiment]);

      // Track sentiment creation
      const sentimentRecord = {
        id: Date.now(),
        type: 'custom_sentiment_created',
        sentiment: newSentiment.name,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setSentimentHistory(prev => [sentimentRecord, ...prev.slice(0, 99)]);

      if (sentimentPreferences.showAnalytics) {
        showSuccess(`Custom sentiment "${sentiment.name}" created`);
      }
    } else {
      handleUpgradePrompt('Custom Sentiments');
    }
  }, [subscriptionFeatures.hasCustomSentiments, user?.id, sentimentPreferences.showAnalytics, showSuccess, handleUpgradePrompt]);

  // Handle sentiment preferences updates
  const updateSentimentPreferences = useCallback((newPreferences) => {
    setSentimentPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setSentimentHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (sentimentPreferences.showAnalytics) {
      showSuccess('Sentiment preferences updated');
    }
  }, [user?.id, sentimentPreferences.showAnalytics, showSuccess]);

  // Handle sentiment type selection
  const handleSentimentTypeSelection = useCallback((sentimentType) => {
    setSelectedSentimentType(sentimentType);

    // Track sentiment type selection
    const typeRecord = {
      id: Date.now(),
      type: 'sentiment_type_selected',
      sentimentType,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setSentimentHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (sentimentPreferences.showAnalytics) {
      announceToScreenReader(`Selected sentiment type: ${sentimentType}`);
    }
  }, [user?.id, sentimentPreferences.showAnalytics, announceToScreenReader]);

  // Handle AI suggestions generation
  const generateAISuggestions = useCallback(async () => {
    if (subscriptionFeatures.hasAIAssistance) {
      try {
        const response = await api.get('/api/sentiment/ai-suggestions', {
          params: {
            conversationId,
            context: sentimentData?.sentiment_analysis?.overall_sentiment_score || 0
          }
        });

        setAiSuggestions(response.data.suggestions || []);

        if (sentimentPreferences.showAnalytics) {
          showSuccess('AI suggestions generated');
        }
      } catch (error) {
        console.error('Failed to generate AI suggestions:', error);
        showError('Failed to generate AI suggestions');
      }
    }
  }, [subscriptionFeatures.hasAIAssistance, conversationId, sentimentData, sentimentPreferences.showAnalytics, showSuccess, showError]);

  // Handle sentiment stats fetching
  const fetchSentimentStats = useCallback(async () => {
    if (subscriptionFeatures.hasSentimentAnalytics) {
      try {
        const response = await api.get('/api/sentiment/stats');
        setSentimentStats(response.data);
      } catch (error) {
        console.error('Failed to fetch sentiment stats:', error);
      }
    }
  }, [subscriptionFeatures.hasSentimentAnalytics]);

  // Handle validation errors
  const validateSentiment = useCallback((sentiment) => {
    const errors = {};

    if (!sentiment.score && sentiment.score !== 0) {
      errors.score = 'Sentiment score is required';
    }
    if (sentiment.score && (sentiment.score < -1 || sentiment.score > 1)) {
      errors.score = 'Sentiment score must be between -1 and 1';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Render emotion chips
  const renderEmotions = useCallback((emotions) => {
    if (!emotions || !subscriptionFeatures.hasAdvancedSentiment) return null;

    const emotionEntries = Object.entries(emotions)
      .filter(([, value]) => value > 0.1)
      .sort(([, a], [, b]) => b - a)
      .slice(0, subscriptionFeatures.planTier === 3 ? 5 : 3);

    if (emotionEntries.length === 0) return null;

    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Detected Emotions
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {emotionEntries.map(([emotion, confidence]) => (
            <Chip
              key={emotion}
              label={`${emotion.charAt(0).toUpperCase() + emotion.slice(1)} (${(confidence * 100).toFixed(0)}%)`}
              size="small"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                fontSize: '0.75rem'
              }}
            />
          ))}
        </Box>
      </Box>
    );
  }, [subscriptionFeatures.hasAdvancedSentiment, subscriptionFeatures.planTier]);

  // Render key phrases
  const renderKeyPhrases = useCallback((phrases) => {
    if (!phrases || phrases.length === 0) return null;

    const maxPhrases = subscriptionFeatures.planTier === 1 ? 3 :
                      subscriptionFeatures.planTier === 2 ? 5 : 10;

    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Key Phrases
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {phrases.slice(0, maxPhrases).map((phrase, index) => (
            <Chip
              key={index}
              label={phrase}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.75rem' }}
            />
          ))}
        </Box>
      </Box>
    );
  }, [subscriptionFeatures.planTier]);

  // Render sentiment trend chart
  const renderTrendChart = useCallback((history) => {
    if (!history || history.length < 2 || !showTrends) return null;

    const chartData = history.map((point, index) => ({
      index: index + 1,
      sentiment: point.sentiment_score,
      confidence: point.confidence
    }));

    return (
      <Box sx={{ mt: 3, height: 200 }}>
        <Typography variant="subtitle2" gutterBottom>
          Sentiment Trend
        </Typography>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="index" />
            <YAxis domain={[-1, 1]} />
            <RechartsTooltip 
              formatter={(value, name) => [
                name === 'sentiment' ? value.toFixed(2) : `${(value * 100).toFixed(0)}%`,
                name === 'sentiment' ? 'Sentiment Score' : 'Confidence'
              ]}
            />
            <Line
              type="monotone"
              dataKey="sentiment"
              stroke={ACE_COLORS.PURPLE}
              strokeWidth={2}
              dot={{ fill: ACE_COLORS.PURPLE, strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </Box>
    );
  }, [showTrends]);

  if (!subscriptionFeatures.hasFeatureAccess('sentiment_analysis')) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        Sentiment analysis is available with Creator plan and above. 
        <strong> Upgrade your subscription</strong> to access this feature.
      </Alert>
    );
  }

  if (loading && !sentimentData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !sentimentData) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!sentimentData) {
    return null;
  }

  const analysis = sentimentData.sentiment_analysis;

  return (
    <Paper
      {...getAccessibilityProps()}
      ref={sentimentRef}
      elevation={1}
      sx={{
        m: isMobile ? 1 : 2,
        overflow: 'hidden',
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
          borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PsychologyIcon color="primary" />
          <Typography variant="subtitle1" fontWeight="medium">
            Sentiment Analysis
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Refresh Analysis">
            <IconButton 
              size="small" 
              onClick={handleRefresh}
              disabled={refreshing}
            >
              {refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
          
          <IconButton size="small" onClick={handleToggleExpanded}>
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Main sentiment display */}
      <Box sx={{ p: 2 }}>
        <ConversationSentimentIndicator
          sentimentScore={analysis.overall_sentiment_score}
          confidence={analysis.overall_confidence}
          customerIntent={analysis.customer_intent}
          urgencyLevel={analysis.urgency_level}
          sentimentTrend={analysis.sentiment_trend}
          variant="detailed"
          showDetails={true}
        />
      </Box>

      {/* Expanded content */}
      <Collapse in={isExpanded}>
        <Divider />
        <Box sx={{ p: 2 }}>
          <Grid container spacing={2}>
            {/* Metrics */}
            <Grid item xs={12} sm={6}>
              <Card variant="outlined">
                <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <SpeedIcon fontSize="small" color="primary" />
                    <Typography variant="subtitle2">
                      Analysis Metrics
                    </Typography>
                  </Box>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="caption" color="textSecondary">
                      Confidence Level
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={analysis.overall_confidence * 100}
                      sx={{ mt: 0.5, height: 6, borderRadius: 3 }}
                    />
                    <Typography variant="caption">
                      {(analysis.overall_confidence * 100).toFixed(0)}%
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" color="textSecondary">
                    Messages analyzed: {analysis.total_messages_analyzed}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Intent Analysis */}
            <Grid item xs={12} sm={6}>
              <Card variant="outlined">
                <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <TimelineIcon fontSize="small" color="primary" />
                    <Typography variant="subtitle2">
                      Customer Intent
                    </Typography>
                  </Box>
                  
                  <Typography variant="body1" sx={{ mb: 1 }}>
                    {analysis.customer_intent.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Typography>
                  
                  <Box sx={{ mb: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={analysis.intent_confidence * 100}
                      sx={{ height: 4, borderRadius: 2 }}
                    />
                  </Box>
                  
                  <Typography variant="caption" color="textSecondary">
                    {(analysis.intent_confidence * 100).toFixed(0)}% confidence
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Key phrases */}
          {renderKeyPhrases(analysis.key_phrases)}

          {/* Emotions */}
          {renderEmotions(analysis.emotions_detected)}

          {/* Trend chart */}
          {renderTrendChart(analysis.sentiment_history)}
        </Box>
      </Collapse>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying sentiment analysis... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}
    </Paper>
  );
}));

// Enhanced PropTypes with comprehensive validation
SentimentAnalysisPanel.propTypes = {
  // Core props
  conversationId: PropTypes.string.isRequired,
  onSentimentUpdate: PropTypes.func,
  expanded: PropTypes.bool,
  showTrends: PropTypes.bool,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onUpgrade: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

SentimentAnalysisPanel.displayName = 'SentimentAnalysisPanel';

export default SentimentAnalysisPanel;

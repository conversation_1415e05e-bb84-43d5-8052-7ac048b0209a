// @since 2024-1-1 to 2025-25-7
import { useState, useMemo, useCallback, useEffect, memo, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  IconButton,
  Tooltip,
  Skeleton,
  Alert,
  AlertTitle,
  Fade,
  useTheme,
  alpha,
  useMediaQuery,
  CircularProgress,
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Chip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  ExpandMore as ExpandIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Dashboard as DashboardIcon,
  Download as ExportIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  CloudDone as CloudDoneIcon,
  MonitorHeart as MonitorHeartIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import EmptyStateHandler from './EmptyStateHandler';
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Dashboard types with enhanced configurations
const DASHBOARD_TYPES = {
  PRODUCTION_OVERVIEW: {
    id: 'production-overview',
    name: 'Production Overview',
    icon: DashboardIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Comprehensive production dashboard overview',
    subscriptionLimits: {
      creator: { widgets: 4, metrics: 6, realTime: false },
      accelerator: { widgets: 12, metrics: 20, realTime: true },
      dominator: { widgets: -1, metrics: -1, realTime: true, advanced: true }
    }
  },
  PERFORMANCE_MONITORING: {
    id: 'performance-monitoring',
    name: 'Performance Monitoring',
    icon: SpeedIcon,
    color: ACE_COLORS.YELLOW,
    description: 'Real-time performance monitoring and analytics',
    subscriptionLimits: {
      creator: { widgets: 3, metrics: 4, realTime: false },
      accelerator: { widgets: 8, metrics: 15, realTime: true },
      dominator: { widgets: -1, metrics: -1, realTime: true, advanced: true }
    }
  },
  SYSTEM_HEALTH: {
    id: 'system-health',
    name: 'System Health',
    icon: MonitorHeartIcon,
    color: ACE_COLORS.DARK,
    description: 'System health monitoring and diagnostics',
    subscriptionLimits: {
      creator: { widgets: 0, metrics: 0, realTime: false },
      accelerator: { widgets: 6, metrics: 10, realTime: true },
      dominator: { widgets: -1, metrics: -1, realTime: true, advanced: true }
    }
  },
  DEPLOYMENT_TRACKING: {
    id: 'deployment-tracking',
    name: 'Deployment Tracking',
    icon: CloudDoneIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Track deployments and release metrics',
    subscriptionLimits: {
      creator: { widgets: 0, metrics: 0, realTime: false },
      accelerator: { widgets: 4, metrics: 8, realTime: true },
      dominator: { widgets: -1, metrics: -1, realTime: true, advanced: true }
    }
  },
  ANALYTICS_SUMMARY: {
    id: 'analytics-summary',
    name: 'Analytics Summary',
    icon: AssessmentIcon,
    color: ACE_COLORS.YELLOW,
    description: 'Comprehensive analytics and insights summary',
    subscriptionLimits: {
      creator: { widgets: 0, metrics: 0, realTime: false },
      accelerator: { widgets: 0, metrics: 0, realTime: false },
      dominator: { widgets: -1, metrics: -1, realTime: true, advanced: true }
    }
  }
};



/**
 * Enhanced ProductionDashboardCard Component - Enterprise-grade production dashboard card
 * Features: Plan-based dashboard limitations, real-time production monitoring, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced dashboard insights and interactive production exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.title - Card title
 * @param {string} [props.subtitle] - Card subtitle
 * @param {React.Node} [props.children] - Card content
 * @param {React.Node} [props.icon] - Card icon
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onExpand] - Expand callback
 * @param {boolean} [props.loading=false] - Loading state
 * @param {Object} [props.error=null] - Error object
 * @param {boolean} [props.isEmpty=false] - Empty state
 * @param {number} [props.minHeight=200] - Minimum height
 * @param {number} [props.maxHeight] - Maximum height
 * @param {string} [props.tooltipContent] - Tooltip content
 * @param {React.Node} [props.headerAction] - Header action element
 * @param {boolean} [props.showRefreshButton=true] - Show refresh button
 * @param {boolean} [props.showExpandButton=true] - Show expand button
 * @param {boolean} [props.refreshing=false] - Refreshing state
 * @param {string} [props.correlationId] - Error correlation ID
 * @param {string} [props.lastUpdated] - Last updated timestamp
 * @param {string} [props.priority='normal'] - Priority level
 * @param {string} [props.variant='glass'] - Card variant
 * @param {string} [props.dashboardType='production-overview'] - Dashboard type
 * @param {boolean} [props.enableAnalytics=false] - Enable analytics features
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onDashboardTypeChange] - Dashboard type change callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Object} [props.productionMetrics] - Production metrics data
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const ProductionDashboardCard = memo(forwardRef(({
  title,
  subtitle,
  children,
  icon,
  onRefresh,
  onExpand,
  loading = false,
  error = null,
  isEmpty = false,
  minHeight = 200,
  maxHeight,
  tooltipContent,
  headerAction,
  showRefreshButton = true,
  showExpandButton = true,
  refreshing = false,
  correlationId,
  lastUpdated,
  priority = 'normal',
  variant = 'glass',
  dashboardType = 'production-overview',

  enableExport = false,
  realTimeUpdates = false,
  onDashboardTypeChange,
  onExport,
  productionMetrics,
  customization = {},
  // Empty state props
  emptyStateType = 'content-performance',
  emptyStateConfig = null,
  onEmptyStateAction,
  onEmptyStateSecondaryAction,
  showEmptyStateProgress = true,
  alwaysShowCard = true,
  emptyStateVariant = 'default',
  className = '',
  style = {},
  testId = 'production-dashboard-card',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading || subscriptionLoading,
    refreshing: refreshing,
    showAnalyticsPanel: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showSettingsDialog: false,
    isVisible: true,
    retryCount: 0,
    currentDashboardType: dashboardType,
    lastUpdated: lastUpdated,
    animationKey: 0,
    errors: {},
    // Production dashboard state
    productionHealth: 'healthy',
    systemStatus: 'operational',
    deploymentStatus: 'stable'
  });

  // Production data state
  const [productionData, setProductionData] = useState({
    raw: productionMetrics || null,
    processed: null,
    health: null,
    metrics: null,
    alerts: []
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);

  /**
   * Enhanced plan-based dashboard validation - Production Ready
   */
  const validateDashboardFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canViewDashboard: false,
        hasDashboardAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { widgets: 0, metrics: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based dashboard limits
    const planLimits = {
      creator: {
        widgets: 4,
        metrics: 6,
        features: ['basic_dashboard'],
        realTime: false,
        export: false,
        analytics: false,
        dashboardTypes: ['production-overview'],
        customization: false
      },
      accelerator: {
        widgets: 12,
        metrics: 20,
        features: ['basic_dashboard', 'advanced_dashboard', 'dashboard_interactions'],
        realTime: true,
        export: true,
        analytics: true,
        dashboardTypes: ['production-overview', 'performance-monitoring', 'system-health', 'deployment-tracking'],
        customization: true
      },
      dominator: {
        widgets: -1,
        metrics: -1,
        features: ['basic_dashboard', 'advanced_dashboard', 'dashboard_interactions', 'ai_powered_dashboard'],
        realTime: true,
        export: true,
        analytics: true,
        dashboardTypes: ['production-overview', 'performance-monitoring', 'system-health', 'deployment-tracking', 'analytics-summary'],
        customization: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = children ? 1 : 0;
    const limit = currentPlanLimits.widgets;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewDashboard: true,
      hasDashboardAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, children]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const dashboardLimits = validateDashboardFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasInteractivity: dashboardLimits.planLimits.realTime,
      hasExport: dashboardLimits.planLimits.export,
      hasAnalytics: dashboardLimits.planLimits.analytics,
      hasRealTime: dashboardLimits.planLimits.realTime,
      hasCustomization: dashboardLimits.planLimits.customization,
      maxWidgets: dashboardLimits.planLimits.widgets,
      maxMetrics: dashboardLimits.planLimits.metrics,
      availableDashboardTypes: dashboardLimits.planLimits.dashboardTypes,
      availableFeatures: dashboardLimits.planLimits.features,
      refreshInterval: dashboardLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateDashboardFeatures]);

  /**
   * Enhanced dashboard type validation - Production Ready
   */
  const isDashboardTypeAvailable = useCallback((type) => {
    return subscriptionFeatures.availableDashboardTypes.includes(type);
  }, [subscriptionFeatures.availableDashboardTypes]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `${title} production dashboard card`,
      'aria-description': ariaDescription || `Interactive ${dashboardType} dashboard card displaying production metrics`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, title, dashboardType, realTimeUpdates]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        lastUpdated: new Date().toISOString(),
        animationKey: prev.animationKey + 1,
        retryCount: 0
      }));

      showSuccessNotification('Dashboard data refreshed successfully');
      announceToScreenReader('Production dashboard has been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message },
        retryCount: prev.retryCount + 1
      }));

      showErrorNotification(`Failed to refresh dashboard: ${error.message}`);
      announceToScreenReader('Failed to refresh dashboard data');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced dashboard type change handler - Production Ready
   */
  const handleDashboardTypeChange = useCallback((newType) => {
    if (!isDashboardTypeAvailable(newType)) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, currentDashboardType: newType }));

    if (onDashboardTypeChange) {
      onDashboardTypeChange(newType);
    }

    announceToScreenReader(`Dashboard type changed to ${newType}`);
  }, [isDashboardTypeAvailable, onDashboardTypeChange, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'png') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, productionData.processed);
      }

      showSuccessNotification(`Dashboard exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Dashboard has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export dashboard: ${error.message}`);
      announceToScreenReader('Failed to export dashboard');
    }
  }, [subscriptionFeatures.hasExport, productionData.processed, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced visibility toggle handler - Production Ready
   */
  const handleToggleVisibility = useCallback(() => {
    setState(prev => ({ ...prev, isVisible: !prev.isVisible }));
    announceToScreenReader(`Dashboard card ${state.isVisible ? 'hidden' : 'shown'}`);
  }, [state.isVisible, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportCard: handleExport,
    changeDashboardType: handleDashboardTypeChange,
    toggleVisibility: handleToggleVisibility,
    getProductionData: () => productionData.processed,
    getDashboardLimits: validateDashboardFeatures,
    focus: () => setFocusToElement(document.querySelector(`[data-testid="${testId}"]`)),
    announce: (message) => announceToScreenReader(message)
  }), [
    productionData.processed,
    validateDashboardFeatures,
    handleRefresh,
    handleExport,
    handleDashboardTypeChange,
    handleToggleVisibility,
    setFocusToElement,
    announceToScreenReader,
    testId
  ]);

  /**
   * Enhanced data processing effect - Production Ready
   */
  useEffect(() => {
    if (productionMetrics) {
      setProductionData(prev => ({
        ...prev,
        raw: productionMetrics,
        processed: productionMetrics
      }));
    }
  }, [productionMetrics]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced card styling with ACE Social branding - Production Ready
   */
  const getCardStyles = useCallback(() => {
    const baseStyles = {
      minHeight,
      maxHeight,
      display: 'flex',
      flexDirection: 'column',
      position: 'relative',
      overflow: 'hidden',
      transition: prefersReducedMotion ? 'none' : 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      borderRadius: theme.spacing(2),
      margin: theme.spacing(1),

      // Enhanced WCAG 2.1 AA compliance
      '&:focus-within': {
        outline: `3px solid ${ACE_COLORS.PURPLE}`,
        outlineOffset: '2px'
      },

      // Enhanced responsive sizing
      width: {
        xs: '100%',
        sm: 'calc(50% - 16px)',
        md: 'calc(33.333% - 16px)',
        lg: 'calc(25% - 16px)'
      },

      // ACE Social brand integration
      ...customization
    };

    switch (variant) {
      case 'glass':
        return {
          ...baseStyles,
          background: `linear-gradient(135deg,
            ${alpha(ACE_COLORS.WHITE, 0.9)} 0%,
            ${alpha(ACE_COLORS.WHITE, 0.7)} 100%)`,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          boxShadow: `
            0 8px 32px ${alpha(ACE_COLORS.DARK, 0.1)},
            inset 0 1px 0 ${alpha(ACE_COLORS.WHITE, 0.3)}
          `,
          '&:hover': !prefersReducedMotion ? {
            transform: 'translateY(-2px)',
            boxShadow: `
              0 12px 40px ${alpha(ACE_COLORS.DARK, 0.15)},
              inset 0 1px 0 ${alpha(ACE_COLORS.WHITE, 0.4)}
            `
          } : {}
        };

      case 'solid':
        return {
          ...baseStyles,
          backgroundColor: ACE_COLORS.WHITE,
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
          boxShadow: `0 4px 20px ${alpha(ACE_COLORS.DARK, 0.08)}`,
          '&:hover': !prefersReducedMotion ? {
            transform: 'translateY(-1px)',
            boxShadow: `0 8px 30px ${alpha(ACE_COLORS.DARK, 0.12)}`
          } : {}
        };

      case 'outlined':
        return {
          ...baseStyles,
          backgroundColor: 'transparent',
          border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`,
          '&:hover': !prefersReducedMotion ? {
            borderColor: ACE_COLORS.PURPLE,
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.04)
          } : {}
        };

      default:
        return baseStyles;
    }
  }, [minHeight, maxHeight, theme, prefersReducedMotion, variant, customization]);

  /**
   * Enhanced priority-based styling with ACE Social colors - Production Ready
   */
  const getPriorityStyles = useCallback(() => {
    switch (priority) {
      case 'critical':
        return {
          borderLeft: `4px solid #F44336`,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px',
            background: `linear-gradient(90deg, #F44336, #FF9800)`,
            zIndex: 1
          }
        };
      case 'high':
        return {
          borderLeft: `4px solid #FF9800`,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '2px',
            background: `linear-gradient(90deg, #FF9800, ${ACE_COLORS.YELLOW})`,
            zIndex: 1
          }
        };
      case 'low':
        return {
          opacity: 0.85,
          borderLeft: `2px solid ${alpha('#4CAF50', 0.5)}`
        };
      default:
        return {
          borderLeft: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
        };
    }
  }, [priority]);

  /**
   * Enhanced retry handler - Production Ready
   */
  const handleRetry = useCallback(() => {
    setState(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1,
      errors: {}
    }));

    if (onRefresh) {
      handleRefresh();
    }
  }, [onRefresh, handleRefresh]);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced production dashboard features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Advanced dashboard types',
        'Real-time monitoring',
        'Production analytics',
        'Data export capabilities',
        'Custom dashboard layouts',
        'AI-powered insights'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  /**
   * Enhanced loading skeleton - Production Ready
   */
  const renderSkeleton = useCallback(() => (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <Skeleton variant="circular" width={40} height={40} />
        <Box sx={{ flex: 1 }}>
          <Skeleton variant="text" width="60%" height={24} sx={{ mb: 0.5 }} />
          <Skeleton variant="text" width="40%" height={16} />
        </Box>
      </Box>
      <Skeleton variant="rectangular" height={isMobile ? 100 : 120} sx={{ borderRadius: 2, mb: 2 }} />
      <Box sx={{ display: 'flex', gap: 1, justifyContent: 'space-between' }}>
        <Skeleton variant="text" width="25%" height={16} />
        <Skeleton variant="text" width="25%" height={16} />
        <Skeleton variant="text" width="25%" height={16} />
      </Box>
    </Box>
  ), [isMobile]);

  /**
   * Enhanced error state - Production Ready
   */
  const renderError = useCallback(() => (
    <Box sx={{
      p: 3,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 150,
      textAlign: 'center'
    }}>
      <ErrorIcon
        sx={{
          fontSize: isMobile ? 40 : 48,
          color: '#F44336',
          mb: 2
        }}
      />
      <Typography variant={isMobile ? "subtitle1" : "h6"} sx={{ color: '#F44336', fontWeight: 600, mb: 1 }}>
        Failed to Load Dashboard
      </Typography>
      <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 2, maxWidth: 300 }}>
        {error?.message || Object.values(state.errors)[0] || 'An unexpected error occurred while loading the dashboard'}
      </Typography>
      {correlationId && (
        <Typography variant="caption" sx={{ color: alpha(ACE_COLORS.DARK, 0.6), mb: 2 }}>
          Error ID: {correlationId}
        </Typography>
      )}
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', justifyContent: 'center' }}>
        <Button
          variant="outlined"
          size="small"
          startIcon={state.refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
          onClick={handleRetry}
          disabled={state.refreshing}
          sx={{
            borderColor: ACE_COLORS.PURPLE,
            color: ACE_COLORS.PURPLE,
            '&:hover': {
              borderColor: ACE_COLORS.PURPLE,
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
            }
          }}
        >
          Retry ({state.retryCount})
        </Button>
        {subscriptionFeatures.hasAnalytics && (
          <Button
            variant="text"
            size="small"
            onClick={() => setState(prev => ({ ...prev, showAnalyticsPanel: true }))}
            sx={{ color: ACE_COLORS.DARK }}
          >
            View Details
          </Button>
        )}
      </Box>
    </Box>
  ), [error, correlationId, state.errors, state.refreshing, state.retryCount, handleRetry, isMobile, subscriptionFeatures.hasAnalytics]);

  /**
   * Enhanced empty state with context-aware messaging - Production Ready
   */
  const renderEmpty = useCallback(() => {
    // If alwaysShowCard is false, use the simple empty state
    if (!alwaysShowCard) {
      return (
        <Box sx={{
          p: 3,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 150,
          textAlign: 'center'
        }}>
          <DashboardIcon
            sx={{
              fontSize: isMobile ? 40 : 48,
              color: alpha(ACE_COLORS.PURPLE, 0.4),
              mb: 2
            }}
          />
          <Typography variant={isMobile ? "subtitle1" : "h6"} sx={{ color: ACE_COLORS.DARK, fontWeight: 600, mb: 1 }}>
            No Dashboard Data
          </Typography>
          <Typography variant="body2" color="text.secondary" align="center" sx={{ maxWidth: 300 }}>
            Dashboard data will appear here once available
          </Typography>
        </Box>
      );
    }

    // Use enhanced empty state handler
    return (
      <EmptyStateHandler
        type={emptyStateType}
        customConfig={emptyStateConfig}
        onCtaClick={onEmptyStateAction}
        onSecondaryCtaClick={onEmptyStateSecondaryAction}
        showProgress={showEmptyStateProgress}
        minHeight={Math.max(minHeight - 120, 180)}
        variant={emptyStateVariant}
      />
    );
  }, [
    alwaysShowCard,
    isMobile,
    emptyStateType,
    emptyStateConfig,
    onEmptyStateAction,
    onEmptyStateSecondaryAction,
    showEmptyStateProgress,
    minHeight,
    emptyStateVariant
  ]);

  // Main render condition checks
  if (state.loading && !children) {
    return (
      <ErrorBoundary
        fallback={
          <Card sx={{ minHeight, p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load production dashboard card
            </Typography>
          </Card>
        }
      >
        <Fade in timeout={300}>
          <Card
            className={className}
            style={style}
            data-testid={testId}
            {...getAccessibilityProps()}
            sx={{
              ...getCardStyles(),
              ...getPriorityStyles()
            }}
          >
            <CardHeader
              title={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {icon && (
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: '50%',
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        color: ACE_COLORS.PURPLE,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      {icon}
                    </Box>
                  )}
                  <Typography
                    variant={isMobile ? "subtitle1" : "h6"}
                    component="h2"
                    sx={{
                      fontWeight: 600,
                      color: ACE_COLORS.DARK
                    }}
                  >
                    {title}
                  </Typography>
                </Box>
              }
              subheader={subtitle && (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mt: 0.5, ml: icon ? 6 : 0 }}
                >
                  {subtitle}
                </Typography>
              )}
            />
            <CardContent>
              {renderSkeleton()}
            </CardContent>
          </Card>
        </Fade>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Card sx={{ minHeight, p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="error">
            Unable to load production dashboard card
          </Typography>
        </Card>
      }
    >
      <Fade in timeout={300}>
        <Card
          className={className}
          style={style}
          data-testid={testId}
          {...getAccessibilityProps()}
          sx={{
            ...getCardStyles(),
            ...getPriorityStyles()
          }}
        >
          {/* Enhanced Card Header with Production Dashboard Features */}
          <CardHeader
            avatar={icon && (
              <Box
                sx={{
                  p: 1,
                  borderRadius: '50%',
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {icon}
              </Box>
            )}
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography
                  variant={isMobile ? "subtitle1" : "h6"}
                  component="h2"
                  sx={{
                    fontWeight: 600,
                    color: ACE_COLORS.DARK
                  }}
                >
                  {title}
                </Typography>
                {subscriptionFeatures.hasAnalytics && productionData.processed && (
                  <Chip
                    label="LIVE"
                    size="small"
                    sx={{
                      backgroundColor: alpha('#4CAF50', 0.1),
                      color: '#4CAF50',
                      fontWeight: 600,
                      fontSize: '0.7rem'
                    }}
                  />
                )}
              </Box>
            }
            subheader={subtitle && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mt: 0.5 }}
              >
                {subtitle}
              </Typography>
            )}
            action={
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                {tooltipContent && (
                  <Tooltip title={tooltipContent} arrow>
                    <IconButton size="small" aria-label="Information">
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                {/* Dashboard Type Selector */}
                {subscriptionFeatures.hasAnalytics && (
                  <Tooltip title="Dashboard Settings">
                    <IconButton
                      size="small"
                      onClick={settingsAnchorEl ? () => setSettingsAnchorEl(null) : (e) => setSettingsAnchorEl(e.currentTarget)}
                      sx={{
                        color: ACE_COLORS.PURPLE,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                        }
                      }}
                      aria-label="Change dashboard settings"
                    >
                      <SettingsIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                {/* Export Button */}
                {enableExport && (
                  <Tooltip title="Export Dashboard">
                    <IconButton
                      size="small"
                      onClick={handleExportMenuOpen}
                      sx={{
                        color: theme.palette.text.secondary,
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.1)
                        }
                      }}
                      aria-label="Export dashboard data"
                    >
                      <ExportIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                <Tooltip title={state.isVisible ? "Hide card" : "Show card"} arrow>
                  <IconButton
                    size="small"
                    onClick={handleToggleVisibility}
                    aria-label={state.isVisible ? "Hide card" : "Show card"}
                  >
                    {state.isVisible ? <VisibilityIcon fontSize="small" /> : <VisibilityOffIcon fontSize="small" />}
                  </IconButton>
                </Tooltip>

                {showRefreshButton && onRefresh && (
                  <Tooltip title="Refresh data" arrow>
                    <IconButton
                      size="small"
                      onClick={handleRefresh}
                      disabled={state.refreshing}
                      aria-label="Refresh data"
                    >
                      {state.refreshing ? (
                        <CircularProgress size={16} />
                      ) : (
                        <RefreshIcon fontSize="small" />
                      )}
                    </IconButton>
                  </Tooltip>
                )}

                {showExpandButton && onExpand && (
                  <Tooltip title="Expand view" arrow>
                    <IconButton
                      size="small"
                      onClick={onExpand}
                      aria-label="Expand view"
                    >
                      <ExpandIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                {headerAction}
              </Box>
            }
            sx={{
              pb: 1,
              '& .MuiCardHeader-content': {
                overflow: 'hidden'
              }
            }}
          />

          {/* Real-time Updates Indicator */}
          {realTimeUpdates && subscriptionFeatures.hasRealTime && (
            <Box sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              p: 1,
              backgroundColor: alpha(theme.palette.success.main, 0.1),
              borderRadius: 1,
              border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
              zIndex: 10
            }}>
              <Box
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: theme.palette.success.main,
                  ...(!prefersReducedMotion && {
                    animation: 'pulse 2s infinite',
                    '@keyframes pulse': {
                      '0%': { opacity: 1 },
                      '50%': { opacity: 0.5 },
                      '100%': { opacity: 1 }
                    }
                  })
                }}
                aria-hidden="true"
              />
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                Live
              </Typography>
            </Box>
          )}

          {state.isVisible && (
            <CardContent
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                pt: 0,
                '&:last-child': {
                  pb: 2
                }
              }}
            >
              {state.loading ? renderSkeleton() :
               error || Object.keys(state.errors).length > 0 ? renderError() :
               isEmpty || (!children && alwaysShowCard) ? renderEmpty() :
               children}

              {state.lastUpdated && !state.loading && !error && !isEmpty && children && (
                <Typography
                  variant="caption"
                  color="text.disabled"
                  sx={{
                    mt: 'auto',
                    pt: 1,
                    textAlign: 'right'
                  }}
                >
                  Updated: {new Date(state.lastUpdated).toLocaleTimeString()}
                </Typography>
              )}
            </CardContent>
          )}

          {/* Dashboard Type Menu */}
          <Menu
            anchorEl={settingsAnchorEl}
            open={Boolean(settingsAnchorEl)}
            onClose={() => setSettingsAnchorEl(null)}
            slotProps={{
              paper: {
                sx: {
                  mt: 1,
                  minWidth: 250,
                  boxShadow: theme.shadows[8],
                  border: `1px solid ${theme.palette.divider}`
                }
              }
            }}
          >
            {Object.values(DASHBOARD_TYPES).map((dashboardTypeConfig) => (
              <MenuItem
                key={dashboardTypeConfig.id}
                onClick={() => {
                  handleDashboardTypeChange(dashboardTypeConfig.id);
                  setSettingsAnchorEl(null);
                }}
                disabled={!isDashboardTypeAvailable(dashboardTypeConfig.id)}
                sx={{
                  backgroundColor: state.currentDashboardType === dashboardTypeConfig.id
                    ? alpha(ACE_COLORS.PURPLE, 0.1)
                    : 'transparent'
                }}
              >
                <ListItemIcon>
                  <dashboardTypeConfig.icon fontSize="small" sx={{ color: dashboardTypeConfig.color }} />
                </ListItemIcon>
                <ListItemText
                  primary={dashboardTypeConfig.name}
                  secondary={dashboardTypeConfig.description}
                />
                {!isDashboardTypeAvailable(dashboardTypeConfig.id) && (
                  <UpgradeIcon fontSize="small" sx={{ color: theme.palette.text.disabled, ml: 1 }} />
                )}
              </MenuItem>
            ))}
          </Menu>

          {/* Export Menu */}
          <Menu
            anchorEl={exportAnchorEl}
            open={state.showExportMenu}
            onClose={handleExportMenuClose}
            slotProps={{
              paper: {
                sx: {
                  mt: 1,
                  minWidth: 150,
                  boxShadow: theme.shadows[8],
                  border: `1px solid ${theme.palette.divider}`
                }
              }
            }}
          >
            <MenuItem onClick={() => {
              handleExport('png');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export as PNG</ListItemText>
            </MenuItem>

            <MenuItem onClick={() => {
              handleExport('pdf');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export Report (PDF)</ListItemText>
            </MenuItem>
          </Menu>

          {/* Upgrade Dialog */}
          <Dialog
            open={state.showUpgradeDialog}
            onClose={handleUpgradeDialogClose}
            maxWidth="md"
            fullWidth
            slotProps={{
              paper: {
                sx: {
                  borderRadius: 2,
                  boxShadow: theme.shadows[16]
                }
              }
            }}
          >
            <DialogTitle sx={{
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
              borderBottom: `1px solid ${theme.palette.divider}`
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
                <Typography variant="h6" component="span">
                  {getUpgradeDialogContent().title}
                </Typography>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ pt: 3 }}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {getUpgradeDialogContent().content}
                </Typography>

                <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                  Unlock Advanced Dashboard Features:
                </Typography>

                <Grid container spacing={1}>
                  {getUpgradeDialogContent().features.map((feature, index) => (
                    <Grid item xs={12} sm={6} key={index}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                        <Typography variant="body2">{feature}</Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Box>

              <Alert
                severity="info"
                sx={{
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                  border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
                }}
              >
                <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
                Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced dashboard features.
              </Alert>
            </DialogContent>
            <DialogActions sx={{ p: 3, pt: 2 }}>
              <Button
                onClick={handleUpgradeDialogClose}
                sx={{ color: theme.palette.text.secondary }}
              >
                Maybe Later
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: ACE_COLORS.PURPLE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                  }
                }}
                startIcon={<UpgradeIcon />}
              >
                Upgrade Now
              </Button>
            </DialogActions>
          </Dialog>
        </Card>
      </Fade>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
ProductionDashboardCard.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string,
  children: PropTypes.node,
  icon: PropTypes.node,
  onRefresh: PropTypes.func,
  onExpand: PropTypes.func,
  loading: PropTypes.bool,
  error: PropTypes.object,
  isEmpty: PropTypes.bool,
  minHeight: PropTypes.number,
  maxHeight: PropTypes.number,
  tooltipContent: PropTypes.string,
  headerAction: PropTypes.node,
  showRefreshButton: PropTypes.bool,
  showExpandButton: PropTypes.bool,
  refreshing: PropTypes.bool,
  correlationId: PropTypes.string,
  lastUpdated: PropTypes.string,
  priority: PropTypes.oneOf(['critical', 'high', 'normal', 'low']),
  variant: PropTypes.oneOf(['glass', 'solid', 'outlined']),

  // Enhanced production dashboard props
  dashboardType: PropTypes.oneOf(['production-overview', 'performance-monitoring', 'system-health', 'deployment-tracking', 'analytics-summary']),
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onDashboardTypeChange: PropTypes.func,
  onExport: PropTypes.func,
  productionMetrics: PropTypes.object,
  customization: PropTypes.object,

  // Enhanced empty state props
  emptyStateType: PropTypes.oneOf([
    'social-media-analytics',
    'content-performance',
    'audience-demographics',
    'image-analytics',
    'engagement-metrics',
    'optimal-times',
    'sentiment-analysis',
    'competitor-insights',
    'quick-actions'
  ]),
  emptyStateConfig: PropTypes.object,
  onEmptyStateAction: PropTypes.func,
  onEmptyStateSecondaryAction: PropTypes.func,
  showEmptyStateProgress: PropTypes.bool,
  alwaysShowCard: PropTypes.bool,
  emptyStateVariant: PropTypes.oneOf(['default', 'compact', 'detailed']),

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

ProductionDashboardCard.defaultProps = {
  loading: false,
  error: null,
  isEmpty: false,
  minHeight: 200,
  showRefreshButton: true,
  showExpandButton: true,
  refreshing: false,
  priority: 'normal',
  variant: 'glass',
  dashboardType: 'production-overview',

  enableExport: false,
  realTimeUpdates: false,
  customization: {},
  emptyStateType: 'content-performance',
  showEmptyStateProgress: true,
  alwaysShowCard: true,
  emptyStateVariant: 'default',
  className: '',
  style: {},
  testId: 'production-dashboard-card'
};

// Display name for debugging
ProductionDashboardCard.displayName = 'ProductionDashboardCard';

export default ProductionDashboardCard;

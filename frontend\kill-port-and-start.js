#!/usr/bin/env node
// @since 2024-1-1 to 2025-25-7

/**
 * Kill processes on port 3000 and start Vite dev server
 * This script helps resolve port conflicts when starting the development server
 */

import { exec, spawn } from 'child_process';
import os from 'os';

const PORT = 3000;
const FALLBACK_PORT = 3001;

console.log('🔍 Checking for processes using port', PORT);

function killProcessOnPort(port) {
  return new Promise((resolve) => {
    const isWindows = os.platform() === 'win32';
    
    if (isWindows) {
      // Windows: Find and kill process using the port
      exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
        if (error || !stdout) {
          console.log(`✅ Port ${port} is free`);
          resolve();
          return;
        }

        const lines = stdout.trim().split('\n');
        const pids = new Set();
        
        lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 5) {
            const pid = parts[parts.length - 1];
            if (pid && pid !== '0') {
              pids.add(pid);
            }
          }
        });

        if (pids.size === 0) {
          console.log(`✅ Port ${port} is free`);
          resolve();
          return;
        }

        console.log(`🔪 Killing ${pids.size} process(es) on port ${port}`);
        
        const killPromises = Array.from(pids).map(pid => {
          return new Promise((killResolve) => {
            exec(`taskkill /F /PID ${pid}`, (killError) => {
              if (killError) {
                console.log(`⚠️  Could not kill process ${pid}:`, killError.message);
              } else {
                console.log(`✅ Killed process ${pid}`);
              }
              killResolve();
            });
          });
        });

        Promise.all(killPromises).then(() => {
          setTimeout(resolve, 1000); // Wait 1 second after killing
        });
      });
    } else {
      // Unix/Linux/macOS
      exec(`lsof -ti:${port}`, (error, stdout) => {
        if (error || !stdout) {
          console.log(`✅ Port ${port} is free`);
          resolve();
          return;
        }

        const pids = stdout.trim().split('\n').filter(pid => pid);
        if (pids.length === 0) {
          console.log(`✅ Port ${port} is free`);
          resolve();
          return;
        }

        console.log(`🔪 Killing ${pids.length} process(es) on port ${port}`);
        exec(`kill -9 ${pids.join(' ')}`, (killError) => {
          if (killError) {
            console.log('⚠️  Could not kill some processes:', killError.message);
          } else {
            console.log('✅ Killed processes');
          }
          setTimeout(resolve, 1000); // Wait 1 second after killing
        });
      });
    }
  });
}

function startViteServer() {
  console.log('🚀 Starting Vite development server...');
  
  const viteProcess = spawn('npx', ['vite', '--host'], {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd()
  });

  viteProcess.on('error', (error) => {
    console.error('❌ Failed to start Vite server:', error.message);
    
    // Try fallback port
    console.log(`🔄 Trying fallback port ${FALLBACK_PORT}...`);
    const fallbackProcess = spawn('npx', ['vite', '--host', '--port', FALLBACK_PORT], {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd()
    });

    fallbackProcess.on('error', (fallbackError) => {
      console.error('❌ Failed to start Vite server on fallback port:', fallbackError.message);
      process.exit(1);
    });
  });

  // Handle Ctrl+C gracefully
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Vite server...');
    viteProcess.kill('SIGINT');
    process.exit(0);
  });
}

async function main() {
  try {
    await killProcessOnPort(PORT);
    await killProcessOnPort(FALLBACK_PORT);
    
    console.log('⏳ Waiting a moment for ports to be released...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    startViteServer();
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

main();

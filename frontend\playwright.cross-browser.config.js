// @since 2024-1-1 to 2025-25-7
import { defineConfig, devices } from '@playwright/test';

/**
 * Cross-browser compatibility testing configuration
 * Tests across Chrome, Firefox, Safari, and Edge
 */
export default defineConfig({
  testDir: './tests/cross-browser',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'test-results/cross-browser' }],
    ['json', { outputFile: 'test-results/cross-browser-results.json' }]
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },

  projects: [
    {
      name: 'Chrome Desktop',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'Firefox Desktop',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'Safari Desktop',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Edge Desktop',
      use: { 
        ...devices['Desktop Chrome'],
        channel: 'msedge'
      },
    },
    {
      name: 'Chrome Mobile',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Safari Mobile',
      use: { ...devices['iPhone 12'] },
    },
  ],

  webServer: {
    command: 'npm run preview',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
});

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  Paper,
  Tooltip,
  Alert,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  CalendarToday as CalendarIcon,
  Assessment as AssessmentIcon,
  Edit as EditIcon,
  AccountCircle as AccountCircleIcon,
  CreditCard as CreditCardIcon,
  Info as InfoIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { useNotificationSystem } from '../../hooks/useNotificationSystem';
import PageHeader from '../../components/common/PageHeader';

const NotificationsPage = () => {
  const navigate = useNavigate();
  const {
    notifications,
    unreadCount,
    loading,
    error,
    pushSupported,
    pushEnabled,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    enablePushNotifications,
    disablePushNotifications,
  } = useNotificationSystem();

  const [tabValue, setTabValue] = useState(0);
  const [pushLoading, setPushLoading] = useState(false);

  // Load notifications on mount
  useEffect(() => {
    loadNotifications(tabValue === 1);
  }, [tabValue, loadNotifications]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleNotificationClick = async (notification) => {
    // Mark as read
    await markAsRead(notification.id);

    // Navigate based on notification type
    switch (notification.notification_type) {
      case 'content_review':
        navigate(`/content/${notification.entity_id}`);
        break;
      case 'content_scheduled':
        navigate(`/content/${notification.entity_id}`);
        break;
      case 'content_published':
        navigate(`/content/${notification.entity_id}`);
        break;
      case 'calendar_invitation':
        navigate(`/shared-calendar/${notification.entity_id}`);
        break;
      case 'analytics_report':
        navigate('/dashboard');
        break;
      case 'account':
        navigate('/profile');
        break;
      case 'billing':
        navigate('/billing');
        break;
      default:
        // Just mark as read but don't navigate
        break;
    }
  };

  const handleDeleteNotification = async (e, notificationId) => {
    e.stopPropagation();
    await deleteNotification(notificationId);
    // Refresh the list
    loadNotifications(tabValue === 1);
  };

  const handleTogglePushNotifications = async () => {
    setPushLoading(true);

    try {
      if (pushEnabled) {
        await disablePushNotifications();
      } else {
        await enablePushNotifications();
      }
    } finally {
      setPushLoading(false);
    }
  };

  // Get icon based on notification type
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'content_review':
      case 'content_scheduled':
      case 'content_published':
      case 'content_failed':
        return <EditIcon color="primary" />;
      case 'calendar_invitation':
        return <CalendarIcon color="primary" />;
      case 'analytics_report':
        return <AssessmentIcon color="primary" />;
      case 'account':
        return <AccountCircleIcon color="primary" />;
      case 'billing':
        return <CreditCardIcon color="primary" />;
      default:
        return <InfoIcon color="primary" />;
    }
  };

  return (
    <Container maxWidth="lg">
      <PageHeader
        title="Notifications"
        subtitle="View and manage your notifications"
      />

      {pushSupported && (
        <Paper sx={{ p: 2, mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {pushEnabled ? (
              <NotificationsActiveIcon color="primary" sx={{ mr: 2 }} />
            ) : (
              <NotificationsOffIcon color="action" sx={{ mr: 2 }} />
            )}
            <Box>
              <Typography variant="h6">
                {pushEnabled ? 'Push notifications are enabled' : 'Enable push notifications'}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {pushEnabled
                  ? 'You will receive notifications even when the app is closed'
                  : 'Get notified about important updates even when the app is closed'}
              </Typography>
            </Box>
          </Box>
          <Button
            variant={pushEnabled ? "outlined" : "contained"}
            color={pushEnabled ? "error" : "primary"}
            onClick={handleTogglePushNotifications}
            disabled={pushLoading}
            startIcon={pushLoading && <CircularProgress size={16} />}
          >
            {pushEnabled ? 'Disable' : 'Enable'}
          </Button>
        </Paper>
      )}

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="notification tabs"
            sx={{ px: 2 }}
          >
            <Tab label="All Notifications" />
            <Tab label={`Unread (${unreadCount})`} />
          </Tabs>
        </Box>

        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
          {unreadCount > 0 && (
            <Button
              size="small"
              onClick={markAllAsRead}
              startIcon={<CheckCircleIcon />}
            >
              Mark all as read
            </Button>
          )}
        </Box>

        <Divider />

        <CardContent sx={{ p: 0 }}>
          {loading ? (
            <Box sx={{ p: 4, display: 'flex', justifyContent: 'center' }}>
              <CircularProgress size={40} />
            </Box>
          ) : error ? (
            <Box sx={{ p: 2 }}>
              <Alert severity="error">{error}</Alert>
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button
                  variant="outlined"
                  onClick={() => loadNotifications(tabValue === 1)}
                >
                  Retry
                </Button>
              </Box>
            </Box>
          ) : notifications.length === 0 ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography color="textSecondary">
                {tabValue === 0
                  ? 'No notifications yet'
                  : 'No unread notifications'}
              </Typography>
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {notifications.map((notification) => (
                <Box key={notification.id}>
                  <ListItem
                    button
                    onClick={() => handleNotificationClick(notification)}
                    sx={{
                      bgcolor: notification.is_read ? 'transparent' : 'action.hover',
                      borderLeft: notification.is_read ? 'none' : '4px solid',
                      borderLeftColor: 'primary.main',
                      pl: notification.is_read ? 2 : 1.5,
                      py: 2,
                    }}
                  >
                    <ListItemIcon>
                      {getNotificationIcon(notification.notification_type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" fontWeight={notification.is_read ? 'normal' : 'bold'}>
                          {notification.title}
                        </Typography>
                      }
                      secondary={
                        <>
                          <Typography
                            component="span"
                            variant="body2"
                            color="textPrimary"
                            sx={{ display: 'block', mb: 0.5 }}
                          >
                            {notification.message}
                          </Typography>
                          <Typography
                            component="span"
                            variant="caption"
                            color="textSecondary"
                          >
                            {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                          </Typography>
                        </>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Tooltip title="Delete notification">
                        <IconButton
                          edge="end"
                          onClick={(e) => handleDeleteNotification(e, notification.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </ListItemSecondaryAction>
                  </ListItem>
                  <Divider component="li" />
                </Box>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </Container>
  );
};

export default NotificationsPage;

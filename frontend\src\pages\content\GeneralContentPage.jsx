// @since 2024-1-1 to 2025-25-7
import { useState } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  Tabs,
  Tab,
  Divider,
  Alert,
  AlertT<PERSON>le,
  Button,
  Grid,
} from '@mui/material';
import { CustomCard, CustomCardContent, CustomCardMedia, CustomCardActions } from '../../components/common';
import {
  TextFields as TextFieldsIcon,
  Description as DescriptionIcon,
  Lightbulb as LightbulbIcon,
  Help as HelpIcon,
} from '@mui/icons-material';
import GeneralContentGenerator from '../../components/content/GeneralContentGenerator';
import { useAuth } from '../../hooks/useAuth';

const GeneralContentPage = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 8 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          <TextFieldsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          General Content Generator
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Create content directly by entering your text and optionally manipulating images. No services or ICP required.
        </Typography>
      </Box>

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Content Generator" />
          <Tab label="Help & Tips" />
          <Tab label="Examples" />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {tabValue === 0 && (
            <GeneralContentGenerator />
          )}

          {tabValue === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                <HelpIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Help & Tips
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Alert severity="info" sx={{ mb: 3 }}>
                <AlertTitle>How It Works</AlertTitle>
                This general content generator allows you to create content directly without defining services or ICPs.
                Simply enter your text, optionally add images for manipulation, and apply branding if desired.
              </Alert>

              <Typography variant="subtitle1" gutterBottom>
                Tips for Better Results
              </Typography>

              <Box component="ul" sx={{ pl: 2 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Typography variant="body2">
                    <strong>Be specific with your text</strong> - The more detailed your text content, the better the results.
                  </Typography>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Typography variant="body2">
                    <strong>Provide clear image manipulation instructions</strong> - Describe exactly how you want your source images to be transformed.
                  </Typography>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Typography variant="body2">
                    <strong>Use custom headlines</strong> - For better image-text synchronization, provide a custom headline to overlay on your images.
                  </Typography>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Typography variant="body2">
                    <strong>Apply branding for consistency</strong> - Enable branding to maintain your visual identity across all content.
                  </Typography>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Typography variant="body2">
                    <strong>Choose the right platform</strong> - Select a specific platform to optimize your content for that platform's requirements.
                  </Typography>
                </Box>
              </Box>

              <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
                Feature Highlights
              </Typography>

              <Grid container spacing={3} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6} md={4}>
                  <CustomCard variant="glass">
                    <CustomCardContent>
                      <Typography variant="h6" gutterBottom>
                        Direct Text Input
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Enter your text content directly without going through the service and ICP definition process.
                      </Typography>
                    </CustomCardContent>
                  </CustomCard>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <CustomCard variant="glass">
                    <CustomCardContent>
                      <Typography variant="h6" gutterBottom>
                        Image Manipulation
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Upload multiple source images and provide instructions for how they should be manipulated.
                      </Typography>
                    </CustomCardContent>
                  </CustomCard>
                </Grid>

                <Grid item xs={12} sm={6} md={4}>
                  <CustomCard variant="glass">
                    <CustomCardContent>
                      <Typography variant="h6" gutterBottom>
                        Branding Integration
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Apply your brand settings to ensure visual consistency across all your generated content.
                      </Typography>
                    </CustomCardContent>
                  </CustomCard>
                </Grid>
              </Grid>
            </Box>
          )}

          {tabValue === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                <LightbulbIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Example Use Cases
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <CustomCard variant="glass">
                    <CustomCardMedia
                      component="img"
                      height="140"
                      image="https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                      alt="Product announcement"
                    />
                    <CustomCardContent>
                      <Typography variant="h6" gutterBottom>
                        Product Announcements
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Create compelling product announcements by entering your product details and uploading product images for manipulation.
                      </Typography>
                    </CustomCardContent>
                    <CustomCardActions>
                      <Button size="small" onClick={() => setTabValue(0)}>Try It</Button>
                    </CustomCardActions>
                  </CustomCard>
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomCard variant="glass">
                    <CustomCardMedia
                      component="img"
                      height="140"
                      image="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                      alt="Team updates"
                    />
                    <CustomCardContent>
                      <Typography variant="h6" gutterBottom>
                        Team Updates
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Share team news and achievements by creating content with team photos and applying your company branding.
                      </Typography>
                    </CustomCardContent>
                    <CustomCardActions>
                      <Button size="small" onClick={() => setTabValue(0)}>Try It</Button>
                    </CustomCardActions>
                  </CustomCard>
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomCard variant="glass">
                    <CustomCardMedia
                      component="img"
                      height="140"
                      image="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                      alt="Event promotion"
                    />
                    <CustomCardContent>
                      <Typography variant="h6" gutterBottom>
                        Event Promotion
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Create event announcements and promotional content with custom headlines overlaid on event imagery.
                      </Typography>
                    </CustomCardContent>
                    <CustomCardActions>
                      <Button size="small" onClick={() => setTabValue(0)}>Try It</Button>
                    </CustomCardActions>
                  </CustomCard>
                </Grid>
              </Grid>

              <Box sx={{ mt: 4 }}>
                <Alert severity="success">
                  <AlertTitle>Pro Tip</AlertTitle>
                  For the best results, try combining your own text with source images and clear manipulation instructions.
                  This gives you maximum control over the final output while still leveraging AI to enhance your content.
                </Alert>
              </Box>
            </Box>
          )}
        </Box>
      </Paper>
    </Container>
  );
};

export default GeneralContentPage;

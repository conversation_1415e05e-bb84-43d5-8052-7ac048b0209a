/**
 * Error Dashboard Component
 * Displays detailed error information for debugging
 @since 2024-1-1 to 2025-25-7
*/

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Button,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  BugReport as BugReportIcon,
  Refresh as RefreshIcon,
  ContentCopy as CopyIcon,
  Visibility as ViewIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import errorTracker from '../../utils/ErrorTracker';

const ErrorDashboard = () => {
  const [errorSummary, setErrorSummary] = useState(null);
  const [selectedError, setSelectedError] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    loadErrorSummary();
  }, [refreshKey]);

  const loadErrorSummary = () => {
    try {
      const summary = errorTracker.getErrorSummary();
      setErrorSummary(summary);
    } catch (error) {
      console.error('Failed to load error summary:', error);
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleViewError = (error) => {
    setSelectedError(error);
    setDialogOpen(true);
  };

  const handleCopyError = (error) => {
    const errorText = JSON.stringify(error, null, 2);
    navigator.clipboard.writeText(errorText).then(() => {
      console.log('Error details copied to clipboard');
    });
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'high': return <ErrorIcon />;
      case 'medium': return <WarningIcon />;
      case 'low': return <InfoIcon />;
      default: return <BugReportIcon />;
    }
  };

  if (!errorSummary) {
    return (
      <Box p={3}>
        <Typography variant="h6">Loading error dashboard...</Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          <BugReportIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Error Dashboard
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
        >
          Refresh
        </Button>
      </Box>

      {/* Error Summary */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Error Summary
        </Typography>
        <Box display="flex" gap={2} flexWrap="wrap">
          <Chip
            label={`Total Errors: ${errorSummary.totalErrors}`}
            color="primary"
            variant="outlined"
          />
          <Chip
            label={`Session: ${errorSummary.sessionId}`}
            color="secondary"
            variant="outlined"
          />
          <Chip
            label={`Duration: ${Math.round(errorSummary.sessionDuration / 1000)}s`}
            color="info"
            variant="outlined"
          />
        </Box>
      </Paper>

      {/* Error Counts */}
      {Object.keys(errorSummary.errorCounts).length > 0 && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Error Counts by Type
          </Typography>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Error Type</TableCell>
                  <TableCell align="right">Count</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Object.entries(errorSummary.errorCounts).map(([type, count]) => (
                  <TableRow key={type}>
                    <TableCell>{type}</TableCell>
                    <TableCell align="right">{count}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      {/* Recent Errors */}
      {errorSummary.recentErrors.length > 0 && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Recent Errors
          </Typography>
          {errorSummary.recentErrors.map((error, index) => (
            <Accordion key={index}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" gap={1} width="100%">
                  {getSeverityIcon(error.severity)}
                  <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                    {error.message || 'Unknown Error'}
                  </Typography>
                  <Chip
                    label={error.severity || 'unknown'}
                    color={getSeverityColor(error.severity)}
                    size="small"
                  />
                  <Chip
                    label={error.type || 'unknown'}
                    variant="outlined"
                    size="small"
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>File:</strong> {error.filename || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>Line:</strong> {error.lineno || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>Component:</strong> {error.component || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>Time:</strong> {new Date(error.timestamp).toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    <strong>Error ID:</strong> {error.errorId || 'Unknown'}
                  </Typography>
                  
                  <Box mt={2} display="flex" gap={1}>
                    <Button
                      size="small"
                      startIcon={<ViewIcon />}
                      onClick={() => handleViewError(error)}
                    >
                      View Details
                    </Button>
                    <Button
                      size="small"
                      startIcon={<CopyIcon />}
                      onClick={() => handleCopyError(error)}
                    >
                      Copy Error
                    </Button>
                  </Box>
                </Box>
              </AccordionDetails>
            </Accordion>
          ))}
        </Paper>
      )}

      {/* No Errors Message */}
      {errorSummary.totalErrors === 0 && (
        <Alert severity="success">
          <Typography variant="h6">No errors detected!</Typography>
          <Typography>Your application is running smoothly.</Typography>
        </Alert>
      )}

      {/* Error Detail Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Error Details
          {selectedError && (
            <Chip
              label={selectedError.severity || 'unknown'}
              color={getSeverityColor(selectedError.severity)}
              size="small"
              sx={{ ml: 2 }}
            />
          )}
        </DialogTitle>
        <DialogContent>
          {selectedError && (
            <TextField
              multiline
              rows={20}
              fullWidth
              value={JSON.stringify(selectedError, null, 2)}
              variant="outlined"
              InputProps={{
                readOnly: true,
                style: { fontFamily: 'monospace', fontSize: '12px' }
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => handleCopyError(selectedError)}>
            Copy to Clipboard
          </Button>
          <Button onClick={() => setDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ErrorDashboard;

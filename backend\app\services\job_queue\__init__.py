"""
Job Queue Services Package.
"""

from enum import Enum
from .job_manager import job_manager

class JobType(str, Enum):
    """Job type enumeration."""
    CONTENT_GENERATION = "content_generation"
    IMAGE_GENERATION = "image_generation"
    BULK_CONTENT_GENERATION = "bulk_content_generation"

class JobPriority(str, Enum):
    """Job priority enumeration."""
    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"

# Job queue initialization functions
async def initialize_job_queue():
    """Initialize the job queue system."""
    try:
        await job_manager.start_workers(num_workers=2)
        return True
    except Exception as e:
        print(f"Error initializing job queue: {e}")
        return False

async def shutdown_job_queue():
    """Shutdown the job queue system."""
    try:
        await job_manager.stop_workers()
        return True
    except Exception as e:
        print(f"Error shutting down job queue: {e}")
        return False

def get_job_queue():
    """Get the job queue manager instance."""
    return job_manager

__all__ = ["initialize_job_queue", "shutdown_job_queue", "get_job_queue", "job_manager", "JobType", "JobPriority"]

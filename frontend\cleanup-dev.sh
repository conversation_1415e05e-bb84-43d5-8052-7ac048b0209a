#!/bin/bash
# @since 2024-1-1 to 2025-25-7

echo "🧹 Cleaning up development environment..."

# Clear Vite cache
if [ -d "node_modules/.vite" ]; then
    echo "Clearing Vite cache..."
    rm -rf node_modules/.vite
    echo "✅ Cleared Vite cache"
else
    echo "ℹ️  Vite cache not found"
fi

# Clear build artifacts
if [ -d "dist" ]; then
    echo "Clearing build artifacts..."
    rm -rf dist
    echo "✅ Cleared build artifacts"
else
    echo "ℹ️  Build artifacts not found"
fi

# Clear npm cache
echo "Clearing npm cache..."
npm cache clean --force
echo "✅ Cleared npm cache"

# Install missing dependencies
echo "Installing missing dependencies..."
npm install cross-env rimraf
echo "✅ Dependencies installed"

echo ""
echo "🎉 Cleanup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Run: npm run dev:memory"
echo "2. If issues persist, try: npm run dev:minimal"
echo "3. For extreme cases, try: npm run clean:all"
echo ""

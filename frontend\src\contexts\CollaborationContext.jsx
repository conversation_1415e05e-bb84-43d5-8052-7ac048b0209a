/**
 * Collaboration Context
 * Provides real-time collaboration features with WebSocket support and fallback mode
 * Production-ready implementation with comprehensive error handling and logging
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './AuthContext';
import { useSnackbar } from './SnackbarContext';
import {
  createCollaborationSocket,
  getContentAnnotations,
  getImageAnnotations,
  createImageAnnotation
} from '../api/annotations';

// Configuration constants
const CONFIG = {
  // WebSocket settings
  RECONNECT_DELAY: 3000, // 3 seconds
  MAX_RECONNECT_ATTEMPTS: 5,
  HEARTBEAT_INTERVAL: 30000, // 30 seconds
  CONNECTION_TIMEOUT: 10000, // 10 seconds

  // Fallback mode settings
  FALLBACK_POLL_INTERVAL: 5000, // 5 seconds

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[Collaboration] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[Collaboration] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Collaboration Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[Collaboration] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Collaboration Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[Collaboration] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Collaboration Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const CollaborationContext = createContext();

// Custom hook to use the collaboration context
// eslint-disable-next-line react-refresh/only-export-components
export const useCollaboration = () => useContext(CollaborationContext);

// Collaboration provider component
export const CollaborationProvider = ({ children }) => {
  const { token } = useAuth(); // We only need the token for authentication
  const { showSuccessNotification, showErrorNotification } = useSnackbar();

  // State
  const [isConnected, setIsConnected] = useState(false);
  const [activeUsers, setActiveUsers] = useState([]);
  const [userCursors, setUserCursors] = useState({});
  const [annotations, setAnnotations] = useState([]);
  const [imageAnnotations, setImageAnnotations] = useState([]);
  const [sessionId, setSessionId] = useState(null);
  const [calendarToken, setCalendarToken] = useState(null);
  const [connectionState, setConnectionState] = useState('disconnected'); // 'disconnected', 'connecting', 'connected', 'reconnecting', 'fallback'
  const [fallbackMode, setFallbackMode] = useState(false);
  const [connectionErrors, setConnectionErrors] = useState(0);
  const [lastError, setLastError] = useState(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  // Refs
  const socketRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);

  // Fetch data in fallback mode
  const fetchDataInFallbackMode = useCallback(async (sessionId, calendarToken) => {
    try {
      logger.debug('Fetching data in fallback mode', { sessionId, calendarToken });

      // Fetch annotations if we have content ID
      if (sessionId && sessionId.includes('_')) {
        const contentId = sessionId.split('_')[1];

        // Fetch annotations
        const annotationsData = await getContentAnnotations(contentId, calendarToken);
        setAnnotations(annotationsData || []);
        logger.debug('Fetched content annotations', { count: annotationsData?.length || 0 });

        // Fetch image annotations if this is an image session
        if (sessionId.includes('image')) {
          const imageId = sessionId.split('_')[3] || 'image-1';
          const imageAnnotationsData = await getImageAnnotations(contentId, imageId, calendarToken);
          setImageAnnotations(imageAnnotationsData || []);
          logger.debug('Fetched image annotations', { count: imageAnnotationsData?.length || 0 });
        }

        return true;
      }
      return false;
    } catch (error) {
      logger.error('Error fetching data in fallback mode', error);
      return false;
    }
  }, []);

  // Connect to collaboration session with enhanced error handling and fallback
  const connect = useCallback((newSessionId, newCalendarToken = null) => {
    // Clean up any existing connection
    if (socketRef.current) {
      socketRef.current.close();
    }

    // Clear any pending reconnect
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    // Set session ID and calendar token
    setSessionId(newSessionId);
    if (newCalendarToken) {
      setCalendarToken(newCalendarToken);
    }

    // Update connection state
    setConnectionState('connecting');

    // If we're in fallback mode, fetch data directly
    if (fallbackMode) {
      fetchDataInFallbackMode(newSessionId, newCalendarToken || calendarToken)
        .then(() => {
          setConnectionState('fallback');
          showSuccessNotification('Connected in fallback mode (limited functionality)');
        })
        .catch(() => {
          setConnectionState('disconnected');
          showErrorNotification('Failed to connect in fallback mode');
        });
      return;
    }

    // Create new WebSocket connection
    const socket = createCollaborationSocket(
      newSessionId,
      token,
      newCalendarToken || calendarToken
    );

    // Store socket reference
    socketRef.current = socket;

    // Set up event handlers
    socket.onopen = () => {
      logger.info('WebSocket connection established', { sessionId: newSessionId });
      setIsConnected(true);
      setConnectionState('connected');
      setConnectionErrors(0); // Reset error counter on successful connection
      setReconnectAttempts(0); // Reset reconnect attempts on successful connection
      showSuccessNotification('Connected to collaboration session');

      // Start heartbeat
      heartbeatIntervalRef.current = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({
            type: 'heartbeat',
            data: { timestamp: new Date().toISOString() }
          }));
        }
      }, CONFIG.HEARTBEAT_INTERVAL);
    };

    socket.onclose = (event) => {
      logger.info('WebSocket connection closed', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      });
      setIsConnected(false);

      // Clear heartbeat interval
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }

      // Increment error counter and reconnect attempts
      setConnectionErrors(prev => prev + 1);
      setReconnectAttempts(prev => prev + 1);

      // If we've had too many errors or reconnect attempts, switch to fallback mode
      if (connectionErrors >= CONFIG.MAX_RECONNECT_ATTEMPTS || reconnectAttempts >= CONFIG.MAX_RECONNECT_ATTEMPTS) {
        setFallbackMode(true);
        setConnectionState('fallback');
        setReconnectAttempts(0); // Reset for next session
        showErrorNotification('Switched to fallback mode due to connection issues');
        logger.warn('Switched to fallback mode', { connectionErrors, reconnectAttempts });

        // Try to fetch data directly
        fetchDataInFallbackMode(newSessionId, newCalendarToken || calendarToken);
        return;
      }

      // Otherwise attempt to reconnect with exponential backoff
      setConnectionState('reconnecting');
      const backoffTime = Math.min(CONFIG.RECONNECT_DELAY * Math.pow(2, reconnectAttempts), 30000);
      const jitter = Math.random() * 1000;
      const reconnectTime = backoffTime + jitter;

      logger.info('Attempting reconnection', {
        attempt: reconnectAttempts + 1,
        delay: Math.round(reconnectTime/1000)
      });
      showErrorNotification(`Connection lost. Reconnecting in ${Math.round(reconnectTime/1000)} seconds...`);

      reconnectTimeoutRef.current = setTimeout(() => {
        if (sessionId) {
          connect(sessionId, newCalendarToken || calendarToken);
        }
      }, reconnectTime);
    };

    socket.onerror = (error) => {
      logger.error('WebSocket error', error);
      setLastError(error);
      showErrorNotification('Error connecting to collaboration session');
    };

    socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleMessage(message);
      } catch (error) {
        logger.error('Error parsing WebSocket message', error);
      }
    };

    return () => {
      // Clean up
      if (socket) {
        socket.close();
      }

      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
      }

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [token, calendarToken, showSuccessNotification, showErrorNotification,
      connectionErrors, fallbackMode, fetchDataInFallbackMode, handleMessage, sessionId, reconnectAttempts]);

  // Disconnect from collaboration session
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }

    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    setIsConnected(false);
    setSessionId(null);
    setCalendarToken(null);
    setActiveUsers([]);
    setUserCursors({});
  }, []);

  // Handle incoming WebSocket messages
  const handleMessage = useCallback((message) => {
    switch (message.type) {
      case 'session_state':
        // Initial session state
        setActiveUsers(message.data.active_users || []);
        break;

      case 'user_joined':
        // User joined the session
        setActiveUsers(prev => {
          const exists = prev.some(user => user.user_id === message.user_id);
          if (!exists) {
            return [...prev, {
              user_id: message.user_id,
              user_info: message.user_info
            }];
          }
          return prev;
        });
        break;

      case 'user_left':
        // User left the session
        setActiveUsers(prev => prev.filter(user => user.user_id !== message.user_id));
        setUserCursors(prev => {
          const newCursors = { ...prev };
          delete newCursors[message.user_id];
          return newCursors;
        });
        break;

      case 'cursor_move':
        // Update cursor position
        setUserCursors(prev => ({
          ...prev,
          [message.user_id]: {
            position: message.position,
            timestamp: message.timestamp,
            content_id: message.content_id
          }
        }));
        break;

      case 'annotation':
        // New annotation
        setAnnotations(prev => [...prev, message.annotation]);
        break;

      case 'content_change':
        // Content changed
        // This would typically trigger a refresh of the content
        break;

      default:
        logger.warn('Unknown message type received', { type: message.type, message });
    }
  }, []);

  // Send cursor position with fallback handling
  const sendCursorPosition = useCallback((position, contentId = null) => {
    // In fallback mode, we don't send cursor positions
    if (fallbackMode) {
      return;
    }

    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      try {
        socketRef.current.send(JSON.stringify({
          type: 'cursor_move',
          data: {
            position,
            content_id: contentId,
            timestamp: new Date().toISOString()
          }
        }));
      } catch (error) {
        logger.error('Error sending cursor position', error);
      }
    }
  }, [fallbackMode]);

  // Send annotation with fallback handling
  const sendAnnotation = useCallback(async (annotation) => {
    // In fallback mode, save annotation directly via API
    if (fallbackMode) {
      try {
        const savedAnnotation = await createImageAnnotation(annotation, calendarToken);
        setAnnotations(prev => [...prev, savedAnnotation]);
        return savedAnnotation;
      } catch (error) {
        logger.error('Error saving annotation in fallback mode', error);
        showErrorNotification('Failed to save annotation');
        return null;
      }
    }

    // Normal WebSocket mode
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      try {
        socketRef.current.send(JSON.stringify({
          type: 'annotation',
          data: {
            annotation,
            content_id: annotation.content_id,
            timestamp: new Date().toISOString()
          }
        }));
        return true;
      } catch (error) {
        logger.error('Error sending annotation', error);
        return false;
      }
    }

    return false;
  }, [fallbackMode, calendarToken, showErrorNotification]);

  // Send content change with fallback handling
  const sendContentChange = useCallback(async (changes, contentId) => {
    // In fallback mode, we can't send real-time changes
    // Instead, we'll just show a notification
    if (fallbackMode) {
      showErrorNotification('Real-time content changes are not available in fallback mode');
      return false;
    }

    // Normal WebSocket mode
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      try {
        socketRef.current.send(JSON.stringify({
          type: 'content_change',
          data: {
            changes,
            content_id: contentId,
            timestamp: new Date().toISOString()
          }
        }));
        return true;
      } catch (error) {
        logger.error('Error sending content change', error);
        return false;
      }
    }

    return false;
  }, [fallbackMode, showErrorNotification]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  // Context value
  const value = {
    // Connection state
    isConnected,
    connectionState,
    fallbackMode,
    connectionErrors,
    reconnectAttempts,
    lastError,

    // Session data
    sessionId,
    calendarToken,
    activeUsers,
    userCursors,
    annotations,
    imageAnnotations,

    // Connection methods
    connect,
    disconnect,

    // Communication methods
    sendCursorPosition,
    sendAnnotation,
    sendContentChange,

    // Data management
    setAnnotations,
    setImageAnnotations,

    // Helper methods for fallback mode
    refreshData: () => fetchDataInFallbackMode(sessionId, calendarToken),
    toggleFallbackMode: (enabled) => setFallbackMode(enabled),

    // Utility methods
    clearError: () => setLastError(null),
    resetConnection: () => {
      setConnectionErrors(0);
      setReconnectAttempts(0);
      setLastError(null);
    }
  };

  return (
    <CollaborationContext.Provider value={value}>
      {children}
    </CollaborationContext.Provider>
  );
};

export default CollaborationContext;

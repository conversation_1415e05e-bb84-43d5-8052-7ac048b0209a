/**
 * Enhanced Recommendation Card - Enterprise-grade recommendation management component
 * Features: Comprehensive recommendation management, intelligent scheduling recommendations, real-time analytics monitoring,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced recommendation capabilities and seamless scheduling workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Chip,
  Button,
  IconButton,
  Tooltip,
  alpha,
  useMediaQuery,
  CircularProgress,
  Alert,
  Snackbar,
  LinearProgress,
  Menu,
  MenuItem,
  Divider,
  Card,
  CardContent,
  CardActions,
  Collapse,
  Rating
} from '@mui/material';
import {
  AccessTime as AccessTimeIcon,
  Info as InfoIcon,
  Schedule as ScheduleIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  MoreVert as MoreVertIcon,
  TrendingUp as TrendingUpIcon,
  Analytics as AnalyticsIcon,
  AutoAwesome as AutoAwesomeIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Star as StarIcon,
  Timeline as TimelineIcon,
  Insights as InsightsIcon,
  Speed as SpeedIcon,
  Psychology as PsychologyIcon,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';

import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Recommendation display modes with enhanced configurations
const RECOMMENDATION_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Recommendation',
    description: 'Basic recommendation interface',
    features: ['basic_recommendation', 'quick_actions', 'confidence_display']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Recommendation',
    description: 'Comprehensive recommendation management',
    features: ['detailed_recommendation', 'analytics_display', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Recommendation',
    description: 'AI-powered recommendation optimization and suggestions',
    features: ['ai_assisted_recommendation', 'ai_optimization', 'recommendation_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Recommendation',
    description: 'Advanced recommendation analytics and forecasting',
    features: ['analytics_recommendation', 'recommendation_insights']
  }
};

// Recommendation types with enhanced metadata
const RECOMMENDATION_TYPES = {
  OPTIMAL_TIMING: {
    id: 'optimal_timing',
    name: 'Optimal Timing',
    description: 'Best time slots for maximum engagement',
    icon: AccessTimeIcon,
    color: ACE_COLORS.PURPLE
  },
  CONTENT_SUGGESTIONS: {
    id: 'content_suggestions',
    name: 'Content Suggestions',
    description: 'AI-powered content recommendations',
    icon: LightbulbIcon,
    color: ACE_COLORS.YELLOW
  },
  AUDIENCE_TARGETING: {
    id: 'audience_targeting',
    name: 'Audience Targeting',
    description: 'Optimal audience targeting strategies',
    icon: PsychologyIcon,
    color: ACE_COLORS.PURPLE
  },
  PLATFORM_OPTIMIZATION: {
    id: 'platform_optimization',
    name: 'Platform Optimization',
    description: 'Platform-specific optimization recommendations',
    icon: TrendingUpIcon,
    color: ACE_COLORS.DARK
  }
};
/**
 * Enhanced Recommendation Card Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Object} props.recommendation - Recommendation data object
 * @param {string} [props.day] - Day of the week for the recommendation
 * @param {Function} [props.onSelect] - Callback when card is selected for details
 * @param {Function} [props.onSchedule] - Callback when schedule button is clicked
 * @param {Function} [props.getConfidenceColor] - Function to get color based on confidence level
 * @param {Function} [props.onAccept] - Callback when recommendation is accepted
 * @param {Function} [props.onReject] - Callback when recommendation is rejected
 * @param {Function} [props.onFeedback] - Callback for recommendation feedback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onRecommendationAction] - Recommendation action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-recommendation-card'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const RecommendationCard = memo(forwardRef(({
  recommendation,
  day,
  onSelect,
  onSchedule,
  getConfidenceColor,
  onAccept,
  onReject,
  onFeedback,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onRecommendationAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-recommendation-card',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');
  const isTablet = useMediaQuery('(max-width:900px)');

  // Core state management
  const recommendationRef = useRef(null);
  const [expanded, setExpanded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [feedbackOpen, setFeedbackOpen] = useState(false);
  const [confidenceScore] = useState(recommendation?.confidence || 0);

  // Enhanced state management
  const [recommendationMode, setRecommendationMode] = useState('compact');
  const [recommendationHistory, setRecommendationHistory] = useState([]);
  const [recommendationAnalytics, setRecommendationAnalytics] = useState(null);
  const [recommendationInsights] = useState(null);
  const [customRecommendationConfigs, setCustomRecommendationConfigs] = useState([]);
  const [recommendationPreferences, setRecommendationPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    recommendationSuggestions: true,
    dismissTimeout: 10000,
    showConfidenceScore: true,
    enableQuickActions: true
  });

  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced recommendation features
  const [recommendationDrawerOpen, setRecommendationDrawerOpen] = useState(false);
  const [selectedRecommendationType, setSelectedRecommendationType] = useState(null);
  const [recommendationStats] = useState(null);
  const [aiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [recommendationScore, setRecommendationScore] = useState(0);
  const [recommendationProgress] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [recommendationVersions] = useState([]);
  const [impactAnalysis, setImpactAnalysis] = useState(null);
  const [optimizationSuggestions, setOptimizationSuggestions] = useState([]);
  const [isAccepted, setIsAccepted] = useState(false);
  const [isRejected, setIsRejected] = useState(false);
  const [feedbackData, setFeedbackData] = useState({
    rating: 0,
    comment: '',
    usefulness: 0,
    accuracy: 0
  });

  /**
   * Enhanced recommendation features - All features enabled without limitations
   */
  const recommendationFeatures = useMemo(() => {
    return {
      maxRecommendations: -1, // Unlimited
      maxDailyRecommendations: -1, // Unlimited
      maxRecommendationTypes: -1, // Unlimited
      hasAdvancedRecommendations: true,
      hasRecommendationAnalytics: true,
      hasCustomRecommendationConfigs: true,
      hasRecommendationInsights: true,
      hasRecommendationHistory: true,
      hasAIAssistance: true,
      hasRecommendationExport: true,
      hasRecommendationAutomation: true,
      hasAnalytics: true,
      hasExport: true,
      trackingLevel: 'full',
      refreshInterval: 1000,
      planName: 'Enhanced Recommendation',
      planTier: 3,
      allowedRecommendationTypes: ['optimal_timing', 'content_suggestions', 'audience_targeting', 'platform_optimization', 'advanced_recommendation', 'recommendation_analytics', 'smart_recommendation', 'recommendation_automation', 'custom_recommendation'],
      maxHistoryDays: -1, // Unlimited
      hasImpactAnalysis: true,
      hasOptimizationSuggestions: true,
      hasBatchOperations: true,
      hasQuickActions: true,
      hasSwipeGestures: true,
      hasFeatureAccess: () => true,
      isWithinLimits: () => true,
      canUseFeature: () => true,
      canUseRecommendationType: () => true
    };
  }, []);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'article',
      'aria-label': ariaLabel || `Recommendation card with ${recommendationFeatures.planName} features`,
      'aria-description': ariaDescription || `Recommendation interface with ${recommendationFeatures.trackingLevel} tracking and confidence score ${confidenceScore}`,
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, recommendationFeatures.planName, recommendationFeatures.trackingLevel, confidenceScore]);

  // Fetch recommendation analytics with enhanced error handling and retry logic
  const fetchRecommendationAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/recommendations/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setRecommendationAnalytics(data);
              if (recommendationPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Recommendation analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch recommendation analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load recommendation analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          console.error("Network error fetching recommendation analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, recommendationPreferences.showAnalytics]);

  // Handle recommendation mode switching
  const handleRecommendationModeChange = useCallback((newMode) => {
    if (RECOMMENDATION_MODES[newMode.toUpperCase()]) {
      setRecommendationMode(newMode);
      announceToScreenReader(`Recommendation mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'recommendation_mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setRecommendationHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (recommendationPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} recommendation mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, recommendationPreferences.showAnalytics, showSuccess]);

  // Handle custom recommendation config management
  const addCustomRecommendationConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomRecommendationConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_recommendation_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setRecommendationHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (recommendationPreferences.showAnalytics) {
      showSuccess(`Custom recommendation config "${configData.name}" created`);
    }
  }, [subscription?.user_id, recommendationPreferences.showAnalytics, showSuccess]);

  // Handle recommendation preferences updates
  const updateRecommendationPreferences = useCallback((newPreferences) => {
    setRecommendationPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'recommendation_preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setRecommendationHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (recommendationPreferences.showAnalytics) {
      showSuccess('Recommendation preferences updated');
    }
  }, [subscription?.user_id, recommendationPreferences.showAnalytics, showSuccess]);

  // Handle recommendation type selection
  const handleRecommendationTypeSelection = useCallback((recommendationType) => {
    setSelectedRecommendationType(recommendationType);

    // Track recommendation type selection
    const typeRecord = {
      id: Date.now(),
      type: 'recommendation_type_selected',
      recommendationType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setRecommendationHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (recommendationPreferences.showAnalytics) {
      announceToScreenReader(`Selected recommendation type: ${recommendationType}`);
    }
  }, [subscription?.user_id, recommendationPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateRecommendationConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Recommendation type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Switch to recommendation version
  const switchToRecommendationVersion = useCallback((versionId) => {
    const version = recommendationVersions.find(v => v.id === versionId);
    if (version) {
      setRecommendationScore(version.score || 0);

      if (recommendationPreferences.showAnalytics) {
        showSuccess(`Switched to recommendation version ${version.name}`);
      }
    }
  }, [recommendationVersions, recommendationPreferences.showAnalytics, showSuccess]);

  // Handle impact analysis
  const handleImpactAnalysis = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.post('/api/recommendations/analyze-impact', {
        recommendationId: recommendation?.id,
        currentSettings: recommendationPreferences
      });

      setImpactAnalysis(response.data);

      if (recommendationPreferences.showAnalytics) {
        showSuccess('Impact analysis completed');
      }
    } catch (error) {
      console.error('Impact analysis failed:', error);
      showError('Failed to analyze recommendation impact');
    } finally {
      setLoading(false);
    }
  }, [recommendation?.id, recommendationPreferences, showSuccess, showError]);

  // Handle recommendation optimization
  const handleRecommendationOptimization = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.post('/api/recommendations/optimize', {
        recommendationId: recommendation?.id,
        preferences: recommendationPreferences
      });

      setOptimizationSuggestions(response.data.suggestions || []);

      if (response.data.suggestions?.length > 0) {
        showSuccess(`${response.data.suggestions.length} optimization suggestions generated`);
      } else {
        showSuccess('Current recommendation is already optimized');
      }
    } catch (error) {
      console.error('Recommendation optimization failed:', error);
      showError('Failed to generate optimization suggestions');
    } finally {
      setLoading(false);
    }
  }, [recommendation?.id, recommendationPreferences, showSuccess, showError]);

  // Handle accept recommendation
  const handleAcceptRecommendation = useCallback(async () => {
    try {
      setLoading(true);

      if (onAccept) {
        await onAccept(recommendation);
      }

      setIsAccepted(true);
      setIsRejected(false);

      // Track acceptance
      const acceptRecord = {
        id: Date.now(),
        type: 'recommendation_accepted',
        recommendationId: recommendation?.id,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setRecommendationHistory(prev => [acceptRecord, ...prev.slice(0, 99)]);

      announceToScreenReader('Recommendation accepted');
      showSuccess('Recommendation accepted successfully');

      if (onRecommendationAction) {
        onRecommendationAction('accept', recommendation);
      }
    } catch (error) {
      console.error('Failed to accept recommendation:', error);
      showError('Failed to accept recommendation');
    } finally {
      setLoading(false);
    }
  }, [onAccept, recommendation, subscription?.user_id, announceToScreenReader, showSuccess, showError, onRecommendationAction]);

  // Handle reject recommendation
  const handleRejectRecommendation = useCallback(async () => {
    try {
      setLoading(true);

      if (onReject) {
        await onReject(recommendation);
      }

      setIsRejected(true);
      setIsAccepted(false);

      // Track rejection
      const rejectRecord = {
        id: Date.now(),
        type: 'recommendation_rejected',
        recommendationId: recommendation?.id,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setRecommendationHistory(prev => [rejectRecord, ...prev.slice(0, 99)]);

      announceToScreenReader('Recommendation rejected');
      showSuccess('Recommendation rejected');

      if (onRecommendationAction) {
        onRecommendationAction('reject', recommendation);
      }
    } catch (error) {
      console.error('Failed to reject recommendation:', error);
      showError('Failed to reject recommendation');
    } finally {
      setLoading(false);
    }
  }, [onReject, recommendation, subscription?.user_id, announceToScreenReader, showSuccess, showError, onRecommendationAction]);

  // Handle provide feedback
  const handleProvideFeedback = useCallback(async (feedback) => {
    try {
      setLoading(true);

      const feedbackData = {
        recommendationId: recommendation?.id,
        ...feedback,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      if (onFeedback) {
        await onFeedback(feedbackData);
      }

      setFeedbackData(prev => ({ ...prev, ...feedback }));

      // Track feedback
      const feedbackRecord = {
        id: Date.now(),
        type: 'recommendation_feedback',
        recommendationId: recommendation?.id,
        feedback: feedbackData,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setRecommendationHistory(prev => [feedbackRecord, ...prev.slice(0, 99)]);

      announceToScreenReader('Feedback provided');
      showSuccess('Thank you for your feedback');

      if (onRecommendationAction) {
        onRecommendationAction('feedback', feedbackData);
      }
    } catch (error) {
      console.error('Failed to provide feedback:', error);
      showError('Failed to submit feedback');
    } finally {
      setLoading(false);
    }
  }, [recommendation?.id, subscription?.user_id, onFeedback, announceToScreenReader, showSuccess, showError, onRecommendationAction]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive recommendation API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getRecommendationHistory: () => recommendationHistory,
    getRecommendationAnalytics: () => recommendationAnalytics,
    getRecommendationInsights: () => recommendationInsights,
    refreshRecommendation: () => {
      fetchRecommendationAnalytics();
      if (onRefresh) onRefresh();
    },

    // Recommendation methods
    focusRecommendation: () => {
      if (recommendationRef.current) {
        recommendationRef.current.focus();
      }
    },
    getRecommendationScore: () => recommendationScore,
    getRecommendationProgress: () => recommendationProgress,
    getImpactAnalysis: () => impactAnalysis,
    getOptimizationSuggestions: () => optimizationSuggestions,
    openRecommendationDrawer: () => setRecommendationDrawerOpen(true),
    closeRecommendationDrawer: () => setRecommendationDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),
    expandCard: () => setExpanded(true),
    collapseCard: () => setExpanded(false),

    // Analytics methods
    exportRecommendationData: () => {
      if (onExport) {
        onExport(recommendationHistory, recommendationAnalytics);
      }
    },

    // Accessibility methods
    announceRecommendation: (message) => announceToScreenReader(message),
    focusRecommendationField: () => setFocusToElement('recommendation-card-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => recommendationMode,
    getRecommendationStats: () => recommendationStats,
    getSelectedRecommendationType: () => selectedRecommendationType,
    getCustomRecommendationConfigs: () => customRecommendationConfigs,
    getRecommendationDrawerOpen: () => recommendationDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getActiveTab: () => activeTab,
    setActiveTab: (tab) => setActiveTab(tab),
    addCustomRecommendationConfig,
    handleRecommendationModeChange,
    updateRecommendationPreferences,
    handleRecommendationTypeSelection,
    validateRecommendationConfig,
    getRecommendationVersions: () => recommendationVersions,
    switchToVersion: (versionId) => switchToRecommendationVersion(versionId),
    analyzeImpact: () => handleImpactAnalysis(),
    optimizeRecommendation: () => handleRecommendationOptimization(),
    acceptRecommendation: () => handleAcceptRecommendation(),
    rejectRecommendation: () => handleRejectRecommendation(),
    provideFeedback: (feedback) => handleProvideFeedback(feedback),
    getConfidenceScore: () => confidenceScore,
    getFeedbackData: () => feedbackData,
    getIsAccepted: () => isAccepted,
    getIsRejected: () => isRejected
  }), [
    recommendationHistory,
    recommendationAnalytics,
    recommendationInsights,
    recommendationStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    recommendationMode,
    selectedRecommendationType,
    customRecommendationConfigs,
    recommendationScore,
    recommendationProgress,
    impactAnalysis,
    optimizationSuggestions,
    recommendationVersions,
    addCustomRecommendationConfig,
    handleRecommendationModeChange,
    updateRecommendationPreferences,
    handleRecommendationTypeSelection,
    validateRecommendationConfig,
    switchToRecommendationVersion,
    handleImpactAnalysis,
    handleRecommendationOptimization,
    handleAcceptRecommendation,
    handleRejectRecommendation,
    handleProvideFeedback,
    activeTab,
    recommendationDrawerOpen,
    showAnalytics,
    fetchRecommendationAnalytics,
    confidenceScore,
    feedbackData,
    isAccepted,
    isRejected
  ]);

  // Get enhanced confidence color with ACE Social branding
  const getEnhancedConfidenceColor = useCallback((confidence) => {
    if (getConfidenceColor) {
      return getConfidenceColor(confidence);
    }

    // Default ACE Social confidence colors
    if (typeof confidence === 'string') {
      switch (confidence.toLowerCase()) {
        case 'high':
          return '#4caf50'; // Green
        case 'medium':
          return ACE_COLORS.YELLOW;
        case 'low':
          return '#f44336'; // Red
        default:
          return ACE_COLORS.PURPLE;
      }
    }

    // Numeric confidence (0-1 or 0-100)
    const score = confidence > 1 ? confidence / 100 : confidence;
    if (score >= 0.8) return '#4caf50'; // Green
    if (score >= 0.6) return ACE_COLORS.YELLOW;
    if (score >= 0.4) return '#ff9800'; // Orange
    return '#f44336'; // Red
  }, [getConfidenceColor]);

  // Handle menu operations
  const handleMenuOpen = useCallback((event) => {
    setMenuAnchorEl(event.currentTarget);
  }, []);

  const handleMenuClose = useCallback(() => {
    setMenuAnchorEl(null);
  }, []);

  // Handle card click with enhanced functionality
  const handleCardClick = useCallback((event) => {
    if (event.target.closest('button') || event.target.closest('[role="button"]')) {
      return; // Don't trigger card click if clicking on buttons
    }

    if (onSelect) {
      onSelect(recommendation);
    }

    // Track card interaction
    const clickRecord = {
      id: Date.now(),
      type: 'recommendation_card_clicked',
      recommendationId: recommendation?.id,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setRecommendationHistory(prev => [clickRecord, ...prev.slice(0, 99)]);

    if (onRecommendationAction) {
      onRecommendationAction('card_click', recommendation);
    }
  }, [onSelect, recommendation, subscription?.user_id, onRecommendationAction]);

  // Handle schedule action with enhanced functionality
  const handleScheduleClick = useCallback((event) => {
    event.stopPropagation();

    if (onSchedule) {
      onSchedule(recommendation);
    }

    // Track schedule action
    const scheduleRecord = {
      id: Date.now(),
      type: 'recommendation_schedule_clicked',
      recommendationId: recommendation?.id,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setRecommendationHistory(prev => [scheduleRecord, ...prev.slice(0, 99)]);

    announceToScreenReader('Schedule action triggered');

    if (onRecommendationAction) {
      onRecommendationAction('schedule', recommendation);
    }
  }, [onSchedule, recommendation, subscription?.user_id, announceToScreenReader, onRecommendationAction]);

  // Get recommendation type info
  const getRecommendationTypeInfo = useCallback(() => {
    const type = recommendation?.type || 'optimal_timing';
    return RECOMMENDATION_TYPES[type.toUpperCase()] || RECOMMENDATION_TYPES.OPTIMAL_TIMING;
  }, [recommendation?.type]);

  // Show loading state if loading
  if (loading && !recommendation) {
    return (
      <Card
        {...getAccessibilityProps()}
        ref={recommendationRef}
        sx={{
          ...sx,
          ...customization,
          mb: 2,
          p: isMobile ? 1.5 : isTablet ? 2 : 2.5,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: isMobile ? 100 : 120,
          bgcolor: alpha(ACE_COLORS.WHITE, 0.95)
        }}
        className={className}
        style={style}
        data-testid={testId}
      >
        <CircularProgress sx={{ color: ACE_COLORS.PURPLE, mr: 2 }} />
        <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
          Loading recommendation...
        </Typography>
      </Card>
    );
  }

  const typeInfo = getRecommendationTypeInfo();
  const TypeIcon = typeInfo.icon;
  const confidenceColor = getEnhancedConfidenceColor(recommendation?.confidence || 0);

  return (
    <Card
      {...getAccessibilityProps()}
      ref={recommendationRef}
      sx={{
        ...sx,
        ...customization,
        mb: isMobile ? 1.5 : 2,
        p: isMobile ? 1 : isTablet ? 1.5 : 2,
        borderLeft: `${isMobile ? 3 : 4}px solid ${confidenceColor}`,
        transition: 'all 0.3s ease-in-out',
        cursor: 'pointer',
        bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
        '&:hover': {
          boxShadow: `0 8px 32px ${alpha(ACE_COLORS.PURPLE, 0.15)}`,
          transform: 'translateY(-2px)',
          borderLeftColor: ACE_COLORS.PURPLE,
        },
        '&:focus': {
          outline: `2px solid ${ACE_COLORS.PURPLE}`,
          outlineOffset: 2,
        },
        ...(isAccepted && {
          borderLeftColor: '#4caf50',
          bgcolor: alpha('#4caf50', 0.05)
        }),
        ...(isRejected && {
          borderLeftColor: '#f44336',
          bgcolor: alpha('#f44336', 0.05)
        })
      }}
      className={className}
      style={style}
      data-testid={testId}
      onClick={handleCardClick}
    >
      <CardContent sx={{ pb: 1 }}>
        {/* Header Section */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            <TypeIcon
              sx={{
                mr: 1,
                color: typeInfo.color,
                fontSize: isMobile ? '1.2rem' : '1.5rem'
              }}
            />
            <Box>
              <Typography
                variant={isMobile ? "subtitle2" : "subtitle1"}
                sx={{
                  fontWeight: 'bold',
                  color: ACE_COLORS.DARK,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                {recommendation?.time_slot || 'Optimal Time'}
                {recommendation?.is_ai_generated && (
                  <AutoAwesomeIcon
                    sx={{
                      ml: 1,
                      fontSize: '1rem',
                      color: ACE_COLORS.YELLOW
                    }}
                  />
                )}
              </Typography>
              <Typography variant="caption" sx={{ color: ACE_COLORS.DARK, opacity: 0.7 }}>
                {typeInfo.description}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Confidence Score */}
            <Chip
              label={
                typeof recommendation?.confidence === 'string'
                  ? recommendation.confidence
                  : `${Math.round((recommendation?.confidence || 0) * 100)}%`
              }
              size="small"
              sx={{
                bgcolor: confidenceColor,
                color: ACE_COLORS.WHITE,
                fontWeight: 'bold',
                fontSize: '0.75rem'
              }}
            />

            {/* Status Indicators */}
            {isAccepted && (
              <CheckCircleIcon sx={{ color: '#4caf50', fontSize: '1.2rem' }} />
            )}
            {isRejected && (
              <CancelIcon sx={{ color: '#f44336', fontSize: '1.2rem' }} />
            )}

            {/* Menu Button */}
            <IconButton
              size="small"
              onClick={handleMenuOpen}
              sx={{
                color: ACE_COLORS.DARK,
                '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.1) }
              }}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Day Information */}
        {day && (
          <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, opacity: 0.8, mb: 1 }}>
            <strong>Day:</strong> {day}
          </Typography>
        )}

        {/* Platform Information */}
        {recommendation?.platform && recommendation.platform !== 'all' && (
          <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, opacity: 0.8, mb: 1 }}>
            <strong>Platform:</strong> {recommendation.platform.charAt(0).toUpperCase() + recommendation.platform.slice(1)}
          </Typography>
        )}

        {/* Real-time Optimization Indicator */}
        {enableRealTimeOptimization && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <SpeedIcon sx={{ mr: 1, fontSize: '1rem', color: ACE_COLORS.YELLOW }} />
            <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, fontSize: '0.85rem' }}>
              Real-time optimization enabled
            </Typography>
          </Box>
        )}

        {/* Engagement Information */}
        {recommendation?.avg_engagement && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <TrendingUpIcon sx={{ mr: 1, fontSize: '1rem', color: ACE_COLORS.PURPLE }} />
            <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
              <strong>Avg. Engagement:</strong> {(recommendation.avg_engagement * 100).toFixed(1)}%
            </Typography>
          </Box>
        )}

        {/* AI Insights */}
        {recommendation?.ai_insights && enableAIInsights && (
          <Box sx={{
            mt: 2,
            p: 1.5,
            bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
            borderRadius: 1,
            border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PsychologyIcon sx={{ mr: 1, fontSize: '1rem', color: ACE_COLORS.YELLOW }} />
              <Typography variant="caption" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK }}>
                AI Insights
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, fontSize: '0.85rem' }}>
              {recommendation.ai_insights}
            </Typography>
          </Box>
        )}

        {/* Expanded Content */}
        <Collapse in={expanded}>
          <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${alpha(ACE_COLORS.DARK, 0.1)}` }}>
            {/* Impact Analysis */}
            {impactAnalysis && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK, mb: 1 }}>
                  Impact Analysis
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
                      Expected Reach
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      +{impactAnalysis.expectedReach || 0}%
                    </Typography>
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
                      Engagement Boost
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                      +{impactAnalysis.engagementBoost || 0}%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            )}

            {/* User Rating */}
            {feedbackData.rating > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK, mb: 1 }}>
                  Your Rating
                </Typography>
                <Rating value={feedbackData.rating} readOnly size="small" />
              </Box>
            )}
          </Box>
        </Collapse>
      </CardContent>

      {/* Action Buttons */}
      <CardActions sx={{ px: 2, pb: 2, pt: 0 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
          {/* Left Side Actions */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Expand/Collapse Button */}
            <Tooltip title={expanded ? "Show less" : "Show more"}>
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  setExpanded(!expanded);
                }}
                sx={{
                  color: ACE_COLORS.DARK,
                  '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.1) }
                }}
              >
                {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Tooltip>

            {/* Info Button */}
            <Tooltip title="View recommendation details">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onSelect) onSelect(recommendation);
                }}
                sx={{
                  color: ACE_COLORS.PURPLE,
                  '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.1) }
                }}
              >
                <InfoIcon />
              </IconButton>
            </Tooltip>

            {/* Analytics Button */}
            {enableAdvancedFeatures && (
              <Tooltip title="View analytics">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowAnalytics(!showAnalytics);
                  }}
                  sx={{
                    color: showAnalytics ? ACE_COLORS.YELLOW : ACE_COLORS.DARK,
                    '&:hover': { bgcolor: alpha(ACE_COLORS.YELLOW, 0.1) }
                  }}
                >
                  <AnalyticsIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          {/* Right Side Actions */}
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            {/* Accept/Reject Buttons */}
            {!isAccepted && !isRejected && (
              <>
                <Tooltip title="Reject recommendation">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRejectRecommendation();
                    }}
                    sx={{
                      color: '#f44336',
                      '&:hover': { bgcolor: alpha('#f44336', 0.1) }
                    }}
                  >
                    <ThumbDownIcon />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Accept recommendation">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAcceptRecommendation();
                    }}
                    sx={{
                      color: '#4caf50',
                      '&:hover': { bgcolor: alpha('#4caf50', 0.1) }
                    }}
                  >
                    <ThumbUpIcon />
                  </IconButton>
                </Tooltip>
              </>
            )}

            {/* Schedule Button */}
            <Button
              variant="contained"
              size="small"
              startIcon={<ScheduleIcon />}
              onClick={handleScheduleClick}
              disabled={loading}
              sx={{
                bgcolor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.WHITE,
                '&:hover': {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.8)
                },
                '&:disabled': {
                  bgcolor: alpha(ACE_COLORS.DARK, 0.3)
                }
              }}
            >
              {loading ? 'Processing...' : 'Schedule'}
            </Button>
          </Box>
        </Box>
      </CardActions>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        slotProps={{
          paper: {
            sx: {
              bgcolor: ACE_COLORS.WHITE,
              border: `1px solid ${alpha(ACE_COLORS.DARK, 0.2)}`
            }
          }
        }}
      >
        <MenuItem onClick={() => { handleImpactAnalysis(); handleMenuClose(); }}>
          <InsightsIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
          Analyze Impact
        </MenuItem>
        <MenuItem onClick={() => { handleRecommendationOptimization(); handleMenuClose(); }}>
          <SpeedIcon sx={{ mr: 1, color: ACE_COLORS.YELLOW }} />
          Optimize
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => { setFeedbackOpen(true); handleMenuClose(); }}>
          <StarIcon sx={{ mr: 1, color: ACE_COLORS.DARK }} />
          Provide Feedback
        </MenuItem>
        {onExport && (
          <MenuItem onClick={() => { onExport(recommendation); handleMenuClose(); }}>
            <TimelineIcon sx={{ mr: 1, color: ACE_COLORS.DARK }} />
            Export Data
          </MenuItem>
        )}
      </Menu>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Progress Indicator */}
      {recommendationProgress > 0 && recommendationProgress < 100 && (
        <LinearProgress
          variant="determinate"
          value={recommendationProgress}
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            '& .MuiLinearProgress-bar': {
              backgroundColor: ACE_COLORS.PURPLE
            }
          }}
        />
      )}

      {/* Feedback Dialog */}
      <Collapse in={feedbackOpen}>
        <Box sx={{
          mt: 2,
          p: 2,
          bgcolor: alpha(ACE_COLORS.PURPLE, 0.05),
          borderRadius: 1,
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
        }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK, mb: 2 }}>
            Provide Feedback
          </Typography>

          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
              Rate this recommendation:
            </Typography>
            <Rating
              value={feedbackData.rating}
              onChange={(event, newValue) => {
                setFeedbackData(prev => ({ ...prev, rating: newValue || 0 }));
              }}
              size="small"
            />
          </Box>

          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
            <Button
              size="small"
              onClick={() => setFeedbackOpen(false)}
              sx={{ color: ACE_COLORS.DARK }}
            >
              Cancel
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={() => {
                handleProvideFeedback(feedbackData);
                setFeedbackOpen(false);
              }}
              sx={{
                bgcolor: ACE_COLORS.PURPLE,
                '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
              }}
            >
              Submit
            </Button>
          </Box>
        </Box>
      </Collapse>
    </Card>
  );
}));

// Enhanced PropTypes with comprehensive validation
RecommendationCard.propTypes = {
  // Core props
  recommendation: PropTypes.object.isRequired,
  day: PropTypes.string,
  onSelect: PropTypes.func,
  onSchedule: PropTypes.func,
  getConfidenceColor: PropTypes.func,

  // Enhanced props
  onAccept: PropTypes.func,
  onReject: PropTypes.func,
  onFeedback: PropTypes.func,
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onRecommendationAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

RecommendationCard.displayName = 'RecommendationCard';

export default RecommendationCard;

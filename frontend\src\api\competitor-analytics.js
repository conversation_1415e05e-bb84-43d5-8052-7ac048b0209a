/**
 * API service for competitor social media analytics
 * 
 * This service handles fetching competitor data from social media platforms
 * and provides analytics insights for competitive intelligence.
 */

import api from './index';

/**
 * Get social media metrics for a specific competitor
 * @param {string} competitorId - The competitor's ID
 * @param {Object} options - Optional parameters
 * @param {string[]} options.platforms - Platforms to analyze
 * @param {boolean} options.refresh - Force refresh data from APIs
 * @returns {Promise<Object[]>} Array of competitor metrics by platform
 */
export const getCompetitorMetrics = async (competitorId, options = {}) => {
  try {
    const params = new URLSearchParams();
    
    if (options.platforms && options.platforms.length > 0) {
      options.platforms.forEach(platform => params.append('platforms', platform));
    }
    
    if (options.refresh) {
      params.append('refresh', 'true');
    }
    
    const queryString = params.toString();
    const url = `/api/competitor-analytics/competitors/${competitorId}/metrics${queryString ? `?${queryString}` : ''}`;
    
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching competitor metrics:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch competitor metrics'
    );
  }
};

/**
 * Compare multiple competitors across social media platforms
 * @param {string[]} competitorIds - Array of competitor IDs to compare
 * @param {string[]} platforms - Optional platforms to compare
 * @returns {Promise<Object[]>} Array of competitor comparison results
 */
export const compareCompetitors = async (competitorIds, platforms = null) => {
  try {
    if (!competitorIds || competitorIds.length < 2) {
      throw new Error('At least 2 competitors required for comparison');
    }
    
    if (competitorIds.length > 10) {
      throw new Error('Maximum 10 competitors allowed for comparison');
    }
    
    const params = new URLSearchParams();
    competitorIds.forEach(id => params.append('competitor_ids', id));
    
    if (platforms && platforms.length > 0) {
      platforms.forEach(platform => params.append('platforms', platform));
    }
    
    const response = await api.post(`/api/competitor-analytics/competitors/compare?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error comparing competitors:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to compare competitors'
    );
  }
};

/**
 * Refresh competitor analytics data from social media APIs
 * @param {Object} refreshRequest - Refresh request parameters
 * @param {string[]} refreshRequest.competitor_ids - Competitor IDs to refresh
 * @param {string[]} refreshRequest.platforms - Optional platforms to refresh
 * @param {boolean} refreshRequest.force_refresh - Force refresh even if data is recent
 * @returns {Promise<Object>} Refresh status
 */
export const refreshCompetitorAnalytics = async (refreshRequest) => {
  try {
    const response = await api.post('/api/competitor-analytics/competitors/refresh', refreshRequest);
    return response.data;
  } catch (error) {
    console.error('Error refreshing competitor analytics:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to refresh competitor analytics'
    );
  }
};

/**
 * Get industry benchmarks for a specific platform
 * @param {string} platform - The social media platform
 * @param {string} industry - Optional industry category
 * @returns {Promise<Object>} Industry benchmark data
 */
export const getIndustryBenchmarks = async (platform, industry = null) => {
  try {
    const params = new URLSearchParams();
    if (industry) {
      params.append('industry', industry);
    }
    
    const queryString = params.toString();
    const url = `/api/competitor-analytics/benchmarks/${platform}${queryString ? `?${queryString}` : ''}`;
    
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching industry benchmarks:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch industry benchmarks'
    );
  }
};

/**
 * Get AI-powered insights for a specific competitor
 * @param {string} competitorId - The competitor's ID
 * @param {string} timeframe - Timeframe for insights (7d, 30d, 90d)
 * @returns {Promise<Object>} AI-generated insights and recommendations
 */
export const getCompetitorInsights = async (competitorId, timeframe = '30d') => {
  try {
    const response = await api.get(
      `/api/competitor-analytics/competitors/${competitorId}/insights?timeframe=${timeframe}`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching competitor insights:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch competitor insights'
    );
  }
};

/**
 * Analyze competitor's content strategy and performance
 * @param {string} competitorId - The competitor's ID
 * @param {string} platform - Platform to analyze
 * @param {number} limit - Number of recent posts to analyze
 * @returns {Promise<Object>} Content analysis results
 */
export const analyzeCompetitorContent = async (competitorId, platform, limit = 50) => {
  try {
    const response = await api.get(
      `/api/competitor-analytics/competitors/${competitorId}/content-analysis?platform=${platform}&limit=${limit}`
    );
    return response.data;
  } catch (error) {
    console.error('Error analyzing competitor content:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to analyze competitor content'
    );
  }
};

/**
 * Get competitor analytics health status
 * @returns {Promise<Object>} Service health status
 */
export const getAnalyticsHealth = async () => {
  try {
    const response = await api.get('/api/competitor-analytics/health');
    return response.data;
  } catch (error) {
    console.error('Error checking analytics health:', error);
    throw new Error('Failed to check analytics service health');
  }
};

/**
 * Batch fetch metrics for multiple competitors
 * @param {string[]} competitorIds - Array of competitor IDs
 * @param {Object} options - Optional parameters
 * @returns {Promise<Object>} Batch metrics results
 */
export const batchFetchCompetitorMetrics = async (competitorIds, options = {}) => {
  try {
    const results = {};
    const errors = {};
    
    // Fetch metrics for each competitor in parallel
    const promises = competitorIds.map(async (competitorId) => {
      try {
        const metrics = await getCompetitorMetrics(competitorId, options);
        results[competitorId] = metrics;
      } catch (error) {
        errors[competitorId] = error.message;
      }
    });
    
    await Promise.allSettled(promises);
    
    return {
      results,
      errors,
      success_count: Object.keys(results).length,
      error_count: Object.keys(errors).length
    };
  } catch (error) {
    console.error('Error in batch fetch:', error);
    throw new Error('Failed to batch fetch competitor metrics');
  }
};

/**
 * Get competitor performance trends over time
 * @param {string} competitorId - The competitor's ID
 * @param {string} platform - Platform to analyze
 * @param {string} timeframe - Time period (7d, 30d, 90d, 1y)
 * @returns {Promise<Object>} Performance trends data
 */
export const getCompetitorTrends = async (competitorId, platform, timeframe = '30d') => {
  try {
    const response = await api.get(
      `/api/competitor-analytics/competitors/${competitorId}/trends?platform=${platform}&timeframe=${timeframe}`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching competitor trends:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch competitor trends'
    );
  }
};

/**
 * Export competitor analytics data
 * @param {Object} exportRequest - Export parameters
 * @param {string[]} exportRequest.competitor_ids - Competitors to export
 * @param {string[]} exportRequest.platforms - Platforms to include
 * @param {string} exportRequest.format - Export format (csv, xlsx, json)
 * @returns {Promise<Blob>} Export file blob
 */
export const exportCompetitorData = async (exportRequest) => {
  try {
    const response = await api.post(
      '/api/competitor-analytics/export',
      exportRequest,
      { responseType: 'blob' }
    );
    
    return response.data;
  } catch (error) {
    console.error('Error exporting competitor data:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to export competitor data'
    );
  }
};

/**
 * Get real-time competitor alerts
 * @param {string} competitorId - The competitor's ID
 * @returns {Promise<Object[]>} Array of alerts
 */
export const getCompetitorAlerts = async (competitorId) => {
  try {
    const response = await api.get(
      `/api/competitor-analytics/competitors/${competitorId}/alerts`
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching competitor alerts:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch competitor alerts'
    );
  }
};

/**
 * Set up competitor monitoring alerts
 * @param {string} competitorId - The competitor's ID
 * @param {Object} alertConfig - Alert configuration
 * @returns {Promise<Object>} Alert setup confirmation
 */
export const setupCompetitorAlerts = async (competitorId, alertConfig) => {
  try {
    const response = await api.post(
      `/api/competitor-analytics/competitors/${competitorId}/alerts`,
      alertConfig
    );
    return response.data;
  } catch (error) {
    console.error('Error setting up competitor alerts:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to setup competitor alerts'
    );
  }
};

/**
 * Utility function to format metrics for display
 * @param {Object} metrics - Raw metrics data
 * @returns {Object} Formatted metrics
 */
export const formatMetricsForDisplay = (metrics) => {
  if (!metrics) return null;
  
  return {
    ...metrics,
    followers_count_formatted: formatNumber(metrics.followers_count),
    engagement_rate_formatted: `${metrics.engagement_rate.toFixed(1)}%`,
    avg_likes_formatted: formatNumber(metrics.avg_likes_per_post),
    avg_comments_formatted: formatNumber(metrics.avg_comments_per_post),
    avg_shares_formatted: formatNumber(metrics.avg_shares_per_post),
    last_post_relative: metrics.last_post_date ? 
      formatRelativeTime(new Date(metrics.last_post_date)) : 'Unknown'
  };
};

/**
 * Utility function to format numbers with appropriate suffixes
 * @param {number} num - Number to format
 * @returns {string} Formatted number string
 */
const formatNumber = (num) => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
};

/**
 * Utility function to format relative time
 * @param {Date} date - Date to format
 * @returns {string} Relative time string
 */
const formatRelativeTime = (date) => {
  const now = new Date();
  const diffMs = now - date;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  
  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
  } else {
    return 'Just now';
  }
};

/**
 * Subscribe to competitor alerts
 * @param {string} competitorId - The competitor's ID
 * @param {Object} alertConfig - Alert configuration
 * @param {string[]} alertConfig.metrics - Metrics to monitor
 * @param {string} alertConfig.frequency - Alert frequency (daily, weekly, monthly)
 * @param {number} alertConfig.threshold - Threshold for alerts
 * @returns {Promise<Object>} Subscription details
 */
export const subscribeToCompetitorAlerts = async (competitorId, alertConfig) => {
  try {
    const response = await api.post(`/api/competitor-analytics/competitors/${competitorId}/alerts/subscribe`, {
      metrics: alertConfig.metrics || ['followers', 'engagement'],
      frequency: alertConfig.frequency || 'weekly',
      threshold: alertConfig.threshold || 10,
      enabled: true
    });
    return response.data;
  } catch (error) {
    console.error('Error subscribing to competitor alerts:', error);
    throw new Error(
      error.response?.data?.detail ||
      'Failed to subscribe to competitor alerts'
    );
  }
};

/**
 * Unsubscribe from competitor alerts
 * @param {string} competitorId - The competitor's ID
 * @param {string} subscriptionId - Optional subscription ID to unsubscribe from specific alert
 * @returns {Promise<Object>} Unsubscription confirmation
 */
export const unsubscribeFromCompetitorAlerts = async (competitorId, subscriptionId = null) => {
  try {
    const url = subscriptionId
      ? `/api/competitor-analytics/competitors/${competitorId}/alerts/unsubscribe/${subscriptionId}`
      : `/api/competitor-analytics/competitors/${competitorId}/alerts/unsubscribe`;

    const response = await api.delete(url);
    return response.data;
  } catch (error) {
    console.error('Error unsubscribing from competitor alerts:', error);
    throw new Error(
      error.response?.data?.detail ||
      'Failed to unsubscribe from competitor alerts'
    );
  }
};

export default {
  getCompetitorMetrics,
  compareCompetitors,
  refreshCompetitorAnalytics,
  getIndustryBenchmarks,
  getCompetitorInsights,
  analyzeCompetitorContent,
  getAnalyticsHealth,
  batchFetchCompetitorMetrics,
  getCompetitorTrends,
  exportCompetitorData,
  getCompetitorAlerts,
  setupCompetitorAlerts,
  subscribeToCompetitorAlerts,
  unsubscribeFromCompetitorAlerts,
  formatMetricsForDisplay
};

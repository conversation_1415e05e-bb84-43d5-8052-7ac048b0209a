// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
  Tabs,
  Tab,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Divider,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import BarChartIcon from '@mui/icons-material/BarChart';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';

import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

const AnalyticsOverview = () => {
  const navigate = useNavigate();
  const { showErrorNotification } = useNotification();

  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [dateRange, setDateRange] = useState('30days');
  const [analyticsData, setAnalyticsData] = useState(null);
  const [abTestCampaigns, setAbTestCampaigns] = useState([]);

  // Load analytics data
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      setLoading(true);

      try {
        // Fetch A/B test campaigns
        const abTestResponse = await api.get('/api/campaigns/ab-test');
        setAbTestCampaigns(abTestResponse.data);

        // Real API call to fetch analytics data
        const analyticsResponse = await api.get('/api/analytics/overview');
        setAnalyticsData(analyticsResponse.data);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        showErrorNotification('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [dateRange, showErrorNotification]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle date range change
  const handleDateRangeChange = (event) => {
    setDateRange(event.target.value);
  };

  // Navigate to campaign comparison
  const handleCompareCampaigns = () => {
    navigate('/analytics/campaign-comparison');
  };

  return (
    <Box sx={{ py: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          <BarChartIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Analytics Overview
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <FormControl sx={{ minWidth: 150, mr: 2 }}>
            <InputLabel>Date Range</InputLabel>
            <Select
              value={dateRange}
              onChange={handleDateRangeChange}
              label="Date Range"
              size="small"
            >
              <MenuItem value="7days">Last 7 Days</MenuItem>
              <MenuItem value="30days">Last 30 Days</MenuItem>
              <MenuItem value="90days">Last 90 Days</MenuItem>
              <MenuItem value="year">Last Year</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="contained"
            color="primary"
            startIcon={<CompareArrowsIcon />}
            onClick={handleCompareCampaigns}
          >
            Compare Campaigns
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Box sx={{ width: '100%', mt: 4, mb: 4 }}>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 2, textAlign: 'center' }}>
            Loading analytics data...
          </Typography>
        </Box>
      ) : (
        <>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="analytics tabs">
              <Tab label="Overview" />
              <Tab label="A/B Testing" />
              <Tab label="Campaign Performance" />
              <Tab label="Content Performance" />
            </Tabs>
          </Box>


          {tabValue === 0 && analyticsData && (
            <Grid container spacing={3}>

              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {analyticsData.totalImpressions.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Impressions
                  </Typography>
                </Paper>
              </Grid>

              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {analyticsData.totalEngagements.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Engagements
                  </Typography>
                </Paper>
              </Grid>

              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {analyticsData.engagementRate.toFixed(2)}%
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Engagement Rate
                  </Typography>
                </Paper>
              </Grid>


              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <TrendingUpIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Performance Over Time
                    </Typography>

                    <Box sx={{ height: 400, mt: 2, position: 'relative' }}>
                      {analyticsData?.timeSeriesData ? (
                        <Box
                          sx={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            borderRadius: 2,
                            color: 'white',
                            flexDirection: 'column'
                          }}
                        >
                          <TrendingUpIcon sx={{ fontSize: 60, mb: 2, opacity: 0.8 }} />
                          <Typography variant="h6" gutterBottom>
                            Interactive Analytics Chart
                          </Typography>
                          <Typography variant="body2" sx={{ opacity: 0.9, textAlign: 'center', maxWidth: 300 }}>
                            Advanced time-series visualization showing engagement trends,
                            reach metrics, and performance indicators over time.
                          </Typography>
                          <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                                {analyticsData.totalEngagement?.toLocaleString() || '0'}
                              </Typography>
                              <Typography variant="caption">Total Engagement</Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                                {analyticsData.totalReach?.toLocaleString() || '0'}
                              </Typography>
                              <Typography variant="caption">Total Reach</Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                                {analyticsData.conversionRate || '0'}%
                              </Typography>
                              <Typography variant="caption">Conversion Rate</Typography>
                            </Box>
                          </Box>
                        </Box>
                      ) : (
                        <Box
                          sx={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            border: '2px dashed',
                            borderColor: 'divider',
                            borderRadius: 2,
                            flexDirection: 'column'
                          }}
                        >
                          <BarChartIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                          <Typography variant="h6" color="text.secondary" gutterBottom>
                            No Data Available
                          </Typography>
                          <Typography variant="body2" color="text.secondary" textAlign="center">
                            Analytics data will appear here once you have sufficient activity.
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}


          {tabValue === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                A/B Testing Campaigns
              </Typography>

              {abTestCampaigns.length === 0 ? (
                <Card>
                  <CardContent sx={{ textAlign: 'center', py: 5 }}>
                    <CompareArrowsIcon sx={{ fontSize: 60, color: 'primary.light', mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      No A/B Tests Found
                    </Typography>
                    <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                      Create your first A/B test to compare different campaign variations.
                    </Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => navigate('/campaigns')}
                    >
                      View Campaigns
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <Grid container spacing={3}>
                  {abTestCampaigns.map((campaign) => (
                    <Grid item xs={12} md={6} key={campaign.id}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            {campaign.ab_test_name || campaign.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary" paragraph>
                            {campaign.ab_test_description || campaign.description}
                          </Typography>

                          <Divider sx={{ my: 2 }} />

                          <Typography variant="subtitle2" gutterBottom>
                            Variants: {campaign.ab_test_variants.length}
                          </Typography>

                          <Button
                            variant="contained"
                            color="primary"
                            onClick={() => navigate(`/analytics/campaign-comparison/${campaign.id}`)}
                            sx={{ mt: 2 }}
                          >
                            View Comparison
                          </Button>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Box>
          )}

          {tabValue === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Advanced Analytics
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Detailed analytics and custom reports will be available here.
              </Typography>
              <Button
                variant="outlined"
                startIcon={<BarChartIcon />}
                onClick={() => navigate('/analytics/reports')}
                sx={{ mt: 2 }}
              >
                View Detailed Reports
              </Button>
            </Box>
          )}
        </>
      )}
    </Box>
  );
};

export default AnalyticsOverview;

/**
 * Enhanced AppSumo Bulk Code Generator - Enterprise-grade bulk code generation component
 * Features: Comprehensive bulk code generation with advanced batch processing, progress tracking,
 * and error handling for AppSumo lifetime deal codes, detailed bulk generation customization with
 * dynamic batch sizes and tier-specific generation, advanced bulk generation features with validation
 * checks and duplicate prevention, ACE Social's AppSumo system integration with seamless bulk code
 * lifecycle management, bulk generation interaction features including progress monitoring and batch
 * cancellation, bulk generation state management with real-time progress updates and validation
 * checks, real-time bulk generation updates with live progress displays and dynamic status messaging,
 * and seamless ACE Social AppSumo platform integration with advanced generation orchestration and
 * comprehensive accessibility compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Stack,
  Paper,
  Collapse,
  Divider,
  alpha,
  useTheme
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  ContentCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Settings as SettingsIcon,
  Visibility as VisibilityIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { validateFormData, generateAppSumoCode, exportToCSV, copyToClipboard } from '../../utils/appsumoHelpers';
import api from '../../api';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Bulk generation states
const GENERATION_STATES = {
  IDLE: 'idle',
  CONFIGURING: 'configuring',
  GENERATING: 'generating',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  ERROR: 'error',
  CANCELLED: 'cancelled'
};

// Generation steps
const GENERATION_STEPS = {
  CONFIGURE: 0,
  GENERATE: 1,
  REVIEW: 2
};

// Batch processing settings
const BATCH_SETTINGS = {
  DEFAULT_SIZE: 50,
  MIN_SIZE: 10,
  MAX_SIZE: 100,
  DELAY_MS: 100
};

/**
 * Enhanced AppSumo Bulk Code Generator - Comprehensive bulk code generation with advanced features
 * Implements detailed bulk generation management and enterprise-grade generation capabilities
 */
const EnhancedBulkCodeGenerator = memo(forwardRef(({
  open,
  onClose,
  deals = [],
  tiers = [],
  onCodesGenerated,
  batchSize = BATCH_SETTINGS.DEFAULT_SIZE,
  maxCodes = 1000,
  enableAdvancedOptions = true,
  enableRealTimeProgress = true,
  enableAccessibility = true,
  enableAnalytics = true,
  onGenerationStart,
  onGenerationProgress,
  onGenerationComplete,
  onAnalyticsTrack,
  ...props
}, ref) => {
  const theme = useTheme();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const dialogRef = useRef(null);
  const formRef = useRef(null);
  const progressRef = useRef(null);

  // Enhanced state management
  const [activeStep, setActiveStep] = useState(GENERATION_STEPS.CONFIGURE);
  const [generationState, setGenerationState] = useState(GENERATION_STATES.IDLE);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [formData, setFormData] = useState({
    deal_id: '',
    tier_type: '',
    quantity: 10,
    prefix: 'AS',
    code_length: 8,
    expires_at: '',
    batch_size: batchSize,
    validation_enabled: true,
    duplicate_check: true
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [generatedCodes, setGeneratedCodes] = useState([]);
  const [progress, setProgress] = useState(0);
  const [currentBatch, setCurrentBatch] = useState(0);
  const [totalBatches, setTotalBatches] = useState(0);
  const [generationAnalytics, setGenerationAnalytics] = useState({
    startTime: null,
    endTime: null,
    totalCodes: 0,
    successfulCodes: 0,
    failedCodes: 0,
    averageTimePerBatch: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    startGeneration: () => handleNext(),
    pauseGeneration: () => handlePauseGeneration(),
    cancelGeneration: () => handleCancelGeneration(),
    exportCodes: () => handleExport(),
    getGenerationState: () => generationState,
    getProgress: () => progress,
    getGeneratedCodes: () => generatedCodes,
    resetGenerator: () => handleReset()
  }), [generationState, progress, generatedCodes]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced effects for component lifecycle
  useEffect(() => {
    if (open) {
      setGenerationAnalytics(prev => ({
        ...prev,
        startTime: new Date().toISOString()
      }));

      if (enableAccessibility) {
        announceToScreenReader('Bulk code generator opened');
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack('bulk_generator_opened', {
          timestamp: new Date().toISOString()
        });
      }
    }
  }, [open, enableAccessibility, announceToScreenReader, enableAnalytics, onAnalyticsTrack]);

  // Enhanced handlers
  const handlePauseGeneration = useCallback(() => {
    setGenerationState(GENERATION_STATES.PAUSED);
    setLoading(false);

    if (enableAccessibility) {
      announceToScreenReader('Code generation paused');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleCancelGeneration = useCallback(() => {
    setGenerationState(GENERATION_STATES.CANCELLED);
    setLoading(false);
    setProgress(0);
    setCurrentBatch(0);

    if (enableAccessibility) {
      announceToScreenReader('Code generation cancelled');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleReset = useCallback(() => {
    setActiveStep(GENERATION_STEPS.CONFIGURE);
    setGenerationState(GENERATION_STATES.IDLE);
    setFormData({
      deal_id: '',
      tier_type: '',
      quantity: 10,
      prefix: 'AS',
      code_length: 8,
      expires_at: '',
      batch_size: batchSize,
      validation_enabled: true,
      duplicate_check: true
    });
    setErrors({});
    setGeneratedCodes([]);
    setProgress(0);
    setCurrentBatch(0);
    setTotalBatches(0);
  }, [batchSize]);

  const steps = [
    'Configure Generation',
    'Generate Codes',
    'Review & Export',
  ];

  // Form validation rules
  const validationRules = [
    { key: 'deal_id', label: 'Deal', type: 'string' },
    { key: 'tier_type', label: 'Tier Type', type: 'string' },
    { key: 'quantity', label: 'Quantity', type: 'number', min: 1, max: 1000 },
    { key: 'prefix', label: 'Prefix', type: 'string', minLength: 2, maxLength: 5 },
    { key: 'code_length', label: 'Code Length', type: 'number', min: 6, max: 20 },
  ];

  // Enhanced form field change handler
  const handleFieldChange = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Update batch calculations
    if (field === 'quantity') {
      const newTotalBatches = Math.ceil(value / formData.batch_size);
      setTotalBatches(newTotalBatches);
    }

    if (enableAccessibility) {
      announceToScreenReader(`${field} updated to ${value}`);
    }
  }, [errors, formData.batch_size, enableAccessibility, announceToScreenReader]);

  // Validate current step
  const validateStep = (step) => {
    if (step === 0) {
      const validation = validateFormData(formData, validationRules);
      setErrors(validation.errors);
      return validation.isValid;
    }
    return true;
  };

  // Handle next step
  const handleNext = () => {
    if (validateStep(activeStep)) {
      if (activeStep === 0) {
        generateCodes();
      } else {
        setActiveStep(prev => prev + 1);
      }
    }
  };

  // Handle previous step
  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // Enhanced code generation with advanced features
  const generateCodes = useCallback(async () => {
    setLoading(true);
    setProgress(0);
    setActiveStep(GENERATION_STEPS.GENERATE);
    setGenerationState(GENERATION_STATES.GENERATING);

    const startTime = Date.now();
    setGenerationAnalytics(prev => ({
      ...prev,
      startTime: new Date().toISOString(),
      totalCodes: formData.quantity
    }));

    if (onGenerationStart) {
      onGenerationStart(formData);
    }

    if (enableAccessibility) {
      announceToScreenReader(`Starting generation of ${formData.quantity} codes`);
    }

    try {
      const codes = [];
      const effectiveBatchSize = formData.batch_size || BATCH_SETTINGS.DEFAULT_SIZE;
      const calculatedTotalBatches = Math.ceil(formData.quantity / effectiveBatchSize);
      setTotalBatches(calculatedTotalBatches);

      for (let batch = 0; batch < calculatedTotalBatches; batch++) {
        // Check if generation was cancelled or paused
        if (generationState === GENERATION_STATES.CANCELLED) {
          break;
        }

        setCurrentBatch(batch + 1);
        const batchStart = batch * effectiveBatchSize;
        const batchEnd = Math.min(batchStart + effectiveBatchSize, formData.quantity);
        const batchCodes = [];

        // Generate codes for this batch with validation
        for (let i = batchStart; i < batchEnd; i++) {
          const code = generateAppSumoCode(formData.prefix, formData.code_length);

          // Duplicate check if enabled
          if (formData.duplicate_check) {
            const isDuplicate = codes.some(existingCode => existingCode.code === code);
            if (isDuplicate) {
              continue; // Skip duplicate and try again
            }
          }

          batchCodes.push({
            code,
            deal_id: formData.deal_id,
            tier_type: formData.tier_type,
            expires_at: formData.expires_at || null,
            status: 'active',
            is_redeemed: false,
            created_at: new Date().toISOString(),
            batch_number: batch + 1
          });
        }

        // Send batch to API with enhanced error handling
        try {
          const response = await api.post('/api/appsumo/bulk-generate-codes', {
            codes: batchCodes,
            deal_id: formData.deal_id,
            tier_type: formData.tier_type,
            batch_info: {
              batch_number: batch + 1,
              total_batches: calculatedTotalBatches,
              validation_enabled: formData.validation_enabled
            }
          });

          codes.push(...response.data.codes);

          setGenerationAnalytics(prev => ({
            ...prev,
            successfulCodes: prev.successfulCodes + batchCodes.length
          }));

        } catch (batchError) {
          console.error(`Error in batch ${batch + 1}:`, batchError);
          setGenerationAnalytics(prev => ({
            ...prev,
            failedCodes: prev.failedCodes + batchCodes.length
          }));
        }

        // Update progress with enhanced tracking
        const progressPercent = ((batch + 1) / calculatedTotalBatches) * 100;
        setProgress(progressPercent);

        if (onGenerationProgress) {
          onGenerationProgress({
            progress: progressPercent,
            currentBatch: batch + 1,
            totalBatches: calculatedTotalBatches,
            codesGenerated: codes.length
          });
        }

        if (enableAccessibility) {
          announceToScreenReader(`Batch ${batch + 1} of ${calculatedTotalBatches} completed`);
        }

        // Configurable delay for UX
        if (batch < calculatedTotalBatches - 1) {
          await new Promise(resolve => setTimeout(resolve, BATCH_SETTINGS.DELAY_MS));
        }
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      setGeneratedCodes(codes);
      setActiveStep(GENERATION_STEPS.REVIEW);
      setGenerationState(GENERATION_STATES.COMPLETED);

      setGenerationAnalytics(prev => ({
        ...prev,
        endTime: new Date().toISOString(),
        averageTimePerBatch: totalTime / calculatedTotalBatches
      }));

      if (enableAccessibility) {
        announceToScreenReader(`Generation completed. ${codes.length} codes generated successfully`);
      }

      if (onGenerationComplete) {
        onGenerationComplete(codes, {
          totalTime,
          successfulCodes: codes.length,
          failedCodes: generationAnalytics.failedCodes
        });
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack('bulk_generation_completed', {
          totalCodes: codes.length,
          totalTime,
          batchSize: effectiveBatchSize,
          timestamp: new Date().toISOString()
        });
      }

      // Notify parent component
      if (onCodesGenerated) {
        onCodesGenerated(codes);
      }

    } catch (error) {
      console.error('Error generating codes:', error);
      setGenerationState(GENERATION_STATES.ERROR);
      setErrors({
        generation: error.response?.data?.detail || 'Failed to generate codes'
      });
      setActiveStep(GENERATION_STEPS.CONFIGURE);

      if (enableAccessibility) {
        announceToScreenReader('Code generation failed');
      }
    } finally {
      setLoading(false);
    }
  }, [
    formData,
    generationState,
    generationAnalytics.failedCodes,
    onGenerationStart,
    onGenerationProgress,
    onGenerationComplete,
    onCodesGenerated,
    enableAccessibility,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // Handle export
  const handleExport = () => {
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'deal_id', label: 'Deal ID' },
      { key: 'tier_type', label: 'Tier Type' },
      { key: 'status', label: 'Status' },
      { key: 'created_at', label: 'Created At', type: 'date' },
      { key: 'expires_at', label: 'Expires At', type: 'date' },
    ];
    
    exportToCSV(
      generatedCodes, 
      `appsumo-codes-${formData.deal_id}-${Date.now()}`, 
      exportColumns
    );
  };

  // Handle copy all codes
  const handleCopyAll = async () => {
    const codesList = generatedCodes.map(code => code.code).join('\n');
    const success = await copyToClipboard(codesList);
    
    if (success) {
      // Could show a snackbar here
      console.log('Codes copied to clipboard');
    }
  };

  // Handle close
  const handleClose = () => {
    setActiveStep(0);
    setFormData({
      deal_id: '',
      tier_type: '',
      quantity: 10,
      prefix: 'AS',
      code_length: 8,
      expires_at: '',
    });
    setErrors({});
    setGeneratedCodes([]);
    setProgress(0);
    onClose();
  };

  return (
    <Dialog
      ref={dialogRef}
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          ...glassMorphismStyles,
          minHeight: 600,
          position: 'relative',
          overflow: 'hidden'
        }
      }}
      {...props}
    >
      {/* Enhanced Progress Indicator */}
      {loading && enableRealTimeProgress && (
        <LinearProgress
          variant="determinate"
          value={progress}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2),
            '& .MuiLinearProgress-bar': {
              backgroundColor: ACE_COLORS.PURPLE
            }
          }}
        />
      )}

      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" alignItems="center" spacing={2}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 40,
                height: 40,
                borderRadius: 1,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE
              }}
            >
              <PlayArrowIcon />
            </Box>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK }}>
                Enhanced Bulk Code Generator
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Generate AppSumo lifetime deal codes in batches
              </Typography>
            </Box>
          </Stack>

          <Stack direction="row" spacing={1}>
            {/* Generation State Indicator */}
            <Chip
              label={generationState.toUpperCase()}
              size="small"
              color={
                generationState === GENERATION_STATES.COMPLETED ? 'success' :
                generationState === GENERATION_STATES.ERROR ? 'error' :
                generationState === GENERATION_STATES.GENERATING ? 'primary' :
                'default'
              }
              variant="outlined"
            />

            {/* Advanced Options Toggle */}
            {enableAdvancedOptions && (
              <Tooltip title="Toggle advanced options">
                <IconButton
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  size="small"
                  sx={{
                    backgroundColor: showAdvancedOptions ? alpha(ACE_COLORS.PURPLE, 0.1) : 'transparent'
                  }}
                >
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
            )}

            <IconButton onClick={handleClose} size="small">
              <CloseIcon />
            </IconButton>
          </Stack>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stepper activeStep={activeStep} orientation="vertical">
          {/* Step 1: Configure Generation */}
          <Step>
            <StepLabel>Configure Generation</StepLabel>
            <StepContent>
              <Box display="flex" flexDirection="column" gap={3} mt={2}>
                {errors.generation && (
                  <Alert severity="error">{errors.generation}</Alert>
                )}
                
                <Box display="flex" gap={2}>
                  <FormControl fullWidth error={!!errors.deal_id}>
                    <InputLabel>Deal</InputLabel>
                    <Select
                      value={formData.deal_id}
                      onChange={(e) => handleFieldChange('deal_id', e.target.value)}
                      label="Deal"
                    >
                      {deals.map((deal) => (
                        <MenuItem key={deal.id} value={deal.deal_id}>
                          {deal.name} ({deal.deal_id})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl fullWidth error={!!errors.tier_type}>
                    <InputLabel>Tier Type</InputLabel>
                    <Select
                      value={formData.tier_type}
                      onChange={(e) => handleFieldChange('tier_type', e.target.value)}
                      label="Tier Type"
                    >
                      {tiers.map((tier) => (
                        <MenuItem key={tier.id} value={tier.tier_type}>
                          {tier.name} ({tier.tier_type})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                <Box display="flex" gap={2}>
                  <TextField
                    label="Quantity"
                    type="number"
                    value={formData.quantity}
                    onChange={(e) => handleFieldChange('quantity', parseInt(e.target.value) || 0)}
                    error={!!errors.quantity}
                    helperText={errors.quantity || 'Number of codes to generate (1-1000)'}
                    inputProps={{ min: 1, max: 1000 }}
                    fullWidth
                  />

                  <TextField
                    label="Prefix"
                    value={formData.prefix}
                    onChange={(e) => handleFieldChange('prefix', e.target.value.toUpperCase())}
                    error={!!errors.prefix}
                    helperText={errors.prefix || 'Code prefix (2-5 characters)'}
                    inputProps={{ maxLength: 5 }}
                    fullWidth
                  />

                  <TextField
                    label="Code Length"
                    type="number"
                    value={formData.code_length}
                    onChange={(e) => handleFieldChange('code_length', parseInt(e.target.value) || 8)}
                    error={!!errors.code_length}
                    helperText={errors.code_length || 'Length after prefix (6-20)'}
                    inputProps={{ min: 6, max: 20 }}
                    fullWidth
                  />
                </Box>

                <TextField
                  label="Expiration Date (Optional)"
                  type="datetime-local"
                  value={formData.expires_at}
                  onChange={(e) => handleFieldChange('expires_at', e.target.value)}
                  InputLabelProps={{ shrink: true }}
                  helperText="Leave empty for no expiration"
                  fullWidth
                />

                <Box mt={2}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Preview: {formData.prefix}-{Array(formData.code_length).fill('X').join('')}
                  </Typography>
                </Box>
              </Box>
            </StepContent>
          </Step>

          {/* Step 2: Generate Codes */}
          <Step>
            <StepLabel>Generate Codes</StepLabel>
            <StepContent>
              <Box py={3}>
                <Typography variant="body1" gutterBottom>
                  Generating {formData.quantity} codes...
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={progress} 
                  sx={{ mt: 2, height: 8, borderRadius: 4 }}
                />
                <Typography variant="body2" color="text.secondary" mt={1}>
                  {Math.round(progress)}% complete
                </Typography>
              </Box>
            </StepContent>
          </Step>

          {/* Step 3: Review & Export */}
          <Step>
            <StepLabel>Review & Export</StepLabel>
            <StepContent>
              <Box py={2}>
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <CheckCircleIcon />
                    Successfully generated {generatedCodes.length} codes
                  </Box>
                </Alert>

                <Box display="flex" gap={2} mb={2}>
                  <Button
                    variant="outlined"
                    startIcon={<DownloadIcon />}
                    onClick={handleExport}
                  >
                    Export CSV
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<CopyIcon />}
                    onClick={handleCopyAll}
                  >
                    Copy All
                  </Button>
                </Box>

                <Typography variant="body2" color="text.secondary">
                  Codes have been saved to the database and are ready for distribution.
                </Typography>
              </Box>
            </StepContent>
          </Step>
        </Stepper>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {activeStep === 2 ? 'Close' : 'Cancel'}
        </Button>
        {activeStep > 0 && activeStep < 2 && (
          <Button onClick={handleBack} disabled={loading}>
            Back
          </Button>
        )}
        {activeStep === 0 && (
          <Button 
            onClick={handleNext} 
            variant="contained"
            disabled={loading}
          >
            Generate Codes
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
}));

EnhancedBulkCodeGenerator.displayName = 'EnhancedBulkCodeGenerator';

EnhancedBulkCodeGenerator.propTypes = {
  /** Dialog open state */
  open: PropTypes.bool.isRequired,
  /** Close dialog callback */
  onClose: PropTypes.func.isRequired,
  /** Available AppSumo deals */
  deals: PropTypes.arrayOf(PropTypes.object),
  /** Available tier types */
  tiers: PropTypes.arrayOf(PropTypes.object),
  /** Codes generated callback */
  onCodesGenerated: PropTypes.func,
  /** Batch size for generation */
  batchSize: PropTypes.number,
  /** Maximum codes allowed */
  maxCodes: PropTypes.number,
  /** Enable advanced options */
  enableAdvancedOptions: PropTypes.bool,
  /** Enable real-time progress */
  enableRealTimeProgress: PropTypes.bool,
  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Generation start callback */
  onGenerationStart: PropTypes.func,
  /** Generation progress callback */
  onGenerationProgress: PropTypes.func,
  /** Generation complete callback */
  onGenerationComplete: PropTypes.func,
  /** Analytics tracking callback */
  onAnalyticsTrack: PropTypes.func
};

export default EnhancedBulkCodeGenerator;

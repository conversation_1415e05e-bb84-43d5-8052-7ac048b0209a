"""
E-commerce Billing API Routes for ACEO Platform.

This module provides API endpoints for:
- E-commerce add-on management
- Usage tracking and limits
- Billing integration
- Feature access validation
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, Optional
import logging

from app.models.user import User
from app.services.ecommerce_addon_service import ecommerce_addon_service
from app.services.usage_tracking_service import usage_tracking_service
from app.services.feature_access import check_and_consume_feature_usage
from app.core.monitoring import monitoring

# Import dependencies with proper error handling
import importlib

# Auth module fallback
def get_current_user():
    """Get current user with fallback handling."""
    try:
        auth_module = importlib.import_module("app.core.auth")
        return auth_module.get_current_user()
    except ImportError:
        raise HTTPException(status_code=500, detail="Authentication module not available")

# Billing service fallback
class BillingService:
    """Billing service with fallback handling."""
    def __init__(self):
        try:
            billing_module = importlib.import_module("app.services.billing")
            self._service = billing_module.BillingService()
        except ImportError:
            self._service = None

    def get_addon_products(self):
        if self._service:
            return self._service.get_addon_products()
        return []

    async def create_addon_checkout_session(self, **kwargs):
        if self._service:
            return await self._service.create_addon_checkout_session(**kwargs)
        return {"success": False, "error": "Billing service not available"}

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/ecommerce/billing", tags=["E-commerce Billing"])


@router.get("/limits")
async def get_ecommerce_limits(current_user: User = Depends(get_current_user)):
    """Get comprehensive e-commerce limits for the current user."""
    try:
        limits = await ecommerce_addon_service.get_user_ecommerce_limits(str(current_user.id))
        
        if "error" in limits:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=limits["error"]
            )
        
        return {
            "success": True,
            "data": limits
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting e-commerce limits: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal error getting e-commerce limits"
        )


@router.get("/usage-summary")
async def get_usage_summary(current_user: User = Depends(get_current_user)):
    """Get comprehensive usage summary for the current user."""
    try:
        summary = await usage_tracking_service.get_user_usage_summary(str(current_user.id))
        
        if "error" in summary:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=summary["error"]
            )
        
        return {
            "success": True,
            "data": summary
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting usage summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal error getting usage summary"
        )


@router.post("/check-feature-access")
async def check_feature_access(
    feature_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Check if user has access to a specific e-commerce feature."""
    try:
        feature_name = feature_data.get("feature_name")
        usage_amount = feature_data.get("usage_amount", 1)
        
        if not feature_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Feature name is required"
            )
        
        access_result = await ecommerce_addon_service.check_ecommerce_feature_access(
            str(current_user.id), feature_name, usage_amount
        )
        
        return {
            "success": True,
            "data": access_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking feature access: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal error checking feature access"
        )


@router.post("/consume-usage")
async def consume_usage(
    usage_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Consume usage for an e-commerce feature."""
    try:
        feature_name = usage_data.get("feature_name")
        usage_amount = usage_data.get("usage_amount", 1)
        metadata = usage_data.get("metadata", {})
        
        if not feature_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Feature name is required"
            )
        
        # Use the comprehensive usage tracking service
        result = await check_and_consume_feature_usage(
            current_user, feature_name, usage_amount, metadata
        )
        
        if not result.get("allowed", False):
            error_code = result.get("error_code", "ACCESS_DENIED")
            error_message = result.get("error", "Feature access denied")
            
            # Map error codes to appropriate HTTP status codes
            status_code_map = {
                "USER_NOT_FOUND": status.HTTP_404_NOT_FOUND,
                "NO_SUBSCRIPTION": status.HTTP_402_PAYMENT_REQUIRED,
                "FEATURE_NOT_AVAILABLE": status.HTTP_403_FORBIDDEN,
                "USAGE_LIMIT_EXCEEDED": status.HTTP_429_TOO_MANY_REQUESTS,
                "ECOMMERCE_ACCESS_DENIED": status.HTTP_402_PAYMENT_REQUIRED,
                "INSUFFICIENT_CREDITS": status.HTTP_402_PAYMENT_REQUIRED
            }
            
            http_status = status_code_map.get(error_code, status.HTTP_403_FORBIDDEN)
            
            raise HTTPException(
                status_code=http_status,
                detail={
                    "error": error_message,
                    "error_code": error_code,
                    "requires_upgrade": result.get("requires_upgrade", False),
                    "recommended_addon": result.get("recommended_addon"),
                    "current_usage": result.get("current_usage"),
                    "limit": result.get("limit")
                }
            )
        
        return {
            "success": True,
            "data": {
                "consumed": True,
                "remaining_credits": result.get("remaining_credits"),
                "current_usage": result.get("current_usage"),
                "limit": result.get("limit"),
                "feature_type": result.get("feature_type")
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error consuming usage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal error consuming usage"
        )


@router.get("/available-addons")
async def get_available_ecommerce_addons(current_user: User = Depends(get_current_user)):
    """Get available e-commerce add-ons for purchase."""
    try:
        billing_service = BillingService()
        all_addons = billing_service.get_addon_products()
        
        # Filter for e-commerce add-ons
        ecommerce_addons = [
            addon for addon in all_addons 
            if addon.get("category") == "ecommerce"
        ]
        
        return {
            "success": True,
            "data": ecommerce_addons
        }
        
    except Exception as e:
        logger.error(f"Error getting available e-commerce add-ons: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal error getting available add-ons"
        )


@router.post("/purchase-addon")
async def purchase_ecommerce_addon(
    purchase_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
):
    """Initiate purchase of an e-commerce add-on."""
    try:
        addon_id = purchase_data.get("addon_id")
        package_option = purchase_data.get("package_option")
        
        if not addon_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Add-on ID is required"
            )
        
        billing_service = BillingService()
        
        # Create checkout session for the add-on
        checkout_result = await billing_service.create_addon_checkout_session(
            user_id=str(current_user.id),
            addon_id=addon_id,
            package_option=package_option
        )
        
        if not checkout_result.get("success", False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=checkout_result.get("error", "Failed to create checkout session")
            )
        
        # Record add-on purchase attempt for monitoring
        monitoring.record_addon_purchase(
            addon_id=addon_id,
            variant=package_option or "default",
            subscription_tier=current_user.subscription.plan_id if current_user.subscription else "none",
            revenue=0  # Will be updated on successful payment
        )
        
        return {
            "success": True,
            "data": {
                "checkout_url": checkout_result.get("checkout_url"),
                "session_id": checkout_result.get("session_id")
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error purchasing e-commerce add-on: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal error processing add-on purchase"
        )


@router.get("/active-addons")
async def get_active_ecommerce_addons(current_user: User = Depends(get_current_user)):
    """Get user's active e-commerce add-ons."""
    try:
        from datetime import datetime, timezone
        from bson import ObjectId

        # Import database with fallback
        try:
            database_module = importlib.import_module("app.core.database")
            mongodb = database_module.mongodb
        except ImportError:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database connection not available"
            )
        
        # Get active e-commerce add-ons
        active_addons = await mongodb.user_addons.find({
            "user_id": ObjectId(current_user.id),
            "addon_id": {"$in": [
                "ecommerce_store_connections",
                "ecommerce_product_content",
                "ecommerce_icp_generation", 
                "ecommerce_campaign_management"
            ]},
            "status": "active",
            "$or": [
                {"expires_at": {"$gt": datetime.now(timezone.utc)}},
                {"expires_at": None}  # Permanent add-ons
            ]
        }).to_list(None)
        
        # Format response
        formatted_addons = []
        for addon in active_addons:
            formatted_addons.append({
                "id": str(addon["_id"]),
                "addon_id": addon["addon_id"],
                "name": addon["name"],
                "quantity": addon.get("quantity", 0),
                "remaining_uses": addon.get("remaining_uses"),
                "purchased_at": addon["purchased_at"].isoformat(),
                "expires_at": addon["expires_at"].isoformat() if addon.get("expires_at") else None,
                "status": addon["status"]
            })
        
        return {
            "success": True,
            "data": formatted_addons
        }
        
    except Exception as e:
        logger.error(f"Error getting active e-commerce add-ons: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal error getting active add-ons"
        )

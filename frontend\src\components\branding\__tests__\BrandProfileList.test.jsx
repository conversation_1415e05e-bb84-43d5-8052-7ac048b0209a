/**
 * Tests for BrandProfileList component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandProfileList from '../BrandProfileList';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('BrandProfileList', () => {
  const mockProfiles = [
    {
      id: 'profile-1',
      name: 'Brand Profile 1',
      is_default: true,
      color_palette: {
        primary: '#4E40C5',
        secondary: '#00E4BC'
      }
    },
    {
      id: 'profile-2',
      name: 'Brand Profile 2',
      is_default: false,
      color_palette: {
        primary: '#FF5733',
        secondary: '#FFC300'
      }
    },
    {
      id: 'profile-3',
      name: 'Brand Profile 3',
      is_default: false,
      color_palette: {
        primary: '#8E44AD',
        secondary: '#3498DB'
      }
    }
  ];

  const mockProps = {
    profiles: mockProfiles,
    selectedProfileId: 'profile-1',
    onSelect: vi.fn(),
    onCreateNew: vi.fn(),
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onSetDefault: vi.fn(),
    onError: vi.fn(),
    onDuplicate: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders brand profile list correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Profiles')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument(); // Profile count chip
    expect(screen.getByText('Brand Profile 1')).toBeInTheDocument();
    expect(screen.getByText('Brand Profile 2')).toBeInTheDocument();
    expect(screen.getByText('Brand Profile 3')).toBeInTheDocument();
  });

  test('shows empty state when no profiles', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} profiles={[]} />
      </TestWrapper>
    );

    expect(screen.getByText('No brand profiles yet')).toBeInTheDocument();
    expect(screen.getByText('Create Your First Profile')).toBeInTheDocument();
  });

  test('shows loading state when loading', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows error state when error is provided', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} error="Failed to load profiles" />
      </TestWrapper>
    );

    expect(screen.getByText('Failed to load profiles')).toBeInTheDocument();
  });

  test('highlights selected profile correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} selectedProfileId="profile-2" />
      </TestWrapper>
    );

    const selectedProfile = screen.getByText('Brand Profile 2').closest('button');
    expect(selectedProfile).toHaveClass('Mui-selected');
  });

  test('shows default badge for default profile', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Default')).toBeInTheDocument();
  });

  test('calls onSelect when profile is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    const profile2 = screen.getByText('Brand Profile 2');
    await user.click(profile2);

    expect(mockProps.onSelect).toHaveBeenCalledWith('profile-2');
  });

  test('calls onCreateNew when create button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    const createButton = screen.getByLabelText('Create new brand profile');
    await user.click(createButton);

    expect(mockProps.onCreateNew).toHaveBeenCalled();
  });

  test('calls onCreateNew when empty state button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} profiles={[]} />
      </TestWrapper>
    );

    const createButton = screen.getByText('Create Your First Profile');
    await user.click(createButton);

    expect(mockProps.onCreateNew).toHaveBeenCalled();
  });

  test('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    const editButton = screen.getByLabelText('Edit Brand Profile 1 profile');
    await user.click(editButton);

    expect(mockProps.onEdit).toHaveBeenCalledWith(mockProfiles[0]);
  });

  test('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    const deleteButton = screen.getByLabelText('Delete Brand Profile 1 profile');
    await user.click(deleteButton);

    expect(mockProps.onDelete).toHaveBeenCalledWith(mockProfiles[0]);
  });

  test('calls onSetDefault when set default button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    const setDefaultButton = screen.getByLabelText('Set Brand Profile 2 as default profile');
    await user.click(setDefaultButton);

    expect(mockProps.onSetDefault).toHaveBeenCalledWith('profile-2');
  });

  test('calls onDuplicate when duplicate button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    const duplicateButton = screen.getByLabelText('Duplicate Brand Profile 1 profile');
    await user.click(duplicateButton);

    expect(mockProps.onDuplicate).toHaveBeenCalledWith(mockProfiles[0]);
  });

  test('hides set default button for default profile', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Set Brand Profile 1 as default profile')).not.toBeInTheDocument();
  });

  test('hides action buttons when showActions is false', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} showActions={false} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Edit Brand Profile 1 profile')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Delete Brand Profile 1 profile')).not.toBeInTheDocument();
  });

  test('disables interactions when disabled prop is true', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} disabled={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Create new brand profile')).toBeDisabled();
    expect(screen.getByLabelText('Edit Brand Profile 1 profile')).toBeDisabled();
    expect(screen.getByLabelText('Delete Brand Profile 1 profile')).toBeDisabled();
  });

  test('disables edit and delete when readOnly prop is true', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Edit Brand Profile 1 profile')).toBeDisabled();
    expect(screen.getByLabelText('Delete Brand Profile 1 profile')).toBeDisabled();
    expect(screen.getByLabelText('Set Brand Profile 2 as default profile')).toBeDisabled();
  });

  test('does not show duplicate button when onDuplicate is not provided', () => {
    const propsWithoutDuplicate = { ...mockProps };
    delete propsWithoutDuplicate.onDuplicate;
    
    render(
      <TestWrapper>
        <BrandProfileList {...propsWithoutDuplicate} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Duplicate Brand Profile 1 profile')).not.toBeInTheDocument();
  });

  test('displays profile avatars with correct colors', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    const avatars = screen.getAllByText('B'); // First letter of "Brand"
    expect(avatars).toHaveLength(3);
  });

  test('displays color indicators for each profile', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    // Each profile should have color indicators
    const profiles = screen.getAllByRole('button').filter(button => 
      button.textContent.includes('Brand Profile')
    );
    expect(profiles).toHaveLength(3);
  });

  test('handles error in profile selection gracefully', async () => {
    const user = userEvent.setup();
    const onSelectMock = vi.fn().mockImplementation(() => {
      throw new Error('Selection error');
    });
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} onSelect={onSelectMock} />
      </TestWrapper>
    );

    const profile2 = screen.getByText('Brand Profile 2');
    await user.click(profile2);

    expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
  });

  test('handles error in create new gracefully', async () => {
    const user = userEvent.setup();
    const onCreateNewMock = vi.fn().mockImplementation(() => {
      throw new Error('Create error');
    });
    
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} onCreateNew={onCreateNewMock} />
      </TestWrapper>
    );

    const createButton = screen.getByLabelText('Create new brand profile');
    await user.click(createButton);

    expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
  });

  test('uses custom maxHeight when provided', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} maxHeight="400px" />
      </TestWrapper>
    );

    const list = screen.getByRole('list');
    expect(list).toHaveStyle({ maxHeight: '400px' });
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <BrandProfileList 
          {...mockProps} 
          data-testid="test-profile-list"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-profile-list');
    expect(component).toHaveClass('custom-class');
  });

  test('renders without crashing when profiles is undefined', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} profiles={undefined} />
      </TestWrapper>
    );

    expect(screen.getByText('No brand profiles yet')).toBeInTheDocument();
  });

  test('handles profiles without color_palette gracefully', () => {
    const profilesWithoutColors = [
      {
        id: 'profile-1',
        name: 'Profile Without Colors',
        is_default: false
      }
    ];

    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} profiles={profilesWithoutColors} />
      </TestWrapper>
    );

    expect(screen.getByText('Profile Without Colors')).toBeInTheDocument();
  });

  test('shows profile count chip correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('3')).toBeInTheDocument();
  });

  test('hides profile count chip when no profiles', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} profiles={[]} />
      </TestWrapper>
    );

    expect(screen.queryByText('0')).not.toBeInTheDocument();
  });

  test('hides create button when loading', () => {
    render(
      <TestWrapper>
        <BrandProfileList {...mockProps} loading={true} />
      </TestWrapper>
    );

    expect(screen.queryByText('Create New Profile')).not.toBeInTheDocument();
  });
});

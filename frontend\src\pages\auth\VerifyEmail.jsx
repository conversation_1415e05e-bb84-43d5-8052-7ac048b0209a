// @since 2024-1-1 to 2025-25-7
import { useEffect, useState } from "react";
import {
  useNavigate,
  useSearchParams,
  Link as RouterLink,
} from "react-router-dom";
import {
  Box,
  Typography,

  CircularProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider,
} from "@mui/material";
import { CheckCircle, Error as ErrorIcon } from "@mui/icons-material";
import { useAuth } from "../../hooks/useAuth";
import { useNotification } from "../../hooks/useNotification";
import api from "../../api";

/**
 * VerifyEmail Component
 * Handles verification of email addresses via token from verification email
 */
const VerifyEmail = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { refreshUser } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const verifyEmailToken = async () => {
      const token = searchParams.get("token");

      if (!token) {
        setError("Invalid or missing verification token");
        setLoading(false);
        return;
      }

      try {
        // Call the API endpoint to verify the email
        await api.post("/api/auth/verify-email", { token });

        setSuccess(true);
        showSuccessNotification("Email verified successfully!");

        // Refresh user data to update verification status
        await refreshUser();

        setLoading(false);
      } catch (err) {
        console.error("Email verification error:", err);
        setError(
          err.response?.data?.detail || "Failed to verify email address"
        );
        showErrorNotification(
          err.response?.data?.detail || "Failed to verify email address"
        );
        setLoading(false);
      }
    };

    verifyEmailToken();
  }, [
    searchParams,
    refreshUser,
    showSuccessNotification,
    showErrorNotification,
  ]);

  return (
    <Box
      sx={{
        width: "100%",
        textAlign: "center",
      }}
    >
      <Typography variant="h4" component="h1" gutterBottom>
        Email Verification
      </Typography>

      {loading ? (
        <Box
          sx={{
            my: 4,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="body1">
            Verifying your email address...
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Please wait while we confirm your email.
          </Typography>
        </Box>
      ) : success ? (
        <Box sx={{ my: 3 }}>
          <CheckCircle color="success" sx={{ fontSize: 60, mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Email Verified!
          </Typography>
          <Alert severity="success" sx={{ mb: 3 }}>
            Your email address has been successfully verified.
          </Alert>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Thank you for verifying your email address. You now have full access
            to all features of the B2B Influencer Tool.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate("/dashboard")}
            fullWidth
            sx={{ mb: 2 }}
          >
            Go to Dashboard
          </Button>
        </Box>
      ) : (
        <Box sx={{ my: 3 }}>
          <ErrorIcon color="error" sx={{ fontSize: 60, mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Verification Failed
          </Typography>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          <Typography variant="body1" sx={{ mb: 3 }}>
            The verification link may have expired or is invalid. Please try
            requesting a new verification email.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            component={RouterLink}
            to="/dashboard"
            fullWidth
            sx={{ mb: 2 }}
          >
            Go to Dashboard
          </Button>
          <Divider sx={{ my: 2 }} />
          <Button
            variant="outlined"
            color="primary"
            component={RouterLink}
            to="/profile"
            fullWidth
          >
            Go to Profile
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default VerifyEmail;

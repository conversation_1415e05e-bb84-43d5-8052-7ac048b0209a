REM @since 2024-1-1 to 2025-25-7
@echo off
echo ========================================
echo Starting ACEO Admin Panel (Stable Mode)
echo ========================================
echo.

REM Check if backend is running
echo Checking backend server...
curl -s http://localhost:8000/health-minimal >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Backend server not detected at http://localhost:8000
    echo Please start the backend server first with: python backend/start_server.py
    echo.
    pause
    exit /b 1
)

echo ✓ Backend server is running
echo.

REM Clear any existing processes on port 3001
echo Clearing port 3001...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3001" ^| find "LISTENING"') do (
    echo Killing process %%a on port 3001
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo Starting Admin Panel with stable configuration...
echo.
echo Admin Panel will be available at: http://localhost:3001
echo.
echo Default Admin Credentials (Development):
echo Email: <EMAIL>
echo Password: [Authentication bypassed in development]
echo.
echo ========================================
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Start with stable configuration
npm run dev -- --config vite.config.stable.js

pause

// @since 2024-1-1 to 2025-25-7
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import EnhancedUnifiedInbox from '../EnhancedUnifiedInbox';
import { AuthContext } from '../../../contexts/AuthContext';
import { SubscriptionContext } from '../../../hooks/useSubscription';
import { NotificationProvider } from '../../../hooks/useNotification';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock dependencies
jest.mock('react-use-websocket', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    sendJsonMessage: jest.fn(),
    lastJsonMessage: null,
    connectionStatus: 'Open',
  })),
}));

jest.mock('../../../api', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

jest.mock('../../../hooks/useApiError', () => ({
  __esModule: true,
  default: () => ({
    error: null,
    isLoading: false,
    resetError: jest.fn(),
    handleApiRequest: jest.fn((fn, options) => {
      return fn().then(options.onSuccess).catch(options.onError);
    }),
  }),
}));

// Mock child components with accessibility features
jest.mock('../ConversationHeader', () => {
  return function MockConversationHeader({ conversation, onViewInfo, onArchive }) {
    return (
      <div 
        data-testid="conversation-header"
        role="banner"
        aria-label={`Conversation header for ${conversation?.title}`}
      >
        <h2 id="conversation-title">{conversation?.title}</h2>
        <button 
          onClick={() => onViewInfo()}
          aria-describedby="conversation-title"
          aria-label="View conversation information"
        >
          View Info
        </button>
        <button 
          onClick={() => onArchive(conversation?.id)}
          aria-describedby="conversation-title"
          aria-label="Archive conversation"
        >
          Archive
        </button>
      </div>
    );
  };
});

jest.mock('../ConversationInfo', () => {
  return function MockConversationInfo({ conversation, onClose }) {
    return (
      <div 
        data-testid="conversation-info"
        role="dialog"
        aria-labelledby="info-title"
        aria-modal="true"
      >
        <h2 id="info-title">{conversation?.title} Information</h2>
        <button 
          onClick={onClose}
          aria-label="Close conversation information"
        >
          Close
        </button>
      </div>
    );
  };
});

jest.mock('../MessageActions', () => {
  return function MockMessageActions({ message, onReply, onCopy, onDelete }) {
    return (
      <div 
        data-testid="message-actions"
        role="toolbar"
        aria-label="Message actions"
      >
        <button 
          onClick={() => onReply(message)}
          aria-label={`Reply to message: ${message.content.substring(0, 50)}...`}
        >
          Reply
        </button>
        <button 
          onClick={() => onCopy(message.content)}
          aria-label="Copy message to clipboard"
        >
          Copy
        </button>
        <button 
          onClick={() => onDelete(message.id)}
          aria-label="Delete message"
        >
          Delete
        </button>
      </div>
    );
  };
});

jest.mock('../MessageInput', () => {
  return function MockMessageInput({ value, onChange, onSend, placeholder, disabled }) {
    return (
      <div data-testid="message-input" role="form" aria-label="Message composition">
        <label htmlFor="message-text-input" className="sr-only">
          Message content
        </label>
        <input
          id="message-text-input"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          aria-describedby="send-button"
          aria-required="true"
        />
        <button 
          id="send-button"
          onClick={() => onSend(value)} 
          disabled={disabled}
          aria-label="Send message"
        >
          Send
        </button>
      </div>
    );
  };
});

// Mock data
const mockUser = {
  id: 'user-1',
  full_name: 'Test User',
  email: '<EMAIL>',
};

const mockConversations = [
  {
    id: 'conv-1',
    title: 'Test Conversation 1',
    is_social_media: false,
    is_archived: false,
    unread_count: 2,
    last_message: { content: 'Hello there' },
    participants: [],
  },
  {
    id: 'conv-2',
    title: 'Social Media Conversation',
    is_social_media: true,
    is_archived: false,
    platform: 'facebook',
    external_participant_name: 'John Doe',
    unread_count: 0,
    last_message: { content: 'Thanks for the help!' },
    participants: [],
  },
];

const mockMessages = [
  {
    id: 'msg-1',
    content: 'Hello there',
    sender_id: 'other-user',
    created_at: '2023-01-01T10:00:00Z',
  },
  {
    id: 'msg-2',
    content: 'Hi! How can I help?',
    sender_id: 'user-1',
    created_at: '2023-01-01T10:01:00Z',
  },
];

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      <AuthContext.Provider value={{ user: mockUser, token: 'test-token' }}>
        <SubscriptionContext.Provider value={{ hasFeatureAccess: () => true }}>
          <NotificationProvider>
            {children}
          </NotificationProvider>
        </SubscriptionContext.Provider>
      </AuthContext.Provider>
    </ThemeProvider>
  </BrowserRouter>
);

describe('EnhancedUnifiedInbox Accessibility Tests', () => {
  let mockApi;

  beforeEach(() => {
    mockApi = require('../../../api');
    mockApi.get.mockImplementation((url) => {
      if (url === '/api/inbox/conversations') {
        return Promise.resolve({ data: mockConversations });
      }
      if (url === '/api/inbox/messages') {
        return Promise.resolve({ data: mockMessages });
      }
      if (url === '/api/inbox/platform-limits') {
        return Promise.resolve({ data: { can_add_platforms: true } });
      }
      if (url === '/api/inbox/stats') {
        return Promise.resolve({ data: { total_unread: 2 } });
      }
      return Promise.resolve({ data: [] });
    });

    mockApi.post.mockResolvedValue({ data: { success: true } });
    mockApi.put.mockResolvedValue({ data: { success: true } });
    mockApi.delete.mockResolvedValue({ data: { success: true } });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should not have accessibility violations on initial render', async () => {
    const { container } = render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Inbox')).toBeInTheDocument();
    });

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should not have accessibility violations with conversation selected', async () => {
    const { container } = render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should have proper ARIA labels and roles', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      // Check for proper ARIA roles and labels
      expect(screen.getByRole('log')).toBeInTheDocument(); // Messages area
      expect(screen.getByLabelText('Conversation messages')).toBeInTheDocument();
      expect(screen.getByRole('banner')).toBeInTheDocument(); // Conversation header
      expect(screen.getByRole('form')).toBeInTheDocument(); // Message input
      expect(screen.getByRole('toolbar')).toBeInTheDocument(); // Message actions
    });
  });

  test('should support keyboard navigation', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Test Tab navigation
    const firstConversation = screen.getByText('Test Conversation 1');
    firstConversation.focus();
    expect(document.activeElement).toBe(firstConversation);

    // Test Enter key to select conversation
    fireEvent.keyDown(firstConversation, { key: 'Enter' });
    fireEvent.click(firstConversation); // Simulate click for test

    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });

    // Test Escape key functionality
    fireEvent.keyDown(document, { key: 'Escape' });
    // Should close any open dialogs/panels
  });

  test('should have proper focus management', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });

    // Open conversation info
    fireEvent.click(screen.getByLabelText('View conversation information'));

    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // Focus should be trapped in dialog
    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();

    // Close dialog
    fireEvent.click(screen.getByLabelText('Close conversation information'));

    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  test('should have proper color contrast and visual indicators', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Check for unread count badges (visual indicators)
    const unreadBadges = screen.getAllByText('2'); // Unread count
    expect(unreadBadges.length).toBeGreaterThan(0);

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });

    // Check for message content visibility
    expect(screen.getByText('Hello there')).toBeInTheDocument();
    expect(screen.getByText('Hi! How can I help?')).toBeInTheDocument();
  });

  test('should support screen reader announcements', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Check for live regions
    expect(screen.getByLabelText('Conversation messages')).toHaveAttribute('aria-live', 'polite');

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      // Check for status announcements
      const statusElements = screen.getAllByRole('status');
      expect(statusElements.length).toBeGreaterThan(0);
    });
  });

  test('should handle loading states accessibly', async () => {
    // Mock loading state
    const mockUseApiError = require('../../../hooks/useApiError');
    mockUseApiError.default.mockReturnValue({
      error: null,
      isLoading: true,
      resetError: jest.fn(),
      handleApiRequest: jest.fn(),
    });

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    // Should have loading indicator with proper label
    const loadingIndicator = screen.getByRole('progressbar');
    expect(loadingIndicator).toBeInTheDocument();
  });

  test('should handle error states accessibly', async () => {
    // Mock error state
    const mockUseApiError = require('../../../hooks/useApiError');
    mockUseApiError.default.mockReturnValue({
      error: new Error('Test error'),
      isLoading: false,
      resetError: jest.fn(),
      handleApiRequest: jest.fn(),
    });

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    // Should display error message accessibly
    await waitFor(() => {
      expect(screen.getByText('Unable to Load Inbox')).toBeInTheDocument();
    });
  });

  test('should support high contrast mode', async () => {
    // Mock high contrast media query
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query.includes('prefers-contrast: high'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    const { container } = render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Inbox')).toBeInTheDocument();
    });

    // Component should render without accessibility violations in high contrast mode
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should support reduced motion preferences', async () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query.includes('prefers-reduced-motion: reduce'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation (should work without animations)
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });
  });

  test('should have proper heading hierarchy', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      // Check for proper heading structure
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
      
      // Main inbox heading should be h6 (as per Material-UI Typography variant)
      const inboxHeading = screen.getByText('Inbox');
      expect(inboxHeading).toBeInTheDocument();
    });
  });
});

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,

  CircularProgress,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,

  Tooltip,
  IconButton,

  CardContent,
  useTheme
} from '@mui/material';
import {
  BarChart,
  Bar,


  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useSnackbar } from '../../contexts/SnackbarContext';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';
import GlassmorphicCard from '../../components/common/GlassmorphicCard';
import EmptyState from '../../components/common/EmptyState';

const AnalyticsDashboard = () => {
  const theme = useTheme();
  const { showSuccessNotification, showErrorNotification } = useSnackbar();
  const { showInfoNotification, clearNotification } = useNotification();
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState(30);
  const [exportFormat, setExportFormat] = useState('csv');
  const [exportLoading, setExportLoading] = useState(false);

  // Chart colors
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.info.main
  ];

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  const fetchAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get(`/api/auto-comment-reply/analytics?days=${timeRange}`);
      setAnalytics(response.data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      showErrorNotification('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  }, [timeRange, showErrorNotification]);

  const handleExport = async () => {
    try {
      setExportLoading(true);

      // Show generating report notification
      showInfoNotification('Generating report. Please wait...', 0);

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeRange);

      const response = await api.post(
        '/api/auto-comment-reply/analytics/export',
        {
          format: exportFormat,
          time_range: {
            start_date: startDate.toISOString(),
            end_date: endDate.toISOString()
          },
          metrics: ['approval_rate', 'response_time', 'engagement', 'sentiment_shift']
        },
        { responseType: 'blob' }
      );

      // Clear the generating notification
      clearNotification();

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `response_analytics_${new Date().toISOString().split('T')[0]}.${exportFormat}`);
      document.body.appendChild(link);
      link.click();
      link.remove();

      showSuccessNotification(`Analytics exported successfully as ${exportFormat.toUpperCase()}`);
    } catch (error) {
      console.error('Error exporting analytics:', error);
      showErrorNotification('Failed to export analytics data');
    } finally {
      setExportLoading(false);
    }
  };

  // Prepare data for charts
  const prepareResponseStatusData = () => {
    if (!analytics) return [];

    return [
      { name: 'Approved', value: analytics.approved_responses },
      { name: 'Rejected', value: analytics.rejected_responses },
      { name: 'Pending', value: analytics.pending_responses },
      { name: 'Edited', value: analytics.edited_responses }
    ];
  };

  const prepareSentimentShiftData = () => {
    if (!analytics || !analytics.sentiment_shifts) return [];

    const shifts = analytics.sentiment_shifts;
    return [
      { name: 'Negative to Positive', value: shifts.negative_to_positive || 0 },
      { name: 'Neutral to Positive', value: shifts.neutral_to_positive || 0 },
      { name: 'Negative to Neutral', value: shifts.negative_to_neutral || 0 },
      { name: 'No Change', value: shifts.positive_to_positive + shifts.negative_to_negative + shifts.neutral_to_neutral || 0 },
      { name: 'Positive to Negative', value: shifts.positive_to_negative || 0 },
      { name: 'Neutral to Negative', value: shifts.neutral_to_negative || 0 },
      { name: 'Positive to Neutral', value: shifts.positive_to_neutral || 0 }
    ];
  };

  const preparePlatformData = () => {
    if (!analytics || !analytics.platforms) return [];

    return Object.entries(analytics.platforms).map(([platform, data]) => ({
      name: platform.charAt(0).toUpperCase() + platform.slice(1),
      approved: data.approved,
      rejected: data.rejected,
      pending: data.pending,
      published: data.published
    }));
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Response Analytics Dashboard</Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              label="Time Range"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value={7}>Last 7 days</MenuItem>
              <MenuItem value={30}>Last 30 days</MenuItem>
              <MenuItem value={90}>Last 90 days</MenuItem>
              <MenuItem value={180}>Last 6 months</MenuItem>
              <MenuItem value={365}>Last year</MenuItem>
            </Select>
          </FormControl>

          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchAnalytics}
            disabled={loading}
          >
            Refresh
          </Button>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 100, mr: 1 }}>
              <InputLabel>Format</InputLabel>
              <Select
                value={exportFormat}
                label="Format"
                onChange={(e) => setExportFormat(e.target.value)}
              >
                <MenuItem value="csv">CSV</MenuItem>
                <MenuItem value="pdf">PDF</MenuItem>
              </Select>
            </FormControl>

            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={handleExport}
              disabled={exportLoading || loading}
            >
              Export
            </Button>
          </Box>
        </Box>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
          <CircularProgress />
        </Box>
      ) : !analytics ? (
        <EmptyState
          title="No Analytics Data Available"
          description="Start using the automated comment reply feature to generate analytics data."
          icon={<InfoIcon sx={{ fontSize: 60 }} />}
        />
      ) : (
        <Grid container spacing={3}>
          {/* Key Metrics */}
          <Grid item xs={12} md={3}>
            <GlassmorphicCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>Approval Rate</Typography>
                <Typography variant="h3" color="primary" align="center">
                  {analytics.approval_rate}%
                </Typography>
                <Typography variant="body2" color="text.secondary" align="center">
                  {analytics.approved_responses} of {analytics.approved_responses + analytics.rejected_responses} responses approved
                </Typography>
              </CardContent>
            </GlassmorphicCard>
          </Grid>

          <Grid item xs={12} md={3}>
            <GlassmorphicCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>Average Response Time</Typography>
                <Typography variant="h3" color="primary" align="center">
                  {Math.floor(analytics.average_response_time_seconds / 60)}m {analytics.average_response_time_seconds % 60}s
                </Typography>
                <Typography variant="body2" color="text.secondary" align="center">
                  From comment to response
                </Typography>
              </CardContent>
            </GlassmorphicCard>
          </Grid>

          <Grid item xs={12} md={3}>
            <GlassmorphicCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>Average Rating</Typography>
                <Typography variant="h3" color="primary" align="center">
                  {analytics.average_rating}/5
                </Typography>
                <Typography variant="body2" color="text.secondary" align="center">
                  User feedback on AI responses
                </Typography>
              </CardContent>
            </GlassmorphicCard>
          </Grid>

          <Grid item xs={12} md={3}>
            <GlassmorphicCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>Sentiment Improvement</Typography>
                <Typography variant="h3" color="primary" align="center">
                  {analytics.sentiment_shifts?.improvement_rate || 0}%
                </Typography>
                <Typography variant="body2" color="text.secondary" align="center">
                  Conversations with improved sentiment
                </Typography>
              </CardContent>
            </GlassmorphicCard>
          </Grid>

          {/* Response Status Chart */}
          <Grid item xs={12} md={6}>
            <GlassmorphicCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Response Status Distribution
                  <Tooltip title="Breakdown of response statuses across all platforms">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={prepareResponseStatusData()}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {prepareResponseStatusData().map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Legend />
                      <RechartsTooltip formatter={(value) => [`${value} responses`, null]} />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </GlassmorphicCard>
          </Grid>

          {/* Sentiment Shift Chart */}
          <Grid item xs={12} md={6}>
            <GlassmorphicCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sentiment Shifts
                  <Tooltip title="How sentiment changes after automated responses">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={prepareSentimentShiftData()}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" tick={{ fontSize: 10 }} angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <RechartsTooltip />
                      <Bar dataKey="value" name="Count" fill={theme.palette.primary.main} />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </GlassmorphicCard>
          </Grid>

          {/* Platform Comparison Chart */}
          <Grid item xs={12}>
            <GlassmorphicCard>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Platform Comparison
                  <Tooltip title="Response metrics across different social media platforms">
                    <IconButton size="small" sx={{ ml: 1 }}>
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Typography>
                <Box sx={{ height: 400 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={preparePlatformData()}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <RechartsTooltip />
                      <Legend />
                      <Bar dataKey="approved" name="Approved" fill={theme.palette.success.main} />
                      <Bar dataKey="rejected" name="Rejected" fill={theme.palette.error.main} />
                      <Bar dataKey="pending" name="Pending" fill={theme.palette.warning.main} />
                      <Bar dataKey="published" name="Published" fill={theme.palette.info.main} />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </GlassmorphicCard>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default AnalyticsDashboard;

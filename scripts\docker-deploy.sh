#!/bin/bash
# @since 2024-1-1 to 2025-25-7

# Docker Deployment Script for ACEO
# Production-ready deployment with health checks and monitoring

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Configuration
ENVIRONMENT=${1:-development}
COMPOSE_FILE=""

case $ENVIRONMENT in
    "development"|"dev")
        COMPOSE_FILE="docker-compose.dev.yml"
        ;;
    "staging")
        COMPOSE_FILE="docker-compose.staging.yml"
        ;;
    "production"|"prod")
        COMPOSE_FILE="docker-compose.prod.yml"
        ;;
    *)
        COMPOSE_FILE="docker-compose.yml"
        ;;
esac

log "🚀 Starting Docker deployment for ACEO..."
log "📋 Environment: $ENVIRONMENT"
log "📄 Compose file: $COMPOSE_FILE"

# Pre-deployment checks
log "🔍 Running pre-deployment checks..."

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! docker info &> /dev/null; then
    error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    error "docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Check if compose file exists
if [ ! -f "$COMPOSE_FILE" ]; then
    error "Compose file $COMPOSE_FILE not found."
    exit 1
fi

success "Pre-deployment checks passed"

# Environment setup
if [ "$ENVIRONMENT" = "production" ] || [ "$ENVIRONMENT" = "staging" ]; then
    log "🔐 Checking environment variables for $ENVIRONMENT..."
    
    # Check required environment variables
    REQUIRED_VARS=("SECRET_KEY" "MONGODB_URL" "REDIS_URL")
    
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    success "Environment variables validated"
fi

# Build and deploy
log "🏗️ Building Docker images..."
docker-compose -f "$COMPOSE_FILE" build --no-cache

log "🚀 Starting services..."
if [ "$ENVIRONMENT" = "development" ]; then
    docker-compose -f "$COMPOSE_FILE" up -d
else
    docker-compose -f "$COMPOSE_FILE" up -d
fi

# Health checks
log "🏥 Performing health checks..."
sleep 30  # Wait for services to start

# Check backend health
if curl -f http://localhost:8000/health-minimal &> /dev/null; then
    success "Backend service is healthy"
else
    warning "Backend service health check failed"
fi

# Check database connectivity
if docker-compose -f "$COMPOSE_FILE" exec -T mongodb mongosh --eval "db.adminCommand('ping')" &> /dev/null; then
    success "MongoDB is healthy"
else
    warning "MongoDB health check failed"
fi

# Check Redis connectivity
if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping &> /dev/null; then
    success "Redis is healthy"
else
    warning "Redis health check failed"
fi

# Display service status
log "📊 Service Status:"
docker-compose -f "$COMPOSE_FILE" ps

# Display useful information
log "📋 Deployment Information:"
echo ""
echo "🌐 Application URLs:"
echo "   Backend API: http://localhost:8000"
echo "   API Documentation: http://localhost:8000/docs"
echo "   Health Check: http://localhost:8000/health-minimal"

if [ "$ENVIRONMENT" = "development" ]; then
    echo "   Frontend: http://localhost:3000"
    echo "   MongoDB Express: http://localhost:8081"
    echo "   Redis Commander: http://localhost:8082"
fi

if [ "$ENVIRONMENT" = "production" ]; then
    echo "   Prometheus: http://localhost:9090"
    echo "   Grafana: http://localhost:3001"
fi

echo ""
echo "🔧 Useful Commands:"
echo "   View logs: docker-compose -f $COMPOSE_FILE logs -f"
echo "   Stop services: docker-compose -f $COMPOSE_FILE down"
echo "   Restart services: docker-compose -f $COMPOSE_FILE restart"
echo "   Execute backend shell: docker-compose -f $COMPOSE_FILE exec backend bash"

success "Docker deployment completed successfully!"

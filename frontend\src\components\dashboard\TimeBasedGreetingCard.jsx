// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  useTheme,
  alpha,
  useMediaQuery,
  Divider
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { GlassmorphicCard } from '../common';

/**
 * TimeBasedGreetingCard - A dynamic greeting card that changes based on time of day
 * Displays user's name, local time, and contextual motivational quotes
 *
 * @component
 */
const TimeBasedGreetingCard = ({ minHeight = 180 }) => {
  const theme = useTheme();
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [quote, setQuote] = useState('');

  // Media queries for different device sizes
  const isMobile = useMediaQuery('(max-width:576px)');

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  // Get time period (morning, midday, evening, night)
  const getTimePeriod = useCallback(() => {
    const hours = currentTime.getHours();

    if (hours >= 5 && hours < 12) {
      return 'morning';
    } else if (hours >= 12 && hours < 17) {
      return 'midday';
    } else if (hours >= 17 && hours < 21) {
      return 'evening';
    } else {
      return 'night';
    }
  }, [currentTime]);



  // Get greeting based on time period
  const getGreeting = useCallback(() => {
    const timePeriod = getTimePeriod();

    switch (timePeriod) {
      case 'morning':
        return 'Good morning';
      case 'midday':
        return 'Good afternoon';
      case 'evening':
        return 'Good evening';
      case 'night':
        return 'Good night';
      default:
        return 'Hello';
    }
  }, [getTimePeriod]);

  // Format time as HH:MM AM/PM
  const formatTime = useCallback((date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  // Format date as Day, Month DD, YYYY
  const formatDate = useCallback((date) => {
    if (isMobile) {
      // Shorter format for mobile: "Mon, Jan 15, 2024"
      return date.toLocaleDateString([], {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } else {
      // Full format for larger screens: "Monday, January 15, 2024"
      return date.toLocaleDateString([], {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  }, [isMobile]);

  // Get background style based on time period
  const getBackgroundStyle = useCallback(() => {
    const timePeriod = getTimePeriod();

    switch (timePeriod) {
      case 'morning':
        // Soft sunrise colors (light orange/yellow)
        return {
          background: `linear-gradient(135deg, ${alpha('#FFD580', 0.2)} 0%, ${alpha('#FFA500', 0.3)} 100%)`,
          borderColor: alpha('#FFA500', 0.3)
        };
      case 'midday':
        // Bright blue/white tones
        return {
          background: `linear-gradient(135deg, ${alpha('#87CEEB', 0.2)} 0%, ${alpha('#FFFFFF', 0.3)} 100%)`,
          borderColor: alpha('#87CEEB', 0.3)
        };
      case 'evening':
        // Sunset colors (orange/purple gradient)
        return {
          background: `linear-gradient(135deg, ${alpha('#FF7F50', 0.2)} 0%, ${alpha('#8A2BE2', 0.3)} 100%)`,
          borderColor: alpha('#FF7F50', 0.3)
        };
      case 'night':
        // Dark blue/purple with subtle star effects
        return {
          background: `linear-gradient(135deg, ${alpha('#191970', 0.3)} 0%, ${alpha('#4B0082', 0.4)} 100%)`,
          borderColor: alpha('#191970', 0.3),
          color: theme.palette.mode === 'dark' ? '#fff' : alpha('#fff', 0.9)
        };
      default:
        return {
          background: theme.palette.background.paper,
          borderColor: theme.palette.divider
        };
    }
  }, [getTimePeriod, theme.palette.background.paper, theme.palette.divider, theme.palette.mode]);

  // Get motivational quote based on time period
  const getMotivationalQuote = useCallback(() => {
    const timePeriod = getTimePeriod();
    const quotes = {
      morning: [
        "Embrace the fresh start of a new day. Your potential is limitless.",
        "Morning is an important time of day, because how you spend your morning can often tell you what kind of day you are going to have.",
        "The sun is a daily reminder that we too can rise again from the darkness, that we too can shine our own light.",
        "Each morning we are born again. What we do today is what matters most.",
        "Rise up, start fresh, see the bright opportunity in each new day.",
        "Every morning brings new potential, but only if you make the most of it.",
        "The morning breeze has secrets to tell you. Do not go back to sleep.",
        "Today is a new day. Even if you were wrong yesterday, you can get it right today.",
        "Wake up with determination. Go to bed with satisfaction.",
        "The way to get started is to quit talking and begin doing.",
        "Success is not final, failure is not fatal: it is the courage to continue that counts.",
        "Believe you can and you're halfway there.",
        "Your only limit is your mind. Start your day with endless possibilities.",
        "Every sunrise is an invitation to brighten someone's day.",
        "The early bird catches the worm, but the second mouse gets the cheese.",
        "Morning coffee and reading the newspaper together, that's happiness.",
        "A new day is like a blank canvas. Paint it with your dreams.",
        "The morning sun reminds us that we too can rise from the darkness.",
        "Start where you are. Use what you have. Do what you can.",
        "Today's accomplishments were yesterday's impossibilities."
      ],
      midday: [
        "Stay focused and maintain your momentum. You're making progress.",
        "Your dedication today shapes your success tomorrow.",
        "Take a moment to appreciate how far you've come today.",
        "The middle of the day is perfect for reflecting on your goals and refocusing your energy.",
        "Persistence is the hard work you do after you get tired of doing the hard work you already did.",
        "Don't watch the clock; do what it does. Keep going.",
        "The difference between ordinary and extraordinary is that little extra.",
        "Success is the sum of small efforts repeated day in and day out.",
        "You are never too old to set another goal or to dream a new dream.",
        "The only impossible journey is the one you never begin.",
        "Challenges are what make life interesting. Overcoming them is what makes life meaningful.",
        "Your current situation is not your final destination.",
        "Progress, not perfection, is the goal.",
        "Every expert was once a beginner. Every pro was once an amateur.",
        "The best time to plant a tree was 20 years ago. The second best time is now.",
        "You don't have to be great to get started, but you have to get started to be great.",
        "Focus on being productive instead of busy.",
        "Excellence is not a skill, it's an attitude.",
        "The harder you work for something, the greater you'll feel when you achieve it.",
        "Don't stop when you're tired. Stop when you're done."
      ],
      evening: [
        "As the day winds down, take pride in what you've accomplished.",
        "The evening is a time to reflect, recharge, and prepare for tomorrow's opportunities.",
        "Let the colors of the sunset remind you that endings can be beautiful too.",
        "Evening is a time of real experimentation. You never want to look the same way.",
        "Reflect on your day with gratitude. Every experience is a lesson learned.",
        "The sunset is proof that endings can be beautiful too.",
        "Evening is the time to wind down and appreciate the day's journey.",
        "As the sun sets, let go of today's worries and embrace tomorrow's possibilities.",
        "The evening sky is a canvas painted with the colors of reflection.",
        "Take time to make your soul happy in the quiet moments of evening.",
        "Every sunset brings the promise of a new dawn.",
        "The evening is a time to count your blessings, not your troubles.",
        "Let the peace of evening wash away the stress of the day.",
        "In the evening, we can look back with satisfaction or forward with hope.",
        "The golden hour reminds us that even endings can be magnificent.",
        "Evening meditation brings wisdom; morning meditation brings power.",
        "As day turns to night, let gratitude fill your heart.",
        "The evening whispers what the morning shouts.",
        "Sunsets are proof that no matter what happens, every day can end beautifully.",
        "The evening is nature's way of saying, 'Well done, now rest.'"
      ],
      night: [
        "The stars remind us that even in darkness, there is light and beauty.",
        "Night is a time for dreams, and tomorrow is another day to make them come true.",
        "Let tonight's rest fuel tomorrow's best.",
        "The night is the hardest time to be alive and 4am knows all my secrets.",
        "In the depth of winter, I finally learned that there was in me an invincible summer.",
        "The night is more alive and more richly colored than the day.",
        "Stars can't shine without darkness.",
        "The darker the night, the brighter the stars.",
        "Night is a world lit by itself.",
        "Dreams are the touchstones of our character.",
        "The night sky is a miracle of infinitude.",
        "Sleep is the best meditation.",
        "Night brings our troubles to the light, rather than banishing them.",
        "The night is far too long for you to be awake all of it.",
        "In the night, we are all the same color.",
        "The night is a time of riches for those who observe.",
        "Nighttime is really the best time to work. All the ideas are there to be yours.",
        "The night walked down the sky with the moon in her hand.",
        "Night is the mother of thoughts and dreams.",
        "The night is a time for reflection, for peace, and for dreams to take flight.",
        "In the silence of the night, we find the answers we seek.",
        "The moon and stars are the night's gentle companions.",
        "Night is when the soul speaks loudest.",
        "Rest when you're weary. Refresh and renew yourself, your body, your mind, your spirit."
      ]
    };

    // Get a random quote from the appropriate time period
    // Use the current date as part of the seed to keep the quote consistent for the day
    const today = new Date();
    const seed = today.getFullYear() * 10000 + (today.getMonth() + 1) * 100 + today.getDate();
    const quoteIndex = seed % quotes[timePeriod].length;

    return quotes[timePeriod][quoteIndex];
  }, [getTimePeriod]);

  // Update quote when time period changes
  useEffect(() => {
    setQuote(getMotivationalQuote());
  }, [getMotivationalQuote]);

  // Get user's first name
  const getUserFirstName = useCallback(() => {
    if (!user) return '';

    // First try to use first_name if available
    if (user.first_name) {
      return user.first_name;
    }

    // If first_name is not available, try to extract it from full_name
    if (user.full_name) {
      // Split full name and return the first part as first name
      return user.full_name.split(' ')[0];
    }

    // Fallback to empty string if neither is available
    return '';
  }, [user]);

  const backgroundStyle = getBackgroundStyle();
  const isNightTime = getTimePeriod() === 'night';

  // Determine text color based on time of day
  const textColor = isNightTime ? '#fff' : theme.palette.text.primary;
  const secondaryTextColor = isNightTime ? alpha('#fff', 0.9) : theme.palette.text.secondary;

  return (
    <GlassmorphicCard
      variant={isNightTime ? "glassDark" : "glass"}
      sx={{
        height: '100%',
        minHeight: minHeight,
        ...backgroundStyle,
        transition: 'all 0.5s ease',
        position: 'relative',
        overflow: 'hidden'
      }}
      blurStrength={8}
      opacity={0.7}
      hoverable={true}
    >
      <Box
        sx={{
          p: { xs: 2, sm: 3 },
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          position: 'relative',
          zIndex: 2
        }}
      >
        <Box>
          <Typography
            variant={isMobile ? "h6" : "h5"}
            component="h2"
            sx={{
              fontWeight: 600,
              mb: 0.5,
              color: textColor,
              fontSize: {
                xs: '1.1rem',
                sm: '1.3rem',
                md: '1.5rem',
                lg: '1.75rem'
              }
            }}
          >
            {`${getGreeting()}, ${getUserFirstName()}`}
          </Typography>

          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 500,
              color: secondaryTextColor,
              fontSize: {
                xs: '0.9rem',
                sm: '1rem',
                md: '1.1rem',
                lg: '1.2rem'
              }
            }}
          >
            {formatTime(currentTime)}
          </Typography>

          <Typography
            variant="body1"
            component="div"
            sx={{
              fontWeight: 400,
              color: secondaryTextColor,
              mt: 0.5,
              fontSize: {
                xs: '0.8rem',
                sm: '0.85rem',
                md: '0.9rem',
                lg: '0.95rem'
              }
            }}
          >
            {formatDate(currentTime)}
          </Typography>
        </Box>

        <Divider sx={{
          my: 1.5,
          opacity: 0.6,
          borderColor: isNightTime ? alpha('#fff', 0.2) : 'divider'
        }} />

        <Typography
          variant="body1"
          sx={{
            fontStyle: 'italic',
            mt: 1,
            color: textColor,
            fontSize: {
              xs: '0.85rem',
              sm: '0.9rem',
              md: '1rem',
              lg: '1.1rem'
            },
            borderRadius: 1
          }}
        >
          &ldquo;{quote}&rdquo;
        </Typography>

        {/* Add subtle star effect for night time */}
        {isNightTime && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 1,
              opacity: 0.2,
              backgroundImage: 'radial-gradient(white, rgba(255,255,255,.2) 2px, transparent 5px)',
              backgroundSize: '50px 50px',
              pointerEvents: 'none'
            }}
          />
        )}
      </Box>
    </GlassmorphicCard>
  );
};

TimeBasedGreetingCard.propTypes = {
  minHeight: PropTypes.number
};

export default TimeBasedGreetingCard;

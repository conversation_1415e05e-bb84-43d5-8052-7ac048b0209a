/**
 * Enhanced Feature Tour - Enterprise-grade feature tour management component
 * Features: Comprehensive feature tour management, interactive guided tours, subscription-free access,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced feature tour capabilities and interactive tour exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Paper,
  Typography,
  Backdrop,
  Fade,
  IconButton,
  useMediaQuery,
  Chip,
  Stack,
  Snackbar,
  Alert,
  alpha
} from '@mui/material';
import {
  Close as CloseIcon,
  ArrowBack as BackIcon,
  ArrowForward as NextIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon
} from '@mui/icons-material';
import { useAccessibility } from '../../hooks/useAccessibility';
import AccessibleButton from '../common/AccessibleButton';
import { useAuth } from '../../contexts/AuthContext';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce, focusVisible } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, focusVisible, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Feature tour display modes with enhanced configurations
const FEATURE_TOUR_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Tours',
    description: 'Basic feature tour management interface',
    features: ['basic_tours', 'analytics_tours', 'ai_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Tours',
    description: 'Comprehensive feature tour management',
    features: ['detailed_tours', 'tour_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Tours',
    description: 'AI-powered feature tour management and optimization',
    features: ['ai_assisted', 'ai_optimization', 'tour_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Tours',
    description: 'Advanced feature tour analytics and insights',
    features: ['analytics_tours', 'tour_insights']
  }
};

/**
 * Enhanced Feature Tour Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.isOpen=false] - Whether the tour is open
 * @param {Function} [props.onClose] - Close callback
 * @param {Array} [props.tourSteps=[]] - Tour steps array
 * @param {boolean} [props.autoPlay=false] - Auto-play enabled
 * @param {number} [props.autoPlayDelay=3000] - Auto-play delay
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights (no restrictions)
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onTourAction] - Tour action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-feature-tour'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const FeatureTour = memo(forwardRef(({
  isOpen = false,
  onClose,
  tourSteps = [],
  autoPlay = false,
  autoPlayDelay = 3000,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onTourAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-feature-tour',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announce, announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { user } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const tourRef = useRef(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [highlightedElement, setHighlightedElement] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });

  const autoPlayRef = useRef(null);
  const tooltipRef = useRef(null);

  // Enhanced state management
  const [tourMode, setTourMode] = useState('compact');
  const [tourHistory, setTourHistory] = useState([]);
  const [tourAnalytics, setTourAnalytics] = useState(null);
  const [tourInsights, setTourInsights] = useState(null);
  const [customTourFlows, setCustomTourFlows] = useState([]);
  const [tourPreferences, setTourPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    tourSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [tourDrawerOpen, setTourDrawerOpen] = useState(false);
  const [selectedTourType, setSelectedTourType] = useState(null);
  const [tourStats, setTourStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastTourCheck, setLastTourCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with full feature access - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // All features are available to all users (no plan-based limitations)
    const features = {
      creator: {
        maxTourTypes: -1,
        maxTourPerDay: -1,
        hasAdvancedTour: true,
        hasTourAnalytics: true,
        hasCustomTour: true,
        hasTourInsights: true,
        hasTourHistory: true,
        hasAIAssistance: true,
        hasTourExport: true,
        hasTourScheduling: true,
        hasTourAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxTourTypes: -1,
        maxTourPerDay: -1,
        hasAdvancedTour: true,
        hasTourAnalytics: true,
        hasCustomTour: true,
        hasTourInsights: true,
        hasTourHistory: true,
        hasAIAssistance: true,
        hasTourExport: true,
        hasTourScheduling: true,
        hasTourAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxTourTypes: -1,
        maxTourPerDay: -1,
        hasAdvancedTour: true,
        hasTourAnalytics: true,
        hasCustomTour: true,
        hasTourInsights: true,
        hasTourHistory: true,
        hasAIAssistance: true,
        hasTourExport: true,
        hasTourScheduling: true,
        hasTourAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxTourTypes === -1 || currentUsage < currentFeatures.maxTourTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'dialog',
      'aria-label': ariaLabel || `Feature tour with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Feature tour interface with ${subscriptionFeatures.trackingLevel} tracking`,
      'aria-modal': 'true',
      tabIndex: -1
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive tour API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getTourHistory: () => tourHistory,
    getTourAnalytics: () => tourAnalytics,
    getTourInsights: () => tourInsights,
    refreshTour: () => {
      fetchTourAnalytics();
      if (onRefresh) onRefresh();
    },

    // Tour methods
    focusTour: () => {
      if (tourRef.current) {
        tourRef.current.focus();
      }
    },
    getCurrentStep: () => currentStep,
    getTotalSteps: () => steps.length,
    goToStep: (stepIndex) => handleStepClick(stepIndex),
    nextStep: () => handleNext(),
    previousStep: () => handlePrevious(),
    closeTour: () => handleClose(),
    toggleAutoPlay: () => toggleAutoPlay(),
    openTourDrawer: () => setTourDrawerOpen(true),
    closeTourDrawer: () => setTourDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportTourData: () => {
      if (onExport) {
        onExport(tourHistory, tourAnalytics);
      }
    },

    // Accessibility methods
    announceTour: (message) => announceToScreenReader(message),
    focusTourField: () => setFocusToElement('feature-tour-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => tourMode,
    getTourStats: () => tourStats,
    getSelectedTourType: () => selectedTourType,
    getCustomTourFlows: () => customTourFlows,
    getActiveStep: () => activeStep,
    setActiveStep: (step) => setActiveStep(step),
    addCustomTourFlow,
    handleTourModeChange,
    updateTourPreferences,
    handleTourTypeSelection,
    validateTourFlow,
    getTourDrawerOpen: () => tourDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    triggerTourAction: (action, data) => {
      if (onTourAction) {
        onTourAction(action, data);
      }
    }
  }), [
    tourHistory,
    tourAnalytics,
    tourInsights,
    tourStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    tourMode,
    currentStep,
    selectedTourType,
    customTourFlows,
    showAnalytics,
    fullscreenMode,
    tourDrawerOpen,
    onTourAction,
    addCustomTourFlow,
    fetchTourAnalytics,
    handleTourModeChange,
    updateTourPreferences,
    handleTourTypeSelection,
    validateTourFlow,
    handleClose,
    handleNext,
    handlePrevious,
    handleStepClick,
    steps.length,
    toggleAutoPlay,
    activeStep
  ]);

  // Fetch tour analytics with enhanced error handling and retry logic
  const fetchTourAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/tour/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setTourAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (tourPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Tour analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch tour analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load tour analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, tourPreferences.showAnalytics]);

  // Handle tour mode switching
  const handleTourModeChange = useCallback((newMode) => {
    if (FEATURE_TOUR_MODES[newMode.toUpperCase()]) {
      setTourMode(newMode);
      announceToScreenReader(`Tour mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setTourHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (tourPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} tour mode`);
      }
    }
  }, [announceToScreenReader, user?.id, tourPreferences.showAnalytics, showSuccess]);

  // Handle custom tour flow management
  const addCustomTourFlow = useCallback((flowData) => {
    const newFlow = {
      id: Date.now(),
      ...flowData,
      createdAt: new Date().toISOString(),
      userId: user?.id
    };

    setCustomTourFlows(prev => [...prev, newFlow]);

    // Track flow creation
    const flowRecord = {
      id: Date.now(),
      type: 'custom_flow_created',
      flow: newFlow.name,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTourHistory(prev => [flowRecord, ...prev.slice(0, 99)]);

    if (tourPreferences.showAnalytics) {
      showSuccess(`Custom tour flow "${flowData.name}" created`);
    }
  }, [user?.id, tourPreferences.showAnalytics, showSuccess]);

  // Handle tour preferences updates
  const updateTourPreferences = useCallback((newPreferences) => {
    setTourPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTourHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (tourPreferences.showAnalytics) {
      showSuccess('Tour preferences updated');
    }
  }, [user?.id, tourPreferences.showAnalytics, showSuccess]);

  // Handle tour type selection
  const handleTourTypeSelection = useCallback((tourType) => {
    setSelectedTourType(tourType);

    // Track tour type selection
    const typeRecord = {
      id: Date.now(),
      type: 'tour_type_selected',
      tourType,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTourHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (tourPreferences.showAnalytics) {
      announceToScreenReader(`Selected tour type: ${tourType}`);
    }
  }, [user?.id, tourPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateTourFlow = useCallback((flowData) => {
    const errors = {};

    if (!flowData.name?.trim()) {
      errors.name = 'Flow name is required';
    }
    if (!flowData.steps?.length) {
      errors.steps = 'At least one step is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Default tour steps if none provided
  const defaultSteps = [
    {
      target: '[data-tour="navigation"]',
      title: 'Navigation Menu',
      content: 'Access all features from this main navigation menu. Click any item to explore different sections.',
      placement: 'right',
    },
    {
      target: '[data-tour="dashboard"]',
      title: 'Dashboard Overview',
      content: 'Your personalized dashboard shows key metrics and recent activity at a glance.',
      placement: 'bottom',
    },
    {
      target: '[data-tour="content-creator"]',
      title: 'Content Creator',
      content: 'Create engaging posts with AI assistance. Generate captions, hashtags, and schedule posts.',
      placement: 'left',
    },
    {
      target: '[data-tour="analytics"]',
      title: 'Analytics Hub',
      content: 'Track your performance with detailed analytics and insights across all platforms.',
      placement: 'bottom',
    },
    {
      target: '[data-tour="notifications"]',
      title: 'Notifications',
      content: 'Stay updated with real-time notifications about your campaigns and engagement.',
      placement: 'left',
    },
  ];

  const steps = tourSteps.length > 0 ? tourSteps : defaultSteps;

  // Initial data loading
  useEffect(() => {
    if (isOpen) {
      fetchTourAnalytics();
      fetchTourInsights();
    }
  }, [isOpen, fetchTourAnalytics, fetchTourInsights]);

  // Fetch tour insights
  const fetchTourInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/tour/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setTourInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch tour insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && isOpen && currentStep >= 0) {
      // Optimize tour management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchTourAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, isOpen, currentStep, fetchTourAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastTourCheck(Date.now());

          if (wasUnavailable && tourPreferences.showAnalytics) {
            showSuccess("Connection restored - Tour features available");
          }
        } else {
          setBackendAvailable(false);
          if (tourPreferences.showAnalytics) {
            showError("Backend service unavailable - Some tour features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastTourCheck;
          if (timeSinceLastCheck > 60000 && tourPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Tour may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production' && isOpen) {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastTourCheck, tourPreferences.showAnalytics, showSuccess, showError, isOpen]);

  // Generate AI suggestions when tour changes
  useEffect(() => {
    if (isOpen && enableAIInsights && tourPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [isOpen, enableAIInsights, tourPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/tour/ai-suggestions', {
        params: {
          context: currentStep,
          totalSteps: steps.length
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (tourPreferences.showAnalytics) {
        showSuccess('AI suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [currentStep, steps.length, tourPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when tour changes
  useEffect(() => {
    if (isOpen && enableAdvancedFeatures) {
      fetchTourStats();
    }
  }, [isOpen, enableAdvancedFeatures, fetchTourStats]);

  // Fetch tour stats function
  const fetchTourStats = useCallback(async () => {
    try {
      const response = await api.get('/api/tour/stats');
      setTourStats(response.data);
    } catch (error) {
      console.error('Failed to fetch tour stats:', error);
    }
  }, []);

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && isOpen && currentStep < steps.length - 1) {
      autoPlayRef.current = setTimeout(() => {
        handleNext();
      }, autoPlayDelay);
    }

    return () => {
      if (autoPlayRef.current) {
        clearTimeout(autoPlayRef.current);
      }
    };
  }, [currentStep, isPlaying, isOpen, autoPlayDelay, steps.length, handleNext]);

  // Sync activeStep with currentStep for enhanced tracking
  useEffect(() => {
    setActiveStep(currentStep);
  }, [currentStep]);

  // Highlight target element and position tooltip
  useEffect(() => {
    if (!isOpen || currentStep >= steps.length) return;

    const step = steps[currentStep];
    const targetElement = document.querySelector(step.target);

    if (targetElement) {
      setHighlightedElement(targetElement);
      
      // Calculate tooltip position
      const rect = targetElement.getBoundingClientRect();
      const tooltipRect = tooltipRef.current?.getBoundingClientRect();
      
      let top = rect.top;
      let left = rect.left;

      // Adjust position based on placement
      switch (step.placement) {
        case 'top':
          top = rect.top - (tooltipRect?.height || 200) - 20;
          left = rect.left + (rect.width / 2) - ((tooltipRect?.width || 300) / 2);
          break;
        case 'bottom':
          top = rect.bottom + 20;
          left = rect.left + (rect.width / 2) - ((tooltipRect?.width || 300) / 2);
          break;
        case 'left':
          top = rect.top + (rect.height / 2) - ((tooltipRect?.height || 200) / 2);
          left = rect.left - (tooltipRect?.width || 300) - 20;
          break;
        case 'right':
          top = rect.top + (rect.height / 2) - ((tooltipRect?.height || 200) / 2);
          left = rect.right + 20;
          break;
        default:
          break;
      }

      // Ensure tooltip stays within viewport
      const padding = 20;
      top = Math.max(padding, Math.min(top, window.innerHeight - (tooltipRect?.height || 200) - padding));
      left = Math.max(padding, Math.min(left, window.innerWidth - (tooltipRect?.width || 300) - padding));

      setTooltipPosition({ top, left });

      // Announce step to screen readers
      announce(`Tour step ${currentStep + 1} of ${steps.length}: ${step.title}`, 'polite');
    }
  }, [currentStep, isOpen, steps, announce]);

  // Keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'Escape':
          handleClose();
          break;
        case 'ArrowRight':
        case ' ':
          event.preventDefault();
          handleNext();
          break;
        case 'ArrowLeft':
          event.preventDefault();
          handlePrevious();
          break;
        case 'p':
        case 'P':
          event.preventDefault();
          toggleAutoPlay();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentStep, handleClose, handleNext, handlePrevious, toggleAutoPlay]);

  const handleNext = useCallback(() => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleClose();
    }
  }, [currentStep, steps.length, handleClose]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleClose = useCallback(() => {
    setIsPlaying(false);
    setCurrentStep(0);
    setHighlightedElement(null);
    if (onClose) onClose();
    announce('Feature tour closed', 'polite');
  }, [onClose, announce]);

  const toggleAutoPlay = useCallback(() => {
    setIsPlaying(!isPlaying);
    announce(isPlaying ? 'Auto-play paused' : 'Auto-play resumed', 'polite');
  }, [isPlaying, announce]);

  const handleStepClick = useCallback((stepIndex) => {
    setCurrentStep(stepIndex);
    setIsPlaying(false);
  }, []);

  if (!isOpen) return null;

  const currentStepData = steps[currentStep];

  return (
    <Box
      {...getAccessibilityProps()}
      ref={tourRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      {/* Backdrop with spotlight effect */}
      <Backdrop
        open={isOpen}
        sx={{
          zIndex: 1300,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
        }}
        onClick={handleClose}
      />

      {/* Spotlight highlight */}
      {highlightedElement && (
        <Box
          sx={{
            position: 'fixed',
            top: highlightedElement.getBoundingClientRect().top - 8,
            left: highlightedElement.getBoundingClientRect().left - 8,
            width: highlightedElement.getBoundingClientRect().width + 16,
            height: highlightedElement.getBoundingClientRect().height + 16,
            border: `3px solid ${ACE_COLORS.PURPLE}`,
            borderRadius: 2,
            boxShadow: `0 0 0 9999px rgba(0, 0, 0, 0.5)`,
            pointerEvents: 'none',
            zIndex: 1301,
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': {
                boxShadow: `0 0 0 0 ${alpha(ACE_COLORS.PURPLE, 0.4)}, 0 0 0 9999px rgba(0, 0, 0, 0.5)`,
              },
              '70%': {
                boxShadow: `0 0 0 10px ${alpha(ACE_COLORS.PURPLE, 0)}, 0 0 0 9999px rgba(0, 0, 0, 0.5)`,
              },
              '100%': {
                boxShadow: `0 0 0 0 ${alpha(ACE_COLORS.PURPLE, 0)}, 0 0 0 9999px rgba(0, 0, 0, 0.5)`,
              },
            },
          }}
        />
      )}

      {/* Tour tooltip */}
      <Fade in={isOpen}>
        <Paper
          ref={tooltipRef}
          elevation={8}
          sx={{
            position: 'fixed',
            top: tooltipPosition.top,
            left: tooltipPosition.left,
            width: isMobile ? 'calc(100vw - 40px)' : (fullscreenMode ? 500 : 350),
            maxWidth: 400,
            zIndex: 1302,
            borderRadius: 2,
            overflow: 'hidden',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <Box
            sx={{
              p: 2,
              background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.DARK} 100%)`,
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h6" component="h2">
                {currentStepData?.title}
              </Typography>
              <Chip
                label={`${currentStep + 1}/${steps.length}`}
                size="small"
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                }}
              />
            </Box>
            
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{ color: 'white' }}
              aria-label="Close tour"
            >
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Content */}
          <Box sx={{ p: 3 }}>
            <Typography variant="body1" paragraph>
              {currentStepData?.content}
            </Typography>

            {/* Progress indicators */}
            <Box sx={{ display: 'flex', gap: 0.5, mb: 2 }}>
              {steps.map((_, index) => (
                <Box
                  key={index}
                  onClick={() => handleStepClick(index)}
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: index === currentStep
                      ? ACE_COLORS.PURPLE
                      : alpha(ACE_COLORS.DARK, 0.3),
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      transform: 'scale(1.2)',
                    },
                  }}
                />
              ))}
            </Box>

            {/* Controls */}
            <Stack direction="row" spacing={1} justifyContent="space-between" alignItems="center">
              <AccessibleButton
                variant="outlined"
                size="small"
                onClick={handlePrevious}
                disabled={currentStep === 0}
                startIcon={<BackIcon />}
                ariaLabel="Previous step"
              >
                Previous
              </AccessibleButton>

              <IconButton
                onClick={toggleAutoPlay}
                size="small"
                aria-label={isPlaying ? 'Pause auto-play' : 'Resume auto-play'}
              >
                {isPlaying ? <PauseIcon /> : <PlayIcon />}
              </IconButton>

              <AccessibleButton
                variant="contained"
                size="small"
                onClick={handleNext}
                endIcon={currentStep === steps.length - 1 ? null : <NextIcon />}
                ariaLabel={currentStep === steps.length - 1 ? 'Finish tour' : 'Next step'}
              >
                {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
              </AccessibleButton>
            </Stack>
          </Box>
        </Paper>
      </Fade>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying tour sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
FeatureTour.propTypes = {
  // Core props
  isOpen: PropTypes.bool,
  onClose: PropTypes.func,
  tourSteps: PropTypes.array,
  autoPlay: PropTypes.bool,
  autoPlayDelay: PropTypes.number,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onTourAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

FeatureTour.displayName = 'FeatureTour';

export default FeatureTour;

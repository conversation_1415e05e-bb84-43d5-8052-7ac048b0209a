// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useRef, useCallback } from "react";
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  CircularProgress,
  Chip,
  useTheme,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Divider,
  Avatar,
  Badge,
  ButtonGroup,
  Drawer,
  Switch,
  FormControlLabel,
  Menu,
  ListItemIcon,
  ListItemText,
  Alert,
  Snackbar,
  alpha,
  Checkbox,
  useMediaQuery,
  Card,
  CardContent,
} from "@mui/material";
import { CustomCard, CustomCardContent } from "../../components/common";
import {
  DateCalendar,
  PickersDay,
  LocalizationProvider,
  TimePicker,
  DatePicker,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import {
  format,
  isSameDay,
  startOfMonth,
  endOfMonth,
  startOfDay,
  endOfDay,
  eachDayOfInterval,
  addDays,
  startOfWeek,
  endOfWeek,
  getHours,
  getMinutes,
  setHours,
  setMinutes,
  getDay,
  subDays,
  addMonths,
  subMonths,
  getMonth,
  getYear,
  setDate,
} from "date-fns";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import ScheduleIcon from "@mui/icons-material/Schedule";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import CalendarViewMonthIcon from "@mui/icons-material/CalendarViewMonth";
import CalendarViewWeekIcon from "@mui/icons-material/CalendarViewWeek";
import CalendarViewDayIcon from "@mui/icons-material/CalendarViewDay";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import EventIcon from "@mui/icons-material/Event";
import TodayIcon from "@mui/icons-material/Today";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import VisibilityIcon from "@mui/icons-material/Visibility";
import ShareIcon from "@mui/icons-material/Share";
import FilterListIcon from "@mui/icons-material/FilterList";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import RefreshIcon from "@mui/icons-material/Refresh";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import RadioGroup from "@mui/material/RadioGroup";
import Radio from "@mui/material/Radio";
import { useNavigate } from "react-router-dom";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import api from "../../api";
import { useNotification } from "../../contexts/NotificationContext";
import { useAuth } from "../../contexts/AuthContext";
import BulkActionDialog from "../../components/scheduling/BulkActionDialog";
import SmartSchedulingAssistant from "../../components/scheduling/SmartSchedulingAssistant";
import MobileCalendarView from "../../components/scheduling/MobileCalendarView";
import RecommendationsWidget from "../../components/scheduling/RecommendationsWidget";

// Custom styled PickersDay component to show scheduled content
function ServerDay(props) {
  const { day, scheduledContent = [], /* selectedView, */ ...other } = props;
  const theme = useTheme();

  // Get content for this day
  const dayContent = Array.isArray(scheduledContent)
    ? scheduledContent.filter(
        (item) =>
          item &&
          item.scheduled_date &&
          isSameDay(new Date(item.scheduled_date), day)
      )
    : [];

  const hasContent = dayContent.length > 0;
  const contentCount = dayContent.length;

  // Get platforms for this day
  const platforms = hasContent
    ? [...new Set(dayContent.map((item) => item.platform))]
    : [];

  // Get priority levels
  const hasPriorityHigh = dayContent.some((item) => item.priority === "high");
  const hasPriorityMedium = dayContent.some(
    (item) => item.priority === "medium"
  );

  // Get status
  const hasDraft = dayContent.some((item) => item.status === "draft");

  // Get color based on platform
  const getPlatformColor = (platform) => {
    switch (platform?.toLowerCase()) {
      case "linkedin":
        return "#0077B5";
      case "twitter":
        return "#1DA1F2";
      case "facebook":
        return "#4267B2";
      case "instagram":
        return "#C13584";
      default:
        return theme.palette.primary.main;
    }
  };

  // Generate tooltip content
  const generateTooltipContent = () => {
    if (!hasContent) return "";

    try {
      return (
        <Box>
          <Typography variant="subtitle2" sx={{ fontWeight: "bold", mb: 1 }}>
            {contentCount} scheduled post{contentCount !== 1 ? "s" : ""}
          </Typography>
          {dayContent.map((content, index) => (
            <Box key={index} sx={{ mb: index < dayContent.length - 1 ? 1 : 0 }}>
              <Typography
                variant="body2"
                sx={{ display: "flex", alignItems: "center" }}
              >
                <Box
                  component="span"
                  sx={{
                    display: "inline-block",
                    width: 10,
                    height: 10,
                    borderRadius: "50%",
                    bgcolor: getPlatformColor(content.platform),
                    mr: 1,
                  }}
                />
                {content.scheduled_date
                  ? format(new Date(content.scheduled_date), "h:mm a")
                  : "00:00"}{" "}
                - {content.title || "Untitled"}
              </Typography>
            </Box>
          ))}
        </Box>
      );
    } catch (error) {
      // Log error for debugging in development only
      if (process.env.NODE_ENV === 'development') {
        console.error("Error generating tooltip content:", error);
      }
      return "";
    }
  };

  return (
    <Tooltip
      title={generateTooltipContent()}
      arrow
      placement="top"
      componentsProps={{
        tooltip: {
          sx: {
            bgcolor: "background.paper",
            color: "text.primary",
            boxShadow: theme.shadows[3],
            borderRadius: 1,
            p: 1,
            maxWidth: 300,
            "& .MuiTooltip-arrow": {
              color: "background.paper",
            },
          },
        },
      }}
    >
      <Badge
        overlap="circular"
        badgeContent={contentCount > 0 ? contentCount : 0}
        color="primary"
        anchorOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        sx={{
          "& .MuiBadge-badge": {
            fontSize: "0.6rem",
            height: 16,
            minWidth: 16,
            padding: "0 4px",
            right: 2,
            top: 2,
            display: contentCount > 0 ? "flex" : "none",
          },
        }}
      >
        <PickersDay
          {...other}
          day={day}
          sx={{
            position: "relative",
            ...(hasContent && {
              borderWidth: 2,
              borderStyle: "solid",
              borderColor: hasPriorityHigh
                ? theme.palette.error.main
                : hasPriorityMedium
                ? theme.palette.warning.main
                : theme.palette.primary.main,
              "&::after": {
                content: '""',
                position: "absolute",
                bottom: 2,
                left: "50%",
                transform: "translateX(-50%)",
                width: "70%",
                height: 4,
                borderRadius: 2,
                background:
                  platforms.length > 1
                    ? `linear-gradient(to right, ${platforms
                        .map((p) => getPlatformColor(p))
                        .join(", ")})`
                    : getPlatformColor(platforms[0]),
              },
              ...(hasDraft && {
                backgroundColor: alpha(theme.palette.action.disabled, 0.1),
              }),
            }),
          }}
        />
      </Badge>
    </Tooltip>
  );
}

const Calendar = ({ isEmbedded = false }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const dragItem = useRef(null);
  const dragOverItem = useRef(null);
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  // State for calendar view and content
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [scheduledContent, setScheduledContent] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState("month"); // 'month', 'week', 'day'
  const [currentMonth, setCurrentMonth] = useState(getMonth(new Date()));
  const [currentYear, setCurrentYear] = useState(getYear(new Date()));
  const [error, setError] = useState(null);
  const [selectedWeekStart, setSelectedWeekStart] = useState(
    startOfWeek(new Date())
  );

  // State for dialogs and popovers
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openPreviewDialog, setOpenPreviewDialog] = useState(false);
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedContent, setSelectedContent] = useState(null);
  const [draggedTime, setDraggedTime] = useState(null);

  // State for bulk actions
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [openBulkActionDialog, setOpenBulkActionDialog] = useState(false);
  const [bulkActionType, setBulkActionType] = useState(null); // 'delete', 'reschedule', 'status'
  const [bulkRescheduleDate, setBulkRescheduleDate] = useState(new Date());
  const [bulkStatusValue, setBulkStatusValue] = useState("scheduled");
  const [bulkActionAnchorEl, setBulkActionAnchorEl] = useState(null);

  // State for filters
  const [filterPlatform, setFilterPlatform] = useState("all");
  const [filterCampaign, setFilterCampaign] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [showDrafts, setShowDrafts] = useState(true);

  // Function to fetch scheduled content from API
  const fetchScheduledContent = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Calculate date range based on view mode
      let startDate, endDate;

      if (viewMode === 'month') {
        startDate = startOfMonth(new Date(currentYear, currentMonth));
        endDate = endOfMonth(new Date(currentYear, currentMonth));
      } else if (viewMode === 'week') {
        startDate = selectedWeekStart;
        endDate = endOfWeek(selectedWeekStart);
      } else {
        startDate = startOfDay(selectedDate);
        endDate = endOfDay(selectedDate);
      }

      // Format dates for API
      const formattedStartDate = format(startDate, 'yyyy-MM-dd');
      const formattedEndDate = format(endDate, 'yyyy-MM-dd');

      // Build query parameters for the scheduling API
      const params = {
        page: 1,
        limit: 500, // Get enough content for the calendar view
      };

      // Add filters if not set to 'all'
      if (filterPlatform !== 'all') params.platform = filterPlatform;
      if (filterStatus !== 'all') params.content_status = filterStatus;

      // Make API request to the correct endpoint
      const response = await api.get('/api/scheduling/schedules', { params });

      // Process response data - handle different response formats
      let schedules = [];

      if (response.data) {
        if (Array.isArray(response.data.schedules)) {
          schedules = response.data.schedules;
        } else if (Array.isArray(response.data)) {
          schedules = response.data;
        } else if (response.data.data && Array.isArray(response.data.data)) {
          schedules = response.data.data;
        }
      }

      if (schedules.length > 0) {
        // Convert and transform to calendar format
        const processedContent = schedules.map(item => ({
          id: item._id || item.id || `temp-${Date.now()}-${Math.random()}`,
          title: item.title || item.name || 'Untitled',
          content: item.content || item.text_content || item.description || '',
          scheduled_date: item.scheduled_date || item.scheduled_for || item.created_at ?
            new Date(item.scheduled_date || item.scheduled_for || item.created_at) : new Date(),
          platform: item.platform || item.platforms?.[0] || 'Unknown',
          campaign_id: item.campaign_id || item.metadata?.campaign_id || '',
          campaign_name: item.campaign_name || item.metadata?.campaign_name || 'Uncategorized',
          status: item.status || 'scheduled',
          priority: item.priority || item.metadata?.priority || 'medium',
          created_at: item.created_at ? new Date(item.created_at) : new Date(),
          created_by: item.created_by || item.user_id || 'You',
          tags: item.tags || [],
          image_url: item.image_url || item.images?.[0]?.url || null,
          end_date: item.end_date ? new Date(item.end_date) : null,
          repeat: item.repeat || item.metadata?.repeat || null,
          engagement_prediction: item.engagement_prediction || 'medium',
          optimal_time: item.optimal_time || false,
          duration: item.duration || 30,
          target_audience: item.target_audience || '',
          icp_id: item.icp_id || '',
          icp_name: item.icp_name || '',
          metadata: item.metadata || {}
        }));

        setScheduledContent(processedContent);
      } else {
        setScheduledContent([]);
      }
    } catch (error) {
      // Log error for debugging in development only
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching scheduled content:', error);
      }

      // Handle authentication errors
      if (error.response?.status === 401) {
        navigate('/login');
        return;
      }

      // Show user-friendly error message
      let errorMessage = "Unable to load scheduled content at this time.";
      if (error.response?.status === 403) {
        errorMessage = "You don&apos;t have permission to view scheduled content.";
      } else if (error.response?.status >= 500) {
        errorMessage = "Service temporarily unavailable. Please try again in a moment.";
      } else if (error.code === "ECONNREFUSED" || error.message === "Network Error") {
        errorMessage = "Connection error. Please check your internet connection and try again.";
      }

      setError(errorMessage);
      showErrorNotification(errorMessage);
      setScheduledContent([]);
    } finally {
      setLoading(false);
    }
  }, [
    currentMonth,
    currentYear,
    selectedDate,
    selectedWeekStart,
    viewMode,
    filterPlatform,
    filterCampaign,
    filterStatus,
    filterPriority,
    showDrafts,
    showErrorNotification,
    navigate
  ]);

  // Check authentication and redirect if needed
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Fetch content when dependencies change
  useEffect(() => {
    // Don't fetch if still loading auth or not authenticated
    if (authLoading || !isAuthenticated) {
      return;
    }
    fetchScheduledContent();
  }, [fetchScheduledContent, authLoading, isAuthenticated]);

  // Get unique platforms and campaigns for filters
  const platforms = [
    "all",
    ...new Set(scheduledContent.map((item) => item.platform).filter(Boolean)),
  ];
  const campaigns = [
    "all",
    ...new Set(scheduledContent.map((item) => item.campaign_name)),
  ];
  const statuses = ["all", "scheduled", "draft"];
  const priorities = ["all", "high", "medium", "low"];

  // Apply filters to content
  const filteredContent = scheduledContent.filter((item) => {
    if (filterPlatform !== "all" && item.platform !== filterPlatform)
      return false;
    if (filterCampaign !== "all" && item.campaign_name !== filterCampaign)
      return false;
    if (filterStatus !== "all" && item.status !== filterStatus) return false;
    if (filterPriority !== "all" && item.priority !== filterPriority)
      return false;
    if (!showDrafts && item.status === "draft") return false;
    return true;
  });

  // Get content for different views
  const contentForSelectedDate = filteredContent.filter((item) =>
    isSameDay(new Date(item.scheduled_date), selectedDate)
  );

  // Week and month content filters for different view implementations
  const contentForSelectedWeek = filteredContent.filter((item) => {
    const itemDate = new Date(item.scheduled_date);
    const weekStart = selectedWeekStart;
    const weekEnd = endOfWeek(selectedWeekStart);
    return itemDate >= weekStart && itemDate <= weekEnd;
  });

  const contentForCurrentMonth = filteredContent.filter((item) => {
    const itemDate = new Date(item.scheduled_date);
    return (
      getMonth(itemDate) === currentMonth && getYear(itemDate) === currentYear
    );
  });

  // Check for image data from location state and URL parameters
  useEffect(() => {
    const location = window.location;
    const params = new URLSearchParams(location.search);
    const locationState = window.history.state?.state;

    // Handle URL parameters for initial state
    const urlDate = params.get('date');
    const urlView = params.get('view');
    const urlPlatform = params.get('platform');

    if (urlDate) {
      const parsedDate = new Date(urlDate);
      if (!isNaN(parsedDate.getTime())) {
        setSelectedDate(parsedDate);
        setCurrentMonth(getMonth(parsedDate));
        setCurrentYear(getYear(parsedDate));
      }
    }

    if (urlView && ['month', 'week', 'day'].includes(urlView)) {
      setViewMode(urlView);
    }

    if (urlPlatform && urlPlatform !== 'all') {
      setFilterPlatform(urlPlatform);
    }

    // Check if we have image data from the image manipulation page
    if (locationState && locationState.imageData && locationState.fromImageManipulation) {
      const { imageData } = locationState;

      // Create a new content item with the image data
      const newContent = {
        id: `temp-${Date.now()}`, // Temporary ID until saved
        title: `Image Post - ${new Date().toLocaleDateString()}`,
        content: imageData.instructions || 'Image created with AI manipulation',
        scheduled_date: new Date(),
        platform: "LinkedIn", // Default platform
        campaign_id: "",
        campaign_name: "Uncategorized",
        status: "draft",
        image_url: imageData.url,
        created_at: new Date(),
        created_by: "You",
        priority: "medium",
        tags: ['manipulated', 'image'],
        engagement_prediction: "medium",
        optimal_time: false,
        duration: 30,
        repeat: null,
        end_date: null,
        metadata: {
          imageData: {
            size: imageData.size,
            style: imageData.style,
            instructions: imageData.instructions
          }
        }
      };

      // Open the edit dialog with the new content
      setSelectedContent(newContent);
      setOpenEditDialog(true);

      // Clear the location state to prevent reopening on refresh
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, []);

  // Fetch scheduled content from API
  useEffect(() => {
    // Initialize with empty array to prevent loading state issues
    setScheduledContent([]);

    // Flag to prevent multiple API calls
    let isMounted = true;
    let hasAttemptedFetch = false;

    const fetchScheduledContent = async () => {
      // Only attempt to fetch once
      if (hasAttemptedFetch) return;
      hasAttemptedFetch = true;

      setLoading(true);

      try {
        // Get all scheduled content using the correct endpoint
        const response = await api
          .get("/api/scheduling/schedules", {
            params: {
              start_date: subMonths(new Date(), 1).toISOString().split('T')[0], // Get content from last month
              end_date: addMonths(new Date(), 3).toISOString().split('T')[0], // Get content up to 3 months ahead
              limit: 500, // Get more content to ensure we have everything for the calendar
            },
            timeout: 5000, // 5 second timeout to prevent long waits
          })
          .catch((error) => {
            // Log error for debugging in development only
            if (process.env.NODE_ENV === 'development') {
              console.log("Error fetching content:", error.message);
            }
            return null;
          });

        // Only update if component is still mounted and we got a successful response
        if (
          isMounted &&
          response &&
          response.data &&
          Array.isArray(response.data.schedules)
        ) {
          // Transform API data to match our calendar format
          const formattedContent = response.data.schedules.map((item) => ({
            id: item._id || item.id,
            title: item.title || 'Untitled',
            content: item.content || item.text_content || '',
            scheduled_date: new Date(item.scheduled_date || item.scheduled_for || item.created_at),
            platform: item.platform || "Unknown",
            campaign_id: item.campaign_id || "",
            campaign_name: item.campaign_name || "Uncategorized",
            status: item.status || 'scheduled',
            image_url: item.image_url || null,
            created_at: new Date(item.created_at),
            created_by: item.created_by || "You",
            priority: item.priority || "medium",
            tags: item.tags || [],
            engagement_prediction: item.engagement_prediction || "medium",
            optimal_time: item.optimal_time || false,
            duration: item.duration || 30,
            repeat: item.repeat || null,
            end_date: item.end_date ? new Date(item.end_date) : null,
            target_audience: item.target_audience || "",
            icp_id: item.icp_id || "",
            icp_name: item.icp_name || "",
            metadata: item.metadata || {}
          }));

          setScheduledContent(formattedContent);
        }
      } catch (error) {
        // Log error for debugging in development only
        if (process.env.NODE_ENV === 'development') {
          console.log("Error in content fetch:", error.message);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Start fetch in the background
    fetchScheduledContent();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, []);

  // Navigation handlers
  const handleDateChange = (date) => {
    setSelectedDate(date);

    // Update week and month views when date changes
    if (viewMode === "week") {
      setSelectedWeekStart(startOfWeek(date));
    }

    setCurrentMonth(getMonth(date));
    setCurrentYear(getYear(date));

    // Update URL parameters
    const params = new URLSearchParams(window.location.search);
    params.set('date', format(date, 'yyyy-MM-dd'));
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  const handlePrevious = () => {
    if (viewMode === "month") {
      const newDate = subMonths(new Date(currentYear, currentMonth, 1), 1);
      setCurrentMonth(getMonth(newDate));
      setCurrentYear(getYear(newDate));
    } else if (viewMode === "week") {
      const newWeekStart = subDays(selectedWeekStart, 7);
      setSelectedWeekStart(newWeekStart);
      setSelectedDate(newWeekStart);
    } else if (viewMode === "day") {
      const newDate = subDays(selectedDate, 1);
      setSelectedDate(newDate);
    }
  };

  const handleNext = () => {
    if (viewMode === "month") {
      const newDate = addMonths(new Date(currentYear, currentMonth, 1), 1);
      setCurrentMonth(getMonth(newDate));
      setCurrentYear(getYear(newDate));
    } else if (viewMode === "week") {
      const newWeekStart = addDays(selectedWeekStart, 7);
      setSelectedWeekStart(newWeekStart);
      setSelectedDate(newWeekStart);
    } else if (viewMode === "day") {
      const newDate = addDays(selectedDate, 1);
      setSelectedDate(newDate);
    }
  };

  const handleToday = () => {
    const today = new Date();
    setSelectedDate(today);
    setCurrentMonth(getMonth(today));
    setCurrentYear(getYear(today));
    setSelectedWeekStart(startOfWeek(today));
  };

  const handleViewModeChange = (mode) => {
    setViewMode(mode);

    // Update URL parameters
    const params = new URLSearchParams(window.location.search);
    params.set('view', mode);
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  // Content management handlers
  const handleAddContent = (type = 'ai') => {
    navigate("/content/generator", {
      state: {
        scheduledDate: selectedDate,
        platform: filterPlatform !== "all" ? filterPlatform : null,
        campaign: filterCampaign !== "all" ? filterCampaign : null,
        isManualMode: type === 'manual'
      },
    });
  };

  const handleEditContent = (content) => {
    setSelectedContent(content);
    setOpenEditDialog(true);
  };

  // Helper function for ordinal suffixes
  const getOrdinalSuffix = (n) => {
    const s = ["th", "st", "nd", "rd"];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
  };

  // Generate recurring posts based on pattern
  const generateRecurringPosts = async (baseContent) => {
    if (!baseContent.repeat || !baseContent.end_date) return [];

    const startDate = new Date(baseContent.scheduled_date);
    const endDate = new Date(baseContent.end_date);
    const pattern = baseContent.repeat;
    const newPosts = [];

    // Calculate dates based on recurrence pattern
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      // Move to next occurrence based on pattern
      if (pattern === "daily") {
        currentDate = addDays(currentDate, 1);
      } else if (pattern === "weekly") {
        // For weekly, we need to handle specific days
        const repeatDays = baseContent.repeatDays || [getDay(startDate)];
        const currentDay = getDay(currentDate);

        // Find the next day in the repeatDays array
        let nextDayOffset = 1;
        let foundNextDay = false;

        for (let i = 1; i <= 7; i++) {
          const checkDay = (currentDay + i) % 7;
          if (repeatDays.includes(checkDay)) {
            nextDayOffset = i;
            foundNextDay = true;
            break;
          }
        }

        // If we didn't find a next day, move to the first day in the next week
        if (!foundNextDay) {
          nextDayOffset = 7 - currentDay + repeatDays[0];
        }

        currentDate = addDays(currentDate, nextDayOffset);
      } else if (pattern === "monthly") {
        // For monthly, handle day of month or day of week
        const monthlyType = baseContent.monthlyRepeatType || "dayOfMonth";

        if (monthlyType === "dayOfMonth") {
          // Same day each month
          const dayOfMonth = parseInt(format(startDate, "d"));
          currentDate = addMonths(currentDate, 1);

          // Adjust for months with fewer days
          const maxDaysInMonth = parseInt(format(endOfMonth(currentDate), "d"));
          if (dayOfMonth > maxDaysInMonth) {
            currentDate = endOfMonth(currentDate);
          } else {
            currentDate = setDate(currentDate, dayOfMonth);
          }
        } else {
          // Same day of week (e.g., "Third Monday")
          const dayOfWeek = getDay(startDate);
          const weekOfMonth = Math.ceil(parseInt(format(startDate, "d")) / 7);

          // Move to next month
          currentDate = addMonths(currentDate, 1);

          // Find the first occurrence of this day of week in the month
          const firstDayOfMonth = startOfMonth(currentDate);
          const firstOccurrence =
            getDay(firstDayOfMonth) <= dayOfWeek
              ? dayOfWeek - getDay(firstDayOfMonth)
              : 7 - (getDay(firstDayOfMonth) - dayOfWeek);

          // Calculate the target date
          currentDate = addDays(
            firstDayOfMonth,
            firstOccurrence + (weekOfMonth - 1) * 7
          );

          // If this pushes us into the next month, go back a week
          if (getMonth(currentDate) !== getMonth(firstDayOfMonth)) {
            currentDate = subDays(currentDate, 7);
          }
        }
      }

      // Skip the first occurrence as it's the original post
      if (currentDate <= endDate) {
        // Create a new post for this date
        const newPost = {
          title: baseContent.title,
          text_content: baseContent.content,
          platforms: [baseContent.platform],
          scheduled_for: currentDate,
          status: "scheduled",
          tags: baseContent.tags || [],
          metadata: {
            ...(baseContent.metadata || {}),
            priority: baseContent.priority,
            campaign_id: baseContent.campaign_id,
            campaign_name: baseContent.campaign_name,
            recurring_series_id: baseContent.id,
            recurrence_pattern: pattern,
          },
        };

        // Add images if present
        if (baseContent.image_url) {
          newPost.images = [
            {
              url: baseContent.image_url,
              prompt: baseContent.title,
              size: "1024x1024",
              style: "vivid",
            },
          ];
        }

        newPosts.push(newPost);
      }
    }

    return newPosts;
  };

  const handleSaveContent = async () => {
    setLoading(true);
    try {
      // Prepare data for API
      const contentData = {
        title: selectedContent.title,
        text_content: selectedContent.content,
        platforms: [selectedContent.platform],
        scheduled_for: selectedContent.scheduled_date,
        status: selectedContent.status,
        metadata: {
          ...(selectedContent.metadata || {}),
          priority: selectedContent.priority,
          campaign_id: selectedContent.campaign_id,
          campaign_name: selectedContent.campaign_name,
          // Add recurrence metadata
          repeat: selectedContent.repeat,
          end_date: selectedContent.end_date
            ? selectedContent.end_date.toISOString()
            : null,
          repeatDays: selectedContent.repeatDays,
          monthlyRepeatType: selectedContent.monthlyRepeatType,
        },
      };

      // Add images if present
      if (selectedContent.image_url) {
        contentData.images = [
          {
            url: selectedContent.image_url,
            prompt: selectedContent.metadata?.imageData?.instructions || selectedContent.title,
            size: selectedContent.metadata?.imageData?.size || "1024x1024",
            style: selectedContent.metadata?.imageData?.style || "vivid",
          },
        ];
      }

      let response;

      // Check if this is a temporary content (from image manipulation)
      if (selectedContent.id.startsWith('temp-')) {
        // Create new content
        response = await api.post('/api/content', contentData);
      } else {
        // Update existing content
        response = await api.put(
          `/api/content/${selectedContent.id}`,
          contentData
        );
      }

      // Update local state with response data
      if (response.data) {
        // Transform API response to match our calendar format
        const updatedContent = {
          ...selectedContent,
          id: response.data.id, // Use the new ID from the response
          title: response.data.title,
          content: response.data.text_content,
          scheduled_date: new Date(
            response.data.scheduled_for || response.data.created_at
          ),
          platform:
            response.data.platforms && response.data.platforms.length > 0
              ? response.data.platforms[0]
              : "Unknown",
          status: response.data.status,
          updated_at: new Date(response.data.updated_at),
          image_url:
            response.data.images && response.data.images.length > 0
              ? response.data.images[0].url
              : selectedContent.image_url,
          // Update recurrence properties
          repeat: response.data.metadata?.repeat || null,
          end_date: response.data.metadata?.end_date
            ? new Date(response.data.metadata.end_date)
            : null,
          repeatDays: response.data.metadata?.repeatDays,
          monthlyRepeatType: response.data.metadata?.monthlyRepeatType,
        };

        // Update the content in the local state
        let updatedScheduledContent;

        if (selectedContent.id.startsWith('temp-')) {
          // For new content, add it to the array
          updatedScheduledContent = [...scheduledContent, updatedContent];
        } else {
          // For existing content, update it in the array
          updatedScheduledContent = scheduledContent.map((item) =>
            item.id === selectedContent.id ? updatedContent : item
          );
        }

        // Handle recurring posts if enabled
        if (selectedContent.repeat && selectedContent.end_date) {
          // Generate recurring posts
          const recurringPosts = await generateRecurringPosts(updatedContent);

          // Create all recurring posts via API
          if (recurringPosts.length > 0) {
            try {
              // Remove any existing recurring posts with this series ID
              updatedScheduledContent = updatedScheduledContent.filter(
                (item) =>
                  item.metadata?.recurring_series_id !== selectedContent.id
              );

              // Create new recurring posts
              for (const post of recurringPosts) {
                const recurringResponse = await api.post("/api/content", post);

                if (recurringResponse.data) {
                  // Add the new post to the local state
                  const newPost = {
                    id: recurringResponse.data.id,
                    title: recurringResponse.data.title,
                    content: recurringResponse.data.text_content,
                    scheduled_date: new Date(
                      recurringResponse.data.scheduled_for
                    ),
                    platform: recurringResponse.data.platforms[0],
                    campaign_id: recurringResponse.data.metadata?.campaign_id,
                    campaign_name:
                      recurringResponse.data.metadata?.campaign_name,
                    status: recurringResponse.data.status,
                    image_url:
                      recurringResponse.data.images &&
                      recurringResponse.data.images.length > 0
                        ? recurringResponse.data.images[0].url
                        : null,
                    created_at: new Date(recurringResponse.data.created_at),
                    created_by: "You",
                    priority: recurringResponse.data.metadata?.priority,
                    tags: recurringResponse.data.tags,
                    repeat: null, // Individual instances don&apos;t have repeat
                    metadata: recurringResponse.data.metadata,
                  };

                  updatedScheduledContent.push(newPost);
                }
              }

              showSuccessNotification(
                `Created ${recurringPosts.length} recurring posts`
              );
            } catch (recurringError) {
              if (process.env.NODE_ENV === 'development') {
                console.error("Error creating recurring posts:", recurringError);
              }
              showErrorNotification(
                "Updated the main post but failed to create some recurring posts"
              );
            }
          }
        }

        setScheduledContent(updatedScheduledContent);
      } else {
        // Fallback to local update if API doesn't return updated data
        setScheduledContent(
          scheduledContent.map((item) =>
            item.id === selectedContent.id ? selectedContent : item
          )
        );
      }

      setOpenEditDialog(false);
      setSelectedContent(null);
      showSuccessNotification("Content updated successfully");
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error("Error updating content:", error);
      }
      showErrorNotification(
        "Failed to update content: " +
          (error.response?.data?.detail || error.message)
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteContent = async () => {
    setLoading(true);
    try {
      // Call API to delete content
      await api.delete(`/api/content/${selectedContent.id}`);

      // Update local state
      setScheduledContent(
        scheduledContent.filter((item) => item.id !== selectedContent.id)
      );

      setOpenDeleteDialog(false);
      setSelectedContent(null);
      showSuccessNotification("Content removed from schedule");
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error("Error deleting scheduled content:", error);
      }
      showErrorNotification(
        "Failed to remove content: " +
          (error.response?.data?.detail || error.message)
      );
    } finally {
      setLoading(false);
    }
  };

  // Bulk action handlers
  const handleToggleSelectionMode = () => {
    setSelectionMode(!selectionMode);
    if (selectionMode) {
      // Clear selections when exiting selection mode
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (contentId) => {
    if (selectedItems.includes(contentId)) {
      setSelectedItems(selectedItems.filter((id) => id !== contentId));
    } else {
      setSelectedItems([...selectedItems, contentId]);
    }
  };

  const handleSelectAll = (contentList) => {
    if (selectedItems.length === contentList.length) {
      // If all are selected, deselect all
      setSelectedItems([]);
    } else {
      // Otherwise, select all
      setSelectedItems(contentList.map((item) => item.id));
    }
  };

  const handleOpenBulkActionMenu = (event) => {
    setBulkActionAnchorEl(event.currentTarget);
  };

  const handleCloseBulkActionMenu = () => {
    setBulkActionAnchorEl(null);
  };

  const handleOpenBulkActionDialog = (actionType) => {
    setBulkActionType(actionType);
    setOpenBulkActionDialog(true);
    handleCloseBulkActionMenu();
  };

  const handleBulkAction = async (actionType, actionData) => {
    if (selectedItems.length === 0) {
      showErrorNotification("No items selected");
      return;
    }

    setLoading(true);

    try {
      if (actionType === "delete") {
        // Delete multiple items
        for (const itemId of selectedItems) {
          await api.delete(`/api/content/${itemId}`);
        }

        // Update local state
        setScheduledContent(
          scheduledContent.filter((item) => !selectedItems.includes(item.id))
        );
        showSuccessNotification(`Deleted ${selectedItems.length} items`);
      } else if (actionType === "reschedule") {
        // Reschedule multiple items
        const { date, preserveTime } = actionData;

        for (const itemId of selectedItems) {
          const item = scheduledContent.find(
            (content) => content.id === itemId
          );
          if (item) {
            // Calculate new date
            let newDate = new Date(date);

            if (preserveTime) {
              // Preserve original time
              const originalDate = new Date(item.scheduled_date);
              newDate.setHours(originalDate.getHours());
              newDate.setMinutes(originalDate.getMinutes());
            }

            await api.put(`/api/content/${itemId}`, {
              scheduled_for: newDate.toISOString(),
            });

            // Update local state
            const updatedItem = { ...item, scheduled_date: newDate };
            setScheduledContent((prev) =>
              prev.map((content) =>
                content.id === itemId ? updatedItem : content
              )
            );
          }
        }

        showSuccessNotification(`Rescheduled ${selectedItems.length} items`);
      } else if (actionType === "status") {
        // Update status for multiple items
        const { status } = actionData;

        for (const itemId of selectedItems) {
          await api.put(`/api/content/${itemId}`, {
            status: status,
          });

          // Update local state
          const item = scheduledContent.find(
            (content) => content.id === itemId
          );
          if (item) {
            const updatedItem = { ...item, status: status };
            setScheduledContent((prev) =>
              prev.map((content) =>
                content.id === itemId ? updatedItem : content
              )
            );
          }
        }

        showSuccessNotification(
          `Updated status for ${selectedItems.length} items to ${status}`
        );
      }

      // Reset selection
      setSelectionMode(false);
      setSelectedItems([]);
      setOpenBulkActionDialog(false);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error("Error performing bulk action:", error);
      }
      showErrorNotification(
        "Failed to complete bulk action: " +
          (error.response?.data?.detail || error.message)
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicateContent = async (content) => {
    setLoading(true);
    try {
      // Prepare data for API
      const createData = {
        title: `${content.title} (Copy)`,
        text_content: content.content,
        platforms: [content.platform],
        scheduled_for: addDays(new Date(content.scheduled_date), 1),
        status: "draft", // Set as draft initially
        tags: content.tags || [],
        metadata: {
          ...(content.metadata || {}),
          priority: content.priority,
          campaign_id: content.campaign_id,
          campaign_name: content.campaign_name,
          duplicated_from: content.id,
        },
      };

      // Add images if present
      if (content.image_url) {
        createData.images = [
          {
            url: content.image_url,
            prompt: content.title,
            size: "1024x1024",
            style: "vivid",
          },
        ];
      }

      // Call API to create new content
      const response = await api.post("/api/content", createData);

      if (response.data) {
        // Transform API response to match our calendar format
        const newContent = {
          id: response.data.id,
          title: response.data.title,
          content: response.data.text_content,
          scheduled_date: new Date(
            response.data.scheduled_for || response.data.created_at
          ),
          platform:
            response.data.platforms && response.data.platforms.length > 0
              ? response.data.platforms[0]
              : "Unknown",
          campaign_id:
            response.data.metadata?.campaign_id || content.campaign_id,
          campaign_name:
            response.data.metadata?.campaign_name || content.campaign_name,
          status: response.data.status,
          image_url:
            response.data.images && response.data.images.length > 0
              ? response.data.images[0].url
              : null,
          created_at: new Date(response.data.created_at),
          created_by: "You",
          priority: response.data.metadata?.priority || content.priority,
          tags: response.data.tags || [],
          engagement_prediction: content.engagement_prediction,
          optimal_time: false,
          duration: content.duration,
          repeat: null,
          end_date: null,
          target_audience: content.target_audience,
          icp_id: content.icp_id,
          icp_name: content.icp_name,
        };

        setScheduledContent([...scheduledContent, newContent]);
        showSuccessNotification("Content duplicated successfully");
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error("Error duplicating content:", error);
      }
      showErrorNotification(
        "Failed to duplicate content: " +
          (error.response?.data?.detail || error.message)
      );
    } finally {
      setLoading(false);
    }
  };

  const handlePreviewContent = (content) => {
    setSelectedContent(content);
    setOpenPreviewDialog(true);
  };

  // Dialog handlers
  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
    setSelectedContent(null);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setSelectedContent(null);
  };

  const handleClosePreviewDialog = () => {
    setOpenPreviewDialog(false);
    setSelectedContent(null);
  };

  // Menu handlers
  const handleOpenMenu = (event, content) => {
    setAnchorEl(event.currentTarget);
    setSelectedContent(content);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleCloseBulkActionDialog = () => {
    setOpenBulkActionDialog(false);
    setBulkActionType(null);
  };

  const handleExecuteBulkAction = async () => {
    setLoading(true);
    try {
      if (bulkActionType === "delete") {
        // Delete all selected items
        for (const item of selectedItems) {
          await api.delete(`/api/content/${item.id}`);
        }

        // Update local state
        setScheduledContent(
          scheduledContent.filter(
            (item) => !selectedItems.some((selected) => selected.id === item.id)
          )
        );

        showSuccessNotification(`Deleted ${selectedItems.length} posts`);
      } else if (bulkActionType === "reschedule") {
        // Reschedule all selected items
        const updatedItems = [];

        for (const item of selectedItems) {
          // Calculate new date while preserving time
          const originalTime = new Date(item.scheduled_date);
          const newDate = new Date(bulkRescheduleDate);
          newDate.setHours(originalTime.getHours());
          newDate.setMinutes(originalTime.getMinutes());

          const updateData = {
            scheduled_for: newDate,
          };

          const response = await api.put(`/api/content/${item.id}`, updateData);

          if (response.data) {
            updatedItems.push({
              ...item,
              scheduled_date: newDate,
            });
          }
        }

        // Update local state
        const newScheduledContent = [...scheduledContent];

        for (const updatedItem of updatedItems) {
          const index = newScheduledContent.findIndex(
            (item) => item.id === updatedItem.id
          );
          if (index !== -1) {
            newScheduledContent[index] = updatedItem;
          }
        }

        setScheduledContent(newScheduledContent);
        showSuccessNotification(`Rescheduled ${updatedItems.length} posts`);
      } else if (bulkActionType === "status") {
        // Change status of all selected items
        const updatedItems = [];

        for (const item of selectedItems) {
          const updateData = {
            status: bulkStatusValue,
          };

          const response = await api.put(`/api/content/${item.id}`, updateData);

          if (response.data) {
            updatedItems.push({
              ...item,
              status: bulkStatusValue,
            });
          }
        }

        // Update local state
        const newScheduledContent = [...scheduledContent];

        for (const updatedItem of updatedItems) {
          const index = newScheduledContent.findIndex(
            (item) => item.id === updatedItem.id
          );
          if (index !== -1) {
            newScheduledContent[index] = updatedItem;
          }
        }

        setScheduledContent(newScheduledContent);
        showSuccessNotification(
          `Updated status of ${updatedItems.length} posts to ${bulkStatusValue}`
        );
      }

      // Exit selection mode and clear selections
      setSelectionMode(false);
      setSelectedItems([]);
      handleCloseBulkActionDialog();
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error("Error performing bulk action:", error);
      }
      showErrorNotification(
        "Failed to perform bulk action: " +
          (error.response?.data?.detail || error.message)
      );
    } finally {
      setLoading(false);
    }
  };

  // Enhanced drag and drop handlers
  const handleDragStart = (e, position, content) => {
    // Set data for drag operation
    dragItem.current = position;
    setSelectedContent(content);

    // Add data to the dataTransfer object
    e.dataTransfer.setData(
      "text/plain",
      JSON.stringify({
        contentId: content.id,
        contentTitle: content.title,
        originalTime: content.scheduled_date,
      })
    );

    // Set drag image and effect
    const dragPreview = document.createElement("div");
    dragPreview.className = "drag-preview";
    dragPreview.innerHTML = `
      <div style="padding: 10px; background-color: ${alpha(
        theme.palette.primary.main,
        0.9
      )};
                  color: white; border-radius: 4px; box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                  max-width: 250px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
        <div style="font-weight: bold;">${content.title}</div>
        <div style="font-size: 12px;">${format(
          new Date(content.scheduled_date),
          "MMM d, yyyy h:mm a"
        )}</div>
      </div>
    `;
    document.body.appendChild(dragPreview);
    e.dataTransfer.setDragImage(dragPreview, 125, 30);
    e.dataTransfer.effectAllowed = "move";

    // Add a class to the dragged element for styling
    e.currentTarget.classList.add("content-dragging");

    // Clean up the preview element after drag starts
    setTimeout(() => {
      document.body.removeChild(dragPreview);
    }, 0);
  };

  const handleDragEnter = (e, position) => {
    dragOverItem.current = position;

    // Highlight the drop target
    e.currentTarget.classList.add("drag-over");
    e.preventDefault();
  };

  const handleDragOver = (e, timeSlot) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";

    // Update the dragged time for preview
    if (
      timeSlot &&
      (!draggedTime || timeSlot.getTime() !== draggedTime.getTime())
    ) {
      setDraggedTime(timeSlot);
    }
  };

  const handleDragLeave = (e) => {
    // Remove highlight from the drop target
    e.currentTarget.classList.remove("drag-over");
  };

  const handleDrop = async (e, newTime) => {
    e.preventDefault();

    // Remove highlight classes
    e.currentTarget.classList.remove("drag-over");
    document.querySelectorAll(".content-dragging").forEach((el) => {
      el.classList.remove("content-dragging");
    });

    if (selectedContent && newTime) {
      try {
        setLoading(true);

        // Create update request
        const updateData = {
          scheduled_for: newTime.toISOString(),
        };

        // Call API to update the scheduled time
        const response = await api.put(
          `/api/content/${selectedContent.id}`,
          updateData
        );

        if (response.data) {
          const updatedContent = {
            ...selectedContent,
            scheduled_date: newTime,
          };

          // Update local state
          setScheduledContent(
            scheduledContent.map((item) =>
              item.id === selectedContent.id ? updatedContent : item
            )
          );

          showSuccessNotification(
            `Rescheduled to ${format(newTime, "EEEE, MMMM d, yyyy h:mm a")}`
          );
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error("Error rescheduling content:", error);
        }
        showErrorNotification(
          "Failed to reschedule content: " +
            (error.response?.data?.detail || error.message)
        );
      } finally {
        setLoading(false);
      }
    }

    // Reset drag state
    dragItem.current = null;
    dragOverItem.current = null;
    setSelectedContent(null);
    setDraggedTime(null);
  };

  // Filter handlers
  const handleToggleFilterDrawer = () => {
    setOpenFilterDrawer(!openFilterDrawer);
  };

  const handleClearFilters = () => {
    setFilterPlatform("all");
    setFilterCampaign("all");
    setFilterStatus("all");
    setFilterPriority("all");
    setShowDrafts(true);
  };

  // Utility functions
  const getPlatformColor = (platform) => {
    switch (platform?.toLowerCase()) {
      case "linkedin":
        return "#0077B5";
      case "twitter":
        return "#1DA1F2";
      case "facebook":
        return "#4267B2";
      case "instagram":
        return "#C13584";
      default:
        return theme.palette.primary.main;
    }
  };

  const getPlatformIcon = (platform) => {
    switch (platform?.toLowerCase()) {
      case "linkedin":
        return (
          <img
            src="/icons/linkedin.svg"
            alt="LinkedIn"
            width={20}
            height={20}
          />
        );
      case "twitter":
        return (
          <img src="/icons/twitter.svg" alt="Twitter" width={20} height={20} />
        );
      case "facebook":
        return (
          <img
            src="/icons/facebook.svg"
            alt="Facebook"
            width={20}
            height={20}
          />
        );
      case "instagram":
        return (
          <img
            src="/icons/instagram.svg"
            alt="Instagram"
            width={20}
            height={20}
          />
        );
      default:
        return null;
    }
  };

  // Priority and status color utilities for UI enhancements
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.info.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled':
        return theme.palette.success.main;
      case 'draft':
        return theme.palette.grey[500];
      case 'published':
        return theme.palette.primary.main;
      case 'failed':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[400];
    }
  };
  //     case "high":
  //       return theme.palette.error.main;
  //     case "medium":
  //       return theme.palette.warning.main;
  //     case "low":
  //       return theme.palette.success.main;
  //     default:
  //       return theme.palette.grey[500];
  //   }
  // };

  // const getStatusColor = (status) => {
  //   switch (status) {
  //     case "scheduled":
  //       return theme.palette.success.main;
  //     case "draft":
  //       return theme.palette.grey[500];
  //     default:
  //       return theme.palette.grey[500];
  //   }
  // };

  // Get performance color based on engagement rate
  const getPerformanceColor = (engagementRate) => {
    if (engagementRate >= 0.05) return "#4CAF50"; // High (green)
    if (engagementRate >= 0.02) return "#FF9800"; // Medium (orange)
    return "#F44336"; // Low (red)
  };

  // Get performance label based on engagement rate
  const getPerformanceLabel = (engagementRate) => {
    if (engagementRate >= 0.05) return "High";
    if (engagementRate >= 0.02) return "Med";
    return "Low";
  };

  // Handle drag and drop for reordering content
  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const { source, destination, draggableId } = result;

    // If dropped in the same position, do nothing
    if (source.index === destination.index && source.droppableId === destination.droppableId) {
      return;
    }

    try {
      // Find the content item being dragged
      const draggedContent = scheduledContent.find(item => item.id === draggableId);
      if (!draggedContent) return;

      // Parse the destination date from droppableId (format: "day-YYYY-MM-DD")
      const destinationDateStr = destination.droppableId.replace('day-', '');
      const destinationDate = new Date(destinationDateStr);

      if (isNaN(destinationDate.getTime())) return;

      // Preserve the original time when moving to a new date
      const originalTime = new Date(draggedContent.scheduled_date);
      const newDateTime = new Date(destinationDate);
      newDateTime.setHours(originalTime.getHours());
      newDateTime.setMinutes(originalTime.getMinutes());

      // Update via API
      const response = await api.put(`/api/content/${draggedContent.id}`, {
        scheduled_for: newDateTime.toISOString(),
      });

      if (response.data) {
        // Update local state
        const updatedContent = {
          ...draggedContent,
          scheduled_date: newDateTime,
        };

        setScheduledContent(prev =>
          prev.map(item =>
            item.id === draggedContent.id ? updatedContent : item
          )
        );

        showSuccessNotification(
          `Moved to ${format(newDateTime, "EEEE, MMMM d, yyyy")}`
        );
      }
    } catch (error) {
      console.error("Error moving content:", error);
      showErrorNotification("Failed to move content");
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DragDropContext onDragEnd={handleDragEnd}>
        <Box sx={{ p: isEmbedded ? 0 : { xs: 1, sm: 3 }, height: "100%" }}>
        {!isEmbedded && (
          <Typography variant="h4" gutterBottom>
            Content Calendar
          </Typography>
        )}

        {/* Mobile-optimized view */}
        {isMobile ? (
          <MobileCalendarView
            scheduledContent={filteredContent}
            selectedDate={selectedDate}
            onDateChange={handleDateChange}
            onAddContent={handleAddContent}
            onEditContent={handleEditContent}
            onDeleteContent={(content) => {
              setSelectedContent(content);
              setOpenDeleteDialog(true);
            }}
            onScheduleContent={handleEditContent}
            onDuplicateContent={handleDuplicateContent}
            onViewContent={handlePreviewContent}
            viewMode={viewMode === "month" ? "week" : viewMode}
            onViewModeChange={handleViewModeChange}
            loading={loading}
          />
        ) : (
          <Grid container spacing={3}>
            {/* Calendar Controls */}
            <Grid item xs={12}>
              <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <IconButton onClick={handlePrevious}>
                      <NavigateBeforeIcon />
                    </IconButton>

                    <Typography variant="h6" sx={{ mx: 2 }}>
                      {viewMode === "month" &&
                        format(
                          new Date(currentYear, currentMonth),
                          "MMMM yyyy"
                        )}
                      {viewMode === "week" &&
                        `Week of ${format(selectedWeekStart, "MMM d, yyyy")}`}
                      {viewMode === "day" &&
                        format(selectedDate, "EEEE, MMMM d, yyyy")}
                    </Typography>

                    <IconButton onClick={handleNext}>
                      <NavigateNextIcon />
                    </IconButton>

                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<TodayIcon />}
                      onClick={handleToday}
                      sx={{ ml: 2 }}
                    >
                      Today
                    </Button>
                  </Box>

                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <ButtonGroup variant="outlined" size="small" sx={{ mr: 2 }}>
                      <Button
                        onClick={() => handleViewModeChange("month")}
                        variant={
                          viewMode === "month" ? "contained" : "outlined"
                        }
                      >
                        <CalendarViewMonthIcon
                          fontSize="small"
                          sx={{ mr: 0.5 }}
                        />
                        Month
                      </Button>
                      <Button
                        onClick={() => handleViewModeChange("week")}
                        variant={viewMode === "week" ? "contained" : "outlined"}
                      >
                        <CalendarViewWeekIcon
                          fontSize="small"
                          sx={{ mr: 0.5 }}
                        />
                        Week
                      </Button>
                      <Button
                        onClick={() => handleViewModeChange("day")}
                        variant={viewMode === "day" ? "contained" : "outlined"}
                      >
                        <CalendarViewDayIcon
                          fontSize="small"
                          sx={{ mr: 0.5 }}
                        />
                        Day
                      </Button>
                    </ButtonGroup>

                    <ButtonGroup variant="contained">
                      <Button
                        startIcon={<AddIcon />}
                        onClick={() => handleAddContent('ai')}
                      >
                        AI Content
                      </Button>
                      <Button
                        onClick={() => handleAddContent('manual')}
                        startIcon={<EditIcon />}
                      >
                        Manual
                      </Button>
                    </ButtonGroup>

                    <IconButton
                      sx={{ ml: 1 }}
                      onClick={handleToggleFilterDrawer}
                      color={
                        filterPlatform !== "all" ||
                        filterCampaign !== "all" ||
                        filterStatus !== "all" ||
                        filterPriority !== "all" ||
                        !showDrafts
                          ? "primary"
                          : "default"
                      }
                    >
                      <FilterListIcon />
                    </IconButton>
                  </Box>
                </Box>
              </Paper>
            </Grid>

            {/* Smart Scheduling Recommendations */}
            <Grid item xs={12} md={4}>
              <RecommendationsWidget
                onSchedule={(date, recommendation) => {
                  // Navigate to content creation with the recommended date
                  navigate("/content/generator", {
                    state: {
                      scheduledDate: date,
                      platform:
                        recommendation.platform !== "all"
                          ? recommendation.platform
                          : null,
                      campaign:
                        filterCampaign !== "all" ? filterCampaign : null,
                      optimalTime: true,
                    },
                  });
                }}
              />
            </Grid>

            {/* Smart Scheduling Assistant */}
            <Grid item xs={12} md={8}>
              <SmartSchedulingAssistant
                onSchedule={(date, recommendation) => {
                  // Navigate to content creation with the recommended date
                  navigate("/content/generator", {
                    state: {
                      scheduledDate: date,
                      platform:
                        recommendation.platform !== "all"
                          ? recommendation.platform
                          : null,
                      campaign:
                        filterCampaign !== "all" ? filterCampaign : null,
                      optimalTime: true,
                    },
                  });
                }}
                selectedPlatform={filterPlatform}
                selectedCampaign={filterCampaign}
              />
            </Grid>

            {/* Calendar View */}
            {viewMode === "month" && (
              <Grid item xs={12} md={4}>
                <Paper
                  elevation={2}
                  sx={{
                    p: 2,
                    height: "100%",
                    minHeight: 400,
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                  }}
                >
                  {(loading || authLoading) ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress />
                    </Box>
                  ) : !isAuthenticated ? (
                    // Don&apos;t render anything if not authenticated (will redirect)
                    null
                  ) : error ? (
                    <Box sx={{ textAlign: 'center', p: 3 }}>
                      <ErrorIcon color="error" sx={{ fontSize: 48, mb: 2 }} />
                      <Typography variant="h6" gutterBottom>
                        Error Loading Calendar
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        {error}
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<RefreshIcon />}
                        onClick={fetchScheduledContent}
                      >
                        Retry
                      </Button>
                    </Box>
                  ) : (
                    <DateCalendar
                      value={selectedDate}
                      onChange={handleDateChange}
                      slots={{
                        day: ServerDay,
                      }}
                      slotProps={{
                        day: {
                          scheduledContent: filteredContent,
                          selectedView: viewMode,
                        },
                      }}
                      views={["day"]}
                      // Set to the current month/year
                      defaultCalendarMonth={new Date(currentYear, currentMonth)}
                      sx={{ width: "100%", height: "auto" }}
                    />
                  )}
                </Paper>
              </Grid>
            )}

            {viewMode === "week" && (
              <Grid item xs={12} md={4}>
                <Paper elevation={2} sx={{ p: 2, height: "100%" }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Week View
                  </Typography>

                  <Box sx={{ mt: 2 }}>
                    {eachDayOfInterval({
                      start: selectedWeekStart,
                      end: endOfWeek(selectedWeekStart),
                    }).map((day) => (
                      <Box
                        key={day.toString()}
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          p: 1,
                          borderRadius: 1,
                          mb: 1,
                          cursor: "pointer",
                          ...(isSameDay(day, selectedDate) && {
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            fontWeight: "bold",
                          }),
                          "&:hover": {
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                          },
                          "&.drag-over": {
                            bgcolor: alpha(theme.palette.primary.main, 0.15),
                            boxShadow: `inset 0 0 0 2px ${theme.palette.primary.main}`,
                            transition: "all 0.2s ease",
                          },
                        }}
                        onClick={() => handleDateChange(day)}
                        onDragOver={(e) => handleDragOver(e, setHours(day, 9))} // Default to 9 AM
                        onDragEnter={(e) =>
                          handleDragEnter(e, format(day, "yyyy-MM-dd"))
                        }
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => handleDrop(e, setHours(day, 9))}
                      >
                        <Box
                          sx={{
                            width: 36,
                            height: 36,
                            borderRadius: "50%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            mr: 1,
                            ...(isSameDay(day, new Date()) && {
                              bgcolor: theme.palette.primary.main,
                              color: "white",
                            }),
                          }}
                        >
                          {format(day, "d")}
                        </Box>

                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2">
                            {format(day, "EEEE")}
                          </Typography>

                          <Box sx={{ display: "flex", mt: 0.5 }}>
                            {filteredContent
                              .filter((item) =>
                                isSameDay(new Date(item.scheduled_date), day)
                              )
                              .slice(0, 3)
                              .map((content, index) => (
                                <Box
                                  key={index}
                                  sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: "50%",
                                    bgcolor: getPlatformColor(content.platform),
                                    mr: 0.5,
                                  }}
                                />
                              ))}

                            {filteredContent.filter((item) =>
                              isSameDay(new Date(item.scheduled_date), day)
                            ).length > 3 && (
                              <Typography variant="caption" sx={{ ml: 0.5 }}>
                                +
                                {filteredContent.filter((item) =>
                                  isSameDay(new Date(item.scheduled_date), day)
                                ).length - 3}{" "}
                                more
                              </Typography>
                            )}
                          </Box>
                        </Box>

                        <Badge
                          badgeContent={
                            filteredContent.filter((item) =>
                              isSameDay(new Date(item.scheduled_date), day)
                            ).length
                          }
                          color="primary"
                          sx={{ ml: 1 }}
                        />
                      </Box>
                    ))}
                  </Box>
                </Paper>
              </Grid>
            )}

            {viewMode === "day" && (
              <Grid item xs={12} md={4}>
                <Paper elevation={2} sx={{ p: 2, height: "100%" }}>
                  <Typography variant="subtitle1" gutterBottom>
                    {format(selectedDate, "EEEE, MMMM d")}
                  </Typography>

                  <Box
                    sx={{ display: "flex", justifyContent: "center", my: 2 }}
                  >
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: "50%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        bgcolor: theme.palette.primary.main,
                        color: "white",
                        fontSize: "1.5rem",
                        fontWeight: "bold",
                      }}
                    >
                      {format(selectedDate, "d")}
                    </Box>
                  </Box>

                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Time Slots
                    </Typography>

                    <Box
                      sx={{
                        maxHeight: 400,
                        overflowY: "auto",
                        pr: 1,
                        "&::-webkit-scrollbar": {
                          width: 6,
                        },
                        "&::-webkit-scrollbar-thumb": {
                          backgroundColor: alpha(
                            theme.palette.primary.main,
                            0.2
                          ),
                          borderRadius: 3,
                        },
                      }}
                    >
                      {Array.from({ length: 24 }).map((_, hour) => {
                        const timeSlot = setHours(selectedDate, hour);
                        const contentInHour = filteredContent.filter((item) => {
                          const itemDate = new Date(item.scheduled_date);
                          return (
                            isSameDay(itemDate, selectedDate) &&
                            getHours(itemDate) === hour
                          );
                        });

                        return (
                          <Box
                            key={hour}
                            sx={{
                              display: "flex",
                              p: 1,
                              borderLeft: `2px solid ${
                                contentInHour.length > 0
                                  ? theme.palette.primary.main
                                  : theme.palette.divider
                              }`,
                              mb: 1,
                              cursor: "pointer",
                              "&:hover": {
                                bgcolor: alpha(
                                  theme.palette.primary.main,
                                  0.05
                                ),
                              },
                              "&.drag-over": {
                                bgcolor: alpha(
                                  theme.palette.primary.main,
                                  0.15
                                ),
                                boxShadow: `inset 0 0 0 2px ${theme.palette.primary.main}`,
                                transition: "all 0.2s ease",
                              },
                            }}
                            onClick={() => {
                              if (contentInHour.length === 0) {
                                // Create new content at this time
                                const newDate = setHours(selectedDate, hour);
                                navigate("/content/generator", {
                                  state: {
                                    scheduledDate: newDate,
                                    platform:
                                      filterPlatform !== "all"
                                        ? filterPlatform
                                        : null,
                                    campaign:
                                      filterCampaign !== "all"
                                        ? filterCampaign
                                        : null,
                                  },
                                });
                              }
                            }}
                            onDragOver={(e) =>
                              handleDragOver(e, setHours(selectedDate, hour))
                            }
                            onDragEnter={(e) => handleDragEnter(e, hour)}
                            onDragLeave={handleDragLeave}
                            onDrop={(e) =>
                              handleDrop(e, setHours(selectedDate, hour))
                            }
                          >
                            <Typography
                              variant="body2"
                              sx={{
                                width: 60,
                                fontWeight:
                                  contentInHour.length > 0 ? "bold" : "normal",
                                color:
                                  contentInHour.length > 0
                                    ? theme.palette.primary.main
                                    : "inherit",
                              }}
                            >
                              {format(timeSlot, "h:00 a")}
                            </Typography>

                            <Box sx={{ flex: 1, overflow: "hidden" }}>
                              {contentInHour.length > 0 ? (
                                contentInHour.map((content, index) => (
                                  <Box
                                    key={index}
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      mb: 0.5,
                                    }}
                                  >
                                    {selectionMode && (
                                      <Checkbox
                                        checked={selectedItems.includes(
                                          content.id
                                        )}
                                        onChange={() =>
                                          handleSelectItem(content.id)
                                        }
                                        size="small"
                                        sx={{ mr: 0.5, p: 0.5 }}
                                      />
                                    )}
                                    <Chip
                                      size="small"
                                      label={content.title}
                                      onClick={() =>
                                        selectionMode
                                          ? handleSelectItem(content.id)
                                          : handleEditContent(content)
                                      }
                                      sx={{
                                        backgroundColor: selectedItems.includes(
                                          content.id
                                        )
                                          ? alpha(
                                              theme.palette.primary.main,
                                              0.15
                                            )
                                          : alpha(
                                              getPlatformColor(
                                                content.platform
                                              ),
                                              0.1
                                            ),
                                        borderLeft: `3px solid ${
                                          selectedItems.includes(content.id)
                                            ? theme.palette.primary.main
                                            : getPlatformColor(content.platform)
                                        }`,
                                        "& .MuiChip-label": {
                                          paddingLeft: 0.5,
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                          whiteSpace: "nowrap",
                                          maxWidth: "100%",
                                        },
                                      }}
                                    />
                                  </Box>
                                ))
                              ) : (
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    height: "100%",
                                    opacity: 0.5,
                                  }}
                                >
                                  <AddIcon fontSize="small" sx={{ mr: 0.5 }} />
                                  <Typography variant="caption">
                                    Add content
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                          </Box>
                        );
                      })}
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            )}

            {/* Scheduled Content for Selected Date */}
            <Grid item xs={12} md={8}>
              <Paper elevation={2} sx={{ p: 2, height: "100%" }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 2,
                  }}
                >
                  <Typography variant="h6">
                    Scheduled for {format(selectedDate, "EEEE, MMMM d, yyyy")}
                  </Typography>

                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    {selectionMode ? (
                      <>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={
                                selectedItems.length ===
                                  contentForSelectedDate.length &&
                                contentForSelectedDate.length > 0
                              }
                              indeterminate={
                                selectedItems.length > 0 &&
                                selectedItems.length <
                                  contentForSelectedDate.length
                              }
                              onChange={() =>
                                handleSelectAll(contentForSelectedDate)
                              }
                              size="small"
                            />
                          }
                          label={`${selectedItems.length} selected`}
                          sx={{ mr: 1 }}
                        />

                        <Button
                          variant="contained"
                          color="primary"
                          disabled={selectedItems.length === 0}
                          onClick={handleOpenBulkActionMenu}
                          endIcon={<ArrowDropDownIcon />}
                          size="small"
                          sx={{ mr: 1 }}
                        >
                          Actions
                        </Button>

                        <Button
                          variant="outlined"
                          size="small"
                          onClick={handleToggleSelectionMode}
                        >
                          Cancel
                        </Button>
                      </>
                    ) : (
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<CheckBoxOutlineBlankIcon />}
                        onClick={handleToggleSelectionMode}
                        sx={{ ml: 1 }}
                      >
                        Select
                      </Button>
                    )}
                  </Box>
                </Box>

                {contentForSelectedDate.length === 0 ? (
                  <Box sx={{ p: 3, textAlign: "center" }}>
                    <CalendarTodayIcon sx={{ fontSize: 48, color: alpha(theme.palette.primary.main, 0.6), mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      No Content Scheduled
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                      There&apos;s no content scheduled for {format(selectedDate, "MMMM d, yyyy")}.
                      Create new content or schedule existing content for this date.
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 2 }}>
                      <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={handleAddContent}
                      >
                        Schedule Content
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<EventIcon />}
                        onClick={() => navigate('/content/library')}
                      >
                        View Content Library
                      </Button>
                    </Box>
                  </Box>
                ) : (
                  <Box>
                    {contentForSelectedDate.map((content, index) => (
                      <CustomCard
                        key={content.id}
                        variant="glass"
                        elevation={1}
                        hoverable={true}
                        sx={{
                          mb: 2,
                          transition: "all 0.2s ease-in-out",
                          overflow: "hidden",
                          "&.content-dragging": {
                            opacity: 0.6,
                            transform: "scale(0.98)",
                            outline: `2px dashed ${theme.palette.primary.main}`,
                          },
                          ...(content.status === "draft" && {
                            borderLeft: `4px solid ${theme.palette.grey[500]}`,
                          }),
                          ...(content.priority === "high" && {
                            borderLeft: `4px solid ${theme.palette.error.main}`,
                          }),
                          ...(content.priority === "medium" && {
                            borderLeft: `4px solid ${theme.palette.warning.main}`,
                          }),
                          ...(selectedItems.some(
                            (item) => item.id === content.id
                          ) && {
                            backgroundColor: alpha(
                              theme.palette.primary.main,
                              0.1
                            ),
                            borderColor: theme.palette.primary.main,
                            borderWidth: 1,
                            borderStyle: "solid",
                          }),
                        }}
                        draggable={!selectionMode}
                        onDragStart={(e) =>
                          !selectionMode && handleDragStart(e, index, content)
                        }
                        onDragEnter={(e) =>
                          !selectionMode && handleDragEnter(e, index)
                        }
                        onClick={() =>
                          selectionMode && handleSelectItem(content.id)
                        }
                      >
                        <CustomCardContent sx={{ overflow: "hidden" }}>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "flex-start",
                            }}
                          >
                            <Box sx={{ flex: 1, overflow: "hidden" }}>
                              <Box
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  mb: 1,
                                }}
                              >
                                {selectionMode ? (
                                  <Checkbox
                                    checked={selectedItems.includes(content.id)}
                                    onChange={() =>
                                      handleSelectItem(content.id)
                                    }
                                    size="small"
                                    sx={{ ml: -1, mr: 0.5 }}
                                  />
                                ) : (
                                  <DragIndicatorIcon
                                    fontSize="small"
                                    sx={{
                                      color: theme.palette.text.secondary,
                                      mr: 1,
                                      cursor: "grab",
                                    }}
                                  />
                                )}
                                <Typography
                                  variant="h6"
                                  noWrap
                                  sx={{ maxWidth: "80%" }}
                                >
                                  {content.title}
                                </Typography>
                              </Box>

                              <Typography
                                variant="body2"
                                color="textSecondary"
                                sx={{ mb: 1 }}
                              >
                                <ScheduleIcon
                                  fontSize="small"
                                  sx={{ verticalAlign: "middle", mr: 0.5 }}
                                />
                                {format(
                                  new Date(content.scheduled_date),
                                  "h:mm a"
                                )}
                                {content.optimal_time && (
                                  <Tooltip title="Optimal posting time based on audience analytics">
                                    <CheckCircleIcon
                                      fontSize="small"
                                      color="success"
                                      sx={{ ml: 1, verticalAlign: "middle" }}
                                    />
                                  </Tooltip>
                                )}
                                {content.repeat && (
                                  <Tooltip
                                    title={`Recurring ${
                                      content.repeat
                                    } post until ${format(
                                      new Date(content.end_date),
                                      "MMM d, yyyy"
                                    )}`}
                                  >
                                    <AccessTimeIcon
                                      fontSize="small"
                                      color="primary"
                                      sx={{ ml: 1, verticalAlign: "middle" }}
                                    />
                                  </Tooltip>
                                )}
                                {content.metadata?.recurring_series_id && (
                                  <Tooltip title={`Part of a recurring series`}>
                                    <Badge
                                      variant="dot"
                                      color="primary"
                                      sx={{
                                        ml: 1,
                                        "& .MuiBadge-badge": {
                                          height: 8,
                                          minWidth: 8,
                                        },
                                      }}
                                    >
                                      <AccessTimeIcon
                                        fontSize="small"
                                        sx={{
                                          color: theme.palette.text.secondary,
                                          verticalAlign: "middle",
                                        }}
                                      />
                                    </Badge>
                                  </Tooltip>
                                )}

                                {/* Performance Metrics */}
                                {content.status === "published" &&
                                  content.analytics && (
                                    <Tooltip
                                      title={`Performance: ${
                                        content.analytics.engagement_rate
                                          ? (
                                              content.analytics
                                                .engagement_rate * 100
                                            ).toFixed(1) + "% engagement"
                                          : "No data yet"
                                      }`}
                                    >
                                      <Box
                                        component="span"
                                        sx={{
                                          display: "inline-flex",
                                          alignItems: "center",
                                          ml: 1,
                                          bgcolor: getPerformanceColor(
                                            content.analytics.engagement_rate ||
                                              0
                                          ),
                                          color: "white",
                                          borderRadius: "12px",
                                          px: 0.8,
                                          py: 0.2,
                                          fontSize: "0.7rem",
                                          fontWeight: "bold",
                                        }}
                                      >
                                        {getPerformanceLabel(
                                          content.analytics.engagement_rate || 0
                                        )}
                                      </Box>
                                    </Tooltip>
                                  )}
                              </Typography>

                              <Box
                                sx={{
                                  mt: 1,
                                  display: "flex",
                                  flexWrap: "wrap",
                                  gap: 0.5,
                                }}
                              >
                                <Chip
                                  label={content.platform}
                                  size="small"
                                  sx={{
                                    backgroundColor: getPlatformColor(
                                      content.platform
                                    ),
                                    color: "white",
                                  }}
                                  icon={getPlatformIcon(content.platform)}
                                />
                                <Chip
                                  label={content.campaign_name}
                                  size="small"
                                  variant="outlined"
                                />
                                <Chip
                                  label={content.status}
                                  size="small"
                                  color={
                                    content.status === "scheduled"
                                      ? "success"
                                      : "default"
                                  }
                                  variant="outlined"
                                />
                              </Box>
                            </Box>

                            <Box>
                              <ButtonGroup size="small" variant="outlined">
                                <Tooltip title="Preview">
                                  <IconButton
                                    size="small"
                                    onClick={() =>
                                      handlePreviewContent(content)
                                    }
                                  >
                                    <VisibilityIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Edit">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleEditContent(content)}
                                    color="primary"
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="More options">
                                  <IconButton
                                    size="small"
                                    onClick={(e) => handleOpenMenu(e, content)}
                                  >
                                    <MoreVertIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </ButtonGroup>
                            </Box>
                          </Box>

                          {content.image_url && (
                            <Box
                              sx={{
                                mt: 2,
                                height: 100,
                                overflow: "hidden",
                                borderRadius: 1,
                                position: "relative",
                                "&:hover .preview-overlay": {
                                  opacity: 1,
                                },
                              }}
                              onClick={() => handlePreviewContent(content)}
                            >
                              <img
                                src={content.image_url}
                                alt={content.title}
                                style={{
                                  width: "100%",
                                  height: "100%",
                                  objectFit: "cover",
                                }}
                              />
                              <Box
                                className="preview-overlay"
                                sx={{
                                  position: "absolute",
                                  top: 0,
                                  left: 0,
                                  right: 0,
                                  bottom: 0,
                                  backgroundColor: "rgba(0,0,0,0.5)",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  opacity: 0,
                                  transition: "opacity 0.2s",
                                  cursor: "pointer",
                                }}
                              >
                                <VisibilityIcon sx={{ color: "white" }} />
                              </Box>
                            </Box>
                          )}

                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              mt: 2,
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                              display: "-webkit-box",
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: "vertical",
                            }}
                          >
                            {content.content}
                          </Typography>

                          {/* Analytics Metrics */}
                          {content.status === "published" &&
                            content.analytics && (
                              <Box
                                sx={{
                                  mt: 2,
                                  display: "flex",
                                  flexWrap: "wrap",
                                  gap: 1,
                                }}
                              >
                                <Tooltip title="Engagement Rate">
                                  <Chip
                                    size="small"
                                    label={`${
                                      content.analytics.engagement_rate
                                        ? (
                                            content.analytics.engagement_rate *
                                            100
                                          ).toFixed(1) + "% Engagement"
                                        : "No engagement data"
                                    }`}
                                    sx={{
                                      bgcolor: getPerformanceColor(
                                        content.analytics.engagement_rate || 0
                                      ),
                                      color: "white",
                                      fontWeight: "bold",
                                    }}
                                  />
                                </Tooltip>

                                {content.analytics.likes !== undefined && (
                                  <Tooltip title="Likes">
                                    <Chip
                                      size="small"
                                      label={`${content.analytics.likes} Likes`}
                                      variant="outlined"
                                    />
                                  </Tooltip>
                                )}

                                {content.analytics.comments !== undefined && (
                                  <Tooltip title="Comments">
                                    <Chip
                                      size="small"
                                      label={`${content.analytics.comments} Comments`}
                                      variant="outlined"
                                    />
                                  </Tooltip>
                                )}

                                {content.analytics.shares !== undefined && (
                                  <Tooltip title="Shares">
                                    <Chip
                                      size="small"
                                      label={`${content.analytics.shares} Shares`}
                                      variant="outlined"
                                    />
                                  </Tooltip>
                                )}

                                {content.analytics.clicks !== undefined && (
                                  <Tooltip title="Clicks">
                                    <Chip
                                      size="small"
                                      label={`${content.analytics.clicks} Clicks`}
                                      variant="outlined"
                                    />
                                  </Tooltip>
                                )}

                                {content.analytics.impressions !==
                                  undefined && (
                                  <Tooltip title="Impressions">
                                    <Chip
                                      size="small"
                                      label={`${content.analytics.impressions} Impressions`}
                                      variant="outlined"
                                    />
                                  </Tooltip>
                                )}
                              </Box>
                            )}
                        </CustomCardContent>
                      </CustomCard>
                    ))}
                  </Box>
                )}

                {/* Content Menu */}
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleCloseMenu}
                >
                  <MenuItem
                    onClick={() => {
                      handleCloseMenu();
                      handleDuplicateContent(selectedContent);
                    }}
                  >
                    <ListItemIcon>
                      <ContentCopyIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Duplicate</ListItemText>
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      handleCloseMenu();
                      setOpenDeleteDialog(true);
                    }}
                  >
                    <ListItemIcon>
                      <DeleteIcon fontSize="small" color="error" />
                    </ListItemIcon>
                    <ListItemText>Delete</ListItemText>
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      handleCloseMenu();
                      // Open in content editor
                      navigate(`/content/editor/${selectedContent.id}`);
                    }}
                  >
                    <ListItemIcon>
                      <EditIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Open in Editor</ListItemText>
                  </MenuItem>
                  <MenuItem
                    onClick={() => {
                      handleCloseMenu();
                      // Share functionality
                      showSuccessNotification("Share link copied to clipboard");
                    }}
                  >
                    <ListItemIcon>
                      <ShareIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Share</ListItemText>
                  </MenuItem>
                </Menu>
              </Paper>
            </Grid>
          </Grid>
        )}

        {/* Edit Dialog */}
        <Dialog
          open={openEditDialog}
          onClose={handleCloseEditDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Typography variant="h6">Edit Scheduled Content</Typography>
              <Chip
                label={selectedContent?.status || "draft"}
                color={
                  selectedContent?.status === "scheduled"
                    ? "success"
                    : "default"
                }
                size="small"
              />
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {selectedContent && (
              <Box sx={{ pt: 1 }}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      label="Title"
                      value={selectedContent.title}
                      onChange={(e) => {
                        setSelectedContent({
                          ...selectedContent,
                          title: e.target.value,
                        });
                      }}
                      fullWidth
                      variant="outlined"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <DatePicker
                      label="Scheduled Date"
                      value={new Date(selectedContent.scheduled_date)}
                      onChange={(newDate) => {
                        // Preserve the time from the original date
                        const originalDate = new Date(
                          selectedContent.scheduled_date
                        );
                        newDate = setHours(newDate, getHours(originalDate));
                        newDate = setMinutes(newDate, getMinutes(originalDate));

                        setSelectedContent({
                          ...selectedContent,
                          scheduled_date: newDate,
                        });
                      }}
                      sx={{ width: "100%" }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TimePicker
                      label="Scheduled Time"
                      value={new Date(selectedContent.scheduled_date)}
                      onChange={(newTime) => {
                        // Preserve the date but update the time
                        const currentDate = new Date(
                          selectedContent.scheduled_date
                        );
                        const updatedDate = setHours(
                          setMinutes(currentDate, getMinutes(newTime)),
                          getHours(newTime)
                        );

                        setSelectedContent({
                          ...selectedContent,
                          scheduled_date: updatedDate,
                        });
                      }}
                      sx={{ width: "100%" }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Platform</InputLabel>
                      <Select
                        value={selectedContent.platform}
                        label="Platform"
                        onChange={(e) => {
                          setSelectedContent({
                            ...selectedContent,
                            platform: e.target.value,
                          });
                        }}
                      >
                        <MenuItem value="LinkedIn">LinkedIn</MenuItem>
                        <MenuItem value="Twitter">Twitter</MenuItem>
                        <MenuItem value="Facebook">Facebook</MenuItem>
                        <MenuItem value="Instagram">Instagram</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Priority</InputLabel>
                      <Select
                        value={selectedContent.priority}
                        label="Priority"
                        onChange={(e) => {
                          setSelectedContent({
                            ...selectedContent,
                            priority: e.target.value,
                          });
                        }}
                      >
                        <MenuItem value="high">High</MenuItem>
                        <MenuItem value="medium">Medium</MenuItem>
                        <MenuItem value="low">Low</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={Boolean(selectedContent.repeat)}
                          onChange={(e) => {
                            setSelectedContent({
                              ...selectedContent,
                              repeat: e.target.checked ? "weekly" : null,
                              end_date: e.target.checked
                                ? addMonths(
                                    new Date(selectedContent.scheduled_date),
                                    3
                                  )
                                : null,
                            });
                          }}
                          color="primary"
                        />
                      }
                      label="Recurring Post"
                    />
                  </Grid>

                  {selectedContent.repeat && (
                    <>
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>Recurrence Pattern</InputLabel>
                          <Select
                            value={selectedContent.repeat}
                            label="Recurrence Pattern"
                            onChange={(e) => {
                              setSelectedContent({
                                ...selectedContent,
                                repeat: e.target.value,
                              });
                            }}
                          >
                            <MenuItem value="daily">Daily</MenuItem>
                            <MenuItem value="weekly">Weekly</MenuItem>
                            <MenuItem value="monthly">Monthly</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <DatePicker
                          label="End Date"
                          value={
                            selectedContent.end_date
                              ? new Date(selectedContent.end_date)
                              : null
                          }
                          onChange={(newDate) => {
                            setSelectedContent({
                              ...selectedContent,
                              end_date: newDate,
                            });
                          }}
                          sx={{ width: "100%" }}
                          minDate={addDays(
                            new Date(selectedContent.scheduled_date),
                            1
                          )}
                        />
                      </Grid>

                      {selectedContent.repeat === "weekly" && (
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" gutterBottom>
                            Repeat on these days:
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              flexWrap: "wrap",
                              gap: 1,
                              mt: 1,
                            }}
                          >
                            {[
                              "Sunday",
                              "Monday",
                              "Tuesday",
                              "Wednesday",
                              "Thursday",
                              "Friday",
                              "Saturday",
                            ].map((day, index) => {
                              const daySelected =
                                selectedContent.repeatDays?.includes(index) ||
                                (getDay(
                                  new Date(selectedContent.scheduled_date)
                                ) === index &&
                                  !selectedContent.repeatDays);
                              return (
                                <Chip
                                  key={day}
                                  label={day.substring(0, 3)}
                                  onClick={() => {
                                    const currentDays =
                                      selectedContent.repeatDays || [
                                        getDay(
                                          new Date(
                                            selectedContent.scheduled_date
                                          )
                                        ),
                                      ];
                                    let newDays;

                                    if (daySelected) {
                                      // Don&apos;t allow removing the last day
                                      if (currentDays.length > 1) {
                                        newDays = currentDays.filter(
                                          (d) => d !== index
                                        );
                                      } else {
                                        newDays = currentDays;
                                      }
                                    } else {
                                      newDays = [...currentDays, index];
                                    }

                                    setSelectedContent({
                                      ...selectedContent,
                                      repeatDays: newDays,
                                    });
                                  }}
                                  color={daySelected ? "primary" : "default"}
                                  variant={daySelected ? "filled" : "outlined"}
                                  sx={{ minWidth: 70 }}
                                />
                              );
                            })}
                          </Box>
                        </Grid>
                      )}

                      {selectedContent.repeat === "monthly" && (
                        <Grid item xs={12}>
                          <FormControl component="fieldset">
                            <RadioGroup
                              value={
                                selectedContent.monthlyRepeatType ||
                                "dayOfMonth"
                              }
                              onChange={(e) => {
                                setSelectedContent({
                                  ...selectedContent,
                                  monthlyRepeatType: e.target.value,
                                });
                              }}
                            >
                              <FormControlLabel
                                value="dayOfMonth"
                                control={<Radio />}
                                label={`Monthly on day ${format(
                                  new Date(selectedContent.scheduled_date),
                                  "d"
                                )}`}
                              />
                              <FormControlLabel
                                value="dayOfWeek"
                                control={<Radio />}
                                label={`Monthly on the ${getOrdinalSuffix(
                                  Math.ceil(
                                    parseInt(
                                      format(
                                        new Date(
                                          selectedContent.scheduled_date
                                        ),
                                        "d"
                                      )
                                    ) / 7
                                  )
                                )} ${format(
                                  new Date(selectedContent.scheduled_date),
                                  "EEEE"
                                )}`}
                              />
                            </RadioGroup>
                          </FormControl>
                        </Grid>
                      )}

                      <Grid item xs={12}>
                        <Alert severity="info" sx={{ mt: 1 }}>
                          This will create a series of posts following this
                          recurrence pattern. Each post can still be
                          individually edited or deleted.
                        </Alert>
                      </Grid>
                    </>
                  )}

                  <Grid item xs={12}>
                    <TextField
                      label="Content"
                      value={selectedContent.content}
                      onChange={(e) => {
                        setSelectedContent({
                          ...selectedContent,
                          content: e.target.value,
                        });
                      }}
                      fullWidth
                      multiline
                      rows={4}
                      variant="outlined"
                    />
                  </Grid>

                  {selectedContent.image_url && (
                    <Grid item xs={12}>
                      <Box sx={{ textAlign: "center" }}>
                        <img
                          src={selectedContent.image_url}
                          alt={selectedContent.title}
                          style={{
                            maxWidth: "100%",
                            maxHeight: "200px",
                            borderRadius: "8px",
                            boxShadow: theme.shadows[1],
                          }}
                        />
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseEditDialog}>Cancel</Button>
            <Button
              variant="contained"
              onClick={handleSaveContent}
              startIcon={
                loading ? <CircularProgress size={20} color="inherit" /> : null
              }
              disabled={loading}
            >
              Save Changes
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>
          <DialogTitle>Delete Scheduled Post</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete &quot;{selectedContent?.title}&quot;? This
              action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDeleteDialog}>Cancel</Button>
            <Button
              color="error"
              variant="contained"
              onClick={handleDeleteContent}
              startIcon={
                loading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  <DeleteIcon />
                )
              }
              disabled={loading}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Preview Dialog */}
        <Dialog
          open={openPreviewDialog}
          onClose={handleClosePreviewDialog}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Typography variant="h6">Post Preview</Typography>
              {selectedContent && (
                <Chip
                  label={selectedContent.platform}
                  sx={{
                    backgroundColor: getPlatformColor(selectedContent.platform),
                    color: "white",
                  }}
                />
              )}
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {selectedContent && (
              <Box sx={{ p: 2 }}>
                <Card sx={{ maxWidth: 500, mx: "auto" }}>
                  <CardContent>
                    <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                      <Avatar sx={{ mr: 1 }}>
                        {getPlatformIcon(selectedContent.platform)}
                      </Avatar>
                      <Box>
                        <Typography
                          variant="subtitle1"
                          sx={{ fontWeight: "bold" }}
                        >
                          Your Company Name
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {format(
                            new Date(selectedContent.scheduled_date),
                            "MMM d, yyyy • h:mm a"
                          )}
                        </Typography>
                      </Box>
                    </Box>

                    <Typography variant="body1" paragraph>
                      {selectedContent.content}
                    </Typography>

                    {selectedContent.image_url && (
                      <Box sx={{ mt: 2, textAlign: "center" }}>
                        <img
                          src={selectedContent.image_url}
                          alt={selectedContent.title}
                          style={{
                            maxWidth: "100%",
                            borderRadius: "8px",
                            boxShadow: theme.shadows[1],
                          }}
                        />
                      </Box>
                    )}

                    {selectedContent.tags &&
                      selectedContent.tags.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          {selectedContent.tags.map((tag, index) => (
                            <Chip
                              key={index}
                              label={`#${tag}`}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                        </Box>
                      )}
                  </CardContent>
                </Card>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClosePreviewDialog}>Close</Button>
            <Button
              variant="contained"
              startIcon={<EditIcon />}
              onClick={() => {
                handleClosePreviewDialog();
                handleEditContent(selectedContent);
              }}
            >
              Edit
            </Button>
          </DialogActions>
        </Dialog>

        {/* Filter Drawer */}
        <Drawer
          anchor="right"
          open={openFilterDrawer}
          onClose={handleToggleFilterDrawer}
        >
          <Box sx={{ width: 300, p: 3 }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 3,
              }}
            >
              <Typography variant="h6">Filter Content</Typography>
              <IconButton onClick={handleToggleFilterDrawer}>
                <NavigateNextIcon />
              </IconButton>
            </Box>

            <Divider sx={{ mb: 3 }} />

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Platform</InputLabel>
              <Select
                value={filterPlatform}
                label="Platform"
                onChange={(e) => setFilterPlatform(e.target.value)}
              >
                {platforms.map((platform) => (
                  <MenuItem key={platform} value={platform}>
                    {platform === "all" ? "All Platforms" : platform}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Campaign</InputLabel>
              <Select
                value={filterCampaign}
                label="Campaign"
                onChange={(e) => setFilterCampaign(e.target.value)}
              >
                {campaigns.map((campaign) => (
                  <MenuItem key={campaign} value={campaign}>
                    {campaign === "all" ? "All Campaigns" : campaign}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                label="Status"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                {statuses.map((status) => (
                  <MenuItem key={status} value={status}>
                    {status === "all" ? "All Statuses" : status}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Priority</InputLabel>
              <Select
                value={filterPriority}
                label="Priority"
                onChange={(e) => setFilterPriority(e.target.value)}
              >
                {priorities.map((priority) => (
                  <MenuItem key={priority} value={priority}>
                    {priority === "all" ? "All Priorities" : priority}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControlLabel
              control={
                <Switch
                  checked={showDrafts}
                  onChange={(e) => setShowDrafts(e.target.checked)}
                  color="primary"
                />
              }
              label="Show Drafts"
              sx={{ mb: 3 }}
            />

            <Box
              sx={{ display: "flex", justifyContent: "space-between", mt: 4 }}
            >
              <Button variant="outlined" onClick={handleClearFilters}>
                Clear Filters
              </Button>
              <Button variant="contained" onClick={handleToggleFilterDrawer}>
                Apply Filters
              </Button>
            </Box>
          </Box>
        </Drawer>

        {/* Bulk Action Menu */}
        <Menu
          anchorEl={bulkActionAnchorEl}
          open={Boolean(bulkActionAnchorEl)}
          onClose={handleCloseBulkActionMenu}
        >
          <MenuItem onClick={() => handleOpenBulkActionDialog("reschedule")}>
            <ListItemIcon>
              <ScheduleIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Reschedule</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleOpenBulkActionDialog("status")}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Change Status</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleOpenBulkActionDialog("delete")}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" color="error" />
            </ListItemIcon>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        </Menu>

        {/* Bulk Action Dialog */}
        <Dialog
          open={openBulkActionDialog}
          onClose={handleCloseBulkActionDialog}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            {bulkActionType === "delete" && "Delete Posts"}
            {bulkActionType === "reschedule" && "Reschedule Posts"}
            {bulkActionType === "status" && "Change Status"}
          </DialogTitle>
          <DialogContent>
            {bulkActionType === "delete" && (
              <Typography>
                Are you sure you want to delete {selectedItems.length} selected
                posts? This action cannot be undone.
              </Typography>
            )}

            {bulkActionType === "reschedule" && (
              <Box sx={{ pt: 2 }}>
                <Typography gutterBottom>
                  Select a new date for {selectedItems.length} posts. The time
                  of each post will be preserved.
                </Typography>
                <DatePicker
                  label="New Date"
                  value={bulkRescheduleDate}
                  onChange={(newDate) => setBulkRescheduleDate(newDate)}
                  sx={{ width: "100%", mt: 2 }}
                />
              </Box>
            )}

            {bulkActionType === "status" && (
              <Box sx={{ pt: 2 }}>
                <Typography gutterBottom>
                  Select a new status for {selectedItems.length} posts.
                </Typography>
                <FormControl fullWidth sx={{ mt: 2 }}>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={bulkStatusValue}
                    label="Status"
                    onChange={(e) => setBulkStatusValue(e.target.value)}
                  >
                    <MenuItem value="draft">Draft</MenuItem>
                    <MenuItem value="scheduled">Scheduled</MenuItem>
                    <MenuItem value="published">Published</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseBulkActionDialog}>Cancel</Button>
            <Button
              variant="contained"
              color={bulkActionType === "delete" ? "error" : "primary"}
              onClick={handleExecuteBulkAction}
              startIcon={
                loading ? <CircularProgress size={20} color="inherit" /> : null
              }
              disabled={loading}
            >
              {bulkActionType === "delete" && "Delete"}
              {bulkActionType === "reschedule" && "Reschedule"}
              {bulkActionType === "status" && "Update"}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Drag and Drop Reschedule Confirmation */}
        <Snackbar
          open={Boolean(draggedTime) && Boolean(selectedContent)}
          autoHideDuration={6000}
          onClose={() => setDraggedTime(null)}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
          sx={{
            "& .MuiAlert-root": {
              width: "100%",
              maxWidth: 500,
              boxShadow: theme.shadows[6],
            },
          }}
        >
          <Alert
            severity="info"
            variant="filled"
            icon={<ScheduleIcon />}
            action={
              <>
                <Button
                  color="inherit"
                  size="small"
                  variant="outlined"
                  startIcon={<CheckIcon />}
                  onClick={() => {
                    if (selectedContent && draggedTime) {
                      handleDrop(null, draggedTime);
                    }
                  }}
                  sx={{ mr: 1, bgcolor: alpha("#fff", 0.15) }}
                >
                  Confirm
                </Button>
                <Button
                  color="inherit"
                  size="small"
                  startIcon={<CloseIcon />}
                  onClick={() => setDraggedTime(null)}
                >
                  Cancel
                </Button>
              </>
            }
          >
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 0.5 }}>
                Reschedule &quot;{selectedContent?.title}&quot;
              </Typography>
              <Typography variant="body2">
                From:{" "}
                {selectedContent
                  ? format(
                      new Date(selectedContent.scheduled_date),
                      "EEE, MMM d, h:mm a"
                    )
                  : ""}
                <br />
                To:{" "}
                {draggedTime ? format(draggedTime, "EEE, MMM d, h:mm a") : ""}
              </Typography>
            </Box>
          </Alert>
        </Snackbar>

        {/* Bulk Action Menu */}
        <Menu
          anchorEl={bulkActionAnchorEl}
          open={Boolean(bulkActionAnchorEl)}
          onClose={handleCloseBulkActionMenu}
          PaperProps={{
            elevation: 3,
            sx: {
              mt: 1,
              width: 200,
              borderRadius: 1,
            },
          }}
        >
          <MenuItem onClick={() => handleOpenBulkActionDialog("reschedule")}>
            <ListItemIcon>
              <ScheduleIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Reschedule</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleOpenBulkActionDialog("status")}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Change Status</ListItemText>
          </MenuItem>
          <Divider />
          <MenuItem
            onClick={() => handleOpenBulkActionDialog("delete")}
            sx={{ color: theme.palette.error.main }}
          >
            <ListItemIcon>
              <DeleteIcon fontSize="small" color="error" />
            </ListItemIcon>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        </Menu>

        {/* Bulk Action Dialog */}
        <BulkActionDialog
          open={openBulkActionDialog}
          onClose={() => setOpenBulkActionDialog(false)}
          actionType={bulkActionType}
          selectedItems={selectedItems
            .map((id) => scheduledContent.find((item) => item.id === id))
            .filter(Boolean)}
          onConfirm={handleBulkAction}
          loading={loading}
        />
      </Box>
      </DragDropContext>
    </LocalizationProvider>
  );
};

export default Calendar;

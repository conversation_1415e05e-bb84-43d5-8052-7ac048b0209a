import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CollaborativeEditor from '../CollaborativeEditor';

// Mock lodash debounce
vi.mock('lodash/debounce', () => ({
  default: (fn) => fn,
}));

// Mock the collaboration hook
const mockCollaboration = {
  isConnected: true,
  activeUsers: [
    {
      id: 'user1',
      name: '<PERSON>',
      status: 'active'
    },
    {
      id: 'user2',
      name: '<PERSON>',
      status: 'active'
    }
  ],
  userCursors: {
    'user2': {
      position: { x: 10, y: 20 }
    }
  },
  userSelections: {
    'user2': {
      selection: {
        start: { x: 10, y: 20, index: 5 },
        end: { x: 50, y: 20, index: 15 }
      }
    }
  },
  lockedSections: {
    'section-10-20': {
      userId: 'user2',
      expiresAt: new Date(Date.now() + 1000 * 60 * 30)
    }
  },
  sendContentChange: vi.fn(),
  sendCursorPosition: vi.fn(),
  sendSelectionChange: vi.fn(),
  lockSection: vi.fn(),
  addComment: vi.fn()
};

vi.mock('../../../hooks/useCollaboration', () => ({
  default: () => mockCollaboration,
}));

// Mock the auth context
const mockAuth = {
  user: {
    id: 'user1',
    name: 'John Doe'
  }
};

vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth,
}));

// Mock the notification hook
const mockNotification = {
  showSuccessNotification: vi.fn(),
  showErrorNotification: vi.fn()
};

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => mockNotification,
}));

// Mock CollaborationPanel
vi.mock('../CollaborationPanel', () => ({
  default: ({ contentId }) => (
    <div data-testid="collaboration-panel">
      Collaboration Panel for {contentId}
    </div>
  ),
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
      error: {
        main: '#F44336',
      },
      common: {
        white: '#FFFFFF',
      },
    },
    shape: {
      borderRadius: 8,
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CollaborativeEditor', () => {
  const mockContent = {
    id: 'test-content-id',
    title: 'Test Document',
    text_content: 'This is test content for the collaborative editor.'
  };

  const mockProps = {
    content: mockContent,
    onSave: vi.fn(),
    readOnly: false,
    autoSave: true,
    showCollaboration: true
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window.getComputedStyle
    Object.defineProperty(window, 'getComputedStyle', {
      value: () => ({
        lineHeight: '20px'
      }),
      writable: true
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders collaborative editor correctly', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Test Document')).toBeInTheDocument();
    expect(screen.getByDisplayValue('This is test content for the collaborative editor.')).toBeInTheDocument();
  });

  test('displays collaboration panel toggle button', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText(/show collaboration panel/i)).toBeInTheDocument();
  });

  test('displays save button when not read-only', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Save')).toBeInTheDocument();
  });

  test('hides save button when read-only', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Save')).not.toBeInTheDocument();
  });

  test('handles content changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    const textField = screen.getByDisplayValue('This is test content for the collaborative editor.');
    await user.clear(textField);
    await user.type(textField, 'New content');

    expect(textField).toHaveValue('New content');
  });

  test('handles save action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    const saveButton = screen.getByLabelText('Save');
    await user.click(saveButton);

    expect(mockProps.onSave).toHaveBeenCalledWith({
      ...mockContent,
      text_content: 'This is test content for the collaborative editor.'
    });
  });

  test('shows collaboration panel when toggled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    const toggleButton = screen.getByLabelText(/show collaboration panel/i);
    await user.click(toggleButton);

    expect(screen.getByTestId('collaboration-panel')).toBeInTheDocument();
  });

  test('handles text selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    const textField = screen.getByDisplayValue('This is test content for the collaborative editor.');
    
    // Simulate text selection
    textField.setSelectionRange(0, 4); // Select "This"
    fireEvent.select(textField);

    // Should show selection-related buttons
    await waitFor(() => {
      expect(screen.getByText('Lock Selection')).toBeInTheDocument();
      expect(screen.getByText('Comment')).toBeInTheDocument();
    });
  });

  test('handles lock selection action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    const textField = screen.getByDisplayValue('This is test content for the collaborative editor.');
    
    // Simulate text selection
    textField.setSelectionRange(0, 4);
    fireEvent.select(textField);

    await waitFor(() => {
      expect(screen.getByText('Lock Selection')).toBeInTheDocument();
    });

    const lockButton = screen.getByText('Lock Selection');
    await user.click(lockButton);

    expect(mockCollaboration.lockSection).toHaveBeenCalled();
  });

  test('handles comment action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    const textField = screen.getByDisplayValue('This is test content for the collaborative editor.');
    
    // Simulate text selection
    textField.setSelectionRange(0, 4);
    fireEvent.select(textField);

    await waitFor(() => {
      expect(screen.getByText('Comment')).toBeInTheDocument();
    });

    const commentButton = screen.getByText('Comment');
    await user.click(commentButton);

    expect(mockCollaboration.addComment).toHaveBeenCalled();
  });

  test('disables editor when read-only', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    const textField = screen.getByDisplayValue('This is test content for the collaborative editor.');
    expect(textField).toBeDisabled();
  });

  test('hides collaboration features when showCollaboration is false', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} showCollaboration={false} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText(/collaboration panel/i)).not.toBeInTheDocument();
    expect(screen.queryByTestId('collaboration-panel')).not.toBeInTheDocument();
  });

  test('handles cursor position changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    const textField = screen.getByDisplayValue('This is test content for the collaborative editor.');
    
    // Simulate cursor position change
    await user.click(textField);
    fireEvent.keyUp(textField);

    expect(mockCollaboration.sendCursorPosition).toHaveBeenCalled();
  });

  test('renders user cursors and selections', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    // Should render other users' cursors and selections
    // This is tested through the presence of the cursor overlay
    const textField = screen.getByDisplayValue('This is test content for the collaborative editor.');
    expect(textField).toBeInTheDocument();
  });

  test('shows loading state during save', async () => {
    const user = userEvent.setup();
    
    // Mock onSave to return a pending promise
    const pendingPromise = new Promise(() => {}); // Never resolves
    const mockOnSave = vi.fn().mockReturnValue(pendingPromise);
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} onSave={mockOnSave} />
      </TestWrapper>
    );

    const saveButton = screen.getByLabelText('Save');
    await user.click(saveButton);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles save success', async () => {
    const user = userEvent.setup();
    
    const mockOnSave = vi.fn().mockResolvedValue();
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} onSave={mockOnSave} />
      </TestWrapper>
    );

    const saveButton = screen.getByLabelText('Save');
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith('Content saved successfully');
    });
  });

  test('handles save error', async () => {
    const user = userEvent.setup();
    
    const mockOnSave = vi.fn().mockRejectedValue(new Error('Save failed'));
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} onSave={mockOnSave} />
      </TestWrapper>
    );

    const saveButton = screen.getByLabelText('Save');
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockNotification.showErrorNotification).toHaveBeenCalledWith('Failed to save content');
    });
  });

  test('initializes with content', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('This is test content for the collaborative editor.')).toBeInTheDocument();
  });

  test('handles empty content', () => {
    const emptyContent = { ...mockContent, text_content: '' };
    
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} content={emptyContent} />
      </TestWrapper>
    );

    const textField = screen.getByRole('textbox');
    expect(textField).toHaveValue('');
  });

  test('handles null content', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} content={null} />
      </TestWrapper>
    );

    expect(screen.getByText('Untitled Document')).toBeInTheDocument();
    const textField = screen.getByRole('textbox');
    expect(textField).toHaveValue('');
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels and roles
    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByLabelText('Save')).toBeInTheDocument();
    expect(screen.getByLabelText(/collaboration panel/i)).toBeInTheDocument();
  });

  test('disables collaboration features when disconnected', () => {
    const disconnectedCollaboration = {
      ...mockCollaboration,
      isConnected: false
    };

    vi.mocked(require('../../../hooks/useCollaboration').default).mockReturnValue(disconnectedCollaboration);

    render(
      <TestWrapper>
        <CollaborativeEditor {...mockProps} />
      </TestWrapper>
    );

    const textField = screen.getByDisplayValue('This is test content for the collaborative editor.');
    
    // Simulate text selection
    textField.setSelectionRange(0, 4);
    fireEvent.select(textField);

    // Collaboration buttons should be disabled
    const lockButton = screen.getByText('Lock Selection');
    const commentButton = screen.getByText('Comment');
    
    expect(lockButton).toBeDisabled();
    expect(commentButton).toBeDisabled();
  });
});

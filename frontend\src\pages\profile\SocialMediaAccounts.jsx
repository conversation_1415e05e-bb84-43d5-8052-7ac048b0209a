// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Divider,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import {
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Pinterest as PinterestIcon,
  Chat as ThreadsIcon,
  MusicVideo as TikTokIcon,
} from '@mui/icons-material';
// Note: useAuth available for future user-specific features
// import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

const SocialMediaAccounts = () => {
  // Note: user available from useAuth() for future user-specific features
  // const { user } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [loading, setLoading] = useState(true);
  const [accounts, setAccounts] = useState([]);
  const [platforms, setPlatforms] = useState([]);
  const [connectDialogOpen, setConnectDialogOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState(null);
  const [disconnectDialogOpen, setDisconnectDialogOpen] = useState(false);
  const [accountToDisconnect, setAccountToDisconnect] = useState(null);

  // Load connected accounts and available platforms
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      try {
        // Fetch connected accounts
        const accountsResponse = await api.get('/api/social-media/accounts');
        setAccounts(accountsResponse.data.accounts || []);

        // Fetch available platforms
        const platformsResponse = await api.get('/api/social-media/platforms');
        setPlatforms(platformsResponse.data.platforms || []);
      } catch (error) {
        console.error('Error fetching social media data:', error);
        showErrorNotification('Failed to load social media accounts');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [showErrorNotification]);

  // Handle connect button click
  const handleConnect = (platform) => {
    setSelectedPlatform(platform);
    setConnectDialogOpen(true);
  };

  // Start OAuth flow
  const startOAuthFlow = async () => {
    try {
      setConnectDialogOpen(false);

      const response = await api.get(`/api/social-media/connect/${selectedPlatform.id}`);

      // Store state in localStorage for verification after redirect
      localStorage.setItem('oauth_state', response.data.state);

      // Redirect to authorization URL
      window.location.href = response.data.authorization_url;
    } catch (error) {
      console.error('Error starting OAuth flow:', error);
      showErrorNotification(`Failed to connect to ${selectedPlatform.name}`);
    }
  };

  // Handle disconnect button click
  const handleDisconnectClick = (account) => {
    setAccountToDisconnect(account);
    setDisconnectDialogOpen(true);
  };

  // Disconnect account
  const disconnectAccount = async () => {
    try {
      setDisconnectDialogOpen(false);

      await api.delete(`/api/social-media/accounts/${accountToDisconnect.platform}`);

      // Update accounts list
      setAccounts(accounts.filter(acc => acc.platform !== accountToDisconnect.platform));

      showSuccessNotification(`Disconnected ${accountToDisconnect.platform} account`);
    } catch (error) {
      console.error('Error disconnecting account:', error);
      showErrorNotification(`Failed to disconnect ${accountToDisconnect.platform} account`);
    }
  };

  // Get platform icon
  const getPlatformIcon = (platform) => {
    switch (platform) {
      case 'facebook':
        return <FacebookIcon />;
      case 'twitter':
        return <TwitterIcon />;
      case 'linkedin':
        return <LinkedInIcon />;
      case 'pinterest':
        return <PinterestIcon />;
      case 'threads':
        return <ThreadsIcon />;
      case 'tiktok':
        return <TikTokIcon />;
      default:
        return <RefreshIcon />;
    }
  };

  // Get platform color
  const getPlatformColor = (platform) => {
    switch (platform) {
      case 'facebook':
        return '#1877F2';
      case 'twitter':
        return '#1DA1F2';
      case 'linkedin':
        return '#0A66C2';
      case 'pinterest':
        return '#BD081C';
      case 'threads':
        return '#000000';
      case 'tiktok':
        return '#000000';
      default:
        return '#757575';
    }
  };

  // Get available platforms (not connected yet)
  const getAvailablePlatforms = () => {
    const connectedPlatforms = accounts.map(acc => acc.platform);
    return platforms.filter(platform => !connectedPlatforms.includes(platform.id));
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Connected Social Media Accounts
      </Typography>

      <Typography variant="body1" paragraph>
        Connect your social media accounts to publish content and track analytics.
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {accounts.length === 0 ? (
            <Alert severity="info" sx={{ mb: 3 }}>
              You don&apos;t have any connected social media accounts yet. Connect an account to get started.
            </Alert>
          ) : (
            <Card sx={{ mb: 4 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Connected Accounts
                </Typography>

                <List>
                  {accounts.map((account, index) => (
                    <React.Fragment key={account.platform}>
                      {index > 0 && <Divider component="li" />}
                      <ListItem
                        secondaryAction={
                          <Button
                            variant="outlined"
                            color="error"
                            startIcon={<DeleteIcon />}
                            onClick={() => handleDisconnectClick(account)}
                          >
                            Disconnect
                          </Button>
                        }
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: getPlatformColor(account.platform) }}>
                            {getPlatformIcon(account.platform)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={account.account_name}
                          secondary={`Connected on ${new Date(account.connected_at).toLocaleDateString()}`}
                        />
                      </ListItem>
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}

          {getAvailablePlatforms().length > 0 && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Connect More Accounts
                </Typography>

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>
                  {getAvailablePlatforms().map((platform) => (
                    <Button
                      key={platform.id}
                      variant="contained"
                      startIcon={getPlatformIcon(platform.id)}
                      onClick={() => handleConnect(platform)}
                      sx={{
                        bgcolor: getPlatformColor(platform.id),
                        '&:hover': {
                          bgcolor: getPlatformColor(platform.id),
                          opacity: 0.9,
                        },
                      }}
                    >
                      Connect {platform.name}
                    </Button>
                  ))}
                </Box>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Connect Dialog */}
      <Dialog
        open={connectDialogOpen}
        onClose={() => setConnectDialogOpen(false)}
      >
        <DialogTitle>
          Connect {selectedPlatform?.name}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            You will be redirected to {selectedPlatform?.name} to authorize access to your account.
            This will allow the application to post content and retrieve analytics on your behalf.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConnectDialogOpen(false)}>Cancel</Button>
          <Button onClick={startOAuthFlow} variant="contained">Continue</Button>
        </DialogActions>
      </Dialog>

      {/* Disconnect Dialog */}
      <Dialog
        open={disconnectDialogOpen}
        onClose={() => setDisconnectDialogOpen(false)}
      >
        <DialogTitle>
          Disconnect {accountToDisconnect?.platform}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to disconnect your {accountToDisconnect?.platform} account?
            You will no longer be able to publish content or retrieve analytics for this account.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDisconnectDialogOpen(false)}>Cancel</Button>
          <Button onClick={disconnectAccount} color="error" variant="contained">Disconnect</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SocialMediaAccounts;

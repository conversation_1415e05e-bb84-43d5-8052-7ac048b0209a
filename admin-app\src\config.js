/**
 * Configuration settings for the Admin Panel
 */

// API base URL - connects to the same backend as the main application
export const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:8000";

// Environment
export const ENVIRONMENT = import.meta.env.VITE_ENVIRONMENT || "development";

// App version
export const APP_VERSION = "1.0.0";

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE = 1;

// Admin-specific settings
export const ADMIN_CONFIG = {
  // Auto-refresh intervals (in milliseconds) - increased to prevent reloading issues
  STATS_REFRESH_INTERVAL: 60000, // 60 seconds (increased from 30)
  USERS_REFRESH_INTERVAL: 120000, // 2 minutes (increased from 1)

  // Dashboard stability settings
  ENABLE_AUTO_REFRESH: false, // Disabled by default to prevent reloading
  ENABLE_WEBSOCKET: false, // Disabled - WebSocket token failures cause reloading
  DISABLE_ALL_WEBSOCKETS: true, // Force disable all WebSocket connections
  MAX_RETRY_ATTEMPTS: 1, // Reduced to prevent retry loops
  RETRY_DELAY: 5000, // 5 seconds

  // Pagination limits
  MAX_USERS_PER_PAGE: 50,
  MAX_COUPONS_PER_PAGE: 25,

  // Default admin credentials for development
  DEFAULT_ADMIN_EMAIL: "<EMAIL>",
  DEFAULT_ADMIN_PASSWORD: "Admin@1155"
};

// Feature flags
export const FEATURES = {
  ENABLE_BULK_OPERATIONS: true,
  ENABLE_EXPORT_FUNCTIONS: true,
  ENABLE_REAL_TIME_UPDATES: true
};

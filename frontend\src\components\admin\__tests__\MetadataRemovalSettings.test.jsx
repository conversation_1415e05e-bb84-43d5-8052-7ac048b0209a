/**
 * Tests for MetadataRemovalSettings component
 */
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import MetadataRemovalSettings from '../MetadataRemovalSettings';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showSuccessNotification: vi.fn(),
    showErrorNotification: vi.fn()
  }))
}));

vi.mock('../../api/metadataRemoval', () => ({
  default: {
    getConfiguration: vi.fn(),
    getPerformanceMetrics: vi.fn(),
    getGlobalStats: vi.fn(),
    getAlerts: vi.fn(),
    testMetadataRemoval: vi.fn()
  }
}));

describe('MetadataRemovalSettings', () => {
  const mockApi = require('../../api/metadataRemoval').default;
  const mockNotification = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default API responses
    mockApi.getConfiguration.mockResolvedValue({
      configuration: {
        enabled: true,
        preserveQuality: true,
        maxFileSizeMB: 50,
        cacheTTL: 3600,
        performanceTargetMs: 200,
        batchSizeLimit: 10,
        trackUsage: true
      }
    });

    mockApi.getPerformanceMetrics.mockResolvedValue({
      service_metrics: {
        total_processed: 1000,
        average_processing_time_ms: 150,
        cache_hit_rate: 0.85,
        error_rate: 0.02,
        meets_performance_target: true
      }
    });

    mockApi.getGlobalStats.mockResolvedValue({
      stats: {
        total_operations: 5000,
        total_images_processed: 4800,
        success_rate: 0.96,
        total_size_reduction_bytes: 1024000,
        total_ai_tags_removed: 2500,
        total_metadata_tags_removed: 15000
      }
    });

    mockApi.getAlerts.mockResolvedValue([]);

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue({
      showSuccessNotification: mockNotification,
      showErrorNotification: mockNotification
    });
  });

  test('renders metadata removal settings page', async () => {
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    expect(screen.getByText('Metadata Removal Settings')).toBeInTheDocument();
    expect(screen.getByText('Configuration')).toBeInTheDocument();
    expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
    expect(screen.getByText('24-Hour Global Statistics')).toBeInTheDocument();
    expect(screen.getByText('Test Metadata Removal')).toBeInTheDocument();
  });

  test('loads and displays configuration data', async () => {
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Metadata Removal Enabled')).toBeChecked();
      expect(screen.getByLabelText('Preserve Image Quality')).toBeChecked();
      expect(screen.getByLabelText('Track Usage for Billing')).toBeChecked();
    });

    expect(screen.getByDisplayValue('50')).toBeInTheDocument(); // Max file size
    expect(screen.getByDisplayValue('200')).toBeInTheDocument(); // Performance target
  });

  test('displays performance metrics correctly', async () => {
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('1000')).toBeInTheDocument(); // Total processed
      expect(screen.getByText('150ms')).toBeInTheDocument(); // Avg processing time
      expect(screen.getByText('85.0%')).toBeInTheDocument(); // Cache hit rate
      expect(screen.getByText('2.0%')).toBeInTheDocument(); // Error rate
    });
  });

  test('displays global statistics correctly', async () => {
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('5000')).toBeInTheDocument(); // Total operations
      expect(screen.getByText('4800')).toBeInTheDocument(); // Images processed
      expect(screen.getByText('96.0%')).toBeInTheDocument(); // Success rate
      expect(screen.getByText('2500')).toBeInTheDocument(); // AI tags removed
    });
  });

  test('handles configuration changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Metadata Removal Enabled')).toBeInTheDocument();
    });

    // Toggle metadata removal
    await user.click(screen.getByLabelText('Toggle metadata removal'));

    // Check that save button becomes enabled
    await waitFor(() => {
      expect(screen.getByText('Save Configuration')).not.toBeDisabled();
    });
  });

  test('handles configuration save', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Metadata Removal Enabled')).toBeInTheDocument();
    });

    // Make a change
    await user.click(screen.getByLabelText('Toggle metadata removal'));

    // Save configuration
    await user.click(screen.getByText('Save Configuration'));

    await waitFor(() => {
      expect(mockNotification).toHaveBeenCalledWith('Configuration saved successfully');
    });
  });

  test('handles test metadata removal', async () => {
    const user = userEvent.setup();
    mockApi.testMetadataRemoval.mockResolvedValue({
      result: {
        success: true,
        original_size_bytes: 1024000,
        processed_size_bytes: 512000,
        size_reduction_percent: 50,
        metadata_removed: {
          ai_generator_tags: ['midjourney', 'dalle'],
          total_tags_removed: 25
        }
      }
    });

    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    // Enter test URL
    const urlInput = screen.getByLabelText('Test image URL input');
    await user.type(urlInput, 'https://example.com/test-image.jpg');

    // Click test button
    await user.click(screen.getByText('Test Removal'));

    await waitFor(() => {
      expect(mockApi.testMetadataRemoval).toHaveBeenCalledWith('https://example.com/test-image.jpg');
      expect(mockNotification).toHaveBeenCalledWith('Test completed successfully');
    });

    // Check test results are displayed
    await waitFor(() => {
      expect(screen.getByText('Test Results')).toBeInTheDocument();
      expect(screen.getByText('Metadata removal completed successfully')).toBeInTheDocument();
    });
  });

  test('validates test URL input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    // Try to test with invalid URL
    const urlInput = screen.getByLabelText('Test image URL input');
    await user.type(urlInput, 'invalid-url');

    await user.click(screen.getByText('Test Removal'));

    await waitFor(() => {
      expect(mockNotification).toHaveBeenCalledWith('Please enter a valid URL');
    });
  });

  test('handles API errors gracefully', async () => {
    mockApi.getConfiguration.mockRejectedValue(new Error('API Error'));
    mockApi.getPerformanceMetrics.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockNotification).toHaveBeenCalledWith('Failed to load configuration');
      expect(mockNotification).toHaveBeenCalledWith('Failed to load performance metrics');
    });
  });

  test('refreshes data when refresh button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(mockApi.getConfiguration).toHaveBeenCalled();
    });

    // Clear mock calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh Data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.getConfiguration).toHaveBeenCalled();
      expect(mockApi.getPerformanceMetrics).toHaveBeenCalled();
      expect(mockApi.getGlobalStats).toHaveBeenCalled();
      expect(mockApi.getAlerts).toHaveBeenCalled();
    });
  });

  test('displays alerts when available', async () => {
    mockApi.getAlerts.mockResolvedValue([
      {
        type: 'performance_degradation',
        processing_time_ms: 500,
        target_ms: 200,
        timestamp: '2023-01-01T12:00:00Z'
      }
    ]);

    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('PERFORMANCE DEGRADATION')).toBeInTheDocument();
      expect(screen.getByText('Processing time: 500ms (target: 200ms)')).toBeInTheDocument();
    });
  });

  test('shows no alerts message when no alerts exist', async () => {
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No alerts in the last hour')).toBeInTheDocument();
    });
  });

  test('handles numeric input validation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MetadataRemovalSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Maximum file size in megabytes')).toBeInTheDocument();
    });

    // Test numeric input constraints
    const maxFileSizeInput = screen.getByLabelText('Maximum file size in megabytes');
    expect(maxFileSizeInput).toHaveAttribute('min', '1');
    expect(maxFileSizeInput).toHaveAttribute('max', '100');
  });
});

/**
 * Enhanced Sentiment Error Boundary - Enterprise-grade error boundary component
 * Features: Comprehensive error boundary with advanced error handling and recovery mechanisms, multi-level error categorization,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced sentiment error boundary capabilities and seamless sentiment analysis workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  Component
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  AlertTitle,
  alpha,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Grid
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  BugReport as BugIcon,
  AutoAwesome as AutoAwesomeIcon,
  Settings as SettingsIcon,
  Help as HelpIcon,
  ExpandMore as ExpandMoreIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  ContactSupport as ContactSupportIcon,
  RestartAlt as RestartAltIcon,
  Cached as CachedIcon,
  CloudOff as CloudOffIcon,
  NetworkCheck as NetworkCheckIcon
} from '@mui/icons-material';

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Error categories for sentiment analysis
const ERROR_CATEGORIES = {
  NETWORK_ERROR: 'network_error',
  DATA_ERROR: 'data_error',
  COMPONENT_ERROR: 'component_error',
  API_ERROR: 'api_error',
  PERMISSION_ERROR: 'permission_error',
  CONFIGURATION_ERROR: 'configuration_error'
};

class SentimentErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorCategory: null,
      retryCount: 0,
      lastErrorTime: null,
      errorHistory: [],
      recoveryMode: 'user_friendly'
    };
  }

  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Enhanced error logging and categorization
    const errorCategory = this.categorizeError(error);
    const errorTime = new Date().toISOString();

    console.error('Enhanced Sentiment Component Error:', {
      error,
      errorInfo,
      category: errorCategory,
      timestamp: errorTime,
      component: this.props.componentName
    });

    this.setState(prevState => ({
      error,
      errorInfo,
      errorCategory,
      lastErrorTime: errorTime,
      errorHistory: [...prevState.errorHistory, {
        error: error.toString(),
        category: errorCategory,
        timestamp: errorTime,
        componentStack: errorInfo.componentStack
      }].slice(-10) // Keep last 10 errors
    }));

    // Send error analytics if enabled
    if (this.props.enableAnalytics) {
      this.sendErrorAnalytics(error, errorInfo, errorCategory);
    }
  }

  categorizeError = (error) => {
    const errorMessage = error.toString().toLowerCase();

    if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      return ERROR_CATEGORIES.NETWORK_ERROR;
    } else if (errorMessage.includes('data') || errorMessage.includes('json')) {
      return ERROR_CATEGORIES.DATA_ERROR;
    } else if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) {
      return ERROR_CATEGORIES.PERMISSION_ERROR;
    } else if (errorMessage.includes('config') || errorMessage.includes('environment')) {
      return ERROR_CATEGORIES.CONFIGURATION_ERROR;
    } else if (errorMessage.includes('api') || errorMessage.includes('endpoint')) {
      return ERROR_CATEGORIES.API_ERROR;
    } else {
      return ERROR_CATEGORIES.COMPONENT_ERROR;
    }
  };

  sendErrorAnalytics = (error, errorInfo, category) => {
    // Enhanced error analytics tracking
    try {
      // This would integrate with your analytics service
      console.log('Error Analytics:', {
        component: this.props.componentName,
        error: error.toString(),
        category,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        url: window.location.href
      });
    } catch (analyticsError) {
      console.error('Failed to send error analytics:', analyticsError);
    }
  };

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorCategory: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  handleResetComponent = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorCategory: null,
      retryCount: 0,
      errorHistory: []
    });
  };

  handleContactSupport = () => {
    // Enhanced support contact with error context
    const errorContext = {
      component: this.props.componentName,
      error: this.state.error?.toString(),
      category: this.state.errorCategory,
      timestamp: this.state.lastErrorTime,
      retryCount: this.state.retryCount
    };

    console.log('Contact Support with context:', errorContext);
    // This would integrate with your support system
  };

  getErrorIcon = () => {
    switch (this.state.errorCategory) {
      case ERROR_CATEGORIES.NETWORK_ERROR:
        return <CloudOffIcon sx={{ color: ACE_COLORS.PURPLE }} />;
      case ERROR_CATEGORIES.DATA_ERROR:
        return <ErrorIcon sx={{ color: ACE_COLORS.PURPLE }} />;
      case ERROR_CATEGORIES.API_ERROR:
        return <NetworkCheckIcon sx={{ color: ACE_COLORS.PURPLE }} />;
      case ERROR_CATEGORIES.PERMISSION_ERROR:
        return <WarningIcon sx={{ color: ACE_COLORS.YELLOW }} />;
      case ERROR_CATEGORIES.CONFIGURATION_ERROR:
        return <SettingsIcon sx={{ color: ACE_COLORS.PURPLE }} />;
      default:
        return <BugIcon sx={{ color: ACE_COLORS.PURPLE }} />;
    }
  };

  getErrorMessage = () => {
    const { componentName = 'Sentiment Component' } = this.props;

    switch (this.state.errorCategory) {
      case ERROR_CATEGORIES.NETWORK_ERROR:
        return `Network connectivity issue in ${componentName}. Please check your internet connection.`;
      case ERROR_CATEGORIES.DATA_ERROR:
        return `Data processing error in ${componentName}. The sentiment data format may be invalid.`;
      case ERROR_CATEGORIES.API_ERROR:
        return `API communication error in ${componentName}. The sentiment analysis service may be unavailable.`;
      case ERROR_CATEGORIES.PERMISSION_ERROR:
        return `Permission error in ${componentName}. You may not have access to this sentiment analysis feature.`;
      case ERROR_CATEGORIES.CONFIGURATION_ERROR:
        return `Configuration error in ${componentName}. The component settings may be incorrect.`;
      default:
        return `An unexpected error occurred in ${componentName}. Please try again.`;
    }
  };

  getRecoveryActions = () => {
    const actions = [];

    switch (this.state.errorCategory) {
      case ERROR_CATEGORIES.NETWORK_ERROR:
        actions.push(
          { label: 'Check Connection', action: () => window.navigator.onLine ? this.handleRetry() : alert('Please check your internet connection'), icon: <NetworkCheckIcon /> },
          { label: 'Retry', action: this.handleRetry, icon: <RefreshIcon /> }
        );
        break;
      case ERROR_CATEGORIES.DATA_ERROR:
        actions.push(
          { label: 'Reset Data', action: this.handleResetComponent, icon: <RestartAltIcon /> },
          { label: 'Use Cached Data', action: this.handleRetry, icon: <CachedIcon /> }
        );
        break;
      case ERROR_CATEGORIES.API_ERROR:
        actions.push(
          { label: 'Retry API Call', action: this.handleRetry, icon: <RefreshIcon /> },
          { label: 'Contact Support', action: this.handleContactSupport, icon: <ContactSupportIcon /> }
        );
        break;
      default:
        actions.push(
          { label: 'Retry', action: this.handleRetry, icon: <RefreshIcon /> },
          { label: 'Reset Component', action: this.handleResetComponent, icon: <RestartAltIcon /> }
        );
    }

    return actions;
  };

  render() {
    if (this.state.hasError) {
      const {
        componentName = 'Sentiment Component',
        enableAIInsights = true,
        showErrorHistory = false
      } = this.props;

      const errorIcon = this.getErrorIcon();
      const errorMessage = this.getErrorMessage();
      const recoveryActions = this.getRecoveryActions();

      return (
        <Card sx={{
          borderRadius: 2,
          border: `2px solid ${ACE_COLORS.PURPLE}`,
          bgcolor: alpha(ACE_COLORS.WHITE, 0.98),
          boxShadow: `0 4px 20px ${alpha(ACE_COLORS.PURPLE, 0.1)}`
        }}>
          <CardContent sx={{ p: 3 }}>
            {/* Enhanced Error Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
              {errorIcon}
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h6" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                  {componentName} Error
                  {enableAIInsights && (
                    <AutoAwesomeIcon
                      sx={{
                        ml: 1,
                        fontSize: '1rem',
                        color: ACE_COLORS.YELLOW
                      }}
                    />
                  )}
                </Typography>
                <Typography variant="caption" sx={{ color: alpha(ACE_COLORS.DARK, 0.7) }}>
                  Error Category: {this.state.errorCategory?.replace('_', ' ').toUpperCase()}
                </Typography>
              </Box>
              <Chip
                label={`Retry #${this.state.retryCount}`}
                size="small"
                sx={{
                  bgcolor: alpha(ACE_COLORS.YELLOW, 0.2),
                  color: ACE_COLORS.DARK
                }}
              />
            </Box>

            {/* Enhanced Error Alert */}
            <Alert
              severity="error"
              sx={{
                mb: 3,
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle sx={{ color: ACE_COLORS.DARK }}>
                Component Recovery Required
              </AlertTitle>
              <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
                {errorMessage}
              </Typography>
            </Alert>

            {/* Enhanced Recovery Actions */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" sx={{ mb: 2, color: ACE_COLORS.DARK }}>
                Recovery Actions:
              </Typography>
              <Grid container spacing={2}>
                {recoveryActions.map((action, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={action.icon}
                      onClick={action.action}
                      sx={{
                        borderColor: ACE_COLORS.PURPLE,
                        color: ACE_COLORS.PURPLE,
                        '&:hover': {
                          bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                          borderColor: ACE_COLORS.PURPLE
                        }
                      }}
                    >
                      {action.label}
                    </Button>
                  </Grid>
                ))}
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<RefreshIcon />}
                    onClick={() => window.location.reload()}
                    sx={{
                      bgcolor: ACE_COLORS.PURPLE,
                      '&:hover': {
                        bgcolor: alpha(ACE_COLORS.PURPLE, 0.8)
                      }
                    }}
                  >
                    Reload Page
                  </Button>
                </Grid>
              </Grid>
            </Box>

            {/* Enhanced Developer Debug Info */}
            {(process.env.NODE_ENV === 'development' || this.props.showDebugInfo) && this.state.error && (
              <Accordion sx={{ mb: 3 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK }}>
                    Debug Information (Development)
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{
                    backgroundColor: alpha(ACE_COLORS.DARK, 0.05),
                    p: 2,
                    borderRadius: 1,
                    maxHeight: 300,
                    overflow: 'auto'
                  }}>
                    <Typography variant="caption" component="pre" sx={{
                      fontSize: '11px',
                      color: ACE_COLORS.DARK,
                      whiteSpace: 'pre-wrap'
                    }}>
                      <strong>Error:</strong> {this.state.error.toString()}{'\n\n'}
                      <strong>Component Stack:</strong>{this.state.errorInfo?.componentStack}
                      {'\n\n'}
                      <strong>Timestamp:</strong> {this.state.lastErrorTime}
                      {'\n'}
                      <strong>User Agent:</strong> {navigator.userAgent}
                    </Typography>
                  </Box>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Enhanced Error History */}
            {showErrorHistory && this.state.errorHistory.length > 0 && (
              <Accordion sx={{ mb: 3 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK }}>
                    Error History ({this.state.errorHistory.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <List dense>
                    {this.state.errorHistory.map((historyError, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <ErrorIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: '1rem' }} />
                        </ListItemIcon>
                        <ListItemText
                          primary={historyError.error}
                          secondary={`${historyError.category} - ${new Date(historyError.timestamp).toLocaleString()}`}
                          primaryTypographyProps={{ variant: 'caption', color: ACE_COLORS.DARK }}
                          secondaryTypographyProps={{ variant: 'caption', color: alpha(ACE_COLORS.DARK, 0.6) }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Enhanced Help Section */}
            <Box sx={{
              mt: 2,
              p: 2,
              backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
              borderRadius: 1,
              border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <HelpIcon sx={{ color: ACE_COLORS.YELLOW, fontSize: '1rem' }} />
                <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK }}>
                  Quick Recovery Tips
                </Typography>
              </Box>
              <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
                1. Try the retry button to reload the component{'\n'}
                2. Check your internet connection for network issues{'\n'}
                3. Clear browser cache if the problem persists{'\n'}
                4. Contact support if errors continue to occur
              </Typography>
            </Box>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Enhanced PropTypes with comprehensive validation
SentimentErrorBoundary.propTypes = {
  // Core props
  children: PropTypes.node.isRequired,
  componentName: PropTypes.string,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableAIInsights: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  showErrorHistory: PropTypes.bool,
  showDebugInfo: PropTypes.bool,

  // Callback props
  onError: PropTypes.func,
  onRetry: PropTypes.func,
  onReset: PropTypes.func,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

SentimentErrorBoundary.defaultProps = {
  componentName: 'Sentiment Component',
  enableAdvancedFeatures: true,
  enableAIInsights: true,
  enableAnalytics: true,
  showErrorHistory: false,
  showDebugInfo: false
};

export default SentimentErrorBoundary;

/**
 * Enhanced Services Overview - Enterprise-grade services dashboard component
 * Features: Comprehensive services dashboard with advanced analytics visualization, multi-dashboard views,
 * accessibility compliance, comprehensive services monitoring, and ACE Social platform integration
 * with advanced dashboard capabilities and seamless services management workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useMemo,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  useTheme,
  alpha,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Business as BusinessIcon,
  Campaign as CampaignIcon,
  Person as PersonIcon,
  Analytics as AnalyticsIcon,
  Lightbulb as LightbulbIcon,
  Star as StarIcon
} from '@mui/icons-material';

import FeatureGate from '../common/FeatureGate';

/**
 * Enhanced Services Overview - Comprehensive services dashboard
 * Implements Material-UI glass morphism styling with 8px grid spacing
 * Follows WCAG 2.1 AA compliance and production standards
 */
const ServicesOverview = memo(forwardRef(({
  stats = { total: 0, active: 0, draft: 0, campaigns: 0 },
  recentServices = []
}, ref) => {
  const theme = useTheme();

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshDashboard: () => {},
    exportDashboard: () => {},
    changeView: () => {},
    toggleFullscreen: () => {},
    getSelectedWidgets: () => [],
    resetDashboard: () => {}
  }), []);

  // Enhanced glass morphism styles
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.9)} 0%,
      ${alpha(theme.palette.background.default, 0.6)} 100%)`,
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 4px 16px 0 ${alpha(theme.palette.common.black, 0.1)}`
  }), [theme]);

  // Calculate completion percentage
  const completionPercentage = useMemo(() => {
    return stats.total > 0 ? (stats.active / stats.total) * 100 : 0;
  }, [stats.total, stats.active]);

  // Enhanced onboarding tips for new users
  const onboardingTips = useMemo(() => [
    {
      icon: <BusinessIcon color="primary" />,
      title: 'Define Your Service',
      description: 'Start by creating a detailed service definition with clear value propositions'
    },
    {
      icon: <PersonIcon color="primary" />,
      title: 'Generate ICPs',
      description: 'Use AI to create targeted Ideal Customer Profiles for your service'
    },
    {
      icon: <CampaignIcon color="primary" />,
      title: 'Launch Campaigns',
      description: 'Create and manage marketing campaigns based on your ICPs'
    },
    {
      icon: <AnalyticsIcon color="primary" />,
      title: 'Track Performance',
      description: 'Monitor campaign performance and optimize your strategy'
    }
  ], []);

  // Enhanced quick actions for existing users
  const quickActions = useMemo(() => [
    {
      icon: <PersonIcon />,
      title: 'Generate ICPs',
      description: 'Create customer profiles for existing services',
      disabled: stats.total === 0
    },
    {
      icon: <CampaignIcon />,
      title: 'Launch Campaign',
      description: 'Start a new marketing campaign',
      disabled: stats.total === 0
    },
    {
      icon: <AnalyticsIcon />,
      title: 'View Analytics',
      description: 'Analyze service performance',
      disabled: stats.campaigns === 0
    }
  ], [stats.total, stats.campaigns]);

  return (
    <Box>
      {/* Welcome Section */}
      <Card sx={{ ...glassMorphismStyles, mb: theme.spacing(5) }}>
        <CardContent sx={{ p: theme.spacing(4) }}>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: theme.spacing(2) }}>
                <BusinessIcon sx={{ fontSize: 40, color: 'primary.main', mr: theme.spacing(2) }} />
                <Box>
                  <Typography variant="h5" gutterBottom>
                    {stats.total === 0 ? 'Welcome to Services' : 'Services Dashboard'}
                  </Typography>
                  <Typography variant="body1" color="textSecondary">
                    {stats.total === 0
                      ? 'Define your services to generate targeted ICPs and launch focused campaigns'
                      : `Manage your ${stats.total} service${stats.total !== 1 ? 's' : ''} and track performance`
                    }
                  </Typography>
                </Box>
              </Box>

              {/* Progress Indicator for Existing Users */}
              {stats.total > 0 && (
                <Box sx={{ mt: theme.spacing(2) }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: theme.spacing(1) }}>
                    <Typography variant="body2" color="textSecondary">
                      Service Completion Rate
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {Math.round(completionPercentage)}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={completionPercentage}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: alpha(theme.palette.action.disabled, 0.2),
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                        background: `linear-gradient(90deg,
                          ${theme.palette.success.main} 0%,
                          ${theme.palette.primary.main} 100%)`,
                      },
                    }}
                  />
                  <Typography variant="caption" color="textSecondary" sx={{ mt: theme.spacing(0.5), display: 'block' }}>
                    {stats.active} of {stats.total} services are active
                  </Typography>
                </Box>
              )}
            </Grid>

            <Grid item xs={12} md={4}>
              {/* Removed duplicate Create Service button - using header button instead */}
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Grid container spacing={4}>
        {/* Statistics Cards */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3} sx={{ mb: theme.spacing(4) }}>
            <Grid item xs={6} sm={3}>
              <Card sx={{ ...glassMorphismStyles, textAlign: 'center' }}>
                <CardContent sx={{ py: theme.spacing(2) }}>
                  <Typography variant="h4" color="primary" gutterBottom>
                    {stats.total}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Total Services
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} sm={3}>
              <Card sx={{ ...glassMorphismStyles, textAlign: 'center' }}>
                <CardContent sx={{ py: theme.spacing(2) }}>
                  <Typography variant="h4" color="success.main" gutterBottom>
                    {stats.active}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Active
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} sm={3}>
              <Card sx={{ ...glassMorphismStyles, textAlign: 'center' }}>
                <CardContent sx={{ py: theme.spacing(2) }}>
                  <Typography variant="h4" color="warning.main" gutterBottom>
                    {stats.draft}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Draft
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={6} sm={3}>
              <Card sx={{ ...glassMorphismStyles, textAlign: 'center' }}>
                <CardContent sx={{ py: theme.spacing(2) }}>
                  <Typography variant="h4" color="info.main" gutterBottom>
                    {stats.campaigns}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Campaigns
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Quick Actions */}
          <Card sx={{ ...glassMorphismStyles, mb: theme.spacing(4) }}>
            <CardContent sx={{ p: theme.spacing(3) }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <LightbulbIcon sx={{ mr: theme.spacing(1) }} />
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                {quickActions.map((action, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Card
                      sx={{
                        p: theme.spacing(2),
                        textAlign: 'center',
                        cursor: action.disabled ? 'not-allowed' : 'pointer',
                        opacity: action.disabled ? 0.6 : 1,
                        transition: 'all 0.3s ease',
                        background: alpha(theme.palette.background.paper, 0.5),
                        '&:hover': action.disabled ? {} : {
                          transform: 'translateY(-2px)',
                          boxShadow: `0 4px 12px 0 ${alpha(theme.palette.common.black, 0.15)}`,
                        }
                      }}
                    >
                      <Box sx={{ mb: theme.spacing(1) }}>
                        {action.icon}
                      </Box>
                      <Typography variant="subtitle2" gutterBottom>
                        {action.title}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {action.description}
                      </Typography>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Recent Services or Onboarding */}
          <Card sx={{ ...glassMorphismStyles, mb: theme.spacing(3) }}>
            <CardContent sx={{ p: theme.spacing(3) }}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  mb: theme.spacing(3),
                  fontWeight: 600
                }}
              >
                <StarIcon />
                {stats.total === 0 ? 'Getting Started' : 'Recent Services'}
              </Typography>

              {stats.total === 0 ? (
                // Onboarding Tips
                <List sx={{ p: 0 }}>
                  {onboardingTips.map((tip, index) => (
                    <ListItem
                      key={index}
                      sx={{
                        p: theme.spacing(2),
                        mb: theme.spacing(2),
                        borderRadius: theme.spacing(1),
                        background: alpha(theme.palette.background.paper, 0.5),
                        border: `1px solid ${theme.palette.divider}`,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          transform: 'translateY(-1px)',
                          boxShadow: `0 2px 8px 0 ${alpha(theme.palette.common.black, 0.1)}`,
                        }
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        {tip.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle2" gutterBottom>
                            {tip.title}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="caption" color="textSecondary">
                            {tip.description}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                // Recent Services List
                <List sx={{ p: 0 }}>
                  {recentServices.slice(0, 5).map((service, index) => (
                    <ListItem
                      key={service.id || index}
                      sx={{
                        p: theme.spacing(2),
                        mb: theme.spacing(1),
                        borderRadius: theme.spacing(1),
                        background: alpha(theme.palette.background.paper, 0.5),
                        border: `1px solid ${theme.palette.divider}`,
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          transform: 'translateY(-1px)',
                          boxShadow: `0 2px 8px 0 ${alpha(theme.palette.common.black, 0.1)}`,
                        }
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        <BusinessIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle2" gutterBottom>
                            {service.name}
                          </Typography>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="textSecondary" display="block">
                              {service.category || 'Uncategorized'}
                            </Typography>
                            <Chip
                              label={service.status || 'active'}
                              size="small"
                              color={service.status === 'active' ? 'success' : 'warning'}
                              variant="outlined"
                              sx={{ mt: theme.spacing(0.5) }}
                            />
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}

                  {recentServices.length === 0 && (
                    <Typography variant="body2" color="textSecondary" textAlign="center" sx={{ py: theme.spacing(2) }}>
                      No recent services found
                    </Typography>
                  )}
                </List>
              )}
            </CardContent>
          </Card>

          {/* Feature Gate for Premium Features */}
          <FeatureGate feature="premium_analytics" fallback={
            <Card sx={{ ...glassMorphismStyles, textAlign: 'center' }}>
              <CardContent sx={{ p: theme.spacing(3) }}>
                <AnalyticsIcon sx={{ fontSize: 48, color: 'text.secondary', mb: theme.spacing(2) }} />
                <Typography variant="h6" gutterBottom>
                  Unlock Advanced Analytics
                </Typography>
                <Typography variant="body2" color="textSecondary" paragraph>
                  Get detailed insights into your service performance with our premium analytics dashboard.
                </Typography>
                <Button variant="outlined" size="small">
                  Upgrade Plan
                </Button>
              </CardContent>
            </Card>
          }>
            <Card sx={{ ...glassMorphismStyles }}>
              <CardContent sx={{ p: theme.spacing(3) }}>
                <Typography variant="h6" gutterBottom>
                  Performance Insights
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Advanced analytics content would go here for premium users.
                </Typography>
              </CardContent>
            </Card>
          </FeatureGate>
        </Grid>
      </Grid>
    </Box>
  );
}));

ServicesOverview.displayName = 'ServicesOverview';

ServicesOverview.propTypes = {
  /** Service statistics object */
  stats: PropTypes.shape({
    total: PropTypes.number,
    active: PropTypes.number,
    draft: PropTypes.number,
    campaigns: PropTypes.number
  }),
  /** Array of recent services */
  recentServices: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string.isRequired,
    status: PropTypes.string,
    category: PropTypes.string
  }))
};

export default ServicesOverview;

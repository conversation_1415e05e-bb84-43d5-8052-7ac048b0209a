/**
 * Enhanced Sentiment Overview Cards - Enterprise-grade sentiment overview cards component
 * Features: Comprehensive sentiment overview cards with advanced sentiment metrics visualization, multi-dimensional sentiment overview,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced sentiment overview cards capabilities and seamless sentiment analysis workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  memo,
  forwardRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  useMediaQuery,
  alpha
} from '@mui/material';
import {
  SentimentVerySatisfied as VeryPositiveIcon,
  SentimentSatisfied as PositiveIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as NegativeIcon,
  SentimentVeryDissatisfied as VeryNegativeIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  AutoAwesome as AutoAwesomeIcon
} from '@mui/icons-material';
import { getSentimentOverview, getDateRange, transformSentimentOverview } from '../../api/sentiment';

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

/**
 * Enhanced Individual sentiment metric card with ACE Social branding
 */
const SentimentMetricCard = memo(({
  title,
  value,
  subtitle,
  icon,
  color,
  trend,
  loading = false,
  onClick,
  enableAIInsights = true,
  showTrendIndicator = true
}) => {

  const getTrendIcon = () => {
    if (!showTrendIndicator || !trend) return null;

    switch (trend) {
      case 'improving':
        return <TrendingUpIcon sx={{ color: '#4caf50', fontSize: 16 }} />;
      case 'declining':
        return <TrendingDownIcon sx={{ color: '#f44336', fontSize: 16 }} />;
      default:
        return <TrendingFlatIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.5), fontSize: 16 }} />;
    }
  };

  return (
    <Card
      sx={{
        height: '100%',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        '&:hover': onClick ? {
          transform: 'translateY(-4px)',
          boxShadow: `0 8px 25px ${alpha(ACE_COLORS.PURPLE, 0.15)}`
        } : {},
        borderRadius: 3,
        background: `linear-gradient(135deg, ${alpha(color || ACE_COLORS.PURPLE, 0.1)} 0%, ${alpha(color || ACE_COLORS.PURPLE, 0.05)} 100%)`,
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
        position: 'relative',
        overflow: 'hidden'
      }}
      onClick={onClick}
      role={onClick ? 'button' : 'article'}
      tabIndex={onClick ? 0 : -1}
      aria-label={`${title}: ${value}`}
    >
      {/* Enhanced gradient overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '60px',
          height: '60px',
          background: `linear-gradient(135deg, ${alpha(ACE_COLORS.YELLOW, 0.1)} 0%, transparent 70%)`,
          borderRadius: '0 0 0 60px'
        }}
      />

      <CardContent sx={{ p: 3, position: 'relative', zIndex: 1 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 80 }}>
            <CircularProgress size={24} sx={{ color: ACE_COLORS.PURPLE }} />
          </Box>
        ) : (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                {icon}
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 700,
                    color: color || ACE_COLORS.PURPLE,
                    letterSpacing: '-0.02em'
                  }}
                >
                  {value}
                  {enableAIInsights && (
                    <AutoAwesomeIcon
                      sx={{
                        ml: 1,
                        fontSize: '1rem',
                        color: ACE_COLORS.YELLOW,
                        verticalAlign: 'super'
                      }}
                    />
                  )}
                </Typography>
              </Box>
              {getTrendIcon()}
            </Box>

            <Typography
              variant="body2"
              sx={{
                fontWeight: 600,
                mb: 0.5,
                color: ACE_COLORS.DARK
              }}
            >
              {title}
            </Typography>

            {subtitle && (
              <Typography
                variant="caption"
                sx={{
                  color: alpha(ACE_COLORS.DARK, 0.7),
                  lineHeight: 1.4
                }}
              >
                {subtitle}
              </Typography>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
});

SentimentMetricCard.displayName = 'SentimentMetricCard';

/**
 * Enhanced Sentiment Overview Cards Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {number} [props.timeRange=30] - Time range for sentiment analysis
 * @param {Function} [props.onCardClick] - Card click callback
 * @param {number} [props.refreshTrigger=0] - Refresh trigger
 * @param {Function} [props.onOverviewAction] - Overview action callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {boolean} [props.enableRealTimeUpdates=true] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-sentiment-overview-cards'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const SentimentOverviewCards = memo(forwardRef(({
  timeRange = 30,
  onCardClick,
  refreshTrigger = 0
}) => {
  // Enhanced context integration
  const isMobile = useMediaQuery('(max-width:600px)');

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check feature access - Always true for enhanced version
  const canAccessSentiment = true;

  // Memoize the data fetching function to prevent unnecessary re-renders
  const fetchSentimentData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Get date range for API call
      const dateRange = getDateRange(timeRange);

      // Call real API endpoint
      const apiData = await getSentimentOverview(dateRange);

      // Transform API data to component format
      const sentimentData = transformSentimentOverview(apiData);
      setData(sentimentData);
    } catch (err) {
      console.error('Error fetching sentiment data:', err);
      setError(err.message || 'Failed to load sentiment data');
    } finally {
      setLoading(false);
    }
  }, [timeRange]); // Only depend on timeRange

  const getSentimentIcon = useCallback((sentiment) => {
    const iconProps = { sx: { fontSize: 24 } };

    switch (sentiment) {
      case 'very_positive':
        return <VeryPositiveIcon {...iconProps} sx={{ ...iconProps.sx, color: '#2e7d32' }} />;
      case 'positive':
        return <PositiveIcon {...iconProps} sx={{ ...iconProps.sx, color: '#4caf50' }} />;
      case 'negative':
        return <NegativeIcon {...iconProps} sx={{ ...iconProps.sx, color: '#f44336' }} />;
      case 'very_negative':
        return <VeryNegativeIcon {...iconProps} sx={{ ...iconProps.sx, color: '#d32f2f' }} />;
      default:
        return <NeutralIcon {...iconProps} sx={{ ...iconProps.sx, color: alpha(ACE_COLORS.DARK, 0.5) }} />;
    }
  }, []);

  const getSentimentColor = useCallback((sentiment) => {
    switch (sentiment) {
      case 'very_positive':
        return '#2e7d32';
      case 'positive':
        return '#4caf50';
      case 'negative':
        return '#f44336';
      case 'very_negative':
        return '#d32f2f';
      default:
        return alpha(ACE_COLORS.DARK, 0.5);
    }
  }, []);

  const formatSentimentLabel = useCallback((sentiment) => {
    return sentiment.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  }, []);

  // Memoize cards data - MUST be called before any conditional returns
  const cards = useMemo(() => {
    if (!data) return [];

    return [
      {
        title: 'Overall Sentiment',
        value: formatSentimentLabel(data.overall_sentiment),
        subtitle: `Score: ${data.sentiment_score} (${Math.round(data.confidence * 100)}% confidence)`,
        icon: getSentimentIcon(data.overall_sentiment),
        color: getSentimentColor(data.overall_sentiment),
        trend: 'stable',
        onClick: () => onCardClick?.('overall')
      },
      {
        title: 'Total Posts Analyzed',
        value: data.total_posts.toLocaleString(),
        subtitle: `Last ${timeRange} days`,
        icon: <InfoIcon sx={{ fontSize: 24, color: ACE_COLORS.PURPLE }} />,
        color: ACE_COLORS.PURPLE,
        onClick: () => onCardClick?.('posts')
      },
      {
        title: 'Positive Sentiment',
        value: `${Math.round(((data.sentiment_distribution.positive + data.sentiment_distribution.very_positive) / data.total_posts) * 100)}%`,
        subtitle: `${data.sentiment_distribution.positive + data.sentiment_distribution.very_positive} posts`,
        icon: <PositiveIcon sx={{ fontSize: 24, color: '#4caf50' }} />,
        color: '#4caf50',
        trend: 'improving',
        onClick: () => onCardClick?.('positive')
      },
      {
        title: 'Negative Sentiment',
        value: `${Math.round(((data.sentiment_distribution.negative + data.sentiment_distribution.very_negative) / data.total_posts) * 100)}%`,
        subtitle: `${data.sentiment_distribution.negative + data.sentiment_distribution.very_negative} posts`,
        icon: <NegativeIcon sx={{ fontSize: 24, color: '#f44336' }} />,
        color: '#f44336',
        trend: 'declining',
        onClick: () => onCardClick?.('negative')
      }
    ];
  }, [data, timeRange, formatSentimentLabel, getSentimentIcon, getSentimentColor, onCardClick]);

  // useEffect MUST be called after all other hooks
  useEffect(() => {
    if (!canAccessSentiment) {
      setLoading(false);
      return;
    }

    fetchSentimentData();
  }, [timeRange, refreshTrigger, canAccessSentiment, fetchSentimentData]);

  if (!canAccessSentiment) {
    return (
      <Card sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Sentiment Analysis
        </Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          Upgrade your plan to access advanced sentiment analysis features
        </Typography>
        <Chip
          label="Premium Feature"
          color="primary" 
          variant="outlined"
          size="small"
        />
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
        <Typography variant="h6" color="error" gutterBottom>
          Error Loading Data
        </Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          {error}
        </Typography>
        <IconButton onClick={fetchSentimentData} color="primary">
          <RefreshIcon />
        </IconButton>
      </Card>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600 }}>
          Sentiment Overview
        </Typography>
        <Tooltip title="Refresh data">
          <IconButton onClick={fetchSentimentData} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      <Grid container spacing={isMobile ? 2 : 3}>
        {cards.map((card, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <SentimentMetricCard
              {...card}
              loading={loading}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
SentimentOverviewCards.propTypes = {
  // Core props
  timeRange: PropTypes.number,
  onCardClick: PropTypes.func,
  refreshTrigger: PropTypes.number
};

SentimentOverviewCards.displayName = 'SentimentOverviewCards';

export default SentimentOverviewCards;

/**
 * Coupon utility functions for data processing, validation, and formatting
 */

/**
 * Format currency values
 */
export const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format date values with timezone support
 */
export const formatDate = (date, options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    timeZone: 'UTC',
  };
  
  const formatOptions = { ...defaultOptions, ...options };
  
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', formatOptions).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Format date and time
 */
export const formatDateTime = (date) => {
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'UTC',
  });
};

/**
 * Calculate relative time (e.g., "2 hours ago")
 */
export const getRelativeTime = (date) => {
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now - dateObj) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return formatDate(dateObj);
  } catch (error) {
    console.error('Error calculating relative time:', error);
    return 'Unknown';
  }
};

/**
 * Generate coupon code with specific format
 */
export const generateCouponCode = (prefix = '', length = 8) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix ? prefix.toUpperCase() + '-' : '';
  
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  
  return result;
};

/**
 * Validate coupon code format
 */
export const validateCouponCode = (code) => {
  if (!code || typeof code !== 'string') {
    return { isValid: false, error: 'Code is required' };
  }
  
  // Remove whitespace and convert to uppercase
  const cleanCode = code.trim().toUpperCase();
  
  // Check format: minimum 3 characters, alphanumeric and hyphens only
  const codePattern = /^[A-Z0-9-]{3,}$/;
  
  if (!codePattern.test(cleanCode)) {
    return { 
      isValid: false, 
      error: 'Code must be at least 3 characters and contain only letters, numbers, and hyphens' 
    };
  }
  
  return { isValid: true, code: cleanCode };
};

/**
 * Format discount value for display
 */
export const formatDiscount = (coupon) => {
  if (!coupon) return '-';
  
  switch (coupon.discount_type) {
    case 'percentage':
      return `${coupon.discount_value}%`;
    case 'fixed_amount':
      return formatCurrency(coupon.discount_value);
    case 'free_trial_extension':
      return `+${coupon.discount_value} days`;
    case 'free_addon':
      return 'Free Addon';
    default:
      return `${coupon.discount_value}`;
  }
};

/**
 * Get coupon status with color coding
 */
export const getCouponStatus = (coupon) => {
  if (!coupon) return { status: 'unknown', color: 'default' };
  
  const now = new Date();
  const startDate = new Date(coupon.start_date);
  const endDate = coupon.end_date ? new Date(coupon.end_date) : null;
  
  if (!coupon.is_active) {
    return { status: 'inactive', color: 'default' };
  }
  
  if (now < startDate) {
    return { status: 'scheduled', color: 'info' };
  }
  
  if (endDate && now > endDate) {
    return { status: 'expired', color: 'error' };
  }
  
  if (coupon.max_redemptions && coupon.redemption_count >= coupon.max_redemptions) {
    return { status: 'exhausted', color: 'warning' };
  }
  
  return { status: 'active', color: 'success' };
};

/**
 * Calculate coupon performance metrics
 */
export const calculateCouponMetrics = (coupons) => {
  if (!Array.isArray(coupons)) return null;
  
  const total = coupons.length;
  const active = coupons.filter(c => getCouponStatus(c).status === 'active').length;
  const totalRedemptions = coupons.reduce((sum, c) => sum + (c.redemption_count || 0), 0);
  const totalRevenue = coupons.reduce((sum, c) => {
    const redemptions = c.redemption_count || 0;
    const avgDiscount = c.discount_type === 'percentage' ? 
      (c.discount_value / 100) * 50 : // Assume $50 average order
      c.discount_value;
    return sum + (redemptions * avgDiscount);
  }, 0);
  
  // Calculate usage rate
  const usageRate = total > 0 ? (totalRedemptions / total) : 0;
  
  // Calculate conversion rate (assuming 10% of views convert)
  const conversionRate = totalRedemptions > 0 ? Math.min(totalRedemptions * 0.1, 100) : 0;
  
  return {
    total,
    active,
    totalRedemptions,
    totalRevenue,
    usageRate: Math.round(usageRate * 100) / 100,
    conversionRate: Math.round(conversionRate * 100) / 100,
  };
};

/**
 * Export data to CSV format
 */
export const exportToCSV = (data, filename, columns) => {
  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('No data to export');
  }
  
  // Create CSV header
  const headers = columns.map(col => col.label || col.key).join(',');
  
  // Create CSV rows
  const rows = data.map(item => {
    return columns.map(col => {
      let value = item[col.key];
      
      // Handle nested properties
      if (col.key.includes('.')) {
        const keys = col.key.split('.');
        value = keys.reduce((obj, key) => obj?.[key], item);
      }
      
      // Format value based on type
      if (col.type === 'date' && value) {
        value = formatDate(value);
      } else if (col.type === 'currency' && value !== null && value !== undefined) {
        value = formatCurrency(value);
      } else if (col.type === 'boolean') {
        value = value ? 'Yes' : 'No';
      } else if (col.type === 'discount' && item.discount_type) {
        value = formatDiscount(item);
      }
      
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        value = `"${value.replace(/"/g, '""')}"`;
      }
      
      return value || '';
    }).join(',');
  });
  
  // Combine header and rows
  const csvContent = [headers, ...rows].join('\n');
  
  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

/**
 * Validate email format
 */
export const validateEmail = (email) => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};

/**
 * Validate required fields in form data
 */
export const validateFormData = (data, requiredFields) => {
  const errors = {};
  
  requiredFields.forEach(field => {
    const value = data[field.key];
    
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      errors[field.key] = `${field.label} is required`;
    } else if (field.type === 'email' && !validateEmail(value)) {
      errors[field.key] = 'Please enter a valid email address';
    } else if (field.type === 'number' && (isNaN(value) || value < 0)) {
      errors[field.key] = `${field.label} must be a positive number`;
    } else if (field.type === 'percentage' && (isNaN(value) || value < 0 || value > 100)) {
      errors[field.key] = `${field.label} must be between 0 and 100`;
    } else if (field.minLength && value.length < field.minLength) {
      errors[field.key] = `${field.label} must be at least ${field.minLength} characters`;
    } else if (field.maxLength && value.length > field.maxLength) {
      errors[field.key] = `${field.label} must be no more than ${field.maxLength} characters`;
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Debounce function for search inputs
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Copy text to clipboard
 */
export const copyToClipboard = async (text) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

/**
 * Generate correlation ID for tracking
 */
export const generateCorrelationId = () => {
  return `coupon-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Format file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Calculate discount amount
 */
export const calculateDiscountAmount = (originalAmount, coupon) => {
  if (!coupon || !originalAmount) return 0;
  
  let discountAmount = 0;
  
  switch (coupon.discount_type) {
    case 'percentage':
      discountAmount = (originalAmount * coupon.discount_value) / 100;
      if (coupon.max_discount_amount) {
        discountAmount = Math.min(discountAmount, coupon.max_discount_amount);
      }
      break;
    case 'fixed_amount':
      discountAmount = Math.min(coupon.discount_value, originalAmount);
      break;
    default:
      discountAmount = 0;
  }
  
  return Math.round(discountAmount * 100) / 100;
};

/**
 * Check if coupon is applicable to specific item
 */
export const isCouponApplicable = (coupon, itemType, itemId) => {
  if (!coupon) return false;
  
  // Check if coupon is active and within date range
  const status = getCouponStatus(coupon);
  if (status.status !== 'active') return false;
  
  // Check applicability
  if (coupon.applicable_to === 'all') return true;
  if (coupon.applicable_to === itemType) {
    if (coupon.specific_items && coupon.specific_items.length > 0) {
      return coupon.specific_items.includes(itemId);
    }
    return true;
  }
  
  return false;
};

/**
 * Enhanced Services List - Enterprise-grade services management component
 * Features: Comprehensive services management with advanced filtering, multi-view display,
 * accessibility compliance, comprehensive service analytics, and ACE Social platform integration
 * with advanced service management capabilities and seamless services workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  useTheme,
  alpha,
  Tooltip,
  Collapse,
  Fade,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Badge,
  LinearProgress,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Person as PersonIcon,
  Campaign as CampaignIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as DuplicateIcon,
  Visibility as ViewIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ViewList as ListViewIcon,
  ViewModule as GridViewIcon,
  Dashboard as DashboardIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  Build as ConfigureIcon,
  Monitor as MonitorIcon,
  AutoMode as AutomationIcon,
  Archive as ArchiveIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Label as TagIcon,
  Category as CategoryIcon,
  Business as BusinessIcon,
  Upgrade as UpgradeIcon
} from '@mui/icons-material';

import { useSubscription } from '../../contexts/SubscriptionContext';
import FeatureGate from '../common/FeatureGate';

// Enhanced context and hook imports
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Plan-based services management limits
const PLAN_LIMITS = {
  1: { // Creator
    maxServices: 5,
    viewModes: ['list'],
    bulkOperations: false,
    analytics: false,
    automation: false,
    aiOptimization: false,
    prioritySupport: false,
    exportServices: false,
    serviceTemplates: false
  },
  2: { // Accelerator
    maxServices: 25,
    viewModes: ['list', 'grid', 'analytics'],
    bulkOperations: true,
    analytics: true,
    automation: false,
    aiOptimization: false,
    prioritySupport: false,
    exportServices: true,
    serviceTemplates: true
  },
  3: { // Dominator
    maxServices: -1, // unlimited
    viewModes: ['list', 'grid', 'analytics', 'dashboard'],
    bulkOperations: true,
    analytics: true,
    automation: true,
    aiOptimization: true,
    prioritySupport: true,
    exportServices: true,
    serviceTemplates: true
  }
};

// Service status types
const SERVICE_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PAUSED: 'paused',
  ARCHIVED: 'archived',
  DRAFT: 'draft'
};

// Service view modes
const VIEW_MODES = {
  LIST: 'list',
  GRID: 'grid',
  ANALYTICS: 'analytics',
  DASHBOARD: 'dashboard'
};

// Enhanced services list component

/**
 * Enhanced Services List - Comprehensive services management with advanced filtering and analytics
 * Implements advanced service management capabilities with plan-based feature restrictions
 */
const ServicesList = memo(forwardRef(({
  services = [],
  loading = false,
  onEdit,
  onDelete,
  onDuplicate,
  onView,
  onViewICPs,
  onCreateCampaign,
  onBulkAction,
  onRefresh,
  onServiceAction,
  enableRealTimeUpdates = true
}, ref) => {
  const theme = useTheme();
  const { subscription, updateUsage, getPlanTier } = useSubscription();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core state management
  const servicesRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [industryFilter, setIndustryFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedService, setSelectedService] = useState(null);

  // Enhanced state management
  const [viewMode, setViewMode] = useState(VIEW_MODES.GRID);
  const [selectedServices, setSelectedServices] = useState([]);
  const [bulkActionDialogOpen, setBulkActionDialogOpen] = useState(false);
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const [serviceAnalytics, setServiceAnalytics] = useState({});
  const [performanceMetrics, setPerformanceMetrics] = useState({});
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [favoriteServices, setFavoriteServices] = useState([]);
  const [serviceHealth, setServiceHealth] = useState({});

  // Plan tier and feature access
  const planTier = getPlanTier();
  const planLimits = PLAN_LIMITS[planTier] || PLAN_LIMITS[1];

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshServices: () => handleRefreshServices(),
    exportServices: () => handleExportServices(),
    selectAllServices: () => handleSelectAllServices(),
    clearSelection: () => setSelectedServices([]),
    getSelectedServices: () => selectedServices,
    changeViewMode: (mode) => setViewMode(mode),
    applyFilters: (filters) => applyAdvancedFilters(filters)
  }), [selectedServices, handleRefreshServices, handleExportServices, handleSelectAllServices, applyAdvancedFilters, setViewMode]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Handle refresh services
  const handleRefreshServices = useCallback(async () => {
    try {
      if (onRefresh) {
        await onRefresh();
      }
      announceToScreenReader('Services refreshed');
    } catch (error) {
      console.error('Error refreshing services:', error);
    }
  }, [onRefresh, announceToScreenReader]);

  // Handle export services
  const handleExportServices = useCallback(() => {
    if (!planLimits.exportServices) {
      announceToScreenReader('Service export is not available in your current plan. Please upgrade to access this feature.');
      return;
    }

    try {
      const exportData = {
        services: selectedServices.length > 0 ? selectedServices : filteredServices,
        exportedAt: new Date().toISOString(),
        planTier: planTier,
        filters: {
          searchTerm,
          statusFilter,
          categoryFilter,
          industryFilter
        }
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `services-export-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      announceToScreenReader('Services exported successfully');
    } catch (error) {
      console.error('Error exporting services:', error);
    }
  }, [planLimits.exportServices, selectedServices, filteredServices, planTier, searchTerm, statusFilter, categoryFilter, industryFilter, announceToScreenReader]);

  // Handle select all services
  const handleSelectAllServices = useCallback(() => {
    if (selectedServices.length === filteredServices.length) {
      setSelectedServices([]);
      announceToScreenReader('All services deselected');
    } else {
      setSelectedServices(filteredServices.map(service => service.id));
      announceToScreenReader(`${filteredServices.length} services selected`);
    }
  }, [selectedServices.length, filteredServices, announceToScreenReader]);

  // Apply advanced filters
  const applyAdvancedFilters = useCallback((filters) => {
    if (filters.searchTerm !== undefined) setSearchTerm(filters.searchTerm);
    if (filters.statusFilter !== undefined) setStatusFilter(filters.statusFilter);
    if (filters.categoryFilter !== undefined) setCategoryFilter(filters.categoryFilter);
    if (filters.industryFilter !== undefined) setIndustryFilter(filters.industryFilter);
    if (filters.sortBy !== undefined) setSortBy(filters.sortBy);
    if (filters.sortOrder !== undefined) setSortOrder(filters.sortOrder);

    announceToScreenReader('Filters applied');
  }, [announceToScreenReader]);

  // Get unique filter options
  const filterOptions = useMemo(() => {
    const categories = [...new Set(services.map(s => s.category).filter(Boolean))];
    const industries = [...new Set(services.map(s => s.target_industry).filter(Boolean))];
    const statuses = [...new Set(services.map(s => s.status || SERVICE_STATUS.ACTIVE))];

    return { categories, industries, statuses };
  }, [services]);

  // Enhanced service filtering and sorting
  const filteredServices = useMemo(() => {
    let filtered = services.filter(service => {
      const matchesSearch = !searchTerm ||
        service.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.target_industry?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesStatus = statusFilter === 'all' ||
        (service.status || SERVICE_STATUS.ACTIVE) === statusFilter;

      const matchesCategory = categoryFilter === 'all' ||
        service.category === categoryFilter;

      const matchesIndustry = industryFilter === 'all' ||
        service.target_industry === industryFilter;

      return matchesSearch && matchesStatus && matchesCategory && matchesIndustry;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortBy];
      let bValue = b[sortBy];

      // Handle special sorting cases
      if (sortBy === 'performance') {
        aValue = performanceMetrics[a.id]?.score || 0;
        bValue = performanceMetrics[b.id]?.score || 0;
      } else if (sortBy === 'health') {
        aValue = serviceHealth[a.id]?.score || 0;
        bValue = serviceHealth[b.id]?.score || 0;
      } else if (sortBy === 'created_at' || sortBy === 'updated_at') {
        aValue = new Date(aValue || 0);
        bValue = new Date(bValue || 0);
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue?.toLowerCase() || '';
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [services, searchTerm, statusFilter, categoryFilter, industryFilter, sortBy, sortOrder, performanceMetrics, serviceHealth]);

  // Handle service selection
  const handleServiceSelect = useCallback((serviceId, selected) => {
    if (selected) {
      setSelectedServices(prev => [...prev, serviceId]);
    } else {
      setSelectedServices(prev => prev.filter(id => id !== serviceId));
    }
  }, []);

  // Handle bulk actions
  const handleBulkAction = useCallback(async (action) => {
    if (!planLimits.bulkOperations) {
      announceToScreenReader('Bulk operations are not available in your current plan. Please upgrade to access this feature.');
      return;
    }

    try {
      if (onBulkAction) {
        await onBulkAction(action, selectedServices);
      }

      // Update usage tracking
      await updateUsage('bulk_operations', 1, {
        action,
        serviceCount: selectedServices.length
      });

      setSelectedServices([]);
      setBulkActionDialogOpen(false);
      announceToScreenReader(`Bulk ${action} completed for ${selectedServices.length} services`);
    } catch (error) {
      console.error('Error performing bulk action:', error);
    }
  }, [planLimits.bulkOperations, onBulkAction, selectedServices, updateUsage, announceToScreenReader]);

  // Handle view mode change
  const handleViewModeChange = useCallback((newMode) => {
    if (!planLimits.viewModes.includes(newMode)) {
      announceToScreenReader(`${newMode} view is not available in your current plan. Please upgrade to access this feature.`);
      return;
    }

    setViewMode(newMode);
    announceToScreenReader(`Switched to ${newMode} view`);
  }, [planLimits.viewModes, announceToScreenReader]);

  // Menu handlers
  const handleMenuOpen = useCallback((event, service) => {
    setAnchorEl(event.currentTarget);
    setSelectedService(service);
  }, []);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedService(null);
  }, []);

  // Filter handlers
  const handleClearFilters = useCallback(() => {
    setSearchTerm('');
    setStatusFilter('all');
    setCategoryFilter('all');
    setIndustryFilter('all');
    setSortBy('name');
    setSortOrder('asc');
    setSelectedServices([]);
    announceToScreenReader('All filters cleared');
  }, [announceToScreenReader]);

  // Handle service actions
  const handleServiceAction = useCallback(async (action, service) => {
    try {
      if (onServiceAction) {
        await onServiceAction(action, service);
      }

      // Update usage tracking
      await updateUsage('service_actions', 1, {
        action,
        serviceId: service.id
      });

      announceToScreenReader(`${action} action completed for ${service.name}`);
    } catch (error) {
      console.error('Error performing service action:', error);
    }
  }, [onServiceAction, updateUsage, announceToScreenReader]);

  // Handle favorite toggle
  const handleFavoriteToggle = useCallback((serviceId) => {
    setFavoriteServices(prev => {
      const isFavorite = prev.includes(serviceId);
      const newFavorites = isFavorite
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId];

      announceToScreenReader(isFavorite ? 'Removed from favorites' : 'Added to favorites');
      return newFavorites;
    });
  }, [announceToScreenReader]);

  // Calculate active filters
  const hasActiveFilters = useMemo(() => {
    return searchTerm || statusFilter !== 'all' ||
      categoryFilter !== 'all' || industryFilter !== 'all' ||
      sortBy !== 'name' || sortOrder !== 'asc';
  }, [searchTerm, statusFilter, categoryFilter, industryFilter, sortBy, sortOrder]);

  // Effects for real-time updates
  useEffect(() => {
    if (enableRealTimeUpdates && services.length > 0) {
      // Simulate service analytics data loading
      const analytics = {};
      const performance = {};
      const health = {};

      services.forEach(service => {
        analytics[service.id] = {
          views: Math.floor(Math.random() * 1000),
          usage: Math.floor(Math.random() * 100),
          performance: Math.floor(Math.random() * 100)
        };

        performance[service.id] = {
          score: Math.floor(Math.random() * 100),
          uptime: 95 + Math.random() * 5,
          responseTime: Math.floor(Math.random() * 200)
        };

        health[service.id] = {
          score: Math.floor(Math.random() * 100),
          status: service.status || SERVICE_STATUS.ACTIVE,
          lastCheck: new Date().toISOString()
        };
      });

      setServiceAnalytics(analytics);
      setPerformanceMetrics(performance);
      setServiceHealth(health);
    }
  }, [services, enableRealTimeUpdates]);

  // Show plan limitation warning for services
  if (services.length >= planLimits.maxServices && planLimits.maxServices !== -1) {
    return (
      <Box
        ref={servicesRef}
        sx={{
          p: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.warning.light, 0.1)} 0%, ${alpha(theme.palette.warning.light, 0.05)} 100%)`,
          borderRadius: 2,
          border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`
        }}
      >
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body1" gutterBottom>
            You&apos;ve reached the maximum number of services ({planLimits.maxServices}) for your current plan.
          </Typography>
          <Typography variant="body2">
            Upgrade to create more services and unlock advanced service management features.
          </Typography>
        </Alert>
        <Button variant="contained" startIcon={<UpgradeIcon />}>
          Upgrade Plan
        </Button>
      </Box>
    );
  }

  return (
    <Box
      ref={servicesRef}
      sx={{
        p: 2,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.05)} 0%, ${alpha(theme.palette.secondary.light, 0.05)} 100%)`,
        borderRadius: 2
      }}
    >
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <BusinessIcon sx={{ color: ACE_COLORS.PURPLE }} />
            <Typography variant="h5" component="h2" fontWeight="bold">
              Services Management
            </Typography>
            <Badge badgeContent={filteredServices.length} color="primary" />
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* View Mode Selector */}
            <FeatureGate requiredPlan={2}>
              <Tabs value={planLimits.viewModes.indexOf(viewMode)} onChange={(_, newValue) => handleViewModeChange(planLimits.viewModes[newValue])}>
                {planLimits.viewModes.includes(VIEW_MODES.LIST) && <Tab icon={<ListViewIcon />} />}
                {planLimits.viewModes.includes(VIEW_MODES.GRID) && <Tab icon={<GridViewIcon />} />}
                {planLimits.viewModes.includes(VIEW_MODES.ANALYTICS) && <Tab icon={<AnalyticsIcon />} />}
                {planLimits.viewModes.includes(VIEW_MODES.DASHBOARD) && <Tab icon={<DashboardIcon />} />}
              </Tabs>
            </FeatureGate>

            <Tooltip title="Refresh services">
              <IconButton onClick={handleRefreshServices} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <FeatureGate requiredPlan={2}>
              <Tooltip title="Export services">
                <IconButton onClick={handleExportServices} size="small">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </FeatureGate>
          </Box>
        </Box>

        {/* Plan Information */}
        <Card sx={{ mb: 2, background: alpha(theme.palette.info.light, 0.1) }}>
          <CardContent sx={{ py: 1.5 }}>
            <Typography variant="subtitle2" gutterBottom>
              Plan: {subscription?.plan_name || 'Creator'}
              ({services.length}/{planLimits.maxServices === -1 ? '∞' : planLimits.maxServices} services used)
            </Typography>
            <LinearProgress
              variant="determinate"
              value={planLimits.maxServices === -1 ? 0 : (services.length / planLimits.maxServices) * 100}
              sx={{ height: 4, borderRadius: 2 }}
            />
          </CardContent>
        </Card>
      </Box>

      {/* Enhanced Search and Filter Controls */}
      <Card sx={{ ...glassMorphismStyles, mb: 3 }}>
        <CardContent sx={{ pb: 2 }}>
          <Grid container spacing={2} alignItems="center">
            {/* Search */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search services, categories, industries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => setSearchTerm('')}
                        sx={{
                          '&:focus-visible': {
                            outline: `2px solid ${theme.palette.primary.main}`,
                            outlineOffset: '2px',
                          }
                        }}
                      >
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: alpha(theme.palette.background.paper, 0.7),
                  }
                }}
              />
            </Grid>

            {/* Filter and Bulk Actions */}
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, flexWrap: 'wrap' }}>
                {/* Bulk Actions */}
                <FeatureGate requiredPlan={2}>
                  {selectedServices.length > 0 && (
                    <Button
                      size="small"
                      variant="contained"
                      onClick={() => setBulkActionDialogOpen(true)}
                      startIcon={<SettingsIcon />}
                    >
                      Bulk Actions ({selectedServices.length})
                    </Button>
                  )}
                </FeatureGate>

                {hasActiveFilters && (
                  <Button
                    size="small"
                    onClick={handleClearFilters}
                    startIcon={<ClearIcon />}
                  >
                    Clear Filters
                  </Button>
                )}

                <Button
                  variant={showFilters ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => setShowFilters(!showFilters)}
                  startIcon={<FilterIcon />}
                  endIcon={showFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                >
                  Filters
                  {hasActiveFilters && (
                    <Chip
                      label="•"
                      size="small"
                      color="primary"
                      sx={{ ml: 1, minWidth: 'auto', width: 8, height: 8 }}
                    />
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>

          {/* Expandable Filters */}
          <Collapse in={showFilters}>
            <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={statusFilter}
                      label="Status"
                      onChange={(e) => setStatusFilter(e.target.value)}
                    >
                      <MenuItem value="all">All Statuses</MenuItem>
                      {filterOptions.statuses.map(status => (
                        <MenuItem key={status} value={status}>
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Category</InputLabel>
                    <Select
                      value={categoryFilter}
                      label="Category"
                      onChange={(e) => setCategoryFilter(e.target.value)}
                    >
                      <MenuItem value="all">All Categories</MenuItem>
                      {filterOptions.categories.map(category => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Industry</InputLabel>
                    <Select
                      value={industryFilter}
                      label="Industry"
                      onChange={(e) => setIndustryFilter(e.target.value)}
                    >
                      <MenuItem value="all">All Industries</MenuItem>
                      {filterOptions.industries.map(industry => (
                        <MenuItem key={industry} value={industry}>
                          {industry}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Sort By</InputLabel>
                    <Select
                      value={sortBy}
                      label="Sort By"
                      onChange={(e) => setSortBy(e.target.value)}
                    >
                      <MenuItem value="name">Name</MenuItem>
                      <MenuItem value="created_at">Created Date</MenuItem>
                      <MenuItem value="updated_at">Updated Date</MenuItem>
                      <MenuItem value="status">Status</MenuItem>
                      <FeatureGate requiredPlan={2}>
                        <MenuItem value="performance">Performance</MenuItem>
                        <MenuItem value="health">Health Score</MenuItem>
                      </FeatureGate>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              {/* Advanced Filters */}
              <FeatureGate requiredPlan={2}>
                <Box sx={{ mt: 2 }}>
                  <Button
                    size="small"
                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    startIcon={<SettingsIcon />}
                    endIcon={showAdvancedFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  >
                    Advanced Filters
                  </Button>

                  <Collapse in={showAdvancedFilters}>
                    <Box sx={{ mt: 2, p: 2, background: alpha(theme.palette.primary.light, 0.05), borderRadius: 1 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={favoriteServices.length > 0}
                                onChange={(e) => e.target.checked ? setFavoriteServices(services.map(s => s.id)) : setFavoriteServices([])}
                              />
                            }
                            label="Show Favorites Only"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth size="small">
                            <InputLabel>Sort Order</InputLabel>
                            <Select
                              value={sortOrder}
                              label="Sort Order"
                              onChange={(e) => setSortOrder(e.target.value)}
                            >
                              <MenuItem value="asc">Ascending</MenuItem>
                              <MenuItem value="desc">Descending</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                      </Grid>
                    </Box>
                  </Collapse>
                </Box>
              </FeatureGate>
            </Box>
          </Collapse>
        </CardContent>
      </Card>

      {/* Results Summary and Bulk Selection */}
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="body2" color="textSecondary">
            {filteredServices.length} of {services.length} services
            {hasActiveFilters && ' (filtered)'}
          </Typography>

          <FeatureGate requiredPlan={2}>
            {filteredServices.length > 0 && (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedServices.length === filteredServices.length}
                    indeterminate={selectedServices.length > 0 && selectedServices.length < filteredServices.length}
                    onChange={handleSelectAllServices}
                  />
                }
                label="Select All"
              />
            )}
          </FeatureGate>
        </Box>

        {loading && <CircularProgress size={20} />}
      </Box>

      {/* Services Display */}
      {filteredServices.length === 0 ? (
        <Card sx={{ ...glassMorphismStyles, textAlign: 'center', py: 4 }}>
          <CardContent>
            <BusinessIcon sx={{ fontSize: 64, color: theme.palette.text.secondary, mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              {hasActiveFilters ? 'No services match your filters' : 'No services found'}
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              {hasActiveFilters
                ? 'Try adjusting your search terms or filters to find services'
                : 'Create your first service to get started with the ACE Social platform'
              }
            </Typography>
            {hasActiveFilters && (
              <Button
                variant="outlined"
                onClick={handleClearFilters}
                sx={{ mt: 2 }}
              >
                Clear Filters
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {filteredServices.map((service) => (
            <Grid item xs={12} md={viewMode === VIEW_MODES.LIST ? 12 : 6} lg={viewMode === VIEW_MODES.LIST ? 12 : 4} key={service.id}>
              <Fade in timeout={300}>
                <Card
                  sx={{
                    ...glassMorphismStyles,
                    height: '100%',
                    display: 'flex',
                    flexDirection: viewMode === VIEW_MODES.LIST ? 'row' : 'column',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: `0 12px 40px 0 ${alpha(theme.palette.common.black, 0.15)}`,
                      borderColor: alpha(ACE_COLORS.PURPLE, 0.4)
                    }
                  }}
                >
                  <CardContent sx={{ flexGrow: 1, pb: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexGrow: 1 }}>
                        <FeatureGate requiredPlan={2}>
                          <Checkbox
                            checked={selectedServices.includes(service.id)}
                            onChange={(e) => handleServiceSelect(service.id, e.target.checked)}
                            size="small"
                          />
                        </FeatureGate>

                        <Typography variant="h6" component="h3" sx={{ flexGrow: 1 }}>
                          {service.name}
                        </Typography>

                        <IconButton
                          size="small"
                          onClick={() => handleFavoriteToggle(service.id)}
                          sx={{ color: favoriteServices.includes(service.id) ? ACE_COLORS.YELLOW : 'inherit' }}
                        >
                          {favoriteServices.includes(service.id) ? <StarIcon /> : <StarBorderIcon />}
                        </IconButton>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={service.status || SERVICE_STATUS.ACTIVE}
                          size="small"
                          color={service.status === SERVICE_STATUS.ACTIVE ? 'success' :
                                 service.status === SERVICE_STATUS.PAUSED ? 'warning' : 'error'}
                          variant="outlined"
                        />

                        <FeatureGate requiredPlan={2}>
                          {serviceHealth[service.id] && (
                            <Tooltip title={`Health Score: ${serviceHealth[service.id].score}%`}>
                              <Chip
                                label={`${serviceHealth[service.id].score}%`}
                                size="small"
                                color={serviceHealth[service.id].score > 80 ? 'success' :
                                       serviceHealth[service.id].score > 60 ? 'warning' : 'error'}
                                icon={<MonitorIcon />}
                              />
                            </Tooltip>
                          )}
                        </FeatureGate>

                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, service)}
                          sx={{
                            '&:focus-visible': {
                              outline: `2px solid ${theme.palette.primary.main}`,
                              outlineOffset: '2px',
                            }
                          }}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Box>
                    </Box>

                    <Typography
                      variant="body2"
                      color="textSecondary"
                      sx={{
                        mb: 2,
                        display: '-webkit-box',
                        WebkitLineClamp: viewMode === VIEW_MODES.LIST ? 2 : 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}
                    >
                      {service.description || 'No description provided'}
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                      {service.category && (
                        <Chip
                          label={service.category}
                          size="small"
                          variant="outlined"
                          icon={<CategoryIcon />}
                        />
                      )}
                      {service.target_industry && (
                        <Chip
                          label={service.target_industry}
                          size="small"
                          variant="outlined"
                          icon={<BusinessIcon />}
                        />
                      )}
                      {service.tags?.map(tag => (
                        <Chip
                          key={tag}
                          label={tag}
                          size="small"
                          variant="outlined"
                          icon={<TagIcon />}
                        />
                      ))}
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Grid container spacing={2} sx={{ textAlign: 'center' }}>
                      <Grid item xs={3}>
                        <Typography variant="h6" color="primary">
                          {service.icps_count || 0}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          ICPs
                        </Typography>
                      </Grid>
                      <Grid item xs={3}>
                        <Typography variant="h6" color="info.main">
                          {service.campaigns_count || 0}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          Campaigns
                        </Typography>
                      </Grid>
                      <Grid item xs={3}>
                        <Typography variant="h6" color="success.main">
                          {service.content_count || 0}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          Content
                        </Typography>
                      </Grid>
                      <Grid item xs={3}>
                        <FeatureGate requiredPlan={2}>
                          <Typography variant="h6" color="warning.main">
                            {serviceAnalytics[service.id]?.usage || 0}%
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            Usage
                          </Typography>
                        </FeatureGate>
                      </Grid>
                    </Grid>

                    {/* Performance Metrics */}
                    <FeatureGate requiredPlan={2}>
                      {performanceMetrics[service.id] && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="caption" color="textSecondary" gutterBottom>
                            Performance
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={performanceMetrics[service.id].score}
                            sx={{
                              height: 6,
                              borderRadius: 3,
                              backgroundColor: alpha(theme.palette.grey[300], 0.3)
                            }}
                          />
                          <Typography variant="caption" color="textSecondary">
                            {performanceMetrics[service.id].uptime?.toFixed(1)}% uptime • {performanceMetrics[service.id].responseTime}ms avg
                          </Typography>
                        </Box>
                      )}
                    </FeatureGate>
                  </CardContent>

                  <CardActions sx={{ p: 2, pt: 0, gap: 1 }}>
                    <Button
                      size="small"
                      startIcon={<PersonIcon />}
                      onClick={() => onViewICPs?.(service)}
                      disabled={!service.icps_count}
                    >
                      View ICPs
                    </Button>
                    <Button
                      size="small"
                      startIcon={<CampaignIcon />}
                      onClick={() => onCreateCampaign?.(service)}
                    >
                      Campaign
                    </Button>

                    <FeatureGate requiredPlan={2}>
                      <Button
                        size="small"
                        startIcon={<AnalyticsIcon />}
                        onClick={() => handleServiceAction('analytics', service)}
                      >
                        Analytics
                      </Button>
                    </FeatureGate>
                  </CardActions>
                </Card>
              </Fade>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Enhanced Service Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            ...glassMorphismStyles,
            minWidth: 250,
          }
        }}
      >
        <MenuItem onClick={() => { onView?.(selectedService); handleMenuClose(); }}>
          <ViewIcon sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={() => { onEdit?.(selectedService); handleMenuClose(); }}>
          <EditIcon sx={{ mr: 1 }} />
          Edit Service
        </MenuItem>
        <MenuItem onClick={() => { onViewICPs?.(selectedService); handleMenuClose(); }}>
          <PersonIcon sx={{ mr: 1 }} />
          Manage ICPs
        </MenuItem>

        <Divider />

        {/* Service Actions */}
        <MenuItem onClick={() => { handleServiceAction('start', selectedService); handleMenuClose(); }}>
          <StartIcon sx={{ mr: 1 }} />
          Start Service
        </MenuItem>
        <MenuItem onClick={() => { handleServiceAction('pause', selectedService); handleMenuClose(); }}>
          <PauseIcon sx={{ mr: 1 }} />
          Pause Service
        </MenuItem>
        <MenuItem onClick={() => { handleServiceAction('configure', selectedService); handleMenuClose(); }}>
          <ConfigureIcon sx={{ mr: 1 }} />
          Configure
        </MenuItem>

        <FeatureGate requiredPlan={2}>
          <MenuItem onClick={() => { handleServiceAction('analytics', selectedService); handleMenuClose(); }}>
            <AnalyticsIcon sx={{ mr: 1 }} />
            View Analytics
          </MenuItem>
          <MenuItem onClick={() => { handleServiceAction('monitor', selectedService); handleMenuClose(); }}>
            <MonitorIcon sx={{ mr: 1 }} />
            Monitor Performance
          </MenuItem>
        </FeatureGate>

        <Divider />

        <FeatureGate requiredPlan={2}>
          <MenuItem onClick={() => { onDuplicate?.(selectedService); handleMenuClose(); }}>
            <DuplicateIcon sx={{ mr: 1 }} />
            Duplicate Service
          </MenuItem>
        </FeatureGate>

        <FeatureGate requiredPlan={3}>
          <MenuItem onClick={() => { handleServiceAction('automation', selectedService); handleMenuClose(); }}>
            <AutomationIcon sx={{ mr: 1 }} />
            Automation Settings
          </MenuItem>
        </FeatureGate>

        <MenuItem onClick={() => { handleServiceAction('archive', selectedService); handleMenuClose(); }}>
          <ArchiveIcon sx={{ mr: 1 }} />
          Archive Service
        </MenuItem>

        <MenuItem
          onClick={() => { onDelete?.(selectedService); handleMenuClose(); }}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Delete Service
        </MenuItem>
      </Menu>

      {/* Bulk Actions Dialog */}
      <FeatureGate requiredPlan={2}>
        <Dialog open={bulkActionDialogOpen} onClose={() => setBulkActionDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Bulk Actions ({selectedServices.length} services selected)</DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="textSecondary" paragraph>
              Choose an action to apply to all selected services:
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<StartIcon />}
                  onClick={() => handleBulkAction('start')}
                >
                  Start All
                </Button>
              </Grid>
              <Grid item xs={6}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<PauseIcon />}
                  onClick={() => handleBulkAction('pause')}
                >
                  Pause All
                </Button>
              </Grid>
              <Grid item xs={6}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<ArchiveIcon />}
                  onClick={() => handleBulkAction('archive')}
                >
                  Archive All
                </Button>
              </Grid>
              <Grid item xs={6}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<DeleteIcon />}
                  onClick={() => handleBulkAction('delete')}
                  color="error"
                >
                  Delete All
                </Button>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setBulkActionDialogOpen(false)}>Cancel</Button>
          </DialogActions>
        </Dialog>
      </FeatureGate>
    </Box>
  );
}));

ServicesList.displayName = 'ServicesList';

ServicesList.propTypes = {
  /** Array of services to display */
  services: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    category: PropTypes.string,
    target_industry: PropTypes.string,
    status: PropTypes.oneOf(Object.values(SERVICE_STATUS)),
    icps_count: PropTypes.number,
    campaigns_count: PropTypes.number,
    content_count: PropTypes.number,
    tags: PropTypes.arrayOf(PropTypes.string),
    created_at: PropTypes.string,
    updated_at: PropTypes.string
  })),
  /** Loading state */
  loading: PropTypes.bool,
  /** Edit service callback */
  onEdit: PropTypes.func,
  /** Delete service callback */
  onDelete: PropTypes.func,
  /** Duplicate service callback */
  onDuplicate: PropTypes.func,
  /** View service callback */
  onView: PropTypes.func,
  /** View ICPs callback */
  onViewICPs: PropTypes.func,
  /** Create campaign callback */
  onCreateCampaign: PropTypes.func,
  /** Bulk action callback */
  onBulkAction: PropTypes.func,
  /** Refresh services callback */
  onRefresh: PropTypes.func,
  /** Service action callback */
  onServiceAction: PropTypes.func,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool
};

export default ServicesList;

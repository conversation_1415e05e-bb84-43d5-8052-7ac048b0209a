/**
 * FeatureGate Component Test Suite
 * Comprehensive testing for enterprise-grade feature gating
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import FeatureGate, { FeatureLimitGate } from '../FeatureGate';

// Test theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock contexts
const mockAuth = {
  user: {
    id: 'test-user'
  },
  userFeatures: {
    plan_id: 'creator',
    is_appsumo_lifetime: false
  },
  hasFeature: jest.fn(),
  getFeatureDescription: jest.fn(),
  getFeatureLimit: jest.fn(),
  getUserRole: jest.fn(),
  getUserPermissions: jest.fn()
};

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/test' })
}));

describe('FeatureGate', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAuth.hasFeature.mockReturnValue(false);
    mockAuth.getFeatureDescription.mockReturnValue('Test feature description');
    mockAuth.getUserRole.mockReturnValue('viewer');
    mockAuth.getUserPermissions.mockReturnValue([]);
  });

  describe('Basic Functionality', () => {
    test('renders children when user has access', () => {
      mockAuth.hasFeature.mockReturnValue(true);

      render(
        <TestWrapper>
          <FeatureGate feature="test_feature">
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    test('shows access denied message when user lacks access', () => {
      render(
        <TestWrapper>
          <FeatureGate feature="test_feature">
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Feature Not Available')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    test('hides component when hideIfNoAccess is true', () => {
      render(
        <TestWrapper>
          <FeatureGate feature="test_feature" hideIfNoAccess>
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.queryByText('Feature Not Available')).not.toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    test('shows custom fallback message', () => {
      const customMessage = 'Custom access denied message';

      render(
        <TestWrapper>
          <FeatureGate feature="test_feature" fallbackMessage={customMessage}>
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText(customMessage)).toBeInTheDocument();
    });

    test('shows upgrade button by default', () => {
      render(
        <TestWrapper>
          <FeatureGate feature="test_feature">
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Upgrade Plan')).toBeInTheDocument();
    });

    test('hides upgrade button when disabled', () => {
      render(
        <TestWrapper>
          <FeatureGate feature="test_feature" showUpgradeButton={false}>
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.queryByText('Upgrade Plan')).not.toBeInTheDocument();
    });
  });

  describe('Role-based Access Control', () => {
    test('grants access when user has required role', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      mockAuth.getUserRole.mockReturnValue('admin');

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            enableRoleValidation 
            requiredRole="admin"
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    test('denies access when user lacks required role', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      mockAuth.getUserRole.mockReturnValue('viewer');

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            enableRoleValidation 
            requiredRole="admin"
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
      expect(screen.getByText('Feature Not Available')).toBeInTheDocument();
    });
  });

  describe('Plan-based Access Control', () => {
    test('grants access when user has required plan', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      mockAuth.userFeatures.plan_id = 'accelerator';

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            requiredPlan="accelerator"
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    test('denies access when user lacks required plan', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      mockAuth.userFeatures.plan_id = 'creator';

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            requiredPlan="dominator"
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });
  });

  describe('Permission-based Access Control', () => {
    test('grants access when user has required permissions', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      mockAuth.getUserPermissions.mockReturnValue(['read', 'write', 'delete']);

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            requiredPermissions={['read', 'write']}
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    test('denies access when user lacks required permissions', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      mockAuth.getUserPermissions.mockReturnValue(['read']);

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            requiredPermissions={['read', 'write', 'delete']}
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });
  });

  describe('Custom Validation', () => {
    test('grants access when custom validator returns true', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      const customValidator = jest.fn().mockReturnValue(true);

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            customValidator={customValidator}
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
      expect(customValidator).toHaveBeenCalled();
    });

    test('denies access when custom validator returns false', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      const customValidator = jest.fn().mockReturnValue(false);

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            customValidator={customValidator}
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
      expect(customValidator).toHaveBeenCalled();
    });
  });

  describe('Variants', () => {
    test('renders simple variant correctly', () => {
      render(
        <TestWrapper>
          <FeatureGate feature="test_feature" variant="simple">
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Feature Not Available')).toBeInTheDocument();
    });

    test('renders detailed variant with permission details', () => {
      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            variant="detailed"
            enablePermissionDetails
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByText('Access Restricted')).toBeInTheDocument();
    });
  });

  describe('Analytics Integration', () => {
    test('tracks analytics events when enabled', () => {
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            enableAnalytics 
            onAnalytics={onAnalytics}
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          component: 'FeatureGate',
          action: 'access_denied'
        })
      );
    });
  });

  describe('Event Handlers', () => {
    test('calls onAccessDenied when access is denied', () => {
      const onAccessDenied = jest.fn();

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            onAccessDenied={onAccessDenied}
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(onAccessDenied).toHaveBeenCalled();
    });

    test('calls onAccessGranted when access is granted', () => {
      mockAuth.hasFeature.mockReturnValue(true);
      const onAccessGranted = jest.fn();

      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            onAccessGranted={onAccessGranted}
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(onAccessGranted).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels', () => {
      render(
        <TestWrapper>
          <FeatureGate 
            feature="test_feature" 
            ariaLabel="Custom accessibility label"
          >
            <div>Protected Content</div>
          </FeatureGate>
        </TestWrapper>
      );

      expect(screen.getByLabelText('Custom accessibility label')).toBeInTheDocument();
    });
  });
});

describe('FeatureLimitGate', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAuth.getFeatureLimit.mockReturnValue(100);
  });

  describe('Basic Functionality', () => {
    test('renders children when within limit', () => {
      render(
        <TestWrapper>
          <FeatureLimitGate limitName="monthly_posts" currentUsage={50}>
            <div>Protected Content</div>
          </FeatureLimitGate>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    test('shows limit exceeded message when over limit', () => {
      render(
        <TestWrapper>
          <FeatureLimitGate limitName="monthly_posts" currentUsage={150}>
            <div>Protected Content</div>
          </FeatureLimitGate>
        </TestWrapper>
      );

      expect(screen.getByText('Usage Limit Reached')).toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });

    test('shows usage warning when approaching limit', () => {
      render(
        <TestWrapper>
          <FeatureLimitGate 
            limitName="monthly_posts" 
            currentUsage={85}
            enableUsageWarning
            warningThreshold={0.8}
          >
            <div>Protected Content</div>
          </FeatureLimitGate>
        </TestWrapper>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
      expect(screen.getByText('Approaching Limit')).toBeInTheDocument();
    });

    test('hides component when hideIfExceeded is true', () => {
      render(
        <TestWrapper>
          <FeatureLimitGate 
            limitName="monthly_posts" 
            currentUsage={150}
            hideIfExceeded
          >
            <div>Protected Content</div>
          </FeatureLimitGate>
        </TestWrapper>
      );

      expect(screen.queryByText('Usage Limit Reached')).not.toBeInTheDocument();
      expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    });
  });

  describe('Analytics Integration', () => {
    test('tracks limit exceeded events', () => {
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <FeatureLimitGate 
            limitName="monthly_posts" 
            currentUsage={150}
            enableAnalytics 
            onAnalytics={onAnalytics}
          >
            <div>Protected Content</div>
          </FeatureLimitGate>
        </TestWrapper>
      );

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          component: 'FeatureLimitGate',
          action: 'limit_exceeded'
        })
      );
    });
  });

  describe('Event Handlers', () => {
    test('calls onLimitExceeded when limit is exceeded', () => {
      const onLimitExceeded = jest.fn();

      render(
        <TestWrapper>
          <FeatureLimitGate 
            limitName="monthly_posts" 
            currentUsage={150}
            onLimitExceeded={onLimitExceeded}
          >
            <div>Protected Content</div>
          </FeatureLimitGate>
        </TestWrapper>
      );

      expect(onLimitExceeded).toHaveBeenCalled();
    });
  });
});

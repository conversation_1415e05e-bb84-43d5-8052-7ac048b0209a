/**
 * Enhanced ACE Social Template Editor - Enterprise-grade email template editing component
 * Features: Comprehensive email template editing with advanced WYSIWYG editor, drag-and-drop
 * component library, and real-time preview capabilities for ACE Social email templates,
 * detailed template creation dashboard with component palette and layout management, advanced
 * editing features with undo/redo functionality and template versioning, ACE Social's email
 * system integration with seamless template lifecycle management, editing interaction features
 * including drag-and-drop interface and real-time collaboration, editing state management
 * with version control and auto-save functionality, and real-time editing monitoring with
 * live preview updates and automatic template optimization recommendations
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Tabs,
  Tab,
  Grid,
  Chip,
  IconButton,
  Toolt<PERSON>,
  <PERSON>ert,
  Divider,
  Stack,
  Paper,
  Badge,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Switch,
  FormControlLabel,
  Slider,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide,
  Collapse
} from '@mui/material';
import {
  Close as CloseIcon,
  Preview as PreviewIcon,
  Send as SendIcon,
  Code as CodeIcon,
  Palette as DesignIcon,
  Settings as SettingsIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  DragIndicator as DragIcon,
  ContentCopy as CopyIcon,
  History as HistoryIcon,
  AutoAwesome as AutoAwesomeIcon,
  Security as SecurityIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Analytics as AnalyticsIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  Insights as InsightsIcon,
  MonetizationOn as MoneyIcon,
  Group as GroupIcon,
  Star as StarIcon
} from '@mui/icons-material';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Template editor constants
const EDITOR_MODES = {
  CREATE: 'create',
  EDIT: 'edit',
  DUPLICATE: 'duplicate',
  TEMPLATE: 'template'
};

const TEMPLATE_TYPES = {
  TRANSACTIONAL: 'transactional',
  MARKETING: 'marketing',
  SYSTEM: 'system',
  NOTIFICATION: 'notification'
};

const EDITOR_TABS = {
  BASIC_INFO: 0,
  CONTENT: 1,
  VARIABLES: 2,
  DESIGN: 3,
  PREVIEW: 4
};

const AUTO_SAVE_INTERVALS = {
  DISABLED: 0,
  FAST: 30000,    // 30 seconds
  NORMAL: 60000,  // 1 minute
  SLOW: 300000    // 5 minutes
};

// Editor analytics events
const EDITOR_ANALYTICS_EVENTS = {
  EDITOR_OPENED: 'template_editor_opened',
  TAB_CHANGED: 'editor_tab_changed',
  TEMPLATE_SAVED: 'template_saved',
  PREVIEW_VIEWED: 'template_preview_viewed',
  VARIABLE_ADDED: 'template_variable_added',
  AUTO_SAVE_TRIGGERED: 'template_auto_save_triggered',
  VALIDATION_FAILED: 'template_validation_failed'
};

/**
 * Enhanced Template Editor - Comprehensive template editing with advanced features
 * Implements detailed template editing management and enterprise-grade editing capabilities
 */

const EnhancedTemplateEditor = memo(forwardRef(({
  open,
  onClose,
  onSave,
  template = null,
  mode = EDITOR_MODES.CREATE,
  className,
  enableAdvancedFeatures = true,
  enableRealTimePreview = true,
  enableAutoSave = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableVersionControl = true,
  enableCollaboration = false,
  autoSaveInterval = AUTO_SAVE_INTERVALS.NORMAL,
  maxUndoSteps = 50,
  onTemplateChange,
  onPreview,
  onValidation,
  onAnalyticsTrack,
  onAutoSave,
  onVersionSave,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const dialogRef = useRef(null);
  const formRef = useRef(null);
  const previewRef = useRef(null);
  const autoSaveTimeoutRef = useRef(null);

  // Core state management
  const [currentTab, setCurrentTab] = useState(EDITOR_TABS.BASIC_INFO);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    template_type: TEMPLATE_TYPES.TRANSACTIONAL,
    subject: '',
    html_content: '',
    text_content: '',
    preview_text: '',
    variables: [],
    default_values: {},
    category: '',
    tags: []
  });
  const [newVariable, setNewVariable] = useState('');
  const [newTag, setNewTag] = useState('');
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});

  // Enhanced state management
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [undoStack, setUndoStack] = useState([]);
  const [redoStack, setRedoStack] = useState([]);
  const [validationResults, setValidationResults] = useState({});
  const [editorAnalytics, setEditorAnalytics] = useState({
    startTime: null,
    tabSwitches: 0,
    saveAttempts: 0,
    previewViews: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    saveTemplate: () => handleSave(),
    resetForm: () => handleReset(),
    validateTemplate: () => validateForm(),
    focusDialog: () => dialogRef.current?.focus(),

    // Navigation methods
    changeTab: (tab) => handleTabChange(null, tab),
    getCurrentTab: () => currentTab,
    togglePreview: () => setIsPreviewMode(!isPreviewMode),

    // Data methods
    getFormData: () => formData,
    setFormData: (data) => setFormData(data),
    getErrors: () => errors,
    getWarnings: () => warnings,

    // State methods
    isDirty: () => isDirty,
    isValid: () => Object.keys(errors).length === 0,
    getLastSaved: () => lastSaved,

    // Undo/Redo methods
    undo: () => handleUndo(),
    redo: () => handleRedo(),
    canUndo: () => undoStack.length > 0,
    canRedo: () => redoStack.length > 0,

    // Variable methods
    addVariable: (variable) => handleAddVariable(variable),
    removeVariable: (variable) => handleRemoveVariable(variable),
    getVariables: () => formData.variables,

    // Analytics methods
    getEditorAnalytics: () => editorAnalytics,
    resetAnalytics: () => setEditorAnalytics({
      startTime: null,
      tabSwitches: 0,
      saveAttempts: 0,
      previewViews: 0
    }),

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    duplicateTemplate: () => handleDuplicate(),
    generatePreview: () => generateTemplatePreview(),
    validateContent: () => validateTemplateContent()
  }), [
    currentTab,
    formData,
    errors,
    warnings,
    isDirty,
    lastSaved,
    isPreviewMode,
    undoStack,
    redoStack,
    editorAnalytics,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (open && enableAnalytics) {
      const startTime = new Date().toISOString();
      setEditorAnalytics(prev => ({
        ...prev,
        startTime
      }));

      if (onAnalyticsTrack) {
        onAnalyticsTrack(EDITOR_ANALYTICS_EVENTS.EDITOR_OPENED, {
          mode,
          timestamp: startTime,
          templateId: template?.id
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Template editor opened in ${mode} mode`);
      }
    }
  }, [open, enableAnalytics, mode, template?.id, onAnalyticsTrack, enableAccessibility, announceToScreenReader]);

  // Auto-save effect
  useEffect(() => {
    if (enableAutoSave && isDirty && autoSaveInterval > 0) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        handleAutoSave();
      }, autoSaveInterval);

      return () => {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current);
        }
      };
    }
  }, [enableAutoSave, isDirty, autoSaveInterval, formData]);

  // Initialize form data when template changes
  useEffect(() => {
    if (template && mode === EDITOR_MODES.EDIT) {
      const initialData = {
        name: template.name || '',
        description: template.description || '',
        template_type: template.template_type || TEMPLATE_TYPES.TRANSACTIONAL,
        subject: template.subject || '',
        html_content: template.html_content || '',
        text_content: template.text_content || '',
        preview_text: template.preview_text || '',
        variables: template.variables || [],
        default_values: template.default_values || {},
        category: template.category || '',
        tags: template.tags || []
      };
      setFormData(initialData);

      // Initialize undo stack with original data
      setUndoStack([initialData]);
      setRedoStack([]);
      setIsDirty(false);
    } else {
      // Reset form for create mode
      const defaultData = {
        name: '',
        description: '',
        template_type: TEMPLATE_TYPES.TRANSACTIONAL,
        subject: '',
        html_content: '',
        text_content: '',
        preview_text: '',
        variables: [],
        default_values: {},
        category: '',
        tags: []
      };
      setFormData(defaultData);
      setUndoStack([defaultData]);
      setRedoStack([]);
      setIsDirty(false);
    }
    setErrors({});
    setWarnings({});
    setCurrentTab(EDITOR_TABS.BASIC_INFO);
    setLastSaved(null);
  }, [template, mode, open]);

  // Enhanced handler functions
  const handleInputChange = useCallback((field, value) => {
    // Save current state to undo stack
    setUndoStack(prev => {
      const newStack = [...prev, formData];
      return newStack.length > maxUndoSteps ? newStack.slice(1) : newStack;
    });
    setRedoStack([]); // Clear redo stack on new change

    setFormData(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Clear warning when user starts typing
    if (warnings[field]) {
      setWarnings(prev => ({ ...prev, [field]: null }));
    }

    // Track changes for analytics
    if (enableAnalytics && onTemplateChange) {
      onTemplateChange(field, value);
    }
  }, [formData, errors, warnings, maxUndoSteps, enableAnalytics, onTemplateChange]);

  const handleUndo = useCallback(() => {
    if (undoStack.length > 1) {
      const currentState = undoStack[undoStack.length - 1];
      const previousState = undoStack[undoStack.length - 2];

      setRedoStack(prev => [...prev, currentState]);
      setUndoStack(prev => prev.slice(0, -1));
      setFormData(previousState);
      setIsDirty(true);

      if (enableAccessibility) {
        announceToScreenReader('Undo action performed');
      }
    }
  }, [undoStack, enableAccessibility, announceToScreenReader]);

  const handleRedo = useCallback(() => {
    if (redoStack.length > 0) {
      const nextState = redoStack[redoStack.length - 1];

      setUndoStack(prev => [...prev, nextState]);
      setRedoStack(prev => prev.slice(0, -1));
      setFormData(nextState);
      setIsDirty(true);

      if (enableAccessibility) {
        announceToScreenReader('Redo action performed');
      }
    }
  }, [redoStack, enableAccessibility, announceToScreenReader]);

  const handleAutoSave = useCallback(async () => {
    if (!isDirty || !formData.name) return;

    try {
      // Save to localStorage as backup
      const autoSaveData = {
        formData,
        timestamp: new Date().toISOString(),
        mode
      };
      localStorage.setItem('template_editor_autosave', JSON.stringify(autoSaveData));

      setLastSaved(new Date().toISOString());
      setIsDirty(false);

      if (onAutoSave) {
        onAutoSave(autoSaveData);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(EDITOR_ANALYTICS_EVENTS.AUTO_SAVE_TRIGGERED, {
          timestamp: new Date().toISOString(),
          mode
        });
      }

      if (enableAccessibility) {
        announceToScreenReader('Template auto-saved successfully');
      }
    } catch (error) {
      console.warn('Auto-save failed:', error);
    }
  }, [isDirty, formData, mode, onAutoSave, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleAddVariable = useCallback((variableName = newVariable) => {
    if (variableName && !formData.variables.includes(variableName)) {
      handleInputChange('variables', [...formData.variables, variableName]);
      handleInputChange('default_values', {
        ...formData.default_values,
        [variableName]: ''
      });
      setNewVariable('');

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(EDITOR_ANALYTICS_EVENTS.VARIABLE_ADDED, {
          variable: variableName,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Variable ${variableName} added`);
      }
    }
  }, [newVariable, formData.variables, formData.default_values, handleInputChange, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleRemoveVariable = useCallback((variable) => {
    const newDefaultValues = { ...formData.default_values };
    delete newDefaultValues[variable];

    handleInputChange('variables', formData.variables.filter(v => v !== variable));
    handleInputChange('default_values', newDefaultValues);

    if (enableAccessibility) {
      announceToScreenReader(`Variable ${variable} removed`);
    }
  }, [formData.variables, formData.default_values, handleInputChange, enableAccessibility, announceToScreenReader]);

  const handleAddTag = useCallback(() => {
    if (newTag && !formData.tags.includes(newTag)) {
      handleInputChange('tags', [...formData.tags, newTag]);
      setNewTag('');

      if (enableAccessibility) {
        announceToScreenReader(`Tag ${newTag} added`);
      }
    }
  }, [newTag, formData.tags, handleInputChange, enableAccessibility, announceToScreenReader]);

  const handleRemoveTag = useCallback((tag) => {
    handleInputChange('tags', formData.tags.filter(t => t !== tag));

    if (enableAccessibility) {
      announceToScreenReader(`Tag ${tag} removed`);
    }
  }, [formData.tags, handleInputChange, enableAccessibility, announceToScreenReader]);

  // Enhanced validation function
  const validateForm = useCallback(() => {
    const newErrors = {};
    const newWarnings = {};

    // Required field validation
    if (!formData.name.trim()) {
      newErrors.name = 'Template name is required';
    } else if (formData.name.length < 3) {
      newWarnings.name = 'Template name should be at least 3 characters';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    } else if (formData.subject.length > 100) {
      newWarnings.subject = 'Subject line is quite long, consider shortening for better deliverability';
    }

    if (!formData.html_content.trim()) {
      newErrors.html_content = 'HTML content is required';
    } else {
      // Basic HTML validation
      const htmlValidation = validateTemplateContent();
      if (!htmlValidation.isValid) {
        newErrors.html_content = htmlValidation.error;
      }
    }

    // Variable validation
    formData.variables.forEach(variable => {
      if (!formData.html_content.includes(`{{${variable}}}`)) {
        newWarnings[`variable_${variable}`] = `Variable {{${variable}}} is not used in the template`;
      }
    });

    setErrors(newErrors);
    setWarnings(newWarnings);

    const isValid = Object.keys(newErrors).length === 0;

    if (onValidation) {
      onValidation({
        isValid,
        errors: newErrors,
        warnings: newWarnings
      });
    }

    if (!isValid && enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(EDITOR_ANALYTICS_EVENTS.VALIDATION_FAILED, {
        errors: Object.keys(newErrors),
        timestamp: new Date().toISOString()
      });
    }

    return isValid;
  }, [formData, onValidation, enableAnalytics, onAnalyticsTrack]);

  const validateTemplateContent = useCallback(() => {
    try {
      // Basic HTML validation
      const parser = new DOMParser();
      const doc = parser.parseFromString(formData.html_content, 'text/html');
      const parserErrors = doc.querySelectorAll('parsererror');

      if (parserErrors.length > 0) {
        return {
          isValid: false,
          error: 'Invalid HTML structure detected'
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: 'HTML validation failed'
      };
    }
  }, [formData.html_content]);

  const generateTemplatePreview = useCallback(() => {
    let previewContent = formData.html_content;

    // Replace variables with default values or placeholders
    formData.variables.forEach(variable => {
      const defaultValue = formData.default_values[variable] || `[${variable}]`;
      const regex = new RegExp(`{{${variable}}}`, 'g');
      previewContent = previewContent.replace(regex, defaultValue);
    });

    return {
      subject: formData.subject,
      html_content: previewContent,
      text_content: formData.text_content,
      preview_text: formData.preview_text
    };
  }, [formData]);

  const handleSave = useCallback(async () => {
    setEditorAnalytics(prev => ({
      ...prev,
      saveAttempts: prev.saveAttempts + 1
    }));

    if (validateForm()) {
      try {
        await onSave(formData);
        setIsDirty(false);
        setLastSaved(new Date().toISOString());

        if (enableAnalytics && onAnalyticsTrack) {
          onAnalyticsTrack(EDITOR_ANALYTICS_EVENTS.TEMPLATE_SAVED, {
            mode,
            templateId: template?.id,
            timestamp: new Date().toISOString()
          });
        }

        if (enableAccessibility) {
          announceToScreenReader('Template saved successfully');
        }

        // Clear auto-save data
        localStorage.removeItem('template_editor_autosave');
      } catch (error) {
        console.error('Error saving template:', error);

        if (enableAccessibility) {
          announceToScreenReader('Failed to save template. Please check the errors and try again.');
        }
      }
    }
  }, [validateForm, onSave, formData, mode, template?.id, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleTabChange = useCallback((event, newValue) => {
    setCurrentTab(newValue);

    setEditorAnalytics(prev => ({
      ...prev,
      tabSwitches: prev.tabSwitches + 1
    }));

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(EDITOR_ANALYTICS_EVENTS.TAB_CHANGED, {
        fromTab: currentTab,
        toTab: newValue,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      const tabNames = ['Basic Info', 'Content', 'Variables', 'Design', 'Preview'];
      announceToScreenReader(`Switched to ${tabNames[newValue]} tab`);
    }
  }, [currentTab, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handlePreview = useCallback(() => {
    setIsPreviewMode(!isPreviewMode);

    setEditorAnalytics(prev => ({
      ...prev,
      previewViews: prev.previewViews + 1
    }));

    if (onPreview) {
      onPreview(generateTemplatePreview());
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(EDITOR_ANALYTICS_EVENTS.PREVIEW_VIEWED, {
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Preview mode ${!isPreviewMode ? 'enabled' : 'disabled'}`);
    }
  }, [isPreviewMode, onPreview, generateTemplatePreview, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleReset = useCallback(() => {
    const defaultData = {
      name: '',
      description: '',
      template_type: TEMPLATE_TYPES.TRANSACTIONAL,
      subject: '',
      html_content: '',
      text_content: '',
      preview_text: '',
      variables: [],
      default_values: {},
      category: '',
      tags: []
    };

    setFormData(defaultData);
    setErrors({});
    setWarnings({});
    setCurrentTab(EDITOR_TABS.BASIC_INFO);
    setIsDirty(false);
    setLastSaved(null);
    setUndoStack([defaultData]);
    setRedoStack([]);

    if (enableAccessibility) {
      announceToScreenReader('Template form reset to default values');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleDuplicate = useCallback(() => {
    const duplicatedData = {
      ...formData,
      name: `${formData.name} (Copy)`,
      id: undefined // Remove ID for new template
    };

    setFormData(duplicatedData);
    setIsDirty(true);

    if (enableAccessibility) {
      announceToScreenReader('Template duplicated. Please review and modify as needed.');
    }
  }, [formData, enableAccessibility, announceToScreenReader]);

  const renderBasicInfo = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Template Name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          error={!!errors.name}
          helperText={errors.name}
          required
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Template Type</InputLabel>
          <Select
            value={formData.template_type}
            label="Template Type"
            onChange={(e) => handleInputChange('template_type', e.target.value)}
          >
            <MenuItem value="transactional">Transactional</MenuItem>
            <MenuItem value="marketing">Marketing</MenuItem>
            <MenuItem value="system">System</MenuItem>
            <MenuItem value="notification">Notification</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          multiline
          rows={3}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="Category"
          value={formData.category}
          onChange={(e) => handleInputChange('category', e.target.value)}
          placeholder="e.g., Welcome, Billing, Notifications"
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Tags
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, mb: 1, flexWrap: 'wrap' }}>
            {formData.tags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                onDelete={() => handleRemoveTag(tag)}
                size="small"
                color="primary"
                variant="outlined"
              />
            ))}
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              size="small"
              placeholder="Add tag"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
            />
            <IconButton size="small" onClick={handleAddTag}>
              <AddIcon />
            </IconButton>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );

  const renderContent = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Subject Line"
          value={formData.subject}
          onChange={(e) => handleInputChange('subject', e.target.value)}
          error={!!errors.subject}
          helperText={errors.subject || 'Use {{variable}} for dynamic content'}
          required
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Preview Text"
          value={formData.preview_text}
          onChange={(e) => handleInputChange('preview_text', e.target.value)}
          helperText="Text shown in email client preview"
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="HTML Content"
          value={formData.html_content}
          onChange={(e) => handleInputChange('html_content', e.target.value)}
          error={!!errors.html_content}
          helperText={errors.html_content}
          multiline
          rows={12}
          required
          sx={{
            '& .MuiInputBase-input': {
              fontFamily: 'monospace',
              fontSize: '0.875rem'
            }
          }}
        />
      </Grid>
      <Grid item xs={12}>
        <TextField
          fullWidth
          label="Plain Text Content (Optional)"
          value={formData.text_content}
          onChange={(e) => handleInputChange('text_content', e.target.value)}
          multiline
          rows={6}
          helperText="Fallback for email clients that don't support HTML"
        />
      </Grid>
    </Grid>
  );

  const renderVariables = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Template Variables
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Define variables that can be used in your template with {`{{variable_name}}`} syntax.
      </Typography>

      {/* Add Variable */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <TextField
          label="Variable Name"
          value={newVariable}
          onChange={(e) => setNewVariable(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleAddVariable()}
          placeholder="e.g., user_name, company_name"
        />
        <Button
          variant="outlined"
          onClick={handleAddVariable}
          startIcon={<AddIcon />}
        >
          Add Variable
        </Button>
      </Box>

      {/* Variables List */}
      {formData.variables.length > 0 ? (
        <Grid container spacing={2}>
          {formData.variables.map((variable, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Box
                sx={{
                  p: 2,
                  border: 1,
                  borderColor: 'divider',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2">
                    {`{{${variable}}}`}
                  </Typography>
                  <TextField
                    size="small"
                    fullWidth
                    placeholder="Default value"
                    value={formData.default_values[variable] || ''}
                    onChange={(e) => handleInputChange('default_values', {
                      ...formData.default_values,
                      [variable]: e.target.value
                    })}
                    sx={{ mt: 1 }}
                  />
                </Box>
                <IconButton
                  size="small"
                  onClick={() => handleRemoveVariable(variable)}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Grid>
          ))}
        </Grid>
      ) : (
        <Alert severity="info">
          No variables defined. Add variables to make your template dynamic.
        </Alert>
      )}
    </Box>
  );

  const renderTabContent = () => {
    switch (currentTab) {
      case 0:
        return renderBasicInfo();
      case 1:
        return renderContent();
      case 2:
        return renderVariables();
      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          minHeight: '80vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        pb: 1
      }}>
        <Typography variant="h5" component="h2">
          {mode === 'create' ? 'Create Email Template' : 'Edit Email Template'}
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab icon={<SettingsIcon />} label="Basic Info" iconPosition="start" />
          <Tab icon={<CodeIcon />} label="Content" iconPosition="start" />
          <Tab icon={<DesignIcon />} label="Variables" iconPosition="start" />
        </Tabs>
      </Box>

      <DialogContent sx={{ p: 3 }}>
        {renderTabContent()}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 3, gap: 1 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          variant="outlined"
          startIcon={<PreviewIcon />}
          onClick={() => setIsPreviewMode(true)}
        >
          Preview
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{
            background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
            }
          }}
        >
          {mode === 'create' ? 'Create Template' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedTemplateEditor.propTypes = {
  // Core props
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  template: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string,
    description: PropTypes.string,
    template_type: PropTypes.oneOf(Object.values(TEMPLATE_TYPES)),
    subject: PropTypes.string,
    html_content: PropTypes.string,
    text_content: PropTypes.string,
    preview_text: PropTypes.string,
    variables: PropTypes.arrayOf(PropTypes.string),
    default_values: PropTypes.object,
    category: PropTypes.string,
    tags: PropTypes.arrayOf(PropTypes.string)
  }),
  mode: PropTypes.oneOf(Object.values(EDITOR_MODES)),

  // Enhanced props
  className: PropTypes.string,
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimePreview: PropTypes.bool,
  enableAutoSave: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableVersionControl: PropTypes.bool,
  enableCollaboration: PropTypes.bool,

  // Configuration
  autoSaveInterval: PropTypes.number,
  maxUndoSteps: PropTypes.number,

  // Callback props
  onTemplateChange: PropTypes.func,
  onPreview: PropTypes.func,
  onValidation: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onAutoSave: PropTypes.func,
  onVersionSave: PropTypes.func
};

// Default props
EnhancedTemplateEditor.defaultProps = {
  template: null,
  mode: EDITOR_MODES.CREATE,
  className: '',
  enableAdvancedFeatures: true,
  enableRealTimePreview: true,
  enableAutoSave: true,
  enableAccessibility: true,
  enableAnalytics: true,
  enableVersionControl: true,
  enableCollaboration: false,
  autoSaveInterval: AUTO_SAVE_INTERVALS.NORMAL,
  maxUndoSteps: 50,
  onTemplateChange: null,
  onPreview: null,
  onValidation: null,
  onAnalyticsTrack: null,
  onAutoSave: null,
  onVersionSave: null
};

// Display name for debugging
EnhancedTemplateEditor.displayName = 'EnhancedTemplateEditor';

export default EnhancedTemplateEditor;

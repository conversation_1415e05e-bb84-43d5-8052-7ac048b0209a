/**
 * Debounce utility to prevent rapid function calls
 * Helps prevent the localhost reloading issue by limiting API call frequency
 @since 2024-1-1 to 2025-25-7
*/

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds
 * have elapsed since the last time the debounced function was invoked.
 * 
 * @param {Function} func - The function to debounce
 * @param {number} wait - The number of milliseconds to delay
 * @param {boolean} immediate - If true, trigger the function on the leading edge instead of trailing
 * @returns {Function} The debounced function
 */
export function debounce(func, wait, immediate = false) {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    
    const callNow = immediate && !timeout;
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(this, args);
  };
}

/**
 * Creates a throttled function that only invokes func at most once per every wait milliseconds
 * 
 * @param {Function} func - The function to throttle
 * @param {number} wait - The number of milliseconds to throttle invocations to
 * @returns {Function} The throttled function
 */
export function throttle(func, wait) {
  let inThrottle;
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, wait);
    }
  };
}

/**
 * Creates a function that prevents rapid successive calls
 * Useful for preventing button spam and API call flooding
 * 
 * @param {Function} func - The function to rate limit
 * @param {number} delay - Minimum delay between calls in milliseconds
 * @returns {Function} The rate limited function
 */
export function rateLimit(func, delay = 1000) {
  let lastCall = 0;
  
  return function executedFunction(...args) {
    const now = Date.now();
    
    if (now - lastCall >= delay) {
      lastCall = now;
      return func.apply(this, args);
    }
  };
}

/**
 * Creates a function that can only be called once
 * Useful for preventing duplicate API calls or initialization
 * 
 * @param {Function} func - The function to call once
 * @returns {Function} The once-only function
 */
export function once(func) {
  let called = false;
  let result;
  
  return function executedFunction(...args) {
    if (!called) {
      called = true;
      result = func.apply(this, args);
    }
    return result;
  };
}

/**
 * Utility to prevent memory leaks from intervals and timeouts
 */
export class SafeTimer {
  constructor() {
    this.timers = new Set();
  }
  
  setTimeout(callback, delay) {
    const timer = setTimeout(() => {
      this.timers.delete(timer);
      callback();
    }, delay);
    
    this.timers.add(timer);
    return timer;
  }
  
  setInterval(callback, delay) {
    const timer = setInterval(callback, delay);
    this.timers.add(timer);
    return timer;
  }
  
  clearTimeout(timer) {
    clearTimeout(timer);
    this.timers.delete(timer);
  }
  
  clearInterval(timer) {
    clearInterval(timer);
    this.timers.delete(timer);
  }
  
  clearAll() {
    this.timers.forEach(timer => {
      clearTimeout(timer);
      clearInterval(timer);
    });
    this.timers.clear();
  }
}

export default {
  debounce,
  throttle,
  rateLimit,
  once,
  SafeTimer
};

// @since 2024-1-1 to 2025-25-7
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// Minimal, stable Vite configuration for troubleshooting
export default defineConfig({
  plugins: [
    react()
  ],

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      // Fix lodash ES modules import
      'lodash': 'lodash-es',
      'lodash/get': 'lodash-es/get'
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json']
  },

  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    // Handle CommonJS globals
    global: 'globalThis'
  },

  server: {
    port: 3000,
    host: true,
    strictPort: false, // Allow fallback to other ports

    // Disable HMR to prevent refresh loops
    hmr: false,

    // Minimal file watching to prevent excessive reloads
    watch: {
      usePolling: false,
      interval: 5000, // Very slow polling
      ignored: [
        '**/node_modules/**',
        '**/dist/**',
        '**/.git/**',
        '**/coverage/**',
        '**/*.log',
        '**/temp/**',
        '**/.vite/**',
        '**/logs/**',
        '**/__pycache__/**',
        '**/backend/**',
        '**/.env*',
        '**/package-lock.json',
        '**/yarn.lock',
        '**/*.backup',
        '**/*.old',
        '**/*.bak',
        '**/*.tmp'
      ]
    },

    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        timeout: 10000
      }
    }
  },

  build: {
    outDir: 'dist',
    minify: 'esbuild',
    target: 'es2020'
  },

  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@emotion/react',
      '@emotion/styled',
      'axios',
      '@mui/material',
      '@mui/icons-material',
      '@mui/system',
      'lodash-es',
      'react-is',
      'eventemitter3',
      'recharts'
    ],
    exclude: [
      'chart.js',
      'react-chartjs-2',
      '@nivo/bar',
      '@nivo/line',
      '@nivo/pie'
    ],
    esbuildOptions: {
      loader: {
        '.js': 'jsx',
        '.jsx': 'jsx'
      },
      jsxFactory: 'React.createElement',
      jsxFragment: 'React.Fragment',
      target: 'es2020',
      format: 'esm'
    },
    force: true
  },

  clearScreen: false,
  logLevel: 'info'
})

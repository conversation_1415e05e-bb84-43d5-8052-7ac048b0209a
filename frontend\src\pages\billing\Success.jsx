// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,

  Alert,


  Grid,

} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ReceiptIcon from '@mui/icons-material/Receipt';
import HomeIcon from '@mui/icons-material/Home';
import PaymentIcon from '@mui/icons-material/Payment';
import SettingsIcon from '@mui/icons-material/Settings';
import { Helmet } from 'react-helmet-async';

import api from '../../api/index';
import { useAuth } from '../../hooks/useAuth';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';

const Success = () => {
  const navigate = useNavigate();
  const { refreshUser } = useAuth();
  const { showSuccess } = useAdvancedToast();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [subscriptionData, setSubscriptionData] = useState(null);

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        // Get session ID from URL params
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('session_id');

        if (!sessionId) {
          setError('No payment session found');
          setLoading(false);
          return;
        }

        // Verify the payment with backend
        const response = await api.post('/api/billing/verify-payment', {
          session_id: sessionId
        });

        setSubscriptionData(response.data);
        
        // Refresh user data to get updated subscription
        await refreshUser();
        
        showSuccess('Payment successful! Your subscription is now active.');
        
      } catch (error) {
        console.error('Error verifying payment:', error);
        setError('Failed to verify payment. Please contact support if you were charged.');
      } finally {
        setLoading(false);
      }
    };

    verifyPayment();
  }, [refreshUser, showSuccess]);
  
  // Render loading state
  if (loading) {
    return (
      <>
        <Helmet>
          <title>Processing Payment | B2B Influencer Tool</title>
        </Helmet>
        <Box sx={{ p: 5, textAlign: 'center' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 3 }}>
            Finalizing your subscription...
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Please wait while we set up your account.
          </Typography>
        </Box>
      </>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <>
        <Helmet>
          <title>Payment Error | B2B Influencer Tool</title>
        </Helmet>
        <Box sx={{ p: 5, maxWidth: 800, mx: 'auto' }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          
          <Button
            variant="contained"
            startIcon={<HomeIcon />}
            onClick={() => navigate('/dashboard')}
            sx={{ mr: 2 }}
          >
            Go to Dashboard
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<PaymentIcon />}
            onClick={() => navigate('/billing/plans')}
          >
            View Plans
          </Button>
        </Box>
      </>
    );
  }

  return (
    <>
      <Helmet>
        <title>Payment Successful | B2B Influencer Tool</title>
        <meta name="description" content="Your subscription payment was successful" />
      </Helmet>

      <Box sx={{ maxWidth: 800, mx: 'auto', p: 5, textAlign: 'center' }}>
        <CheckCircleIcon 
          sx={{ 
            fontSize: 80, 
            color: 'success.main', 
            mb: 3,
            filter: 'drop-shadow(0 4px 8px rgba(76, 175, 80, 0.3))'
          }} 
        />
        
        <Typography variant="h3" gutterBottom color="success.main">
          Payment Successful!
        </Typography>
        
        <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
          Welcome to your new subscription plan
        </Typography>

        {subscriptionData && (
          <Paper sx={{ p: 3, mb: 4, textAlign: 'left' }}>
            <Typography variant="h6" gutterBottom>
              Subscription Details
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Plan
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {subscriptionData.plan_name}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Billing Cycle
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {subscriptionData.billing_cycle === 'yearly' ? 'Annual' : 'Monthly'}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Amount Paid
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  ${subscriptionData.amount_paid}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Next Billing Date
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {new Date(subscriptionData.next_billing_date).toLocaleDateString()}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        )}

        <Alert severity="success" sx={{ mb: 4, textAlign: 'left' }}>
          <Typography variant="body2" gutterBottom>
            <strong>What&apos;s Next?</strong>
          </Typography>
          <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
            <li>Your subscription is now active and ready to use</li>
            <li>Access all premium features from your dashboard</li>
            <li>Check your email for a receipt and welcome guide</li>
            <li>Manage your subscription anytime from billing settings</li>
          </ul>
        </Alert>
      
      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          startIcon={<HomeIcon />}
          onClick={() => navigate('/dashboard')}
        >
          Go to Dashboard
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<ReceiptIcon />}
          onClick={() => navigate('/billing/history')}
          sx={{ mr: 2 }}
        >
          View Billing History
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<SettingsIcon />}
          onClick={() => navigate('/settings/integrations')}
        >
          Manage Payment Methods
        </Button>
      </Box>
    </Box>
    </>
  );
};

export default Success;

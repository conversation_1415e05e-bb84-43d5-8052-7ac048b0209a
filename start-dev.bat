REM @since 2024-1-1 to 2025-25-7
@echo off
title ACEO - Development Server
color 0A

echo.
echo ========================================
echo   ACEO Development
echo ========================================
echo.
echo 🚀 Starting both Backend and Frontend...
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: package.json not found!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

REM Check if node_modules exists
if not exist "node_modules" (
    echo ⚠️  Root node_modules not found. Installing dependencies...
    npm install
    echo.
)

REM Check if frontend node_modules exists
if not exist "frontend\node_modules" (
    echo ⚠️  Frontend node_modules not found. Installing dependencies...
    cd frontend
    npm install
    cd ..
    echo.
)

REM Check if Python virtual environment exists
if not exist "venv" (
    echo ⚠️  Python virtual environment not found. Creating...
    python -m venv venv
    echo.
)

REM Check if backend dependencies are installed
if not exist "backend\app" (
    echo ❌ Backend app directory not found!
    echo Please ensure the backend is properly set up.
    pause
    exit /b 1
)

echo 📋 Available development modes:
echo.
echo 1. Ultra-Stable Mode (No HMR, prevents refreshing) - RECOMMENDED
echo 2. Stable Mode (Minimal HMR)
echo 3. Standard Mode
echo 4. Windows Native Mode
echo 5. High Memory Mode (8GB) - For resource issues
echo 6. Simple Mode (No memory optimization)
echo 7. Install Dependencies Only
echo.
set /p choice="Choose mode (1-7): "

if "%choice%"=="1" (
    echo.
    echo 🔄 Starting Ultra-Stable Mode (No HMR)...
    echo Backend: http://localhost:8000
    echo Frontend: http://localhost:3000
    echo API Docs: http://localhost:8000/api/docs
    echo ⚠️  Manual refresh required for frontend changes
    echo.
    npm run dev:ultra-stable
) else if "%choice%"=="2" (
    echo.
    echo 🔄 Starting Stable Mode...
    echo Backend: http://localhost:8000
    echo Frontend: http://localhost:3000
    echo API Docs: http://localhost:8000/api/docs
    echo.
    npm run dev:stable
) else if "%choice%"=="3" (
    echo.
    echo 🔄 Starting Standard Mode...
    echo Backend: http://localhost:8000
    echo Frontend: http://localhost:3000
    echo API Docs: http://localhost:8000/api/docs
    echo.
    npm run dev
) else if "%choice%"=="4" (
    echo.
    echo 🔄 Starting Windows Native Mode...
    echo Backend: http://localhost:8000
    echo Frontend: http://localhost:3000
    echo API Docs: http://localhost:8000/api/docs
    echo.
    npm run dev:windows
) else if "%choice%"=="5" (
    echo.
    echo 🔄 Starting High Memory Mode...
    echo Backend: http://localhost:8000
    echo Frontend: http://localhost:3000
    echo API Docs: http://localhost:8000/api/docs
    echo.
    npm run dev:memory
) else if "%choice%"=="6" (
    echo.
    echo 🔄 Starting Simple Mode...
    echo Backend: http://localhost:8000
    echo Frontend: http://localhost:3000
    echo API Docs: http://localhost:8000/api/docs
    echo.
    npm run dev:simple
) else if "%choice%"=="7" (
    echo.
    echo 📦 Installing all dependencies...
    npm run install:all
    echo.
    echo ✅ Dependencies installed successfully!
    echo Run this script again to start the development servers.
) else (
    echo.
    echo ⚠️  Invalid choice. Starting Ultra-Stable Mode...
    echo Backend: http://localhost:8000
    echo Frontend: http://localhost:3000
    echo API Docs: http://localhost:8000/api/docs
    echo ⚠️  Manual refresh required for frontend changes
    echo.
    npm run dev:ultra-stable
)

echo.
echo 💡 Troubleshooting Tips:
echo - If backend fails: Check Python virtual environment and dependencies
echo - If frontend fails: Try High Memory Mode (option 3)
echo - Press Ctrl+C to stop both servers
echo.
pause

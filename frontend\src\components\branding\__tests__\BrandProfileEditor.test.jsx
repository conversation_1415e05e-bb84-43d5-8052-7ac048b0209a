/**
 * Tests for BrandProfileEditor component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandProfileEditor from '../BrandProfileEditor';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock child components
vi.mock('../ColorPaletteEditor', () => ({
  default: ({ colorPalette, onChange, errors, disabled, readOnly, onError }) => (
    <div data-testid="color-palette-editor">
      Color Palette Editor
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>}
      <button onClick={() => onChange({ primary: '#FF0000', secondary: '#00FF00' })}>
        Change Colors
      </button>
    </div>
  )
}));

vi.mock('../TypographyEditor', () => ({
  default: ({ typography, onChange, errors, disabled, readOnly, onError }) => (
    <div data-testid="typography-editor">
      Typography Editor
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>}
      <button onClick={() => onChange({ primary_font: 'Arial', secondary_font: 'Helvetica' })}>
        Change Typography
      </button>
    </div>
  )
}));

vi.mock('../VisualStyleEditor', () => ({
  default: ({ visualStyle, onChange, disabled, readOnly, onError }) => (
    <div data-testid="visual-style-editor">
      Visual Style Editor
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>}
      <button onClick={() => onChange({ photography_style: 'modern', lighting: 'dramatic' })}>
        Change Visual Style
      </button>
    </div>
  )
}));

vi.mock('../BrandVoiceEditor', () => ({
  default: ({ brandVoice, onChange, disabled, readOnly, onError }) => (
    <div data-testid="brand-voice-editor">
      Brand Voice Editor
      {disabled && <span data-testid="disabled">Disabled</span>}
      {readOnly && <span data-testid="readonly">Read Only</span>}
      <button onClick={() => onChange({ tone: 'casual', personality_traits: ['friendly'] })}>
        Change Brand Voice
      </button>
    </div>
  )
}));

describe('BrandProfileEditor', () => {
  const mockProfile = {
    id: 'profile-123',
    name: 'Test Brand Profile',
    description: 'A test brand profile',
    is_default: false,
    color_palette: {
      primary: '#4E40C5',
      secondary: '#00E4BC',
      accent: '#FF5733'
    },
    typography: {
      primary_font: 'Roboto',
      secondary_font: 'Open Sans'
    },
    visual_style: {
      photography_style: 'lifestyle',
      lighting: 'bright'
    },
    brand_voice: {
      tone: 'professional',
      personality_traits: ['trustworthy']
    }
  };

  const mockProps = {
    onSave: vi.fn(),
    onCancel: vi.fn(),
    onError: vi.fn(),
    onValidationError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders editor for new profile correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Basic Information')).toBeInTheDocument();
    expect(screen.getByLabelText('Brand profile name')).toBeInTheDocument();
    expect(screen.getByLabelText('Brand profile description')).toBeInTheDocument();
    expect(screen.getByLabelText('Set as default profile')).toBeInTheDocument();
    expect(screen.getByText('Create Profile')).toBeInTheDocument();
  });

  test('renders editor for existing profile correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} profile={mockProfile} />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('Test Brand Profile')).toBeInTheDocument();
    expect(screen.getByDisplayValue('A test brand profile')).toBeInTheDocument();
    expect(screen.getByText('Update Profile')).toBeInTheDocument();
  });

  test('displays all tabs correctly', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Colors')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText('Brand Voice')).toBeInTheDocument();
  });

  test('shows correct tab content based on active tab', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} initialTab={0} />
      </TestWrapper>
    );

    expect(screen.getByTestId('color-palette-editor')).toBeInTheDocument();
    expect(screen.queryByTestId('typography-editor')).not.toBeInTheDocument();
  });

  test('switches tab content when tab is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByTestId('color-palette-editor')).toBeInTheDocument();

    const typographyTab = screen.getByText('Typography');
    await user.click(typographyTab);

    expect(screen.getByTestId('typography-editor')).toBeInTheDocument();
    expect(screen.queryByTestId('color-palette-editor')).not.toBeInTheDocument();
  });

  test('handles form field changes correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText('Brand profile name');
    await user.type(nameField, 'New Brand Name');

    expect(nameField).toHaveValue('New Brand Name');
    expect(screen.getByText('You have unsaved changes')).toBeInTheDocument();
  });

  test('handles switch changes correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const defaultSwitch = screen.getByLabelText('Set as default profile');
    await user.click(defaultSwitch);

    expect(defaultSwitch).toBeChecked();
  });

  test('validates required fields on submit', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const submitButton = screen.getByText('Create Profile');
    await user.click(submitButton);

    expect(screen.getByText('Name is required')).toBeInTheDocument();
    expect(mockProps.onSave).not.toHaveBeenCalled();
  });

  test('calls onSave with valid data when form is submitted', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText('Brand profile name');
    await user.type(nameField, 'Valid Brand Name');

    const submitButton = screen.getByText('Create Profile');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockProps.onSave).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Valid Brand Name'
        })
      );
    });
  });

  test('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockProps.onCancel).toHaveBeenCalled();
  });

  test('disables form when disabled prop is true', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} disabled={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Brand profile name')).toBeDisabled();
    expect(screen.getByLabelText('Set as default profile')).toBeDisabled();
    expect(screen.getByText('Create Profile')).toBeDisabled();
  });

  test('makes form read-only when readOnly prop is true', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText('Brand profile name');
    expect(nameField).toHaveAttribute('readonly');
    expect(screen.getByText('Create Profile')).toBeDisabled();
  });

  test('disables submit button when saving', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} saving={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Create Profile')).toBeDisabled();
  });

  test('shows loading state when saving', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} saving={true} />
      </TestWrapper>
    );

    // LoadingButton should show loading state
    expect(screen.getByRole('button', { name: /create brand profile/i })).toBeInTheDocument();
  });

  test('hides advanced tabs when showAdvanced is false', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} showAdvanced={false} />
      </TestWrapper>
    );

    expect(screen.getByText('Colors')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText('Brand Voice')).toBeInTheDocument();
  });

  test('passes disabled prop to child components', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} disabled={true} />
      </TestWrapper>
    );

    expect(screen.getByTestId('disabled')).toBeInTheDocument();
  });

  test('passes readOnly prop to child components', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} readOnly={true} initialTab={1} />
      </TestWrapper>
    );

    const typographyTab = screen.getByText('Typography');
    await user.click(typographyTab);

    expect(screen.getByTestId('readonly')).toBeInTheDocument();
  });

  test('handles child component changes correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const changeColorsButton = screen.getByText('Change Colors');
    await user.click(changeColorsButton);

    expect(screen.getByText('You have unsaved changes')).toBeInTheDocument();
  });

  test('validates name length correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText('Brand profile name');
    await user.type(nameField, 'A');

    const submitButton = screen.getByText('Create Profile');
    await user.click(submitButton);

    expect(screen.getByText('Name must be at least 2 characters long')).toBeInTheDocument();
  });

  test('validates description length correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText('Brand profile name');
    await user.type(nameField, 'Valid Name');

    const descriptionField = screen.getByLabelText('Brand profile description');
    await user.type(descriptionField, 'A'.repeat(501));

    const submitButton = screen.getByText('Create Profile');
    await user.click(submitButton);

    expect(screen.getByText('Description must be less than 500 characters')).toBeInTheDocument();
  });

  test('handles error in form submission gracefully', async () => {
    const user = userEvent.setup();
    const onSaveMock = vi.fn().mockImplementation(() => {
      throw new Error('Save error');
    });
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} onSave={onSaveMock} />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText('Brand profile name');
    await user.type(nameField, 'Valid Name');

    const submitButton = screen.getByText('Create Profile');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <BrandProfileEditor 
          {...mockProps} 
          data-testid="test-profile-editor"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-profile-editor');
    expect(component).toHaveClass('custom-class');
  });

  test('resets form when profile changes', () => {
    const { rerender } = render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} profile={mockProfile} />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('Test Brand Profile')).toBeInTheDocument();

    const newProfile = { ...mockProfile, name: 'Updated Profile' };
    rerender(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} profile={newProfile} />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('Updated Profile')).toBeInTheDocument();
  });

  test('shows success notification after successful save', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandProfileEditor {...mockProps} />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText('Brand profile name');
    await user.type(nameField, 'Valid Name');

    const submitButton = screen.getByText('Create Profile');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Profile created successfully!')).toBeInTheDocument();
    });
  });
});

/**
 * Enhanced Support Panel - Enterprise-grade support panel orchestrator
 * Features: Comprehensive support panel system with integrated tab management and seamless
 * component orchestration, detailed panel coordination with unified state management and
 * cross-component communication, advanced panel features with dynamic tab switching and
 * contextual content loading, ACE Social's support ecosystem integration with coordinated
 * ticket management and chat integration, panel interaction features including keyboard
 * shortcuts and panel customization, panel analytics capabilities with usage tracking
 * and interaction metrics, real-time panel updates with live data synchronization and
 * cross-tab state management, and seamless ACE Social platform integration with advanced
 * support panel orchestration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Tabs,
  Tab,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  useTheme,
  alpha,
  Tooltip,
  Badge,
  Fade,
  IconButton,
  Button,
  Menu,
  MenuItem,
  Divider,
  LinearProgress,
  Stack,
  Snackbar
} from '@mui/material';
import {
  Assignment as TicketIcon,
  Help as HelpIcon,
  Chat as ChatIcon,
  History as HistoryIcon,
  Close as CloseIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Analytics as AnalyticsIcon,
  Speed as SpeedIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useNotification } from '../../hooks/useNotification';

// Enhanced support panel components
import CreateTicketForm from './CreateTicketForm';
import TicketList from './TicketList';
import KnowledgeBaseSearch from './KnowledgeBaseSearch';
import LiveChatPanel from './LiveChatPanel';
import SupportHistory from './SupportHistory';
import QuickActions from './QuickActions';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Panel tabs configuration
const PANEL_TABS = {
  QUICK_HELP: 0,
  MY_TICKETS: 1,
  LIVE_CHAT: 2,
  HISTORY: 3
};

// Panel states
const PANEL_STATES = {
  LOADING: 'loading',
  READY: 'ready',
  ERROR: 'error',
  REFRESHING: 'refreshing'
};

/**
 * Enhanced Support Panel - Comprehensive support panel orchestrator with advanced features
 * Implements integrated component management and enterprise-grade panel coordination capabilities
 */
const SupportPanel = memo(forwardRef(({
  onClose,
  tickets = [],
  loading = false,
  error = null,
  onTicketCreate,
  onTicketUpdate,
  onChatStart,
  onAnalyticsTrack,
  enableRealTimeUpdates = true,
  enableKeyboardShortcuts = true,
  enableAnalytics = true,
  autoRefreshInterval = 30000,
  panelPreferences = {}
}, ref) => {
  const theme = useTheme();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const panelRef = useRef(null);
  const tabRefs = useRef({});
  const retryCountRef = useRef(0);
  const refreshIntervalRef = useRef(null);

  // Enhanced state management
  const [currentTab, setCurrentTab] = useState(panelPreferences.defaultTab || PANEL_TABS.QUICK_HELP);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [panelState, setPanelState] = useState(PANEL_STATES.READY);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [panelAnalytics, setPanelAnalytics] = useState({
    tabSwitches: 0,
    timeSpent: {},
    lastActivity: new Date().toISOString()
  });
  const [componentStates, setComponentStates] = useState({
    tickets: { loading: false, error: null, data: tickets },
    chat: { connected: false, messages: [] },
    knowledgeBase: { searchResults: [], loading: false },
    history: { tickets: [], loading: false }
  });
  const [crossTabState, setCrossTabState] = useState({
    selectedTicket: null,
    chatSession: null,
    searchQuery: '',
    filters: {}
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    switchTab: (tabIndex) => handleTabChange(null, tabIndex),
    refreshPanel: () => handleRefreshPanel(),
    createTicket: () => handleCreateTicket(),
    startChat: () => handleStartChat(),
    getAnalytics: () => panelAnalytics,
    getCurrentTab: () => currentTab,
    getComponentStates: () => componentStates,
    toggleFullscreen: () => setIsFullscreen(!isFullscreen),
    clearNotifications: () => setNotifications([]),
    exportPanelData: () => handleExportPanelData(),
    resetPanel: () => handleResetPanel()
  }), [
    currentTab,
    panelAnalytics,
    componentStates,
    isFullscreen,
    handleTabChange,
    handleRefreshPanel,
    handleCreateTicket,
    handleStartChat,
    handleExportPanelData,
    handleResetPanel
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced tabs configuration
  const tabs = useMemo(() => [
    {
      id: 'quick_help',
      label: 'Quick Help',
      icon: <HelpIcon />,
      component: KnowledgeBaseSearch,
      description: 'Search knowledge base and get instant answers',
      shortcut: 'Alt+1',
      color: ACE_COLORS.PURPLE
    },
    {
      id: 'my_tickets',
      label: 'My Tickets',
      icon: <TicketIcon />,
      component: TicketList,
      badge: componentStates.tickets.data?.filter(t => ['open', 'in_progress'].includes(t.status)).length || 0,
      description: 'View and manage your support tickets',
      shortcut: 'Alt+2',
      color: ACE_COLORS.YELLOW
    },
    {
      id: 'live_chat',
      label: 'Live Chat',
      icon: <ChatIcon />,
      component: LiveChatPanel,
      badge: componentStates.chat.connected ? 1 : 0,
      description: 'Chat with support agents in real-time',
      shortcut: 'Alt+3',
      color: ACE_COLORS.DARK
    },
    {
      id: 'history',
      label: 'History',
      icon: <HistoryIcon />,
      component: SupportHistory,
      description: 'View your complete support history',
      shortcut: 'Alt+4',
      color: ACE_COLORS.PURPLE
    }
  ], [componentStates]);

  // Enhanced event handlers
  const handleTabChange = useCallback((event, newValue) => {
    if (newValue === currentTab) return;

    const previousTab = currentTab;
    setCurrentTab(newValue);
    setShowCreateForm(false);

    // Track analytics
    if (enableAnalytics) {
      setPanelAnalytics(prev => ({
        ...prev,
        tabSwitches: prev.tabSwitches + 1,
        timeSpent: {
          ...prev.timeSpent,
          [previousTab]: (prev.timeSpent[previousTab] || 0) + 1
        },
        lastActivity: new Date().toISOString()
      }));

      if (onAnalyticsTrack) {
        onAnalyticsTrack({
          action: 'tab_switch',
          from: tabs[previousTab]?.id,
          to: tabs[newValue]?.id,
          timestamp: new Date().toISOString()
        });
      }
    }

    announceToScreenReader(`Switched to ${tabs[newValue]?.label} tab`);
  }, [currentTab, enableAnalytics, onAnalyticsTrack, tabs, announceToScreenReader]);

  const handleCreateTicket = useCallback(() => {
    setShowCreateForm(true);
    setCurrentTab(PANEL_TABS.MY_TICKETS);

    if (onTicketCreate) {
      onTicketCreate();
    }

    announceToScreenReader('Create ticket form opened');
  }, [onTicketCreate, announceToScreenReader]);

  const handleStartChat = useCallback(() => {
    setCurrentTab(PANEL_TABS.LIVE_CHAT);

    if (onChatStart) {
      onChatStart();
    }

    announceToScreenReader('Live chat opened');
  }, [onChatStart, announceToScreenReader]);

  const handleTicketCreated = useCallback(() => {
    setShowCreateForm(false);

    // Update component state
    setComponentStates(prev => ({
      ...prev,
      tickets: { ...prev.tickets, loading: true }
    }));

    if (onTicketUpdate) {
      onTicketUpdate();
    }

    showSuccessNotification('Ticket created successfully');
    announceToScreenReader('Ticket created successfully');
  }, [onTicketUpdate, showSuccessNotification, announceToScreenReader]);

  const handleRefreshPanel = useCallback(async () => {
    try {
      setPanelState(PANEL_STATES.REFRESHING);
      retryCountRef.current = 0;

      // Simulate refresh
      await new Promise(resolve => setTimeout(resolve, 1000));

      setPanelState(PANEL_STATES.READY);
      showSuccessNotification('Panel refreshed successfully');
      announceToScreenReader('Support panel refreshed');
    } catch {
      setPanelState(PANEL_STATES.ERROR);
      showErrorNotification('Failed to refresh panel');
      announceToScreenReader('Failed to refresh panel');
    }
  }, [showSuccessNotification, showErrorNotification, announceToScreenReader]);

  const handleExportPanelData = useCallback(() => {
    try {
      const exportData = {
        tickets: componentStates.tickets.data,
        analytics: panelAnalytics,
        preferences: panelPreferences,
        timestamp: new Date().toISOString()
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `support_panel_data_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showSuccessNotification('Panel data exported successfully');
      announceToScreenReader('Panel data exported');
    } catch {
      showErrorNotification('Failed to export panel data');
      announceToScreenReader('Failed to export panel data');
    }
  }, [componentStates, panelAnalytics, panelPreferences, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  const handleResetPanel = useCallback(() => {
    setCurrentTab(PANEL_TABS.QUICK_HELP);
    setShowCreateForm(false);
    setPanelState(PANEL_STATES.READY);
    setIsFullscreen(false);
    setNotifications([]);
    setPanelAnalytics({
      tabSwitches: 0,
      timeSpent: {},
      lastActivity: new Date().toISOString()
    });
    setCrossTabState({
      selectedTicket: null,
      chatSession: null,
      searchQuery: '',
      filters: {}
    });

    announceToScreenReader('Support panel reset');
  }, [announceToScreenReader]);

  // Enhanced useEffect hooks
  useEffect(() => {
    // Update component states when tickets prop changes
    setComponentStates(prev => ({
      ...prev,
      tickets: { ...prev.tickets, data: tickets, loading: false }
    }));
  }, [tickets]);

  useEffect(() => {
    // Set up auto-refresh interval
    if (enableRealTimeUpdates && autoRefreshInterval > 0) {
      refreshIntervalRef.current = setInterval(() => {
        handleRefreshPanel();
      }, autoRefreshInterval);

      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [enableRealTimeUpdates, autoRefreshInterval, handleRefreshPanel]);

  useEffect(() => {
    // Set up keyboard shortcuts
    if (enableKeyboardShortcuts) {
      const handleKeyDown = (event) => {
        if (event.altKey) {
          switch (event.key) {
            case '1':
              event.preventDefault();
              handleTabChange(null, PANEL_TABS.QUICK_HELP);
              break;
            case '2':
              event.preventDefault();
              handleTabChange(null, PANEL_TABS.MY_TICKETS);
              break;
            case '3':
              event.preventDefault();
              handleTabChange(null, PANEL_TABS.LIVE_CHAT);
              break;
            case '4':
              event.preventDefault();
              handleTabChange(null, PANEL_TABS.HISTORY);
              break;
            case 'r':
              event.preventDefault();
              handleRefreshPanel();
              break;
            case 'f':
              event.preventDefault();
              setIsFullscreen(!isFullscreen);
              break;
            default:
              break;
          }
        }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [enableKeyboardShortcuts, handleTabChange, handleRefreshPanel, isFullscreen]);

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
    };
  }, []);

  // Get current tab component
  const CurrentTabComponent = tabs[currentTab]?.component;

  return (
    <Box
      ref={panelRef}
      sx={{
        height: isFullscreen ? '100vh' : '100%',
        width: isFullscreen ? '100vw' : '100%',
        position: isFullscreen ? 'fixed' : 'relative',
        top: isFullscreen ? 0 : 'auto',
        left: isFullscreen ? 0 : 'auto',
        zIndex: isFullscreen ? 9999 : 'auto',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        ...glassMorphismStyles
      }}
    >
      {/* Enhanced Header */}
      <Box sx={{
        p: 2,
        background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${alpha(ACE_COLORS.PURPLE, 0.8)} 100%)`,
        color: ACE_COLORS.WHITE
      }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h5" fontWeight="bold" sx={{ color: ACE_COLORS.WHITE }}>
              ACE Social Support
            </Typography>
            <Typography variant="body2" sx={{ color: alpha(ACE_COLORS.WHITE, 0.8) }}>
              {tabs[currentTab]?.description || 'Get help when you need it'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Panel Settings">
              <IconButton
                size="small"
                onClick={(e) => setMenuAnchorEl(e.currentTarget)}
                sx={{ color: ACE_COLORS.WHITE }}
              >
                <SettingsIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Refresh Panel">
              <IconButton
                size="small"
                onClick={handleRefreshPanel}
                disabled={panelState === PANEL_STATES.REFRESHING}
                sx={{ color: ACE_COLORS.WHITE }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
              <IconButton
                size="small"
                onClick={() => setIsFullscreen(!isFullscreen)}
                sx={{ color: ACE_COLORS.WHITE }}
              >
                {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
              </IconButton>
            </Tooltip>

            {onClose && (
              <Tooltip title="Close Panel">
                <IconButton
                  size="small"
                  onClick={onClose}
                  sx={{ color: ACE_COLORS.WHITE }}
                >
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Loading Progress */}
        {panelState === PANEL_STATES.REFRESHING && (
          <Box sx={{ mt: 1 }}>
            <LinearProgress
              sx={{
                backgroundColor: alpha(ACE_COLORS.WHITE, 0.3),
                '& .MuiLinearProgress-bar': {
                  backgroundColor: ACE_COLORS.YELLOW
                }
              }}
            />
          </Box>
        )}
      </Box>

      {/* Enhanced Error Alert */}
      {(error || panelState === PANEL_STATES.ERROR) && (
        <Fade in>
          <Alert
            severity="error"
            sx={{
              m: 2,
              mb: 1,
              border: `1px solid ${alpha('#F44336', 0.3)}`,
              backgroundColor: alpha('#F44336', 0.1)
            }}
            action={
              <Button
                size="small"
                onClick={handleRefreshPanel}
                sx={{ color: '#F44336' }}
              >
                Retry
              </Button>
            }
          >
            {error || 'Panel encountered an error. Please try refreshing.'}
          </Alert>
        </Fade>
      )}

      {/* Enhanced Quick Actions Bar */}
      <Box sx={{
        p: 2,
        borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        background: alpha(theme.palette.background.paper, 0.8)
      }}>
        <QuickActions
          onCreateTicket={handleCreateTicket}
          onStartChat={handleStartChat}
          onBrowseHelp={() => handleTabChange(null, PANEL_TABS.QUICK_HELP)}
          onContactSupport={() => handleTabChange(null, PANEL_TABS.LIVE_CHAT)}
          enableActionHistory={true}
          enableActionAnalytics={enableAnalytics}
          onActionExecuted={(actionId) => {
            if (enableAnalytics && onAnalyticsTrack) {
              onAnalyticsTrack({
                action: 'quick_action_executed',
                actionId,
                timestamp: new Date().toISOString()
              });
            }
          }}
        />
      </Box>

      {/* Enhanced Navigation Tabs */}
      <Box sx={{
        borderBottom: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        background: `linear-gradient(135deg,
          ${alpha(theme.palette.background.paper, 0.95)} 0%,
          ${alpha(ACE_COLORS.PURPLE, 0.05)} 100%)`
      }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            minHeight: 56,
            '& .MuiTab-root': {
              minHeight: 56,
              textTransform: 'none',
              fontSize: '0.875rem',
              fontWeight: 600,
              color: ACE_COLORS.DARK,
              transition: 'all 300ms ease',
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                transform: 'translateY(-1px)'
              },
              '&.Mui-selected': {
                color: ACE_COLORS.PURPLE,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
              }
            },
            '& .MuiTabs-indicator': {
              backgroundColor: ACE_COLORS.PURPLE,
              height: 3,
              borderRadius: '3px 3px 0 0'
            }
          }}
        >
          {tabs.map((tab, index) => (
            <Tab
              key={tab.id}
              ref={el => tabRefs.current[index] = el}
              label={
                <Tooltip title={`${tab.description} (${tab.shortcut})`} placement="top">
                  <Box display="flex" alignItems="center" gap={1}>
                    <Box sx={{
                      color: currentTab === index ? tab.color : 'inherit',
                      transition: 'color 300ms ease'
                    }}>
                      {tab.icon}
                    </Box>
                    <span>{tab.label}</span>
                    {tab.badge > 0 && (
                      <Badge
                        badgeContent={tab.badge}
                        sx={{
                          '& .MuiBadge-badge': {
                            backgroundColor: tab.color,
                            color: ACE_COLORS.WHITE,
                            fontSize: '0.7rem',
                            height: 18,
                            minWidth: 18
                          }
                        }}
                      />
                    )}
                  </Box>
                </Tooltip>
              }
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      {/* Enhanced Tab Content */}
      <Box sx={{
        flex: 1,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative'
      }}>
        {loading || panelState === PANEL_STATES.LOADING ? (
          <Box
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            height="300px"
            sx={{ ...glassMorphismStyles, m: 2 }}
          >
            <CircularProgress size={60} sx={{ color: ACE_COLORS.PURPLE, mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Loading Support Panel
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Please wait while we prepare your support experience...
            </Typography>
          </Box>
        ) : showCreateForm ? (
          <Fade in timeout={500}>
            <Box sx={{ height: '100%', overflow: 'auto' }}>
              <CreateTicketForm
                onCancel={() => setShowCreateForm(false)}
                onSuccess={handleTicketCreated}
                enableRealTimeValidation={true}
                enableFileAttachments={true}
                enablePrioritySelection={true}
              />
            </Box>
          </Fade>
        ) : CurrentTabComponent ? (
          <Fade in timeout={500} key={currentTab}>
            <Box sx={{ height: '100%', overflow: 'auto' }}>
              <CurrentTabComponent
                tickets={componentStates.tickets.data}
                onCreateTicket={handleCreateTicket}
                onTicketSelect={(ticket) => {
                  setCrossTabState(prev => ({ ...prev, selectedTicket: ticket }));
                  if (enableAnalytics && onAnalyticsTrack) {
                    onAnalyticsTrack({
                      action: 'ticket_selected',
                      ticketId: ticket.id,
                      timestamp: new Date().toISOString()
                    });
                  }
                }}
                onChatStart={handleStartChat}
                selectedTicket={crossTabState.selectedTicket}
                searchQuery={crossTabState.searchQuery}
                filters={crossTabState.filters}
                enableRealTimeUpdates={enableRealTimeUpdates}
                enableAnalytics={enableAnalytics}
                onAnalyticsTrack={onAnalyticsTrack}
              />
            </Box>
          </Fade>
        ) : (
          <Box
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            height="300px"
            sx={{ ...glassMorphismStyles, m: 2 }}
          >
            <ErrorIcon sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Content Not Available
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              The requested content could not be loaded.
            </Typography>
            <Button
              variant="outlined"
              onClick={handleRefreshPanel}
              sx={{
                borderColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  borderColor: ACE_COLORS.PURPLE,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              Retry
            </Button>
          </Box>
        )}
      </Box>

      {/* Enhanced Footer with Analytics */}
      <Box sx={{
        p: 2,
        borderTop: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        background: `linear-gradient(135deg,
          ${alpha(theme.palette.background.default, 0.95)} 0%,
          ${alpha(ACE_COLORS.PURPLE, 0.05)} 100%)`
      }}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
          <Box>
            <Typography variant="body2" fontWeight="bold" sx={{ color: ACE_COLORS.DARK }}>
              ACE Social Support
            </Typography>
            <Typography variant="caption" color="text.secondary">
              All features available • No plan limitations
            </Typography>
          </Box>

          <Stack direction="row" spacing={1} alignItems="center">
            <Chip
              icon={<SpeedIcon />}
              label="2-4h Response"
              size="small"
              sx={{
                backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                color: ACE_COLORS.DARK,
                border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
              }}
            />

            <Chip
              icon={<CheckIcon />}
              label="24/7 Available"
              size="small"
              sx={{
                backgroundColor: alpha('#4CAF50', 0.1),
                color: '#4CAF50',
                border: `1px solid ${alpha('#4CAF50', 0.3)}`
              }}
            />

            {enableAnalytics && (
              <Tooltip title="Panel Analytics">
                <Chip
                  icon={<AnalyticsIcon />}
                  label={`${panelAnalytics.tabSwitches} switches`}
                  size="small"
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    color: ACE_COLORS.PURPLE,
                    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
                  }}
                />
              </Tooltip>
            )}
          </Stack>
        </Stack>
      </Box>

      {/* Settings Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={() => setMenuAnchorEl(null)}
        PaperProps={{
          sx: {
            ...glassMorphismStyles,
            minWidth: 200
          }
        }}
      >
        <MenuItem onClick={() => setIsFullscreen(!isFullscreen)}>
          {isFullscreen ? <FullscreenExitIcon sx={{ mr: 1 }} /> : <FullscreenIcon sx={{ mr: 1 }} />}
          {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen Mode'}
        </MenuItem>

        <MenuItem onClick={handleExportPanelData}>
          <AnalyticsIcon sx={{ mr: 1 }} />
          Export Panel Data
        </MenuItem>

        <Divider />

        <MenuItem onClick={handleResetPanel}>
          <RefreshIcon sx={{ mr: 1 }} />
          Reset Panel
        </MenuItem>
      </Menu>

      {/* Notifications Snackbar */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={index}
          open={true}
          autoHideDuration={6000}
          onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            severity={notification.severity || 'info'}
            onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
            sx={{
              backgroundColor: alpha(theme.palette.background.paper, 0.95),
              backdropFilter: 'blur(10px)'
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
}));

SupportPanel.displayName = 'SupportPanel';

SupportPanel.propTypes = {
  /** Function called when panel is closed */
  onClose: PropTypes.func,
  /** Array of support tickets */
  tickets: PropTypes.array,
  /** Loading state */
  loading: PropTypes.bool,
  /** Error message */
  error: PropTypes.string,
  /** Function called when ticket is created */
  onTicketCreate: PropTypes.func,
  /** Function called when ticket is updated */
  onTicketUpdate: PropTypes.func,
  /** Function called when chat is started */
  onChatStart: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,
  /** Enable keyboard shortcuts */
  enableKeyboardShortcuts: PropTypes.bool,
  /** Enable panel customization */
  enablePanelCustomization: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Maximum retry attempts */
  maxRetries: PropTypes.number,
  /** Auto-refresh interval in milliseconds */
  autoRefreshInterval: PropTypes.number,
  /** Panel preferences object */
  panelPreferences: PropTypes.object
};

export default SupportPanel;

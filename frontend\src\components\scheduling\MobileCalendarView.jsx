/**
 * Enhanced Mobile Calendar View - Enterprise-grade mobile calendar management component
 * Features: Comprehensive mobile calendar management, touch-optimized interactions, real-time scheduling monitoring,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced mobile calendar capabilities and seamless mobile workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import platformService from '../../services/platformService';
import {
  Box,
  Typography,
  IconButton,
  Paper,
  Chip,
  alpha,
  Button,
  SwipeableDrawer,
  Avatar,
  useMediaQuery,
  Alert,
  Snackbar,
  LinearProgress,
  CircularProgress,
  Tooltip,
  Fab
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Schedule as ScheduleIcon,
  ContentCopy as ContentCopyIcon,
  Visibility as VisibilityIcon,
  ArrowForward as ArrowForwardIcon,
  Today as TodayIcon
} from '@mui/icons-material';
import {
  format,
  isSameDay,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  isToday,
  addDays,
  subDays,
  addMonths,
  subMonths
} from 'date-fns';
import TouchableCard from '../common/TouchableCard';

import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Mobile calendar display modes with enhanced configurations
const MOBILE_CALENDAR_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Mobile Calendar',
    description: 'Basic mobile calendar interface',
    features: ['basic_mobile_calendar', 'touch_navigation', 'mobile_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Mobile Calendar',
    description: 'Comprehensive mobile calendar management',
    features: ['detailed_mobile_calendar', 'mobile_analytics', 'real_time_mobile_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Mobile Calendar',
    description: 'AI-powered mobile calendar optimization and suggestions',
    features: ['ai_assisted_mobile', 'ai_mobile_optimization', 'mobile_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Mobile Calendar',
    description: 'Advanced mobile calendar analytics and forecasting',
    features: ['analytics_mobile_calendar', 'mobile_insights']
  }
};

/**
 * Enhanced Mobile Calendar View Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Array} props.scheduledContent - Array of scheduled content items
 * @param {Date} props.selectedDate - Currently selected date
 * @param {Function} props.onDateChange - Date change callback
 * @param {Function} props.onAddContent - Add content callback
 * @param {Function} props.onEditContent - Edit content callback
 * @param {Function} props.onDeleteContent - Delete content callback
 * @param {Function} props.onScheduleContent - Schedule content callback
 * @param {Function} props.onDuplicateContent - Duplicate content callback
 * @param {Function} [props.onViewContent] - View content callback
 * @param {string} [props.viewMode='day'] - Calendar view mode
 * @param {Function} props.onViewModeChange - View mode change callback
 * @param {boolean} [props.loading=false] - Loading state
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onMobileCalendarAction] - Mobile calendar action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-mobile-calendar-view'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const MobileCalendarView = memo(forwardRef(({
  scheduledContent,
  selectedDate,
  onDateChange,
  onAddContent,
  onEditContent,
  onDeleteContent,
  onScheduleContent,
  onDuplicateContent,
  onViewContent,
  viewMode = 'day',
  onViewModeChange,
  loading = false,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onMobileCalendarAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-mobile-calendar-view',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');
  const isTablet = useMediaQuery('(max-width:900px)');

  // Core state management
  const mobileCalendarRef = useRef(null);
  const [touchStartX, setTouchStartX] = useState(null);
  const [touchEndX, setTouchEndX] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [selectedContent, setSelectedContent] = useState(null);

  // Enhanced state management
  const [mobileCalendarMode, setMobileCalendarMode] = useState('compact');
  const [mobileCalendarHistory, setMobileCalendarHistory] = useState([]);
  const [mobileCalendarAnalytics, setMobileCalendarAnalytics] = useState(null);
  const [mobileCalendarInsights, setMobileCalendarInsights] = useState(null);
  const [customMobileCalendarConfigs, setCustomMobileCalendarConfigs] = useState([]);
  const [mobileCalendarPreferences, setMobileCalendarPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    mobileCalendarSuggestions: true,
    dismissTimeout: 10000,
    hapticFeedback: true,
    swipeNavigation: true,
    touchOptimization: true
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced mobile features
  const [mobileCalendarDrawerOpen, setMobileCalendarDrawerOpen] = useState(false);
  const [selectedMobileCalendarType, setSelectedMobileCalendarType] = useState(null);
  const [mobileCalendarStats, setMobileCalendarStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [mobileCalendarScore, setMobileCalendarScore] = useState(0);
  const [mobileCalendarProgress, setMobileCalendarProgress] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [mobileCalendarVersions, setMobileCalendarVersions] = useState([]);
  const [conflictDetection, setConflictDetection] = useState(null);
  const [optimizationSuggestions, setOptimizationSuggestions] = useState([]);
  const [touchGestures] = useState({
    swipeThreshold: 100,
    tapThreshold: 10,
    longPressThreshold: 500
  });
  const [mobileOrientation, setMobileOrientation] = useState('portrait');

  /**
   * Enhanced mobile calendar features - All features enabled without limitations
   */
  const mobileCalendarFeatures = useMemo(() => {
    return {
      maxMobileCalendarMonths: -1, // Unlimited
      maxScheduledEvents: -1, // Unlimited
      maxDailySchedules: -1, // Unlimited
      hasAdvancedMobileCalendar: true,
      hasMobileCalendarAnalytics: true,
      hasCustomMobileCalendarConfigs: true,
      hasMobileCalendarInsights: true,
      hasMobileCalendarHistory: true,
      hasAIAssistance: true,
      hasMobileCalendarExport: true,
      hasMobileCalendarAutomation: true,
      hasAnalytics: true,
      hasExport: true,
      trackingLevel: 'full',
      refreshInterval: 1000,
      planName: 'Enhanced Mobile',
      planTier: 3,
      allowedMobileCalendarTypes: ['basic_mobile_calendar', 'basic_mobile_scheduling', 'advanced_mobile_calendar', 'mobile_calendar_analytics', 'optimal_mobile_scheduling', 'smart_mobile_calendar', 'mobile_calendar_automation', 'custom_mobile_calendar'],
      maxHistoryDays: -1, // Unlimited
      hasConflictDetection: true,
      hasOptimizationSuggestions: true,
      hasMultiMonthView: true,
      hasTouchGestures: true,
      hasHapticFeedback: true,
      hasSwipeNavigation: true,
      hasFeatureAccess: () => true,
      isWithinLimits: () => true,
      canUseFeature: () => true,
      canUseMobileCalendarType: () => true
    };
  }, []);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'application',
      'aria-label': ariaLabel || `Mobile calendar widget with ${mobileCalendarFeatures.planName} features`,
      'aria-description': ariaDescription || `Mobile calendar interface with ${mobileCalendarFeatures.trackingLevel} tracking and touch optimization`,
      tabIndex: -1
    };
  }, [ariaLabel, ariaDescription, mobileCalendarFeatures.planName, mobileCalendarFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive mobile calendar API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getMobileCalendarHistory: () => mobileCalendarHistory,
    getMobileCalendarAnalytics: () => mobileCalendarAnalytics,
    getMobileCalendarInsights: () => mobileCalendarInsights,
    refreshMobileCalendar: () => {
      fetchMobileCalendarAnalytics();
      if (onRefresh) onRefresh();
    },

    // Mobile calendar methods
    focusMobileCalendar: () => {
      if (mobileCalendarRef.current) {
        mobileCalendarRef.current.focus();
      }
    },
    getMobileCalendarScore: () => mobileCalendarScore,
    getMobileCalendarProgress: () => mobileCalendarProgress,
    getConflictDetection: () => conflictDetection,
    getOptimizationSuggestions: () => optimizationSuggestions,
    openMobileCalendarDrawer: () => setMobileCalendarDrawerOpen(true),
    closeMobileCalendarDrawer: () => setMobileCalendarDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportMobileCalendarData: () => {
      if (onExport) {
        onExport(mobileCalendarHistory, mobileCalendarAnalytics);
      }
    },

    // Accessibility methods
    announceMobileCalendar: (message) => announceToScreenReader(message),
    focusMobileCalendarField: () => setFocusToElement('mobile-calendar-widget-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => mobileCalendarMode,
    getMobileCalendarStats: () => mobileCalendarStats,
    getSelectedMobileCalendarType: () => selectedMobileCalendarType,
    getCustomMobileCalendarConfigs: () => customMobileCalendarConfigs,
    getMobileCalendarDrawerOpen: () => mobileCalendarDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    getActiveTab: () => activeTab,
    setActiveTab: (tab) => setActiveTab(tab),
    addCustomMobileCalendarConfig,
    handleMobileCalendarModeChange,
    updateMobileCalendarPreferences,
    handleMobileCalendarTypeSelection,
    validateMobileCalendarConfig,
    getMobileCalendarVersions: () => mobileCalendarVersions,
    switchToVersion: (versionId) => switchToMobileCalendarVersion(versionId),
    detectConflicts: () => handleConflictDetection(),
    optimizeScheduling: () => handleSchedulingOptimization(),
    enableHapticFeedback: () => triggerHapticFeedback(),
    getMobileOrientation: () => mobileOrientation,
    getTouchGestures: () => touchGestures
  }), [
    mobileCalendarHistory,
    mobileCalendarAnalytics,
    mobileCalendarInsights,
    mobileCalendarStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    mobileCalendarMode,
    selectedMobileCalendarType,
    customMobileCalendarConfigs,
    mobileCalendarScore,
    mobileCalendarProgress,
    conflictDetection,
    optimizationSuggestions,
    mobileCalendarVersions,
    addCustomMobileCalendarConfig,
    handleMobileCalendarModeChange,
    updateMobileCalendarPreferences,
    handleMobileCalendarTypeSelection,
    validateMobileCalendarConfig,
    switchToMobileCalendarVersion,
    handleConflictDetection,
    handleSchedulingOptimization,
    activeTab,
    fullscreenMode,
    mobileCalendarDrawerOpen,
    showAnalytics,
    fetchMobileCalendarAnalytics,
    mobileOrientation,
    touchGestures,
    triggerHapticFeedback
  ]);

  // Fetch mobile calendar analytics with enhanced error handling and retry logic
  const fetchMobileCalendarAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/mobile-calendar/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setMobileCalendarAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (mobileCalendarPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Mobile calendar analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch mobile calendar analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load mobile calendar analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching mobile analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, mobileCalendarPreferences.showAnalytics]);

  // Handle mobile calendar mode switching
  const handleMobileCalendarModeChange = useCallback((newMode) => {
    if (MOBILE_CALENDAR_MODES[newMode.toUpperCase()]) {
      setMobileCalendarMode(newMode);
      announceToScreenReader(`Mobile calendar mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mobile_mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setMobileCalendarHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (mobileCalendarPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} mobile calendar mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, mobileCalendarPreferences.showAnalytics, showSuccess]);

  // Handle custom mobile calendar config management
  const addCustomMobileCalendarConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomMobileCalendarConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_mobile_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setMobileCalendarHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (mobileCalendarPreferences.showAnalytics) {
      showSuccess(`Custom mobile calendar config "${configData.name}" created`);
    }
  }, [subscription?.user_id, mobileCalendarPreferences.showAnalytics, showSuccess]);

  // Handle mobile calendar preferences updates
  const updateMobileCalendarPreferences = useCallback((newPreferences) => {
    setMobileCalendarPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'mobile_preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setMobileCalendarHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (mobileCalendarPreferences.showAnalytics) {
      showSuccess('Mobile calendar preferences updated');
    }
  }, [subscription?.user_id, mobileCalendarPreferences.showAnalytics, showSuccess]);

  // Handle mobile calendar type selection
  const handleMobileCalendarTypeSelection = useCallback((calendarType) => {
    setSelectedMobileCalendarType(calendarType);

    // Track calendar type selection
    const typeRecord = {
      id: Date.now(),
      type: 'mobile_calendar_type_selected',
      calendarType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setMobileCalendarHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (mobileCalendarPreferences.showAnalytics) {
      announceToScreenReader(`Selected mobile calendar type: ${calendarType}`);
    }
  }, [subscription?.user_id, mobileCalendarPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateMobileCalendarConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Mobile calendar type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Switch to mobile calendar version
  const switchToMobileCalendarVersion = useCallback((versionId) => {
    const version = mobileCalendarVersions.find(v => v.id === versionId);
    if (version) {
      setMobileCalendarScore(version.score || 0);

      if (mobileCalendarPreferences.showAnalytics) {
        showSuccess(`Switched to mobile version ${version.name}`);
      }
    }
  }, [mobileCalendarVersions, mobileCalendarPreferences.showAnalytics, showSuccess]);

  // Handle conflict detection - always available
  const handleConflictDetection = useCallback(async () => {
    try {
      const response = await api.post('/api/mobile-calendar/detect-conflicts', {
        selectedDate,
        scheduledContent
      });

      setConflictDetection(response.data);

      if (response.data.conflicts?.length > 0) {
        showError(`${response.data.conflicts.length} mobile scheduling conflicts detected`);
      } else {
        showSuccess('No mobile scheduling conflicts detected');
      }
    } catch (error) {
      console.error('Mobile conflict detection failed:', error);
      showError('Failed to detect mobile conflicts');
    }
  }, [selectedDate, scheduledContent, showError, showSuccess]);

  // Handle scheduling optimization - always available
  const handleSchedulingOptimization = useCallback(async () => {
    try {
      const response = await api.post('/api/mobile-calendar/optimize', {
        selectedDate,
        preferences: mobileCalendarPreferences
      });

      setOptimizationSuggestions(response.data.suggestions || []);

      if (response.data.suggestions?.length > 0) {
        showSuccess(`${response.data.suggestions.length} mobile optimization suggestions generated`);
      } else {
        showSuccess('Current mobile scheduling is already optimized');
      }
    } catch (error) {
      console.error('Mobile optimization failed:', error);
      showError('Failed to generate mobile optimization suggestions');
    }
  }, [selectedDate, mobileCalendarPreferences, showError, showSuccess]);

  // Trigger haptic feedback
  const triggerHapticFeedback = useCallback((type = 'light') => {
    if (mobileCalendarPreferences.hapticFeedback && navigator.vibrate) {
      const patterns = {
        light: 30,
        medium: 50,
        heavy: 100,
        success: [50, 50, 50],
        error: [100, 50, 100]
      };

      navigator.vibrate(patterns[type] || patterns.light);
    }
  }, [mobileCalendarPreferences.hapticFeedback]);

  // Get content for the selected date
  const contentForSelectedDate = useMemo(() =>
    scheduledContent.filter(item =>
      isSameDay(new Date(item.scheduled_date), selectedDate)
    ), [scheduledContent, selectedDate]
  );

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && selectedDate) {
      // Optimize mobile calendar based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchMobileCalendarAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, selectedDate, fetchMobileCalendarAnalytics]);

  // Generate AI suggestions when mobile calendar changes
  useEffect(() => {
    if (enableAIInsights && mobileCalendarPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, mobileCalendarPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      setMobileCalendarProgress(25); // Start progress

      const response = await api.get('/api/mobile-calendar/ai-suggestions', {
        params: {
          selectedDate: selectedDate?.toISOString(),
          planTier: mobileCalendarFeatures.planTier,
          isMobile: true,
          orientation: mobileOrientation
        }
      });

      setMobileCalendarProgress(75); // Update progress
      setAiSuggestions(response.data.suggestions || []);
      setMobileCalendarInsights(response.data.insights || null);

      // Create a new version for this suggestion set
      const newVersion = {
        id: Date.now(),
        name: `AI Mobile Calendar v${Date.now()}`,
        suggestions: response.data.suggestions || [],
        createdAt: new Date().toISOString()
      };
      setMobileCalendarVersions(prev => [newVersion, ...prev.slice(0, 4)]); // Keep last 5 versions

      setMobileCalendarProgress(100); // Complete progress

      if (mobileCalendarPreferences.showAnalytics) {
        showSuccess('AI mobile calendar suggestions generated');
      }

      // Reset progress after delay
      setTimeout(() => setMobileCalendarProgress(0), 2000);
    } catch (error) {
      console.error('Failed to generate AI mobile suggestions:', error);
      showError('Failed to generate AI mobile suggestions');
      setMobileCalendarProgress(0);
    }
  }, [selectedDate, mobileCalendarFeatures.planTier, mobileOrientation, mobileCalendarPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when mobile calendar changes
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchMobileCalendarStats();
    }
  }, [enableAdvancedFeatures, fetchMobileCalendarStats]);

  // Fetch mobile calendar stats function
  const fetchMobileCalendarStats = useCallback(async () => {
    try {
      const response = await api.get('/api/mobile-calendar/stats');
      setMobileCalendarStats(response.data);
    } catch (error) {
      console.error('Failed to fetch mobile calendar stats:', error);
    }
  }, []);

  // Monitor orientation changes
  useEffect(() => {
    const handleOrientationChange = () => {
      const orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
      setMobileOrientation(orientation);

      if (mobileCalendarPreferences.showAnalytics) {
        announceToScreenReader(`Mobile orientation changed to ${orientation}`);
      }
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    // Initial check
    handleOrientationChange();

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, [mobileCalendarPreferences.showAnalytics, announceToScreenReader]);

  // Get days for week view
  const weekDays = useMemo(() => eachDayOfInterval({
    start: startOfWeek(selectedDate, { weekStartsOn: 1 }),
    end: endOfWeek(selectedDate, { weekStartsOn: 1 }),
  }), [selectedDate]);

  // Handle swipe navigation with enhanced mobile functionality
  const handleTouchStart = useCallback((e) => {
    setTouchStartX(e.touches[0].clientX);

    // Track touch interaction
    const touchRecord = {
      id: Date.now(),
      type: 'touch_start',
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id,
      coordinates: { x: e.touches[0].clientX, y: e.touches[0].clientY }
    };

    setMobileCalendarHistory(prev => [touchRecord, ...prev.slice(0, 99)]);

    // Call mobile calendar action callback if provided
    if (onMobileCalendarAction) {
      onMobileCalendarAction('touch_start', { coordinates: { x: e.touches[0].clientX, y: e.touches[0].clientY } });
    }

    // Trigger haptic feedback for touch start
    triggerHapticFeedback('light');
  }, [subscription?.user_id, onMobileCalendarAction, triggerHapticFeedback]);

  const handleTouchMove = useCallback((e) => {
    setTouchEndX(e.touches[0].clientX);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (!touchStartX || !touchEndX) return;

    const diff = touchEndX - touchStartX;

    // If swipe is significant, navigate
    if (diff > 100 && mobileCalendarPreferences.swipeNavigation) {
      // Swipe right - go to previous day/week
      handlePrevious();
      triggerHapticFeedback('medium');
      announceToScreenReader('Navigated to previous period');
    } else if (diff < -100 && mobileCalendarPreferences.swipeNavigation) {
      // Swipe left - go to next day/week
      handleNext();
      triggerHapticFeedback('medium');
      announceToScreenReader('Navigated to next period');
    }

    // Track swipe interaction
    const swipeRecord = {
      id: Date.now(),
      type: 'swipe_gesture',
      direction: diff > 100 ? 'right' : diff < -100 ? 'left' : 'none',
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id,
      distance: Math.abs(diff)
    };

    setMobileCalendarHistory(prev => [swipeRecord, ...prev.slice(0, 99)]);

    // Reset touch tracking
    setTouchStartX(null);
    setTouchEndX(null);
  }, [touchStartX, touchEndX, mobileCalendarPreferences.swipeNavigation, triggerHapticFeedback, announceToScreenReader, subscription?.user_id, handlePrevious, handleNext]);

  // Navigation handlers
  const handlePrevious = useCallback(() => {
    if (viewMode === 'day') {
      onDateChange(subDays(selectedDate, 1));
    } else if (viewMode === 'week') {
      onDateChange(subDays(selectedDate, 7));
    } else if (viewMode === 'month') {
      onDateChange(subMonths(selectedDate, 1));
    }
  }, [viewMode, selectedDate, onDateChange]);

  const handleNext = useCallback(() => {
    if (viewMode === 'day') {
      onDateChange(addDays(selectedDate, 1));
    } else if (viewMode === 'week') {
      onDateChange(addDays(selectedDate, 7));
    } else if (viewMode === 'month') {
      onDateChange(addMonths(selectedDate, 1));
    }
  }, [viewMode, selectedDate, onDateChange]);

  const handleToday = useCallback(() => {
    onDateChange(new Date());
    announceToScreenReader('Navigated to today');
  }, [onDateChange, announceToScreenReader]);

  // Content action handlers
  const handleContentClick = (content) => {
    setSelectedContent(content);
    setDetailsOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsOpen(false);
  };

  // Get platform color using centralized service
  const getPlatformColor = useCallback((platform) => {
    if (!platform) return ACE_COLORS.PURPLE;

    try {
      return platformService.getPlatformColor(platform);
    } catch (error) {
      console.warn(`Failed to get platform color for ${platform}:`, error);
      return ACE_COLORS.PURPLE;
    }
  }, []);

  // Get status color
  const getStatusColor = useCallback((status) => {
    switch (status) {
      case 'published':
        return '#4caf50'; // Green
      case 'scheduled':
        return ACE_COLORS.PURPLE;
      case 'draft':
        return ACE_COLORS.YELLOW;
      case 'failed':
        return '#f44336'; // Red
      default:
        return ACE_COLORS.DARK;
    }
  }, []);

  // Render day view
  const renderDayView = () => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle1" gutterBottom>
          {contentForSelectedDate.length > 0
            ? `${contentForSelectedDate.length} posts scheduled`
            : 'No posts scheduled for this day'}
        </Typography>

        {contentForSelectedDate.map((content) => (
          <TouchableCard
            key={content.id}
            title={content.title}
            subtitle={`${format(new Date(content.scheduled_date), 'h:mm a')} • ${content.platform}`}
            content={content.content}
            image={content.image_url}
            onSwipeLeft={() => onDeleteContent(content)}
            onSwipeRight={() => onScheduleContent(content)}
            onClick={() => handleContentClick(content)}
            actions={[
              ...(onViewContent ? [{
                icon: <VisibilityIcon />,
                onClick: () => onViewContent(content),
                color: 'primary',
              }] : []),
              {
                icon: <EditIcon />,
                onClick: () => onEditContent(content),
                color: 'primary',
              },
              {
                icon: <ScheduleIcon />,
                onClick: () => onScheduleContent(content),
                color: 'secondary',
              },
              {
                icon: <ContentCopyIcon />,
                onClick: () => onDuplicateContent(content),
              },
              {
                icon: <DeleteIcon />,
                onClick: () => onDeleteContent(content),
                color: 'error',
              },
            ]}
            variant="glass"
          />
        ))}
      </Box>
    );
  };

  // Render week view
  const renderWeekView = () => {
    return (
      <Box sx={{ mt: 2 }}>
        <Box
          sx={{
            display: 'flex',
            overflowX: 'auto',
            pb: 1,
            '&::-webkit-scrollbar': {
              height: 6,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2),
              borderRadius: 3,
            }
          }}
        >
          {weekDays.map((day) => {
            const isSelected = isSameDay(day, selectedDate);
            const dayContent = scheduledContent.filter(item =>
              isSameDay(new Date(item.scheduled_date), day)
            );

            return (
              <Box
                key={day.toString()}
                onClick={() => onDateChange(day)}
                sx={{
                  minWidth: 60,
                  height: 80,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 1,
                  borderRadius: 2,
                  cursor: 'pointer',
                  bgcolor: isSelected
                    ? alpha(ACE_COLORS.PURPLE, 0.1)
                    : isToday(day)
                    ? alpha(ACE_COLORS.YELLOW, 0.05)
                    : 'transparent',
                  border: isSelected
                    ? `2px solid ${ACE_COLORS.PURPLE}`
                    : isToday(day)
                    ? `1px solid ${ACE_COLORS.YELLOW}`
                    : `1px solid ${alpha(ACE_COLORS.DARK, 0.2)}`,
                  transition: 'all 0.2s ease',
                }}
              >
                <Typography
                  variant="caption"
                  sx={{
                    fontWeight: 'medium',
                    color: isToday(day) ? ACE_COLORS.YELLOW : ACE_COLORS.DARK,
                  }}
                >
                  {format(day, 'EEE')}
                </Typography>

                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: isSelected || isToday(day) ? 'bold' : 'medium',
                    color: isSelected
                      ? ACE_COLORS.PURPLE
                      : isToday(day)
                      ? ACE_COLORS.YELLOW
                      : ACE_COLORS.DARK,
                  }}
                >
                  {format(day, 'd')}
                </Typography>

                {dayContent.length > 0 && (
                  <Box sx={{ display: 'flex', mt: 0.5 }}>
                    {dayContent.slice(0, 3).map((content, index) => (
                      <Box
                        key={index}
                        sx={{
                          width: 6,
                          height: 6,
                          borderRadius: '50%',
                          bgcolor: getPlatformColor(content.platform),
                          mx: 0.25,
                        }}
                      />
                    ))}

                    {dayContent.length > 3 && (
                      <Typography variant="caption" sx={{ fontSize: '0.6rem', ml: 0.25 }}>
                        +{dayContent.length - 3}
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
            );
          })}
        </Box>

        {renderDayView()}
      </Box>
    );
  };

  // Show loading indicator if loading
  if (loading) {
    return (
      <Box
        {...getAccessibilityProps()}
        ref={mobileCalendarRef}
        sx={{
          ...sx,
          ...customization,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: alpha(ACE_COLORS.WHITE, 0.95)
        }}
        className={className}
        style={style}
        data-testid={testId}
      >
        <CircularProgress sx={{ color: ACE_COLORS.PURPLE, mb: 2 }} />
        <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
          Loading mobile calendar...
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      {...getAccessibilityProps()}
      ref={mobileCalendarRef}
      sx={{
        ...sx,
        ...customization,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
        padding: isMobile ? 1 : isTablet ? 2 : 3
      }}
      className={className}
      style={style}
      data-testid={testId}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Calendar header */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          mb: 2,
          bgcolor: alpha(ACE_COLORS.WHITE, 0.8),
          borderRadius: 2,
          border: `1px solid ${alpha(ACE_COLORS.DARK, 0.2)}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <IconButton onClick={handlePrevious} sx={{ width: 48, height: 48 }}>
          <ChevronLeftIcon />
        </IconButton>

        <Box sx={{ textAlign: 'center' }}>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {viewMode === 'day' && format(selectedDate, 'EEEE, MMMM d')}
            {viewMode === 'week' && (
              <>
                {format(weekDays[0], 'MMM d')} - {format(weekDays[6], 'MMM d')}
              </>
            )}
            {viewMode === 'month' && format(selectedDate, 'MMMM yyyy')}

            {isToday(selectedDate) && (
              <Chip
                label="Today"
                size="small"
                color="primary"
                variant="outlined"
                sx={{ ml: 1, height: 20, fontSize: '0.6rem' }}
              />
            )}
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 0.5, gap: 1 }}>
            <Button
              size="small"
              variant="outlined"
              onClick={handleToday}
              startIcon={<TodayIcon />}
              sx={{
                minWidth: 'auto',
                px: 1,
                py: 0.5,
                bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
                borderColor: ACE_COLORS.YELLOW,
                color: ACE_COLORS.DARK,
                '&:hover': {
                  bgcolor: alpha(ACE_COLORS.YELLOW, 0.2)
                }
              }}
            >
              Today
            </Button>

            <Button
              size="small"
              variant={viewMode === 'day' ? 'contained' : 'outlined'}
              onClick={() => onViewModeChange('day')}
              sx={{
                minWidth: 'auto',
                px: 1,
                py: 0.5,
                bgcolor: viewMode === 'day' ? ACE_COLORS.PURPLE : 'transparent',
                borderColor: ACE_COLORS.PURPLE,
                color: viewMode === 'day' ? ACE_COLORS.WHITE : ACE_COLORS.PURPLE
              }}
            >
              Day
            </Button>

            <Button
              size="small"
              variant={viewMode === 'week' ? 'contained' : 'outlined'}
              onClick={() => onViewModeChange('week')}
              sx={{
                minWidth: 'auto',
                px: 1,
                py: 0.5,
                bgcolor: viewMode === 'week' ? ACE_COLORS.PURPLE : 'transparent',
                borderColor: ACE_COLORS.PURPLE,
                color: viewMode === 'week' ? ACE_COLORS.WHITE : ACE_COLORS.PURPLE
              }}
            >
              Week
            </Button>
          </Box>
        </Box>

        <IconButton onClick={handleNext} sx={{ width: 48, height: 48 }}>
          <ChevronRightIcon />
        </IconButton>
      </Paper>

      {/* Calendar content */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {viewMode === 'day' && renderDayView()}
        {viewMode === 'week' && renderWeekView()}
      </Box>

      {/* Floating action buttons */}
      <Box sx={{ position: 'fixed', bottom: 16, right: 16, display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Tooltip title="Create Manual Content" placement="left">
          <Fab
            color="secondary"
            aria-label="add manual"
            onClick={() => onAddContent('manual')}
            sx={{ width: 56, height: 56 }}
          >
            <EditIcon sx={{ fontSize: '1.5rem' }} />
          </Fab>
        </Tooltip>

        <Tooltip title="Create AI Content" placement="left">
          <Fab
            color="primary"
            aria-label="add ai"
            onClick={() => onAddContent('ai')}
            sx={{ width: 56, height: 56 }}
          >
            <AddIcon sx={{ fontSize: '1.5rem' }} />
          </Fab>
        </Tooltip>
      </Box>

      {/* Content details drawer */}
      <SwipeableDrawer
        anchor="bottom"
        open={detailsOpen}
        onClose={handleCloseDetails}
        onOpen={() => {}}
        disableSwipeToOpen
        PaperProps={{
          sx: {
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            maxHeight: '80vh',
          }
        }}
      >
        {selectedContent && (
          <Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                p: 2,
                borderBottom: `1px solid ${alpha(ACE_COLORS.DARK, 0.2)}`,
              }}
            >
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Post Details
              </Typography>

              <IconButton onClick={handleCloseDetails}>
                <ArrowForwardIcon />
              </IconButton>
            </Box>

            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ mr: 1, bgcolor: getPlatformColor(selectedContent.platform) }}>
                  {selectedContent.platform?.charAt(0).toUpperCase() || 'P'}
                </Avatar>

                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                    {selectedContent.title}
                  </Typography>

                  <Typography variant="caption" color="text.secondary">
                    {format(new Date(selectedContent.scheduled_date), 'EEEE, MMMM d, yyyy • h:mm a')}
                  </Typography>
                </Box>
              </Box>

              <Chip
                label={selectedContent.status}
                size="small"
                sx={{
                  bgcolor: alpha(getStatusColor(selectedContent.status), 0.1),
                  color: getStatusColor(selectedContent.status),
                  fontWeight: 'medium',
                  mb: 2,
                }}
              />

              {selectedContent.image_url && (
                <Box
                  sx={{
                    height: 200,
                    mb: 2,
                    borderRadius: 2,
                    overflow: 'hidden',
                    '& img': {
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }
                  }}
                >
                  <img src={selectedContent.image_url} alt={selectedContent.title} />
                </Box>
              )}

              <Typography variant="body1" paragraph>
                {selectedContent.content}
              </Typography>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => {
                    onEditContent(selectedContent);
                    handleCloseDetails();
                  }}
                >
                  Edit
                </Button>

                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<ScheduleIcon />}
                  onClick={() => {
                    onScheduleContent(selectedContent);
                    handleCloseDetails();
                  }}
                >
                  Reschedule
                </Button>

                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={() => {
                    onDeleteContent(selectedContent);
                    handleCloseDetails();
                  }}
                >
                  Delete
                </Button>
              </Box>
            </Box>
          </Box>
        )}
      </SwipeableDrawer>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying mobile calendar sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}

      {/* Mobile Calendar Progress Indicator */}
      {mobileCalendarProgress > 0 && mobileCalendarProgress < 100 && (
        <Box sx={{
          position: 'fixed',
          bottom: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          p: 2,
          borderRadius: 2,
          bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          zIndex: 9999,
          minWidth: 200
        }}>
          <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
            Processing mobile calendar...
          </Typography>
          <LinearProgress
            variant="determinate"
            value={mobileCalendarProgress}
            sx={{
              '& .MuiLinearProgress-bar': {
                backgroundColor: ACE_COLORS.PURPLE
              }
            }}
          />
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
MobileCalendarView.propTypes = {
  // Core props
  scheduledContent: PropTypes.array.isRequired,
  selectedDate: PropTypes.instanceOf(Date).isRequired,
  onDateChange: PropTypes.func.isRequired,
  onAddContent: PropTypes.func.isRequired,
  onEditContent: PropTypes.func.isRequired,
  onDeleteContent: PropTypes.func.isRequired,
  onScheduleContent: PropTypes.func.isRequired,
  onDuplicateContent: PropTypes.func.isRequired,
  onViewContent: PropTypes.func,
  viewMode: PropTypes.oneOf(['day', 'week', 'month']),
  onViewModeChange: PropTypes.func.isRequired,
  loading: PropTypes.bool,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onMobileCalendarAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

MobileCalendarView.displayName = 'MobileCalendarView';

export default MobileCalendarView;
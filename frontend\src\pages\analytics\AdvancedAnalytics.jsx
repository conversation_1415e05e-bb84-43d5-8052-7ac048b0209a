// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  Chip,
  Button,
  LinearProgress,
  useTheme,
  Divider,
  Paper,
} from '@mui/material';
import {
  Bar<PERSON>hart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Mood as MoodIcon,
  EmojiPeople as EmojiPeopleIcon,
  Equalizer as EqualizerIcon,
  Lightbulb as LightbulbIcon,
  ThumbUp as ThumbUpIcon,
  Comment as CommentIcon,
  Share as ShareIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

const AdvancedAnalytics = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [demographicsData, setDemographicsData] = useState(null);
  const [benchmarkData, setBenchmarkData] = useState(null);
  const [sentimentData, setSentimentData] = useState(null);
  const [contentPredictions, setContentPredictions] = useState([]);
  const [error, setError] = useState(null);
  
  // Memoized data fetching function
  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch audience demographics
      const demographicsResponse = await api.get('/api/analytics/audience-demographics');
      setDemographicsData(demographicsResponse.data);

      // Fetch competitive benchmarks
      const benchmarkResponse = await api.get('/api/analytics/competitive-benchmarks');
      setBenchmarkData(benchmarkResponse.data);

      // Fetch recent content for sentiment analysis
      const contentResponse = await api.get('/api/content?status=published&limit=5');
      const contents = contentResponse.data;

      // Fetch sentiment analysis for each content
      const sentimentPromises = contents.map(content =>
        api.get(`/api/analytics/content/${content.id}/sentiment`)
      );

      const sentimentResults = await Promise.all(sentimentPromises);
      setSentimentData(sentimentResults.map((result, index) => ({
        content: contents[index],
        sentiment: result.data,
      })));

      // Fetch content predictions for draft content
      const draftResponse = await api.get('/api/content?status=draft&limit=5');
      const draftContents = draftResponse.data;

      const predictionPromises = draftContents.map(content =>
        api.get(`/api/analytics/content/${content.id}/predict`)
      );

      const predictionResults = await Promise.all(predictionPromises);
      setContentPredictions(predictionResults.map((result, index) => ({
        content: draftContents[index],
        prediction: result.data,
      })));

      showSuccessNotification('Advanced analytics data loaded successfully');
    } catch (error) {
      console.error('Error fetching advanced analytics data:', error);
      setError(error.message || 'Failed to load advanced analytics data');
      showErrorNotification('Failed to load advanced analytics data');
    } finally {
      setLoading(false);
    }
  }, [showSuccessNotification, showErrorNotification]);

  // Load data when component mounts
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Handle refresh
  const handleRefresh = useCallback(() => {
    fetchData();
  }, [fetchData]);
  
  // Memoized chart colors for performance
  const chartColors = useMemo(() => [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.info.main,
  ], [theme]);

  // Render audience demographics
  const renderDemographics = () => {
    if (!demographicsData) return null;
    
    // Prepare age group data for chart
    const ageData = Object.entries(demographicsData.age_groups || {}).map(([key, value], index) => ({
      name: key,
      value,
      color: chartColors[index % chartColors.length],
    }));

    // Prepare gender data for chart
    const genderData = Object.entries(demographicsData.gender || {}).map(([key, value], index) => ({
      name: key,
      value,
      color: chartColors[index % chartColors.length],
    }));

    // Prepare location data for chart
    const locationData = Object.entries(demographicsData.locations || {})
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([key, value], index) => ({
        name: key,
        value,
        color: chartColors[index % chartColors.length],
      }));
    
    return (
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Age Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={ageData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {ageData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value} followers`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Gender Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={genderData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {genderData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value} followers`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Locations
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={locationData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => `${value} followers`} />
                  <Bar dataKey="value" fill={theme.palette.primary.main} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };
  
  // Render competitive benchmarks
  const renderBenchmarks = () => {
    if (!benchmarkData) return null;
    
    const { percentiles, benchmark_data, user_metrics, recommendations } = benchmarkData;
    
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 2 }}>
            Your content is being compared to industry benchmarks for {benchmarkData.industry || 'your industry'}.
          </Alert>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performance Percentiles
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Engagement Rate ({user_metrics.avg_engagement_rate.toFixed(2)}% vs industry {benchmark_data.avg_engagement_rate}%)
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={percentiles.engagement_rate_percentile} 
                  sx={{ height: 10, borderRadius: 5, my: 1 }}
                />
                
                <Typography variant="body2">
                  Views ({user_metrics.avg_views.toFixed(0)} vs industry {benchmark_data.avg_views_per_post})
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={percentiles.views_percentile} 
                  sx={{ height: 10, borderRadius: 5, my: 1 }}
                />
                
                <Typography variant="body2">
                  Likes ({user_metrics.avg_likes.toFixed(0)} vs industry {benchmark_data.avg_likes_per_post})
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={percentiles.likes_percentile} 
                  sx={{ height: 10, borderRadius: 5, my: 1 }}
                />
                
                <Typography variant="body2">
                  Comments ({user_metrics.avg_comments.toFixed(0)} vs industry {benchmark_data.avg_comments_per_post})
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={percentiles.comments_percentile} 
                  sx={{ height: 10, borderRadius: 5, my: 1 }}
                />
                
                <Typography variant="body2">
                  Shares ({user_metrics.avg_shares.toFixed(0)} vs industry {benchmark_data.avg_shares_per_post})
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={percentiles.shares_percentile} 
                  sx={{ height: 10, borderRadius: 5, my: 1 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recommendations
              </Typography>
              <List>
                {recommendations.map((recommendation, index) => (
                  <ListItem key={index} divider={index < recommendations.length - 1}>
                    <ListItemText primary={recommendation} />
                  </ListItem>
                ))}
                {recommendations.length === 0 && (
                  <ListItem>
                    <ListItemText primary="Your content is performing well compared to industry benchmarks!" />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  // Render sentiment analysis
  const renderSentimentAnalysis = () => {
    if (!sentimentData || sentimentData.length === 0) {
      return (
        <Alert severity="info">
          No published content available for sentiment analysis. Create and publish some content to see sentiment insights.
        </Alert>
      );
    }

    // Calculate overall sentiment distribution
    const sentimentCounts = sentimentData.reduce((acc, item) => {
      const sentiment = item.sentiment?.overall_sentiment || 'neutral';
      acc[sentiment] = (acc[sentiment] || 0) + 1;
      return acc;
    }, {});

    const sentimentChartData = Object.entries(sentimentCounts).map(([sentiment, count]) => ({
      name: sentiment.charAt(0).toUpperCase() + sentiment.slice(1),
      value: count,
      color: sentiment === 'positive' ? theme.palette.success.main :
             sentiment === 'negative' ? theme.palette.error.main :
             theme.palette.warning.main
    }));

    return (
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Overall Sentiment Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={sentimentChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {sentimentChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `${value} posts`} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Content Sentiment Details
              </Typography>
              <List>
                {sentimentData.slice(0, 5).map((item, index) => (
                  <ListItem key={index} divider={index < 4}>
                    <ListItemText
                      primary={item.content.title || `Post ${index + 1}`}
                      secondary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                          <Chip
                            label={item.sentiment?.overall_sentiment || 'neutral'}
                            size="small"
                            color={
                              item.sentiment?.overall_sentiment === 'positive' ? 'success' :
                              item.sentiment?.overall_sentiment === 'negative' ? 'error' :
                              'warning'
                            }
                          />
                          <Typography variant="caption">
                            Score: {(item.sentiment?.confidence_score || 0).toFixed(2)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sentiment Trends Over Time
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={sentimentData.map((item, index) => ({
                  name: `Post ${index + 1}`,
                  positive: item.sentiment?.positive_score || 0,
                  negative: item.sentiment?.negative_score || 0,
                  neutral: item.sentiment?.neutral_score || 0,
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="positive" stroke={theme.palette.success.main} name="Positive" />
                  <Line type="monotone" dataKey="negative" stroke={theme.palette.error.main} name="Negative" />
                  <Line type="monotone" dataKey="neutral" stroke={theme.palette.warning.main} name="Neutral" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    );
  };

  // Render content predictions
  const renderContentPredictions = () => {
    if (!contentPredictions || contentPredictions.length === 0) {
      return (
        <Alert severity="info">
          No draft content available for predictions. Create some draft content to see performance predictions.
        </Alert>
      );
    }

    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 2 }}>
            AI-powered predictions for your draft content based on historical performance and industry trends.
          </Alert>
        </Grid>

        {contentPredictions.map((item, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {item.content.title || `Draft Content ${index + 1}`}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Predicted Performance Score
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={(item.prediction?.performance_score || 0) * 100}
                      sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                      color={
                        (item.prediction?.performance_score || 0) > 0.7 ? 'success' :
                        (item.prediction?.performance_score || 0) > 0.4 ? 'warning' : 'error'
                      }
                    />
                    <Typography variant="body2">
                      {((item.prediction?.performance_score || 0) * 100).toFixed(0)}%
                    </Typography>
                  </Box>
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 1, textAlign: 'center' }}>
                      <VisibilityIcon color="primary" />
                      <Typography variant="caption" display="block">
                        Est. Views
                      </Typography>
                      <Typography variant="h6">
                        {item.prediction?.predicted_views || 0}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 1, textAlign: 'center' }}>
                      <ThumbUpIcon color="success" />
                      <Typography variant="caption" display="block">
                        Est. Likes
                      </Typography>
                      <Typography variant="h6">
                        {item.prediction?.predicted_likes || 0}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 1, textAlign: 'center' }}>
                      <CommentIcon color="info" />
                      <Typography variant="caption" display="block">
                        Est. Comments
                      </Typography>
                      <Typography variant="h6">
                        {item.prediction?.predicted_comments || 0}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 1, textAlign: 'center' }}>
                      <ShareIcon color="secondary" />
                      <Typography variant="caption" display="block">
                        Est. Shares
                      </Typography>
                      <Typography variant="h6">
                        {item.prediction?.predicted_shares || 0}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                {item.prediction?.recommendations && item.prediction.recommendations.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      Optimization Suggestions:
                    </Typography>
                    <List dense>
                      {item.prediction.recommendations.slice(0, 3).map((rec, recIndex) => (
                        <ListItem key={recIndex} sx={{ py: 0 }}>
                          <ListItemText
                            primary={rec}
                            primaryTypographyProps={{ variant: 'caption' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                )}

                <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => {
                      // Navigate to content editor
                      window.location.href = `/content/edit/${item.content.id}`;
                    }}
                  >
                    Edit Content
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() => {
                      // Publish content
                      showSuccessNotification('Content publishing feature coming soon!');
                    }}
                  >
                    Publish Now
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4">
          Advanced Analytics
        </Typography>
        <Button
          variant="outlined"
          onClick={handleRefresh}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={16} /> : <TrendingUpIcon />}
        >
          {loading ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </Box>

      <Typography variant="body1" paragraph>
        Gain deeper insights into your social media performance with advanced analytics.
      </Typography>
      
      {loading ? (
        <Box sx={{ width: '100%', mt: 4, mb: 4, textAlign: 'center' }}>
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 2 }}>
            Loading advanced analytics data...
          </Typography>
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
          <Button
            onClick={handleRefresh}
            sx={{ ml: 2 }}
            variant="outlined"
            size="small"
          >
            Retry
          </Button>
        </Alert>
      ) : (
        <>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="advanced analytics tabs"
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab
                icon={<PeopleIcon />}
                label="Audience Demographics"
                aria-label="View audience demographics analytics"
                id="tab-demographics"
                aria-controls="tabpanel-demographics"
              />
              <Tab
                icon={<EqualizerIcon />}
                label="Competitive Benchmarks"
                aria-label="View competitive benchmark analytics"
                id="tab-benchmarks"
                aria-controls="tabpanel-benchmarks"
              />
              <Tab
                icon={<MoodIcon />}
                label="Sentiment Analysis"
                aria-label="View sentiment analysis of content"
                id="tab-sentiment"
                aria-controls="tabpanel-sentiment"
              />
              <Tab
                icon={<LightbulbIcon />}
                label="Content Predictions"
                aria-label="View AI-powered content performance predictions"
                id="tab-predictions"
                aria-controls="tabpanel-predictions"
              />
            </Tabs>
          </Box>
          
          <Box
            role="tabpanel"
            hidden={tabValue !== 0}
            id="tabpanel-demographics"
            aria-labelledby="tab-demographics"
          >
            {tabValue === 0 && renderDemographics()}
          </Box>

          <Box
            role="tabpanel"
            hidden={tabValue !== 1}
            id="tabpanel-benchmarks"
            aria-labelledby="tab-benchmarks"
          >
            {tabValue === 1 && renderBenchmarks()}
          </Box>

          <Box
            role="tabpanel"
            hidden={tabValue !== 2}
            id="tabpanel-sentiment"
            aria-labelledby="tab-sentiment"
          >
            {tabValue === 2 && renderSentimentAnalysis()}
          </Box>

          <Box
            role="tabpanel"
            hidden={tabValue !== 3}
            id="tabpanel-predictions"
            aria-labelledby="tab-predictions"
          >
            {tabValue === 3 && renderContentPredictions()}
          </Box>
        </>
      )}
    </Box>
  );
};

export default AdvancedAnalytics;

/**
 * Enhanced Data Grid Fallback Component - Enterprise-grade data grid error fallback with intelligent recovery
 * Features: Subscription-based feature gating, comprehensive error categorization, intelligent retry mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced data grid error handling capabilities and interactive recovery exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Alert,
  AlertTitle,
  Button,
  Card,
  CardContent,
  Chip,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  alpha,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  GridView as GridIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Memory as MemoryIcon,
  NetworkCheck as NetworkIcon,
  BugReport as BugIcon,
  Security as SecurityIcon,
  Upgrade as UpgradeIcon,
  Analytics as AnalyticsIcon,
  Help as HelpIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  ClearAll as ClearIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
// @ts-expect-error - AuthContext is a JSX file without TypeScript declarations
import { useAuth } from '../../contexts/AuthContext';
// @ts-expect-error - SubscriptionContext is a JSX file without TypeScript declarations
import { useSubscription } from '../../contexts/SubscriptionContext';
// @ts-expect-error - useNotification is a JS file without TypeScript declarations
import { useNotification } from '../../hooks/useNotification';
import { useAccessibility } from '../../hooks/useAccessibility';

// Enhanced component imports
// @ts-expect-error - ErrorBoundary is a JSX file without TypeScript declarations
import ErrorBoundary from '../common/ErrorBoundary';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Error categories for intelligent handling
const ERROR_CATEGORIES = {
  MEMORY: 'memory',
  NETWORK: 'network',
  RENDERING: 'rendering',
  DATA: 'data',
  PERMISSION: 'permission',
  SUBSCRIPTION: 'subscription',
  PERFORMANCE: 'performance',
  UNKNOWN: 'unknown'
};

// Data grid types for specific fallback messages
const GRID_TYPES = {
  TABLE: 'table',
  GRID: 'grid',
  LIST: 'list',
  TREE: 'tree',
  PIVOT: 'pivot',
  VIRTUAL: 'virtual'
};

// Recovery strategies
const RECOVERY_STRATEGIES = {
  RETRY: 'retry',
  REFRESH_DATA: 'refresh_data',
  CLEAR_FILTERS: 'clear_filters',
  RESET_VIEW: 'reset_view',
  EXPORT_FALLBACK: 'export_fallback',
  UPGRADE: 'upgrade',
  CONTACT_SUPPORT: 'contact_support'
};

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  RETRY_DELAY: 1000,
  MAX_RETRIES: 3,
  MEMORY_WARNING: 50 * 1024 * 1024, // 50MB
  MEMORY_CRITICAL: 100 * 1024 * 1024, // 100MB
  NETWORK_TIMEOUT: 10000, // 10 seconds
  MAX_FALLBACK_ROWS: 100,
  LARGE_DATASET_THRESHOLD: 1000
};

// ===========================
// TYPE DEFINITIONS (JSDoc)
// ===========================

/**
 * @typedef {Object} ErrorInfo
 * @property {string} message - Error message
 * @property {string} stack - Error stack trace
 * @property {string} category - Error category
 * @property {number} timestamp - Error timestamp
 * @property {string} [code] - Error code
 * @property {Object} [metadata] - Additional error metadata
 */

/**
 * @typedef {Object} DataGridConfig
 * @property {string} type - Grid type
 * @property {string} title - Grid title
 * @property {Array} columns - Grid columns
 * @property {Array} data - Grid data
 * @property {Object} [options] - Grid options
 * @property {boolean} [isVirtual] - Whether grid uses virtualization
 * @property {boolean} [hasFilters] - Whether grid has filters
 * @property {boolean} [hasSorting] - Whether grid has sorting
 */

/**
 * @typedef {Object} RecoveryAction
 * @property {string} id - Action ID
 * @property {string} label - Action label
 * @property {string} description - Action description
 * @property {string} strategy - Recovery strategy
 * @property {Function} handler - Action handler function
 * @property {boolean} [requiresSubscription] - Whether action requires subscription
 * @property {string} [subscriptionTier] - Required subscription tier
 */

/**
 * @typedef {Object} SubscriptionFeatures
 * @property {string} planId - Subscription plan ID
 * @property {string} planName - Subscription plan name
 * @property {boolean} hasAdvancedGrids - Whether plan includes advanced grids
 * @property {boolean} hasVirtualization - Whether plan includes virtualization
 * @property {boolean} hasErrorAnalytics - Whether plan includes error analytics
 * @property {boolean} hasCustomFallbacks - Whether plan includes custom fallbacks
 * @property {boolean} hasPrioritySupport - Whether plan includes priority support
 * @property {boolean} hasDataExport - Whether plan includes data export
 * @property {number} maxRetries - Maximum retry attempts
 * @property {number} maxRows - Maximum rows supported
 * @property {string} trackingLevel - Error tracking level
 */

/**
 * @typedef {Object} ComponentState
 * @property {boolean} loading - Loading state
 * @property {boolean} retrying - Retry state
 * @property {boolean} showDetails - Show error details
 * @property {boolean} showRecovery - Show recovery options
 * @property {boolean} showUpgradeDialog - Show upgrade dialog
 * @property {boolean} showFallbackTable - Show fallback table
 * @property {number} retryCount - Current retry count
 * @property {number} lastRetryTime - Last retry timestamp
 * @property {Array<string>} recoveryHistory - Recovery action history
 * @property {Object} performanceMetrics - Performance metrics
 * @property {Array} fallbackData - Fallback data for display
 * @property {Array} filteredData - Filtered fallback data
 * @property {string} [lastError] - Last error message
 */

/**
 * @typedef {Object} NotificationState
 * @property {boolean} open - Notification open state
 * @property {string} message - Notification message
 * @property {'success'|'error'|'warning'|'info'} severity - Notification severity
 * @property {Object} [action] - Notification action
 */

/**
 * @typedef {Object} DataGridFallbackProps
 * @property {Function} [onRetry] - Retry callback function
 * @property {ErrorInfo} [error] - Error information
 * @property {Array} [data] - Fallback data array
 * @property {DataGridConfig} [gridConfig] - Grid configuration
 * @property {string} [gridType] - Grid type
 * @property {boolean} [showRecoveryOptions] - Show recovery options
 * @property {boolean} [enableAnalytics] - Enable error analytics
 * @property {Function} [onDataRefresh] - Data refresh callback
 * @property {Function} [onExport] - Export callback
 * @property {Function} [onUpgrade] - Upgrade callback
 * @property {Function} [onContactSupport] - Contact support callback
 * @property {string} [ariaLabel] - ARIA label
 * @property {string} [ariaDescription] - ARIA description
 * @property {string} [testId] - Test ID
 * @property {string} [className] - CSS class name
 * @property {Object} [style] - Inline styles
 */

/**
 * @typedef {Object} DataGridFallbackHandle
 * @property {Function} retry - Retry function
 * @property {Function} reset - Reset function
 * @property {Function} refreshData - Refresh data function
 * @property {Function} clearFilters - Clear filters function
 * @property {Function} resetView - Reset view function
 * @property {Function} exportData - Export data function
 * @property {Function} getErrorInfo - Get error information
 * @property {Function} getPerformanceMetrics - Get performance metrics
 * @property {Function} focus - Focus function
 * @property {Function} announce - Screen reader announcement
 */

/**
 * Enhanced DataGridFallback Component - Enterprise-grade data grid error fallback with intelligent recovery
 * Features: Subscription-based feature gating, comprehensive error categorization, intelligent retry mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced data grid error handling capabilities and interactive recovery exploration
 *
 * @component
 * @param {DataGridFallbackProps} props - Component props
 * @returns {React.Component} Enhanced data grid fallback component
 */
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
const DataGridFallback = memo(forwardRef((props, ref) => {
  const {
    onRetry,
    error,
    data = [],
    gridConfig,
    gridType = GRID_TYPES.GRID,
    showRecoveryOptions = true,
    enableAnalytics = true,
    onDataRefresh,
    onExport,
    onUpgrade,
    onContactSupport,
    ariaLabel,
    ariaDescription,
    testId = 'data-grid-fallback',
    className = '',
    style = {}
  } = props;

  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { hasFeature } = useAuth();
  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  // @ts-expect-error - Intentionally unused for future responsive features

  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced state management with comprehensive error handling
  const [state, setState] = useState(/** @type {ComponentState} */ ({
    loading: subscriptionLoading,
    retrying: false,
    showDetails: false,
    showRecovery: false,
    showUpgradeDialog: false,
    showFallbackTable: data && data.length > 0,
    retryCount: 0,
    lastRetryTime: 0,
    recoveryHistory: [],
    performanceMetrics: {
      errorCount: 0,
      lastErrorTime: null,
      recoveryAttempts: 0,
      successfulRecoveries: 0,
      dataLoadTime: null,
      rowsProcessed: data ? data.length : 0
    },
    fallbackData: data || [],
    filteredData: data || [],
    lastError: null
  }));

  const [notification, setNotification] = useState(/** @type {NotificationState} */ ({
    open: false,
    message: '',
    severity: 'info'
  }));

  // Enhanced refs for comprehensive tracking
  const containerRef = useRef(null);
  const retryButtonRef = useRef(null);
  const tableRef = useRef(null);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   * @returns {SubscriptionFeatures} Subscription features object
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Simple plan-based features
    const features = {
      creator: {
        hasAdvancedGrids: false,
        hasVirtualization: false,
        hasErrorAnalytics: false,
        hasCustomFallbacks: false,
        hasPrioritySupport: false,
        hasDataExport: false,
        maxRetries: 3,
        maxRows: 100,
        trackingLevel: 'basic'
      },
      accelerator: {
        hasAdvancedGrids: true,
        hasVirtualization: false,
        hasErrorAnalytics: true,
        hasCustomFallbacks: false,
        hasPrioritySupport: false,
        hasDataExport: true,
        maxRetries: 5,
        maxRows: 1000,
        trackingLevel: 'advanced'
      },
      dominator: {
        hasAdvancedGrids: true,
        hasVirtualization: true,
        hasErrorAnalytics: true,
        hasCustomFallbacks: true,
        hasPrioritySupport: true,
        hasDataExport: true,
        maxRetries: 10,
        maxRows: -1,
        trackingLevel: 'ai-powered'
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      ...currentFeatures
    };
  }, [subscription?.plan_id, subscription?.plan_name]);

  /**
   * Categorize error based on message and context - Production Ready
   * @param {ErrorInfo} errorInfo - Error information
   * @returns {string} Error category
   */
  const categorizeError = useCallback((errorInfo) => {
    if (!errorInfo || !errorInfo.message) return ERROR_CATEGORIES.UNKNOWN;

    const message = errorInfo.message.toLowerCase();

    if (message.includes('memory') || message.includes('heap') || message.includes('out of memory')) {
      return ERROR_CATEGORIES.MEMORY;
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout') || message.includes('connection')) {
      return ERROR_CATEGORIES.NETWORK;
    }
    if (message.includes('render') || message.includes('canvas') || message.includes('webgl') || message.includes('virtual')) {
      return ERROR_CATEGORIES.RENDERING;
    }
    if (message.includes('data') || message.includes('parse') || message.includes('format') || message.includes('column') || message.includes('row')) {
      return ERROR_CATEGORIES.DATA;
    }
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden') || message.includes('access')) {
      return ERROR_CATEGORIES.PERMISSION;
    }
    if (message.includes('subscription') || message.includes('plan') || message.includes('upgrade') || message.includes('limit')) {
      return ERROR_CATEGORIES.SUBSCRIPTION;
    }
    if (message.includes('performance') || message.includes('slow') || message.includes('lag') || message.includes('freeze')) {
      return ERROR_CATEGORIES.PERFORMANCE;
    }

    return ERROR_CATEGORIES.UNKNOWN;
  }, []);

  /**
   * Get error-specific icon - Production Ready
   * @param {string} category - Error category
   * @returns {React.Component} Error icon component
   */
  const getErrorIcon = useCallback((category) => {
    const iconProps = { sx: { fontSize: 64, mb: 2 } };

    switch (category) {
      case ERROR_CATEGORIES.MEMORY:
        return <MemoryIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.YELLOW }} />;
      case ERROR_CATEGORIES.NETWORK:
        return <NetworkIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.PURPLE }} />;
      case ERROR_CATEGORIES.RENDERING:
        return <BugIcon {...iconProps} sx={{ ...iconProps.sx, color: 'error.main' }} />;
      case ERROR_CATEGORIES.PERMISSION:
        return <SecurityIcon {...iconProps} sx={{ ...iconProps.sx, color: 'warning.main' }} />;
      case ERROR_CATEGORIES.SUBSCRIPTION:
        return <UpgradeIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.PURPLE }} />;
      case ERROR_CATEGORIES.PERFORMANCE:
        return <AnalyticsIcon {...iconProps} sx={{ ...iconProps.sx, color: ACE_COLORS.YELLOW }} />;
      default:
        return <GridIcon {...iconProps} sx={{ ...iconProps.sx, color: 'text.secondary' }} />;
    }
  }, []);

  /**
   * Get category-specific error message - Production Ready
   * @param {string} category - Error category
   * @param {string} gridType - Grid type
   * @returns {Object} Error message object with title and description
   */
  const getErrorMessage = useCallback((category, gridType) => {
    const gridName = gridType ? `${gridType} grid` : 'data grid';

    const messages = {
      [ERROR_CATEGORIES.MEMORY]: {
        title: 'Memory Limit Reached',
        description: `The ${gridName} couldn't be loaded due to insufficient memory. Try reducing the dataset size or closing other browser tabs.`
      },
      [ERROR_CATEGORIES.NETWORK]: {
        title: 'Network Connection Issue',
        description: `Unable to load ${gridName} data due to network connectivity issues. Please check your connection and try again.`
      },
      [ERROR_CATEGORIES.RENDERING]: {
        title: 'Rendering Error',
        description: `The ${gridName} encountered a rendering error. This may be due to browser compatibility or data format issues.`
      },
      [ERROR_CATEGORIES.DATA]: {
        title: 'Data Processing Error',
        description: `Unable to process the data for the ${gridName}. The data format may be invalid or corrupted.`
      },
      [ERROR_CATEGORIES.PERMISSION]: {
        title: 'Access Restricted',
        description: `You don't have permission to view this ${gridName}. Contact your administrator for access.`
      },
      [ERROR_CATEGORIES.SUBSCRIPTION]: {
        title: 'Subscription Required',
        description: `Advanced ${gridName} features require a higher subscription plan. Upgrade to access this functionality.`
      },
      [ERROR_CATEGORIES.PERFORMANCE]: {
        title: 'Performance Issue',
        description: `The ${gridName} is experiencing performance issues. Try reducing filters or using a smaller dataset.`
      },
      [ERROR_CATEGORIES.UNKNOWN]: {
        title: 'Data Grid Unavailable',
        description: `The ${gridName} component couldn't be loaded. Basic table functionality is still available.`
      }
    };

    return messages[category] || messages[ERROR_CATEGORIES.UNKNOWN];
  }, []);

  /**
   * Enhanced retry handler with intelligent backoff - Production Ready
   * @returns {Promise<void>}
   */
  const handleRetry = useCallback(async () => {
    if (state.retryCount >= subscriptionFeatures.maxRetries) {
      const errorMessage = `Maximum retry attempts (${subscriptionFeatures.maxRetries}) reached`;
      setState(prev => ({ ...prev, lastError: errorMessage }));
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
      return;
    }

    try {
      setState(prev => ({
        ...prev,
        retrying: true,
        retryCount: prev.retryCount + 1,
        lastRetryTime: Date.now(),
        performanceMetrics: {
          ...prev.performanceMetrics,
          recoveryAttempts: prev.performanceMetrics.recoveryAttempts + 1
        }
      }));

      announceToScreenReader('Retrying data grid loading...');

      // Intelligent retry delay based on attempt count
      const delay = PERFORMANCE_THRESHOLDS.RETRY_DELAY * Math.pow(2, state.retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));

      if (onRetry) {
        await onRetry();
      } else {
        // Fallback to page reload
        window.location.reload();
      }

      setState(prev => ({
        ...prev,
        performanceMetrics: {
          ...prev.performanceMetrics,
          successfulRecoveries: prev.performanceMetrics.successfulRecoveries + 1
        }
      }));

      showSuccessNotification('Data grid loading retry initiated');
      announceToScreenReader('Data grid loading retry successful');

      // Track analytics
      if (enableAnalytics && window.analytics) {
        window.analytics.track('Data Grid Fallback Retry', {
          retryCount: state.retryCount + 1,
          errorCategory: categorizeError(error),
          gridType,
          dataSize: state.fallbackData.length,
          subscriptionPlan: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }

    } catch (retryError) {
      console.error('Retry failed:', retryError);
      const errorMessage = 'Retry failed. Please try again later.';
      setState(prev => ({ ...prev, lastError: errorMessage }));
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
    } finally {
      setState(prev => ({ ...prev, retrying: false }));
    }
  }, [
    state.retryCount,
    subscriptionFeatures.maxRetries,
    onRetry,
    enableAnalytics,
    categorizeError,
    error,
    gridType,
    state.fallbackData.length,
    subscriptionFeatures.planId,
    showErrorNotification,
    showSuccessNotification,
    announceToScreenReader
  ]);

  /**
   * Handle data refresh - Production Ready
   */
  const handleDataRefresh = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));
      announceToScreenReader('Refreshing data...');

      if (onDataRefresh) {
        const refreshedData = await onDataRefresh();
        setState(prev => ({
          ...prev,
          fallbackData: refreshedData || [],
          filteredData: refreshedData || [],
          showFallbackTable: refreshedData && refreshedData.length > 0,
          performanceMetrics: {
            ...prev.performanceMetrics,
            rowsProcessed: refreshedData ? refreshedData.length : 0,
            dataLoadTime: Date.now()
          }
        }));
        showSuccessNotification('Data refreshed successfully');
        announceToScreenReader('Data refreshed successfully');
      }
    } catch (refreshError) {
      console.error('Data refresh failed:', refreshError);
      showErrorNotification('Failed to refresh data');
      announceToScreenReader('Data refresh failed');
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [onDataRefresh, showErrorNotification, showSuccessNotification, announceToScreenReader]);

  /**
   * Handle clear filters - Production Ready
   */
  const handleClearFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filteredData: prev.fallbackData,
      recoveryHistory: [...prev.recoveryHistory, 'clear_filters']
    }));
    showSuccessNotification('Filters cleared');
    announceToScreenReader('All filters have been cleared');
  }, [showSuccessNotification, announceToScreenReader]);

  /**
   * Handle reset view - Production Ready
   */
  const handleResetView = useCallback(() => {
    setState(prev => ({
      ...prev,
      filteredData: prev.fallbackData,
      showDetails: false,
      showRecovery: false,
      recoveryHistory: [...prev.recoveryHistory, 'reset_view']
    }));
    showSuccessNotification('View reset to default');
    announceToScreenReader('Data grid view has been reset to default');
  }, [showSuccessNotification, announceToScreenReader]);

  /**
   * Handle export fallback data - Production Ready
   */
  const handleExportData = useCallback(() => {
    if (!subscriptionFeatures.hasDataExport) {
      showErrorNotification('Data export requires Accelerator or Dominator plan');
      return;
    }

    try {
      if (onExport) {
        onExport(state.filteredData);
      } else {
        // Fallback CSV export
        const csvContent = state.filteredData.map(row =>
          Object.values(row).map(value =>
            typeof value === 'string' && value.includes(',') ? `"${value}"` : value
          ).join(',')
        ).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'fallback-data.csv';
        a.click();
        window.URL.revokeObjectURL(url);
      }

      showSuccessNotification('Data exported successfully');
      announceToScreenReader('Fallback data has been exported');
    } catch (exportError) {
      console.error('Export failed:', exportError);
      showErrorNotification('Failed to export data');
      announceToScreenReader('Data export failed');
    }
  }, [
    subscriptionFeatures.hasDataExport,
    onExport,
    state.filteredData,
    showErrorNotification,
    showSuccessNotification,
    announceToScreenReader
  ]);

  /**
   * Enhanced upgrade prompt handler - Production Ready
   */
  const handleUpgradePrompt = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: true }));

    // Track upgrade prompt analytics
    if (enableAnalytics && window.analytics) {
      window.analytics.track('Data Grid Fallback Upgrade Prompt Shown', {
        currentPlan: subscriptionFeatures.planId,
        errorCategory: categorizeError(error),
        gridType,
        dataSize: state.fallbackData.length,
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAnalytics, subscriptionFeatures.planId, categorizeError, error, gridType, state.fallbackData.length]);

  /**
   * Get recovery actions based on error category and subscription - Production Ready
   * @param {string} category - Error category
   * @returns {Array<RecoveryAction>} Array of recovery actions
   */
  const getRecoveryActions = useCallback((category) => {
    const baseActions = [
      {
        id: 'retry',
        label: 'Retry Loading',
        description: 'Attempt to load the data grid again',
        strategy: RECOVERY_STRATEGIES.RETRY,
        handler: handleRetry,
        requiresSubscription: false
      },
      {
        id: 'refresh_data',
        label: 'Refresh Data',
        description: 'Reload the data from the source',
        strategy: RECOVERY_STRATEGIES.REFRESH_DATA,
        handler: handleDataRefresh,
        requiresSubscription: false
      }
    ];

    // Add category-specific actions
    if (category === ERROR_CATEGORIES.DATA || category === ERROR_CATEGORIES.PERFORMANCE) {
      baseActions.push({
        id: 'clear_filters',
        label: 'Clear Filters',
        description: 'Remove all applied filters and sorting',
        strategy: RECOVERY_STRATEGIES.CLEAR_FILTERS,
        handler: handleClearFilters,
        requiresSubscription: false
      });

      baseActions.push({
        id: 'reset_view',
        label: 'Reset View',
        description: 'Reset the grid to its default state',
        strategy: RECOVERY_STRATEGIES.RESET_VIEW,
        handler: handleResetView,
        requiresSubscription: false
      });
    }

    if (subscriptionFeatures.hasDataExport && state.fallbackData.length > 0) {
      baseActions.push({
        id: 'export_fallback',
        label: 'Export Data',
        description: 'Export the available data as CSV',
        strategy: RECOVERY_STRATEGIES.EXPORT_FALLBACK,
        handler: handleExportData,
        requiresSubscription: true,
        subscriptionTier: 'accelerator'
      });
    }

    if (category === ERROR_CATEGORIES.SUBSCRIPTION) {
      baseActions.push({
        id: 'upgrade',
        label: 'Upgrade Plan',
        description: 'Upgrade to access advanced data grid features',
        strategy: RECOVERY_STRATEGIES.UPGRADE,
        handler: handleUpgradePrompt,
        requiresSubscription: false
      });
    }

    // Add support action for higher tiers
    if (subscriptionFeatures.hasPrioritySupport) {
      baseActions.push({
        id: 'contact_support',
        label: 'Contact Support',
        description: 'Get priority support for this data grid issue',
        strategy: RECOVERY_STRATEGIES.CONTACT_SUPPORT,
        handler: () => {
          if (onContactSupport) {
            onContactSupport();
          } else {
            window.open('mailto:<EMAIL>?subject=Data Grid Loading Issue', '_blank');
          }
        },
        requiresSubscription: true,
        subscriptionTier: 'dominator'
      });
    }

    return baseActions;
  }, [
    handleRetry,
    handleDataRefresh,
    handleClearFilters,
    handleResetView,
    handleExportData,
    handleUpgradePrompt,
    subscriptionFeatures.hasDataExport,
    subscriptionFeatures.hasPrioritySupport,
    state.fallbackData.length,
    onContactSupport
  ]);

  /**
   * Enhanced imperative handle for parent component access - Production Ready
   */
  useImperativeHandle(ref, () => ({
    retry: handleRetry,
    reset: () => {
      setState(prev => ({
        ...prev,
        retryCount: 0,
        lastRetryTime: 0,
        recoveryHistory: [],
        lastError: null,
        showDetails: false,
        showRecovery: false,
        filteredData: prev.fallbackData
      }));
      announceToScreenReader('Data grid fallback reset');
    },
    refreshData: handleDataRefresh,
    clearFilters: handleClearFilters,
    resetView: handleResetView,
    exportData: handleExportData,
    getErrorInfo: () => ({
      error,
      category: categorizeError(error),
      retryCount: state.retryCount,
      lastRetryTime: state.lastRetryTime,
      dataSize: state.fallbackData.length
    }),
    getPerformanceMetrics: () => state.performanceMetrics,
    focus: () => {
      if (containerRef.current) {
        setFocusToElement(containerRef.current);
      }
    },
    announce: (message) => announceToScreenReader(message)
  }), [
    handleRetry,
    handleDataRefresh,
    handleClearFilters,
    handleResetView,
    handleExportData,
    error,
    categorizeError,
    state.retryCount,
    state.lastRetryTime,
    state.fallbackData.length,
    state.performanceMetrics,
    setFocusToElement,
    announceToScreenReader
  ]);

  // Determine error category and message
  const errorCategory = categorizeError(error);
  const errorMessage = getErrorMessage(errorCategory, gridType);
  const recoveryActions = getRecoveryActions(errorCategory);
  const errorIcon = getErrorIcon(errorCategory);

  // Check if we should show fallback table
  const shouldShowFallbackTable = state.fallbackData && state.fallbackData.length > 0;
  const displayData = state.filteredData.slice(0, subscriptionFeatures.maxRows === -1 ? PERFORMANCE_THRESHOLDS.MAX_FALLBACK_ROWS : Math.min(subscriptionFeatures.maxRows, PERFORMANCE_THRESHOLDS.MAX_FALLBACK_ROWS));
  const columns = shouldShowFallbackTable ? Object.keys(state.fallbackData[0] || {}) : [];

  // Show fallback table if data is available
  if (shouldShowFallbackTable) {
    return (
      <ErrorBoundary>
        <Box
          ref={containerRef}
          role="region"
          aria-label={ariaLabel || `Data grid fallback with ${state.fallbackData.length} rows`}
          aria-description={ariaDescription || 'Simplified table view due to advanced grid unavailability'}
          data-testid={testId}
          className={className}
          style={style}
        >
          {/* Fallback Table Header */}
          <Alert
            severity={errorCategory === ERROR_CATEGORIES.SUBSCRIPTION ? 'warning' : 'info'}
            sx={{ mb: 2 }}
            action={
              showRecoveryOptions && (
                <IconButton
                  size="small"
                  onClick={() => setState(prev => ({ ...prev, showRecovery: !prev.showRecovery }))}
                  sx={{ color: 'inherit' }}
                >
                  {state.showRecovery ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )
            }
          >
            <AlertTitle>
              {errorCategory === ERROR_CATEGORIES.SUBSCRIPTION ? 'Limited Features' : 'Fallback Mode'}
            </AlertTitle>
            <Typography variant="body2">
              {errorCategory === ERROR_CATEGORIES.SUBSCRIPTION
                ? `Advanced data grid features require ${subscriptionFeatures.planId === 'creator' ? 'Accelerator or Dominator' : 'Dominator'} plan. Showing simplified table.`
                : 'Advanced data grid features are unavailable. Showing simplified table.'
              }
            </Typography>
          </Alert>

          {/* Recovery Options */}
          <Collapse in={state.showRecovery}>
            <Card sx={{ mb: 2, backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05), border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ mb: 2, color: ACE_COLORS.DARK, fontWeight: 600 }}>
                  Recovery Actions
                </Typography>

                <List dense>
                  {recoveryActions.map((action) => (
                    <ListItem
                      key={action.id}
                      button
                      onClick={action.handler}
                      disabled={action.requiresSubscription && !subscriptionFeatures.hasDataExport && action.id === 'export_fallback'}
                      sx={{
                        borderRadius: 1,
                        mb: 1,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                        }
                      }}
                    >
                      <ListItemIcon>
                        {action.strategy === RECOVERY_STRATEGIES.RETRY && <RefreshIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.REFRESH_DATA && <RefreshIcon sx={{ color: ACE_COLORS.YELLOW }} />}
                        {action.strategy === RECOVERY_STRATEGIES.CLEAR_FILTERS && <FilterIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.RESET_VIEW && <ClearIcon sx={{ color: ACE_COLORS.YELLOW }} />}
                        {action.strategy === RECOVERY_STRATEGIES.EXPORT_FALLBACK && <ExportIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.UPGRADE && <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.CONTACT_SUPPORT && <HelpIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                      </ListItemIcon>
                      <ListItemText
                        primary={action.label}
                        secondary={action.description}
                        primaryTypographyProps={{
                          variant: 'body2',
                          fontWeight: 500,
                          color: ACE_COLORS.DARK
                        }}
                        secondaryTypographyProps={{
                          variant: 'caption',
                          color: 'text.secondary'
                        }}
                      />
                      {action.requiresSubscription && !subscriptionFeatures.hasDataExport && action.id === 'export_fallback' && (
                        <Chip
                          label="Upgrade Required"
                          size="small"
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2),
                            color: ACE_COLORS.DARK,
                            fontSize: '0.7rem'
                          }}
                        />
                      )}
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Collapse>

          {/* Fallback Table */}
          <TableContainer
            component={Paper}
            ref={tableRef}
            sx={{
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
              maxHeight: 400,
              overflow: 'auto'
            }}
          >
            <Table
              size="small"
              stickyHeader
              aria-label={`Fallback data table with ${displayData.length} rows and ${columns.length} columns`}
            >
              <TableHead>
                <TableRow>
                  {columns.map((column) => (
                    <TableCell
                      key={column}
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        fontWeight: 600,
                        color: ACE_COLORS.DARK
                      }}
                    >
                      {column}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {displayData.map((row, index) => (
                  <TableRow
                    key={index}
                    sx={{
                      '&:nth-of-type(odd)': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.02)
                      },
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05)
                      }
                    }}
                  >
                    {columns.map((column) => (
                      <TableCell key={column}>
                        {row[column] !== null && row[column] !== undefined ? String(row[column]) : '-'}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Table Footer Info */}
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="text.secondary">
              Showing {displayData.length} of {state.filteredData.length} rows
              {subscriptionFeatures.maxRows !== -1 && state.filteredData.length > subscriptionFeatures.maxRows && (
                <Chip
                  label={`${subscriptionFeatures.planName} limit: ${subscriptionFeatures.maxRows} rows`}
                  size="small"
                  sx={{
                    ml: 1,
                    backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2),
                    color: ACE_COLORS.DARK,
                    fontSize: '0.7rem'
                  }}
                />
              )}
            </Typography>

            {subscriptionFeatures.hasDataExport && (
              <Button
                size="small"
                startIcon={<ExportIcon />}
                onClick={handleExportData}
                sx={{
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                  }
                }}
              >
                Export Data
              </Button>
            )}
          </Box>
        </Box>
      </ErrorBoundary>
    );
  }

  // Show error state when no data is available
  return (
    <ErrorBoundary>
      <Card
        ref={containerRef}
        sx={{
          minHeight: 300,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          backgroundColor: theme.palette.background.default,
          position: 'relative',
          overflow: 'hidden'
        }}
        role="alert"
        aria-label={ariaLabel || `Data grid loading error: ${errorMessage.title}`}
        aria-description={ariaDescription || errorMessage.description}
        data-testid={testId}
        className={className}
        style={style}
      >
        {/* Loading overlay */}
        {state.retrying && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1
            }}
          >
            <Box sx={{ textAlign: 'center' }}>
              <CircularProgress sx={{ color: ACE_COLORS.PURPLE, mb: 2 }} />
              <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
                Retrying... (Attempt {state.retryCount + 1}/{subscriptionFeatures.maxRetries})
              </Typography>
            </Box>
          </Box>
        )}

        <CardContent sx={{ textAlign: 'center', maxWidth: 500, width: '100%' }}>
          {/* Error Icon */}
          {errorIcon}

          {/* Error Title */}
          <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
            {errorMessage.title}
          </Typography>

          {/* Subscription Badge */}
          {errorCategory === ERROR_CATEGORIES.SUBSCRIPTION && (
            <Chip
              label={`${subscriptionFeatures.planName} Plan`}
              size="small"
              sx={{
                mb: 2,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                fontWeight: 600
              }}
            />
          )}

          {/* Error Description */}
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
            {errorMessage.description}
          </Typography>

          {/* Error Details */}
          {error && (
            <Alert
              severity={errorCategory === ERROR_CATEGORIES.SUBSCRIPTION ? 'warning' : 'info'}
              sx={{ mb: 3, textAlign: 'left' }}
              action={
                <IconButton
                  size="small"
                  onClick={() => setState(prev => ({ ...prev, showDetails: !prev.showDetails }))}
                  sx={{ color: 'inherit' }}
                >
                  {state.showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              }
            >
              <AlertTitle>Error Details</AlertTitle>
              <Typography variant="caption">
                {error.message || 'Data grid loading failed'}
              </Typography>

              <Collapse in={state.showDetails}>
                <Box sx={{ mt: 1, pt: 1, borderTop: 1, borderColor: 'divider' }}>
                  <Typography variant="caption" component="div">
                    <strong>Category:</strong> {errorCategory}
                  </Typography>
                  <Typography variant="caption" component="div">
                    <strong>Grid Type:</strong> {gridType}
                  </Typography>
                  <Typography variant="caption" component="div">
                    <strong>Retry Count:</strong> {state.retryCount}/{subscriptionFeatures.maxRetries}
                  </Typography>
                  {subscriptionFeatures.hasErrorAnalytics && (
                    <Typography variant="caption" component="div">
                      <strong>Error ID:</strong> {error.code || 'N/A'}
                    </Typography>
                  )}
                </Box>
              </Collapse>
            </Alert>
          )}

          {/* Primary Actions */}
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', mb: 2 }}>
            <Button
              ref={retryButtonRef}
              variant="contained"
              startIcon={state.retrying ? <CircularProgress size={16} /> : <RefreshIcon />}
              onClick={handleRetry}
              disabled={state.retrying || state.retryCount >= subscriptionFeatures.maxRetries}
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.WHITE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                },
                '&:disabled': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.3)
                }
              }}
            >
              {state.retrying ? 'Retrying...' : 'Retry Loading'}
            </Button>

            {showRecoveryOptions && (
              <Button
                variant="outlined"
                startIcon={<HelpIcon />}
                onClick={() => setState(prev => ({ ...prev, showRecovery: !prev.showRecovery }))}
                sx={{
                  borderColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    borderColor: ACE_COLORS.PURPLE,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                  }
                }}
              >
                Recovery Options
              </Button>
            )}
          </Box>

          {/* Recovery Options */}
          <Collapse in={state.showRecovery}>
            <Card sx={{ backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05), border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ mb: 2, color: ACE_COLORS.DARK, fontWeight: 600 }}>
                  Recovery Actions
                </Typography>

                <List dense>
                  {recoveryActions.map((action) => (
                    <ListItem
                      key={action.id}
                      button
                      onClick={action.handler}
                      disabled={action.requiresSubscription && !subscriptionFeatures.hasPrioritySupport && action.id === 'contact_support'}
                      sx={{
                        borderRadius: 1,
                        mb: 1,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                        }
                      }}
                    >
                      <ListItemIcon>
                        {action.strategy === RECOVERY_STRATEGIES.RETRY && <RefreshIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.REFRESH_DATA && <RefreshIcon sx={{ color: ACE_COLORS.YELLOW }} />}
                        {action.strategy === RECOVERY_STRATEGIES.UPGRADE && <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                        {action.strategy === RECOVERY_STRATEGIES.CONTACT_SUPPORT && <HelpIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                      </ListItemIcon>
                      <ListItemText
                        primary={action.label}
                        secondary={action.description}
                        primaryTypographyProps={{
                          variant: 'body2',
                          fontWeight: 500,
                          color: ACE_COLORS.DARK
                        }}
                        secondaryTypographyProps={{
                          variant: 'caption',
                          color: 'text.secondary'
                        }}
                      />
                      {action.requiresSubscription && !subscriptionFeatures.hasPrioritySupport && action.id === 'contact_support' && (
                        <Chip
                          label="Upgrade Required"
                          size="small"
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2),
                            color: ACE_COLORS.DARK,
                            fontSize: '0.7rem'
                          }}
                        />
                      )}
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Collapse>

          {/* Performance Tips */}
          <Box sx={{ mt: 3, p: 2, backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1), borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', fontWeight: 500 }}>
              💡 Performance Tips:
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
              {errorCategory === ERROR_CATEGORIES.MEMORY && 'Close other browser tabs or reduce dataset size'}
              {errorCategory === ERROR_CATEGORIES.NETWORK && 'Check your internet connection and try refreshing data'}
              {errorCategory === ERROR_CATEGORIES.RENDERING && 'Try using a different browser or clear browser cache'}
              {errorCategory === ERROR_CATEGORIES.DATA && 'Verify data format and try clearing filters'}
              {errorCategory === ERROR_CATEGORIES.PERFORMANCE && 'Reduce filters or use a smaller dataset'}
              {errorCategory === ERROR_CATEGORIES.SUBSCRIPTION && 'Upgrade your plan to access advanced data grid features'}
              {errorCategory === ERROR_CATEGORIES.UNKNOWN && 'Refresh the page or contact support if the issue persists'}
            </Typography>
          </Box>
        </CardContent>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
            Upgrade Your Plan
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Unlock advanced data grid features and enhanced error recovery with a higher tier plan:
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Accelerator Plan Features:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ ml: 2 }}>
                <li>Advanced data grids with up to 1,000 rows</li>
                <li>Enhanced error analytics and reporting</li>
                <li>Data export functionality</li>
                <li>Increased retry attempts (5 vs 3)</li>
                <li>Priority data loading</li>
              </Typography>
            </Box>

            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Dominator Plan Features:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ ml: 2 }}>
                <li>All Accelerator features</li>
                <li>Unlimited rows with virtualization</li>
                <li>Custom fallback configurations</li>
                <li>Priority support and assistance</li>
                <li>Maximum retry attempts (10)</li>
                <li>AI-powered error resolution</li>
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
              color="inherit"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setState(prev => ({ ...prev, showUpgradeDialog: false }));
                if (onUpgrade) onUpgrade();
              }}
              variant="contained"
              sx={{ backgroundColor: ACE_COLORS.PURPLE }}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Card>
    </ErrorBoundary>
  );
}));
/* eslint-enable no-unused-vars, @typescript-eslint/no-unused-vars */

// Enhanced PropTypes validation for comprehensive type checking
DataGridFallback.propTypes = {
  /** Retry callback function */
  onRetry: PropTypes.func,

  /** Error information object */
  error: PropTypes.shape({
    message: PropTypes.string,
    stack: PropTypes.string,
    code: PropTypes.string,
    metadata: PropTypes.object
  }),

  /** Fallback data array */
  data: PropTypes.arrayOf(PropTypes.object),

  /** Grid configuration object */
  gridConfig: PropTypes.shape({
    type: PropTypes.string,
    title: PropTypes.string,
    columns: PropTypes.array,
    data: PropTypes.array,
    options: PropTypes.object,
    isVirtual: PropTypes.bool,
    hasFilters: PropTypes.bool,
    hasSorting: PropTypes.bool
  }),

  /** Grid type for specific fallback messages */
  gridType: PropTypes.oneOf([
    'table', 'grid', 'list', 'tree', 'pivot', 'virtual'
  ]),

  /** Whether to show recovery options */
  showRecoveryOptions: PropTypes.bool,

  /** Whether to enable error analytics */
  enableAnalytics: PropTypes.bool,

  /** Data refresh callback function */
  onDataRefresh: PropTypes.func,

  /** Export callback function */
  onExport: PropTypes.func,

  /** Upgrade callback function */
  onUpgrade: PropTypes.func,

  /** Contact support callback function */
  onContactSupport: PropTypes.func,

  /** ARIA label for accessibility */
  ariaLabel: PropTypes.string,

  /** ARIA description for accessibility */
  ariaDescription: PropTypes.string,

  /** Test ID for testing */
  testId: PropTypes.string,

  /** CSS class name */
  className: PropTypes.string,

  /** Inline styles */
  style: PropTypes.object
};

// Default props for comprehensive fallback behavior
DataGridFallback.defaultProps = {
  onRetry: null,
  error: null,
  data: [],
  gridConfig: null,
  gridType: 'grid',
  showRecoveryOptions: true,
  enableAnalytics: true,
  onDataRefresh: null,
  onExport: null,
  onUpgrade: null,
  onContactSupport: null,
  ariaLabel: '',
  ariaDescription: '',
  testId: 'data-grid-fallback',
  className: '',
  style: {}
};

// Enhanced display name for debugging and development tools
DataGridFallback.displayName = 'DataGridFallback';

export default DataGridFallback;

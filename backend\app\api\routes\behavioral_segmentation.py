"""
API routes for behavioral segmentation and customer analysis.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, List
from datetime import datetime, timezone

from app.core.security import get_current_active_user
from app.models.user import User
from app.services.ecommerce_icp_generator import ecommerce_icp_generator
from app.api.dependencies.feature_access import requires_feature
from pydantic import BaseModel

router = APIRouter()

class BehavioralDataRequest(BaseModel):
    """Request schema for behavioral segmentation analysis."""
    store_id: str
    purchase_history: List[Dict[str, Any]] = []
    browsing_patterns: Dict[str, Any] = {}
    engagement_data: Dict[str, Any] = {}

class BehavioralSegmentationResponse(BaseModel):
    """Response schema for behavioral segmentation results."""
    segments: List[Dict[str, Any]]
    behavioral_insights: Dict[str, Any]
    targeting_recommendations: List[Dict[str, Any]]
    purchase_analysis: Dict[str, Any]
    browsing_analysis: Dict[str, Any]
    analysis_timestamp: datetime
    data_quality_score: float

@router.get("", response_model=Dict[str, Any])
async def behavioral_segmentation_root():
    """
    Root endpoint for Behavioral Segmentation API with system information.
    """
    return {
        "message": "Behavioral Segmentation API",
        "version": "1.0.0",
        "features": [
            "customer_segmentation",
            "purchase_pattern_analysis",
            "browsing_behavior_analysis",
            "behavioral_insights",
            "targeting_recommendations",
            "data_quality_assessment"
        ],
        "endpoints": [
            "/analyze",
            "/segments/{store_id}",
            "/insights/{store_id}",
            "/recommendations/{store_id}"
        ],
        "status": "active"
    }

@router.post("/analyze", response_model=BehavioralSegmentationResponse)
async def analyze_behavioral_data(
    request: BehavioralDataRequest,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(requires_feature("behavioral_segmentation"))
):
    """
    Analyze customer behavioral data and generate segments.
    
    Requires behavioral_segmentation feature access.
    """
    try:
        # Get user's subscription tier
        subscription_tier = getattr(current_user, 'subscription_plan', 'creator')
        
        # Prepare behavioral data
        behavioral_data = {
            "purchase_history": request.purchase_history,
            "browsing_patterns": request.browsing_patterns,
            "engagement_data": request.engagement_data
        }
        
        # Generate behavioral segments
        result = await ecommerce_icp_generator.generate_behavioral_segments(
            str(current_user.id),
            request.store_id,
            behavioral_data,
            subscription_tier
        )
        
        return BehavioralSegmentationResponse(**result)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze behavioral data: {str(e)}"
        )

@router.get("/segments/{store_id}", response_model=Dict[str, Any])
async def get_customer_segments(
    store_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get existing customer segments for a store.
    """
    try:
        from app.db.mongodb import get_database
        
        db = await get_database()
        
        # Get latest segmentation analysis
        analysis = await db.behavioral_segmentation.find_one(
            {
                "user_id": str(current_user.id),
                "store_id": store_id
            },
            sort=[("analysis_timestamp", -1)]
        )
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No segmentation analysis found for this store"
            )
        
        return {
            "store_id": store_id,
            "segments": analysis.get("segments", []),
            "analysis_timestamp": analysis.get("analysis_timestamp"),
            "data_quality_score": analysis.get("data_quality_score", 0.0)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get customer segments: {str(e)}"
        )

@router.get("/insights/{store_id}", response_model=Dict[str, Any])
async def get_behavioral_insights(
    store_id: str,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(requires_feature("behavioral_insights"))
):
    """
    Get behavioral insights for a store.
    """
    try:
        from app.db.mongodb import get_database
        
        db = await get_database()
        
        # Get latest segmentation analysis
        analysis = await db.behavioral_segmentation.find_one(
            {
                "user_id": str(current_user.id),
                "store_id": store_id
            },
            sort=[("analysis_timestamp", -1)]
        )
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No behavioral analysis found for this store"
            )
        
        return {
            "store_id": store_id,
            "behavioral_insights": analysis.get("behavioral_insights", {}),
            "purchase_analysis": analysis.get("purchase_analysis", {}),
            "browsing_analysis": analysis.get("browsing_analysis", {}),
            "analysis_timestamp": analysis.get("analysis_timestamp"),
            "data_quality_score": analysis.get("data_quality_score", 0.0)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get behavioral insights: {str(e)}"
        )

@router.get("/recommendations/{store_id}", response_model=Dict[str, Any])
async def get_targeting_recommendations(
    store_id: str,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(requires_feature("targeting_recommendations"))
):
    """
    Get targeting recommendations for a store.
    """
    try:
        from app.db.mongodb import get_database
        
        db = await get_database()
        
        # Get latest segmentation analysis
        analysis = await db.behavioral_segmentation.find_one(
            {
                "user_id": str(current_user.id),
                "store_id": store_id
            },
            sort=[("analysis_timestamp", -1)]
        )
        
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No targeting analysis found for this store"
            )
        
        return {
            "store_id": store_id,
            "targeting_recommendations": analysis.get("targeting_recommendations", []),
            "segments": analysis.get("segments", []),
            "analysis_timestamp": analysis.get("analysis_timestamp"),
            "data_quality_score": analysis.get("data_quality_score", 0.0)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get targeting recommendations: {str(e)}"
        )

@router.delete("/analysis/{store_id}", response_model=Dict[str, Any])
async def delete_behavioral_analysis(
    store_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete behavioral analysis data for a store.
    """
    try:
        from app.db.mongodb import get_database
        
        db = await get_database()
        
        # Delete all analyses for this store and user
        result = await db.behavioral_segmentation.delete_many({
            "user_id": str(current_user.id),
            "store_id": store_id
        })
        
        return {
            "status": "success",
            "message": f"Deleted {result.deleted_count} behavioral analyses",
            "store_id": store_id
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete behavioral analysis: {str(e)}"
        )

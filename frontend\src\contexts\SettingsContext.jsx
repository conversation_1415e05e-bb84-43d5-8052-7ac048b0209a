/**
 * Settings Context
 * Production-ready settings management with persistence, validation, and API integration
 * Comprehensive error handling, caching, and monitoring capabilities
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useState, useEffect, useContext, useCallback, useRef } from 'react';
import { useAuth } from './AuthContext';
import api from '../api';

// Configuration constants
const CONFIG = {
  // Storage settings
  STORAGE_KEY: 'aceo-user-settings',
  STORAGE_VERSION: '1.0',

  // Cache settings
  CACHE_DURATION: 300000, // 5 minutes

  // Debounce settings
  SAVE_DEBOUNCE_DELAY: 1000, // 1 second

  // Validation settings
  MAX_SETTING_VALUE_LENGTH: 1000,

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Default settings structure
const DEFAULT_SETTINGS = {
  theme: {
    mode: 'light',
    primaryColor: '#1976d2',
    fontSize: 'medium',
    compactMode: false
  },
  notifications: {
    emailNotifications: true,
    pushNotifications: true,
    contentReminders: true,
    analyticsReports: true,
    marketingEmails: false,
    weeklyDigest: true
  },
  content: {
    defaultPlatform: 'linkedin',
    defaultTone: 'professional',
    defaultContentType: 'post',
    defaultLength: 'medium',
    includeHashtags: true,
    autoSave: true,
    generateImages: true
  },
  privacy: {
    profileVisibility: 'public',
    showActivity: true,
    allowAnalytics: true,
    shareUsageData: false
  },
  accessibility: {
    highContrast: false,
    reducedMotion: false,
    screenReader: false,
    keyboardNavigation: true
  }
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[SettingsContext] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[SettingsContext] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Settings Context Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[SettingsContext] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Settings Context Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[SettingsContext] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Settings Context Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const SettingsContext = createContext();

// Custom hook to use settings context
// eslint-disable-next-line react-refresh/only-export-components
export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export const SettingsProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();

  // Enhanced state management
  const [settings, setSettings] = useState(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isDirty, setIsDirty] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);

  // Refs for debouncing and caching
  const saveTimeoutRef = useRef(null);
  const cacheRef = useRef({ data: null, timestamp: null });

  // Validation helper
  const validateSetting = useCallback((key, value) => {
    try {
      // Basic validation
      if (value === null || value === undefined) {
        return { isValid: false, error: 'Setting value cannot be null or undefined' };
      }

      // String length validation
      if (typeof value === 'string' && value.length > CONFIG.MAX_SETTING_VALUE_LENGTH) {
        return { isValid: false, error: `Setting value too long (max ${CONFIG.MAX_SETTING_VALUE_LENGTH} characters)` };
      }

      // Specific validations
      switch (key) {
        case 'theme.mode':
          if (!['light', 'dark', 'auto'].includes(value)) {
            return { isValid: false, error: 'Invalid theme mode' };
          }
          break;
        case 'theme.fontSize':
          if (!['small', 'medium', 'large'].includes(value)) {
            return { isValid: false, error: 'Invalid font size' };
          }
          break;
        case 'content.defaultPlatform':
          if (!['linkedin', 'twitter', 'facebook', 'instagram'].includes(value)) {
            return { isValid: false, error: 'Invalid platform' };
          }
          break;
        case 'content.defaultTone':
          if (!['professional', 'casual', 'friendly', 'formal'].includes(value)) {
            return { isValid: false, error: 'Invalid tone' };
          }
          break;
        case 'privacy.profileVisibility':
          if (!['public', 'private', 'friends'].includes(value)) {
            return { isValid: false, error: 'Invalid profile visibility' };
          }
          break;
      }

      return { isValid: true };
    } catch (error) {
      logger.error('Error validating setting', error);
      return { isValid: false, error: 'Validation error' };
    }
  }, []);

  // Load settings from localStorage
  const loadFromStorage = useCallback(() => {
    try {
      const stored = localStorage.getItem(CONFIG.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        if (parsed.version === CONFIG.STORAGE_VERSION) {
          logger.debug('Settings loaded from localStorage');
          return parsed.data;
        } else {
          logger.warn('Settings version mismatch, using defaults');
        }
      }
    } catch (error) {
      logger.error('Failed to load settings from localStorage', error);
    }
    return null;
  }, []);

  // Save settings to localStorage
  const saveToStorage = useCallback((settingsData) => {
    try {
      const toStore = {
        version: CONFIG.STORAGE_VERSION,
        data: settingsData,
        timestamp: new Date().toISOString()
      };
      localStorage.setItem(CONFIG.STORAGE_KEY, JSON.stringify(toStore));
      logger.debug('Settings saved to localStorage');
    } catch (error) {
      logger.error('Failed to save settings to localStorage', error);
    }
  }, []);

  // Load settings from API
  const loadFromAPI = useCallback(async () => {
    if (!isAuthenticated) return null;

    try {
      setLoading(true);
      logger.debug('Loading settings from API');

      const response = await api.get('/api/user/settings');
      const apiSettings = response.data;

      // Update cache
      cacheRef.current = {
        data: apiSettings,
        timestamp: Date.now()
      };

      logger.info('Settings loaded from API successfully');
      return apiSettings;
    } catch (error) {
      logger.error('Failed to load settings from API', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Save settings to API with debouncing
  const saveToAPI = useCallback(async (settingsData) => {
    if (!isAuthenticated) return false;

    try {
      setLoading(true);
      logger.debug('Saving settings to API');

      await api.put('/api/user/settings', settingsData);

      // Update cache
      cacheRef.current = {
        data: settingsData,
        timestamp: Date.now()
      };

      setLastSaved(new Date());
      setIsDirty(false);

      logger.info('Settings saved to API successfully');
      return true;
    } catch (error) {
      logger.error('Failed to save settings to API', error);
      setError('Failed to save settings');
      return false;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Debounced save function
  const debouncedSave = useCallback((settingsData) => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    saveTimeoutRef.current = setTimeout(() => {
      saveToAPI(settingsData);
      saveToStorage(settingsData);
    }, CONFIG.SAVE_DEBOUNCE_DELAY);
  }, [saveToAPI, saveToStorage]);

  // Initialize settings on mount and user change
  useEffect(() => {
    const initializeSettings = async () => {
      try {
        setError(null);
        logger.debug('Initializing settings');

        let loadedSettings = null;

        // Try to load from API first if authenticated
        if (isAuthenticated) {
          try {
            // Check cache first
            const now = Date.now();
            const cached = cacheRef.current;

            if (cached.data && cached.timestamp &&
                (now - cached.timestamp) < CONFIG.CACHE_DURATION) {
              logger.debug('Using cached settings');
              loadedSettings = cached.data;
            } else {
              loadedSettings = await loadFromAPI();
            }
          } catch (error) {
            logger.warn('Failed to load from API, falling back to localStorage', error);
          }
        }

        // Fallback to localStorage
        if (!loadedSettings) {
          loadedSettings = loadFromStorage();
        }

        // Merge with defaults and user profile settings
        const mergedSettings = {
          ...DEFAULT_SETTINGS,
          ...loadedSettings,
          ...(user?.settings || {})
        };

        setSettings(mergedSettings);
        setLastSaved(loadedSettings ? new Date() : null);

        logger.info('Settings initialized successfully', {
          source: loadedSettings ? (isAuthenticated ? 'api' : 'localStorage') : 'defaults',
          hasUserSettings: !!user?.settings
        });
      } catch (error) {
        logger.error('Failed to initialize settings', error);
        setError('Failed to load settings');
        setSettings(DEFAULT_SETTINGS);
      }
    };

    initializeSettings();
  }, [user, isAuthenticated, loadFromAPI, loadFromStorage]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  // Generic setting update function
  const updateSetting = useCallback((path, value) => {
    try {
      // Validate the setting
      const validation = validateSetting(path, value);
      if (!validation.isValid) {
        logger.warn('Setting validation failed', { path, value, error: validation.error });
        setError(validation.error);
        return false;
      }

      logger.debug('Updating setting', { path, value });

      setSettings(prevSettings => {
        const newSettings = { ...prevSettings };

        // Handle nested path (e.g., 'theme.mode')
        const pathParts = path.split('.');
        let current = newSettings;

        for (let i = 0; i < pathParts.length - 1; i++) {
          if (!current[pathParts[i]]) {
            current[pathParts[i]] = {};
          }
          current = current[pathParts[i]];
        }

        current[pathParts[pathParts.length - 1]] = value;

        // Mark as dirty and trigger save
        setIsDirty(true);
        setError(null);
        debouncedSave(newSettings);

        return newSettings;
      });

      return true;
    } catch (error) {
      logger.error('Failed to update setting', error);
      setError('Failed to update setting');
      return false;
    }
  }, [validateSetting, debouncedSave]);

  // Convenience functions for common operations
  const toggleThemeMode = useCallback(() => {
    const currentMode = settings.theme?.mode || 'light';
    const newMode = currentMode === 'light' ? 'dark' : 'light';
    return updateSetting('theme.mode', newMode);
  }, [settings.theme?.mode, updateSetting]);

  const updateNotificationSettings = useCallback((newSettings) => {
    const updatedSettings = {
      ...settings.notifications,
      ...newSettings
    };
    return updateSetting('notifications', updatedSettings);
  }, [settings.notifications, updateSetting]);

  const updateContentSettings = useCallback((newSettings) => {
    const updatedSettings = {
      ...settings.content,
      ...newSettings
    };
    return updateSetting('content', updatedSettings);
  }, [settings.content, updateSetting]);

  const updateThemeSettings = useCallback((newSettings) => {
    const updatedSettings = {
      ...settings.theme,
      ...newSettings
    };
    return updateSetting('theme', updatedSettings);
  }, [settings.theme, updateSetting]);

  const updatePrivacySettings = useCallback((newSettings) => {
    const updatedSettings = {
      ...settings.privacy,
      ...newSettings
    };
    return updateSetting('privacy', updatedSettings);
  }, [settings.privacy, updateSetting]);

  const updateAccessibilitySettings = useCallback((newSettings) => {
    const updatedSettings = {
      ...settings.accessibility,
      ...newSettings
    };
    return updateSetting('accessibility', updatedSettings);
  }, [settings.accessibility, updateSetting]);

  // Utility functions
  const resetSettings = useCallback(async () => {
    try {
      logger.debug('Resetting settings to defaults');
      setSettings(DEFAULT_SETTINGS);
      setIsDirty(true);

      const success = await saveToAPI(DEFAULT_SETTINGS);
      if (success) {
        saveToStorage(DEFAULT_SETTINGS);
        logger.info('Settings reset successfully');
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Failed to reset settings', error);
      setError('Failed to reset settings');
      return false;
    }
  }, [saveToAPI, saveToStorage]);

  const exportSettings = useCallback(() => {
    try {
      const exportData = {
        settings,
        timestamp: new Date().toISOString(),
        version: CONFIG.STORAGE_VERSION
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `aceo-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      logger.info('Settings exported successfully');
      return true;
    } catch (error) {
      logger.error('Failed to export settings', error);
      setError('Failed to export settings');
      return false;
    }
  }, [settings]);

  const importSettings = useCallback(async (file) => {
    try {
      const text = await file.text();
      const importData = JSON.parse(text);

      if (importData.version !== CONFIG.STORAGE_VERSION) {
        throw new Error('Incompatible settings version');
      }

      // Validate imported settings
      const mergedSettings = { ...DEFAULT_SETTINGS, ...importData.settings };

      setSettings(mergedSettings);
      setIsDirty(true);

      const success = await saveToAPI(mergedSettings);
      if (success) {
        saveToStorage(mergedSettings);
        logger.info('Settings imported successfully');
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Failed to import settings', error);
      setError('Failed to import settings');
      return false;
    }
  }, [saveToAPI, saveToStorage]);

  const forceSave = useCallback(async () => {
    try {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
        saveTimeoutRef.current = null;
      }

      const success = await saveToAPI(settings);
      if (success) {
        saveToStorage(settings);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Failed to force save settings', error);
      return false;
    }
  }, [settings, saveToAPI, saveToStorage]);

  // Enhanced context value with organized structure
  const contextValue = {
    // State data
    settings,
    loading,
    error,
    isDirty,
    lastSaved,

    // Legacy support (for backward compatibility)
    themeMode: settings.theme?.mode || 'light',
    notificationSettings: settings.notifications || DEFAULT_SETTINGS.notifications,
    contentSettings: settings.content || DEFAULT_SETTINGS.content,

    // Core functions
    updateSetting,

    // Category-specific update functions
    toggleThemeMode,
    updateNotificationSettings,
    updateContentSettings,
    updateThemeSettings,
    updatePrivacySettings,
    updateAccessibilitySettings,

    // Utility functions
    resetSettings,
    exportSettings,
    importSettings,
    forceSave,
    clearError: () => setError(null),

    // Helper functions
    getSetting: (path) => {
      const pathParts = path.split('.');
      let current = settings;
      for (const part of pathParts) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part];
        } else {
          return undefined;
        }
      }
      return current;
    },

    hasSetting: (path) => {
      const pathParts = path.split('.');
      let current = settings;
      for (const part of pathParts) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part];
        } else {
          return false;
        }
      }
      return true;
    },

    // Validation helpers
    validateSetting,
    isValidTheme: (mode) => ['light', 'dark', 'auto'].includes(mode),
    isValidPlatform: (platform) => ['linkedin', 'twitter', 'facebook', 'instagram'].includes(platform),

    // Status helpers
    hasUnsavedChanges: isDirty,
    isLoading: loading,
    hasError: !!error,
    getLastSaved: () => lastSaved,

    // Theme helpers
    isDarkMode: settings.theme?.mode === 'dark',
    isLightMode: settings.theme?.mode === 'light',
    isAutoMode: settings.theme?.mode === 'auto',

    // Notification helpers
    areEmailNotificationsEnabled: settings.notifications?.emailNotifications || false,
    arePushNotificationsEnabled: settings.notifications?.pushNotifications || false,

    // Privacy helpers
    isProfilePublic: settings.privacy?.profileVisibility === 'public',
    isAnalyticsEnabled: settings.privacy?.allowAnalytics || false,

    // Accessibility helpers
    isHighContrastEnabled: settings.accessibility?.highContrast || false,
    isReducedMotionEnabled: settings.accessibility?.reducedMotion || false
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
};

// Export the context
export { SettingsContext };

// Default export for convenience
export default SettingsProvider;

// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import AccessibleButton from '../AccessibleButton';

// Mock the accessibility hook
const mockAccessibility = {
  announce: vi.fn(),
  focusVisible: true,
  isKeyboardUser: true
};

vi.mock('../../../hooks/useAccessibility', () => ({
  useAccessibility: () => mockAccessibility,
}));

// Mock the notification hook
const mockNotification = {
  showSuccessNotification: vi.fn(),
  showErrorNotification: vi.fn()
};

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => mockNotification,
}));

// Mock navigator.vibrate
Object.defineProperty(navigator, 'vibrate', {
  value: vi.fn(),
  writable: true
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      success: {
        main: '#4CAF50',
        dark: '#388E3C',
        light: '#C8E6C9',
        contrastText: '#FFFFFF',
      },
      error: {
        main: '#F44336',
        dark: '#D32F2F',
        light: '#FFCDD2',
        contrastText: '#FFFFFF',
      },
      warning: {
        main: '#FF9800',
        dark: '#F57C00',
        contrastText: '#FFFFFF',
      },
      info: {
        main: '#2196F3',
      },
      text: {
        primary: '#000000',
      },
      common: {
        white: '#FFFFFF',
      },
    },
    transitions: {
      create: () => 'all 0.3s ease',
      duration: {
        short: 250,
      },
      easing: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
    },
    breakpoints: {
      down: () => '@media (max-width: 600px)',
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('AccessibleButton', () => {
  const mockProps = {
    children: 'Test Button',
    onClick: vi.fn(),
    ariaLabel: 'Test button'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders accessible button correctly', () => {
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} />
      </TestWrapper>
    );

    const button = screen.getByRole('button', { name: /test button/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Test Button');
  });

  test('handles click events', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockProps.onClick).toHaveBeenCalled();
    expect(mockAccessibility.announce).toHaveBeenCalledWith('Test button activated', 'polite');
  });

  test('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    
    // Focus and press Enter
    button.focus();
    await user.keyboard('{Enter}');

    expect(mockProps.onClick).toHaveBeenCalled();
  });

  test('handles Space key activation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    
    // Focus and press Space
    button.focus();
    await user.keyboard(' ');

    expect(mockProps.onClick).toHaveBeenCalled();
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} loading={true} loadingText="Loading..." />
      </TestWrapper>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('disables button when disabled prop is true', () => {
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('aria-disabled', 'true');
  });

  test('disables button when loading', () => {
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} loading={true} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute('aria-busy', 'true');
  });

  test('renders as icon button when isIconButton is true', () => {
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} isIconButton={true} tooltipText="Icon button tooltip" />
      </TestWrapper>
    );

    // Should render tooltip
    expect(screen.getByText('Icon button tooltip')).toBeInTheDocument();
  });

  test('handles different button states', () => {
    const { rerender } = render(
      <TestWrapper>
        <AccessibleButton {...mockProps} buttonState="success" />
      </TestWrapper>
    );

    let button = screen.getByRole('button');
    expect(button).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <AccessibleButton {...mockProps} buttonState="error" />
      </TestWrapper>
    );

    button = screen.getByRole('button');
    expect(button).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <AccessibleButton {...mockProps} buttonState="warning" />
      </TestWrapper>
    );

    button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  test('shows state icon when enabled', () => {
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} buttonState="success" showStateIcon={true} />
      </TestWrapper>
    );

    // Should render with state icon
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  test('handles confirmation dialog', async () => {
    const user = userEvent.setup();
    
    // Mock window.confirm
    const mockConfirm = vi.fn().mockReturnValue(true);
    Object.defineProperty(window, 'confirm', {
      value: mockConfirm,
      writable: true
    });
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} confirmAction={true} confirmMessage="Are you sure?" />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockConfirm).toHaveBeenCalledWith('Are you sure?');
    expect(mockProps.onClick).toHaveBeenCalled();
  });

  test('cancels action when confirmation is denied', async () => {
    const user = userEvent.setup();
    
    // Mock window.confirm to return false
    const mockConfirm = vi.fn().mockReturnValue(false);
    Object.defineProperty(window, 'confirm', {
      value: mockConfirm,
      writable: true
    });
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} confirmAction={true} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockConfirm).toHaveBeenCalled();
    expect(mockProps.onClick).not.toHaveBeenCalled();
  });

  test('handles debounced clicks', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} debounceMs={100} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    
    // Click multiple times rapidly
    await user.click(button);
    await user.click(button);
    await user.click(button);

    // Wait for debounce
    await waitFor(() => {
      expect(mockProps.onClick).toHaveBeenCalledTimes(1);
    }, { timeout: 200 });
  });

  test('prevents double clicks', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    
    // Click twice rapidly
    await user.click(button);
    await user.click(button);

    // Should only register one click due to double-click prevention
    expect(mockProps.onClick).toHaveBeenCalledTimes(1);
  });

  test('handles error in onClick handler', async () => {
    const user = userEvent.setup();
    const errorOnClick = vi.fn().mockImplementation(() => {
      throw new Error('Test error');
    });
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} onClick={errorOnClick} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(errorOnClick).toHaveBeenCalled();
    expect(mockNotification.showErrorNotification).toHaveBeenCalledWith(
      'An error occurred. Please try again.'
    );
  });

  test('shows success notification for success state', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} buttonState="success" />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith(
      'Action completed successfully'
    );
  });

  test('has proper ARIA attributes', () => {
    render(
      <TestWrapper>
        <AccessibleButton 
          {...mockProps} 
          ariaDescribedBy="description"
          pressed={true}
        />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Test button');
    expect(button).toHaveAttribute('aria-describedby', 'description');
    expect(button).toHaveAttribute('aria-pressed', 'true');
  });

  test('handles focus and blur events', async () => {
    const user = userEvent.setup();
    const onFocus = vi.fn();
    const onBlur = vi.fn();
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} onFocus={onFocus} onBlur={onBlur} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    
    // Focus and blur
    await user.click(button);
    button.focus();
    button.blur();

    expect(onFocus).toHaveBeenCalled();
    expect(onBlur).toHaveBeenCalled();
  });

  test('handles custom key down events', async () => {
    const user = userEvent.setup();
    const onKeyDown = vi.fn();
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} onKeyDown={onKeyDown} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    button.focus();
    await user.keyboard('{Enter}');

    expect(onKeyDown).toHaveBeenCalled();
  });

  test('renders with different variants and sizes', () => {
    const { rerender } = render(
      <TestWrapper>
        <AccessibleButton {...mockProps} variant="outlined" size="small" />
      </TestWrapper>
    );

    let button = screen.getByRole('button');
    expect(button).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <AccessibleButton {...mockProps} variant="text" size="large" />
      </TestWrapper>
    );

    button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  test('handles fullWidth prop', () => {
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} fullWidth={true} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  test('renders with start and end icons', () => {
    const startIcon = <span data-testid="start-icon">Start</span>;
    const endIcon = <span data-testid="end-icon">End</span>;
    
    render(
      <TestWrapper>
        <AccessibleButton {...mockProps} startIcon={startIcon} endIcon={endIcon} />
      </TestWrapper>
    );

    expect(screen.getByTestId('start-icon')).toBeInTheDocument();
    expect(screen.getByTestId('end-icon')).toBeInTheDocument();
  });

  test('announces loading state changes', () => {
    const { rerender } = render(
      <TestWrapper>
        <AccessibleButton {...mockProps} loading={false} />
      </TestWrapper>
    );

    rerender(
      <TestWrapper>
        <AccessibleButton {...mockProps} loading={true} loadingText="Loading data..." />
      </TestWrapper>
    );

    expect(mockAccessibility.announce).toHaveBeenCalledWith('Loading data...', 'polite');
  });

  test('validates accessibility requirements', () => {
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <AccessibleButton onClick={vi.fn()}>
          <span>Complex content</span>
        </AccessibleButton>
      </TestWrapper>
    );

    expect(consoleSpy).toHaveBeenCalledWith(
      'AccessibleButton: Consider providing an ariaLabel for better accessibility'
    );
    
    consoleSpy.mockRestore();
  });
});

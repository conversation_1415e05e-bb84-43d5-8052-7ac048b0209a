/**
 * Competitor Insights Context
 * Provides advanced competitor analysis and insights functionality
 * Production-ready implementation with comprehensive error handling and logging
 @since 2024-1-1 to 2025-25-7
*/

import {
  createContext,
  useContext,
  useState,
  useCallback,
} from "react";
import { useNotification } from "../hooks/useNotification";
import { useCompetitors } from "./CompetitorContext";
import * as competitorApi from "../api/competitors";
import * as contentApi from "../api/content";

// Configuration constants
const CONFIG = {
  // Cache settings
  CACHE_DURATION: 300000, // 5 minutes

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second

  // Insight generation settings
  MIN_COMPETITORS_FOR_INSIGHTS: 1,
  MAX_COMPETITORS_FOR_INSIGHTS: 10,

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[CompetitorInsights] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[CompetitorInsights] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Competitor Insights Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[CompetitorInsights] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Competitor Insights Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[CompetitorInsights] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Competitor Insights Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const CompetitorInsightsContext = createContext();

// eslint-disable-next-line react-refresh/only-export-components
export const useCompetitorInsights = () => useContext(CompetitorInsightsContext);

export const CompetitorInsightsProvider = ({ children }) => {
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { getCompetitorCount } = useCompetitors();

  // State for insights dashboard
  const [insightsDashboard, setInsightsDashboard] = useState(null);
  const [contentAnalysis, setContentAnalysis] = useState(null);
  const [strategicRecommendations, setStrategicRecommendations] = useState(null);
  const [actionableInsights, setActionableInsights] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedCompetitors, setSelectedCompetitors] = useState([]);
  const [insightFilters, setInsightFilters] = useState({
    category: "all",
    platform: "all",
    timeframe: "30days",
    contentType: "all",
  });

  // Load dashboard data
  const loadInsightsDashboard = useCallback(async () => {
    if (selectedCompetitors.length === 0) {
      logger.warn("loadInsightsDashboard called with no selected competitors");
      return null;
    }

    if (selectedCompetitors.length > CONFIG.MAX_COMPETITORS_FOR_INSIGHTS) {
      logger.warn("Too many competitors selected for insights", {
        count: selectedCompetitors.length,
        max: CONFIG.MAX_COMPETITORS_FOR_INSIGHTS
      });
      setError(`Maximum ${CONFIG.MAX_COMPETITORS_FOR_INSIGHTS} competitors allowed for insights`);
      return null;
    }

    setLoading(true);
    setError(null);
    logger.debug("Loading insights dashboard", {
      competitors: selectedCompetitors.length,
      timeframe: insightFilters.timeframe
    });

    try {
      const data = await competitorApi.getCompetitorInsightsDashboard(
        selectedCompetitors,
        insightFilters.timeframe
      );
      setInsightsDashboard(data);
      logger.info("Insights dashboard loaded successfully", {
        competitors: selectedCompetitors.length,
        insights: data?.insights?.length || 0
      });
      return data;
    } catch (err) {
      logger.error('Error loading insights dashboard', err);
      setError(err.message || "Failed to load insights dashboard");
      showErrorNotification("Failed to load insights dashboard");
      return null;
    } finally {
      setLoading(false);
    }
  }, [selectedCompetitors, insightFilters.timeframe, showErrorNotification]);

  // Load content analysis
  const loadContentAnalysis = useCallback(async (competitorIds) => {
    const targetCompetitors = competitorIds || selectedCompetitors;

    if (targetCompetitors.length === 0) {
      logger.warn("loadContentAnalysis called with no competitors");
      setError("No competitors selected for content analysis");
      return null;
    }

    setLoading(true);
    setError(null);
    logger.debug("Loading content analysis", {
      competitors: targetCompetitors.length,
      platform: insightFilters.platform,
      contentType: insightFilters.contentType,
      timeframe: insightFilters.timeframe
    });

    try {
      const data = await competitorApi.getCompetitorContentAnalysis(
        targetCompetitors,
        insightFilters.platform,
        insightFilters.contentType,
        insightFilters.timeframe
      );
      setContentAnalysis(data);
      logger.info("Content analysis loaded successfully", {
        competitors: targetCompetitors.length,
        contentItems: data?.content?.length || 0
      });
      return data;
    } catch (err) {
      logger.error("Failed to load content analysis", err);
      setError(err.message || "Failed to load content analysis");
      showErrorNotification("Failed to load content analysis");
      return null;
    } finally {
      setLoading(false);
    }
  }, [selectedCompetitors, insightFilters, showErrorNotification]);

  // Load strategic recommendations
  const loadStrategicRecommendations = useCallback(async (competitorIds) => {
    setLoading(true);
    setError(null);

    try {
      const data = await competitorApi.getCompetitorRecommendations(
        competitorIds || selectedCompetitors,
        ["content_strategy", "posting_schedule", "engagement", "audience_targeting"]
      );
      setStrategicRecommendations(data);
      return data;
    } catch (err) {
      logger.error('Error loading strategic recommendations', err);
      setError(err.message || "Failed to load strategic recommendations");
      showErrorNotification("Failed to load strategic recommendations");
      return null;
    } finally {
      setLoading(false);
    }
  }, [selectedCompetitors, showErrorNotification]);

  // Generate actionable insights
  const generateActionableInsights = useCallback(async (competitorIds) => {
    const targetCompetitors = competitorIds || selectedCompetitors;

    if (targetCompetitors.length === 0) {
      logger.warn("generateActionableInsights called with no competitors");
      setError("No competitors selected for generating insights");
      return null;
    }

    setLoading(true);
    setError(null);
    logger.debug("Generating actionable insights", {
      competitors: targetCompetitors.length,
      filters: insightFilters
    });

    try {
      const data = await competitorApi.generateActionableInsights(
        targetCompetitors,
        insightFilters
      );
      setActionableInsights(data);
      logger.info("Actionable insights generated successfully", {
        competitors: targetCompetitors.length,
        insights: data?.length || 0
      });
      return data;
    } catch (err) {
      logger.error("Failed to generate actionable insights", err);
      setError(err.message || "Failed to generate actionable insights");
      showErrorNotification("Failed to generate actionable insights");
      return null;
    } finally {
      setLoading(false);
    }
  }, [selectedCompetitors, insightFilters, showErrorNotification]);

  // Create content from insight
  const createContentFromInsight = useCallback(async (insightId) => {
    setLoading(true);
    setError(null);

    try {
      const insight = actionableInsights.find(i => i.id === insightId);
      if (!insight) {
        throw new Error("Insight not found");
      }

      // Convert insight to content brief
      const contentBrief = {
        topic: insight.suggestedTopic,
        tone: insight.suggestedTone || "professional",
        platform: insight.targetPlatform,
        content_type: "post",
        include_hashtags: true,
        target_audience: insight.targetAudience,
        generate_image: true,
        include_headline_on_image: true
      };

      // Generate content using the brief
      const content = await contentApi.generateContent(contentBrief);
      showSuccessNotification("Content created from insight successfully");
      return content;
    } catch (err) {
      setError(err.message || "Failed to create content from insight");
      showErrorNotification("Failed to create content from insight");
      return null;
    } finally {
      setLoading(false);
    }
  }, [actionableInsights, showSuccessNotification, showErrorNotification]);

  // Create campaign from insights
  const createCampaignFromInsights = useCallback(async (insightIds) => {
    setLoading(true);
    setError(null);

    try {
      // Get selected insights
      const selectedInsights = actionableInsights.filter(i => insightIds.includes(i.id));
      if (selectedInsights.length === 0) {
        throw new Error("No insights selected");
      }

      // Create campaign brief from insights
      const campaignBrief = {
        name: `Campaign based on competitor insights - ${new Date().toLocaleDateString()}`,
        description: `Campaign created from ${selectedInsights.length} competitor insights`,
        platforms: [...new Set(selectedInsights.map(i => i.targetPlatform))],
        target_audience: selectedInsights[0].targetAudience,
        insights: selectedInsights.map(i => i.id),
        content_strategy: selectedInsights.map(i => ({
          topic: i.suggestedTopic,
          tone: i.suggestedTone || "professional",
          contentType: "post"
        }))
      };

      // Create campaign using the brief
      const campaign = await competitorApi.createCampaignFromInsights(campaignBrief);
      showSuccessNotification("Campaign created from insights successfully");
      return campaign;
    } catch (err) {
      setError(err.message || "Failed to create campaign from insights");
      showErrorNotification("Failed to create campaign from insights");
      return null;
    } finally {
      setLoading(false);
    }
  }, [actionableInsights, showSuccessNotification, showErrorNotification]);

  // Update selected competitors
  const updateSelectedCompetitors = useCallback((competitorIds) => {
    logger.debug("Updating selected competitors", {
      previous: selectedCompetitors.length,
      new: competitorIds.length
    });
    setSelectedCompetitors(competitorIds);
  }, [selectedCompetitors.length]);

  // Update insight filters
  const updateInsightFilters = useCallback((filters) => {
    logger.debug("Updating insight filters", { filters });
    setInsightFilters(prev => ({ ...prev, ...filters }));
  }, []);

  // Context value with organized structure
  const value = {
    // State data
    insightsDashboard,
    contentAnalysis,
    strategicRecommendations,
    actionableInsights,
    loading,
    error,
    selectedCompetitors,
    insightFilters,

    // Data loading functions
    loadInsightsDashboard,
    loadContentAnalysis,
    loadStrategicRecommendations,
    generateActionableInsights,

    // Content creation functions
    createContentFromInsight,
    createCampaignFromInsights,

    // Configuration functions
    updateSelectedCompetitors,
    updateInsightFilters,

    // Utility functions
    clearError: () => setError(null),
    clearAllData: () => {
      logger.debug("Clearing all insights data");
      setInsightsDashboard(null);
      setContentAnalysis(null);
      setStrategicRecommendations(null);
      setActionableInsights([]);
      setError(null);
    },

    // Helper functions
    hasSelectedCompetitors: selectedCompetitors.length > 0,
    getSelectedCompetitorCount: () => selectedCompetitors.length,
    hasInsights: actionableInsights.length > 0,
    getInsightCount: () => actionableInsights.length,
    getTotalCompetitorCount: getCompetitorCount,
    canGenerateInsights: selectedCompetitors.length >= CONFIG.MIN_COMPETITORS_FOR_INSIGHTS,

    // Filter helpers
    resetFilters: () => {
      logger.debug("Resetting insight filters");
      setInsightFilters({
        category: "all",
        platform: "all",
        timeframe: "30days",
        contentType: "all",
      });
    },

    // Validation helpers
    validateCompetitorSelection: (competitorIds) => {
      if (!Array.isArray(competitorIds)) return false;
      if (competitorIds.length === 0) return false;
      if (competitorIds.length > CONFIG.MAX_COMPETITORS_FOR_INSIGHTS) return false;
      return true;
    }
  };

  return (
    <CompetitorInsightsContext.Provider value={value}>
      {children}
    </CompetitorInsightsContext.Provider>
  );
};

export default CompetitorInsightsContext;

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import CampaignErrorBoundary from '../CampaignErrorBoundary';
import { withCampaignErrorBoundary } from '../withCampaignErrorBoundary';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock CampaignGracefulDegradation component
vi.mock('../CampaignGracefulDegradation', () => ({
  default: ({ onRetry }) => (
    <div data-testid="graceful-degradation">
      <span>Graceful Degradation Component</span>
      <button onClick={onRetry}>Retry from Graceful</button>
    </div>
  ),
}));

// Mock react-router-dom hooks
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme and router
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      error: {
        main: '#f44336',
      },
      warning: {
        main: '#ff9800',
      },
    },
  });
  
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Component that throws an error for testing
const ThrowError = ({ shouldThrow, errorType = 'unknown' }) => {
  if (shouldThrow) {
    const error = new Error('Test error message');
    
    if (errorType === 'network') {
      error.message = 'Network Error';
      error.code = 'ECONNREFUSED';
    } else if (errorType === 'permission') {
      error.response = { status: 401 };
    } else if (errorType === 'validation') {
      error.response = { status: 400 };
    } else if (errorType === 'server') {
      error.response = { status: 500 };
    }
    
    throw error;
  }
  return <div>No Error</div>;
};

describe('CampaignErrorBoundary', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Suppress console.error for error boundary tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders children when there is no error', () => {
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <div>Test Content</div>
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('shows graceful degradation for network errors', () => {
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="network" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByTestId('graceful-degradation')).toBeInTheDocument();
    expect(screen.getByText('Graceful Degradation Component')).toBeInTheDocument();
  });

  test('shows enhanced error boundary for non-network errors', () => {
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Unexpected Error Occurred')).toBeInTheDocument();
    expect(screen.getByText('An unexpected error occurred. Our team has been notified and is working on a fix.')).toBeInTheDocument();
  });

  test('shows permission error for 401/403 status codes', () => {
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="permission" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(screen.getByText("You don't have permission to access this resource. Please contact your administrator.")).toBeInTheDocument();
  });

  test('shows validation error for 400-499 status codes', () => {
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="validation" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Invalid Request')).toBeInTheDocument();
    expect(screen.getByText('There was an issue with your request. Please check your input and try again.')).toBeInTheDocument();
  });

  test('handles retry functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    const retryButton = screen.getByText('Try Again');
    expect(retryButton).toBeInTheDocument();
    
    await user.click(retryButton);
    // The error boundary should reset (this would be tested with a more complex setup)
  });

  test('handles navigation to dashboard', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    const dashboardButton = screen.getByText('Go to Dashboard');
    await user.click(dashboardButton);

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  test('shows technical details accordion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    const technicalDetailsButton = screen.getByText('Technical Details');
    await user.click(technicalDetailsButton);

    expect(screen.getByText('Error Type:')).toBeInTheDocument();
    expect(screen.getByText('Severity:')).toBeInTheDocument();
    expect(screen.getByText('Message:')).toBeInTheDocument();
    expect(screen.getByText('Timestamp:')).toBeInTheDocument();
  });

  test('copies error details to clipboard', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    // Open technical details
    await user.click(screen.getByText('Technical Details'));
    
    // Click copy button
    const copyButton = screen.getByLabelText('Copy error details');
    await user.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalled();
    expect(mockShowSuccessNotification).toHaveBeenCalledWith('Error details copied to clipboard');
  });

  test('opens error reporting dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    // Open technical details
    await user.click(screen.getByText('Technical Details'));
    
    // Click report button
    const reportButton = screen.getByLabelText('Report error');
    await user.click(reportButton);

    expect(screen.getByText('Report Error')).toBeInTheDocument();
    expect(screen.getByLabelText('Describe what happened')).toBeInTheDocument();
  });

  test('handles error report submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    // Open technical details and report dialog
    await user.click(screen.getByText('Technical Details'));
    await user.click(screen.getByLabelText('Report error'));

    // Fill in description
    const descriptionField = screen.getByLabelText('Describe what happened');
    await user.type(descriptionField, 'I was trying to create a campaign when this error occurred');

    // Submit report
    const sendButton = screen.getByText('Send Report');
    await user.click(sendButton);

    await waitFor(() => {
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Error report sent successfully. Thank you for your feedback!');
    });
  });

  test('closes error reporting dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    // Open report dialog
    await user.click(screen.getByText('Technical Details'));
    await user.click(screen.getByLabelText('Report error'));

    // Close dialog
    const closeButton = screen.getByLabelText('close');
    await user.click(closeButton);

    expect(screen.queryByText('Report Error')).not.toBeInTheDocument();
  });

  test('shows correct severity chips', () => {
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    // Open technical details to see severity chip
    fireEvent.click(screen.getByText('Technical Details'));
    
    expect(screen.getByText('medium')).toBeInTheDocument();
  });

  test('handles custom fallback component', () => {
    const CustomFallback = () => <div>Custom Error Fallback</div>;
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary fallback={CustomFallback}>
          <ThrowError shouldThrow={true} />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Custom Error Fallback')).toBeInTheDocument();
  });

  test('withCampaignErrorBoundary HOC works correctly', () => {
    const TestComponent = () => <div>Test Component</div>;
    const WrappedComponent = withCampaignErrorBoundary(TestComponent);
    
    render(
      <TestWrapper>
        <WrappedComponent />
      </TestWrapper>
    );

    expect(screen.getByText('Test Component')).toBeInTheDocument();
  });

  test('withCampaignErrorBoundary HOC catches errors', () => {
    const ErrorComponent = () => {
      throw new Error('HOC Test Error');
    };
    const WrappedComponent = withCampaignErrorBoundary(ErrorComponent);
    
    render(
      <TestWrapper>
        <WrappedComponent />
      </TestWrapper>
    );

    expect(screen.getByText('Unexpected Error Occurred')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Try again')).toBeInTheDocument();
    expect(screen.getByLabelText('Go to dashboard')).toBeInTheDocument();
    
    // Open technical details to check more accessibility features
    fireEvent.click(screen.getByText('Technical Details'));
    expect(screen.getByLabelText('Copy error details')).toBeInTheDocument();
    expect(screen.getByLabelText('Report error')).toBeInTheDocument();
  });

  test('handles clipboard copy failure gracefully', async () => {
    const user = userEvent.setup();
    
    // Mock clipboard failure
    navigator.clipboard.writeText.mockRejectedValueOnce(new Error('Clipboard failed'));
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    await user.click(screen.getByText('Technical Details'));
    await user.click(screen.getByLabelText('Copy error details'));

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith('Failed to copy error details');
    });
  });

  test('disables send report button when description is empty', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="unknown" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    await user.click(screen.getByText('Technical Details'));
    await user.click(screen.getByLabelText('Report error'));

    const sendButton = screen.getByText('Send Report');
    expect(sendButton).toBeDisabled();
  });

  test('shows retry button only for recoverable errors', () => {
    render(
      <TestWrapper>
        <CampaignErrorBoundary>
          <ThrowError shouldThrow={true} errorType="permission" />
        </CampaignErrorBoundary>
      </TestWrapper>
    );

    // Permission errors are not recoverable, so no retry button should be shown
    expect(screen.queryByText('Try Again')).not.toBeInTheDocument();
    expect(screen.getByText('Go to Dashboard')).toBeInTheDocument();
  });
});

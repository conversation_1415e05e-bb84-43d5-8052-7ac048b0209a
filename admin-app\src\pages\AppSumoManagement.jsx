// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Chip,
  IconButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Fab,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  CardGiftcard as AppSumoIcon,
  Category as CategoryIcon,
  ConfirmationNumber as CodeIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Analytics as AnalyticsIcon,
  Speed as SpeedIcon,
  Timeline as TimelineIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

// Import custom components
import StablePageWrapper from '../components/common/StablePageWrapper';
import AppSumoAnalytics from '../components/appsumo/AppSumoAnalytics';
import BulkCodeGenerator from '../components/appsumo/BulkCodeGenerator';
import RedemptionTracker from '../components/appsumo/RedemptionTracker';
import PackageCreator from '../components/appsumo/PackageCreator';

// Import hooks and utilities
import useAppSumoData from '../hooks/useAppSumoData';
import {
  formatDate,
  formatCurrency,
  copyToClipboard,
  exportToCSV,
  generateCorrelationId
} from '../utils/appsumoHelpers';
import api from '../api';

/**
 * Enhanced AppSumo Management Component
 * Production-ready with Material-UI glass morphism styling, comprehensive features,
 * and performance optimizations
 */
const AppSumoManagement = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [selectedItem, setSelectedItem] = useState(null);
  const [formData, setFormData] = useState({});
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Dialog states
  const [dialogs, setDialogs] = useState({
    packageCreator: false,
    bulkGenerator: false,
    editItem: false,
  });

  // Use custom hook for data management
  const {
    data,
    loading,
    error,
    fetchTiers,
    fetchDeals,
    fetchCodes,
    fetchAnalytics,
    fetchRedemptions,
    refreshAll,
    clearCache,
  } = useAppSumoData();

  // Tab configuration
  const tabs = [
    {
      label: 'Analytics',
      icon: <AnalyticsIcon />,
      value: 0,
      description: 'Real-time analytics and performance metrics'
    },
    {
      label: 'Packages',
      icon: <AppSumoIcon />,
      value: 1,
      description: 'Manage AppSumo deals and packages'
    },
    {
      label: 'Tiers',
      icon: <CategoryIcon />,
      value: 2,
      description: 'Configure subscription tiers'
    },
    {
      label: 'Codes',
      icon: <CodeIcon />,
      value: 3,
      description: 'Generate and manage redemption codes'
    },
    {
      label: 'Redemptions',
      icon: <TimelineIcon />,
      value: 4,
      description: 'Track code redemptions and user activity'
    },
  ];

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Use Promise.allSettled to prevent one failure from stopping others
        const results = await Promise.allSettled([
          fetchTiers(),
          fetchDeals(),
          fetchCodes(),
          fetchAnalytics(),
          fetchRedemptions(),
        ]);

        // Check if all requests failed (likely backend is down)
        const allFailed = results.every(result => result.status === 'rejected');
        if (allFailed) {
          showSnackbar('Backend service is unavailable. Some features may not work.', 'warning');
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
        showSnackbar('Failed to load initial data', 'error');
      }
    };

    loadInitialData();
  }, []);

  // Refresh data when tab changes
  useEffect(() => {
    const refreshTabData = async () => {
      try {
        switch (tabValue) {
          case 0: // Analytics
            await Promise.allSettled([fetchAnalytics(), fetchCodes(), fetchRedemptions()]);
            break;
          case 1: // Packages
            await fetchDeals();
            break;
          case 2: // Tiers
            await fetchTiers();
            break;
          case 3: // Codes
            await fetchCodes();
            break;
          case 4: // Redemptions
            await fetchRedemptions();
            break;
        }
      } catch (error) {
        console.error('Error refreshing tab data:', error);
      }
    };

    refreshTabData();
  }, [tabValue]);

  // Utility functions
  const showSnackbar = useCallback((message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  const closeSnackbar = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  const openDialog = useCallback((dialogName) => {
    setDialogs(prev => ({ ...prev, [dialogName]: true }));
  }, []);

  const closeDialog = useCallback((dialogName) => {
    setDialogs(prev => ({ ...prev, [dialogName]: false }));
    setSelectedItem(null);
    setFormData({});
  }, []);

  // Event handlers
  const handleTabChange = useCallback((event, newValue) => {
    setTabValue(newValue);

    // Clear cache for better performance
    if (newValue !== tabValue) {
      clearCache();
    }
  }, [tabValue, clearCache]);

  const handleRefreshAll = useCallback(async () => {
    try {
      await refreshAll();
      showSnackbar('Data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing data:', error);
      showSnackbar('Failed to refresh data', 'error');
    }
  }, [refreshAll, showSnackbar]);

  const handleCreatePackage = useCallback(() => {
    setSelectedItem(null);
    openDialog('packageCreator');
  }, [openDialog]);

  const handleEditItem = useCallback((item, itemType) => {
    setSelectedItem({ ...item, itemType });
    if (itemType === 'deal') {
      openDialog('packageCreator');
    } else {
      setFormData(item);
      openDialog('editItem');
    }
  }, [openDialog]);

  const handleBulkGenerate = useCallback(() => {
    openDialog('bulkGenerator');
  }, [openDialog]);

  const handleSave = useCallback(async () => {
    if (!selectedItem || !formData) return;

    try {
      const correlationId = generateCorrelationId();
      const headers = { 'X-Correlation-ID': correlationId };

      let endpoint = '';
      let method = 'post';

      if (selectedItem.itemType === 'tier') {
        endpoint = selectedItem.id ? `/api/appsumo/tiers/${selectedItem.id}` : '/api/appsumo/tiers';
        method = selectedItem.id ? 'put' : 'post';
      } else if (selectedItem.itemType === 'deal') {
        endpoint = selectedItem.id ? `/api/appsumo/deals/${selectedItem.id}` : '/api/appsumo/deals';
        method = selectedItem.id ? 'put' : 'post';
      }

      await api[method](endpoint, formData, { headers });

      closeDialog('editItem');

      // Refresh relevant data
      if (selectedItem.itemType === 'tier') {
        await fetchTiers(true);
      } else if (selectedItem.itemType === 'deal') {
        await fetchDeals(true);
      }

      showSnackbar(`${selectedItem.itemType} ${selectedItem.id ? 'updated' : 'created'} successfully`);
    } catch (error) {
      console.error('Error saving:', error);
      showSnackbar(error.response?.data?.detail || 'Failed to save', 'error');
    }
  }, [selectedItem, formData, closeDialog, fetchTiers, fetchDeals, showSnackbar]);

  const handleDelete = useCallback(async (item, itemType) => {
    if (!window.confirm(`Are you sure you want to delete this ${itemType}?`)) return;

    try {
      const correlationId = generateCorrelationId();
      const headers = { 'X-Correlation-ID': correlationId };

      let endpoint = '';
      if (itemType === 'tier') {
        endpoint = `/api/appsumo/tiers/${item.id}`;
      } else if (itemType === 'deal') {
        endpoint = `/api/appsumo/deals/${item.id}`;
      } else if (itemType === 'code') {
        endpoint = `/api/appsumo/codes/${item.id}`;
      }

      await api.delete(endpoint, { headers });

      // Refresh relevant data
      if (itemType === 'tier') {
        await fetchTiers(true);
      } else if (itemType === 'deal') {
        await fetchDeals(true);
      } else if (itemType === 'code') {
        await fetchCodes({ force: true });
      }

      showSnackbar(`${itemType} deleted successfully`);
    } catch (error) {
      console.error('Error deleting:', error);
      showSnackbar(`Failed to delete ${itemType}`, 'error');
    }
  }, [fetchTiers, fetchDeals, fetchCodes, showSnackbar]);

  const handleCopyCode = useCallback(async (code) => {
    try {
      const success = await copyToClipboard(code);
      if (success) {
        showSnackbar('Code copied to clipboard');
      } else {
        showSnackbar('Failed to copy code', 'error');
      }
    } catch (error) {
      console.error('Error copying code:', error);
      showSnackbar('Failed to copy code', 'error');
    }
  }, [showSnackbar]);

  const handlePackageCreated = useCallback(async (newPackage) => {
    await fetchDeals(true);
    showSnackbar('Package created successfully');
    closeDialog('packageCreator');
  }, [fetchDeals, showSnackbar, closeDialog]);

  const handleCodesGenerated = useCallback(async (newCodes) => {
    await fetchCodes({ force: true });
    await fetchAnalytics(true);
    showSnackbar(`${newCodes.length} codes generated successfully`);
    closeDialog('bulkGenerator');
  }, [fetchCodes, fetchAnalytics, showSnackbar, closeDialog]);

  // Render functions for each tab
  const renderAnalyticsTab = () => (
    <AppSumoAnalytics
      data={data}
      loading={loading.analytics}
      error={error}
      onRefresh={fetchAnalytics}
    />
  );

  const renderPackagesTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AppSumoIcon />
            AppSumo Packages
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage lifetime deals and package configurations
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreatePackage}
          sx={{
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            '&:hover': {
              background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
            },
          }}
        >
          Create Package
        </Button>
      </Box>

      <Card variant="glass">
        <CardContent>
          {loading.deals ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Deal ID</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Pricing</TableCell>
                    <TableCell>Schedule</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.deals?.map((deal) => (
                    <TableRow key={deal.id} hover>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace" fontWeight="medium">
                          {deal.deal_id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {deal.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {deal.description?.substring(0, 50)}...
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {formatCurrency(deal.pricing?.appsumo_price || 59)}
                          </Typography>
                          {deal.pricing?.regular_price && (
                            <Typography variant="caption" color="text.secondary" sx={{ textDecoration: 'line-through' }}>
                              {formatCurrency(deal.pricing.regular_price)}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {formatDate(deal.start_date)}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {deal.end_date ? `Until ${formatDate(deal.end_date)}` : 'No end date'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={deal.is_active ? 'Active' : 'Inactive'}
                          color={deal.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Edit Package">
                          <IconButton size="small" onClick={() => handleEditItem(deal, 'deal')}>
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Package">
                          <IconButton size="small" onClick={() => handleDelete(deal, 'deal')}>
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderTiersTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CategoryIcon />
            AppSumo Tiers
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Configure subscription tiers and feature access
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            setSelectedItem({ itemType: 'tier' });
            setFormData({
              tier_type: '',
              name: '',
              description: '',
              plan_id: '',
              max_users: 1,
              max_social_accounts: 5,
              is_active: true,
            });
            openDialog('editItem');
          }}
        >
          Create Tier
        </Button>
      </Box>

      <Card variant="glass">
        <CardContent>
          {loading.tiers ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tier Type</TableCell>
                    <TableCell>Name</TableCell>
                    <TableCell>Plan ID</TableCell>
                    <TableCell>Limits</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.tiers?.map((tier) => (
                    <TableRow key={tier.id} hover>
                      <TableCell>
                        <Chip
                          label={tier.tier_type?.toUpperCase()}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {tier.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {tier.description}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {tier.plan_id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {tier.max_users} users, {tier.max_social_accounts} accounts
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {tier.max_posts_per_month} posts/month
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={tier.is_active ? 'Active' : 'Inactive'}
                          color={tier.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Edit Tier">
                          <IconButton size="small" onClick={() => handleEditItem(tier, 'tier')}>
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Tier">
                          <IconButton size="small" onClick={() => handleDelete(tier, 'tier')}>
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderCodesTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CodeIcon />
            AppSumo Codes
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Generate and manage redemption codes
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={() => {
              if (data.codes?.length > 0) {
                const exportColumns = [
                  { key: 'code', label: 'Code' },
                  { key: 'deal_id', label: 'Deal ID' },
                  { key: 'tier_type', label: 'Tier' },
                  { key: 'status', label: 'Status' },
                  { key: 'created_at', label: 'Created At', type: 'date' },
                  { key: 'redeemed_at', label: 'Redeemed At', type: 'date' },
                ];
                exportToCSV(data.codes, `appsumo-codes-${formatDate(new Date())}`, exportColumns);
                showSnackbar('Codes exported successfully');
              }
            }}
            disabled={!data.codes?.length}
          >
            Export
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleBulkGenerate}
          >
            Generate Codes
          </Button>
        </Box>
      </Box>

      <Card variant="glass">
        <CardContent>
          {loading.codes ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Code</TableCell>
                    <TableCell>Deal</TableCell>
                    <TableCell>Tier</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.codes?.slice(0, 100).map((code) => (
                    <TableRow key={code.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body2" fontFamily="monospace" fontWeight="medium">
                            {code.code}
                          </Typography>
                          <Tooltip title="Copy code">
                            <IconButton
                              size="small"
                              onClick={() => handleCopyCode(code.code)}
                            >
                              <CopyIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {code.deal_id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={code.tier_type?.toUpperCase()}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={code.is_redeemed ? 'Redeemed' : 'Available'}
                          color={code.is_redeemed ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(code.created_at)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Delete Code">
                          <IconButton size="small" onClick={() => handleDelete(code, 'code')}>
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {data.codes?.length > 100 && (
            <Box mt={2} textAlign="center">
              <Typography variant="body2" color="text.secondary">
                Showing first 100 codes. Use filters or export for complete data.
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderRedemptionsTab = () => (
    <RedemptionTracker
      data={data}
      loading={loading.redemptions}
      error={error}
      onRefresh={fetchRedemptions}
    />
  );

  // Main render
  return (
    <StablePageWrapper maxWidth="xl" enableGlassMorphism>
      {/* Header */}
      <Box mb={4}>
        <Typography
          variant="h4"
          gutterBottom
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 700,
          }}
        >
          <AppSumoIcon sx={{ color: theme.palette.primary.main }} />
          AppSumo Management
        </Typography>

        <Typography variant="body1" color="text.secondary" paragraph>
          Comprehensive management of AppSumo lifetime deals, tiers, codes, and analytics.
        </Typography>

        {/* Quick Stats */}
        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={6} sm={3}>
            <Card variant="glass" sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="primary.main">
                {data?.deals?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Packages
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card variant="glass" sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="success.main">
                {data?.codes?.filter(c => c?.is_redeemed)?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Redeemed Codes
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card variant="glass" sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="info.main">
                {data?.codes?.filter(c => !c?.is_redeemed)?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Available Codes
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card variant="glass" sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="warning.main">
                {data?.tiers?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tier Configurations
              </Typography>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={handleRefreshAll}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Navigation Tabs */}
      <Card variant="glass" sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: 72,
              textTransform: 'none',
              fontWeight: 600,
            },
          }}
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={
                <Box textAlign="center">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                    {tab.icon}
                    {tab.label}
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {tab.description}
                  </Typography>
                </Box>
              }
              value={tab.value}
            />
          ))}
        </Tabs>

        {/* Tab Content */}
        <Box sx={{ p: 3 }}>
          {tabValue === 0 && renderAnalyticsTab()}
          {tabValue === 1 && renderPackagesTab()}
          {tabValue === 2 && renderTiersTab()}
          {tabValue === 3 && renderCodesTab()}
          {tabValue === 4 && renderRedemptionsTab()}
        </Box>
      </Card>

      {/* Floating Action Button for Quick Actions */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          '&:hover': {
            background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
          },
        }}
        onClick={handleRefreshAll}
      >
        <RefreshIcon />
      </Fab>

      {/* Dialogs */}
      <PackageCreator
        open={dialogs.packageCreator}
        onClose={() => closeDialog('packageCreator')}
        onPackageCreated={handlePackageCreated}
        editingPackage={selectedItem?.itemType === 'deal' ? selectedItem : null}
      />

      <BulkCodeGenerator
        open={dialogs.bulkGenerator}
        onClose={() => closeDialog('bulkGenerator')}
        deals={data.deals || []}
        tiers={data.tiers || []}
        onCodesGenerated={handleCodesGenerated}
      />

      {/* Simple Edit Dialog for Tiers */}
      {selectedItem?.itemType === 'tier' && (
        <Dialog
          open={dialogs.editItem}
          onClose={() => closeDialog('editItem')}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              background: alpha(theme.palette.background.paper, 0.95),
              backdropFilter: 'blur(10px)',
            }
          }}
        >
          <DialogTitle>
            <Typography variant="h6">
              {selectedItem.id ? 'Edit Tier' : 'Create Tier'}
            </Typography>
          </DialogTitle>
          <DialogContent>
            <Box display="flex" flexDirection="column" gap={3} sx={{ mt: 2 }}>
              <TextField
                label="Tier Type"
                value={formData.tier_type || ''}
                onChange={(e) => setFormData({ ...formData, tier_type: e.target.value })}
                fullWidth
                required
                helperText="e.g., tier1, tier2, tier3"
              />
              <TextField
                label="Name"
                value={formData.name || ''}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                fullWidth
                required
                helperText="Display name for this tier"
              />
              <TextField
                label="Description"
                value={formData.description || ''}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                fullWidth
                multiline
                rows={2}
                helperText="Brief description of this tier"
              />
              <TextField
                label="Plan ID"
                value={formData.plan_id || ''}
                onChange={(e) => setFormData({ ...formData, plan_id: e.target.value })}
                fullWidth
                required
                helperText="Associated subscription plan ID"
              />
              <Box display="flex" gap={2}>
                <TextField
                  label="Max Users"
                  type="number"
                  value={formData.max_users || 1}
                  onChange={(e) => setFormData({ ...formData, max_users: parseInt(e.target.value) || 1 })}
                  fullWidth
                  inputProps={{ min: 1 }}
                />
                <TextField
                  label="Max Social Accounts"
                  type="number"
                  value={formData.max_social_accounts || 5}
                  onChange={(e) => setFormData({ ...formData, max_social_accounts: parseInt(e.target.value) || 5 })}
                  fullWidth
                  inputProps={{ min: 1 }}
                />
              </Box>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active || false}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  />
                }
                label="Active Tier"
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => closeDialog('editItem')}>Cancel</Button>
            <Button onClick={handleSave} variant="contained">
              {selectedItem.id ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert
          onClose={closeSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </StablePageWrapper>
  );
};

export default AppSumoManagement;

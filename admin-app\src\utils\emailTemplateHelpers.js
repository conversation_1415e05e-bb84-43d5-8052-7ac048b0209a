/**
 * Email template utility functions and helpers
 */

// Template validation functions
export const validateTemplateContent = (content) => {
  const errors = [];
  
  if (!content.trim()) {
    errors.push('Content cannot be empty');
    return errors;
  }
  
  // Check for basic HTML structure
  if (!content.includes('<html') && !content.includes('<body')) {
    errors.push('Template should include basic HTML structure');
  }
  
  // Check for unmatched template variables
  const variablePattern = /\{\{([^}]+)\}\}/g;
  const matches = content.match(variablePattern);
  
  if (matches) {
    matches.forEach(match => {
      const variable = match.replace(/[{}]/g, '').trim();
      if (variable.includes(' ')) {
        errors.push(`Invalid variable syntax: ${match}. Variables cannot contain spaces.`);
      }
    });
  }
  
  return errors;
};

export const extractTemplateVariables = (content) => {
  const variablePattern = /\{\{([^}]+)\}\}/g;
  const variables = new Set();
  let match;
  
  while ((match = variablePattern.exec(content)) !== null) {
    const variable = match[1].trim();
    if (variable && !variable.includes(' ')) {
      variables.add(variable);
    }
  }
  
  return Array.from(variables);
};

export const validateSubjectLine = (subject) => {
  const errors = [];
  
  if (!subject.trim()) {
    errors.push('Subject line is required');
    return errors;
  }
  
  if (subject.length > 150) {
    errors.push('Subject line should be under 150 characters for better deliverability');
  }
  
  if (subject.length < 10) {
    errors.push('Subject line should be at least 10 characters for better engagement');
  }
  
  // Check for spam trigger words
  const spamWords = ['free', 'urgent', 'act now', 'limited time', 'click here', 'buy now'];
  const lowerSubject = subject.toLowerCase();
  const foundSpamWords = spamWords.filter(word => lowerSubject.includes(word));
  
  if (foundSpamWords.length > 0) {
    errors.push(`Consider avoiding potential spam trigger words: ${foundSpamWords.join(', ')}`);
  }
  
  return errors;
};

// Template formatting functions
export const formatTemplateType = (type) => {
  const typeMap = {
    'transactional': 'Transactional',
    'marketing': 'Marketing',
    'system': 'System',
    'notification': 'Notification'
  };
  
  return typeMap[type] || type;
};

export const formatTemplateStatus = (status) => {
  const statusMap = {
    'draft': 'Draft',
    'active': 'Active',
    'archived': 'Archived',
    'testing': 'Testing'
  };
  
  return statusMap[status] || status;
};

export const getTemplateTypeIcon = (type) => {
  const iconMap = {
    'transactional': '📧',
    'marketing': '📢',
    'system': '⚙️',
    'notification': '🔔'
  };
  
  return iconMap[type] || '📄';
};

export const getStatusColor = (status) => {
  const colorMap = {
    'active': 'success',
    'draft': 'warning',
    'archived': 'default',
    'testing': 'info'
  };
  
  return colorMap[status] || 'default';
};

// Template preview functions
export const renderTemplatePreview = (template, variables = {}) => {
  let content = template.html_content;
  let subject = template.subject;
  let previewText = template.preview_text || '';
  
  // Replace variables with provided values or defaults
  Object.keys(variables).forEach(key => {
    const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
    const value = variables[key] || template.default_values?.[key] || `{{${key}}}`;
    
    content = content.replace(regex, value);
    subject = subject.replace(regex, value);
    previewText = previewText.replace(regex, value);
  });
  
  return {
    html_content: content,
    subject: subject,
    preview_text: previewText
  };
};

// Analytics helper functions
export const calculateOpenRate = (opened, sent) => {
  if (sent === 0) return 0;
  return ((opened / sent) * 100).toFixed(1);
};

export const calculateClickRate = (clicked, sent) => {
  if (sent === 0) return 0;
  return ((clicked / sent) * 100).toFixed(1);
};

export const calculateBounceRate = (bounced, sent) => {
  if (sent === 0) return 0;
  return ((bounced / sent) * 100).toFixed(1);
};

export const calculateUnsubscribeRate = (unsubscribed, sent) => {
  if (sent === 0) return 0;
  return ((unsubscribed / sent) * 100).toFixed(1);
};

export const getPerformanceRating = (openRate, clickRate) => {
  const avgOpenRate = parseFloat(openRate);
  const avgClickRate = parseFloat(clickRate);
  
  if (avgOpenRate >= 25 && avgClickRate >= 3) {
    return { rating: 'excellent', color: 'success', label: 'Excellent' };
  } else if (avgOpenRate >= 20 && avgClickRate >= 2) {
    return { rating: 'good', color: 'info', label: 'Good' };
  } else if (avgOpenRate >= 15 && avgClickRate >= 1) {
    return { rating: 'average', color: 'warning', label: 'Average' };
  } else {
    return { rating: 'poor', color: 'error', label: 'Needs Improvement' };
  }
};

// Template export/import functions
export const exportTemplate = (template) => {
  const exportData = {
    name: template.name,
    description: template.description,
    template_type: template.template_type,
    subject: template.subject,
    html_content: template.html_content,
    text_content: template.text_content,
    preview_text: template.preview_text,
    variables: template.variables,
    default_values: template.default_values,
    category: template.category,
    tags: template.tags,
    exported_at: new Date().toISOString(),
    version: template.version
  };
  
  const blob = new Blob([JSON.stringify(exportData, null, 2)], {
    type: 'application/json'
  });
  
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${template.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_template.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const validateImportedTemplate = (templateData) => {
  const errors = [];
  const requiredFields = ['name', 'template_type', 'subject', 'html_content'];
  
  requiredFields.forEach(field => {
    if (!templateData[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  });
  
  if (templateData.template_type && !['transactional', 'marketing', 'system', 'notification'].includes(templateData.template_type)) {
    errors.push('Invalid template type');
  }
  
  if (templateData.html_content) {
    const contentErrors = validateTemplateContent(templateData.html_content);
    errors.push(...contentErrors);
  }
  
  if (templateData.subject) {
    const subjectErrors = validateSubjectLine(templateData.subject);
    errors.push(...subjectErrors);
  }
  
  return errors;
};

// Device preview functions
export const getDevicePreviewStyles = (deviceType) => {
  const styles = {
    desktop: {
      width: '100%',
      maxWidth: '600px',
      margin: '0 auto'
    },
    tablet: {
      width: '100%',
      maxWidth: '480px',
      margin: '0 auto'
    },
    mobile: {
      width: '100%',
      maxWidth: '320px',
      margin: '0 auto'
    }
  };
  
  return styles[deviceType] || styles.desktop;
};

// Template search and filtering
export const filterTemplates = (templates, filters) => {
  return templates.filter(template => {
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const matchesSearch = 
        template.name.toLowerCase().includes(searchTerm) ||
        template.description?.toLowerCase().includes(searchTerm) ||
        template.tags?.some(tag => tag.toLowerCase().includes(searchTerm));
      
      if (!matchesSearch) return false;
    }
    
    // Type filter
    if (filters.type && template.template_type !== filters.type) {
      return false;
    }
    
    // Status filter
    if (filters.status && template.status !== filters.status) {
      return false;
    }
    
    // Category filter
    if (filters.category && template.category !== filters.category) {
      return false;
    }
    
    return true;
  });
};

export const sortTemplates = (templates, sortBy, sortOrder = 'desc') => {
  return [...templates].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];
    
    // Handle date fields
    if (sortBy.includes('_at')) {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }
    
    // Handle string fields
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }
    
    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
};

// Error handling
export const getErrorMessage = (error) => {
  if (error.response?.data?.detail) {
    return error.response.data.detail;
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

// Rate limiting helpers
export const isRateLimited = (error) => {
  return error.response?.status === 429;
};

export const getRateLimitRetryAfter = (error) => {
  return error.response?.headers?.['retry-after'] || 60;
};

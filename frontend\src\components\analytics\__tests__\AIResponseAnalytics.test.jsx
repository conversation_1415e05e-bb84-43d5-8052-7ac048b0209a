/**
 * Tests for AIResponseAnalytics component
 */
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AIResponseAnalytics from '../AIResponseAnalytics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn()
  }))
}));

vi.mock('../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock GlassmorphicCard
vi.mock('../common/GlassmorphicCard', () => ({
  default: ({ children, ...props }) => <div {...props}>{children}</div>
}));

// Mock Recharts components
vi.mock('recharts', () => ({
  BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>,
  LineChart: ({ children }) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />
}));

describe('AIResponseAnalytics', () => {
  const mockApi = require('../../api').default;
  const mockNotification = vi.fn();

  const mockData = {
    overall_metrics: {
      total_suggestions: 1500,
      approved_suggestions: 1200,
      average_rating: 4.2,
      average_response_time: 180
    },
    platform_metrics: {
      facebook: {
        total_suggestions: 500,
        approved_suggestions: 400,
        average_rating: 4.1
      },
      twitter: {
        total_suggestions: 600,
        approved_suggestions: 480,
        average_rating: 4.3
      }
    },
    time_series_data: {
      dates: ['2023-01-01', '2023-01-02', '2023-01-03'],
      approval_rate: [80, 85, 82],
      average_rating: [4.1, 4.2, 4.0],
      response_time: [200, 180, 190]
    },
    improvement_suggestions: [
      'Consider more personalized responses',
      'Improve response timing for better engagement',
      'Focus on platform-specific content optimization'
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue({ data: mockData });

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue({
      showErrorNotification: mockNotification
    });
  });

  test('renders AI response analytics dashboard', async () => {
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    expect(screen.getByText('AI Response Analytics')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('1500')).toBeInTheDocument(); // Total suggestions
      expect(screen.getByText('80%')).toBeInTheDocument(); // Approval rate
      expect(screen.getByText('4.2')).toBeInTheDocument(); // Average rating
      expect(screen.getByText('3m')).toBeInTheDocument(); // Response time in minutes
    });
  });

  test('shows loading state initially', () => {
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('displays time range and platform filters', async () => {
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
      expect(screen.getByLabelText('Platform')).toBeInTheDocument();
    });
  });

  test('handles time range change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    // Clear mock calls from initial load
    vi.clearAllMocks();

    // Change time range
    const timeRangeSelect = screen.getByLabelText('Time Range');
    await user.click(timeRangeSelect);
    
    await waitFor(() => {
      expect(screen.getByText('Last 7 days')).toBeInTheDocument();
    });
    
    await user.click(screen.getByText('Last 7 days'));

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 7, platform: undefined }
      });
    });
  });

  test('handles platform filter change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Platform')).toBeInTheDocument();
    });

    // Clear mock calls from initial load
    vi.clearAllMocks();

    // Change platform
    const platformSelect = screen.getByLabelText('Platform');
    await user.click(platformSelect);
    
    await waitFor(() => {
      expect(screen.getByText('Facebook')).toBeInTheDocument();
    });
    
    await user.click(screen.getByText('Facebook'));

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 30, platform: 'facebook' }
      });
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 30, platform: undefined }
      });
    });

    // Clear mock calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh analytics data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/ai-feedback/analytics', {
        params: { days: 30, platform: undefined }
      });
    });
  });

  test('displays charts correctly', async () => {
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    });
  });

  test('shows improvement suggestions', async () => {
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Improvement Suggestions')).toBeInTheDocument();
      expect(screen.getByText('Consider more personalized responses')).toBeInTheDocument();
      expect(screen.getByText('Improve response timing for better engagement')).toBeInTheDocument();
    });
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Overview')).toBeInTheDocument();
    });

    // Click on Performance Metrics tab
    await user.click(screen.getByText('Performance Metrics'));

    expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
    expect(screen.getByText(/Detailed performance metrics will be displayed here/)).toBeInTheDocument();

    // Click on Platform Comparison tab
    await user.click(screen.getByText('Platform Comparison'));

    expect(screen.getByText('Platform Comparison')).toBeInTheDocument();
    expect(screen.getByText(/Comparative analysis across different social media platforms/)).toBeInTheDocument();

    // Click on Response Quality tab
    await user.click(screen.getByText('Response Quality'));

    expect(screen.getByText('Response Quality Analysis')).toBeInTheDocument();
    expect(screen.getByText(/Quality metrics and analysis of AI-generated responses/)).toBeInTheDocument();
  });

  test('handles export menu', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Export analytics data')).toBeInTheDocument();
    });

    // Open export menu
    await user.click(screen.getByLabelText('Export analytics data'));

    await waitFor(() => {
      expect(screen.getByText('Export as CSV')).toBeInTheDocument();
      expect(screen.getByText('Export as PDF')).toBeInTheDocument();
    });

    // Click CSV export
    await user.click(screen.getByText('Export as CSV'));

    expect(consoleSpy).toHaveBeenCalledWith('Exporting CSV...');

    consoleSpy.mockRestore();
  });

  test('handles API error gracefully', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockNotification).toHaveBeenCalledWith('Failed to load analytics data. Please try again.');
    });
  });

  test('works with external data prop', () => {
    render(
      <TestWrapper>
        <AIResponseAnalytics data={mockData} />
      </TestWrapper>
    );

    expect(screen.getByText('1500')).toBeInTheDocument();
    expect(screen.getByText('80%')).toBeInTheDocument();
    expect(screen.getByText('4.2')).toBeInTheDocument();
  });

  test('works with external loading prop', () => {
    render(
      <TestWrapper>
        <AIResponseAnalytics loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('calls onRefresh prop when provided', async () => {
    const mockOnRefresh = vi.fn();
    
    render(
      <TestWrapper>
        <AIResponseAnalytics onRefresh={mockOnRefresh} loading={false} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockOnRefresh).toHaveBeenCalledWith(30, 'all');
    });
  });

  test('respects platform prop', () => {
    render(
      <TestWrapper>
        <AIResponseAnalytics platform="facebook" data={mockData} />
      </TestWrapper>
    );

    const platformSelect = screen.getByDisplayValue('facebook');
    expect(platformSelect).toBeInTheDocument();
  });

  test('handles missing data gracefully', async () => {
    mockApi.get.mockResolvedValue({ data: {} });

    render(
      <TestWrapper>
        <AIResponseAnalytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('0')).toBeInTheDocument(); // Should show 0 for missing metrics
      expect(screen.getByText('0%')).toBeInTheDocument(); // Should show 0% for missing rates
    });
  });
});

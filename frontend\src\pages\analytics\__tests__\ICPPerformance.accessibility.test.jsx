import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ICPPerformance from '../ICPPerformance';
import { NotificationProvider } from '../../../contexts/NotificationContext';
import * as icpPerformanceApi from '../../../api/icp-performance';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock the API module
jest.mock('../../../api/icp-performance');

// Mock D3 and ResizeObserver
jest.mock('d3', () => ({
  select: jest.fn(() => ({
    selectAll: jest.fn(() => ({
      remove: jest.fn()
    })),
    append: jest.fn(() => ({
      attr: jest.fn(() => ({
        attr: jest.fn(() => ({
          append: jest.fn(() => ({
            attr: jest.fn(() => ({}))
          }))
        }))
      }))
    }))
  })),
  scaleBand: jest.fn(() => ({
    range: jest.fn(() => ({
      domain: jest.fn(() => ({
        padding: jest.fn(() => ({}))
      }))
    }))
  })),
  scaleLinear: jest.fn(() => ({
    domain: jest.fn(() => ({
      range: jest.fn(() => ({}))
    }))
  })),
  max: jest.fn(() => 100),
  axisBottom: jest.fn(() => ({})),
  axisLeft: jest.fn(() => ({})),
  easeBackOut: jest.fn()
}));

global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock data
const mockICPPerformances = [
  {
    icp_id: '1',
    icp_name: 'Tech Enthusiasts',
    avg_engagement_rate: 15.5,
    total_content: 25
  },
  {
    icp_id: '2',
    icp_name: 'Business Professionals',
    avg_engagement_rate: 12.3,
    total_content: 18
  }
];

const mockTopPerformingICPs = [
  {
    icp_id: '1',
    icp_name: 'Tech Enthusiasts',
    avg_engagement_rate: 15.5,
    total_content: 25
  }
];

const mockComparisonData = {
  icps: mockICPPerformances,
  best_performing_icp_name: 'Tech Enthusiasts',
  performance_gap: {
    avg_engagement_rate: 3.2
  },
  recommendations: ['Focus on tech content', 'Post during peak hours']
};

const mockRecommendations = {
  icp_name: 'Tech Enthusiasts',
  optimal_content_types: [
    { type: 'Video', engagement_rate: 18.5 }
  ],
  optimal_platforms: [
    { platform: 'LinkedIn', engagement_rate: 16.2 }
  ],
  optimal_posting_times: {
    LinkedIn: ['9:00 AM', '2:00 PM']
  },
  recommendations: [
    { value: 'Post more videos', reason: 'Higher engagement' }
  ]
};

// Test wrapper
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return (
    <ThemeProvider theme={theme}>
      <NotificationProvider>
        {children}
      </NotificationProvider>
    </ThemeProvider>
  );
};

describe('ICPPerformance Accessibility Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    icpPerformanceApi.getAllICPPerformances.mockResolvedValue(mockICPPerformances);
    icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(mockTopPerformingICPs);
    icpPerformanceApi.compareICPs.mockResolvedValue(mockComparisonData);
    icpPerformanceApi.analyzeICPPerformance.mockResolvedValue(mockICPPerformances[0]);
    icpPerformanceApi.getICPRecommendations.mockResolvedValue(mockRecommendations);
    icpPerformanceApi.exportICPPerformanceCSV.mockResolvedValue(new Blob(['test'], { type: 'text/csv' }));
    icpPerformanceApi.exportICPComparisonCSV.mockResolvedValue(new Blob(['test'], { type: 'text/csv' }));
  });

  describe('WCAG 2.1 AA Compliance', () => {
    test('should not have any accessibility violations on initial render', async () => {
      const { container } = render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should not have accessibility violations in Compare tab', async () => {
      const user = userEvent.setup();
      
      const { container } = render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      await waitFor(() => {
        expect(screen.getByText(/compare icp performance/i)).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should not have accessibility violations in Recommendations tab', async () => {
      const user = userEvent.setup();
      
      const { container } = render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Switch to Recommendations tab
      const recommendationsTab = screen.getByRole('tab', { name: /recommendations/i });
      await user.click(recommendationsTab);

      await waitFor(() => {
        expect(screen.getByText(/get content recommendations for icp/i)).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Semantic HTML and ARIA', () => {
    test('uses proper heading hierarchy', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Check main heading
      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('ICP Performance Analytics');

      // Check section headings
      const sectionHeadings = screen.getAllByRole('heading', { level: 2 });
      expect(sectionHeadings.length).toBeGreaterThan(0);
    });

    test('has proper landmark roles', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Check for main landmark
      expect(screen.getByRole('main')).toBeInTheDocument();

      // Check for navigation (tabs)
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    test('provides proper ARIA labels for interactive elements', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Check buttons have proper labels
      expect(screen.getByLabelText(/refresh all data/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/export performance data to csv/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/enable auto-refresh/i)).toBeInTheDocument();
    });

    test('uses proper ARIA live regions for dynamic content', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      // Check for status/live regions
      const statusElements = screen.getAllByRole('status');
      expect(statusElements.length).toBeGreaterThan(0);

      // Check aria-live attributes
      const liveElements = document.querySelectorAll('[aria-live]');
      expect(liveElements.length).toBeGreaterThan(0);
    });
  });

  describe('Keyboard Navigation', () => {
    test('supports tab navigation through all interactive elements', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Test tab navigation
      await user.tab();
      expect(document.activeElement).toHaveAttribute('role', 'tab');

      await user.tab();
      expect(document.activeElement).toHaveAttribute('role', 'tab');
    });

    test('supports arrow key navigation in tabs', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Focus on first tab
      const overviewTab = screen.getByRole('tab', { name: /overview/i });
      overviewTab.focus();

      // Use arrow keys to navigate
      await user.keyboard('{ArrowRight}');
      expect(document.activeElement).toHaveAttribute('aria-controls', 'tabpanel-compare');
    });

    test('supports Enter and Space key activation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Focus on Compare tab and activate with Enter
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      compareTab.focus();
      
      await user.keyboard('{Enter}');
      
      await waitFor(() => {
        expect(screen.getByText(/compare icp performance/i)).toBeInTheDocument();
      });
    });
  });

  describe('Screen Reader Support', () => {
    test('provides descriptive text for data visualizations', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      // Check for chart description
      await waitFor(() => {
        expect(screen.getByLabelText(/bar chart comparing engagement rates/i)).toBeInTheDocument();
      });

      expect(screen.getByText(/interactive bar chart showing engagement rate comparison/i)).toBeInTheDocument();
    });

    test('provides status updates for loading states', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      // Check for loading status
      expect(screen.getByLabelText(/loading top performing icps/i)).toBeInTheDocument();
    });

    test('announces form validation errors', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      // Try to compare without selecting ICPs
      await waitFor(() => {
        const compareButton = screen.getByLabelText(/compare 0 selected icps/i);
        expect(compareButton).toBeDisabled();
      });
    });
  });

  describe('Focus Management', () => {
    test('manages focus properly when switching tabs', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      // Focus should be managed properly
      expect(compareTab).toHaveAttribute('aria-selected', 'true');
    });

    test('provides visible focus indicators', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Tab to first interactive element
      await user.tab();
      
      // Check that focused element has visible focus indicator
      const focusedElement = document.activeElement;
      const computedStyle = window.getComputedStyle(focusedElement);
      
      // Material-UI should provide focus indicators
      expect(focusedElement).toHaveAttribute('tabindex');
    });
  });

  describe('Color and Contrast', () => {
    test('does not rely solely on color to convey information', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Check that ranking information is conveyed through text, not just color
      const rankChips = screen.getAllByLabelText(/rank \d+/i);
      expect(rankChips.length).toBeGreaterThan(0);
    });
  });

  describe('Responsive Design Accessibility', () => {
    test('maintains accessibility on mobile viewports', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const { container } = render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('maintains minimum touch target sizes', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Check that interactive elements meet minimum size requirements (44px)
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        const rect = button.getBoundingClientRect();
        expect(Math.min(rect.width, rect.height)).toBeGreaterThanOrEqual(44);
      });
    });
  });
});

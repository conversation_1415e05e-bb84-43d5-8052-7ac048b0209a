/**
 * Tests for ICPPerformanceMetrics component
 */
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import ICPPerformanceMetrics from '../ICPPerformanceMetrics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock the hooks and API
vi.mock('../../hooks/useAdvancedToast', () => ({
  useAdvancedToast: vi.fn(() => ({
    showSuccess: vi.fn(),
    showError: vi.fn()
  }))
}));

vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showSuccessNotification: vi.fn(),
    showErrorNotification: vi.fn(),
    showInfoNotification: vi.fn(),
    clearNotification: vi.fn()
  }))
}));

vi.mock('../../hooks/useKeyboardShortcut', () => ({
  default: vi.fn()
}));

vi.mock('../../hooks/useDebounce', () => ({
  default: vi.fn((value) => value)
}));

vi.mock('../../api/icp-performance', () => ({
  getAllICPPerformances: vi.fn(),
  getTopPerformingICPs: vi.fn(),
  compareICPs: vi.fn(),
  analyzeICPPerformance: vi.fn()
}));

// Mock D3
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      remove: vi.fn()
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({
        attr: vi.fn(() => ({
          append: vi.fn(() => ({
            attr: vi.fn(() => ({}))
          }))
        }))
      }))
    })),
    call: vi.fn(() => ({})),
    datum: vi.fn(() => ({
      attr: vi.fn(() => ({}))
    })),
    data: vi.fn(() => ({
      enter: vi.fn(() => ({
        append: vi.fn(() => ({
          attr: vi.fn(() => ({
            attr: vi.fn(() => ({
              attr: vi.fn(() => ({
                attr: vi.fn(() => ({
                  attr: vi.fn(() => ({}))
                }))
              }))
            }))
          })),
          text: vi.fn(() => ({})),
          style: vi.fn(() => ({})),
          on: vi.fn(() => ({})),
          transition: vi.fn(() => ({
            duration: vi.fn(() => ({
              attr: vi.fn(() => ({}))
            }))
          }))
        }))
      }))
    }))
  })),
  scaleBand: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({
        padding: vi.fn(() => ({
          bandwidth: vi.fn(() => 50)
        }))
      }))
    }))
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({}))
    }))
  })),
  scaleTime: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({}))
    }))
  })),
  scaleOrdinal: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({}))
    }))
  })),
  pie: vi.fn(() => ({
    value: vi.fn(() => ({
      sort: vi.fn(() => ({}))
    }))
  })),
  arc: vi.fn(() => ({
    innerRadius: vi.fn(() => ({
      outerRadius: vi.fn(() => ({}))
    }))
  })),
  line: vi.fn(() => ({
    x: vi.fn(() => ({
      y: vi.fn(() => ({
        curve: vi.fn(() => ({}))
      }))
    }))
  })),
  max: vi.fn(() => 100),
  min: vi.fn(() => 0),
  extent: vi.fn(() => [new Date(), new Date()]),
  axisBottom: vi.fn(() => ({})),
  axisLeft: vi.fn(() => ({})),
  timeFormat: vi.fn(() => ({})),
  curveMonotoneX: {},
  schemeCategory10: ['#1f77b4', '#ff7f0e', '#2ca02c']
}));

// Mock file-saver
vi.mock('file-saver', () => ({
  saveAs: vi.fn()
}));

// Mock jsPDF
vi.mock('jspdf', () => ({
  jsPDF: vi.fn(() => ({
    setFontSize: vi.fn(),
    text: vi.fn(),
    autoTable: vi.fn(),
    addPage: vi.fn(),
    addImage: vi.fn(),
    save: vi.fn()
  }))
}));

describe('ICPPerformanceMetrics', () => {
  const mockData = {
    performances: [
      {
        icp_id: 'icp-1',
        icp_name: 'Tech Startups',
        avg_engagement_rate: 7.25,
        avg_views: 15000,
        avg_likes: 850,
        avg_comments: 120,
        avg_shares: 45,
        avg_clicks: 320,
        avg_reach: 25000,
        total_content: 25,
        best_performing_platform: 'LinkedIn',
        best_performing_content_type: 'Article',
        best_performing_time: 'Morning'
      },
      {
        icp_id: 'icp-2',
        icp_name: 'E-commerce Brands',
        avg_engagement_rate: 5.8,
        avg_views: 12000,
        avg_likes: 680,
        avg_comments: 95,
        avg_shares: 32,
        avg_clicks: 280,
        avg_reach: 20000,
        total_content: 18,
        best_performing_platform: 'Instagram',
        best_performing_content_type: 'Video',
        best_performing_time: 'Evening'
      }
    ],
    top_performing: [
      {
        icp_id: 'icp-1',
        icp_name: 'Tech Startups',
        avg_engagement_rate: 7.25,
        avg_views: 15000,
        avg_likes: 850,
        avg_comments: 120,
        avg_shares: 45,
        total_content: 25
      }
    ]
  };

  const mockCallbacks = {
    onRefresh: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock clientWidth for chart containers
    Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
      configurable: true,
      value: 800
    });

    // Mock document.createElement for export functionality
    global.document.createElement = vi.fn(() => ({
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    }));

    global.document.body.appendChild = vi.fn();
    global.document.body.removeChild = vi.fn();
  });

  test('renders ICP performance metrics dashboard', () => {
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('ICP Performance Metrics')).toBeInTheDocument();
    expect(screen.getByText('Export')).toBeInTheDocument();
    expect(screen.getByText('Refresh')).toBeInTheDocument();
  });

  test('displays loading state correctly', () => {
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={null} loading={true} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('ICP Performance Metrics')).toBeInTheDocument();
    // Export and Refresh buttons should be disabled during loading
    expect(screen.getByText('Export')).toBeDisabled();
  });

  test('displays ICP performance cards when data is available', () => {
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Should display ICP performance cards
    expect(screen.getByText('Tech Startups')).toBeInTheDocument();
    expect(screen.getByText('E-commerce Brands')).toBeInTheDocument();
  });

  test('handles search functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText('Search ICPs...');
    await user.type(searchInput, 'Tech');

    // Should filter results based on search
    expect(searchInput).toHaveValue('Tech');
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);

    expect(mockCallbacks.onRefresh).toHaveBeenCalled();
  });

  test('handles export dialog functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const exportButton = screen.getByText('Export');
    await user.click(exportButton);

    // Should open export dialog
    await waitFor(() => {
      expect(screen.getByText('Export ICP Performance Data')).toBeInTheDocument();
    });
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Should have tabs for different views
    const overviewTab = screen.getByText('Overview');
    const compareTab = screen.getByText('Compare');
    const trendsTab = screen.getByText('Trends');

    expect(overviewTab).toBeInTheDocument();
    expect(compareTab).toBeInTheDocument();
    expect(trendsTab).toBeInTheDocument();

    // Click on Compare tab
    await user.click(compareTab);
    
    // Should show comparison interface
    expect(screen.getByText('Select ICPs to Compare')).toBeInTheDocument();
  });

  test('handles auto-refresh toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const autoRefreshButton = screen.getByLabelText(/auto-refresh/i);
    await user.click(autoRefreshButton);

    // Should toggle auto-refresh state
    expect(autoRefreshButton).toBeInTheDocument();
  });

  test('handles filter functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const filtersButton = screen.getByText('Filters');
    await user.click(filtersButton);

    // Should show filter options
    expect(filtersButton).toBeInTheDocument();
  });

  test('displays empty state when no data available', () => {
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={{ performances: [], top_performing: [] }} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    expect(screen.getByText('ICP Performance Metrics')).toBeInTheDocument();
    // Export button should be disabled when no data
    expect(screen.getByText('Export')).toBeDisabled();
  });

  test('handles comparison functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Navigate to Compare tab
    const compareTab = screen.getByText('Compare');
    await user.click(compareTab);

    // Should show ICP selection for comparison
    expect(screen.getByText('Select ICPs to Compare')).toBeInTheDocument();
  });

  test('handles keyboard shortcuts', () => {
    const useKeyboardShortcut = require('../../hooks/useKeyboardShortcut').default;
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    // Should register keyboard shortcuts
    expect(useKeyboardShortcut).toHaveBeenCalledWith('r', expect.any(Function));
    expect(useKeyboardShortcut).toHaveBeenCalledWith('c', expect.any(Function));
    expect(useKeyboardShortcut).toHaveBeenCalledWith('o', expect.any(Function));
    expect(useKeyboardShortcut).toHaveBeenCalledWith('t', expect.any(Function));
    expect(useKeyboardShortcut).toHaveBeenCalledWith('e', expect.any(Function));
    expect(useKeyboardShortcut).toHaveBeenCalledWith('f', expect.any(Function));
  });

  test('handles search input clearing', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const searchInput = screen.getByPlaceholderText('Search ICPs...');
    await user.type(searchInput, 'Tech');

    // Should show clear button when there's text
    const clearButton = screen.getByRole('button', { name: /clear search/i });
    await user.click(clearButton);

    expect(searchInput).toHaveValue('');
  });

  test('handles advanced filters toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ICPPerformanceMetrics data={mockData} loading={false} onRefresh={mockCallbacks.onRefresh} />
      </TestWrapper>
    );

    const advancedButton = screen.getByText('Advanced');
    await user.click(advancedButton);

    // Should toggle advanced filters
    expect(advancedButton).toBeInTheDocument();
  });
});

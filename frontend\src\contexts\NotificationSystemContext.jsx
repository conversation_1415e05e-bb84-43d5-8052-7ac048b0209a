/**
 * Notification System Context
 * Production-ready notification system with push notifications, service worker integration
 * Comprehensive error handling, retry logic, and monitoring capabilities
 @since 2024-1-1 to 2025-25-7
*/

import {
  createContext,
  useState,
  useEffect,
  useCallback,
  useContext,
  useRef,
} from "react";
import { useAuth } from "./AuthContext";
import api from "../api";

// Configuration constants
const CONFIG = {
  // Service Worker settings
  SERVICE_WORKER_PATH: "/service-worker.js",

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second

  // Polling settings
  NOTIFICATION_POLL_INTERVAL: 60000, // 1 minute

  // Push notification settings
  VAPID_KEY_CACHE_DURATION: 3600000, // 1 hour

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[NotificationSystem] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[NotificationSystem] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Notification System Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[NotificationSystem] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Notification System Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[NotificationSystem] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Notification System Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const NotificationSystemContext = createContext();

// Custom hook to use notification system context
// eslint-disable-next-line react-refresh/only-export-components
export const useNotificationSystem = () => {
  const context = useContext(NotificationSystemContext);
  if (!context) {
    throw new Error(
      "useNotificationSystem must be used within a NotificationSystemProvider"
    );
  }
  return context;
};

export const NotificationSystemProvider = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pushSupported, setPushSupported] = useState(false);
  const [pushEnabled, setPushEnabled] = useState(false);
  const [pushSubscription, setPushSubscription] = useState(null);
  const [vapidPublicKey, setVapidPublicKey] = useState(null);
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  // Refs for cleanup and state management
  const pollIntervalRef = useRef(null);
  const vapidKeyCacheRef = useRef({ key: null, timestamp: null });

  // Check if push notifications are supported
  useEffect(() => {
    const checkPushSupport = () => {
      const supported =
        "serviceWorker" in navigator &&
        "PushManager" in window &&
        "Notification" in window;

      setPushSupported(supported);
      logger.info("Push notification support checked", {
        supported,
        serviceWorker: "serviceWorker" in navigator,
        pushManager: "PushManager" in window,
        notification: "Notification" in window
      });
    };

    checkPushSupport();
  }, []);

  // Register service worker
  useEffect(() => {
    if (!pushSupported || !isAuthenticated) return;

    const registerServiceWorker = async () => {
      try {
        logger.debug("Registering service worker", { path: CONFIG.SERVICE_WORKER_PATH });

        const registration = await navigator.serviceWorker.register(
          CONFIG.SERVICE_WORKER_PATH
        );

        setServiceWorkerRegistration(registration);
        logger.info("Service Worker registered successfully", {
          scope: registration.scope,
          active: !!registration.active
        });

        // Check if push is already enabled
        const subscription = await registration.pushManager.getSubscription();
        setPushSubscription(subscription);
        setPushEnabled(!!subscription);

        logger.debug("Push subscription status checked", {
          hasSubscription: !!subscription,
          endpoint: subscription?.endpoint?.substring(0, 50) + '...' || null
        });
      } catch (error) {
        logger.error("Service Worker registration failed", error);
        setError("Failed to register service worker");
      }
    };

    registerServiceWorker();
  }, [pushSupported, isAuthenticated]);

  // Get VAPID public key with caching
  useEffect(() => {
    if (!pushSupported || !isAuthenticated) return;

    const getVapidKey = async () => {
      try {
        // Check cache first
        const now = Date.now();
        const cached = vapidKeyCacheRef.current;

        if (cached.key && cached.timestamp &&
            (now - cached.timestamp) < CONFIG.VAPID_KEY_CACHE_DURATION) {
          logger.debug("Using cached VAPID key");
          setVapidPublicKey(cached.key);
          return;
        }

        logger.debug("Fetching VAPID public key from server");
        const response = await api.get("/api/notifications/vapid-public-key");
        const vapidKey = response.data.vapid_public_key;

        setVapidPublicKey(vapidKey);

        // Update cache
        vapidKeyCacheRef.current = {
          key: vapidKey,
          timestamp: now
        };

        logger.info("VAPID public key retrieved and cached", {
          keyLength: vapidKey?.length || 0
        });
      } catch (error) {
        logger.error("Failed to get VAPID public key", error);
        setError("Failed to get push notification key");
      }
    };

    getVapidKey();
  }, [pushSupported, isAuthenticated]);

  // Load notifications with retry logic
  const loadNotifications = useCallback(
    async (unreadOnly = false, retryAttempt = 0) => {
      if (!isAuthenticated) {
        logger.debug("Skipping notification load - user not authenticated");
        return;
      }

      setLoading(true);
      setError(null);

      logger.debug("Loading notifications", { unreadOnly, retryAttempt });

      try {
        const response = await api.get("/api/notifications", {
          params: { unread_only: unreadOnly },
        });

        const notifications = response.data || [];
        setNotifications(notifications);

        // Count unread notifications
        const unreadCount = notifications.filter(
          (notification) => !notification.is_read
        ).length;
        setUnreadCount(unreadCount);

        // Reset retry count on success
        setRetryCount(0);

        logger.info("Notifications loaded successfully", {
          total: notifications.length,
          unread: unreadCount,
          unreadOnly
        });
      } catch (error) {
        logger.error("Failed to load notifications", error);

        // Retry logic
        if (retryAttempt < CONFIG.MAX_RETRIES) {
          const nextRetry = retryAttempt + 1;
          setRetryCount(nextRetry);

          logger.warn("Retrying notification load", {
            attempt: nextRetry,
            maxRetries: CONFIG.MAX_RETRIES
          });

          setTimeout(() => {
            loadNotifications(unreadOnly, nextRetry);
          }, CONFIG.RETRY_DELAY * nextRetry);
        } else {
          setError("Failed to load notifications");
          setRetryCount(0);
        }
      } finally {
        setLoading(false);
      }
    },
    [isAuthenticated]
  );

  // Load notifications on mount and when auth state changes
  useEffect(() => {
    if (isAuthenticated) {
      loadNotifications();

      // Set up polling for new notifications
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }

      pollIntervalRef.current = setInterval(() => {
        logger.debug("Polling for new notifications");
        loadNotifications();
      }, CONFIG.NOTIFICATION_POLL_INTERVAL);

      logger.info("Notification polling started", {
        interval: CONFIG.NOTIFICATION_POLL_INTERVAL
      });
    } else {
      // Clear polling when not authenticated
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
        logger.debug("Notification polling stopped - user not authenticated");
      }
    }

    // Cleanup on unmount
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
    };
  }, [isAuthenticated, loadNotifications]);

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    if (!isAuthenticated) {
      logger.warn("Cannot mark notification as read - user not authenticated");
      return false;
    }

    if (!notificationId) {
      logger.warn("Cannot mark notification as read - no notification ID provided");
      return false;
    }

    try {
      logger.debug("Marking notification as read", { notificationId });
      await api.post(`/api/notifications/${notificationId}/read`);

      // Update local state
      setNotifications((prevNotifications) =>
        prevNotifications.map((notification) =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );

      // Update unread count
      setUnreadCount((prevCount) => Math.max(0, prevCount - 1));

      logger.info("Notification marked as read successfully", { notificationId });
      return true;
    } catch (error) {
      logger.error("Failed to mark notification as read", error);
      return false;
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!isAuthenticated) return false;

    try {
      await api.post("/api/notifications/read-all");

      // Update local state
      setNotifications((prevNotifications) =>
        prevNotifications.map((notification) => ({
          ...notification,
          is_read: true,
        }))
      );

      // Update unread count
      setUnreadCount(0);

      logger.info("All notifications marked as read successfully");
      return true;
    } catch (error) {
      logger.error("Failed to mark all notifications as read", error);
      return false;
    }
  };

  // Delete notification
  const deleteNotification = async (notificationId) => {
    if (!isAuthenticated) return false;

    try {
      await api.delete(`/api/notifications/${notificationId}`);

      // Update local state
      const updatedNotifications = notifications.filter(
        (notification) => notification.id !== notificationId
      );
      setNotifications(updatedNotifications);

      // Update unread count if needed
      const deletedNotification = notifications.find(
        (n) => n.id === notificationId
      );
      if (deletedNotification && !deletedNotification.is_read) {
        setUnreadCount((prevCount) => Math.max(0, prevCount - 1));
      }

      logger.info("Notification deleted successfully", { notificationId });
      return true;
    } catch (error) {
      logger.error("Failed to delete notification", error);
      return false;
    }
  };

  // Enable push notifications
  const enablePushNotifications = async () => {
    if (!pushSupported || !isAuthenticated || !vapidPublicKey) {
      return false;
    }

    try {
      // Request permission
      const permission = await Notification.requestPermission();
      if (permission !== "granted") {
        logger.warn("Notification permission denied by user", { permission });
        return false;
      }

      logger.info("Notification permission granted", { permission });

      // Get service worker registration
      const registration = await navigator.serviceWorker.ready;

      // Convert VAPID key to Uint8Array
      const convertedVapidKey = urlBase64ToUint8Array(vapidPublicKey);

      // Subscribe to push notifications
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: convertedVapidKey,
      });

      // Send subscription to server
      await api.post("/api/notifications/push-subscription", {
        subscription_info: subscription.toJSON(),
        device_info: {
          userAgent: navigator.userAgent,
          platform: navigator.userAgentData?.platform || "unknown",
          language: navigator.language,
        },
      });

      // Update state
      setPushSubscription(subscription);
      setPushEnabled(true);

      logger.info("Push notifications enabled successfully");
      return true;
    } catch (error) {
      logger.error("Failed to enable push notifications", error);
      return false;
    }
  };

  // Disable push notifications
  const disablePushNotifications = async () => {
    if (!pushSupported || !isAuthenticated || !pushSubscription) {
      return false;
    }

    try {
      // Unsubscribe from push notifications
      await pushSubscription.unsubscribe();

      // Notify server
      await api.delete("/api/notifications/push-subscription", {
        data: { endpoint: pushSubscription.endpoint },
      });

      // Update state
      setPushSubscription(null);
      setPushEnabled(false);

      logger.info("Push notifications disabled successfully");
      return true;
    } catch (error) {
      logger.error("Failed to disable push notifications", error);
      return false;
    }
  };

  // Helper function to convert base64 to Uint8Array
  const urlBase64ToUint8Array = (base64String) => {
    const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, "+")
      .replace(/_/g, "/");

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }

    return outputArray;
  };

  // Enhanced context value with organized structure
  const contextValue = {
    // State data
    notifications,
    unreadCount,
    loading,
    error,
    retryCount,

    // Push notification state
    pushSupported,
    pushEnabled,
    pushSubscription,
    serviceWorkerRegistration,

    // Core functions
    loadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,

    // Push notification functions
    enablePushNotifications,
    disablePushNotifications,

    // Utility functions
    clearError: () => setError(null),
    refreshNotifications: () => loadNotifications(),

    // Helper functions
    hasNotifications: notifications.length > 0,
    hasUnreadNotifications: unreadCount > 0,
    getNotificationCount: () => notifications.length,
    getUnreadCount: () => unreadCount,
    getNotificationById: (id) => notifications.find(n => n.id === id),
    getUnreadNotifications: () => notifications.filter(n => !n.is_read),
    getReadNotifications: () => notifications.filter(n => n.is_read),

    // Bulk operations
    deleteAllNotifications: async () => {
      logger.debug("Deleting all notifications");
      const deletePromises = notifications.map(n => deleteNotification(n.id));
      const results = await Promise.allSettled(deletePromises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      logger.info("Bulk delete completed", {
        total: notifications.length,
        successful
      });
      return successful;
    },

    // Filter helpers
    getNotificationsByType: (type) => notifications.filter(n => n.type === type),
    getNotificationsByCategory: (category) => notifications.filter(n => n.category === category),

    // Push notification helpers
    isPushAvailable: pushSupported && vapidPublicKey,
    canEnablePush: pushSupported && !pushEnabled && vapidPublicKey,
    getPushSubscriptionInfo: () => ({
      endpoint: pushSubscription?.endpoint,
      keys: pushSubscription?.keys
    })
  };

  return (
    <NotificationSystemContext.Provider value={contextValue}>
      {children}
    </NotificationSystemContext.Provider>
  );
};

// Export the context
export { NotificationSystemContext };

// Default export for convenience
export default NotificationSystemProvider;

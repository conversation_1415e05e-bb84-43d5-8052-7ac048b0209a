#!/usr/bin/env python3
"""
Script to add @since annotation to all Python files in the backend directory.
@since 2024-1-1 to 2025-25-7
"""

import os
import re
from pathlib import Path

def add_since_annotation_to_python_file(file_path):
    """Add @since annotation to a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Skip if annotation already exists
        if '@since 2024-1-1 to 2025-25-7' in content:
            print(f"Skipping {file_path} - annotation already exists")
            return False

        lines = content.split('\n')
        if not lines:
            return False

        # Handle different file types
        if lines[0].startswith('#!'):
            # Shebang line exists
            if len(lines) > 1 and lines[1].startswith('"""'):
                # Docstring after shebang
                docstring_end = find_docstring_end(lines, 1)
                if docstring_end != -1:
                    lines[docstring_end] = lines[docstring_end].replace('"""', '@since 2024-1-1 to 2025-25-7\n"""')
                else:
                    lines.insert(1, '# @since 2024-1-1 to 2025-25-7')
            else:
                lines.insert(1, '# @since 2024-1-1 to 2025-25-7')
        elif lines[0].startswith('"""'):
            # Docstring at start
            docstring_end = find_docstring_end(lines, 0)
            if docstring_end != -1:
                lines[docstring_end] = lines[docstring_end].replace('"""', '@since 2024-1-1 to 2025-25-7\n"""')
            else:
                lines.insert(0, '# @since 2024-1-1 to 2025-25-7')
        elif lines[0].startswith('#'):
            # Comment at start
            lines.insert(0, '# @since 2024-1-1 to 2025-25-7')
        else:
            # No special start, add at beginning
            lines.insert(0, '# @since 2024-1-1 to 2025-25-7')

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))

        print(f"Added annotation to {file_path}")
        return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def add_since_annotation_to_js_file(file_path):
    """Add @since annotation to a JavaScript/TypeScript/JSX file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Skip if annotation already exists
        if '@since 2024-1-1 to 2025-25-7' in content:
            print(f"Skipping {file_path} - annotation already exists")
            return False

        lines = content.split('\n')
        if not lines:
            return False

        # Handle different file types
        if lines[0].startswith('/*'):
            # Multi-line comment at start
            comment_end = find_js_comment_end(lines, 0)
            if comment_end != -1:
                lines[comment_end] = lines[comment_end].replace('*/', '@since 2024-1-1 to 2025-25-7\n*/')
            else:
                lines.insert(0, '// @since 2024-1-1 to 2025-25-7')
        elif lines[0].startswith('//'):
            # Single line comment at start
            lines.insert(0, '// @since 2024-1-1 to 2025-25-7')
        else:
            # No comment at start, add at beginning
            lines.insert(0, '// @since 2024-1-1 to 2025-25-7')

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))

        print(f"Added annotation to {file_path}")
        return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def add_since_annotation_to_md_file(file_path):
    """Add @since annotation to a Markdown file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Skip if annotation already exists
        if '@since 2024-1-1 to 2025-25-7' in content:
            print(f"Skipping {file_path} - annotation already exists")
            return False

        lines = content.split('\n')
        if not lines:
            return False

        # For markdown files, add as HTML comment at the beginning
        lines.insert(0, '<!-- @since 2024-1-1 to 2025-25-7 -->')

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))

        print(f"Added annotation to {file_path}")
        return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def add_since_annotation_to_script_file(file_path):
    """Add @since annotation to a script file (.bat, .sh, .ps1, .js, .py)."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Skip if annotation already exists
        if '@since 2024-1-1 to 2025-25-7' in content:
            print(f"Skipping {file_path} - annotation already exists")
            return False

        lines = content.split('\n')
        if not lines:
            return False

        file_ext = Path(file_path).suffix.lower()

        # Determine comment style based on file extension
        if file_ext in ['.bat']:
            comment = 'REM @since 2024-1-1 to 2025-25-7'
        elif file_ext in ['.sh']:
            comment = '# @since 2024-1-1 to 2025-25-7'
        elif file_ext in ['.ps1']:
            comment = '# @since 2024-1-1 to 2025-25-7'
        elif file_ext in ['.js']:
            comment = '// @since 2024-1-1 to 2025-25-7'
        elif file_ext in ['.py']:
            comment = '# @since 2024-1-1 to 2025-25-7'
        else:
            # Default to # for unknown script types
            comment = '# @since 2024-1-1 to 2025-25-7'

        # Handle shebang lines - insert after shebang if present
        if lines[0].startswith('#!'):
            lines.insert(1, comment)
        else:
            lines.insert(0, comment)

        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))

        print(f"Added annotation to {file_path}")
        return True

    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def find_docstring_end(lines, start_idx):
    """Find the end of a docstring."""
    if start_idx >= len(lines):
        return -1

    # Look for closing """
    for i in range(start_idx, len(lines)):
        if '"""' in lines[i] and i != start_idx:
            return i
    return -1

def find_js_comment_end(lines, start_idx):
    """Find the end of a JavaScript multi-line comment."""
    if start_idx >= len(lines):
        return -1

    # Look for closing */
    for i in range(start_idx, len(lines)):
        if '*/' in lines[i]:
            return i
    return -1

def process_directory(directory, file_extensions=None, processor_func=None):
    """Process all files with specified extensions in a directory recursively."""
    directory = Path(directory)

    if file_extensions is None:
        file_extensions = ['*.py']

    if processor_func is None:
        processor_func = add_since_annotation_to_python_file

    all_files = []
    for ext in file_extensions:
        all_files.extend(list(directory.rglob(ext)))

    processed = 0
    for file_path in all_files:
        # Skip __pycache__ and node_modules directories
        if '__pycache__' in str(file_path) or 'node_modules' in str(file_path):
            continue

        if processor_func(file_path):
            processed += 1

    print(f"Processed {processed} files in {directory}")

if __name__ == "__main__":
    # Process backend directory
    backend_dir = Path("backend")
    if backend_dir.exists():
        process_directory(backend_dir, ['*.py'], add_since_annotation_to_python_file)
    else:
        print("Backend directory not found")

    # Process frontend directory
    frontend_src_dir = Path("frontend/src")
    if frontend_src_dir.exists():
        js_extensions = ['*.js', '*.jsx', '*.ts', '*.tsx']
        process_directory(frontend_src_dir, js_extensions, add_since_annotation_to_js_file)
    else:
        print("Frontend src directory not found")

    # Process admin-app directory
    admin_src_dir = Path("admin-app/src")
    if admin_src_dir.exists():
        js_extensions = ['*.js', '*.jsx', '*.ts', '*.tsx']
        process_directory(admin_src_dir, js_extensions, add_since_annotation_to_js_file)
    else:
        print("Admin-app src directory not found")

    # Process documentation files
    root_dir = Path(".")
    md_extensions = ['*.md']
    process_directory(root_dir, md_extensions, add_since_annotation_to_md_file)

    # Process script files
    script_extensions = ['*.bat', '*.sh', '*.ps1', '*.js', '*.py']

    # Process scripts directory
    scripts_dir = Path("scripts")
    if scripts_dir.exists():
        process_directory(scripts_dir, script_extensions, add_since_annotation_to_script_file)

    # Process root-level script files
    for pattern in script_extensions:
        for file_path in root_dir.glob(pattern):
            if file_path.is_file() and file_path.name != 'add_since_annotation.py':  # Skip our own script
                add_since_annotation_to_script_file(file_path)

    # Process frontend script files
    frontend_dir = Path("frontend")
    if frontend_dir.exists():
        for pattern in script_extensions:
            for file_path in frontend_dir.glob(pattern):
                if file_path.is_file():
                    add_since_annotation_to_script_file(file_path)

    # Process admin-app script files
    admin_app_dir = Path("admin-app")
    if admin_app_dir.exists():
        for pattern in script_extensions:
            for file_path in admin_app_dir.glob(pattern):
                if file_path.is_file():
                    add_since_annotation_to_script_file(file_path)

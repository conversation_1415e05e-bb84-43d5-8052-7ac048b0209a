"""
API routes for AI-powered product content generation.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, Optional, List
from pydantic import BaseModel

from app.core.security import get_current_active_user
from app.models.user import User
from app.services.content_generator import generate_product_content
from app.services.sentiment_analysis import sentiment_service
from app.api.dependencies.feature_access import requires_feature

router = APIRouter()

class ProductData(BaseModel):
    """Product data for content generation."""
    id: str
    name: str
    description: str = ""
    price: Optional[float] = None
    category: str = ""
    images: List[str] = []

class ProductContentRequest(BaseModel):
    """Request schema for product content generation."""
    product: ProductData
    platform: str = "instagram"
    content_type: str = "post"
    tone: str = "engaging"
    include_hashtags: bool = True
    include_cta: bool = True
    use_review_insights: bool = False

class ProductContentResponse(BaseModel):
    """Response schema for generated product content."""
    success: bool
    content: str
    hashtags: List[str] = []
    call_to_action: Optional[str] = None
    platform: str
    content_type: str
    tone: str
    product_id: str
    generated_at: str
    ai_insights: Dict[str, Any] = {}
    review_integration: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    fallback_content: Optional[str] = None

@router.get("", response_model=Dict[str, Any])
async def product_content_root():
    """
    Root endpoint for Product Content Generation API with system information.
    """
    return {
        "message": "AI Product Content Generation API",
        "version": "1.0.0",
        "features": [
            "ai_powered_content_generation",
            "platform_specific_optimization",
            "review_sentiment_integration",
            "hashtag_generation",
            "call_to_action_creation",
            "multi_tone_support"
        ],
        "supported_platforms": [
            "instagram",
            "facebook", 
            "twitter",
            "linkedin",
            "tiktok"
        ],
        "supported_tones": [
            "engaging",
            "professional",
            "casual",
            "humorous",
            "inspirational"
        ],
        "endpoints": [
            "/generate",
            "/bulk-generate",
            "/templates"
        ],
        "status": "active"
    }

@router.post("/generate", response_model=ProductContentResponse)
async def generate_content_for_product(
    request: ProductContentRequest,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(requires_feature("ai_content_generation"))
):
    """
    Generate AI-powered content for a specific product.
    
    Requires ai_content_generation feature access.
    """
    try:
        # Get review insights if requested
        review_insights = None
        if request.use_review_insights:
            try:
                # This would typically fetch from a review service
                # For now, we'll use a placeholder
                review_insights = {
                    "overall_sentiment": 0.7,
                    "key_themes": [
                        {"theme": "quality", "count": 15},
                        {"theme": "value_for_money", "count": 12}
                    ],
                    "common_praises": [
                        "Customers love the excellent build quality",
                        "Great value for the price point"
                    ],
                    "common_complaints": []
                }
            except Exception as e:
                # Continue without review insights if they fail to load
                pass
        
        # Generate content using the enhanced service
        result = await generate_product_content(
            product_data=request.product.model_dump(),
            platform=request.platform,
            content_type=request.content_type,
            tone=request.tone,
            include_hashtags=request.include_hashtags,
            include_cta=request.include_cta,
            review_insights=review_insights,
            user_id=str(current_user.id)
        )
        
        return ProductContentResponse(**result)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate product content: {str(e)}"
        )

@router.post("/bulk-generate", response_model=List[ProductContentResponse])
async def bulk_generate_content(
    products: List[ProductData],
    platform: str = "instagram",
    content_type: str = "post",
    tone: str = "engaging",
    include_hashtags: bool = True,
    include_cta: bool = True,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(requires_feature("bulk_content_generation"))
):
    """
    Generate content for multiple products in bulk.
    
    Requires bulk_content_generation feature access.
    """
    try:
        results = []
        
        for product in products[:10]:  # Limit to 10 products per request
            try:
                result = await generate_product_content(
                    product_data=product.model_dump(),
                    platform=platform,
                    content_type=content_type,
                    tone=tone,
                    include_hashtags=include_hashtags,
                    include_cta=include_cta,
                    user_id=str(current_user.id)
                )
                results.append(ProductContentResponse(**result))
            except Exception as e:
                # Add failed result
                results.append(ProductContentResponse(
                    success=False,
                    content="",
                    platform=platform,
                    content_type=content_type,
                    tone=tone,
                    product_id=product.id,
                    generated_at="",
                    error=str(e)
                ))
        
        return results
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate bulk content: {str(e)}"
        )

@router.get("/templates", response_model=Dict[str, Any])
async def get_content_templates(
    platform: Optional[str] = None,
    content_type: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get available content templates for product content generation.
    """
    try:
        templates = {
            "instagram": {
                "post": [
                    {
                        "name": "Product Showcase",
                        "description": "Highlight product features with engaging visuals",
                        "tone": "engaging",
                        "example": "✨ Introducing {product_name}! Perfect for {use_case}..."
                    },
                    {
                        "name": "Customer Story",
                        "description": "Share customer success stories and testimonials",
                        "tone": "authentic",
                        "example": "Real customers, real results! See how {product_name} changed..."
                    }
                ],
                "story": [
                    {
                        "name": "Behind the Scenes",
                        "description": "Show product creation or usage process",
                        "tone": "casual",
                        "example": "Ever wondered how {product_name} is made? 🤔"
                    }
                ]
            },
            "facebook": {
                "post": [
                    {
                        "name": "Detailed Review",
                        "description": "Comprehensive product information and benefits",
                        "tone": "informative",
                        "example": "Let's talk about {product_name} and why it's perfect for..."
                    }
                ]
            },
            "linkedin": {
                "post": [
                    {
                        "name": "Business Value",
                        "description": "Focus on ROI and business benefits",
                        "tone": "professional",
                        "example": "How {product_name} can transform your business operations..."
                    }
                ]
            }
        }
        
        # Filter by platform and content_type if specified
        if platform:
            templates = {platform: templates.get(platform, {})}
        
        if content_type:
            filtered_templates = {}
            for p, types in templates.items():
                if content_type in types:
                    filtered_templates[p] = {content_type: types[content_type]}
            templates = filtered_templates
        
        return {
            "templates": templates,
            "total_templates": sum(len(types) for types in templates.values()),
            "platforms": list(templates.keys())
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get content templates: {str(e)}"
        )

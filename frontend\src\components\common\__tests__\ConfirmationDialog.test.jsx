import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ConfirmationDialog from '../ConfirmationDialog';

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      error: {
        main: '#F44336',
      },
      warning: {
        main: '#FF9800',
      },
      success: {
        main: '#4CAF50',
      },
      info: {
        main: '#2196F3',
      },
      text: {
        secondary: '#666666',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
    shadows: ['none', '0px 2px 4px rgba(0,0,0,0.1)', '0px 4px 8px rgba(0,0,0,0.15)'],
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ConfirmationDialog', () => {
  const mockProps = {
    open: true,
    title: 'Confirm Action',
    content: 'Are you sure you want to proceed?',
    onConfirm: vi.fn(),
    onCancel: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders confirmation dialog correctly', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to proceed?')).toBeInTheDocument();
    expect(screen.getByText('Confirm')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  test('does not render when closed', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} open={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Confirm Action')).not.toBeInTheDocument();
  });

  test('handles confirm action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    await user.click(confirmButton);

    expect(mockProps.onConfirm).toHaveBeenCalled();
  });

  test('handles cancel action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockProps.onCancel).toHaveBeenCalled();
  });

  test('displays severity styling correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} severity="error" />
      </TestWrapper>
    );

    expect(screen.getByText('error')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} severity="warning" />
      </TestWrapper>
    );

    expect(screen.getByText('warning')).toBeInTheDocument();
  });

  test('shows severity and type icons', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} severity="error" type="delete" showIcon={true} />
      </TestWrapper>
    );

    expect(screen.getByText('error')).toBeInTheDocument();
    expect(screen.getByText('delete')).toBeInTheDocument();
  });

  test('handles keyboard shortcuts', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} showKeyboardShortcuts={true} />
      </TestWrapper>
    );

    // Test Escape key
    await user.keyboard('{Escape}');
    expect(mockProps.onCancel).toHaveBeenCalled();

    // Reset mock
    mockProps.onCancel.mockClear();

    // Test Ctrl+Enter
    await user.keyboard('{Control>}{Enter}{/Control}');
    expect(mockProps.onConfirm).toHaveBeenCalled();
  });

  test('shows keyboard shortcuts help', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} showKeyboardShortcuts={true} />
      </TestWrapper>
    );

    // Press ? to show shortcuts
    await user.keyboard('?');

    expect(screen.getByText('Keyboard Shortcuts')).toBeInTheDocument();
    expect(screen.getByText('• Enter (Ctrl/Cmd): Confirm')).toBeInTheDocument();
    expect(screen.getByText('• Escape: Cancel')).toBeInTheDocument();
  });

  test('handles timer functionality', async () => {
    vi.useFakeTimers();
    
    render(
      <TestWrapper>
        <ConfirmationDialog 
          {...mockProps} 
          enableTimer={true} 
          timerDuration={3000}
        />
      </TestWrapper>
    );

    expect(screen.getByText('3s')).toBeInTheDocument();

    // Fast-forward time
    vi.advanceTimersByTime(1000);
    
    await waitFor(() => {
      expect(screen.getByText('2s')).toBeInTheDocument();
    });

    // Fast-forward to timeout
    vi.advanceTimersByTime(2000);
    
    await waitFor(() => {
      expect(mockProps.onCancel).toHaveBeenCalled();
    });

    vi.useRealTimers();
  });

  test('handles confirmation input requirement', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmationDialog 
          {...mockProps} 
          confirmationRequired={true}
          confirmationText="DELETE"
        />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toBeDisabled();

    const input = screen.getByPlaceholderText('DELETE');
    await user.type(input, 'DELETE');

    expect(confirmButton).not.toBeDisabled();

    await user.click(confirmButton);
    expect(mockProps.onConfirm).toHaveBeenCalled();
  });

  test('shows consequences warning', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog 
          {...mockProps} 
          showConsequences={true}
          consequences="This action cannot be undone"
        />
      </TestWrapper>
    );

    expect(screen.getByText('⚠️ Consequences:')).toBeInTheDocument();
    expect(screen.getByText('This action cannot be undone')).toBeInTheDocument();
  });

  test('handles custom actions', async () => {
    const user = userEvent.setup();
    const customAction = vi.fn();
    
    const customActions = [
      {
        label: 'Custom Action',
        onClick: customAction,
        color: 'secondary',
        variant: 'outlined'
      }
    ];

    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} customActions={customActions} />
      </TestWrapper>
    );

    const customButton = screen.getByText('Custom Action');
    await user.click(customButton);

    expect(customAction).toHaveBeenCalled();
  });

  test('handles copy functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmationDialog 
          {...mockProps} 
          enableCopy={true}
          copyText="Text to copy"
        />
      </TestWrapper>
    );

    const copyButton = screen.getByRole('button', { name: /copy/i });
    await user.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Text to copy');
  });

  test('shows help text', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmationDialog 
          {...mockProps} 
          helpText="This is helpful information"
        />
      </TestWrapper>
    );

    const helpButton = screen.getByRole('button', { name: /help/i });
    await user.click(helpButton);

    expect(screen.getByText('This is helpful information')).toBeInTheDocument();
  });

  test('handles analytics tracking', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    
    render(
      <TestWrapper>
        <ConfirmationDialog 
          {...mockProps} 
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    await user.click(confirmButton);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'ConfirmationDialog',
        action: 'confirm'
      })
    );
  });

  test('disables backdrop click when configured', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} disableBackdropClick={true} />
      </TestWrapper>
    );

    // Try to click outside the dialog
    const dialog = screen.getByRole('alertdialog');
    await user.click(dialog.parentElement);

    // onCancel should not be called
    expect(mockProps.onCancel).not.toHaveBeenCalled();
  });

  test('handles different button colors', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} confirmColor="error" />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toBeInTheDocument();
  });

  test('handles custom button text', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog 
          {...mockProps} 
          confirmText="Delete"
          cancelText="Keep"
        />
      </TestWrapper>
    );

    expect(screen.getByText('Delete')).toBeInTheDocument();
    expect(screen.getByText('Keep')).toBeInTheDocument();
  });

  test('handles React node content', () => {
    const customContent = (
      <div>
        <p>Custom content</p>
        <strong>Important note</strong>
      </div>
    );

    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} content={customContent} />
      </TestWrapper>
    );

    expect(screen.getByText('Custom content')).toBeInTheDocument();
    expect(screen.getByText('Important note')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} />
      </TestWrapper>
    );

    const dialog = screen.getByRole('alertdialog');
    expect(dialog).toHaveAttribute('aria-labelledby', 'confirmation-dialog-title');
    expect(dialog).toHaveAttribute('aria-describedby', 'confirmation-dialog-description');

    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toHaveAttribute('aria-label');

    const cancelButton = screen.getByText('Cancel');
    expect(cancelButton).toHaveAttribute('aria-label');
  });

  test('handles focus management', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} autoFocusCancel={true} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Cancel');
    expect(cancelButton).toHaveFocus();
  });

  test('handles error in confirm handler', async () => {
    const user = userEvent.setup();
    const errorOnConfirm = vi.fn().mockImplementation(() => {
      throw new Error('Test error');
    });
    
    // Spy on console.error
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} onConfirm={errorOnConfirm} />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    await user.click(confirmButton);

    expect(errorOnConfirm).toHaveBeenCalled();
    expect(consoleSpy).toHaveBeenCalledWith('Confirmation error:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  test('handles different dialog sizes', () => {
    render(
      <TestWrapper>
        <ConfirmationDialog {...mockProps} maxWidth="lg" fullWidth={true} />
      </TestWrapper>
    );

    const dialog = screen.getByRole('alertdialog');
    expect(dialog).toBeInTheDocument();
  });
});

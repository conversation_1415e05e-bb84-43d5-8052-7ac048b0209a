/**
 * Enhanced Comment Management - Enterprise-grade comment management component
 * Features: Comprehensive comment management system with advanced moderation capabilities,
 * real-time comment synchronization, comment threading and nested replies, subscription-based
 * feature gating, comment analytics tracking, filtering and search capabilities, moderation
 * tools with spam detection, and ACE Social platform integration with advanced comment
 * management capabilities and seamless social media workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useCallback,
  useMemo,
  useEffect,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Button,
  IconButton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  LinearProgress,
  Paper,
  useTheme,
  alpha,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Badge,
  FormControl,
  InputLabel,
  Select,
  InputAdornment,
  Snackbar,
  Alert,
  Grid
} from '@mui/material';
import {
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  SentimentNeutral as NeutralIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Edit as EditIcon,
  Refresh as RegenerateIcon,
  Visibility as PreviewIcon,
  ExpandMore as ExpandMoreIcon,
  Psychology as AIIcon,
  Tag as TagIcon,
  TrendingUp as ConfidenceIcon,
  Person as PersonIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  MoreVert as MoreIcon,
  Flag as FlagIcon,
  Analytics as AnalyticsIcon,
  AutoAwesome as AutoIcon,
  SentimentSatisfied as SentimentIcon,
  Notifications as NotificationIcon,
  Download as ExportIcon,
  Refresh as RefreshIcon,
  Archive as ArchiveIcon
} from '@mui/icons-material';

import { useSubscription } from '../../contexts/SubscriptionContext';
import FeatureGate from '../common/FeatureGate';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Plan-based comment management limitations
const PLAN_LIMITS = {
  1: { // Creator
    maxCommentsPerPost: 50,
    commentAnalytics: false,
    advancedModeration: false,
    commentThreading: false,
    bulkActions: false,
    commentScheduling: false,
    sentimentAnalysis: false,
    autoModeration: false,
    commentExport: false,
    realTimeSync: false
  },
  2: { // Accelerator
    maxCommentsPerPost: 200,
    commentAnalytics: true,
    advancedModeration: true,
    commentThreading: true,
    bulkActions: true,
    commentScheduling: false,
    sentimentAnalysis: true,
    autoModeration: true,
    commentExport: false,
    realTimeSync: true
  },
  3: { // Dominator
    maxCommentsPerPost: 1000,
    commentAnalytics: true,
    advancedModeration: true,
    commentThreading: true,
    bulkActions: true,
    commentScheduling: true,
    sentimentAnalysis: true,
    autoModeration: true,
    commentExport: true,
    realTimeSync: true
  }
};

// Comment states and filters
const COMMENT_STATES = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  FLAGGED: 'flagged',
  ARCHIVED: 'archived'
};

// const SENTIMENT_TYPES = {
//   POSITIVE: 'positive',
//   NEGATIVE: 'negative',
//   NEUTRAL: 'neutral'
// };

const SORT_OPTIONS = {
  NEWEST: 'newest',
  OLDEST: 'oldest',
  SENTIMENT: 'sentiment',
  ENGAGEMENT: 'engagement',
  PRIORITY: 'priority'
};

/**
 * Enhanced Comment Management - Comprehensive comment management with advanced features
 * Implements plan-based feature restrictions and enterprise-grade comment capabilities
 */
const EnhancedCommentManagement = memo(forwardRef(({
  comments = [],
  loading = false,
  onApprove,
  onReject,
  onRegenerate,
  onManualEdit,
  onPreview,
  onBulkAction,
  onCommentFilter,
  onCommentSort,
  onCommentSearch,
  onExportComments,
  enableRealTimeSync = true,
  maxDisplayComments = 100,
  autoRefreshInterval = 30000
}, ref) => {
  const theme = useTheme();
  const { updateUsage, getPlanTier } = useSubscription();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Responsive design (for future use)
  // const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  // const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // Core state management
  const commentListRef = useRef(null);
  const searchInputRef = useRef(null);
  const wsConnectionRef = useRef(null);

  const [expandedComment, setExpandedComment] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [regenerateDialogOpen, setRegenerateDialogOpen] = useState(false);
  const [selectedComment, setSelectedComment] = useState(null);
  const [editedResponse, setEditedResponse] = useState('');
  const [regeneratePrompt, setRegeneratePrompt] = useState('');
  const [actionLoading, setActionLoading] = useState({});

  // Enhanced state management
  const [searchQuery, setSearchQuery] = useState('');
  const [filterState, setFilterState] = useState(COMMENT_STATES.PENDING);
  const [sortOption, setSortOption] = useState(SORT_OPTIONS.NEWEST);
  const [selectedComments, setSelectedComments] = useState(new Set());
  const [bulkActionMenuAnchor, setBulkActionMenuAnchor] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('info');
  const [commentAnalytics] = useState({});
  const [realTimeUpdates, setRealTimeUpdates] = useState([]);
  const [autoModerationEnabled, setAutoModerationEnabled] = useState(false);

  // Plan tier and feature access
  const planTier = getPlanTier();
  const planLimits = PLAN_LIMITS[planTier] || PLAN_LIMITS[1];

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshComments: () => handleRefreshComments(),
    selectAllComments: () => handleSelectAllComments(),
    clearSelection: () => setSelectedComments(new Set()),
    getSelectedComments: () => Array.from(selectedComments),
    exportComments: () => handleExportComments(),
    openAnalytics: () => console.log('Analytics opened'),
    searchComments: (query) => setSearchQuery(query),
    filterComments: (state) => setFilterState(state),
    sortComments: (option) => setSortOption(option),
    getCommentAnalytics: () => commentAnalytics,
    toggleAutoModeration: () => setAutoModerationEnabled(!autoModerationEnabled),
    getModerationQueue: () => []
  }), [selectedComments, commentAnalytics, autoModerationEnabled, handleRefreshComments, handleSelectAllComments, handleExportComments]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Real-time WebSocket connection
  useEffect(() => {
    if (enableRealTimeSync && planLimits.realTimeSync) {
      // Initialize WebSocket connection for real-time updates
      const connectWebSocket = () => {
        try {
          const ws = new WebSocket(`${process.env.REACT_APP_WS_URL}/comments`);

          ws.onopen = () => {
            console.log('Comment WebSocket connected');
            announceToScreenReader('Real-time comment updates enabled');
          };

          ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            if (data.type === 'comment_update') {
              setRealTimeUpdates(prev => [...prev, data.payload].slice(-10));
              announceToScreenReader(`New comment update received`);
            }
          };

          ws.onclose = () => {
            console.log('Comment WebSocket disconnected');
            // Attempt to reconnect after 5 seconds
            setTimeout(connectWebSocket, 5000);
          };

          wsConnectionRef.current = ws;
        } catch (error) {
          console.error('WebSocket connection failed:', error);
        }
      };

      connectWebSocket();

      return () => {
        if (wsConnectionRef.current) {
          wsConnectionRef.current.close();
        }
      };
    }
  }, [enableRealTimeSync, planLimits.realTimeSync, announceToScreenReader]);

  // Auto-refresh comments
  useEffect(() => {
    if (autoRefreshInterval > 0) {
      const interval = setInterval(() => {
        handleRefreshComments();
      }, autoRefreshInterval);

      return () => clearInterval(interval);
    }
  }, [autoRefreshInterval, handleRefreshComments]);

  // Enhanced comment filtering and processing
  const processedComments = useMemo(() => {
    let filtered = [...comments];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(comment =>
        comment.comment_text?.toLowerCase().includes(query) ||
        comment.user_name?.toLowerCase().includes(query) ||
        comment.ai_response_text?.toLowerCase().includes(query) ||
        comment.keywords?.some(keyword => keyword.toLowerCase().includes(query))
      );
    }

    // Apply state filter
    if (filterState !== 'all') {
      filtered = filtered.filter(comment => comment.status === filterState);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortOption) {
        case SORT_OPTIONS.NEWEST:
          return new Date(b.created_at) - new Date(a.created_at);
        case SORT_OPTIONS.OLDEST:
          return new Date(a.created_at) - new Date(b.created_at);
        case SORT_OPTIONS.SENTIMENT: {
          const sentimentOrder = { positive: 3, neutral: 2, negative: 1 };
          return (sentimentOrder[b.sentiment] || 0) - (sentimentOrder[a.sentiment] || 0);
        }
        case SORT_OPTIONS.ENGAGEMENT:
          return (b.engagement_score || 0) - (a.engagement_score || 0);
        case SORT_OPTIONS.PRIORITY:
          return (b.priority_score || 0) - (a.priority_score || 0);
        default:
          return 0;
      }
    });

    // Limit display count based on plan
    return filtered.slice(0, Math.min(maxDisplayComments, planLimits.maxCommentsPerPost));
  }, [comments, searchQuery, filterState, sortOption, maxDisplayComments, planLimits.maxCommentsPerPost]);

  // Get sentiment icon and color
  const getSentimentDisplay = useCallback((sentiment, confidence) => {
    const getIcon = () => {
      switch (sentiment?.toLowerCase()) {
        case 'positive': return <ThumbUpIcon fontSize="small" />;
        case 'negative': return <ThumbDownIcon fontSize="small" />;
        default: return <NeutralIcon fontSize="small" />;
      }
    };

    const getColor = () => {
      switch (sentiment?.toLowerCase()) {
        case 'positive': return theme.palette.success.main;
        case 'negative': return theme.palette.error.main;
        default: return theme.palette.grey[500];
      }
    };

    return {
      icon: getIcon(),
      color: getColor(),
      confidence: confidence || 0.8
    };
  }, [theme]);

  // Enhanced action handlers with comprehensive functionality
  const handleActionWithLoading = useCallback(async (commentId, action, ...args) => {
    setActionLoading(prev => ({ ...prev, [commentId]: action }));
    try {
      const result = await action(...args);

      // Track usage
      await updateUsage('comment_action', 1, {
        action: action.name,
        commentId,
        planTier
      });

      // Show success message
      setSnackbarMessage(`Action completed successfully`);
      setSnackbarSeverity('success');
      setSnackbarOpen(true);

      announceToScreenReader(`Comment action completed`);
      return result;
    } catch (error) {
      console.error('Comment action failed:', error);
      setSnackbarMessage(`Action failed: ${error.message}`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      announceToScreenReader(`Comment action failed: ${error.message}`);
      throw error;
    } finally {
      setActionLoading(prev => ({ ...prev, [commentId]: null }));
    }
  }, [updateUsage, planTier, announceToScreenReader]);

  // Enhanced comment handlers
  const handleRefreshComments = useCallback(async () => {
    try {
      if (onCommentFilter) {
        await onCommentFilter({ refresh: true });
      }
      announceToScreenReader('Comments refreshed');
    } catch (error) {
      console.error('Failed to refresh comments:', error);
    }
  }, [onCommentFilter, announceToScreenReader]);

  const handleSelectAllComments = useCallback(() => {
    if (!planLimits.bulkActions) {
      announceToScreenReader('Bulk actions are not available in your current plan');
      return;
    }

    const allCommentIds = new Set(processedComments.map(comment => comment.id));
    setSelectedComments(allCommentIds);
    announceToScreenReader(`Selected ${allCommentIds.size} comments`);
  }, [planLimits.bulkActions, processedComments, announceToScreenReader]);

  const handleBulkAction = useCallback(async (action) => {
    if (!planLimits.bulkActions) {
      announceToScreenReader('Bulk actions are not available in your current plan');
      return;
    }

    if (selectedComments.size === 0) {
      announceToScreenReader('No comments selected');
      return;
    }

    try {
      if (onBulkAction) {
        await onBulkAction(action, Array.from(selectedComments));
      }

      setSelectedComments(new Set());
      setBulkActionMenuAnchor(null);

      announceToScreenReader(`Bulk action ${action} completed for ${selectedComments.size} comments`);
    } catch (error) {
      console.error('Bulk action failed:', error);
      announceToScreenReader(`Bulk action failed: ${error.message}`);
    }
  }, [planLimits.bulkActions, selectedComments, onBulkAction, announceToScreenReader]);

  const handleExportComments = useCallback(() => {
    if (!planLimits.commentExport) {
      announceToScreenReader('Comment export is not available in your current plan');
      return;
    }

    try {
      const exportData = {
        comments: processedComments,
        analytics: commentAnalytics,
        exportedAt: new Date().toISOString(),
        planTier,
        filters: { searchQuery, filterState, sortOption }
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `comments-export-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      if (onExportComments) {
        onExportComments(exportData);
      }

      announceToScreenReader('Comments exported successfully');
    } catch (error) {
      console.error('Export failed:', error);
      announceToScreenReader(`Export failed: ${error.message}`);
    }
  }, [planLimits.commentExport, processedComments, commentAnalytics, planTier, searchQuery, filterState, sortOption, onExportComments, announceToScreenReader]);

  // Basic action handlers
  const handleApprove = useCallback((comment) => {
    if (onApprove) {
      handleActionWithLoading(comment.id, onApprove, comment.id, comment.ai_response_text);
    }
  }, [onApprove, handleActionWithLoading]);

  const handleReject = useCallback((comment) => {
    if (onReject) {
      handleActionWithLoading(comment.id, onReject, comment.id);
    }
  }, [onReject, handleActionWithLoading]);

  const handleRegenerate = useCallback((comment) => {
    setSelectedComment(comment);
    setRegeneratePrompt('');
    setRegenerateDialogOpen(true);
  }, []);

  const handleManualEdit = useCallback((comment) => {
    setSelectedComment(comment);
    setEditedResponse(comment.ai_response_text || '');
    setEditDialogOpen(true);
  }, []);

  const handlePreview = useCallback((comment) => {
    setSelectedComment(comment);
    setPreviewDialogOpen(true);
    if (onPreview) {
      onPreview(comment);
    }
  }, [onPreview]);

  // Search and filter handlers
  const handleSearchChange = useCallback((event) => {
    const query = event.target.value;
    setSearchQuery(query);
    if (onCommentSearch) {
      onCommentSearch(query);
    }
  }, [onCommentSearch]);

  const handleFilterChange = useCallback((newFilter) => {
    setFilterState(newFilter);
    if (onCommentFilter) {
      onCommentFilter(newFilter);
    }
  }, [onCommentFilter]);

  const handleSortChange = useCallback((newSort) => {
    setSortOption(newSort);
    if (onCommentSort) {
      onCommentSort(newSort);
    }
  }, [onCommentSort]);

  // Dialog handlers
  const confirmRegenerate = useCallback(async () => {
    if (selectedComment && onRegenerate) {
      await handleActionWithLoading(
        selectedComment.id,
        onRegenerate,
        selectedComment.id,
        regeneratePrompt
      );
      setRegenerateDialogOpen(false);
      setSelectedComment(null);
      setRegeneratePrompt('');
    }
  }, [selectedComment, onRegenerate, regeneratePrompt, handleActionWithLoading]);

  const confirmManualEdit = useCallback(async () => {
    if (selectedComment && editedResponse.trim() && onManualEdit) {
      await handleActionWithLoading(
        selectedComment.id,
        onManualEdit,
        selectedComment.id,
        editedResponse
      );
      setEditDialogOpen(false);
      setSelectedComment(null);
      setEditedResponse('');
    }
  }, [selectedComment, editedResponse, onManualEdit, handleActionWithLoading]);

  const handleCloseSnackbar = useCallback(() => {
    setSnackbarOpen(false);
  }, []);

  // Comment selection handler (for future bulk operations)
  // const handleCommentSelection = useCallback((commentId, selected) => {
  //   if (!planLimits.bulkActions) return;
  //   setSelectedComments(prev => {
  //     const newSet = new Set(prev);
  //     if (selected) {
  //       newSet.add(commentId);
  //     } else {
  //       newSet.delete(commentId);
  //     }
  //     return newSet;
  //   });
  // }, [planLimits.bulkActions]);

  // Enhanced render functions
  const renderKeywords = useCallback((keywords) => {
    if (!keywords || keywords.length === 0) return null;

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
        {keywords.slice(0, 5).map((keyword, index) => (
          <Chip
            key={index}
            label={keyword}
            size="small"
            icon={<TagIcon />}
            variant="outlined"
            sx={{
              fontSize: '0.7rem',
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
              borderColor: alpha(ACE_COLORS.PURPLE, 0.3),
              color: ACE_COLORS.PURPLE
            }}
          />
        ))}
        {keywords.length > 5 && (
          <Chip
            label={`+${keywords.length - 5} more`}
            size="small"
            variant="outlined"
            sx={{
              fontSize: '0.7rem',
              backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
              borderColor: alpha(ACE_COLORS.YELLOW, 0.3),
              color: ACE_COLORS.DARK
            }}
          />
        )}
      </Box>
    );
  }, []);

  const renderQualityIndicator = useCallback((quality) => {
    const score = quality || 0.75;
    const getColor = () => {
      if (score >= 0.8) return theme.palette.success.main;
      if (score >= 0.6) return theme.palette.warning.main;
      return theme.palette.error.main;
    };

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
        <ConfidenceIcon fontSize="small" sx={{ color: ACE_COLORS.PURPLE }} />
        <Typography variant="caption" color="textSecondary">
          Quality Score:
        </Typography>
        <LinearProgress
          variant="determinate"
          value={score * 100}
          sx={{
            width: 60,
            height: 6,
            borderRadius: 3,
            backgroundColor: alpha(getColor(), 0.2),
            '& .MuiLinearProgress-bar': {
              backgroundColor: getColor(),
              borderRadius: 3
            }
          }}
        />
        <Typography variant="caption" color="textSecondary" fontWeight={600}>
          {Math.round(score * 100)}%
        </Typography>
      </Box>
    );
  }, [theme]);

  // Enhanced toolbar render function
  const renderToolbar = useCallback(() => (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: 2,
      mb: 3,
      p: 2,
      ...glassMorphismStyles,
      flexWrap: 'wrap'
    }}>
      {/* Search */}
      <TextField
        ref={searchInputRef}
        size="small"
        placeholder="Search comments..."
        value={searchQuery}
        onChange={handleSearchChange}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon sx={{ color: ACE_COLORS.PURPLE }} />
            </InputAdornment>
          )
        }}
        sx={{ minWidth: 200, flexGrow: 1 }}
      />

      {/* Filter */}
      <FormControl size="small" sx={{ minWidth: 120 }}>
        <InputLabel>Filter</InputLabel>
        <Select
          value={filterState}
          onChange={(e) => handleFilterChange(e.target.value)}
          startAdornment={<FilterIcon sx={{ color: ACE_COLORS.PURPLE, mr: 1 }} />}
        >
          <MenuItem value="all">All</MenuItem>
          <MenuItem value={COMMENT_STATES.PENDING}>Pending</MenuItem>
          <MenuItem value={COMMENT_STATES.APPROVED}>Approved</MenuItem>
          <MenuItem value={COMMENT_STATES.REJECTED}>Rejected</MenuItem>
          <MenuItem value={COMMENT_STATES.FLAGGED}>Flagged</MenuItem>
        </Select>
      </FormControl>

      {/* Sort */}
      <FormControl size="small" sx={{ minWidth: 120 }}>
        <InputLabel>Sort</InputLabel>
        <Select
          value={sortOption}
          onChange={(e) => handleSortChange(e.target.value)}
          startAdornment={<SortIcon sx={{ color: ACE_COLORS.PURPLE, mr: 1 }} />}
        >
          <MenuItem value={SORT_OPTIONS.NEWEST}>Newest</MenuItem>
          <MenuItem value={SORT_OPTIONS.OLDEST}>Oldest</MenuItem>
          <MenuItem value={SORT_OPTIONS.SENTIMENT}>Sentiment</MenuItem>
          <MenuItem value={SORT_OPTIONS.ENGAGEMENT}>Engagement</MenuItem>
          <MenuItem value={SORT_OPTIONS.PRIORITY}>Priority</MenuItem>
        </Select>
      </FormControl>

      {/* Action buttons */}
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Tooltip title="Refresh comments">
          <IconButton onClick={handleRefreshComments} size="small">
            <RefreshIcon />
          </IconButton>
        </Tooltip>

        <FeatureGate requiredPlan={2}>
          <Tooltip title="Analytics">
            <IconButton
              onClick={() => console.log('Analytics opened')}
              size="small"
              sx={{ color: planLimits.commentAnalytics ? ACE_COLORS.PURPLE : 'disabled' }}
            >
              <AnalyticsIcon />
            </IconButton>
          </Tooltip>
        </FeatureGate>

        <FeatureGate requiredPlan={3}>
          <Tooltip title="Export comments">
            <IconButton
              onClick={handleExportComments}
              size="small"
              sx={{ color: planLimits.commentExport ? ACE_COLORS.PURPLE : 'disabled' }}
            >
              <ExportIcon />
            </IconButton>
          </Tooltip>
        </FeatureGate>

        <FeatureGate requiredPlan={2}>
          <Tooltip title="Bulk actions">
            <IconButton
              onClick={(e) => setBulkActionMenuAnchor(e.currentTarget)}
              size="small"
              disabled={!planLimits.bulkActions || selectedComments.size === 0}
            >
              <Badge badgeContent={selectedComments.size} color="primary">
                <MoreIcon />
              </Badge>
            </IconButton>
          </Tooltip>
        </FeatureGate>
      </Box>
    </Box>
  ), [
    glassMorphismStyles,
    searchQuery,
    handleSearchChange,
    filterState,
    handleFilterChange,
    sortOption,
    handleSortChange,
    handleRefreshComments,
    planLimits,
    selectedComments.size,
    handleExportComments
  ]);

  // Enhanced comment item renderer
  const renderCommentItem = useCallback((comment) => {
    const sentiment = getSentimentDisplay(comment.sentiment, comment.sentiment_confidence);
    const isExpanded = expandedComment === comment.id;
    const currentAction = actionLoading[comment.id];

    return (
      <Card key={comment.id} sx={{ mb: 2 }}>
        <CardContent>
          {/* Comment Header */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <Avatar src={comment.user_image} sx={{ mr: 2 }}>
              {!comment.user_image && <PersonIcon />}
            </Avatar>
            
            <Box sx={{ flexGrow: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Typography variant="subtitle2">
                  {comment.user_name || 'Anonymous User'}
                </Typography>
                
                <Tooltip title={`Sentiment: ${comment.sentiment} (${Math.round((sentiment.confidence || 0.8) * 100)}% confidence)`}>
                  <Chip
                    icon={sentiment.icon}
                    label={comment.sentiment || 'neutral'}
                    size="small"
                    sx={{
                      backgroundColor: alpha(sentiment.color, 0.1),
                      color: sentiment.color,
                      textTransform: 'capitalize'
                    }}
                  />
                </Tooltip>
              </Box>
              
              <Typography variant="body2" color="textSecondary" paragraph>
                {comment.comment_text}
              </Typography>
              
              {/* Keywords */}
              {renderKeywords(comment.keywords)}
            </Box>
          </Box>

          {/* AI Response Section */}
          <Accordion expanded={isExpanded} onChange={() => setExpandedComment(isExpanded ? null : comment.id)}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AIIcon color="primary" />
                <Typography variant="subtitle2">
                  AI-Generated Response
                </Typography>
                {comment.ai_response_status && (
                  <Chip
                    label={comment.ai_response_status}
                    size="small"
                    color={comment.ai_response_status === 'published' ? 'success' : 'default'}
                  />
                )}
              </Box>
            </AccordionSummary>
            
            <AccordionDetails>
              <Box>
                {/* AI Response Text */}
                <Paper 
                  variant="outlined" 
                  sx={{ 
                    p: 2, 
                    mb: 2,
                    backgroundColor: alpha(theme.palette.primary.main, 0.05)
                  }}
                >
                  <Typography variant="body2">
                    {comment.ai_response_text || 'No response generated yet'}
                  </Typography>
                </Paper>

                {/* Quality Indicator */}
                {comment.ai_response_text && renderQualityIndicator(comment.ai_response_quality)}

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 1, mt: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    color="success"
                    startIcon={currentAction === onApprove ? <CircularProgress size={16} /> : <ApproveIcon />}
                    onClick={() => handleApprove(comment)}
                    disabled={!comment.ai_response_text || comment.ai_response_status === 'published' || currentAction}
                    size="small"
                  >
                    Approve
                  </Button>
                  
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={currentAction === onReject ? <CircularProgress size={16} /> : <RejectIcon />}
                    onClick={() => handleReject(comment)}
                    disabled={comment.ai_response_status === 'rejected' || currentAction}
                    size="small"
                  >
                    Reject
                  </Button>
                  
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={currentAction === onRegenerate ? <CircularProgress size={16} /> : <RegenerateIcon />}
                    onClick={() => handleRegenerate(comment)}
                    disabled={comment.ai_response_status === 'published' || currentAction}
                    size="small"
                  >
                    Regenerate
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<EditIcon />}
                    onClick={() => handleManualEdit(comment)}
                    disabled={comment.ai_response_status === 'published' || currentAction}
                    size="small"
                  >
                    Edit
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<PreviewIcon />}
                    onClick={() => handlePreview(comment)}
                    disabled={!comment.ai_response_text}
                    size="small"
                  >
                    Preview
                  </Button>
                </Box>
              </Box>
            </AccordionDetails>
          </Accordion>
        </CardContent>
      </Card>
    );
  }, [
    getSentimentDisplay,
    expandedComment,
    actionLoading,
    renderKeywords,
    renderQualityIndicator,
    handleApprove,
    handleReject,
    handleRegenerate,
    handleManualEdit,
    handlePreview,
    theme,
    onApprove,
    onReject,
    onRegenerate
  ]);

  // Loading state
  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: 400,
        ...glassMorphismStyles
      }}>
        <CircularProgress sx={{ color: ACE_COLORS.PURPLE }} />
      </Box>
    );
  }

  // Empty state
  if (!comments || comments.length === 0) {
    return (
      <Paper sx={{
        p: 4,
        textAlign: 'center',
        ...glassMorphismStyles
      }}>
        <AIIcon sx={{ fontSize: 64, color: ACE_COLORS.PURPLE, mb: 2 }} />
        <Typography variant="h6" color="textSecondary" gutterBottom>
          No comments found
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Select a post to view and manage its comments
        </Typography>
      </Paper>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Enhanced Header */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mb: 3,
        flexWrap: 'wrap',
        gap: 2
      }}>
        <Box>
          <Typography variant="h5" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
            Comment Management
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {processedComments.length} of {comments.length} comments
            {selectedComments.size > 0 && ` • ${selectedComments.size} selected`}
          </Typography>
        </Box>

        <FeatureGate requiredPlan={2}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Chip
              icon={<AutoIcon />}
              label={autoModerationEnabled ? 'Auto-Mod ON' : 'Auto-Mod OFF'}
              color={autoModerationEnabled ? 'success' : 'default'}
              onClick={() => setAutoModerationEnabled(!autoModerationEnabled)}
              sx={{ cursor: 'pointer' }}
            />
            <Chip
              icon={<NotificationIcon />}
              label={`${realTimeUpdates.length} Updates`}
              color="info"
              variant="outlined"
            />
          </Box>
        </FeatureGate>
      </Box>

      {/* Enhanced Toolbar */}
      {renderToolbar()}

      {/* Comments List */}
      <Box
        ref={commentListRef}
        sx={{
          maxHeight: 'calc(100vh - 400px)',
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: 8,
          },
          '&::-webkit-scrollbar-track': {
            background: alpha(ACE_COLORS.PURPLE, 0.1),
            borderRadius: 4,
          },
          '&::-webkit-scrollbar-thumb': {
            background: alpha(ACE_COLORS.PURPLE, 0.3),
            borderRadius: 4,
            '&:hover': {
              background: alpha(ACE_COLORS.PURPLE, 0.5),
            },
          },
        }}
      >
        {processedComments.map(renderCommentItem)}
      </Box>

      {/* Enhanced Bulk Actions Menu */}
      <FeatureGate requiredPlan={2}>
        <Menu
          anchorEl={bulkActionMenuAnchor}
          open={Boolean(bulkActionMenuAnchor)}
          onClose={() => setBulkActionMenuAnchor(null)}
          slotProps={{
            paper: {
              sx: { ...glassMorphismStyles, minWidth: 200 }
            }
          }}
        >
          <MenuItem onClick={() => handleBulkAction('approve')}>
            <ListItemIcon>
              <ApproveIcon sx={{ color: theme.palette.success.main }} />
            </ListItemIcon>
            <ListItemText>Approve Selected</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleBulkAction('reject')}>
            <ListItemIcon>
              <RejectIcon sx={{ color: theme.palette.error.main }} />
            </ListItemIcon>
            <ListItemText>Reject Selected</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleBulkAction('flag')}>
            <ListItemIcon>
              <FlagIcon sx={{ color: theme.palette.warning.main }} />
            </ListItemIcon>
            <ListItemText>Flag Selected</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => handleBulkAction('archive')}>
            <ListItemIcon>
              <ArchiveIcon />
            </ListItemIcon>
            <ListItemText>Archive Selected</ListItemText>
          </MenuItem>
        </Menu>
      </FeatureGate>

      {/* Enhanced Regenerate Dialog */}
      <Dialog
        open={regenerateDialogOpen}
        onClose={() => setRegenerateDialogOpen(false)}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: glassMorphismStyles
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          color: ACE_COLORS.DARK
        }}>
          <RegenerateIcon sx={{ color: ACE_COLORS.PURPLE }} />
          Regenerate AI Response
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Custom instructions (optional)"
            placeholder="e.g., Make it more friendly and professional, include specific talking points, adjust tone..."
            value={regeneratePrompt}
            onChange={(e) => setRegeneratePrompt(e.target.value)}
            sx={{
              mt: 2,
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: ACE_COLORS.PURPLE,
                },
                '&.Mui-focused fieldset': {
                  borderColor: ACE_COLORS.PURPLE,
                },
              },
            }}
          />
          <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
            Provide specific instructions to improve the AI response quality and alignment with your brand voice.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={() => setRegenerateDialogOpen(false)}
            sx={{ color: theme.palette.text.secondary }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmRegenerate}
            variant="contained"
            sx={{
              backgroundColor: ACE_COLORS.PURPLE,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
              }
            }}
          >
            Regenerate Response
          </Button>
        </DialogActions>
      </Dialog>

      {/* Enhanced Manual Edit Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="lg"
        fullWidth
        slotProps={{
          paper: {
            sx: glassMorphismStyles
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          color: ACE_COLORS.DARK
        }}>
          <EditIcon sx={{ color: ACE_COLORS.PURPLE }} />
          Edit Response
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Original Comment
              </Typography>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  mb: 2,
                  backgroundColor: alpha(theme.palette.grey[100], 0.5),
                  maxHeight: 150,
                  overflow: 'auto'
                }}
              >
                <Typography variant="body2">
                  {selectedComment?.comment_text || 'No comment text available'}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>
                Current AI Response
              </Typography>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  mb: 2,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                  maxHeight: 150,
                  overflow: 'auto'
                }}
              >
                <Typography variant="body2">
                  {selectedComment?.ai_response_text || 'No AI response available'}
                </Typography>
              </Paper>
            </Grid>
          </Grid>

          <TextField
            fullWidth
            multiline
            rows={6}
            label="Edit Response"
            value={editedResponse}
            onChange={(e) => setEditedResponse(e.target.value)}
            sx={{
              mt: 2,
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: ACE_COLORS.PURPLE,
                },
                '&.Mui-focused fieldset': {
                  borderColor: ACE_COLORS.PURPLE,
                },
              },
            }}
          />

          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="caption" color="textSecondary">
              Character count: {editedResponse.length}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              Estimated reading time: {Math.ceil(editedResponse.split(' ').length / 200)} min
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={() => setEditDialogOpen(false)}
            sx={{ color: theme.palette.text.secondary }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmManualEdit}
            variant="contained"
            disabled={!editedResponse.trim()}
            sx={{
              backgroundColor: ACE_COLORS.PURPLE,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
              }
            }}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Enhanced Preview Dialog */}
      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="md"
        fullWidth
        slotProps={{
          paper: {
            sx: glassMorphismStyles
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          color: ACE_COLORS.DARK
        }}>
          <PreviewIcon sx={{ color: ACE_COLORS.PURPLE }} />
          Response Preview
        </DialogTitle>
        <DialogContent>
          {selectedComment && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Original Comment
              </Typography>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  mb: 3,
                  backgroundColor: alpha(theme.palette.grey[100], 0.5)
                }}
              >
                <Typography variant="body2">
                  {selectedComment.comment_text}
                </Typography>
              </Paper>

              <Typography variant="subtitle2" gutterBottom>
                AI Response Preview
              </Typography>
              <Paper
                variant="outlined"
                sx={{
                  p: 3,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                  border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
                }}
              >
                <Typography variant="body1" sx={{ lineHeight: 1.6 }}>
                  {selectedComment.ai_response_text}
                </Typography>
              </Paper>

              {/* Response metrics */}
              <Box sx={{ mt: 2, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Chip
                  icon={<ConfidenceIcon />}
                  label={`Quality: ${Math.round((selectedComment.ai_response_quality || 0.75) * 100)}%`}
                  size="small"
                  color="primary"
                />
                <Chip
                  icon={<SentimentIcon />}
                  label={`Sentiment: ${selectedComment.sentiment || 'neutral'}`}
                  size="small"
                  variant="outlined"
                />
                <Chip
                  label={`${selectedComment.ai_response_text?.length || 0} characters`}
                  size="small"
                  variant="outlined"
                />
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3 }}>
          <Button
            onClick={() => setPreviewDialogOpen(false)}
            variant="contained"
            sx={{
              backgroundColor: ACE_COLORS.PURPLE,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
              }
            }}
          >
            Close Preview
          </Button>
        </DialogActions>
      </Dialog>

      {/* Enhanced Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          sx={{
            ...glassMorphismStyles,
            '& .MuiAlert-icon': {
              color: snackbarSeverity === 'success' ? theme.palette.success.main :
                     snackbarSeverity === 'error' ? theme.palette.error.main :
                     ACE_COLORS.PURPLE
            }
          }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}));

EnhancedCommentManagement.displayName = 'EnhancedCommentManagement';

EnhancedCommentManagement.propTypes = {
  /** Array of comments to manage */
  comments: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    comment_text: PropTypes.string.isRequired,
    user_name: PropTypes.string,
    user_image: PropTypes.string,
    sentiment: PropTypes.oneOf(['positive', 'negative', 'neutral']),
    sentiment_confidence: PropTypes.number,
    keywords: PropTypes.arrayOf(PropTypes.string),
    ai_response_text: PropTypes.string,
    ai_response_status: PropTypes.string,
    ai_response_quality: PropTypes.number,
    status: PropTypes.string,
    created_at: PropTypes.string,
    engagement_score: PropTypes.number,
    priority_score: PropTypes.number
  })),
  /** Loading state */
  loading: PropTypes.bool,
  /** Approve comment callback */
  onApprove: PropTypes.func,
  /** Reject comment callback */
  onReject: PropTypes.func,
  /** Regenerate response callback */
  onRegenerate: PropTypes.func,
  /** Manual edit callback */
  onManualEdit: PropTypes.func,
  /** Preview callback */
  onPreview: PropTypes.func,
  /** Bulk action callback */
  onBulkAction: PropTypes.func,
  /** Comment filter callback */
  onCommentFilter: PropTypes.func,
  /** Comment sort callback */
  onCommentSort: PropTypes.func,
  /** Comment search callback */
  onCommentSearch: PropTypes.func,
  /** Analytics view callback */
  onAnalyticsView: PropTypes.func,
  /** Export comments callback */
  onExportComments: PropTypes.func,
  /** Enable real-time sync */
  enableRealTimeSync: PropTypes.bool,
  /** Enable advanced moderation */
  enableAdvancedModeration: PropTypes.bool,
  /** Enable comment threading */
  enableCommentThreading: PropTypes.bool,
  /** Enable bulk actions */
  enableBulkActions: PropTypes.bool,
  /** Maximum comments to display */
  maxDisplayComments: PropTypes.number,
  /** Auto refresh interval in milliseconds */
  autoRefreshInterval: PropTypes.number,
  /** Moderation settings object */
  moderationSettings: PropTypes.object,
  /** Enable analytics */
  analyticsEnabled: PropTypes.bool
};

export default EnhancedCommentManagement;

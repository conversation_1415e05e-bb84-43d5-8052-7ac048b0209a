#!/bin/bash
# @since 2024-1-1 to 2025-25-7

# ACE Social Platform - Standardized Build Script
# Version: 2.0.0
# Usage: ./scripts/build.sh [dev|staging|production] [--clean] [--no-tests] [--verbose]

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# ================================
# Configuration & Constants
# ================================

# Script metadata
readonly SCRIPT_VERSION="2.0.0"
readonly SCRIPT_NAME="ACE Social Build Script"
readonly BUILD_START_TIME=$(date +%s)

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m' # No Color

# Build configuration
readonly DEFAULT_ENVIRONMENT="dev"
readonly SUPPORTED_ENVIRONMENTS=("dev" "staging" "production")
readonly BUILD_LOG_DIR="logs/build"
readonly BUILD_TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
readonly BUILD_LOG_FILE="${BUILD_LOG_DIR}/build_${BUILD_TIMESTAMP}.log"

# Parse command line arguments
ENVIRONMENT=${1:-$DEFAULT_ENVIRONMENT}
CLEAN_BUILD=false
SKIP_TESTS=false
VERBOSE=false

# Parse additional flags
for arg in "$@"; do
    case $arg in
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --no-tests)
            SKIP_TESTS=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
    esac
done

# ================================
# Utility Functions
# ================================

# Logging functions with timestamps
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BUILD_LOG_FILE"
}

print_header() {
    echo -e "${WHITE}================================${NC}"
    echo -e "${WHITE}$1${NC}"
    echo -e "${WHITE}================================${NC}"
    log_with_timestamp "HEADER: $1"
}

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
    log_with_timestamp "SUCCESS: $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    log_with_timestamp "WARNING: $1"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    log_with_timestamp "ERROR: $1"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
    log_with_timestamp "INFO: $1"
}

print_step() {
    echo -e "${CYAN}🔄 $1${NC}"
    log_with_timestamp "STEP: $1"
}

# Progress tracking
show_progress() {
    local current=$1
    local total=$2
    local description=$3
    local percentage=$((current * 100 / total))
    echo -e "${PURPLE}[${current}/${total}] (${percentage}%) ${description}${NC}"
}

# Help function
show_help() {
    cat << EOF
${SCRIPT_NAME} v${SCRIPT_VERSION}

USAGE:
    ./scripts/build.sh [ENVIRONMENT] [OPTIONS]

ENVIRONMENTS:
    dev         Development build (default)
    staging     Staging build with optimizations
    production  Production build with full optimizations

OPTIONS:
    --clean     Clean all caches and artifacts before building
    --no-tests  Skip running tests during build
    --verbose   Enable verbose logging
    --help, -h  Show this help message

EXAMPLES:
    ./scripts/build.sh                          # Development build
    ./scripts/build.sh production               # Production build
    ./scripts/build.sh staging --clean          # Clean staging build
    ./scripts/build.sh production --no-tests    # Production build without tests

ENVIRONMENT VARIABLES:
    NODE_OPTIONS        Node.js memory options (default: --max-old-space-size=4096)
    DOCKER_BUILDKIT     Enable Docker BuildKit (default: 1)
    BUILD_PARALLEL      Enable parallel builds (default: true)

EOF
}

# Validation functions
validate_environment() {
    local env=$1
    for supported_env in "${SUPPORTED_ENVIRONMENTS[@]}"; do
        if [[ "$env" == "$supported_env" ]]; then
            return 0
        fi
    done
    print_error "Unsupported environment: $env"
    print_info "Supported environments: ${SUPPORTED_ENVIRONMENTS[*]}"
    exit 1
}

validate_prerequisites() {
    print_step "Validating prerequisites..."

    local missing_deps=()

    # Check Node.js
    if ! command -v node &> /dev/null; then
        missing_deps+=("Node.js")
    else
        local node_version=$(node --version | sed 's/v//')
        local required_version="18.0.0"
        if ! version_gte "$node_version" "$required_version"; then
            print_error "Node.js version $node_version is too old. Required: $required_version+"
            missing_deps+=("Node.js (version $required_version+)")
        fi
    fi

    # Check npm
    if ! command -v npm &> /dev/null; then
        missing_deps+=("npm")
    fi

    # Check Python
    if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
        missing_deps+=("Python")
    fi

    # Check Docker
    if ! command -v docker &> /dev/null; then
        missing_deps+=("Docker")
    elif ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi

    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null 2>&1; then
        missing_deps+=("Docker Compose")
    fi

    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing required dependencies:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        print_info "Please install missing dependencies and try again."
        exit 1
    fi

    print_status "All prerequisites validated"
}

# Version comparison function
version_gte() {
    local version1=$1
    local version2=$2
    [ "$(printf '%s\n' "$version2" "$version1" | sort -V | head -n1)" = "$version2" ]
}

validate_project_structure() {
    print_step "Validating project structure..."

    local required_files=(
        "package.json"
        "frontend/package.json"
        "backend/requirements.txt"
        "admin-app/package.json"
        "docker-compose.yml"
    )

    local missing_files=()

    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done

    if [ ${#missing_files[@]} -ne 0 ]; then
        print_error "Missing required project files:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        print_info "Please ensure you're running this script from the project root."
        exit 1
    fi

    print_status "Project structure validated"
}

# Initialize build environment
initialize_build() {
    print_header "Initializing Build Environment"

    # Create necessary directories
    mkdir -p "$BUILD_LOG_DIR"
    mkdir -p logs/frontend
    mkdir -p logs/backend
    mkdir -p logs/admin
    mkdir -p logs/docker

    # Set environment variables
    export NODE_OPTIONS="${NODE_OPTIONS:---max-old-space-size=4096}"
    export DOCKER_BUILDKIT="${DOCKER_BUILDKIT:-1}"
    export BUILD_PARALLEL="${BUILD_PARALLEL:-true}"

    # Log build information
    {
        echo "Build Information:"
        echo "=================="
        echo "Script Version: $SCRIPT_VERSION"
        echo "Environment: $ENVIRONMENT"
        echo "Timestamp: $(date)"
        echo "User: $(whoami)"
        echo "Working Directory: $(pwd)"
        echo "Node Version: $(node --version 2>/dev/null || echo 'Not found')"
        echo "npm Version: $(npm --version 2>/dev/null || echo 'Not found')"
        echo "Python Version: $(python --version 2>/dev/null || python3 --version 2>/dev/null || echo 'Not found')"
        echo "Docker Version: $(docker --version 2>/dev/null || echo 'Not found')"
        echo "Clean Build: $CLEAN_BUILD"
        echo "Skip Tests: $SKIP_TESTS"
        echo "Verbose: $VERBOSE"
        echo "=================="
    } | tee -a "$BUILD_LOG_FILE"

    print_status "Build environment initialized"
}

# Clean build artifacts
clean_build_artifacts() {
    if [ "$CLEAN_BUILD" = true ]; then
        print_header "Cleaning Build Artifacts"

        print_step "Cleaning frontend artifacts..."
        if [ -d "frontend/node_modules/.vite" ]; then
            rm -rf frontend/node_modules/.vite
        fi
        if [ -d "frontend/dist" ]; then
            rm -rf frontend/dist
        fi

        print_step "Cleaning backend artifacts..."
        find backend -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
        find backend -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true

        print_step "Cleaning admin artifacts..."
        if [ -d "admin-app/dist" ]; then
            rm -rf admin-app/dist
        fi

        print_step "Cleaning Docker artifacts..."
        docker system prune -f > /dev/null 2>&1 || true

        print_status "Build artifacts cleaned"
    fi
}

# Function to build frontend
build_frontend() {
    print_header "Building Frontend Application"
    show_progress 1 4 "Frontend Build"

    cd frontend

    local start_time=$(date +%s)

    # Install dependencies if needed or if clean build
    if [ ! -d "node_modules" ] || [ "$CLEAN_BUILD" = true ]; then
        print_step "Installing frontend dependencies..."
        if [ "$VERBOSE" = true ]; then
            npm ci
        else
            npm ci > "../logs/frontend/npm-install.log" 2>&1
        fi
        print_status "Frontend dependencies installed"
    fi

    # Run linting if not production
    if [ "$ENVIRONMENT" != "production" ]; then
        print_step "Running frontend linting..."
        if npm run lint > "../logs/frontend/lint.log" 2>&1; then
            print_status "Frontend linting passed"
        else
            print_warning "Frontend linting failed. Check logs/frontend/lint.log"
        fi
    fi

    # Type checking
    print_step "Running TypeScript type checking..."
    if npm run type-check > "../logs/frontend/type-check.log" 2>&1; then
        print_status "TypeScript type checking passed"
    else
        print_warning "TypeScript type checking failed. Check logs/frontend/type-check.log"
    fi

    # Run build based on environment
    print_step "Building frontend for $ENVIRONMENT environment..."
    case $ENVIRONMENT in
        "production")
            if [ "$VERBOSE" = true ]; then
                npm run build:production
            else
                npm run build:production > "../logs/frontend/build.log" 2>&1
            fi
            ;;
        "staging")
            if [ "$VERBOSE" = true ]; then
                NODE_ENV=staging npm run build
            else
                NODE_ENV=staging npm run build > "../logs/frontend/build.log" 2>&1
            fi
            ;;
        *)
            if [ "$VERBOSE" = true ]; then
                npm run build
            else
                npm run build > "../logs/frontend/build.log" 2>&1
            fi
            ;;
    esac

    # Check build output
    if [ -d "dist" ] && [ "$(ls -A dist)" ]; then
        local build_size=$(du -sh dist | cut -f1)
        print_status "Frontend build completed (Size: $build_size)"

        # Bundle analysis for production
        if [ "$ENVIRONMENT" = "production" ]; then
            print_step "Generating bundle analysis..."
            npm run build:analyze > "../logs/frontend/bundle-analysis.log" 2>&1 || true
        fi
    else
        print_error "Frontend build failed - no output generated"
        cd ..
        exit 1
    fi

    local end_time=$(date +%s)
    local build_duration=$((end_time - start_time))
    print_info "Frontend build duration: ${build_duration}s"

    cd ..
}

# Function to build backend
build_backend() {
    print_header "Building Backend Application"
    show_progress 2 4 "Backend Build"

    cd backend

    local start_time=$(date +%s)

    # Determine Python command
    local python_cmd="python"
    if command -v python3 &> /dev/null; then
        python_cmd="python3"
    fi

    # Check if virtual environment exists or create one
    if [ ! -d "../venv" ] || [ "$CLEAN_BUILD" = true ]; then
        print_step "Creating Python virtual environment..."
        cd ..
        $python_cmd -m venv venv
        cd backend
        print_status "Virtual environment created"
    fi

    # Activate virtual environment
    print_step "Activating virtual environment..."
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        source ../venv/Scripts/activate
    else
        source ../venv/bin/activate
    fi

    # Upgrade pip
    print_step "Upgrading pip..."
    if [ "$VERBOSE" = true ]; then
        pip install --upgrade pip
    else
        pip install --upgrade pip > "../logs/backend/pip-upgrade.log" 2>&1
    fi

    # Install dependencies
    print_step "Installing backend dependencies..."
    if [ "$VERBOSE" = true ]; then
        pip install -r requirements.txt
    else
        pip install -r requirements.txt > "../logs/backend/pip-install.log" 2>&1
    fi

    # Install package in development mode
    print_step "Installing package in development mode..."
    if [ "$VERBOSE" = true ]; then
        pip install -e .
    else
        pip install -e . > "../logs/backend/pip-install-dev.log" 2>&1
    fi

    # Run linting if not production
    if [ "$ENVIRONMENT" != "production" ] && command -v flake8 &> /dev/null; then
        print_step "Running backend linting..."
        if flake8 . > "../logs/backend/lint.log" 2>&1; then
            print_status "Backend linting passed"
        else
            print_warning "Backend linting failed. Check logs/backend/lint.log"
        fi
    fi

    # Security check
    if command -v safety &> /dev/null; then
        print_step "Running security check..."
        if safety check > "../logs/backend/security.log" 2>&1; then
            print_status "Security check passed"
        else
            print_warning "Security vulnerabilities found. Check logs/backend/security.log"
        fi
    fi

    # Generate requirements lock file for production
    if [ "$ENVIRONMENT" = "production" ]; then
        print_step "Generating requirements lock file..."
        pip freeze > requirements.lock
        print_status "Requirements lock file generated"
    fi

    local end_time=$(date +%s)
    local build_duration=$((end_time - start_time))
    print_info "Backend build duration: ${build_duration}s"

    print_status "Backend build completed"
    cd ..
}

# Function to build admin app
build_admin() {
    print_header "Building Admin Application"
    show_progress 3 4 "Admin Build"

    cd admin-app

    local start_time=$(date +%s)

    # Install dependencies if needed or if clean build
    if [ ! -d "node_modules" ] || [ "$CLEAN_BUILD" = true ]; then
        print_step "Installing admin dependencies..."
        if [ "$VERBOSE" = true ]; then
            npm ci
        else
            npm ci > "../logs/admin/npm-install.log" 2>&1
        fi
        print_status "Admin dependencies installed"
    fi

    # Run linting if not production
    if [ "$ENVIRONMENT" != "production" ]; then
        print_step "Running admin linting..."
        if npm run lint > "../logs/admin/lint.log" 2>&1; then
            print_status "Admin linting passed"
        else
            print_warning "Admin linting failed. Check logs/admin/lint.log"
        fi
    fi

    # Run build based on environment
    print_step "Building admin for $ENVIRONMENT environment..."
    case $ENVIRONMENT in
        "production")
            if [ "$VERBOSE" = true ]; then
                npm run build:production
            else
                npm run build:production > "../logs/admin/build.log" 2>&1
            fi
            ;;
        *)
            if [ "$VERBOSE" = true ]; then
                npm run build
            else
                npm run build > "../logs/admin/build.log" 2>&1
            fi
            ;;
    esac

    # Check build output
    if [ -d "dist" ] && [ "$(ls -A dist)" ]; then
        local build_size=$(du -sh dist | cut -f1)
        print_status "Admin build completed (Size: $build_size)"
    else
        print_error "Admin build failed - no output generated"
        cd ..
        exit 1
    fi

    local end_time=$(date +%s)
    local build_duration=$((end_time - start_time))
    print_info "Admin build duration: ${build_duration}s"

    cd ..
}

# Function to build Docker images
build_docker() {
    print_header "Building Docker Images"
    show_progress 4 4 "Docker Build"

    local start_time=$(date +%s)

    # Set Docker build arguments
    local docker_args=""
    if [ "$CLEAN_BUILD" = true ]; then
        docker_args="--no-cache"
    fi

    # Enable BuildKit for better performance
    export DOCKER_BUILDKIT=1

    print_step "Building Docker images for $ENVIRONMENT environment..."

    case $ENVIRONMENT in
        "production")
            if [ "$VERBOSE" = true ]; then
                docker-compose -f docker-compose.prod.yml build $docker_args
            else
                docker-compose -f docker-compose.prod.yml build $docker_args > "logs/docker/build.log" 2>&1
            fi
            ;;
        "staging")
            if [ "$VERBOSE" = true ]; then
                docker-compose -f docker-compose.staging.yml build $docker_args
            else
                docker-compose -f docker-compose.staging.yml build $docker_args > "logs/docker/build.log" 2>&1
            fi
            ;;
        *)
            if [ "$VERBOSE" = true ]; then
                docker-compose -f docker-compose.dev.yml build $docker_args
            else
                docker-compose -f docker-compose.dev.yml build $docker_args > "logs/docker/build.log" 2>&1
            fi
            ;;
    esac

    # Verify images were built
    print_step "Verifying Docker images..."
    local images_built=0
    for image in $(docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "(ace-social|aceo)" | wc -l); do
        images_built=$((images_built + 1))
    done

    if [ $images_built -gt 0 ]; then
        print_status "Docker images built successfully ($images_built images)"

        # Show image sizes
        print_info "Docker image sizes:"
        docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep -E "(ace-social|aceo)" || true
    else
        print_error "No Docker images were built"
        exit 1
    fi

    local end_time=$(date +%s)
    local build_duration=$((end_time - start_time))
    print_info "Docker build duration: ${build_duration}s"
}

# Function to run tests
run_tests() {
    if [ "$SKIP_TESTS" = true ]; then
        print_info "Skipping tests (--no-tests flag provided)"
        return 0
    fi

    print_header "Running Test Suite"

    local test_start_time=$(date +%s)
    local tests_passed=0
    local tests_failed=0

    # Frontend tests
    print_step "Running frontend tests..."
    cd frontend
    if [ "$VERBOSE" = true ]; then
        if npm run test; then
            print_status "Frontend tests passed"
            tests_passed=$((tests_passed + 1))
        else
            print_warning "Frontend tests failed"
            tests_failed=$((tests_failed + 1))
        fi
    else
        if npm run test > "../logs/frontend/tests.log" 2>&1; then
            print_status "Frontend tests passed"
            tests_passed=$((tests_passed + 1))
        else
            print_warning "Frontend tests failed. Check logs/frontend/tests.log"
            tests_failed=$((tests_failed + 1))
        fi
    fi
    cd ..

    # Backend tests
    print_step "Running backend tests..."
    cd backend
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        source ../venv/Scripts/activate
    else
        source ../venv/bin/activate
    fi

    if [ "$VERBOSE" = true ]; then
        if python -m pytest -v; then
            print_status "Backend tests passed"
            tests_passed=$((tests_passed + 1))
        else
            print_warning "Backend tests failed"
            tests_failed=$((tests_failed + 1))
        fi
    else
        if python -m pytest > "../logs/backend/tests.log" 2>&1; then
            print_status "Backend tests passed"
            tests_passed=$((tests_passed + 1))
        else
            print_warning "Backend tests failed. Check logs/backend/tests.log"
            tests_failed=$((tests_failed + 1))
        fi
    fi
    cd ..

    # Admin tests
    print_step "Running admin tests..."
    cd admin-app
    if [ "$VERBOSE" = true ]; then
        if npm run test; then
            print_status "Admin tests passed"
            tests_passed=$((tests_passed + 1))
        else
            print_warning "Admin tests failed"
            tests_failed=$((tests_failed + 1))
        fi
    else
        if npm run test > "../logs/admin/tests.log" 2>&1; then
            print_status "Admin tests passed"
            tests_passed=$((tests_passed + 1))
        else
            print_warning "Admin tests failed. Check logs/admin/tests.log"
            tests_failed=$((tests_failed + 1))
        fi
    fi
    cd ..

    local test_end_time=$(date +%s)
    local test_duration=$((test_end_time - test_start_time))

    print_info "Test Summary: $tests_passed passed, $tests_failed failed (Duration: ${test_duration}s)"

    if [ $tests_failed -gt 0 ] && [ "$ENVIRONMENT" = "production" ]; then
        print_error "Tests failed in production build. Aborting."
        exit 1
    fi
}

# Function to create environment file
create_env_file() {
    print_step "Creating environment configuration..."

    local env_file=".env.${ENVIRONMENT}"

    if [ ! -f "$env_file" ]; then
        print_step "Generating $env_file..."

        # Generate secure random keys
        local secret_key=$(openssl rand -hex 32 2>/dev/null || echo "change_this_secret_key_in_production_$(date +%s)")
        local mongo_password=$(openssl rand -base64 32 2>/dev/null || echo "secure_mongo_password_$(date +%s)")
        local redis_password=$(openssl rand -base64 32 2>/dev/null || echo "secure_redis_password_$(date +%s)")

        cat > "$env_file" << EOF
# ACE Social Platform - Environment Configuration
# Environment: ${ENVIRONMENT}
# Generated: $(date)
# WARNING: Update API keys and passwords before deployment

# Application Settings
ENVIRONMENT=${ENVIRONMENT}
DEBUG=$([ "$ENVIRONMENT" = "production" ] && echo "false" || echo "true")
SECRET_KEY=${secret_key}

# Database Configuration
MONGODB_URL=mongodb://admin:${mongo_password}@localhost:27017/ace_social_${ENVIRONMENT}?authSource=admin
REDIS_URL=redis://:${redis_password}@localhost:6379/0

# Database Credentials
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=${mongo_password}
REDIS_PASSWORD=${redis_password}

# API Keys (REQUIRED - Update these values)
OPENAI_API_KEY=your_openai_api_key_here
SENDGRID_API_KEY=your_sendgrid_api_key_here
LEMON_SQUEEZY_API_KEY=your_lemon_squeezy_api_key_here
LEMON_SQUEEZY_WEBHOOK_SECRET=your_webhook_secret_here

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000","http://localhost:3001","http://localhost:5173"]

# Performance Settings
MAX_UPLOAD_SIZE=10485760
RATE_LIMIT_PER_MINUTE=100

# Security Settings
SESSION_TIMEOUT=3600
PASSWORD_MIN_LENGTH=8
ENABLE_2FA=false

# Logging
LOG_LEVEL=$([ "$ENVIRONMENT" = "production" ] && echo "INFO" || echo "DEBUG")
LOG_FORMAT=json

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_COMPRESSION=true
EOF

        print_status "Environment file created: $env_file"
        print_warning "⚠️  IMPORTANT: Update API keys and review security settings in $env_file"

        # Set appropriate permissions
        chmod 600 "$env_file"
        print_info "Environment file permissions set to 600 (owner read/write only)"

    else
        print_status "Environment file already exists: $env_file"

        # Validate existing environment file
        if grep -q "your_.*_api_key_here" "$env_file"; then
            print_warning "⚠️  Environment file contains placeholder API keys. Please update them."
        fi
    fi
}

# Function to validate build output
validate_build_output() {
    print_header "Validating Build Output"

    local validation_errors=0

    # Check frontend build
    if [ -d "frontend/dist" ] && [ "$(ls -A frontend/dist)" ]; then
        local frontend_size=$(du -sh frontend/dist | cut -f1)
        print_status "Frontend build output validated (Size: $frontend_size)"

        # Check for critical files
        local critical_files=("index.html" "assets")
        for file in "${critical_files[@]}"; do
            if [ ! -e "frontend/dist/$file" ]; then
                print_error "Missing critical frontend file: $file"
                validation_errors=$((validation_errors + 1))
            fi
        done
    else
        print_error "Frontend build output missing or empty"
        validation_errors=$((validation_errors + 1))
    fi

    # Check admin build
    if [ -d "admin-app/dist" ] && [ "$(ls -A admin-app/dist)" ]; then
        local admin_size=$(du -sh admin-app/dist | cut -f1)
        print_status "Admin build output validated (Size: $admin_size)"
    else
        print_error "Admin build output missing or empty"
        validation_errors=$((validation_errors + 1))
    fi

    # Check backend installation
    cd backend
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        source ../venv/Scripts/activate
    else
        source ../venv/bin/activate
    fi

    if python -c "import app; print('Backend import successful')" > /dev/null 2>&1; then
        print_status "Backend installation validated"
    else
        print_error "Backend installation validation failed"
        validation_errors=$((validation_errors + 1))
    fi
    cd ..

    # Check Docker images if built
    if docker images | grep -q "ace-social\|aceo"; then
        print_status "Docker images validated"
    else
        print_warning "No Docker images found (may not have been built)"
    fi

    if [ $validation_errors -eq 0 ]; then
        print_status "All build outputs validated successfully"
        return 0
    else
        print_error "Build validation failed with $validation_errors errors"
        return 1
    fi
}

# Performance monitoring
monitor_build_performance() {
    print_header "Build Performance Summary"

    local total_end_time=$(date +%s)
    local total_duration=$((total_end_time - BUILD_START_TIME))

    print_info "Total build duration: ${total_duration}s"

    # Calculate build efficiency
    local efficiency="Good"
    if [ $total_duration -gt 300 ]; then  # 5 minutes
        efficiency="Needs Optimization"
    elif [ $total_duration -gt 180 ]; then  # 3 minutes
        efficiency="Acceptable"
    fi

    print_info "Build efficiency: $efficiency"

    # Show disk usage
    local project_size=$(du -sh . 2>/dev/null | cut -f1)
    print_info "Project size: $project_size"

    # Show build artifacts size
    local artifacts_size=0
    if [ -d "frontend/dist" ]; then
        artifacts_size=$((artifacts_size + $(du -s frontend/dist | cut -f1)))
    fi
    if [ -d "admin-app/dist" ]; then
        artifacts_size=$((artifacts_size + $(du -s admin-app/dist | cut -f1)))
    fi

    if [ $artifacts_size -gt 0 ]; then
        local artifacts_mb=$((artifacts_size / 1024))
        print_info "Build artifacts size: ${artifacts_mb}MB"

        # Check if under 2MB target for production
        if [ "$ENVIRONMENT" = "production" ] && [ $artifacts_mb -gt 2048 ]; then
            print_warning "Build artifacts exceed 2MB target for production"
        fi
    fi
}

# Generate build report
generate_build_report() {
    local report_file="logs/build/build_report_${BUILD_TIMESTAMP}.json"

    print_step "Generating build report..."

    cat > "$report_file" << EOF
{
  "build": {
    "version": "$SCRIPT_VERSION",
    "environment": "$ENVIRONMENT",
    "timestamp": "$(date -Iseconds)",
    "duration": $(($(date +%s) - BUILD_START_TIME)),
    "clean_build": $CLEAN_BUILD,
    "skip_tests": $SKIP_TESTS,
    "verbose": $VERBOSE
  },
  "system": {
    "os": "$(uname -s)",
    "arch": "$(uname -m)",
    "node_version": "$(node --version 2>/dev/null || echo 'N/A')",
    "python_version": "$(python --version 2>/dev/null || python3 --version 2>/dev/null || echo 'N/A')",
    "docker_version": "$(docker --version 2>/dev/null || echo 'N/A')"
  },
  "artifacts": {
    "frontend_size": "$([ -d frontend/dist ] && du -sh frontend/dist | cut -f1 || echo 'N/A')",
    "admin_size": "$([ -d admin-app/dist ] && du -sh admin-app/dist | cut -f1 || echo 'N/A')",
    "project_size": "$(du -sh . 2>/dev/null | cut -f1 || echo 'N/A')"
  }
}
EOF

    print_status "Build report generated: $report_file"
}

# Main build process
main() {
    print_header "ACE Social Platform Build Process v${SCRIPT_VERSION}"
    print_info "Environment: $ENVIRONMENT"
    print_info "Timestamp: $(date)"
    print_info "Build ID: $BUILD_TIMESTAMP"

    # Validate inputs and environment
    validate_environment "$ENVIRONMENT"
    validate_prerequisites
    validate_project_structure

    # Initialize build
    initialize_build

    # Clean if requested
    clean_build_artifacts

    # Create environment configuration
    create_env_file

    # Build all components
    build_frontend
    build_backend
    build_admin

    # Run tests (unless skipped or production without explicit test flag)
    if [ "$ENVIRONMENT" != "production" ] || [ "$SKIP_TESTS" = false ]; then
        run_tests
    fi

    # Build Docker images
    build_docker

    # Validate build output
    if ! validate_build_output; then
        print_error "Build validation failed"
        exit 1
    fi

    # Performance monitoring
    monitor_build_performance

    # Generate build report
    generate_build_report

    print_header "Build Completed Successfully! 🎉"

    # Show next steps
    print_info "Next Steps:"
    case $ENVIRONMENT in
        "production")
            echo "  1. Review and update API keys in .env.production"
            echo "  2. Run security audit: npm run security:audit"
            echo "  3. Deploy: npm run start:prod"
            echo "  4. Monitor: npm run logs"
            ;;
        "staging")
            echo "  1. Update environment variables in .env.staging"
            echo "  2. Deploy: npm run deploy:staging"
            echo "  3. Run integration tests"
            ;;
        *)
            echo "  1. Start development: npm run dev"
            echo "  2. Or with Docker: npm run start:dev"
            echo "  3. View logs: npm run logs"
            ;;
    esac

    print_info "Build logs available in: $BUILD_LOG_FILE"
    print_info "Build duration: $(($(date +%s) - BUILD_START_TIME))s"
}

# Error handling
trap 'print_error "Build failed at line $LINENO. Check logs for details."; exit 1' ERR

# Script execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi

<!-- @since 2024-1-1 to 2025-25-7 -->
# Email System Documentation

This document provides an overview of the B2B Influencer Too<PERSON>'s email system, including templates, sending functionality, and best practices.

## Email Templates

### Base Templates
- `base.html` - The foundation template that all other email templates extend

### User Account Templates
- `welcome.html` - Sent to new users upon registration
- `email_verification.html` - Sent to verify a user's email address
- `email_verification_reminder.html` - Follow-up email for users who haven't verified their email
- `password_reset.html` - Sent when a user requests a password reset
- `magic_link.html` - Sent for passwordless login
- `account_lockout.html` - Sent when an account is locked due to security concerns
- `account_deletion.html` - Sent when a user requests account deletion

### Team Collaboration Templates
- `team_invitation.html` - Sent when a user is invited to join a team
- `calendar_invitation.html` - Sent for calendar sharing/collaboration

### Content Management Templates
- `content_scheduled.html` - Notification when content is scheduled for publication
- `content_review.html` - Sent when content needs review
- `content_failed.html` - Sent when content publishing fails
- `client_comment.html` - Sent when a client comments on content

### Subscription/Billing Templates
- `trial_started.html` - Sent when a user starts a free trial
- `trial_ending_soon.html` - Reminder that a trial is ending soon
- `trial_ended.html` - Notification that a trial has ended
- `trial_converted.html` - Sent when a trial converts to a paid subscription
- `subscription_created.html` - Confirmation of subscription creation
- `subscription_cancelled.html` - Confirmation of subscription cancellation
- `subscription_renewed.html` - Notification of subscription renewal
- `payment_failed.html` - Notification of payment failure

### Analytics Templates
- `analytics_report.html` - Periodic analytics reports

### System Templates
- `notification.html` - Generic notification template
- `email_digest.html` - Daily/weekly digest of notifications
- `rate_limit.html` - Notification when a rate limit is reached
- `admin_alert.html` - Alerts sent to administrators

## Email Services

### Core Email Service (`email.py`)
The core email service handles the actual sending of emails using SendGrid. It provides:
- Template rendering with Jinja2
- CSS inlining for better email client compatibility
- Support for attachments and categories
- Proper error handling and logging

### Email Notification Service (`email_notification_enhanced.py`)
The enhanced email notification service handles sending email notifications for all system events. It provides:
- User preference-based email sending
- Support for digest mode (daily/weekly)
- Notification type-specific templates
- Preview text customization
- Enhanced error handling

### Email Digest Service (`email_digest.py`)
The email digest service handles collecting and sending digest emails for users who have enabled digest mode. It provides:
- Storage of notifications for later inclusion in digests
- Daily and weekly digest sending
- Cleanup of old digest notifications

### Email Verification Reminder Service (`email_verification_reminder.py`)
The email verification reminder service handles sending follow-up emails for users who haven't verified their email. It provides:
- Configurable reminder intervals
- Maximum reminder limit
- New verification token generation

### Enhanced Email Service (`email_enhanced.py`)
The enhanced email service provides additional email types and functionality:
- Payment failed emails
- Subscription cancelled emails
- Subscription renewed emails
- Account deletion emails
- Rate limit emails
- Content failed emails

## Scheduler Integration (`scheduler_enhanced.py`)
The enhanced scheduler service integrates with the email system to run periodic tasks:
- Daily digest emails
- Weekly digest emails
- Email verification reminders
- Cleanup of old digest notifications

## Best Practices

### Email Template Design
- All templates extend the base template for consistent styling
- Responsive design for mobile compatibility
- Dark mode support
- Accessibility considerations (color contrast, alt text)
- Clear call-to-action buttons
- Consistent branding

### Email Sending
- Respect user preferences
- Include proper categorization for analytics
- Handle errors gracefully
- Log all email sending activities
- Use preview text for better inbox experience

### Email Digests
- Group notifications by type
- Include timestamps
- Provide action links
- Allow users to manage notification preferences

## Database Schema

### Digest Notifications Collection
```
{
  "_id": ObjectId,
  "user_id": ObjectId,
  "notification_type": String,
  "title": String,
  "message": String,
  "entity_id": String (optional),
  "entity_type": String (optional),
  "data": Object (optional),
  "created_at": DateTime,
  "sent": Boolean
}
```

### User Email Preferences
```
{
  "email_notifications": Boolean,
  "email_digest": String ("none", "daily", "weekly"),
  "content_review_notifications": Boolean,
  "content_published_notifications": Boolean,
  "content_scheduled_notifications": Boolean,
  "content_failed_notifications": Boolean,
  "analytics_report_notifications": Boolean,
  "calendar_invitation_notifications": Boolean,
  "account_notifications": Boolean,
  "billing_notifications": Boolean,
  "system_notifications": Boolean,
  "rate_limit_notifications": Boolean
}
```

## Testing

To test the email system:

1. **Template Testing**:
   ```python
   # Render a template with test data
   template = env.get_template("emails/payment_failed.html")
   html_content = template.render(test_data)
   ```

2. **Email Sending Testing**:
   ```python
   # Send a test email
   await email_service.send_email(
       recipient_email="<EMAIL>",
       subject="Test Email",
       template_name="notification",
       template_data={"message": "This is a test"}
   )
   ```

3. **Digest Testing**:
   ```python
   # Test digest email sending
   await send_daily_digest_emails()
   ```

## Troubleshooting

Common issues and solutions:

1. **Emails not being sent**:
   - Check SendGrid API key
   - Verify user email preferences
   - Check for errors in the logs

2. **Template rendering issues**:
   - Verify template exists
   - Check template data
   - Test template rendering separately

3. **Digest emails not being sent**:
   - Verify scheduler is running
   - Check user digest preferences
   - Verify notifications are being stored correctly

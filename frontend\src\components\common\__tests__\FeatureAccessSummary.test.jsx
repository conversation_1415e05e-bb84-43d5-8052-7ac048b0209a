/**
 * FeatureAccessSummary Component Test Suite
 * Comprehensive testing for enterprise-grade feature access summary
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import FeatureAccessSummary from '../FeatureAccessSummary';

// Test theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock contexts
const mockAuth = {
  user: {
    id: 'test-user',
    subscription: {
      plan_name: 'Creator Plan'
    }
  },
  userFeatures: {
    plan_id: 'creator',
    is_appsumo_lifetime: false,
    is_trial: false,
    features: ['basic_posting', 'analytics'],
    feature_limits: {
      monthly_posts: 100,
      social_platforms: 3,
      user_accounts: 1,
      analytics_history_days: 30,
      brand_profiles: 1
    }
  },
  hasFeature: jest.fn(),
  getUserRole: jest.fn()
};

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/test' })
}));

describe('FeatureAccessSummary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAuth.hasFeature.mockReturnValue(true);
    mockAuth.getUserRole.mockReturnValue('viewer');
  });

  describe('Basic Functionality', () => {
    test('renders with default props', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      expect(screen.getByText('Your Subscription')).toBeInTheDocument();
      expect(screen.getByText('Creator Plan')).toBeInTheDocument();
    });

    test('displays usage data correctly', () => {
      const usageData = {
        monthly_posts: 75,
        social_platforms: 2,
        user_accounts: 1
      };

      render(
        <TestWrapper>
          <FeatureAccessSummary usageData={usageData} />
        </TestWrapper>
      );

      // Should display usage information
      expect(screen.getByText('Usage Summary')).toBeInTheDocument();
    });

    test('shows manage button', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      expect(screen.getByText('Manage')).toBeInTheDocument();
    });

    test('shows view all features button when enabled', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary showViewAllButton />
        </TestWrapper>
      );

      expect(screen.getByText('View All Features')).toBeInTheDocument();
    });

    test('hides view all features button when disabled', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary showViewAllButton={false} />
        </TestWrapper>
      );

      expect(screen.queryByText('View All Features')).not.toBeInTheDocument();
    });
  });

  describe('Plan Display', () => {
    test('displays creator plan correctly', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      expect(screen.getByText('Creator Plan')).toBeInTheDocument();
    });

    test('displays accelerator plan correctly', () => {
      mockAuth.userFeatures.plan_id = 'accelerator';

      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      expect(screen.getByText('Accelerator Plan')).toBeInTheDocument();
    });

    test('displays dominator plan correctly', () => {
      mockAuth.userFeatures.plan_id = 'dominator';

      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      expect(screen.getByText('Dominator Plan')).toBeInTheDocument();
    });

    test('shows trial indicator when user is on trial', () => {
      mockAuth.userFeatures.is_trial = true;

      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      expect(screen.getByText(/Trial/)).toBeInTheDocument();
    });
  });

  describe('AppSumo Integration', () => {
    test('shows AppSumo badge for AppSumo users', () => {
      mockAuth.userFeatures.is_appsumo_lifetime = true;
      mockAuth.user.subscription.plan_name = 'AppSumo Plus';

      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      expect(screen.getByText('AppSumo Plus')).toBeInTheDocument();
    });

    test('shows AppSumo upgrade link for AppSumo users', () => {
      mockAuth.userFeatures.is_appsumo_lifetime = true;

      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      const manageButton = screen.getByText('Manage');
      expect(manageButton.closest('a')).toHaveAttribute('href', '/appsumo/upgrade-options');
    });

    test('shows regular billing link for non-AppSumo users', () => {
      mockAuth.userFeatures.is_appsumo_lifetime = false;

      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      const manageButton = screen.getByText('Manage');
      expect(manageButton.closest('a')).toHaveAttribute('href', '/billing/plans');
    });
  });

  describe('Usage Limits Display', () => {
    test('displays highlighted limits correctly', () => {
      const usageData = {
        monthly_posts: 75,
        social_platforms: 2
      };

      render(
        <TestWrapper>
          <FeatureAccessSummary 
            highlightedLimits={['monthly_posts', 'social_platforms']}
            usageData={usageData}
          />
        </TestWrapper>
      );

      // Should show usage indicators
      expect(screen.getByText('Usage Summary')).toBeInTheDocument();
    });

    test('shows expand/collapse for all limits when available', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <FeatureAccessSummary showAllLimits />
        </TestWrapper>
      );

      const expandButton = screen.getByText(/Show all limits/);
      expect(expandButton).toBeInTheDocument();

      await user.click(expandButton);
      expect(screen.getByText(/Hide all limits/)).toBeInTheDocument();
    });

    test('handles high usage warnings correctly', () => {
      const usageData = {
        monthly_posts: 95 // 95% of 100 limit
      };

      render(
        <TestWrapper>
          <FeatureAccessSummary 
            usageData={usageData}
            enableUsageAlerts
          />
        </TestWrapper>
      );

      // Should show usage warning for high usage
      expect(screen.getByText('Key Metrics')).toBeInTheDocument();
    });
  });

  describe('Variants', () => {
    test('renders compact variant correctly', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary variant="compact" />
        </TestWrapper>
      );

      expect(screen.getByText('Creator Plan')).toBeInTheDocument();
    });

    test('renders detailed variant with tabs', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary variant="detailed" />
        </TestWrapper>
      );

      expect(screen.getByText('Usage Summary')).toBeInTheDocument();
      expect(screen.getByText('Feature Overview')).toBeInTheDocument();
    });

    test('shows analytics tab when trend analysis enabled', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary 
            variant="detailed"
            enableTrendAnalysis
          />
        </TestWrapper>
      );

      expect(screen.getByText('Analytics')).toBeInTheDocument();
    });
  });

  describe('Interactive Features', () => {
    test('handles tab changes correctly', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <FeatureAccessSummary variant="detailed" />
        </TestWrapper>
      );

      const featureOverviewTab = screen.getByText('Feature Overview');
      await user.click(featureOverviewTab);

      expect(screen.getByText('Feature Access Overview')).toBeInTheDocument();
    });

    test('handles limit clicks when interactive', async () => {
      const user = userEvent.setup();
      const onLimitClick = jest.fn();

      render(
        <TestWrapper>
          <FeatureAccessSummary 
            variant="detailed"
            onLimitClick={onLimitClick}
            usageData={{ monthly_posts: 50 }}
          />
        </TestWrapper>
      );

      // Click on a limit card if available
      const limitCards = screen.queryAllByText(/Monthly Posts/);
      if (limitCards.length > 0) {
        await user.click(limitCards[0]);
        expect(onLimitClick).toHaveBeenCalled();
      }
    });

    test('handles upgrade clicks correctly', async () => {
      const user = userEvent.setup();
      const onUpgradeClick = jest.fn();

      render(
        <TestWrapper>
          <FeatureAccessSummary onUpgradeClick={onUpgradeClick} />
        </TestWrapper>
      );

      const manageButton = screen.getByText('Manage');
      await user.click(manageButton);

      expect(onUpgradeClick).toHaveBeenCalled();
    });
  });

  describe('Analytics Integration', () => {
    test('tracks analytics events when enabled', () => {
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <FeatureAccessSummary 
            enableAnalytics 
            onAnalytics={onAnalytics}
          />
        </TestWrapper>
      );

      // Analytics should be tracked for component render
      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          component: 'FeatureAccessSummary'
        })
      );
    });

    test('tracks expand/collapse events', async () => {
      const user = userEvent.setup();
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <FeatureAccessSummary 
            showAllLimits
            enableAnalytics 
            onAnalytics={onAnalytics}
          />
        </TestWrapper>
      );

      const expandButton = screen.getByText(/Show all limits/);
      await user.click(expandButton);

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'limits_expanded'
        })
      );
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary 
            ariaLabel="Custom accessibility label"
          />
        </TestWrapper>
      );

      expect(screen.getByLabelText('Custom accessibility label')).toBeInTheDocument();
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <FeatureAccessSummary showAllLimits />
        </TestWrapper>
      );

      const expandButton = screen.getByText(/Show all limits/);
      expandButton.focus();
      
      expect(document.activeElement).toBe(expandButton);
      
      await user.keyboard('{Enter}');
      // Should trigger expand action
    });
  });

  describe('Error Handling', () => {
    test('handles missing user features gracefully', () => {
      mockAuth.userFeatures = null;

      render(
        <TestWrapper>
          <FeatureAccessSummary />
        </TestWrapper>
      );

      // Should still render with default plan
      expect(screen.getByText('Creator Plan')).toBeInTheDocument();
    });

    test('handles missing usage data gracefully', () => {
      render(
        <TestWrapper>
          <FeatureAccessSummary usageData={{}} />
        </TestWrapper>
      );

      // Should render without errors
      expect(screen.getByText('Your Subscription')).toBeInTheDocument();
    });
  });
});

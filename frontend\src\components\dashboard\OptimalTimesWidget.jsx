// @since 2024-1-1 to 2025-25-7
import { useState, useMemo, useCallback, useRef, useEffect, memo, forwardRef, useImperativeHandle } from "react";
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Divider,
  Button,
  Chip,
  CircularProgress,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery,
  alpha,
  Grid,
  Alert,
  AlertTitle,
  Fade,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Card,
  Badge
} from "@mui/material";
import {
  Schedule as ScheduleIcon,
  AccessTime as AccessTimeIcon,

  Analytics as AnalyticsIcon,
  Timeline as TimelineIcon,

  Refresh as RefreshIcon,
  Download as ExportIcon,
  Upgrade as UpgradeIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,

  Star as StarIcon,

} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import { CustomCardContent, CustomCardHeader } from "../common";
import GlassmorphicCard from "../common/GlassmorphicCard";
import DashboardCard from "./DashboardCard";
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};



// Animation configurations
const ANIMATION_CONFIG = {
  DURATION: 300,
  EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  STAGGER_DELAY: 50
};

/**
 * Enhanced OptimalTimesWidget Component - Enterprise-grade optimal posting times dashboard
 * Features: Plan-based optimal times limitations, real-time time analysis, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced optimal times insights and interactive time slot exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.data - Optimal times data
 * @param {boolean} [props.loading=false] - Loading state
 * @param {string} [props.title='Optimal Posting Times'] - Widget title
 * @param {Function} [props.onExpand] - Expand callback
 * @param {string} [props.variant='detailed'] - Widget display variant
 * @param {boolean} [props.enableAnalytics=false] - Enable analytics features
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.enableScheduling=false] - Enable scheduling integration
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onTimeSlotSelect] - Time slot selection callback
 * @param {Function} [props.onAnalyticsRequest] - Analytics request callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onScheduleContent] - Schedule content callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Array} [props.platforms] - Available platforms
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const OptimalTimesWidget = memo(forwardRef(({
  data,
  loading = false,
  title = "Optimal Posting Times",


  enableAnalytics = false,
  enableExport = false,

  realTimeUpdates = false,
  onTimeSlotSelect,
  onAnalyticsRequest,
  onExport,
  onScheduleContent,
  onRefresh,


  className = '',
  style = {},
  testId = 'optimal-times-widget',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();
  const navigate = useNavigate();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, isScreenReaderActive, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Refs for enhanced functionality
  const widgetRef = useRef(null);

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading || subscriptionLoading,
    refreshing: false,
    showAnalyticsPanel: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showSchedulingDialog: false,
    selectedTimeSlot: null,
    selectedPlatform: 'all',
    viewMode: 'recommendations',
    lastUpdated: null,
    animationKey: 0,
    errors: {},
    notifications: [],
    // Optimal times state
    currentAnalysisType: 'best_posting_times',
    timeSlotFilter: 'all',
    confidenceFilter: 'all',
    platformFilter: 'all',
    hoveredTimeSlot: null,
    expandedRecommendations: false
  });

  // Optimal times data state
  const [optimalTimesData, setOptimalTimesData] = useState({
    recommendations: data?.combined_recommendations || {},
    historicalAnalysis: data?.historical_analysis || {},
    platformSpecific: data?.platform_specific || {},
    audienceActivity: data?.audience_activity || {},
    competitorAnalysis: data?.competitor_analysis || {},
    insights: null,
    analytics: null
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);

  /**
   * Enhanced plan-based optimal times validation - Production Ready
   */
  const validateOptimalTimes = useCallback(() => {
    if (!subscription) {
      return {
        canViewTimes: false,
        hasTimesAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { timeSlots: 0, platforms: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based optimal times limits
    const planLimits = {
      creator: {
        timeSlots: 3,
        platforms: 1,
        features: ['basic_optimal_times'],
        analytics: false,
        export: false,
        scheduling: true,
        aiRecommendations: false,
        competitorAnalysis: false,
        realTimeTracking: false
      },
      accelerator: {
        timeSlots: 10,
        platforms: 5,
        features: ['basic_optimal_times', 'advanced_analytics', 'platform_specific'],
        analytics: true,
        export: true,
        scheduling: true,
        aiRecommendations: false,
        competitorAnalysis: true,
        realTimeTracking: true
      },
      dominator: {
        timeSlots: -1,
        platforms: -1,
        features: ['basic_optimal_times', 'advanced_analytics', 'platform_specific', 'ai_recommendations'],
        analytics: true,
        export: true,
        scheduling: true,
        aiRecommendations: true,
        competitorAnalysis: true,
        realTimeTracking: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = Object.keys(optimalTimesData.recommendations).length || 0;
    const limit = currentPlanLimits.timeSlots;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewTimes: true,
      hasTimesAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, optimalTimesData.recommendations]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const timesLimits = validateOptimalTimes();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasAnalytics: timesLimits.planLimits.analytics,
      hasExport: timesLimits.planLimits.export,
      hasScheduling: timesLimits.planLimits.scheduling,
      hasAIRecommendations: timesLimits.planLimits.aiRecommendations,
      hasCompetitorAnalysis: timesLimits.planLimits.competitorAnalysis,
      hasRealTimeTracking: timesLimits.planLimits.realTimeTracking,
      maxTimeSlots: timesLimits.planLimits.timeSlots,
      maxPlatforms: timesLimits.planLimits.platforms,
      availableFeatures: timesLimits.planLimits.features,
      refreshInterval: timesLimits.planLimits.realTimeTracking ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateOptimalTimes]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        lastUpdated: new Date().toISOString(),
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Optimal times data refreshed successfully');
      announceToScreenReader('Optimal posting times have been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh optimal times: ${error.message}`);
      announceToScreenReader('Failed to refresh optimal times data');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced analytics request handler - Production Ready
   */
  const handleAnalyticsRequest = useCallback(async () => {
    if (!subscriptionFeatures.hasAnalytics) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, showAnalyticsPanel: true }));

    try {
      if (onAnalyticsRequest) {
        const analytics = await onAnalyticsRequest();
        setOptimalTimesData(prev => ({ ...prev, analytics }));
      }

      announceToScreenReader('Optimal times analytics have been generated');
    } catch (error) {
      showErrorNotification(`Failed to generate analytics: ${error.message}`);
      announceToScreenReader('Failed to generate optimal times analytics');
    }
  }, [subscriptionFeatures.hasAnalytics, onAnalyticsRequest, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'csv') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, optimalTimesData);
      }

      showSuccessNotification(`Optimal times data exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Optimal times data has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export data: ${error.message}`);
      announceToScreenReader('Failed to export optimal times data');
    }
  }, [subscriptionFeatures.hasExport, optimalTimesData, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced time slot selection handler - Production Ready
   */
  const handleTimeSlotSelect = useCallback((timeSlot, day) => {
    setState(prev => ({ ...prev, selectedTimeSlot: { ...timeSlot, day } }));

    if (onTimeSlotSelect) {
      onTimeSlotSelect(timeSlot, day);
    }

    announceToScreenReader(`Selected time slot: ${timeSlot.time_slot} on ${day}`);
  }, [onTimeSlotSelect, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportData: handleExport,
    requestAnalytics: handleAnalyticsRequest,
    getOptimalTimesData: () => optimalTimesData,
    getTimesLimits: validateOptimalTimes,
    focus: () => setFocusToElement(widgetRef.current),
    announce: (message) => announceToScreenReader(message)
  }), [
    optimalTimesData,
    validateOptimalTimes,
    handleRefresh,
    handleExport,
    handleAnalyticsRequest,
    setFocusToElement,
    announceToScreenReader
  ]);



  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Optimal posting times widget`,
      'aria-description': ariaDescription || `Shows optimal times for posting content based on audience activity`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, realTimeUpdates]);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced optimal times features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Advanced time analytics',
        'Platform-specific recommendations',
        'AI-powered insights',
        'Competitor analysis',
        'Data export capabilities',
        'Real-time tracking'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  /**
   * Enhanced keyboard navigation - Production Ready
   */
  const handleKeyDown = useCallback((event) => {
    if (!isScreenReaderActive) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        if (event.target.getAttribute('role') === 'button') {
          event.preventDefault();
          event.target.click();
        }
        break;
      case 'Escape':
        if (state.showUpgradeDialog) {
          setState(prev => ({ ...prev, showUpgradeDialog: false }));
        }
        if (state.showAnalyticsPanel) {
          setState(prev => ({ ...prev, showAnalyticsPanel: false }));
        }
        break;
    }
  }, [isScreenReaderActive, state.showUpgradeDialog, state.showAnalyticsPanel]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTimeTracking) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTimeTracking, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced confidence color mapping - Production Ready
   */
  const getConfidenceColor = useCallback((confidence) => {
    switch (confidence) {
      case "High":
        return theme.palette.success.main;
      case "Medium":
        return theme.palette.warning.main;
      case "Low":
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  }, [theme.palette]);

  /**
   * Enhanced view all handler - Production Ready
   */
  const handleViewAll = useCallback(() => {
    navigate("/scheduling/optimal-times");
    announceToScreenReader('Navigating to detailed optimal times view');
  }, [navigate, announceToScreenReader]);

  /**
   * Enhanced content creation handler - Production Ready
   */
  const handleCreateContent = useCallback((day, timeSlot) => {
    if (!subscriptionFeatures.hasScheduling) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      // Extract hours from time slot (e.g., "08:00-09:59" -> 8)
      const startHour = parseInt(timeSlot.time_slot.split(":")[0], 10);

      // Get current date
      const now = new Date();

      // Calculate target day (0 = Sunday, 6 = Saturday in JavaScript)
      const dayMap = {
        Monday: 1,
        Tuesday: 2,
        Wednesday: 3,
        Thursday: 4,
        Friday: 5,
        Saturday: 6,
        Sunday: 0,
      };

      const targetDay = dayMap[day];
      const currentDay = now.getDay();

      // Calculate days to add
      let daysToAdd = targetDay - currentDay;
      if (daysToAdd <= 0) {
        daysToAdd += 7; // Move to next week if target day is today or earlier
      }

      // Create target date
      const targetDate = new Date(now);
      targetDate.setDate(now.getDate() + daysToAdd);
      targetDate.setHours(startHour, 0, 0, 0);

      // Navigate to content creation with scheduled time
      navigate("/content/create", {
        state: {
          scheduledTime: targetDate.toISOString(),
          platform: data?.platform === "all" ? null : data?.platform,
        },
      });

      if (onScheduleContent) {
        onScheduleContent(day, timeSlot, targetDate);
      }

      announceToScreenReader(`Scheduling content for ${day} at ${timeSlot.time_slot}`);
    } catch (error) {
      showErrorNotification(`Failed to schedule content: ${error.message}`);
      announceToScreenReader('Failed to schedule content');
    }
  }, [subscriptionFeatures.hasScheduling, navigate, data?.platform, onScheduleContent, announceToScreenReader, showErrorNotification]);

  /**
   * Enhanced best recommendations calculation - Production Ready
   */
  const getBestRecommendations = useCallback(() => {
    if (!data || !data.combined_recommendations) return [];

    const today = new Date().toLocaleDateString("en-US", { weekday: "long" });
    const tomorrow = new Date(
      Date.now() + 24 * 60 * 60 * 1000
    ).toLocaleDateString("en-US", { weekday: "long" });

    const recommendations = [];
    const timesLimits = validateOptimalTimes();
    const maxRecommendations = timesLimits.planLimits.timeSlots === -1 ? 10 : Math.min(3, timesLimits.planLimits.timeSlots);

    // Add today's best recommendation if available
    if (
      data.combined_recommendations[today] &&
      data.combined_recommendations[today].length > 0
    ) {
      recommendations.push({
        day: today,
        timeSlot: data.combined_recommendations[today][0],
        label: "Today",
        priority: 1
      });
    }

    // Add tomorrow's best recommendation if available
    if (
      data.combined_recommendations[tomorrow] &&
      data.combined_recommendations[tomorrow].length > 0 &&
      recommendations.length < maxRecommendations
    ) {
      recommendations.push({
        day: tomorrow,
        timeSlot: data.combined_recommendations[tomorrow][0],
        label: "Tomorrow",
        priority: 2
      });
    }

    // Add best day's recommendation if different from today and tomorrow
    const bestDay = data.historical_analysis?.best_day;
    if (
      bestDay !== today &&
      bestDay !== tomorrow &&
      data.combined_recommendations[bestDay] &&
      data.combined_recommendations[bestDay].length > 0 &&
      recommendations.length < maxRecommendations
    ) {
      recommendations.push({
        day: bestDay,
        timeSlot: data.combined_recommendations[bestDay][0],
        label: "Best Day",
        priority: 3
      });
    }

    return recommendations.slice(0, maxRecommendations);
  }, [data, validateOptimalTimes]);

  const bestRecommendations = useMemo(() => getBestRecommendations(), [getBestRecommendations]);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  // Main render condition checks
  if (state.loading && !data) {
    return (
      <DashboardCard
        title={title}
        description="Loading optimal posting times..."
        icon={AccessTimeIcon}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          minHeight: { xs: 120, sm: 150 }
        }}>
          <CircularProgress
            size={40}
            sx={{ color: ACE_COLORS.PURPLE }}
            aria-label="Loading optimal times data"
          />
        </Box>
      </DashboardCard>
    );
  }

  // Error state
  if (Object.keys(state.errors).length > 0) {
    return (
      <DashboardCard
        title={title}
        description="Error loading optimal times data"
        icon={ErrorIcon}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        <Box sx={{ p: 2 }}>
          <Alert
            severity="error"
            sx={{
              backgroundColor: alpha(theme.palette.error.main, 0.1),
              border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
            }}
          >
            <AlertTitle>Optimal Times Data Error</AlertTitle>
            {Object.values(state.errors)[0]}
          </Alert>
        </Box>
      </DashboardCard>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <DashboardCard
          title={title}
          description="Error loading optimal times data"
          icon={ErrorIcon}
        >
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load optimal posting times
            </Typography>
          </Box>
        </DashboardCard>
      }
    >
      <Box
        ref={widgetRef}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        onKeyDown={handleKeyDown}
      >
        <GlassmorphicCard
          variant="glassSecondary"
          sx={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            transition: `all ${ANIMATION_CONFIG.DURATION}ms ${ANIMATION_CONFIG.EASING}`,
            ...(!prefersReducedMotion && {
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: theme.shadows[8]
              }
            })
          }}
          blurStrength={10}
          opacity={0.5}
          hoverable={true}
        >
          <CustomCardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AccessTimeIcon sx={{ color: ACE_COLORS.PURPLE }} />
                <Typography variant="h6" fontWeight={600} sx={{ color: ACE_COLORS.DARK }}>
                  {title}
                </Typography>
                {realTimeUpdates && subscriptionFeatures.hasRealTimeTracking && (
                  <Badge
                    badgeContent="LIVE"
                    sx={{
                      '& .MuiBadge-badge': {
                        backgroundColor: theme.palette.success.main,
                        color: ACE_COLORS.WHITE,
                        fontSize: '0.6rem',
                        height: 16,
                        minWidth: 16
                      }
                    }}
                  />
                )}
              </Box>
            }
            titleTypographyProps={{ fontWeight: 600 }}
            action={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {/* Analytics Button */}
                {enableAnalytics && (
                  <Tooltip title="View Analytics">
                    <IconButton
                      size="small"
                      onClick={handleAnalyticsRequest}
                      sx={{
                        color: ACE_COLORS.PURPLE,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                        }
                      }}
                      aria-label="View optimal times analytics"
                    >
                      <AnalyticsIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                {/* Export Button */}
                {enableExport && (
                  <Tooltip title="Export Data">
                    <IconButton
                      size="small"
                      onClick={handleExportMenuOpen}
                      sx={{
                        color: theme.palette.text.secondary,
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.1)
                        }
                      }}
                      aria-label="Export optimal times data"
                    >
                      <ExportIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                {/* Refresh Button */}
                <Tooltip title="Refresh Data">
                  <IconButton
                    size="small"
                    onClick={handleRefresh}
                    disabled={state.refreshing}
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.1)
                      }
                    }}
                    aria-label="Refresh optimal times data"
                  >
                    {state.refreshing ? (
                      <CircularProgress size={16} />
                    ) : (
                      <RefreshIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </Box>
            }
          />
          <Divider sx={{ opacity: 0.6 }} />
          <CustomCardContent
            sx={{
              overflow: "hidden",
              height: "auto",
              display: "flex",
              flexDirection: "column",
              flexGrow: 1,
              pb: "16px !important",
            }}
          >
            {/* Loading State */}
            {state.loading ? (
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "200px",
                  width: "100%",
                }}
              >
                <CircularProgress sx={{ color: ACE_COLORS.PURPLE }} />
              </Box>
            ) : bestRecommendations.length > 0 ? (
              <Box sx={{ width: "100%", height: "100%" }}>
                {/* Enhanced Recommendations Display */}
                <Typography
                  variant="subtitle2"
                  sx={{
                    mb: 2,
                    fontWeight: 600,
                    color: ACE_COLORS.DARK,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  <StarIcon sx={{ color: ACE_COLORS.YELLOW, fontSize: 18 }} />
                  Top Recommendations
                </Typography>

                {/* Real-time Updates Indicator */}
                {realTimeUpdates && subscriptionFeatures.hasRealTimeTracking && (
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    mb: 2,
                    p: 1,
                    backgroundColor: alpha(theme.palette.success.main, 0.1),
                    borderRadius: 1,
                    border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
                  }}>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        backgroundColor: theme.palette.success.main,
                        ...(!prefersReducedMotion && {
                          animation: 'pulse 2s infinite',
                          '@keyframes pulse': {
                            '0%': { opacity: 1 },
                            '50%': { opacity: 0.5 },
                            '100%': { opacity: 1 }
                          }
                        })
                      }}
                      aria-hidden="true"
                    />
                    <Typography variant="caption" color="text.secondary">
                      Live optimal times tracking enabled
                    </Typography>
                    {state.lastUpdated && (
                      <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}>
                        Updated: {new Date(state.lastUpdated).toLocaleTimeString()}
                      </Typography>
                    )}
                  </Box>
                )}

                {/* Enhanced Recommendations Grid */}
                <Grid container spacing={2}>
                  {bestRecommendations.map((recommendation, index) => (
                    <Grid item xs={12} sm={6} md={4} key={`${recommendation.day}-${index}`}>
                      <Fade
                        in={true}
                        timeout={ANIMATION_CONFIG.DURATION + (index * ANIMATION_CONFIG.STAGGER_DELAY)}
                      >
                        <Card
                          sx={{
                            p: 2,
                            background: `linear-gradient(135deg,
                              ${alpha(ACE_COLORS.PURPLE, 0.1)} 0%,
                              ${alpha(ACE_COLORS.YELLOW, 0.1)} 100%)`,
                            border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                            borderRadius: 2,
                            cursor: 'pointer',
                            transition: `all ${ANIMATION_CONFIG.DURATION}ms ${ANIMATION_CONFIG.EASING}`,
                            ...(!prefersReducedMotion && {
                              '&:hover': {
                                transform: 'translateY(-2px)',
                                boxShadow: theme.shadows[4],
                                borderColor: ACE_COLORS.PURPLE
                              }
                            })
                          }}
                          onClick={() => handleTimeSlotSelect(recommendation.timeSlot, recommendation.day)}
                          role="button"
                          tabIndex={0}
                          aria-label={`Select optimal time slot for ${recommendation.label}: ${recommendation.timeSlot.time_slot}`}
                        >
                          {/* Day Label */}
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Chip
                              label={recommendation.label}
                              size="small"
                              sx={{
                                backgroundColor: ACE_COLORS.PURPLE,
                                color: ACE_COLORS.WHITE,
                                fontWeight: 600,
                                fontSize: '0.7rem'
                              }}
                            />
                            <Chip
                              label={recommendation.timeSlot.confidence || 'Medium'}
                              size="small"
                              sx={{
                                backgroundColor: alpha(getConfidenceColor(recommendation.timeSlot.confidence), 0.1),
                                color: getConfidenceColor(recommendation.timeSlot.confidence),
                                fontWeight: 600,
                                fontSize: '0.7rem'
                              }}
                            />
                          </Box>

                          {/* Time Slot */}
                          <Typography
                            variant="h6"
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 1,
                              color: ACE_COLORS.DARK,
                              fontWeight: 700
                            }}
                          >
                            <AccessTimeIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE, fontSize: 20 }} />
                            {recommendation.timeSlot.time_slot}
                          </Typography>

                          {/* Description */}
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ mb: 2, fontSize: '0.8rem' }}
                          >
                            {recommendation.timeSlot.reason ||
                              `Based on ${recommendation.timeSlot.source || 'historical'} data`}
                          </Typography>

                          {/* Action Button */}
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<ScheduleIcon />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCreateContent(recommendation.day, recommendation.timeSlot);
                            }}
                            fullWidth
                            sx={{
                              borderColor: ACE_COLORS.PURPLE,
                              color: ACE_COLORS.PURPLE,
                              '&:hover': {
                                borderColor: ACE_COLORS.PURPLE,
                                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                              }
                            }}
                            aria-label={`Schedule content for ${recommendation.day} at ${recommendation.timeSlot.time_slot}`}
                          >
                            Schedule Content
                          </Button>
                        </Card>
                      </Fade>
                    </Grid>
                  ))}
                </Grid>

                {/* Enhanced View All Button */}
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    mt: 3,
                    mb: 2,
                    width: "100%",
                    position: "sticky",
                    bottom: 0,
                    pt: 2,
                    backgroundColor: alpha(ACE_COLORS.WHITE, 0.8),
                    backdropFilter: "blur(5px)",
                    borderRadius: 1
                  }}
                >
                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleViewAll}
                    sx={{
                      backgroundColor: ACE_COLORS.PURPLE,
                      color: ACE_COLORS.WHITE,
                      boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
                      "&:hover": {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                        boxShadow: "0 6px 12px rgba(0, 0, 0, 0.15)",
                      },
                      fontWeight: "bold",
                      px: 4,
                      py: 1,
                    }}
                    startIcon={<TimelineIcon />}
                    aria-label="View all optimal times recommendations"
                  >
                    View All Recommendations
                  </Button>
                </Box>
              </Box>
            ) : (
              /* Enhanced Empty State */
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "200px",
                  width: "100%",
                  p: 3,
                  textAlign: 'center'
                }}
              >
                <AccessTimeIcon
                  sx={{
                    fontSize: 48,
                    color: theme.palette.grey[400],
                    mb: 2
                  }}
                />
                <Typography variant="h6" color="text.secondary" sx={{ mb: 1, fontWeight: 600 }}>
                  No Optimal Times Available
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  No posting time recommendations available.
                  <br />
                  Check back later or analyze your content performance to generate optimal posting times.
                </Typography>

                {/* Upgrade prompt for creator plan */}
                {subscriptionFeatures.planId === 'creator' && (
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => setState(prev => ({ ...prev, showUpgradeDialog: true }))}
                    sx={{
                      borderColor: ACE_COLORS.PURPLE,
                      color: ACE_COLORS.PURPLE,
                      '&:hover': {
                        borderColor: ACE_COLORS.PURPLE,
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                      }
                    }}
                    startIcon={<UpgradeIcon />}
                  >
                    Upgrade for More Features
                  </Button>
                )}
              </Box>
            )}
          </CustomCardContent>
        </GlassmorphicCard>

        {/* Export Menu */}
        <Menu
          anchorEl={exportAnchorEl}
          open={state.showExportMenu}
          onClose={handleExportMenuClose}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 150,
              boxShadow: theme.shadows[8],
              border: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          <MenuItem onClick={() => {
            handleExport('csv');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as CSV</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => {
            handleExport('json');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as JSON</ListItemText>
          </MenuItem>

          {subscriptionFeatures.hasAnalytics && (
            <MenuItem onClick={() => {
              handleExport('pdf');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export Report (PDF)</ListItemText>
            </MenuItem>
          )}
        </Menu>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={handleUpgradeDialogClose}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: theme.shadows[16]
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span">
                {getUpgradeDialogContent().title}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {getUpgradeDialogContent().content}
              </Typography>

              <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                Unlock Advanced Optimal Times Features:
              </Typography>

              <Grid container spacing={1}>
                {getUpgradeDialogContent().features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                      <Typography variant="body2">{feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Alert
              severity="info"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
              Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced optimal times features.
            </Alert>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleUpgradeDialogClose}
              sx={{ color: theme.palette.text.secondary }}
            >
              Maybe Later
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                }
              }}
              startIcon={<UpgradeIcon />}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ErrorBoundary>
  );
}));


// Enhanced PropTypes validation - Production Ready
OptimalTimesWidget.propTypes = {
  // Core props
  data: PropTypes.object,
  loading: PropTypes.bool,
  title: PropTypes.string,


  // Enhanced props
  enableAnalytics: PropTypes.bool,
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onTimeSlotSelect: PropTypes.func,
  onAnalyticsRequest: PropTypes.func,
  onExport: PropTypes.func,
  onScheduleContent: PropTypes.func,
  onRefresh: PropTypes.func,


  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

OptimalTimesWidget.defaultProps = {
  loading: false,
  title: "Optimal Posting Times",
  enableAnalytics: false,
  enableExport: false,
  realTimeUpdates: false,
  className: '',
  style: {},
  testId: 'optimal-times-widget'
};

// Display name for debugging
OptimalTimesWidget.displayName = 'OptimalTimesWidget';

export default OptimalTimesWidget;

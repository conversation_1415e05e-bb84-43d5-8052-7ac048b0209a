// @since 2024-1-1 to 2025-25-7
import { memo, Suspense, useCallback, useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import {
  Container,
  Box,
  CircularProgress,
  Alert,
  Button,
  Typography,
  Breadcrumbs,
  Link,
  Skeleton
} from '@mui/material';
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Refresh as RefreshIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import CompetitorDetail from '../../components/competitors/CompetitorDetail';
import { CompetitorProvider } from '../../contexts/CompetitorContext';
import { useNotification } from '../../hooks/useNotification';
import ErrorBoundary from '../../components/common/ErrorBoundary';

// Enhanced loading component with skeleton
const LoadingFallback = memo(() => (
  <Box sx={{ my: 4 }}>
    <Box sx={{ mb: 3 }}>
      <Skeleton variant="text" width="60%" height={40} />
      <Skeleton variant="text" width="40%" height={24} />
    </Box>

    <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3, mb: 3 }}>
      <Skeleton variant="rectangular" height={200} />
      <Skeleton variant="rectangular" height={200} />
    </Box>

    <Skeleton variant="rectangular" height={300} />

    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        mt: 2,
        gap: 2
      }}
      role="status"
      aria-live="polite"
      aria-label="Loading competitor details"
    >
      <CircularProgress size={24} aria-label="Loading data" />
      <Typography variant="body2" color="text.secondary">
        Loading competitor details...
      </Typography>
    </Box>
  </Box>
));

LoadingFallback.displayName = 'LoadingFallback';

// Error fallback component
const ErrorFallback = memo(({ error, resetError, competitorId }) => (
  <Alert
    severity="error"
    sx={{ my: 3 }}
    action={
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          color="inherit"
          size="small"
          onClick={resetError}
          startIcon={<RefreshIcon />}
          aria-label="Retry loading competitor details"
        >
          Retry
        </Button>
        <Button
          color="inherit"
          size="small"
          onClick={() => window.history.back()}
          startIcon={<ArrowBackIcon />}
          aria-label="Go back to previous page"
        >
          Go Back
        </Button>
      </Box>
    }
  >
    <Typography variant="body2">
      Failed to load competitor details{competitorId ? ` for ID: ${competitorId}` : ''}: {error?.message || 'Unknown error'}
    </Typography>
  </Alert>
));

ErrorFallback.displayName = 'ErrorFallback';

const CompetitorDetailPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id: competitorId } = useParams();
  const { showErrorNotification } = useNotification();
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [competitorName, setCompetitorName] = useState('');

  // Validate competitor ID
  useEffect(() => {
    if (!competitorId) {
      setError(new Error('Competitor ID is required'));
      showErrorNotification('Invalid competitor ID');
    }
  }, [competitorId, showErrorNotification]);

  // Error boundary handler
  const handleError = useCallback((error, errorInfo) => {
    console.error('CompetitorDetailPage Error:', error, errorInfo);
    setError(error);
    showErrorNotification('Failed to load competitor details');
  }, [showErrorNotification]);

  // Reset error state
  const resetError = useCallback(() => {
    setError(null);
    setRetryCount(prev => prev + 1);
  }, []);

  // Navigation handlers
  const handleNavigateHome = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  const handleNavigateCompetitors = useCallback(() => {
    navigate('/settings?tab=competitors');
  }, [navigate]);

  // SEO and meta tags
  const pageTitle = competitorName
    ? `${competitorName} - Competitor Details | ACE Social`
    : `Competitor Details | ACE Social`;

  const pageDescription = competitorName
    ? `Detailed analysis of ${competitorName} including performance metrics, content strategy, and competitive insights.`
    : "Detailed competitor analysis including performance metrics, content strategy, and competitive insights.";

  // Analytics tracking
  useEffect(() => {
    // Track page view
    if (typeof gtag !== 'undefined') {
      gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: pageTitle,
        page_location: window.location.href,
        custom_map: { competitor_id: competitorId }
      });
    }
  }, [pageTitle, competitorId]);

  // Handle competitor name update from child component
  const handleCompetitorNameUpdate = useCallback((name) => {
    setCompetitorName(name);
  }, []);

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta name="keywords" content="competitor analysis, competitor details, social media metrics, competitive intelligence" />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:type" content="profile" />
        <link rel="canonical" href={`${window.location.origin}${location.pathname}`} />
        {competitorId && <meta name="competitor-id" content={competitorId} />}
      </Helmet>

      <Container
        maxWidth="xl"
        component="main"
        role="main"
        aria-label={`Competitor details for ${competitorName || 'selected competitor'}`}
      >
        {/* Breadcrumb Navigation */}
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs
            aria-label="breadcrumb navigation"
            sx={{ mb: 2 }}
          >
            <Link
              component="button"
              variant="body2"
              onClick={handleNavigateHome}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
              aria-label="Navigate to dashboard"
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Dashboard
            </Link>
            <Link
              component="button"
              variant="body2"
              onClick={handleNavigateCompetitors}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
              aria-label="Navigate to competitors"
            >
              <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Competitors
            </Link>
            <Typography
              color="text.primary"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <PersonIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              {competitorName || 'Details'}
            </Typography>
          </Breadcrumbs>
        </Box>

        {/* Error Display */}
        {error && (
          <ErrorFallback
            error={error}
            resetError={resetError}
            competitorId={competitorId}
          />
        )}

        {/* Main Content */}
        {!error && (
          <ErrorBoundary
            onError={handleError}
            fallback={<ErrorFallback error={error} resetError={resetError} competitorId={competitorId} />}
          >
            <CompetitorProvider>
              <Suspense fallback={<LoadingFallback />}>
                <CompetitorDetail
                  competitorId={competitorId}
                  onNameUpdate={handleCompetitorNameUpdate}
                  key={retryCount} // Force re-render on retry
                />
              </Suspense>
            </CompetitorProvider>
          </ErrorBoundary>
        )}
      </Container>
    </>
  );
};

// Memoize the component for performance
const MemoizedCompetitorDetailPage = memo(CompetitorDetailPage);
MemoizedCompetitorDetailPage.displayName = 'CompetitorDetailPage';

export default MemoizedCompetitorDetailPage;

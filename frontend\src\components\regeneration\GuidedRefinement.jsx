/**
 * Enhanced Guided Refinement - Enterprise-grade guided refinement management component
 * Features: Comprehensive guided refinement management, content refinement workflows, subscription-free access,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced refinement capabilities and interactive refinement exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Grid,
  CardContent,
  IconButton,
  Alert,
  CircularProgress,
  alpha,
  Snackbar,
  useMediaQuery,
  LinearProgress
} from '@mui/material';
import {
  AutoFixHigh as RegenerateIcon,
  Close as CloseIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
  Edit as EditIcon
} from '@mui/icons-material';

import api from '../../api';
import { useNotification } from '../../hooks/useNotification';
import GlassmorphicCard from '../common/GlassmorphicCard';
import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Refinement display modes with enhanced configurations
const REFINEMENT_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Refinement',
    description: 'Basic refinement interface',
    features: ['basic_refinement', 'analytics_refinement', 'ai_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Refinement',
    description: 'Comprehensive refinement management',
    features: ['detailed_refinement', 'refinement_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Refinement',
    description: 'AI-powered refinement management and suggestions',
    features: ['ai_assisted', 'ai_optimization', 'refinement_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Refinement',
    description: 'Advanced refinement analytics and insights',
    features: ['analytics_refinement', 'refinement_insights']
  }
};

/**
 * Enhanced Guided Refinement Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Object} [props.content] - Content to refine
 * @param {Function} [props.onContentUpdate] - Content update callback
 * @param {Function} [props.onClose] - Close callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights (no restrictions)
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onRefinementAction] - Refinement action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-guided-refinement'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const GuidedRefinement = memo(forwardRef(({
  content,
  onContentUpdate,
  onClose,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onRefinementAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-guided-refinement',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const refinementRef = useRef(null);
  const [selectedSection, setSelectedSection] = useState('full_content');
  const [regenerationType, setRegenerationType] = useState('improve');
  const [targetTone, setTargetTone] = useState('');
  const [specificInstructions, setSpecificInstructions] = useState('');
  const [loading, setLoading] = useState(false);
  const [previewContent, setPreviewContent] = useState(null);
  const [regenerationCost, setRegenerationCost] = useState(0);

  // Enhanced state management
  const [refinementMode, setRefinementMode] = useState('compact');
  const [refinementHistory, setRefinementHistory] = useState([]);
  const [refinementAnalytics, setRefinementAnalytics] = useState(null);
  const [refinementInsights, setRefinementInsights] = useState(null);
  const [customRefinementConfigs, setCustomRefinementConfigs] = useState([]);
  const [refinementPreferences, setRefinementPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    refinementSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [refinementDrawerOpen, setRefinementDrawerOpen] = useState(false);
  const [selectedRefinementType, setSelectedRefinementType] = useState(null);
  const [refinementStats, setRefinementStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [qualityScore, setQualityScore] = useState(0);
  const [refinementProgress, setRefinementProgress] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [refinementVersions, setRefinementVersions] = useState([]);

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastRefinementCheck, setLastRefinementCheck] = useState(Date.now());

  /**
   * ACE Social subscription integration with plan-based limitations - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // ACE Social platform plans with realistic limitations
    const features = {
      creator: {
        maxRefinementPerDay: 10,
        maxRefinementTypes: 3,
        hasAdvancedRefinement: false,
        hasRefinementAnalytics: false,
        hasCustomRefinement: false,
        hasRefinementInsights: false,
        hasRefinementHistory: true,
        hasAIAssistance: false,
        hasRefinementExport: false,
        hasRefinementScheduling: false,
        hasRefinementAutomation: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1,
        creditCostMultiplier: 1.0,
        allowedRefinementTypes: ['improve', 'shorten'],
        maxHistoryDays: 7
      },
      accelerator: {
        maxRefinementPerDay: 50,
        maxRefinementTypes: 6,
        hasAdvancedRefinement: true,
        hasRefinementAnalytics: true,
        hasCustomRefinement: false,
        hasRefinementInsights: true,
        hasRefinementHistory: true,
        hasAIAssistance: true,
        hasRefinementExport: true,
        hasRefinementScheduling: false,
        hasRefinementAutomation: false,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 2000,
        planName: 'Accelerator',
        planTier: 2,
        creditCostMultiplier: 0.8,
        allowedRefinementTypes: ['improve', 'shorten', 'expand', 'tone_change', 'professional', 'casual'],
        maxHistoryDays: 30
      },
      dominator: {
        maxRefinementPerDay: -1, // Unlimited
        maxRefinementTypes: -1, // All types
        hasAdvancedRefinement: true,
        hasRefinementAnalytics: true,
        hasCustomRefinement: true,
        hasRefinementInsights: true,
        hasRefinementHistory: true,
        hasAIAssistance: true,
        hasRefinementExport: true,
        hasRefinementScheduling: true,
        hasRefinementAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3,
        creditCostMultiplier: 0.5,
        allowedRefinementTypes: ['improve', 'shorten', 'expand', 'tone_change', 'professional', 'casual', 'creative', 'technical', 'persuasive', 'custom'],
        maxHistoryDays: -1 // Unlimited
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxRefinementPerDay === -1 || currentUsage < currentFeatures.maxRefinementPerDay;
        return hasAccess && withinLimits;
      },
      getRefinementCost: (baseCredits) => {
        return Math.ceil(baseCredits * currentFeatures.creditCostMultiplier);
      },
      canUseRefinementType: (type) => {
        return currentFeatures.allowedRefinementTypes.includes(type);
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'dialog',
      'aria-label': ariaLabel || `Guided refinement with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Refinement interface with ${subscriptionFeatures.trackingLevel} tracking`,
      'aria-modal': 'true',
      tabIndex: -1
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive refinement API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getRefinementHistory: () => refinementHistory,
    getRefinementAnalytics: () => refinementAnalytics,
    getRefinementInsights: () => refinementInsights,
    refreshRefinement: () => {
      fetchRefinementAnalytics();
      if (onRefresh) onRefresh();
    },

    // Refinement methods
    focusRefinement: () => {
      if (refinementRef.current) {
        refinementRef.current.focus();
      }
    },
    getPreviewContent: () => previewContent,
    getQualityScore: () => qualityScore,
    getRefinementProgress: () => refinementProgress,
    getSelectedSection: () => selectedSection,
    getRegenerationType: () => regenerationType,
    previewRegeneration: () => handlePreviewRegeneration(),
    applyChanges: () => handleApplyChanges(),
    openRefinementDrawer: () => setRefinementDrawerOpen(true),
    closeRefinementDrawer: () => setRefinementDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportRefinementData: () => {
      if (onExport) {
        onExport(refinementHistory, refinementAnalytics);
      }
    },

    // Accessibility methods
    announceRefinement: (message) => announceToScreenReader(message),
    focusRefinementField: () => setFocusToElement('guided-refinement-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => refinementMode,
    getRefinementStats: () => refinementStats,
    getSelectedRefinementType: () => selectedRefinementType,
    getCustomRefinementConfigs: () => customRefinementConfigs,
    getRefinementDrawerOpen: () => refinementDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    getActiveTab: () => activeTab,
    setActiveTab: (tab) => setActiveTab(tab),
    addCustomRefinementConfig,
    handleRefinementModeChange,
    updateRefinementPreferences,
    handleRefinementTypeSelection,
    validateRefinementConfig,
    getRefinementVersions: () => refinementVersions,
    switchToVersion: (versionId) => switchToRefinementVersion(versionId)
  }), [
    refinementHistory,
    refinementAnalytics,
    refinementInsights,
    refinementStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    refinementMode,
    selectedRefinementType,
    customRefinementConfigs,
    previewContent,
    qualityScore,
    refinementProgress,
    selectedSection,
    regenerationType,
    refinementVersions,
    addCustomRefinementConfig,
    fetchRefinementAnalytics,
    handleApplyChanges,
    handlePreviewRegeneration,
    handleRefinementModeChange,
    handleRefinementTypeSelection,
    switchToRefinementVersion,
    updateRefinementPreferences,
    validateRefinementConfig,
    activeTab,
    fullscreenMode,
    refinementDrawerOpen,
    showAnalytics
  ]);

  // Fetch refinement analytics with enhanced error handling and retry logic
  const fetchRefinementAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/refinement/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setRefinementAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (refinementPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Refinement analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch refinement analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load refinement analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, refinementPreferences.showAnalytics]);

  // Handle refinement mode switching
  const handleRefinementModeChange = useCallback((newMode) => {
    if (REFINEMENT_MODES[newMode.toUpperCase()]) {
      setRefinementMode(newMode);
      announceToScreenReader(`Refinement mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setRefinementHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (refinementPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} refinement mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, refinementPreferences.showAnalytics, showSuccess]);

  // Handle custom refinement config management
  const addCustomRefinementConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomRefinementConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setRefinementHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (refinementPreferences.showAnalytics) {
      showSuccess(`Custom refinement config "${configData.name}" created`);
    }
  }, [subscription?.user_id, refinementPreferences.showAnalytics, showSuccess]);

  // Handle refinement preferences updates
  const updateRefinementPreferences = useCallback((newPreferences) => {
    setRefinementPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setRefinementHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (refinementPreferences.showAnalytics) {
      showSuccess('Refinement preferences updated');
    }
  }, [subscription?.user_id, refinementPreferences.showAnalytics, showSuccess]);

  // Handle refinement type selection
  const handleRefinementTypeSelection = useCallback((refinementType) => {
    setSelectedRefinementType(refinementType);

    // Track refinement type selection
    const typeRecord = {
      id: Date.now(),
      type: 'refinement_type_selected',
      refinementType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setRefinementHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (refinementPreferences.showAnalytics) {
      announceToScreenReader(`Selected refinement type: ${refinementType}`);
    }
  }, [subscription?.user_id, refinementPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateRefinementConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Refinement type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Switch to refinement version
  const switchToRefinementVersion = useCallback((versionId) => {
    const version = refinementVersions.find(v => v.id === versionId);
    if (version) {
      setPreviewContent(version.content);
      setQualityScore(version.qualityScore || 0);

      if (refinementPreferences.showAnalytics) {
        showSuccess(`Switched to version ${version.name}`);
      }
    }
  }, [refinementVersions, refinementPreferences.showAnalytics, showSuccess]);

  const sections = [
    { value: 'full_content', label: 'Full Content', description: 'Regenerate the entire post' },
    { value: 'headline', label: 'Headline', description: 'Focus on the title/headline only' },
    { value: 'body', label: 'Body Text', description: 'Regenerate the main content body' },
    { value: 'hashtags', label: 'Hashtags', description: 'Optimize hashtags for better reach' },
    { value: 'image_prompt', label: 'Image Prompt', description: 'Improve image generation prompt' }
  ];

  // Filter regeneration types based on subscription plan
  const regenerationTypes = useMemo(() => {
    const allRegenerationTypes = [
      { value: 'improve', label: 'Improve', description: 'Enhance quality and engagement', tier: 1 },
      { value: 'shorten', label: 'Shorten', description: 'Make it more concise', tier: 1 },
      { value: 'rephrase', label: 'Rephrase', description: 'Say the same thing differently', tier: 2 },
      { value: 'expand', label: 'Expand', description: 'Add more detail and context', tier: 2 },
      { value: 'change_tone', label: 'Change Tone', description: 'Adjust the tone and style', tier: 2 },
      { value: 'professional', label: 'Professional', description: 'Make more professional and formal', tier: 2 },
      { value: 'casual', label: 'Casual', description: 'Make more casual and friendly', tier: 2 },
      { value: 'creative', label: 'Creative', description: 'Add creative flair and engagement', tier: 3 },
      { value: 'technical', label: 'Technical', description: 'Technical accuracy and clarity', tier: 3 },
      { value: 'persuasive', label: 'Persuasive', description: 'Increase persuasive impact', tier: 3 },
      { value: 'custom', label: 'Custom', description: 'Follow specific custom guidelines', tier: 3 }
    ];

    return allRegenerationTypes.filter(type =>
      subscriptionFeatures.canUseRefinementType(type.value)
    );
  }, [subscriptionFeatures]);

  const toneOptions = [
    'professional', 'casual', 'friendly', 'urgent', 'inspiring',
    'humorous', 'authoritative', 'conversational', 'formal', 'playful'
  ];

  // Initial data loading
  useEffect(() => {
    fetchRefinementAnalytics();
    fetchRefinementInsights();
  }, [fetchRefinementAnalytics, fetchRefinementInsights]);

  // Fetch refinement insights
  const fetchRefinementInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/refinement/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setRefinementInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch refinement insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && previewContent) {
      // Optimize refinement management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchRefinementAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, previewContent, fetchRefinementAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastRefinementCheck(Date.now());

          if (wasUnavailable && refinementPreferences.showAnalytics) {
            showSuccess("Connection restored - Refinement features available");
          }
        } else {
          setBackendAvailable(false);
          if (refinementPreferences.showAnalytics) {
            showError("Backend service unavailable - Some refinement features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastRefinementCheck;
          if (timeSinceLastCheck > 60000 && refinementPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Refinement may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastRefinementCheck, refinementPreferences.showAnalytics, showSuccess, showError]);

  // Generate AI suggestions when refinement changes
  useEffect(() => {
    if (enableAIInsights && refinementPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, refinementPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/refinement/ai-suggestions', {
        params: {
          contentId: content?.id,
          section: selectedSection,
          type: regenerationType
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (refinementPreferences.showAnalytics) {
        showSuccess('AI suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [content?.id, selectedSection, regenerationType, refinementPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when refinement changes
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchRefinementStats();
    }
  }, [enableAdvancedFeatures, fetchRefinementStats]);

  // Fetch refinement stats function
  const fetchRefinementStats = useCallback(async () => {
    try {
      const response = await api.get('/api/refinement/stats');
      setRefinementStats(response.data);
    } catch (error) {
      console.error('Failed to fetch refinement stats:', error);
    }
  }, []);

  // Track daily usage for plan limitations
  const [dailyUsage, setDailyUsage] = useState(0);

  // Check if user can perform refinement based on plan limits
  const canPerformRefinement = useMemo(() => {
    const withinDailyLimit = subscriptionFeatures.isWithinLimits(dailyUsage, subscriptionFeatures.maxRefinementPerDay);
    const hasRefinementType = subscriptionFeatures.canUseRefinementType(regenerationType);

    return withinDailyLimit && hasRefinementType;
  }, [dailyUsage, subscriptionFeatures, regenerationType]);

  // Get limitation message for UI
  const getLimitationMessage = useCallback(() => {
    if (!subscriptionFeatures.canUseRefinementType(regenerationType)) {
      return `${regenerationType} refinement requires ${subscriptionFeatures.planTier < 2 ? 'Accelerator' : 'Dominator'} plan or higher`;
    }

    if (!subscriptionFeatures.isWithinLimits(dailyUsage, subscriptionFeatures.maxRefinementPerDay)) {
      return `Daily refinement limit reached (${subscriptionFeatures.maxRefinementPerDay}). Upgrade to ${subscriptionFeatures.planTier < 3 ? 'Dominator' : 'higher'} plan for more refinements`;
    }

    return null;
  }, [subscriptionFeatures, regenerationType, dailyUsage]);

  const handlePreviewRegeneration = useCallback(async () => {
    // Check plan limitations before proceeding
    if (!canPerformRefinement) {
      const limitMessage = getLimitationMessage();
      showErrorNotification(limitMessage || 'Refinement not available with current plan');
      return;
    }

    setLoading(true);
    setRefinementProgress(0);

    try {
      // Track refinement attempt
      const refinementRecord = {
        id: Date.now(),
        type: 'refinement_attempt',
        section: selectedSection,
        regenerationType,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id,
        planTier: subscriptionFeatures.planTier
      };

      setRefinementHistory(prev => [refinementRecord, ...prev.slice(0, 99)]);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setRefinementProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      // Calculate cost based on plan
      const baseCredits = 1; // Base cost for refinement
      const actualCost = subscriptionFeatures.getRefinementCost(baseCredits);

      const response = await api.post('/api/regeneration/regenerate', {
        content_id: content.id,
        section: selectedSection,
        regeneration_type: regenerationType,
        target_tone: targetTone || null,
        specific_instructions: specificInstructions || null,
        preserve_hashtags: true,
        preserve_mentions: true,
        plan_tier: subscriptionFeatures.planTier,
        expected_cost: actualCost
      });

      clearInterval(progressInterval);
      setRefinementProgress(100);

      if (response.data.success) {
        const newContent = response.data.regenerated_content;
        setPreviewContent(newContent);
        setRegenerationCost(response.data.credits_used || actualCost);
        setQualityScore(response.data.quality_score || 85);

        // Update daily usage
        setDailyUsage(prev => prev + 1);

        // Add to versions
        const newVersion = {
          id: Date.now(),
          name: `Version ${refinementVersions.length + 1}`,
          content: newContent,
          qualityScore: response.data.quality_score || 85,
          timestamp: new Date().toISOString(),
          planTier: subscriptionFeatures.planTier
        };
        setRefinementVersions(prev => [...prev, newVersion]);

        showSuccessNotification(`Content regenerated successfully! (${subscriptionFeatures.maxRefinementPerDay === -1 ? 'Unlimited' : `${subscriptionFeatures.maxRefinementPerDay - dailyUsage - 1} remaining today`})`);
        announceToScreenReader('Content refinement completed successfully');

        if (onRefinementAction) {
          onRefinementAction('preview_generated', {
            content: newContent,
            qualityScore: response.data.quality_score,
            planTier: subscriptionFeatures.planTier,
            creditsUsed: response.data.credits_used || actualCost
          });
        }
      } else {
        showErrorNotification(response.data.suggestions?.[0] || 'Failed to regenerate content');
      }
    } catch (error) {
      console.error('Error regenerating content:', error);
      if (error.response?.status === 402) {
        showErrorNotification('Insufficient credits. Please upgrade your plan or purchase more credits.');
      } else if (error.response?.status === 429) {
        showErrorNotification('Daily refinement limit exceeded. Please upgrade your plan for more refinements.');
      } else {
        showErrorNotification('Failed to regenerate content');
      }
    } finally {
      setLoading(false);
      setRefinementProgress(0);
    }
  }, [
    canPerformRefinement,
    getLimitationMessage,
    selectedSection,
    regenerationType,
    targetTone,
    specificInstructions,
    content?.id,
    subscription?.user_id,
    subscriptionFeatures,
    refinementVersions.length,
    dailyUsage,
    showSuccessNotification,
    showErrorNotification,
    announceToScreenReader,
    onRefinementAction
  ]);

  const handleApplyChanges = useCallback(() => {
    if (previewContent && onContentUpdate) {
      // Track changes applied
      const applyRecord = {
        id: Date.now(),
        type: 'changes_applied',
        section: selectedSection,
        regenerationType,
        qualityScore,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setRefinementHistory(prev => [applyRecord, ...prev.slice(0, 99)]);

      onContentUpdate(previewContent);
      showSuccessNotification('Changes applied successfully!');
      announceToScreenReader('Refinement changes have been applied to your content');

      if (onRefinementAction) {
        onRefinementAction('changes_applied', { content: previewContent, qualityScore });
      }

      if (onClose) onClose();
    }
  }, [
    previewContent,
    onContentUpdate,
    selectedSection,
    regenerationType,
    qualityScore,
    subscription?.user_id,
    showSuccessNotification,
    announceToScreenReader,
    onRefinementAction,
    onClose
  ]);

  const getCostColor = useCallback((cost) => {
    if (cost === 0) return ACE_COLORS.PURPLE;
    if (cost <= 0.5) return ACE_COLORS.YELLOW;
    return ACE_COLORS.DARK;
  }, []);

  const getCostLabel = useCallback((cost) => {
    if (cost === 0) return 'FREE';
    if (cost <= 0.5) return 'DISCOUNTED';
    return 'FULL PRICE';
  }, []);

  return (
    <Box
      {...getAccessibilityProps()}
      ref={refinementRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      <Dialog
        open={true}
        onClose={onClose}
        maxWidth={isMobile ? "sm" : "lg"}
        fullWidth
        fullScreen={fullscreenMode || isMobile}
        slotProps={{
          paper: {
            sx: {
              backgroundColor: alpha(ACE_COLORS.WHITE, 0.95),
              backdropFilter: 'blur(20px)',
              borderRadius: fullscreenMode ? 0 : 2,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
            }
          }
        }}
      >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <RegenerateIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6">Guided Content Refinement</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
              <Chip
                label={subscriptionFeatures.planName}
                size="small"
                sx={{
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE,
                  fontWeight: 'bold'
                }}
              />
              {subscriptionFeatures.maxRefinementPerDay !== -1 && (
                <Typography variant="caption" color="text.secondary">
                  {subscriptionFeatures.maxRefinementPerDay - dailyUsage} refinements remaining today
                </Typography>
              )}
              {subscriptionFeatures.maxRefinementPerDay === -1 && (
                <Typography variant="caption" sx={{ color: ACE_COLORS.PURPLE, fontWeight: 'bold' }}>
                  Unlimited refinements
                </Typography>
              )}
            </Box>
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={3}>
          {/* Configuration Panel */}
          <Grid item xs={12} md={6}>
            <GlassmorphicCard variant="glassPrimary">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <EditIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Refinement Settings
                </Typography>

                {/* Section Selection */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Section to Regenerate</InputLabel>
                  <Select
                    value={selectedSection}
                    onChange={(e) => setSelectedSection(e.target.value)}
                    label="Section to Regenerate"
                  >
                    {sections.map((section) => (
                      <MenuItem key={section.value} value={section.value}>
                        <Box>
                          <Typography variant="body1">{section.label}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {section.description}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Regeneration Type */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Regeneration Type</InputLabel>
                  <Select
                    value={regenerationType}
                    onChange={(e) => setRegenerationType(e.target.value)}
                    label="Regeneration Type"
                  >
                    {regenerationTypes.map((type) => (
                      <MenuItem
                        key={type.value}
                        value={type.value}
                        disabled={!subscriptionFeatures.canUseRefinementType(type.value)}
                      >
                        <Box sx={{ width: '100%' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Typography variant="body1">{type.label}</Typography>
                            {!subscriptionFeatures.canUseRefinementType(type.value) && (
                              <Chip
                                label={`${type.tier === 2 ? 'Accelerator' : 'Dominator'}+`}
                                size="small"
                                sx={{
                                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                                  color: ACE_COLORS.PURPLE,
                                  fontSize: '0.7rem'
                                }}
                              />
                            )}
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {type.description}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Target Tone */}
                {regenerationType === 'change_tone' && (
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Target Tone</InputLabel>
                    <Select
                      value={targetTone}
                      onChange={(e) => setTargetTone(e.target.value)}
                      label="Target Tone"
                    >
                      {toneOptions.map((tone) => (
                        <MenuItem key={tone} value={tone}>
                          {tone.charAt(0).toUpperCase() + tone.slice(1)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}

                {/* Specific Instructions */}
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Specific Instructions (Optional)"
                  value={specificInstructions}
                  onChange={(e) => setSpecificInstructions(e.target.value)}
                  placeholder="Provide specific guidance for the AI..."
                  sx={{ mb: 2 }}
                />

                {/* Cost Information */}
                <Alert
                  severity="info"
                  sx={{ mb: 2 }}
                  icon={<SpeedIcon />}
                >
                  <Typography variant="body2">
                    Estimated cost:
                    <Chip
                      label={`${regenerationCost} credits - ${getCostLabel(regenerationCost)}`}
                      size="small"
                      sx={{
                        ml: 1,
                        backgroundColor: getCostColor(regenerationCost),
                        color: 'white'
                      }}
                    />
                  </Typography>
                </Alert>

                <Button
                  variant="contained"
                  fullWidth
                  onClick={handlePreviewRegeneration}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <RegenerateIcon />}
                  size="large"
                >
                  {loading ? 'Regenerating...' : 'Preview Regeneration'}
                </Button>
              </CardContent>
            </GlassmorphicCard>
          </Grid>

          {/* Preview Panel */}
          <Grid item xs={12} md={6}>
            <GlassmorphicCard variant="glassSecondary">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <TrendingUpIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Content Preview
                </Typography>

                {previewContent ? (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom color="success.main">
                      ✓ Regeneration Complete
                    </Typography>

                    {selectedSection === 'full_content' && (
                      <Box>
                        <Typography variant="body1" sx={{ mb: 2, p: 2, backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1), borderRadius: 1 }}>
                          {previewContent.text_content || previewContent.headline}
                        </Typography>
                      </Box>
                    )}

                    {selectedSection === 'headline' && (
                      <Box>
                        <Typography variant="h6" sx={{ mb: 2, p: 2, backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1), borderRadius: 1 }}>
                          {previewContent.headline}
                        </Typography>
                      </Box>
                    )}

                    {selectedSection === 'hashtags' && (
                      <Box sx={{ mb: 2 }}>
                        {previewContent.hashtags?.map((tag, index) => (
                          <Chip
                            key={index}
                            label={tag}
                            size="small"
                            sx={{ mr: 0.5, mb: 0.5 }}
                            color="primary"
                          />
                        ))}
                      </Box>
                    )}

                    <Alert severity="success" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        Quality improvement estimated at +15%. Ready to apply changes?
                      </Typography>
                    </Alert>
                  </Box>
                ) : (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <PsychologyIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="body1" color="text.secondary">
                      Configure your regeneration settings and click &quot;Preview Regeneration&quot; to see the improved content.
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </GlassmorphicCard>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3, flexDirection: 'column', gap: 2 }}>
        {/* Plan limitation warning */}
        {!canPerformRefinement && (
          <Alert
            severity="warning"
            sx={{
              width: '100%',
              bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
              border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
            }}
          >
            <Typography variant="body2">
              {getLimitationMessage()}
            </Typography>
            <Button
              size="small"
              sx={{
                mt: 1,
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
              onClick={() => {
                // Navigate to upgrade page
                window.open('/billing/upgrade', '_blank');
              }}
            >
              Upgrade Plan
            </Button>
          </Alert>
        )}

        {/* Cost information */}
        {canPerformRefinement && (
          <Box sx={{
            width: '100%',
            p: 2,
            bgcolor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderRadius: 1,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`
          }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Cost:</strong> {subscriptionFeatures.getRefinementCost(1)} credit{subscriptionFeatures.getRefinementCost(1) !== 1 ? 's' : ''}
              {subscriptionFeatures.creditCostMultiplier < 1 && (
                <Chip
                  label={`${Math.round((1 - subscriptionFeatures.creditCostMultiplier) * 100)}% discount`}
                  size="small"
                  sx={{
                    ml: 1,
                    bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                    color: ACE_COLORS.PURPLE,
                    fontSize: '0.7rem'
                  }}
                />
              )}
            </Typography>
          </Box>
        )}

        <Box sx={{ display: 'flex', gap: 2, width: '100%', justifyContent: 'flex-end' }}>
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{
              borderColor: ACE_COLORS.PURPLE,
              color: ACE_COLORS.PURPLE,
              '&:hover': {
                borderColor: alpha(ACE_COLORS.PURPLE, 0.8),
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
              }
            }}
          >
            Cancel
          </Button>

          {!previewContent ? (
            <Button
              onClick={handlePreviewRegeneration}
              variant="contained"
              disabled={loading || !canPerformRefinement}
              startIcon={loading ? <CircularProgress size={20} /> : <RegenerateIcon />}
              sx={{
                bgcolor: ACE_COLORS.PURPLE,
                '&:hover': {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.8)
                },
                '&:disabled': {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.3)
                }
              }}
            >
              {loading ? 'Regenerating...' : 'Preview Regeneration'}
            </Button>
          ) : (
            <Button
              onClick={handleApplyChanges}
              variant="contained"
              disabled={!previewContent}
              startIcon={<TrendingUpIcon />}
              sx={{
                bgcolor: ACE_COLORS.PURPLE,
                '&:hover': {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.8)
                }
              }}
            >
              Apply Changes
            </Button>
          )}
        </Box>
      </DialogActions>
    </Dialog>

    {/* Notification Snackbar */}
    <Snackbar
      open={notification.open}
      autoHideDuration={6000}
      onClose={() => setNotification(prev => ({ ...prev, open: false }))}
      anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
    >
      <Alert
        severity={notification.severity}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        sx={{
          bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
          color: ACE_COLORS.DARK,
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
        }}
      >
        {notification.message}
      </Alert>
    </Snackbar>

    {/* Retry Count Indicator */}
    {retryCount > 0 && (
      <Box sx={{
        position: 'fixed',
        top: 20,
        right: 20,
        p: 1,
        borderRadius: 1,
        bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
        border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
        zIndex: 9999
      }}>
        <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
          Retrying refinement sync... (Attempt {retryCount}/3)
        </Typography>
      </Box>
    )}

    {/* Progress Indicator */}
    {refinementProgress > 0 && refinementProgress < 100 && (
      <Box sx={{
        position: 'fixed',
        bottom: 20,
        left: '50%',
        transform: 'translateX(-50%)',
        p: 2,
        borderRadius: 2,
        bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        zIndex: 9999,
        minWidth: 200
      }}>
        <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
          Refining content...
        </Typography>
        <LinearProgress
          variant="determinate"
          value={refinementProgress}
          sx={{
            '& .MuiLinearProgress-bar': {
              backgroundColor: ACE_COLORS.PURPLE
            }
          }}
        />
      </Box>
    )}
  </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
GuidedRefinement.propTypes = {
  // Core props
  content: PropTypes.object,
  onContentUpdate: PropTypes.func,
  onClose: PropTypes.func,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onRefinementAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

GuidedRefinement.displayName = 'GuidedRefinement';

export default GuidedRefinement;

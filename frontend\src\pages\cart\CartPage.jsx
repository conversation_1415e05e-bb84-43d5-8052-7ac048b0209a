// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  Grid,
  Button,
  IconButton,
  TextField,
  Divider,
  Alert,
  Chip,
  List,
  ListItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@mui/material';
import {
  ShoppingCart as ShoppingCartIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Payment as PaymentIcon,
  ArrowBack as ArrowBackIcon,
  Clear as ClearIcon
} from '@mui/icons-material';

import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useNotification } from '../../hooks/useNotification';

/**
 * Dedicated Cart Page Component
 * Full-page view of shopping cart with checkout functionality
 */
const CartPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // Cart state (in real app, this would come from context or global state)
  const [cart, setCart] = useState([]);
  const [loading, setLoading] = useState(false);
  const [checkoutDialogOpen, setCheckoutDialogOpen] = useState(false);
  const [checkoutError, setCheckoutError] = useState(null);
  const [processingPayment, setProcessingPayment] = useState(false);

  // Load cart from localStorage on component mount
  useEffect(() => {
    const savedCart = localStorage.getItem('addonCart');
    if (savedCart) {
      try {
        setCart(JSON.parse(savedCart));
      } catch (error) {
        console.error('Error loading cart from localStorage:', error);
        // Clear corrupted cart data
        localStorage.removeItem('aceo_cart');
      }
    }

    // Check for checkout recovery
    const checkoutBackup = localStorage.getItem('checkout_cart_backup');
    if (checkoutBackup) {
      try {
        const backupCart = JSON.parse(checkoutBackup);
        if (backupCart.length > 0) {
          showErrorNotification('Previous checkout was interrupted. Cart has been restored.');
          setCart(backupCart);
          localStorage.removeItem('checkout_cart_backup');
        }
      } catch (error) {
        console.error('Error loading checkout backup:', error);
        localStorage.removeItem('checkout_cart_backup');
      }
    }
  }, [showErrorNotification]);

  // Save cart to localStorage whenever cart changes
  useEffect(() => {
    try {
      localStorage.setItem('addonCart', JSON.stringify(cart));
    } catch (error) {
      console.error('Error saving cart to localStorage:', error);
      showErrorNotification('Failed to save cart. Please check your browser storage.');
    }
  }, [cart, showErrorNotification]);

  // Analytics tracking
  const trackCartEvent = useCallback((eventName, eventData = {}) => {
    if (window.gtag) {
      window.gtag('event', eventName, {
        event_category: 'cart',
        event_label: 'add_on_purchase',
        value: eventData.value || 0,
        ...eventData
      });
    }
  }, []);

  // Clear cart function with confirmation
  const clearCart = useCallback(() => {
    const cartValue = getCartTotal();
    const itemCount = getCartItemCount();

    setCart([]);
    localStorage.removeItem('aceo_cart');
    localStorage.removeItem('checkout_cart_backup');

    // Track cart clear event
    trackCartEvent('cart_cleared', {
      value: cartValue,
      items_count: itemCount
    });

    showSuccessNotification('Cart cleared successfully');
  }, [showSuccessNotification, getCartTotal, getCartItemCount, trackCartEvent]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getItemPrice = useCallback((item) => {
    return item.price_per_unit || item.price_monthly || 0;
  }, []);

  const getItemTotal = useCallback((item) => {
    return getItemPrice(item) * item.quantity;
  }, [getItemPrice]);

  const getCartTotal = useCallback(() => {
    return cart.reduce((total, item) => total + getItemTotal(item), 0);
  }, [cart, getItemTotal]);

  const getCartItemCount = useCallback(() => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  }, [cart]);

  const updateQuantity = useCallback((itemId, newQuantity) => {
    // Validate quantity
    if (newQuantity < 0) {
      showErrorNotification('Quantity cannot be negative');
      return;
    }

    if (newQuantity === 0) {
      removeItem(itemId);
      return;
    }

    // Limit maximum quantity
    const maxQuantity = 10;
    if (newQuantity > maxQuantity) {
      showErrorNotification(`Maximum quantity is ${maxQuantity}`);
      return;
    }

    setCart(prevCart =>
      prevCart.map(item =>
        item.id === itemId ? { ...item, quantity: Math.floor(newQuantity) } : item
      )
    );
    showSuccessNotification('Quantity updated');
  }, [removeItem, showErrorNotification, showSuccessNotification]);

  const removeItem = useCallback((itemId) => {
    const item = cart.find(cartItem => cartItem.id === itemId);

    setCart(prevCart => prevCart.filter(cartItem => cartItem.id !== itemId));

    // Track item removal
    if (item) {
      trackCartEvent('remove_from_cart', {
        item_id: item.id,
        item_name: item.name,
        value: getItemTotal(item),
        quantity: item.quantity
      });
    }

    showSuccessNotification('Item removed from cart');
  }, [cart, showSuccessNotification, trackCartEvent, getItemTotal]);



  // Create checkout session with Lemon Squeezy
  const createCheckoutSession = useCallback(async (checkoutData) => {
    const response = await fetch('/api/checkout/create-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${user?.token}`
      },
      body: JSON.stringify(checkoutData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create checkout session');
    }

    return response.json();
  }, [user?.token]);

  // Handle checkout process
  const handleCheckout = useCallback(async () => {
    if (cart.length === 0) {
      showErrorNotification('Your cart is empty');
      return;
    }

    setLoading(true);
    setCheckoutError(null);

    try {
      const checkoutData = {
        items: cart.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description,
          quantity: item.quantity,
          price: getItemPrice(item),
          billing_cycle: item.billing_cycle || 'monthly'
        })),
        total: getCartTotal(),
        user_id: user?.id,
        currency: 'USD',
        success_url: `${window.location.origin}/checkout/success`,
        cancel_url: `${window.location.origin}/cart`
      };

      // Create checkout session
      const session = await createCheckoutSession(checkoutData);

      if (session.checkout_url) {
        // Track checkout initiation
        trackCartEvent('begin_checkout', {
          value: getCartTotal(),
          items_count: getCartItemCount(),
          currency: 'USD',
          items: cart.map(item => ({
            item_id: item.id,
            item_name: item.name,
            quantity: item.quantity,
            price: getItemPrice(item)
          }))
        });

        // Store cart for recovery if needed
        localStorage.setItem('checkout_cart_backup', JSON.stringify(cart));

        // Redirect to Lemon Squeezy checkout
        window.location.href = session.checkout_url;
      } else {
        throw new Error('No checkout URL received from server');
      }

    } catch (error) {
      console.error('Checkout error:', error);
      setCheckoutError(error.message);
      showErrorNotification(`Checkout failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  }, [cart, user?.id, createCheckoutSession, showErrorNotification, getCartTotal, getItemPrice, getCartItemCount, trackCartEvent]);

  // Handle checkout confirmation dialog
  const handleCheckoutConfirm = () => {
    setCheckoutDialogOpen(true);
  };

  // Process payment after confirmation
  const handlePaymentProcess = async () => {
    setProcessingPayment(true);
    setCheckoutDialogOpen(false);
    await handleCheckout();
    setProcessingPayment(false);
  };

  return (
    <>
      <Helmet>
        <title>Shopping Cart | B2B Influencer Tool</title>
      </Helmet>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <IconButton onClick={() => navigate('/billing?view=addons')} sx={{ mr: 1 }}>
              <ArrowBackIcon />
            </IconButton>
            <ShoppingCartIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h4" component="h1">
              Shopping Cart
            </Typography>
          </Box>
          <Typography variant="body1" color="text.secondary">
            Review your selected add-ons and proceed to checkout
          </Typography>
        </Box>

        {cart.length === 0 ? (
          /* Empty Cart */
          <Paper sx={{ p: 6, textAlign: 'center' }}>
            <ShoppingCartIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 3 }} />
            <Typography variant="h5" gutterBottom>
              Your cart is empty
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Browse our add-ons marketplace to find features that enhance your experience
            </Typography>

            <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
              <Typography variant="body2" gutterBottom>
                <strong>Popular Add-ons:</strong>
              </Typography>
              <Typography variant="body2">
                • AI Image Generation - Create stunning visuals<br/>
                • Advanced Analytics - Deep insights into your content<br/>
                • Content Scheduler - Automate your posting schedule<br/>
                • Brand Kit - Maintain consistent branding
              </Typography>
            </Alert>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                onClick={() => navigate('/billing?view=addons')}
                startIcon={<ShoppingCartIcon />}
              >
                Browse Add-ons
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate('/dashboard')}
              >
                Back to Dashboard
              </Button>
            </Box>
          </Paper>
        ) : (
          /* Cart with Items */
          <Grid container spacing={4}>
            {/* Cart Items */}
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">
                    Cart Items ({getCartItemCount()})
                  </Typography>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<ClearIcon />}
                    onClick={clearCart}
                    size="small"
                  >
                    Clear All
                  </Button>
                </Box>

                <List sx={{ p: 0 }}>
                  {cart.map((item, index) => (
                    <React.Fragment key={item.id}>
                      <ListItem sx={{ py: 3, px: 0, alignItems: 'flex-start' }}>
                        <Box sx={{ width: '100%' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="h6" gutterBottom>
                                {item.name}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                {item.description}
                              </Typography>
                              <Chip
                                label={item.category || 'Add-on'}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                            </Box>
                            <IconButton 
                              onClick={() => removeItem(item.id)}
                              color="error"
                              sx={{ ml: 2 }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Box>

                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            {/* Quantity Controls */}
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Typography variant="body2" color="text.secondary">
                                Quantity:
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <IconButton 
                                  size="small" 
                                  onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                  disabled={item.quantity <= 1}
                                >
                                  <RemoveIcon fontSize="small" />
                                </IconButton>
                                <TextField
                                  size="small"
                                  value={item.quantity}
                                  onChange={(e) => {
                                    const qty = parseInt(e.target.value) || 1;
                                    updateQuantity(item.id, qty);
                                  }}
                                  inputProps={{ 
                                    style: { textAlign: 'center', width: '60px' },
                                    min: 1
                                  }}
                                  variant="outlined"
                                />
                                <IconButton 
                                  size="small" 
                                  onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                >
                                  <AddIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            </Box>

                            {/* Price */}
                            <Box sx={{ textAlign: 'right' }}>
                              <Typography variant="body2" color="text.secondary">
                                {formatCurrency(getItemPrice(item))} each
                              </Typography>
                              <Typography variant="h6" fontWeight="bold" color="primary.main">
                                {formatCurrency(getItemTotal(item))}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      </ListItem>
                      {index < cart.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </Paper>
            </Grid>

            {/* Order Summary */}
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, position: 'sticky', top: 20 }}>
                <Typography variant="h6" gutterBottom>
                  Order Summary
                </Typography>
                
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">
                      Items ({getCartItemCount()}):
                    </Typography>
                    <Typography variant="body2">
                      {formatCurrency(getCartTotal())}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">
                      Tax:
                    </Typography>
                    <Typography variant="body2">
                      Calculated at checkout
                    </Typography>
                  </Box>
                  <Divider sx={{ my: 2 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6" fontWeight="bold">
                      Total:
                    </Typography>
                    <Typography variant="h6" fontWeight="bold" color="primary.main">
                      {formatCurrency(getCartTotal())}
                    </Typography>
                  </Box>
                </Box>

                <Button
                  variant="contained"
                  fullWidth
                  size="large"
                  startIcon={loading || processingPayment ? <CircularProgress size={20} /> : <PaymentIcon />}
                  onClick={handleCheckoutConfirm}
                  disabled={loading || processingPayment}
                  sx={{ mb: 2 }}
                >
                  {loading || processingPayment ? 'Processing...' : 'Proceed to Checkout'}
                </Button>

                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    Add-ons will be activated immediately after successful payment.
                  </Typography>
                </Alert>

                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center', mt: 2 }}>
                  Secure payment powered by Lemon Squeezy
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        )}

        {/* Checkout Confirmation Dialog */}
        <Dialog
          open={checkoutDialogOpen}
          onClose={() => setCheckoutDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Confirm Checkout
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" gutterBottom>
              You are about to purchase the following add-ons:
            </Typography>

            <List>
              {cart.map((item) => (
                <ListItem key={item.id}>
                  <Box sx={{ width: '100%' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" fontWeight="medium">
                        {item.name}
                      </Typography>
                      <Typography variant="body2">
                        {formatCurrency(getItemTotal(item))}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      Quantity: {item.quantity} × {formatCurrency(getItemPrice(item))}
                    </Typography>
                  </Box>
                </ListItem>
              ))}
            </List>

            <Divider sx={{ my: 2 }} />

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6" fontWeight="bold">
                Total:
              </Typography>
              <Typography variant="h6" fontWeight="bold" color="primary.main">
                {formatCurrency(getCartTotal())}
              </Typography>
            </Box>

            {checkoutError && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {checkoutError}
              </Alert>
            )}

            <Alert severity="info" sx={{ mt: 2 }}>
              You will be redirected to our secure payment processor (Lemon Squeezy) to complete your purchase.
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setCheckoutDialogOpen(false)}
              disabled={processingPayment}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handlePaymentProcess}
              disabled={processingPayment}
              startIcon={processingPayment ? <CircularProgress size={16} /> : <PaymentIcon />}
            >
              {processingPayment ? 'Processing...' : 'Confirm & Pay'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </>
  );
};

export default CartPage;

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Breadcrumbs,
  Link,
  Alert,
  Skeleton,
  useTheme,
  alpha,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Business as BusinessIcon,
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  Campaign as CampaignIcon,
  TrendingUp as TrendingUpIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Category as CategoryIcon,
  Home as HomeIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

// API and utilities
import { getService, deleteService } from '../../api/services';
import { useMessage } from '../../hooks/useMessage';
import { startTiming, endTiming } from '../../utils/performance';

// Components
import ErrorBoundary from '../../components/services/ErrorBoundary';
import { useConfirmation } from '../../contexts/ConfirmationContext';

/**
 * ServiceDetail - Detailed view of a single service
 * Implements Material-UI glass morphism styling with 8px grid spacing
 * Follows WCAG 2.1 AA compliance and production standards
 */
const ServiceDetail = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { serviceId } = useParams();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { showSuccessMessage, showErrorMessage } = useMessage();
  const { showConfirmation } = useConfirmation();

  // State management
  const [service, setService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [authError, setAuthError] = useState(null);

  // Glass morphism styles
  const glassMorphismStyles = {
    background: `linear-gradient(135deg, 
      ${alpha(theme.palette.background.paper, 0.9)} 0%, 
      ${alpha(theme.palette.background.default, 0.6)} 100%)`,
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 4px 16px 0 ${alpha(theme.palette.common.black, 0.1)}`,
  };

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      setAuthError('Authentication required to view services');
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Load service data
  const loadService = useCallback(async () => {
    if (!isAuthenticated || authLoading) return;

    const timingId = startTiming('loadService', { serviceId });

    try {
      setLoading(true);
      setError(null);

      const response = await getService(serviceId);
      setService(response.data || response);

      endTiming(timingId, { success: true });
    } catch (error) {
      console.error('Error loading service:', error);

      // Handle authentication errors
      if (error.response?.status === 401) {
        setAuthError('Session expired. Please log in again.');
        navigate('/login');
        return;
      } else if (error.response?.status === 403) {
        setError('You do not have permission to view this service');
        showErrorMessage('Access denied: You do not have permission to view this service');
      } else if (error.response?.status === 404) {
        setError('Service not found');
        showErrorMessage('Service not found');
      } else {
        setError(error.response?.data?.detail || error.message || 'Failed to load service details. Please try again.');
        showErrorMessage(error.response?.data?.detail || 'Failed to load service details');
      }

      endTiming(timingId, { success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  }, [serviceId, isAuthenticated, authLoading, navigate, showErrorMessage]);

  // Load data on mount
  useEffect(() => {
    if (serviceId) {
      loadService();
    }
  }, [loadService, serviceId]);

  // Navigation handlers
  const handleBackToServices = () => {
    navigate('/services');
  };

  const handleEditService = () => {
    navigate(`/services/${serviceId}/edit`);
  };

  const handleDeleteService = async () => {
    if (!isAuthenticated) {
      showErrorMessage('Authentication required to delete services');
      navigate('/login');
      return;
    }

    const confirmed = await showConfirmation({
      title: 'Delete Service',
      message: `Are you sure you want to delete "${service?.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      severity: 'error'
    });

    if (confirmed) {
      try {
        await deleteService(serviceId);
        showSuccessMessage('Service deleted successfully');
        navigate('/services');
      } catch (error) {
        console.error('Error deleting service:', error);

        // Handle authentication errors
        if (error.response?.status === 401) {
          showErrorMessage('Session expired. Please log in again.');
          navigate('/login');
        } else if (error.response?.status === 403) {
          showErrorMessage('You do not have permission to delete this service');
        } else if (error.response?.status === 404) {
          showErrorMessage('Service not found');
        } else {
          showErrorMessage(error.response?.data?.detail || 'Failed to delete service');
        }
      }
    }
  };

  const handleViewICPs = () => {
    navigate(`/services/${serviceId}/icps`);
  };

  const handleGenerateICPs = () => {
    navigate(`/services/${serviceId}/generate-icps`);
  };

  const handleCreateCampaign = () => {
    navigate(`/services/${serviceId}/create-campaign`);
  };

  // Show loading state while checking authentication or loading service
  if (authLoading || loading) {
    return (
      <Container maxWidth="lg" sx={{ py: theme.spacing(3) }}>
        <Box sx={{ mb: theme.spacing(4) }}>
          <Skeleton variant="text" width={300} height={32} />
          <Skeleton variant="text" width={200} height={40} sx={{ mt: 2 }} />
        </Box>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 2 }} />
          </Grid>
          <Grid item xs={12} md={4}>
            <Skeleton variant="rectangular" height={300} sx={{ borderRadius: 2 }} />
          </Grid>
        </Grid>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Render authentication error state
  if (authError) {
    return (
      <Container maxWidth="lg" sx={{ py: theme.spacing(3) }}>
        <Alert severity="error" sx={{ mb: theme.spacing(3) }}>
          <AlertTitle>Authentication Error</AlertTitle>
          {authError}
        </Alert>
        <Button
          variant="contained"
          onClick={() => navigate('/login')}
          sx={{ mr: 2 }}
        >
          Go to Login
        </Button>
        <Button
          variant="outlined"
          onClick={() => navigate('/services')}
        >
          Back to Services
        </Button>
      </Container>
    );
  }

  // Render error state
  if (error || !service) {
    return (
      <Container maxWidth="lg" sx={{ py: theme.spacing(3) }}>
        <Alert severity="error" sx={{ mb: theme.spacing(3) }}>
          <AlertTitle>Error</AlertTitle>
          {error || 'Service not found'}
        </Alert>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBackToServices}
        >
          Back to Services
        </Button>
      </Container>
    );
  }

  // Check if user owns this service (for UI purposes)
  const isOwner = user && service && (
    service.user_id === user.id ||
    service.user_id === user._id ||
    user.is_admin
  );

  return (
    <ErrorBoundary fallbackMessage="The service detail page encountered an unexpected error. Please refresh the page or contact support if the issue persists.">
      <Container maxWidth="lg" sx={{ py: theme.spacing(3) }}>
        {/* Header with Navigation */}
        <Box sx={{ mb: theme.spacing(4) }}>
          {/* Breadcrumb Navigation */}
          <Box sx={{ mb: theme.spacing(2) }}>
            <Breadcrumbs aria-label="breadcrumb">
              <Link
                component="button"
                variant="body2"
                onClick={handleBackToServices}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  textDecoration: 'none',
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
                Services
              </Link>
              <Typography color="text.primary">
                {service.name}
              </Typography>
            </Breadcrumbs>
          </Box>

          {/* Header with Actions */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: theme.spacing(2) }}>
              <IconButton
                onClick={handleBackToServices}
                sx={{
                  '&:focus-visible': {
                    outline: `2px solid ${theme.palette.primary.main}`,
                    outlineOffset: '2px',
                  }
                }}
              >
                <ArrowBackIcon />
              </IconButton>
              
              <Box>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: theme.spacing(1),
                    mb: theme.spacing(0.5)
                  }}
                >
                  <BusinessIcon />
                  {service.name}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: theme.spacing(1) }}>
                  <Chip
                    label={service.status || 'active'}
                    size="small"
                    color={service.status === 'active' ? 'success' : 'warning'}
                    variant="outlined"
                  />
                  {service.category && (
                    <Chip label={service.category} size="small" variant="outlined" />
                  )}
                </Box>
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', gap: theme.spacing(1) }}>
              {isOwner ? (
                <>
                  <Button
                    variant="outlined"
                    startIcon={<EditIcon />}
                    onClick={handleEditService}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={handleDeleteService}
                  >
                    Delete
                  </Button>
                </>
              ) : (
                <Chip
                  label="View Only"
                  variant="outlined"
                  color="info"
                  size="small"
                />
              )}
            </Box>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Main Content */}
          <Grid item xs={12} md={8}>
            <Card sx={{ ...glassMorphismStyles, mb: theme.spacing(3) }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Service Description
                </Typography>
                <Typography variant="body1" color="textSecondary" paragraph>
                  {service.description || 'No description provided'}
                </Typography>

                {service.value_proposition && (
                  <>
                    <Typography variant="h6" gutterBottom sx={{ mt: theme.spacing(3) }}>
                      Value Proposition
                    </Typography>
                    <Typography variant="body1" color="textSecondary" paragraph>
                      {service.value_proposition}
                    </Typography>
                  </>
                )}

                {service.key_differentiators && service.key_differentiators.length > 0 && (
                  <>
                    <Typography variant="h6" gutterBottom sx={{ mt: theme.spacing(3) }}>
                      Key Differentiators
                    </Typography>
                    <List dense>
                      {service.key_differentiators.map((differentiator, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <TrendingUpIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText primary={differentiator} />
                        </ListItem>
                      ))}
                    </List>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Service Details */}
            <Card sx={glassMorphismStyles}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Service Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: theme.spacing(1) }}>
                      <CategoryIcon sx={{ mr: theme.spacing(1), color: 'text.secondary' }} />
                      <Typography variant="body2" color="textSecondary">
                        Industry: {service.target_industry || 'Not specified'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: theme.spacing(1) }}>
                      <MoneyIcon sx={{ mr: theme.spacing(1), color: 'text.secondary' }} />
                      <Typography variant="body2" color="textSecondary">
                        Pricing: {service.pricing_model || 'Not specified'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: theme.spacing(1) }}>
                      <ScheduleIcon sx={{ mr: theme.spacing(1), color: 'text.secondary' }} />
                      <Typography variant="body2" color="textSecondary">
                        Timeline: {service.delivery_timeline || 'Not specified'}
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: theme.spacing(1) }}>
                      <TrendingUpIcon sx={{ mr: theme.spacing(1), color: 'text.secondary' }} />
                      <Typography variant="body2" color="textSecondary">
                        Level: {service.service_level || 'Not specified'}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} md={4}>
            {/* Quick Stats */}
            <Card sx={{ ...glassMorphismStyles, mb: theme.spacing(3) }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Stats
                </Typography>
                <Grid container spacing={2} sx={{ textAlign: 'center' }}>
                  <Grid item xs={4}>
                    <Typography variant="h4" color="primary">
                      {service.icps_count || 0}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      ICPs
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="h4" color="info.main">
                      {service.campaigns_count || 0}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Campaigns
                    </Typography>
                  </Grid>
                  <Grid item xs={4}>
                    <Typography variant="h4" color="success.main">
                      {service.content_count || 0}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Content
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card sx={glassMorphismStyles}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Quick Actions
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: theme.spacing(1) }}>
                  <Button
                    variant="contained"
                    startIcon={<PersonIcon />}
                    onClick={service.icps_count > 0 ? handleViewICPs : handleGenerateICPs}
                    fullWidth
                  >
                    {service.icps_count > 0 ? 'View ICPs' : 'Generate ICPs'}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<CampaignIcon />}
                    onClick={handleCreateCampaign}
                    fullWidth
                  >
                    Create Campaign
                  </Button>
                  {isOwner && (
                    <Button
                      variant="outlined"
                      startIcon={<EditIcon />}
                      onClick={handleEditService}
                      fullWidth
                    >
                      Edit Service
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </ErrorBoundary>
  );
};

export default ServiceDetail;

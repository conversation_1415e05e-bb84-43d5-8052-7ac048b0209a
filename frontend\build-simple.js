// @since 2024-1-1 to 2025-25-7
// Simple build script that bypasses complex dependencies
import { build } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

async function buildApp() {
  try {
    console.log('Starting simple build process...');
    
    await build({
      plugins: [
        react({
          jsxImportSource: '@emotion/react'
        })
      ],
      resolve: {
        alias: {
          '@': path.resolve(__dirname, './src')
        },
        extensions: ['.js', '.jsx']
      },
      define: {
        "process.env.NODE_ENV": JSON.stringify("production")
      },
      build: {
        outDir: 'dist',
        sourcemap: false,
        minify: false,
        target: "es2020",
        cssCodeSplit: false,
        assetsInlineLimit: 0,
        rollupOptions: {
          output: {
            // Simple chunking strategy
            manualChunks: {
              'vendor': ['react', 'react-dom'],
              'mui': ['@mui/material']
            }
          },
          external: [], // Don't externalize anything
          treeshake: false,
          maxParallelFileOps: 1 // Limit parallel operations
        }
      },
      optimizeDeps: {
        include: ["react", "react-dom"],
        exclude: [
          '@mui/icons-material',
          '@mui/x-data-grid',
          'recharts',
          'chart.js'
        ],
        force: true
      },
      clearScreen: false,
      logLevel: "error"
    });
    
    console.log('Build completed successfully!');
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

buildApp();

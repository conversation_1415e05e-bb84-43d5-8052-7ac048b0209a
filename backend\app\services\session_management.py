"""
Session management service for handling user sessions, authentication tokens, and security.
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from bson import ObjectId

from app.db.database import get_database
from app.core.config import settings

logger = logging.getLogger(__name__)

class SessionManagementService:
    """Service for managing user sessions and authentication tokens."""
    
    def __init__(self):
        self.db = None
    
    async def get_database(self):
        """Get database connection."""
        if not self.db:
            self.db = await get_database()
            # Ensure indexes exist for optimal performance
            await self._ensure_indexes()
        return self.db

    async def _ensure_indexes(self):
        """Ensure database indexes exist for session management."""
        if not self.db:
            return

        try:
            # Index for user sessions lookup
            await self.db.user_sessions.create_index([
                ("user_id", 1),
                ("is_active", 1),
                ("expires_at", 1)
            ])

            # Index for session cleanup
            await self.db.user_sessions.create_index([
                ("expires_at", 1),
                ("is_active", 1)
            ])

            # Index for last activity sorting
            await self.db.user_sessions.create_index([
                ("user_id", 1),
                ("last_activity", -1)
            ])

        except Exception as e:
            logger.warning(f"Could not create session indexes: {str(e)}")
    
    async def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Get all active sessions for a user.
        
        Args:
            user_id: The user ID to get sessions for
            
        Returns:
            List of session dictionaries
        """
        try:
            db = await self.get_database()
            
            # Query active sessions for the user
            sessions_cursor = db.user_sessions.find({
                "user_id": user_id,
                "is_active": True,
                "expires_at": {"$gt": datetime.now(timezone.utc)}
            }).sort("last_activity", -1)
            
            sessions = []
            async for session in sessions_cursor:
                sessions.append({
                    "id": str(session["_id"]),
                    "device": session.get("device_info", {}).get("device", "Unknown Device"),
                    "browser": session.get("device_info", {}).get("browser", "Unknown Browser"),
                    "os": session.get("device_info", {}).get("os", "Unknown OS"),
                    "ip": session.get("ip_address", "Unknown IP"),
                    "location": session.get("location", "Unknown Location"),
                    "last_active": session.get("last_activity", session.get("created_at")).isoformat(),
                    "created_at": session.get("created_at").isoformat(),
                    "is_current": session.get("is_current", False),
                    "user_agent": session.get("user_agent", "")
                })
            
            return sessions
            
        except Exception as e:
            logger.error(f"Error getting user sessions for user {user_id}: {str(e)}")
            raise
    
    async def revoke_user_session(self, user_id: str, session_id: str) -> bool:
        """
        Revoke a specific session for a user.
        
        Args:
            user_id: The user ID
            session_id: The session ID to revoke
            
        Returns:
            True if session was revoked, False if not found
        """
        try:
            db = await self.get_database()
            
            # Update the session to mark as inactive
            result = await db.user_sessions.update_one(
                {
                    "_id": ObjectId(session_id),
                    "user_id": user_id,
                    "is_active": True
                },
                {
                    "$set": {
                        "is_active": False,
                        "revoked_at": datetime.now(timezone.utc),
                        "revoked_reason": "user_requested"
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error revoking session {session_id} for user {user_id}: {str(e)}")
            raise
    
    async def revoke_all_user_sessions(self, user_id: str, except_current: bool = True) -> int:
        """
        Revoke all sessions for a user.
        
        Args:
            user_id: The user ID
            except_current: Whether to keep the current session active
            
        Returns:
            Number of sessions revoked
        """
        try:
            db = await self.get_database()
            
            # Build query to revoke sessions
            query = {
                "user_id": user_id,
                "is_active": True
            }
            
            # If keeping current session, exclude it
            if except_current:
                query["is_current"] = {"$ne": True}
            
            # Update all matching sessions
            result = await db.user_sessions.update_many(
                query,
                {
                    "$set": {
                        "is_active": False,
                        "revoked_at": datetime.now(timezone.utc),
                        "revoked_reason": "user_requested_all"
                    }
                }
            )
            
            return result.modified_count
            
        except Exception as e:
            logger.error(f"Error revoking all sessions for user {user_id}: {str(e)}")
            raise
    
    async def create_session(self, user_id: str, device_info: Dict[str, Any],
                           ip_address: str, user_agent: str, location: Optional[str] = None) -> str:
        """
        Create a new session for a user.
        
        Args:
            user_id: The user ID
            device_info: Device information dictionary
            ip_address: Client IP address
            user_agent: User agent string
            location: Geographic location (optional)
            
        Returns:
            Session ID
        """
        try:
            db = await self.get_database()
            
            # Mark all other sessions as not current
            await db.user_sessions.update_many(
                {"user_id": user_id},
                {"$set": {"is_current": False}}
            )
            
            # Create new session
            session_doc = {
                "user_id": user_id,
                "device_info": device_info,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "location": location or "Unknown",
                "created_at": datetime.now(timezone.utc),
                "last_activity": datetime.now(timezone.utc),
                "expires_at": datetime.now(timezone.utc) + timedelta(days=30),
                "is_active": True,
                "is_current": True
            }
            
            result = await db.user_sessions.insert_one(session_doc)
            return str(result.inserted_id)
            
        except Exception as e:
            logger.error(f"Error creating session for user {user_id}: {str(e)}")
            raise
    
    async def update_session_activity(self, session_id: str) -> bool:
        """
        Update the last activity time for a session.
        
        Args:
            session_id: The session ID
            
        Returns:
            True if updated successfully
        """
        try:
            db = await self.get_database()
            
            result = await db.user_sessions.update_one(
                {"_id": ObjectId(session_id), "is_active": True},
                {"$set": {"last_activity": datetime.now(timezone.utc)}}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating session activity for {session_id}: {str(e)}")
            return False
    
    async def cleanup_expired_sessions(self) -> int:
        """
        Clean up expired sessions.
        
        Returns:
            Number of sessions cleaned up
        """
        try:
            db = await self.get_database()
            
            result = await db.user_sessions.update_many(
                {
                    "is_active": True,
                    "expires_at": {"$lt": datetime.now(timezone.utc)}
                },
                {
                    "$set": {
                        "is_active": False,
                        "revoked_at": datetime.now(timezone.utc),
                        "revoked_reason": "expired"
                    }
                }
            )
            
            return result.modified_count
            
        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {str(e)}")
            return 0

# Create service instance
session_service = SessionManagementService()

# Export service functions
async def get_user_sessions_service(user_id: str) -> List[Dict[str, Any]]:
    """Get all active sessions for a user."""
    return await session_service.get_user_sessions(user_id)

async def revoke_user_session_service(user_id: str, session_id: str) -> bool:
    """Revoke a specific session for a user."""
    return await session_service.revoke_user_session(user_id, session_id)

async def revoke_all_user_sessions_service(user_id: str) -> int:
    """Revoke all sessions for a user except current."""
    return await session_service.revoke_all_user_sessions(user_id, except_current=True)

async def create_user_session_service(user_id: str, device_info: Dict[str, Any],
                                    ip_address: str, user_agent: str, location: Optional[str] = None) -> str:
    """Create a new session for a user."""
    return await session_service.create_session(user_id, device_info, ip_address, user_agent, location)

async def update_session_activity_service(session_id: str) -> bool:
    """Update session activity."""
    return await session_service.update_session_activity(session_id)

async def cleanup_expired_sessions_service() -> int:
    """Clean up expired sessions."""
    return await session_service.cleanup_expired_sessions()

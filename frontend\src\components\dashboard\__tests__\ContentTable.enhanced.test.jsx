/**
 * Comprehensive tests for enhanced ContentTable component with production readiness features.
 * Tests export functionality, error boundaries, performance monitoring, and user interactions.
 @since 2024-1-1 to 2025-25-7
*/
import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { SnackbarProvider } from 'notistack';
import ContentTable from '../ContentTable';
import * as useChartPerformance from '../../../hooks/useChartPerformance';
import * as html2canvas from 'html2canvas';
import * as jsPDF from 'jspdf';

// Mock dependencies
jest.mock('../../../hooks/useChartPerformance');
jest.mock('../../../hooks/useAdvancedToast', () => ({
  useAdvancedToast: () => ({
    showSuccess: jest.fn(),
    showError: jest.fn(),
    showInfo: jest.fn(),
    showWarning: jest.fn(),
  }),
}));
jest.mock('html2canvas');
jest.mock('jspdf');
jest.mock('react-router-dom', () => ({
  useNavigate: () => jest.fn(),
}));

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <SnackbarProvider maxSnack={3}>
      {children}
    </SnackbarProvider>
  </ThemeProvider>
);

describe('ContentTable Enhanced Features', () => {
  const mockPerformanceHook = {
    trackInteraction: jest.fn(),
    exportChart: jest.fn(),
    startPerformanceMeasurement: jest.fn(),
    endPerformanceMeasurement: jest.fn(),
    performanceMetrics: {
      renderTime: 150,
      averageRenderTime: 120,
      renderCount: 5,
      performanceIssues: 0,
    },
  };

  const sampleData = [
    {
      id: '1',
      title: 'Sample Content 1',
      platform: 'linkedin',
      published_at: '2023-12-01T10:00:00Z',
      views: 1500,
      likes: 120,
      comments: 25,
      shares: 10,
      engagement_rate: 8.5,
      content_type: 'Image',
    },
    {
      id: '2',
      title: 'Sample Content 2',
      platform: 'twitter',
      published_at: '2023-12-02T14:30:00Z',
      views: 2200,
      likes: 180,
      comments: 35,
      shares: 15,
      engagement_rate: 10.2,
      content_type: 'Video',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    useChartPerformance.useChartPerformance.mockReturnValue(mockPerformanceHook);
  });

  describe('Export Functionality', () => {
    test('should render export button and menu', async () => {
      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Find export button
      const exportButton = screen.getByRole('button', { name: /export data/i });
      expect(exportButton).toBeInTheDocument();

      // Click export button to open menu
      await userEvent.click(exportButton);

      // Check menu items
      expect(screen.getByText('Export as PNG')).toBeInTheDocument();
      expect(screen.getByText('Export as PDF')).toBeInTheDocument();
    });

    test('should export as PNG successfully', async () => {
      const mockCanvas = {
        toDataURL: jest.fn().mockReturnValue('data:image/png;base64,mock'),
      };
      html2canvas.default = jest.fn().mockResolvedValue(mockCanvas);
      mockPerformanceHook.exportChart.mockResolvedValue(true);

      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Open export menu and click PNG option
      const exportButton = screen.getByRole('button', { name: /export data/i });
      await userEvent.click(exportButton);
      
      const pngOption = screen.getByText('Export as PNG');
      await userEvent.click(pngOption);

      await waitFor(() => {
        expect(mockPerformanceHook.exportChart).toHaveBeenCalledWith(
          expect.any(Object),
          'png',
          expect.stringContaining('content-performance-table-')
        );
      });
    });

    test('should export as PDF successfully', async () => {
      const mockCanvas = {
        toDataURL: jest.fn().mockReturnValue('data:image/png;base64,mock'),
        width: 800,
        height: 600,
      };
      html2canvas.default = jest.fn().mockResolvedValue(mockCanvas);
      mockPerformanceHook.exportChart.mockResolvedValue(true);

      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Open export menu and click PDF option
      const exportButton = screen.getByRole('button', { name: /export data/i });
      await userEvent.click(exportButton);
      
      const pdfOption = screen.getByText('Export as PDF');
      await userEvent.click(pdfOption);

      await waitFor(() => {
        expect(mockPerformanceHook.exportChart).toHaveBeenCalledWith(
          expect.any(Object),
          'pdf',
          expect.stringContaining('content-performance-table-')
        );
      });
    });

    test('should handle export errors gracefully', async () => {
      mockPerformanceHook.exportChart.mockRejectedValue(new Error('Export failed'));

      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Open export menu and click PNG option
      const exportButton = screen.getByRole('button', { name: /export data/i });
      await userEvent.click(exportButton);
      
      const pngOption = screen.getByText('Export as PNG');
      await userEvent.click(pngOption);

      await waitFor(() => {
        expect(mockPerformanceHook.exportChart).toHaveBeenCalled();
      });

      // Export button should be re-enabled after error
      expect(exportButton).not.toBeDisabled();
    });
  });

  describe('View Mode Toggle with Performance Monitoring', () => {
    test('should track performance when switching view modes', async () => {
      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Find chart view toggle button
      const chartToggle = screen.getByRole('button', { name: /chart view/i });
      
      await userEvent.click(chartToggle);

      expect(mockPerformanceHook.startPerformanceMeasurement).toHaveBeenCalled();
      expect(mockPerformanceHook.trackInteraction).toHaveBeenCalledWith(
        'view_toggle',
        expect.objectContaining({
          from_view: 'table',
          to_view: 'chart',
          performance_metrics: mockPerformanceHook.performanceMetrics,
        })
      );

      // Performance measurement should end after state update
      await waitFor(() => {
        expect(mockPerformanceHook.endPerformanceMeasurement).toHaveBeenCalled();
      });
    });

    test('should render chart view with error boundary', async () => {
      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Switch to chart view
      const chartToggle = screen.getByRole('button', { name: /chart view/i });
      await userEvent.click(chartToggle);

      // Chart should be wrapped in error boundary
      expect(screen.getByText(/content distribution by type/i)).toBeInTheDocument();
    });
  });

  describe('Performance Monitoring Integration', () => {
    test('should initialize performance monitoring hook correctly', () => {
      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      expect(useChartPerformance.useChartPerformance).toHaveBeenCalledWith(
        'ContentTable',
        expect.objectContaining({
          onPerformanceIssue: expect.any(Function),
        })
      );
    });

    test('should handle performance issues', () => {
      const onPerformanceIssue = jest.fn();
      useChartPerformance.useChartPerformance.mockReturnValue({
        ...mockPerformanceHook,
        onPerformanceIssue,
      });

      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Simulate performance issue callback
      const hookCall = useChartPerformance.useChartPerformance.mock.calls[0];
      const options = hookCall[1];
      options.onPerformanceIssue(250); // Simulate 250ms render time

      // Should log warning (we can't easily test console.warn, but we can verify the function was called)
      expect(typeof options.onPerformanceIssue).toBe('function');
    });
  });

  describe('Error Boundary Integration', () => {
    test('should render error boundary for chart view', async () => {
      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Switch to chart view
      const chartToggle = screen.getByRole('button', { name: /chart view/i });
      await userEvent.click(chartToggle);

      // Error boundary should be present (we can't easily test error scenarios without more complex setup)
      expect(screen.getByText(/content distribution by type/i)).toBeInTheDocument();
    });

    test('should track retry interactions in error boundary', async () => {
      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      // Switch to chart view to render error boundary
      const chartToggle = screen.getByRole('button', { name: /chart view/i });
      await userEvent.click(chartToggle);

      // The error boundary onRetry callback should be set up to track interactions
      // This is tested indirectly through the component structure
      expect(mockPerformanceHook.trackInteraction).toHaveBeenCalled();
    });
  });

  describe('Accessibility and User Experience', () => {
    test('should have proper ARIA labels for export functionality', async () => {
      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      const exportButton = screen.getByRole('button', { name: /export data/i });
      expect(exportButton).toHaveAttribute('aria-label', 'Export data');
    });

    test('should disable export button during export process', async () => {
      // Mock a slow export process
      mockPerformanceHook.exportChart.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      const exportButton = screen.getByRole('button', { name: /export data/i });
      await userEvent.click(exportButton);
      
      const pngOption = screen.getByText('Export as PNG');
      await userEvent.click(pngOption);

      // Button should be disabled during export
      expect(exportButton).toBeDisabled();

      // Wait for export to complete
      await waitFor(() => {
        expect(exportButton).not.toBeDisabled();
      });
    });

    test('should show loading indicator during export', async () => {
      mockPerformanceHook.exportChart.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      const exportButton = screen.getByRole('button', { name: /export data/i });
      await userEvent.click(exportButton);
      
      const pngOption = screen.getByText('Export as PNG');
      await userEvent.click(pngOption);

      // Should show loading indicator
      expect(screen.getByRole('progressbar')).toBeInTheDocument();

      // Wait for export to complete
      await waitFor(() => {
        expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
      });
    });
  });

  describe('Data Handling and Edge Cases', () => {
    test('should handle empty data gracefully', () => {
      render(
        <TestWrapper>
          <ContentTable data={[]} />
        </TestWrapper>
      );

      expect(screen.getByText(/no content data available/i)).toBeInTheDocument();
    });

    test('should handle loading state', () => {
      render(
        <TestWrapper>
          <ContentTable data={sampleData} loading={true} />
        </TestWrapper>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    test('should handle malformed data', () => {
      const malformedData = [
        {
          id: '1',
          title: null, // Invalid title
          engagement_rate: 'invalid', // Invalid number
        },
      ];

      render(
        <TestWrapper>
          <ContentTable data={malformedData} />
        </TestWrapper>
      );

      // Should render without crashing
      expect(screen.getByRole('table')).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    test('should adapt export button size for mobile', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 400,
      });

      render(
        <TestWrapper>
          <ContentTable data={sampleData} />
        </TestWrapper>
      );

      const exportButton = screen.getByRole('button', { name: /export data/i });
      expect(exportButton).toBeInTheDocument();
      // Size would be tested through CSS classes or computed styles
    });
  });
});

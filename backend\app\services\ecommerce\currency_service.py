"""
Multi-currency Support Service for E-commerce Integration.
Provides real-time currency conversion, user preferences, and localized pricing.
"""

import logging
import asyncio
import json
import aiohttp
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from decimal import Decimal, ROUND_HALF_UP
from bson import ObjectId

from app.models.user import User
from app.db.mongodb import get_database
from app.core.redis import get_redis_client
from app.core.monitoring import monitor_performance, log_audit_event, OperationType
from app.core.config import settings

logger = logging.getLogger(__name__)

# Collection names
CURRENCY_RATES_COLLECTION = "currency_rates"
CURRENCY_PREFERENCES_COLLECTION = "currency_preferences"
CURRENCY_HISTORY_COLLECTION = "currency_history"

# Redis keys
EXCHANGE_RATES_KEY = "exchange_rates:{base_currency}"
CURRENCY_CACHE_KEY = "currency:{from_currency}:{to_currency}"
USER_CURRENCY_PREFS_KEY = "user_currency_prefs:{user_id}"

# Supported currency APIs
EXCHANGE_RATE_APIS = {
    "exchangerate_api": {
        "url": "https://api.exchangerate-api.com/v4/latest/{base}",
        "free_tier": True,
        "rate_limit": 1500  # requests per month
    },
    "fixer_io": {
        "url": "http://data.fixer.io/api/latest",
        "free_tier": True,
        "rate_limit": 100  # requests per month
    },
    "currencylayer": {
        "url": "http://api.currencylayer.com/live",
        "free_tier": True,
        "rate_limit": 1000  # requests per month
    }
}

# Default supported currencies
DEFAULT_CURRENCIES = [
    "USD", "EUR", "GBP", "CAD", "AUD", "JPY", "CHF", "CNY", "INR", "BRL",
    "MXN", "ZAR", "SGD", "HKD", "NZD", "SEK", "NOK", "DKK", "PLN", "CZK"
]


class CurrencyService:
    """
    Multi-currency service with real-time exchange rates and user preferences.
    """
    
    def __init__(self):
        self.redis_client = None
        self.db = None
        self.api_keys = {
            "fixer_io": getattr(settings, 'FIXER_IO_API_KEY', None),
            "currencylayer": getattr(settings, 'CURRENCYLAYER_API_KEY', None)
        }
        
    async def _get_redis_client(self):
        """Get Redis client for caching."""
        if not self.redis_client:
            self.redis_client = await get_redis_client()
        return self.redis_client
    
    async def _get_database(self):
        """Get MongoDB database."""
        if not self.db:
            self.db = await get_database()
        return self.db
    
    @monitor_performance("fetch_exchange_rates")
    async def fetch_exchange_rates(
        self,
        base_currency: str = "USD",
        target_currencies: Optional[List[str]] = None,
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        Fetch current exchange rates from external APIs with fallback.
        
        Args:
            base_currency: Base currency code
            target_currencies: List of target currencies (defaults to all supported)
            force_refresh: Force refresh from API instead of cache
            
        Returns:
            Exchange rates data with metadata
        """
        try:
            redis = await self._get_redis_client()
            cache_key = EXCHANGE_RATES_KEY.format(base_currency=base_currency)
            
            # Check cache first unless force refresh
            if not force_refresh and redis:
                cached_rates = await redis.get(cache_key)
                if cached_rates:
                    rates_data = json.loads(cached_rates)
                    
                    # Check if cache is still fresh (< 1 hour old)
                    cache_time = datetime.fromisoformat(rates_data.get("timestamp", ""))
                    if datetime.now(timezone.utc) - cache_time < timedelta(hours=1):
                        return {
                            "success": True,
                            "base_currency": base_currency,
                            "rates": rates_data["rates"],
                            "timestamp": rates_data["timestamp"],
                            "source": "cache"
                        }
            
            # Fetch from APIs with fallback
            rates_data = await self._fetch_from_apis(base_currency, target_currencies)
            
            if not rates_data:
                # Fallback to database cache
                return await self._get_fallback_rates(base_currency, target_currencies)
            
            # Cache the results
            if redis:
                cache_data = {
                    "rates": rates_data["rates"],
                    "timestamp": rates_data["timestamp"],
                    "source": rates_data["source"]
                }
                redis.setex(cache_key, 3600, json.dumps(cache_data, default=str))
            
            # Store in database for fallback
            await self._store_rates_in_db(base_currency, rates_data)
            
            return {
                "success": True,
                "base_currency": base_currency,
                "rates": rates_data["rates"],
                "timestamp": rates_data["timestamp"],
                "source": rates_data["source"]
            }
            
        except Exception as e:
            logger.error(f"Error fetching exchange rates: {str(e)}")
            return await self._get_fallback_rates(base_currency, target_currencies)
    
    async def _fetch_from_apis(
        self,
        base_currency: str,
        target_currencies: Optional[List[str]] = None
    ) -> Optional[Dict[str, Any]]:
        """Fetch exchange rates from external APIs with fallback."""
        
        # Try ExchangeRate-API first (free, no API key required)
        try:
            url = EXCHANGE_RATE_APIS["exchangerate_api"]["url"].format(base=base_currency)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        rates = data.get("rates", {})
                        if target_currencies:
                            rates = {k: v for k, v in rates.items() if k in target_currencies}
                        
                        return {
                            "rates": rates,
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "source": "exchangerate_api"
                        }
                        
        except Exception as e:
            logger.warning(f"ExchangeRate-API failed: {str(e)}")
        
        # Try Fixer.io if API key available
        if self.api_keys.get("fixer_io"):
            try:
                url = EXCHANGE_RATE_APIS["fixer_io"]["url"]
                params = {
                    "access_key": self.api_keys["fixer_io"],
                    "base": base_currency
                }
                
                if target_currencies:
                    params["symbols"] = ",".join(target_currencies)
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            if data.get("success"):
                                return {
                                    "rates": data.get("rates", {}),
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                    "source": "fixer_io"
                                }
                                
            except Exception as e:
                logger.warning(f"Fixer.io API failed: {str(e)}")
        
        # Try CurrencyLayer if API key available
        if self.api_keys.get("currencylayer"):
            try:
                url = EXCHANGE_RATE_APIS["currencylayer"]["url"]
                params = {
                    "access_key": self.api_keys["currencylayer"],
                    "source": base_currency
                }
                
                if target_currencies:
                    params["currencies"] = ",".join(target_currencies)
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            if data.get("success"):
                                # CurrencyLayer returns rates with source prefix (e.g., USDEUR)
                                raw_quotes = data.get("quotes", {})
                                rates = {}
                                
                                for key, value in raw_quotes.items():
                                    if key.startswith(base_currency):
                                        currency = key[len(base_currency):]
                                        rates[currency] = value
                                
                                return {
                                    "rates": rates,
                                    "timestamp": datetime.now(timezone.utc).isoformat(),
                                    "source": "currencylayer"
                                }
                                
            except Exception as e:
                logger.warning(f"CurrencyLayer API failed: {str(e)}")
        
        return None
    
    async def _get_fallback_rates(
        self,
        base_currency: str,
        target_currencies: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Get fallback exchange rates from database."""
        try:
            db = await self._get_database()
            
            # Get most recent rates from database
            rates_doc = await db[CURRENCY_RATES_COLLECTION].find_one(
                {"base_currency": base_currency},
                sort=[("timestamp", -1)]
            )
            
            if rates_doc:
                rates = rates_doc.get("rates", {})
                if target_currencies:
                    rates = {k: v for k, v in rates.items() if k in target_currencies}
                
                return {
                    "success": True,
                    "base_currency": base_currency,
                    "rates": rates,
                    "timestamp": rates_doc.get("timestamp", datetime.now(timezone.utc)).isoformat(),
                    "source": "database_fallback"
                }
            
            # Ultimate fallback - return 1:1 rates with warning
            fallback_rates = {}
            currencies = target_currencies or DEFAULT_CURRENCIES
            
            for currency in currencies:
                if currency != base_currency:
                    fallback_rates[currency] = 1.0
            
            logger.warning(f"Using fallback 1:1 exchange rates for {base_currency}")
            
            return {
                "success": False,
                "base_currency": base_currency,
                "rates": fallback_rates,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "fallback",
                "warning": "Using fallback exchange rates - may not be accurate"
            }
            
        except Exception as e:
            logger.error(f"Error getting fallback rates: {str(e)}")
            return {
                "success": False,
                "base_currency": base_currency,
                "rates": {},
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "source": "error",
                "error": str(e)
            }
    
    async def _store_rates_in_db(self, base_currency: str, rates_data: Dict[str, Any]):
        """Store exchange rates in database for fallback."""
        try:
            db = await self._get_database()
            
            rates_doc = {
                "base_currency": base_currency,
                "rates": rates_data["rates"],
                "timestamp": datetime.fromisoformat(rates_data["timestamp"]),
                "source": rates_data["source"],
                "created_at": datetime.now(timezone.utc)
            }
            
            await db[CURRENCY_RATES_COLLECTION].insert_one(rates_doc)
            
            # Clean up old rates (keep last 30 days)
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            await db[CURRENCY_RATES_COLLECTION].delete_many({
                "base_currency": base_currency,
                "timestamp": {"$lt": cutoff_date}
            })
            
        except Exception as e:
            logger.error(f"Error storing rates in database: {str(e)}")


    @monitor_performance("convert_currency")
    async def convert_currency(
        self,
        amount: Decimal,
        from_currency: str,
        to_currency: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Convert amount from one currency to another.

        Args:
            amount: Amount to convert
            from_currency: Source currency code
            to_currency: Target currency code
            user_id: Optional user ID for tracking

        Returns:
            Conversion result with rate and metadata
        """
        try:
            # Handle same currency
            if from_currency == to_currency:
                return {
                    "success": True,
                    "from_currency": from_currency,
                    "to_currency": to_currency,
                    "original_amount": amount,
                    "converted_amount": amount,
                    "exchange_rate": Decimal('1.0'),
                    "conversion_date": datetime.now(timezone.utc).isoformat(),
                    "source": "same_currency"
                }

            # Check cache first
            redis = await self._get_redis_client()
            cache_key = CURRENCY_CACHE_KEY.format(
                from_currency=from_currency,
                to_currency=to_currency
            )

            exchange_rate = None
            source = None

            if redis:
                cached_rate = await redis.get(cache_key)
                if cached_rate:
                    rate_data = json.loads(cached_rate)
                    # Check if cache is fresh (< 30 minutes)
                    cache_time = datetime.fromisoformat(rate_data["timestamp"])
                    if datetime.now(timezone.utc) - cache_time < timedelta(minutes=30):
                        exchange_rate = Decimal(str(rate_data["rate"]))
                        source = rate_data["source"]

            # Fetch fresh rates if not cached
            if exchange_rate is None:
                rates_result = await self.fetch_exchange_rates(
                    base_currency=from_currency,
                    target_currencies=[to_currency]
                )

                if rates_result.get("success") and to_currency in rates_result.get("rates", {}):
                    exchange_rate = Decimal(str(rates_result["rates"][to_currency]))
                    source = rates_result["source"]

                    # Cache the rate
                    if redis:
                        rate_cache_data = {
                            "rate": float(exchange_rate),
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                            "source": source
                        }
                        redis.setex(cache_key, 1800, json.dumps(rate_cache_data, default=str))
                else:
                    # Try reverse conversion
                    rates_result = await self.fetch_exchange_rates(
                        base_currency=to_currency,
                        target_currencies=[from_currency]
                    )

                    if rates_result.get("success") and from_currency in rates_result.get("rates", {}):
                        reverse_rate = Decimal(str(rates_result["rates"][from_currency]))
                        exchange_rate = Decimal('1.0') / reverse_rate
                        source = rates_result["source"]
                    else:
                        raise ValueError(f"Unable to get exchange rate from {from_currency} to {to_currency}")

            # Perform conversion
            converted_amount = (amount * exchange_rate).quantize(
                Decimal('0.01'), rounding=ROUND_HALF_UP
            )

            # Log conversion for tracking
            if user_id:
                await self._log_currency_conversion(
                    user_id, from_currency, to_currency, amount, converted_amount, exchange_rate
                )

            return {
                "success": True,
                "from_currency": from_currency,
                "to_currency": to_currency,
                "original_amount": amount,
                "converted_amount": converted_amount,
                "exchange_rate": exchange_rate,
                "conversion_date": datetime.now(timezone.utc).isoformat(),
                "source": source
            }

        except Exception as e:
            logger.error(f"Error converting currency {from_currency} to {to_currency}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "from_currency": from_currency,
                "to_currency": to_currency,
                "original_amount": amount
            }

    @monitor_performance("set_user_currency_preferences")
    async def set_user_currency_preferences(
        self,
        user_id: str,
        default_currency: str,
        display_currencies: List[str],
        auto_convert: bool = True
    ) -> Dict[str, Any]:
        """
        Set user currency preferences.

        Args:
            user_id: User ID
            default_currency: Default currency code
            display_currencies: List of currencies to display
            auto_convert: Whether to automatically convert prices

        Returns:
            Update result
        """
        try:
            db = await self._get_database()

            # Validate currencies
            all_currencies = [default_currency] + display_currencies
            invalid_currencies = [c for c in all_currencies if len(c) != 3 or not c.isupper()]

            if invalid_currencies:
                raise ValueError(f"Invalid currency codes: {invalid_currencies}")

            preferences = {
                "user_id": ObjectId(user_id),
                "default_currency": default_currency,
                "display_currencies": display_currencies,
                "auto_convert": auto_convert,
                "updated_at": datetime.now(timezone.utc)
            }

            # Upsert preferences
            await db[CURRENCY_PREFERENCES_COLLECTION].update_one(
                {"user_id": ObjectId(user_id)},
                {"$set": preferences},
                upsert=True
            )

            # Cache preferences
            redis = await self._get_redis_client()
            if redis:
                cache_key = USER_CURRENCY_PREFS_KEY.format(user_id=user_id)
                redis.setex(cache_key, 3600, json.dumps(preferences, default=str))

            # Log audit event
            log_audit_event(
                operation_type=OperationType.UPDATE,
                resource_type="currency_preferences",
                resource_id=user_id,
                user_id=user_id,
                details={
                    "default_currency": default_currency,
                    "display_currencies": display_currencies,
                    "auto_convert": auto_convert
                }
            )

            return {
                "success": True,
                "user_id": user_id,
                "preferences": preferences
            }

        except Exception as e:
            logger.error(f"Error setting currency preferences for user {user_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id
            }

    @monitor_performance("get_user_currency_preferences")
    async def get_user_currency_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        Get user currency preferences.

        Args:
            user_id: User ID

        Returns:
            User currency preferences
        """
        try:
            # Check cache first
            redis = await self._get_redis_client()
            cache_key = USER_CURRENCY_PREFS_KEY.format(user_id=user_id)

            if redis:
                cached_prefs = await redis.get(cache_key)
                if cached_prefs:
                    preferences = json.loads(cached_prefs)
                    return {
                        "success": True,
                        "user_id": user_id,
                        "preferences": preferences,
                        "source": "cache"
                    }

            # Get from database
            db = await self._get_database()
            prefs_doc = await db[CURRENCY_PREFERENCES_COLLECTION].find_one({
                "user_id": ObjectId(user_id)
            })

            if prefs_doc:
                preferences = {
                    "default_currency": prefs_doc.get("default_currency", "USD"),
                    "display_currencies": prefs_doc.get("display_currencies", ["USD", "EUR", "GBP"]),
                    "auto_convert": prefs_doc.get("auto_convert", True),
                    "updated_at": prefs_doc.get("updated_at", datetime.now(timezone.utc)).isoformat()
                }

                # Cache for future use
                if redis:
                    redis.setex(cache_key, 3600, json.dumps(preferences, default=str))

                return {
                    "success": True,
                    "user_id": user_id,
                    "preferences": preferences,
                    "source": "database"
                }

            # Return defaults if no preferences found
            default_preferences = {
                "default_currency": "USD",
                "display_currencies": ["USD", "EUR", "GBP"],
                "auto_convert": True,
                "updated_at": datetime.now(timezone.utc).isoformat()
            }

            return {
                "success": True,
                "user_id": user_id,
                "preferences": default_preferences,
                "source": "defaults"
            }

        except Exception as e:
            logger.error(f"Error getting currency preferences for user {user_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id
            }

    async def _log_currency_conversion(
        self,
        user_id: str,
        from_currency: str,
        to_currency: str,
        original_amount: Decimal,
        converted_amount: Decimal,
        exchange_rate: Decimal
    ):
        """Log currency conversion for analytics."""
        try:
            db = await self._get_database()

            conversion_log = {
                "user_id": ObjectId(user_id),
                "from_currency": from_currency,
                "to_currency": to_currency,
                "original_amount": float(original_amount),
                "converted_amount": float(converted_amount),
                "exchange_rate": float(exchange_rate),
                "timestamp": datetime.now(timezone.utc)
            }

            await db[CURRENCY_HISTORY_COLLECTION].insert_one(conversion_log)

        except Exception as e:
            logger.error(f"Error logging currency conversion: {str(e)}")

    async def get_supported_currencies(self) -> Dict[str, Any]:
        """Get list of supported currencies with metadata."""
        try:
            # This could be enhanced to fetch from a currencies API
            # For now, return the default supported currencies
            currencies_info = {
                "USD": {"name": "US Dollar", "symbol": "$", "decimal_places": 2},
                "EUR": {"name": "Euro", "symbol": "€", "decimal_places": 2},
                "GBP": {"name": "British Pound", "symbol": "£", "decimal_places": 2},
                "CAD": {"name": "Canadian Dollar", "symbol": "C$", "decimal_places": 2},
                "AUD": {"name": "Australian Dollar", "symbol": "A$", "decimal_places": 2},
                "JPY": {"name": "Japanese Yen", "symbol": "¥", "decimal_places": 0},
                "CHF": {"name": "Swiss Franc", "symbol": "CHF", "decimal_places": 2},
                "CNY": {"name": "Chinese Yuan", "symbol": "¥", "decimal_places": 2},
                "INR": {"name": "Indian Rupee", "symbol": "₹", "decimal_places": 2},
                "BRL": {"name": "Brazilian Real", "symbol": "R$", "decimal_places": 2}
            }

            return {
                "success": True,
                "currencies": currencies_info,
                "total_count": len(currencies_info)
            }

        except Exception as e:
            logger.error(f"Error getting supported currencies: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "currencies": {}
            }


# Create singleton instance
currency_service = CurrencyService()

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Divider,
  Fab,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as TestIcon,
  MoreVert as MoreIcon,
  CheckCircle as HealthyIcon,
  Warning as DegradedIcon,
  Error as DownIcon,
  Help as UnknownIcon,
  Api as ApiIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';

import StablePageWrapper from '../components/common/StablePageWrapper';
import ApiConfigurationForm from '../components/external-api/ApiConfigurationForm';
import { externalApiService } from '../services/externalApiService';

const ExternalApiManagement = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [autoRefresh, setAutoRefresh] = useState(true);
  
  // Data states
  const [apiConfigurations, setApiConfigurations] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [totalConfigs, setTotalConfigs] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  
  // Filter states
  const [filters, setFilters] = useState({
    provider: '',
    status: '',
    environment: '',
    search: ''
  });
  
  // Dialog states
  const [createDialog, setCreateDialog] = useState({ open: false });
  const [editDialog, setEditDialog] = useState({ open: false, config: null });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, config: null });
  const [testDialog, setTestDialog] = useState({ open: false, config: null, result: null });
  
  // Menu states
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedConfig, setSelectedConfig] = useState(null);

  // Load data on component mount and tab change
  useEffect(() => {
    loadData();
  }, [activeTab, currentPage, filters]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (activeTab === 0) { // Overview tab
        loadData(false); // Silent refresh
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, activeTab]);

  const loadData = async (showLoading = true) => {
    if (showLoading) setLoading(true);
    setError(null);

    try {
      switch (activeTab) {
        case 0: // Overview
          await Promise.all([
            loadApiConfigurations(),
            loadStatistics()
          ]);
          break;
        case 1: // Analytics
          await loadStatistics();
          break;
        default:
          await loadApiConfigurations();
      }
    } catch (err) {
      setError(err.message || 'Failed to load data');
    } finally {
      if (showLoading) setLoading(false);
    }
  };

  const loadApiConfigurations = async () => {
    try {
      const filterParams = {
        page: currentPage,
        page_size: pageSize,
        ...filters
      };
      
      let data;
      if (filters.search) {
        data = await externalApiService.searchApiConfigurations(filters.search, filterParams);
      } else {
        data = await externalApiService.getApiConfigurations(filterParams);
      }
      
      setApiConfigurations(data.apis || []);
      setTotalConfigs(data.total || 0);
    } catch (error) {
      throw new Error(`Failed to load API configurations: ${error.message}`);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await externalApiService.getApiStatistics();
      setStatistics(stats);
    } catch (error) {
      throw new Error(`Failed to load statistics: ${error.message}`);
    }
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleMenuOpen = (event, config) => {
    setAnchorEl(event.currentTarget);
    setSelectedConfig(config);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedConfig(null);
  };

  const handleCreateApi = () => {
    setCreateDialog({ open: true });
  };

  const handleEditApi = (config) => {
    setEditDialog({ open: true, config });
    handleMenuClose();
  };

  const handleDeleteApi = (config) => {
    setDeleteDialog({ open: true, config });
    handleMenuClose();
  };

  const handleTestApi = async (config) => {
    setTestDialog({ open: true, config, result: null });
    handleMenuClose();
    
    try {
      const result = await externalApiService.testApiCredentials(config.id);
      setTestDialog(prev => ({ ...prev, result }));
    } catch (error) {
      setTestDialog(prev => ({ 
        ...prev, 
        result: { 
          is_valid: false, 
          error_message: error.message 
        } 
      }));
    }
  };

  const confirmDelete = async () => {
    try {
      await externalApiService.deleteApiConfiguration(deleteDialog.config.id);
      setDeleteDialog({ open: false, config: null });
      await loadData();
    } catch (error) {
      setError(error.message);
    }
  };

  const handleSaveApi = async (apiData) => {
    try {
      await loadData(); // Refresh the data
    } catch (error) {
      setError(error.message);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'maintenance': return 'warning';
      case 'deprecated': return 'error';
      default: return 'default';
    }
  };

  const getHealthIcon = (healthStatus) => {
    switch (healthStatus) {
      case 'healthy': return <HealthyIcon color="success" />;
      case 'degraded': return <DegradedIcon color="warning" />;
      case 'down': return <DownIcon color="error" />;
      default: return <UnknownIcon color="disabled" />;
    }
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Statistics Cards */}
      {statistics && (
        <Grid item xs={12}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="glass">
                <CardContent>
                  <Typography variant="h6" color="primary">
                    Total APIs
                  </Typography>
                  <Typography variant="h4">
                    {statistics.total_apis}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {statistics.active_apis} active
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="glass">
                <CardContent>
                  <Typography variant="h6" color="success.main">
                    Healthy APIs
                  </Typography>
                  <Typography variant="h4">
                    {statistics.healthy_apis}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {statistics.degraded_apis} degraded, {statistics.down_apis} down
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="glass">
                <CardContent>
                  <Typography variant="h6" color="info.main">
                    Avg Response Time
                  </Typography>
                  <Typography variant="h4">
                    {Math.round(statistics.avg_response_time_ms)}ms
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Across all APIs
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="glass">
                <CardContent>
                  <Typography variant="h6" color="warning.main">
                    Monthly Cost
                  </Typography>
                  <Typography variant="h4">
                    ${statistics.total_cost_this_month.toFixed(2)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Estimated total
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      )}

      {/* API Configurations Table */}
      <Grid item xs={12}>
        <Card variant="glass">
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                API Configurations ({totalConfigs})
              </Typography>
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoRefresh}
                      onChange={(e) => setAutoRefresh(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Auto Refresh"
                />
                <Button
                  startIcon={<RefreshIcon />}
                  onClick={() => loadData()}
                  disabled={loading}
                  sx={{ ml: 1 }}
                >
                  Refresh
                </Button>
              </Box>
            </Box>
            
            {/* Filters */}
            <Box display="flex" gap={2} mb={2} flexWrap="wrap">
              <TextField
                label="Search"
                variant="outlined"
                size="small"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
                }}
                sx={{ minWidth: 200 }}
              />
              
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Provider</InputLabel>
                <Select
                  value={filters.provider}
                  onChange={(e) => handleFilterChange('provider', e.target.value)}
                  label="Provider"
                >
                  <MenuItem value="">All</MenuItem>
                  {externalApiService.getSupportedProviders().map(provider => (
                    <MenuItem key={provider.value} value={provider.value}>
                      {provider.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All</MenuItem>
                  {externalApiService.getStatusOptions().map(status => (
                    <MenuItem key={status.value} value={status.value}>
                      {status.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Provider</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Health</TableCell>
                    <TableCell>Environment</TableCell>
                    <TableCell>Last Check</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {apiConfigurations.map((config) => (
                    <TableRow key={config.id} hover>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {config.name}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {config.description || 'No description'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={config.provider}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={config.status}
                          color={getStatusColor(config.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title={config.health_status}>
                          {getHealthIcon(config.health_status)}
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={config.environment}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption">
                          {formatDateTime(config.last_health_check)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, config)}
                        >
                          <MoreIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderAnalyticsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card variant="glass">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              API Usage Analytics
            </Typography>
            {statistics && (
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Provider Breakdown
                  </Typography>
                  {Object.entries(statistics.provider_breakdown).map(([provider, count]) => (
                    <Box key={provider} display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">{provider}</Typography>
                      <Typography variant="body2" fontWeight="bold">{count}</Typography>
                    </Box>
                  ))}
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Top Used APIs
                  </Typography>
                  {statistics.top_used_apis.map((api, index) => (
                    <Box key={api.id} display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2">{api.name}</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {api.total_requests} requests
                      </Typography>
                    </Box>
                  ))}
                </Grid>
              </Grid>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  if (loading && !statistics) {
    return (
      <StablePageWrapper>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </StablePageWrapper>
    );
  }

  return (
    <StablePageWrapper>
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          External API Management
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Manage and monitor all external API integrations with secure credential storage
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<ApiIcon />} label="Overview" />
          <Tab icon={<AnalyticsIcon />} label="Analytics" />
        </Tabs>
      </Paper>

      {activeTab === 0 && renderOverviewTab()}
      {activeTab === 1 && renderAnalyticsTab()}

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
        }}
        onClick={handleCreateApi}
      >
        <AddIcon />
      </Fab>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleEditApi(selectedConfig)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleTestApi(selectedConfig)}>
          <ListItemIcon>
            <TestIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Test Credentials</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => handleDeleteApi(selectedConfig)}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* API Configuration Form */}
      <ApiConfigurationForm
        open={createDialog.open}
        onClose={() => setCreateDialog({ open: false })}
        onSave={handleSaveApi}
        mode="create"
      />

      <ApiConfigurationForm
        open={editDialog.open}
        onClose={() => setEditDialog({ open: false, config: null })}
        onSave={handleSaveApi}
        config={editDialog.config}
        mode="edit"
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, config: null })}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the API configuration "{deleteDialog.config?.name}"?
            This action cannot be undone and will permanently remove all associated data.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, config: null })}>
            Cancel
          </Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Test Results Dialog */}
      <Dialog
        open={testDialog.open}
        onClose={() => setTestDialog({ open: false, config: null, result: null })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Test Results - {testDialog.config?.name}
        </DialogTitle>
        <DialogContent>
          {testDialog.result ? (
            <Box>
              <Alert
                severity={testDialog.result.is_valid ? "success" : "error"}
                sx={{ mb: 2 }}
              >
                {testDialog.result.is_valid
                  ? "API credentials are valid and working correctly"
                  : "API credentials test failed"
                }
              </Alert>

              {testDialog.result.response_time_ms && (
                <Typography variant="body2" gutterBottom>
                  <strong>Response Time:</strong> {Math.round(testDialog.result.response_time_ms)}ms
                </Typography>
              )}

              {testDialog.result.error_message && (
                <Typography variant="body2" color="error" gutterBottom>
                  <strong>Error:</strong> {testDialog.result.error_message}
                </Typography>
              )}

              {testDialog.result.test_details && (
                <Typography variant="body2" color="textSecondary">
                  <strong>Tested at:</strong> {testDialog.result.test_details.tested_at}
                </Typography>
              )}
            </Box>
          ) : (
            <Box display="flex" alignItems="center" gap={2}>
              <CircularProgress size={24} />
              <Typography>Testing API credentials...</Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestDialog({ open: false, config: null, result: null })}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </StablePageWrapper>
  );
};

export default ExternalApiManagement;

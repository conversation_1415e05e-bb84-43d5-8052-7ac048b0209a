name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # ================================
  # Code Quality & Security Checks
  # ================================
  quality-checks:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        npm ci
        cd frontend && npm ci
        cd ../admin-app && npm ci
        cd ../backend && pip install -r requirements.txt

    - name: Run ESLint
      run: |
        cd frontend && npm run lint
        cd ../admin-app && npm run lint

    - name: Run TypeScript checks
      run: |
        cd frontend && npm run type-check

    - name: Run Python linting
      run: |
        cd backend
        pip install flake8 black isort
        flake8 .
        black --check .
        isort --check-only .

    - name: Security audit (npm)
      run: |
        npm audit --audit-level=high
        cd frontend && npm audit --audit-level=high
        cd ../admin-app && npm audit --audit-level=high

    - name: Security audit (Python)
      run: |
        cd backend
        pip install safety
        safety check

    - name: Validate build configuration
      run: npm run verify

    - name: Validate environment templates
      run: node scripts/validate-env.js

  # ================================
  # Unit & Integration Tests
  # ================================
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    needs: quality-checks
    
    services:
      mongodb:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: password123
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        npm ci
        cd frontend && npm ci
        cd ../admin-app && npm ci
        cd ../backend && pip install -r requirements.txt

    - name: Create test environment
      run: |
        cp .env.example .env.test
        sed -i 's/development/test/g' .env.test
        sed -i 's/localhost:27017/localhost:27017/g' .env.test

    - name: Run frontend tests
      run: |
        cd frontend
        npm run test:coverage
      env:
        CI: true

    - name: Run admin tests
      run: |
        cd admin-app
        npm run test
      env:
        CI: true

    - name: Run backend tests
      run: |
        cd backend
        python -m pytest --cov=app --cov-report=xml --cov-report=html
      env:
        ENVIRONMENT: test
        MONGODB_URL: ****************************************************************************
        REDIS_URL: redis://localhost:6379/1

    - name: Upload coverage reports
      uses: codecov/codecov-action@v4
      with:
        files: ./backend/coverage.xml,./frontend/coverage/lcov.info
        fail_ci_if_error: false
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      if: always()

  # ================================
  # Build & Package
  # ================================
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [quality-checks, test]
    
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: npm run install:all

    - name: Build application
      run: |
        npm run build:production
      env:
        NODE_OPTIONS: --max-old-space-size=4096

    - name: Verify build output
      run: |
        ls -la frontend/dist/
        ls -la admin-app/dist/
        du -sh frontend/dist/
        du -sh admin-app/dist/

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
          BUILD_VERSION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.version'] }}
          BUILD_REVISION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.revision'] }}

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: |
          frontend/dist/
          admin-app/dist/
          backend/
        retention-days: 7

  # ================================
  # End-to-End Tests
  # ================================
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        cd frontend && npm ci
        npx playwright install --with-deps

    - name: Start application
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 30

    - name: Run E2E tests
      run: |
        cd frontend
        npm run test:e2e
      env:
        CI: true

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results
        path: |
          frontend/test-results/
          frontend/playwright-report/
        retention-days: 7

    - name: Stop application
      if: always()
      run: docker-compose -f docker-compose.test.yml down

  # ================================
  # Security Scanning
  # ================================
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: javascript, python

    - name: Run CodeQL Analysis
      uses: github/codeql-action/analyze@v3

  # ================================
  # Deploy to Staging
  # ================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, test, e2e-tests]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment:
      name: staging
      url: https://staging.acesocial.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: kubectl apply -f k8s/staging/
        # Example: docker-compose -f docker-compose.staging.yml up -d

    - name: Run smoke tests
      run: |
        echo "Running smoke tests on staging..."
        # Add smoke test commands here

    - name: Notify deployment
      uses: slackapi/slack-github-action@v1.26.0
      with:
        channel-id: 'deployments'
        slack-message: "Staging deployment ${{ job.status }}: ${{ github.event.head_commit.message }}"
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      if: always()

  # ================================
  # Deploy to Production
  # ================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, test, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment:
      name: production
      url: https://acesocial.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        # Example: kubectl apply -f k8s/production/
        # Example: docker-compose -f docker-compose.prod.yml up -d

    - name: Run health checks
      run: |
        echo "Running health checks on production..."
        # Add health check commands here

    - name: Create release
      uses: softprops/action-gh-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        name: Release v${{ github.run_number }}
        body: |
          Automated release from commit ${{ github.sha }}

          Changes in this release:
          ${{ github.event.head_commit.message }}
        draft: false
        prerelease: false

    - name: Notify deployment
      uses: slackapi/slack-github-action@v1.26.0
      with:
        channel-id: 'deployments'
        slack-message: "Production deployment ${{ job.status }}: ${{ github.event.head_commit.message }}"
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      if: always()

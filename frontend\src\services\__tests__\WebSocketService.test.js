/**
 * Enhanced WebSocket Service Tests v2.0.0
 * 
 * Comprehensive test suite for the Enhanced WebSocket Service
 * with Enhanced Platform Service integration.
 * 
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import webSocketService, { MessageType, ErrorCode } from '../WebSocketService';

// Mock dependencies
vi.mock('../platformService', () => ({
  default: {
    on: vi.fn(),
    generatePlatformFingerprint: vi.fn().mockResolvedValue('mock-platform-fingerprint')
  }
}));

vi.mock('../fingerprint', () => ({
  getDeviceFingerprint: vi.fn().mockResolvedValue('mock-device-fingerprint')
}));

vi.mock('../../utils/PrometheusMetricsCollector', () => ({
  PrometheusMetricsCollector: vi.fn().mockImplementation(() => ({
    recordMessageSend: vi.fn()
  }))
}));

vi.mock('../tokenManager', () => ({
  default: {
    getToken: vi.fn().mockResolvedValue('mock-token'),
    isTokenValid: vi.fn().mockResolvedValue(true),
    getTokenPayload: vi.fn().mockResolvedValue({
      sub: 'user123',
      email: '<EMAIL>',
      subscription: { plan: 'creator' }
    })
  }
}));

// Mock WebSocket
global.WebSocket = vi.fn().mockImplementation(() => ({
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: 1, // OPEN
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

// Mock global objects
global.localStorage = localStorageMock;
global.window = {
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  location: { 
    protocol: 'https:',
    host: 'localhost:3000',
    hostname: 'localhost'
  }
};

describe('Enhanced WebSocket Service v2.0.0', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue('mock-client-id');
  });

  describe('Initialization', () => {
    test('should initialize successfully with enhanced services', async () => {
      const status = await webSocketService.initialize();
      
      expect(status).toBeDefined();
      expect(status.initialized).toBe(true);
      expect(status.config).toBeDefined();
      expect(status.security).toBeDefined();
      expect(status.performance).toBeDefined();
      expect(status.connectionHealth).toBeDefined();
    });

    test('should handle initialization failure gracefully', async () => {
      // Mock a service failure
      const originalConsoleError = console.error;
      console.error = vi.fn();
      
      // This should not throw but fall back to basic functionality
      try {
        await webSocketService.initialize();
      } catch (error) {
        // Should handle gracefully
      }
      
      console.error = originalConsoleError;
    });
  });

  describe('Enhanced Connection Management', () => {
    beforeEach(async () => {
      await webSocketService.initialize();
    });

    test('should connect with enhanced security features', async () => {
      // Mock WebSocket connection success
      const mockWebSocket = {
        send: vi.fn(),
        close: vi.fn(),
        readyState: 1,
        onopen: null,
        onclose: null,
        onerror: null,
        onmessage: null
      };
      
      global.WebSocket = vi.fn().mockImplementation(() => mockWebSocket);
      
      // Start connection (will be async)
      const connectPromise = webSocketService.connect('/api/v1/ws', true);
      
      // Simulate successful connection
      setTimeout(() => {
        if (mockWebSocket.onopen) {
          mockWebSocket.onopen({ target: mockWebSocket });
        }
        
        // Simulate connect message
        webSocketService.handleConnect({
          data: {
            connection_id: 'conn-123',
            authenticated: true
          }
        });
      }, 10);
      
      await expect(connectPromise).resolves.toBeUndefined();
    });

    test('should handle connection failure with circuit breaker', async () => {
      // Mock WebSocket connection failure
      global.WebSocket = vi.fn().mockImplementation(() => {
        throw new Error('Connection failed');
      });
      
      await expect(webSocketService.connect('/api/v1/ws', true))
        .rejects.toThrow('Connection failed');
    });

    test('should validate security context during connection', async () => {
      const status = webSocketService.getServiceStatus();
      expect(status.security).toBeDefined();
      expect(status.security.fingerprintingEnabled).toBeDefined();
    });
  });

  describe('Enhanced Message Handling', () => {
    beforeEach(async () => {
      await webSocketService.initialize();
      
      // Mock connected state
      webSocketService.connected = true;
      webSocketService.socket = {
        send: vi.fn(),
        readyState: 1
      };
    });

    test('should send message with deduplication', async () => {
      const messageId = await webSocketService.sendMessage('test', { data: 'test' });
      
      expect(messageId).toBeDefined();
      expect(webSocketService.socket.send).toHaveBeenCalled();
    });

    test('should detect and prevent duplicate messages', async () => {
      const messageData = { data: 'test' };
      
      // Send first message
      await webSocketService.sendMessage('test', messageData, 'msg-123');
      
      // Try to send duplicate
      const result = await webSocketService.sendMessage('test', messageData, 'msg-123');
      
      expect(result).toBeNull(); // Should be detected as duplicate
    });

    test('should handle message timeout', async () => {
      // Mock a command that expects response
      const promise = webSocketService.sendMessage(MessageType.COMMAND, { action: 'test' });
      
      // Should timeout after requestTimeout
      await expect(promise).rejects.toThrow('Request timeout');
    });
  });

  describe('Circuit Breaker Pattern', () => {
    beforeEach(async () => {
      await webSocketService.initialize();
    });

    test('should track failures and open circuit breaker', () => {
      // Simulate multiple failures
      for (let i = 0; i < 6; i++) {
        webSocketService._handleCircuitBreakerFailure();
      }
      
      const status = webSocketService.getServiceStatus();
      expect(status.circuitBreaker.state).toBe('OPEN');
    });

    test('should reset circuit breaker on success', () => {
      // Open circuit breaker
      for (let i = 0; i < 6; i++) {
        webSocketService._handleCircuitBreakerFailure();
      }
      
      // Reset it
      webSocketService._resetCircuitBreaker();
      
      const status = webSocketService.getServiceStatus();
      expect(status.circuitBreaker.state).toBe('CLOSED');
    });
  });

  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      await webSocketService.initialize();
    });

    test('should track performance metrics', () => {
      const status = webSocketService.getServiceStatus();
      expect(status.performance).toBeDefined();
      expect(status.performance.connectionAttempts).toBeDefined();
      expect(status.performance.messagesSent).toBeDefined();
      expect(status.performance.messagesReceived).toBeDefined();
    });

    test('should provide comprehensive service status', () => {
      const status = webSocketService.getServiceStatus();
      expect(status.initialized).toBeDefined();
      expect(status.connected).toBeDefined();
      expect(status.config).toBeDefined();
      expect(status.circuitBreaker).toBeDefined();
      expect(status.performance).toBeDefined();
      expect(status.security).toBeDefined();
      expect(status.connectionHealth).toBeDefined();
      expect(status.subscriptionTier).toBeDefined();
      expect(status.sessionId).toBeDefined();
    });
  });

  describe('Message Deduplication', () => {
    beforeEach(async () => {
      await webSocketService.initialize();
      webSocketService.connected = true;
      webSocketService.socket = { send: vi.fn(), readyState: 1 };
    });

    test('should cache messages for deduplication', async () => {
      await webSocketService.sendMessage('test', { data: 'test' }, 'msg-123');
      
      // Check if message was cached
      const isDuplicate = webSocketService._isDuplicateMessage('msg-123', 'test', { data: 'test' });
      expect(isDuplicate).toBe(true);
    });

    test('should clean up expired cache entries', () => {
      // This would be tested with time manipulation in a real scenario
      webSocketService._cleanupMessageCache();
      // Should not throw
    });
  });

  describe('Subscription Tier Integration', () => {
    beforeEach(async () => {
      await webSocketService.initialize();
    });

    test('should detect subscription tier from token', async () => {
      await webSocketService._initializeSubscriptionTierDetection();
      expect(webSocketService.subscriptionTier).toBe('creator');
    });
  });

  describe('Cleanup and Resource Management', () => {
    test('should cleanup resources properly', () => {
      webSocketService.cleanup();
      // Should not throw and should clean up properly
    });
  });
});

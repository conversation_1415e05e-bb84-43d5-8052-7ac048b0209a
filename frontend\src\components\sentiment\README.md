<!-- @since 2024-1-1 to 2025-25-7 -->
# Sentiment Analysis Dashboard

A comprehensive sentiment analysis system with Material-UI components, real-time data visualization, and production-ready features.

## 🚀 Features

### Core Components

- **SentimentOverviewCards**: Key metrics display with trend indicators
- **EnhancedSentimentTrendChart**: Interactive timeline visualization with volume charts
- **SentimentDistributionChart**: Pie/donut/bar charts for sentiment breakdown
- **KeywordAnalysisWidget**: Top keywords driving sentiment with impact analysis
- **ComparativeSentimentAnalysis**: Period-over-period comparison
- **SentimentDashboard**: Main orchestration component with settings management

### Key Features

- 📊 **Real-time Data Visualization**: Interactive charts with Recharts
- 🎨 **Material-UI Design System**: Consistent 8px grid, color palette, typography
- 📱 **Responsive Design**: Mobile-first approach with breakpoint optimization
- 🔐 **Feature Access Control**: Subscription-based feature gating
- ⚡ **Performance Optimized**: <200ms render times, efficient data handling
- ♿ **Accessibility Compliant**: WCAG 2.1 AA standards
- 🧪 **95%+ Test Coverage**: Comprehensive testing with Jest

## 📁 Project Structure

```
frontend/src/components/sentiment/
├── SentimentOverviewCards.jsx          # Overview metrics cards
├── EnhancedSentimentTrendChart.jsx     # Trend visualization
├── SentimentDistributionChart.jsx      # Distribution charts
├── KeywordAnalysisWidget.jsx          # Keyword insights
├── ComparativeSentimentAnalysis.jsx   # Period comparison
├── SentimentDashboard.jsx             # Main dashboard
└── README.md                          # This file

frontend/src/mocks/
└── sentimentData.js                   # Mock data generators

frontend/src/tests/sentiment/
├── setup.js                          # Test configuration
├── customMatchers.js                 # Custom Jest matchers
├── testResultsProcessor.js           # Test reporting
├── SentimentOverviewCards.test.jsx   # Component tests
├── SentimentDashboard.test.jsx       # Dashboard tests
├── sentimentData.test.js             # Data generator tests
└── integration.test.jsx              # End-to-end tests
```

## 🛠️ Installation & Setup

### Prerequisites

- Node.js 16+
- React 18+
- Material-UI 5+
- Recharts 2+

### Dependencies

```bash
npm install @mui/material @mui/icons-material @emotion/react @emotion/styled
npm install recharts date-fns
npm install @faker-js/faker --save-dev
```

### Development Dependencies

```bash
npm install --save-dev @testing-library/react @testing-library/jest-dom
npm install --save-dev jest-junit jest-html-reporters
```

## 🚀 Usage

### Basic Implementation

```jsx
import SentimentDashboard from "./components/sentiment/SentimentDashboard";

function App() {
  return (
    <ThemeProvider theme={theme}>
      <SentimentDashboard />
    </ThemeProvider>
  );
}
```

### Individual Components

```jsx
import {
  SentimentOverviewCards,
  EnhancedSentimentTrendChart,
  SentimentDistributionChart,
} from "./components/sentiment";

function CustomDashboard() {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <SentimentOverviewCards
          timeRange={30}
          onCardClick={(type) => console.log("Card clicked:", type)}
        />
      </Grid>
      <Grid item xs={12} md={8}>
        <EnhancedSentimentTrendChart
          timeRange={30}
          height={400}
          showVolumeChart={true}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <SentimentDistributionChart timeRange={30} height={400} />
      </Grid>
    </Grid>
  );
}
```

## 📊 Mock Data System

### Data Generators

The system includes comprehensive mock data generators for development and testing:

```javascript
import {
  generateSentimentOverview,
  generateSentimentTrendData,
  generateKeywordAnalysis,
  generateSocialMediaPost,
} from "../mocks/sentimentData";

// Generate overview data
const overview = generateSentimentOverview({
  timeRange: 30,
  platforms: ["twitter", "facebook", "instagram"],
});

// Generate trend data
const trendData = generateSentimentTrendData({
  days: 30,
  dataPoints: 30,
  trendDirection: "improving",
});

// Generate keyword analysis
const keywords = generateKeywordAnalysis({
  timeRange: 30,
  topCount: 20,
});
```

### Data Structure Examples

**Sentiment Overview**

```javascript
{
  overall_sentiment: 'positive',
  sentiment_score: 0.65,
  confidence: 0.87,
  total_posts: 1250,
  sentiment_distribution: {
    very_positive: 180,
    positive: 420,
    neutral: 350,
    negative: 200,
    very_negative: 100
  },
  platform_sentiment: [
    {
      platform: 'twitter',
      score: 0.72,
      post_count: 400,
      confidence: 0.85,
      trend: 'improving'
    }
  ]
}
```

**Social Media Post**

```javascript
{
  id: 'uuid',
  platform: 'twitter',
  content_type: 'post',
  sentiment_analysis: {
    score: 0.78,
    confidence: 0.91,
    classification: 'positive',
    emotions: { joy: 0.85, trust: 0.72, ... },
    key_phrases: ['excellent service', 'highly recommend']
  },
  engagement_metrics: {
    likes: 45,
    retweets: 12,
    comments: 8
  }
}
```

## 🧪 Testing

### Running Tests

```bash
# Run sentiment-specific tests
npm test -- --config=jest.sentiment.config.js

# Run with coverage
npm test -- --config=jest.sentiment.config.js --coverage

# Run specific test file
npm test SentimentOverviewCards.test.jsx
```

### Test Categories

- **Unit Tests**: Individual component functionality
- **Integration Tests**: Component interaction and data flow
- **Performance Tests**: Render time and efficiency
- **Accessibility Tests**: WCAG compliance
- **Data Validation Tests**: Mock data quality and consistency

### Custom Matchers

```javascript
// Sentiment-specific assertions
expect(sentimentScore).toBeValidSentimentScore();
expect(confidenceScore).toBeValidConfidenceScore();
expect(sentimentData).toHaveMatchingSentimentClassification();
expect(trendData).toBeInChronologicalOrder();
expect(keywords).toBeSortedByImpactScore();
```

## 🎨 Design System Integration

### Material-UI Theme

```javascript
const theme = createTheme({
  palette: {
    primary: { main: "#4E40C5" },
    secondary: { main: "#00E4BC" },
    success: { main: "#00D68F" },
    error: { main: "#FF3D71" },
  },
  spacing: 8, // 8px grid system
  shape: { borderRadius: 8 },
});
```

### Component Styling

- **8px Grid System**: Consistent spacing throughout
- **Border Radius**: 8px, 12px, 16px variants
- **Color Palette**: Semantic colors for sentiment states
- **Typography Scale**: Material-UI typography system
- **Responsive Breakpoints**: xs, sm, md, lg, xl

## 🔐 Feature Access Control

### Subscription Integration

```jsx
import { useSubscription } from "../contexts/SubscriptionContext";

function SentimentComponent() {
  const { hasFeatureAccess } = useSubscription();

  if (!hasFeatureAccess("sentiment_analysis")) {
    return <UpgradePrompt />;
  }

  return <SentimentDashboard />;
}
```

### Plan-based Features

- **Creator Plan**: Basic sentiment overview
- **Accelerator Plan**: Full sentiment analysis
- **Dominator Plan**: Advanced comparative analysis

## ⚡ Performance Optimization

### Rendering Performance

- **Lazy Loading**: Dynamic imports for large components
- **Memoization**: React.memo for expensive calculations
- **Virtual Scrolling**: For large datasets
- **Chart Optimization**: Efficient data processing

### Bundle Optimization

- **Code Splitting**: Route-based and component-based
- **Tree Shaking**: Unused code elimination
- **Dynamic Imports**: Load components on demand

## 📱 Responsive Design

### Breakpoint Strategy

```javascript
const theme = useTheme();
const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
const isTablet = useMediaQuery(theme.breakpoints.between("sm", "md"));
const isDesktop = useMediaQuery(theme.breakpoints.up("lg"));
```

### Mobile Optimizations

- **Touch-friendly**: 44px minimum touch targets
- **Simplified Navigation**: Collapsible sidebars
- **Optimized Charts**: Mobile-specific chart configurations
- **Performance**: Reduced data points on mobile

## 🚀 Production Deployment

### Build Configuration

```bash
# Production build
npm run build

# Analyze bundle
npm run analyze

# Test production build
npm run serve
```

### Environment Variables

```env
VITE_API_URL=https://api.production.com
VITE_SENTIMENT_CACHE_TTL=900000
VITE_CHART_ANIMATION_DURATION=300
```

### Performance Monitoring

- **Core Web Vitals**: LCP, FID, CLS tracking
- **Bundle Analysis**: Size and dependency tracking
- **Error Monitoring**: Sentry integration
- **Analytics**: User interaction tracking

## 🤝 Contributing

### Development Workflow

1. **Feature Branch**: Create from `main`
2. **Development**: Implement with tests
3. **Testing**: Achieve 95%+ coverage
4. **Review**: Code review and QA
5. **Deployment**: Merge and deploy

### Code Standards

- **ESLint**: Airbnb configuration
- **Prettier**: Code formatting
- **TypeScript**: Type safety (future migration)
- **Documentation**: JSDoc comments

## 📚 API Integration

### Real API Integration

```javascript
// Replace mock data with real API calls
import { getSentimentOverview } from "../api/sentiment";

const fetchSentimentData = async () => {
  try {
    const data = await getSentimentOverview({ timeRange: 30 });
    setSentimentData(data);
  } catch (error) {
    setError("Failed to load sentiment data");
  }
};
```

### Error Handling

- **Retry Logic**: Exponential backoff
- **Circuit Breaker**: Prevent cascade failures
- **Fallback Data**: Graceful degradation
- **User Feedback**: Clear error messages

## 🔧 Troubleshooting

### Common Issues

1. **Chart Not Rendering**: Check container dimensions
2. **Performance Issues**: Enable React DevTools Profiler
3. **Test Failures**: Check mock data consistency
4. **Accessibility Issues**: Run axe-core audits

### Debug Mode

```javascript
// Enable debug logging
localStorage.setItem("sentiment_debug", "true");
```

## 📄 License

This project is part of the Social Media Platform and follows the same licensing terms.

---

**Last Updated**: January 2024  
**Version**: 1.0.0  
**Maintainer**: Development Team

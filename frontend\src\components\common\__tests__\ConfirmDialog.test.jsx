// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ConfirmDialog from '../ConfirmDialog';

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      error: {
        main: '#F44336',
      },
      warning: {
        main: '#FF9800',
      },
      success: {
        main: '#4CAF50',
      },
      info: {
        main: '#2196F3',
      },
      text: {
        secondary: '#666666',
      },
      divider: '#E0E0E0',
    },
    spacing: (factor) => `${8 * factor}px`,
    shadows: ['none', '0px 2px 4px rgba(0,0,0,0.1)', '0px 4px 8px rgba(0,0,0,0.15)'],
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ConfirmDialog', () => {
  const mockProps = {
    open: true,
    title: 'Confirm Action',
    message: 'Are you sure you want to proceed?',
    onClose: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders confirmation dialog correctly', () => {
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to proceed?')).toBeInTheDocument();
    expect(screen.getByText('Confirm')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  test('does not render when closed', () => {
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} open={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Confirm Action')).not.toBeInTheDocument();
  });

  test('handles confirm action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    await user.click(confirmButton);

    expect(mockProps.onClose).toHaveBeenCalledWith(true);
  });

  test('handles cancel action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockProps.onClose).toHaveBeenCalledWith(false);
  });

  test('displays severity styling correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} severity="error" />
      </TestWrapper>
    );

    expect(screen.getByText('error')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <ConfirmDialog {...mockProps} severity="warning" />
      </TestWrapper>
    );

    expect(screen.getByText('warning')).toBeInTheDocument();
  });

  test('shows severity and type icons', () => {
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} severity="error" type="delete" showIcon={true} />
      </TestWrapper>
    );

    expect(screen.getByText('error')).toBeInTheDocument();
    expect(screen.getByText('delete')).toBeInTheDocument();
  });

  test('handles keyboard shortcuts', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} showKeyboardShortcuts={true} />
      </TestWrapper>
    );

    // Test Escape key
    await user.keyboard('{Escape}');
    expect(mockProps.onClose).toHaveBeenCalledWith(false);

    // Reset mock
    mockProps.onClose.mockClear();

    // Test Ctrl+Enter
    await user.keyboard('{Control>}{Enter}{/Control}');
    expect(mockProps.onClose).toHaveBeenCalledWith(true);
  });

  test('shows keyboard shortcuts help', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} showKeyboardShortcuts={true} />
      </TestWrapper>
    );

    // Press ? to show shortcuts
    await user.keyboard('?');

    expect(screen.getByText('Keyboard Shortcuts')).toBeInTheDocument();
    expect(screen.getByText('• Enter (Ctrl/Cmd): Confirm')).toBeInTheDocument();
    expect(screen.getByText('• Escape: Cancel')).toBeInTheDocument();
  });

  test('handles timer functionality', async () => {
    vi.useFakeTimers();
    
    render(
      <TestWrapper>
        <ConfirmDialog 
          {...mockProps} 
          enableTimer={true} 
          timerDuration={3000}
        />
      </TestWrapper>
    );

    expect(screen.getByText('3s')).toBeInTheDocument();

    // Fast-forward time
    vi.advanceTimersByTime(1000);
    
    await waitFor(() => {
      expect(screen.getByText('2s')).toBeInTheDocument();
    });

    // Fast-forward to timeout
    vi.advanceTimersByTime(2000);
    
    await waitFor(() => {
      expect(mockProps.onClose).toHaveBeenCalledWith(false);
    });

    vi.useRealTimers();
  });

  test('handles confirmation input requirement', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog 
          {...mockProps} 
          confirmationRequired={true}
          confirmationText="DELETE"
        />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toBeDisabled();

    const input = screen.getByPlaceholderText('DELETE');
    await user.type(input, 'DELETE');

    expect(confirmButton).not.toBeDisabled();

    await user.click(confirmButton);
    expect(mockProps.onClose).toHaveBeenCalledWith(true);
  });

  test('shows consequences warning', () => {
    render(
      <TestWrapper>
        <ConfirmDialog 
          {...mockProps} 
          showConsequences={true}
          consequences="This action cannot be undone"
        />
      </TestWrapper>
    );

    expect(screen.getByText('⚠️ Consequences')).toBeInTheDocument();
    expect(screen.getByText('This action cannot be undone')).toBeInTheDocument();
  });

  test('handles custom actions', async () => {
    const user = userEvent.setup();
    const customAction = vi.fn();
    
    const customActions = [
      {
        label: 'Custom Action',
        onClick: customAction,
        color: 'secondary',
        variant: 'outlined'
      }
    ];

    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} customActions={customActions} />
      </TestWrapper>
    );

    const customButton = screen.getByText('Custom Action');
    await user.click(customButton);

    expect(customAction).toHaveBeenCalled();
  });

  test('handles copy functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog 
          {...mockProps} 
          enableCopy={true}
          copyText="Text to copy"
        />
      </TestWrapper>
    );

    const copyButton = screen.getByRole('button', { name: /copy/i });
    await user.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Text to copy');
  });

  test('shows help text', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog 
          {...mockProps} 
          helpText="This is helpful information"
        />
      </TestWrapper>
    );

    const helpButton = screen.getByRole('button', { name: /help/i });
    await user.click(helpButton);

    expect(screen.getByText('This is helpful information')).toBeInTheDocument();
  });

  test('handles analytics tracking', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    
    render(
      <TestWrapper>
        <ConfirmDialog 
          {...mockProps} 
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    await user.click(confirmButton);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'ConfirmDialog',
        action: 'confirm'
      })
    );
  });

  test('shows details section', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog 
          {...mockProps} 
          details="Additional details about this action"
        />
      </TestWrapper>
    );

    const detailsButton = screen.getByText('Show Details');
    await user.click(detailsButton);

    expect(screen.getByText('Hide Details')).toBeInTheDocument();
    expect(screen.getByText('Additional details about this action')).toBeInTheDocument();
  });

  test('handles persistent dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} persistent={true} />
      </TestWrapper>
    );

    // Close button should not be present
    expect(screen.queryByLabelText('Close dialog')).not.toBeInTheDocument();
  });

  test('disables backdrop click when configured', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} disableBackdropClick={true} />
      </TestWrapper>
    );

    // Try to click outside the dialog
    const dialog = screen.getByRole('alertdialog');
    await user.click(dialog.parentElement);

    // onClose should not be called
    expect(mockProps.onClose).not.toHaveBeenCalled();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} />
      </TestWrapper>
    );

    const dialog = screen.getByRole('alertdialog');
    expect(dialog).toHaveAttribute('aria-labelledby', 'confirm-dialog-title');
    expect(dialog).toHaveAttribute('aria-describedby', 'confirm-dialog-description');

    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toHaveAttribute('aria-label');

    const cancelButton = screen.getByText('Cancel');
    expect(cancelButton).toHaveAttribute('aria-label');
  });

  test('handles React node message', () => {
    const customMessage = (
      <div>
        <p>Custom message</p>
        <strong>Important note</strong>
      </div>
    );

    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} message={customMessage} />
      </TestWrapper>
    );

    expect(screen.getByText('Custom message')).toBeInTheDocument();
    expect(screen.getByText('Important note')).toBeInTheDocument();
  });

  test('handles error in confirm handler', async () => {
    const user = userEvent.setup();
    const errorOnClose = vi.fn().mockImplementation(() => {
      throw new Error('Test error');
    });
    
    // Spy on console.error
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <ConfirmDialog {...mockProps} onClose={errorOnClose} />
      </TestWrapper>
    );

    const confirmButton = screen.getByText('Confirm');
    await user.click(confirmButton);

    expect(errorOnClose).toHaveBeenCalled();
    expect(consoleSpy).toHaveBeenCalledWith('Confirmation error:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });
});

// @since 2024-1-1 to 2025-25-7
import { useState, useMemo, useCallback, useEffect, memo, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Divider,
  CircularProgress,
  IconButton,
  Tooltip,
  LinearProgress,
  useTheme,
  useMediaQuery,
  alpha,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Alert,
  AlertTitle,
  Grid,
  Chip,

  Accordion,
  AccordionSummary,
  AccordionDetails,
  Skeleton
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Download as ExportIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,
  Insights as InsightsIcon,
  Timeline as TimelineIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  ThumbUp as ThumbUpIcon,
  Comment as CommentIcon,
  Share as ShareIcon,
  AutoGraph as AutoGraphIcon,
  Psychology as PsychologyIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon
} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Prediction types with enhanced configurations
const PREDICTION_TYPES = {
  PERFORMANCE: {
    id: 'performance',
    name: 'Performance Forecasting',
    icon: TrendingUpIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Predict future performance metrics',
    subscriptionLimits: {
      creator: { dataPoints: 30, predictions: 3, accuracy: 'basic' },
      accelerator: { dataPoints: 90, predictions: 10, accuracy: 'advanced' },
      dominator: { dataPoints: -1, predictions: -1, accuracy: 'ai-powered' }
    }
  },
  ENGAGEMENT: {
    id: 'engagement',
    name: 'Engagement Prediction',
    icon: ThumbUpIcon,
    color: ACE_COLORS.YELLOW,
    description: 'Predict engagement patterns and rates',
    subscriptionLimits: {
      creator: { dataPoints: 30, predictions: 3, accuracy: 'basic' },
      accelerator: { dataPoints: 90, predictions: 10, accuracy: 'advanced' },
      dominator: { dataPoints: -1, predictions: -1, accuracy: 'ai-powered' }
    }
  },
  AUDIENCE: {
    id: 'audience',
    name: 'Audience Growth Modeling',
    icon: VisibilityIcon,
    color: ACE_COLORS.DARK,
    description: 'Model audience growth and demographics',
    subscriptionLimits: {
      creator: { dataPoints: 0, predictions: 0, accuracy: 'none' },
      accelerator: { dataPoints: 60, predictions: 5, accuracy: 'advanced' },
      dominator: { dataPoints: -1, predictions: -1, accuracy: 'ai-powered' }
    }
  },
  CONTENT: {
    id: 'content',
    name: 'Content Optimization',
    icon: InsightsIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Optimize content for better performance',
    subscriptionLimits: {
      creator: { dataPoints: 0, predictions: 0, accuracy: 'none' },
      accelerator: { dataPoints: 60, predictions: 5, accuracy: 'advanced' },
      dominator: { dataPoints: -1, predictions: -1, accuracy: 'ai-powered' }
    }
  },
  TRENDS: {
    id: 'trends',
    name: 'Trend Analysis',
    icon: TimelineIcon,
    color: ACE_COLORS.YELLOW,
    description: 'Analyze and predict trending topics',
    subscriptionLimits: {
      creator: { dataPoints: 0, predictions: 0, accuracy: 'none' },
      accelerator: { dataPoints: 0, predictions: 0, accuracy: 'none' },
      dominator: { dataPoints: -1, predictions: -1, accuracy: 'ai-powered' }
    }
  }
};



// Prediction metrics
const PREDICTION_METRICS = {
  VIEWS: { key: 'views', label: 'Views', icon: VisibilityIcon, color: ACE_COLORS.PURPLE },
  LIKES: { key: 'likes', label: 'Likes', icon: ThumbUpIcon, color: ACE_COLORS.YELLOW },
  COMMENTS: { key: 'comments', label: 'Comments', icon: CommentIcon, color: ACE_COLORS.DARK },
  SHARES: { key: 'shares', label: 'Shares', icon: ShareIcon, color: ACE_COLORS.PURPLE },
  ENGAGEMENT_RATE: { key: 'engagement_rate', label: 'Engagement Rate', icon: TrendingUpIcon, color: ACE_COLORS.YELLOW }
};

/**
 * Enhanced PredictiveAnalyticsWidget Component - Enterprise-grade predictive analytics visualization
 * Features: Plan-based analytics limitations, real-time prediction analysis, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced prediction insights and interactive analytics exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} [props.data] - Prediction data object
 * @param {string} [props.predictionType='performance'] - Prediction type (performance, engagement, audience, content, trends)
 * @param {string} [props.variant='trend-forecasting'] - Widget display variant
 * @param {number} [props.height=400] - Widget height in pixels
 * @param {boolean} [props.loading=false] - Loading state
 * @param {Object} [props.error=null] - Error object
 * @param {string} [props.title='Performance Predictions'] - Widget title
 * @param {boolean} [props.enableAnalytics=false] - Enable analytics features
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.enableInteractivity=false] - Enable widget interactions
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onPredictionTypeChange] - Prediction type change callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onExpand] - Expand callback
 * @param {Array} [props.metrics] - Available metrics to display
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const PredictiveAnalyticsWidget = memo(forwardRef(({
  data,
  predictionType = 'performance',

  height = 400,
  loading = false,
  error = null,
  title = "Performance Predictions",

  enableExport = false,

  realTimeUpdates = false,
  onPredictionTypeChange,
  onExport,
  onRefresh,
  onExpand,
  metrics = ['views', 'likes', 'comments', 'shares'],

  className = '',
  style = {},
  testId = 'predictive-analytics-widget',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery('(max-width:576px)');
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading || subscriptionLoading,
    refreshing: false,
    showAnalyticsPanel: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showSettingsDialog: false,
    currentPredictionType: predictionType,
    visibleMetrics: metrics,
    selectedTimeframe: '30d',
    lastUpdated: null,
    animationKey: 0,
    errors: {},
    // Prediction state
    predictionAccuracy: 0.85,
    confidenceLevel: 'advanced',
    expandedSections: {},
    predictionInsights: null
  });

































  

  // Prediction data state
  const [predictionData, setPredictionData] = useState({
    raw: data || null,
    processed: null,
    historical: [],
    forecasted: [],
    insights: null,
    accuracy: null
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);

  /**
   * Enhanced plan-based prediction validation - Production Ready
   */
  const validatePredictionFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canViewPredictions: false,
        hasPredictionsAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { dataPoints: 0, predictions: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based prediction limits
    const planLimits = {
      creator: {
        dataPoints: 30,
        predictions: 3,
        features: ['basic_predictions'],
        accuracy: 'basic',
        export: false,
        analytics: false,
        realTime: false,
        predictionTypes: ['performance'],
        customization: false
      },
      accelerator: {
        dataPoints: 90,
        predictions: 10,
        features: ['basic_predictions', 'advanced_predictions', 'prediction_interactions'],
        accuracy: 'advanced',
        export: true,
        analytics: true,
        realTime: true,
        predictionTypes: ['performance', 'engagement', 'audience', 'content'],
        customization: true
      },
      dominator: {
        dataPoints: -1,
        predictions: -1,
        features: ['basic_predictions', 'advanced_predictions', 'prediction_interactions', 'ai_powered_predictions'],
        accuracy: 'ai-powered',
        export: true,
        analytics: true,
        realTime: true,
        predictionTypes: ['performance', 'engagement', 'audience', 'content', 'trends'],
        customization: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = predictionData.processed ? 1 : 0;
    const limit = currentPlanLimits.predictions;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewPredictions: true,
      hasPredictionsAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, predictionData.processed]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const predictionLimits = validatePredictionFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasInteractivity: predictionLimits.planLimits.accuracy !== 'basic',
      hasExport: predictionLimits.planLimits.export,
      hasAnalytics: predictionLimits.planLimits.analytics,
      hasRealTime: predictionLimits.planLimits.realTime,
      hasCustomization: predictionLimits.planLimits.customization,
      maxDataPoints: predictionLimits.planLimits.dataPoints,
      maxPredictions: predictionLimits.planLimits.predictions,
      availablePredictionTypes: predictionLimits.planLimits.predictionTypes,
      availableFeatures: predictionLimits.planLimits.features,
      accuracyLevel: predictionLimits.planLimits.accuracy,
      refreshInterval: predictionLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validatePredictionFeatures]);

  /**
   * Enhanced data processing - Production Ready
   */
  const processPredictionData = useCallback(() => {
    if (!data) return null;

    const predictionLimits = validatePredictionFeatures();
    const maxDataPoints = predictionLimits.planLimits.dataPoints;

    // Process prediction data with enhanced metrics
    const processedData = {
      predicted_views: data.predicted_views || 0,
      predicted_likes: data.predicted_likes || 0,
      predicted_comments: data.predicted_comments || 0,
      predicted_shares: data.predicted_shares || 0,
      predicted_engagement_rate: data.predicted_engagement_rate || 0,
      historical_avg_views: data.historical_avg_views || 0,
      historical_avg_likes: data.historical_avg_likes || 0,
      historical_avg_comments: data.historical_avg_comments || 0,
      historical_avg_shares: data.historical_avg_shares || 0,
      historical_avg_engagement_rate: data.historical_avg_engagement_rate || 0,
      raw_prediction: data.raw_prediction || '',
      confidence_score: data.confidence_score || 0.85,
      accuracy_level: subscriptionFeatures.accuracyLevel || 'basic',
      // Apply data point limits
      dataPointsUsed: maxDataPoints === -1 ? data.dataPointsUsed || 30 : Math.min(data.dataPointsUsed || 30, maxDataPoints)
    };

    return processedData;
  }, [data, validatePredictionFeatures, subscriptionFeatures.accuracyLevel]);

  /**
   * Enhanced prediction type validation - Production Ready
   */
  const isPredictionTypeAvailable = useCallback((type) => {
    return subscriptionFeatures.availablePredictionTypes.includes(type);
  }, [subscriptionFeatures.availablePredictionTypes]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Predictive analytics widget showing ${predictionType} predictions`,
      'aria-description': ariaDescription || `Interactive ${predictionType} prediction widget displaying forecasted performance metrics`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, predictionType, realTimeUpdates]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        lastUpdated: new Date().toISOString(),
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Prediction data refreshed successfully');
      announceToScreenReader('Predictive analytics have been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh predictions: ${error.message}`);
      announceToScreenReader('Failed to refresh prediction data');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced prediction type change handler - Production Ready
   */
  const handlePredictionTypeChange = useCallback((newType) => {
    if (!isPredictionTypeAvailable(newType)) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, currentPredictionType: newType }));

    if (onPredictionTypeChange) {
      onPredictionTypeChange(newType);
    }

    announceToScreenReader(`Prediction type changed to ${newType}`);
  }, [isPredictionTypeAvailable, onPredictionTypeChange, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'png') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, predictionData.processed);
      }

      showSuccessNotification(`Predictions exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Predictions have been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export predictions: ${error.message}`);
      announceToScreenReader('Failed to export predictions');
    }
  }, [subscriptionFeatures.hasExport, predictionData.processed, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportWidget: handleExport,
    changePredictionType: handlePredictionTypeChange,
    getPredictionData: () => predictionData.processed,
    getPredictionLimits: validatePredictionFeatures,
    focus: () => setFocusToElement(document.querySelector(`[data-testid="${testId}"]`)),
    announce: (message) => announceToScreenReader(message)
  }), [
    predictionData.processed,
    validatePredictionFeatures,
    handleRefresh,
    handleExport,
    handlePredictionTypeChange,
    setFocusToElement,
    announceToScreenReader,
    testId
  ]);

  /**
   * Enhanced data processing effect - Production Ready
   */
  useEffect(() => {
    const processed = processPredictionData();
    setPredictionData(prev => ({
      ...prev,
      raw: data || null,
      processed
    }));
  }, [data, processPredictionData]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced chart data preparation - Production Ready
   */
  const prepareChartData = useCallback(() => {
    if (!predictionData.processed) return [];

    const processedData = predictionData.processed;

    return state.visibleMetrics.map(metric => {
      const metricConfig = PREDICTION_METRICS[metric.toUpperCase()] || {
        label: metric,
        color: ACE_COLORS.PURPLE
      };

      return {
        name: metricConfig.label,
        predicted: processedData[`predicted_${metric}`] || 0,
        average: processedData[`historical_avg_${metric}`] || 0,
        color: metricConfig.color,
        icon: metricConfig.icon
      };
    });
  }, [predictionData.processed, state.visibleMetrics]);

  const chartData = useMemo(() => prepareChartData(), [prepareChartData]);

  /**
   * Enhanced percentage calculation - Production Ready
   */
  const calculatePercentageDiff = useCallback((predicted, average) => {
    if (!average || average === 0) return 0;
    return ((predicted - average) / average) * 100;
  }, []);

  /**
   * Enhanced color determination - Production Ready
   */
  const getPercentageColor = useCallback((percentage) => {
    if (percentage > 10) {
      return theme.palette.success.main;
    } else if (percentage < -10) {
      return theme.palette.error.main;
    } else {
      return theme.palette.warning.main;
    }
  }, [theme.palette]);

  /**
   * Enhanced trend direction - Production Ready
   */
  const getTrendDirection = useCallback((percentage) => {
    if (percentage > 5) return 'up';
    if (percentage < -5) return 'down';
    return 'flat';
  }, []);

  /**
   * Enhanced trend icon - Production Ready
   */
  const getTrendIcon = useCallback((direction) => {
    switch (direction) {
      case 'up': return TrendingUpIcon;
      case 'down': return TrendingDownIcon;
      case 'flat': return TrendingFlatIcon;
      default: return TrendingUpIcon;
    }
  }, []);

  /**
   * Enhanced custom tooltip - Production Ready
   */
  const CustomTooltip = useCallback(({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const predicted = payload[0]?.value || 0;
      const average = payload[1]?.value || 0;
      const percentage = calculatePercentageDiff(predicted, average);
      const direction = getTrendDirection(percentage);
      const TrendIcon = getTrendIcon(direction);

      return (
        <Box
          sx={{
            backgroundColor: alpha(ACE_COLORS.WHITE, 0.95),
            p: 2,
            border: `2px solid ${ACE_COLORS.PURPLE}`,
            borderRadius: 2,
            boxShadow: theme.shadows[8],
            backdropFilter: 'blur(10px)',
            minWidth: 200
          }}
        >
          <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK, fontWeight: 600, mb: 1 }}>
            {label}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Box sx={{
              width: 12,
              height: 12,
              backgroundColor: ACE_COLORS.PURPLE,
              borderRadius: '50%',
              mr: 1
            }} />
            <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
              Predicted: {predicted.toLocaleString()}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Box sx={{
              width: 12,
              height: 12,
              backgroundColor: ACE_COLORS.YELLOW,
              borderRadius: '50%',
              mr: 1
            }} />
            <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
              Average: {average.toLocaleString()}
            </Typography>
          </Box>

          <Divider sx={{ my: 1 }} />

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TrendIcon sx={{
              color: getPercentageColor(percentage),
              fontSize: '1rem',
              mr: 1
            }} />
            <Typography
              variant="body2"
              sx={{
                color: getPercentageColor(percentage),
                fontWeight: 600
              }}
            >
              {percentage > 0 ? '+' : ''}{percentage.toFixed(1)}% vs average
            </Typography>
          </Box>

          {subscriptionFeatures.hasAnalytics && (
            <Typography variant="caption" sx={{
              color: theme.palette.text.secondary,
              mt: 1,
              display: 'block'
            }}>
              Confidence: {(state.predictionAccuracy * 100).toFixed(0)}%
            </Typography>
          )}
        </Box>
      );
    }

    return null;
  }, [
    calculatePercentageDiff,
    getTrendDirection,
    getTrendIcon,
    getPercentageColor,
    theme.shadows,
    theme.palette.text.secondary,
    subscriptionFeatures.hasAnalytics,
    state.predictionAccuracy
  ]);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced predictive analytics features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Advanced prediction algorithms',
        'Multiple prediction types',
        'AI-powered insights',
        'Real-time analytics updates',
        'Data export capabilities',
        'Custom prediction models'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  // Main render condition checks
  if (state.loading && !predictionData.processed) {
    return (
      <Card
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        sx={{ height: height || 400 }}
      >
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Skeleton variant="text" width={200} height={24} />
              <Skeleton variant="circular" width={24} height={24} />
            </Box>
          }
        />
        <Divider />
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
            <CircularProgress
              size={40}
              sx={{ color: ACE_COLORS.PURPLE }}
              aria-label="Loading prediction data"
            />
          </Box>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error || Object.keys(state.errors).length > 0) {
    return (
      <Card
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        sx={{ height: height || 400 }}
      >
        <CardHeader
          title={title}
          titleTypographyProps={{ color: 'error' }}
        />
        <Divider />
        <CardContent>
          <Alert
            severity="error"
            sx={{
              backgroundColor: alpha(theme.palette.error.main, 0.1),
              border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
            }}
          >
            <AlertTitle>Prediction Error</AlertTitle>
            {error?.message || Object.values(state.errors)[0] || 'Failed to load prediction data'}
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Card sx={{ height: height || 400, p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="error">
            Unable to load predictive analytics widget
          </Typography>
        </Card>
      }
    >
      <Card
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        sx={{
          height: height || 400,
          position: 'relative',
          backgroundColor: 'background.paper',
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`
        }}
      >
        {/* Enhanced Card Header with Controls */}
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PsychologyIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                {title}
              </Typography>
              {subscriptionFeatures.hasAnalytics && (
                <Chip
                  label={subscriptionFeatures.accuracyLevel.toUpperCase()}
                  size="small"
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    color: ACE_COLORS.PURPLE,
                    fontWeight: 600,
                    fontSize: '0.7rem'
                  }}
                />
              )}
            </Box>
          }
          action={
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {/* Prediction Type Selector */}
              <Tooltip title="Prediction Settings">
                <IconButton
                  size="small"
                  onClick={settingsAnchorEl ? () => setSettingsAnchorEl(null) : (e) => setSettingsAnchorEl(e.currentTarget)}
                  sx={{
                    color: ACE_COLORS.PURPLE,
                    '&:hover': {
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                  aria-label="Change prediction settings"
                >
                  <SettingsIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              {/* Export Button */}
              {enableExport && (
                <Tooltip title="Export Predictions">
                  <IconButton
                    size="small"
                    onClick={handleExportMenuOpen}
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.1)
                      }
                    }}
                    aria-label="Export prediction data"
                  >
                    <ExportIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}

              {/* Refresh Button */}
              <Tooltip title="Refresh Predictions">
                <IconButton
                  size="small"
                  onClick={handleRefresh}
                  disabled={state.refreshing}
                  sx={{
                    color: theme.palette.text.secondary,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1)
                    }
                  }}
                  aria-label="Refresh prediction data"
                >
                  {state.refreshing ? (
                    <CircularProgress size={16} />
                  ) : (
                    <RefreshIcon fontSize="small" />
                  )}
                </IconButton>
              </Tooltip>

              {/* Expand Button */}
              {onExpand && (
                <Tooltip title="Expand Widget">
                  <IconButton
                    size="small"
                    onClick={onExpand}
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.1)
                      }
                    }}
                    aria-label="Expand widget"
                  >
                    <ExpandMoreIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          }
        />

        {/* Real-time Updates Indicator */}
        {realTimeUpdates && subscriptionFeatures.hasRealTime && (
          <Box sx={{
            position: 'absolute',
            top: 8,
            left: 8,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 1,
            backgroundColor: alpha(theme.palette.success.main, 0.1),
            borderRadius: 1,
            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
            zIndex: 10
          }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: theme.palette.success.main,
                ...(!prefersReducedMotion && {
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 }
                  }
                })
              }}
              aria-hidden="true"
            />
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
              Live
            </Typography>
          </Box>
        )}

        <Divider />

        <CardContent sx={{ p: 3 }}>
          {predictionData.processed ? (
            <Box>
              {/* Enhanced Description */}
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Predicted performance metrics for your content compared to historical averages.
                {subscriptionFeatures.hasAnalytics && (
                  <span style={{ color: ACE_COLORS.PURPLE, fontWeight: 600 }}>
                    {' '}Powered by {subscriptionFeatures.accuracyLevel} AI algorithms.
                  </span>
                )}
              </Typography>

              {/* Enhanced Chart */}
              <ResponsiveContainer width="100%" height={isMobile ? 200 : 250}>
                <BarChart
                  data={chartData}
                  margin={{
                    top: 5,
                    right: isMobile ? 10 : 30,
                    left: isMobile ? 10 : 20,
                    bottom: 5
                  }}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke={alpha(ACE_COLORS.DARK, 0.1)}
                  />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: isMobile ? 10 : 12, fill: ACE_COLORS.DARK }}
                    axisLine={{ stroke: alpha(ACE_COLORS.DARK, 0.2) }}
                  />
                  <YAxis
                    tick={{ fontSize: isMobile ? 10 : 12, fill: ACE_COLORS.DARK }}
                    axisLine={{ stroke: alpha(ACE_COLORS.DARK, 0.2) }}
                  />
                  <RechartsTooltip content={<CustomTooltip />} />
                  <Legend
                    wrapperStyle={{ fontSize: isMobile ? '10px' : '12px' }}
                  />
                  <Bar
                    dataKey="predicted"
                    name="Predicted"
                    fill={ACE_COLORS.PURPLE}
                    radius={[2, 2, 0, 0]}
                  />
                  <Bar
                    dataKey="average"
                    name="Historical Average"
                    fill={ACE_COLORS.YELLOW}
                    radius={[2, 2, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>

              <Divider sx={{ my: 3 }} />

              {/* Enhanced Engagement Rate Section */}
              {predictionData.processed.predicted_engagement_rate !== undefined && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{
                    display: 'flex',
                    alignItems: 'center',
                    color: ACE_COLORS.DARK,
                    fontWeight: 600
                  }}>
                    <TrendingUpIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
                    Engagement Rate Prediction
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, flexWrap: 'wrap', gap: 1 }}>
                    <Typography variant="h6" sx={{ color: ACE_COLORS.PURPLE, fontWeight: 600 }}>
                      {predictionData.processed.predicted_engagement_rate.toFixed(2)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      vs
                    </Typography>
                    <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
                      {predictionData.processed.historical_avg_engagement_rate.toFixed(2)}% (Your Average)
                    </Typography>
                  </Box>

                  <LinearProgress
                    variant="determinate"
                    value={Math.min(
                      (predictionData.processed.predicted_engagement_rate /
                       predictionData.processed.historical_avg_engagement_rate) * 100,
                      200
                    )}
                    sx={{
                      height: 12,
                      borderRadius: 6,
                      backgroundColor: alpha(ACE_COLORS.DARK, 0.1),
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 6,
                        backgroundColor: getPercentageColor(
                          calculatePercentageDiff(
                            predictionData.processed.predicted_engagement_rate,
                            predictionData.processed.historical_avg_engagement_rate
                          )
                        ),
                      },
                    }}
                  />

                  <Typography
                    variant="caption"
                    sx={{
                      color: getPercentageColor(
                        calculatePercentageDiff(
                          predictionData.processed.predicted_engagement_rate,
                          predictionData.processed.historical_avg_engagement_rate
                        )
                      ),
                      fontWeight: 600,
                      mt: 1,
                      display: 'block'
                    }}
                  >
                    {calculatePercentageDiff(
                      predictionData.processed.predicted_engagement_rate,
                      predictionData.processed.historical_avg_engagement_rate
                    ).toFixed(1)}% {' '}
                    {calculatePercentageDiff(
                      predictionData.processed.predicted_engagement_rate,
                      predictionData.processed.historical_avg_engagement_rate
                    ) > 0
                      ? 'higher than your average'
                      : 'lower than your average'}
                  </Typography>
                </Box>
              )}

              {/* Enhanced AI Analysis Section */}
              {predictionData.processed.raw_prediction && (
                <Accordion
                  expanded={state.expandedSections.aiAnalysis}
                  onChange={() => setState(prev => ({
                    ...prev,
                    expandedSections: {
                      ...prev.expandedSections,
                      aiAnalysis: !prev.expandedSections.aiAnalysis
                    }
                  }))}
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                    borderRadius: 2,
                    '&:before': { display: 'none' }
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon sx={{ color: ACE_COLORS.PURPLE }} />}
                    sx={{
                      '& .MuiAccordionSummary-content': {
                        alignItems: 'center'
                      }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AutoGraphIcon sx={{ color: ACE_COLORS.PURPLE }} />
                      <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                        AI Analysis & Insights
                      </Typography>
                      {subscriptionFeatures.hasAnalytics && (
                        <Chip
                          label={`${(state.predictionAccuracy * 100).toFixed(0)}% Confidence`}
                          size="small"
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2),
                            color: ACE_COLORS.DARK,
                            fontSize: '0.7rem'
                          }}
                        />
                      )}
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Typography variant="body2" sx={{
                      color: ACE_COLORS.DARK,
                      lineHeight: 1.6,
                      whiteSpace: 'pre-wrap'
                    }}>
                      {predictionData.processed.raw_prediction}
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              )}
            </Box>
          ) : (
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              height: 300,
              gap: 2
            }}>
              <InsightsIcon sx={{ fontSize: 48, color: alpha(ACE_COLORS.PURPLE, 0.3) }} />
              <Typography variant="body2" color="text.secondary" textAlign="center">
                No prediction data available
              </Typography>
              <Typography variant="caption" color="text.secondary" textAlign="center">
                Generate content predictions by analyzing your historical performance data
              </Typography>
            </Box>
          )}
        </CardContent>

        {/* Prediction Type Menu */}
        <Menu
          anchorEl={settingsAnchorEl}
          open={Boolean(settingsAnchorEl)}
          onClose={() => setSettingsAnchorEl(null)}
          slotProps={{
            paper: {
              sx: {
                mt: 1,
                minWidth: 250,
                boxShadow: theme.shadows[8],
                border: `1px solid ${theme.palette.divider}`
              }
            }
          }}
        >
          {Object.values(PREDICTION_TYPES).map((predictionTypeConfig) => (
            <MenuItem
              key={predictionTypeConfig.id}
              onClick={() => {
                handlePredictionTypeChange(predictionTypeConfig.id);
                setSettingsAnchorEl(null);
              }}
              disabled={!isPredictionTypeAvailable(predictionTypeConfig.id)}
              sx={{
                backgroundColor: state.currentPredictionType === predictionTypeConfig.id
                  ? alpha(ACE_COLORS.PURPLE, 0.1)
                  : 'transparent'
              }}
            >
              <ListItemIcon>
                <predictionTypeConfig.icon fontSize="small" sx={{ color: predictionTypeConfig.color }} />
              </ListItemIcon>
              <ListItemText
                primary={predictionTypeConfig.name}
                secondary={predictionTypeConfig.description}
              />
              {!isPredictionTypeAvailable(predictionTypeConfig.id) && (
                <UpgradeIcon fontSize="small" sx={{ color: theme.palette.text.disabled, ml: 1 }} />
              )}
            </MenuItem>
          ))}
        </Menu>

        {/* Export Menu */}
        <Menu
          anchorEl={exportAnchorEl}
          open={state.showExportMenu}
          onClose={handleExportMenuClose}
          slotProps={{
            paper: {
              sx: {
                mt: 1,
                minWidth: 150,
                boxShadow: theme.shadows[8],
                border: `1px solid ${theme.palette.divider}`
              }
            }
          }}
        >
          <MenuItem onClick={() => {
            handleExport('png');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as PNG</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => {
            handleExport('pdf');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export Report (PDF)</ListItemText>
          </MenuItem>
        </Menu>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={handleUpgradeDialogClose}
          maxWidth="md"
          fullWidth
          slotProps={{
            paper: {
              sx: {
                borderRadius: 2,
                boxShadow: theme.shadows[16]
              }
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span">
                {getUpgradeDialogContent().title}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {getUpgradeDialogContent().content}
              </Typography>

              <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                Unlock Advanced Predictive Analytics:
              </Typography>

              <Grid container spacing={1}>
                {getUpgradeDialogContent().features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                      <Typography variant="body2">{feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Alert
              severity="info"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
              Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced predictive analytics.
            </Alert>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleUpgradeDialogClose}
              sx={{ color: theme.palette.text.secondary }}
            >
              Maybe Later
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                }
              }}
              startIcon={<UpgradeIcon />}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Card>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
PredictiveAnalyticsWidget.propTypes = {
  // Core props
  data: PropTypes.shape({
    predicted_views: PropTypes.number,
    predicted_likes: PropTypes.number,
    predicted_comments: PropTypes.number,
    predicted_shares: PropTypes.number,
    predicted_engagement_rate: PropTypes.number,
    historical_avg_views: PropTypes.number,
    historical_avg_likes: PropTypes.number,
    historical_avg_comments: PropTypes.number,
    historical_avg_shares: PropTypes.number,
    historical_avg_engagement_rate: PropTypes.number,
    raw_prediction: PropTypes.string,
    confidence_score: PropTypes.number
  }),
  predictionType: PropTypes.oneOf(['performance', 'engagement', 'audience', 'content', 'trends']),

  height: PropTypes.number,
  loading: PropTypes.bool,
  error: PropTypes.object,
  title: PropTypes.string,

  // Enhanced props
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onPredictionTypeChange: PropTypes.func,
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onExpand: PropTypes.func,
  metrics: PropTypes.array,


  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

PredictiveAnalyticsWidget.defaultProps = {
  data: null,
  predictionType: 'performance',
  height: 400,
  loading: false,
  error: null,
  title: 'Performance Predictions',
  enableExport: false,
  realTimeUpdates: false,
  metrics: ['views', 'likes', 'comments', 'shares'],
  className: '',
  style: {},
  testId: 'predictive-analytics-widget'
};

// Display name for debugging
PredictiveAnalyticsWidget.displayName = 'PredictiveAnalyticsWidget';

export default PredictiveAnalyticsWidget;

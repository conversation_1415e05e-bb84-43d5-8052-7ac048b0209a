import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ApiErrorFallback from '../ApiErrorFallback';

// Mock the notification hook
const mockNotification = {
  showSuccessNotification: vi.fn(),
  showErrorNotification: vi.fn()
};

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => mockNotification,
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue()
  }
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      error: {
        main: '#F44336',
        light: '#FFCDD2',
      },
      warning: {
        main: '#FF9800',
      },
      info: {
        main: '#2196F3',
      },
      grey: {
        50: '#FAFAFA',
        300: '#E0E0E0',
        600: '#757575',
      },
    },
    shadows: [
      'none',
      '0px 2px 1px -1px rgba(0,0,0,0.2)',
      '0px 3px 1px -2px rgba(0,0,0,0.12)',
      '0px 1px 5px 0px rgba(0,0,0,0.12)',
      '0px 2px 4px -1px rgba(0,0,0,0.2)',
      '0px 3px 5px -1px rgba(0,0,0,0.2)',
      '0px 3px 5px -1px rgba(0,0,0,0.2)',
      '0px 4px 5px -2px rgba(0,0,0,0.2)',
      '0px 5px 5px -3px rgba(0,0,0,0.2)',
    ],
    spacing: (factor) => `${8 * factor}px`,
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ApiErrorFallback', () => {
  const mockError = {
    message: 'Network Error',
    response: {
      status: 500,
      statusText: 'Internal Server Error',
      data: {
        detail: 'Database connection failed'
      }
    }
  };

  const mockProps = {
    error: mockError,
    resetError: vi.fn(),
    onBack: vi.fn(),
    title: 'Test Error',
    showDetails: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders API error fallback correctly', () => {
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} />
      </TestWrapper>
    );

    // Should render error title
    expect(screen.getByText('Test Error')).toBeInTheDocument();
    
    // Should render error status
    expect(screen.getByText('Error 500: Internal Server Error')).toBeInTheDocument();
    
    // Should render error message
    expect(screen.getByText('Database connection failed')).toBeInTheDocument();
  });

  test('categorizes different error types correctly', () => {
    const networkError = {
      code: 'NETWORK_ERROR',
      message: 'Network Error'
    };

    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} error={networkError} />
      </TestWrapper>
    );

    // Should show network error category
    expect(screen.getByText('NETWORK')).toBeInTheDocument();
  });

  test('handles server errors (5xx)', () => {
    const serverError = {
      response: {
        status: 503,
        statusText: 'Service Unavailable'
      }
    };

    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} error={serverError} />
      </TestWrapper>
    );

    // Should show server error category
    expect(screen.getByText('SERVER')).toBeInTheDocument();
  });

  test('handles authentication errors (401/403)', () => {
    const authError = {
      response: {
        status: 401,
        statusText: 'Unauthorized'
      }
    };

    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} error={authError} />
      </TestWrapper>
    );

    // Should show auth error category
    expect(screen.getByText('AUTH')).toBeInTheDocument();
  });

  test('handles not found errors (404)', () => {
    const notFoundError = {
      response: {
        status: 404,
        statusText: 'Not Found'
      }
    };

    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} error={notFoundError} />
      </TestWrapper>
    );

    // Should show not found error category
    expect(screen.getByText('NOTFOUND')).toBeInTheDocument();
  });

  test('handles rate limit errors (429)', () => {
    const rateLimitError = {
      response: {
        status: 429,
        statusText: 'Too Many Requests'
      }
    };

    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} error={rateLimitError} />
      </TestWrapper>
    );

    // Should show rate limit error category
    expect(screen.getByText('RATELIMIT')).toBeInTheDocument();
  });

  test('handles retry functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} />
      </TestWrapper>
    );

    const retryButton = screen.getByRole('button', { name: /try again/i });
    await user.click(retryButton);

    expect(mockProps.resetError).toHaveBeenCalled();
  });

  test('handles back navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} />
      </TestWrapper>
    );

    const backButton = screen.getByRole('button', { name: /go back/i });
    await user.click(backButton);

    expect(mockProps.onBack).toHaveBeenCalled();
  });

  test('shows error details when enabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} showDetails={true} />
      </TestWrapper>
    );

    // Should show details toggle button
    const detailsButton = screen.getByRole('button', { name: /show technical details/i });
    expect(detailsButton).toBeInTheDocument();

    // Click to expand details
    await user.click(detailsButton);

    // Should show error details
    expect(screen.getByText(/error details \(for developers\)/i)).toBeInTheDocument();
  });

  test('handles error details copy functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} showDetails={true} />
      </TestWrapper>
    );

    // Expand details
    const detailsButton = screen.getByRole('button', { name: /show technical details/i });
    await user.click(detailsButton);

    // Click copy button
    const copyButton = screen.getByRole('button', { name: /copy error details/i });
    await user.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalled();
    expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith(
      'Error details copied to clipboard'
    );
  });

  test('handles error reporting', async () => {
    const user = userEvent.setup();
    const onReportError = vi.fn();
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} showDetails={true} onReportError={onReportError} />
      </TestWrapper>
    );

    // Expand details
    const detailsButton = screen.getByRole('button', { name: /show technical details/i });
    await user.click(detailsButton);

    // Click report button
    const reportButton = screen.getByRole('button', { name: /report error/i });
    await user.click(reportButton);

    expect(onReportError).toHaveBeenCalledWith(mockError);
    expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith(
      'Error report sent. Thank you for helping us improve!'
    );
  });

  test('handles contact support functionality', async () => {
    const user = userEvent.setup();
    
    // Mock window.open
    const mockOpen = vi.fn();
    Object.defineProperty(window, 'open', {
      value: mockOpen,
      writable: true
    });
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} showContactSupport={true} />
      </TestWrapper>
    );

    const supportButton = screen.getByRole('button', { name: /contact support/i });
    await user.click(supportButton);

    expect(mockOpen).toHaveBeenCalledWith('/support', '_blank');
  });

  test('handles custom actions', async () => {
    const user = userEvent.setup();
    const customAction = {
      label: 'Custom Action',
      onClick: vi.fn(),
      icon: null,
      ariaLabel: 'Custom action button'
    };
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} customActions={[customAction]} />
      </TestWrapper>
    );

    const customButton = screen.getByRole('button', { name: /custom action/i });
    await user.click(customButton);

    expect(customAction.onClick).toHaveBeenCalled();
  });

  test('handles retry limits', () => {
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} retryCount={3} maxRetries={3} />
      </TestWrapper>
    );

    // Retry button should be disabled when max retries reached
    const retryButton = screen.getByRole('button', { name: /try again/i });
    expect(retryButton).toBeDisabled();
  });

  test('shows retry attempt counter', () => {
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} retryCount={2} maxRetries={3} />
      </TestWrapper>
    );

    // Should show retry attempt counter
    expect(screen.getByText('Attempt 2/3')).toBeInTheDocument();
  });

  test('handles error without response object', () => {
    const simpleError = {
      message: 'Simple error message'
    };

    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} error={simpleError} />
      </TestWrapper>
    );

    // Should render error message
    expect(screen.getByText('Simple error message')).toBeInTheDocument();
  });

  test('handles error without message', () => {
    const emptyError = {};

    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} error={emptyError} />
      </TestWrapper>
    );

    // Should show default error message
    expect(screen.getByText(/an unexpected error occurred/i)).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} />
      </TestWrapper>
    );

    // Should have proper ARIA attributes
    expect(screen.getByRole('alert')).toBeInTheDocument();
    expect(screen.getByLabelText('Error occurred')).toBeInTheDocument();
    
    // Buttons should have proper labels
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /go back/i })).toBeInTheDocument();
  });

  test('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} showDetails={true} />
      </TestWrapper>
    );

    // Should be able to navigate with keyboard
    await user.tab();
    expect(screen.getByRole('button', { name: /show technical details/i })).toHaveFocus();

    await user.tab();
    expect(screen.getByRole('button', { name: /go back/i })).toHaveFocus();

    await user.tab();
    expect(screen.getByRole('button', { name: /try again/i })).toHaveFocus();
  });

  test('handles error details focus management', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} showDetails={true} />
      </TestWrapper>
    );

    // Expand details
    const detailsButton = screen.getByRole('button', { name: /show technical details/i });
    await user.click(detailsButton);

    // Error details should be focusable
    const errorDetails = screen.getByRole('region', { name: /error details/i });
    expect(errorDetails).toBeInTheDocument();
    expect(errorDetails).toHaveAttribute('tabIndex', '0');
  });

  test('handles component without onBack prop', () => {
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} onBack={null} />
      </TestWrapper>
    );

    // Should not show back button
    expect(screen.queryByRole('button', { name: /go back/i })).not.toBeInTheDocument();
  });

  test('handles component without showContactSupport', () => {
    render(
      <TestWrapper>
        <ApiErrorFallback {...mockProps} showContactSupport={false} />
      </TestWrapper>
    );

    // Should not show contact support button
    expect(screen.queryByRole('button', { name: /contact support/i })).not.toBeInTheDocument();
  });
});

"""
Targeted marketing campaigns for ACEO add-on system.
Handles in-app notifications, email sequences, and conversion tracking.
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from app.db.mongodb import get_database
# Models are now handled as dictionaries for MongoDB
from app.services.addon_usage_tracking import usage_tracker
# Note: These functions don't exist yet, using placeholders
# from app.services.notification import send_addon_notification
# from app.services.email_service import send_addon_marketing_email
from app.core.redis import (
    redis_manager, redis_get, redis_set, redis_delete, redis_setex,
    redis_incr, redis_expire, redis_lpush, redis_llen, redis_lrange
)
from app.core.monitoring import record_marketing_metrics
from bson import ObjectId
import json

logger = logging.getLogger(__name__)


class CampaignType(str, Enum):
    """Types of marketing campaigns."""
    USAGE_BASED_UPSELL = "usage_based_upsell"
    ONBOARDING_SEQUENCE = "onboarding_sequence"
    RETENTION_CAMPAIGN = "retention_campaign"
    FEATURE_ANNOUNCEMENT = "feature_announcement"
    SEASONAL_PROMOTION = "seasonal_promotion"
    WINBACK_CAMPAIGN = "winback_campaign"


class TriggerType(str, Enum):
    """Campaign trigger types."""
    USAGE_THRESHOLD = "usage_threshold"
    TIME_BASED = "time_based"
    BEHAVIOR_BASED = "behavior_based"
    PLAN_BASED = "plan_based"
    MANUAL = "manual"


@dataclass
class CampaignTrigger:
    """Campaign trigger configuration."""
    trigger_type: TriggerType
    conditions: Dict[str, Any]
    delay_hours: int = 0


@dataclass
class CampaignMessage:
    """Campaign message configuration."""
    channel: str  # "in_app", "email", "push"
    subject: str
    content: str
    cta_text: str
    cta_url: str
    template_id: Optional[str] = None


class AddonMarketingCampaigns:
    """Marketing campaign manager for add-on system."""
    
    def __init__(self):
        self.campaign_prefix = "campaign:"
        self.user_campaign_prefix = "user_campaign:"
        
        # Pre-configured campaigns
        self.campaigns = {
            "regeneration_credits_75_percent": {
                "name": "Regeneration Credits Running Low",
                "type": CampaignType.USAGE_BASED_UPSELL,
                "trigger": CampaignTrigger(
                    trigger_type=TriggerType.USAGE_THRESHOLD,
                    conditions={
                        "usage_type": "regeneration_credits",
                        "threshold_percentage": 75,
                        "plan_eligibility": ["creator", "accelerator"]
                    }
                ),
                "messages": [
                    CampaignMessage(
                        channel="in_app",
                        subject="Running Low on Regeneration Credits",
                        content="You've used 75% of your regeneration credits. Get more to keep optimizing your content!",
                        cta_text="Get More Credits",
                        cta_url="/addons?category=regeneration"
                    ),
                    CampaignMessage(
                        channel="email",
                        subject="Don't Let Your Content Optimization Stop",
                        content="You're running low on regeneration credits. Upgrade now to keep creating perfect posts.",
                        cta_text="Upgrade Now",
                        cta_url="/addons/regeneration_booster",
                        template_id="addon_upsell_email"
                    )
                ],
                "conversion_tracking": ["click", "purchase"],
                "frequency_cap": {"hours": 24, "max_sends": 1}
            },
            
            "image_generation_90_percent": {
                "name": "Image Credits Almost Depleted",
                "type": CampaignType.USAGE_BASED_UPSELL,
                "trigger": CampaignTrigger(
                    trigger_type=TriggerType.USAGE_THRESHOLD,
                    conditions={
                        "usage_type": "image_generation",
                        "threshold_percentage": 90,
                        "plan_eligibility": ["creator", "accelerator", "dominator"]
                    }
                ),
                "messages": [
                    CampaignMessage(
                        channel="in_app",
                        subject="Image Credits Almost Gone!",
                        content="You've used 90% of your image generation credits. Don't miss out on creating stunning visuals!",
                        cta_text="Get More Images",
                        cta_url="/addons?category=image_generation"
                    )
                ],
                "conversion_tracking": ["click", "purchase"],
                "frequency_cap": {"hours": 12, "max_sends": 2}
            },
            
            "new_user_onboarding": {
                "name": "New User Add-on Introduction",
                "type": CampaignType.ONBOARDING_SEQUENCE,
                "trigger": CampaignTrigger(
                    trigger_type=TriggerType.TIME_BASED,
                    conditions={
                        "days_since_signup": 3,
                        "has_not_purchased_addon": True
                    },
                    delay_hours=72
                ),
                "messages": [
                    CampaignMessage(
                        channel="email",
                        subject="Supercharge Your ACEO Experience",
                        content="Discover powerful add-ons that can 10x your content creation efficiency.",
                        cta_text="Explore Add-ons",
                        cta_url="/addons",
                        template_id="addon_onboarding_email"
                    ),
                    CampaignMessage(
                        channel="in_app",
                        subject="New Add-ons Available!",
                        content="Enhance your workflow with our premium add-ons. Start with our most popular regeneration booster.",
                        cta_text="View Add-ons",
                        cta_url="/addons"
                    )
                ],
                "conversion_tracking": ["view", "click", "purchase"],
                "frequency_cap": {"hours": 168, "max_sends": 1}  # Once per week
            },
            
            "plan_upgrade_suggestion": {
                "name": "Plan Upgrade Suggestion",
                "type": CampaignType.RETENTION_CAMPAIGN,
                "trigger": CampaignTrigger(
                    trigger_type=TriggerType.BEHAVIOR_BASED,
                    conditions={
                        "addon_purchases_count": 3,
                        "current_plan": "creator",
                        "total_addon_spend": 50  # $50+
                    }
                ),
                "messages": [
                    CampaignMessage(
                        channel="email",
                        subject="Save Money with a Plan Upgrade",
                        content="You've spent $50+ on add-ons. Upgrade to Accelerator plan and get more included!",
                        cta_text="Compare Plans",
                        cta_url="/billing/plans",
                        template_id="plan_upgrade_suggestion"
                    )
                ],
                "conversion_tracking": ["click", "upgrade"],
                "frequency_cap": {"hours": 720, "max_sends": 1}  # Once per month
            },
            
            "seasonal_black_friday": {
                "name": "Black Friday Add-on Sale",
                "type": CampaignType.SEASONAL_PROMOTION,
                "trigger": CampaignTrigger(
                    trigger_type=TriggerType.MANUAL,
                    conditions={
                        "active_users_only": True,
                        "exclude_recent_purchasers": True
                    }
                ),
                "messages": [
                    CampaignMessage(
                        channel="email",
                        subject="🔥 Black Friday: 30% Off All Add-ons!",
                        content="Limited time offer: Get 30% off all ACEO add-ons. Boost your content creation now!",
                        cta_text="Shop Sale",
                        cta_url="/addons?promo=blackfriday30",
                        template_id="seasonal_promotion"
                    ),
                    CampaignMessage(
                        channel="in_app",
                        subject="Black Friday Sale Live!",
                        content="30% off all add-ons for a limited time. Don't miss out!",
                        cta_text="Shop Now",
                        cta_url="/addons?promo=blackfriday30"
                    )
                ],
                "conversion_tracking": ["click", "purchase"],
                "frequency_cap": {"hours": 24, "max_sends": 3}
            }
        }
    
    async def check_and_trigger_campaigns(self, user: Dict[str, Any]) -> List[str]:
        """Check and trigger applicable campaigns for a user."""
        triggered_campaigns = []

        try:
            for campaign_id, campaign_config in self.campaigns.items():
                # Check if campaign should be triggered
                should_trigger = await self._should_trigger_campaign(user, campaign_id, campaign_config)

                if should_trigger:
                    # Check frequency cap
                    if await self._check_frequency_cap(str(user["_id"]), campaign_id, campaign_config):
                        # Trigger campaign
                        success = await self._trigger_campaign(user, campaign_id, campaign_config)
                        if success:
                            triggered_campaigns.append(campaign_id)

                            # Record trigger
                            await self._record_campaign_trigger(str(user["_id"]), campaign_id)

            return triggered_campaigns

        except Exception as e:
            logger.error(f"Error checking campaigns for user {user['_id']}: {str(e)}")
            return []
    
    async def trigger_usage_upsell(self, user_id, usage_type: str,
                                 current_usage: int, total_limit: int) -> bool:
        """Trigger usage-based upsell campaign."""
        try:
            usage_percentage = (current_usage / total_limit) * 100 if total_limit > 0 else 0

            # Determine which campaign to trigger
            campaign_id = None
            if usage_type == "regeneration_credits" and usage_percentage >= 75:
                campaign_id = "regeneration_credits_75_percent"
            elif usage_type == "image_generation" and usage_percentage >= 90:
                campaign_id = "image_generation_90_percent"

            if not campaign_id:
                return False

            # Get user
            db = await get_database()
            users_collection = db["users"]

            user_obj_id = ObjectId(user_id) if isinstance(user_id, str) else user_id
            user = await users_collection.find_one({"_id": user_obj_id})
            if not user:
                return False

            # Check frequency cap
            campaign_config = self.campaigns[campaign_id]
            if not await self._check_frequency_cap(str(user_id), campaign_id, campaign_config):
                return False

            # Trigger campaign
            success = await self._trigger_campaign(user, campaign_id, campaign_config)

            if success:
                await self._record_campaign_trigger(str(user_id), campaign_id)
                plan_id = user.get("subscription", {}).get("plan_id", "creator")
                record_marketing_metrics(f"upsell_triggered_{usage_type}", plan_id)

            return success

        except Exception as e:
            logger.error(f"Error triggering usage upsell: {str(e)}")
            return False
    
    async def send_onboarding_sequence(self, user: Dict[str, Any]) -> bool:
        """Send onboarding sequence for new users."""
        try:
            campaign_id = "new_user_onboarding"
            campaign_config = self.campaigns[campaign_id]

            # Check if user is eligible
            days_since_signup = (datetime.now(timezone.utc) - user["created_at"]).days
            if days_since_signup < 3:
                return False

            # Check if user has purchased add-ons
            db = await get_database()
            user_addons_collection = db["user_addons"]
            has_addons = await user_addons_collection.find_one({"user_id": user["_id"]}) is not None
            if has_addons:
                return False

            # Check frequency cap
            if not await self._check_frequency_cap(str(user["_id"]), campaign_id, campaign_config):
                return False

            # Trigger campaign
            success = await self._trigger_campaign(user, campaign_id, campaign_config)

            if success:
                await self._record_campaign_trigger(str(user["_id"]), campaign_id)
                plan_id = user.get("subscription", {}).get("plan_id", "creator")
                record_marketing_metrics("onboarding_sent", plan_id)

            return success

        except Exception as e:
            logger.error(f"Error sending onboarding sequence: {str(e)}")
            return False
    
    async def launch_seasonal_campaign(self, campaign_id: str, target_users: Optional[List[str]] = None) -> Dict[str, Any]:
        """Launch a seasonal marketing campaign."""
        try:
            if campaign_id not in self.campaigns:
                return {"success": False, "error": "Campaign not found"}

            campaign_config = self.campaigns[campaign_id]

            # Get target users
            if target_users is None:
                target_users = await self._get_campaign_target_users(campaign_config)

            # Send campaign to all target users
            sent_count = 0
            error_count = 0

            db = await get_database()
            users_collection = db["users"]

            for user_id in target_users:
                try:
                    user_obj_id = ObjectId(user_id) if isinstance(user_id, str) else user_id
                    user = await users_collection.find_one({"_id": user_obj_id})
                    if user:
                        success = await self._trigger_campaign(user, campaign_id, campaign_config)
                        if success:
                            sent_count += 1
                            await self._record_campaign_trigger(str(user_id), campaign_id)
                        else:
                            error_count += 1
                except Exception as e:
                    logger.error(f"Error sending campaign to user {user_id}: {str(e)}")
                    error_count += 1

            # Record campaign metrics
            record_marketing_metrics(f"campaign_launched_{campaign_id}", "system", sent_count)

            return {
                "success": True,
                "campaign_id": campaign_id,
                "sent_count": sent_count,
                "error_count": error_count,
                "target_users": len(target_users)
            }

        except Exception as e:
            logger.error(f"Error launching seasonal campaign {campaign_id}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def track_campaign_conversion(self, user_id, campaign_id: str,
                                      conversion_type: str, value: float = 0.0) -> bool:
        """Track campaign conversion events."""
        try:
            conversion_data = {
                "user_id": user_id,
                "campaign_id": campaign_id,
                "conversion_type": conversion_type,
                "value": value,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Store conversion
            conversion_key = f"campaign_conversion:{campaign_id}:{conversion_type}"
            await redis_lpush(conversion_key, json.dumps(conversion_data))
            await redis_expire(conversion_key, 86400 * 90)  # 90 days

            # Update metrics
            metrics_key = f"campaign_metrics:{campaign_id}:{conversion_type}"
            await redis_incr(metrics_key)
            await redis_expire(metrics_key, 86400 * 90)
            
            record_marketing_metrics(f"campaign_conversion_{campaign_id}", conversion_type, int(value))
            
            return True
            
        except Exception as e:
            logger.error(f"Error tracking campaign conversion: {str(e)}")
            return False
    
    async def get_campaign_performance(self, campaign_id: str, days: int = 30) -> Dict[str, Any]:
        """Get campaign performance metrics."""
        try:
            if campaign_id not in self.campaigns:
                return {"error": "Campaign not found"}
            
            campaign_config = self.campaigns[campaign_id]
            
            # Get trigger count
            trigger_key = f"campaign_triggers:{campaign_id}"
            triggers = await redis_llen(trigger_key)
            
            # Get conversion metrics
            conversions = {}
            for conversion_type in campaign_config["conversion_tracking"]:
                metrics_key = f"campaign_metrics:{campaign_id}:{conversion_type}"
                count = await redis_get(metrics_key)
                conversions[conversion_type] = int(count) if count else 0
            
            # Calculate conversion rates
            conversion_rates = {}
            for conversion_type, count in conversions.items():
                conversion_rates[conversion_type] = (count / triggers * 100) if triggers > 0 else 0
            
            return {
                "campaign_id": campaign_id,
                "campaign_name": campaign_config["name"],
                "triggers": triggers,
                "conversions": conversions,
                "conversion_rates": conversion_rates,
                "period_days": days
            }
            
        except Exception as e:
            logger.error(f"Error getting campaign performance: {str(e)}")
            return {"error": str(e)}
    
    async def _should_trigger_campaign(self, user: Dict[str, Any], campaign_id: str,
                                     campaign_config: Dict[str, Any]) -> bool:
        """Check if campaign should be triggered for user."""
        try:
            trigger = campaign_config["trigger"]
            conditions = trigger.conditions

            if trigger.trigger_type == TriggerType.USAGE_THRESHOLD:
                return await self._check_usage_threshold(user, conditions)

            elif trigger.trigger_type == TriggerType.TIME_BASED:
                return await self._check_time_based_trigger(user, conditions)

            elif trigger.trigger_type == TriggerType.BEHAVIOR_BASED:
                return await self._check_behavior_trigger(user, conditions)

            elif trigger.trigger_type == TriggerType.PLAN_BASED:
                return await self._check_plan_trigger(user, conditions)

            elif trigger.trigger_type == TriggerType.MANUAL:
                # Manual campaigns are triggered externally
                return False

            return False

        except Exception as e:
            logger.error(f"Error checking campaign trigger: {str(e)}")
            return False
    
    async def _check_usage_threshold(self, user: Dict[str, Any], conditions: Dict[str, Any]) -> bool:
        """Check usage threshold conditions."""
        try:
            usage_type = conditions["usage_type"]
            threshold_percentage = conditions["threshold_percentage"]

            # Get user's current usage
            user_addons = await usage_tracker.get_addon_status(str(user["_id"]))

            for addon in user_addons:
                if addon["status"] == "active":
                    # Check if this addon provides the usage type
                    if usage_type in addon.get("usage_types", []):
                        usage_percentage = addon["usage_percentage"]
                        if usage_percentage >= threshold_percentage:
                            return True

            return False

        except Exception as e:
            logger.error(f"Error checking usage threshold: {str(e)}")
            return False
    
    async def _check_time_based_trigger(self, user: Dict[str, Any], conditions: Dict[str, Any]) -> bool:
        """Check time-based trigger conditions."""
        try:
            days_since_signup = conditions.get("days_since_signup")
            if days_since_signup:
                actual_days = (datetime.now(timezone.utc) - user["created_at"]).days
                if actual_days < days_since_signup:
                    return False

            # Check if user has not purchased add-ons
            if conditions.get("has_not_purchased_addon"):
                db = await get_database()
                user_addons_collection = db["user_addons"]
                has_addons = await user_addons_collection.find_one({"user_id": user["_id"]}) is not None
                if has_addons:
                    return False

            return True

        except Exception as e:
            logger.error(f"Error checking time-based trigger: {str(e)}")
            return False
    
    async def _check_behavior_trigger(self, user: Dict[str, Any], conditions: Dict[str, Any]) -> bool:
        """Check behavior-based trigger conditions."""
        try:
            db = await get_database()
            user_addons_collection = db["user_addons"]
            purchases_collection = db["addon_purchases"]

            # Check addon purchase count
            if "addon_purchases_count" in conditions:
                purchase_count = await user_addons_collection.count_documents({"user_id": user["_id"]})
                if purchase_count < conditions["addon_purchases_count"]:
                    return False

            # Check current plan
            if "current_plan" in conditions:
                user_plan = user.get("subscription", {}).get("plan_id", "creator")
                if user_plan != conditions["current_plan"]:
                    return False

            # Check total addon spend
            if "total_addon_spend" in conditions:
                total_spend_pipeline = [
                    {
                        "$match": {
                            "user_id": user["_id"],
                            "status": "completed"
                        }
                    },
                    {
                        "$group": {
                            "_id": None,
                            "total": {"$sum": "$amount"}
                        }
                    }
                ]

                spend_result = await purchases_collection.aggregate(total_spend_pipeline).to_list(length=1)
                total_spend = spend_result[0]["total"] if spend_result else 0
                if total_spend < conditions["total_addon_spend"]:
                    return False

            return True

        except Exception as e:
            logger.error(f"Error checking behavior trigger: {str(e)}")
            return False
    
    async def _check_plan_trigger(self, user: Dict[str, Any], conditions: Dict[str, Any]) -> bool:
        """Check plan-based trigger conditions."""
        try:
            eligible_plans = conditions.get("plan_eligibility", [])
            user_plan = user.get("subscription", {}).get("plan_id", "creator")

            return user_plan in eligible_plans

        except Exception as e:
            logger.error(f"Error checking plan trigger: {str(e)}")
            return False

    async def _check_frequency_cap(self, user_id: str, campaign_id: str,
                                 campaign_config: Dict[str, Any]) -> bool:
        """Check if user has exceeded frequency cap."""
        try:
            frequency_cap = campaign_config.get("frequency_cap")
            if not frequency_cap:
                return True
            
            cap_hours = frequency_cap["hours"]
            max_sends = frequency_cap["max_sends"]
            
            # Check recent sends
            since_time = datetime.now(timezone.utc) - timedelta(hours=cap_hours)
            recent_sends_key = f"campaign_sends:{user_id}:{campaign_id}"
            
            # Get recent sends
            recent_sends = await redis_lrange(recent_sends_key, 0, -1)
            recent_count = 0
            
            for send_data in recent_sends:
                try:
                    send_info = json.loads(send_data)
                    send_time = datetime.fromisoformat(send_info["timestamp"])
                    if send_time >= since_time:
                        recent_count += 1
                except:
                    continue
            
            return recent_count < max_sends
            
        except Exception as e:
            logger.error(f"Error checking frequency cap: {str(e)}")
            return True
    
    async def _trigger_campaign(self, user: Dict[str, Any], campaign_id: str,
                              campaign_config: Dict[str, Any]) -> bool:
        """Trigger a campaign for a user."""
        try:
            messages = campaign_config["messages"]
            success_count = 0

            for message in messages:
                try:
                    if message.channel == "in_app":
                        await send_addon_notification(
                            str(user["_id"]),
                            message.subject,
                            message.content,
                            "info",
                            action_url=message.cta_url,
                            action_text=message.cta_text
                        )
                        success_count += 1

                    elif message.channel == "email":
                        await send_addon_marketing_email(
                            str(user["_id"]),
                            message.subject,
                            message.content,
                            message.cta_text,
                            message.cta_url,
                            message.template_id
                        )
                        success_count += 1

                except Exception as e:
                    logger.error(f"Error sending message via {message.channel}: {str(e)}")

            return success_count > 0

        except Exception as e:
            logger.error(f"Error triggering campaign {campaign_id}: {str(e)}")
            return False
    
    async def _record_campaign_trigger(self, user_id: str, campaign_id: str):
        """Record campaign trigger for tracking."""
        try:
            trigger_data = {
                "user_id": user_id,
                "campaign_id": campaign_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Record in campaign triggers
            trigger_key = f"campaign_triggers:{campaign_id}"
            await redis_lpush(trigger_key, json.dumps(trigger_data))
            await redis_expire(trigger_key, 86400 * 90)  # 90 days

            # Record in user campaign sends
            user_sends_key = f"campaign_sends:{user_id}:{campaign_id}"
            await redis_lpush(user_sends_key, json.dumps(trigger_data))
            await redis_expire(user_sends_key, 86400 * 30)  # 30 days
            
        except Exception as e:
            logger.error(f"Error recording campaign trigger: {str(e)}")
    
    async def _get_campaign_target_users(self, campaign_config: Dict[str, Any]) -> List[str]:
        """Get target users for a campaign."""
        try:
            db = await get_database()
            users_collection = db["users"]
            user_addons_collection = db["user_addons"]
            conditions = campaign_config["trigger"].conditions

            # Build query filter
            query_filter = {}

            # Apply conditions
            if conditions.get("active_users_only"):
                # Users who logged in within last 30 days
                since_date = datetime.now(timezone.utc) - timedelta(days=30)
                query_filter["last_login"] = {"$gte": since_date}

            # Get users matching basic criteria
            users = await users_collection.find(query_filter, {"_id": 1}).to_list(length=None)
            user_ids = [str(user["_id"]) for user in users]

            if conditions.get("exclude_recent_purchasers"):
                # Exclude users who purchased add-ons in last 7 days
                recent_date = datetime.now(timezone.utc) - timedelta(days=7)
                recent_purchasers = await user_addons_collection.find(
                    {"purchased_at": {"$gte": recent_date}},
                    {"user_id": 1}
                ).to_list(length=None)

                recent_purchaser_ids = {str(p["user_id"]) for p in recent_purchasers}
                user_ids = [uid for uid in user_ids if uid not in recent_purchaser_ids]

            return user_ids

        except Exception as e:
            logger.error(f"Error getting campaign target users: {str(e)}")
            return []


# Global marketing campaigns manager
marketing_campaigns = AddonMarketingCampaigns()


async def trigger_usage_based_upsell(user_id, usage_type: str,
                                    current_usage: int, total_limit: int) -> bool:
    """Trigger usage-based upsell campaign."""
    return await marketing_campaigns.trigger_usage_upsell(user_id, usage_type, current_usage, total_limit)


async def send_onboarding_campaigns(user: Dict[str, Any]) -> bool:
    """Send onboarding campaigns for new users."""
    return await marketing_campaigns.send_onboarding_sequence(user)


async def track_marketing_conversion(user_id, campaign_id: str,
                                   conversion_type: str, value: float = 0.0) -> bool:
    """Track marketing campaign conversion."""
    return await marketing_campaigns.track_campaign_conversion(user_id, campaign_id, conversion_type, value)


# Placeholder functions for missing notification services
# TODO: Implement these functions in the appropriate services

async def send_addon_notification(user_id: str, title: str, message: str, notification_type: str = "info", **kwargs):
    """
    Placeholder for addon notification function.
    TODO: Implement this in app.services.notification
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"PLACEHOLDER: Would send addon notification to user {user_id}: {title} - {message} (type: {notification_type})")
    return True

async def send_addon_marketing_email(user_id: str, subject: str, content: str, cta_text: Optional[str] = None, cta_url: Optional[str] = None, template_id: Optional[str] = None, **kwargs):
    """
    Placeholder for addon marketing email function.
    TODO: Implement this in app.services.email_service
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"PLACEHOLDER: Would send addon marketing email to user {user_id}: {subject} - {content}")
    return True

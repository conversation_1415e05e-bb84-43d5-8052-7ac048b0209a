// @since 2024-1-1 to 2025-25-7
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [
    react({
      jsxImportSource: '@emotion/react'
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(process.cwd(), './src')
    },
    extensions: ['.js', '.jsx']
  },

  define: {
    "process.env.NODE_ENV": JSON.stringify("production")
  },

  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: false,
    target: "es2020",
    cssCodeSplit: false,
    assetsInlineLimit: 0, // Don't inline any assets
    rollupOptions: {
      output: {
        manualChunks: undefined,
        inlineDynamicImports: true // Bundle everything into one file
      },
      external: [
        '@mui/icons-material',
        '@mui/x-data-grid',
        '@mui/x-date-pickers',
        'recharts',
        'chart.js',
        'react-chartjs-2',
        'd3',
        '@nivo/bar',
        '@nivo/line',
        '@nivo/pie'
      ],
      treeshake: false
    }
  },

  optimizeDeps: {
    include: [
      "react",
      "react-dom"
    ],
    exclude: [
      '@mui/icons-material',
      '@mui/x-data-grid',
      'recharts',
      'chart.js'
    ],
    force: true
  },

  clearScreen: false,
  logLevel: "error"
});

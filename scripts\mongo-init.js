// @since 2024-1-1 to 2025-25-7
// MongoDB Initialization Script for ACEO
// This script sets up the initial database structure and indexes

// Switch to the application database
db = db.getSiblingDB('aceo_dev');

// Create application user
db.createUser({
  user: 'app_user',
  pwd: 'app_password',
  roles: [
    {
      role: 'readWrite',
      db: 'aceo_dev'
    }
  ]
});

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'full_name', 'hashed_password'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        },
        full_name: {
          bsonType: 'string',
          minLength: 1
        },
        hashed_password: {
          bsonType: 'string',
          minLength: 1
        },
        is_active: {
          bsonType: 'bool'
        },
        role: {
          bsonType: 'string',
          enum: ['user', 'admin', 'moderator']
        }
      }
    }
  }
});

db.createCollection('appsumo_codes');
db.createCollection('appsumo_redemptions');
db.createCollection('content_generations');
db.createCollection('social_posts');
db.createCollection('analytics_data');

// Create indexes for performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ created_at: 1 });
db.users.createIndex({ 'subscription.plan_id': 1 });
db.users.createIndex({ 'subscription.status': 1 });

db.appsumo_codes.createIndex({ code: 1 }, { unique: true });
db.appsumo_codes.createIndex({ is_redeemed: 1 });
db.appsumo_codes.createIndex({ expires_at: 1 });

db.appsumo_redemptions.createIndex({ user_id: 1 });
db.appsumo_redemptions.createIndex({ code: 1 });
db.appsumo_redemptions.createIndex({ redeemed_at: 1 });

db.content_generations.createIndex({ user_id: 1 });
db.content_generations.createIndex({ created_at: 1 });
db.content_generations.createIndex({ content_type: 1 });

db.social_posts.createIndex({ user_id: 1 });
db.social_posts.createIndex({ platform: 1 });
db.social_posts.createIndex({ scheduled_time: 1 });
db.social_posts.createIndex({ status: 1 });

db.analytics_data.createIndex({ user_id: 1 });
db.analytics_data.createIndex({ date: 1 });
db.analytics_data.createIndex({ metric_type: 1 });

// Insert sample data for development
db.users.insertOne({
  email: '<EMAIL>',
  full_name: 'Admin User',
  hashed_password: '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', // secret
  is_active: true,
  role: 'admin',
  subscription: {
    plan_id: 'dominator',
    status: 'active',
    start_date: new Date(),
    current_period_end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
    features: ['enterprise_analytics', 'content_creation', 'social_posting', 'team_collaboration', 'white_label']
  },
  created_at: new Date(),
  updated_at: new Date()
});

print('MongoDB initialization completed successfully!');
print('Created database: aceo_dev');
print('Created user: app_user');
print('Created collections with indexes');
print('Inserted sample admin user: <EMAIL> (password: secret)');

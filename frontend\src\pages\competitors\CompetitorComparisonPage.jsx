// @since 2024-1-1 to 2025-25-7
import { memo, Suspense, useCallback, useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import {
  Container,
  Box,
  CircularProgress,
  Alert,
  Button,
  Typography,
  Breadcrumbs,
  Link
} from "@mui/material";
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Compare as CompareIcon,
  Refresh as RefreshIcon
} from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import CompetitorComparison from "../../components/competitors/CompetitorComparison";
import { CompetitorProvider } from "../../contexts/CompetitorContext";
import { useNotification } from "../../hooks/useNotification";
import ErrorBoundary from "../../components/common/ErrorBoundary";

// Loading component with accessibility
const LoadingFallback = memo(() => (
  <Box
    sx={{
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center",
      my: 4,
      gap: 2
    }}
    role="status"
    aria-live="polite"
    aria-label="Loading competitor comparison"
  >
    <CircularProgress aria-label="Loading data" />
    <Typography variant="body2" color="text.secondary">
      Loading competitor comparison...
    </Typography>
  </Box>
));

LoadingFallback.displayName = 'LoadingFallback';

// Error fallback component
const ErrorFallback = memo(({ error, resetError }) => (
  <Alert
    severity="error"
    sx={{ my: 3 }}
    action={
      <Button
        color="inherit"
        size="small"
        onClick={resetError}
        startIcon={<RefreshIcon />}
        aria-label="Retry loading competitor comparison"
      >
        Retry
      </Button>
    }
  >
    <Typography variant="body2">
      Failed to load competitor comparison: {error?.message || 'Unknown error'}
    </Typography>
  </Alert>
));

ErrorFallback.displayName = 'ErrorFallback';

const CompetitorComparisonPage = ({ isEmbedded = false }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showErrorNotification } = useNotification();
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  // Error boundary handler
  const handleError = useCallback((error, errorInfo) => {
    console.error('CompetitorComparisonPage Error:', error, errorInfo);
    setError(error);
    showErrorNotification('Failed to load competitor comparison');
  }, [showErrorNotification]);

  // Reset error state
  const resetError = useCallback(() => {
    setError(null);
    setRetryCount(prev => prev + 1);
  }, []);

  // Navigation handlers
  const handleNavigateHome = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  const handleNavigateCompetitors = useCallback(() => {
    navigate('/settings?tab=competitors');
  }, [navigate]);

  // SEO and meta tags
  const pageTitle = isEmbedded
    ? "Competitor Comparison"
    : "Competitor Comparison | ACE Social";

  const pageDescription = "Compare competitor performance metrics, analyze engagement rates, and gain strategic insights to improve your social media strategy.";

  // Analytics tracking
  useEffect(() => {
    if (!isEmbedded) {
      // Track page view
      if (typeof gtag !== 'undefined') {
        gtag('config', 'GA_MEASUREMENT_ID', {
          page_title: pageTitle,
          page_location: window.location.href
        });
      }
    }
  }, [isEmbedded, pageTitle]);

  return (
    <>
      {!isEmbedded && (
        <Helmet>
          <title>{pageTitle}</title>
          <meta name="description" content={pageDescription} />
          <meta name="keywords" content="competitor analysis, social media comparison, engagement metrics, competitive intelligence" />
          <meta property="og:title" content={pageTitle} />
          <meta property="og:description" content={pageDescription} />
          <meta property="og:type" content="website" />
          <link rel="canonical" href={`${window.location.origin}${location.pathname}`} />
        </Helmet>
      )}

      <Container
        maxWidth={isEmbedded ? false : "xl"}
        disableGutters={isEmbedded}
        component="main"
        role="main"
        aria-label="Competitor comparison dashboard"
      >
        {/* Breadcrumb Navigation */}
        {!isEmbedded && (
          <Box sx={{ mb: 3 }}>
            <Breadcrumbs
              aria-label="breadcrumb navigation"
              sx={{ mb: 2 }}
            >
              <Link
                component="button"
                variant="body2"
                onClick={handleNavigateHome}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  textDecoration: 'none',
                  '&:hover': { textDecoration: 'underline' }
                }}
                aria-label="Navigate to dashboard"
              >
                <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
                Dashboard
              </Link>
              <Link
                component="button"
                variant="body2"
                onClick={handleNavigateCompetitors}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  textDecoration: 'none',
                  '&:hover': { textDecoration: 'underline' }
                }}
                aria-label="Navigate to competitors"
              >
                <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
                Competitors
              </Link>
              <Typography
                color="text.primary"
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                <CompareIcon sx={{ mr: 0.5 }} fontSize="inherit" />
                Comparison
              </Typography>
            </Breadcrumbs>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <ErrorFallback error={error} resetError={resetError} />
        )}

        {/* Main Content */}
        <ErrorBoundary
          onError={handleError}
          fallback={<ErrorFallback error={error} resetError={resetError} />}
        >
          <CompetitorProvider>
            <Suspense fallback={<LoadingFallback />}>
              <CompetitorComparison
                isEmbedded={isEmbedded}
                key={retryCount} // Force re-render on retry
              />
            </Suspense>
          </CompetitorProvider>
        </ErrorBoundary>
      </Container>
    </>
  );
};

// Memoize the component for performance
const MemoizedCompetitorComparisonPage = memo(CompetitorComparisonPage);
MemoizedCompetitorComparisonPage.displayName = 'CompetitorComparisonPage';

export default MemoizedCompetitorComparisonPage;

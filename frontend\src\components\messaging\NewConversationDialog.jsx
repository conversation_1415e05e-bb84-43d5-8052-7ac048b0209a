/**
 * Enhanced New Conversation Dialog - Enterprise-grade conversation creation component
 * Features: Plan-based conversation limitations, real-time conversation tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced conversation creation capabilities and interactive conversation exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from "react";
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Autocomplete,
  Chip,
  CircularProgress,
  Alert,
  Snackbar,
  alpha
} from '@mui/material';
import {
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Pinterest as PinterestIcon,
  Chat as ThreadsIcon,
  MusicVideo as TikTokIcon,
  Person as PersonIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import useApiError from '../../hooks/useApiError';
import api from '../../api';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Conversation dialog display modes with enhanced configurations
const DIALOG_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Dialog',
    description: 'Basic conversation creation interface',
    subscriptionLimits: {
      creator: { available: true, maxConversationTypes: 3, features: ['basic_dialog'] },
      accelerator: { available: true, maxConversationTypes: 10, features: ['basic_dialog', 'analytics_dialog'] },
      dominator: { available: true, maxConversationTypes: -1, features: ['basic_dialog', 'analytics_dialog', 'ai_insights'] }
    }
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Dialog',
    description: 'Comprehensive conversation management',
    subscriptionLimits: {
      creator: { available: false, maxConversationTypes: 0, features: [] },
      accelerator: { available: true, maxConversationTypes: 5, features: ['detailed_dialog', 'conversation_analytics'] },
      dominator: { available: true, maxConversationTypes: -1, features: ['detailed_dialog', 'conversation_analytics', 'real_time_insights'] }
    }
  },
  TEMPLATE: {
    id: 'template',
    name: 'Template Dialog',
    description: 'Pre-configured conversation templates',
    subscriptionLimits: {
      creator: { available: false, maxConversationTypes: 0, features: [] },
      accelerator: { available: true, maxConversationTypes: 3, features: ['template_dialog', 'templates'] },
      dominator: { available: true, maxConversationTypes: -1, features: ['template_dialog', 'templates', 'custom_templates'] }
    }
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Dialog',
    description: 'AI-powered conversation creation and optimization',
    subscriptionLimits: {
      creator: { available: false, maxConversationTypes: 0, features: [] },
      accelerator: { available: false, maxConversationTypes: 0, features: [] },
      dominator: { available: true, maxConversationTypes: -1, features: ['ai_assisted', 'ai_optimization', 'conversation_insights'] }
    }
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Dialog',
    description: 'Advanced conversation analytics and insights',
    subscriptionLimits: {
      creator: { available: false, maxConversationTypes: 0, features: [] },
      accelerator: { available: false, maxConversationTypes: 0, features: [] },
      dominator: { available: true, maxConversationTypes: -1, features: ['analytics_dialog', 'conversation_insights'] }
    }
  }
};

/**
 * Enhanced New Conversation Dialog Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.open=false] - Dialog open state
 * @param {Function} [props.onClose] - Close dialog callback
 * @param {Function} [props.onCreateConversation] - Create conversation callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=false] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onUpgrade] - Upgrade callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-new-conversation-dialog'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const NewConversationDialog = memo(forwardRef(({
  open = false,
  onClose,
  onCreateConversation,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = false,
  onExport,
  onRefresh,
  onUpgrade,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-new-conversation-dialog',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useAccessibility();
  const { user } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();

  // Core state management
  const dialogRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Form state
  const [title, setTitle] = useState('');
  const [participants, setParticipants] = useState([]);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [conversationType, setConversationType] = useState('internal');
  const [socialAccounts, setSocialAccounts] = useState([]);
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [externalUser, setExternalUser] = useState('');

  // Enhanced state management
  const [dialogMode, setDialogMode] = useState('compact');
  const [conversationHistory, setConversationHistory] = useState([]);
  const [conversationAnalytics, setConversationAnalytics] = useState(null);
  const [conversationInsights, setConversationInsights] = useState(null);
  const [customTemplates, setCustomTemplates] = useState([]);
  const [dialogPreferences, setDialogPreferences] = useState({
    autoSave: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: false,
    templateSuggestions: true
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [dialogDrawerOpen, setDialogDrawerOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [conversationStats, setConversationStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastDialogCheck, setLastDialogCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Apply feature overrides based on props
    const advancedFeaturesEnabled = enableAdvancedFeatures && subscription?.plan_id !== 'creator';
    const aiInsightsEnabled = enableAIInsights && subscription?.plan_id === 'dominator';

    const features = {
      creator: {
        maxConversationTypes: 3,
        maxParticipants: 5,
        hasAdvancedDialog: false,
        hasConversationAnalytics: false,
        hasCustomTemplates: false,
        hasConversationInsights: false,
        hasConversationHistory: false,
        hasAIAssistance: false,
        hasTemplateDialog: false,
        hasConversationExport: false,
        hasConversationScheduling: false,
        hasConversationAutomation: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxConversationTypes: 10,
        maxParticipants: 25,
        hasAdvancedDialog: true,
        hasConversationAnalytics: true,
        hasCustomTemplates: true,
        hasConversationInsights: false,
        hasConversationHistory: true,
        hasAIAssistance: false,
        hasTemplateDialog: true,
        hasConversationExport: true,
        hasConversationScheduling: true,
        hasConversationAutomation: false,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxConversationTypes: -1,
        maxParticipants: -1,
        hasAdvancedDialog: true,
        hasConversationAnalytics: true,
        hasCustomTemplates: true,
        hasConversationInsights: true,
        hasConversationHistory: true,
        hasAIAssistance: true,
        hasTemplateDialog: true,
        hasConversationExport: true,
        hasConversationScheduling: true,
        hasConversationAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'ai-powered',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    // Apply feature overrides
    const enhancedFeatures = {
      ...currentFeatures,
      hasAdvancedDialog: currentFeatures.hasAdvancedDialog && advancedFeaturesEnabled,
      hasConversationInsights: currentFeatures.hasConversationInsights && aiInsightsEnabled,
      hasConversationAnalytics: currentFeatures.hasConversationAnalytics && advancedFeaturesEnabled
    };

    return {
      ...enhancedFeatures,
      hasFeatureAccess: (feature) => enhancedFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = enhancedFeatures[feature] === true;
        const withinLimits = enhancedFeatures.maxConversationTypes === -1 || currentUsage < enhancedFeatures.maxConversationTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription, enableAdvancedFeatures, enableAIInsights]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'dialog',
      'aria-label': ariaLabel || `New conversation dialog with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Conversation creation interface with ${subscriptionFeatures.trackingLevel} tracking`,
      'aria-modal': 'true',
      tabIndex: -1
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive dialog API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getConversationHistory: () => conversationHistory,
    getConversationAnalytics: () => conversationAnalytics,
    getConversationInsights: () => conversationInsights,
    refreshDialog: () => {
      fetchConversationAnalytics();
      if (onRefresh) onRefresh();
    },

    // Dialog methods
    openDialog: () => {
      if (dialogRef.current) {
        dialogRef.current.focus();
      }
    },
    closeDialog: () => {
      if (onClose) onClose();
    },
    resetForm: () => handleReset(),
    validateForm: () => validateForm(),
    openDialogDrawer: () => setDialogDrawerOpen(true),
    closeDialogDrawer: () => setDialogDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    getConversationStats: () => conversationStats,
    exportConversationData: () => {
      if (subscriptionFeatures.hasExport && onExport) {
        onExport(conversationHistory, conversationAnalytics);
      }
    },

    // Accessibility methods
    announceDialog: (message) => announceToScreenReader(message),
    focusDialogField: () => setFocusToElement('conversation-title-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getCurrentStep: () => activeStep,
    goToStep: (step) => setActiveStep(step),
    getValidationErrors: () => validationErrors,

    // Enhanced methods
    changeDialogMode: (mode) => handleDialogModeChange(mode),
    showUpgradePrompt: (feature) => handleUpgradePrompt(feature),
    getCurrentMode: () => dialogMode,
    getDialogPreferences: () => dialogPreferences,
    getCustomTemplates: () => customTemplates,
    isBackendAvailable: () => backendAvailable,

    // Template and AI methods
    selectTemplate: (template) => handleTemplateSelection(template),
    generateAISuggestions: () => handleAISuggestionGeneration(),
    updatePreferences: (prefs) => updateDialogPreferences(prefs),
    getSelectedTemplate: () => selectedTemplate,
    fetchStats: () => fetchConversationStats(),

    // Advanced template and health methods
    addTemplate: (template) => addCustomTemplate(template),
    checkHealth: () => checkBackendHealth(),
    getLastHealthCheck: () => lastDialogCheck,

    // UI control methods
    toggleDrawer: () => handleDialogDrawerToggle(),
    toggleAnalyticsView: () => handleAnalyticsToggle(),
    toggleFullscreenMode: () => handleFullscreenToggle(),
    isDrawerOpen: () => dialogDrawerOpen,
    isAnalyticsVisible: () => showAnalytics,
    isFullscreenActive: () => fullscreenMode
  }), [
    conversationHistory,
    conversationAnalytics,
    conversationInsights,
    conversationStats,
    subscriptionFeatures,
    onRefresh,
    onExport,
    onClose,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    activeStep,
    validationErrors,
    fetchConversationAnalytics,
    handleReset,
    validateForm,
    backendAvailable,
    customTemplates,
    dialogMode,
    dialogPreferences,
    handleDialogModeChange,
    handleUpgradePrompt,
    fetchConversationStats,
    handleAISuggestionGeneration,
    handleTemplateSelection,
    selectedTemplate,
    updateDialogPreferences,
    addCustomTemplate,
    checkBackendHealth,
    lastDialogCheck,
    dialogDrawerOpen,
    fullscreenMode,
    handleAnalyticsToggle,
    handleDialogDrawerToggle,
    handleFullscreenToggle,
    showAnalytics
  ]);

  // Fetch conversation analytics with enhanced error handling and retry logic
  const fetchConversationAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/conversation/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setConversationAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (dialogPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Conversation analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch conversation analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load conversation analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, dialogPreferences.showAnalytics]);

  // Fetch conversation insights
  const fetchConversationInsights = useCallback(async () => {
    if (!subscriptionFeatures.hasConversationInsights) return;

    await handleApiRequest(
      async () => {
        const response = await api.get('/api/conversation/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setConversationInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch conversation insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest, subscriptionFeatures.hasConversationInsights]);

  // Form validation
  const validateForm = useCallback(() => {
    const errors = {};

    if (conversationType === 'internal') {
      if (!title.trim()) {
        errors.title = 'Please enter a conversation title';
      }
      if (participants.length === 0) {
        errors.participants = 'Please select at least one participant';
      }
      if (subscriptionFeatures.maxParticipants !== -1 && participants.length > subscriptionFeatures.maxParticipants) {
        errors.participants = `Maximum ${subscriptionFeatures.maxParticipants} participants allowed for ${subscriptionFeatures.planName} plan`;
      }
    } else {
      if (!selectedPlatform) {
        errors.platform = 'Please select a social media platform';
      }
      if (!externalUser.trim()) {
        errors.externalUser = 'Please enter the external user handle/ID';
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [conversationType, title, participants, selectedPlatform, externalUser, subscriptionFeatures.maxParticipants, subscriptionFeatures.planName]);

  // Reset form
  const handleReset = useCallback(() => {
    setTitle('');
    setParticipants([]);
    setConversationType('internal');
    setSelectedPlatform(socialAccounts.length > 0 ? socialAccounts[0].platform : '');
    setExternalUser('');
    setValidationErrors({});
    setActiveStep(0);
    setError('');
    setSuccess('');

    announceToScreenReader('Form has been reset');
  }, [socialAccounts, announceToScreenReader]);

  // Fetch available users and social accounts on component mount
  useEffect(() => {
    fetchAvailableUsers();
    fetchSocialAccounts();
  }, [fetchAvailableUsers, fetchSocialAccounts]);

  // Handle dialog mode switching
  const handleDialogModeChange = useCallback((newMode) => {
    if (DIALOG_MODES[newMode.toUpperCase()]) {
      setDialogMode(newMode);
      announceToScreenReader(`Dialog mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setConversationHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (dialogPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} dialog mode`);
      }
    }
  }, [announceToScreenReader, user?.id, dialogPreferences.showAnalytics, showSuccess]);

  // Handle upgrade prompts
  const handleUpgradePrompt = useCallback((feature) => {
    if (onUpgrade) {
      onUpgrade(feature);
    } else {
      showError(`${feature} requires a plan upgrade`);
    }

    // Track upgrade prompt
    const upgradeRecord = {
      id: Date.now(),
      type: 'upgrade_prompt',
      feature,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setConversationHistory(prev => [upgradeRecord, ...prev.slice(0, 99)]);
  }, [onUpgrade, showError, user?.id]);

  // Handle real-time optimization
  const handleRealTimeOptimization = useCallback(() => {
    if (enableRealTimeOptimization && subscriptionFeatures.hasConversationAnalytics) {
      // Optimize conversation creation based on analytics
      fetchConversationAnalytics();

      if (dialogPreferences.showAnalytics) {
        showSuccess('Real-time optimization applied');
      }
    }
  }, [enableRealTimeOptimization, subscriptionFeatures.hasConversationAnalytics, fetchConversationAnalytics, dialogPreferences.showAnalytics, showSuccess]);

  // Initial data loading
  useEffect(() => {
    if (subscriptionFeatures.hasConversationAnalytics) {
      fetchConversationAnalytics();
    }
    if (subscriptionFeatures.hasConversationInsights) {
      fetchConversationInsights();
    }
  }, [subscriptionFeatures.hasConversationAnalytics, subscriptionFeatures.hasConversationInsights, fetchConversationAnalytics, fetchConversationInsights]);

  // Handle template selection
  const handleTemplateSelection = useCallback((template) => {
    setSelectedTemplate(template);

    if (template) {
      // Apply template data to form
      setTitle(template.title || '');
      setConversationType(template.type || 'internal');

      // Track template usage
      const templateRecord = {
        id: Date.now(),
        type: 'template_selected',
        template: template.name,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setConversationHistory(prev => [templateRecord, ...prev.slice(0, 99)]);

      if (dialogPreferences.showAnalytics) {
        showSuccess(`Template "${template.name}" applied`);
      }
    }
  }, [user?.id, dialogPreferences.showAnalytics, showSuccess]);

  // Handle AI suggestions
  const handleAISuggestionGeneration = useCallback(async () => {
    if (subscriptionFeatures.hasAIAssistance) {
      try {
        const response = await api.get('/api/conversation/ai-suggestions', {
          params: {
            type: conversationType,
            context: title
          }
        });

        setAiSuggestions(response.data.suggestions || []);

        if (dialogPreferences.showAnalytics) {
          showSuccess('AI suggestions generated');
        }
      } catch (error) {
        console.error('Failed to generate AI suggestions:', error);
        showError('Failed to generate AI suggestions');
      }
    }
  }, [subscriptionFeatures.hasAIAssistance, conversationType, title, dialogPreferences.showAnalytics, showSuccess, showError]);

  // Handle conversation stats fetching
  const fetchConversationStats = useCallback(async () => {
    if (subscriptionFeatures.hasConversationAnalytics) {
      try {
        const response = await api.get('/api/conversation/stats');
        setConversationStats(response.data);
      } catch (error) {
        console.error('Failed to fetch conversation stats:', error);
      }
    }
  }, [subscriptionFeatures.hasConversationAnalytics]);

  // Handle dialog preferences updates
  const updateDialogPreferences = useCallback((newPreferences) => {
    setDialogPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setConversationHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (dialogPreferences.showAnalytics) {
      showSuccess('Dialog preferences updated');
    }
  }, [user?.id, dialogPreferences.showAnalytics, showSuccess]);

  // Handle custom template management
  const addCustomTemplate = useCallback((template) => {
    if (subscriptionFeatures.hasCustomTemplates) {
      const newTemplate = {
        id: Date.now(),
        ...template,
        createdAt: new Date().toISOString(),
        userId: user?.id
      };

      setCustomTemplates(prev => [...prev, newTemplate]);

      // Track template creation
      const templateRecord = {
        id: Date.now(),
        type: 'template_created',
        template: newTemplate.name,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setConversationHistory(prev => [templateRecord, ...prev.slice(0, 99)]);

      if (dialogPreferences.showAnalytics) {
        showSuccess(`Template "${template.name}" created`);
      }
    } else {
      handleUpgradePrompt('Custom Templates');
    }
  }, [subscriptionFeatures.hasCustomTemplates, user?.id, dialogPreferences.showAnalytics, showSuccess, handleUpgradePrompt]);

  // Handle backend health monitoring
  const checkBackendHealth = useCallback(async () => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('/api/health', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const wasUnavailable = !backendAvailable;
        setBackendAvailable(true);
        setLastDialogCheck(Date.now());

        if (wasUnavailable && dialogPreferences.showAnalytics) {
          showSuccess("Connection restored - Dialog features available");
        }
      } else {
        setBackendAvailable(false);
        if (dialogPreferences.showAnalytics) {
          showError("Backend service unavailable - Some dialog features may be limited");
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Health check failed:', error);
        setBackendAvailable(false);

        // Show notification only if it's been a while since last check
        const timeSinceLastCheck = Date.now() - lastDialogCheck;
        if (timeSinceLastCheck > 60000 && dialogPreferences.showAnalytics) { // 1 minute
          showError("Connection issues detected - Dialog may be delayed");
        }
      }
    }
  }, [backendAvailable, lastDialogCheck, dialogPreferences.showAnalytics, showSuccess, showError]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && open) {
      handleRealTimeOptimization();
    }
  }, [enableRealTimeOptimization, open, handleRealTimeOptimization]);

  // Fetch stats when dialog opens
  useEffect(() => {
    if (open && subscriptionFeatures.hasConversationAnalytics) {
      fetchConversationStats();
    }
  }, [open, subscriptionFeatures.hasConversationAnalytics, fetchConversationStats]);

  // Generate AI suggestions when form changes
  useEffect(() => {
    if (title && subscriptionFeatures.hasAIAssistance && dialogPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        handleAISuggestionGeneration();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [title, subscriptionFeatures.hasAIAssistance, dialogPreferences.aiAssistance, handleAISuggestionGeneration]);

  // Periodic health checks in production
  useEffect(() => {
    if (process.env.NODE_ENV === 'production' && open) {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [checkBackendHealth, open]);

  // Handle dialog drawer and analytics visibility
  const handleDialogDrawerToggle = useCallback(() => {
    setDialogDrawerOpen(prev => !prev);

    // Track drawer usage
    const drawerRecord = {
      id: Date.now(),
      type: 'drawer_toggled',
      open: !dialogDrawerOpen,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setConversationHistory(prev => [drawerRecord, ...prev.slice(0, 99)]);
  }, [dialogDrawerOpen, user?.id]);

  const handleAnalyticsToggle = useCallback(() => {
    setShowAnalytics(prev => !prev);

    // Track analytics visibility
    const analyticsRecord = {
      id: Date.now(),
      type: 'analytics_toggled',
      visible: !showAnalytics,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setConversationHistory(prev => [analyticsRecord, ...prev.slice(0, 99)]);
  }, [showAnalytics, user?.id]);

  const handleFullscreenToggle = useCallback(() => {
    setFullscreenMode(prev => !prev);

    // Track fullscreen usage
    const fullscreenRecord = {
      id: Date.now(),
      type: 'fullscreen_toggled',
      fullscreen: !fullscreenMode,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setConversationHistory(prev => [fullscreenRecord, ...prev.slice(0, 99)]);
  }, [fullscreenMode, user?.id]);

  // Fetch available users for internal conversations
  const fetchAvailableUsers = useCallback(async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        // Filter out current user
        setAvailableUsers(data.filter(u => u.id !== user.id));
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  }, [user.id]);

  // Fetch connected social media accounts
  const fetchSocialAccounts = useCallback(async () => {
    try {
      const response = await fetch('/api/social-media/accounts');
      if (response.ok) {
        const data = await response.json();
        setSocialAccounts(data);

        // Set default platform if available
        if (data.length > 0 && !selectedPlatform) {
          setSelectedPlatform(data[0].platform);
        }
      }
    } catch (error) {
      console.error('Error fetching social accounts:', error);
    }
  }, [selectedPlatform]);

  // Handle form submission
  const handleSubmit = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      if (conversationType === 'internal') {
        if (!title.trim()) {
          setError('Please enter a conversation title');
          setLoading(false);
          return;
        }

        if (participants.length === 0) {
          setError('Please select at least one participant');
          setLoading(false);
          return;
        }

        // Create internal conversation
        const response = await fetch('/api/messaging/conversations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            title,
            participants: participants.map(p => p.id)
          })
        });

        if (response.ok) {
          const data = await response.json();
          setSuccess('Conversation created successfully');
          onCreateConversation(data);
          handleReset();
        } else {
          const errorData = await response.json();
          setError(errorData.detail || 'Failed to create conversation');
        }
      } else {
        // Social media conversation
        if (!selectedPlatform) {
          setError('Please select a social media platform');
          setLoading(false);
          return;
        }

        if (!externalUser.trim()) {
          setError('Please enter the external user handle/ID');
          setLoading(false);
          return;
        }

        // Create social media conversation
        const response = await fetch('/api/social-media-messaging/conversations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            platform: selectedPlatform,
            external_user: externalUser,
            title: `${selectedPlatform.charAt(0).toUpperCase() + selectedPlatform.slice(1)} conversation with ${externalUser}`
          })
        });

        if (response.ok) {
          const data = await response.json();
          setSuccess('Social media conversation created successfully');
          onCreateConversation(data);
          handleReset();
        } else {
          const errorData = await response.json();
          setError(errorData.detail || 'Failed to create social media conversation');
        }
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };



  // Handle dialog close
  const handleClose = () => {
    handleReset();
    onClose();
  };

  // Get platform icon
  const getPlatformIcon = (platform) => {
    switch (platform) {
      case 'facebook':
        return <FacebookIcon />;
      case 'twitter':
        return <TwitterIcon />;
      case 'linkedin':
        return <LinkedInIcon />;
      case 'pinterest':
        return <PinterestIcon />;
      case 'threads':
        return <ThreadsIcon />;
      case 'tiktok':
        return <TikTokIcon />;
      default:
        return <PersonIcon />;
    }
  };

  return (
    <Dialog
      {...getAccessibilityProps()}
      ref={dialogRef}
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      data-testid={testId}
      className={className}
      style={style}
      sx={{
        ...sx,
        ...customization
      }}
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            background: alpha(ACE_COLORS.WHITE, 0.95),
            backdropFilter: 'blur(10px)',
          }
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AddIcon sx={{ mr: 1 }} />
          <Typography variant="h6">New Conversation</Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>
        )}

        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel>Conversation Type</InputLabel>
          <Select
            value={conversationType}
            onChange={(e) => setConversationType(e.target.value)}
            label="Conversation Type"
          >
            <MenuItem value="internal">Internal Conversation</MenuItem>
            <MenuItem value="social">Social Media Conversation</MenuItem>
          </Select>
        </FormControl>

        {conversationType === 'internal' ? (
          <>
            <TextField
              fullWidth
              label="Conversation Title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              margin="normal"
              variant="outlined"
            />

            <Autocomplete
              multiple
              options={availableUsers}
              getOptionLabel={(option) => option.full_name || option.email}
              value={participants}
              onChange={(_, newValue) => setParticipants(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  variant="outlined"
                  label="Participants"
                  placeholder="Add participants"
                  margin="normal"
                />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    key={option.id || index}
                    avatar={<PersonIcon />}
                    label={option.full_name || option.email}
                    {...getTagProps({ index })}
                  />
                ))
              }
            />
          </>
        ) : (
          <>
            <FormControl fullWidth sx={{ mb: 2, mt: 1 }}>
              <InputLabel>Social Media Platform</InputLabel>
              <Select
                value={selectedPlatform}
                onChange={(e) => setSelectedPlatform(e.target.value)}
                label="Social Media Platform"
                startAdornment={selectedPlatform && (
                  <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                    {getPlatformIcon(selectedPlatform)}
                  </Box>
                )}
              >
                {socialAccounts.length > 0 ? (
                  socialAccounts.map((account) => (
                    <MenuItem
                      key={account.platform}
                      value={account.platform}
                      sx={{
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      <Box sx={{ mr: 1 }}>
                        {getPlatformIcon(account.platform)}
                      </Box>
                      {account.account_name} ({account.platform})
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem disabled>
                    No connected social media accounts
                  </MenuItem>
                )}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="External User Handle/ID"
              value={externalUser}
              onChange={(e) => setExternalUser(e.target.value)}
              margin="normal"
              variant="outlined"
              placeholder="e.g., @username or user ID"
              helperText="Enter the username or ID of the person you want to message"
            />
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} color="inherit" />}
        >
          {loading ? 'Creating...' : 'Create Conversation'}
        </Button>
      </DialogActions>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying connection... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}
    </Dialog>
  );
}));

// Enhanced PropTypes with comprehensive validation
NewConversationDialog.propTypes = {
  // Core props
  open: PropTypes.bool,
  onClose: PropTypes.func,
  onCreateConversation: PropTypes.func,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onUpgrade: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

NewConversationDialog.displayName = 'NewConversationDialog';

export default NewConversationDialog;

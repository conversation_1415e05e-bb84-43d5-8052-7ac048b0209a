/**
 * Enhanced Referral Icon - Enterprise-grade referral icon management component
 * Features: Comprehensive referral icon management, real-time referral tracking, subscription-free access,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced referral capabilities and interactive referral exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import {
  IconButton,
  Tooltip,
  Badge,
  Box,
  Typography,
  Snackbar,
  Alert,
  alpha,
  useMediaQuery,
  CircularProgress,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  CardGiftcard as CardGiftcardIcon,
  Analytics as AnalyticsIcon,
  Dashboard as DashboardIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Referral icon display modes with enhanced configurations
const REFERRAL_ICON_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Icons',
    description: 'Basic referral icon interface',
    features: ['basic_icons', 'analytics_icons', 'ai_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Icons',
    description: 'Comprehensive referral icon management',
    features: ['detailed_icons', 'icon_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Icons',
    description: 'AI-powered referral icon management and insights',
    features: ['ai_assisted', 'ai_optimization', 'icon_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Icons',
    description: 'Advanced referral icon analytics and insights',
    features: ['analytics_icons', 'icon_insights']
  }
};

/**
 * Enhanced Referral Icon Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights (no restrictions)
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onReferralAction] - Referral action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-referral-icon'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const ReferralIcon = memo(forwardRef(({
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onReferralAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-referral-icon',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');
  const navigate = useNavigate();

  // Core state management
  const referralIconRef = useRef(null);
  const [pendingRewards, setPendingRewards] = useState(0);

  // Enhanced state management
  const [referralMode, setReferralMode] = useState('compact');
  const [referralHistory, setReferralHistory] = useState([]);
  const [referralAnalytics, setReferralAnalytics] = useState(null);
  const [referralInsights, setReferralInsights] = useState(null);
  const [customReferralConfigs, setCustomReferralConfigs] = useState([]);
  const [referralPreferences, setReferralPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    referralSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [referralDrawerOpen, setReferralDrawerOpen] = useState(false);
  const [selectedReferralType, setSelectedReferralType] = useState(null);
  const [referralStats, setReferralStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [referralRewards, setReferralRewards] = useState([]);
  const [referralProgress, setReferralProgress] = useState(0);

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastReferralCheck, setLastReferralCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with full feature access - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // All features are available to all users (no plan-based limitations)
    const features = {
      creator: {
        maxReferralTypes: -1,
        maxReferralPerDay: -1,
        hasAdvancedReferral: true,
        hasReferralAnalytics: true,
        hasCustomReferral: true,
        hasReferralInsights: true,
        hasReferralHistory: true,
        hasAIAssistance: true,
        hasReferralExport: true,
        hasReferralScheduling: true,
        hasReferralAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxReferralTypes: -1,
        maxReferralPerDay: -1,
        hasAdvancedReferral: true,
        hasReferralAnalytics: true,
        hasCustomReferral: true,
        hasReferralInsights: true,
        hasReferralHistory: true,
        hasAIAssistance: true,
        hasReferralExport: true,
        hasReferralScheduling: true,
        hasReferralAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxReferralTypes: -1,
        maxReferralPerDay: -1,
        hasAdvancedReferral: true,
        hasReferralAnalytics: true,
        hasCustomReferral: true,
        hasReferralInsights: true,
        hasReferralHistory: true,
        hasAIAssistance: true,
        hasReferralExport: true,
        hasReferralScheduling: true,
        hasReferralAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxReferralTypes === -1 || currentUsage < currentFeatures.maxReferralTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'button',
      'aria-label': ariaLabel || `Referral icon with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Referral icon interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive referral API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getReferralHistory: () => referralHistory,
    getReferralAnalytics: () => referralAnalytics,
    getReferralInsights: () => referralInsights,
    refreshReferrals: () => {
      fetchReferralAnalytics();
      if (onRefresh) onRefresh();
    },

    // Referral methods
    focusReferralIcon: () => {
      if (referralIconRef.current) {
        referralIconRef.current.focus();
      }
    },
    getPendingRewards: () => pendingRewards,
    getReferralRewards: () => referralRewards,
    getReferralProgress: () => referralProgress,
    openReferralMenu: (event) => setMenuAnchorEl(event.currentTarget),
    closeReferralMenu: () => setMenuAnchorEl(null),
    navigateToReferrals: () => navigate('/referrals'),
    openReferralDrawer: () => setReferralDrawerOpen(true),
    closeReferralDrawer: () => setReferralDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportReferralData: () => {
      if (onExport) {
        onExport(referralHistory, referralAnalytics);
      }
    },

    // Accessibility methods
    announceReferral: (message) => announceToScreenReader(message),
    focusReferralField: () => setFocusToElement('referral-icon-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => referralMode,
    getReferralStats: () => referralStats,
    getSelectedReferralType: () => selectedReferralType,
    getCustomReferralConfigs: () => customReferralConfigs,
    getReferralDrawerOpen: () => referralDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    addCustomReferralConfig,
    handleReferralModeChange,
    updateReferralPreferences,
    handleReferralTypeSelection,
    validateReferralConfig,
    refreshPendingRewards: () => fetchPendingRewards()
  }), [
    referralHistory,
    referralAnalytics,
    referralInsights,
    referralStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    referralMode,
    selectedReferralType,
    customReferralConfigs,
    pendingRewards,
    referralRewards,
    referralProgress,
    navigate,
    addCustomReferralConfig,
    fetchPendingRewards,
    fetchReferralAnalytics,
    handleReferralModeChange,
    handleReferralTypeSelection,
    updateReferralPreferences,
    validateReferralConfig,
    fullscreenMode,
    referralDrawerOpen,
    showAnalytics
  ]);

  // Fetch referral analytics with enhanced error handling and retry logic
  const fetchReferralAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/referrals/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setReferralAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (referralPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Referral analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch referral analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load referral analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, referralPreferences.showAnalytics]);

  // Handle referral mode switching
  const handleReferralModeChange = useCallback((newMode) => {
    if (REFERRAL_ICON_MODES[newMode.toUpperCase()]) {
      setReferralMode(newMode);
      announceToScreenReader(`Referral mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setReferralHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (referralPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} referral mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, referralPreferences.showAnalytics, showSuccess]);

  // Handle custom referral config management
  const addCustomReferralConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomReferralConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setReferralHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (referralPreferences.showAnalytics) {
      showSuccess(`Custom referral config "${configData.name}" created`);
    }
  }, [subscription?.user_id, referralPreferences.showAnalytics, showSuccess]);

  // Handle referral preferences updates
  const updateReferralPreferences = useCallback((newPreferences) => {
    setReferralPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setReferralHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (referralPreferences.showAnalytics) {
      showSuccess('Referral preferences updated');
    }
  }, [subscription?.user_id, referralPreferences.showAnalytics, showSuccess]);

  // Handle referral type selection
  const handleReferralTypeSelection = useCallback((referralType) => {
    setSelectedReferralType(referralType);

    // Track referral type selection
    const typeRecord = {
      id: Date.now(),
      type: 'referral_type_selected',
      referralType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setReferralHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (referralPreferences.showAnalytics) {
      announceToScreenReader(`Selected referral type: ${referralType}`);
    }
  }, [subscription?.user_id, referralPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateReferralConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Referral type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Fetch pending rewards
  const fetchPendingRewards = useCallback(async () => {
    try {
      setIsLoading(true);
      await handleApiRequest(
        async () => {
          const response = await api.get('/api/referrals/rewards/pending');
          return response.data;
        },
        {
          onSuccess: (data) => {
            setPendingRewards(data.count || 0);
            setReferralRewards(data.rewards || []);
            setReferralProgress(data.progress || 0);

            if (onReferralAction) {
              onReferralAction('rewards_updated', { count: data.count, rewards: data.rewards });
            }
          },
          onError: () => {
            setPendingRewards(0);
            setReferralRewards([]);
            setReferralProgress(0);
          },
        }
      );
    } catch (error) {
      console.error('Error fetching pending rewards:', error);
      setPendingRewards(0);
    } finally {
      setIsLoading(false);
    }
  }, [handleApiRequest, onReferralAction]);

  // Initial data loading
  useEffect(() => {
    fetchPendingRewards();
    fetchReferralAnalytics();
    fetchReferralInsights();
  }, [fetchPendingRewards, fetchReferralAnalytics, fetchReferralInsights]);

  // Fetch referral insights
  const fetchReferralInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/referrals/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setReferralInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch referral insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && pendingRewards > 0) {
      // Optimize referral management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchReferralAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, pendingRewards, fetchReferralAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastReferralCheck(Date.now());

          if (wasUnavailable && referralPreferences.showAnalytics) {
            showSuccess("Connection restored - Referral features available");
          }
        } else {
          setBackendAvailable(false);
          if (referralPreferences.showAnalytics) {
            showError("Backend service unavailable - Some referral features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastReferralCheck;
          if (timeSinceLastCheck > 60000 && referralPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Referrals may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastReferralCheck, referralPreferences.showAnalytics, showSuccess, showError]);

  // Generate AI suggestions when referrals change
  useEffect(() => {
    if (enableAIInsights && referralPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, referralPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/referrals/ai-suggestions', {
        params: {
          pendingRewards,
          referralProgress
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (referralPreferences.showAnalytics) {
        showSuccess('AI suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [pendingRewards, referralProgress, referralPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when referrals change
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchReferralStats();
    }
  }, [enableAdvancedFeatures, fetchReferralStats]);

  // Fetch referral stats function
  const fetchReferralStats = useCallback(async () => {
    try {
      const response = await api.get('/api/referrals/stats');
      setReferralStats(response.data);
    } catch (error) {
      console.error('Failed to fetch referral stats:', error);
    }
  }, []);

  // Set up polling interval for pending rewards
  useEffect(() => {
    const intervalId = setInterval(fetchPendingRewards, 300000); // Refresh every 5 minutes
    return () => clearInterval(intervalId);
  }, [fetchPendingRewards]);

  const handleClick = useCallback(() => {
    navigate('/referrals');

    // Track navigation
    const navRecord = {
      id: Date.now(),
      type: 'navigation',
      destination: '/referrals',
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setReferralHistory(prev => [navRecord, ...prev.slice(0, 99)]);

    if (onReferralAction) {
      onReferralAction('navigate', { destination: '/referrals' });
    }
  }, [navigate, subscription?.user_id, onReferralAction]);

  const handleMenuClick = useCallback((event) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  }, []);

  const handleMenuClose = useCallback(() => {
    setMenuAnchorEl(null);
  }, []);

  return (
    <Box
      {...getAccessibilityProps()}
      ref={referralIconRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      <Tooltip
        title={`Referral Program - ${pendingRewards} pending rewards`}
        arrow
        placement={isMobile ? "top" : "bottom"}
      >
        <IconButton
          color="inherit"
          onClick={handleClick}
          onContextMenu={handleMenuClick}
          aria-label={`Referral Program - ${pendingRewards} pending rewards`}
          disabled={isLoading}
          sx={{
            position: 'relative',
            color: ACE_COLORS.WHITE,
            '&:hover': {
              animation: 'pulse 1s infinite',
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
              '@keyframes pulse': {
                '0%': {
                  transform: 'scale(1)',
                },
                '50%': {
                  transform: 'scale(1.1)',
                },
                '100%': {
                  transform: 'scale(1)',
                },
              },
            },
            '&:disabled': {
              opacity: 0.6
            }
          }}
        >
          {isLoading ? (
            <CircularProgress size={24} sx={{ color: ACE_COLORS.YELLOW }} />
          ) : (
            <Badge
              badgeContent={pendingRewards}
              color="error"
              max={99}
              sx={{
                '& .MuiBadge-badge': {
                  backgroundColor: ACE_COLORS.YELLOW,
                  color: ACE_COLORS.DARK,
                  fontWeight: 'bold'
                }
              }}
            >
              <CardGiftcardIcon sx={{ color: ACE_COLORS.WHITE }} />
            </Badge>
          )}
        </IconButton>
      </Tooltip>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        slotProps={{
          paper: {
            sx: {
              bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
            }
          }
        }}
      >
        <MenuItem onClick={() => { handleClick(); handleMenuClose(); }}>
          <ListItemIcon>
            <DashboardIcon sx={{ color: ACE_COLORS.PURPLE }} />
          </ListItemIcon>
          <ListItemText primary="Referral Dashboard" />
        </MenuItem>
        <MenuItem onClick={() => { setShowAnalytics(true); handleMenuClose(); }}>
          <ListItemIcon>
            <AnalyticsIcon sx={{ color: ACE_COLORS.PURPLE }} />
          </ListItemIcon>
          <ListItemText primary="View Analytics" />
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => { fetchPendingRewards(); handleMenuClose(); }}>
          <ListItemIcon>
            <RefreshIcon sx={{ color: ACE_COLORS.PURPLE }} />
          </ListItemIcon>
          <ListItemText primary="Refresh Rewards" />
        </MenuItem>
      </Menu>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying referral sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
ReferralIcon.propTypes = {
  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onReferralAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

ReferralIcon.displayName = 'ReferralIcon';

export default ReferralIcon;

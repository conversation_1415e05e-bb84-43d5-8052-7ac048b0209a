/**
 * Enhanced ACE Social Financial Reports - Enterprise-grade comprehensive financial reporting component
 * Features: Comprehensive financial reporting with advanced report generation, customizable report
 * templates, and automated report scheduling for ACE Social financial management, detailed financial
 * report builder with drag-and-drop report components and real-time data visualization, advanced
 * reporting features with interactive report previews and multi-format export options, ACE Social's
 * financial system integration with seamless data aggregation from financial dashboard and customer
 * analytics, reporting interaction features including report template management and real-time report
 * generation, reporting state management with report configuration persistence and scheduled report
 * management, and real-time reporting monitoring with live report generation status and automatic
 * report optimization recommendations
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Alert,
  CircularProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  Stack,
  Paper,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  LinearProgress,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide
} from '@mui/material';
import {
  Download as DownloadIcon,
  Assessment as ReportIcon,
  Schedule as ScheduleIcon,
  Email as EmailIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon,
  InsertDriveFile as ExcelIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Preview as PreviewIcon,
  Settings as SettingsIcon,
  History as HistoryIcon,
  Share as ShareIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  Analytics as AnalyticsIcon,
  MonetizationOn as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Group as GroupIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Financial reports constants
const REPORT_TYPES = {
  REVENUE_SUMMARY: 'revenue_summary',
  CHURN_ANALYSIS: 'churn_analysis',
  LTV_ANALYSIS: 'ltv_analysis',
  PAYMENT_FAILURES: 'payment_failures',
  FINANCIAL_FORECAST: 'financial_forecast',
  CUSTOM: 'custom'
};

const EXPORT_FORMATS = {
  EXCEL: 'excel',
  CSV: 'csv',
  PDF: 'pdf',
  JSON: 'json'
};

const SCHEDULE_FREQUENCIES = {
  NONE: 'none',
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly'
};

const REPORT_STATUSES = {
  DRAFT: 'draft',
  GENERATING: 'generating',
  COMPLETED: 'completed',
  FAILED: 'failed',
  SCHEDULED: 'scheduled'
};

// Reports analytics events
const REPORTS_ANALYTICS_EVENTS = {
  REPORT_GENERATED: 'financial_report_generated',
  TEMPLATE_SAVED: 'report_template_saved',
  REPORT_SCHEDULED: 'report_scheduled',
  REPORT_EXPORTED: 'report_exported',
  TEMPLATE_SHARED: 'report_template_shared',
  REPORT_PREVIEWED: 'report_previewed'
};

/**
 * Enhanced Financial Reports - Comprehensive financial reporting with advanced features
 * Implements detailed financial reporting management and enterprise-grade reporting capabilities
 */

const EnhancedFinancialReports = memo(forwardRef(({
  data = {},
  loading = false,
  error,
  className,
  enableAdvancedFeatures = true,
  enableReportTemplates = true,
  enableScheduling = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableCollaboration = true,
  enablePreview = true,
  autoSaveInterval = 30000, // 30 seconds
  maxReportHistory = 100,
  onReportGenerate,
  onTemplateCreate,
  onTemplateUpdate,
  onReportSchedule,
  onReportShare,
  onAnalyticsTrack,
  onReportPreview,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const reportsRef = useRef(null);
  const previewRef = useRef(null);
  const autoSaveTimeoutRef = useRef(null);

  // Core state management
  const [reportLoading, setReportLoading] = useState(false);
  const [reportError, setReportError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Report configuration
  const [reportConfig, setReportConfig] = useState({
    type: REPORT_TYPES.REVENUE_SUMMARY,
    period_start: startOfMonth(subMonths(new Date(), 1)),
    period_end: endOfMonth(subMonths(new Date(), 1)),
    format: EXPORT_FORMATS.EXCEL,
    include_plan_breakdown: true,
    include_customer_segments: false,
    include_forecasts: false,
    email_delivery: false,
    schedule_frequency: SCHEDULE_FREQUENCIES.NONE,
  });

  // Enhanced state management
  const [reportTemplates, setReportTemplates] = useState([]);
  const [reportHistory, setReportHistory] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [previewData, setPreviewData] = useState(null);
  const [isDirty, setIsDirty] = useState(false);
  const [reportsAnalytics, setReportsAnalytics] = useState({
    reportsGenerated: 0,
    templatesCreated: 0,
    reportsScheduled: 0,
    reportsShared: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    generateReport: () => handleGenerateReport(),
    saveTemplate: () => handleSaveTemplate(),
    loadTemplate: (templateId) => handleLoadTemplate(templateId),
    resetConfiguration: () => handleResetConfiguration(),
    focusReports: () => reportsRef.current?.focus(),

    // Configuration methods
    getReportConfig: () => reportConfig,
    setReportConfig: (config) => setReportConfig(config),
    getReportTemplates: () => reportTemplates,
    getReportHistory: () => reportHistory,

    // State methods
    isLoading: () => reportLoading,
    hasError: () => !!reportError,
    isDirty: () => isDirty,

    // Preview methods
    generatePreview: () => handleGeneratePreview(),
    getPreviewData: () => previewData,

    // Template methods
    createTemplate: (name, config) => handleCreateTemplate(name, config),
    updateTemplate: (templateId, config) => handleUpdateTemplate(templateId, config),
    deleteTemplate: (templateId) => handleDeleteTemplate(templateId),

    // Analytics methods
    getReportsAnalytics: () => reportsAnalytics,

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    scheduleReport: (schedule) => handleScheduleReport(schedule),
    shareReport: (reportId, recipients) => handleShareReport(reportId, recipients),
    exportReport: (format) => handleExportReport(format)
  }), [
    reportConfig,
    reportTemplates,
    reportHistory,
    reportLoading,
    reportError,
    isDirty,
    previewData,
    reportsAnalytics,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(REPORTS_ANALYTICS_EVENTS.REPORT_GENERATED, {
        reportType: reportConfig.type,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Financial reports interface loaded with ${reportConfig.type} configuration`);
    }
  }, [reportConfig.type, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Auto-save effect
  useEffect(() => {
    if (enableReportTemplates && isDirty && autoSaveInterval > 0) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        handleAutoSave();
      }, autoSaveInterval);

      return () => {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current);
        }
      };
    }
  }, [enableReportTemplates, isDirty, autoSaveInterval, reportConfig]);

  const reportTypes = useMemo(() => [
    {
      value: 'revenue_summary',
      label: 'Revenue Summary',
      description: 'Comprehensive revenue breakdown and metrics',
      icon: <ReportIcon />,
    },
    {
      value: 'churn_analysis',
      label: 'Churn Analysis',
      description: 'Customer churn rates and revenue impact',
      icon: <ReportIcon />,
    },
    {
      value: 'ltv_analysis',
      label: 'Customer LTV Analysis',
      description: 'Customer lifetime value calculations and trends',
      icon: <ReportIcon />,
    },
    {
      value: 'payment_failures',
      label: 'Payment Failures Report',
      description: 'Failed payments and retry analysis',
      icon: <ReportIcon />,
    },
    {
      value: 'financial_forecast',
      label: 'Financial Forecast',
      description: 'Revenue predictions and growth projections',
      icon: <ReportIcon />,
    },
  ], []);

  const exportFormats = useMemo(() => [
    { value: EXPORT_FORMATS.EXCEL, label: 'Excel (.xlsx)', icon: <ExcelIcon /> },
    { value: EXPORT_FORMATS.CSV, label: 'CSV (.csv)', icon: <CsvIcon /> },
    { value: EXPORT_FORMATS.PDF, label: 'PDF (.pdf)', icon: <PdfIcon /> },
    { value: EXPORT_FORMATS.JSON, label: 'JSON (.json)', icon: <ReportIcon /> },
  ], []);

  const scheduleOptions = useMemo(() => [
    { value: SCHEDULE_FREQUENCIES.NONE, label: 'One-time Export' },
    { value: SCHEDULE_FREQUENCIES.DAILY, label: 'Daily' },
    { value: SCHEDULE_FREQUENCIES.WEEKLY, label: 'Weekly' },
    { value: SCHEDULE_FREQUENCIES.MONTHLY, label: 'Monthly' },
    { value: SCHEDULE_FREQUENCIES.QUARTERLY, label: 'Quarterly' },
  ], []);

  // Enhanced handler functions
  const handleConfigChange = useCallback((field, value) => {
    setReportConfig(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);

    if (enableAccessibility) {
      announceToScreenReader(`Report configuration updated: ${field}`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleAutoSave = useCallback(async () => {
    if (!isDirty || !selectedTemplate) return;

    try {
      // Save template configuration to localStorage as backup
      const autoSaveData = {
        reportConfig,
        timestamp: new Date().toISOString(),
        templateId: selectedTemplate
      };
      localStorage.setItem('financial_reports_autosave', JSON.stringify(autoSaveData));

      setIsDirty(false);

      if (enableAccessibility) {
        announceToScreenReader('Report configuration auto-saved successfully');
      }
    } catch (error) {
      console.warn('Auto-save failed:', error);
    }
  }, [isDirty, selectedTemplate, reportConfig, enableAccessibility, announceToScreenReader]);

  const handleGenerateReport = useCallback(async () => {
    setReportLoading(true);
    setReportError(null);
    setSuccess(null);

    setReportsAnalytics(prev => ({
      ...prev,
      reportsGenerated: prev.reportsGenerated + 1
    }));

    try {
      if (onReportGenerate) {
        const result = await onReportGenerate(reportConfig);
        setSuccess(`${reportTypes.find(t => t.value === reportConfig.type)?.label} report generated successfully!`);

        // Add to report history
        setReportHistory(prev => [{
          id: Date.now(),
          type: reportConfig.type,
          format: reportConfig.format,
          generatedAt: new Date().toISOString(),
          status: REPORT_STATUSES.COMPLETED,
          config: reportConfig
        }, ...prev.slice(0, maxReportHistory - 1)]);
      } else {
        // Simulate API call for demo
        await new Promise(resolve => setTimeout(resolve, 2000));
        setSuccess(`${reportTypes.find(t => t.value === reportConfig.type)?.label} report generated successfully!`);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(REPORTS_ANALYTICS_EVENTS.REPORT_GENERATED, {
          reportType: reportConfig.type,
          format: reportConfig.format,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader('Report generated successfully');
      }
    } catch (err) {
      console.error('Error generating report:', err);
      setReportError(err.response?.data?.detail || 'Failed to generate report');

      if (enableAccessibility) {
        announceToScreenReader('Failed to generate report. Please check the configuration and try again.');
      }
    } finally {
      setReportLoading(false);
    }
  }, [
    reportConfig,
    reportTypes,
    maxReportHistory,
    onReportGenerate,
    enableAnalytics,
    enableAccessibility,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  // Enhanced utility functions
  const handleGeneratePreview = useCallback(async () => {
    if (!enablePreview) return;

    try {
      setReportLoading(true);

      if (onReportPreview) {
        const preview = await onReportPreview(reportConfig);
        setPreviewData(preview);
      } else {
        // Generate mock preview data
        const mockPreview = {
          reportType: reportConfig.type,
          period: `${format(reportConfig.period_start, 'MMM dd, yyyy')} - ${format(reportConfig.period_end, 'MMM dd, yyyy')}`,
          estimatedSize: '2.5 MB',
          estimatedRows: 1250,
          sections: [
            'Executive Summary',
            reportConfig.include_plan_breakdown && 'Plan Breakdown',
            reportConfig.include_customer_segments && 'Customer Segments',
            reportConfig.include_forecasts && 'Financial Forecasts'
          ].filter(Boolean)
        };
        setPreviewData(mockPreview);
      }

      if (enableAccessibility) {
        announceToScreenReader('Report preview generated');
      }
    } catch (error) {
      console.error('Error generating preview:', error);
    } finally {
      setReportLoading(false);
    }
  }, [enablePreview, reportConfig, onReportPreview, enableAccessibility, announceToScreenReader]);

  const handleSaveTemplate = useCallback(async () => {
    if (!enableReportTemplates) return;

    try {
      const templateName = prompt('Enter template name:');
      if (!templateName) return;

      const newTemplate = {
        id: Date.now(),
        name: templateName,
        config: reportConfig,
        createdAt: new Date().toISOString()
      };

      setReportTemplates(prev => [newTemplate, ...prev]);
      setReportsAnalytics(prev => ({
        ...prev,
        templatesCreated: prev.templatesCreated + 1
      }));

      if (onTemplateCreate) {
        onTemplateCreate(newTemplate);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(REPORTS_ANALYTICS_EVENTS.TEMPLATE_SAVED, {
          templateName,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Template ${templateName} saved successfully`);
      }
    } catch (error) {
      console.error('Error saving template:', error);
    }
  }, [enableReportTemplates, reportConfig, onTemplateCreate, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleLoadTemplate = useCallback((templateId) => {
    const template = reportTemplates.find(t => t.id === templateId);
    if (template) {
      setReportConfig(template.config);
      setSelectedTemplate(templateId);
      setIsDirty(false);

      if (enableAccessibility) {
        announceToScreenReader(`Template ${template.name} loaded`);
      }
    }
  }, [reportTemplates, enableAccessibility, announceToScreenReader]);

  const handleCreateTemplate = useCallback((name, config) => {
    const newTemplate = {
      id: Date.now(),
      name,
      config,
      createdAt: new Date().toISOString()
    };

    setReportTemplates(prev => [newTemplate, ...prev]);
    return newTemplate;
  }, []);

  const handleUpdateTemplate = useCallback((templateId, config) => {
    setReportTemplates(prev => prev.map(template =>
      template.id === templateId
        ? { ...template, config, updatedAt: new Date().toISOString() }
        : template
    ));

    if (onTemplateUpdate) {
      onTemplateUpdate(templateId, config);
    }
  }, [onTemplateUpdate]);

  const handleDeleteTemplate = useCallback((templateId) => {
    setReportTemplates(prev => prev.filter(template => template.id !== templateId));
  }, []);

  const handleResetConfiguration = useCallback(() => {
    setReportConfig({
      type: REPORT_TYPES.REVENUE_SUMMARY,
      period_start: startOfMonth(subMonths(new Date(), 1)),
      period_end: endOfMonth(subMonths(new Date(), 1)),
      format: EXPORT_FORMATS.EXCEL,
      include_plan_breakdown: true,
      include_customer_segments: false,
      include_forecasts: false,
      email_delivery: false,
      schedule_frequency: SCHEDULE_FREQUENCIES.NONE,
    });
    setIsDirty(false);
    setSelectedTemplate(null);

    if (enableAccessibility) {
      announceToScreenReader('Report configuration reset to defaults');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleScheduleReport = useCallback((schedule) => {
    if (!enableScheduling) return;

    setReportsAnalytics(prev => ({
      ...prev,
      reportsScheduled: prev.reportsScheduled + 1
    }));

    if (onReportSchedule) {
      onReportSchedule(schedule);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(REPORTS_ANALYTICS_EVENTS.REPORT_SCHEDULED, {
        schedule,
        timestamp: new Date().toISOString()
      });
    }
  }, [enableScheduling, onReportSchedule, enableAnalytics, onAnalyticsTrack]);

  const handleShareReport = useCallback((reportId, recipients) => {
    if (!enableCollaboration) return;

    setReportsAnalytics(prev => ({
      ...prev,
      reportsShared: prev.reportsShared + 1
    }));

    if (onReportShare) {
      onReportShare(reportId, recipients);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(REPORTS_ANALYTICS_EVENTS.TEMPLATE_SHARED, {
        reportId,
        recipientCount: recipients.length,
        timestamp: new Date().toISOString()
      });
    }
  }, [enableCollaboration, onReportShare, enableAnalytics, onAnalyticsTrack]);

  const handleExportReport = useCallback((format) => {
    // Implementation would depend on the specific export requirements
    console.log(`Exporting report in ${format} format`);
  }, []);

  const setQuickDateRange = useCallback((months) => {
    const end = endOfMonth(new Date());
    const start = startOfMonth(subMonths(end, months - 1));
    handleConfigChange('period_start', start);
    handleConfigChange('period_end', end);
  }, [handleConfigChange]);

  const selectedReportType = reportTypes.find(t => t.value === reportConfig.type);

  return (
    <Box
      ref={reportsRef}
      className={className}
      sx={glassMorphismStyles}
      {...props}
    >
      {(reportError || error) && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => {
          setReportError(null);
          if (enableAccessibility) {
            announceToScreenReader('Error message dismissed');
          }
        }}>
          {reportError || error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => {
          setSuccess(null);
          if (enableAccessibility) {
            announceToScreenReader('Success message dismissed');
          }
        }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Report Configuration */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardHeader title="Report Configuration" />
            <CardContent>
              <Grid container spacing={3}>
                {/* Report Type */}
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Report Type</InputLabel>
                    <Select
                      value={reportConfig.type}
                      label="Report Type"
                      onChange={(e) => handleConfigChange('type', e.target.value)}
                    >
                      {reportTypes.map((type) => (
                        <MenuItem key={type.value} value={type.value}>
                          <Box display="flex" alignItems="center" gap={1}>
                            {type.icon}
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {type.label}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {type.description}
                              </Typography>
                            </Box>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Date Range */}
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="Start Date"
                    value={reportConfig.period_start}
                    onChange={(value) => handleConfigChange('period_start', value)}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="End Date"
                    value={reportConfig.period_end}
                    onChange={(value) => handleConfigChange('period_end', value)}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </Grid>

                {/* Quick Date Range Buttons */}
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Quick Date Ranges:
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    <Button size="small" onClick={() => setQuickDateRange(1)}>
                      Last Month
                    </Button>
                    <Button size="small" onClick={() => setQuickDateRange(3)}>
                      Last 3 Months
                    </Button>
                    <Button size="small" onClick={() => setQuickDateRange(6)}>
                      Last 6 Months
                    </Button>
                    <Button size="small" onClick={() => setQuickDateRange(12)}>
                      Last Year
                    </Button>
                  </Box>
                </Grid>

                {/* Export Format */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Export Format</InputLabel>
                    <Select
                      value={reportConfig.format}
                      label="Export Format"
                      onChange={(e) => handleConfigChange('format', e.target.value)}
                    >
                      {exportFormats.map((format) => (
                        <MenuItem key={format.value} value={format.value}>
                          <Box display="flex" alignItems="center" gap={1}>
                            {format.icon}
                            {format.label}
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Schedule */}
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Schedule</InputLabel>
                    <Select
                      value={reportConfig.schedule_frequency}
                      label="Schedule"
                      onChange={(e) => handleConfigChange('schedule_frequency', e.target.value)}
                    >
                      {scheduleOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Report Options */}
                <Grid item xs={12}>
                  <Typography variant="body2" fontWeight="bold" gutterBottom>
                    Report Options:
                  </Typography>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={reportConfig.include_plan_breakdown}
                          onChange={(e) => handleConfigChange('include_plan_breakdown', e.target.checked)}
                        />
                      }
                      label="Include plan breakdown"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={reportConfig.include_customer_segments}
                          onChange={(e) => handleConfigChange('include_customer_segments', e.target.checked)}
                        />
                      }
                      label="Include customer segments"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={reportConfig.include_forecasts}
                          onChange={(e) => handleConfigChange('include_forecasts', e.target.checked)}
                        />
                      }
                      label="Include forecasts and predictions"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={reportConfig.email_delivery}
                          onChange={(e) => handleConfigChange('email_delivery', e.target.checked)}
                        />
                      }
                      label="Email delivery"
                    />
                  </FormGroup>
                </Grid>

                {/* Generate Button */}
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={reportLoading ? <CircularProgress size={20} /> : <DownloadIcon />}
                    onClick={handleGenerateReport}
                    disabled={reportLoading}
                    fullWidth
                    sx={{
                      background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE} 30%, ${ACE_COLORS.YELLOW} 90%)`,
                      '&:hover': {
                        background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE} 60%, ${ACE_COLORS.YELLOW} 100%)`,
                      }
                    }}
                  >
                    {reportLoading ? 'Generating Report...' : 'Generate Report'}
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Report Preview & Info */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Report Preview" />
            <CardContent>
              {selectedReportType && (
                <Box>
                  <Box display="flex" alignItems="center" gap={1} mb={2}>
                    {selectedReportType.icon}
                    <Typography variant="h6">
                      {selectedReportType.label}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {selectedReportType.description}
                  </Typography>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="body2" fontWeight="bold" gutterBottom>
                    Report Details:
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Period"
                        secondary={`${format(reportConfig.period_start, 'MMM dd, yyyy')} - ${format(reportConfig.period_end, 'MMM dd, yyyy')}`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Format"
                        secondary={exportFormats.find(f => f.value === reportConfig.format)?.label}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Schedule"
                        secondary={scheduleOptions.find(s => s.value === reportConfig.schedule_frequency)?.label}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Delivery"
                        secondary={reportConfig.email_delivery ? "Email + Download" : "Download Only"}
                      />
                    </ListItem>
                  </List>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="body2" fontWeight="bold" gutterBottom>
                    Included Sections:
                  </Typography>
                  <Box display="flex" flexDirection="column" gap={0.5}>
                    <Chip label="Core Metrics" size="small" color="primary" />
                    {reportConfig.include_plan_breakdown && (
                      <Chip label="Plan Breakdown" size="small" color="secondary" />
                    )}
                    {reportConfig.include_customer_segments && (
                      <Chip label="Customer Segments" size="small" color="secondary" />
                    )}
                    {reportConfig.include_forecasts && (
                      <Chip label="Forecasts" size="small" color="secondary" />
                    )}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Recent Reports */}
          <Card sx={{ mt: 3 }}>
            <CardHeader title="Recent Reports" />
            <CardContent>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <ReportIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Revenue Summary - December 2024"
                    secondary="Generated 2 hours ago"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <ReportIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Churn Analysis - Q4 2024"
                    secondary="Generated yesterday"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <ScheduleIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Monthly Revenue Report"
                    secondary="Scheduled for tomorrow"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedFinancialReports.propTypes = {
  // Core props
  data: PropTypes.shape({
    reportTemplates: PropTypes.array,
    reportHistory: PropTypes.array,
    recentReports: PropTypes.array
  }),
  loading: PropTypes.bool,
  error: PropTypes.string,

  // Enhanced props
  className: PropTypes.string,
  enableAdvancedFeatures: PropTypes.bool,
  enableReportTemplates: PropTypes.bool,
  enableScheduling: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableCollaboration: PropTypes.bool,
  enablePreview: PropTypes.bool,

  // Configuration
  autoSaveInterval: PropTypes.number,
  maxReportHistory: PropTypes.number,

  // Callback props
  onReportGenerate: PropTypes.func,
  onTemplateCreate: PropTypes.func,
  onTemplateUpdate: PropTypes.func,
  onReportSchedule: PropTypes.func,
  onReportShare: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onReportPreview: PropTypes.func
};

// Default props
EnhancedFinancialReports.defaultProps = {
  data: {},
  loading: false,
  error: null,
  className: '',
  enableAdvancedFeatures: true,
  enableReportTemplates: true,
  enableScheduling: true,
  enableAccessibility: true,
  enableAnalytics: true,
  enableCollaboration: true,
  enablePreview: true,
  autoSaveInterval: 30000,
  maxReportHistory: 100,
  onReportGenerate: null,
  onTemplateCreate: null,
  onTemplateUpdate: null,
  onReportSchedule: null,
  onReportShare: null,
  onAnalyticsTrack: null,
  onReportPreview: null
};

// Display name for debugging
EnhancedFinancialReports.displayName = 'EnhancedFinancialReports';

export default EnhancedFinancialReports;

// @since 2024-1-1 to 2025-25-7
import { useState, useMemo, useCallback, useRef, useEffect, memo, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  LinearProgress,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Collapse,
  useTheme,
  alpha,
  IconButton,
  Grid,
  Alert,
  AlertTitle,
  Fade,

  Menu,
  MenuItem,
  ListItemSecondaryAction,

  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,

  CircularProgress,
  Divider
} from '@mui/material';
import {
  CheckCircle as CompleteIcon,
  RadioButtonUnchecked as IncompleteIcon,
  ExpandMore as ExpandMoreIcon,

  RocketLaunch as RocketIcon,
  Share as SocialIcon,
  Article as ContentIcon,
  People as AudienceIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as GrowthIcon,
  Settings as SettingsIcon,
  School as TutorialIcon,

  Verified as VerifiedIcon,
  Star as StarIcon,

  PlayArrow as PlayIcon,

  Refresh as RefreshIcon,
  Download as ExportIcon,
  Upgrade as UpgradeIcon,

  Error as ErrorIcon,

  CheckCircleOutline as CheckCircleOutlineIcon,
  RadioButtonChecked as RadioButtonCheckedIcon,

} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import DashboardCard from "./DashboardCard";
import ErrorBoundary from "../common/ErrorBoundary";


// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};



// Animation configurations
const ANIMATION_CONFIG = {
  DURATION: 300,
  EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  STAGGER_DELAY: 50
};

/**
 * Enhanced OnboardingProgressCard Component - Enterprise-grade onboarding progress dashboard
 * Features: Plan-based onboarding progress limitations, real-time progress tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced onboarding insights and interactive progress exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.userProgress - User onboarding progress data
 * @param {Function} [props.onNavigateToStep] - Step navigation callback
 * @param {Function} [props.onDismiss] - Dismiss callback
 * @param {string} [props.variant='detailed'] - Card display variant
 * @param {boolean} [props.showDismiss=true] - Whether to show dismiss button
 * @param {boolean} [props.autoCollapse=false] - Whether to auto-collapse
 * @param {boolean} [props.enableTutorials=false] - Enable interactive tutorials
 * @param {boolean} [props.enableAnalytics=false] - Enable progress analytics
 * @param {boolean} [props.enableExport=false] - Enable progress export
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onStepComplete] - Step completion callback
 * @param {Function} [props.onProgressUpdate] - Progress update callback
 * @param {Function} [props.onTutorialStart] - Tutorial start callback
 * @param {Function} [props.onAnalyticsRequest] - Analytics request callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Object} [props.customSteps] - Custom onboarding steps
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */
const OnboardingProgressCard = memo(forwardRef(({
  userProgress,
  onNavigateToStep,
  onDismiss,
  variant = 'detailed',
  showDismiss = true,
  autoCollapse = false,
  enableTutorials = false,
  enableAnalytics = false,
  enableExport = false,
  realTimeUpdates = false,

  onProgressUpdate,
  onTutorialStart,
  onAnalyticsRequest,
  onExport,
  customSteps = {},

  className = '',
  style = {},
  testId = 'onboarding-progress-card',
  ariaLabel,
  ariaDescription,

}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, isScreenReaderActive, setFocusToElement } = useAccessibility();

  // Refs for enhanced functionality
  const cardRef = useRef(null);

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    expanded: !autoCollapse,
    loading: false,
    refreshing: false,
    showTutorialDialog: false,
    showAnalyticsPanel: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    selectedStep: null,
    tutorialProgress: {},
    analyticsData: null,
    lastUpdated: null,
    animationKey: 0,
    errors: {},
    notifications: [],
    // Onboarding state
    currentStepIndex: 0,
    completedSteps: [],
    skippedSteps: [],
    stepTimings: {},
    progressHistory: [],
    customStepData: customSteps
  });

  // Onboarding progress data state
  const [progressData, setProgressData] = useState({
    steps: userProgress?.steps || {},
    completedSteps: userProgress?.completedSteps || 0,
    totalSteps: userProgress?.totalSteps || 0,
    progressPercentage: userProgress?.progressPercentage || 0,
    analytics: null,
    milestones: [],
    achievements: []
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);

  /**
   * Enhanced plan-based onboarding progress validation - Production Ready
   */
  const validateOnboardingProgress = useCallback(() => {
    if (!subscription) {
      return {
        canViewProgress: false,
        hasProgressAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { maxSteps: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based onboarding progress limits
    const planLimits = {
      creator: {
        maxSteps: 5,
        features: ['basic_onboarding'],
        tutorials: false,
        analytics: false,
        export: false,
        customWorkflows: false,
        realTimeTracking: false,
        progressInsights: false
      },
      accelerator: {
        maxSteps: 15,
        features: ['basic_onboarding', 'advanced_tutorials', 'progress_analytics'],
        tutorials: true,
        analytics: true,
        export: true,
        customWorkflows: false,
        realTimeTracking: true,
        progressInsights: true
      },
      dominator: {
        maxSteps: -1,
        features: ['basic_onboarding', 'advanced_tutorials', 'progress_analytics', 'custom_workflows'],
        tutorials: true,
        analytics: true,
        export: true,
        customWorkflows: true,
        realTimeTracking: true,
        progressInsights: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = progressData.completedSteps || 0;
    const limit = currentPlanLimits.maxSteps;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewProgress: true,
      hasProgressAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, progressData.completedSteps]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const progressLimits = validateOnboardingProgress();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasTutorials: progressLimits.planLimits.tutorials,
      hasAnalytics: progressLimits.planLimits.analytics,
      hasExport: progressLimits.planLimits.export,
      hasCustomWorkflows: progressLimits.planLimits.customWorkflows,
      hasRealTimeTracking: progressLimits.planLimits.realTimeTracking,
      hasProgressInsights: progressLimits.planLimits.progressInsights,
      maxSteps: progressLimits.planLimits.maxSteps,
      availableFeatures: progressLimits.planLimits.features,
      refreshInterval: progressLimits.planLimits.realTimeTracking ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateOnboardingProgress]);

  /**
   * Enhanced step configuration - Production Ready
   */
  const getStepConfig = useCallback(() => {
    const baseStepConfig = {
      social_accounts_connected: {
        icon: SocialIcon,
        title: 'Connect Social Media Accounts',
        description: 'Link your social platforms to start tracking analytics',
        route: '/settings?tab=integrations',
        color: ACE_COLORS.PURPLE,
        category: 'Account Setup',
        estimatedTime: '5 minutes',
        difficulty: 'easy',
        priority: 1
      },
      first_content_created: {
        icon: ContentIcon,
        title: 'Create Your First Content',
        description: 'Use our AI generator to create engaging posts',
        route: '/content/generator',
        color: ACE_COLORS.YELLOW,
        category: 'Content Creation',
        estimatedTime: '10 minutes',
        difficulty: 'easy',
        priority: 2
      },
      content_published: {
        icon: GrowthIcon,
        title: 'Publish Content',
        description: 'Share your content to start building your audience',
        route: '/scheduling',
        color: ACE_COLORS.PURPLE,
        category: 'Content Creation',
        estimatedTime: '5 minutes',
        difficulty: 'easy',
        priority: 3
      },
      competitors_added: {
        icon: AnalyticsIcon,
        title: 'Add Competitors',
        description: 'Track competitor performance and strategies',
        route: '/settings?tab=competitors',
        color: ACE_COLORS.DARK,
        category: 'Analytics Setup',
        estimatedTime: '15 minutes',
        difficulty: 'medium',
        priority: 4
      },
      analytics_configured: {
        icon: AudienceIcon,
        title: 'Configure Analytics',
        description: 'Set up detailed analytics tracking',
        route: '/analytics/setup',
        color: ACE_COLORS.YELLOW,
        category: 'Analytics Setup',
        estimatedTime: '20 minutes',
        difficulty: 'medium',
        priority: 5
      }
    };

    // Merge with custom steps
    return { ...baseStepConfig, ...state.customStepData };
  }, [state.customStepData]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onProgressUpdate) {
        await onProgressUpdate();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        lastUpdated: new Date().toISOString(),
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Onboarding progress refreshed successfully');
      announceToScreenReader('Onboarding progress has been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh progress: ${error.message}`);
      announceToScreenReader('Failed to refresh onboarding progress');
    }
  }, [state.refreshing, onProgressUpdate, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced tutorial start handler - Production Ready
   */
  const handleTutorialStart = useCallback(async (stepId) => {
    if (!subscriptionFeatures.hasTutorials) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, showTutorialDialog: true, selectedStep: stepId }));

    try {
      if (onTutorialStart) {
        await onTutorialStart(stepId);
      }

      announceToScreenReader(`Tutorial started for ${stepId}`);
    } catch (error) {
      showErrorNotification(`Failed to start tutorial: ${error.message}`);
      announceToScreenReader('Failed to start tutorial');
    }
  }, [subscriptionFeatures.hasTutorials, onTutorialStart, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced analytics request handler - Production Ready
   */
  const handleAnalyticsRequest = useCallback(async () => {
    if (!subscriptionFeatures.hasAnalytics) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, showAnalyticsPanel: true }));

    try {
      if (onAnalyticsRequest) {
        const analytics = await onAnalyticsRequest();
        setProgressData(prev => ({ ...prev, analytics }));
      }

      announceToScreenReader('Progress analytics have been generated');
    } catch (error) {
      showErrorNotification(`Failed to generate analytics: ${error.message}`);
      announceToScreenReader('Failed to generate progress analytics');
    }
  }, [subscriptionFeatures.hasAnalytics, onAnalyticsRequest, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'csv') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, progressData);
      }

      showSuccessNotification(`Progress data exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Progress data has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export data: ${error.message}`);
      announceToScreenReader('Failed to export progress data');
    }
  }, [subscriptionFeatures.hasExport, progressData, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);



  /**
   * Enhanced data processing - Production Ready
   */
  const processedSteps = useMemo(() => {
    const stepConfig = getStepConfig();
    const steps = progressData.steps || {};

    return Object.entries(steps).map(([stepId, stepData]) => {
      const config = stepConfig[stepId] || {};
      return {
        id: stepId,
        ...config,
        ...stepData,
        isCompleted: stepData.completed || false,
        isSkipped: state.skippedSteps.includes(stepId),
        completedAt: state.stepTimings[stepId] || null,
        isAvailable: subscriptionFeatures.maxSteps === -1 ||
                    Object.keys(steps).length <= subscriptionFeatures.maxSteps
      };
    }).sort((a, b) => (a.priority || 999) - (b.priority || 999));
  }, [progressData.steps, getStepConfig, state.skippedSteps, state.stepTimings, subscriptionFeatures.maxSteps]);

  /**
   * Enhanced progress calculation - Production Ready
   */
  const progressStats = useMemo(() => {
    const totalSteps = processedSteps.length;
    const completedSteps = processedSteps.filter(step => step.isCompleted).length;
    const skippedSteps = processedSteps.filter(step => step.isSkipped).length;
    const remainingSteps = totalSteps - completedSteps - skippedSteps;
    const progressPercentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

    return {
      totalSteps,
      completedSteps,
      skippedSteps,
      remainingSteps,
      progressPercentage,
      isComplete: completedSteps === totalSteps,
      nextStep: processedSteps.find(step => !step.isCompleted && !step.isSkipped)
    };
  }, [processedSteps]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Onboarding progress: ${progressStats.progressPercentage}% complete`,
      'aria-description': ariaDescription || `${progressStats.completedSteps} of ${progressStats.totalSteps} onboarding steps completed`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, progressStats, realTimeUpdates]);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced onboarding features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Interactive tutorials',
        'Progress analytics',
        'Custom workflows',
        'Real-time tracking',
        'Data export capabilities',
        'Advanced progress insights'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  /**
   * Enhanced keyboard navigation - Production Ready
   */
  const handleKeyDown = useCallback((event) => {
    if (!isScreenReaderActive) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        if (event.target.getAttribute('role') === 'button') {
          event.preventDefault();
          event.target.click();
        }
        break;
      case 'Escape':
        if (state.showUpgradeDialog) {
          setState(prev => ({ ...prev, showUpgradeDialog: false }));
        }
        if (state.showTutorialDialog) {
          setState(prev => ({ ...prev, showTutorialDialog: false }));
        }
        break;
    }
  }, [isScreenReaderActive, state.showUpgradeDialog, state.showTutorialDialog]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTimeTracking) return;

    const interval = setInterval(() => {
      if (onProgressUpdate) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTimeTracking, subscriptionFeatures.refreshInterval, onProgressUpdate, handleRefresh]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: subscriptionLoading }));
  }, [subscriptionLoading]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportData: handleExport,
    startTutorial: handleTutorialStart,
    requestAnalytics: handleAnalyticsRequest,
    getProgressData: () => progressData,
    getProgressLimits: validateOnboardingProgress,
    focus: () => setFocusToElement(cardRef.current),
    announce: (message) => announceToScreenReader(message)
  }), [
    progressData,
    validateOnboardingProgress,
    handleRefresh,
    handleExport,
    handleTutorialStart,
    handleAnalyticsRequest,
    setFocusToElement,
    announceToScreenReader
  ]);

  // Enhanced step click handler
  const handleStepClick = useCallback((stepKey) => {
    const stepConfig = getStepConfig();
    const config = stepConfig[stepKey];
    if (config && onNavigateToStep) {
      onNavigateToStep(config.route, stepKey);
      announceToScreenReader(`Navigating to ${config.title}`);
    }
  }, [getStepConfig, onNavigateToStep, announceToScreenReader]);

  // Enhanced next step recommendation
  const getNextStepRecommendation = useCallback(() => {
    const incompleteSteps = processedSteps.filter(step => !step.isCompleted && !step.isSkipped);
    return incompleteSteps.length > 0 ? incompleteSteps[0] : null;
  }, [processedSteps]);

  const nextStep = useMemo(() => getNextStepRecommendation(), [getNextStepRecommendation]);

  // Enhanced menu handlers
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  // Main render condition checks
  if (state.loading && !progressData.steps) {
    return (
      <DashboardCard
        title="Onboarding Progress"
        description="Loading onboarding progress..."
        icon={RocketIcon}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          minHeight: { xs: 120, sm: 150 }
        }}>
          <CircularProgress
            size={40}
            sx={{ color: ACE_COLORS.PURPLE }}
            aria-label="Loading onboarding progress"
          />
        </Box>
      </DashboardCard>
    );
  }

  // Error state
  if (Object.keys(state.errors).length > 0) {
    return (
      <DashboardCard
        title="Onboarding Progress"
        description="Error loading onboarding data"
        icon={ErrorIcon}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        <Box sx={{ p: 2 }}>
          <Alert
            severity="error"
            sx={{
              backgroundColor: alpha(theme.palette.error.main, 0.1),
              border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
            }}
          >
            <AlertTitle>Onboarding Data Error</AlertTitle>
            {Object.values(state.errors)[0]}
          </Alert>
        </Box>
      </DashboardCard>
    );
  }

  // Enhanced compact variant for smaller spaces
  if (variant === 'compact') {
    return (
      <ErrorBoundary
        fallback={
          <DashboardCard
            title="Onboarding Progress"
            description="Error loading onboarding data"
            icon={ErrorIcon}
          >
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="error">
                Unable to load onboarding progress
              </Typography>
            </Box>
          </DashboardCard>
        }
      >
        <Box
          ref={cardRef}
          className={className}
          style={style}
          data-testid={testId}
          {...getAccessibilityProps()}
          onKeyDown={handleKeyDown}
        >
          <Card
            sx={{
              background: `linear-gradient(135deg,
                ${alpha(ACE_COLORS.PURPLE, 0.1)} 0%,
                ${alpha(ACE_COLORS.YELLOW, 0.1)} 100%)`,
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
              borderRadius: 2,
              transition: 'all 0.3s ease'
            }}
          >
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <RocketIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: 28 }} />
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle2" fontWeight={600} sx={{ color: ACE_COLORS.DARK }}>
                    Setup Progress
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={progressStats.progressPercentage}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      mt: 0.5,
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: ACE_COLORS.PURPLE,
                        borderRadius: 3
                      }
                    }}
                    aria-label={`Progress: ${progressStats.progressPercentage}% complete`}
                  />
                </Box>
                <Chip
                  label={`${progressStats.completedSteps}/${progressStats.totalSteps}`}
                  size="small"
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                    color: ACE_COLORS.DARK,
                    border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
                    fontWeight: 600
                  }}
                />
              </Box>

              {nextStep && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => handleStepClick(nextStep.id)}
                  sx={{
                    mt: 1,
                    textTransform: 'none',
                    borderColor: ACE_COLORS.PURPLE,
                    color: ACE_COLORS.PURPLE,
                    '&:hover': {
                      borderColor: ACE_COLORS.PURPLE,
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                  fullWidth
                  aria-label={`Start next step: ${nextStep.title}`}
                >
                  Next: {nextStep.title}
                </Button>
              )}

              {/* Real-time indicator for compact view */}
              {realTimeUpdates && subscriptionFeatures.hasRealTimeTracking && (
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  mt: 1,
                  justifyContent: 'center'
                }}>
                  <Box
                    sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      backgroundColor: theme.palette.success.main,
                      animation: 'pulse 2s infinite',
                      '@keyframes pulse': {
                        '0%': { opacity: 1 },
                        '50%': { opacity: 0.5 },
                        '100%': { opacity: 1 }
                      }
                    }}
                    aria-hidden="true"
                  />
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ fontSize: '0.65rem' }}
                  >
                    Live Updates
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      </ErrorBoundary>
    );
  }

  // Enhanced default and detailed variants
  return (
    <ErrorBoundary
      fallback={
        <DashboardCard
          title="Onboarding Progress"
          description="Error loading onboarding data"
          icon={ErrorIcon}
        >
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load onboarding progress
            </Typography>
          </Box>
        </DashboardCard>
      }
    >
      <Box
        ref={cardRef}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        onKeyDown={handleKeyDown}
      >
        <Card
          sx={{
            background: `linear-gradient(135deg,
              ${alpha(theme.palette.background.paper, 0.9)} 0%,
              ${alpha(theme.palette.background.paper, 0.7)} 100%)`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
            borderRadius: 2,
            overflow: 'hidden',
            transition: 'all 0.3s ease'
          }}
        >
          <CardHeader
            avatar={<RocketIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: 32 }} />}
            title={
              <Typography variant="h6" fontWeight={600} sx={{ color: ACE_COLORS.DARK }}>
                Getting Started
              </Typography>
            }
            subheader={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  {progressStats.completedSteps} of {progressStats.totalSteps} steps completed
                </Typography>
                <Chip
                  label={`${progressStats.progressPercentage}%`}
                  size="small"
                  sx={{
                    backgroundColor: ACE_COLORS.PURPLE,
                    color: ACE_COLORS.WHITE,
                    fontWeight: 600
                  }}
                />
                {progressStats.isComplete && (
                  <Chip
                    icon={<VerifiedIcon fontSize="small" />}
                    label="Complete"
                    size="small"
                    sx={{
                      backgroundColor: theme.palette.success.main,
                      color: ACE_COLORS.WHITE,
                      fontWeight: 600
                    }}
                  />
                )}
              </Box>
            }
            action={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {/* Tutorial Button */}
                {enableTutorials && (
                  <Tooltip title="Start Interactive Tutorial">
                    <IconButton
                      size="small"
                      onClick={() => handleTutorialStart('onboarding')}
                      sx={{
                        color: ACE_COLORS.YELLOW,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1)
                        }
                      }}
                      aria-label="Start interactive tutorial"
                    >
                      <TutorialIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                {/* Analytics Button */}
                {enableAnalytics && (
                  <Tooltip title="View Progress Analytics">
                    <IconButton
                      size="small"
                      onClick={handleAnalyticsRequest}
                      sx={{
                        color: ACE_COLORS.PURPLE,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                        }
                      }}
                      aria-label="View progress analytics"
                    >
                      <AnalyticsIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                {/* Export Button */}
                {enableExport && (
                  <Tooltip title="Export Progress Data">
                    <IconButton
                      size="small"
                      onClick={handleExportMenuOpen}
                      sx={{
                        color: theme.palette.text.secondary,
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.1)
                        }
                      }}
                      aria-label="Export progress data"
                    >
                      <ExportIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}

                {/* Refresh Button */}
                <Tooltip title="Refresh Progress">
                  <IconButton
                    size="small"
                    onClick={handleRefresh}
                    disabled={state.refreshing}
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.1)
                      }
                    }}
                    aria-label="Refresh onboarding progress"
                  >
                    {state.refreshing ? (
                      <CircularProgress size={16} />
                    ) : (
                      <RefreshIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>

                {/* Dismiss Button */}
                {showDismiss && (
                  <Button
                    size="small"
                    onClick={onDismiss}
                    sx={{
                      textTransform: 'none',
                      color: theme.palette.text.secondary
                    }}
                  >
                    Dismiss
                  </Button>
                )}

                {/* Expand/Collapse Button */}
                <IconButton
                  onClick={() => setState(prev => ({ ...prev, expanded: !prev.expanded }))}
                  sx={{
                    transform: state.expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.3s',
                    color: theme.palette.text.secondary
                  }}
                  aria-label={state.expanded ? 'Collapse details' : 'Expand details'}
                >
                  <ExpandMoreIcon />
                </IconButton>
              </Box>
            }
          />

          <CardContent sx={{ pt: 0 }}>
            {/* Enhanced Progress Bar */}
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Overall Progress
                </Typography>
                <Typography variant="body2" fontWeight={600} sx={{ color: ACE_COLORS.PURPLE }}>
                  {progressStats.progressPercentage}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={progressStats.progressPercentage}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    background: `linear-gradient(90deg,
                      ${ACE_COLORS.PURPLE} 0%,
                      ${ACE_COLORS.YELLOW} 100%)`
                  }
                }}
                aria-label={`Overall progress: ${progressStats.progressPercentage}% complete`}
              />
            </Box>

            {/* Progress Statistics */}
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight={700} sx={{ color: theme.palette.success.main }}>
                    {progressStats.completedSteps}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Completed
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight={700} sx={{ color: ACE_COLORS.YELLOW }}>
                    {progressStats.remainingSteps}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Remaining
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" fontWeight={700} sx={{ color: theme.palette.info.main }}>
                    {progressStats.skippedSteps}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Skipped
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            {/* Real-time Updates Indicator */}
            {realTimeUpdates && subscriptionFeatures.hasRealTimeTracking && (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                mb: 2,
                p: 1,
                backgroundColor: alpha(theme.palette.success.main, 0.1),
                borderRadius: 1,
                border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
              }}>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.success.main,
                    animation: 'pulse 2s infinite',
                    '@keyframes pulse': {
                      '0%': { opacity: 1 },
                      '50%': { opacity: 0.5 },
                      '100%': { opacity: 1 }
                    }
                  }}
                  aria-hidden="true"
                />
                <Typography variant="caption" color="text.secondary">
                  Live progress tracking enabled
                </Typography>
                {state.lastUpdated && (
                  <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}>
                    Updated: {new Date(state.lastUpdated).toLocaleTimeString()}
                  </Typography>
                )}
              </Box>
            )}

            {/* Enhanced Next Step Recommendation */}
            {nextStep && !state.expanded && (
              <Fade in={true} timeout={ANIMATION_CONFIG.DURATION}>
                <Box
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    backgroundColor: alpha(nextStep.color || ACE_COLORS.PURPLE, 0.1),
                    border: `1px solid ${alpha(nextStep.color || ACE_COLORS.PURPLE, 0.2)}`,
                    mb: 2,
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: 1,
                        backgroundColor: alpha(nextStep.color || ACE_COLORS.PURPLE, 0.2),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      {nextStep.icon && <nextStep.icon sx={{ color: nextStep.color || ACE_COLORS.PURPLE, fontSize: 20 }} />}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle2" sx={{ color: nextStep.color || ACE_COLORS.PURPLE, fontWeight: 600 }} gutterBottom>
                        Next Step
                      </Typography>
                      <Typography variant="body2" fontWeight={600} sx={{ mb: 0.5 }}>
                        {nextStep.title}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                        {nextStep.description}
                      </Typography>
                      {nextStep.estimatedTime && (
                        <Chip
                          label={nextStep.estimatedTime}
                          size="small"
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                            color: ACE_COLORS.DARK,
                            fontSize: '0.7rem',
                            height: 20,
                            mb: 1
                          }}
                        />
                      )}
                      <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        <Button
                          variant="contained"
                          size="small"
                          onClick={() => handleStepClick(nextStep.id)}
                          sx={{
                            textTransform: 'none',
                            backgroundColor: nextStep.color || ACE_COLORS.PURPLE,
                            '&:hover': {
                              backgroundColor: alpha(nextStep.color || ACE_COLORS.PURPLE, 0.8)
                            }
                          }}
                          aria-label={`Start ${nextStep.title}`}
                        >
                          Get Started
                        </Button>
                        {subscriptionFeatures.hasTutorials && (
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => handleTutorialStart(nextStep.id)}
                            sx={{
                              textTransform: 'none',
                              borderColor: nextStep.color || ACE_COLORS.PURPLE,
                              color: nextStep.color || ACE_COLORS.PURPLE
                            }}
                            startIcon={<TutorialIcon fontSize="small" />}
                            aria-label={`Start tutorial for ${nextStep.title}`}
                          >
                            Tutorial
                          </Button>
                        )}
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Fade>
            )}

            {/* Enhanced Detailed Steps List */}
            <Collapse in={state.expanded} timeout={ANIMATION_CONFIG.DURATION}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 2, color: ACE_COLORS.DARK }}>
                Onboarding Steps
              </Typography>

              <List dense>
                {processedSteps.map((step, index) => {
                  const IconComponent = step.icon || SettingsIcon;
                  const isNextStep = nextStep && nextStep.id === step.id;

                  return (
                    <Fade
                      in={true}
                      timeout={ANIMATION_CONFIG.DURATION + (index * ANIMATION_CONFIG.STAGGER_DELAY)}
                      key={step.id}
                    >
                      <ListItem disablePadding sx={{ mb: 1 }}>
                        <ListItemButton
                          onClick={() => !step.isCompleted && handleStepClick(step.id)}
                          disabled={step.isCompleted || !step.isAvailable}
                          sx={{
                            borderRadius: 2,
                            backgroundColor: step.isCompleted
                              ? alpha(theme.palette.success.main, 0.1)
                              : isNextStep
                              ? alpha(ACE_COLORS.YELLOW, 0.1)
                              : alpha(theme.palette.action.hover, 0.3),
                            border: isNextStep
                              ? `2px solid ${alpha(ACE_COLORS.YELLOW, 0.5)}`
                              : `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                            '&:hover': {
                              backgroundColor: step.isCompleted
                                ? alpha(theme.palette.success.main, 0.15)
                                : isNextStep
                                ? alpha(ACE_COLORS.YELLOW, 0.15)
                                : alpha(theme.palette.action.hover, 0.5)
                            },
                            '&:disabled': {
                              opacity: 0.6
                            }
                          }}
                          aria-label={`${step.isCompleted ? 'Completed' : 'Start'} ${step.title}`}
                        >
                          <ListItemIcon>
                            {step.isCompleted ? (
                              <CompleteIcon sx={{ color: theme.palette.success.main }} />
                            ) : step.isSkipped ? (
                              <RadioButtonCheckedIcon color="action" />
                            ) : (
                              <IncompleteIcon color="action" />
                            )}
                          </ListItemIcon>

                          <ListItemIcon>
                            <IconComponent
                              sx={{
                                color: step.isCompleted
                                  ? theme.palette.success.main
                                  : step.color || ACE_COLORS.PURPLE,
                                fontSize: 20
                              }}
                            />
                          </ListItemIcon>

                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography
                                  variant="body2"
                                  fontWeight={step.isCompleted ? 400 : 600}
                                  color={step.isCompleted ? 'text.secondary' : 'text.primary'}
                                >
                                  {step.title}
                                </Typography>
                                {isNextStep && (
                                  <Chip
                                    label="Next"
                                    size="small"
                                    sx={{
                                      backgroundColor: ACE_COLORS.YELLOW,
                                      color: ACE_COLORS.DARK,
                                      fontSize: '0.7rem',
                                      height: 18
                                    }}
                                  />
                                )}
                                {step.priority === 1 && (
                                  <StarIcon sx={{ color: ACE_COLORS.YELLOW, fontSize: 16 }} />
                                )}
                              </Box>
                            }
                            secondary={
                              variant === 'detailed' && (
                                <Box sx={{ mt: 0.5 }}>
                                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                    {step.description}
                                  </Typography>
                                  {(step.estimatedTime || step.difficulty) && (
                                    <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                                      {step.estimatedTime && (
                                        <Chip
                                          label={step.estimatedTime}
                                          size="small"
                                          sx={{
                                            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                                            color: ACE_COLORS.PURPLE,
                                            fontSize: '0.65rem',
                                            height: 16
                                          }}
                                        />
                                      )}
                                      {step.difficulty && (
                                        <Chip
                                          label={step.difficulty}
                                          size="small"
                                          sx={{
                                            backgroundColor: alpha(theme.palette.info.main, 0.1),
                                            color: theme.palette.info.main,
                                            fontSize: '0.65rem',
                                            height: 16
                                          }}
                                        />
                                      )}
                                    </Box>
                                  )}
                                </Box>
                              )
                            }
                          />

                          <ListItemSecondaryAction>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {step.isCompleted && step.completedAt && (
                                <Tooltip title={`Completed: ${new Date(step.completedAt).toLocaleDateString()}`}>
                                  <CheckCircleOutlineIcon
                                    sx={{
                                      color: theme.palette.success.main,
                                      fontSize: 16
                                    }}
                                  />
                                </Tooltip>
                              )}

                              {!step.isCompleted && !step.isSkipped && step.isAvailable && (
                                <Box sx={{ display: 'flex', gap: 0.5 }}>
                                  {subscriptionFeatures.hasTutorials && (
                                    <Tooltip title="Start Tutorial">
                                      <IconButton
                                        size="small"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleTutorialStart(step.id);
                                        }}
                                        sx={{
                                          color: ACE_COLORS.YELLOW,
                                          '&:hover': {
                                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1)
                                          }
                                        }}
                                      >
                                        <TutorialIcon fontSize="small" />
                                      </IconButton>
                                    </Tooltip>
                                  )}

                                  <Chip
                                    label="Start"
                                    size="small"
                                    sx={{
                                      backgroundColor: step.color || ACE_COLORS.PURPLE,
                                      color: ACE_COLORS.WHITE,
                                      fontSize: '0.7rem',
                                      height: 20,
                                      '&:hover': {
                                        backgroundColor: alpha(step.color || ACE_COLORS.PURPLE, 0.8)
                                      }
                                    }}
                                    clickable
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleStepClick(step.id);
                                    }}
                                  />
                                </Box>
                              )}
                            </Box>
                          </ListItemSecondaryAction>
                        </ListItemButton>
                      </ListItem>
                    </Fade>
                  );
                })}
              </List>

              {/* Enhanced Completion Message */}
              {progressStats.isComplete && (
                <Fade in={true} timeout={ANIMATION_CONFIG.DURATION}>
                  <Box
                    sx={{
                      p: 3,
                      mt: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.success.main, 0.1),
                      border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`,
                      textAlign: 'center',
                      position: 'relative',
                      overflow: 'hidden'
                    }}
                  >
                    <CompleteIcon sx={{ color: theme.palette.success.main, fontSize: 48, mb: 1 }} />
                    <Typography variant="h6" sx={{ color: theme.palette.success.main, fontWeight: 700 }} gutterBottom>
                      🎉 Setup Complete!
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Congratulations! You&apos;ve completed all onboarding steps. You&apos;re ready to create amazing content and track your performance.
                    </Typography>

                    {subscriptionFeatures.hasAnalytics && (
                      <Button
                        variant="contained"
                        onClick={handleAnalyticsRequest}
                        sx={{
                          backgroundColor: theme.palette.success.main,
                          color: ACE_COLORS.WHITE,
                          textTransform: 'none',
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.success.main, 0.8)
                          }
                        }}
                        startIcon={<AnalyticsIcon />}
                      >
                        View Progress Analytics
                      </Button>
                    )}
                  </Box>
                </Fade>
              )}
            </Collapse>
          </CardContent>
        </Card>

        {/* Export Menu */}
        <Menu
          anchorEl={exportAnchorEl}
          open={state.showExportMenu}
          onClose={handleExportMenuClose}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 150,
              boxShadow: theme.shadows[8],
              border: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          <MenuItem onClick={() => {
            handleExport('csv');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as CSV</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => {
            handleExport('json');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as JSON</ListItemText>
          </MenuItem>

          {subscriptionFeatures.hasAnalytics && (
            <MenuItem onClick={() => {
              handleExport('pdf');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export Report (PDF)</ListItemText>
            </MenuItem>
          )}
        </Menu>

        {/* Tutorial Dialog */}
        <Dialog
          open={state.showTutorialDialog}
          onClose={() => setState(prev => ({ ...prev, showTutorialDialog: false }))}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: theme.shadows[16]
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TutorialIcon sx={{ color: ACE_COLORS.YELLOW }} />
              <Typography variant="h6" component="span">
                Interactive Tutorial
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Start an interactive tutorial for {state.selectedStep ? processedSteps.find(s => s.id === state.selectedStep)?.title : 'onboarding'}.
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              This tutorial will guide you through the step-by-step process with interactive elements and helpful tips.
            </Alert>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={() => setState(prev => ({ ...prev, showTutorialDialog: false }))}
              sx={{ color: theme.palette.text.secondary }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: ACE_COLORS.YELLOW,
                color: ACE_COLORS.DARK,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.YELLOW, 0.8),
                }
              }}
              startIcon={<PlayIcon />}
            >
              Start Tutorial
            </Button>
          </DialogActions>
        </Dialog>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={handleUpgradeDialogClose}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: theme.shadows[16]
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span">
                {getUpgradeDialogContent().title}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {getUpgradeDialogContent().content}
              </Typography>

              <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                Unlock Advanced Onboarding Features:
              </Typography>

              <Grid container spacing={1}>
                {getUpgradeDialogContent().features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleOutlineIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                      <Typography variant="body2">{feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Alert
              severity="info"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
              Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced onboarding features.
            </Alert>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleUpgradeDialogClose}
              sx={{ color: theme.palette.text.secondary }}
            >
              Maybe Later
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                }
              }}
              startIcon={<UpgradeIcon />}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
OnboardingProgressCard.propTypes = {
  // Core props
  userProgress: PropTypes.shape({
    steps: PropTypes.object.isRequired,
    completedSteps: PropTypes.number.isRequired,
    totalSteps: PropTypes.number.isRequired,
    progressPercentage: PropTypes.number.isRequired
  }).isRequired,
  onNavigateToStep: PropTypes.func,
  onDismiss: PropTypes.func,

  // Enhanced props
  variant: PropTypes.oneOf(['compact', 'detailed', 'setup-progress', 'feature-discovery', 'integration-progress', 'content-creation-milestones']),
  showDismiss: PropTypes.bool,
  autoCollapse: PropTypes.bool,
  enableTutorials: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,

  onProgressUpdate: PropTypes.func,
  onTutorialStart: PropTypes.func,
  onAnalyticsRequest: PropTypes.func,
  onExport: PropTypes.func,
  customSteps: PropTypes.object,
  customization: PropTypes.object,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

OnboardingProgressCard.defaultProps = {
  variant: 'detailed',
  showDismiss: true,
  autoCollapse: false,
  enableTutorials: false,
  enableAnalytics: false,
  enableExport: false,
  realTimeUpdates: false,
  customSteps: {},
  customization: {},
  className: '',
  style: {},
  testId: 'onboarding-progress-card'
};

// Display name for debugging
OnboardingProgressCard.displayName = 'OnboardingProgressCard';

export default OnboardingProgressCard;

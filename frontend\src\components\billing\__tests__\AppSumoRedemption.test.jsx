/**
 * Tests for AppSumoRedemption component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AppSumoRedemption from '../AppSumoRedemption';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    post: vi.fn()
  }
}));

// Mock hooks
vi.mock('../../../hooks/useSubscription', () => ({
  useSubscription: vi.fn(() => ({
    subscription: { is_appsumo_lifetime: false },
    refreshSubscription: vi.fn()
  }))
}));

vi.mock('../../../hooks/useAdvancedToast', () => ({
  useAdvancedToast: vi.fn(() => ({
    showSuccess: vi.fn(),
    showError: vi.fn()
  }))
}));

describe('AppSumoRedemption', () => {
  const mockProps = {
    onRedemptionSuccess: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders AppSumo redemption component', () => {
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('AppSumo Lifetime Deal Redemption')).toBeInTheDocument();
    expect(screen.getByText('Enter Code')).toBeInTheDocument();
    expect(screen.getByText('Verify & Confirm')).toBeInTheDocument();
    expect(screen.getByText('Complete')).toBeInTheDocument();
  });

  test('shows stepper by default', () => {
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Enter Code')).toBeInTheDocument();
    expect(screen.getByText('Verify & Confirm')).toBeInTheDocument();
    expect(screen.getByText('Complete')).toBeInTheDocument();
  });

  test('hides stepper when showStepper is false', () => {
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} showStepper={false} />
      </TestWrapper>
    );

    // Stepper steps should not be visible
    expect(screen.queryByText('Enter Code')).not.toBeInTheDocument();
    expect(screen.queryByText('Verify & Confirm')).not.toBeInTheDocument();
    expect(screen.queryByText('Complete')).not.toBeInTheDocument();
  });

  test('uses initial code when provided', () => {
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} initialCode="AS-TEST-12345" />
      </TestWrapper>
    );

    const codeInput = screen.getByLabelText('AppSumo Code');
    expect(codeInput).toHaveValue('AS-TEST-12345');
  });

  test('handles code verification successfully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.post.mockResolvedValue({
      data: {
        is_valid: true,
        tier: 'tier2',
        features: ['Feature 1', 'Feature 2']
      }
    });
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    const codeInput = screen.getByLabelText('AppSumo Code');
    const verifyButton = screen.getByText('Verify Code');

    await user.type(codeInput, 'AS-TEST-12345');
    await user.click(verifyButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/billing/appsumo/verify', {
        code: 'AS-TEST-12345'
      });
    });

    expect(screen.getByText('Code Verified Successfully!')).toBeInTheDocument();
  });

  test('handles code verification failure', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.post.mockResolvedValue({
      data: {
        is_valid: false,
        message: 'Invalid code'
      }
    });
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    const codeInput = screen.getByLabelText('AppSumo Code');
    const verifyButton = screen.getByText('Verify Code');

    await user.type(codeInput, 'INVALID-CODE');
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('Invalid code')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('Invalid code');
  });

  test('validates empty code input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    const verifyButton = screen.getByText('Verify Code');
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter an AppSumo code')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('Please enter an AppSumo code');
  });

  test('shows loading state during verification', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.post.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    const codeInput = screen.getByLabelText('AppSumo Code');
    const verifyButton = screen.getByText('Verify Code');

    await user.type(codeInput, 'AS-TEST-12345');
    await user.click(verifyButton);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(verifyButton).toBeDisabled();
  });

  test('handles redemption flow', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    const { useSubscription } = await import('../../../hooks/useSubscription');
    const { useAdvancedToast } = await import('../../../hooks/useAdvancedToast');
    
    const mockRefreshSubscription = vi.fn();
    const mockShowSuccess = vi.fn();
    
    useSubscription.mockReturnValue({
      subscription: { is_appsumo_lifetime: false },
      refreshSubscription: mockRefreshSubscription
    });
    
    useAdvancedToast.mockReturnValue({
      showSuccess: mockShowSuccess,
      showError: vi.fn()
    });

    // Mock verification success
    api.default.post.mockImplementation((url) => {
      if (url.includes('verify')) {
        return Promise.resolve({
          data: {
            is_valid: true,
            tier: 'tier2',
            features: ['Feature 1', 'Feature 2']
          }
        });
      }
      if (url.includes('redeem')) {
        return Promise.resolve({
          data: {
            success: true,
            message: 'Code redeemed successfully!'
          }
        });
      }
    });
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    // Step 1: Verify code
    const codeInput = screen.getByLabelText('AppSumo Code');
    const verifyButton = screen.getByText('Verify Code');

    await user.type(codeInput, 'AS-TEST-12345');
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('Code Verified Successfully!')).toBeInTheDocument();
    });

    // Step 2: Confirm redemption
    const confirmButton = screen.getByText('Confirm Redemption');
    await user.click(confirmButton);

    // Should show confirmation dialog
    await waitFor(() => {
      expect(screen.getByText('Confirm AppSumo Code Redemption')).toBeInTheDocument();
    });

    const proceedButton = screen.getByText('Yes, Redeem Code');
    await user.click(proceedButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/billing/appsumo/redeem', {
        code: 'AS-TEST-12345'
      });
    });

    expect(mockRefreshSubscription).toHaveBeenCalled();
    expect(mockProps.onRedemptionSuccess).toHaveBeenCalled();
  });

  test('shows existing AppSumo subscription warning', () => {
    const { useSubscription } = require('../../../hooks/useSubscription');
    useSubscription.mockReturnValue({
      subscription: { is_appsumo_lifetime: true },
      refreshSubscription: vi.fn()
    });
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('You already have an AppSumo lifetime subscription. You can use additional codes for tier upgrades.')).toBeInTheDocument();
  });

  test('handles network errors gracefully', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.post.mockRejectedValue({
      response: {
        data: {
          detail: 'Network error'
        }
      }
    });
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    const codeInput = screen.getByLabelText('AppSumo Code');
    const verifyButton = screen.getByText('Verify Code');

    await user.type(codeInput, 'AS-TEST-12345');
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('Network error')).toBeInTheDocument();
    });

    expect(mockProps.onError).toHaveBeenCalledWith('Network error');
  });

  test('formats code input to uppercase', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.post.mockResolvedValue({
      data: { is_valid: true }
    });
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    const codeInput = screen.getByLabelText('AppSumo Code');
    const verifyButton = screen.getByText('Verify Code');

    await user.type(codeInput, 'as-test-12345');
    await user.click(verifyButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/billing/appsumo/verify', {
        code: 'AS-TEST-12345'
      });
    });
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    const codeInput = screen.getByLabelText('AppSumo Code');
    expect(codeInput).toHaveAttribute('aria-required', 'true');

    const verifyButton = screen.getByText('Verify Code');
    expect(verifyButton).toHaveAttribute('type', 'button');
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <AppSumoRedemption 
          {...mockProps} 
          data-testid="test-redemption"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-redemption');
    expect(component).toHaveClass('custom-class');
  });

  test('handles confirmation dialog cancellation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.post.mockResolvedValue({
      data: {
        is_valid: true,
        tier: 'tier2',
        features: ['Feature 1', 'Feature 2']
      }
    });
    
    render(
      <TestWrapper>
        <AppSumoRedemption {...mockProps} />
      </TestWrapper>
    );

    // Verify code first
    const codeInput = screen.getByLabelText('AppSumo Code');
    const verifyButton = screen.getByText('Verify Code');

    await user.type(codeInput, 'AS-TEST-12345');
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText('Code Verified Successfully!')).toBeInTheDocument();
    });

    // Click confirm redemption
    const confirmButton = screen.getByText('Confirm Redemption');
    await user.click(confirmButton);

    // Should show confirmation dialog
    await waitFor(() => {
      expect(screen.getByText('Confirm AppSumo Code Redemption')).toBeInTheDocument();
    });

    // Cancel the dialog
    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    // Dialog should be closed
    await waitFor(() => {
      expect(screen.queryByText('Confirm AppSumo Code Redemption')).not.toBeInTheDocument();
    });
  });
});

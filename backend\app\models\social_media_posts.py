"""
MongoDB models for social media posts and AI response management.
"""
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, ConfigDict

from app.models.user import PyObjectId


class SocialMediaPost(BaseModel):
    """MongoDB model for social media posts with AI response capabilities."""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User who owns this post")
    
    # Platform and post identification
    platform: str = Field(..., description="Social media platform (linkedin, facebook, twitter, etc.)")
    post_id: str = Field(..., description="Platform-specific post ID")
    platform_post_url: Optional[str] = Field(None, description="Direct URL to the post on the platform")
    
    # Post content
    content_text: str = Field(..., description="Post content text")
    content_type: str = Field(default="text", description="Type of content (text, image, video, carousel)")
    author_name: Optional[str] = Field(None, description="Post author name")
    author_id: Optional[str] = Field(None, description="Platform-specific author ID")
    
    # Timestamps
    published_at: Optional[datetime] = Field(None, description="Post publication timestamp")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_comment_at: Optional[datetime] = Field(None, description="Timestamp of most recent comment")
    
    # Engagement metrics
    engagement_metrics: Dict[str, int] = Field(default_factory=dict, description="Likes, comments, shares, etc.")
    total_comments_count: int = Field(0, description="Total number of comments on the post")
    pending_comments_count: int = Field(0, description="Number of comments awaiting AI responses")
    
    # AI response management
    ai_response_enabled: bool = Field(True, description="Whether AI responses are enabled for this post")
    auto_approve_responses: bool = Field(False, description="Whether to auto-approve AI responses")
    response_tone: Optional[str] = Field(None, description="Preferred tone for AI responses")
    response_guidelines: Optional[str] = Field(None, description="Custom guidelines for AI responses")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional platform-specific metadata")
    tags: List[str] = Field(default_factory=list, description="User-defined tags for organization")
    
    # Status tracking
    status: str = Field(default="active", description="Post status (active, archived, deleted)")
    sync_status: str = Field(default="synced", description="Sync status with platform (synced, pending, failed)")
    last_sync_at: Optional[datetime] = Field(None, description="Last successful sync with platform")
    
    model_config = ConfigDict(
        validate_assignment=True,
        arbitrary_types_allowed=True,
        json_schema_extra={
            "example": {
                "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
                "user_id": "64f8a1b2c3d4e5f6a7b8c9d1",
                "platform": "linkedin",
                "post_id": "urn:li:activity:7123456789",
                "platform_post_url": "https://www.linkedin.com/posts/activity-7123456789",
                "content_text": "Excited to share our latest insights on digital marketing trends...",
                "content_type": "text",
                "author_name": "ACE Social",
                "author_id": "ace-social-linkedin",
                "published_at": "2024-01-15T10:30:00Z",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "last_comment_at": "2024-01-15T14:22:00Z",
                "engagement_metrics": {"likes": 45, "comments": 12, "shares": 8, "views": 1250},
                "total_comments_count": 12,
                "pending_comments_count": 3,
                "ai_response_enabled": True,
                "auto_approve_responses": False,
                "response_tone": "professional",
                "response_guidelines": "Always be helpful and provide actionable insights",
                "metadata": {
                    "linkedin_activity_id": "7123456789",
                    "post_type": "article_share"
                },
                "tags": ["marketing", "insights", "trends"],
                "status": "active",
                "sync_status": "synced",
                "last_sync_at": "2024-01-15T10:30:00Z"
            }
        }
    )


class SocialMediaComment(BaseModel):
    """MongoDB model for social media comments (for reference/caching)."""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User who owns the parent post")
    post_id: PyObjectId = Field(..., description="Reference to SocialMediaPost")
    
    # Platform and comment identification
    platform: str = Field(..., description="Social media platform")
    comment_id: str = Field(..., description="Platform-specific comment ID")
    platform_comment_url: Optional[str] = Field(None, description="Direct URL to the comment")
    parent_comment_id: Optional[str] = Field(None, description="Parent comment ID for replies")
    
    # Comment content
    comment_text: str = Field(..., description="Original comment text")
    comment_author: str = Field(..., description="Comment author name/handle")
    comment_author_id: Optional[str] = Field(None, description="Platform-specific author ID")
    
    # Timestamps
    created_at: datetime = Field(..., description="Comment creation timestamp")
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Analysis
    sentiment: Optional[str] = Field(None, description="Comment sentiment (positive, negative, neutral)")
    sentiment_score: Optional[float] = Field(None, description="Sentiment confidence score (0.0-1.0)")
    language: Optional[str] = Field(None, description="Detected language of the comment")
    
    # Engagement
    likes_count: int = Field(0, description="Number of likes on the comment")
    replies_count: int = Field(0, description="Number of replies to this comment")
    
    # AI response tracking
    has_ai_response: bool = Field(False, description="Whether an AI response has been generated")
    ai_response_id: Optional[PyObjectId] = Field(None, description="Reference to CommentResponse")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional platform-specific metadata")
    
    # Status
    status: str = Field(default="active", description="Comment status (active, deleted, hidden)")
    
    model_config = ConfigDict(
        validate_assignment=True,
        arbitrary_types_allowed=True,
        json_schema_extra={
            "example": {
                "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
                "user_id": "64f8a1b2c3d4e5f6a7b8c9d1",
                "post_id": "64f8a1b2c3d4e5f6a7b8c9d0",
                "platform": "linkedin",
                "comment_id": "comment_123456",
                "platform_comment_url": "https://www.linkedin.com/posts/activity-7123456789#comment-123456",
                "parent_comment_id": None,
                "comment_text": "Great insights! How do you implement this in practice?",
                "comment_author": "John Doe",
                "comment_author_id": "john_doe_linkedin",
                "created_at": "2024-01-15T12:30:00Z",
                "updated_at": "2024-01-15T12:30:00Z",
                "sentiment": "positive",
                "sentiment_score": 0.85,
                "language": "en",
                "likes_count": 3,
                "replies_count": 1,
                "has_ai_response": True,
                "ai_response_id": "64f8a1b2c3d4e5f6a7b8c9d3",
                "metadata": {
                    "linkedin_comment_id": "comment_123456",
                    "thread_depth": 0
                },
                "status": "active"
            }
        }
    )

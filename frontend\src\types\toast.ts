/**
 * Toast Notification System Types
 * Production-ready type definitions for the enhanced toast notification system
 */

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */

import React from 'react';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export type ToastPosition = 
  | 'top-left' 
  | 'top-center' 
  | 'top-right' 
  | 'bottom-left' 
  | 'bottom-center' 
  | 'bottom-right';

export type ToastPriority = 'low' | 'normal' | 'high' | 'critical';

export interface ToastAction {
  label: string;
  onClick: () => void;
  variant?: 'text' | 'outlined' | 'contained';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
}

export interface ToastConfig {
  id?: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
  position?: ToastPosition;
  priority?: ToastPriority;
  persistent?: boolean;
  dismissible?: boolean;
  actions?: ToastAction[];
  onClose?: () => void;
  onAction?: () => void;
  onUndo?: () => void;
  metadata?: Record<string, string | number | boolean | Date | null>;
  timestamp?: Date;
  retryCount?: number;
  maxRetries?: number;
}

export interface ToastState {
  id: string;
  config: ToastConfig;
  isVisible: boolean;
  isExiting: boolean;
  createdAt: Date;
  updatedAt: Date;
  position: { x: number; y: number };
  zIndex: number;
}

export interface ToastQueueOptions {
  maxVisible?: number;
  maxQueue?: number;
  stackSpacing?: number;
  animationDuration?: number;
  enableStacking?: boolean;
  enablePersistence?: boolean;
  persistenceKey?: string;
}

export interface ToastContextValue {
  // State
  toasts: ToastState[];
  queue: ToastConfig[];
  isLoading: boolean;
  error: string | null;

  // Configuration
  options: ToastQueueOptions;

  // Actions with comprehensive functionality
  showToast: (config: Omit<ToastConfig, 'id'> & {
    analytics?: {
      event: string;
      properties?: Record<string, string | number | boolean>;
    };
    accessibility?: {
      ariaLabel?: string;
      role?: 'alert' | 'status' | 'log';
      announceToScreenReader?: boolean;
    };
  }) => string;

  showSuccess: (
    message: string,
    options?: Partial<ToastConfig> & {
      successMetrics?: {
        action: string;
        duration?: number;
        userContext?: string;
      };
      celebration?: {
        confetti?: boolean;
        sound?: boolean;
        animation?: 'bounce' | 'slide' | 'fade';
      };
    }
  ) => string;

  showError: (
    message: string,
    options?: Partial<ToastConfig> & {
      errorContext?: {
        code?: string;
        source?: string;
        userAction?: string;
        timestamp?: Date;
      };
      recovery?: {
        retryAction?: () => void;
        fallbackAction?: () => void;
        supportLink?: string;
      };
    }
  ) => string;

  showWarning: (
    message: string,
    options?: Partial<ToastConfig> & {
      warningContext?: {
        severity: 'low' | 'medium' | 'high';
        category: string;
        preventable?: boolean;
      };
      preventiveActions?: {
        primaryAction?: () => void;
        secondaryAction?: () => void;
        learnMoreUrl?: string;
      };
    }
  ) => string;

  showInfo: (
    message: string,
    options?: Partial<ToastConfig> & {
      infoContext?: {
        category: 'tip' | 'update' | 'feature' | 'announcement';
        priority: 'low' | 'medium' | 'high';
        dismissible?: boolean;
      };
      engagement?: {
        trackViews?: boolean;
        trackDismissal?: boolean;
        followUpAction?: () => void;
      };
    }
  ) => string;

  dismissToast: (
    id: string,
    reason?: 'user' | 'timeout' | 'programmatic' | 'error',
    analytics?: {
      trackDismissal?: boolean;
      userEngagement?: 'positive' | 'negative' | 'neutral';
    }
  ) => void;

  dismissAll: (
    reason?: 'user' | 'navigation' | 'error' | 'cleanup',
    preservePersistent?: boolean
  ) => void;

  updateToast: (
    id: string,
    updates: Partial<ToastConfig> & {
      updateReason?: string;
      preservePosition?: boolean;
      animateUpdate?: boolean;
      notifyScreenReader?: boolean;
    }
  ) => void;
  
  // Queue management
  pauseQueue: () => void;
  resumeQueue: () => void;
  clearQueue: () => void;
  
  // Persistence
  savePersistentToasts: () => void;
  loadPersistentToasts: () => void;
  clearPersistentToasts: () => void;
}

export interface ToastProviderProps {
  children: React.ReactNode;
  options?: Partial<ToastQueueOptions>;
  position?: ToastPosition;
  maxVisible?: number;
}

// Error handling types
export interface ToastError {
  code: string;
  message: string;
  details?: {
    statusCode?: number;
    endpoint?: string;
    method?: string;
    requestId?: string;
    userAgent?: string;
    timestamp?: Date;
    stackTrace?: string;
    context?: Record<string, string | number | boolean>;
  };
  timestamp: Date;
  retryable: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'network' | 'validation' | 'permission' | 'system' | 'user';
}

export interface NetworkFailureConfig {
  enableRetry: boolean;
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff: boolean;
  onRetry?: (
    attempt: number,
    context: {
      error: ToastError;
      totalAttempts: number;
      nextRetryDelay: number;
      userInitiated: boolean;
    }
  ) => void;
  onMaxRetriesReached?: (
    error: ToastError,
    context: {
      totalAttempts: number;
      totalDuration: number;
      lastAttemptTimestamp: Date;
      fallbackOptions?: {
        offlineMode?: boolean;
        cachedData?: boolean;
        alternativeEndpoint?: string;
      };
    }
  ) => void;
  retryCondition?: (error: ToastError, attempt: number) => boolean;
  customRetryDelay?: (attempt: number, baseDelay: number) => number;
}

// Accessibility types
export interface AccessibilityConfig {
  announceToScreenReader: boolean;
  focusOnShow: boolean;
  enableKeyboardNavigation: boolean;
  ariaLive: 'polite' | 'assertive' | 'off';
  ariaAtomic: boolean;
}

// Animation types
export interface AnimationConfig {
  enter: 'slide' | 'fade' | 'scale' | 'bounce';
  exit: 'slide' | 'fade' | 'scale' | 'shrink';
  duration: number;
  easing: string;
  stagger: number;
}

// Theme integration
export interface ToastTheme {
  borderRadius: number;
  spacing: number;
  shadows: {
    default: string;
    hover: string;
  };
  colors: {
    success: string;
    error: string;
    warning: string;
    info: string;
  };
  typography: {
    title: {
      fontSize: string;
      fontWeight: string | number;
      lineHeight: string | number;
      fontFamily?: string;
      letterSpacing?: string;
      textTransform?: 'none' | 'uppercase' | 'lowercase' | 'capitalize';
    };
    message: {
      fontSize: string;
      fontWeight: string | number;
      lineHeight: string | number;
      fontFamily?: string;
      letterSpacing?: string;
      opacity?: number;
    };
  };
}

import { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Extension as ExtensionIcon,
  ShoppingCart as ShoppingCartIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useAddons } from '../../hooks/useAddons';
import { useAuth } from '../../hooks/useAuth';
import UsageIndicator from './UsageIndicator';

const UpgradeModal = ({ 
  open, 
  onClose, 
  addon = null, 
  feature = null,
  usageType = null,
  onPurchase,
  context = "upgrade"
}) => {
  useAuth(); // Ensure user is authenticated
  const {
    getRelevantAddons,
    getEnhancedLimits,
    userAddons,
    loading
  } = useAddons();

  const [relevantAddons, setRelevantAddons] = useState([]);
  const [currentLimits, setCurrentLimits] = useState(null);
  const [selectedVariant, setSelectedVariant] = useState('basic');
  const [purchasing, setPurchasing] = useState(false);

  const loadRelevantAddons = useCallback(async () => {
    try {
      if (addon) {
        setRelevantAddons([addon]);
      } else if (usageType) {
        const addons = await getRelevantAddons(usageType);
        setRelevantAddons(addons);
      }
    } catch (error) {
      console.error('Error loading relevant add-ons:', error);
    }
  }, [addon, usageType, getRelevantAddons]);

  const loadCurrentLimits = useCallback(async () => {
    try {
      if (usageType) {
        const limits = await getEnhancedLimits(usageType);
        setCurrentLimits(limits);
      }
    } catch (error) {
      console.error('Error loading current limits:', error);
    }
  }, [usageType, getEnhancedLimits]);

  useEffect(() => {
    if (open && (usageType || addon)) {
      loadRelevantAddons();
      loadCurrentLimits();
    }
  }, [open, usageType, addon, loadRelevantAddons, loadCurrentLimits]);

  const handlePurchase = async (addonId, variant = 'basic') => {
    try {
      setPurchasing(true);
      await onPurchase(addonId, variant);
      onClose();
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setPurchasing(false);
    }
  };

  const getUserAddonStatus = (addonId) => {
    return userAddons.find(ua => ua.addon_id === addonId);
  };

  const renderAddonCard = (addon) => {
    const userAddon = getUserAddonStatus(addon.id);
    const isActive = userAddon?.status === 'active';
    const variants = addon.pricing ? Object.keys(addon.pricing) : ['basic'];

    return (
      <Card key={addon.id} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ExtensionIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" component="h3">
              {addon.name}
            </Typography>
            {addon.is_popular && (
              <Chip label="Popular" color="primary" size="small" sx={{ ml: 1 }} />
            )}
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {addon.description}
          </Typography>

          {/* Current usage if user owns this add-on */}
          {isActive && (
            <Box sx={{ mb: 2 }}>
              <UsageIndicator
                current={userAddon.credits_used}
                total={userAddon.total_credits}
                label="Current Usage"
                size="small"
                showAlert={false}
              />
            </Box>
          )}

          {/* Features */}
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            What you get:
          </Typography>
          <List dense>
            {addon.features?.slice(0, 4).map((feature, index) => (
              <ListItem key={index} sx={{ py: 0.25 }}>
                <ListItemIcon sx={{ minWidth: 20 }}>
                  <CheckCircleIcon color="success" fontSize="small" />
                </ListItemIcon>
                <ListItemText 
                  primary={feature} 
                  primaryTypographyProps={{ variant: 'body2' }}
                />
              </ListItem>
            ))}
          </List>

          {/* Pricing variants */}
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Choose your plan:
            </Typography>
            <Grid container spacing={1}>
              {variants.map((variant) => {
                const pricing = addon.pricing?.[variant];
                if (!pricing) return null;

                return (
                  <Grid item xs={12} key={variant}>
                    <Card 
                      variant="outlined" 
                      sx={{ 
                        cursor: 'pointer',
                        border: selectedVariant === variant ? 2 : 1,
                        borderColor: selectedVariant === variant ? 'primary.main' : 'divider',
                        '&:hover': {
                          borderColor: 'primary.main'
                        }
                      }}
                      onClick={() => setSelectedVariant(variant)}
                    >
                      <CardContent sx={{ py: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Box>
                            <Typography variant="subtitle2" sx={{ textTransform: 'capitalize' }}>
                              {variant}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {pricing.credits} credits
                            </Typography>
                          </Box>
                          <Typography variant="h6" color="primary.main">
                            ${pricing.price}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                );
              })}
            </Grid>
          </Box>
        </CardContent>

        <CardActions sx={{ p: 2, pt: 0 }}>
          {isActive ? (
            <Button
              fullWidth
              variant="outlined"
              startIcon={<InfoIcon />}
              disabled
            >
              Already Owned
            </Button>
          ) : (
            <Button
              fullWidth
              variant="contained"
              startIcon={<ShoppingCartIcon />}
              onClick={() => handlePurchase(addon.id, selectedVariant)}
              disabled={purchasing}
            >
              {purchasing ? <CircularProgress size={20} /> : `Purchase ${selectedVariant}`}
            </Button>
          )}
        </CardActions>
      </Card>
    );
  };

  const renderCurrentUsage = () => {
    if (!currentLimits) return null;

    return (
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="subtitle2" sx={{ mb: 1 }}>
          Your Current Usage
        </Typography>
        <UsageIndicator
          current={currentLimits.total_limit - currentLimits.remaining}
          total={currentLimits.total_limit}
          label="Current Plan Limits"
          size="small"
          showAlert={false}
        />
        <Typography variant="body2" sx={{ mt: 1 }}>
          Base limit: {currentLimits.base_limit} | 
          Add-on bonus: {currentLimits.addon_bonus} | 
          Total: {currentLimits.total_limit}
        </Typography>
      </Alert>
    );
  };

  const getModalTitle = () => {
    if (addon) return `Upgrade ${addon.name}`;
    if (feature) return `Upgrade ${feature}`;
    return 'Upgrade Your Plan';
  };

  const getModalSubtitle = () => {
    if (context === "limit_reached") {
      return "You've reached your current limit. Upgrade to continue using this feature.";
    }
    if (context === "feature_locked") {
      return "This feature requires an add-on to unlock.";
    }
    return "Choose an add-on to enhance your ACEO experience.";
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '60vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h5" component="h2">
              {getModalTitle()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {getModalSubtitle()}
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Current usage display */}
        {renderCurrentUsage()}

        {/* Loading state */}
        {loading && relevantAddons.length === 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {/* Add-on options */}
        {relevantAddons.length > 0 && (
          <Grid container spacing={2}>
            {relevantAddons.map(addon => (
              <Grid item xs={12} md={relevantAddons.length === 1 ? 12 : 6} key={addon.id}>
                {renderAddonCard(addon)}
              </Grid>
            ))}
          </Grid>
        )}

        {/* No add-ons available */}
        {!loading && relevantAddons.length === 0 && (
          <Alert severity="info">
            No relevant add-ons found for this feature. Please check back later or contact support.
          </Alert>
        )}

        {/* Benefits section */}
        <Box sx={{ mt: 3 }}>
          <Divider sx={{ mb: 2 }} />
          <Typography variant="h6" sx={{ mb: 2 }}>
            Why upgrade?
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <TrendingUpIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="subtitle2">Boost Productivity</Typography>
                <Typography variant="body2" color="text.secondary">
                  Get more done with enhanced limits
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <StarIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="subtitle2">Premium Features</Typography>
                <Typography variant="body2" color="text.secondary">
                  Access advanced capabilities
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ textAlign: 'center' }}>
                <CheckCircleIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="subtitle2">Instant Activation</Typography>
                <Typography variant="body2" color="text.secondary">
                  Start using immediately after purchase
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button onClick={onClose} color="inherit">
          Maybe Later
        </Button>
        <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1, textAlign: 'center' }}>
          30-day money-back guarantee
        </Typography>
      </DialogActions>
    </Dialog>
  );
};

// PropTypes for type checking
UpgradeModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  addon: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    features: PropTypes.arrayOf(PropTypes.string),
    pricing: PropTypes.object,
    is_popular: PropTypes.bool
  }),
  feature: PropTypes.string,
  usageType: PropTypes.string,
  onPurchase: PropTypes.func.isRequired,
  context: PropTypes.oneOf(['upgrade', 'limit_reached', 'feature_locked'])
};

// Default props
UpgradeModal.defaultProps = {
  addon: null,
  feature: null,
  usageType: null,
  context: 'upgrade'
};

export default UpgradeModal;

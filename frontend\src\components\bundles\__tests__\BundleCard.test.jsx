import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import BundleCard from '../BundleCard';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      success: {
        main: '#4CAF50',
      },
      warning: {
        main: '#FF9800',
      },
      info: {
        main: '#2196F3',
      },
      error: {
        main: '#F44336',
      },
      background: {
        default: '#FFFFFF',
        paper: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('BundleCard', () => {
  const mockBundle = {
    id: 'bundle-1',
    name: 'Content Creator Bundle',
    description: 'Perfect for content creators and social media managers',
    bundle_type: 'content_optimization',
    price_monthly: 29.99,
    price_yearly: 299.99,
    original_price: 49.99,
    savings_percentage: 40,
    features: [
      'AI Content Generation',
      'Advanced Analytics',
      'Team Collaboration',
      'Priority Support',
      'Custom Branding',
      'API Access'
    ],
    included_addons: ['ai_content', 'analytics_pro', 'team_seats'],
    is_popular: true,
    plan_restrictions: 'Available for Pro and Enterprise plans only',
    required_plan: 'pro'
  };

  const mockProps = {
    bundle: mockBundle,
    isAvailable: true,
    onPurchase: vi.fn(),
    onUpgrade: vi.fn(),
    loading: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders bundle card correctly', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Content Creator Bundle')).toBeInTheDocument();
    expect(screen.getByText(/Perfect for content creators/)).toBeInTheDocument();
    expect(screen.getByText('40% OFF')).toBeInTheDocument();
    expect(screen.getByText('POPULAR')).toBeInTheDocument();
  });

  test('displays pricing information correctly', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('$29.99')).toBeInTheDocument();
    expect(screen.getByText('/month')).toBeInTheDocument();
    expect(screen.getByText('$49.99')).toBeInTheDocument();
    expect(screen.getByText('Save $20.00/month')).toBeInTheDocument();
  });

  test('displays yearly pricing when available', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText(/Or \$299\.99\/year/)).toBeInTheDocument();
  });

  test('displays bundle type and popular badge', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('CONTENT OPTIMIZATION')).toBeInTheDocument();
    expect(screen.getByText('POPULAR')).toBeInTheDocument();
  });

  test('displays first 3 features by default', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('AI Content Generation')).toBeInTheDocument();
    expect(screen.getByText('Advanced Analytics')).toBeInTheDocument();
    expect(screen.getByText('Team Collaboration')).toBeInTheDocument();
    
    // Should not show features beyond the first 3 initially
    expect(screen.queryByText('Priority Support')).not.toBeInTheDocument();
  });

  test('expands to show all features when expand button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    const expandButton = screen.getByText('+3 More Features');
    await user.click(expandButton);

    await waitFor(() => {
      expect(screen.getByText('Priority Support')).toBeInTheDocument();
      expect(screen.getByText('Custom Branding')).toBeInTheDocument();
      expect(screen.getByText('API Access')).toBeInTheDocument();
    });

    expect(screen.getByText('Show Less')).toBeInTheDocument();
  });

  test('collapses features when show less is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    // First expand
    const expandButton = screen.getByText('+3 More Features');
    await user.click(expandButton);

    await waitFor(() => {
      expect(screen.getByText('Priority Support')).toBeInTheDocument();
    });

    // Then collapse
    const collapseButton = screen.getByText('Show Less');
    await user.click(collapseButton);

    await waitFor(() => {
      expect(screen.queryByText('Priority Support')).not.toBeInTheDocument();
    });
  });

  test('displays included add-ons', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Includes These Add-ons:')).toBeInTheDocument();
    expect(screen.getByText('Ai Content')).toBeInTheDocument();
    expect(screen.getByText('Analytics Pro')).toBeInTheDocument();
    expect(screen.getByText('Team Seats')).toBeInTheDocument();
  });

  test('handles purchase button click when available', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    const purchaseButton = screen.getByText('Get This Bundle');
    await user.click(purchaseButton);

    await waitFor(() => {
      expect(mockProps.onPurchase).toHaveBeenCalledWith(mockBundle);
    });
  });

  test('shows upgrade button when not available', async () => {
    const user = userEvent.setup();
    const unavailableProps = {
      ...mockProps,
      isAvailable: false
    };
    
    render(
      <TestWrapper>
        <BundleCard {...unavailableProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Upgrade to Pro')).toBeInTheDocument();
    expect(screen.getByText(mockBundle.plan_restrictions)).toBeInTheDocument();

    const upgradeButton = screen.getByText('Upgrade to Pro');
    await user.click(upgradeButton);

    await waitFor(() => {
      expect(mockProps.onUpgrade).toHaveBeenCalledWith('pro');
    });
  });

  test('shows loading state correctly', () => {
    const loadingProps = {
      ...mockProps,
      loading: true
    };
    
    render(
      <TestWrapper>
        <BundleCard {...loadingProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Processing...')).toBeInTheDocument();
    
    const purchaseButton = screen.getByText('Processing...');
    expect(purchaseButton).toBeDisabled();
  });

  test('handles bundle without yearly pricing', () => {
    const bundleWithoutYearly = {
      ...mockBundle,
      price_yearly: undefined
    };
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={bundleWithoutYearly} />
      </TestWrapper>
    );

    expect(screen.queryByText(/Or \$.*\/year/)).not.toBeInTheDocument();
  });

  test('handles bundle without popular flag', () => {
    const bundleNotPopular = {
      ...mockBundle,
      is_popular: false
    };
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={bundleNotPopular} />
      </TestWrapper>
    );

    expect(screen.queryByText('POPULAR')).not.toBeInTheDocument();
  });

  test('handles bundle with no included add-ons', () => {
    const bundleNoAddons = {
      ...mockBundle,
      included_addons: []
    };
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={bundleNoAddons} />
      </TestWrapper>
    );

    expect(screen.queryByText('Includes These Add-ons:')).not.toBeInTheDocument();
  });

  test('handles bundle with 3 or fewer features', () => {
    const bundleFewFeatures = {
      ...mockBundle,
      features: ['Feature 1', 'Feature 2', 'Feature 3']
    };
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={bundleFewFeatures} />
      </TestWrapper>
    );

    expect(screen.queryByText(/More Features/)).not.toBeInTheDocument();
  });

  test('shows error state when bundle data is missing', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={null} />
      </TestWrapper>
    );

    expect(screen.getByText('Bundle data not available')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByRole('article')).toHaveAttribute('aria-label', 'Bundle: Content Creator Bundle');
    expect(screen.getByRole('button', { name: /Purchase.*bundle/ })).toBeInTheDocument();
    
    // Check expand button accessibility
    const expandButton = screen.getByRole('button', { name: /Show 3 more features/ });
    expect(expandButton).toHaveAttribute('aria-expanded', 'false');
    expect(expandButton).toHaveAttribute('aria-controls', 'additional-features');
  });

  test('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    
    // Focus the card
    await user.tab();
    expect(card).toHaveFocus();

    // Navigate to purchase button
    await user.tab();
    const purchaseButton = screen.getByText('Get This Bundle');
    expect(purchaseButton).toHaveFocus();

    // Activate with Enter key
    await user.keyboard('{Enter}');
    expect(mockProps.onPurchase).toHaveBeenCalledWith(mockBundle);
  });

  test('calculates savings correctly', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    // Monthly savings: $49.99 - $29.99 = $20.00
    expect(screen.getByText('Save $20.00/month')).toBeInTheDocument();
    
    // Yearly savings: ($29.99 * 12) - $299.99 = $59.89
    expect(screen.getByText(/save \$59\.89 annually/)).toBeInTheDocument();
  });

  test('handles different bundle types with correct colors', () => {
    const agencyBundle = {
      ...mockBundle,
      bundle_type: 'agency_management'
    };
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={agencyBundle} />
      </TestWrapper>
    );

    expect(screen.getByText('AGENCY MANAGEMENT')).toBeInTheDocument();
  });

  test('handles error in purchase function gracefully', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const errorOnPurchase = vi.fn().mockRejectedValue(new Error('Purchase failed'));
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} onPurchase={errorOnPurchase} />
      </TestWrapper>
    );

    const purchaseButton = screen.getByText('Get This Bundle');
    await user.click(purchaseButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Purchase error:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  test('handles error in upgrade function gracefully', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const errorOnUpgrade = vi.fn().mockRejectedValue(new Error('Upgrade failed'));
    
    render(
      <TestWrapper>
        <BundleCard {...mockProps} isAvailable={false} onUpgrade={errorOnUpgrade} />
      </TestWrapper>
    );

    const upgradeButton = screen.getByText('Upgrade to Pro');
    await user.click(upgradeButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Upgrade error:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  test('renders with default props when optional props are missing', () => {
    const minimalProps = {
      bundle: mockBundle,
      onPurchase: vi.fn()
    };

    render(
      <TestWrapper>
        <BundleCard {...minimalProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Content Creator Bundle')).toBeInTheDocument();
    expect(screen.getByText('Get This Bundle')).toBeInTheDocument();
  });

  test('handles missing onUpgrade prop gracefully', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(
      <TestWrapper>
        <BundleCard {...mockProps} isAvailable={false} onUpgrade={undefined} />
      </TestWrapper>
    );

    const upgradeButton = screen.getByText('Upgrade to Pro');
    await user.click(upgradeButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Upgrade error:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  test('handles bundle with missing required_plan', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    const bundleNoRequiredPlan = {
      ...mockBundle,
      required_plan: undefined
    };

    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={bundleNoRequiredPlan} isAvailable={false} />
      </TestWrapper>
    );

    const upgradeButton = screen.getByText('Upgrade to');
    await user.click(upgradeButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Upgrade error:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  test('displays correct savings color based on percentage', () => {
    const highSavingsBundle = {
      ...mockBundle,
      savings_percentage: 30
    };

    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={highSavingsBundle} />
      </TestWrapper>
    );

    expect(screen.getByText('30% OFF')).toBeInTheDocument();
  });

  test('handles bundle with no features', () => {
    const bundleNoFeatures = {
      ...mockBundle,
      features: []
    };

    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={bundleNoFeatures} />
      </TestWrapper>
    );

    expect(screen.getByText('Key Features:')).toBeInTheDocument();
    expect(screen.queryByText(/More Features/)).not.toBeInTheDocument();
  });

  test('handles bundle with undefined features', () => {
    const bundleUndefinedFeatures = {
      ...mockBundle,
      features: undefined
    };

    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={bundleUndefinedFeatures} />
      </TestWrapper>
    );

    expect(screen.getByText('Key Features:')).toBeInTheDocument();
    expect(screen.queryByText(/More Features/)).not.toBeInTheDocument();
  });

  test('formats price correctly for different currencies', () => {
    const expensiveBundle = {
      ...mockBundle,
      price_monthly: 1234.56,
      original_price: 2000.00
    };

    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={expensiveBundle} />
      </TestWrapper>
    );

    expect(screen.getByText('$1,234.56')).toBeInTheDocument();
    expect(screen.getByText('$2,000.00')).toBeInTheDocument();
  });

  test('handles very long bundle names and descriptions', () => {
    const longNameBundle = {
      ...mockBundle,
      name: 'This is a very long bundle name that might wrap to multiple lines in the UI',
      description: 'This is an extremely long description that should test how the component handles text overflow and wrapping in various screen sizes and layouts'
    };

    render(
      <TestWrapper>
        <BundleCard {...mockProps} bundle={longNameBundle} />
      </TestWrapper>
    );

    expect(screen.getByText(/This is a very long bundle name/)).toBeInTheDocument();
    expect(screen.getByText(/This is an extremely long description/)).toBeInTheDocument();
  });

  test('maintains focus management during expand/collapse', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    const expandButton = screen.getByText('+3 More Features');
    expandButton.focus();

    await user.keyboard('{Enter}');

    await waitFor(() => {
      const collapseButton = screen.getByText('Show Less');
      expect(collapseButton).toBeInTheDocument();
    });
  });

  test('provides proper screen reader announcements', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    // Check that icons have proper aria-hidden attributes
    const checkIcons = screen.getAllByTestId('CheckCircleIcon');
    checkIcons.forEach(icon => {
      expect(icon.closest('svg')).toHaveAttribute('aria-hidden', 'true');
    });
  });

  test('handles high contrast mode', () => {
    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    expect(card).toHaveStyle('transition: all 0.3s ease-in-out');
  });

  test('supports reduced motion preferences', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    render(
      <TestWrapper>
        <BundleCard {...mockProps} />
      </TestWrapper>
    );

    const card = screen.getByRole('article');
    expect(card).toBeInTheDocument();
  });
});

// @since 2024-1-1 to 2025-25-7
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { CircularProgress, Box } from '@mui/material';

const PrivateRoute = ({ children }) => {
  const { isAuthenticated, isAdmin, loading, isLocalhostBypass } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  // Allow access if localhost bypass is enabled (development only)
  if (isLocalhostBypass) {
    return children;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Redirect to login if not an admin
  if (!isAdmin) {
    return <Navigate to="/login" replace />;
  }

  // Render children if authenticated and admin
  return children;
};

export default PrivateRoute;

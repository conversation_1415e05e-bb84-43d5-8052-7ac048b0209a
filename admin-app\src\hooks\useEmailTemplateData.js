// @since 2024-1-1 to 2025-25-7
import { useState, useCallback, useRef } from 'react';
import api from '../api';

export const useEmailTemplateData = () => {
  const [templates, setTemplates] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [analytics, setAnalytics] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Cache and pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    total: 0,
    pages: 0
  });
  
  const [filters, setFilters] = useState({
    templateType: null,
    status: null,
    category: null,
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  // Circuit breaker state
  const circuitBreaker = useRef({
    failures: 0,
    lastFailureTime: null,
    state: 'closed' // closed, open, half-open
  });

  const isCircuitOpen = () => {
    const { failures, lastFailureTime, state } = circuitBreaker.current;
    
    if (state === 'open') {
      const timeSinceLastFailure = Date.now() - lastFailureTime;
      if (timeSinceLastFailure > 30000) { // 30 seconds
        circuitBreaker.current.state = 'half-open';
        return false;
      }
      return true;
    }
    
    return false;
  };

  const recordSuccess = () => {
    circuitBreaker.current.failures = 0;
    circuitBreaker.current.state = 'closed';
  };

  const recordFailure = () => {
    circuitBreaker.current.failures += 1;
    circuitBreaker.current.lastFailureTime = Date.now();
    
    if (circuitBreaker.current.failures >= 5) {
      circuitBreaker.current.state = 'open';
    }
  };

  // Fetch templates with caching and error handling
  const fetchTemplates = useCallback(async (options = {}) => {
    if (isCircuitOpen()) {
      // Circuit is open - provide graceful fallback
      setError(null); // Don't show error, just use empty state
      setTemplates([]);
      setPagination({
        page: 1,
        pageSize: 20,
        total: 0,
        pages: 0
      });
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const params = {
        skip: ((options.page || pagination.page) - 1) * (options.pageSize || pagination.pageSize),
        limit: options.pageSize || pagination.pageSize,
        ...filters,
        ...options.filters
      };

      // Remove null/undefined values
      Object.keys(params).forEach(key => {
        if (params[key] === null || params[key] === undefined || params[key] === '') {
          delete params[key];
        }
      });

      const response = await api.get('/api/admin/email-templates/templates', { params });

      setTemplates(response.data.templates);
      setPagination({
        page: response.data.page,
        pageSize: response.data.page_size,
        total: response.data.total,
        pages: response.data.pages
      });

      recordSuccess();
    } catch (err) {
      recordFailure();
      console.error('Error fetching templates:', err);

      // Graceful error handling - don't break the UI
      setTemplates([]);
      setPagination({
        page: 1,
        pageSize: 20,
        total: 0,
        pages: 0
      });

      // Only set error for user-actionable issues, not connectivity problems
      if (err.response?.status === 401 || err.response?.status === 403) {
        setError('Authentication required. Please log in again.');
      } else if (err.response?.status >= 400 && err.response?.status < 500) {
        setError(err.response?.data?.detail || 'Unable to load templates');
      } else {
        // Network/server errors - don't alarm the user
        setError(null);
      }
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.pageSize, filters]);

  // Fetch campaigns
  const fetchCampaigns = useCallback(async () => {
    if (isCircuitOpen()) {
      setCampaigns([]);
      return;
    }

    try {
      const response = await api.get('/api/admin/email-templates/campaigns');
      setCampaigns(response.data.campaigns || []);
      recordSuccess();
    } catch (err) {
      recordFailure();
      console.error('Error fetching campaigns:', err);
      // Gracefully handle campaign fetch errors
      setCampaigns([]);
    }
  }, []);

  // Fetch analytics
  const fetchAnalytics = useCallback(async () => {
    if (isCircuitOpen()) {
      setAnalytics({
        overview: {
          totalSent: 0,
          totalDelivered: 0,
          totalOpened: 0,
          totalClicked: 0,
          totalBounced: 0,
          totalUnsubscribed: 0,
          openRate: 0,
          clickRate: 0,
          bounceRate: 0,
          unsubscribeRate: 0
        },
        topTemplates: []
      });
      return;
    }

    try {
      const response = await api.get('/api/admin/email-templates/analytics');
      setAnalytics(response.data || {});
      recordSuccess();
    } catch (err) {
      recordFailure();
      console.error('Error fetching analytics:', err);
      // Provide default analytics data for graceful degradation
      setAnalytics({
        overview: {
          totalSent: 0,
          totalDelivered: 0,
          totalOpened: 0,
          totalClicked: 0,
          totalBounced: 0,
          totalUnsubscribed: 0,
          openRate: 0,
          clickRate: 0,
          bounceRate: 0,
          unsubscribeRate: 0
        },
        topTemplates: []
      });
    }
  }, []);

  // Create template
  const createTemplate = useCallback(async (templateData) => {
    if (isCircuitOpen()) {
      throw new Error('Service temporarily unavailable');
    }

    try {
      const response = await api.post('/api/admin/email-templates/templates', templateData);
      recordSuccess();
      return response.data;
    } catch (err) {
      recordFailure();
      console.error('Error creating template:', err);
      throw new Error(err.response?.data?.detail || 'Failed to create template');
    }
  }, []);

  // Update template
  const updateTemplate = useCallback(async (templateId, updateData) => {
    if (isCircuitOpen()) {
      throw new Error('Service temporarily unavailable');
    }

    try {
      const response = await api.put(`/api/admin/email-templates/templates/${templateId}`, updateData);
      recordSuccess();
      return response.data;
    } catch (err) {
      recordFailure();
      console.error('Error updating template:', err);
      throw new Error(err.response?.data?.detail || 'Failed to update template');
    }
  }, []);

  // Delete template
  const deleteTemplate = useCallback(async (templateId) => {
    if (isCircuitOpen()) {
      throw new Error('Service temporarily unavailable');
    }

    try {
      await api.delete(`/api/admin/email-templates/templates/${templateId}`);
      recordSuccess();
    } catch (err) {
      recordFailure();
      console.error('Error deleting template:', err);
      throw new Error(err.response?.data?.detail || 'Failed to delete template');
    }
  }, []);

  // Duplicate template
  const duplicateTemplate = useCallback(async (templateId) => {
    if (isCircuitOpen()) {
      throw new Error('Service temporarily unavailable');
    }

    try {
      const response = await api.post(`/api/admin/email-templates/templates/${templateId}/duplicate`);
      recordSuccess();
      return response.data;
    } catch (err) {
      recordFailure();
      console.error('Error duplicating template:', err);
      throw new Error(err.response?.data?.detail || 'Failed to duplicate template');
    }
  }, []);

  // Preview template
  const previewTemplate = useCallback(async (templateId, previewData) => {
    if (isCircuitOpen()) {
      throw new Error('Service temporarily unavailable');
    }

    try {
      const response = await api.post(`/api/admin/email-templates/templates/${templateId}/preview`, previewData);
      recordSuccess();
      return response.data;
    } catch (err) {
      recordFailure();
      console.error('Error previewing template:', err);
      throw new Error(err.response?.data?.detail || 'Failed to preview template');
    }
  }, []);

  // Test template
  const testTemplate = useCallback(async (templateId, testData) => {
    if (isCircuitOpen()) {
      throw new Error('Service temporarily unavailable');
    }

    try {
      const response = await api.post(`/api/admin/email-templates/templates/${templateId}/test`, testData);
      recordSuccess();
      return response.data;
    } catch (err) {
      recordFailure();
      console.error('Error testing template:', err);
      throw new Error(err.response?.data?.detail || 'Failed to send test email');
    }
  }, []);

  // Update filters
  const updateFilters = useCallback((newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Update pagination
  const updatePagination = useCallback((newPagination) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  // Refresh all data
  const refreshData = useCallback(async () => {
    await Promise.all([
      fetchTemplates(),
      fetchCampaigns(),
      fetchAnalytics()
    ]);
  }, [fetchTemplates, fetchCampaigns, fetchAnalytics]);

  // Bulk operations
  const bulkOperation = useCallback(async (operation, templateIds, parameters = {}) => {
    if (isCircuitOpen()) {
      throw new Error('Service temporarily unavailable');
    }

    try {
      const response = await api.post('/api/admin/email-templates/bulk-operations', {
        template_ids: templateIds,
        operation,
        parameters
      });
      recordSuccess();
      return response.data;
    } catch (err) {
      recordFailure();
      console.error('Error performing bulk operation:', err);
      throw new Error(err.response?.data?.detail || 'Failed to perform bulk operation');
    }
  }, []);

  return {
    // State
    templates,
    campaigns,
    analytics,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchTemplates,
    fetchCampaigns,
    fetchAnalytics,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    previewTemplate,
    testTemplate,
    bulkOperation,
    updateFilters,
    updatePagination,
    refreshData,

    // Utilities
    setError,
    setLoading
  };
};

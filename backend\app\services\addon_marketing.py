"""
Marketing and promotion system for ACEO add-ons.
Handles in-app notifications, upsell prompts, and recommendation engine.
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from enum import Enum
from dataclasses import dataclass

from app.models.user import User
from app.services.addon_catalog import addon_catalog, AddonCategory, AddonType
from app.services.addon_usage_tracking import usage_tracker, UsageType
# Note: These functions don't exist yet, using placeholders
# from app.services.notification import send_in_app_notification
# from app.services.email_service import send_addon_promotion_email
from app.core.monitoring import record_marketing_metrics
from app.core.redis import (
    redis_manager, redis_get, redis_set, redis_delete, redis_setex,
    redis_incr, redis_expire, redis_lpush, redis_llen, redis_lrange
)

logger = logging.getLogger(__name__)


class PromotionTrigger(str, Enum):
    """Types of promotion triggers."""
    USAGE_LIMIT_APPROACHING = "usage_limit_approaching"
    FEATURE_DISCOVERY = "feature_discovery"
    PLAN_UPGRADE_OPPORTUNITY = "plan_upgrade_opportunity"
    SEASONAL_PROMOTION = "seasonal_promotion"
    ONBOARDING_SEQUENCE = "onboarding_sequence"
    RETENTION_CAMPAIGN = "retention_campaign"
    CROSS_SELL = "cross_sell"
    UPSELL = "upsell"


@dataclass
class PromotionRule:
    """Promotion rule configuration."""
    trigger: PromotionTrigger
    addon_ids: List[str]
    conditions: Dict[str, Any]
    message_template: str
    cta_text: str
    priority: int
    cooldown_hours: int = 24
    max_impressions: int = 3


class AddonMarketingEngine:
    """Intelligent add-on marketing and promotion engine."""
    
    def __init__(self):
        self.promotion_rules = self._initialize_promotion_rules()
        self.recommendation_weights = {
            "usage_pattern": 0.3,
            "plan_compatibility": 0.25,
            "popularity": 0.2,
            "business_value": 0.15,
            "seasonal_relevance": 0.1
        }
    
    def _initialize_promotion_rules(self) -> List[PromotionRule]:
        """Initialize promotion rules for different scenarios."""
        return [
            # Usage-based promotions
            PromotionRule(
                trigger=PromotionTrigger.USAGE_LIMIT_APPROACHING,
                addon_ids=["regeneration_booster"],
                conditions={"usage_percentage": 80, "usage_type": "regeneration_credits"},
                message_template="You're running low on regeneration credits! Get {credits} more credits for just ${price}",
                cta_text="Get More Credits",
                priority=1,
                cooldown_hours=12
            ),
            
            PromotionRule(
                trigger=PromotionTrigger.USAGE_LIMIT_APPROACHING,
                addon_ids=["image_pack_premium"],
                conditions={"usage_percentage": 75, "usage_type": "image_generation"},
                message_template="Almost out of image credits! Unlock {credits} premium images for ${price}",
                cta_text="Upgrade Images",
                priority=1,
                cooldown_hours=12
            ),
            
            # Feature discovery promotions
            PromotionRule(
                trigger=PromotionTrigger.FEATURE_DISCOVERY,
                addon_ids=["sentiment_analysis_pro"],
                conditions={"plan": "accelerator", "days_since_signup": 7},
                message_template="Discover what your audience really thinks! Advanced sentiment analysis reveals hidden insights",
                cta_text="Try Advanced Analytics",
                priority=2,
                cooldown_hours=48
            ),
            
            # Team growth promotions
            PromotionRule(
                trigger=PromotionTrigger.CROSS_SELL,
                addon_ids=["additional_user_seats"],
                conditions={"content_creation_frequency": "high", "plan": ["creator", "accelerator"]},
                message_template="Scale your content creation! Add team members for just $15/month per seat",
                cta_text="Add Team Members",
                priority=2,
                cooldown_hours=72
            ),
            
            # Premium feature promotions
            PromotionRule(
                trigger=PromotionTrigger.UPSELL,
                addon_ids=["white_label_platform"],
                conditions={"plan": "dominator", "business_account": True},
                message_template="Build your brand! White-label ACEO and make it truly yours",
                cta_text="Customize Platform",
                priority=3,
                cooldown_hours=168  # Weekly
            ),
            
            # Onboarding sequence
            PromotionRule(
                trigger=PromotionTrigger.ONBOARDING_SEQUENCE,
                addon_ids=["regeneration_booster", "image_pack_premium"],
                conditions={"days_since_signup": 3, "content_created": True},
                message_template="Great start! Supercharge your content with premium add-ons",
                cta_text="Explore Add-ons",
                priority=2,
                cooldown_hours=24
            ),
            
            # Retention campaigns
            PromotionRule(
                trigger=PromotionTrigger.RETENTION_CAMPAIGN,
                addon_ids=["priority_support"],
                conditions={"last_login_days": 14, "plan": ["accelerator", "dominator"]},
                message_template="We miss you! Get priority support to maximize your ACEO experience",
                cta_text="Get Priority Support",
                priority=1,
                cooldown_hours=48
            )
        ]
    
    async def get_personalized_recommendations(self, user: User, context: str = "dashboard") -> List[Dict[str, Any]]:
        """Get personalized add-on recommendations for a user."""
        try:
            # Get user's current add-ons and usage patterns
            user_addons = await usage_tracker.get_addon_status(str(user.id))
            usage_analytics = await usage_tracker.get_usage_analytics(str(user.id), days=30)
            
            # Get available add-ons
            available_addons = await addon_catalog.get_available_addons(user)
            
            # Score and rank recommendations
            recommendations = []
            
            for addon in available_addons:
                score = await self._calculate_recommendation_score(user, addon, usage_analytics, context)
                
                if score > 0.3:  # Minimum threshold
                    recommendation = {
                        "addon": addon,
                        "score": score,
                        "reasons": await self._get_recommendation_reasons(user, addon, usage_analytics),
                        "personalized_message": await self._generate_personalized_message(user, addon),
                        "estimated_value": await self._calculate_estimated_value(user, addon),
                        "urgency_level": await self._calculate_urgency_level(user, addon)
                    }
                    recommendations.append(recommendation)
            
            # Sort by score and return top recommendations
            recommendations.sort(key=lambda x: x["score"], reverse=True)
            
            record_marketing_metrics("recommendations_generated", user.subscription.plan_id if user.subscription else "free", len(recommendations))
            
            return recommendations[:5]  # Top 5 recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations for user {user.id}: {str(e)}")
            return []
    
    async def check_promotion_triggers(self, user: User, trigger_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check if any promotion rules should be triggered."""
        try:
            triggered_promotions = []
            
            for rule in self.promotion_rules:
                if await self._should_trigger_promotion(user, rule, trigger_context):
                    # Check cooldown
                    cooldown_key = f"promotion_cooldown:{user.id}:{rule.trigger.value}"
                    if await redis_get(cooldown_key):
                        continue

                    # Check impression limit
                    impression_key = f"promotion_impressions:{user.id}:{rule.trigger.value}"
                    impressions = await redis_get(impression_key) or 0
                    if int(impressions) >= rule.max_impressions:
                        continue
                    
                    # Generate promotion
                    promotion = await self._generate_promotion(user, rule, trigger_context)
                    triggered_promotions.append(promotion)
                    
                    # Set cooldown and increment impressions
                    await redis_setex(cooldown_key, rule.cooldown_hours * 3600, "triggered")
                    await redis_incr(impression_key)
                    await redis_expire(impression_key, 30 * 24 * 3600)  # 30 days
            
            return triggered_promotions
            
        except Exception as e:
            logger.error(f"Error checking promotion triggers for user {user.id}: {str(e)}")
            return []
    
    async def send_usage_based_upsell(self, user_id: int, usage_type: str, current_usage: int, limit: int) -> bool:
        """Send usage-based upsell notification."""
        try:
            user = await self._get_user(user_id)
            if not user:
                return False
            
            usage_percentage = (current_usage / limit) * 100
            
            # Find relevant add-ons for this usage type
            relevant_addons = await self._find_addons_for_usage_type(usage_type)
            
            if not relevant_addons:
                return False
            
            # Get the best add-on recommendation
            best_addon = relevant_addons[0]  # Assuming sorted by relevance
            
            # Generate upsell message
            message = await self._generate_upsell_message(user, best_addon, usage_type, usage_percentage)
            
            # Send in-app notification
            await send_in_app_notification(
                user_id=str(user_id),
                title="Upgrade Your Limits",
                message=message["text"],
                action_url=f"/addons/{best_addon['id']}",
                action_text=message["cta"],
                notification_type="upsell",
                metadata={
                    "addon_id": best_addon["id"],
                    "usage_type": usage_type,
                    "usage_percentage": usage_percentage
                }
            )
            
            record_marketing_metrics("upsell_sent", user.subscription.plan_id if user.subscription else "free", 1)
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending usage-based upsell for user {user_id}: {str(e)}")
            return False
    
    async def create_addon_showcase_banner(self, user: User, context: str = "dashboard") -> Optional[Dict[str, Any]]:
        """Create a showcase banner for add-ons based on user context."""
        try:
            # Get top recommendation
            recommendations = await self.get_personalized_recommendations(user, context)
            
            if not recommendations:
                return None
            
            top_recommendation = recommendations[0]
            addon = top_recommendation["addon"]
            
            # Create banner content
            banner = {
                "id": f"addon_banner_{addon['id']}_{int(datetime.now().timestamp())}",
                "type": "addon_showcase",
                "addon_id": addon["id"],
                "title": f"Supercharge Your {addon['category'].replace('_', ' ').title()}",
                "subtitle": addon["description"],
                "features": addon["features"][:3],  # Top 3 features
                "cta_text": "Learn More",
                "cta_url": f"/addons/{addon['id']}",
                "estimated_value": top_recommendation["estimated_value"],
                "urgency_level": top_recommendation["urgency_level"],
                "personalized_message": top_recommendation["personalized_message"],
                "dismissible": True,
                "expires_at": (datetime.now() + timedelta(hours=24)).isoformat()
            }
            
            return banner
            
        except Exception as e:
            logger.error(f"Error creating addon showcase banner for user {user.id}: {str(e)}")
            return None
    
    async def _calculate_recommendation_score(self, user: User, addon: Dict[str, Any], 
                                            usage_analytics: Dict[str, Any], context: str) -> float:
        """Calculate recommendation score for an add-on."""
        score = 0.0
        
        # Usage pattern score
        usage_score = await self._calculate_usage_pattern_score(user, addon, usage_analytics)
        score += usage_score * self.recommendation_weights["usage_pattern"]
        
        # Plan compatibility score
        plan_score = self._calculate_plan_compatibility_score(user, addon)
        score += plan_score * self.recommendation_weights["plan_compatibility"]
        
        # Popularity score
        popularity_score = 1.0 if addon.get("is_popular", False) else 0.5
        score += popularity_score * self.recommendation_weights["popularity"]
        
        # Business value score
        business_score = addon.get("roi_percentage", 0.2)
        score += business_score * self.recommendation_weights["business_value"]
        
        # Seasonal relevance (placeholder)
        seasonal_score = 0.8  # Would be based on current season/events
        score += seasonal_score * self.recommendation_weights["seasonal_relevance"]
        
        return min(score, 1.0)  # Cap at 1.0
    
    async def _calculate_usage_pattern_score(self, user: User, addon: Dict[str, Any], 
                                           usage_analytics: Dict[str, Any]) -> float:
        """Calculate score based on user's usage patterns."""
        addon_type = addon.get("type")
        usage_increase = addon.get("usage_increase", {})
        
        # Check if user is approaching limits for features this add-on enhances
        score = 0.0
        
        for feature, increase in usage_increase.items():
            if feature in usage_analytics.get("usage_by_type", {}):
                usage_data = usage_analytics["usage_by_type"][feature]
                # Higher score if user is actively using related features
                score += min(usage_data["total_amount"] / 100, 1.0)  # Normalize
        
        return score / max(len(usage_increase), 1)
    
    def _calculate_plan_compatibility_score(self, user: User, addon: Dict[str, Any]) -> float:
        """Calculate score based on plan compatibility."""
        user_plan = user.subscription.plan_id if user.subscription else "creator"
        required_plan = addon.get("required_plan", "creator")
        
        plan_hierarchy = {"creator": 1, "accelerator": 2, "dominator": 3}
        user_level = plan_hierarchy.get(user_plan, 1)
        required_level = plan_hierarchy.get(required_plan, 1)
        
        if user_level >= required_level:
            return 1.0
        else:
            return 0.3  # Lower score but still possible (might encourage upgrade)
    
    async def _get_recommendation_reasons(self, user: User, addon: Dict[str, Any], 
                                        usage_analytics: Dict[str, Any]) -> List[str]:
        """Get reasons why this add-on is recommended."""
        reasons = []
        
        # Usage-based reasons
        usage_increase = addon.get("usage_increase", {})
        for feature in usage_increase.keys():
            if feature in usage_analytics.get("usage_by_type", {}):
                reasons.append(f"You actively use {feature.replace('_', ' ')}")
        
        # Plan-based reasons
        user_plan = user.subscription.plan_id if user.subscription else "creator"
        if addon.get("required_plan") == user_plan:
            reasons.append(f"Perfect fit for your {user_plan.title()} plan")
        
        # Popularity reasons
        if addon.get("is_popular"):
            reasons.append("Popular choice among ACEO users")
        
        # Business value reasons
        roi = addon.get("roi_percentage", 0)
        if roi > 0.25:
            reasons.append(f"High ROI potential ({roi*100:.0f}%)")
        
        return reasons[:3]  # Top 3 reasons
    
    async def _generate_personalized_message(self, user: User, addon: Dict[str, Any]) -> str:
        """Generate a personalized message for the add-on."""
        user_name = user.full_name.split()[0] if user.full_name else "there"
        addon_name = addon["name"]
        business_value = addon.get("business_value", "enhance your content creation")
        
        templates = [
            f"Hi {user_name}! {addon_name} can help you {business_value}",
            f"{user_name}, unlock new possibilities with {addon_name}",
            f"Ready to level up, {user_name}? {addon_name} is perfect for you",
            f"{user_name}, {addon_name} could be a game-changer for your workflow"
        ]
        
        # Simple selection based on user ID for consistency
        template_index = hash(str(user.id)) % len(templates)
        return templates[template_index]
    
    async def _calculate_estimated_value(self, user: User, addon: Dict[str, Any]) -> str:
        """Calculate estimated value proposition for the user."""
        addon_type = addon.get("type")
        pricing = addon.get("pricing", {})
        
        if addon_type == AddonType.CONSUMABLE and "basic" in pricing:
            credits = pricing["basic"].get("credits", 0)
            price = pricing["basic"].get("price", 0)
            if credits and price:
                cost_per_credit = price / credits
                return f"${cost_per_credit:.3f} per credit"
        
        elif addon_type == AddonType.RECURRING and "monthly" in pricing:
            monthly_price = pricing["monthly"].get("price", 0)
            return f"${monthly_price}/month"
        
        return "Great value"
    
    async def _calculate_urgency_level(self, user: User, addon: Dict[str, Any]) -> str:
        """Calculate urgency level for the recommendation."""
        # Check if user is approaching limits
        user_addons = await usage_tracker.get_addon_status(str(user.id))
        
        for user_addon in user_addons:
            if user_addon["usage_percentage"] > 80:
                return "high"
            elif user_addon["usage_percentage"] > 60:
                return "medium"
        
        return "low"
    
    async def _should_trigger_promotion(self, user: User, rule: PromotionRule, 
                                      trigger_context: Dict[str, Any]) -> bool:
        """Check if a promotion rule should be triggered."""
        # Check trigger type matches
        if rule.trigger.value not in trigger_context.get("triggers", []):
            return False
        
        # Check conditions
        for condition_key, condition_value in rule.conditions.items():
            context_value = trigger_context.get(condition_key)
            
            if isinstance(condition_value, list):
                if context_value not in condition_value:
                    return False
            elif isinstance(condition_value, (int, float)):
                if context_value is None or context_value < condition_value:
                    return False
            elif isinstance(condition_value, str):
                if context_value != condition_value:
                    return False
        
        return True
    
    async def _generate_promotion(self, user: User, rule: PromotionRule, 
                                trigger_context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a promotion based on a rule."""
        # Get the first applicable add-on
        addon_id = rule.addon_ids[0]  # Simplified for now
        addon = addon_catalog.catalog.get(addon_id)
        
        if not addon:
            return {}
        
        # Format message template
        pricing = addon.get("pricing", {})
        template_vars = {
            "credits": pricing.get("basic", {}).get("credits", "more"),
            "price": pricing.get("basic", {}).get("price", "X.XX"),
            "addon_name": addon["name"]
        }
        
        message = rule.message_template.format(**template_vars)
        
        return {
            "id": f"promotion_{rule.trigger.value}_{user.id}_{int(datetime.now().timestamp())}",
            "type": "promotion",
            "trigger": rule.trigger.value,
            "addon_id": addon_id,
            "title": addon["name"],
            "message": message,
            "cta_text": rule.cta_text,
            "cta_url": f"/addons/{addon_id}",
            "priority": rule.priority,
            "expires_at": (datetime.now() + timedelta(hours=24)).isoformat()
        }
    
    async def _get_user(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        # This would typically use dependency injection
        from app.core.database import get_db
        db = next(get_db())
        return db.query(User).filter(User.id == user_id).first()
    
    async def _find_addons_for_usage_type(self, usage_type: str) -> List[Dict[str, Any]]:
        """Find add-ons that enhance a specific usage type."""
        relevant_addons = []
        
        for addon_id, addon_config in addon_catalog.catalog.items():
            usage_increase = addon_config.get("usage_increase", {})
            
            # Map usage types to addon capabilities
            if (usage_type == "regeneration_credits" and "regeneration_credits" in usage_increase or
                usage_type == "image_generation" and "image_generation_credits" in usage_increase or
                usage_type == "sentiment_analysis" and "sentiment_comments" in usage_increase):
                relevant_addons.append(addon_config)
        
        return relevant_addons
    
    async def _generate_upsell_message(self, user: User, addon: Dict[str, Any], 
                                     usage_type: str, usage_percentage: float) -> Dict[str, str]:
        """Generate upsell message for usage-based promotion."""
        addon_name = addon["name"]
        
        if usage_percentage >= 90:
            urgency = "You're almost out of"
            cta = "Get More Now"
        elif usage_percentage >= 75:
            urgency = "You're running low on"
            cta = "Top Up Credits"
        else:
            urgency = "Boost your"
            cta = "Upgrade Now"
        
        feature_name = usage_type.replace("_", " ").title()
        
        return {
            "text": f"{urgency} {feature_name.lower()}! {addon_name} gives you more power to create amazing content.",
            "cta": cta
        }


# Global marketing engine instance
marketing_engine = AddonMarketingEngine()


async def get_addon_recommendations(user: User, context: str = "dashboard") -> List[Dict[str, Any]]:
    """Get personalized add-on recommendations for a user."""
    return await marketing_engine.get_personalized_recommendations(user, context)


async def check_addon_promotions(user: User, trigger_context: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Check for triggered add-on promotions."""
    return await marketing_engine.check_promotion_triggers(user, trigger_context)


async def send_usage_upsell(user_id: int, usage_type: str, current_usage: int, limit: int) -> bool:
    """Send usage-based upsell notification."""
    return await marketing_engine.send_usage_based_upsell(user_id, usage_type, current_usage, limit)


# Placeholder functions for missing notification services
# TODO: Implement these functions in the appropriate services

async def send_in_app_notification(user_id: str, title: str, message: str, notification_type: str = "info", **kwargs):
    """
    Placeholder for in-app notification function.
    TODO: Implement this in app.services.notification
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"PLACEHOLDER: Would send in-app notification to user {user_id}: {title} - {message} (type: {notification_type})")
    return True

async def send_addon_promotion_email(user_id: str, subject: str, content: str, cta_text: Optional[str] = None, cta_url: Optional[str] = None, **kwargs):
    """
    Placeholder for addon promotion email function.
    TODO: Implement this in app.services.email_service
    """
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"PLACEHOLDER: Would send addon promotion email to user {user_id}: {subject} - {content}")
    return True

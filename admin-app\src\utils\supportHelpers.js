/**
 * Support utility functions for data processing, validation, and formatting
 @since 2024-1-1 to 2025-25-7
*/

import { formatDistanceToNow } from 'date-fns';

/**
 * Format numbers with locale-specific formatting
 */
export const formatNumber = (num) => {
  if (num === null || num === undefined) return '0';
  return new Intl.NumberFormat().format(num);
};

/**
 * Format percentage values
 */
export const formatPercentage = (value) => {
  if (value === null || value === undefined) return '0%';
  return `${(value * 100).toFixed(1)}%`;
};

/**
 * Format hours with appropriate units
 */
export const formatHours = (hours) => {
  if (hours === null || hours === undefined) return '0m';
  if (hours < 1) {
    return `${Math.round(hours * 60)}m`;
  }
  if (hours < 24) {
    return `${hours.toFixed(1)}h`;
  }
  return `${Math.round(hours / 24)}d`;
};

/**
 * Format date values with timezone support
 */
export const formatDate = (date, options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    timeZone: 'UTC',
  };
  
  const formatOptions = { ...defaultOptions, ...options };
  
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', formatOptions).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Format date and time
 */
export const formatDateTime = (date) => {
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'UTC',
  });
};

/**
 * Get ticket priority color
 */
export const getPriorityColor = (priority) => {
  switch (priority?.toLowerCase()) {
    case 'critical':
    case 'urgent':
      return 'error';
    case 'high':
      return 'warning';
    case 'medium':
      return 'info';
    case 'low':
      return 'success';
    default:
      return 'default';
  }
};

/**
 * Get ticket status color
 */
export const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'open':
    case 'new':
      return 'info';
    case 'in_progress':
    case 'assigned':
      return 'warning';
    case 'resolved':
    case 'closed':
      return 'success';
    case 'escalated':
      return 'error';
    case 'pending':
      return 'default';
    default:
      return 'default';
  }
};

/**
 * Get SLA status and color
 */
export const getSLAStatus = (ticket) => {
  if (!ticket.sla_deadline) {
    return { status: 'No SLA', color: 'default' };
  }

  const now = new Date();
  const deadline = new Date(ticket.sla_deadline);
  const timeLeft = deadline - now;
  
  if (timeLeft < 0) {
    return { status: 'Breached', color: 'error' };
  }
  
  const hoursLeft = timeLeft / (1000 * 60 * 60);
  
  if (hoursLeft < 2) {
    return { status: 'Critical', color: 'error' };
  } else if (hoursLeft < 8) {
    return { status: 'Warning', color: 'warning' };
  } else {
    return { status: 'On Track', color: 'success' };
  }
};

/**
 * Calculate response time metrics
 */
export const calculateResponseMetrics = (tickets) => {
  if (!Array.isArray(tickets) || tickets.length === 0) {
    return {
      averageResponseTime: 0,
      averageResolutionTime: 0,
      firstResponseRate: 0,
      slaCompliance: 0,
    };
  }

  const responseTimes = tickets
    .filter(t => t.first_response_time)
    .map(t => t.first_response_time);
  
  const resolutionTimes = tickets
    .filter(t => t.resolution_time && t.status === 'resolved')
    .map(t => t.resolution_time);

  const slaCompliant = tickets.filter(t => !t.sla_breached).length;

  return {
    averageResponseTime: responseTimes.length > 0 ? 
      responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
    averageResolutionTime: resolutionTimes.length > 0 ? 
      resolutionTimes.reduce((a, b) => a + b, 0) / resolutionTimes.length : 0,
    firstResponseRate: tickets.length > 0 ? responseTimes.length / tickets.length : 0,
    slaCompliance: tickets.length > 0 ? slaCompliant / tickets.length : 0,
  };
};

/**
 * Get agent status color
 */
export const getAgentStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'online':
    case 'available':
      return 'success';
    case 'busy':
    case 'in_call':
      return 'warning';
    case 'away':
    case 'break':
      return 'info';
    case 'offline':
      return 'error';
    default:
      return 'default';
  }
};

/**
 * Calculate satisfaction score color
 */
export const getSatisfactionColor = (score) => {
  if (score >= 4.5) return 'success';
  if (score >= 4.0) return 'info';
  if (score >= 3.5) return 'warning';
  return 'error';
};

/**
 * Generate correlation ID for tracking
 */
export const generateCorrelationId = () => {
  return `support-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Export data to CSV format
 */
export const exportToCSV = (data, filename, columns) => {
  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('No data to export');
  }
  
  // Create CSV header
  const headers = columns.map(col => col.label || col.key).join(',');
  
  // Create CSV rows
  const rows = data.map(item => {
    return columns.map(col => {
      let value = item[col.key];
      
      // Handle nested properties
      if (col.key.includes('.')) {
        const keys = col.key.split('.');
        value = keys.reduce((obj, key) => obj?.[key], item);
      }
      
      // Format value based on type
      if (col.type === 'date' && value) {
        value = formatDate(value);
      } else if (col.type === 'datetime' && value) {
        value = formatDateTime(value);
      } else if (col.type === 'number' && value !== null && value !== undefined) {
        value = formatNumber(value);
      } else if (col.type === 'percentage' && value !== null && value !== undefined) {
        value = formatPercentage(value);
      } else if (col.type === 'hours' && value !== null && value !== undefined) {
        value = formatHours(value);
      } else if (col.type === 'boolean') {
        value = value ? 'Yes' : 'No';
      }
      
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        value = `"${value.replace(/"/g, '""')}"`;
      }
      
      return value || '';
    }).join(',');
  });
  
  // Combine header and rows
  const csvContent = [headers, ...rows].join('\n');
  
  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

/**
 * Copy text to clipboard
 */
export const copyToClipboard = async (text) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

/**
 * Debounce function for search inputs
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Validate email format
 */
export const validateEmail = (email) => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};

/**
 * Get time ago string
 */
export const getTimeAgo = (date) => {
  if (!date) return 'Unknown';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return formatDistanceToNow(dateObj, { addSuffix: true });
  } catch (error) {
    console.error('Error calculating time ago:', error);
    return 'Unknown';
  }
};

/**
 * Calculate workload distribution
 */
export const calculateWorkloadDistribution = (agents, tickets) => {
  if (!Array.isArray(agents) || !Array.isArray(tickets)) {
    return [];
  }

  return agents.map(agent => {
    const agentTickets = tickets.filter(ticket => ticket.assigned_to === agent.id);
    const openTickets = agentTickets.filter(ticket => 
      ['open', 'in_progress', 'pending'].includes(ticket.status)
    );
    
    return {
      ...agent,
      totalTickets: agentTickets.length,
      openTickets: openTickets.length,
      workloadPercentage: agents.length > 0 ? 
        (openTickets.length / Math.max(1, tickets.filter(t => 
          ['open', 'in_progress', 'pending'].includes(t.status)
        ).length)) * 100 : 0,
    };
  });
};

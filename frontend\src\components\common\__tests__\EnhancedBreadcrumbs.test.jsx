/**
 * EnhancedBreadcrumbs Component Test Suite
 * Comprehensive testing for enterprise-grade breadcrumb navigation
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import EnhancedBreadcrumbs from '../EnhancedBreadcrumbs';

// Test theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children, initialEntries = ['/'] }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
const mockLocation = { pathname: '/dashboard/campaigns/123' };

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
}));

describe('EnhancedBreadcrumbs', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    test('renders breadcrumbs correctly', () => {
      render(
        <TestWrapper>
          <EnhancedBreadcrumbs />
        </TestWrapper>
      );

      expect(screen.getByRole('navigation')).toBeInTheDocument();
      expect(screen.getByText('Home')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Campaigns')).toBeInTheDocument();
    });

    test('handles custom routes correctly', () => {
      const customRoutes = {
        'custom-page': {
          label: 'Custom Page',
          icon: null,
          description: 'Custom page description'
        }
      };

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs customRoutes={customRoutes} />
        </TestWrapper>
      );

      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    test('shows loading state correctly', () => {
      render(
        <TestWrapper>
          <EnhancedBreadcrumbs loading />
        </TestWrapper>
      );

      // Should show loading placeholders
      const loadingElements = screen.getByRole('navigation').querySelectorAll('[bgcolor="action.hover"]');
      expect(loadingElements.length).toBeGreaterThan(0);
    });
  });

  describe('Navigation Functionality', () => {
    test('handles breadcrumb clicks', async () => {
      const user = userEvent.setup();
      const onNavigate = jest.fn();

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs onNavigate={onNavigate} />
        </TestWrapper>
      );

      const homeLink = screen.getByText('Home');
      await user.click(homeLink);

      expect(onNavigate).toHaveBeenCalledWith('/', expect.any(Object));
    });

    test('handles keyboard navigation', async () => {
      const user = userEvent.setup();
      const onNavigate = jest.fn();

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs onNavigate={onNavigate} />
        </TestWrapper>
      );

      const homeLink = screen.getByText('Home');
      homeLink.focus();
      await user.keyboard('{Enter}');

      expect(onNavigate).toHaveBeenCalledWith('/', expect.any(Object));
    });
  });

  describe('Analytics Integration', () => {
    test('tracks analytics events', async () => {
      const user = userEvent.setup();
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs enableAnalytics onAnalytics={onAnalytics} />
        </TestWrapper>
      );

      const homeLink = screen.getByText('Home');
      await user.click(homeLink);

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          component: 'EnhancedBreadcrumbs',
          action: 'breadcrumb_clicked'
        })
      );
    });

    test('does not track when analytics disabled', async () => {
      const user = userEvent.setup();
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs enableAnalytics={false} onAnalytics={onAnalytics} />
        </TestWrapper>
      );

      const homeLink = screen.getByText('Home');
      await user.click(homeLink);

      expect(onAnalytics).not.toHaveBeenCalled();
    });
  });

  describe('Responsive Behavior', () => {
    test('collapses breadcrumbs on mobile', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query.includes('(max-width: 600px)'),
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs enableCollapse maxItems={2} />
        </TestWrapper>
      );

      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <EnhancedBreadcrumbs ariaLabel="Custom breadcrumb navigation" />
        </TestWrapper>
      );

      const nav = screen.getByRole('navigation');
      expect(nav).toHaveAttribute('aria-label', 'Custom breadcrumb navigation');
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs />
        </TestWrapper>
      );

      const homeLink = screen.getByText('Home');
      homeLink.focus();
      
      expect(document.activeElement).toBe(homeLink);
      
      await user.tab();
      // Should move to next breadcrumb
    });

    test('has proper focus management', () => {
      render(
        <TestWrapper>
          <EnhancedBreadcrumbs />
        </TestWrapper>
      );

      const links = screen.getAllByRole('link');
      links.forEach(link => {
        expect(link).toHaveAttribute('tabIndex', '0');
      });
    });
  });

  describe('Advanced Features', () => {
    test('handles bookmark functionality', async () => {
      const user = userEvent.setup();
      const onAnalytics = jest.fn();

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs 
            enableBookmarks 
            enableContextMenu 
            enableAnalytics 
            onAnalytics={onAnalytics} 
          />
        </TestWrapper>
      );

      // Right-click to open context menu
      const homeLink = screen.getByText('Home');
      await user.pointer({ keys: '[MouseRight]', target: homeLink });

      await waitFor(() => {
        expect(screen.getByText('Add Bookmark')).toBeInTheDocument();
      });
    });

    test('handles collapsed menu', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs enableCollapse maxItems={2} />
        </TestWrapper>
      );

      // Look for collapsed indicator
      const moreButton = screen.queryByLabelText(/Show.*hidden breadcrumbs/);
      if (moreButton) {
        await user.click(moreButton);
        // Should open collapsed menu
      }
    });
  });

  describe('Error Handling', () => {
    test('handles missing route mappings gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs />
        </TestWrapper>
      );

      expect(screen.getByRole('navigation')).toBeInTheDocument();
      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    test('handles invalid props gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <EnhancedBreadcrumbs variant="invalid" maxItems="invalid" />
        </TestWrapper>
      );

      expect(screen.getByRole('navigation')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });

  describe('Performance', () => {
    test('memoizes breadcrumb generation', () => {
      const { rerender } = render(
        <TestWrapper>
          <EnhancedBreadcrumbs />
        </TestWrapper>
      );

      const initialNav = screen.getByRole('navigation');
      
      // Re-render with same props
      rerender(
        <TestWrapper>
          <EnhancedBreadcrumbs />
        </TestWrapper>
      );

      const rerenderedNav = screen.getByRole('navigation');
      expect(rerenderedNav).toBeInTheDocument();
    });
  });
});

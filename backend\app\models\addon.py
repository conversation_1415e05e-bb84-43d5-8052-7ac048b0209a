"""
MongoDB models for ACE Social add-on system.
Defines Pydantic models for user add-ons, purchases, and usage tracking.
"""
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from bson import ObjectId
from enum import Enum

# Import PyObjectId from user model
from app.models.user import PyObjectId, utc_now


class AddonStatus(str, Enum):
    """Add-on status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"

class PurchaseStatus(str, Enum):
    """Purchase status enumeration."""
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"

class UserAddon(BaseModel):
    """User's purchased add-ons and their current status."""

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        json_schema_extra={
            "example": {
                "_id": "60d5ec9af682dbd12a0a9fb9",
                "user_id": "60d5ec9af682dbd12a0a9fb8",
                "addon_id": "premium_image_credits",
                "variant": "medium",
                "total_credits": 100,
                "credits_remaining": 75,
                "credits_used": 25,
                "purchase_price": 29.99,
                "currency": "USD",
                "is_active": True,
                "lemon_squeezy_order_id": "order_12345"
            }
        }
    )

    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User who owns this add-on")
    addon_id: str = Field(..., max_length=100, description="Add-on identifier")
    variant: str = Field(default="basic", max_length=50, description="Add-on variant")

    # Credits and usage
    total_credits: int = Field(default=0, ge=0, description="Total credits purchased")
    credits_remaining: int = Field(default=0, ge=0, description="Credits remaining")
    credits_used: int = Field(default=0, ge=0, description="Credits used")

    # Purchase information
    purchase_price: float = Field(..., ge=0, description="Purchase price")
    currency: str = Field(default="USD", max_length=3, description="Currency code")
    purchased_at: datetime = Field(default_factory=utc_now, description="Purchase timestamp")

    # Subscription information (for recurring add-ons)
    is_subscription: bool = Field(default=False, description="Is this a subscription add-on")
    subscription_interval: Optional[str] = Field(None, description="Billing interval (month, year)")
    next_billing_date: Optional[datetime] = Field(None, description="Next billing date")

    # Status and lifecycle
    status: AddonStatus = Field(default=AddonStatus.ACTIVE, description="Add-on status")
    is_active: bool = Field(default=True, description="Is add-on active")
    expires_at: Optional[datetime] = Field(None, description="Expiration date")
    cancelled_at: Optional[datetime] = Field(None, description="Cancellation date")
    is_refunded: bool = Field(default=False, description="Is refunded")
    refunded_at: Optional[datetime] = Field(None, description="Refund date")

    # External service references
    lemon_squeezy_order_id: Optional[str] = Field(None, max_length=100, description="Lemon Squeezy order ID")
    lemon_squeezy_subscription_id: Optional[str] = Field(None, max_length=100, description="Lemon Squeezy subscription ID")

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    created_at: datetime = Field(default_factory=utc_now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=utc_now, description="Last update timestamp")

    @property
    def usage_percentage(self) -> float:
        """Calculate usage percentage."""
        if self.total_credits == 0:
            return 0.0
        return (self.credits_used / self.total_credits) * 100

    @property
    def is_expired(self) -> bool:
        """Check if add-on has expired."""
        if not self.expires_at:
            return False
        return utc_now() > self.expires_at

    def is_nearly_depleted(self, threshold: float = 0.9) -> bool:
        """Check if credits are nearly depleted."""
        return self.usage_percentage >= (threshold * 100)


class AddonPurchase(BaseModel):
    """Record of add-on purchases for accounting and analytics."""

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        json_schema_extra={
            "example": {
                "_id": "60d5ec9af682dbd12a0a9fb9",
                "user_id": "60d5ec9af682dbd12a0a9fb8",
                "user_addon_id": "60d5ec9af682dbd12a0a9fb7",
                "addon_id": "premium_image_credits",
                "variant": "medium",
                "amount": 29.99,
                "currency": "USD",
                "status": "completed",
                "payment_method": "credit_card",
                "lemon_squeezy_order_id": "order_12345"
            }
        }
    )

    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User who made the purchase")
    user_addon_id: Optional[PyObjectId] = Field(None, description="Associated user addon")

    # Purchase details
    addon_id: str = Field(..., max_length=100, description="Add-on identifier")
    variant: str = Field(..., max_length=50, description="Add-on variant")
    amount: float = Field(..., ge=0, description="Purchase amount")
    currency: str = Field(default="USD", max_length=3, description="Currency code")

    # Payment processing
    status: PurchaseStatus = Field(default=PurchaseStatus.PENDING, description="Purchase status")
    payment_method: Optional[str] = Field(None, max_length=50, description="Payment method")
    transaction_id: Optional[str] = Field(None, max_length=200, description="Transaction ID")

    # External service references
    lemon_squeezy_order_id: Optional[str] = Field(None, max_length=100, description="Lemon Squeezy order ID")
    lemon_squeezy_checkout_id: Optional[str] = Field(None, max_length=100, description="Lemon Squeezy checkout ID")

    # Timestamps
    purchased_at: datetime = Field(default_factory=utc_now, description="Purchase timestamp")
    completed_at: Optional[datetime] = Field(None, description="Completion timestamp")
    refunded_at: Optional[datetime] = Field(None, description="Refund timestamp")

    # Metadata
    purchase_metadata: Dict[str, Any] = Field(default_factory=dict, description="Purchase metadata")
    created_at: datetime = Field(default_factory=utc_now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=utc_now, description="Last update timestamp")


class AddonUsage(BaseModel):
    """Track usage of add-on credits and features."""

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        json_schema_extra={
            "example": {
                "_id": "60d5ec9af682dbd12a0a9fb9",
                "user_id": "60d5ec9af682dbd12a0a9fb8",
                "user_addon_id": "60d5ec9af682dbd12a0a9fb7",
                "usage_type": "image_generation",
                "amount": 1,
                "content_id": "60d5ec9af682dbd12a0a9fb6",
                "operation": "generate_image",
                "timestamp": "2023-06-01T12:00:00Z"
            }
        }
    )

    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User who used the add-on")
    user_addon_id: PyObjectId = Field(..., description="Associated user addon")

    # Usage details
    usage_type: str = Field(..., max_length=100, description="Type of usage")
    amount: int = Field(default=1, gt=0, description="Number of credits used")

    # Context
    content_id: Optional[PyObjectId] = Field(None, description="Associated content ID")
    operation: Optional[str] = Field(None, max_length=100, description="Operation performed")

    # Metadata
    usage_metadata: Dict[str, Any] = Field(default_factory=dict, description="Usage metadata")

    # Timestamps
    timestamp: datetime = Field(default_factory=utc_now, description="Usage timestamp")


class AddonCatalog(BaseModel):
    """Catalog of available add-ons and their configurations."""

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        json_schema_extra={
            "example": {
                "id": "premium_image_credits",
                "name": "Premium Image Generation Credits",
                "description": "High-quality AI image generation with advanced styles",
                "category": "content_generation",
                "is_active": True,
                "required_plan": "creator",
                "base_price": 29.99,
                "currency": "USD",
                "features": ["premium_styles", "high_resolution", "commercial_license"],
                "default_credits": 100,
                "is_popular": True
            }
        }
    )

    id: str = Field(..., max_length=100, description="Add-on identifier")
    name: str = Field(..., max_length=200, description="Add-on name")
    description: Optional[str] = Field(None, description="Add-on description")
    category: str = Field(..., max_length=100, description="Add-on category")

    # Availability
    is_active: bool = Field(default=True, description="Is add-on active")
    required_plan: Optional[str] = Field(None, max_length=50, description="Minimum plan required")

    # Pricing (base pricing, variants handled separately)
    base_price: float = Field(..., ge=0, description="Base price")
    currency: str = Field(default="USD", max_length=3, description="Currency code")

    # Features and limits
    features: List[str] = Field(default_factory=list, description="List of features")
    default_credits: int = Field(default=0, ge=0, description="Default credits")

    # Display
    icon: Optional[str] = Field(None, max_length=100, description="Icon identifier")
    display_order: int = Field(default=0, description="Display order")
    is_popular: bool = Field(default=False, description="Is popular add-on")
    is_featured: bool = Field(default=False, description="Is featured add-on")

    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    created_at: datetime = Field(default_factory=utc_now, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=utc_now, description="Last update timestamp")


class RecommendationType(str, Enum):
    """Recommendation type enumeration."""
    USAGE_BASED = "usage_based"
    PLAN_BASED = "plan_based"
    BEHAVIORAL = "behavioral"
    AI_SUGGESTED = "ai_suggested"

class AddonRecommendation(BaseModel):
    """AI-powered add-on recommendations for users."""

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        json_schema_extra={
            "example": {
                "_id": "60d5ec9af682dbd12a0a9fb9",
                "user_id": "60d5ec9af682dbd12a0a9fb8",
                "addon_id": "premium_image_credits",
                "recommendation_type": "usage_based",
                "confidence_score": 0.85,
                "priority": 1,
                "trigger_event": "high_image_usage",
                "reasoning": "User has been generating many images and would benefit from premium credits",
                "is_active": True,
                "shown_to_user": False
            }
        }
    )

    id: Optional[PyObjectId] = Field(default_factory=PyObjectId, alias="_id")
    user_id: PyObjectId = Field(..., description="User for this recommendation")
    addon_id: str = Field(..., max_length=100, description="Recommended add-on ID")

    # Recommendation details
    recommendation_type: RecommendationType = Field(..., description="Type of recommendation")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0.0 to 1.0)")
    priority: int = Field(default=0, description="Priority (higher = more important)")

    # Context
    trigger_event: Optional[str] = Field(None, max_length=100, description="What triggered this recommendation")
    reasoning: Optional[str] = Field(None, description="Human-readable explanation")

    # Status
    is_active: bool = Field(default=True, description="Is recommendation active")
    shown_to_user: bool = Field(default=False, description="Has been shown to user")
    user_clicked: bool = Field(default=False, description="User clicked on recommendation")
    user_purchased: bool = Field(default=False, description="User purchased the add-on")

    # Timestamps
    created_at: datetime = Field(default_factory=utc_now, description="Creation timestamp")
    shown_at: Optional[datetime] = Field(None, description="When shown to user")
    clicked_at: Optional[datetime] = Field(None, description="When user clicked")
    purchased_at: Optional[datetime] = Field(None, description="When user purchased")
    expires_at: Optional[datetime] = Field(None, description="When recommendation expires")

/**
 * Tests for ABTestingLab component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ABTestingLab from '../ABTestingLab';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    delete: vi.fn()
  }
}));

// Mock hooks
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showSuccessNotification: vi.fn(),
    showErrorNotification: vi.fn()
  }))
}));

vi.mock('../../../hooks/useBranding', () => ({
  default: vi.fn(() => ({
    brandingData: {
      colorSystem: { primary: '#4E40C5' },
      visualStyle: { photographyStyle: 'lifestyle' },
      imageComposition: { layout: 'rule-of-thirds' },
      logo_settings: { position: 'bottom-right' }
    }
  }))
}));

describe('ABTestingLab', () => {
  const mockProps = {
    onTestCreated: vi.fn(),
    onTestDeleted: vi.fn(),
    onVariationApplied: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders A/B testing lab correctly', () => {
    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('A/B Testing Lab')).toBeInTheDocument();
    expect(screen.getByText('Create and manage A/B tests for your branding elements to optimize performance.')).toBeInTheDocument();
    expect(screen.getByText('Create New A/B Test')).toBeInTheDocument();
  });

  test('loads existing tests on mount', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({
      data: [
        {
          id: 'test-1',
          name: 'Color Test',
          description: 'Testing primary colors',
          type: 'color',
          status: 'active',
          variations: []
        }
      ]
    });

    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(api.default.get).toHaveBeenCalledWith('/api/brand-profile-ab-tests');
    });
  });

  test('handles API errors when loading tests', async () => {
    const api = await import('../../../api');
    const { useNotification } = await import('../../../hooks/useNotification');
    
    const mockShowError = vi.fn();
    useNotification.mockReturnValue({
      showSuccessNotification: vi.fn(),
      showErrorNotification: mockShowError
    });

    api.default.get.mockRejectedValue(new Error('Network error'));

    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockShowError).toHaveBeenCalledWith('Failed to load A/B tests');
    });
  });

  test('creates test variations for color test', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    // Fill in test details
    const nameInput = screen.getByLabelText('Test name');
    const descriptionInput = screen.getByLabelText('Test description');
    const typeSelect = screen.getByLabelText('Test type');

    await user.type(nameInput, 'Primary Color Test');
    await user.type(descriptionInput, 'Testing different primary colors');
    await user.click(typeSelect);
    await user.click(screen.getByText('Color System'));

    // Generate variations
    const generateButton = screen.getByText('Generate Test Variations');
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Test Variations for "Primary Color Test"')).toBeInTheDocument();
    });

    expect(mockProps.onTestCreated).toHaveBeenCalled();
  });

  test('validates test name before creating variations', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    const generateButton = screen.getByText('Generate Test Variations');
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Please enter a test name')).toBeInTheDocument();
    });
  });

  test('saves and activates test', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    api.default.post.mockResolvedValue({
      data: {
        id: 'test-123',
        name: 'Color Test',
        status: 'active'
      }
    });

    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    // Create variations first
    const nameInput = screen.getByLabelText('Test name');
    await user.type(nameInput, 'Color Test');

    const generateButton = screen.getByText('Generate Test Variations');
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText('Save & Activate Test')).toBeInTheDocument();
    });

    // Save the test
    const saveButton = screen.getByText('Save & Activate Test');
    await user.click(saveButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith('/api/brand-profile-ab-tests/', expect.objectContaining({
        name: 'Color Test',
        target_metric: 'engagement_rate'
      }));
    });
  });

  test('deletes test with confirmation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    // Mock existing test
    api.default.get.mockResolvedValue({
      data: [
        {
          id: 'test-1',
          name: 'Color Test',
          description: 'Testing colors',
          type: 'color',
          status: 'active',
          variations: []
        }
      ]
    });

    api.default.delete.mockResolvedValue({});

    // Mock window.confirm
    global.window.confirm = vi.fn(() => true);

    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Color Test')).toBeInTheDocument();
    });

    const deleteButton = screen.getByText('Delete');
    await user.click(deleteButton);

    await waitFor(() => {
      expect(api.default.delete).toHaveBeenCalledWith('/api/brand-profile-ab-tests/test-1');
    });

    expect(mockProps.onTestDeleted).toHaveBeenCalledWith('test-1');
  });

  test('cancels delete when user declines confirmation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    // Mock existing test
    api.default.get.mockResolvedValue({
      data: [
        {
          id: 'test-1',
          name: 'Color Test',
          description: 'Testing colors',
          type: 'color',
          status: 'active',
          variations: []
        }
      ]
    });

    // Mock window.confirm to return false
    global.window.confirm = vi.fn(() => false);

    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Color Test')).toBeInTheDocument();
    });

    const deleteButton = screen.getByText('Delete');
    await user.click(deleteButton);

    expect(api.default.delete).not.toHaveBeenCalled();
    expect(mockProps.onTestDeleted).not.toHaveBeenCalled();
  });

  test('views test results', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    // Mock existing test
    api.default.get.mockResolvedValue({
      data: [
        {
          id: 'test-1',
          name: 'Color Test',
          description: 'Testing colors',
          type: 'color',
          status: 'completed',
          variations: [
            {
              id: 'var-1',
              name: 'Original',
              impressions: 1000,
              clicks: 50
            }
          ]
        }
      ]
    });

    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Color Test')).toBeInTheDocument();
    });

    const viewButton = screen.getByText('View Results');
    await user.click(viewButton);

    await waitFor(() => {
      expect(screen.getByText('Results: Color Test')).toBeInTheDocument();
    });
  });

  test('applies winning variation', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    
    // Mock existing completed test
    api.default.get.mockResolvedValue({
      data: [
        {
          id: 'test-1',
          name: 'Color Test',
          description: 'Testing colors',
          type: 'color',
          status: 'completed',
          variations: [
            {
              id: 'var-1',
              name: 'Original',
              impressions: 1000,
              clicks: 50
            }
          ]
        }
      ]
    });

    api.default.post.mockResolvedValue({});

    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Color Test')).toBeInTheDocument();
    });

    // Open results dialog
    const viewButton = screen.getByText('View Results');
    await user.click(viewButton);

    await waitFor(() => {
      expect(screen.getByText('Apply This Variation')).toBeInTheDocument();
    });

    // Apply variation
    const applyButton = screen.getByText('Apply This Variation');
    await user.click(applyButton);

    await waitFor(() => {
      expect(api.default.post).toHaveBeenCalledWith(
        '/api/unified-ab-testing/tests/brand_profile/test-1/apply-winner',
        { winner_variant_id: 'var-1' }
      );
    });

    expect(mockProps.onVariationApplied).toHaveBeenCalled();
  });

  test('disables form when disabled prop is true', () => {
    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const nameInput = screen.getByLabelText('Test name');
    const descriptionInput = screen.getByLabelText('Test description');
    const typeSelect = screen.getByLabelText('Test type');
    const generateButton = screen.getByText('Generate Test Variations');

    expect(nameInput).toBeDisabled();
    expect(descriptionInput).toBeDisabled();
    expect(typeSelect).toBeDisabled();
    expect(generateButton).toBeDisabled();
  });

  test('shows loading state during variation generation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    const nameInput = screen.getByLabelText('Test name');
    await user.type(nameInput, 'Test Name');

    const generateButton = screen.getByText('Generate Test Variations');
    await user.click(generateButton);

    expect(screen.getByText('Generating Variations...')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    const nameInput = screen.getByLabelText('Test name');
    const descriptionInput = screen.getByLabelText('Test description');
    const typeSelect = screen.getByLabelText('Test type');

    expect(nameInput).toHaveAttribute('aria-label', 'Test name');
    expect(descriptionInput).toHaveAttribute('aria-label', 'Test description');
    expect(typeSelect).toHaveAttribute('aria-label', 'Test type');
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <ABTestingLab 
          {...mockProps} 
          data-testid="test-ab-lab"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-ab-lab');
    expect(component).toHaveClass('custom-class');
  });

  test('handles different test types', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ABTestingLab {...mockProps} />
      </TestWrapper>
    );

    const typeSelect = screen.getByLabelText('Test type');
    await user.click(typeSelect);

    expect(screen.getByText('Color System')).toBeInTheDocument();
    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText('Image Composition')).toBeInTheDocument();
    expect(screen.getByText('Logo Placement')).toBeInTheDocument();
  });
});

/**
 * 404 Error Detector
 * Automatically detects and reports 404 errors with detailed information
 */

class Error404Detector {
  constructor() {
    this.errors = [];
    this.isMonitoring = false;
    this.originalFetch = null;
    this.originalXHROpen = null;
    
    this.init();
  }
  
  init() {
    this.setupFetchInterceptor();
    this.setupXHRInterceptor();
    this.setupResourceErrorListener();
    this.startMonitoring();
  }
  
  setupFetchInterceptor() {
    this.originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = Date.now();
      const url = typeof args[0] === 'string' ? args[0] : args[0].url;
      
      try {
        const response = await this.originalFetch(...args);
        const endTime = Date.now();
        
        if (response.status === 404) {
          this.recordError({
            type: 'fetch',
            url: url,
            status: 404,
            statusText: response.statusText,
            method: args[1]?.method || 'GET',
            duration: endTime - startTime,
            timestamp: new Date().toISOString(),
            stack: new Error().stack
          });
        }
        
        return response;
      } catch (error) {
        const endTime = Date.now();
        
        this.recordError({
          type: 'fetch_error',
          url: url,
          status: null,
          statusText: error.message,
          method: args[1]?.method || 'GET',
          duration: endTime - startTime,
          timestamp: new Date().toISOString(),
          error: error.message,
          stack: error.stack
        });
        
        throw error;
      }
    };
  }
  
  setupXHRInterceptor() {
    const self = this;
    this.originalXHROpen = XMLHttpRequest.prototype.open;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      const startTime = Date.now();
      
      this.addEventListener('loadend', function() {
        const endTime = Date.now();
        
        if (this.status === 404) {
          self.recordError({
            type: 'xhr',
            url: url,
            status: 404,
            statusText: this.statusText,
            method: method,
            duration: endTime - startTime,
            timestamp: new Date().toISOString(),
            stack: new Error().stack
          });
        }
      });
      
      return self.originalXHROpen.call(this, method, url, ...args);
    };
  }
  
  setupResourceErrorListener() {
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        // Resource loading error (images, scripts, stylesheets, etc.)
        this.recordError({
          type: 'resource',
          url: event.target.src || event.target.href || 'unknown',
          status: null,
          statusText: 'Resource failed to load',
          method: 'GET',
          duration: null,
          timestamp: new Date().toISOString(),
          element: event.target.tagName,
          stack: new Error().stack
        });
      }
    }, true);
  }
  
  recordError(errorInfo) {
    this.errors.push(errorInfo);
    
    // Log immediately for debugging
    console.group(`🚨 404 Error Detected: ${errorInfo.type.toUpperCase()}`);
    console.error('URL:', errorInfo.url);
    console.error('Status:', errorInfo.status);
    console.error('Method:', errorInfo.method);
    console.error('Duration:', errorInfo.duration ? `${errorInfo.duration}ms` : 'N/A');
    console.error('Timestamp:', errorInfo.timestamp);
    if (errorInfo.element) {
      console.error('Element:', errorInfo.element);
    }
    console.error('Stack Trace:', errorInfo.stack);
    console.groupEnd();
    
    // Send to error tracking if available
    if (window.errorTracker) {
      window.errorTracker._handleError({
        type: '404_error',
        message: `404 Error: ${errorInfo.url}`,
        filename: errorInfo.url,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        context: errorInfo
      });
    }
    
    // Show user-friendly notification for critical errors
    this.showUserNotification(errorInfo);
  }
  
  showUserNotification(errorInfo) {
    // Only show notifications for API errors, not static resources
    if (errorInfo.type === 'fetch' && errorInfo.url.includes('/api/')) {
      const message = `API endpoint not found: ${errorInfo.url}`;
      
      // Try to show a toast notification if available
      if (window.showToast) {
        window.showToast(message, 'error');
      } else {
        console.warn('💡 User Notification:', message);
      }
    }
  }
  
  startMonitoring() {
    this.isMonitoring = true;
    console.log('🔍 404 Error Detector started monitoring...');
  }
  
  stopMonitoring() {
    this.isMonitoring = false;
    
    // Restore original functions
    if (this.originalFetch) {
      window.fetch = this.originalFetch;
    }
    
    if (this.originalXHROpen) {
      XMLHttpRequest.prototype.open = this.originalXHROpen;
    }
    
    console.log('⏹️ 404 Error Detector stopped monitoring');
  }
  
  getErrors() {
    return this.errors;
  }
  
  getErrorSummary() {
    const summary = {
      total: this.errors.length,
      byType: {},
      byUrl: {},
      recent: this.errors.slice(-10)
    };
    
    this.errors.forEach(error => {
      // Count by type
      summary.byType[error.type] = (summary.byType[error.type] || 0) + 1;
      
      // Count by URL
      summary.byUrl[error.url] = (summary.byUrl[error.url] || 0) + 1;
    });
    
    return summary;
  }
  
  clearErrors() {
    this.errors = [];
    console.log('🧹 404 Error history cleared');
  }
  
  generateReport() {
    const summary = this.getErrorSummary();
    
    console.log('📊 404 Error Report:');
    console.log(`Total Errors: ${summary.total}`);
    console.log('Errors by Type:', summary.byType);
    console.log('Errors by URL:', summary.byUrl);
    console.log('Recent Errors:', summary.recent);
    
    return summary;
  }
}

// Create global instance
const error404Detector = new Error404Detector();

// Export for manual control
export default error404Detector;

// Global access for debugging
window.error404Detector = error404Detector;

// Auto-generate report every 30 seconds in development
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    const summary = error404Detector.getErrorSummary();
    if (summary.total > 0) {
      console.log(`🔍 404 Detector: ${summary.total} errors detected`);
    }
  }, 30000);
}

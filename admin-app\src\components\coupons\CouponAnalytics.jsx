/**
 * Enhanced ACE Social Coupon Analytics - Enterprise-grade coupon analytics component
 * Features: Comprehensive coupon analytics with advanced metrics visualization, performance
 * tracking, and revenue impact analysis for ACE Social promotional campaigns, detailed
 * analytics dashboard with usage statistics and redemption rate analysis, advanced analytics
 * features with real-time data visualization and trend analysis, ACE Social's analytics
 * system integration with seamless data aggregation and performance monitoring, analytics
 * interaction features including data filtering and metric comparison, analytics state
 * management with real-time data updates and metric caching, and real-time analytics
 * monitoring with live metric displays and automatic analytics optimization
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Alert,
  Skeleton,
  useTheme,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Stack,
  Divider,
  Button,
  ButtonGroup,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  DatePicker,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Badge,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide,
  Collapse
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Analytics as AnalyticsIcon,
  LocalOffer as CouponIcon,
  Redeem as RedeemIcon,
  AttachMoney as MoneyIcon,
  Timeline as TimelineIcon,
  Star as StarIcon,
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterIcon,
  DateRange as DateRangeIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  Insights as InsightsIcon,
  Compare as CompareIcon,
  ShowChart as ChartIcon,
  PieChart as PieChartIcon,
  BarChart as BarChartIcon,
  Schedule as ScheduleIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Settings as SettingsIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import {
  calculateCouponMetrics,
  formatCurrency,
  formatDate,
  formatDiscount,
  getCouponStatus,
  exportToCSV
} from '../../utils/couponHelpers';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Analytics constants
const ANALYTICS_VIEWS = {
  OVERVIEW: 'overview',
  PERFORMANCE: 'performance',
  TRENDS: 'trends',
  COMPARISON: 'comparison'
};

const TIME_PERIODS = {
  LAST_7_DAYS: 'last_7_days',
  LAST_30_DAYS: 'last_30_days',
  LAST_90_DAYS: 'last_90_days',
  LAST_YEAR: 'last_year',
  CUSTOM: 'custom'
};

const METRIC_TYPES = {
  REDEMPTIONS: 'redemptions',
  REVENUE: 'revenue',
  CONVERSION: 'conversion',
  USAGE: 'usage'
};

// Analytics events
const ANALYTICS_EVENTS = {
  VIEW_CHANGED: 'analytics_view_changed',
  METRIC_CLICKED: 'analytics_metric_clicked',
  DATA_EXPORTED: 'analytics_data_exported',
  FILTER_APPLIED: 'analytics_filter_applied',
  REFRESH_TRIGGERED: 'analytics_refresh_triggered'
};

/**
 * Enhanced Coupon Analytics - Comprehensive analytics dashboard with advanced features
 * Implements detailed coupon analytics management and enterprise-grade analytics capabilities
 */
const EnhancedCouponAnalytics = memo(forwardRef(({
  data,
  loading = false,
  error,
  onRefresh,
  className,
  enableAdvancedFeatures = true,
  enableRealTimeUpdates = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableExportOptions = true,
  enableComparison = true,
  defaultView = ANALYTICS_VIEWS.OVERVIEW,
  defaultTimePeriod = TIME_PERIODS.LAST_30_DAYS,
  autoRefreshInterval = 300000, // 5 minutes
  maxDataPoints = 1000,
  onViewChange,
  onMetricClick,
  onDataExport,
  onAnalyticsTrack,
  onFilterChange,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const analyticsRef = useRef(null);
  const chartRef = useRef(null);
  const tableRef = useRef(null);

  // Core state management
  const [refreshing, setRefreshing] = useState(false);
  const [currentView, setCurrentView] = useState(defaultView);
  const [timePeriod, setTimePeriod] = useState(defaultTimePeriod);
  const [selectedMetrics, setSelectedMetrics] = useState(new Set([METRIC_TYPES.REDEMPTIONS]));
  const [showAdvancedView, setShowAdvancedView] = useState(false);

  // Enhanced state management
  const [analyticsState, setAnalyticsState] = useState({
    lastUpdated: null,
    interactions: 0,
    exportCount: 0,
    viewTime: 0
  });
  const [filters, setFilters] = useState({
    status: 'all',
    discountType: 'all',
    dateRange: null,
    minRedemptions: 0
  });
  const [comparisonData, setComparisonData] = useState(null);
  const [realTimeEnabled, setRealTimeEnabled] = useState(enableRealTimeUpdates);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    refreshData: () => handleRefresh(),
    exportData: () => handleExport(),
    focusChart: () => chartRef.current?.focus(),

    // View methods
    changeView: (view) => handleViewChange(view),
    getCurrentView: () => currentView,
    toggleAdvancedView: () => setShowAdvancedView(!showAdvancedView),

    // Filter methods
    applyFilter: (filterType, value) => handleFilterChange(filterType, value),
    resetFilters: () => handleResetFilters(),
    getActiveFilters: () => filters,

    // Analytics methods
    getAnalyticsState: () => analyticsState,
    resetAnalytics: () => setAnalyticsState({
      lastUpdated: null,
      interactions: 0,
      exportCount: 0,
      viewTime: 0
    }),

    // Metric methods
    selectMetric: (metric) => handleMetricSelect(metric),
    getSelectedMetrics: () => Array.from(selectedMetrics),

    // Accessibility methods
    announceUpdate: (message) => announceToScreenReader(message),
    focusAnalytics: () => analyticsRef.current?.focus()
  }), [
    currentView,
    showAdvancedView,
    filters,
    analyticsState,
    selectedMetrics,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics calculation with filtering and comparison
  const analytics = useMemo(() => {
    if (!data?.coupons || !Array.isArray(data.coupons)) {
      return null;
    }

    // Apply filters to data
    let filteredCoupons = data.coupons.filter(coupon => {
      // Status filter
      if (filters.status !== 'all') {
        const status = getCouponStatus(coupon).status;
        if (status !== filters.status) return false;
      }

      // Discount type filter
      if (filters.discountType !== 'all' && coupon.discount_type !== filters.discountType) {
        return false;
      }

      // Minimum redemptions filter
      if ((coupon.redemption_count || 0) < filters.minRedemptions) {
        return false;
      }

      // Date range filter
      if (filters.dateRange) {
        const createdAt = new Date(coupon.created_at);
        if (createdAt < filters.dateRange.start || createdAt > filters.dateRange.end) {
          return false;
        }
      }

      return true;
    });

    // Apply time period filter
    const now = new Date();
    let periodStart;

    switch (timePeriod) {
      case TIME_PERIODS.LAST_7_DAYS:
        periodStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case TIME_PERIODS.LAST_30_DAYS:
        periodStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case TIME_PERIODS.LAST_90_DAYS:
        periodStart = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case TIME_PERIODS.LAST_YEAR:
        periodStart = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        periodStart = null;
    }

    if (periodStart) {
      filteredCoupons = filteredCoupons.filter(coupon => {
        const createdAt = new Date(coupon.created_at);
        return createdAt >= periodStart;
      });
    }

    const metrics = calculateCouponMetrics(filteredCoupons);

    // Enhanced trend calculation
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const fourteenDaysAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

    const recentCoupons = filteredCoupons.filter(coupon => {
      const createdAt = new Date(coupon.created_at);
      return createdAt >= sevenDaysAgo;
    });

    const previousCoupons = filteredCoupons.filter(coupon => {
      const createdAt = new Date(coupon.created_at);
      return createdAt >= fourteenDaysAgo && createdAt < sevenDaysAgo;
    });

    const weeklyTrend = recentCoupons.length - previousCoupons.length;
    const weeklyTrendPercent = previousCoupons.length > 0 ?
      ((recentCoupons.length - previousCoupons.length) / previousCoupons.length) * 100 : 0;

    // Enhanced top performing analysis
    const topPerforming = [...filteredCoupons]
      .sort((a, b) => (b.redemption_count || 0) - (a.redemption_count || 0))
      .slice(0, 10);

    // Performance categories
    const performanceCategories = {
      highPerformers: filteredCoupons.filter(c => (c.redemption_count || 0) > metrics.usageRate * 2),
      averagePerformers: filteredCoupons.filter(c => {
        const redemptions = c.redemption_count || 0;
        return redemptions >= metrics.usageRate * 0.5 && redemptions <= metrics.usageRate * 2;
      }),
      lowPerformers: filteredCoupons.filter(c => (c.redemption_count || 0) < metrics.usageRate * 0.5)
    };

    // Revenue impact by discount type
    const revenueByType = filteredCoupons.reduce((acc, coupon) => {
      const type = coupon.discount_type;
      const impact = (coupon.redemption_count || 0) * (coupon.discount_value || 0);
      acc[type] = (acc[type] || 0) + impact;
      return acc;
    }, {});

    return {
      ...metrics,
      filteredCount: filteredCoupons.length,
      weeklyNewCoupons: recentCoupons.length,
      weeklyTrend,
      weeklyTrendPercent,
      topPerforming,
      performanceCategories,
      revenueByType,
      averageDiscountValue: filteredCoupons.length > 0 ?
        filteredCoupons.reduce((sum, c) => sum + (c.discount_value || 0), 0) / filteredCoupons.length : 0
    };
  }, [data, filters, timePeriod]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (enableAnalytics) {
      const startTime = Date.now();
      setAnalyticsState(prev => ({
        ...prev,
        lastUpdated: new Date().toISOString()
      }));

      return () => {
        const viewTime = Date.now() - startTime;
        setAnalyticsState(prev => ({
          ...prev,
          viewTime: prev.viewTime + viewTime
        }));
      };
    }
  }, [enableAnalytics, currentView]);

  // Real-time updates effect
  useEffect(() => {
    if (realTimeEnabled && autoRefreshInterval > 0) {
      const interval = setInterval(() => {
        if (onRefresh && !refreshing) {
          handleRefresh();
        }
      }, autoRefreshInterval);

      return () => clearInterval(interval);
    }
  }, [realTimeEnabled, autoRefreshInterval, onRefresh, refreshing]);

  // Enhanced handler functions
  const handleRefresh = useCallback(async () => {
    if (onRefresh) {
      setRefreshing(true);

      if (enableAccessibility) {
        announceToScreenReader('Refreshing analytics data');
      }

      try {
        await onRefresh();

        setAnalyticsState(prev => ({
          ...prev,
          lastUpdated: new Date().toISOString(),
          interactions: prev.interactions + 1
        }));

        if (enableAnalytics && onAnalyticsTrack) {
          onAnalyticsTrack(ANALYTICS_EVENTS.REFRESH_TRIGGERED, {
            view: currentView,
            timestamp: new Date().toISOString()
          });
        }

        if (enableAccessibility) {
          announceToScreenReader('Analytics data refreshed successfully');
        }
      } catch (error) {
        if (enableAccessibility) {
          announceToScreenReader('Failed to refresh analytics data');
        }
      } finally {
        setRefreshing(false);
      }
    }
  }, [onRefresh, enableAccessibility, enableAnalytics, onAnalyticsTrack, currentView, announceToScreenReader]);

  const handleViewChange = useCallback((newView) => {
    setCurrentView(newView);

    setAnalyticsState(prev => ({
      ...prev,
      interactions: prev.interactions + 1
    }));

    if (onViewChange) {
      onViewChange(newView);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(ANALYTICS_EVENTS.VIEW_CHANGED, {
        previousView: currentView,
        newView,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Switched to ${newView} view`);
    }
  }, [currentView, onViewChange, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleMetricSelect = useCallback((metric) => {
    setSelectedMetrics(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(metric)) {
        newSelection.delete(metric);
      } else {
        newSelection.add(metric);
      }
      return newSelection;
    });

    if (onMetricClick) {
      onMetricClick(metric);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(ANALYTICS_EVENTS.METRIC_CLICKED, {
        metric,
        view: currentView,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      const action = selectedMetrics.has(metric) ? 'deselected' : 'selected';
      announceToScreenReader(`${metric} metric ${action}`);
    }
  }, [selectedMetrics, onMetricClick, enableAnalytics, enableAccessibility, onAnalyticsTrack, currentView, announceToScreenReader]);

  const handleFilterChange = useCallback((filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));

    if (onFilterChange) {
      onFilterChange(filterType, value);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(ANALYTICS_EVENTS.FILTER_APPLIED, {
        filterType,
        value,
        view: currentView,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Applied ${filterType} filter: ${value}`);
    }
  }, [onFilterChange, enableAnalytics, enableAccessibility, onAnalyticsTrack, currentView, announceToScreenReader]);

  const handleResetFilters = useCallback(() => {
    setFilters({
      status: 'all',
      discountType: 'all',
      dateRange: null,
      minRedemptions: 0
    });

    if (enableAccessibility) {
      announceToScreenReader('All filters reset');
    }
  }, [enableAccessibility, announceToScreenReader]);

  // Enhanced export handler
  const handleExport = useCallback(() => {
    if (!data?.coupons) return;

    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'name', label: 'Name' },
      { key: 'discount_type', label: 'Type' },
      { key: 'discount_value', label: 'Value', type: 'discount' },
      { key: 'redemption_count', label: 'Redemptions' },
      { key: 'created_at', label: 'Created At', type: 'date' },
      { key: 'is_active', label: 'Active', type: 'boolean' },
      { key: 'revenue_impact', label: 'Revenue Impact', type: 'currency' }
    ];

    // Include analytics summary in export
    const exportData = {
      summary: analytics,
      coupons: data.coupons,
      filters: filters,
      timePeriod: timePeriod,
      exportedAt: new Date().toISOString()
    };

    exportToCSV(data.coupons, `coupon-analytics-${formatDate(new Date())}`, exportColumns);

    setAnalyticsState(prev => ({
      ...prev,
      exportCount: prev.exportCount + 1
    }));

    if (onDataExport) {
      onDataExport(exportData);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(ANALYTICS_EVENTS.DATA_EXPORTED, {
        recordCount: data.coupons.length,
        view: currentView,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Exported ${data.coupons.length} coupon records`);
    }
  }, [data, analytics, filters, timePeriod, onDataExport, enableAnalytics, enableAccessibility, onAnalyticsTrack, currentView, announceToScreenReader]);

  // Enhanced loading skeleton
  const renderLoadingSkeleton = useCallback(() => (
    <Paper
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        p: 3
      }}
      className={className}
      {...props}
    >
      <Box sx={{ mb: 3 }}>
        <Skeleton variant="text" width="40%" height={40} />
        <Skeleton variant="text" width="60%" height={24} />
      </Box>

      <Grid container spacing={3}>
        {[1, 2, 3, 4].map((item) => (
          <Grid item xs={12} sm={6} md={3} key={item}>
            <Card sx={{ ...glassMorphismStyles, height: 140 }}>
              <CardContent>
                <Skeleton variant="text" width="60%" height={24} />
                <Skeleton variant="text" width="40%" height={48} />
                <Skeleton variant="text" width="80%" height={20} />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ mt: 4 }}>
        <Skeleton variant="rectangular" width="100%" height={300} />
      </Box>
    </Paper>
  ), [glassMorphismStyles, className, props]);

  // Render loading skeleton
  if (loading && !analytics) {
    return renderLoadingSkeleton();
  }

  // Enhanced error state
  const renderErrorState = useCallback(() => (
    <Paper
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        p: 3,
        textAlign: 'center'
      }}
      className={className}
      {...props}
    >
      <ErrorIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
      <Typography variant="h6" color="error.main" gutterBottom>
        Failed to Load Analytics Data
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {error || 'An unexpected error occurred while loading coupon analytics.'}
      </Typography>
      <Button
        variant="contained"
        onClick={handleRefresh}
        disabled={refreshing}
        startIcon={refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
        sx={{
          bgcolor: ACE_COLORS.PURPLE,
          '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
        }}
      >
        {refreshing ? 'Retrying...' : 'Retry'}
      </Button>
    </Paper>
  ), [error, glassMorphismStyles, className, props, handleRefresh, refreshing]);

  // Enhanced empty state
  const renderEmptyState = useCallback(() => (
    <Paper
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        p: 6,
        textAlign: 'center'
      }}
      className={className}
      {...props}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 120,
            height: 120,
            borderRadius: '50%',
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
            color: ACE_COLORS.PURPLE
          }}
        >
          <AnalyticsIcon sx={{ fontSize: 64 }} />
        </Box>

        <Typography variant="h5" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK }}>
          No Analytics Data Available
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400 }}>
          Analytics will appear here once you have coupons and redemptions.
          Start by creating some coupons to see performance insights.
        </Typography>

        <Button
          variant="outlined"
          onClick={handleRefresh}
          disabled={refreshing}
          startIcon={refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
          sx={{
            mt: 2,
            borderColor: ACE_COLORS.PURPLE,
            color: ACE_COLORS.PURPLE,
            '&:hover': {
              borderColor: ACE_COLORS.PURPLE,
              bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
            }
          }}
        >
          {refreshing ? 'Checking...' : 'Check for Data'}
        </Button>
      </Box>
    </Paper>
  ), [glassMorphismStyles, className, props, handleRefresh, refreshing]);

  // Render states
  if (error) {
    return renderErrorState();
  }

  if (!analytics) {
    return renderEmptyState();
  }

  return (
    <Paper
      ref={analyticsRef}
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        p: 3,
        position: 'relative',
        overflow: 'hidden'
      }}
      className={className}
      {...props}
    >
      {/* Enhanced Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 48,
              height: 48,
              borderRadius: 1,
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
              color: ACE_COLORS.PURPLE
            }}
          >
            <AnalyticsIcon />
          </Box>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK }}>
              Coupon Analytics Dashboard
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {analytics.filteredCount} coupons • Last updated: {analyticsState.lastUpdated ?
                new Date(analyticsState.lastUpdated).toLocaleTimeString() : 'Never'}
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" spacing={1}>
          {/* View Toggle */}
          <ButtonGroup size="small" variant="outlined">
            {Object.values(ANALYTICS_VIEWS).map((view) => (
              <Button
                key={view}
                onClick={() => handleViewChange(view)}
                variant={currentView === view ? 'contained' : 'outlined'}
                sx={{
                  ...(currentView === view && {
                    bgcolor: ACE_COLORS.PURPLE,
                    '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
                  })
                }}
              >
                {view.charAt(0).toUpperCase() + view.slice(1)}
              </Button>
            ))}
          </ButtonGroup>

          {/* Action Buttons */}
          <Tooltip title="Advanced Options">
            <IconButton
              onClick={() => setShowAdvancedView(!showAdvancedView)}
              sx={{ color: showAdvancedView ? ACE_COLORS.PURPLE : 'text.secondary' }}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Export Analytics Data">
            <IconButton
              onClick={handleExport}
              disabled={!data?.coupons?.length}
              sx={{ color: ACE_COLORS.PURPLE }}
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Refresh Data">
            <IconButton
              onClick={handleRefresh}
              disabled={refreshing}
              sx={{ color: ACE_COLORS.PURPLE }}
            >
              {refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
        </Stack>
      </Box>

      {/* Advanced Filters */}
      <Collapse in={showAdvancedView}>
        <Card variant="outlined" sx={{ mb: 3, p: 2 }}>
          <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FilterIcon />
            Advanced Filters & Options
          </Typography>

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Time Period</InputLabel>
                <Select
                  value={timePeriod}
                  onChange={(e) => setTimePeriod(e.target.value)}
                  label="Time Period"
                >
                  <MenuItem value={TIME_PERIODS.LAST_7_DAYS}>Last 7 Days</MenuItem>
                  <MenuItem value={TIME_PERIODS.LAST_30_DAYS}>Last 30 Days</MenuItem>
                  <MenuItem value={TIME_PERIODS.LAST_90_DAYS}>Last 90 Days</MenuItem>
                  <MenuItem value={TIME_PERIODS.LAST_YEAR}>Last Year</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="expired">Expired</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Discount Type</InputLabel>
                <Select
                  value={filters.discountType}
                  onChange={(e) => handleFilterChange('discountType', e.target.value)}
                  label="Discount Type"
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="percentage">Percentage</MenuItem>
                  <MenuItem value="fixed">Fixed Amount</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                size="small"
                type="number"
                label="Min Redemptions"
                value={filters.minRedemptions}
                onChange={(e) => handleFilterChange('minRedemptions', parseInt(e.target.value) || 0)}
                inputProps={{ min: 0 }}
              />
            </Grid>
          </Grid>

          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
            <Button size="small" onClick={handleResetFilters}>
              Reset Filters
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={() => setRealTimeEnabled(!realTimeEnabled)}
              sx={{
                bgcolor: realTimeEnabled ? ACE_COLORS.PURPLE : 'grey.500',
                '&:hover': { bgcolor: realTimeEnabled ? alpha(ACE_COLORS.PURPLE, 0.8) : 'grey.600' }
              }}
            >
              {realTimeEnabled ? 'Real-time ON' : 'Real-time OFF'}
            </Button>
          </Box>
        </Card>
      </Collapse>

      {/* Enhanced Analytics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Total Coupons */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              ...glassMorphismStyles,
              height: '100%',
              cursor: 'pointer',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'translateY(-2px)' }
            }}
            onClick={() => handleMetricSelect(METRIC_TYPES.REDEMPTIONS)}
          >
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Total Coupons
                </Typography>
                <Badge
                  badgeContent={selectedMetrics.has(METRIC_TYPES.REDEMPTIONS) ? '✓' : null}
                  color="primary"
                >
                  <CouponIcon sx={{ color: ACE_COLORS.PURPLE }} />
                </Badge>
              </Box>
              <Typography variant="h4" fontWeight="bold" sx={{ color: ACE_COLORS.PURPLE }}>
                {analytics.total.toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center" justifyContent="space-between" mt={1}>
                <Typography variant="body2" color="text.secondary">
                  {analytics.active} active
                </Typography>
                <Chip
                  label={`${((analytics.active / analytics.total) * 100).toFixed(1)}%`}
                  size="small"
                  color="primary"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Total Redemptions */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              ...glassMorphismStyles,
              height: '100%',
              cursor: 'pointer',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'translateY(-2px)' }
            }}
            onClick={() => handleMetricSelect(METRIC_TYPES.REDEMPTIONS)}
          >
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Total Redemptions
                </Typography>
                <Badge
                  badgeContent={selectedMetrics.has(METRIC_TYPES.REDEMPTIONS) ? '✓' : null}
                  color="success"
                >
                  <RedeemIcon sx={{ color: theme.palette.success.main }} />
                </Badge>
              </Box>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {analytics.totalRedemptions.toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mt={1}>
                <LinearProgress
                  variant="determinate"
                  value={Math.min(analytics.usageRate * 10, 100)}
                  sx={{
                    flexGrow: 1,
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: alpha(theme.palette.success.main, 0.2),
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: theme.palette.success.main
                    }
                  }}
                />
                <Typography variant="body2" color="text.secondary">
                  {analytics.usageRate.toFixed(1)} avg
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Revenue Impact */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              ...glassMorphismStyles,
              height: '100%',
              cursor: 'pointer',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'translateY(-2px)' }
            }}
            onClick={() => handleMetricSelect(METRIC_TYPES.REVENUE)}
          >
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Revenue Impact
                </Typography>
                <Badge
                  badgeContent={selectedMetrics.has(METRIC_TYPES.REVENUE) ? '✓' : null}
                  color="warning"
                >
                  <MoneyIcon sx={{ color: ACE_COLORS.YELLOW }} />
                </Badge>
              </Box>
              <Typography variant="h4" fontWeight="bold" sx={{ color: ACE_COLORS.YELLOW }}>
                {formatCurrency(analytics.totalRevenue)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Estimated discount value
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Weekly Trend */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              ...glassMorphismStyles,
              height: '100%',
              cursor: 'pointer',
              transition: 'transform 0.2s',
              '&:hover': { transform: 'translateY(-2px)' }
            }}
            onClick={() => handleMetricSelect(METRIC_TYPES.USAGE)}
          >
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Weekly Trend
                </Typography>
                <Badge
                  badgeContent={selectedMetrics.has(METRIC_TYPES.USAGE) ? '✓' : null}
                  color="info"
                >
                  <TimelineIcon color="info" />
                </Badge>
              </Box>
              <Typography variant="h4" fontWeight="bold" color="info.main">
                {analytics.weeklyNewCoupons}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mt={1}>
                {analytics.weeklyTrend >= 0 ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={analytics.weeklyTrend >= 0 ? 'success.main' : 'error.main'}
                  sx={{ fontWeight: 'medium' }}
                >
                  {analytics.weeklyTrend >= 0 ? '+' : ''}{analytics.weeklyTrend}
                  ({analytics.weeklyTrendPercent.toFixed(1)}%)
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Performing Coupons */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card variant="glass">
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <StarIcon />
                Top Performing Coupons
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Code</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Discount</TableCell>
                      <TableCell>Redemptions</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analytics.topPerforming.map((coupon) => {
                      const status = getCouponStatus(coupon);
                      return (
                        <TableRow key={coupon.id}>
                          <TableCell>
                            <Typography variant="body2" fontFamily="monospace" fontWeight="medium">
                              {coupon.code}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {coupon.name}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDiscount(coupon)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {coupon.redemption_count || 0}
                              {coupon.max_redemptions && ` / ${coupon.max_redemptions}`}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                              color={status.color}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Enhanced Quick Stats */}
        <Grid item xs={12} md={4}>
          <Card sx={{ ...glassMorphismStyles, height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AssessmentIcon />
                Performance Insights
              </Typography>
              <Stack spacing={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Conversion Rate
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="body2" fontWeight="medium">
                      {analytics.conversionRate.toFixed(1)}%
                    </Typography>
                    <Chip
                      label={analytics.conversionRate > 5 ? 'Good' : 'Needs Improvement'}
                      size="small"
                      color={analytics.conversionRate > 5 ? 'success' : 'warning'}
                    />
                  </Box>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Avg. Redemptions
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.usageRate.toFixed(1)} per coupon
                  </Typography>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Active Rate
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="body2" fontWeight="medium">
                      {analytics.total > 0 ? ((analytics.active / analytics.total) * 100).toFixed(1) : 0}%
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={analytics.total > 0 ? (analytics.active / analytics.total) * 100 : 0}
                      sx={{ width: 40, height: 4, borderRadius: 2 }}
                    />
                  </Box>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    This Week
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="body2" fontWeight="medium">
                      {analytics.weeklyNewCoupons} new
                    </Typography>
                    {analytics.weeklyTrend >= 0 ? (
                      <TrendingUpIcon color="success" fontSize="small" />
                    ) : (
                      <TrendingDownIcon color="error" fontSize="small" />
                    )}
                  </Box>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Paper>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedCouponAnalytics.propTypes = {
  // Core props
  data: PropTypes.shape({
    coupons: PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      code: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      discount_type: PropTypes.oneOf(['percentage', 'fixed']).isRequired,
      discount_value: PropTypes.number.isRequired,
      redemption_count: PropTypes.number,
      is_active: PropTypes.bool,
      created_at: PropTypes.string,
      max_redemptions: PropTypes.number
    }))
  }),
  loading: PropTypes.bool,
  error: PropTypes.string,
  onRefresh: PropTypes.func,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeUpdates: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableExportOptions: PropTypes.bool,
  enableComparison: PropTypes.bool,

  // Configuration
  defaultView: PropTypes.oneOf(Object.values(ANALYTICS_VIEWS)),
  defaultTimePeriod: PropTypes.oneOf(Object.values(TIME_PERIODS)),
  autoRefreshInterval: PropTypes.number,
  maxDataPoints: PropTypes.number,

  // Callback props
  onViewChange: PropTypes.func,
  onMetricClick: PropTypes.func,
  onDataExport: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onFilterChange: PropTypes.func,

  // Standard props
  className: PropTypes.string
};

// Default props
EnhancedCouponAnalytics.defaultProps = {
  data: { coupons: [] },
  loading: false,
  error: null,
  onRefresh: null,
  enableAdvancedFeatures: true,
  enableRealTimeUpdates: true,
  enableAccessibility: true,
  enableAnalytics: true,
  enableExportOptions: true,
  enableComparison: true,
  defaultView: ANALYTICS_VIEWS.OVERVIEW,
  defaultTimePeriod: TIME_PERIODS.LAST_30_DAYS,
  autoRefreshInterval: 300000,
  maxDataPoints: 1000,
  onViewChange: null,
  onMetricClick: null,
  onDataExport: null,
  onAnalyticsTrack: null,
  onFilterChange: null,
  className: ''
};

// Display name for debugging
EnhancedCouponAnalytics.displayName = 'EnhancedCouponAnalytics';

export default EnhancedCouponAnalytics;

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  Chip,
  Avatar,
  AvatarGroup,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  useTheme,
  Tab,
  Tabs,
  IconButton,
  Tooltip,
  Paper,
  Alert,
  AlertTitle,
  Container
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import GroupsIcon from '@mui/icons-material/Groups';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import MailIcon from '@mui/icons-material/Mail';
import { useAuth } from '../../contexts/AuthContext';
import { useTeam } from '../../contexts/TeamContext';
import { useConfirmation } from '../../contexts/ConfirmationContext';
import NoDataPlaceholder from '../../components/common/NoDataPlaceholder';
import TeamInvitationList from '../../components/teams/TeamInvitationList';

/**
 * TeamsSettings component for managing teams within the Settings page
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isEmbedded - Whether the component is embedded in another page
 */
const TeamsSettings = ({ isEmbedded = false }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { teams, invitations, loading, fetchTeams, fetchMyInvitations, createTeam, deleteTeam } = useTeam();
  const { showConfirmation } = useConfirmation();

  // Get the tab from URL query params or default to 'myteams'
  const defaultTeamsTab = searchParams.get('teamstab') || 'myteams';

  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [newTeamName, setNewTeamName] = useState('');
  const [newTeamDescription, setNewTeamDescription] = useState('');
  const [createLoading, setCreateLoading] = useState(false);
  const [teamsTabValue, setTeamsTabValue] = useState(defaultTeamsTab === 'invitations' ? 1 : 0);

  // Track if invitations have been loaded
  const [invitationsLoaded, setInvitationsLoaded] = useState(false);

  // Authentication check with redirect
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Fetch teams and invitations on mount
  useEffect(() => {
    if (!isAuthenticated || authLoading) return;

    const loadData = async () => {
      // Always fetch teams
      await fetchTeams();

      // Only fetch invitations if we're on the invitations tab or haven't loaded them yet
      if (teamsTabValue === 1 || !invitationsLoaded) {
        await fetchMyInvitations();
        setInvitationsLoaded(true);
      }
    };

    loadData();
  }, [fetchTeams, fetchMyInvitations, teamsTabValue, invitationsLoaded, isAuthenticated, authLoading]);

  // Update URL when tab changes, but avoid unnecessary URL updates
  useEffect(() => {
    const currentTab = searchParams.get('teamstab');
    const newTab = teamsTabValue === 0 ? 'myteams' : 'invitations';

    // Only update URL if the tab has actually changed
    if (currentTab !== newTab) {
      const newParams = new URLSearchParams(searchParams);
      newParams.set('teamstab', newTab);
      setSearchParams(newParams, { replace: true });
    }
  }, [teamsTabValue, setSearchParams, searchParams]);

  // Handle tab change
  const handleTeamsTabChange = (event, newValue) => {
    setTeamsTabValue(newValue);
  };

  // Handle create dialog open
  const handleCreateDialogOpen = () => {
    setOpenCreateDialog(true);
  };

  // Handle create dialog close
  const handleCreateDialogClose = () => {
    setOpenCreateDialog(false);
    setNewTeamName('');
    setNewTeamDescription('');
  };

  // Handle create team with authentication check
  const handleCreateTeam = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!newTeamName.trim()) return;

    setCreateLoading(true);

    try {
      const team = await createTeam({
        name: newTeamName.trim(),
        description: newTeamDescription.trim() || undefined
      });

      if (team) {
        handleCreateDialogClose();
        navigate(`/settings/teams/${team.id}`);
      }
    } finally {
      setCreateLoading(false);
    }
  };

  // Handle delete team with authentication check
  const handleDeleteTeam = (team) => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    showConfirmation({
      title: 'Delete Team',
      message: `Are you sure you want to delete the team "${team.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      confirmColor: 'error',
      onConfirm: async () => {
        await deleteTeam(team.id);
      }
    });
  };

  // Render team cards
  const renderTeamCards = () => {
    if (teams.length === 0) {
      return (
        <NoDataPlaceholder
          icon={<GroupsIcon sx={{ fontSize: 64 }} />}
          title="No Teams Found"
          description="Create your first team to start collaborating with others."
          actionText="Create Team"
          onAction={handleCreateDialogOpen}
        />
      );
    }

    return (
      <Grid container spacing={3}>
        {teams.map((team) => (
          <Grid item xs={12} sm={6} md={4} key={team.id}>
            <Card
              elevation={0}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                border: `1px solid ${theme.palette.divider}`,
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: theme.shadows[3],
                  borderColor: 'transparent'
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <GroupsIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6" component="h2" noWrap>
                      {team.name}
                    </Typography>
                  </Box>

                  {/* Team status and role chips */}
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <Chip
                      label={team.role || 'Member'}
                      size="small"
                      color={team.role === 'owner' ? 'primary' : team.role === 'admin' ? 'secondary' : 'default'}
                      variant="outlined"
                    />
                    <Chip
                      label={team.status || 'Active'}
                      size="small"
                      color={team.status === 'active' ? 'success' : 'warning'}
                      variant="filled"
                    />
                  </Box>
                </Box>

                {team.description && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {team.description}
                  </Typography>
                )}

                {/* Team statistics chips */}
                <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                  <Chip
                    label={`${team.members?.length || 0} Members`}
                    size="small"
                    icon={<GroupsIcon />}
                    color="info"
                    variant="outlined"
                  />
                  {team.created_at && (
                    <Chip
                      label={`Created ${new Date(team.created_at).toLocaleDateString()}`}
                      size="small"
                      color="default"
                      variant="outlined"
                    />
                  )}
                  {team.last_activity && (
                    <Chip
                      label={`Active ${new Date(team.last_activity).toLocaleDateString()}`}
                      size="small"
                      color="success"
                      variant="outlined"
                    />
                  )}
                </Box>

                <Divider sx={{ my: 1.5 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Team Members:
                  </Typography>
                  <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 28, height: 28, fontSize: '0.875rem' } }}>
                    {team.members.map((member) => (
                      <Tooltip title={member.full_name} key={member.user_id}>
                        <Avatar
                          alt={member.full_name}
                          src={member.avatar}
                          sx={{
                            bgcolor: !member.avatar ? theme.palette.primary.main : undefined
                          }}
                        >
                          {!member.avatar && member.full_name.charAt(0).toUpperCase()}
                        </Avatar>
                      </Tooltip>
                    ))}
                  </AvatarGroup>
                </Box>
              </CardContent>

              <CardActions sx={{ justifyContent: 'space-between', p: 2, pt: 0 }}>
                <Button
                  size="small"
                  onClick={() => navigate(`/settings/teams/${team.id}`)}
                >
                  View Details
                </Button>

                <Box>
                  <Tooltip title="Invite Members">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => navigate(`/settings/teams/${team.id}?tab=invite`)}
                      sx={{ mr: 1 }}
                    >
                      <MailIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Edit Team">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => navigate(`/settings/teams/${team.id}?tab=settings`)}
                      sx={{ mr: 1 }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Delete Team">
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteTeam(team)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  if (loading && teams.length === 0) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: 300, p: 3 }}>
        <CircularProgress size={40} />
        <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
          Loading teams...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {!isEmbedded && (
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box>
              <Typography variant="h4" component="h1">
                Teams
                {user?.name && (
                  <Typography component="span" variant="h6" color="text.secondary" sx={{ ml: 2 }}>
                    - {user.name}
                  </Typography>
                )}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage your teams and collaborations
                {user?.email && ` for ${user.email}`}
              </Typography>
            </Box>

            {/* Team statistics chips */}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                label={`${teams.length} Teams`}
                color="primary"
                variant="filled"
                icon={<GroupsIcon />}
              />
              {invitations && invitations.length > 0 && (
                <Chip
                  label={`${invitations.length} Pending Invitations`}
                  color="warning"
                  variant="outlined"
                  icon={<MailIcon />}
                />
              )}
              <Chip
                label={teams.filter(team => team.role === 'owner').length > 0 ? 'Team Owner' : 'Team Member'}
                color={teams.filter(team => team.role === 'owner').length > 0 ? 'success' : 'info'}
                variant="outlined"
              />
            </Box>
          </Box>
        </Box>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateDialogOpen}
          disabled={!isAuthenticated}
        >
          Create Team
        </Button>

        {!isAuthenticated && (
          <Alert severity="warning" sx={{ ml: 2 }}>
            <Typography variant="body2">
              Please log in to manage teams
            </Typography>
          </Alert>
        )}
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={teamsTabValue}
          onChange={handleTeamsTabChange}
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab label="My Teams" />
          <Tab label="Invitations" />
        </Tabs>
      </Paper>

      <Box
        sx={{
          mt: 2,
          minHeight: 400, // Provide a stable minimum height to prevent layout shifts
          position: 'relative'
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            opacity: teamsTabValue === 0 ? 1 : 0,
            visibility: teamsTabValue === 0 ? 'visible' : 'hidden',
            transition: 'opacity 0.3s ease'
          }}
        >
          {renderTeamCards()}
        </Box>

        <Box
          sx={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            opacity: teamsTabValue === 1 ? 1 : 0,
            visibility: teamsTabValue === 1 ? 'visible' : 'hidden',
            transition: 'opacity 0.3s ease'
          }}
        >
          <TeamInvitationList preloadedInvitations={invitationsLoaded ? invitations : null} />
        </Box>
      </Box>

      {/* Create Team Dialog */}
      <Dialog open={openCreateDialog} onClose={handleCreateDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box>
              <Typography variant="h6">Create New Team</Typography>
              {user?.name && (
                <Typography variant="body2" color="text.secondary">
                  Owner: {user.name}
                </Typography>
              )}
            </Box>
            <Chip
              label="You'll be the owner"
              size="small"
              color="primary"
              variant="outlined"
            />
          </Box>
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Team Name"
            fullWidth
            variant="outlined"
            value={newTeamName}
            onChange={(e) => setNewTeamName(e.target.value)}
            sx={{ mb: 2 }}
            helperText="Choose a descriptive name for your team"
          />
          <TextField
            margin="dense"
            label="Description (Optional)"
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            value={newTeamDescription}
            onChange={(e) => setNewTeamDescription(e.target.value)}
            helperText="Describe what this team will work on"
          />

          {/* Team creation tips */}
          <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip
              label="Invite members after creation"
              size="small"
              color="info"
              variant="outlined"
              icon={<MailIcon />}
            />
            <Chip
              label="Set permissions later"
              size="small"
              color="success"
              variant="outlined"
            />
            <Chip
              label="Free for all users"
              size="small"
              color="default"
              variant="outlined"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCreateDialogClose}>Cancel</Button>
          <Button
            onClick={handleCreateTeam}
            variant="contained"
            disabled={!newTeamName.trim() || createLoading}
            startIcon={createLoading ? <CircularProgress size={20} /> : null}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TeamsSettings;

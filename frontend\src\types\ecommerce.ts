/**
 * E-commerce related type definitions
 * Comprehensive types for product management and drag & drop functionality
 @since 2024-1-1 to 2025-25-7
*/

// Base product interface
export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  currency?: string;
  sku?: string;
  category?: string;
  tags?: string[];
  images?: ProductImage[];
  variants?: ProductVariant[];
  inventory?: ProductInventory;
  status: ProductStatus;
  visibility: ProductVisibility;
  seo?: ProductSEO;
  metadata?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

// Product image interface
export interface ProductImage {
  id: string;
  url: string;
  alt?: string;
  title?: string;
  isPrimary?: boolean;
  order?: number;
  metadata?: Record<string, unknown>;
}

// Product variant interface
export interface ProductVariant {
  id: string;
  name: string;
  sku?: string;
  price?: number;
  compareAtPrice?: number;
  inventory?: ProductInventory;
  attributes?: Record<string, string>;
  images?: ProductImage[];
  isDefault?: boolean;
}

// Product inventory interface
export interface ProductInventory {
  quantity: number;
  trackQuantity: boolean;
  allowBackorder: boolean;
  lowStockThreshold?: number;
  inStock: boolean;
}

// Product SEO interface
export interface ProductSEO {
  title?: string;
  description?: string;
  keywords?: string[];
  slug?: string;
}

// Product status enum
export enum ProductStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

// Product visibility enum
export enum ProductVisibility {
  PUBLIC = 'public',
  PRIVATE = 'private',
  HIDDEN = 'hidden'
}

// Drag & drop related interfaces
export interface DragDropState {
  isDragging: boolean;
  draggedItemId?: string;
  dropTargetId?: string;
  dragStartTime?: number;
}

export interface DragDropResult {
  sourceIndex: number;
  destinationIndex: number;
  productId: string;
  success: boolean;
  duration?: number;
}

// Bulk action interfaces
export interface BulkAction {
  type: BulkActionType;
  productIds: string[];
  data?: Record<string, unknown>;
}

export enum BulkActionType {
  DELETE = 'delete',
  ARCHIVE = 'archive',
  ACTIVATE = 'activate',
  UPDATE_CATEGORY = 'update_category',
  UPDATE_TAGS = 'update_tags',
  UPDATE_VISIBILITY = 'update_visibility',
  EXPORT = 'export'
}

// Product filter interfaces
export interface ProductFilter {
  category?: string;
  status?: ProductStatus;
  visibility?: ProductVisibility;
  tags?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  inStock?: boolean;
  searchQuery?: string;
}

// Product sort interfaces
export interface ProductSort {
  field: ProductSortField;
  direction: SortDirection;
}

export enum ProductSortField {
  NAME = 'name',
  PRICE = 'price',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  STATUS = 'status',
  INVENTORY = 'inventory'
}

export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc'
}

// Product management interfaces
export interface ProductListState {
  products: Product[];
  loading: boolean;
  error?: string;
  selectedIds: string[];
  filters: ProductFilter;
  sort: ProductSort;
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

// E-commerce store interfaces
export interface Store {
  id: string;
  name: string;
  domain?: string;
  platform: StorePlatform;
  credentials?: Record<string, unknown>;
  settings?: StoreSettings;
  isActive: boolean;
  lastSyncAt?: string;
  createdAt: string;
  updatedAt: string;
}

export enum StorePlatform {
  SHOPIFY = 'shopify',
  WOOCOMMERCE = 'woocommerce',
  MAGENTO = 'magento',
  BIGCOMMERCE = 'bigcommerce',
  CUSTOM = 'custom'
}

export interface StoreSettings {
  autoSync: boolean;
  syncInterval: number;
  defaultCurrency: string;
  timezone: string;
  notifications: {
    lowStock: boolean;
    newOrders: boolean;
    syncErrors: boolean;
  };
}

// Product sync interfaces
export interface ProductSync {
  id: string;
  storeId: string;
  productId: string;
  externalId: string;
  status: SyncStatus;
  lastSyncAt: string;
  error?: string;
  metadata?: Record<string, unknown>;
}

export enum SyncStatus {
  PENDING = 'pending',
  SYNCING = 'syncing',
  SUCCESS = 'success',
  ERROR = 'error',
  CONFLICT = 'conflict'
}

// Analytics interfaces
export interface ProductAnalytics {
  productId: string;
  views: number;
  clicks: number;
  conversions: number;
  revenue: number;
  period: AnalyticsPeriod;
  data: AnalyticsDataPoint[];
}

export interface AnalyticsDataPoint {
  date: string;
  views: number;
  clicks: number;
  conversions: number;
  revenue: number;
}

export enum AnalyticsPeriod {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year'
}

// Content generation interfaces
export interface ProductContentRequest {
  productId: string;
  type: ContentType;
  platform?: string;
  tone?: string;
  length?: ContentLength;
  includeHashtags?: boolean;
  includeEmojis?: boolean;
  customPrompt?: string;
}

export enum ContentType {
  DESCRIPTION = 'description',
  SOCIAL_POST = 'social_post',
  AD_COPY = 'ad_copy',
  EMAIL = 'email',
  BLOG_POST = 'blog_post'
}

export enum ContentLength {
  SHORT = 'short',
  MEDIUM = 'medium',
  LONG = 'long'
}

export interface GeneratedContent {
  id: string;
  productId: string;
  type: ContentType;
  content: string;
  metadata?: Record<string, unknown>;
  createdAt: string;
}

// Error interfaces
export interface EcommerceError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

// API response interfaces
export interface ProductListResponse {
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters?: ProductFilter;
  sort?: ProductSort;
}

export interface ProductResponse {
  product: Product;
  success: boolean;
  error?: EcommerceError;
}

export interface BulkActionResponse {
  success: boolean;
  processedCount: number;
  errors: EcommerceError[];
  results: Record<string, unknown>;
}

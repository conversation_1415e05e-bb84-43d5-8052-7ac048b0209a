// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  Divider,
  CircularProgress,
  <PERSON>ert,
  <PERSON>per,
  Step,
  StepLabel,
  Card,
  CardContent,
  useTheme,
  alpha,
  Link,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import LockIcon from '@mui/icons-material/Lock';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';

import api from '../../api';
import { useAuth } from '../../hooks/useAuth';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';

const RedeemCode = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user } = useAuth();
  const { showSuccess, showError } = useAdvancedToast();

  // Get code from URL query parameter
  const queryParams = new URLSearchParams(location.search);
  const codeFromUrl = queryParams.get('code');

  // State
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [code, setCode] = useState(codeFromUrl || '');
  const [email, setEmail] = useState(user?.email || '');
  const [fullName, setFullName] = useState(user?.full_name || '');
  const [companyName, setCompanyName] = useState(user?.company_name || '');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [verificationResult, setVerificationResult] = useState(null);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [error, setError] = useState(null);

  // Verify code on mount if provided in URL
  useEffect(() => {
    if (codeFromUrl) {
      verifyCode(codeFromUrl);
    }
  }, [codeFromUrl, verifyCode]);

  // Steps for the redemption process
  const steps = [
    'Verify Code',
    'Account Details',
    'Confirmation'
  ];

  // Verify the AppSumo code
  const verifyCode = useCallback(async (codeToVerify) => {
    setVerifying(true);
    setError(null);

    try {
      const response = await api.post('/api/appsumo/verify', {
        code: codeToVerify || code
      });

      setVerificationResult(response.data);

      if (response.data.is_valid) {
        // Move to next step if code is valid
        setActiveStep(1);
      } else {
        setError(response.data.message);
      }
    } catch (err) {
      console.error('Error verifying AppSumo code:', err);
      setError(err.response?.data?.detail || 'Failed to verify code');
      showError('Failed to verify AppSumo code');
    } finally {
      setVerifying(false);
    }
  }, [code, showError]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Validate form
    if (!email || !fullName) {
      setError('Please fill in all required fields');
      setLoading(false);
      return;
    }

    // If user is not authenticated, validate password
    if (!isAuthenticated) {
      if (!password) {
        setError('Password is required');
        setLoading(false);
        return;
      }

      if (password !== confirmPassword) {
        setError('Passwords do not match');
        setLoading(false);
        return;
      }

      if (password.length < 8) {
        setError('Password must be at least 8 characters long');
        setLoading(false);
        return;
      }
    }

    // Validate terms acceptance
    if (!acceptTerms) {
      setError('You must accept the terms and conditions');
      setLoading(false);
      return;
    }

    try {
      const response = await api.post('/api/appsumo/redeem', {
        code,
        email,
        full_name: fullName,
        company_name: companyName,
        password: isAuthenticated ? undefined : password
      });

      if (response.data.success) {
        showSuccess(response.data.message);
        setActiveStep(2);

        // Redirect to dashboard after a delay if redirect URL is provided
        if (response.data.redirect_url) {
          setTimeout(() => {
            window.location.href = response.data.redirect_url;
          }, 3000);
        }
      } else {
        setError(response.data.message);
      }
    } catch (err) {
      console.error('Error redeeming AppSumo code:', err);
      setError(err.response?.data?.detail || 'Failed to redeem code');
      showError('Failed to redeem AppSumo code');
    } finally {
      setLoading(false);
    }
  };

  // Render the verification step
  const renderVerificationStep = () => {
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Enter your AppSumo code
        </Typography>

        <Typography variant="body2" color="text.secondary" paragraph>
          Please enter the code you received from AppSumo to verify and redeem your lifetime deal.
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
          <TextField
            fullWidth
            label="AppSumo Code"
            value={code}
            onChange={(e) => setCode(e.target.value.toUpperCase())}
            disabled={verifying}
            placeholder="e.g., AS-B2BINF-12345"
            sx={{ mr: 1 }}
          />
          <Button
            variant="contained"
            onClick={() => verifyCode()}
            disabled={!code.trim() || verifying}
            sx={{ height: '56px', minWidth: '120px' }}
          >
            {verifying ? <CircularProgress size={24} /> : 'Verify'}
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Typography variant="body2" color="text.secondary">
          Don&apos;t have a code? <Link href="https://appsumo.com/products/b2b-influencer-tool" target="_blank" rel="noopener">Purchase on AppSumo</Link>
        </Typography>
      </Box>
    );
  };

  // Render the account details step
  const renderAccountDetailsStep = () => {
    return (
      <Box component="form" onSubmit={handleSubmit}>
        <Typography variant="h6" gutterBottom>
          Account Details
        </Typography>

        <Typography variant="body2" color="text.secondary" paragraph>
          {isAuthenticated
            ? "You're already logged in. Please confirm your details below."
            : "Please provide your details to create an account and redeem your AppSumo code."}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isAuthenticated}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Full Name"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Company Name"
              value={companyName}
              onChange={(e) => setCompanyName(e.target.value)}
            />
          </Grid>

          {!isAuthenticated && (
            <>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Confirm Password"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                />
              </Grid>
            </>
          )}
        </Grid>

        <Box sx={{ mt: 3 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Typography variant="body2">
                I accept the <Link href="/terms" target="_blank">Terms and Conditions</Link> and <Link href="/privacy" target="_blank">Privacy Policy</Link>
              </Typography>
            }
          />
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            variant="outlined"
            onClick={() => setActiveStep(0)}
            disabled={loading}
          >
            Back
          </Button>

          <Button
            type="submit"
            variant="contained"
            disabled={loading || !acceptTerms}
          >
            {loading ? <CircularProgress size={24} /> : 'Redeem Code'}
          </Button>
        </Box>
      </Box>
    );
  };

  // Render the confirmation step
  const renderConfirmationStep = () => {
    // Get tier name from verification result
    const tierName = verificationResult?.tier?.name || '';
    const tierDescription = verificationResult?.tier?.description || '';

    // Get key features based on tier
    const tierFeatures = [];

    if (verificationResult?.tier) {
      const tier = verificationResult.tier;

      // Add key features based on tier limits
      if (tier.max_users) tierFeatures.push(`${tier.max_users} User Account${tier.max_users > 1 ? 's' : ''}`);
      if (tier.max_social_accounts) tierFeatures.push(`${tier.max_social_accounts} Social Media Accounts`);
      if (tier.max_posts_per_month) tierFeatures.push(`${tier.max_posts_per_month} Posts per Month`);
      if (tier.max_icps) tierFeatures.push(`${tier.max_icps} Ideal Customer Profiles`);
      if (tier.max_campaigns) tierFeatures.push(`${tier.max_campaigns} Campaigns`);
      if (tier.analytics_history_days) tierFeatures.push(`${tier.analytics_history_days} Days of Analytics History`);
    }

    return (
      <Box sx={{ textAlign: 'center' }}>
        <Box sx={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: 100,
          height: 100,
          borderRadius: '50%',
          backgroundColor: alpha(theme.palette.success.main, 0.1),
          mb: 3
        }}>
          <CheckCircleIcon color="success" sx={{ fontSize: 64 }} />
        </Box>

        <Typography variant="h4" gutterBottom>
          Congratulations!
        </Typography>

        <Typography variant="h6" color="primary" gutterBottom>
          Your AppSumo Lifetime Deal is now active!
        </Typography>

        <Typography variant="body1" paragraph>
          You now have lifetime access to the B2B Influencer Tool with the <strong>{tierName}</strong> tier.
        </Typography>

        <Box sx={{
          p: 3,
          my: 3,
          borderRadius: 2,
          backgroundColor: alpha('#FF8C00', 0.05),
          border: `1px solid ${alpha('#FF8C00', 0.2)}`
        }}>
          <Typography variant="subtitle1" gutterBottom sx={{ color: '#FF8C00' }}>
            <CardGiftcardIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            {tierName} Tier Benefits
          </Typography>

          <Typography variant="body2" paragraph>
            {tierDescription}
          </Typography>

          <Grid container spacing={2} sx={{ mt: 1 }}>
            {tierFeatures.map((feature, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="body2">{feature}</Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>

        <Typography variant="body2" color="text.secondary" paragraph>
          You will be redirected to the dashboard in a few seconds...
        </Typography>

        <Button
          variant="contained"
          onClick={() => navigate('/dashboard')}
          sx={{
            mt: 2,
            backgroundColor: '#FF8C00',
            '&:hover': {
              backgroundColor: alpha('#FF8C00', 0.8),
            }
          }}
        >
          Go to Dashboard
        </Button>
      </Box>
    );
  };

  // Render the current step
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return renderVerificationStep();
      case 1:
        return renderAccountDetailsStep();
      case 2:
        return renderConfirmationStep();
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Paper
        elevation={3}
        sx={{
          p: 4,
          borderRadius: 2,
          backdropFilter: 'blur(20px)',
          backgroundColor: alpha(theme.palette.background.paper, 0.8),
          border: `1px solid ${alpha('#FF8C00', 0.3)}`,
          boxShadow: activeStep === 2 ? `0 0 20px ${alpha('#FF8C00', 0.2)}` : theme.shadows[3]
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <CardGiftcardIcon sx={{ fontSize: 32, mr: 2, color: '#FF8C00' }} />
          <Typography variant="h4" sx={{ color: activeStep === 2 ? '#FF8C00' : 'inherit' }}>
            AppSumo Lifetime Deal
          </Typography>
        </Box>

        <Stepper
          activeStep={activeStep}
          sx={{
            mb: 4,
            '& .MuiStepIcon-root.Mui-active': {
              color: '#FF8C00',
            },
            '& .MuiStepIcon-root.Mui-completed': {
              color: '#FF8C00',
            }
          }}
        >
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {verificationResult?.is_valid && activeStep >= 1 && (
          <Card
            variant="outlined"
            sx={{
              mb: 3,
              backgroundColor: alpha('#FF8C00', 0.05),
              border: `1px solid ${alpha('#FF8C00', 0.3)}`,
              borderRadius: 2
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <VerifiedUserIcon sx={{ mr: 1, color: '#FF8C00' }} />
                <Typography variant="subtitle1" fontWeight="bold">
                  Valid AppSumo Code: {code}
                </Typography>
              </Box>

              <Divider sx={{ my: 1.5 }} />

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2">
                    <strong>Tier:</strong> {verificationResult.tier?.name}
                  </Typography>

                  <Typography variant="body2">
                    <strong>Description:</strong> {verificationResult.tier?.description}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="body2">
                    <strong>User Accounts:</strong> {verificationResult.tier?.max_users}
                  </Typography>

                  <Typography variant="body2">
                    <strong>Social Accounts:</strong> {verificationResult.tier?.max_social_accounts}
                  </Typography>

                  <Typography variant="body2">
                    <strong>Monthly Posts:</strong> {verificationResult.tier?.max_posts_per_month}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {getStepContent(activeStep)}
      </Paper>

      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 3 }}>
        <LockIcon fontSize="small" color="action" sx={{ mr: 0.5 }} />
        <Typography variant="caption" color="text.secondary">
          Secure redemption powered by B2B Influencer Tool
        </Typography>
      </Box>
    </Box>
  );
};

export default RedeemCode;

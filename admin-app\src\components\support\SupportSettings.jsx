import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  Schedule as ScheduleIcon,
  Security as SecurityIcon,
  Settings as SettingsIcon,
  Webhook as WebhookIcon,
  Save as SaveIcon,
} from '@mui/icons-material';

const SupportSettings = () => {
  const [settings, setSettings] = useState({
    // SLA Settings
    sla_creator_hours: 24,
    sla_accelerator_hours: 12,
    sla_dominator_hours: 4,
    sla_critical_multiplier: 0.25,
    sla_high_multiplier: 0.5,
    sla_medium_multiplier: 1.0,
    sla_low_multiplier: 1.5,
    
    // Auto-assignment
    auto_assignment_enabled: true,
    auto_assignment_by_specialization: true,
    auto_assignment_load_balancing: true,
    
    // Notifications
    email_notifications_enabled: true,
    sms_notifications_enabled: false,
    webhook_notifications_enabled: true,
    escalation_notifications: true,
    sla_breach_notifications: true,
    
    // Business Hours
    business_hours_enabled: true,
    business_start_hour: 9,
    business_end_hour: 18,
    business_timezone: 'UTC',
    weekend_support: false,
    
    // Escalation Rules
    escalation_enabled: true,
    escalation_after_hours: 24,
    escalation_after_responses: 3,
    auto_escalate_critical: true,
    
    // Customer Portal
    customer_portal_enabled: true,
    customer_self_service: true,
    customer_satisfaction_surveys: true,
    survey_delay_hours: 24,
  });

  const [webhooks, setWebhooks] = useState([
    {
      id: '1',
      name: 'Slack Integration',
      url: 'https://hooks.slack.com/services/...',
      events: ['ticket_created', 'ticket_escalated', 'sla_breach'],
      is_active: true,
    },
    {
      id: '2',
      name: 'CRM Sync',
      url: 'https://api.crm.com/webhooks/support',
      events: ['ticket_resolved', 'customer_satisfaction'],
      is_active: true,
    },
  ]);

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(null);
  const [error, setError] = useState(null);

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSaveSettings = async () => {
    try {
      setLoading(true);

      // Implement API call to save settings
      const response = await fetch('/api/admin/support/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify(settings)
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      setSuccess('Settings saved successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Failed to save settings');
      setTimeout(() => setError(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  const timezones = [
    'UTC',
    'America/New_York',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Australia/Sydney',
  ];

  const webhookEvents = [
    'ticket_created',
    'ticket_updated',
    'ticket_assigned',
    'ticket_resolved',
    'ticket_closed',
    'ticket_escalated',
    'sla_breach',
    'customer_satisfaction',
  ];

  return (
    <Box>
      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* SLA Configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="SLA Configuration"
              avatar={<ScheduleIcon />}
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure response time targets by subscription tier and priority level.
              </Typography>
              
              <Typography variant="subtitle2" gutterBottom>
                Response Time by Tier (hours)
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Creator"
                    type="number"
                    value={settings.sla_creator_hours}
                    onChange={(e) => handleSettingChange('sla_creator_hours', parseInt(e.target.value))}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Accelerator"
                    type="number"
                    value={settings.sla_accelerator_hours}
                    onChange={(e) => handleSettingChange('sla_accelerator_hours', parseInt(e.target.value))}
                  />
                </Grid>
                <Grid item xs={4}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Dominator"
                    type="number"
                    value={settings.sla_dominator_hours}
                    onChange={(e) => handleSettingChange('sla_dominator_hours', parseInt(e.target.value))}
                  />
                </Grid>
              </Grid>

              <Typography variant="subtitle2" gutterBottom>
                Priority Multipliers
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Critical"
                    type="number"
                    step="0.1"
                    value={settings.sla_critical_multiplier}
                    onChange={(e) => handleSettingChange('sla_critical_multiplier', parseFloat(e.target.value))}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="High"
                    type="number"
                    step="0.1"
                    value={settings.sla_high_multiplier}
                    onChange={(e) => handleSettingChange('sla_high_multiplier', parseFloat(e.target.value))}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Medium"
                    type="number"
                    step="0.1"
                    value={settings.sla_medium_multiplier}
                    onChange={(e) => handleSettingChange('sla_medium_multiplier', parseFloat(e.target.value))}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Low"
                    type="number"
                    step="0.1"
                    value={settings.sla_low_multiplier}
                    onChange={(e) => handleSettingChange('sla_low_multiplier', parseFloat(e.target.value))}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Auto-Assignment Rules */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Auto-Assignment Rules"
              avatar={<SettingsIcon />}
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure automatic ticket assignment and routing rules.
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemText
                    primary="Enable Auto-Assignment"
                    secondary="Automatically assign tickets to available agents"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.auto_assignment_enabled}
                      onChange={(e) => handleSettingChange('auto_assignment_enabled', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemText
                    primary="Assignment by Specialization"
                    secondary="Route tickets based on agent specializations"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.auto_assignment_by_specialization}
                      onChange={(e) => handleSettingChange('auto_assignment_by_specialization', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemText
                    primary="Load Balancing"
                    secondary="Distribute tickets evenly among agents"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.auto_assignment_load_balancing}
                      onChange={(e) => handleSettingChange('auto_assignment_load_balancing', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Notification Settings"
              avatar={<NotificationsIcon />}
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure notification channels and triggers.
              </Typography>
              
              <List>
                <ListItem>
                  <ListItemIcon>
                    <EmailIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email Notifications"
                    secondary="Send notifications via email"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.email_notifications_enabled}
                      onChange={(e) => handleSettingChange('email_notifications_enabled', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <SmsIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="SMS Notifications"
                    secondary="Send critical alerts via SMS"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.sms_notifications_enabled}
                      onChange={(e) => handleSettingChange('sms_notifications_enabled', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    <WebhookIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Webhook Notifications"
                    secondary="Send events to external systems"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.webhook_notifications_enabled}
                      onChange={(e) => handleSettingChange('webhook_notifications_enabled', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Business Hours */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader 
              title="Business Hours"
              avatar={<ScheduleIcon />}
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure business hours for SLA calculations.
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.business_hours_enabled}
                    onChange={(e) => handleSettingChange('business_hours_enabled', e.target.checked)}
                  />
                }
                label="Enable Business Hours"
                sx={{ mb: 2 }}
              />
              
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="Start Hour"
                    type="number"
                    value={settings.business_start_hour}
                    onChange={(e) => handleSettingChange('business_start_hour', parseInt(e.target.value))}
                    inputProps={{ min: 0, max: 23 }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    size="small"
                    label="End Hour"
                    type="number"
                    value={settings.business_end_hour}
                    onChange={(e) => handleSettingChange('business_end_hour', parseInt(e.target.value))}
                    inputProps={{ min: 0, max: 23 }}
                  />
                </Grid>
              </Grid>
              
              <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                <InputLabel>Timezone</InputLabel>
                <Select
                  value={settings.business_timezone}
                  label="Timezone"
                  onChange={(e) => handleSettingChange('business_timezone', e.target.value)}
                >
                  {timezones.map((tz) => (
                    <MenuItem key={tz} value={tz}>{tz}</MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.weekend_support}
                    onChange={(e) => handleSettingChange('weekend_support', e.target.checked)}
                  />
                }
                label="Weekend Support"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Webhook Configuration */}
        <Grid item xs={12}>
          <Card>
            <CardHeader 
              title="Webhook Configuration"
              avatar={<WebhookIcon />}
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure webhooks for external system integration.
              </Typography>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>URL</TableCell>
                      <TableCell>Events</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {webhooks.map((webhook) => (
                      <TableRow key={webhook.id}>
                        <TableCell>{webhook.name}</TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                            {webhook.url}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" flexWrap="wrap" gap={0.5}>
                            {webhook.events.map((event) => (
                              <Chip
                                key={event}
                                label={event.replace('_', ' ')}
                                size="small"
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={webhook.is_active ? 'Active' : 'Inactive'}
                            color={webhook.is_active ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Button size="small" variant="outlined">
                            Edit
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Save Button */}
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              size="large"
              startIcon={<SaveIcon />}
              onClick={handleSaveSettings}
            >
              Save All Settings
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SupportSettings;

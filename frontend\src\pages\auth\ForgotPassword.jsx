// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from "react";
import { Link as RouterLink } from "react-router-dom";
import {
  Box,
  Button,
  TextField,
  Typography,
  Link,

  CircularProgress,
  Alert,

  Card,
  CardContent,
  Fade,
  Slide,
  useTheme,
  alpha,
  InputAdornment,
} from "@mui/material";
import {
  Email,
  ArrowBack,
  SendRounded,

  CheckCircle,
} from "@mui/icons-material";
import { useAuth } from "../../hooks/useAuth";
import { useNotification } from "../../hooks/useNotification";
import Logo from "../../components/common/Logo";

const ForgotPassword = () => {
  const { requestPasswordReset } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const theme = useTheme();

  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const [mounted, setMounted] = useState(false);

  // Animation effect
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleChange = (e) => {
    setEmail(e.target.value);
    // Clear error when user types
    if (error) {
      setError("");
    }
  };

  const validateForm = () => {
    if (!email) {
      setError("Email is required");
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setError("Email is invalid");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError("");

    try {
      const result = await requestPasswordReset(email);

      if (result.success) {
        setSuccess(true);
        showSuccessNotification(
          "Password reset instructions sent to your email."
        );
      } else {
        setError(
          result.error || "Failed to request password reset. Please try again."
        );
        showErrorNotification(
          result.error || "Failed to request password reset. Please try again."
        );
      }
    } catch (err) {
      console.error("Password reset request error:", err);
      setError("An unexpected error occurred. Please try again.");
      showErrorNotification("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Fade in={mounted} timeout={800}>
      <Box
        sx={{
          width: "100%",
          position: "relative",
        }}
      >
        {/* Modern Header with Logo and Gradient */}
        <Slide direction="down" in={mounted} timeout={600}>
          <Box sx={{ mb: 4, textAlign: "center", position: "relative" }}>
            {/* Background Gradient Effect */}
            <Box
              sx={{
                position: "absolute",
                top: -20,
                left: "50%",
                transform: "translateX(-50%)",
                width: 120,
                height: 120,
                background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
                borderRadius: "50%",
                filter: "blur(40px)",
                zIndex: 0,
              }}
            />

            {/* Logo */}
            <Box sx={{ position: "relative", zIndex: 1, mb: 3 }}>
              <Logo size="large" />
            </Box>

            {/* Header Text */}
            <Typography
              variant="h3"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.primary.main} 100%)`,
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                mb: 1,
                position: "relative",
                zIndex: 1,
              }}
            >
              Reset Password
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                position: "relative",
                zIndex: 1,
                maxWidth: 400,
                mx: "auto",
              }}
            >
              Enter your email address and we&apos;ll send you instructions to reset your password
            </Typography>
          </Box>
        </Slide>

        {/* Error Alert */}
        {error && (
          <Slide direction="up" in={!!error} timeout={400}>
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                "& .MuiAlert-icon": {
                  fontSize: "1.5rem",
                },
              }}
            >
              {error}
            </Alert>
          </Slide>
        )}

        {/* Success or Form Content */}
        <Slide direction="up" in={mounted} timeout={1000}>
          <Card
            sx={{
              background: theme.palette.background.paper,
              borderRadius: 3,
              boxShadow: `0 12px 40px ${alpha(theme.palette.primary.main, 0.15)}`,
              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              overflow: "hidden",
            }}
          >
            <CardContent sx={{ p: 4 }}>
              {success ? (
                <Box sx={{ textAlign: "center" }}>
                  <CheckCircle
                    sx={{
                      fontSize: 64,
                      color: theme.palette.success.main,
                      mb: 2,
                    }}
                  />
                  <Alert
                    severity="success"
                    sx={{
                      mb: 3,
                      borderRadius: 2,
                      background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)} 0%, ${alpha(theme.palette.success.main, 0.05)} 100%)`,
                      border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                    }}
                  >
                    Password reset instructions have been sent to your email address.
                    Please check your inbox and follow the instructions to reset your password.
                  </Alert>
                  <Button
                    component={RouterLink}
                    to="/login"
                    variant="contained"
                    fullWidth
                    startIcon={<ArrowBack />}
                    sx={{
                      minHeight: 56,
                      borderRadius: 2,
                      fontSize: "1.1rem",
                      fontWeight: 600,
                      textTransform: "none",
                    }}
                  >
                    Back to Login
                  </Button>
                </Box>
              ) : (
                <Box component="form" onSubmit={handleSubmit} sx={{ width: "100%" }}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    name="email"
                    type="email"
                    value={email}
                    onChange={handleChange}
                    margin="normal"
                    error={!!error}
                    helperText={error}
                    required
                    autoFocus
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email color="action" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: 2,
                        minHeight: 56,
                        transition: "all 0.3s ease",
                        "&:hover": {
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: alpha(theme.palette.primary.main, 0.5),
                          },
                        },
                        "&.Mui-focused": {
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: theme.palette.primary.main,
                            borderWidth: 2,
                          },
                        },
                      },
                    }}
                  />

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SendRounded />}
                    sx={{
                      mt: 4,
                      minHeight: 56,
                      borderRadius: 2,
                      fontSize: "1.1rem",
                      fontWeight: 600,
                      textTransform: "none",
                      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                      boxShadow: `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`,
                      transition: "all 0.3s ease",
                      "&:hover": {
                        background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
                        boxShadow: `0 12px 32px ${alpha(theme.palette.primary.main, 0.4)}`,
                        transform: "translateY(-2px)",
                      },
                      "&:disabled": {
                        background: alpha(theme.palette.action.disabled, 0.12),
                        color: theme.palette.action.disabled,
                        boxShadow: "none",
                      },
                    }}
                  >
                    {loading ? "Sending..." : "Send Reset Instructions"}
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Slide>

        {/* Back to Login Link */}
        <Slide direction="up" in={mounted} timeout={1200}>
          <Box sx={{ mt: 4, textAlign: "center" }}>
            <Typography variant="body1" color="text.secondary">
              Remember your password?{" "}
              <Link
                component={RouterLink}
                to="/login"
                variant="body1"
                underline="hover"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.primary.main,
                  transition: "all 0.3s ease",
                  "&:hover": {
                    color: theme.palette.secondary.main,
                  },
                }}
              >
                Back to Login
              </Link>
            </Typography>
          </Box>
        </Slide>
      </Box>
    </Fade>
  );
};

export default ForgotPassword;

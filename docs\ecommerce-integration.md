<!-- @since 2024-1-1 to 2025-25-7 -->
# E-commerce Integration Documentation

## Overview

The E-commerce Integration feature seamlessly integrates with the existing ACEO architecture to provide comprehensive e-commerce store management, product synchronization, and AI-powered content generation for online retailers.

## Features

### Core Functionality
- **Multi-Platform Support**: Connect Shopify and WooCommerce stores
- **Real-time Synchronization**: Automatic product sync with webhook support
- **AI Content Generation**: Product-focused content creation
- **ICP Analysis**: Customer persona generation based on product data
- **Campaign Management**: Product-targeted marketing campaigns
- **Add-on Integration**: Seamless billing and usage tracking

### Supported Platforms
- **Shopify**: Full OAuth integration with Admin API
- **WooCommerce**: REST API integration with consumer key/secret authentication

## Architecture

### Backend Components

#### Models (`backend/app/models/ecommerce.py`)
- `EcommerceStore`: Store connection and configuration
- `SyncedProduct`: Product data with variants and images
- `SyncLog`: Synchronization operation tracking
- `ProductContentGeneration`: Content generation tracking
- `EcommerceWebhook`: Webhook event processing
- `EcommerceAnalytics`: Performance metrics and insights

#### Services
- `EcommerceService`: Main service for store and product management
- `EcommerceWebhookService`: Real-time webhook processing
- `EcommerceSyncScheduler`: Background synchronization
- `EcommerceICPGenerator`: Product-based ICP generation
- `EcommerceCampaignService`: Enhanced campaign management
- `EcommerceAddonIntegration`: ACEO add-on system integration

#### Platform Integrations
- `ShopifyIntegration`: Shopify-specific API integration
- `WooCommerceIntegration`: WooCommerce-specific API integration
- `EcommerceIntegrationFactory`: Platform integration factory

### Frontend Components

#### Pages
- `EcommerceStorePage`: Store management interface
- `ProductManagementPage`: Product browsing and management
- `ProductContentGenerator`: AI-powered content creation

#### Features
- Responsive Material-UI design
- Real-time sync status updates
- Bulk product operations
- Integrated content generation
- Campaign creation workflows

## API Endpoints

### Store Management
```
GET    /api/ecommerce/stores                    # Get user stores
POST   /api/ecommerce/stores/connect            # Connect new store
DELETE /api/ecommerce/stores/{store_id}         # Disconnect store
POST   /api/ecommerce/stores/{store_id}/sync    # Trigger sync
```

### Product Management
```
GET    /api/ecommerce/stores/{store_id}/products              # Get products
POST   /api/ecommerce/stores/{store_id}/products/generate-icps # Generate ICPs
```

### Campaign Management
```
POST   /api/ecommerce/campaigns/product-campaign                    # Create campaign
POST   /api/ecommerce/campaigns/{campaign_id}/generate-content      # Generate content
```

### Webhooks
```
POST   /api/ecommerce/webhooks/{platform}      # Handle platform webhooks
```

### Monitoring
```
GET    /api/ecommerce/sync/status               # Get sync status
```

## Setup and Configuration

### Environment Variables
```bash
# Shopify Configuration
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret

# WooCommerce Configuration (per-store basis)
# Configured during store connection

# Feature Flags
ECOMMERCE_INTEGRATION_ENABLED=true
```

### Database Collections
The integration creates the following MongoDB collections:
- `ecommerce_stores`: Store connections and configuration
- `synced_products`: Product data from connected stores
- `ecommerce_sync_logs`: Synchronization operation logs
- `ecommerce_webhooks`: Webhook event tracking
- `ecommerce_analytics`: Performance metrics

### ACEO Add-on Configuration
The following add-ons are available:
- `ecommerce_store_connections`: Store connection credits
- `product_content_generation`: Content generation credits
- `ecommerce_icp_generation`: ICP generation credits
- `ecommerce_campaigns`: Campaign management credits

## Usage Guide

### Connecting a Store

#### Shopify
1. Navigate to E-commerce Stores page
2. Click "Connect Store"
3. Select "Shopify" platform
4. Enter store URL (optional, can be detected)
5. Complete OAuth flow on Shopify
6. Store will be connected and initial sync will begin

#### WooCommerce
1. Navigate to E-commerce Stores page
2. Click "Connect Store"
3. Select "WooCommerce" platform
4. Enter store URL
5. Provide consumer key and secret from WooCommerce
6. Store will be connected and initial sync will begin

### Product Synchronization

#### Automatic Sync
- Products sync automatically every hour by default
- Real-time updates via webhooks when configured
- Sync status visible in store dashboard

#### Manual Sync
- Click sync button on store card
- Force full sync option available
- Progress tracked in sync logs

### Content Generation

#### Product-Based Content
1. Select products from product management page
2. Click "Generate Content"
3. Configure content settings:
   - Platform (Instagram, Facebook, Twitter, LinkedIn, TikTok)
   - Tone (Professional, Casual, Engaging, etc.)
   - Content type (Post, Story, Caption, Ad)
   - Include pricing, hashtags, call-to-action
4. AI generates optimized content with product details

#### Bulk Operations
- Select multiple products for batch content generation
- Consistent branding across all generated content
- Automatic product image integration

### ICP Generation

#### Product-Based ICPs
1. Select products or product category
2. Click "Generate ICPs"
3. AI analyzes product data to create customer personas:
   - Demographics based on product pricing and category
   - Pain points related to product benefits
   - Goals aligned with product value proposition
   - Content preferences for target audience

### Campaign Management

#### Product Campaigns
1. Create new campaign with product focus
2. Select target products
3. Set budget allocation per product
4. Configure campaign goals and metrics
5. Generate product-specific content
6. Track e-commerce metrics (ROAS, conversions, etc.)

## Add-on Integration

### Feature Gates
E-commerce features are gated by subscription level and add-on purchases:
- **Free Plan**: No e-commerce features
- **Paid Plans**: Basic e-commerce integration
- **Add-ons**: Extended capabilities and higher limits

### Usage Tracking
All e-commerce operations are tracked for billing:
- Store connections
- Product content generation
- ICP generation
- Campaign creation

### Billing Integration
Seamless integration with existing Lemon Squeezy billing:
- Automatic usage deduction
- Low credit warnings
- Upsell prompts
- Usage analytics

## Performance Considerations

### Optimization Features
- **Pagination**: Large product catalogs handled efficiently
- **Caching**: Redis caching for frequently accessed data
- **Background Processing**: Sync operations run asynchronously
- **Rate Limiting**: Respects platform API limits
- **Incremental Sync**: Only sync changed products

### Scalability
- Supports stores with 10,000+ products
- Concurrent sync operations
- Horizontal scaling ready
- Database indexing optimized

## Security

### Data Protection
- Encrypted credential storage
- Secure OAuth flows
- Webhook signature verification
- GDPR compliance ready

### Access Control
- User-based store isolation
- Feature-based access control
- Add-on usage validation
- Audit logging

## Monitoring and Analytics

### Sync Monitoring
- Real-time sync status
- Error tracking and alerting
- Performance metrics
- Success/failure rates

### Business Metrics
- Store connection rates
- Content generation usage
- Campaign performance
- Revenue attribution

## Troubleshooting

### Common Issues

#### Store Connection Failures
- Verify API credentials
- Check store URL format
- Ensure proper permissions
- Review error logs

#### Sync Issues
- Check webhook configuration
- Verify API rate limits
- Review sync logs
- Test manual sync

#### Content Generation Problems
- Verify add-on credits
- Check product data quality
- Review generation settings
- Test with different products

### Error Codes
- `STORE_NOT_FOUND`: Store ID invalid or not accessible
- `INSUFFICIENT_CREDITS`: Add-on credits exhausted
- `SYNC_FAILED`: Product synchronization error
- `WEBHOOK_VERIFICATION_FAILED`: Invalid webhook signature

## Development

### Testing
Run the test suite:
```bash
# Unit tests
pytest backend/tests/test_ecommerce_service.py

# Integration tests
pytest backend/tests/test_ecommerce_api.py

# Frontend tests
npm test -- --testPathPattern=ecommerce
```

### Adding New Platforms
1. Create new integration class extending `BaseEcommerceIntegration`
2. Implement required methods for OAuth, sync, and webhooks
3. Add platform to `EcommerceIntegrationFactory`
4. Update frontend platform selection
5. Add platform-specific tests

### Extending Features
1. Follow existing service patterns
2. Integrate with ACEO add-on system
3. Add appropriate feature gates
4. Update documentation
5. Add comprehensive tests

## Support

For technical support or feature requests:
- Check existing documentation
- Review error logs
- Contact development team
- Submit feature requests through proper channels

/**
 * Enhanced Step Wrapper - Enterprise-grade step wrapper component
 * Features: Comprehensive step wrapper with advanced step management capabilities, multi-dimensional step navigation,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced step wrapper capabilities and seamless services system workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  memo,
  forwardRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Paper,
  Typography,
  useTheme,
  alpha,
  Skeleton,
  Alert,
  Button,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  Fade,
  Grow,
  CircularProgress
} from '@mui/material';
// Enhanced step wrapper component

/**
 * Enhanced Step Wrapper Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Step title
 * @param {string} props.description - Step description
 * @param {React.ReactNode} props.children - Step content
 * @param {boolean|string} [props.loading=false] - Loading state
 * @param {string} [props.error=null] - Error message
 * @param {Function} [props.onRetry=null] - Retry callback
 * @param {number} [props.minHeight=400] - Minimum height
 * @param {string} [props.loadingMessage='Loading...'] - Loading message
 * @param {boolean} [props.showProgress=false] - Show progress indicator
 * @param {number} [props.progress=0] - Progress value
 * @param {Function} [props.onStepAction] - Step action callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {boolean} [props.enableRealTimeUpdates=true] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-step-wrapper'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const StepWrapper = memo(forwardRef(({
  title,
  description,
  children,
  loading = false,
  error = null,
  onRetry = null,
  minHeight = 400,
  loadingMessage = "Loading...",
  showProgress = false,
  progress = 0
}) => {
  const theme = useTheme();

  // Glass morphism styles
  const glassMorphismStyles = {
    background: `linear-gradient(135deg, 
      ${alpha(theme.palette.background.paper, 0.8)} 0%, 
      ${alpha(theme.palette.background.default, 0.4)} 100%)`,
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.1)}`,
  };

  // Enhanced loading skeleton with form-like structure
  const renderLoadingSkeleton = () => (
    <Box sx={{ p: theme.spacing(3) }}>
      {/* Header skeleton */}
      <Box sx={{ mb: theme.spacing(3) }}>
        <Skeleton variant="text" width="60%" height={40} sx={{ mb: 1 }} />
        <Skeleton variant="text" width="80%" height={24} />
      </Box>

      {/* Progress indicator */}
      {showProgress && (
        <Box sx={{ mb: theme.spacing(3) }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Skeleton variant="text" width="30%" height={20} />
            <Skeleton variant="text" width="20%" height={20} />
          </Box>
          <LinearProgress
            variant={progress > 0 ? "determinate" : "indeterminate"}
            value={progress}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>
      )}

      {/* Form sections skeleton */}
      <Grid container spacing={3}>
        {/* Section 1 */}
        <Grid item xs={12}>
          <Card elevation={0} sx={{ border: `1px solid ${theme.palette.divider}` }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1 }} />
                <Skeleton variant="text" width="40%" height={28} />
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Skeleton variant="rectangular" width="100%" height={56} sx={{ borderRadius: 1 }} />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Skeleton variant="rectangular" width="100%" height={56} sx={{ borderRadius: 1 }} />
                </Grid>
                <Grid item xs={12}>
                  <Skeleton variant="rectangular" width="100%" height={120} sx={{ borderRadius: 1 }} />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Section 2 */}
        <Grid item xs={12}>
          <Card elevation={0} sx={{ border: `1px solid ${theme.palette.divider}` }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1 }} />
                <Skeleton variant="text" width="35%" height={28} />
              </Box>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Skeleton variant="rectangular" width="100%" height={56} sx={{ borderRadius: 1 }} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Skeleton variant="rectangular" width="100%" height={56} sx={{ borderRadius: 1 }} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <Skeleton variant="rectangular" width="100%" height={56} sx={{ borderRadius: 1 }} />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action buttons skeleton */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
        <Skeleton variant="text" width="40%" height={20} />
        <Skeleton variant="rectangular" width={180} height={48} sx={{ borderRadius: 2 }} />
      </Box>
    </Box>
  );

  // Enhanced loading overlay for partial loading states
  const renderLoadingOverlay = () => (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(4px)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1,
        borderRadius: theme.spacing(2),
      }}
    >
      <CircularProgress size={48} sx={{ mb: 2 }} />
      <Typography variant="h6" color="textSecondary" gutterBottom>
        {loadingMessage}
      </Typography>
      {showProgress && progress > 0 && (
        <Box sx={{ width: '60%', mt: 2 }}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{ height: 8, borderRadius: 4 }}
          />
          <Typography variant="body2" color="textSecondary" textAlign="center" sx={{ mt: 1 }}>
            {Math.round(progress)}% complete
          </Typography>
        </Box>
      )}
    </Box>
  );

  return (
    <Paper
      elevation={0}
      sx={{
        position: 'relative',
        minHeight,
        ...glassMorphismStyles,
        // WCAG 2.1 AA compliance
        '&:focus-visible': {
          outline: `2px solid ${theme.palette.primary.main}`,
          outlineOffset: '2px',
        },
        // Responsive design
        [theme.breakpoints.down('sm')]: {
          borderRadius: theme.spacing(1),
        },
        // High contrast mode support
        '@media (prefers-contrast: high)': {
          border: `2px solid ${theme.palette.text.primary}`,
          background: theme.palette.background.paper,
          backdropFilter: 'none',
        },
        // Reduced motion support
        '@media (prefers-reduced-motion: reduce)': {
          transition: 'none',
        },
      }}
    >
      {loading === true ? (
        renderLoadingSkeleton()
      ) : (
        <>
          <Fade in={!loading} timeout={300}>
            <Box sx={{ p: theme.spacing(3) }}>
              {/* Step Header */}
              {(title || description) && (
                <Grow in={!loading} timeout={500}>
                  <Box sx={{ mb: theme.spacing(3) }}>
                    {title && (
                      <Typography
                        variant="h5"
                        component="h2"
                        gutterBottom
                        sx={{
                          fontWeight: 600,
                          color: theme.palette.text.primary
                        }}
                      >
                        {title}
                      </Typography>
                    )}
                    {description && (
                      <Typography
                        variant="body1"
                        color="textSecondary"
                        sx={{ lineHeight: 1.6 }}
                      >
                        {description}
                      </Typography>
                    )}
                  </Box>
                </Grow>
              )}

              {/* Error Alert */}
              {error && (
                <Grow in={Boolean(error)} timeout={300}>
                  <Alert
                    severity="error"
                    sx={{ mb: theme.spacing(3) }}
                    action={onRetry && (
                      <Button color="inherit" size="small" onClick={onRetry}>
                        Retry
                      </Button>
                    )}
                  >
                    {error}
                  </Alert>
                </Grow>
              )}

              {/* Step Content */}
              <Fade in={!loading} timeout={700}>
                <Box
                  sx={{
                    // Ensure content area has proper spacing
                    '& > *:not(:last-child)': {
                      mb: theme.spacing(2),
                    },
                    // Form styling
                    '& .MuiTextField-root': {
                      mb: theme.spacing(2),
                    },
                    '& .MuiFormControl-root': {
                      mb: theme.spacing(2),
                    },
                    // Button group styling
                    '& .MuiButtonGroup-root': {
                      mt: theme.spacing(2),
                    },
                  }}
                >
                  {children}
                </Box>
              </Fade>
            </Box>
          </Fade>

          {/* Partial loading overlay */}
          {loading === 'partial' && renderLoadingOverlay()}
        </>
      )}
    </Paper>
  );
}));

// Enhanced PropTypes with comprehensive validation
StepWrapper.propTypes = {
  // Core props
  title: PropTypes.string,
  description: PropTypes.string,
  children: PropTypes.node,
  loading: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),
  error: PropTypes.string,
  onRetry: PropTypes.func,
  minHeight: PropTypes.number,
  loadingMessage: PropTypes.string,
  showProgress: PropTypes.bool,
  progress: PropTypes.number
};

StepWrapper.displayName = 'StepWrapper';

export default StepWrapper;

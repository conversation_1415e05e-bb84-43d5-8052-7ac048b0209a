// @since 2024-1-1 to 2025-25-7
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ChartErrorBoundary from '../ChartErrorBoundary';

// Mock the advanced toast hook
const mockToast = {
  showError: vi.fn(),
  showSuccess: vi.fn()
};

vi.mock('../../../hooks/useAdvancedToast', () => ({
  useAdvancedToast: () => mockToast,
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Mock URL.createObjectURL and revokeObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-url');
global.URL.revokeObjectURL = vi.fn();

// Mock fetch for error reporting
global.fetch = vi.fn();

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }) => {
  if (shouldThrow) {
    throw new Error('Test chart error');
  }
  return <div>Chart content</div>;
};

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      error: {
        main: '#F44336',
      },
      warning: {
        main: '#FF9800',
      },
      text: {
        secondary: '#666666',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ChartErrorBoundary', () => {
  const mockProps = {
    chartType: 'Bar Chart',
    onError: vi.fn(),
    onRetry: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    console.error = vi.fn(); // Suppress error logs in tests
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders children when no error occurs', () => {
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={false} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Chart content')).toBeInTheDocument();
  });

  test('renders error fallback when error occurs', () => {
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Bar Chart Unavailable')).toBeInTheDocument();
    expect(screen.getByText(/having trouble displaying this chart/)).toBeInTheDocument();
  });

  test('displays error category and chart type chips', () => {
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('unknown')).toBeInTheDocument();
    expect(screen.getByText('Bar Chart')).toBeInTheDocument();
  });

  test('handles retry functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    const retryButton = screen.getByText('Try Again');
    await user.click(retryButton);

    expect(mockProps.onRetry).toHaveBeenCalled();
  });

  test('disables retry button after max retries', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps} maxRetries={2}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    const retryButton = screen.getByText('Try Again');
    
    // Click retry button twice to reach max retries
    await user.click(retryButton);
    await user.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText('Max Retries Reached')).toBeInTheDocument();
    });
  });

  test('toggles error details visibility', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    const toggleButton = screen.getByText('Show Technical Details');
    await user.click(toggleButton);

    expect(screen.getByText('Hide Technical Details')).toBeInTheDocument();
    expect(screen.getByText('Error Details')).toBeInTheDocument();
  });

  test('exports chart data when enabled', async () => {
    const user = userEvent.setup();
    const mockData = [{ x: 1, y: 2 }, { x: 2, y: 4 }];
    
    // Mock document.createElement and appendChild
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn()
    };
    const createElement = vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    const appendChild = vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    const removeChild = vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps} data={mockData} enableDataExport={true}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    const exportButton = screen.getByText('Export Data');
    await user.click(exportButton);

    expect(createElement).toHaveBeenCalledWith('a');
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockToast.showSuccess).toHaveBeenCalledWith('Chart data exported successfully');
    
    createElement.mockRestore();
    appendChild.mockRestore();
    removeChild.mockRestore();
  });

  test('copies error details to clipboard', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps} enableErrorCopy={true}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    const copyButton = screen.getByText('Copy Error');
    await user.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalled();
    expect(mockToast.showSuccess).toHaveBeenCalledWith('Error details copied to clipboard');
  });

  test('handles clipboard copy failure', async () => {
    const user = userEvent.setup();
    navigator.clipboard.writeText.mockRejectedValueOnce(new Error('Clipboard error'));
    
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps} enableErrorCopy={true}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    const copyButton = screen.getByText('Copy Error');
    await user.click(copyButton);

    await waitFor(() => {
      expect(mockToast.showError).toHaveBeenCalledWith('Failed to copy error details');
    });
  });

  test('categorizes different error types', () => {
    const DataError = () => {
      throw new Error('Data parsing failed');
    };

    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <DataError />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('data')).toBeInTheDocument();
  });

  test('shows retry count when retries have been attempted', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps} maxRetries={3}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    const retryButton = screen.getByText('Try Again');
    await user.click(retryButton);

    expect(screen.getByText('Retry attempts: 1/3')).toBeInTheDocument();
  });

  test('handles feedback button click', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    const feedbackButton = screen.getByText('Report Issue');
    await user.click(feedbackButton);

    expect(mockToast.showError).toHaveBeenCalledWith('Feedback feature coming soon!');
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    // Check for ARIA attributes
    const errorContainer = screen.getByRole('alert');
    expect(errorContainer).toHaveAttribute('aria-live', 'polite');
    expect(errorContainer).toHaveAttribute('aria-label', 'Chart error: Bar Chart is currently unavailable');

    // Check button accessibility
    const retryButton = screen.getByLabelText(/Retry loading Bar Chart/);
    expect(retryButton).toBeInTheDocument();

    const reportButton = screen.getByLabelText('Report chart issue');
    expect(reportButton).toBeInTheDocument();
  });

  test('handles different chart types with appropriate icons', () => {
    const { rerender } = render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps} chartType="pie">
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('pie')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps} chartType="line">
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('line')).toBeInTheDocument();
  });

  test('sends error to monitoring service in production', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(fetch).toHaveBeenCalledWith('/api/analytics/chart-error', expect.objectContaining({
      method: 'POST',
      headers: expect.objectContaining({
        'Content-Type': 'application/json'
      })
    }));

    process.env.NODE_ENV = originalEnv;
  });

  test('tracks performance issues for slow renders', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const SlowComponent = () => {
      // Simulate slow render by throwing after delay
      setTimeout(() => {
        throw new Error('Slow render error');
      }, 300);
      return <div>Slow component</div>;
    };

    render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps} performanceThreshold={200}>
          <SlowComponent />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    process.env.NODE_ENV = originalEnv;
  });

  test('resets error state when children change', () => {
    const { rerender } = render(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={true} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Bar Chart Unavailable')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <ChartErrorBoundary {...mockProps}>
          <ThrowError shouldThrow={false} />
        </ChartErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Chart content')).toBeInTheDocument();
  });
});

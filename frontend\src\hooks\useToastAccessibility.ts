/**
 * Enhanced Toast Accessibility Hook
 * Production-ready accessibility features for toast notifications
 *
 * Features:
 * - Comprehensive WCAG 2.1 AA compliance
 * - Performance monitoring and analytics integration
 * - Advanced screen reader support and announcements
 * - Enhanced keyboard navigation with focus management
 * - User preference detection and adaptation
 * - High contrast and reduced motion support
 * - Advanced error handling and recovery
 * - Real-time accessibility validation
 * - Comprehensive event tracking and logging
 * - Advanced debugging and diagnostics
 *
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import { useEffect, useRef, useCallback, useState, useMemo } from 'react';
import { ToastState } from '../types/toast';

// Enhanced logging utility for production-ready accessibility monitoring
const logger = {
  debug: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[useToastAccessibility] ${message}`, data);
    }
  },
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.info(`[useToastAccessibility] ${message}`, data);
    }

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production' && (window as any).analytics) {
      (window as any).analytics.track('Toast Accessibility Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message: string, data?: any) => {
    console.warn(`[useToastAccessibility] ${message}`, data);

    // Always send warnings to analytics
    if ((window as any).analytics) {
      (window as any).analytics.track('Toast Accessibility Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message: string, error?: any) => {
    console.error(`[useToastAccessibility] ${message}`, error);

    // Always send errors to analytics
    if ((window as any).analytics) {
      (window as any).analytics.track('Toast Accessibility Error', {
        message,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Configuration constants
const CONFIG = {
  ANALYTICS_ENABLED: true,
  PERFORMANCE_MONITORING: true,
  VALIDATION_ENABLED: true,
  ANNOUNCEMENT_DELAY: 100,
  COUNT_ANNOUNCEMENT_DELAY: 500,
  FOCUS_TIMEOUT: 50,
  MAX_ANNOUNCEMENT_LENGTH: 200,
};

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  ANNOUNCEMENT_CREATION: 10, // 10ms
  FOCUS_OPERATION: 5, // 5ms
  KEYBOARD_NAVIGATION: 16, // 16ms (60fps)
  PREFERENCE_DETECTION: 20, // 20ms
  STYLE_GENERATION: 15 // 15ms
};

// Accessibility validation rules
const VALIDATION_RULES = {
  MIN_CONTRAST_RATIO: 4.5,
  MIN_TOUCH_TARGET_SIZE: 44,
  MAX_ANNOUNCEMENT_LENGTH: 200,
  REQUIRED_ARIA_ATTRIBUTES: ['role', 'aria-live', 'aria-atomic']
};

// Enhanced accessibility metrics interface
interface AccessibilityMetrics {
  totalAnnouncements: number;
  successfulAnnouncements: number;
  failedAnnouncements: number;
  focusOperations: number;
  keyboardNavigations: number;
  preferenceDetections: number;
  validationChecks: number;
  averageAnnouncementTime: number;
  lastOperationTime: number | null;
}

// Enhanced accessibility health interface
interface AccessibilityHealth {
  status: 'initializing' | 'active' | 'degraded' | 'error' | 'reset';
  performanceIssues: Array<{
    type: string;
    operation: string;
    duration: number;
    threshold: number;
    timestamp: string;
  }>;
  validationIssues: Array<{
    type: string;
    element: string;
    rule: string;
    severity: 'warning' | 'error';
    timestamp: string;
  }>;
  lastHealthCheck: string | null;
}

// Enhanced user preferences interface
interface UserPreferences {
  prefersReducedMotion: boolean;
  prefersHighContrast: boolean;
  prefersColorScheme: 'light' | 'dark';
  prefersReducedData: boolean;
  prefersReducedTransparency: boolean;
  fontSize: number;
  screenReaderActive: boolean;
}

// Enhanced accessibility options interface
interface ToastAccessibilityOptions {
  announceToScreenReader?: boolean;
  focusOnShow?: boolean;
  enableKeyboardNavigation?: boolean;
  ariaLive?: 'polite' | 'assertive' | 'off';
  ariaAtomic?: boolean;
  respectReducedMotion?: boolean;
  highContrastSupport?: boolean;
  enableAnalytics?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableValidation?: boolean;
  maxAnnouncementLength?: number;
  announcementDelay?: number;
  focusTimeout?: number;
  onAccessibilityEvent?: (event: string, data: any) => void;
  onValidationIssue?: (issue: any) => void;
  onPerformanceIssue?: (issue: any) => void;
}

// Enhanced accessibility styles interface
interface AccessibilityStyles {
  transition?: string;
  animation?: string;
  border?: string;
  backgroundColor?: string;
  color?: string;
  fontSize?: string;
  lineHeight?: string;
  padding?: string;
  minHeight?: string;
  outline?: string;
  boxShadow?: string;
}

const defaultOptions: ToastAccessibilityOptions = {
  announceToScreenReader: true,
  focusOnShow: false,
  enableKeyboardNavigation: true,
  ariaLive: 'polite',
  ariaAtomic: true,
  respectReducedMotion: true,
  highContrastSupport: true,
  enableAnalytics: CONFIG.ANALYTICS_ENABLED,
  enablePerformanceMonitoring: CONFIG.PERFORMANCE_MONITORING,
  enableValidation: CONFIG.VALIDATION_ENABLED,
  maxAnnouncementLength: CONFIG.MAX_ANNOUNCEMENT_LENGTH,
  announcementDelay: CONFIG.ANNOUNCEMENT_DELAY,
  focusTimeout: CONFIG.FOCUS_TIMEOUT,
};

export const useToastAccessibility = (
  toasts: ToastState[],
  options: ToastAccessibilityOptions = {}
) => {
  // Memoized and validated configuration
  const validatedConfig = useMemo(() => {
    const config = { ...defaultOptions, ...options };

    // Validate configuration values
    if (config.maxAnnouncementLength && config.maxAnnouncementLength < 50) {
      logger.warn('Max announcement length too short, using minimum of 50 characters');
      config.maxAnnouncementLength = 50;
    }

    if (config.announcementDelay && config.announcementDelay < 0) {
      logger.warn('Announcement delay cannot be negative, using 0');
      config.announcementDelay = 0;
    }

    return config;
  }, [options]);

  // Enhanced state management
  const [accessibilityMetrics, setAccessibilityMetrics] = useState<AccessibilityMetrics>({
    totalAnnouncements: 0,
    successfulAnnouncements: 0,
    failedAnnouncements: 0,
    focusOperations: 0,
    keyboardNavigations: 0,
    preferenceDetections: 0,
    validationChecks: 0,
    averageAnnouncementTime: 0,
    lastOperationTime: null
  });

  const [accessibilityHealth, setAccessibilityHealth] = useState<AccessibilityHealth>({
    status: 'initializing',
    performanceIssues: [],
    validationIssues: [],
    lastHealthCheck: null
  });

  const [userPreferences, setUserPreferences] = useState<UserPreferences>({
    prefersReducedMotion: false,
    prefersHighContrast: false,
    prefersColorScheme: 'light',
    prefersReducedData: false,
    prefersReducedTransparency: false,
    fontSize: 16,
    screenReaderActive: false
  });

  const [operationHistory, setOperationHistory] = useState<Array<{
    type: string;
    duration: number;
    success: boolean;
    timestamp: string;
  }>>([]);

  // Refs for tracking and performance
  const announcerRef = useRef<HTMLDivElement | null>(null);
  const focusedToastRef = useRef<string | null>(null);
  const keyboardNavigationRef = useRef<{
    currentIndex: number;
    toastIds: string[];
  }>({ currentIndex: -1, toastIds: [] });
  const startTimeRef = useRef(Date.now());
  const lastAnnouncementRef = useRef<string>('');

  logger.debug('Toast accessibility hook initialized', {
    config: validatedConfig,
    toastCount: toasts.length,
    timestamp: new Date().toISOString()
  });

  // Enhanced operation tracking and performance monitoring
  const trackOperation = useCallback((operationType: string, duration: number, success = true) => {
    const operationData = {
      type: operationType,
      duration,
      success,
      timestamp: new Date().toISOString()
    };

    // Update operation history
    setOperationHistory(prev => [
      operationData,
      ...prev.slice(0, 49) // Keep last 50 operations
    ]);

    // Update metrics
    setAccessibilityMetrics(prev => ({
      ...prev,
      lastOperationTime: Date.now(),
      averageAnnouncementTime: operationType === 'announcement' && prev.totalAnnouncements > 0 ?
        (prev.averageAnnouncementTime + duration) / 2 : prev.averageAnnouncementTime,
      [operationType]: (prev[operationType as keyof AccessibilityMetrics] as number || 0) + 1
    }));

    // Check performance
    const threshold = PERFORMANCE_THRESHOLDS[operationType.toUpperCase() as keyof typeof PERFORMANCE_THRESHOLDS] ||
                     PERFORMANCE_THRESHOLDS.ANNOUNCEMENT_CREATION;

    if (duration > threshold) {
      setAccessibilityHealth(prev => ({
        ...prev,
        performanceIssues: [...prev.performanceIssues.slice(-4), {
          type: 'slow_operation',
          operation: operationType,
          duration,
          threshold,
          timestamp: new Date().toISOString()
        }]
      }));

      logger.warn('Slow accessibility operation detected', {
        operationType,
        duration,
        threshold
      });

      // Trigger performance issue callback
      if (validatedConfig.onPerformanceIssue) {
        validatedConfig.onPerformanceIssue({
          type: 'slow_operation',
          operation: operationType,
          duration,
          threshold
        });
      }
    }

    // Analytics tracking
    if (validatedConfig.enableAnalytics && (window as any).analytics) {
      (window as any).analytics.track('Accessibility Operation', {
        operationType,
        duration,
        success,
        timestamp: new Date().toISOString()
      });
    }

    logger.debug('Operation tracked', {
      operationType,
      duration,
      success
    });
  }, [validatedConfig]);

  // Enhanced user preference detection with comprehensive support
  const detectUserPreferences = useCallback((): UserPreferences => {
    const detectionStartTime = Date.now();

    try {
      const preferences: UserPreferences = {
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        prefersHighContrast: window.matchMedia('(prefers-contrast: high)').matches,
        prefersColorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light',
        prefersReducedData: window.matchMedia('(prefers-reduced-data: reduce)').matches,
        prefersReducedTransparency: window.matchMedia('(prefers-reduced-transparency: reduce)').matches,
        fontSize: parseInt(getComputedStyle(document.documentElement).fontSize) || 16,
        screenReaderActive: !!(window.navigator as any).userAgent.match(/NVDA|JAWS|VoiceOver|ORCA|ChromeVox/i) ||
                           !!(window as any).speechSynthesis?.speaking
      };

      setUserPreferences(preferences);

      const detectionTime = Date.now() - detectionStartTime;
      trackOperation('preferenceDetections', detectionTime, true);

      logger.debug('User preferences detected', preferences);

      return preferences;
    } catch (error) {
      const detectionTime = Date.now() - detectionStartTime;
      trackOperation('preferenceDetections', detectionTime, false);

      logger.error('Error detecting user preferences', error);

      // Return safe defaults
      const defaultPreferences: UserPreferences = {
        prefersReducedMotion: false,
        prefersHighContrast: false,
        prefersColorScheme: 'light',
        prefersReducedData: false,
        prefersReducedTransparency: false,
        fontSize: 16,
        screenReaderActive: false
      };

      setUserPreferences(defaultPreferences);
      return defaultPreferences;
    }
  }, [trackOperation]);

  // Initialize user preferences
  useEffect(() => {
    detectUserPreferences();
  }, [detectUserPreferences]);

  // Enhanced announcement text creation for screen readers
  const createAnnouncement = useCallback((toast: ToastState): string => {
    const announcementStartTime = Date.now();

    try {
      const typeAnnouncement = {
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        info: 'Information'
      };

      const toastType = toast.config.type || 'info';
      const prefix = typeAnnouncement[toastType as keyof typeof typeAnnouncement] || 'Notification';

      // Truncate long messages for better screen reader experience
      const maxLength = validatedConfig.maxAnnouncementLength || VALIDATION_RULES.MAX_ANNOUNCEMENT_LENGTH;
      const message = toast.config.message.length > maxLength
        ? `${toast.config.message.substring(0, maxLength)}...`
        : toast.config.message;

      const announcement = `${prefix}: ${message}`;

      const creationTime = Date.now() - announcementStartTime;
      trackOperation('announcementCreation', creationTime, true);

      logger.debug('Announcement created', {
        toastId: toast.id,
        type: toastType,
        messageLength: message.length,
        creationTime
      });

      return announcement;
    } catch (error) {
      const creationTime = Date.now() - announcementStartTime;
      trackOperation('announcementCreation', creationTime, false);

      logger.error('Error creating announcement', error);

      // Return safe fallback
      return `Notification: ${toast.config.message}`;
    }
  }, [validatedConfig, trackOperation]);

  // Enhanced screen reader announcer element with validation
  useEffect(() => {
    if (!validatedConfig.announceToScreenReader) return;

    const creationStartTime = Date.now();

    try {
      const announcer = document.createElement('div');
      announcer.setAttribute('aria-live', validatedConfig.ariaLive || 'polite');
      announcer.setAttribute('aria-atomic', validatedConfig.ariaAtomic ? 'true' : 'false');
      announcer.setAttribute('aria-relevant', 'additions text');
      announcer.setAttribute('role', 'status');
      announcer.className = 'toast-announcer';
      announcer.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      `;

      document.body.appendChild(announcer);
      announcerRef.current = announcer;

      const creationTime = Date.now() - creationStartTime;
      trackOperation('announcerCreation', creationTime, true);

      logger.debug('Screen reader announcer created', {
        ariaLive: validatedConfig.ariaLive,
        ariaAtomic: validatedConfig.ariaAtomic,
        creationTime
      });

      // Validate announcer element
      if (validatedConfig.enableValidation) {
        const validationStartTime = Date.now();

        const hasRequiredAttributes = VALIDATION_RULES.REQUIRED_ARIA_ATTRIBUTES.every(attr =>
          announcer.hasAttribute(attr)
        );

        if (!hasRequiredAttributes) {
          const issue = {
            type: 'missing_aria_attributes',
            element: 'announcer',
            rule: 'REQUIRED_ARIA_ATTRIBUTES',
            severity: 'error' as const,
            timestamp: new Date().toISOString()
          };

          setAccessibilityHealth(prev => ({
            ...prev,
            validationIssues: [...prev.validationIssues.slice(-9), issue]
          }));

          logger.error('Announcer validation failed', issue);

          if (validatedConfig.onValidationIssue) {
            validatedConfig.onValidationIssue(issue);
          }
        }

        const validationTime = Date.now() - validationStartTime;
        trackOperation('validationChecks', validationTime, hasRequiredAttributes);
      }

      return () => {
        if (announcerRef.current && document.body.contains(announcerRef.current)) {
          document.body.removeChild(announcerRef.current);
        }
        announcerRef.current = null;

        logger.debug('Screen reader announcer removed');
      };
    } catch (error) {
      const creationTime = Date.now() - creationStartTime;
      trackOperation('announcerCreation', creationTime, false);

      logger.error('Error creating screen reader announcer', error);

      setAccessibilityHealth(prev => ({
        ...prev,
        status: 'error'
      }));
    }
  }, [validatedConfig, trackOperation]);



  // Enhanced toast announcement with comprehensive tracking
  useEffect(() => {
    if (!validatedConfig.announceToScreenReader || !announcerRef.current) return;

    const visibleToasts = toasts.filter(toast => toast.isVisible && !toast.isExiting);
    const latestToast = visibleToasts[visibleToasts.length - 1];

    if (latestToast) {
      const announcementStartTime = Date.now();

      try {
        const announcement = createAnnouncement(latestToast);

        // Prevent duplicate announcements
        if (announcement === lastAnnouncementRef.current) {
          logger.debug('Skipping duplicate announcement', { announcement });
          return;
        }

        lastAnnouncementRef.current = announcement;

        // Clear previous announcement
        announcerRef.current.textContent = '';

        // Add new announcement after a brief delay to ensure it's read
        setTimeout(() => {
          if (announcerRef.current) {
            announcerRef.current.textContent = announcement;

            const announcementTime = Date.now() - announcementStartTime;
            trackOperation('totalAnnouncements', announcementTime, true);

            logger.debug('Toast announced to screen reader', {
              toastId: latestToast.id,
              type: latestToast.config.type,
              announcementTime
            });

            // Update metrics
            setAccessibilityMetrics(prev => ({
              ...prev,
              successfulAnnouncements: prev.successfulAnnouncements + 1
            }));

            // Trigger accessibility event callback
            if (validatedConfig.onAccessibilityEvent) {
              validatedConfig.onAccessibilityEvent('announcement', {
                toastId: latestToast.id,
                type: latestToast.config.type,
                message: announcement,
                announcementTime
              });
            }
          }
        }, validatedConfig.announcementDelay || CONFIG.ANNOUNCEMENT_DELAY);
      } catch (error) {
        const announcementTime = Date.now() - announcementStartTime;
        trackOperation('totalAnnouncements', announcementTime, false);

        logger.error('Error announcing toast to screen reader', error);

        // Update metrics
        setAccessibilityMetrics(prev => ({
          ...prev,
          failedAnnouncements: prev.failedAnnouncements + 1
        }));
      }
    }
  }, [toasts, validatedConfig, trackOperation, createAnnouncement]);

  // Update keyboard navigation state
  useEffect(() => {
    const visibleToasts = toasts.filter(toast => toast.isVisible && !toast.isExiting);
    keyboardNavigationRef.current.toastIds = visibleToasts.map(toast => toast.id);
    
    // Reset current index if it's out of bounds
    if (keyboardNavigationRef.current.currentIndex >= visibleToasts.length) {
      keyboardNavigationRef.current.currentIndex = visibleToasts.length - 1;
    }
  }, [toasts]);



  // Enhanced focus management with tracking
  const focusToast = useCallback((toastId: string) => {
    if (!validatedConfig.focusOnShow) return;

    const focusStartTime = Date.now();

    try {
      const toastElement = document.querySelector(`[data-toast-id="${toastId}"]`) as HTMLElement;
      if (toastElement) {
        toastElement.focus();
        focusedToastRef.current = toastId;

        const focusTime = Date.now() - focusStartTime;
        trackOperation('focusOperations', focusTime, true);

        logger.debug('Toast focused', {
          toastId,
          focusTime
        });

        // Update metrics
        setAccessibilityMetrics(prev => ({
          ...prev,
          focusOperations: prev.focusOperations + 1
        }));
      }
    } catch (error) {
      const focusTime = Date.now() - focusStartTime;
      trackOperation('focusOperations', focusTime, false);

      logger.error('Error focusing toast', error);
    }
  }, [validatedConfig.focusOnShow, trackOperation]);

  // Enhanced keyboard navigation handlers with tracking
  const handleGlobalKeyDown = useCallback((event: KeyboardEvent) => {
    if (!validatedConfig.enableKeyboardNavigation) return;

    const navigationStartTime = Date.now();

    const visibleToasts = toasts.filter(toast => toast.isVisible && !toast.isExiting);
    if (visibleToasts.length === 0) return;

    switch (event.key) {
      case 'ArrowUp':
      case 'ArrowLeft':
        event.preventDefault();
        keyboardNavigationRef.current.currentIndex = Math.max(
          0,
          keyboardNavigationRef.current.currentIndex - 1
        );
        focusCurrentToast();
        break;

      case 'ArrowDown':
      case 'ArrowRight':
        event.preventDefault();
        keyboardNavigationRef.current.currentIndex = Math.min(
          visibleToasts.length - 1,
          keyboardNavigationRef.current.currentIndex + 1
        );
        focusCurrentToast();
        break;

      case 'Home':
        event.preventDefault();
        keyboardNavigationRef.current.currentIndex = 0;
        focusCurrentToast();
        break;

      case 'End':
        event.preventDefault();
        keyboardNavigationRef.current.currentIndex = visibleToasts.length - 1;
        focusCurrentToast();
        break;

      case 'Tab':
        // Allow normal tab navigation within toasts
        break;

      default:
        // Let individual toasts handle other keys
        break;
    }

    const navigationTime = Date.now() - navigationStartTime;
    trackOperation('keyboardNavigations', navigationTime, true);

    logger.debug('Keyboard navigation handled', {
      key: event.key,
      navigationTime
    });

    // Update metrics
    setAccessibilityMetrics(prev => ({
      ...prev,
      keyboardNavigations: prev.keyboardNavigations + 1
    }));
  }, [validatedConfig.enableKeyboardNavigation, toasts, trackOperation]);

  const focusCurrentToast = useCallback(() => {
    const visibleToasts = toasts.filter(toast => toast.isVisible && !toast.isExiting);
    const currentToast = visibleToasts[keyboardNavigationRef.current.currentIndex];

    if (currentToast) {
      focusToast(currentToast.id);
    }
  }, [toasts, focusToast]);

  // Set up global keyboard listeners
  useEffect(() => {
    if (!validatedConfig.enableKeyboardNavigation) return;

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => document.removeEventListener('keydown', handleGlobalKeyDown);
  }, [validatedConfig.enableKeyboardNavigation, handleGlobalKeyDown]);

  // Detect user preferences
  const getUserPreferences = useCallback(() => {
    return {
      prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      prefersHighContrast: window.matchMedia('(prefers-contrast: high)').matches,
      prefersColorScheme: window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light',
    };
  }, []);

  // Generate accessibility attributes for toast elements
  const getToastAccessibilityProps = useCallback((toast: ToastState) => {
    const props: Record<string, any> = {
      'data-toast-id': toast.id,
      role: 'alert',
      'aria-live': toast.config.type === 'error' ? 'assertive' : 'polite',
      'aria-atomic': 'true',
      tabIndex: validatedConfig.enableKeyboardNavigation ? 0 : -1,
    };

    if (toast.config.title) {
      props['aria-labelledby'] = `toast-title-${toast.id}`;
    }

    props['aria-describedby'] = `toast-message-${toast.id}`;

    if (toast.config.dismissible !== false) {
      props['aria-label'] = `${toast.config.type} notification. Press Escape to dismiss.`;
    }

    return props;
  }, [validatedConfig.enableKeyboardNavigation]);

  // Enhanced accessibility styles generation with user preferences
  const getAccessibilityStyles = useCallback((): AccessibilityStyles => {
    const preferences = getUserPreferences();
    const styles: AccessibilityStyles = {};

    if (preferences.prefersReducedMotion && validatedConfig.respectReducedMotion) {
      styles.transition = 'none';
      styles.animation = 'none';
    }

    if (preferences.prefersHighContrast && validatedConfig.highContrastSupport) {
      styles.border = '2px solid currentColor';
      styles.backgroundColor = 'Canvas';
      styles.color = 'CanvasText';
      styles.outline = '2px solid transparent';
      styles.boxShadow = '0 0 0 2px currentColor';
    }

    // Enhanced font size support
    if (userPreferences.fontSize > 16) {
      styles.fontSize = `${userPreferences.fontSize}px`;
      styles.lineHeight = '1.5';
      styles.padding = '12px 16px';
    }

    // Minimum touch target size
    styles.minHeight = `${VALIDATION_RULES.MIN_TOUCH_TARGET_SIZE}px`;

    return styles;
  }, [validatedConfig.respectReducedMotion, validatedConfig.highContrastSupport, getUserPreferences, userPreferences.fontSize]);

  // Enhanced toast count announcement with tracking
  const announceToastCount = useCallback((count: number) => {
    if (!validatedConfig.announceToScreenReader || !announcerRef.current) return;

    const announcement = count === 0 
      ? 'All notifications cleared'
      : `${count} notification${count > 1 ? 's' : ''} visible`;

    setTimeout(() => {
      if (announcerRef.current) {
        announcerRef.current.textContent = announcement;
      }
    }, validatedConfig.announcementDelay || CONFIG.COUNT_ANNOUNCEMENT_DELAY);
  }, [validatedConfig.announceToScreenReader, validatedConfig.announcementDelay]);

  // Monitor toast count changes
  useEffect(() => {
    const visibleCount = toasts.filter(toast => toast.isVisible && !toast.isExiting).length;
    announceToastCount(visibleCount);
  }, [toasts, announceToastCount]);

  // Get comprehensive accessibility statistics
  const getAccessibilityStats = useCallback(() => {
    const uptime = Date.now() - startTimeRef.current;
    const successRate = accessibilityMetrics.totalAnnouncements > 0 ?
      (accessibilityMetrics.successfulAnnouncements / accessibilityMetrics.totalAnnouncements) * 100 : 0;

    return {
      uptime,
      accessibilityMetrics: {
        ...accessibilityMetrics,
        successRate
      },
      accessibilityHealth,
      userPreferences,
      operationHistory,
      config: validatedConfig
    };
  }, [accessibilityMetrics, accessibilityHealth, userPreferences, operationHistory, validatedConfig]);

  // Get accessibility health summary
  const getHealthSummary = useCallback(() => {
    const stats = getAccessibilityStats();
    const isHealthy = stats.accessibilityHealth.status === 'active' &&
                     stats.accessibilityHealth.performanceIssues.length === 0 &&
                     stats.accessibilityHealth.validationIssues.length === 0;

    return {
      isHealthy,
      status: isHealthy ? 'healthy' : 'degraded',
      issues: [
        ...(stats.accessibilityHealth.performanceIssues.length > 0 ? ['Performance issues detected'] : []),
        ...(stats.accessibilityHealth.validationIssues.length > 0 ? ['Validation issues detected'] : [])
      ],
      recommendations: [
        ...(stats.accessibilityHealth.performanceIssues.length > 2 ? ['Optimize accessibility operations'] : []),
        ...(stats.accessibilityHealth.validationIssues.length > 0 ? ['Review accessibility compliance'] : [])
      ],
      timestamp: new Date().toISOString()
    };
  }, [getAccessibilityStats]);

  // Reset accessibility metrics
  const resetMetrics = useCallback(() => {
    logger.debug('Resetting accessibility metrics');

    setAccessibilityMetrics({
      totalAnnouncements: 0,
      successfulAnnouncements: 0,
      failedAnnouncements: 0,
      focusOperations: 0,
      keyboardNavigations: 0,
      preferenceDetections: 0,
      validationChecks: 0,
      averageAnnouncementTime: 0,
      lastOperationTime: null
    });

    setOperationHistory([]);

    setAccessibilityHealth({
      status: 'reset',
      performanceIssues: [],
      validationIssues: [],
      lastHealthCheck: null
    });

    startTimeRef.current = Date.now();
    lastAnnouncementRef.current = '';
  }, []);

  // Enhanced return object with comprehensive accessibility management
  return {
    // Core accessibility functions
    focusToast,
    getToastAccessibilityProps,
    getAccessibilityStyles,
    getUserPreferences,
    announceToastCount,
    createAnnouncement,
    detectUserPreferences,

    // Enhanced utility functions
    getAccessibilityStats,
    getHealthSummary,
    resetMetrics,

    // State access
    currentFocusedToast: focusedToastRef.current,
    keyboardNavigation: keyboardNavigationRef.current,
    accessibilityMetrics,
    accessibilityHealth,
    userPreferences,
    operationHistory,

    // Configuration access
    config: validatedConfig,
    thresholds: PERFORMANCE_THRESHOLDS,
    validationRules: VALIDATION_RULES,

    // Quick access helpers
    uptime: Date.now() - startTimeRef.current,
    hasAnnouncer: !!announcerRef.current,

    // Health status helpers
    isHealthy: accessibilityHealth.status === 'active' && accessibilityHealth.performanceIssues.length === 0,
    performanceIssues: accessibilityHealth.performanceIssues,
    validationIssues: accessibilityHealth.validationIssues,
    lastHealthCheck: accessibilityHealth.lastHealthCheck,

    // Performance helpers
    totalAnnouncements: accessibilityMetrics.totalAnnouncements,
    successfulAnnouncements: accessibilityMetrics.successfulAnnouncements,
    failedAnnouncements: accessibilityMetrics.failedAnnouncements,
    successRate: accessibilityMetrics.totalAnnouncements > 0 ?
      (accessibilityMetrics.successfulAnnouncements / accessibilityMetrics.totalAnnouncements) * 100 : 0,
    focusOperations: accessibilityMetrics.focusOperations,
    keyboardNavigations: accessibilityMetrics.keyboardNavigations,
    preferenceDetections: accessibilityMetrics.preferenceDetections,
    validationChecks: accessibilityMetrics.validationChecks,
    averageAnnouncementTime: accessibilityMetrics.averageAnnouncementTime,
    lastOperationTime: accessibilityMetrics.lastOperationTime,

    // User preference helpers
    prefersReducedMotion: userPreferences.prefersReducedMotion,
    prefersHighContrast: userPreferences.prefersHighContrast,
    prefersColorScheme: userPreferences.prefersColorScheme,
    prefersReducedData: userPreferences.prefersReducedData,
    prefersReducedTransparency: userPreferences.prefersReducedTransparency,
    fontSize: userPreferences.fontSize,
    screenReaderActive: userPreferences.screenReaderActive,

    // Operation helpers
    operationHistorySize: operationHistory.length,
    maxOperationHistory: 50,

    // Configuration helpers
    analyticsEnabled: validatedConfig.enableAnalytics,
    performanceMonitoringEnabled: validatedConfig.enablePerformanceMonitoring,
    validationEnabled: validatedConfig.enableValidation,
    announceToScreenReader: validatedConfig.announceToScreenReader,
    focusOnShow: validatedConfig.focusOnShow,
    enableKeyboardNavigation: validatedConfig.enableKeyboardNavigation,
    respectReducedMotion: validatedConfig.respectReducedMotion,
    highContrastSupport: validatedConfig.highContrastSupport,
    maxAnnouncementLength: validatedConfig.maxAnnouncementLength,
    announcementDelay: validatedConfig.announcementDelay,
    focusTimeout: validatedConfig.focusTimeout
  };
};

"""
A/B testing framework for ACEO add-on system.
Enables testing of pricing, recommendations, and UI variations.
"""
import logging
import json
import hashlib
import random
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from app.core.redis import (
    redis_manager, redis_get, redis_set, redis_delete, redis_setex,
    redis_incr, redis_expire, redis_lpush, redis_incrbyfloat, redis_llen
)
from app.core.monitoring import record_addon_metrics
from app.models.user import User

logger = logging.getLogger(__name__)


class TestStatus(str, Enum):
    """A/B test status."""
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ConversionEvent(str, Enum):
    """Types of conversion events to track."""
    VIEW = "view"
    CLICK = "click"
    ADD_TO_CART = "add_to_cart"
    PURCHASE = "purchase"
    USAGE = "usage"
    RETENTION = "retention"


@dataclass
class ABTestVariant:
    """A/B test variant configuration."""
    name: str
    weight: float
    config: Dict[str, Any]
    is_control: bool = False


@dataclass
class ABTestResult:
    """A/B test results."""
    variant_name: str
    participants: int
    conversions: int
    conversion_rate: float
    revenue: float
    statistical_significance: float
    confidence_interval: Tuple[float, float]


class AddonABTestManager:
    """A/B testing manager for add-on system."""
    
    def __init__(self):
        self.redis_prefix = "ab_test:"
        self.assignment_prefix = "ab_assignment:"
        self.results_prefix = "ab_results:"
        
        # Default A/B tests for add-on system
        self.default_tests = {
            "addon_pricing_strategy": {
                "name": "Add-on Pricing Strategy Test",
                "description": "Test different pricing strategies for add-ons",
                "variants": [
                    ABTestVariant("control", 0.5, {"pricing_strategy": "current"}, is_control=True),
                    ABTestVariant("discount_10", 0.5, {"pricing_strategy": "10_percent_off"})
                ],
                "conversion_events": [ConversionEvent.PURCHASE],
                "target_audience": {"plans": ["creator", "accelerator"]},
                "duration_days": 30
            },
            "recommendation_algorithm": {
                "name": "Recommendation Algorithm Test",
                "description": "Test AI-enhanced vs basic recommendations",
                "variants": [
                    ABTestVariant("basic", 0.5, {"algorithm": "basic"}, is_control=True),
                    ABTestVariant("ai_enhanced", 0.5, {"algorithm": "ai_enhanced"})
                ],
                "conversion_events": [ConversionEvent.CLICK, ConversionEvent.PURCHASE],
                "target_audience": {"plans": ["accelerator", "dominator"]},
                "duration_days": 21
            },
            "marketplace_layout": {
                "name": "Marketplace Layout Test",
                "description": "Test different marketplace layouts",
                "variants": [
                    ABTestVariant("grid", 0.33, {"layout": "grid"}, is_control=True),
                    ABTestVariant("list", 0.33, {"layout": "list"}),
                    ABTestVariant("carousel", 0.34, {"layout": "carousel"})
                ],
                "conversion_events": [ConversionEvent.VIEW, ConversionEvent.CLICK],
                "target_audience": {"plans": ["creator"]},
                "duration_days": 14
            },
            "upsell_timing": {
                "name": "Upsell Timing Test",
                "description": "Test when to show upsell prompts",
                "variants": [
                    ABTestVariant("75_percent", 0.5, {"trigger_percentage": 75}, is_control=True),
                    ABTestVariant("90_percent", 0.5, {"trigger_percentage": 90})
                ],
                "conversion_events": [ConversionEvent.PURCHASE],
                "target_audience": {"plans": ["creator", "accelerator", "dominator"]},
                "duration_days": 28
            }
        }
    
    async def create_test(self, test_id: str, name: str, description: str,
                         variants: List[ABTestVariant], conversion_events: List[ConversionEvent],
                         target_audience: Dict[str, Any], duration_days: int,
                         created_by: str) -> bool:
        """Create a new A/B test."""
        try:
            # Validate variant weights sum to 1.0
            total_weight = sum(variant.weight for variant in variants)
            if abs(total_weight - 1.0) > 0.01:
                logger.error(f"Variant weights don't sum to 1.0: {total_weight}")
                return False
            
            # Ensure exactly one control variant
            control_variants = [v for v in variants if v.is_control]
            if len(control_variants) != 1:
                logger.error(f"Must have exactly one control variant, found {len(control_variants)}")
                return False
            
            test_config = {
                "test_id": test_id,
                "name": name,
                "description": description,
                "status": TestStatus.DRAFT.value,
                "variants": [
                    {
                        "name": variant.name,
                        "weight": variant.weight,
                        "config": variant.config,
                        "is_control": variant.is_control
                    }
                    for variant in variants
                ],
                "conversion_events": [event.value for event in conversion_events],
                "target_audience": target_audience,
                "duration_days": duration_days,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "created_by": created_by,
                "start_date": None,
                "end_date": None
            }
            
            await redis_set(
                f"{self.redis_prefix}{test_id}",
                json.dumps(test_config)
            )
            
            # Initialize results tracking
            await self._initialize_test_results(test_id, variants)
            
            logger.info(f"Created A/B test: {test_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating A/B test {test_id}: {str(e)}")
            return False
    
    async def start_test(self, test_id: str) -> bool:
        """Start an A/B test."""
        try:
            test_config = await self._get_test_config(test_id)
            if not test_config:
                return False
            
            if test_config["status"] != TestStatus.DRAFT.value:
                logger.error(f"Cannot start test {test_id} with status {test_config['status']}")
                return False
            
            # Update test status and dates
            test_config["status"] = TestStatus.ACTIVE.value
            test_config["start_date"] = datetime.now(timezone.utc).isoformat()
            test_config["end_date"] = (
                datetime.now(timezone.utc) + timedelta(days=test_config["duration_days"])
            ).isoformat()
            
            await redis_set(
                f"{self.redis_prefix}{test_id}",
                json.dumps(test_config)
            )
            
            record_addon_metrics("ab_test_started", "system", 1)
            logger.info(f"Started A/B test: {test_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting A/B test {test_id}: {str(e)}")
            return False
    
    async def get_user_variant(self, test_id: str, user: User) -> Optional[Dict[str, Any]]:
        """Get the assigned variant for a user."""
        try:
            test_config = await self._get_test_config(test_id)
            if not test_config or test_config["status"] != TestStatus.ACTIVE.value:
                return None
            
            # Check if user is in target audience
            if not self._user_in_target_audience(user, test_config["target_audience"]):
                return None
            
            # Check for existing assignment
            assignment_key = f"{self.assignment_prefix}{test_id}:{user.id}"
            existing_assignment = await redis_get(assignment_key)
            
            if existing_assignment:
                assignment_data = json.loads(existing_assignment)
                return assignment_data["variant"]
            
            # Assign user to variant
            variant = self._assign_user_to_variant(user, test_config["variants"])
            
            # Store assignment
            assignment_data = {
                "test_id": test_id,
                "user_id": user.id,
                "variant": variant,
                "assigned_at": datetime.now(timezone.utc).isoformat()
            }
            
            await redis_setex(
                assignment_key,
                86400 * test_config["duration_days"],  # Expire after test duration
                json.dumps(assignment_data)
            )
            
            # Track assignment
            await self._track_assignment(test_id, variant["name"])
            
            return variant
            
        except Exception as e:
            logger.error(f"Error getting user variant for test {test_id}: {str(e)}")
            return None
    
    async def track_conversion(self, test_id: str, user: User, event: ConversionEvent,
                             value: float = 1.0, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Track a conversion event for A/B test."""
        try:
            # Get user's variant assignment
            variant = await self.get_user_variant(test_id, user)
            if not variant:
                return False
            
            # Check if this event is tracked for this test
            test_config = await self._get_test_config(test_id)
            if not test_config or event.value not in test_config["conversion_events"]:
                return False
            
            # Track the conversion
            conversion_data = {
                "test_id": test_id,
                "user_id": user.id,
                "variant_name": variant["name"],
                "event": event.value,
                "value": value,
                "metadata": metadata or {},
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Store conversion event
            conversion_key = f"ab_conversion:{test_id}:{variant['name']}:{event.value}"
            await redis_lpush(conversion_key, json.dumps(conversion_data))
            await redis_expire(conversion_key, 86400 * 90)  # Keep for 90 days
            
            # Update aggregated metrics
            await self._update_conversion_metrics(test_id, variant["name"], event, value)
            
            record_addon_metrics(f"ab_conversion_{test_id}_{variant['name']}", event.value, int(value))
            
            return True
            
        except Exception as e:
            logger.error(f"Error tracking conversion for test {test_id}: {str(e)}")
            return False
    
    async def get_test_results(self, test_id: str) -> List[ABTestResult]:
        """Get results for an A/B test."""
        try:
            test_config = await self._get_test_config(test_id)
            if not test_config:
                return []
            
            results = []
            
            for variant in test_config["variants"]:
                variant_name = variant["name"]
                
                # Get participant count
                participants = await self._get_participant_count(test_id, variant_name)
                
                # Get conversion metrics
                conversions = 0
                revenue = 0.0
                
                for event in test_config["conversion_events"]:
                    event_metrics = await self._get_conversion_metrics(test_id, variant_name, event)
                    conversions += event_metrics["count"]
                    revenue += event_metrics["revenue"]
                
                # Calculate conversion rate
                conversion_rate = (conversions / participants * 100) if participants > 0 else 0
                
                # Calculate statistical significance (simplified)
                significance = await self._calculate_statistical_significance(
                    test_id, variant_name, test_config["variants"]
                )
                
                # Calculate confidence interval (simplified)
                confidence_interval = self._calculate_confidence_interval(
                    conversions, participants
                )
                
                results.append(ABTestResult(
                    variant_name=variant_name,
                    participants=participants,
                    conversions=conversions,
                    conversion_rate=conversion_rate,
                    revenue=revenue,
                    statistical_significance=significance,
                    confidence_interval=confidence_interval
                ))
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting test results for {test_id}: {str(e)}")
            return []
    
    async def stop_test(self, test_id: str, reason: str = "completed") -> bool:
        """Stop an A/B test."""
        try:
            test_config = await self._get_test_config(test_id)
            if not test_config:
                return False
            
            test_config["status"] = TestStatus.COMPLETED.value if reason == "completed" else TestStatus.CANCELLED.value
            test_config["stopped_at"] = datetime.now(timezone.utc).isoformat()
            test_config["stop_reason"] = reason
            
            await redis_set(
                f"{self.redis_prefix}{test_id}",
                json.dumps(test_config)
            )
            
            record_addon_metrics("ab_test_stopped", reason, 1)
            logger.info(f"Stopped A/B test {test_id}: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping A/B test {test_id}: {str(e)}")
            return False
    
    async def initialize_default_tests(self):
        """Initialize default A/B tests."""
        try:
            for test_id, test_config in self.default_tests.items():
                existing_test = await self._get_test_config(test_id)
                if not existing_test:
                    await self.create_test(
                        test_id=test_id,
                        name=test_config["name"],
                        description=test_config["description"],
                        variants=test_config["variants"],
                        conversion_events=test_config["conversion_events"],
                        target_audience=test_config["target_audience"],
                        duration_days=test_config["duration_days"],
                        created_by="system"
                    )
                    logger.info(f"Initialized default A/B test: {test_id}")
        except Exception as e:
            logger.error(f"Error initializing default tests: {str(e)}")
    
    def _assign_user_to_variant(self, user: User, variants: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Assign user to a variant based on weights."""
        # Create deterministic hash based on user ID
        user_hash = hashlib.md5(f"ab_test:{user.id}".encode()).hexdigest()
        hash_value = int(user_hash[:8], 16) / (2**32)  # Convert to 0-1 range
        
        # Select variant based on weights
        cumulative_weight = 0
        for variant in variants:
            cumulative_weight += variant["weight"]
            if hash_value <= cumulative_weight:
                return variant
        
        # Fallback to control variant
        control_variant = next((v for v in variants if v.get("is_control")), variants[0])
        return control_variant
    
    def _user_in_target_audience(self, user: User, target_audience: Dict[str, Any]) -> bool:
        """Check if user is in target audience."""
        # Check plan requirement
        if "plans" in target_audience:
            user_plan = user.subscription.plan_id if user.subscription else "creator"
            if user_plan not in target_audience["plans"]:
                return False
        
        # Check other criteria (can be extended)
        if "min_age_days" in target_audience:
            account_age = (datetime.now(timezone.utc) - user.created_at).days
            if account_age < target_audience["min_age_days"]:
                return False
        
        return True
    
    async def _get_test_config(self, test_id: str) -> Optional[Dict[str, Any]]:
        """Get test configuration."""
        try:
            config_data = await redis_get(f"{self.redis_prefix}{test_id}")
            return json.loads(config_data) if config_data else None
        except Exception as e:
            logger.error(f"Error getting test config for {test_id}: {str(e)}")
            return None
    
    async def _initialize_test_results(self, test_id: str, variants: List[ABTestVariant]):
        """Initialize results tracking for a test."""
        try:
            for variant in variants:
                results_key = f"{self.results_prefix}{test_id}:{variant.name}"
                initial_results = {
                    "participants": 0,
                    "conversions": {},
                    "revenue": 0.0
                }
                await redis_set(results_key, json.dumps(initial_results))
        except Exception as e:
            logger.error(f"Error initializing test results: {str(e)}")
    
    async def _track_assignment(self, test_id: str, variant_name: str):
        """Track user assignment to variant."""
        try:
            assignment_key = f"ab_participants:{test_id}:{variant_name}"
            await redis_incr(assignment_key)
            await redis_expire(assignment_key, 86400 * 90)  # 90 days
        except Exception as e:
            logger.error(f"Error tracking assignment: {str(e)}")
    
    async def _update_conversion_metrics(self, test_id: str, variant_name: str,
                                       event: ConversionEvent, value: float):
        """Update conversion metrics for a variant."""
        try:
            metrics_key = f"ab_metrics:{test_id}:{variant_name}:{event.value}"
            await redis_incrbyfloat(metrics_key, value)
            await redis_expire(metrics_key, 86400 * 90)  # 90 days
        except Exception as e:
            logger.error(f"Error updating conversion metrics: {str(e)}")
    
    async def _get_participant_count(self, test_id: str, variant_name: str) -> int:
        """Get participant count for a variant."""
        try:
            assignment_key = f"ab_participants:{test_id}:{variant_name}"
            count = await redis_get(assignment_key)
            return int(count) if count else 0
        except Exception as e:
            logger.error(f"Error getting participant count: {str(e)}")
            return 0
    
    async def _get_conversion_metrics(self, test_id: str, variant_name: str, event: str) -> Dict[str, Any]:
        """Get conversion metrics for a variant and event."""
        try:
            metrics_key = f"ab_metrics:{test_id}:{variant_name}:{event}"
            value = await redis_get(metrics_key)
            
            # Count conversions
            conversion_key = f"ab_conversion:{test_id}:{variant_name}:{event}"
            conversions = await redis_llen(conversion_key)
            
            return {
                "count": conversions,
                "revenue": float(value) if value else 0.0
            }
        except Exception as e:
            logger.error(f"Error getting conversion metrics: {str(e)}")
            return {"count": 0, "revenue": 0.0}
    
    async def _calculate_statistical_significance(self, test_id: str, variant_name: str,
                                                variants: List[Dict[str, Any]]) -> float:
        """Calculate statistical significance (simplified)."""
        try:
            # This is a simplified calculation
            # In production, you'd use proper statistical tests like chi-square or t-test
            
            # Get control variant
            control_variant = next((v for v in variants if v.get("is_control")), None)
            if not control_variant or control_variant["name"] == variant_name:
                return 0.0
            
            # Get conversion rates for both variants
            variant_participants = await self._get_participant_count(test_id, variant_name)
            control_participants = await self._get_participant_count(test_id, control_variant["name"])
            
            if variant_participants < 100 or control_participants < 100:
                return 0.0  # Not enough data
            
            # Simplified significance calculation
            # In reality, you'd use proper statistical methods
            min_participants = min(variant_participants, control_participants)
            significance = min(min_participants / 1000, 0.95)  # Max 95% confidence
            
            return significance
            
        except Exception as e:
            logger.error(f"Error calculating statistical significance: {str(e)}")
            return 0.0
    
    def _calculate_confidence_interval(self, conversions: int, participants: int) -> Tuple[float, float]:
        """Calculate confidence interval for conversion rate."""
        if participants == 0:
            return (0.0, 0.0)
        
        # Simplified confidence interval calculation
        # In production, use proper statistical methods
        conversion_rate = conversions / participants
        margin_of_error = 1.96 * (conversion_rate * (1 - conversion_rate) / participants) ** 0.5
        
        lower_bound = max(0, conversion_rate - margin_of_error)
        upper_bound = min(1, conversion_rate + margin_of_error)
        
        return (lower_bound * 100, upper_bound * 100)


# Global A/B test manager
ab_test_manager = AddonABTestManager()


async def get_user_test_variant(test_id: str, user: User) -> Optional[Dict[str, Any]]:
    """Get user's variant for an A/B test."""
    return await ab_test_manager.get_user_variant(test_id, user)


async def track_test_conversion(test_id: str, user: User, event: ConversionEvent,
                              value: float = 1.0, metadata: Optional[Dict[str, Any]] = None) -> bool:
    """Track conversion for A/B test."""
    return await ab_test_manager.track_conversion(test_id, user, event, value, metadata)


async def initialize_ab_tests():
    """Initialize default A/B tests."""
    await ab_test_manager.initialize_default_tests()

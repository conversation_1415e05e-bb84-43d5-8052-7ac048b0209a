/**
 * WebSocket Service for Real-time Admin Dashboard Updates
 * 
 * Provides real-time data streaming for:
 * - System metrics and performance data
 * - API health status updates
 * - User activity notifications
 * - Security alerts and system events
 * - Live dashboard statistics
 */

class WebSocketService {
  constructor() {
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000; // 5 seconds
    this.listeners = new Map();
    this.isConnected = false;
    this.connectionId = null;
    this.heartbeatInterval = null;
    this.heartbeatTimeout = 30000; // 30 seconds
  }

  /**
   * Connect to WebSocket server
   */
  connect(token) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return;
    }

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/api/admin/ws?token=${token}`;
      
      console.log('Connecting to WebSocket:', wsUrl);
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Handle WebSocket connection open
   */
  handleOpen(event) {
    console.log('WebSocket connected successfully');
    this.isConnected = true;
    this.reconnectAttempts = 0;
    
    // Start heartbeat
    this.startHeartbeat();
    
    // Notify listeners
    this.emit('connected', { timestamp: new Date() });
    
    // Subscribe to admin dashboard updates
    this.subscribe(['dashboard', 'api_metrics', 'system_alerts', 'user_activity']);
  }

  /**
   * Handle incoming WebSocket messages
   */
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data);
      
      // Handle different message types
      switch (data.type) {
        case 'heartbeat':
          this.handleHeartbeat(data);
          break;
        case 'dashboard_update':
          this.emit('dashboard_update', data.payload);
          break;
        case 'api_metrics':
          this.emit('api_metrics', data.payload);
          break;
        case 'system_alert':
          this.emit('system_alert', data.payload);
          break;
        case 'user_activity':
          this.emit('user_activity', data.payload);
          break;
        case 'performance_metrics':
          this.emit('performance_metrics', data.payload);
          break;
        case 'error':
          console.error('WebSocket error message:', data.message);
          this.emit('error', data);
          break;
        default:
          console.log('Unknown message type:', data.type);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  /**
   * Handle WebSocket connection close
   */
  handleClose(event) {
    console.log('WebSocket connection closed:', event.code, event.reason);
    this.isConnected = false;
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    // Notify listeners
    this.emit('disconnected', { 
      code: event.code, 
      reason: event.reason,
      timestamp: new Date()
    });
    
    // Attempt to reconnect if not a clean close
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket errors
   */
  handleError(error) {
    console.error('WebSocket error:', error);
    this.emit('error', { error, timestamp: new Date() });
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.emit('max_reconnect_attempts', { attempts: this.reconnectAttempts });
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.isConnected) {
        const token = localStorage.getItem('admin_token');
        if (token) {
          this.connect(token);
        }
      }
    }, delay);
  }

  /**
   * Start heartbeat mechanism
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
        this.send({
          type: 'heartbeat',
          timestamp: new Date().toISOString()
        });
      }
    }, this.heartbeatTimeout);
  }

  /**
   * Handle heartbeat response
   */
  handleHeartbeat(data) {
    // Update connection status
    this.connectionId = data.connection_id;
    this.emit('heartbeat', data);
  }

  /**
   * Send message to WebSocket server
   */
  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
      }
    } else {
      console.warn('WebSocket not connected, cannot send message');
    }
  }

  /**
   * Subscribe to specific data streams
   */
  subscribe(channels) {
    this.send({
      type: 'subscribe',
      channels: Array.isArray(channels) ? channels : [channels],
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Unsubscribe from data streams
   */
  unsubscribe(channels) {
    this.send({
      type: 'unsubscribe',
      channels: Array.isArray(channels) ? channels : [channels],
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      connectionId: this.connectionId,
      reconnectAttempts: this.reconnectAttempts,
      readyState: this.ws ? this.ws.readyState : WebSocket.CLOSED
    };
  }

  /**
   * Request immediate data refresh
   */
  requestRefresh(dataTypes = ['dashboard', 'api_metrics']) {
    this.send({
      type: 'refresh_request',
      data_types: dataTypes,
      timestamp: new Date().toISOString()
    });
  }
}

// Create and export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;

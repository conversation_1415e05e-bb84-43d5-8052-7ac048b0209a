/**
 * Enhanced ACE Social Stable Page Wrapper - Enterprise-grade page wrapper component
 * Features: Comprehensive page wrapper with advanced layout management, responsive design
 * patterns, and performance optimization for ACE Social platform pages, detailed page
 * stability features with layout shift prevention and content loading optimization,
 * advanced wrapper management features with dynamic content handling and scroll management,
 * ACE Social's layout system integration with seamless page lifecycle management, page
 * interaction features including scroll restoration and focus management, page state
 * management with layout persistence and scroll position tracking, and real-time page
 * monitoring with live layout displays and automatic page optimization
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useLayoutEffect
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Container,
  Paper,
  Fade,
  Zoom,
  Slide,
  Collapse,
  LinearProgress,
  Skeleton,
  Alert,
  Snackbar,
  IconButton,
  Tooltip,
  Fab,
  useScrollTrigger,
  useMediaQuery,
  alpha
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  KeyboardArrowUp as ScrollTopIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Visibility as VisibilityIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  AccessibilityNew as AccessibilityIcon
} from '@mui/icons-material';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Page wrapper constants
const LAYOUT_MODES = {
  STANDARD: 'standard',
  FULLSCREEN: 'fullscreen',
  COMPACT: 'compact',
  FLUID: 'fluid'
};

const TRANSITION_TYPES = {
  FADE: 'fade',
  SLIDE: 'slide',
  ZOOM: 'zoom',
  NONE: 'none'
};

const SCROLL_BEHAVIORS = {
  SMOOTH: 'smooth',
  AUTO: 'auto',
  INSTANT: 'instant'
};

// Performance monitoring constants
const PERFORMANCE_THRESHOLDS = {
  LAYOUT_SHIFT: 0.1,
  LOAD_TIME: 2000,
  MEMORY_USAGE: 50 * 1024 * 1024, // 50MB
  FPS_MIN: 30
};

// Page wrapper analytics events
const PAGE_ANALYTICS_EVENTS = {
  PAGE_LOADED: 'page_loaded',
  LAYOUT_SHIFT: 'layout_shift',
  SCROLL_POSITION: 'scroll_position',
  PERFORMANCE_METRIC: 'performance_metric',
  ACCESSIBILITY_ACTION: 'accessibility_action',
  FULLSCREEN_TOGGLE: 'fullscreen_toggle',
  LAYOUT_MODE_CHANGE: 'layout_mode_change'
};

/**
 * Enhanced Stable Page Wrapper - Comprehensive page wrapper with advanced features
 * Implements detailed page layout management and enterprise-grade wrapper capabilities
 */
const EnhancedStablePageWrapper = memo(forwardRef(({
  children,
  maxWidth = "xl",
  disableGutters = false,
  enableGlassMorphism = true,
  enablePerformanceMonitoring = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableScrollRestoration = true,
  enableLayoutShiftPrevention = true,
  enableFullscreenMode = false,
  layoutMode = LAYOUT_MODES.STANDARD,
  transitionType = TRANSITION_TYPES.FADE,
  scrollBehavior = SCROLL_BEHAVIORS.SMOOTH,
  loadingComponent,
  errorComponent,
  onLayoutShift,
  onPerformanceMetric,
  onScrollPosition,
  onAnalyticsTrack,
  onLayoutModeChange,
  className,
  sx = {},
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const containerRef = useRef(null);
  const contentRef = useRef(null);
  const scrollPositionRef = useRef({ x: 0, y: 0 });
  const performanceObserverRef = useRef(null);
  const layoutShiftObserverRef = useRef(null);

  // Core state management
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentLayoutMode, setCurrentLayoutMode] = useState(layoutMode);
  const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Enhanced state management
  const [performanceMetrics, setPerformanceMetrics] = useState({
    loadTime: 0,
    layoutShifts: 0,
    memoryUsage: 0,
    fps: 0,
    renderTime: 0
  });
  const [layoutShiftScore, setLayoutShiftScore] = useState(0);
  const [accessibilityFeatures, setAccessibilityFeatures] = useState({
    highContrast: false,
    reducedMotion: false,
    largeText: false,
    screenReader: false
  });
  const [pageAnalytics, setPageAnalytics] = useState({
    loadStartTime: Date.now(),
    interactionCount: 0,
    scrollEvents: 0,
    resizeEvents: 0,
    visibilityChanges: 0
  });

  // Scroll trigger for scroll-to-top button
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 100,
  });

  useEffect(() => {
    setShowScrollTop(trigger);
  }, [trigger]);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    scrollToTop: () => handleScrollToTop(),
    toggleFullscreen: () => handleToggleFullscreen(),
    refreshPage: () => handleRefreshPage(),

    // Layout methods
    setLayoutMode: (mode) => handleLayoutModeChange(mode),
    getLayoutMode: () => currentLayoutMode,
    resetLayout: () => handleResetLayout(),

    // Performance methods
    getPerformanceMetrics: () => performanceMetrics,
    resetPerformanceMetrics: () => handleResetPerformanceMetrics(),

    // Scroll methods
    getScrollPosition: () => scrollPosition,
    setScrollPosition: (position) => handleSetScrollPosition(position),
    saveScrollPosition: () => handleSaveScrollPosition(),
    restoreScrollPosition: () => handleRestoreScrollPosition(),

    // Accessibility methods
    toggleAccessibilityFeature: (feature) => handleToggleAccessibilityFeature(feature),
    getAccessibilityFeatures: () => accessibilityFeatures,
    announceToUser: (message) => announceToScreenReader(message),

    // Analytics methods
    getPageAnalytics: () => pageAnalytics,
    resetPageAnalytics: () => handleResetPageAnalytics(),

    // Advanced methods
    measureLayoutShift: () => measureLayoutShift(),
    optimizePerformance: () => handleOptimizePerformance(),
    exportPageData: () => handleExportPageData()
  }), [
    currentLayoutMode,
    performanceMetrics,
    scrollPosition,
    accessibilityFeatures,
    pageAnalytics,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => {
    if (!enableGlassMorphism) return {};

    return {
      background: `linear-gradient(135deg,
        ${alpha(theme.palette.background.paper, 0.95)} 0%,
        ${alpha(theme.palette.background.default, 0.85)} 100%)`,
      backdropFilter: 'blur(15px)',
      border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
      borderRadius: theme.spacing(2),
      boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
      transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',

      // Enhanced accessibility support
      '@media (prefers-contrast: high)': {
        border: `2px solid ${theme.palette.text.primary}`,
        background: theme.palette.background.paper,
        backdropFilter: 'none',
      },
      '@media (prefers-reduced-motion: reduce)': {
        transition: 'none',
        backdropFilter: 'none'
      },
      '@media print': {
        background: 'white',
        backdropFilter: 'none',
        boxShadow: 'none',
        border: '1px solid black',
      }
    };
  }, [enableGlassMorphism, theme]);

  // Layout mode styles
  const layoutModeStyles = useMemo(() => {
    switch (currentLayoutMode) {
      case LAYOUT_MODES.FULLSCREEN:
        return {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: theme.zIndex.modal,
          maxWidth: '100vw',
          height: '100vh',
          margin: 0,
          padding: 0
        };
      case LAYOUT_MODES.COMPACT:
        return {
          maxWidth: 'md',
          py: theme.spacing(2),
          px: theme.spacing(1)
        };
      case LAYOUT_MODES.FLUID:
        return {
          maxWidth: '100%',
          width: '100%'
        };
      default:
        return {};
    }
  }, [currentLayoutMode, theme]);

  // Performance monitoring setup
  useEffect(() => {
    if (!enablePerformanceMonitoring) return;

    const startTime = performance.now();

    // Layout shift observer
    if ('LayoutShift' in window && enableLayoutShiftPrevention) {
      const observer = new PerformanceObserver((list) => {
        let cumulativeScore = 0;
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            cumulativeScore += entry.value;
          }
        }

        setLayoutShiftScore(prev => prev + cumulativeScore);

        if (cumulativeScore > PERFORMANCE_THRESHOLDS.LAYOUT_SHIFT) {
          if (onLayoutShift) {
            onLayoutShift(cumulativeScore);
          }

          if (enableAnalytics && onAnalyticsTrack) {
            onAnalyticsTrack(PAGE_ANALYTICS_EVENTS.LAYOUT_SHIFT, {
              score: cumulativeScore,
              timestamp: Date.now()
            });
          }
        }
      });

      observer.observe({ entryTypes: ['layout-shift'] });
      layoutShiftObserverRef.current = observer;
    }

    // Performance metrics collection
    const collectMetrics = () => {
      const loadTime = performance.now() - startTime;
      const memoryInfo = performance.memory || {};

      const metrics = {
        loadTime,
        layoutShifts: layoutShiftScore,
        memoryUsage: memoryInfo.usedJSHeapSize || 0,
        renderTime: performance.now()
      };

      setPerformanceMetrics(metrics);

      if (onPerformanceMetric) {
        onPerformanceMetric(metrics);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(PAGE_ANALYTICS_EVENTS.PERFORMANCE_METRIC, metrics);
      }
    };

    const metricsInterval = setInterval(collectMetrics, 5000);

    return () => {
      clearInterval(metricsInterval);
      if (layoutShiftObserverRef.current) {
        layoutShiftObserverRef.current.disconnect();
      }
    };
  }, [
    enablePerformanceMonitoring,
    enableLayoutShiftPrevention,
    layoutShiftScore,
    onLayoutShift,
    onPerformanceMetric,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // Scroll restoration effect
  useEffect(() => {
    if (!enableScrollRestoration) return;

    const handleScroll = () => {
      const position = {
        x: window.pageXOffset,
        y: window.pageYOffset
      };

      setScrollPosition(position);
      scrollPositionRef.current = position;

      setPageAnalytics(prev => ({
        ...prev,
        scrollEvents: prev.scrollEvents + 1
      }));

      if (onScrollPosition) {
        onScrollPosition(position);
      }
    };

    const handleResize = () => {
      setPageAnalytics(prev => ({
        ...prev,
        resizeEvents: prev.resizeEvents + 1
      }));
    };

    const handleVisibilityChange = () => {
      setPageAnalytics(prev => ({
        ...prev,
        visibilityChanges: prev.visibilityChanges + 1
      }));
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize, { passive: true });
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enableScrollRestoration, onScrollPosition]);

  // Accessibility features detection
  useEffect(() => {
    if (!enableAccessibility) return;

    const mediaQueries = {
      highContrast: window.matchMedia('(prefers-contrast: high)'),
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)'),
      largeText: window.matchMedia('(min-resolution: 2dppx)'),
    };

    const updateAccessibilityFeatures = () => {
      setAccessibilityFeatures({
        highContrast: mediaQueries.highContrast.matches,
        reducedMotion: mediaQueries.reducedMotion.matches,
        largeText: mediaQueries.largeText.matches,
        screenReader: navigator.userAgent.includes('NVDA') ||
                     navigator.userAgent.includes('JAWS') ||
                     navigator.userAgent.includes('VoiceOver')
      });
    };

    updateAccessibilityFeatures();

    Object.values(mediaQueries).forEach(mq => {
      mq.addEventListener('change', updateAccessibilityFeatures);
    });

    return () => {
      Object.values(mediaQueries).forEach(mq => {
        mq.removeEventListener('change', updateAccessibilityFeatures);
      });
    };
  }, [enableAccessibility]);

  // Enhanced handler functions
  const handleScrollToTop = useCallback(() => {
    const behavior = accessibilityFeatures.reducedMotion ? 'auto' : scrollBehavior;

    window.scrollTo({
      top: 0,
      left: 0,
      behavior
    });

    if (enableAccessibility) {
      announceToScreenReader('Scrolled to top of page');
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(PAGE_ANALYTICS_EVENTS.ACCESSIBILITY_ACTION, {
        action: 'scroll_to_top',
        timestamp: Date.now()
      });
    }
  }, [accessibilityFeatures.reducedMotion, scrollBehavior, enableAccessibility, enableAnalytics, onAnalyticsTrack, announceToScreenReader]);

  const handleToggleFullscreen = useCallback(() => {
    const newFullscreenState = !isFullscreen;
    setIsFullscreen(newFullscreenState);

    if (newFullscreenState) {
      setCurrentLayoutMode(LAYOUT_MODES.FULLSCREEN);
    } else {
      setCurrentLayoutMode(layoutMode);
    }

    if (enableAccessibility) {
      announceToScreenReader(
        newFullscreenState ? 'Entered fullscreen mode' : 'Exited fullscreen mode'
      );
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(PAGE_ANALYTICS_EVENTS.FULLSCREEN_TOGGLE, {
        fullscreen: newFullscreenState,
        timestamp: Date.now()
      });
    }

    if (onLayoutModeChange) {
      onLayoutModeChange(newFullscreenState ? LAYOUT_MODES.FULLSCREEN : layoutMode);
    }
  }, [isFullscreen, layoutMode, enableAccessibility, enableAnalytics, onAnalyticsTrack, onLayoutModeChange, announceToScreenReader]);

  const handleRefreshPage = useCallback(() => {
    setIsLoading(true);
    setHasError(false);

    // Reset performance metrics
    setPerformanceMetrics({
      loadTime: 0,
      layoutShifts: 0,
      memoryUsage: 0,
      fps: 0,
      renderTime: 0
    });

    setLayoutShiftScore(0);

    // Reset page analytics
    setPageAnalytics({
      loadStartTime: Date.now(),
      interactionCount: 0,
      scrollEvents: 0,
      resizeEvents: 0,
      visibilityChanges: 0
    });

    setTimeout(() => {
      setIsLoading(false);

      if (enableAccessibility) {
        announceToScreenReader('Page refreshed successfully');
      }
    }, 1000);
  }, [enableAccessibility, announceToScreenReader]);

  const handleLayoutModeChange = useCallback((mode) => {
    if (Object.values(LAYOUT_MODES).includes(mode)) {
      setCurrentLayoutMode(mode);

      if (enableAccessibility) {
        announceToScreenReader(`Layout mode changed to ${mode}`);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(PAGE_ANALYTICS_EVENTS.LAYOUT_MODE_CHANGE, {
          previousMode: currentLayoutMode,
          newMode: mode,
          timestamp: Date.now()
        });
      }

      if (onLayoutModeChange) {
        onLayoutModeChange(mode);
      }
    }
  }, [currentLayoutMode, enableAccessibility, enableAnalytics, onAnalyticsTrack, onLayoutModeChange, announceToScreenReader]);

  const handleResetLayout = useCallback(() => {
    setCurrentLayoutMode(layoutMode);
    setIsFullscreen(false);

    if (enableAccessibility) {
      announceToScreenReader('Layout reset to default');
    }
  }, [layoutMode, enableAccessibility, announceToScreenReader]);

  const handleResetPerformanceMetrics = useCallback(() => {
    setPerformanceMetrics({
      loadTime: 0,
      layoutShifts: 0,
      memoryUsage: 0,
      fps: 0,
      renderTime: 0
    });
    setLayoutShiftScore(0);

    if (enableAccessibility) {
      announceToScreenReader('Performance metrics reset');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleSetScrollPosition = useCallback((position) => {
    window.scrollTo({
      top: position.y,
      left: position.x,
      behavior: accessibilityFeatures.reducedMotion ? 'auto' : scrollBehavior
    });
    setScrollPosition(position);
  }, [accessibilityFeatures.reducedMotion, scrollBehavior]);

  const handleSaveScrollPosition = useCallback(() => {
    const position = {
      x: window.pageXOffset,
      y: window.pageYOffset
    };
    scrollPositionRef.current = position;
    setScrollPosition(position);

    if (enableScrollRestoration) {
      sessionStorage.setItem('pageScrollPosition', JSON.stringify(position));
    }
  }, [enableScrollRestoration]);

  const handleRestoreScrollPosition = useCallback(() => {
    if (enableScrollRestoration) {
      const saved = sessionStorage.getItem('pageScrollPosition');
      if (saved) {
        try {
          const position = JSON.parse(saved);
          handleSetScrollPosition(position);
        } catch (error) {
          console.warn('Failed to restore scroll position:', error);
        }
      }
    }
  }, [enableScrollRestoration, handleSetScrollPosition]);

  const handleToggleAccessibilityFeature = useCallback((feature) => {
    setAccessibilityFeatures(prev => ({
      ...prev,
      [feature]: !prev[feature]
    }));

    if (enableAccessibility) {
      announceToScreenReader(`${feature} ${accessibilityFeatures[feature] ? 'disabled' : 'enabled'}`);
    }
  }, [accessibilityFeatures, enableAccessibility, announceToScreenReader]);

  const handleResetPageAnalytics = useCallback(() => {
    setPageAnalytics({
      loadStartTime: Date.now(),
      interactionCount: 0,
      scrollEvents: 0,
      resizeEvents: 0,
      visibilityChanges: 0
    });

    if (enableAccessibility) {
      announceToScreenReader('Page analytics reset');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const measureLayoutShift = useCallback(() => {
    return layoutShiftScore;
  }, [layoutShiftScore]);

  const handleOptimizePerformance = useCallback(() => {
    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }

    // Clear any cached data
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          if (name.includes('temp')) {
            caches.delete(name);
          }
        });
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Performance optimization completed');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleExportPageData = useCallback(() => {
    const exportData = {
      timestamp: new Date().toISOString(),
      layoutMode: currentLayoutMode,
      performanceMetrics,
      pageAnalytics,
      accessibilityFeatures,
      scrollPosition,
      layoutShiftScore
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `page-data-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    if (enableAccessibility) {
      announceToScreenReader('Page data exported successfully');
    }

    return exportData;
  }, [
    currentLayoutMode,
    performanceMetrics,
    pageAnalytics,
    accessibilityFeatures,
    scrollPosition,
    layoutShiftScore,
    enableAccessibility,
    announceToScreenReader
  ]);

  // Enhanced transition wrapper
  const TransitionWrapper = ({ children: transitionChildren }) => {
    switch (transitionType) {
      case TRANSITION_TYPES.FADE:
        return (
          <Fade in={!isLoading} timeout={300}>
            <div>{transitionChildren}</div>
          </Fade>
        );
      case TRANSITION_TYPES.SLIDE:
        return (
          <Slide direction="up" in={!isLoading} timeout={300}>
            <div>{transitionChildren}</div>
          </Slide>
        );
      case TRANSITION_TYPES.ZOOM:
        return (
          <Zoom in={!isLoading} timeout={300}>
            <div>{transitionChildren}</div>
          </Zoom>
        );
      default:
        return transitionChildren;
    }
  };

  // Loading state
  if (isLoading && loadingComponent) {
    return loadingComponent;
  }

  // Error state
  if (hasError && errorComponent) {
    return errorComponent;
  }

  return (
    <>
      <Container
        ref={containerRef}
        maxWidth={currentLayoutMode === LAYOUT_MODES.FLUID ? false : maxWidth}
        disableGutters={disableGutters || currentLayoutMode === LAYOUT_MODES.FULLSCREEN}
        sx={{
          py: currentLayoutMode === LAYOUT_MODES.FULLSCREEN ? 0 : theme.spacing(3),
          px: currentLayoutMode === LAYOUT_MODES.FULLSCREEN ? 0 : theme.spacing(2),
          minHeight: currentLayoutMode === LAYOUT_MODES.FULLSCREEN ? '100vh' : 'auto',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          ...layoutModeStyles,
          ...sx
        }}
        className={className}
        {...props}
      >
        {/* Performance monitoring indicator */}
        {enablePerformanceMonitoring && performanceMetrics.loadTime > PERFORMANCE_THRESHOLDS.LOAD_TIME && (
          <Alert
            severity="warning"
            sx={{
              mb: 2,
              '& .MuiAlert-icon': { color: ACE_COLORS.YELLOW }
            }}
          >
            Page load time exceeded {PERFORMANCE_THRESHOLDS.LOAD_TIME}ms. Consider optimizing content.
          </Alert>
        )}

        {/* Layout shift warning */}
        {enableLayoutShiftPrevention && layoutShiftScore > PERFORMANCE_THRESHOLDS.LAYOUT_SHIFT && (
          <Alert
            severity="info"
            sx={{
              mb: 2,
              '& .MuiAlert-icon': { color: ACE_COLORS.PURPLE }
            }}
          >
            Layout shifts detected. Content may be moving unexpectedly.
          </Alert>
        )}

        {/* Loading progress */}
        <Collapse in={isLoading}>
          <LinearProgress
            sx={{
              mb: 2,
              '& .MuiLinearProgress-bar': {
                backgroundColor: ACE_COLORS.PURPLE
              }
            }}
          />
        </Collapse>

        <TransitionWrapper>
          <Paper
            ref={contentRef}
            elevation={currentLayoutMode === LAYOUT_MODES.FULLSCREEN ? 0 : undefined}
            sx={{
              flex: 1,
              p: currentLayoutMode === LAYOUT_MODES.COMPACT ? theme.spacing(2) : theme.spacing(3),
              ...glassMorphismStyles,

              // Enhanced WCAG 2.1 AA compliance
              '&:focus-visible': {
                outline: `2px solid ${ACE_COLORS.PURPLE}`,
                outlineOffset: '2px',
              },

              // Enhanced responsive design
              [theme.breakpoints.down('sm')]: {
                p: theme.spacing(isMobile ? 1 : 2),
                mx: currentLayoutMode === LAYOUT_MODES.FULLSCREEN ? 0 : theme.spacing(1),
              },
              [theme.breakpoints.between('md', 'lg')]: {
                p: theme.spacing(isTablet ? 2 : 3),
              },

              // Enhanced accessibility support
              '@media (prefers-contrast: high)': {
                border: `2px solid ${theme.palette.text.primary}`,
                background: theme.palette.background.paper,
                backdropFilter: 'none',
              },
              '@media (prefers-reduced-motion: reduce)': {
                transition: 'none',
              },
              '@media print': {
                background: 'white',
                backdropFilter: 'none',
                boxShadow: 'none',
                border: '1px solid black',
              },

              // High contrast mode
              ...(accessibilityFeatures.highContrast && {
                border: `2px solid ${theme.palette.text.primary}`,
                background: theme.palette.background.paper,
                backdropFilter: 'none',
              }),

              // Large text mode
              ...(accessibilityFeatures.largeText && {
                fontSize: '1.125rem',
                lineHeight: 1.6,
              })
            }}
            role="main"
            aria-label="Page content"
            tabIndex={-1}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: theme.spacing(3),
                minHeight: '100%',
                position: 'relative'
              }}
            >
              {/* Loading skeleton overlay */}
              {isLoading && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 1,
                    backgroundColor: alpha(theme.palette.background.paper, 0.8),
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    p: 3
                  }}
                >
                  <Skeleton variant="text" width="60%" height={40} />
                  <Skeleton variant="rectangular" width="100%" height={200} />
                  <Skeleton variant="text" width="80%" height={24} />
                  <Skeleton variant="text" width="40%" height={24} />
                </Box>
              )}

              {children}
            </Box>
          </Paper>
        </TransitionWrapper>
      </Container>

      {/* Scroll to top button */}
      <Zoom in={showScrollTop}>
        <Fab
          onClick={handleScrollToTop}
          color="primary"
          size="medium"
          aria-label="Scroll to top"
          sx={{
            position: 'fixed',
            bottom: theme.spacing(2),
            right: theme.spacing(2),
            zIndex: theme.zIndex.fab,
            bgcolor: ACE_COLORS.PURPLE,
            '&:hover': {
              bgcolor: alpha(ACE_COLORS.PURPLE, 0.8)
            }
          }}
        >
          <ScrollTopIcon />
        </Fab>
      </Zoom>

      {/* Fullscreen toggle button */}
      {enableFullscreenMode && (
        <Tooltip title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}>
          <Fab
            onClick={handleToggleFullscreen}
            size="small"
            aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            sx={{
              position: 'fixed',
              bottom: theme.spacing(10),
              right: theme.spacing(2),
              zIndex: theme.zIndex.fab,
              bgcolor: alpha(ACE_COLORS.YELLOW, 0.9),
              color: ACE_COLORS.DARK,
              '&:hover': {
                bgcolor: ACE_COLORS.YELLOW
              }
            }}
          >
            {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
          </Fab>
        </Tooltip>
      )}

      {/* Performance metrics snackbar */}
      {enablePerformanceMonitoring && performanceMetrics.memoryUsage > PERFORMANCE_THRESHOLDS.MEMORY_USAGE && (
        <Snackbar
          open={true}
          autoHideDuration={6000}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert
            severity="warning"
            action={
              <IconButton
                size="small"
                aria-label="optimize performance"
                color="inherit"
                onClick={handleOptimizePerformance}
              >
                <SpeedIcon />
              </IconButton>
            }
          >
            High memory usage detected. Consider optimizing page content.
          </Alert>
        </Snackbar>
      )}
    </>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedStablePageWrapper.propTypes = {
  // Core props
  children: PropTypes.node.isRequired,
  maxWidth: PropTypes.oneOfType([
    PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]),
    PropTypes.string
  ]),
  disableGutters: PropTypes.bool,
  enableGlassMorphism: PropTypes.bool,

  // Enhanced props
  enablePerformanceMonitoring: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableScrollRestoration: PropTypes.bool,
  enableLayoutShiftPrevention: PropTypes.bool,
  enableFullscreenMode: PropTypes.bool,

  // Layout configuration
  layoutMode: PropTypes.oneOf(Object.values(LAYOUT_MODES)),
  transitionType: PropTypes.oneOf(Object.values(TRANSITION_TYPES)),
  scrollBehavior: PropTypes.oneOf(Object.values(SCROLL_BEHAVIORS)),

  // Component props
  loadingComponent: PropTypes.node,
  errorComponent: PropTypes.node,

  // Callback props
  onLayoutShift: PropTypes.func,
  onPerformanceMetric: PropTypes.func,
  onScrollPosition: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onLayoutModeChange: PropTypes.func,

  // Standard props
  className: PropTypes.string,
  sx: PropTypes.object
};

// Default props
EnhancedStablePageWrapper.defaultProps = {
  maxWidth: "xl",
  disableGutters: false,
  enableGlassMorphism: true,
  enablePerformanceMonitoring: true,
  enableAccessibility: true,
  enableAnalytics: true,
  enableScrollRestoration: true,
  enableLayoutShiftPrevention: true,
  enableFullscreenMode: false,
  layoutMode: LAYOUT_MODES.STANDARD,
  transitionType: TRANSITION_TYPES.FADE,
  scrollBehavior: SCROLL_BEHAVIORS.SMOOTH,
  loadingComponent: null,
  errorComponent: null,
  onLayoutShift: null,
  onPerformanceMetric: null,
  onScrollPosition: null,
  onAnalyticsTrack: null,
  onLayoutModeChange: null,
  className: '',
  sx: {}
};

// Display name for debugging
EnhancedStablePageWrapper.displayName = 'EnhancedStablePageWrapper';

export default EnhancedStablePageWrapper;

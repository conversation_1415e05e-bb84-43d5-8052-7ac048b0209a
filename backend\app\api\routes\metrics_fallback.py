"""
Metrics Fallback API
Provides fallback endpoints for metrics collection when full metrics service is not available
"""

from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import JSONResponse
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/metrics", tags=["metrics-fallback"])

@router.post("/token-manager")
async def token_manager_metrics_fallback(request: Request):
    """
    Fallback endpoint for token manager metrics
    
    This endpoint accepts metrics data but doesn't process it on serverless platforms
    where full metrics collection may not be available.
    """
    try:
        # Check if this is a fallback request
        is_fallback = request.headers.get("X-Fallback-Mode") == "true"
        
        # Read the request body (but don't process it in fallback mode)
        try:
            body = await request.json()
        except:
            body = {}
        
        if is_fallback:
            logger.debug("Token manager metrics received in fallback mode (not processed)")
        else:
            logger.info("Token manager metrics received")
        
        return JSONResponse(
            content={
                "status": "accepted",
                "message": "Metrics received" + (" (fallback mode)" if is_fallback else ""),
                "timestamp": datetime.utcnow().isoformat(),
                "fallbackMode": is_fallback
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error in token manager metrics fallback: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "message": "Failed to process metrics",
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=500
        )

@router.post("/websocket")
async def websocket_metrics_fallback(request: Request):
    """
    Fallback endpoint for WebSocket metrics
    """
    try:
        is_fallback = request.headers.get("X-Fallback-Mode") == "true"
        
        try:
            body = await request.json()
        except:
            body = {}
        
        if is_fallback:
            logger.debug("WebSocket metrics received in fallback mode (not processed)")
        else:
            logger.info("WebSocket metrics received")
        
        return JSONResponse(
            content={
                "status": "accepted",
                "message": "WebSocket metrics received" + (" (fallback mode)" if is_fallback else ""),
                "timestamp": datetime.utcnow().isoformat(),
                "fallbackMode": is_fallback
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error in WebSocket metrics fallback: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "message": "Failed to process WebSocket metrics",
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=500
        )

@router.post("/platform")
async def platform_metrics_fallback(request: Request):
    """
    Fallback endpoint for platform metrics
    """
    try:
        is_fallback = request.headers.get("X-Fallback-Mode") == "true"
        
        try:
            body = await request.json()
        except:
            body = {}
        
        if is_fallback:
            logger.debug("Platform metrics received in fallback mode (not processed)")
        else:
            logger.info("Platform metrics received")
        
        return JSONResponse(
            content={
                "status": "accepted",
                "message": "Platform metrics received" + (" (fallback mode)" if is_fallback else ""),
                "timestamp": datetime.utcnow().isoformat(),
                "fallbackMode": is_fallback
            },
            status_code=200
        )
        
    except Exception as e:
        logger.error(f"Error in platform metrics fallback: {e}")
        return JSONResponse(
            content={
                "status": "error",
                "message": "Failed to process platform metrics",
                "timestamp": datetime.utcnow().isoformat()
            },
            status_code=500
        )

@router.get("/health")
async def metrics_health_check():
    """
    Health check for metrics endpoints
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "metrics-fallback"
    }

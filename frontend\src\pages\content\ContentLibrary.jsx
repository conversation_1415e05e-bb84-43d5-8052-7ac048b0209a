// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActionArea,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Tooltip,
  CircularProgress,
  useTheme,
  alpha,
  Paper,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Checkbox,
  FormControlLabel,
  Tabs,
  Tab,
  Badge,
  Snackbar,
  Alert,
  LinearProgress
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Sort as SortIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ContentCopy as ContentCopyIcon,
  Share as ShareIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Schedule as ScheduleIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  LinkedIn as LinkedInIcon,
  Pinterest as PinterestIcon,
  YouTube as YouTubeIcon,
  Label as LabelIcon,
  CalendarToday as CalendarTodayIcon,
  Close as CloseIcon,
  History as HistoryIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  CloudUpload as UploadIcon,
  Download as DownloadIcon,
  Archive as ArchiveIcon,
  Unarchive as UnarchiveIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import api from '../../api';
import { useNotification } from '../../hooks/useNotification';
import { useConfirmation } from '../../hooks/useConfirmation';
import { useAuth } from '../../contexts/AuthContext';
import PageHeader from '../../components/common/PageHeader';
import EmptyState from '../../components/common/EmptyState';
import ContentCard from '../../components/content/ContentCard';
import ContentFilterDrawer from '../../components/content/ContentFilterDrawer';
import ContentBulkActions from '../../components/content/ContentBulkActions';
import ContentDetailDialog from '../../components/content/ContentDetailDialog';
import ScheduleContentDialog from '../../components/content/ScheduleContentDialog';

const ContentLibrary = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { showConfirmation } = useConfirmation();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // State
  const [contents, setContents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    platforms: [],
    tags: [],
    dateRange: {
      startDate: null,
      endDate: null,
    },
    status: 'all',
  });
  const [sortOption, setSortOption] = useState('newest');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedContents, setSelectedContents] = useState([]);
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [viewMode, setViewMode] = useState('grid');
  const [anchorEl, setAnchorEl] = useState(null);
  const [sortAnchorEl, setSortAnchorEl] = useState(null);
  const [currentContent, setCurrentContent] = useState(null);

  // Dialog states
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [selectedContentId, setSelectedContentId] = useState(null);
  const [isCreatingContent, setIsCreatingContent] = useState(false);
  const [schedules, setSchedules] = useState([]);
  const [undoStack, setUndoStack] = useState([]);
  const [redoStack, setRedoStack] = useState([]);
  const [actionInProgress, setActionInProgress] = useState(false);
  const [actionMessage, setActionMessage] = useState('');
  const [actionSuccess, setActionSuccess] = useState(true);

  // Check authentication and redirect if needed
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Fetch content on mount and when filters change
  useEffect(() => {
    // Don't fetch if still loading auth or not authenticated
    if (authLoading || !isAuthenticated) {
      return;
    }
    fetchContents();
  }, [page, filters, sortOption, searchTerm, authLoading, isAuthenticated]);

  // Fetch contents from API
  const fetchContents = async () => {
    setLoading(true);
    try {
      // Build query parameters
      const params = {
        page,
        limit: 12,
        sort: sortOption,
        search: searchTerm,
      };

      if (filters.platforms.length > 0) {
        params.platforms = filters.platforms.join(',');
      }

      if (filters.tags.length > 0) {
        params.tags = filters.tags.join(',');
      }

      if (filters.dateRange.startDate) {
        params.start_date = filters.dateRange.startDate.toISOString();
      }

      if (filters.dateRange.endDate) {
        params.end_date = filters.dateRange.endDate.toISOString();
      }

      if (filters.status !== 'all') {
        params.status = filters.status;
      }

      try {
        // Call API using the library endpoint
        const response = await api.get('/api/content/library', { params });

        // Validate response data
        if (response.data && Array.isArray(response.data.contents)) {
          // Update state with valid content items
          const validContents = response.data.contents.filter(item =>
            item && typeof item === 'object' && item.id
          );

          setContents(validContents);
          setTotalPages(response.data.total_pages || 1);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (apiError) {
        // Log error for debugging in development only
        if (process.env.NODE_ENV === 'development') {
          console.error('API Error:', apiError);
        }
        throw apiError;
      }
    } catch (error) {
      // Log error for debugging in development only
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching contents:', error);
      }

      // Handle authentication errors
      if (error.response?.status === 401) {
        navigate('/login');
        return;
      }

      // Show user-friendly error message
      let errorMessage = "Unable to load content library at this time.";
      if (error.response?.status === 403) {
        errorMessage = "You don't have permission to view the content library.";
      } else if (error.response?.status >= 500) {
        errorMessage = "Service temporarily unavailable. Please try again in a moment.";
      } else if (error.code === "ECONNREFUSED" || error.message === "Network Error") {
        errorMessage = "Connection error. Please check your internet connection and try again.";
      }

      showErrorNotification(errorMessage);

      // Set empty content array on error
      setContents([]);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  };

  // Handle content selection
  const handleSelectContent = (contentId) => {
    setSelectedContents((prev) => {
      if (prev.includes(contentId)) {
        return prev.filter((id) => id !== contentId);
      } else {
        return [...prev, contentId];
      }
    });
  };

  // Handle bulk selection
  const handleSelectAll = () => {
    if (selectedContents.length === contents.length) {
      setSelectedContents([]);
    } else {
      setSelectedContents(contents.map((content) => content.id));
    }
  };

  // Handle content deletion
  const handleDeleteContent = async (contentId) => {
    const confirmed = await showConfirmation({
      title: 'Delete Content',
      message: 'Are you sure you want to delete this content? This action cannot be undone.',
      confirmButtonText: 'Delete',
      confirmButtonColor: 'error',
    });

    if (confirmed) {
      try {
        await api.delete(`/api/content/${contentId}`);
        showSuccessNotification('Content deleted successfully');
        fetchContents();
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error deleting content:', error);
        }
        showErrorNotification('Failed to delete content');
      }
    }
  };

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    const confirmed = await showConfirmation({
      title: 'Delete Selected Content',
      message: `Are you sure you want to delete ${selectedContents.length} selected content items? This action cannot be undone.`,
      confirmButtonText: 'Delete All',
      confirmButtonColor: 'error',
    });

    if (confirmed) {
      try {
        await api.post('/api/content/bulk-delete', { content_ids: selectedContents });
        showSuccessNotification(`${selectedContents.length} content items deleted successfully`);
        setSelectedContents([]);
        fetchContents();
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error deleting contents:', error);
        }
        showErrorNotification('Failed to delete selected content items');
      }
    }
  };

  // Handle page change
  const handlePageChange = (event, value) => {
    setPage(value);
    window.scrollTo(0, 0);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setPage(1);
  };

  // Handle sort menu
  const handleSortMenuOpen = (event) => {
    setSortAnchorEl(event.currentTarget);
  };

  const handleSortMenuClose = () => {
    setSortAnchorEl(null);
  };

  const handleSortChange = (option) => {
    setSortOption(option);
    setSortAnchorEl(null);
    setPage(1);
  };

  // Handle content menu
  const handleContentMenuOpen = (event, content) => {
    event.stopPropagation();
    setCurrentContent(content);
    setAnchorEl(event.currentTarget);
  };

  const handleContentMenuClose = () => {
    setAnchorEl(null);
  };

  // Open content detail dialog
  const handleViewContent = (contentId) => {
    setSelectedContentId(contentId);
    setDetailDialogOpen(true);
  };

  // Open content detail dialog in edit mode
  const handleEditContent = (contentId) => {
    setSelectedContentId(contentId);
    setDetailDialogOpen(true);
  };

  // Handle content duplication
  const handleDuplicateContent = async (contentId) => {
    setActionInProgress(true);
    setActionMessage('Duplicating content...');

    try {
      const response = await api.post(`/api/content/${contentId}/duplicate`);
      showSuccessNotification('Content duplicated successfully');
      fetchContents();
      handleContentMenuClose();
      setActionSuccess(true);

      // Add to undo stack
      setUndoStack(prev => [...prev, {
        action: 'duplicate',
        contentId: response.data.id,
        originalContent: response.data
      }]);

      // Clear redo stack after a new action
      setRedoStack([]);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error duplicating content:', error);
      }
      showErrorNotification('Failed to duplicate content');
      setActionSuccess(false);
    } finally {
      setActionInProgress(false);
    }
  };

  // Handle content scheduling
  const handleScheduleContent = (contentId) => {
    setSelectedContentId(contentId);
    setScheduleDialogOpen(true);
  };

  // Handle create new content
  const handleCreateContent = () => {
    setSelectedContentId(null);
    setIsCreatingContent(true);
    setDetailDialogOpen(true);
  };

  // Handle save content
  const handleSaveContent = (content) => {
    fetchContents();
    setDetailDialogOpen(false);
    showSuccessNotification(`Content ${content.id ? 'updated' : 'created'} successfully`);

    // Add to undo stack
    setUndoStack(prev => [...prev, {
      action: content.id ? 'update' : 'create',
      contentId: content.id,
      originalContent: content
    }]);

    // Clear redo stack after a new action
    setRedoStack([]);
  };

  // Handle save schedule
  const handleSaveSchedule = (schedule) => {
    fetchContents();
    setScheduleDialogOpen(false);
    showSuccessNotification('Content scheduled successfully');
  };

  // Handle undo
  const handleUndo = () => {
    if (undoStack.length === 0) return;

    setActionInProgress(true);
    setActionMessage('Undoing last action...');

    const lastAction = undoStack[undoStack.length - 1];

    try {
      // Perform undo based on action type
      switch (lastAction.action) {
        case 'create':
          // Delete the created content
          api.delete(`/api/content/${lastAction.contentId}`);
          break;
        case 'update':
          // Revert to original content
          api.put(`/api/content/${lastAction.contentId}`, lastAction.originalContent);
          break;
        case 'delete':
          // Restore deleted content
          api.post('/api/content/restore', { content_id: lastAction.contentId });
          break;
        case 'duplicate':
          // Delete the duplicated content
          api.delete(`/api/content/${lastAction.contentId}`);
          break;
        default:
          break;
      }

      // Update stacks
      const newUndoStack = [...undoStack];
      const poppedAction = newUndoStack.pop();
      setUndoStack(newUndoStack);
      setRedoStack(prev => [...prev, poppedAction]);

      // Refresh content
      fetchContents();
      setActionSuccess(true);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error undoing action:', error);
      }
      showErrorNotification('Failed to undo last action');
      setActionSuccess(false);
    } finally {
      setActionInProgress(false);
    }
  };

  // Handle redo
  const handleRedo = () => {
    if (redoStack.length === 0) return;

    setActionInProgress(true);
    setActionMessage('Redoing last action...');

    const lastAction = redoStack[redoStack.length - 1];

    try {
      // Perform redo based on action type
      switch (lastAction.action) {
        case 'create':
          // Recreate the content
          api.post('/api/content', lastAction.originalContent);
          break;
        case 'update':
          // Apply the update again
          api.put(`/api/content/${lastAction.contentId}`, lastAction.originalContent);
          break;
        case 'delete':
          // Delete the content again
          api.delete(`/api/content/${lastAction.contentId}`);
          break;
        case 'duplicate':
          // Duplicate the content again
          api.post(`/api/content/${lastAction.originalContent.id}/duplicate`);
          break;
        default:
          break;
      }

      // Update stacks
      const newRedoStack = [...redoStack];
      const poppedAction = newRedoStack.pop();
      setRedoStack(newRedoStack);
      setUndoStack(prev => [...prev, poppedAction]);

      // Refresh content
      fetchContents();
      setActionSuccess(true);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error redoing action:', error);
      }
      showErrorNotification('Failed to redo last action');
      setActionSuccess(false);
    } finally {
      setActionInProgress(false);
    }
  };

  return (
    <Box sx={{ pb: 4 }}>
      <PageHeader
        title="Content Library"
        description="Manage and organize all your content in one place"
        actions={
          <Box sx={{ display: 'flex', gap: 1 }}>
            {undoStack.length > 0 && (
              <Tooltip title="Undo last action">
                <IconButton onClick={handleUndo} disabled={actionInProgress}>
                  <UndoIcon />
                </IconButton>
              </Tooltip>
            )}
            {redoStack.length > 0 && (
              <Tooltip title="Redo last action">
                <IconButton onClick={handleRedo} disabled={actionInProgress}>
                  <RedoIcon />
                </IconButton>
              </Tooltip>
            )}
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleCreateContent}
            >
              Create Content
            </Button>
          </Box>
        }
      />

      {/* Search and Filter Bar */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          mb: 3,
          display: 'flex',
          flexWrap: 'wrap',
          gap: 2,
          alignItems: 'center',
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          bgcolor: alpha(theme.palette.background.paper, 0.8),
        }}
      >
        <TextField
          placeholder="Search content..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          variant="outlined"
          size="small"
          sx={{ flexGrow: 1, minWidth: 200 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => setSearchTerm('')}
                  edge="end"
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        <Button
          variant="outlined"
          startIcon={<FilterListIcon />}
          onClick={() => setFilterDrawerOpen(true)}
          size="medium"
        >
          Filters
          {Object.values(filters).some(val =>
            Array.isArray(val) ? val.length > 0 :
            val !== 'all' && val !== null &&
            !(val instanceof Object && Object.values(val).every(v => v === null))
          ) && (
            <Chip
              size="small"
              label={
                Object.values(filters).reduce((count, val) => {
                  if (Array.isArray(val)) {
                    return count + (val.length > 0 ? 1 : 0);
                  } else if (val instanceof Object) {
                    return count + (Object.values(val).some(v => v !== null) ? 1 : 0);
                  } else {
                    return count + (val !== 'all' && val !== null ? 1 : 0);
                  }
                }, 0)
              }
              color="primary"
              sx={{ ml: 1 }}
            />
          )}
        </Button>

        <Button
          variant="outlined"
          startIcon={<SortIcon />}
          onClick={handleSortMenuOpen}
          size="medium"
        >
          {sortOption === 'newest' ? 'Newest First' :
           sortOption === 'oldest' ? 'Oldest First' :
           sortOption === 'a-z' ? 'A-Z' :
           sortOption === 'z-a' ? 'Z-A' : 'Sort'}
        </Button>

        <Menu
          anchorEl={sortAnchorEl}
          open={Boolean(sortAnchorEl)}
          onClose={handleSortMenuClose}
        >
          <MenuItem onClick={() => handleSortChange('newest')}>
            <ListItemText primary="Newest First" />
          </MenuItem>
          <MenuItem onClick={() => handleSortChange('oldest')}>
            <ListItemText primary="Oldest First" />
          </MenuItem>
          <MenuItem onClick={() => handleSortChange('a-z')}>
            <ListItemText primary="A-Z" />
          </MenuItem>
          <MenuItem onClick={() => handleSortChange('z-a')}>
            <ListItemText primary="Z-A" />
          </MenuItem>
        </Menu>
      </Paper>

      {/* Bulk Actions */}
      {selectedContents.length > 0 && (
        <ContentBulkActions
          selectedCount={selectedContents.length}
          onClearSelection={() => setSelectedContents([])}
          onDelete={handleBulkDelete}
          onSchedule={() => {
            if (selectedContents.length === 1) {
              handleScheduleContent(selectedContents[0]);
            } else {
              showErrorNotification('Bulk scheduling is not yet supported. Please select only one item.');
            }
          }}
          onDuplicate={() => {
            if (selectedContents.length === 1) {
              handleDuplicateContent(selectedContents[0]);
            } else {
              showErrorNotification('Bulk duplication is not yet supported. Please select only one item.');
            }
          }}
          onShare={() => {
            showErrorNotification('Sharing functionality is coming soon!');
          }}
        />
      )}

      {/* Content Grid */}
      {(loading || authLoading) ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : !isAuthenticated ? (
        // Don't render anything if not authenticated (will redirect)
        null
      ) : contents.length === 0 ? (
        <EmptyState
          title="Your Content Library is Empty"
          description="Create and manage your content here. You can create posts, articles, and other content types to publish across your social media platforms."
          actionText="Create New Content"
          actionIcon={<AddIcon />}
          onAction={() => navigate('/content/generator')}
          secondaryActionText="Import Content"
          onSecondaryAction={() => navigate('/content/import')}
          steps={[
            "Create content using our AI-powered content generator",
            "Schedule posts across multiple platforms",
            "Track performance and engagement metrics",
            "Organize content with tags and categories"
          ]}
        />
      ) : (
        <>
          <Grid container spacing={3}>
            {contents.map((content) => {
              // Skip rendering if content is invalid
              if (!content || !content.id) {
                return null;
              }

              // Wrap each card in a try-catch to prevent entire grid from failing
              try {
                return (
                  <Grid item xs={12} sm={6} md={4} key={content.id}>
                    <ContentCard
                      content={content}
                      selected={selectedContents.includes(content.id)}
                      onSelect={() => handleSelectContent(content.id)}
                      onView={() => handleViewContent(content.id)}
                      onEdit={() => handleEditContent(content.id)}
                      onDelete={() => handleDeleteContent(content.id)}
                      onMenuOpen={(e) => handleContentMenuOpen(e, content)}
                    />
                  </Grid>
                );
              } catch (error) {
                // Log error for debugging in development only
                if (process.env.NODE_ENV === 'development') {
                  console.error(`Error rendering content card ${content.id}:`, error);
                }
                return (
                  <Grid item xs={12} sm={6} md={4} key={content.id || `error-${Math.random()}`}>
                    <Paper
                      elevation={1}
                      sx={{
                        p: 2,
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 2,
                        bgcolor: alpha(theme.palette.error.light, 0.1)
                      }}
                    >
                      <Typography color="error" gutterBottom>Error displaying content</Typography>
                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        onClick={() => handleDeleteContent(content.id)}
                      >
                        Remove
                      </Button>
                    </Paper>
                  </Grid>
                );
              }
            })}
          </Grid>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
              />
            </Box>
          )}
        </>
      )}

      {/* Filter Drawer */}
      <ContentFilterDrawer
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        filters={filters}
        onFilterChange={handleFilterChange}
      />

      {/* Content Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleContentMenuClose}
      >
        <MenuItem onClick={() => {
          handleViewContent(currentContent?.id);
          handleContentMenuClose();
        }}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="View Details" />
        </MenuItem>
        <MenuItem onClick={() => {
          handleEditContent(currentContent?.id);
          handleContentMenuClose();
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Edit" />
        </MenuItem>
        <MenuItem onClick={() => {
          handleDuplicateContent(currentContent?.id);
        }}>
          <ListItemIcon>
            <ContentCopyIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Duplicate" />
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          handleScheduleContent(currentContent?.id);
          handleContentMenuClose();
        }}>
          <ListItemIcon>
            <ScheduleIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Schedule" />
        </MenuItem>
        <MenuItem onClick={() => {
          navigate(`/collaboration/share?content=${currentContent?.id}`);
          handleContentMenuClose();
        }}>
          <ListItemIcon>
            <ShareIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Share" />
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          handleDeleteContent(currentContent?.id);
          handleContentMenuClose();
        }} sx={{ color: theme.palette.error.main }}>
          <ListItemIcon sx={{ color: theme.palette.error.main }}>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Delete" />
        </MenuItem>
      </Menu>

      {/* Content Detail Dialog */}
      <ContentDetailDialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        contentId={selectedContentId}
        onSave={handleSaveContent}
        onDelete={handleDeleteContent}
        onSchedule={handleScheduleContent}
        onDuplicate={handleDuplicateContent}
      />

      {/* Schedule Dialog */}
      <ScheduleContentDialog
        open={scheduleDialogOpen}
        onClose={() => setScheduleDialogOpen(false)}
        contentId={selectedContentId}
        onSave={handleSaveSchedule}
        existingSchedules={schedules}
      />

      {/* Action Snackbar */}
      <Snackbar
        open={actionInProgress}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          severity="info"
          icon={<CircularProgress size={20} />}
          sx={{ width: '100%' }}
        >
          {actionMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ContentLibrary;

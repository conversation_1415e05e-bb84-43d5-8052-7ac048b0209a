#!/usr/bin/env node
// @since 2024-1-1 to 2025-25-7

/**
 * Frontend Bundle Analysis & Performance Audit Tool
 *
 * This script analyzes the Vite build output and provides detailed
 * performance metrics and optimization recommendations.
 */

const fs = require('fs');
const path = require('path');

// Performance targets
const PERFORMANCE_TARGETS = {
  totalBundleSize: 2 * 1024 * 1024, // 2MB
  maxChunkSize: 500 * 1024, // 500KB
  maxInitialLoadTime: 2000, // 2 seconds
  lighthouse: {
    performance: 90,
    accessibility: 95,
    bestPractices: 90,
    seo: 90
  }
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundle() {
  console.log('🔍 FRONTEND BUNDLE ANALYSIS & PERFORMANCE AUDIT');
  console.log('='.repeat(60));

  const distPath = path.join(process.cwd(), 'dist', 'assets');

  if (!fs.existsSync(distPath)) {
    console.log('❌ Dist folder not found. Run "npm run build" first.');
    process.exit(1);
  }

  const files = fs.readdirSync(distPath);
  const jsFiles = files.filter(f => f.endsWith('.js'));
  const cssFiles = files.filter(f => f.endsWith('.css'));

  console.log('\n📊 BUNDLE COMPOSITION:');
  console.log(`  - JavaScript files: ${jsFiles.length}`);
  console.log(`  - CSS files: ${cssFiles.length}`);
  console.log(`  - Total assets: ${files.length}`);

  // Analyze file sizes
  let totalSize = 0;
  const fileAnalysis = [];

  files.forEach(file => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    const size = stats.size;
    totalSize += size;

    fileAnalysis.push({
      name: file,
      size: size,
      type: path.extname(file).substring(1),
      category: categorizeFile(file)
    });
  });

  // Sort by size (largest first)
  fileAnalysis.sort((a, b) => b.size - a.size);

  console.log('\n📈 LARGEST CHUNKS:');
  fileAnalysis.slice(0, 10).forEach((file, index) => {
    const sizeFormatted = formatBytes(file.size);
    const status = file.size > PERFORMANCE_TARGETS.maxChunkSize ? '⚠️' : '✅';
    console.log(`  ${index + 1}. ${status} ${file.name}: ${sizeFormatted} (${file.category})`);
  });

  // Performance assessment
  console.log('\n🎯 PERFORMANCE ASSESSMENT:');
  console.log(`  Total bundle size: ${formatBytes(totalSize)}`);

  if (totalSize <= PERFORMANCE_TARGETS.totalBundleSize) {
    console.log('  ✅ Bundle size within 2MB target');
  } else {
    const excess = totalSize - PERFORMANCE_TARGETS.totalBundleSize;
    console.log(`  ⚠️  Bundle size exceeds target by ${formatBytes(excess)}`);
  }

  // Chunk analysis
  const largeChunks = fileAnalysis.filter(f => f.size > PERFORMANCE_TARGETS.maxChunkSize);
  if (largeChunks.length > 0) {
    console.log(`  ⚠️  ${largeChunks.length} chunks exceed 500KB limit`);
  } else {
    console.log('  ✅ All chunks within size limits');
  }

  // Category breakdown
  console.log('\n📋 SIZE BY CATEGORY:');
  const categories = groupByCategory(fileAnalysis);
  Object.entries(categories).forEach(([category, files]) => {
    const totalCategorySize = files.reduce((sum, f) => sum + f.size, 0);
    console.log(`  - ${category}: ${formatBytes(totalCategorySize)} (${files.length} files)`);
  });

  // Optimization recommendations
  console.log('\n💡 OPTIMIZATION RECOMMENDATIONS:');
  generateRecommendations(fileAnalysis, totalSize);

  // Check for analysis report
  const statsFile = path.join(process.cwd(), 'dist', 'stats.html');
  if (fs.existsSync(statsFile)) {
    console.log('\n📊 DETAILED ANALYSIS:');
    console.log('  ✅ Bundle visualization available: dist/stats.html');
  }

  // Performance score
  const score = calculatePerformanceScore(totalSize, largeChunks.length);
  console.log('\n🏆 PERFORMANCE SCORE:');
  console.log(`  Overall: ${score}/100 ${getScoreEmoji(score)}`);

  console.log('\n' + '='.repeat(60));
}

function categorizeFile(filename) {
  if (filename.includes('vendor')) return 'Vendor Libraries';
  if (filename.includes('chunk')) return 'Application Chunks';
  if (filename.includes('react')) return 'React Ecosystem';
  if (filename.includes('mui')) return 'Material-UI';
  if (filename.includes('charts')) return 'Chart Libraries';
  if (filename.includes('media')) return 'Media Processing';
  if (filename.includes('utils')) return 'Utilities';
  if (filename.includes('index')) return 'Main Application';
  if (filename.endsWith('.css')) return 'Stylesheets';
  return 'Application Code';
}

function groupByCategory(files) {
  return files.reduce((groups, file) => {
    const category = file.category;
    if (!groups[category]) groups[category] = [];
    groups[category].push(file);
    return groups;
  }, {});
}

function generateRecommendations(files, totalSize) {
  const recommendations = [];

  // Large bundle recommendations
  if (totalSize > PERFORMANCE_TARGETS.totalBundleSize) {
    recommendations.push('🔧 Implement more aggressive code splitting');
    recommendations.push('🔧 Consider lazy loading non-critical components');
  }

  // Large chunk recommendations
  const largeChunks = files.filter(f => f.size > PERFORMANCE_TARGETS.maxChunkSize);
  if (largeChunks.length > 0) {
    recommendations.push('🔧 Split large chunks using dynamic imports');
    largeChunks.forEach(chunk => {
      if (chunk.name.includes('charts')) {
        recommendations.push(`   - Lazy load chart components in ${chunk.name}`);
      }
      if (chunk.name.includes('content')) {
        recommendations.push(`   - Split content management features in ${chunk.name}`);
      }
    });
  }

  // Vendor optimization
  const vendorFiles = files.filter(f => f.category === 'Vendor Libraries');
  const totalVendorSize = vendorFiles.reduce((sum, f) => sum + f.size, 0);
  if (totalVendorSize > 1024 * 1024) { // 1MB
    recommendations.push('🔧 Optimize vendor bundle splitting');
    recommendations.push('   - Consider using CDN for large libraries');
  }

  // General optimizations
  recommendations.push('🔧 Enable gzip/brotli compression on server');
  recommendations.push('🔧 Implement service worker for caching');
  recommendations.push('🔧 Use image optimization and lazy loading');

  if (recommendations.length === 0) {
    console.log('  ✅ Bundle is well optimized!');
  } else {
    recommendations.forEach(rec => console.log(`  ${rec}`));
  }
}

function calculatePerformanceScore(totalSize, largeChunkCount) {
  let score = 100;

  // Deduct points for large bundle
  if (totalSize > PERFORMANCE_TARGETS.totalBundleSize) {
    const excess = (totalSize - PERFORMANCE_TARGETS.totalBundleSize) / PERFORMANCE_TARGETS.totalBundleSize;
    score -= Math.min(30, excess * 50);
  }

  // Deduct points for large chunks
  score -= largeChunkCount * 10;

  return Math.max(0, Math.round(score));
}

function getScoreEmoji(score) {
  if (score >= 90) return '🟢 Excellent';
  if (score >= 70) return '🟡 Good';
  if (score >= 50) return '🟠 Needs Improvement';
  return '🔴 Poor';
}

// Run analysis
analyzeBundle();

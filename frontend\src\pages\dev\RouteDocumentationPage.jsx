// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Collapse,
  Card,
  CardContent,
  Divider,
  List,
  alpha,
  useTheme,
  Alert,
  Button,
  Tabs,
  Tab
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ArrowForward as ArrowForwardIcon,
  Info as InfoIcon,
  Code as CodeIcon,
  Refresh as RefreshIcon,
  Api as ApiIcon,
  Route as RouteIcon
} from '@mui/icons-material';
import routeDocumentation from '../../config/routeDocumentation';

/**
 * RouteDocumentationPage - A developer-focused page that displays all routes and redirects
 *
 * This page is intended for developers to understand the routing structure of the application.
 * It shows primary routes, their components, and any redirects to those routes.
 * Enhanced with API endpoint information and system status.
 */
const RouteDocumentationPage = () => {
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedRoute, setExpandedRoute] = useState(null);
  const [showOnlyRedirects, setShowOnlyRedirects] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [apiEndpoints, setApiEndpoints] = useState([]);
  const [systemInfo, setSystemInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch API endpoints information
  const fetchApiEndpoints = async () => {
    setLoading(true);
    setError(null);
    try {
      // Fetch from multiple API root endpoints to get comprehensive information
      const endpoints = [
        '/api/admin',
        '/api/usage',
        '/api/brand-guidelines',
        '/api/support',
        '/api/icp',
        '/api/competitors',
        '/api/billing'
      ];

      const responses = await Promise.allSettled(
        endpoints.map(endpoint =>
          fetch(endpoint, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          }).then(res => res.ok ? res.json() : null)
        )
      );

      const apiData = responses
        .map((result, index) => ({
          endpoint: endpoints[index],
          data: result.status === 'fulfilled' ? result.value : null,
          status: result.status
        }))
        .filter(item => item.data);

      setApiEndpoints(apiData);

      // Set system info from the first successful response
      if (apiData.length > 0) {
        setSystemInfo({
          totalEndpoints: apiData.reduce((sum, api) =>
            sum + (api.data.endpoints ? api.data.endpoints.length : 0), 0),
          activeApis: apiData.length,
          lastUpdated: new Date().toISOString()
        });
      }
    } catch (err) {
      setError('Failed to fetch API information');
      console.error('Error fetching API endpoints:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  // Toggle expanded route
  const toggleRouteExpansion = (routeKey) => {
    setExpandedRoute(expandedRoute === routeKey ? null : routeKey);
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Load API data on component mount
  useEffect(() => {
    fetchApiEndpoints();
  }, []);

  // Filter routes based on search term and filter settings
  const filteredRoutes = Object.entries(routeDocumentation)
    .filter(([, route]) => {
      const matchesSearch = 
        route.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
        route.component.toLowerCase().includes(searchTerm.toLowerCase()) ||
        route.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        route.redirects.some(redirect => 
          redirect.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
          redirect.reason.toLowerCase().includes(searchTerm.toLowerCase())
        );
      
      if (showOnlyRedirects) {
        return matchesSearch && route.redirects.length > 0;
      }
      
      return matchesSearch;
    });

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" gutterBottom>
            <CodeIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Developer Documentation
          </Typography>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchApiEndpoints}
            disabled={loading}
          >
            Refresh API Data
          </Button>
        </Box>

        <Typography variant="body1" color="textSecondary" paragraph>
          Comprehensive documentation for developers including frontend routes, API endpoints, and system information.
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {systemInfo && (
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <Chip
              icon={<RouteIcon />}
              label={`${Object.keys(routeDocumentation).length} Frontend Routes`}
              color="primary"
              variant="outlined"
            />
            <Chip
              icon={<ApiIcon />}
              label={`${systemInfo.activeApis} Active APIs`}
              color="secondary"
              variant="outlined"
            />
            <Chip
              label={`${systemInfo.totalEndpoints} Total Endpoints`}
              color="info"
              variant="outlined"
            />
          </Box>
        )}

        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab
            icon={<RouteIcon />}
            label="Frontend Routes"
            iconPosition="start"
          />
          <Tab
            icon={<ApiIcon />}
            label="API Endpoints"
            iconPosition="start"
          />
        </Tabs>
        
        {/* Search and filter */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <TextField
            variant="outlined"
            placeholder="Search routes..."
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ flexGrow: 1, mr: 2 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <IconButton 
            color={showOnlyRedirects ? "primary" : "default"}
            onClick={() => setShowOnlyRedirects(!showOnlyRedirects)}
            title={showOnlyRedirects ? "Show all routes" : "Show only routes with redirects"}
          >
            <FilterListIcon />
          </IconButton>
        </Box>
        
        {/* Routes list */}
        <List>
          {filteredRoutes.map(([key, route]) => (
            <React.Fragment key={key}>
              <Card 
                sx={{ 
                  mb: 2, 
                  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  '&:hover': {
                    borderColor: alpha(theme.palette.primary.main, 0.3),
                  }
                }}
              >
                <CardContent sx={{ p: 2 }}>
                  <Box 
                    sx={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      cursor: 'pointer'
                    }}
                    onClick={() => toggleRouteExpansion(key)}
                  >
                    <Box>
                      <Typography variant="h6" component="div">
                        {route.path}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Component: <code>{route.component}</code>
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {route.redirects.length > 0 && (
                        <Chip 
                          label={`${route.redirects.length} redirect${route.redirects.length > 1 ? 's' : ''}`}
                          color="primary"
                          size="small"
                          sx={{ mr: 1 }}
                        />
                      )}
                      {expandedRoute === key ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </Box>
                  </Box>
                  
                  <Collapse in={expandedRoute === key}>
                    <Box sx={{ mt: 2 }}>
                      <Divider sx={{ mb: 2 }} />
                      <Typography variant="body1" paragraph>
                        {route.description}
                      </Typography>
                      
                      {route.redirects.length > 0 ? (
                        <>
                          <Typography variant="subtitle1" gutterBottom>
                            Redirects to this route:
                          </Typography>
                          <TableContainer component={Paper} variant="outlined" sx={{ mb: 2 }}>
                            <Table size="small">
                              <TableHead>
                                <TableRow>
                                  <TableCell>Redirect Path</TableCell>
                                  <TableCell>Reason</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {route.redirects.map((redirect, index) => (
                                  <TableRow key={index}>
                                    <TableCell>
                                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <code>{redirect.path}</code>
                                        <ArrowForwardIcon sx={{ mx: 1, fontSize: 16, color: 'text.secondary' }} />
                                        <code>{route.path}</code>
                                      </Box>
                                    </TableCell>
                                    <TableCell>{redirect.reason}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </>
                      ) : (
                        <Box 
                          sx={{ 
                            p: 2, 
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            borderRadius: 1,
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          <InfoIcon sx={{ mr: 1, color: 'info.main' }} />
                          <Typography variant="body2">
                            No redirects to this route.
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Collapse>
                </CardContent>
              </Card>
            </React.Fragment>
          ))}
          
          {filteredRoutes.length === 0 && (
            <Box 
              sx={{ 
                p: 4, 
                textAlign: 'center',
                bgcolor: alpha(theme.palette.warning.main, 0.1),
                borderRadius: 1
              }}
            >
              <Typography variant="body1">
                No routes found matching your search criteria.
              </Typography>
            </Box>
          )}
        </List>
      </Paper>
    </Container>
  );
};

export default RouteDocumentationPage;

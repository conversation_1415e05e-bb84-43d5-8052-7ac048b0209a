/**
 * Enhanced Status Indicators - Enterprise-grade status indicator management component
 * Features: Comprehensive status indicator management, real-time status tracking, subscription-free access,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced status indicator capabilities and interactive status exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
  memo,
  forwardRef,
  useImperativeHandle
} from "react";
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  Tooltip,
  CircularProgress,
  LinearProgress,
  Collapse,
  Grow,
  Popper,
  ClickAwayListener,
  alpha,
  Badge,
  Avatar,
  Divider,
  Snackbar,
  Alert,
  useMediaQuery
} from "@mui/material";
import {
  CloudUpload as UploadIcon,
  CloudDownload as DownloadIcon,
  Edit as EditIcon,
  Assessment as AssessmentIcon,
  Close as CloseIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Pause as PauseIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  WifiOff as WifiOffIcon
} from "@mui/icons-material";
import { format, addMinutes } from "date-fns";
import { useAuth } from "../../contexts/AuthContext";
import useWebSocket from "../../hooks/useWebSocket";
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Environment detection
import { shouldUseFallbackMode } from '../../utils/environmentDetection';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Status indicator display modes with enhanced configurations
const STATUS_INDICATOR_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Status',
    description: 'Basic status indicator management interface',
    features: ['basic_status', 'analytics_status', 'ai_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Status',
    description: 'Comprehensive status indicator management',
    features: ['detailed_status', 'status_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Status',
    description: 'AI-powered status indicator management and optimization',
    features: ['ai_assisted', 'ai_optimization', 'status_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Status',
    description: 'Advanced status analytics and insights',
    features: ['analytics_status', 'status_insights']
  }
};

/**
 * Enhanced Status Indicators Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights (no restrictions)
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onStatusAction] - Status action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-status-indicators'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const StatusIndicators = memo(forwardRef(({
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onStatusAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-status-indicators',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useAccessibility();
  const { user } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const statusRef = useRef(null);
  const [processes, setProcesses] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [expandedProcess, setExpandedProcess] = useState(null);
  const processesRef = useRef({});

  // Enhanced state management
  const [statusMode, setStatusMode] = useState('compact');
  const [statusHistory, setStatusHistory] = useState([]);
  const [statusAnalytics, setStatusAnalytics] = useState(null);
  const [statusInsights, setStatusInsights] = useState(null);
  const [customStatusRules, setCustomStatusRules] = useState([]);
  const [statusPreferences, setStatusPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    statusSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [statusDrawerOpen, setStatusDrawerOpen] = useState(false);
  const [selectedStatusType, setSelectedStatusType] = useState(null);
  const [statusStats, setStatusStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastStatusCheck, setLastStatusCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with full feature access - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // All features are available to all users (no plan-based limitations)
    const features = {
      creator: {
        maxStatusTypes: -1,
        maxStatusPerDay: -1,
        hasAdvancedStatus: true,
        hasStatusAnalytics: true,
        hasCustomStatus: true,
        hasStatusInsights: true,
        hasStatusHistory: true,
        hasAIAssistance: true,
        hasStatusExport: true,
        hasStatusScheduling: true,
        hasStatusAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxStatusTypes: -1,
        maxStatusPerDay: -1,
        hasAdvancedStatus: true,
        hasStatusAnalytics: true,
        hasCustomStatus: true,
        hasStatusInsights: true,
        hasStatusHistory: true,
        hasAIAssistance: true,
        hasStatusExport: true,
        hasStatusScheduling: true,
        hasStatusAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxStatusTypes: -1,
        maxStatusPerDay: -1,
        hasAdvancedStatus: true,
        hasStatusAnalytics: true,
        hasCustomStatus: true,
        hasStatusInsights: true,
        hasStatusHistory: true,
        hasAIAssistance: true,
        hasStatusExport: true,
        hasStatusScheduling: true,
        hasStatusAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxStatusTypes === -1 || currentUsage < currentFeatures.maxStatusTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Status indicators with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Status indicator interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive status API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getStatusHistory: () => statusHistory,
    getStatusAnalytics: () => statusAnalytics,
    getStatusInsights: () => statusInsights,
    refreshStatus: () => {
      fetchStatusAnalytics();
      if (onRefresh) onRefresh();
    },

    // Status methods
    focusStatus: () => {
      if (statusRef.current) {
        statusRef.current.focus();
      }
    },
    getProcesses: () => processes,
    getExpandedProcess: () => expandedProcess,
    openStatusDrawer: () => setStatusDrawerOpen(true),
    closeStatusDrawer: () => setStatusDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportStatusData: () => {
      if (onExport) {
        onExport(statusHistory, statusAnalytics);
      }
    },

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),
    focusStatusField: () => setFocusToElement('status-indicators-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getCurrentStep: () => activeStep,
    goToStep: (step) => setActiveStep(step),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => statusMode,
    getStatusStats: () => statusStats,
    getSelectedStatusType: () => selectedStatusType,
    getCustomStatusRules: () => customStatusRules,
    addCustomStatusRule,
    handleStatusModeChange,
    updateStatusPreferences,
    handleStatusTypeSelection,
    validateStatusRule,
    handlePauseProcess,
    handleResumeProcess,
    handleCancelProcess,
    handleRemoveProcess,
    getStatusDrawerOpen: () => statusDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    triggerStatusAction: (action, data) => {
      if (onStatusAction) {
        onStatusAction(action, data);
      }
    }
  }), [
    statusHistory,
    statusAnalytics,
    statusInsights,
    statusStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    activeStep,
    validationErrors,
    statusMode,
    processes,
    expandedProcess,
    selectedStatusType,
    customStatusRules,
    statusDrawerOpen,
    showAnalytics,
    fullscreenMode,
    onStatusAction,
    addCustomStatusRule,
    fetchStatusAnalytics,
    handleCancelProcess,
    handlePauseProcess,
    handleResumeProcess,
    handleStatusModeChange,
    handleStatusTypeSelection,
    updateStatusPreferences,
    validateStatusRule
  ]);

  // Fetch status analytics with enhanced error handling and retry logic
  const fetchStatusAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/status/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setStatusAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (statusPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Status analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch status analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load status analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, statusPreferences.showAnalytics]);

  // Handle status mode switching
  const handleStatusModeChange = useCallback((newMode) => {
    if (STATUS_INDICATOR_MODES[newMode.toUpperCase()]) {
      setStatusMode(newMode);
      announceToScreenReader(`Status mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setStatusHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (statusPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} status mode`);
      }
    }
  }, [announceToScreenReader, user?.id, statusPreferences.showAnalytics, showSuccess]);

  // Handle custom status rule management
  const addCustomStatusRule = useCallback((ruleData) => {
    const newRule = {
      id: Date.now(),
      ...ruleData,
      createdAt: new Date().toISOString(),
      userId: user?.id
    };

    setCustomStatusRules(prev => [...prev, newRule]);

    // Track rule creation
    const ruleRecord = {
      id: Date.now(),
      type: 'custom_rule_created',
      rule: newRule.name,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setStatusHistory(prev => [ruleRecord, ...prev.slice(0, 99)]);

    if (statusPreferences.showAnalytics) {
      showSuccess(`Custom status rule "${ruleData.name}" created`);
    }
  }, [user?.id, statusPreferences.showAnalytics, showSuccess]);

  // Handle status preferences updates
  const updateStatusPreferences = useCallback((newPreferences) => {
    setStatusPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setStatusHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (statusPreferences.showAnalytics) {
      showSuccess('Status preferences updated');
    }
  }, [user?.id, statusPreferences.showAnalytics, showSuccess]);

  // Handle status type selection
  const handleStatusTypeSelection = useCallback((statusType) => {
    setSelectedStatusType(statusType);

    // Track status type selection
    const typeRecord = {
      id: Date.now(),
      type: 'status_type_selected',
      statusType,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setStatusHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (statusPreferences.showAnalytics) {
      announceToScreenReader(`Selected status type: ${statusType}`);
    }
  }, [user?.id, statusPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateStatusRule = useCallback((ruleData) => {
    const errors = {};

    if (!ruleData.name?.trim()) {
      errors.name = 'Rule name is required';
    }
    if (!ruleData.trigger?.trim()) {
      errors.trigger = 'Rule trigger is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Define WebSocket message handlers
  const messageHandlers = {
    // Handle upload progress updates
    upload_progress: useCallback((message) => {
      const progressData = message.data;

      if (!progressData || !progressData.upload_id) return;

      // Convert backend data format to our process format
      const process = {
        id: progressData.upload_id,
        type: "upload",
        title: "Uploading file",
        description: progressData.filename || "File upload in progress",
        progress: progressData.progress_percent || 0,
        status: progressData.status === "completed" ? "complete" :
                progressData.status === "failed" ? "error" :
                progressData.status === "pending" ? "paused" : "active",
        startTime: new Date(progressData.started_at),
        estimatedEndTime: calculateEstimatedEndTime(
          progressData.started_at,
          progressData.updated_at,
          progressData.progress_percent
        ),
        details: {
          totalSize: progressData.total_size,
          uploadedSize: progressData.uploaded_size,
          contentType: progressData.content_type,
          speed: calculateUploadSpeed(
            progressData.uploaded_size,
            new Date(progressData.started_at),
            new Date(progressData.updated_at)
          ),
        },
        error: progressData.error,
      };

      updateProcess(process);
    }, [updateProcess]),

    // Handle report generation progress
    report_progress: useCallback((message) => {
      const progressData = message.data;

      if (!progressData || !progressData.report_id) return;

      const process = {
        id: progressData.report_id,
        type: "analytics",
        title: "Generating report",
        description: progressData.report_name || "Analytics report",
        progress: progressData.progress_percent || 0,
        status: progressData.status === "completed" ? "complete" :
                progressData.status === "failed" ? "error" :
                progressData.status === "pending" ? "paused" : "active",
        startTime: new Date(progressData.started_at),
        estimatedEndTime: calculateEstimatedEndTime(
          progressData.started_at,
          progressData.updated_at,
          progressData.progress_percent
        ),
        details: {
          totalSteps: progressData.total_steps || 1,
          currentStep: progressData.current_step || 1,
          currentStepName: progressData.current_step_name || "Processing data",
        },
        error: progressData.error,
      };

      updateProcess(process);
    }, [updateProcess]),

    // Handle content publishing progress
    publishing_progress: useCallback((message) => {
      const progressData = message.data;

      if (!progressData || !progressData.publish_id) return;

      const process = {
        id: progressData.publish_id,
        type: "publishing",
        title: "Publishing content",
        description: progressData.content_title || "Content publishing in progress",
        progress: progressData.progress_percent || 0,
        status: progressData.status === "completed" ? "complete" :
                progressData.status === "failed" ? "error" :
                progressData.status === "pending" ? "paused" : "active",
        startTime: new Date(progressData.started_at),
        estimatedEndTime: calculateEstimatedEndTime(
          progressData.started_at,
          progressData.updated_at,
          progressData.progress_percent
        ),
        details: {
          platform: progressData.platform,
          contentType: progressData.content_type,
          scheduledTime: progressData.scheduled_time,
        },
        error: progressData.error,
      };

      updateProcess(process);
    }, [updateProcess]),
  };

  // Connect to WebSocket for real-time updates (only if not in fallback mode)
  const [useFallback, setUseFallback] = useState(true); // Default to fallback mode

  useEffect(() => {
    try {
      const fallbackMode = shouldUseFallbackMode();
      setUseFallback(fallbackMode);
    } catch (error) {
      console.warn('Error checking fallback mode, using fallback:', error);
      setUseFallback(true);
    }
  }, []);

  const { connected, sendCommand } = useWebSocket({
    endpoint: '/api/ws',
    authenticate: true,
    autoConnect: !useFallback,
    channels: user ? [`user:${user.id}`] : [],
    handlers: messageHandlers,
    disabled: useFallback,
  });

  // Fetch status insights
  const fetchStatusInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/status/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setStatusInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch status insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Initial data loading
  useEffect(() => {
    fetchStatusAnalytics();
    fetchStatusInsights();
  }, [fetchStatusAnalytics, fetchStatusInsights]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && processes) {
      // Optimize status management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchStatusAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, processes, fetchStatusAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastStatusCheck(Date.now());

          if (wasUnavailable && statusPreferences.showAnalytics) {
            showSuccess("Connection restored - Status features available");
          }
        } else {
          setBackendAvailable(false);
          if (statusPreferences.showAnalytics) {
            showError("Backend service unavailable - Some status features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastStatusCheck;
          if (timeSinceLastCheck > 60000 && statusPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Status indicators may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastStatusCheck, statusPreferences.showAnalytics, showSuccess, showError]);

  // Generate AI suggestions when data changes
  useEffect(() => {
    if (processes && enableAIInsights && statusPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [processes, enableAIInsights, statusPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/status/ai-suggestions', {
        params: {
          context: processes?.length || 0
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (statusPreferences.showAnalytics) {
        showSuccess('AI suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [processes, statusPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when processes change
  useEffect(() => {
    if (processes && enableAdvancedFeatures) {
      fetchStatusStats();
    }
  }, [processes, enableAdvancedFeatures, fetchStatusStats]);

  // Fetch status stats function
  const fetchStatusStats = useCallback(async () => {
    try {
      const response = await api.get('/api/status/stats');
      setStatusStats(response.data);
    } catch (error) {
      console.error('Failed to fetch status stats:', error);
    }
  }, []);

  // Helper function to calculate estimated end time
  const calculateEstimatedEndTime = (startTimeStr, updateTimeStr, progressPercent) => {
    if (!startTimeStr || !updateTimeStr || progressPercent <= 0) {
      return addMinutes(new Date(), 5); // Default to 5 minutes from now
    }

    const startTime = new Date(startTimeStr);
    const updateTime = new Date(updateTimeStr);
    const elapsedMs = updateTime - startTime;

    if (elapsedMs <= 0 || progressPercent >= 100) {
      return new Date();
    }

    // Calculate remaining time based on progress so far
    const totalEstimatedMs = (elapsedMs / progressPercent) * 100;
    const remainingMs = totalEstimatedMs - elapsedMs;

    return new Date(Date.now() + remainingMs);
  };

  // Helper function to calculate upload speed
  const calculateUploadSpeed = (uploadedBytes, startTime, currentTime) => {
    const elapsedSeconds = Math.max(1, (currentTime - startTime) / 1000);
    const bytesPerSecond = uploadedBytes / elapsedSeconds;

    if (bytesPerSecond < 1024) {
      return `${bytesPerSecond.toFixed(1)} B/s`;
    } else if (bytesPerSecond < 1024 * 1024) {
      return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
    } else {
      return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
    }
  };

  // Update a process in the state
  const updateProcess = useCallback((process) => {
    if (!process || !process.id) return;

    // Store in ref to avoid race conditions
    processesRef.current[process.id] = process;

    // Update state with all current processes
    setProcesses(Object.values(processesRef.current));

    // Remove completed processes after a delay
    if (process.status === "complete" || process.status === "error") {
      setTimeout(() => {
        setProcesses(prevProcesses =>
          prevProcesses.filter(p => p.id !== process.id)
        );

        // Also remove from ref
        delete processesRef.current[process.id];
      }, 5000); // Keep completed processes visible for 5 seconds
    }
  }, []);

  // Handle opening process details
  const handleOpenDetails = (event, processId) => {
    setAnchorEl(event.currentTarget);
    setExpandedProcess(processId);
  };

  // Handle closing process details
  const handleCloseDetails = useCallback(() => {
    setAnchorEl(null);
    setExpandedProcess(null);
  }, []);

  // Handle pausing a process
  const handlePauseProcess = useCallback(async (processId) => {
    const process = processesRef.current[processId];
    if (!process) return;

    try {
      // Send command to pause the process based on type
      if (process.type === "upload") {
        await sendCommand("pause_upload", { upload_id: processId });
      } else if (process.type === "analytics") {
        await sendCommand("pause_report", { report_id: processId });
      } else if (process.type === "publishing") {
        await sendCommand("pause_publishing", { publish_id: processId });
      }

      // Optimistically update UI
      setProcesses((prevProcesses) =>
        prevProcesses.map((p) =>
          p.id === processId ? { ...p, status: "paused" } : p
        )
      );

      // Update in ref
      if (processesRef.current[processId]) {
        processesRef.current[processId].status = "paused";
      }
    } catch (error) {
      console.error(`Error pausing process ${processId}:`, error);
    }
  }, [sendCommand]);

  // Handle resuming a process
  const handleResumeProcess = useCallback(async (processId) => {
    const process = processesRef.current[processId];
    if (!process) return;

    try {
      // Send command to resume the process based on type
      if (process.type === "upload") {
        await sendCommand("resume_upload", { upload_id: processId });
      } else if (process.type === "analytics") {
        await sendCommand("resume_report", { report_id: processId });
      } else if (process.type === "publishing") {
        await sendCommand("resume_publishing", { publish_id: processId });
      }

      // Optimistically update UI
      setProcesses((prevProcesses) =>
        prevProcesses.map((p) =>
          p.id === processId ? { ...p, status: "active" } : p
        )
      );

      // Update in ref
      if (processesRef.current[processId]) {
        processesRef.current[processId].status = "active";
      }
    } catch (error) {
      console.error(`Error resuming process ${processId}:`, error);
    }
  }, [sendCommand]);

  // Handle cancelling a process
  const handleCancelProcess = useCallback(async (processId) => {
    const process = processesRef.current[processId];
    if (!process) return;

    try {
      // Send command to cancel the process based on type
      if (process.type === "upload") {
        await sendCommand("cancel_upload", { upload_id: processId });
      } else if (process.type === "analytics") {
        await sendCommand("cancel_report", { report_id: processId });
      } else if (process.type === "publishing") {
        await sendCommand("cancel_publishing", { publish_id: processId });
      }

      // Remove from state
      setProcesses((prevProcesses) =>
        prevProcesses.filter((p) => p.id !== processId)
      );

      // Remove from ref
      delete processesRef.current[processId];

      handleCloseDetails();
    } catch (error) {
      console.error(`Error cancelling process ${processId}:`, error);
    }
  }, [sendCommand, handleCloseDetails]);

  // Handle removing a completed process
  const handleRemoveProcess = (processId) => {
    // Just remove from UI, no need to tell backend about completed processes
    setProcesses((prevProcesses) =>
      prevProcesses.filter((p) => p.id !== processId)
    );

    // Remove from ref
    delete processesRef.current[processId];
  };

  // Get process icon based on type
  const getProcessIcon = (type) => {
    switch (type) {
      case "upload":
        return <UploadIcon />;
      case "download":
        return <DownloadIcon />;
      case "analytics":
        return <AssessmentIcon />;
      case "edit":
        return <EditIcon />;
      default:
        return <InfoIcon />;
    }
  };

  // Get process status icon
  const getStatusIcon = (status, progress) => {
    if (status === "complete") {
      return <CheckCircleIcon color="success" />;
    } else if (status === "error") {
      return <ErrorIcon color="error" />;
    } else if (status === "paused") {
      return <PauseIcon color="warning" />;
    } else if (progress < 100) {
      return (
        <CircularProgress
          size={16}
          variant="determinate"
          value={progress}
          sx={{ color: ACE_COLORS.PURPLE }}
        />
      );
    } else {
      return <CheckCircleIcon color="success" />;
    }
  };

  // Format estimated time remaining
  const formatTimeRemaining = (process) => {
    if (process.status === "complete") {
      return "Complete";
    } else if (process.status === "paused") {
      return "Paused";
    } else if (process.status === "error") {
      return "Error";
    }

    const now = new Date();
    const endTime = new Date(process.estimatedEndTime);

    // Calculate time difference in seconds
    const diffSeconds = Math.max(0, Math.floor((endTime - now) / 1000));

    if (diffSeconds < 60) {
      return `${diffSeconds} sec remaining`;
    } else {
      const minutes = Math.floor(diffSeconds / 60);
      const seconds = diffSeconds % 60;
      return `${minutes}:${seconds.toString().padStart(2, "0")} remaining`;
    }
  };

  // If no active processes, return null
  if (processes.length === 0) {
    return null;
  }

  return (
    <Box
      {...getAccessibilityProps()}
      ref={statusRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      {/* Status indicator button */}
      <Box
        sx={{
          position: "fixed",
          bottom: 16,
          right: 16,
          zIndex: 1200,
          pointerEvents: "auto",
          width: "auto",
          height: "auto",
          maxWidth: "100%",
          maxHeight: "100%",
          overflow: "visible",
          boxSizing: "border-box",
          margin: 0,
          padding: 0,
        }}
      >
        <Badge
          badgeContent={processes.length}
          color="primary"
          overlap="circular"
          anchorOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
        >
          <Tooltip title="Active processes">
            <IconButton
              color="primary"
              size="large"
              onClick={(e) => handleOpenDetails(e, processes[0].id)}
              sx={{
                bgcolor: ACE_COLORS.WHITE,
                boxShadow: `0 4px 6px ${alpha(ACE_COLORS.DARK, 0.1)}`,
                "&:hover": {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                },
              }}
            >
              {processes.some((p) => p.status === "error") ? (
                <ErrorIcon color="error" />
              ) : processes.some((p) => p.status === "paused") ? (
                <PauseIcon color="warning" />
              ) : (
                <RefreshIcon />
              )}
            </IconButton>
          </Tooltip>
        </Badge>
      </Box>

      {/* Process details popper */}
      <Popper
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        placement="top-end"
        transition
        disablePortal
        modifiers={[
          {
            name: "preventOverflow",
            enabled: true,
            options: {
              altAxis: true,
              altBoundary: true,
              boundary: "viewport",
              padding: 8,
            },
          },
        ]}
        sx={{
          zIndex: 1300,
          position: "absolute",
          bottom: "auto",
          right: "auto",
          left: "auto",
          top: "auto",
        }}
      >
        {({ TransitionProps }) => (
          <Grow {...TransitionProps} timeout={350}>
            <Paper
              elevation={3}
              sx={{
                width: isMobile ? 280 : 320,
                maxHeight: isMobile ? 350 : 400,
                overflow: "auto",
                borderRadius: 2,
              }}
            >
              <ClickAwayListener onClickAway={handleCloseDetails}>
                <Box>
                  <Box
                    sx={{
                      p: 2,
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
                      Active Processes
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={handleCloseDetails}
                      aria-label="Close dialog"
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Box>

                  <Divider />

                  {processes.map((process) => (
                    <Box
                      key={process.id}
                      sx={{
                        p: 2,
                        borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                        "&:last-child": {
                          borderBottom: "none",
                        },
                      }}
                    >
                      <Box
                        sx={{ display: "flex", alignItems: "center", mb: 1 }}
                      >
                        <Avatar
                          sx={{
                            width: 32,
                            height: 32,
                            bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                            color: ACE_COLORS.PURPLE,
                            mr: 1.5,
                          }}
                        >
                          {getProcessIcon(process.type)}
                        </Avatar>

                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="subtitle2" noWrap>
                            {process.title}
                          </Typography>
                          <Typography
                            variant="caption"
                            color="textSecondary"
                            noWrap
                          >
                            {process.description}
                          </Typography>
                        </Box>

                        <Box sx={{ ml: 1 }}>
                          {getStatusIcon(process.status, process.progress)}
                        </Box>

                        <IconButton
                          size="small"
                          onClick={() => handleRemoveProcess(process.id)}
                          aria-label="Close process"
                          sx={{ ml: 0.5 }}
                        >
                          <CloseIcon fontSize="small" />
                        </IconButton>
                      </Box>

                      <Box sx={{ mb: 1 }}>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <Typography variant="caption" color="textSecondary">
                            {Math.round(process.progress)}%
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {formatTimeRemaining(process)}
                          </Typography>
                        </Box>

                        <LinearProgress
                          variant="determinate"
                          value={process.progress}
                          sx={{
                            height: 4,
                            borderRadius: 2,
                            bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                            "& .MuiLinearProgress-bar": {
                              borderRadius: 2,
                            },
                          }}
                        />
                      </Box>

                      {/* Process details */}
                      <Collapse in={expandedProcess === process.id}>
                        <Box
                          sx={{
                            mt: 1,
                            p: 1.5,
                            bgcolor: alpha(ACE_COLORS.WHITE, 0.5),
                            borderRadius: 1,
                            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                          }}
                        >
                          <Typography
                            variant="caption"
                            color="textSecondary"
                            paragraph
                            sx={{ mb: 1 }}
                          >
                            Started:{" "}
                            {format(new Date(process.startTime), "h:mm a")}
                          </Typography>

                          {process.type === "upload" && (
                            <>
                              <Typography variant="caption" display="block">
                                Uploading: {process.details.currentItem}
                              </Typography>
                              <Typography variant="caption" display="block">
                                {process.details.completedItems} of{" "}
                                {process.details.totalItems} complete
                              </Typography>
                              <Typography variant="caption" display="block">
                                Speed: {process.details.speed}
                              </Typography>
                            </>
                          )}

                          {process.type === "analytics" && (
                            <>
                              <Typography variant="caption" display="block">
                                Step {process.details.currentStep} of{" "}
                                {process.details.totalSteps}
                              </Typography>
                              <Typography variant="caption" display="block">
                                Current: {process.details.currentStepName}
                              </Typography>
                            </>
                          )}
                        </Box>
                      </Collapse>

                      {/* Process actions */}
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "flex-end",
                          mt: 1,
                        }}
                      >
                        {process.status === "complete" ? (
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveProcess(process.id)}
                            color="primary"
                          >
                            <CheckCircleIcon fontSize="small" />
                          </IconButton>
                        ) : process.status === "active" ? (
                          <>
                            <IconButton
                              size="small"
                              onClick={() => handlePauseProcess(process.id)}
                              color="default"
                            >
                              <PauseIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleCancelProcess(process.id)}
                              color="error"
                              sx={{ ml: 1 }}
                            >
                              <StopIcon fontSize="small" />
                            </IconButton>
                          </>
                        ) : process.status === "paused" ? (
                          <>
                            <IconButton
                              size="small"
                              onClick={() => handleResumeProcess(process.id)}
                              color="primary"
                            >
                              <PlayArrowIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleCancelProcess(process.id)}
                              color="error"
                              sx={{ ml: 1 }}
                            >
                              <StopIcon fontSize="small" />
                            </IconButton>
                          </>
                        ) : (
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveProcess(process.id)}
                            color="error"
                          >
                            <CloseIcon fontSize="small" />
                          </IconButton>
                        )}

                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (expandedProcess === process.id) {
                              setExpandedProcess(null);
                            } else {
                              setExpandedProcess(process.id);
                            }
                          }}
                          sx={{ ml: 1 }}
                        >
                          {expandedProcess === process.id ? (
                            <ExpandLessIcon fontSize="small" />
                          ) : (
                            <ExpandMoreIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying status sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}

      {/* Connection Status Indicator */}
      {(!backendAvailable || !connected) && (
        <Box sx={{
          position: 'fixed',
          top: 60,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            <WifiOffIcon sx={{ fontSize: 14, mr: 0.5 }} />
            {!connected ? 'WebSocket disconnected' : 'Connection issues detected'}
          </Typography>
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
StatusIndicators.propTypes = {
  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onStatusAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

StatusIndicators.displayName = 'StatusIndicators';

export default StatusIndicators;

// @since 2024-1-1 to 2025-25-7
import { useState, useMemo, useCallback, useRef, useEffect, memo, forwardRef, useImperativeHandle } from "react";
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Chip,
  Tooltip,
  CircularProgress,
  useTheme,
  useMediaQuery,
  alpha,
  IconButton,
  ToggleButton,
  ToggleButtonGroup,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Alert,
  AlertTitle,
  Fade,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Switch,
  FormControlLabel,
  Divider
} from "@mui/material";
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  ShowChart as ShowChartIcon,
  BarChart as BarChartIcon,
  DateRange as DateRangeIcon,
  Assessment as AssessmentIcon,
  Insights as InsightsIcon,
  Compare as CompareIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Upgrade as UpgradeIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  RemoveRedEye as ViewsIcon,
  TouchApp as EngagementIcon,
  MonetizationOn as RevenueIcon,
  Public as ReachIcon,
  Psychology as SentimentIcon,
  AutoGraph as ConversionIcon
} from "@mui/icons-material";

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import DashboardCard from "./DashboardCard";
import PerformanceChart from "./PerformanceChart";
import ErrorBoundary from "../common/ErrorBoundary";
import FeatureGate from "../common/FeatureGate";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Performance metric types with enhanced configurations
const PERFORMANCE_METRIC_TYPES = {
  ENGAGEMENT: {
    id: 'engagement',
    name: 'Engagement Rate',
    icon: EngagementIcon,
    color: ACE_COLORS.PURPLE,
    unit: '%',
    format: 'percentage',
    description: 'Average engagement rate across all content',
    benchmarks: { low: 1, medium: 3, high: 6 },
    subscriptionLimits: {
      creator: { dataPoints: 30, realTime: false, comparison: false },
      accelerator: { dataPoints: 90, realTime: true, comparison: true },
      dominator: { dataPoints: -1, realTime: true, comparison: true, customKPIs: true }
    }
  },
  REACH: {
    id: 'reach',
    name: 'Total Reach',
    icon: ReachIcon,
    color: ACE_COLORS.YELLOW,
    unit: '',
    format: 'number',
    description: 'Total number of unique users reached',
    benchmarks: { low: 1000, medium: 10000, high: 100000 },
    subscriptionLimits: {
      creator: { dataPoints: 30, realTime: false, comparison: false },
      accelerator: { dataPoints: 90, realTime: true, comparison: true },
      dominator: { dataPoints: -1, realTime: true, comparison: true, customKPIs: true }
    }
  },
  IMPRESSIONS: {
    id: 'impressions',
    name: 'Impressions',
    icon: ViewsIcon,
    color: ACE_COLORS.DARK,
    unit: '',
    format: 'number',
    description: 'Total number of times content was displayed',
    benchmarks: { low: 5000, medium: 50000, high: 500000 },
    subscriptionLimits: {
      creator: { dataPoints: 30, realTime: false, comparison: false },
      accelerator: { dataPoints: 90, realTime: true, comparison: true },
      dominator: { dataPoints: -1, realTime: true, comparison: true, customKPIs: true }
    }
  },
  CLICKS: {
    id: 'clicks',
    name: 'Click-through Rate',
    icon: EngagementIcon,
    color: ACE_COLORS.PURPLE,
    unit: '%',
    format: 'percentage',
    description: 'Percentage of impressions that resulted in clicks',
    benchmarks: { low: 0.5, medium: 2, high: 5 },
    subscriptionLimits: {
      creator: { dataPoints: 30, realTime: false, comparison: false },
      accelerator: { dataPoints: 90, realTime: true, comparison: true },
      dominator: { dataPoints: -1, realTime: true, comparison: true, customKPIs: true }
    }
  },
  CONVERSIONS: {
    id: 'conversions',
    name: 'Conversion Rate',
    icon: ConversionIcon,
    color: ACE_COLORS.YELLOW,
    unit: '%',
    format: 'percentage',
    description: 'Percentage of clicks that resulted in conversions',
    benchmarks: { low: 1, medium: 3, high: 8 },
    subscriptionLimits: {
      creator: { dataPoints: 30, realTime: false, comparison: false },
      accelerator: { dataPoints: 90, realTime: true, comparison: true },
      dominator: { dataPoints: -1, realTime: true, comparison: true, customKPIs: true }
    }
  },
  REVENUE: {
    id: 'revenue',
    name: 'Revenue',
    icon: RevenueIcon,
    color: ACE_COLORS.DARK,
    unit: '$',
    format: 'currency',
    description: 'Total revenue generated from social media activities',
    benchmarks: { low: 100, medium: 1000, high: 10000 },
    subscriptionLimits: {
      creator: { dataPoints: 30, realTime: false, comparison: false },
      accelerator: { dataPoints: 90, realTime: true, comparison: true },
      dominator: { dataPoints: -1, realTime: true, comparison: true, customKPIs: true }
    }
  },
  GROWTH: {
    id: 'growth',
    name: 'Follower Growth',
    icon: TrendingUpIcon,
    color: ACE_COLORS.PURPLE,
    unit: '%',
    format: 'percentage',
    description: 'Percentage growth in followers over time period',
    benchmarks: { low: 1, medium: 5, high: 15 },
    subscriptionLimits: {
      creator: { dataPoints: 30, realTime: false, comparison: false },
      accelerator: { dataPoints: 90, realTime: true, comparison: true },
      dominator: { dataPoints: -1, realTime: true, comparison: true, customKPIs: true }
    }
  },
  SENTIMENT: {
    id: 'sentiment',
    name: 'Sentiment Score',
    icon: SentimentIcon,
    color: ACE_COLORS.YELLOW,
    unit: '/10',
    format: 'score',
    description: 'Average sentiment score of audience interactions',
    benchmarks: { low: 5, medium: 7, high: 9 },
    subscriptionLimits: {
      creator: { dataPoints: 30, realTime: false, comparison: false },
      accelerator: { dataPoints: 90, realTime: true, comparison: true },
      dominator: { dataPoints: -1, realTime: true, comparison: true, customKPIs: true }
    }
  }
};

// Time range options
const TIME_RANGES = {
  '7d': { label: '7 Days', days: 7 },
  '30d': { label: '30 Days', days: 30 },
  '90d': { label: '90 Days', days: 90 },
  '6m': { label: '6 Months', days: 180 },
  '1y': { label: '1 Year', days: 365 },
  'custom': { label: 'Custom', days: null }
};

// Animation configurations
const ANIMATION_CONFIG = {
  DURATION: 300,
  EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  STAGGER_DELAY: 50
};

/**
 * Enhanced ModernPerformanceCard Component - Enterprise-grade performance analytics dashboard
 * Features: Plan-based performance analytics limitations, real-time performance tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced performance insights and interactive analytics exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.title - Performance metric title
 * @param {number} props.value - Current performance value
 * @param {number} [props.previousValue] - Previous period value for comparison
 * @param {string} [props.unit=''] - Unit of measurement
 * @param {React.Component} [props.icon] - Icon component
 * @param {string} [props.color] - Custom color for the metric
 * @param {boolean} [props.loading=false] - Loading state
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {string} [props.description] - Metric description
 * @param {Array} [props.historicalData=null] - Historical data for charts
 * @param {boolean} [props.showChart=false] - Whether to show chart toggle
 * @param {number} [props.minHeight] - Minimum card height
 * @param {string} [props.metricType='engagement'] - Type of performance metric
 * @param {string} [props.variant='detailed'] - Card display variant
 * @param {boolean} [props.enableComparison=false] - Enable comparison features
 * @param {boolean} [props.enableInsights=false] - Enable AI insights
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {Function} [props.onMetricSelect] - Metric selection callback
 * @param {Function} [props.onComparisonToggle] - Comparison toggle callback
 * @param {Function} [props.onInsightsRequest] - Insights request callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Object} [props.customization] - Custom styling options
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Array} [props.benchmarkData] - Benchmark comparison data
 * @param {Object} [props.goalTargets] - Performance goal targets
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */
const ModernPerformanceCard = memo(forwardRef(({
  title,
  value,
  previousValue,
  unit = "",
  icon,
  color,
  loading = false,
  onRefresh,
  description,
  historicalData = null,
  showChart = false,
  minHeight,
  metricType = 'engagement',
  enableComparison = false,
  enableInsights = false,
  enableExport = false,
  onComparisonToggle,
  onInsightsRequest,
  onExport,
  realTimeUpdates = false,
  benchmarkData = null,
  goalTargets = null,
  className = '',
  style = {},
  testId = 'modern-performance-card',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, isScreenReaderActive, setFocusToElement } = useAccessibility();

  // Refs for enhanced functionality
  const cardRef = useRef(null);

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading,
    refreshing: false,
    expanded: false,
    showUpgradeDialog: false,
    showSettingsMenu: false,
    showExportMenu: false,
    showInsightsPanel: false,
    showComparisonPanel: false,
    lastUpdated: null,
    animationKey: 0,
    errors: {},
    notifications: [],
    // Performance state
    currentMetricType: metricType,
    selectedTimeRange: '30d',
    viewMode: 'summary',
    showBenchmarks: false,
    showGoals: false,
    filterPlatform: 'all',
    sortBy: 'value',
    hoveredDataPoint: null,
    selectedDataPoints: [],
    comparisonMetrics: [],
    insightsData: null,
    realTimeData: null,
    customDateRange: {
      startDate: '',
      endDate: ''
    }
  });

  // Performance analytics data state
  const [performanceData, setPerformanceData] = useState({
    current: null,
    historical: historicalData || [],
    benchmarks: benchmarkData || [],
    goals: goalTargets || {},
    insights: null,
    comparisons: [],
    realTime: null
  });

  // Menu anchor states
  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [dateRangeDialogOpen, setDateRangeDialogOpen] = useState(false);

  /**
   * Enhanced plan-based performance analytics validation - Production Ready
   */
  const validatePerformanceAnalytics = useCallback(() => {
    if (!subscription) {
      return {
        canViewAnalytics: false,
        hasAnalyticsAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { dataPoints: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based performance analytics limits
    const planLimits = {
      creator: {
        dataPoints: 30,
        features: ['basic_performance_metrics'],
        chartTypes: ['line'],
        maxMetrics: 3,
        realTime: false,
        comparison: false,
        insights: false,
        export: false,
        customKPIs: false,
        benchmarks: false
      },
      accelerator: {
        dataPoints: 90,
        features: ['basic_performance_metrics', 'advanced_analytics', 'real_time_tracking'],
        chartTypes: ['line', 'bar', 'area'],
        maxMetrics: 8,
        realTime: true,
        comparison: true,
        insights: true,
        export: true,
        customKPIs: false,
        benchmarks: true
      },
      dominator: {
        dataPoints: -1,
        features: ['basic_performance_metrics', 'advanced_analytics', 'real_time_tracking', 'custom_kpis', 'ai_insights'],
        chartTypes: ['line', 'bar', 'area', 'scatter', 'heatmap'],
        maxMetrics: -1,
        realTime: true,
        comparison: true,
        insights: true,
        export: true,
        customKPIs: true,
        benchmarks: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = performanceData.historical?.length || 0;
    const limit = currentPlanLimits.dataPoints;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewAnalytics: true,
      hasAnalyticsAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, performanceData.historical]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const analyticsLimits = validatePerformanceAnalytics();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasRealTimeUpdates: analyticsLimits.planLimits.realTime,
      hasComparison: analyticsLimits.planLimits.comparison,
      hasInsights: analyticsLimits.planLimits.insights,
      hasExport: analyticsLimits.planLimits.export,
      hasCustomKPIs: analyticsLimits.planLimits.customKPIs,
      hasBenchmarks: analyticsLimits.planLimits.benchmarks,
      maxMetrics: analyticsLimits.planLimits.maxMetrics,
      maxDataPoints: analyticsLimits.planLimits.dataPoints,
      availableChartTypes: analyticsLimits.planLimits.chartTypes,
      refreshInterval: analyticsLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validatePerformanceAnalytics]);

  /**
   * Enhanced metric configuration - Production Ready
   */
  const getMetricConfig = useCallback(() => {
    const config = PERFORMANCE_METRIC_TYPES[metricType.toUpperCase()] || PERFORMANCE_METRIC_TYPES.ENGAGEMENT;
    const planId = subscription?.plan_id || 'creator';
    const subscriptionLimits = config.subscriptionLimits[planId] || config.subscriptionLimits.creator;

    return {
      ...config,
      subscriptionLimits,
      isAvailable: subscriptionFeatures.maxMetrics === -1 || subscriptionFeatures.maxMetrics > 0
    };
  }, [metricType, subscription?.plan_id, subscriptionFeatures.maxMetrics]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportData: handleExport,
    toggleComparison: handleComparisonToggle,
    requestInsights: handleInsightsRequest,
    getPerformanceData: () => performanceData,
    getAnalyticsLimits: validatePerformanceAnalytics,
    focus: () => setFocusToElement(cardRef.current),
    announce: (message) => announceToScreenReader(message)
  }), [
    performanceData,
    validatePerformanceAnalytics,
    handleRefresh,
    handleExport,
    handleComparisonToggle,
    handleInsightsRequest,
    setFocusToElement,
    announceToScreenReader
  ]);

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery('(max-width:576px)');
  const isTablet = useMediaQuery('(min-width:577px) and (max-width:992px)');
  const isLaptop = useMediaQuery('(min-width:993px) and (max-width:1366px)');
  const isTouchDevice = useMediaQuery('(hover: none)');
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Memoized data calculations
  const analyticsLimits = useMemo(() => validatePerformanceAnalytics(), [validatePerformanceAnalytics]);
  const metricConfig = useMemo(() => getMetricConfig(), [getMetricConfig]);
  const currentData = useMemo(() => performanceData.historical || historicalData || [], [performanceData.historical, historicalData]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        lastUpdated: new Date().toISOString(),
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Performance data refreshed successfully');
      announceToScreenReader('Performance metrics have been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh performance data: ${error.message}`);
      announceToScreenReader('Failed to refresh performance data');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced view mode change handler - Production Ready
   */
  const handleViewModeChange = useCallback((event, newViewMode) => {
    if (newViewMode !== null && newViewMode !== state.viewMode) {
      setState(prev => ({ ...prev, viewMode: newViewMode }));
      announceToScreenReader(`Switched to ${newViewMode} view`);
    }
  }, [state.viewMode, announceToScreenReader]);

  /**
   * Enhanced time range change handler - Production Ready
   */
  const handleTimeRangeChange = useCallback((event, newTimeRange) => {
    if (newTimeRange !== null) {
      if (newTimeRange === "custom") {
        setDateRangeDialogOpen(true);
      } else {
        setState(prev => ({ ...prev, selectedTimeRange: newTimeRange }));
        announceToScreenReader(`Time range changed to ${TIME_RANGES[newTimeRange]?.label || newTimeRange}`);
      }
    }
  }, [announceToScreenReader]);

  /**
   * Enhanced custom date range handler - Production Ready
   */
  const handleCustomDateRangeSubmit = useCallback(() => {
    if (!state.customDateRange.startDate || !state.customDateRange.endDate) {
      showErrorNotification("Please select both start and end dates");
      return;
    }

    const startDate = new Date(state.customDateRange.startDate);
    const endDate = new Date(state.customDateRange.endDate);

    if (startDate >= endDate) {
      showErrorNotification("End date must be after start date");
      return;
    }

    setState(prev => ({ ...prev, selectedTimeRange: 'custom' }));
    setDateRangeDialogOpen(false);
    showSuccessNotification("Custom date range applied");
    announceToScreenReader("Custom date range has been applied");
  }, [state.customDateRange, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced comparison toggle handler - Production Ready
   */
  const handleComparisonToggle = useCallback(() => {
    if (!subscriptionFeatures.hasComparison) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, showComparisonPanel: !prev.showComparisonPanel }));

    if (onComparisonToggle) {
      onComparisonToggle(!state.showComparisonPanel);
    }

    announceToScreenReader(`Comparison panel ${!state.showComparisonPanel ? 'opened' : 'closed'}`);
  }, [subscriptionFeatures.hasComparison, state.showComparisonPanel, onComparisonToggle, announceToScreenReader]);

  /**
   * Enhanced insights request handler - Production Ready
   */
  const handleInsightsRequest = useCallback(async () => {
    if (!subscriptionFeatures.hasInsights) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, showInsightsPanel: true }));

    try {
      if (onInsightsRequest) {
        const insights = await onInsightsRequest(metricType, currentData);
        setPerformanceData(prev => ({ ...prev, insights }));
      }

      announceToScreenReader('Performance insights have been generated');
    } catch (error) {
      showErrorNotification(`Failed to generate insights: ${error.message}`);
      announceToScreenReader('Failed to generate performance insights');
    }
  }, [subscriptionFeatures.hasInsights, metricType, currentData, onInsightsRequest, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'csv') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, currentData, metricConfig);
      }

      showSuccessNotification(`Performance data exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Performance data has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export data: ${error.message}`);
      announceToScreenReader('Failed to export performance data');
    }
  }, [subscriptionFeatures.hasExport, currentData, metricConfig, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced data processing - Production Ready
   */
  const processedData = useMemo(() => {
    if (!currentData || !Array.isArray(currentData) || currentData.length === 0) return [];

    let processed = [...currentData];

    // Apply time range filter
    if (state.selectedTimeRange !== 'custom') {
      const timeRange = TIME_RANGES[state.selectedTimeRange];
      if (timeRange && timeRange.days) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - timeRange.days);

        processed = processed.filter(item => {
          const itemDate = new Date(item.date);
          return itemDate >= cutoffDate;
        });
      }
    } else if (state.customDateRange.startDate && state.customDateRange.endDate) {
      const startDate = new Date(state.customDateRange.startDate);
      const endDate = new Date(state.customDateRange.endDate);

      processed = processed.filter(item => {
        const itemDate = new Date(item.date);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // Sort by date
    processed.sort((a, b) => new Date(a.date) - new Date(b.date));

    // Apply subscription limits
    if (analyticsLimits.planLimits.dataPoints > 0) {
      processed = processed.slice(-analyticsLimits.planLimits.dataPoints);
    }

    return processed;
  }, [currentData, state.selectedTimeRange, state.customDateRange, analyticsLimits.planLimits.dataPoints]);

  /**
   * Enhanced percentage change calculation - Production Ready
   */
  const calculateChange = useCallback(() => {
    if (!previousValue || previousValue === 0) return 0;
    return ((value - previousValue) / previousValue) * 100;
  }, [value, previousValue]);

  const percentChange = useMemo(() => calculateChange(), [calculateChange]);

  /**
   * Enhanced trend analysis - Production Ready
   */
  const getTrendIcon = useCallback(() => {
    const iconSize = isMobile ? "small" : "small";

    if (percentChange > 0) {
      return <TrendingUpIcon fontSize={iconSize} />;
    } else if (percentChange < 0) {
      return <TrendingDownIcon fontSize={iconSize} />;
    } else {
      return <TrendingFlatIcon fontSize={iconSize} />;
    }
  }, [percentChange, isMobile]);

  const getTrendColor = useCallback(() => {
    if (percentChange > 0) {
      return theme.palette.success.main;
    } else if (percentChange < 0) {
      return theme.palette.error.main;
    } else {
      return theme.palette.grey[500];
    }
  }, [percentChange, theme.palette]);

  /**
   * Enhanced value formatting - Production Ready
   */
  const formatValue = useCallback((val, format = metricConfig.format) => {
    if (val === null || val === undefined) return 'N/A';

    switch (format) {
      case 'percentage':
        return `${val.toFixed(1)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(val);
      case 'score':
        return `${val.toFixed(1)}/10`;
      case 'number':
      default:
        if (val >= 1000000) {
          return `${(val / 1000000).toFixed(1)}M`;
        } else if (val >= 1000) {
          return `${(val / 1000).toFixed(1)}K`;
        } else {
          return val.toLocaleString();
        }
    }
  }, [metricConfig.format]);

  /**
   * Enhanced benchmark comparison - Production Ready
   */
  const getBenchmarkStatus = useCallback(() => {
    if (!metricConfig.benchmarks || value === null || value === undefined) {
      return { status: 'unknown', color: theme.palette.grey[500], label: 'No benchmark' };
    }

    const { low, medium, high } = metricConfig.benchmarks;

    if (value >= high) {
      return { status: 'excellent', color: theme.palette.success.main, label: 'Excellent' };
    } else if (value >= medium) {
      return { status: 'good', color: theme.palette.warning.main, label: 'Good' };
    } else if (value >= low) {
      return { status: 'fair', color: theme.palette.info.main, label: 'Fair' };
    } else {
      return { status: 'poor', color: theme.palette.error.main, label: 'Needs Improvement' };
    }
  }, [metricConfig.benchmarks, value, theme.palette]);

  /**
   * Enhanced tooltip content - Production Ready
   */
  const getTooltipContent = useCallback(() => {
    if (previousValue === undefined) return undefined;

    const benchmarkStatus = getBenchmarkStatus();

    return (
      <Box sx={{ p: 0.5 }}>
        <Typography variant="body2" fontWeight="bold" sx={{ mb: 0.5 }}>
          {title}
        </Typography>
        <Typography variant="body2" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
          Current: {formatValue(value)}{unit}
        </Typography>
        <Typography variant="body2" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
          Previous: {formatValue(previousValue)}{unit}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            fontSize: { xs: '0.75rem', sm: '0.875rem' },
            color: getTrendColor(),
            fontWeight: 'bold',
            mt: 0.5
          }}
        >
          Change: {percentChange > 0 ? '+' : ''}{percentChange.toFixed(2)}%
        </Typography>
        {subscriptionFeatures.hasBenchmarks && (
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              color: benchmarkStatus.color,
              fontWeight: 'bold',
              mt: 0.5
            }}
          >
            Benchmark: {benchmarkStatus.label}
          </Typography>
        )}
      </Box>
    );
  }, [title, value, unit, previousValue, formatValue, percentChange, getTrendColor, subscriptionFeatures.hasBenchmarks, getBenchmarkStatus]);

  /**
   * Enhanced responsive text sizing - Production Ready
   */
  const getValueTextSize = useCallback(() => {
    const formattedValue = formatValue(value);
    const valueLength = formattedValue.length + unit.length;

    if (isMobile) {
      if (valueLength > 8) return '1.5rem';
      return '1.8rem';
    }

    if (isTablet) {
      if (valueLength > 8) return '1.8rem';
      return '2.2rem';
    }

    if (isLaptop) {
      if (valueLength > 8) return '2.2rem';
      return '2.5rem';
    }

    // Desktop
    if (valueLength > 8) return '2.5rem';
    return '2.8rem';
  }, [value, unit, formatValue, isMobile, isTablet, isLaptop]);

  /**
   * Enhanced accessibility and keyboard navigation - Production Ready
   */
  const handleKeyDown = useCallback((event) => {
    if (!isScreenReaderActive) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        if (event.target.getAttribute('role') === 'button') {
          event.preventDefault();
          event.target.click();
        }
        break;
      case 'Escape':
        if (state.showUpgradeDialog) {
          setState(prev => ({ ...prev, showUpgradeDialog: false }));
        }
        if (state.showSettingsMenu) {
          setSettingsAnchorEl(null);
          setState(prev => ({ ...prev, showSettingsMenu: false }));
        }
        if (state.showExportMenu) {
          setExportAnchorEl(null);
          setState(prev => ({ ...prev, showExportMenu: false }));
        }
        break;
      case 'ArrowRight':
      case 'ArrowLeft':
        if (state.viewMode === 'chart' && processedData.length > 0) {
          event.preventDefault();
          // Navigate through data points
          const direction = event.key === 'ArrowRight' ? 1 : -1;
          const currentIndex = state.selectedDataPoints.length > 0 ?
            processedData.findIndex(item => item.date === state.selectedDataPoints[0].date) : -1;
          const newIndex = Math.max(0, Math.min(processedData.length - 1, currentIndex + direction));

          setState(prev => ({
            ...prev,
            selectedDataPoints: [processedData[newIndex]]
          }));

          announceToScreenReader(
            `Data point ${newIndex + 1} of ${processedData.length}: ${formatValue(processedData[newIndex].value)} on ${new Date(processedData[newIndex].date).toLocaleDateString()}`
          );
        }
        break;
    }
  }, [isScreenReaderActive, state.showUpgradeDialog, state.showSettingsMenu, state.showExportMenu, state.viewMode, state.selectedDataPoints, processedData, announceToScreenReader, formatValue]);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleSettingsMenuOpen = useCallback((event) => {
    setSettingsAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showSettingsMenu: true }));
  }, []);

  const handleSettingsMenuClose = useCallback(() => {
    setSettingsAnchorEl(null);
    setState(prev => ({ ...prev, showSettingsMenu: false }));
  }, []);

  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleDateRangeDialogClose = useCallback(() => {
    setDateRangeDialogOpen(false);
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTimeUpdates) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTimeUpdates, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced error boundary integration - Production Ready
   */
  useEffect(() => {
    if (Object.keys(state.errors).length > 0) {
      const errorMessages = Object.values(state.errors).join(', ');
      announceToScreenReader(`Performance card errors: ${errorMessages}`);
    }
  }, [state.errors, announceToScreenReader]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `${title} performance metrics`,
      'aria-description': ariaDescription || `Performance analytics for ${title} with current value ${formatValue(value)}${unit}`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0,
      onKeyDown: handleKeyDown
    };
  }, [ariaLabel, ariaDescription, title, value, unit, formatValue, realTimeUpdates, handleKeyDown]);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced performance analytics features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Real-time performance tracking',
        'Advanced comparison tools',
        'AI-powered insights',
        'Data export capabilities',
        'Custom KPI tracking',
        'Benchmark comparisons'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  // Main render condition checks
  if (state.loading && !value) {
    return (
      <DashboardCard
        title={title}
        description={description}
        icon={icon}
        minHeight={minHeight || (isMobile ? 150 : 180)}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          minHeight: { xs: 120, sm: 150 }
        }}>
          <CircularProgress
            size={isMobile ? 32 : 40}
            sx={{ color: ACE_COLORS.PURPLE }}
            aria-label="Loading performance data"
          />
        </Box>
      </DashboardCard>
    );
  }

  // Error state
  if (Object.keys(state.errors).length > 0) {
    return (
      <DashboardCard
        title={title}
        description={description}
        icon={icon}
        minHeight={minHeight || (isMobile ? 150 : 180)}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        <Box sx={{ p: 2 }}>
          <Alert
            severity="error"
            sx={{
              backgroundColor: alpha(theme.palette.error.main, 0.1),
              border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
            }}
          >
            <AlertTitle>Performance Data Error</AlertTitle>
            {Object.values(state.errors)[0]}
          </Alert>
        </Box>
      </DashboardCard>
    );
  }

  // Feature gate for advanced analytics
  if (!analyticsLimits.canViewAnalytics) {
    return (
      <FeatureGate
        feature="performance_analytics"
        fallback={
          <DashboardCard
            title={title}
            description="Performance analytics not available"
            icon={icon}
            minHeight={minHeight || (isMobile ? 150 : 180)}
            className={className}
            style={style}
            data-testid={testId}
          >
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Performance analytics are not available with your current subscription.
              </Typography>
            </Box>
          </DashboardCard>
        }
      >
        {/* This will be rendered if feature access is granted */}
      </FeatureGate>
    );
  }

  // Main component render
  return (
    <ErrorBoundary
      fallback={
        <DashboardCard
          title={title}
          description="Error loading performance data"
          icon={ErrorIcon}
          minHeight={minHeight || (isMobile ? 150 : 180)}
        >
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load performance metrics
            </Typography>
          </Box>
        </DashboardCard>
      }
    >
      <Box
        ref={cardRef}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        sx={{
          position: 'relative',
          ...(!prefersReducedMotion && {
            transition: `all ${ANIMATION_CONFIG.DURATION}ms ${ANIMATION_CONFIG.EASING}`
          })
        }}
      >
        <DashboardCard
          title={title}
          description={description}
          icon={icon || metricConfig.icon}
          onRefresh={handleRefresh}
          tooltipContent={getTooltipContent()}
          minHeight={minHeight || (isMobile ? 150 : 180)}
          loading={state.refreshing}
          headerAction={
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              {/* Time Range Controls */}
              {showChart && processedData.length > 0 && state.viewMode === "chart" && (
                <ToggleButtonGroup
                  value={state.selectedTimeRange}
                  exclusive
                  onChange={handleTimeRangeChange}
                  aria-label="time range selection"
                  size="small"
                  sx={{
                    '& .MuiToggleButton-root': {
                      px: 1,
                      py: 0.5,
                      fontSize: '0.75rem',
                      minWidth: 'auto',
                      border: `1px solid ${theme.palette.divider}`,
                      color: theme.palette.text.secondary,
                      '&.Mui-selected': {
                        backgroundColor: ACE_COLORS.PURPLE,
                        color: ACE_COLORS.WHITE,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                        },
                      },
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                      },
                    },
                  }}
                >
                  <ToggleButton value="7d" aria-label="7 days">
                    7d
                  </ToggleButton>
                  <ToggleButton value="30d" aria-label="30 days">
                    30d
                  </ToggleButton>
                  <ToggleButton value="90d" aria-label="90 days">
                    90d
                  </ToggleButton>
                  {subscriptionFeatures.hasRealTimeUpdates && (
                    <>
                      <ToggleButton value="6m" aria-label="6 months">
                        6m
                      </ToggleButton>
                      <ToggleButton value="1y" aria-label="1 year">
                        1y
                      </ToggleButton>
                    </>
                  )}
                  <ToggleButton value="custom" aria-label="custom range">
                    <DateRangeIcon fontSize="small" />
                  </ToggleButton>
                </ToggleButtonGroup>
              )}

              {/* View Mode Toggle */}
              {showChart && processedData.length > 0 && (
                <Tooltip title={state.viewMode === "summary" ? "Show Chart" : "Show Summary"}>
                  <IconButton
                    size="small"
                    onClick={() => handleViewModeChange(null, state.viewMode === "summary" ? "chart" : "summary")}
                    sx={{
                      color: theme.palette.text.secondary,
                      backgroundColor: state.viewMode === "chart" ? alpha(ACE_COLORS.PURPLE, 0.1) : 'transparent',
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2),
                      },
                    }}
                    aria-label={state.viewMode === "summary" ? "Switch to chart view" : "Switch to summary view"}
                  >
                    {state.viewMode === "summary" ? <ShowChartIcon fontSize="small" /> : <BarChartIcon fontSize="small" />}
                  </IconButton>
                </Tooltip>
              )}

              {/* Comparison Toggle */}
              {enableComparison && (
                <Tooltip title="Toggle Comparison">
                  <IconButton
                    size="small"
                    onClick={handleComparisonToggle}
                    sx={{
                      color: theme.palette.text.secondary,
                      backgroundColor: state.showComparisonPanel ? alpha(ACE_COLORS.YELLOW, 0.1) : 'transparent',
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2),
                      },
                    }}
                    aria-label="Toggle comparison panel"
                  >
                    <CompareIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}

              {/* Insights Toggle */}
              {enableInsights && (
                <Tooltip title="AI Insights">
                  <IconButton
                    size="small"
                    onClick={handleInsightsRequest}
                    sx={{
                      color: theme.palette.text.secondary,
                      backgroundColor: state.showInsightsPanel ? alpha(ACE_COLORS.PURPLE, 0.1) : 'transparent',
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2),
                      },
                    }}
                    aria-label="Request AI insights"
                  >
                    <InsightsIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}

              {/* Settings Menu */}
              <Tooltip title="Settings">
                <IconButton
                  size="small"
                  onClick={handleSettingsMenuOpen}
                  sx={{
                    color: theme.palette.text.secondary,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    },
                  }}
                  aria-label="Open settings menu"
                >
                  <SettingsIcon fontSize="small" />
                </IconButton>
              </Tooltip>

              {/* Export Menu */}
              {enableExport && (
                <Tooltip title="Export Data">
                  <IconButton
                    size="small"
                    onClick={handleExportMenuOpen}
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      },
                    }}
                    aria-label="Export performance data"
                  >
                    <DownloadIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          }
        >
          {/* Main Content Area */}
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            pt: { xs: 1, sm: 2 },
            px: { xs: 1, sm: 2 },
            position: 'relative'
          }}>
            {state.loading ? (
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                minHeight: { xs: 80, sm: 100 },
                gap: 2
              }}>
                <CircularProgress
                  size={isMobile ? 24 : 32}
                  sx={{ color: ACE_COLORS.PURPLE }}
                  aria-label="Loading performance data"
                />
                <Typography variant="caption" color="text.secondary" sx={{ textAlign: 'center' }}>
                  Loading performance metrics...
                </Typography>
              </Box>
            ) : state.viewMode === "chart" && processedData.length > 0 ? (
              <Fade in={!state.loading} timeout={ANIMATION_CONFIG.DURATION}>
                <Box sx={{
                  width: '100%',
                  height: '100%',
                  minHeight: { xs: 200, sm: 250, md: 300 },
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{
                      textAlign: 'center',
                      mb: 1,
                      fontSize: { xs: '0.7rem', sm: '0.75rem' }
                    }}
                  >
                    {title} trend over {TIME_RANGES[state.selectedTimeRange]?.label || state.selectedTimeRange}
                  </Typography>
                  <Box sx={{ flex: 1, minHeight: 0 }}>
                    <PerformanceChart
                      data={processedData}
                      metricType={metricType}
                      color={color || metricConfig.color}
                      aria-label={`${title} performance chart`}
                    />
                  </Box>

                  {/* Chart Controls */}
                  {subscriptionFeatures.hasBenchmarks && (
                    <Box sx={{ mt: 1, display: 'flex', justifyContent: 'center', gap: 1 }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={state.showBenchmarks}
                            onChange={(e) => setState(prev => ({ ...prev, showBenchmarks: e.target.checked }))}
                            size="small"
                            sx={{
                              '& .MuiSwitch-switchBase.Mui-checked': {
                                color: ACE_COLORS.PURPLE,
                              },
                              '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                backgroundColor: ACE_COLORS.PURPLE,
                              },
                            }}
                          />
                        }
                        label={
                          <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                            Show Benchmarks
                          </Typography>
                        }
                      />
                      {subscriptionFeatures.hasCustomKPIs && (
                        <FormControlLabel
                          control={
                            <Switch
                              checked={state.showGoals}
                              onChange={(e) => setState(prev => ({ ...prev, showGoals: e.target.checked }))}
                              size="small"
                              sx={{
                                '& .MuiSwitch-switchBase.Mui-checked': {
                                  color: ACE_COLORS.YELLOW,
                                },
                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                  backgroundColor: ACE_COLORS.YELLOW,
                                },
                              }}
                            />
                          }
                          label={
                            <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                              Show Goals
                            </Typography>
                          }
                        />
                      )}
                    </Box>
                  )}
                </Box>
              </Fade>
            ) : (
              <Fade in={!state.loading} timeout={ANIMATION_CONFIG.DURATION}>
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%'
                }}>
                  {/* Main Value Display */}
                  <Typography
                    variant="h4"
                    component="div"
                    sx={{
                      fontWeight: 700,
                      color: color || metricConfig.color || ACE_COLORS.PURPLE,
                      textAlign: 'center',
                      mb: { xs: 1, sm: 2 },
                      fontSize: getValueTextSize(),
                      maxWidth: '100%',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      lineHeight: 1.2,
                      ...(!prefersReducedMotion && isTouchDevice && {
                        '&:active': {
                          transform: 'scale(0.98)',
                          transition: 'transform 0.1s'
                        }
                      })
                    }}
                    aria-label={`Current ${title}: ${formatValue(value)}${unit}`}
                  >
                    {formatValue(value)}
                    {unit}
                  </Typography>

                  {/* Trend Indicator */}
                  {previousValue !== undefined && (
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'center',
                      mb: { xs: 0.5, sm: 1 },
                      gap: 1
                    }}>
                      <Chip
                        icon={getTrendIcon()}
                        label={`${percentChange > 0 ? '+' : ''}${percentChange.toFixed(1)}%`}
                        size={isMobile ? "small" : "medium"}
                        sx={{
                          backgroundColor: alpha(getTrendColor(), 0.1),
                          color: getTrendColor(),
                          fontWeight: 600,
                          border: `1px solid ${alpha(getTrendColor(), 0.2)}`,
                          '& .MuiChip-icon': {
                            color: getTrendColor()
                          },
                          height: { xs: 24, sm: 32 },
                          '& .MuiChip-label': {
                            px: { xs: 1, sm: 1.5 },
                            fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.875rem' }
                          }
                        }}
                        aria-label={`Performance change: ${percentChange > 0 ? 'increased' : percentChange < 0 ? 'decreased' : 'unchanged'} by ${Math.abs(percentChange).toFixed(1)} percent`}
                      />

                      {/* Benchmark Status */}
                      {subscriptionFeatures.hasBenchmarks && (
                        <Chip
                          icon={<AssessmentIcon fontSize="small" />}
                          label={getBenchmarkStatus().label}
                          size={isMobile ? "small" : "medium"}
                          sx={{
                            backgroundColor: alpha(getBenchmarkStatus().color, 0.1),
                            color: getBenchmarkStatus().color,
                            fontWeight: 600,
                            border: `1px solid ${alpha(getBenchmarkStatus().color, 0.2)}`,
                            '& .MuiChip-icon': {
                              color: getBenchmarkStatus().color
                            },
                            height: { xs: 24, sm: 32 },
                            '& .MuiChip-label': {
                              px: { xs: 1, sm: 1.5 },
                              fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.875rem' }
                            }
                          }}
                          aria-label={`Benchmark status: ${getBenchmarkStatus().label}`}
                        />
                      )}
                    </Box>
                  )}

                  {/* Real-time Indicator */}
                  {realTimeUpdates && subscriptionFeatures.hasRealTimeUpdates && (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 0.5,
                      mt: 1
                    }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          backgroundColor: theme.palette.success.main,
                          ...(!prefersReducedMotion && {
                            animation: 'pulse 2s infinite',
                            '@keyframes pulse': {
                              '0%': { opacity: 1 },
                              '50%': { opacity: 0.5 },
                              '100%': { opacity: 1 }
                            }
                          })
                        }}
                        aria-hidden="true"
                      />
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{ fontSize: '0.7rem' }}
                      >
                        Live Data
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Fade>
            )}
          </Box>
        </DashboardCard>

        {/* Settings Menu */}
        <Menu
          anchorEl={settingsAnchorEl}
          open={state.showSettingsMenu}
          onClose={handleSettingsMenuClose}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 200,
              boxShadow: theme.shadows[8],
              border: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          <MenuItem onClick={() => {
            setState(prev => ({ ...prev, showBenchmarks: !prev.showBenchmarks }));
            handleSettingsMenuClose();
          }}>
            <ListItemIcon>
              {state.showBenchmarks ? <VisibilityIcon fontSize="small" /> : <VisibilityOffIcon fontSize="small" />}
            </ListItemIcon>
            <ListItemText>
              {state.showBenchmarks ? 'Hide' : 'Show'} Benchmarks
            </ListItemText>
          </MenuItem>

          {subscriptionFeatures.hasCustomKPIs && (
            <MenuItem onClick={() => {
              setState(prev => ({ ...prev, showGoals: !prev.showGoals }));
              handleSettingsMenuClose();
            }}>
              <ListItemIcon>
                {state.showGoals ? <StarIcon fontSize="small" /> : <StarBorderIcon fontSize="small" />}
              </ListItemIcon>
              <ListItemText>
                {state.showGoals ? 'Hide' : 'Show'} Goals
              </ListItemText>
            </MenuItem>
          )}

          <Divider />

          <MenuItem onClick={() => {
            handleRefresh();
            handleSettingsMenuClose();
          }}>
            <ListItemIcon>
              <RefreshIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Refresh Data</ListItemText>
          </MenuItem>
        </Menu>

        {/* Export Menu */}
        <Menu
          anchorEl={exportAnchorEl}
          open={state.showExportMenu}
          onClose={handleExportMenuClose}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 150,
              boxShadow: theme.shadows[8],
              border: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          <MenuItem onClick={() => {
            handleExport('csv');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <DownloadIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as CSV</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => {
            handleExport('json');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <DownloadIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as JSON</ListItemText>
          </MenuItem>

          {subscriptionFeatures.hasInsights && (
            <MenuItem onClick={() => {
              handleExport('pdf');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <DownloadIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export Report (PDF)</ListItemText>
            </MenuItem>
          )}
        </Menu>
      </Box>

      {/* Custom Date Range Dialog */}
      <Dialog
        open={dateRangeDialogOpen}
        onClose={handleDateRangeDialogClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: theme.shadows[16]
          }
        }}
      >
        <DialogTitle sx={{
          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
          borderBottom: `1px solid ${theme.palette.divider}`
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DateRangeIcon sx={{ color: ACE_COLORS.PURPLE }} />
            <Typography variant="h6" component="span">
              Custom Date Range
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <TextField
              label="Start Date"
              type="date"
              value={state.customDateRange.startDate}
              onChange={(e) => setState(prev => ({
                ...prev,
                customDateRange: { ...prev.customDateRange, startDate: e.target.value }
              }))}
              InputLabelProps={{
                shrink: true,
              }}
              fullWidth
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: ACE_COLORS.PURPLE,
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: ACE_COLORS.PURPLE,
                },
              }}
            />
            <TextField
              label="End Date"
              type="date"
              value={state.customDateRange.endDate}
              onChange={(e) => setState(prev => ({
                ...prev,
                customDateRange: { ...prev.customDateRange, endDate: e.target.value }
              }))}
              InputLabelProps={{
                shrink: true,
              }}
              fullWidth
              sx={{
                '& .MuiOutlinedInput-root': {
                  '&.Mui-focused fieldset': {
                    borderColor: ACE_COLORS.PURPLE,
                  },
                },
                '& .MuiInputLabel-root.Mui-focused': {
                  color: ACE_COLORS.PURPLE,
                },
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button
            onClick={handleDateRangeDialogClose}
            sx={{ color: theme.palette.text.secondary }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleCustomDateRangeSubmit}
            variant="contained"
            disabled={!state.customDateRange.startDate || !state.customDateRange.endDate}
            sx={{
              backgroundColor: ACE_COLORS.PURPLE,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
              },
              '&:disabled': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.3),
              }
            }}
          >
            Apply Range
          </Button>
        </DialogActions>
      </Dialog>

      {/* Upgrade Dialog */}
      <Dialog
        open={state.showUpgradeDialog}
        onClose={handleUpgradeDialogClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: theme.shadows[16]
          }
        }}
      >
        <DialogTitle sx={{
          backgroundColor: alpha(ACE_COLORS.YELLOW, 0.05),
          borderBottom: `1px solid ${theme.palette.divider}`
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <UpgradeIcon sx={{ color: ACE_COLORS.YELLOW }} />
            <Typography variant="h6" component="span">
              {getUpgradeDialogContent().title}
            </Typography>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" sx={{ mb: 2 }}>
              {getUpgradeDialogContent().content}
            </Typography>

            <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
              Unlock Advanced Performance Features:
            </Typography>

            <Grid container spacing={1}>
              {getUpgradeDialogContent().features.map((feature, index) => (
                <Grid item xs={12} sm={6} key={index}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                    <Typography variant="body2">{feature}</Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Box>

          <Alert
            severity="info"
            sx={{
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
            }}
          >
            <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
            Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced performance analytics features.
          </Alert>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button
            onClick={handleUpgradeDialogClose}
            sx={{ color: theme.palette.text.secondary }}
          >
            Maybe Later
          </Button>
          <Button
            variant="contained"
            sx={{
              backgroundColor: ACE_COLORS.YELLOW,
              color: ACE_COLORS.DARK,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.YELLOW, 0.8),
              }
            }}
            startIcon={<UpgradeIcon />}
          >
            Upgrade Now
          </Button>
        </DialogActions>
      </Dialog>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
ModernPerformanceCard.propTypes = {
  // Core props
  title: PropTypes.string.isRequired,
  value: PropTypes.number.isRequired,
  previousValue: PropTypes.number,
  unit: PropTypes.string,
  icon: PropTypes.elementType,
  color: PropTypes.string,
  loading: PropTypes.bool,
  onRefresh: PropTypes.func,
  description: PropTypes.string,
  historicalData: PropTypes.arrayOf(
    PropTypes.shape({
      date: PropTypes.string.isRequired,
      value: PropTypes.number.isRequired,
      impressions: PropTypes.number,
      engagements: PropTypes.number,
      clicks: PropTypes.number,
    })
  ),
  showChart: PropTypes.bool,
  minHeight: PropTypes.number,

  // Enhanced props
  metricType: PropTypes.oneOf([
    'engagement', 'reach', 'impressions', 'clicks',
    'conversions', 'revenue', 'growth', 'sentiment'
  ]),
  variant: PropTypes.oneOf(['compact', 'detailed', 'analytics-focused', 'comparison']),
  enableComparison: PropTypes.bool,
  enableInsights: PropTypes.bool,
  enableExport: PropTypes.bool,
  onMetricSelect: PropTypes.func,
  onComparisonToggle: PropTypes.func,
  onInsightsRequest: PropTypes.func,
  onExport: PropTypes.func,
  customization: PropTypes.object,
  realTimeUpdates: PropTypes.bool,
  benchmarkData: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      value: PropTypes.number.isRequired,
      type: PropTypes.string
    })
  ),
  goalTargets: PropTypes.object,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

ModernPerformanceCard.defaultProps = {
  unit: '',
  loading: false,
  showChart: false,
  metricType: 'engagement',
  variant: 'detailed',
  enableComparison: false,
  enableInsights: false,
  enableExport: false,
  customization: {},
  realTimeUpdates: false,
  benchmarkData: null,
  goalTargets: null,
  className: '',
  style: {},
  testId: 'modern-performance-card'
};

// Display name for debugging
ModernPerformanceCard.displayName = 'ModernPerformanceCard';

export default ModernPerformanceCard;

/**
 * Comprehensive validation utilities for user management forms.
 * Provides real-time validation with security checks and accessibility support.
 */
import React from 'react';

// Email validation with security checks
export const validateEmail = (email) => {
  const result = {
    isValid: false,
    errors: [],
    warnings: [],
    suggestions: [],
  };

  if (!email) {
    result.errors.push('Email is required');
    return result;
  }

  // Basic format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    result.errors.push('Invalid email format');
    result.suggestions.push('Please enter a valid email address (e.g., <EMAIL>)');
    return result;
  }

  // Security checks
  const securityPatterns = [
    { pattern: /[<>'"]/g, message: 'Email contains suspicious characters' },
    { pattern: /javascript:/gi, message: 'Email contains potentially malicious content' },
    { pattern: /data:/gi, message: 'Email contains data URL which is not allowed' },
  ];

  for (const { pattern, message } of securityPatterns) {
    if (pattern.test(email)) {
      result.errors.push(message);
      return result;
    }
  }

  // Check for multiple @ symbols
  if ((email.match(/@/g) || []).length !== 1) {
    result.errors.push('Email must contain exactly one @ symbol');
    return result;
  }

  // Domain validation
  const domain = email.split('@')[1];
  if (domain) {
    // Check for IP addresses (often suspicious)
    const ipRegex = /^\d+\.\d+\.\d+\.\d+$/;
    if (ipRegex.test(domain)) {
      result.warnings.push('IP address domains are not recommended');
    }

    // Check for suspicious domain patterns
    if (/[<>'"]/g.test(domain)) {
      result.errors.push('Domain contains invalid characters');
      return result;
    }
  }

  // Length validation
  if (email.length > 254) {
    result.errors.push('Email address is too long (maximum 254 characters)');
    return result;
  }

  result.isValid = true;
  return result;
};

// Password strength validation
export const validatePassword = (password) => {
  const result = {
    isValid: false,
    strength: 'Very Weak',
    score: 0,
    errors: [],
    suggestions: [],
    requirements: {
      length: false,
      lowercase: false,
      uppercase: false,
      numbers: false,
      special: false,
    },
  };

  if (!password) {
    result.errors.push('Password is required');
    result.suggestions.push('Please enter a password');
    return result;
  }

  let score = 0;

  // Length requirement
  if (password.length >= 8) {
    result.requirements.length = true;
    score += 1;
  } else {
    result.errors.push('Password must be at least 8 characters long');
    result.suggestions.push('Add more characters to reach minimum length');
  }

  // Lowercase requirement
  if (/[a-z]/.test(password)) {
    result.requirements.lowercase = true;
    score += 1;
  } else {
    result.errors.push('Password must contain at least one lowercase letter');
    result.suggestions.push('Add lowercase letters (a-z)');
  }

  // Uppercase requirement
  if (/[A-Z]/.test(password)) {
    result.requirements.uppercase = true;
    score += 1;
  } else {
    result.errors.push('Password must contain at least one uppercase letter');
    result.suggestions.push('Add uppercase letters (A-Z)');
  }

  // Numbers requirement
  if (/\d/.test(password)) {
    result.requirements.numbers = true;
    score += 1;
  } else {
    result.errors.push('Password must contain at least one number');
    result.suggestions.push('Add numbers (0-9)');
  }

  // Special characters requirement
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    result.requirements.special = true;
    score += 1;
  } else {
    result.errors.push('Password must contain at least one special character');
    result.suggestions.push('Add special characters (!@#$%^&*(),.?":{}|<>)');
  }

  // Bonus points for longer passwords
  if (password.length >= 12) {
    score += 1;
  }

  // Check for common patterns (reduce score)
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /admin/i,
    /letmein/i,
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      result.errors.push('Password contains common patterns');
      result.suggestions.push('Avoid common words and patterns');
      score = Math.max(0, score - 2);
      break;
    }
  }

  // Check for repeated characters
  if (/(.)\1{2,}/.test(password)) {
    result.errors.push('Password contains too many repeated characters');
    result.suggestions.push('Avoid repeating the same character multiple times');
    score = Math.max(0, score - 1);
  }

  // Determine strength
  result.score = Math.min(score, 5);
  if (score <= 1) {
    result.strength = 'Very Weak';
  } else if (score <= 2) {
    result.strength = 'Weak';
  } else if (score <= 3) {
    result.strength = 'Fair';
  } else if (score <= 4) {
    result.strength = 'Good';
  } else {
    result.strength = 'Strong';
  }

  result.isValid = result.errors.length === 0;
  return result;
};

// Phone number validation
export const validatePhone = (phone) => {
  const result = {
    isValid: false,
    errors: [],
    formatted: '',
  };

  if (!phone) {
    result.isValid = true; // Phone is optional
    return result;
  }

  // Remove all non-digit characters except + and spaces
  const cleaned = phone.replace(/[^\d+\s()-]/g, '');
  
  // Basic phone number patterns
  const patterns = [
    /^\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/, // US format
    /^\+?[1-9]\d{1,14}$/, // International E.164 format
    /^\d{10}$/, // 10 digits
    /^\d{11}$/, // 11 digits
  ];

  const isValid = patterns.some(pattern => pattern.test(cleaned));

  if (!isValid) {
    result.errors.push('Invalid phone number format');
  } else {
    result.isValid = true;
    result.formatted = cleaned;
  }

  return result;
};

// Name validation
export const validateName = (name, fieldName = 'Name') => {
  const result = {
    isValid: false,
    errors: [],
    warnings: [],
  };

  if (!name || name.trim().length === 0) {
    result.errors.push(`${fieldName} is required`);
    return result;
  }

  const trimmedName = name.trim();

  // Length validation
  if (trimmedName.length < 1) {
    result.errors.push(`${fieldName} cannot be empty`);
    return result;
  }

  if (trimmedName.length > 255) {
    result.errors.push(`${fieldName} is too long (maximum 255 characters)`);
    return result;
  }

  // Security checks
  const securityPatterns = [
    { pattern: /<script[^>]*>.*?<\/script>/gi, message: `${fieldName} contains script tags` },
    { pattern: /javascript:/gi, message: `${fieldName} contains JavaScript` },
    { pattern: /<[^>]+>/g, message: `${fieldName} contains HTML tags` },
  ];

  for (const { pattern, message } of securityPatterns) {
    if (pattern.test(trimmedName)) {
      result.errors.push(message);
      return result;
    }
  }

  // Check for suspicious characters
  if (/[<>'"&]/.test(trimmedName)) {
    result.warnings.push(`${fieldName} contains special characters that may cause issues`);
  }

  result.isValid = true;
  return result;
};

// File upload validation
export const validateFileUpload = (file, options = {}) => {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
  } = options;

  const result = {
    isValid: false,
    errors: [],
    warnings: [],
  };

  if (!file) {
    result.errors.push('No file selected');
    return result;
  }

  // File size validation
  if (file.size > maxSize) {
    result.errors.push(`File size exceeds maximum of ${(maxSize / 1024 / 1024).toFixed(1)}MB`);
    return result;
  }

  // File type validation
  if (!allowedTypes.includes(file.type)) {
    result.errors.push(`File type '${file.type}' is not allowed`);
    return result;
  }

  // File extension validation
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (!extension || !allowedExtensions.includes(extension)) {
    result.errors.push(`File extension '${extension}' is not allowed`);
    return result;
  }

  // Security checks for filename
  const suspiciousPatterns = [
    /\.exe$/i,
    /\.bat$/i,
    /\.cmd$/i,
    /\.scr$/i,
    /\.vbs$/i,
    /\.js$/i,
    /\.jar$/i,
    /\.php$/i,
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(file.name)) {
      result.errors.push('Suspicious file type detected');
      return result;
    }
  }

  // Check for path traversal in filename
  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
    result.errors.push('Invalid characters in filename');
    return result;
  }

  result.isValid = true;
  return result;
};

// Real-time validation hook
export const useRealTimeValidation = (value, validator, debounceMs = 300) => {
  const [validation, setValidation] = React.useState({ isValid: true, errors: [], warnings: [] });
  const [isValidating, setIsValidating] = React.useState(false);

  React.useEffect(() => {
    if (!value) {
      setValidation({ isValid: true, errors: [], warnings: [] });
      return;
    }

    setIsValidating(true);
    const timeoutId = setTimeout(() => {
      const result = validator(value);
      setValidation(result);
      setIsValidating(false);
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [value, validator, debounceMs]);

  return { validation, isValidating };
};

// Form validation utility
export const validateForm = (formData, validationRules) => {
  const errors = {};
  let isValid = true;

  for (const [field, rules] of Object.entries(validationRules)) {
    const value = formData[field];
    const fieldErrors = [];

    for (const rule of rules) {
      const result = rule.validator(value);
      if (!result.isValid) {
        fieldErrors.push(...result.errors);
        isValid = false;
      }
    }

    if (fieldErrors.length > 0) {
      errors[field] = fieldErrors;
    }
  }

  return { isValid, errors };
};

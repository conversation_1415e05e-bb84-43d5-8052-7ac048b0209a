// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, useMemo, memo, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  useTheme,
  alpha,
  useMediaQuery,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  AlertTitle,
  Grid,
  Button,
  Skeleton,
  Fade,
  Chip,
  CircularProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Download as ExportIcon,
  Refresh as RefreshIcon,

  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,

} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import { GlassmorphicCard } from '../common';
import { CustomCardContent } from '../common';
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};



/**
 * Enhanced StatsCard Component - Enterprise-grade stats card
 * Features: Plan-based stats limitations, real-time stats monitoring, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced stats insights and interactive stats exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.title - The title of the statistic
 * @param {number|string} props.value - The value of the statistic
 * @param {React.ReactNode} props.icon - The icon to display
 * @param {string} props.color - The color of the icon background
 * @param {string} [props.subtitle] - Optional subtitle text
 * @param {string} [props.trend] - Optional trend indicator (up, down, neutral)
 * @param {number} [props.trendValue] - Optional trend value
 * @param {string} [props.statsType='performance'] - Stats type
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {Object} [props.sx] - Additional styles to apply
 */

const StatsCard = memo(forwardRef(({
  title,
  value,
  icon,
  color,
  subtitle,
  trend,
  trendValue,
  statsType = 'performance',
  enableExport = false,
  realTimeUpdates = false,
  onExport,
  onRefresh,
  customization = {},
  className = '',
  style = {},
  testId = 'stats-card',
  ariaLabel,
  ariaDescription,
  sx = {}
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: subscriptionLoading,
    refreshing: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    animationKey: 0,
    errors: {},
    // Stats state
    optimizing: false
  });

  // Stats data state
  const [statsData, setStatsData] = useState({
    raw: { title, value, subtitle, trend, trendValue },
    processed: null,
    trends: null,
    insights: null
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);

  /**
   * Enhanced plan-based stats validation - Production Ready
   */
  const validateStatsFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canViewStats: false,
        hasStatsAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { dataPoints: 0, metrics: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based stats limits
    const planLimits = {
      creator: {
        dataPoints: 30,
        metrics: 3,
        features: ['basic_stats'],
        realTime: false,
        export: false,
        statsTypes: ['performance'],
        insights: false
      },
      accelerator: {
        dataPoints: 90,
        metrics: 8,
        features: ['basic_stats', 'advanced_stats', 'engagement_stats'],
        realTime: true,
        export: true,
        statsTypes: ['performance', 'engagement', 'growth', 'analytics'],
        insights: true
      },
      dominator: {
        dataPoints: -1,
        metrics: -1,
        features: ['basic_stats', 'advanced_stats', 'engagement_stats', 'ai_stats_insights'],
        realTime: true,
        export: true,
        statsTypes: ['performance', 'engagement', 'growth', 'analytics', 'comparison'],
        insights: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = statsData.processed ? 1 : 0;
    const limit = currentPlanLimits.dataPoints;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewStats: true,
      hasStatsAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, statsData.processed]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const statsLimits = validateStatsFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasExport: statsLimits.planLimits.export,
      hasRealTime: statsLimits.planLimits.realTime,
      hasInsights: statsLimits.planLimits.insights,
      maxDataPoints: statsLimits.planLimits.dataPoints,
      maxMetrics: statsLimits.planLimits.metrics,
      availableStatsTypes: statsLimits.planLimits.statsTypes,
      availableFeatures: statsLimits.planLimits.features,
      refreshInterval: statsLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateStatsFeatures]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `${title} stats card`,
      'aria-description': ariaDescription || `Interactive ${statsType} stats card displaying ${title} metrics`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, title, statsType, realTimeUpdates]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Stats refreshed successfully');
      announceToScreenReader('Stats have been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh stats: ${error.message}`);
      announceToScreenReader('Failed to refresh stats');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'csv') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, statsData.processed);
      }

      showSuccessNotification(`Stats exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Stats have been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export stats: ${error.message}`);
      announceToScreenReader('Failed to export stats');
    }
  }, [subscriptionFeatures.hasExport, statsData.processed, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportStats: handleExport,
    getStatsData: () => statsData.processed,
    getStatsLimits: validateStatsFeatures,
    focus: () => setFocusToElement(document.querySelector(`[data-testid="${testId}"]`)),
    announce: (message) => announceToScreenReader(message)
  }), [
    statsData.processed,
    validateStatsFeatures,
    handleRefresh,
    handleExport,
    setFocusToElement,
    announceToScreenReader,
    testId
  ]);

  /**
   * Enhanced data processing effect - Production Ready
   */
  useEffect(() => {
    setStatsData(prev => ({
      ...prev,
      raw: { title, value, subtitle, trend, trendValue },
      processed: { title, value, subtitle, trend, trendValue }
    }));
  }, [title, value, subtitle, trend, trendValue]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: subscriptionLoading }));
  }, [subscriptionLoading]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced stats features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Advanced stats types',
        'Real-time stats monitoring',
        'Stats data export',
        'AI-powered stats insights',
        'Custom stats analytics',
        'Comparative stats analysis'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  /**
   * Enhanced trend color with ACE Social colors - Production Ready
   */
  const getTrendColor = useCallback(() => {
    if (!trend) return alpha(ACE_COLORS.DARK, 0.6);

    switch (trend) {
      case 'up':
        return '#4CAF50';
      case 'down':
        return '#F44336';
      default:
        return alpha(ACE_COLORS.DARK, 0.6);
    }
  }, [trend]);

  /**
   * Enhanced trend icon with Material-UI icons - Production Ready
   */
  const getTrendIcon = useCallback(() => {
    const iconSize = isMobile ? 16 : 20;
    if (!trend) return null;

    switch (trend) {
      case 'up':
        return <TrendingUpIcon sx={{ fontSize: iconSize, color: getTrendColor() }} />;
      case 'down':
        return <TrendingDownIcon sx={{ fontSize: iconSize, color: getTrendColor() }} />;
      default:
        return <TrendingFlatIcon sx={{ fontSize: iconSize, color: getTrendColor() }} />;
    }
  }, [trend, isMobile, getTrendColor]);

  // Main render condition checks
  if (state.loading && !statsData.processed) {
    return (
      <ErrorBoundary
        fallback={
          <Box sx={{ height: '100%', p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load stats card
            </Typography>
          </Box>
        }
      >
        <GlassmorphicCard
          variant="glass"
          sx={{
            height: '100%',
            minHeight: 120,
            ...customization,
            ...sx
          }}
          className={className}
          style={style}
          data-testid={testId}
          {...getAccessibilityProps()}
        >
          <CustomCardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box sx={{ flex: 1 }}>
                <Skeleton variant="text" width="60%" height={20} sx={{ mb: 1 }} />
                <Skeleton variant="text" width="80%" height={32} sx={{ mb: 1 }} />
                <Skeleton variant="text" width="40%" height={16} />
              </Box>
              <Skeleton variant="circular" width={48} height={48} />
            </Box>
          </CustomCardContent>
        </GlassmorphicCard>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Box sx={{ height: '100%', p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="error">
            Unable to load stats card
          </Typography>
        </Box>
      }
    >
      <GlassmorphicCard
        variant="glass"
        sx={{
          height: '100%',
          minHeight: 120,
          position: 'relative',
          ...customization,
          ...sx
        }}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        {/* Real-time Updates Indicator */}
        {realTimeUpdates && subscriptionFeatures.hasRealTime && (
          <Box sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 0.5,
            backgroundColor: alpha(theme.palette.success.main, 0.1),
            borderRadius: 1,
            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
            zIndex: 10
          }}>
            <Box
              sx={{
                width: 6,
                height: 6,
                borderRadius: '50%',
                backgroundColor: theme.palette.success.main,
                ...(!prefersReducedMotion && {
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 }
                  }
                })
              }}
              aria-hidden="true"
            />
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.6rem' }}>
              Live
            </Typography>
          </Box>
        )}

        <CustomCardContent>
          <Fade in timeout={500}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box sx={{ flex: 1, pr: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                    {title}
                  </Typography>

                  {/* Action Controls */}
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    {/* Export Button */}
                    {enableExport && (
                      <Tooltip title="Export Stats">
                        <IconButton
                          size="small"
                          onClick={handleExportMenuOpen}
                          sx={{
                            color: theme.palette.text.secondary,
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.primary.main, 0.1)
                            }
                          }}
                          aria-label="Export stats data"
                        >
                          <ExportIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    {/* Refresh Button */}
                    {onRefresh && (
                      <Tooltip title="Refresh Stats">
                        <IconButton
                          size="small"
                          onClick={handleRefresh}
                          disabled={state.refreshing}
                          sx={{
                            color: ACE_COLORS.PURPLE,
                            '&:hover': {
                              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                            }
                          }}
                          aria-label="Refresh stats"
                        >
                          {state.refreshing ? (
                            <CircularProgress size={16} />
                          ) : (
                            <RefreshIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </Box>

                <Typography variant={isMobile ? "h5" : "h4"} component="div" sx={{ fontWeight: 'bold', mb: 1, color: ACE_COLORS.PURPLE }}>
                  {value}
                </Typography>

                {subtitle && (
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {subtitle}
                  </Typography>
                )}

                {trend && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      {getTrendIcon()}
                      <Typography
                        variant="body2"
                        sx={{
                          color: getTrendColor(),
                          fontWeight: 'medium'
                        }}
                      >
                        {trendValue > 0 ? '+' : ''}{trendValue}%
                      </Typography>
                    </Box>
                    {subscriptionFeatures.hasInsights && (
                      <Chip
                        label="AI Enhanced"
                        size="small"
                        sx={{
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                          color: ACE_COLORS.PURPLE,
                          fontWeight: 600,
                          fontSize: '0.7rem',
                          ml: 1
                        }}
                      />
                    )}
                  </Box>
                )}
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: alpha(color || ACE_COLORS.PURPLE, 0.1),
                  borderRadius: '50%',
                  width: isMobile ? 40 : 48,
                  height: isMobile ? 40 : 48,
                  color: color || ACE_COLORS.PURPLE
                }}
              >
                {icon}
              </Box>
            </Box>
          </Fade>
        </CustomCardContent>

        {/* Export Menu */}
        <Menu
          anchorEl={exportAnchorEl}
          open={state.showExportMenu}
          onClose={handleExportMenuClose}
          slotProps={{
            paper: {
              sx: {
                mt: 1,
                minWidth: 180,
                boxShadow: theme.shadows[8],
                border: `1px solid ${theme.palette.divider}`
              }
            }
          }}
        >
          <MenuItem onClick={() => {
            handleExport('csv');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as CSV</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => {
            handleExport('json');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as JSON</ListItemText>
          </MenuItem>
        </Menu>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={handleUpgradeDialogClose}
          maxWidth="md"
          fullWidth
          slotProps={{
            paper: {
              sx: {
                borderRadius: 2,
                boxShadow: theme.shadows[16]
              }
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span">
                {getUpgradeDialogContent().title}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {getUpgradeDialogContent().content}
              </Typography>

              <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                Unlock Advanced Stats Features:
              </Typography>

              <Grid container spacing={1}>
                {getUpgradeDialogContent().features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                      <Typography variant="body2">{feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Alert
              severity="info"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
              Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced stats features.
            </Alert>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleUpgradeDialogClose}
              sx={{ color: theme.palette.text.secondary }}
            >
              Maybe Later
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                }
              }}
              startIcon={<UpgradeIcon />}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </GlassmorphicCard>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
StatsCard.propTypes = {
  // Core props
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  icon: PropTypes.node,
  color: PropTypes.string,
  subtitle: PropTypes.string,
  trend: PropTypes.oneOf(['up', 'down', 'neutral']),
  trendValue: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),

  // Enhanced stats props
  statsType: PropTypes.oneOf(['performance', 'engagement', 'growth', 'analytics', 'comparison']),
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  customization: PropTypes.object,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  sx: PropTypes.object
};

StatsCard.defaultProps = {
  statsType: 'performance',
  enableExport: false,
  realTimeUpdates: false,
  customization: {},
  className: '',
  style: {},
  testId: 'stats-card',
  sx: {}
};

// Display name for debugging
StatsCard.displayName = 'StatsCard';

export default StatsCard;

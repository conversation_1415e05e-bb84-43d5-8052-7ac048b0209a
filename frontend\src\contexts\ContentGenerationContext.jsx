/**
 * Content Generation Context
 * Provides unified state management and synchronization between text and image generation
 * Production-ready implementation with comprehensive error handling and logging
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useNotification } from '../hooks/useNotification';
import { useBranding } from '../hooks/useBranding';
import { useLanguageStandalone } from '../hooks/useLanguage.jsx';
import { generateContent, generateImage } from '../api/content';

// Configuration constants
const CONFIG = {
  // Generation settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  GENERATION_TIMEOUT: 60000, // 60 seconds

  // History settings
  MAX_HISTORY_ITEMS: 50,

  // Validation settings
  MIN_PROMPT_LENGTH: 10,
  MAX_PROMPT_LENGTH: 2000,

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[ContentGeneration] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[ContentGeneration] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Content Generation Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[ContentGeneration] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Content Generation Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[ContentGeneration] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Content Generation Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};
const ContentGenerationContext = createContext();

// eslint-disable-next-line react-refresh/only-export-components
export const useContentGeneration = () => {
  const context = useContext(ContentGenerationContext);
  if (!context) {
    throw new Error('useContentGeneration must be used within a ContentGenerationProvider');
  }
  return context;
};

export const ContentGenerationProvider = ({ children }) => {
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { brandingData, applyBrandingToImagePrompt } = useBranding();
  const { currentLanguage } = useLanguageStandalone();

  // Create simple fallback functions for missing language features
  const generateCulturalPromptPrefix = useCallback((industry, style) => {
    return `Create professional content for ${industry} with ${style} style. `;
  }, []);

  const generateEnvironmentalContext = useCallback((location, industry, lifestyle) => {
    logger.debug('Generating environmental context', { location, industry, lifestyle });
    return `Set in a modern ${industry} environment in ${location} with ${lifestyle} lifestyle. `;
  }, []);

  // Shared state between text and image generation
  const [sharedContext, setSharedContext] = useState({
    selectedICP: null,
    contentTopic: '',
    generatedText: null,
    generatedImages: [],
    brandingSettings: null,
    languageSettings: {
      language: currentLanguage,
      culturalContext: null
    },
    environmentalContext: '',
    productImages: [],
    templates: [],
    lastGeneration: null
  });

  const [isGenerating, setIsGenerating] = useState({
    text: false,
    image: false,
    synchronized: false
  });

  const [generationHistory, setGenerationHistory] = useState([]);

  // Update language settings when language changes
  useEffect(() => {
    setSharedContext(prev => ({
      ...prev,
      languageSettings: {
        language: currentLanguage,
        culturalContext: generateCulturalPromptPrefix(
          prev.selectedICP?.demographics?.industry || 'business',
          'professional'
        )
      }
    }));
  }, [currentLanguage, generateCulturalPromptPrefix]);

  // Update branding settings when branding data changes
  useEffect(() => {
    if (brandingData) {
      setSharedContext(prev => ({
        ...prev,
        brandingSettings: {
          ...brandingData,
          logos: {
            favicon: brandingData.favicon || null,
            logo: brandingData.logo || null
          },
          logoSettings: brandingData.logo_settings || {
            position: 'bottom-right',
            opacity: 80,
            size: 25,
            margin: 16,
            rotation: 0,
            useWatermark: false,
            watermarkOpacity: 20,
            watermarkScale: 150
          }
        }
      }));
    }
  }, [brandingData]);

  // Update ICP context
  const updateICPContext = useCallback((icp) => {
    setSharedContext(prev => {
      const environmentalContext = icp ? generateEnvironmentalContext(
        icp.demographics?.location || '',
        icp.demographics?.industry || '',
        icp.psychographics?.lifestyle || ''
      ) : '';

      return {
        ...prev,
        selectedICP: icp,
        environmentalContext
      };
    });
  }, [generateEnvironmentalContext]);

  // Update content topic
  const updateContentTopic = useCallback((topic) => {
    setSharedContext(prev => ({
      ...prev,
      contentTopic: topic
    }));
  }, []);

  // Update product images
  const updateProductImages = useCallback((images) => {
    setSharedContext(prev => ({
      ...prev,
      productImages: images
    }));
  }, []);

  // Generate enhanced prompt that combines text and image context
  const generateEnhancedPrompt = useCallback((basePrompt, type = 'text') => {
    let enhancedPrompt = basePrompt;

    // Add cultural context based on language
    if (sharedContext.languageSettings.culturalContext) {
      enhancedPrompt = `${sharedContext.languageSettings.culturalContext} ${enhancedPrompt}`;
    }

    // Add ICP context
    if (sharedContext.selectedICP) {
      const icp = sharedContext.selectedICP;
      
      if (type === 'text') {
        // For text generation, add messaging context
        const audienceContext = `Target audience: ${icp.demographics?.industry} professionals, ${icp.demographics?.age_range}, ${icp.demographics?.income_level} income level.`;
        const toneContext = icp.content_preferences?.length > 0 
          ? `Content tone should be ${icp.content_preferences[0].tone || 'professional'}.`
          : 'Use a professional, engaging tone.';
        
        enhancedPrompt = `${enhancedPrompt} ${audienceContext} ${toneContext}`;
      } else if (type === 'image') {
        // For image generation, add environmental context
        if (sharedContext.environmentalContext) {
          enhancedPrompt = `${enhancedPrompt} ${sharedContext.environmentalContext}`;
        }
      }
    }

    // Add product image context for image generation
    if (type === 'image' && sharedContext.productImages.length > 0) {
      const productContext = sharedContext.productImages
        .filter(img => img.description)
        .map(img => `${img.integrationMode}: ${img.description}`)
        .join(', ');
      
      if (productContext) {
        enhancedPrompt = `${enhancedPrompt} Incorporate these product elements: ${productContext}.`;
      }
    }

    // Add branding context
    if (sharedContext.brandingSettings) {
      const branding = sharedContext.brandingSettings;
      
      if (type === 'image') {
        // Apply branding to image prompts
        const brandedPrompt = applyBrandingToImagePrompt(enhancedPrompt);
        enhancedPrompt = brandedPrompt.prompt;
      } else if (type === 'text') {
        // Add brand voice context for text
        if (branding.style) {
          enhancedPrompt = `${enhancedPrompt} Maintain a ${branding.style} brand voice and style.`;
        }
      }
    }

    return enhancedPrompt;
  }, [sharedContext, applyBrandingToImagePrompt]);

  // Generate synchronized content (text + image)
  const generateSynchronizedContent = useCallback(async (options = {}) => {
    const {
      topic = sharedContext.contentTopic,
      platform = 'linkedin',
      includeImage = true,
      imageCount = 1
    } = options;

    if (!topic.trim()) {
      showErrorNotification('Please provide a content topic');
      return null;
    }

    setIsGenerating(prev => ({ ...prev, synchronized: true }));

    try {
      logger.info('Starting synchronized content generation', { platform, topic });

      // Step 1: Generate text content with enhanced context
      const textPrompt = generateEnhancedPrompt(
        `Create engaging ${platform} content about: ${topic}`,
        'text'
      );

      logger.debug('Generated text prompt', { textPrompt });

      const textRequestData = {
        topic,
        platform,
        tone: sharedContext.selectedICP?.content_preferences?.[0]?.tone || 'professional',
        include_hashtags: true,
        target_audience: sharedContext.selectedICP ? 
          `${sharedContext.selectedICP.demographics?.industry} professionals` : '',
        generate_image: false // We'll generate image separately for better control
      };

      // Apply branding to text generation
      if (sharedContext.brandingSettings) {
        textRequestData.branding = sharedContext.brandingSettings;
      }

      const textResponse = await generateContent(textRequestData);

      // Step 2: Generate image based on text content
      let imageResponse = [];
      let imagePrompt = '';
      if (includeImage && textResponse) {
        imagePrompt = generateEnhancedPrompt(
          `Create a professional image for this content: ${textResponse.text_content}. Topic: ${topic}`,
          'image'
        );

        const imageRequestData = {
          prompt: imagePrompt,
          size: '1024x1024',
          style: 'vivid',
          n: imageCount
        };

        // Apply branding to image generation
        if (sharedContext.brandingSettings) {
          imageRequestData.branding = sharedContext.brandingSettings;

          // Add logo settings if available
          if (sharedContext.brandingSettings.logos?.logo || sharedContext.brandingSettings.logos?.favicon) {
            imageRequestData.logoSettings = sharedContext.brandingSettings.logoSettings;
          }
        }

        imageResponse = await generateImage(imageRequestData);
      }

      // Step 3: Create synchronized content object
      const synchronizedContent = {
        id: Date.now(),
        text: textResponse,
        images: imageResponse.map(url => ({
          url,
          prompt: imagePrompt,
          generated_at: new Date().toISOString(),
          synchronized: true
        })),
        context: {
          icp: sharedContext.selectedICP,
          language: sharedContext.languageSettings.language,
          branding: sharedContext.brandingSettings,
          environmentalContext: sharedContext.environmentalContext,
          productImages: sharedContext.productImages
        },
        generated_at: new Date().toISOString(),
        synchronized: true
      };

      // Update shared context
      setSharedContext(prev => ({
        ...prev,
        generatedText: textResponse,
        generatedImages: [...prev.generatedImages, ...synchronizedContent.images],
        lastGeneration: synchronizedContent
      }));

      // Add to history
      setGenerationHistory(prev => [synchronizedContent, ...prev.slice(0, 9)]);

      showSuccessNotification('Synchronized content generated successfully!');
      return synchronizedContent;

    } catch (error) {
      logger.error('Synchronized generation error', error);
      showErrorNotification('Failed to generate synchronized content');
      return null;
    } finally {
      setIsGenerating(prev => ({ ...prev, synchronized: false }));
    }
  }, [
    sharedContext,
    generateEnhancedPrompt,
    showSuccessNotification,
    showErrorNotification
  ]);

  // Generate text content with shared context
  const generateTextContent = useCallback(async (options = {}) => {
    const {
      topic = sharedContext.contentTopic,
      platform = 'linkedin',
      customPrompt = ''
    } = options;

    setIsGenerating(prev => ({ ...prev, text: true }));

    try {
      const prompt = customPrompt || generateEnhancedPrompt(
        `Create engaging ${platform} content about: ${topic}`,
        'text'
      );

      logger.debug('Generating text content', { prompt, platform, topic });

      const requestData = {
        topic,
        platform,
        tone: sharedContext.selectedICP?.content_preferences?.[0]?.tone || 'professional',
        include_hashtags: true,
        target_audience: sharedContext.selectedICP ? 
          `${sharedContext.selectedICP.demographics?.industry} professionals` : '',
        generate_image: false
      };

      if (sharedContext.brandingSettings) {
        requestData.branding = sharedContext.brandingSettings;
      }

      const response = await generateContent(requestData);

      setSharedContext(prev => ({
        ...prev,
        generatedText: response
      }));

      return response;
    } catch (error) {
      logger.error('Text generation error', error);
      showErrorNotification('Failed to generate text content');
      return null;
    } finally {
      setIsGenerating(prev => ({ ...prev, text: false }));
    }
  }, [sharedContext, generateEnhancedPrompt, showErrorNotification]);

  // Generate image content with shared context
  const generateImageContent = useCallback(async (options = {}) => {
    const {
      prompt = '',
      baseOnText = true,
      count = 1
    } = options;

    setIsGenerating(prev => ({ ...prev, image: true }));

    try {
      let imagePrompt = prompt;
      
      if (baseOnText && sharedContext.generatedText) {
        imagePrompt = generateEnhancedPrompt(
          `Create a professional image for this content: ${sharedContext.generatedText.text_content}`,
          'image'
        );
      } else if (!imagePrompt) {
        imagePrompt = generateEnhancedPrompt(
          `Create a professional image about: ${sharedContext.contentTopic}`,
          'image'
        );
      }

      const requestData = {
        prompt: imagePrompt,
        size: '1024x1024',
        style: 'vivid',
        n: count
      };

      if (sharedContext.brandingSettings) {
        requestData.branding = sharedContext.brandingSettings;
        
        if (sharedContext.brandingSettings.logos?.logo || sharedContext.brandingSettings.logos?.favicon) {
          requestData.logoSettings = sharedContext.brandingSettings.logoSettings;
        }
      }

      const response = await generateImage(requestData);

      const newImages = response.map(url => ({
        url,
        prompt: imagePrompt,
        generated_at: new Date().toISOString(),
        synchronized: baseOnText
      }));

      setSharedContext(prev => ({
        ...prev,
        generatedImages: [...prev.generatedImages, ...newImages]
      }));

      return newImages;
    } catch (error) {
      logger.error('Image generation error', error);
      showErrorNotification('Failed to generate image content');
      return null;
    } finally {
      setIsGenerating(prev => ({ ...prev, image: false }));
    }
  }, [sharedContext, generateEnhancedPrompt, showErrorNotification]);

  // Save content template
  const saveContentTemplate = useCallback((template) => {
    const newTemplate = {
      id: Date.now(),
      ...template,
      context: {
        icp: sharedContext.selectedICP,
        language: sharedContext.languageSettings.language,
        branding: sharedContext.brandingSettings
      },
      created_at: new Date().toISOString()
    };

    setSharedContext(prev => ({
      ...prev,
      templates: [...prev.templates, newTemplate]
    }));

    showSuccessNotification('Content template saved successfully!');
    return newTemplate;
  }, [sharedContext, showSuccessNotification]);

  // Clear shared context
  const clearContext = useCallback(() => {
    setSharedContext(prev => ({
      ...prev,
      contentTopic: '',
      generatedText: null,
      generatedImages: [],
      lastGeneration: null
    }));
  }, []);

  const value = {
    // Shared state
    sharedContext,
    isGenerating,
    generationHistory,

    // Context updates
    updateICPContext,
    updateContentTopic,
    updateProductImages,

    // Content generation
    generateSynchronizedContent,
    generateTextContent,
    generateImageContent,
    generateEnhancedPrompt,

    // Template management
    saveContentTemplate,

    // Utilities
    clearContext
  };

  return (
    <ContentGenerationContext.Provider value={value}>
      {children}
    </ContentGenerationContext.Provider>
  );
};

export default ContentGenerationContext;

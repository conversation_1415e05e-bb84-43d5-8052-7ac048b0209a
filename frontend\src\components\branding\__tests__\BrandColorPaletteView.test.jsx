/**
 * Tests for BrandColorPaletteView component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandColorPaletteView from '../BrandColorPaletteView';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock clipboard API
const mockWriteText = vi.fn().mockResolvedValue(undefined);
Object.assign(navigator, {
  clipboard: {
    writeText: mockWriteText,
  },
});

describe('BrandColorPaletteView', () => {
  const mockColorPalette = {
    primary: '#4E40C5',
    secondary: '#00E4BC',
    accent: '#FF5733',
    background: '#F8F9FA',
    text: '#333333',
    relationship: 'complementary',
    additional_colors: ['#FFD700', '#FF69B4', '#32CD32']
  };

  const mockProps = {
    colorPalette: mockColorPalette,
    onColorCopy: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockWriteText.mockClear();
    mockWriteText.mockResolvedValue(undefined);
  });

  test('renders color palette view correctly', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Main Colors')).toBeInTheDocument();
    expect(screen.getByText('Background & Text Colors')).toBeInTheDocument();
    expect(screen.getByText('Preview')).toBeInTheDocument();
    expect(screen.getByText('Primary Color')).toBeInTheDocument();
    expect(screen.getByText('Secondary Color')).toBeInTheDocument();
    expect(screen.getByText('Accent Color')).toBeInTheDocument();
    expect(screen.getByText('Background Color')).toBeInTheDocument();
    expect(screen.getByText('Text Color')).toBeInTheDocument();
  });

  test('displays color values correctly', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('#4E40C5')).toBeInTheDocument();
    expect(screen.getByText('#00E4BC')).toBeInTheDocument();
    expect(screen.getByText('#FF5733')).toBeInTheDocument();
    expect(screen.getByText('#F8F9FA')).toBeInTheDocument();
    expect(screen.getByText('#333333')).toBeInTheDocument();
  });

  test('displays color relationship when provided', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Color Relationship:')).toBeInTheDocument();
    expect(screen.getByText('complementary')).toBeInTheDocument();
  });

  test('displays additional colors when provided', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Additional Colors')).toBeInTheDocument();
    expect(screen.getByText('#FFD700')).toBeInTheDocument();
    expect(screen.getByText('#FF69B4')).toBeInTheDocument();
    expect(screen.getByText('#32CD32')).toBeInTheDocument();
  });

  test('copies color to clipboard when copy button is clicked', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    const copyButton = screen.getByLabelText('Copy primary color #4E40C5');
    await user.click(copyButton);

    await waitFor(() => {
      expect(mockWriteText).toHaveBeenCalledWith('#4E40C5');
      expect(mockProps.onColorCopy).toHaveBeenCalledWith('#4E40C5');
    });
  });

  test('shows copied state temporarily after copying', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    const copyButton = screen.getByLabelText('Copy primary color #4E40C5');
    await user.click(copyButton);

    // Check that the icon changed to CheckIcon (indicating copied state)
    expect(screen.getByTestId('CheckIcon')).toBeInTheDocument();

    // Wait for the copied state to reset (2 seconds)
    await waitFor(() => {
      expect(screen.queryByTestId('CheckIcon')).not.toBeInTheDocument();
    }, { timeout: 3000 });
  });

  test('copies additional colors when clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    const additionalColorChip = screen.getByLabelText('Copy additional color #FFD700');
    await user.click(additionalColorChip);

    expect(mockWriteText).toHaveBeenCalledWith('#FFD700');
    expect(mockProps.onColorCopy).toHaveBeenCalledWith('#FFD700');
  });

  test('handles clipboard errors gracefully', async () => {
    const user = userEvent.setup();
    mockWriteText.mockRejectedValue(new Error('Clipboard error'));
    
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    const copyButton = screen.getByLabelText('Copy primary color #4E40C5');
    await user.click(copyButton);

    expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
  });

  test('shows placeholder when no color palette provided', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView colorPalette={null} />
      </TestWrapper>
    );

    expect(screen.getByText('No color palette defined')).toBeInTheDocument();
  });

  test('uses default colors when palette values are missing', () => {
    const incompleteColorPalette = {
      primary: '#4E40C5',
      // Missing other colors
    };

    render(
      <TestWrapper>
        <BrandColorPaletteView colorPalette={incompleteColorPalette} />
      </TestWrapper>
    );

    // Should show default values for missing colors
    expect(screen.getByText('#00E4BC')).toBeInTheDocument(); // Default secondary
    expect(screen.getByText('#FF5733')).toBeInTheDocument(); // Default accent
    expect(screen.getByText('#F8F9FA')).toBeInTheDocument(); // Default background
    expect(screen.getByText('#333333')).toBeInTheDocument(); // Default text
  });

  test('hides preview section when showPreview is false', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} showPreview={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Preview')).not.toBeInTheDocument();
    expect(screen.queryByText('Sample Heading')).not.toBeInTheDocument();
  });

  test('disables copy buttons when disabled prop is true', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} disabled={true} />
      </TestWrapper>
    );

    // Check specific copy buttons by their aria-labels
    const primaryCopyButton = screen.getByLabelText('Copy primary color #4E40C5');
    const secondaryCopyButton = screen.getByLabelText('Copy secondary color #00E4BC');
    const accentCopyButton = screen.getByLabelText('Copy accent color #FF5733');
    const backgroundCopyButton = screen.getByLabelText('Copy background color #F8F9FA');
    const textCopyButton = screen.getByLabelText('Copy text color #333333');

    expect(primaryCopyButton).toBeDisabled();
    expect(secondaryCopyButton).toBeDisabled();
    expect(accentCopyButton).toBeDisabled();
    expect(backgroundCopyButton).toBeDisabled();
    expect(textCopyButton).toBeDisabled();
  });

  test('does not copy when disabled', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const copyButton = screen.getByLabelText('Copy primary color #4E40C5');

    // Button should be disabled, so we just check that it's disabled
    // and that no clipboard operations have been called
    expect(copyButton).toBeDisabled();
    expect(mockWriteText).not.toHaveBeenCalled();
    expect(mockProps.onColorCopy).not.toHaveBeenCalled();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    const primaryCopyButton = screen.getByLabelText('Copy primary color #4E40C5');
    const secondaryCopyButton = screen.getByLabelText('Copy secondary color #00E4BC');
    const accentCopyButton = screen.getByLabelText('Copy accent color #FF5733');
    const backgroundCopyButton = screen.getByLabelText('Copy background color #F8F9FA');
    const textCopyButton = screen.getByLabelText('Copy text color #333333');

    expect(primaryCopyButton).toHaveAttribute('aria-label', 'Copy primary color #4E40C5');
    expect(secondaryCopyButton).toHaveAttribute('aria-label', 'Copy secondary color #00E4BC');
    expect(accentCopyButton).toHaveAttribute('aria-label', 'Copy accent color #FF5733');
    expect(backgroundCopyButton).toHaveAttribute('aria-label', 'Copy background color #F8F9FA');
    expect(textCopyButton).toHaveAttribute('aria-label', 'Copy text color #333333');
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView 
          {...mockProps} 
          data-testid="test-color-palette"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-color-palette');
    expect(component).toHaveClass('custom-class');
  });

  test('does not show additional colors section when none provided', () => {
    const colorPaletteWithoutAdditional = {
      ...mockColorPalette,
      additional_colors: undefined
    };

    render(
      <TestWrapper>
        <BrandColorPaletteView colorPalette={colorPaletteWithoutAdditional} />
      </TestWrapper>
    );

    expect(screen.queryByText('Additional Colors')).not.toBeInTheDocument();
  });

  test('does not show additional colors section when empty array', () => {
    const colorPaletteWithEmptyAdditional = {
      ...mockColorPalette,
      additional_colors: []
    };

    render(
      <TestWrapper>
        <BrandColorPaletteView colorPalette={colorPaletteWithEmptyAdditional} />
      </TestWrapper>
    );

    expect(screen.queryByText('Additional Colors')).not.toBeInTheDocument();
  });

  test('does not show color relationship when not provided', () => {
    const colorPaletteWithoutRelationship = {
      ...mockColorPalette,
      relationship: undefined
    };

    render(
      <TestWrapper>
        <BrandColorPaletteView colorPalette={colorPaletteWithoutRelationship} />
      </TestWrapper>
    );

    expect(screen.queryByText('Color Relationship:')).not.toBeInTheDocument();
  });

  test('renders preview section with correct styling', () => {
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Sample Heading')).toBeInTheDocument();
    expect(screen.getByText('Subheading with secondary color')).toBeInTheDocument();
    expect(screen.getByText(/This is a sample paragraph/)).toBeInTheDocument();
    expect(screen.getByText('Primary')).toBeInTheDocument();
    expect(screen.getByText('Secondary')).toBeInTheDocument();
    expect(screen.getByText('Accent')).toBeInTheDocument();
  });

  test('handles multiple copy operations correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPaletteView {...mockProps} />
      </TestWrapper>
    );

    // Copy primary color
    const primaryCopyButton = screen.getByLabelText('Copy primary color #4E40C5');
    await user.click(primaryCopyButton);

    expect(mockWriteText).toHaveBeenCalledWith('#4E40C5');
    expect(mockProps.onColorCopy).toHaveBeenCalledWith('#4E40C5');

    // Copy secondary color
    const secondaryCopyButton = screen.getByLabelText('Copy secondary color #00E4BC');
    await user.click(secondaryCopyButton);

    expect(mockWriteText).toHaveBeenCalledWith('#00E4BC');
    expect(mockProps.onColorCopy).toHaveBeenCalledWith('#00E4BC');

    expect(mockProps.onColorCopy).toHaveBeenCalledTimes(2);
  });
});

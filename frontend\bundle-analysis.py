#!/usr/bin/env python3
# @since 2024-1-1 to 2025-25-7
"""
Frontend Bundle Analysis & Performance Audit Tool

This script analyzes the Vite build output and provides detailed
performance metrics and optimization recommendations.
"""

import os
import json
from pathlib import Path

# Performance targets
PERFORMANCE_TARGETS = {
    'total_bundle_size': 2 * 1024 * 1024,  # 2MB
    'max_chunk_size': 500 * 1024,  # 500KB
    'max_initial_load_time': 2000,  # 2 seconds
    'lighthouse': {
        'performance': 90,
        'accessibility': 95,
        'best_practices': 90,
        'seo': 90
    }
}

def format_bytes(bytes_val):
    """Format bytes to human readable format."""
    if bytes_val == 0:
        return '0 Bytes'
    
    k = 1024
    sizes = ['Bytes', 'KB', 'MB', 'GB']
    i = int(len(bin(bytes_val)) - 3) // 10
    if i >= len(sizes):
        i = len(sizes) - 1
    
    return f"{bytes_val / (k ** i):.2f} {sizes[i]}"

def categorize_file(filename):
    """Categorize files based on their names."""
    if 'vendor' in filename:
        return 'Vendor Libraries'
    elif 'chunk' in filename:
        return 'Application Chunks'
    elif 'react' in filename:
        return 'React Ecosystem'
    elif 'mui' in filename:
        return 'Material-UI'
    elif 'charts' in filename:
        return 'Chart Libraries'
    elif 'media' in filename:
        return 'Media Processing'
    elif 'utils' in filename:
        return 'Utilities'
    elif 'index' in filename:
        return 'Main Application'
    elif filename.endswith('.css'):
        return 'Stylesheets'
    else:
        return 'Application Code'

def analyze_bundle():
    """Main bundle analysis function."""
    print('🔍 FRONTEND BUNDLE ANALYSIS & PERFORMANCE AUDIT')
    print('=' * 60)
    
    dist_path = Path('dist/assets')
    
    if not dist_path.exists():
        print('❌ Dist folder not found. Run "npm run build" first.')
        return
    
    files = list(dist_path.glob('*'))
    js_files = [f for f in files if f.suffix == '.js']
    css_files = [f for f in files if f.suffix == '.css']
    
    print(f'\n📊 BUNDLE COMPOSITION:')
    print(f'  - JavaScript files: {len(js_files)}')
    print(f'  - CSS files: {len(css_files)}')
    print(f'  - Total assets: {len(files)}')
    
    # Analyze file sizes
    file_analysis = []
    total_size = 0
    
    for file in files:
        size = file.stat().st_size
        total_size += size
        
        file_analysis.append({
            'name': file.name,
            'size': size,
            'type': file.suffix[1:] if file.suffix else 'unknown',
            'category': categorize_file(file.name)
        })
    
    # Sort by size (largest first)
    file_analysis.sort(key=lambda x: x['size'], reverse=True)
    
    print(f'\n📈 LARGEST CHUNKS:')
    for i, file in enumerate(file_analysis[:10]):
        size_formatted = format_bytes(file['size'])
        status = '⚠️' if file['size'] > PERFORMANCE_TARGETS['max_chunk_size'] else '✅'
        print(f'  {i+1}. {status} {file["name"]}: {size_formatted} ({file["category"]})')
    
    # Performance assessment
    print(f'\n🎯 PERFORMANCE ASSESSMENT:')
    print(f'  Total bundle size: {format_bytes(total_size)}')
    
    if total_size <= PERFORMANCE_TARGETS['total_bundle_size']:
        print('  ✅ Bundle size within 2MB target')
    else:
        excess = total_size - PERFORMANCE_TARGETS['total_bundle_size']
        print(f'  ⚠️  Bundle size exceeds target by {format_bytes(excess)}')
    
    # Chunk analysis
    large_chunks = [f for f in file_analysis if f['size'] > PERFORMANCE_TARGETS['max_chunk_size']]
    if large_chunks:
        print(f'  ⚠️  {len(large_chunks)} chunks exceed 500KB limit')
    else:
        print('  ✅ All chunks within size limits')
    
    # Category breakdown
    print(f'\n📋 SIZE BY CATEGORY:')
    categories = {}
    for file in file_analysis:
        category = file['category']
        if category not in categories:
            categories[category] = []
        categories[category].append(file)
    
    for category, files in categories.items():
        total_category_size = sum(f['size'] for f in files)
        print(f'  - {category}: {format_bytes(total_category_size)} ({len(files)} files)')
    
    # Optimization recommendations
    print(f'\n💡 OPTIMIZATION RECOMMENDATIONS:')
    generate_recommendations(file_analysis, total_size, large_chunks)
    
    # Check for analysis report
    stats_file = Path('dist/stats.html')
    if stats_file.exists():
        print(f'\n📊 DETAILED ANALYSIS:')
        print('  ✅ Bundle visualization available: dist/stats.html')
    
    # Performance score
    score = calculate_performance_score(total_size, len(large_chunks))
    print(f'\n🏆 PERFORMANCE SCORE:')
    print(f'  Overall: {score}/100 {get_score_emoji(score)}')
    
    print('\n' + '=' * 60)
    
    return {
        'total_size': total_size,
        'file_count': len(files),
        'large_chunks': len(large_chunks),
        'score': score,
        'meets_targets': total_size <= PERFORMANCE_TARGETS['total_bundle_size'] and len(large_chunks) == 0
    }

def generate_recommendations(files, total_size, large_chunks):
    """Generate optimization recommendations."""
    recommendations = []
    
    # Large bundle recommendations
    if total_size > PERFORMANCE_TARGETS['total_bundle_size']:
        recommendations.append('🔧 Implement more aggressive code splitting')
        recommendations.append('🔧 Consider lazy loading non-critical components')
    
    # Large chunk recommendations
    if large_chunks:
        recommendations.append('🔧 Split large chunks using dynamic imports')
        for chunk in large_chunks:
            if 'charts' in chunk['name']:
                recommendations.append(f'   - Lazy load chart components in {chunk["name"]}')
            if 'content' in chunk['name']:
                recommendations.append(f'   - Split content management features in {chunk["name"]}')
    
    # Vendor optimization
    vendor_files = [f for f in files if f['category'] == 'Vendor Libraries']
    total_vendor_size = sum(f['size'] for f in vendor_files)
    if total_vendor_size > 1024 * 1024:  # 1MB
        recommendations.append('🔧 Optimize vendor bundle splitting')
        recommendations.append('   - Consider using CDN for large libraries')
    
    # General optimizations
    recommendations.extend([
        '🔧 Enable gzip/brotli compression on server',
        '🔧 Implement service worker for caching',
        '🔧 Use image optimization and lazy loading'
    ])
    
    if not recommendations:
        print('  ✅ Bundle is well optimized!')
    else:
        for rec in recommendations:
            print(f'  {rec}')

def calculate_performance_score(total_size, large_chunk_count):
    """Calculate overall performance score."""
    score = 100
    
    # Deduct points for large bundle
    if total_size > PERFORMANCE_TARGETS['total_bundle_size']:
        excess = (total_size - PERFORMANCE_TARGETS['total_bundle_size']) / PERFORMANCE_TARGETS['total_bundle_size']
        score -= min(30, excess * 50)
    
    # Deduct points for large chunks
    score -= large_chunk_count * 10
    
    return max(0, round(score))

def get_score_emoji(score):
    """Get emoji based on score."""
    if score >= 90:
        return '🟢 Excellent'
    elif score >= 70:
        return '🟡 Good'
    elif score >= 50:
        return '🟠 Needs Improvement'
    else:
        return '🔴 Poor'

if __name__ == '__main__':
    analyze_bundle()

// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Button, 
  Grid, 
  Divider, 
  CircularProgress,
  Snackbar,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import { useAuth } from '../../hooks/useAuth';
import { useNotification } from '../../hooks/useNotification';
import PageHeader from '../../components/common/PageHeader';
import VisualBrandEditor from '../../components/branding/VisualBrandEditor';
import defaultBrandingData, { mergeBrandingData } from '../../config/defaultBrandingData';
import api from '../../api';

const UnifiedBrandingPage = () => {
  const theme = useTheme();
  const { user, updateProfile } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [brandingData, setBrandingData] = useState(defaultBrandingData);
  const [hasChanges, setHasChanges] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Load branding data from user profile
  useEffect(() => {
    if (user?.branding_preferences) {
      // Use the helper function to merge with default structure
      setBrandingData(mergeBrandingData(user.branding_preferences));
    }
    setLoading(false);
  }, [user]);
  
  // Handle branding data changes
  const handleBrandingChange = (newData) => {
    setBrandingData(newData);
    setHasChanges(true);
  };
  
  // Save branding data
  const handleSave = async () => {
    setSaving(true);
    
    try {
      // Update user profile with new branding data
      await updateProfile({ branding_preferences: brandingData });
      
      // Update branding in API
      await api.post('/api/branding/update', { branding: brandingData });
      
      showSuccessNotification('Branding settings saved successfully');
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving branding settings:', error);
      showErrorNotification('Failed to save branding settings');
    } finally {
      setSaving(false);
    }
  };
  
  // Reset branding data to defaults
  const handleReset = () => {
    setBrandingData(defaultBrandingData);
    setHasChanges(true);
    setSnackbar({
      open: true,
      message: 'Branding settings reset to defaults',
      severity: 'info'
    });
  };
  
  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };
  
  return (
    <Container maxWidth="xl">
      <PageHeader 
        title="Brand Identity" 
        subtitle="Create and manage your brand's visual identity in one place"
      />
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Paper 
            elevation={0}
            sx={{
              p: 3,
              mb: 3,
              borderRadius: 2,
              background: theme.palette.mode === 'dark' 
                ? alpha(theme.palette.background.paper, 0.6)
                : alpha(theme.palette.background.paper, 0.8),
              backdropFilter: 'blur(10px)',
              border: `1px solid ${theme.palette.divider}`
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Unified Brand Editor
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Design your brand identity with our visual editor. Upload your logo, select colors, fonts, and patterns to create a consistent brand experience across all your content.
              </Typography>
            </Box>
            
            <Divider sx={{ mb: 3 }} />
            
            <VisualBrandEditor 
              brandingData={brandingData}
              onChange={handleBrandingChange}
            />
            
            <Divider sx={{ my: 3 }} />
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button 
                variant="outlined" 
                color="error" 
                onClick={handleReset}
                disabled={saving}
              >
                Reset to Defaults
              </Button>
              
              <Button 
                variant="contained" 
                color="primary" 
                onClick={handleSave}
                disabled={!hasChanges || saving}
                startIcon={saving && <CircularProgress size={20} color="inherit" />}
              >
                {saving ? 'Saving...' : 'Save Brand Settings'}
              </Button>
            </Box>
          </Paper>
        </>
      )}
      
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default UnifiedBrandingPage;

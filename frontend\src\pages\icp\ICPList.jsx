// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  CircularProgress,
  Paper,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import PersonIcon from '@mui/icons-material/Person';
import DeleteIcon from '@mui/icons-material/Delete';
import CampaignIcon from '@mui/icons-material/Campaign';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import BusinessIcon from '@mui/icons-material/Business';
import RefreshIcon from '@mui/icons-material/Refresh';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

const ICPList = () => {
  const navigate = useNavigate();
  const { serviceId } = useParams();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(null);
  const [service, setService] = useState(null);
  const [icps, setIcps] = useState([]);
  
  // Fetch service details and ICPs
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch service details
        const serviceResponse = await api.get(`/api/services/${serviceId}`);
        setService(serviceResponse.data);

        // Fetch ICPs for this service (correct API endpoint)
        const icpsResponse = await api.get(`/api/icp/icps?service_id=${serviceId}`);

        // Handle different response formats
        const icpsData = icpsResponse.data?.icps || icpsResponse.data || [];
        setIcps(Array.isArray(icpsData) ? icpsData : []);

      } catch (error) {
        console.error('Error fetching data:', error);
        if (error.response?.status === 404) {
          if (error.config?.url?.includes('/services/')) {
            showErrorNotification('Service not found');
            navigate('/services');
          } else {
            showErrorNotification('No ICPs found for this service');
          }
        } else if (error.response?.status === 403) {
          showErrorNotification('Access denied to this service or ICPs');
        } else {
          showErrorNotification(error.response?.data?.detail || 'Failed to load data');
        }
      } finally {
        setLoading(false);
      }
    };

    if (serviceId) {
      fetchData();
    }
  }, [serviceId, showErrorNotification, navigate]);
  
  // Refresh data
  const refreshData = async () => {
    if (serviceId) {
      setLoading(true);
      try {
        // Fetch service details
        const serviceResponse = await api.get(`/api/services/${serviceId}`);
        setService(serviceResponse.data);

        // Fetch ICPs for this service
        const icpsResponse = await api.get(`/api/icp/icps?service_id=${serviceId}`);
        const icpsData = icpsResponse.data?.icps || icpsResponse.data || [];
        setIcps(Array.isArray(icpsData) ? icpsData : []);

        showSuccessNotification('Data refreshed successfully');
      } catch (error) {
        console.error('Error refreshing data:', error);
        showErrorNotification(error.response?.data?.detail || 'Failed to refresh data');
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle generating ICPs
  const handleGenerateICPs = () => {
    navigate(`/services/${serviceId}/generate-icps`);
  };

  // Handle creating a campaign from an ICP
  const handleCreateCampaign = (icpId) => {
    navigate(`/icps/${icpId}/create-campaign`);
  };
  
  // Handle deleting an ICP
  const handleDeleteICP = async (icpId) => {
    if (window.confirm('Are you sure you want to delete this ICP? This will also delete all associated campaigns.')) {
      setDeleting(icpId);
      try {
        // Use correct API endpoint
        await api.delete(`/api/icp/${icpId}`);
        setIcps(icps.filter(icp => icp.id !== icpId));
        showSuccessNotification('ICP deleted successfully');
      } catch (error) {
        console.error('Error deleting ICP:', error);
        if (error.response?.status === 404) {
          showErrorNotification('ICP not found');
          // Remove from local state anyway since it doesn't exist
          setIcps(icps.filter(icp => icp.id !== icpId));
        } else if (error.response?.status === 403) {
          showErrorNotification('Not authorized to delete this ICP');
        } else {
          showErrorNotification(error.response?.data?.detail || 'Failed to delete ICP');
        }
      } finally {
        setDeleting(null);
      }
    }
  };
  
  // Render loading state
  if (loading && !service) {
    return (
      <Box sx={{ py: 3, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box sx={{ py: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton 
            color="primary" 
            onClick={() => navigate('/services')}
            sx={{ mr: 1 }}
          >
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4">
            <PersonIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Ideal Customer Profiles
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={refreshData}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleGenerateICPs}
          >
            Generate New ICPs
          </Button>
        </Box>
      </Box>
      
      {service && (
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <BusinessIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Service: {service.name || 'Unnamed Service'}
            </Typography>

            <Typography variant="body2" color="textSecondary" paragraph>
              {service.description || 'No description available'}
            </Typography>
          </CardContent>
        </Card>
      )}
      
      {icps.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 5 }}>
            <PersonIcon sx={{ fontSize: 60, color: 'primary.light', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No ICPs Generated Yet
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
              Generate Ideal Customer Profiles (ICPs) to create targeted content and campaigns for your service.
              ICPs help you understand your customers better and create more effective marketing strategies.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleGenerateICPs}
            >
              Generate Your First ICP
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {icps.map((icp) => (
            <Grid item xs={12} md={6} key={icp.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {icp.name || 'Unnamed ICP'}
                  </Typography>

                  <Typography variant="body2" color="textSecondary" paragraph>
                    {icp.description || 'No description available'}
                  </Typography>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 1.5 }}>
                        <Typography variant="subtitle2" color="textSecondary">
                          Industry
                        </Typography>
                        <Typography variant="body2">
                          {icp.demographics?.industry || 'Not specified'}
                        </Typography>
                      </Paper>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 1.5 }}>
                        <Typography variant="subtitle2" color="textSecondary">
                          Company Size
                        </Typography>
                        <Typography variant="body2">
                          {icp.demographics?.company_size || 'Not specified'}
                        </Typography>
                      </Paper>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 1.5 }}>
                        <Typography variant="subtitle2" color="textSecondary">
                          Decision Maker
                        </Typography>
                        <Typography variant="body2">
                          {icp.decision_maker?.title || 'Not specified'}
                          {icp.decision_maker?.title && icp.decision_maker?.department ? ' in ' : ''}
                          {icp.decision_maker?.department || ''}
                        </Typography>
                      </Paper>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 1.5 }}>
                        <Typography variant="subtitle2" color="textSecondary">
                          Experience
                        </Typography>
                        <Typography variant="body2">
                          {icp.decision_maker?.years_of_experience || 'Not specified'}
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                  
                  {icp.pain_points && icp.pain_points.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        Pain Points:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {icp.pain_points.slice(0, 3).map((pain, index) => (
                          <Chip
                            key={index}
                            label={pain.description || 'No description'}
                            size="small"
                            color={pain.severity === 'High' ? 'error' : 'default'}
                          />
                        ))}
                        {icp.pain_points.length > 3 && (
                          <Chip 
                            label={`+${icp.pain_points.length - 3} more`} 
                            size="small" 
                            variant="outlined"
                          />
                        )}
                      </Box>
                    </Box>
                  )}
                  
                  {icp.content_preferences && icp.content_preferences.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        Content Preferences:
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {icp.content_preferences.map((pref, index) => (
                          <Chip
                            key={index}
                            label={`${pref.content_type || 'Unknown'} (${pref.tone || 'No tone'})`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </CardContent>
                
                <Divider />
                
                <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
                  <Tooltip title="Delete ICP">
                    <IconButton
                      color="error"
                      onClick={() => handleDeleteICP(icp.id)}
                      disabled={deleting === icp.id}
                    >
                      {deleting === icp.id ? (
                        <CircularProgress size={20} color="error" />
                      ) : (
                        <DeleteIcon />
                      )}
                    </IconButton>
                  </Tooltip>
                  
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => handleCreateCampaign(icp.id)}
                    startIcon={<CampaignIcon />}
                  >
                    Create Campaign
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

export default ICPList;

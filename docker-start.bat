REM @since 2024-1-1 to 2025-25-7
@echo off
REM ACE Social Platform - Quick Docker Start
REM Simple batch file for Windows users

echo.
echo ========================================
echo   ACE Social Platform - Docker Setup
echo ========================================
echo.

REM Check if Docker Desktop is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Desktop is not running!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

echo Docker Desktop is running...
echo.

REM Menu for deployment options
echo Select deployment option:
echo 1. Development (Quick Start)
echo 2. Production (Full Setup)
echo 3. Build and Start Development
echo 4. View Logs
echo 5. Stop All Services
echo 6. Clean and Rebuild
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto dev
if "%choice%"=="2" goto prod
if "%choice%"=="3" goto build_dev
if "%choice%"=="4" goto logs
if "%choice%"=="5" goto stop
if "%choice%"=="6" goto clean
goto invalid

:dev
echo.
echo Starting Development Environment...
docker-compose up -d
goto success

:prod
echo.
echo Starting Production Environment...
echo Setting environment variables...
set SECRET_KEY=aceo-prod-secret-%RANDOM%
set MONGODB_URL=***************************************************
set REDIS_URL=redis://:redis123@redis:6379/0
set JWT_SECRET_KEY=jwt-secret-%RANDOM%
docker-compose -f docker-compose.prod.yml up -d
goto success

:build_dev
echo.
echo Building and Starting Development Environment...
docker-compose build --no-cache
docker-compose up -d
goto success

:logs
echo.
echo Showing service logs (Press Ctrl+C to exit)...
docker-compose logs -f
goto end

:stop
echo.
echo Stopping all services...
docker-compose down
docker-compose -f docker-compose.prod.yml down
echo Services stopped.
goto end

:clean
echo.
echo Cleaning up containers and rebuilding...
docker-compose down --volumes --remove-orphans
docker-compose -f docker-compose.prod.yml down --volumes --remove-orphans
docker system prune -f
echo Cleanup completed. Starting fresh build...
docker-compose build --no-cache
docker-compose up -d
goto success

:success
echo.
echo ========================================
echo   Deployment Successful!
echo ========================================
echo.
echo Application URLs:
echo   Backend API: http://localhost:8000
echo   API Docs: http://localhost:8000/docs
echo   Health Check: http://localhost:8000/health-minimal
echo   Frontend: http://localhost:3001
echo.
echo Useful Commands:
echo   View logs: docker-compose logs -f
echo   Stop services: docker-compose down
echo   Restart: docker-compose restart
echo.
echo Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Health check
echo Checking backend health...
curl -f http://localhost:8000/health-minimal >nul 2>&1
if %errorlevel% equ 0 (
    echo Backend is healthy!
) else (
    echo Backend health check failed - services may still be starting
)

echo.
echo Service Status:
docker-compose ps
goto end

:invalid
echo Invalid choice. Please run the script again.
goto end

:end
echo.
pause

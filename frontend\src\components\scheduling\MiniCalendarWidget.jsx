/**
 * Enhanced Mini Calendar Widget - Enterprise-grade calendar management component
 * Features: Comprehensive calendar management, real-time scheduling monitoring, plan-based limitations,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced calendar capabilities and ACEO add-on marketplace integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Popover,
  Chip,
  alpha,
  useMediaQuery,
  CircularProgress,
  Alert,
  Snackbar,
  LinearProgress
} from '@mui/material';
import {
  DateCalendar,
  TimePicker,
  LocalizationProvider
} from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  format,
  isSameDay,
  addDays,
  addMonths,
  setHours,
  setMinutes
} from 'date-fns';
import {
  Today as TodayIcon,
  AccessTime as AccessTimeIcon,
  Event as EventIcon,
  CalendarToday as CalendarTodayIcon,
  CalendarMonth as CalendarMonthIcon,
  Bolt as BoltIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';

import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Calendar display modes with enhanced configurations
const CALENDAR_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Calendar',
    description: 'Basic calendar interface',
    features: ['basic_calendar', 'date_selection', 'calendar_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Calendar',
    description: 'Comprehensive calendar management',
    features: ['detailed_calendar', 'calendar_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Calendar',
    description: 'AI-powered calendar optimization and suggestions',
    features: ['ai_assisted', 'ai_optimization', 'calendar_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Calendar',
    description: 'Advanced calendar analytics and forecasting',
    features: ['analytics_calendar', 'calendar_insights']
  }
};

/**
 * Enhanced Mini Calendar Widget Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Date} [props.selectedDate] - Currently selected date
 * @param {Function} props.onChange - Date change callback
 * @param {Function} [props.onOptimalTimeSelect] - Optimal time selection callback
 * @param {boolean} [props.showOptimalTimes=true] - Show optimal times feature
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onCalendarAction] - Calendar action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-mini-calendar-widget'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const MiniCalendarWidget = memo(forwardRef(({
  selectedDate,
  onChange,
  onOptimalTimeSelect,
  showOptimalTimes = true,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onCalendarAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-mini-calendar-widget',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const calendarRef = useRef(null);
  const [calendarAnchorEl, setCalendarAnchorEl] = useState(null);
  const [timePickerAnchorEl, setTimePickerAnchorEl] = useState(null);
  const [optimalTimesAnchorEl, setOptimalTimesAnchorEl] = useState(null);
  const [optimalTimes, setOptimalTimes] = useState(null);
  const [loadingOptimalTimes, setLoadingOptimalTimes] = useState(false);

  // Enhanced state management
  const [calendarMode, setCalendarMode] = useState('compact');
  const [calendarHistory, setCalendarHistory] = useState([]);
  const [calendarAnalytics, setCalendarAnalytics] = useState(null);
  const [calendarInsights, setCalendarInsights] = useState(null);
  const [customCalendarConfigs, setCustomCalendarConfigs] = useState([]);
  const [calendarPreferences, setCalendarPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    calendarSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [calendarDrawerOpen, setCalendarDrawerOpen] = useState(false);
  const [selectedCalendarType, setSelectedCalendarType] = useState(null);
  const [calendarStats, setCalendarStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [calendarScore, setCalendarScore] = useState(0);
  const [calendarProgress, setCalendarProgress] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [calendarVersions, setCalendarVersions] = useState([]);
  const [conflictDetection, setConflictDetection] = useState(null);
  const [optimizationSuggestions, setOptimizationSuggestions] = useState([]);
  const [scheduledEvents, setScheduledEvents] = useState([]);
  const [calendarView, setCalendarView] = useState('month'); // month, week, day
  const [navigationDate, setNavigationDate] = useState(new Date());

  /**
   * Enhanced calendar features - All features enabled without plan limitations
   */
  const calendarFeatures = useMemo(() => {
    return {
      maxCalendarMonths: -1, // Unlimited
      maxScheduledEvents: -1, // Unlimited
      maxDailySchedules: -1, // Unlimited
      hasAdvancedCalendar: true,
      hasCalendarAnalytics: true,
      hasCustomCalendarConfigs: true,
      hasCalendarInsights: true,
      hasCalendarHistory: true,
      hasAIAssistance: true,
      hasCalendarExport: true,
      hasCalendarAutomation: true,
      hasAnalytics: true,
      hasExport: true,
      trackingLevel: 'full',
      refreshInterval: 1000,
      planName: 'Enhanced',
      planTier: 3,
      allowedCalendarTypes: ['basic_calendar', 'basic_scheduling', 'advanced_calendar', 'calendar_analytics', 'optimal_scheduling', 'smart_calendar', 'calendar_automation', 'custom_calendar'],
      maxHistoryDays: -1, // Unlimited
      hasConflictDetection: true,
      hasOptimizationSuggestions: true,
      hasMultiMonthView: true,
      hasFeatureAccess: () => true,
      isWithinLimits: () => true,
      canUseFeature: () => true,
      canUseCalendarType: () => true
    };
  }, []);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'application',
      'aria-label': ariaLabel || `Calendar widget with ${calendarFeatures.planName} features`,
      'aria-description': ariaDescription || `Calendar interface with ${calendarFeatures.trackingLevel} tracking`,
      tabIndex: -1
    };
  }, [ariaLabel, ariaDescription, calendarFeatures.planName, calendarFeatures.trackingLevel]);



  /**
   * Enhanced imperative handle for parent component access with comprehensive calendar API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getCalendarHistory: () => calendarHistory,
    getCalendarAnalytics: () => calendarAnalytics,
    getCalendarInsights: () => calendarInsights,
    refreshCalendar: () => {
      fetchCalendarAnalytics();
      if (onRefresh) onRefresh();
    },

    // Calendar methods
    focusCalendar: () => {
      if (calendarRef.current) {
        calendarRef.current.focus();
      }
    },
    getCalendarScore: () => calendarScore,
    getCalendarProgress: () => calendarProgress,
    getScheduledEvents: () => scheduledEvents,
    getConflictDetection: () => conflictDetection,
    getOptimizationSuggestions: () => optimizationSuggestions,
    openCalendarDrawer: () => setCalendarDrawerOpen(true),
    closeCalendarDrawer: () => setCalendarDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportCalendarData: () => {
      if (onExport) {
        onExport(calendarHistory, calendarAnalytics);
      }
    },

    // Accessibility methods
    announceCalendar: (message) => announceToScreenReader(message),
    focusCalendarField: () => setFocusToElement('calendar-widget-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => calendarMode,
    getCalendarStats: () => calendarStats,
    getSelectedCalendarType: () => selectedCalendarType,
    getCustomCalendarConfigs: () => customCalendarConfigs,
    getCalendarDrawerOpen: () => calendarDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    getActiveTab: () => activeTab,
    setActiveTab: (tab) => setActiveTab(tab),
    addCustomCalendarConfig,
    handleCalendarModeChange,
    updateCalendarPreferences,
    handleCalendarTypeSelection,
    validateCalendarConfig,
    getCalendarVersions: () => calendarVersions,
    switchToVersion: (versionId) => switchToCalendarVersion(versionId),
    detectConflicts: () => handleConflictDetection(),
    optimizeScheduling: () => handleSchedulingOptimization(),
    navigateToDate: (date) => setNavigationDate(date),
    changeView: (view) => setCalendarView(view)
  }), [
    calendarHistory,
    calendarAnalytics,
    calendarInsights,
    calendarStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    calendarMode,
    selectedCalendarType,
    customCalendarConfigs,
    calendarScore,
    calendarProgress,
    scheduledEvents,
    conflictDetection,
    optimizationSuggestions,
    calendarVersions,
    addCustomCalendarConfig,
    handleCalendarModeChange,
    updateCalendarPreferences,
    handleCalendarTypeSelection,
    validateCalendarConfig,
    switchToCalendarVersion,
    handleConflictDetection,
    handleSchedulingOptimization,
    activeTab,
    fullscreenMode,
    calendarDrawerOpen,
    showAnalytics,
    fetchCalendarAnalytics
  ]);

  // Quick date options - all options available
  const quickDateOptions = useMemo(() => {
    const baseOptions = [
      { label: 'Today', date: new Date(), icon: <TodayIcon fontSize="small" /> },
      { label: 'Tomorrow', date: addDays(new Date(), 1), icon: <ArrowForwardIcon fontSize="small" /> },
      { label: 'Next Week', date: addDays(new Date(), 7), icon: <CalendarTodayIcon fontSize="small" /> },
      { label: 'Next Month', date: addMonths(new Date(), 1), icon: <CalendarMonthIcon fontSize="small" /> }
    ];

    return baseOptions;
  }, []);

  // Fetch calendar analytics with enhanced error handling and retry logic
  const fetchCalendarAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/calendar/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setCalendarAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (calendarPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Calendar analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch calendar analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load calendar analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, calendarPreferences.showAnalytics]);

  // Handle calendar mode switching
  const handleCalendarModeChange = useCallback((newMode) => {
    if (CALENDAR_MODES[newMode.toUpperCase()]) {
      setCalendarMode(newMode);
      announceToScreenReader(`Calendar mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setCalendarHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (calendarPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} calendar mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, calendarPreferences.showAnalytics, showSuccess]);

  // Handle custom calendar config management
  const addCustomCalendarConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomCalendarConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCalendarHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (calendarPreferences.showAnalytics) {
      showSuccess(`Custom calendar config "${configData.name}" created`);
    }
  }, [subscription?.user_id, calendarPreferences.showAnalytics, showSuccess]);

  // Handle calendar preferences updates
  const updateCalendarPreferences = useCallback((newPreferences) => {
    setCalendarPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCalendarHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (calendarPreferences.showAnalytics) {
      showSuccess('Calendar preferences updated');
    }
  }, [subscription?.user_id, calendarPreferences.showAnalytics, showSuccess]);

  // Handle calendar type selection
  const handleCalendarTypeSelection = useCallback((calendarType) => {
    setSelectedCalendarType(calendarType);

    // Track calendar type selection
    const typeRecord = {
      id: Date.now(),
      type: 'calendar_type_selected',
      calendarType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCalendarHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (calendarPreferences.showAnalytics) {
      announceToScreenReader(`Selected calendar type: ${calendarType}`);
    }
  }, [subscription?.user_id, calendarPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateCalendarConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Calendar type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Switch to calendar version
  const switchToCalendarVersion = useCallback((versionId) => {
    const version = calendarVersions.find(v => v.id === versionId);
    if (version) {
      setCalendarScore(version.score || 0);

      if (calendarPreferences.showAnalytics) {
        showSuccess(`Switched to version ${version.name}`);
      }
    }
  }, [calendarVersions, calendarPreferences.showAnalytics, showSuccess]);

  // Handle conflict detection - always available
  const handleConflictDetection = useCallback(async () => {
    try {
      const response = await api.post('/api/calendar/detect-conflicts', {
        selectedDate,
        scheduledEvents
      });

      setConflictDetection(response.data);

      if (response.data.conflicts?.length > 0) {
        showError(`${response.data.conflicts.length} scheduling conflicts detected`);
      } else {
        showSuccess('No scheduling conflicts detected');
      }
    } catch (error) {
      console.error('Conflict detection failed:', error);
      showError('Failed to detect conflicts');
    }
  }, [selectedDate, scheduledEvents, showError, showSuccess]);

  // Handle scheduling optimization - always available
  const handleSchedulingOptimization = useCallback(async () => {
    try {
      const response = await api.post('/api/calendar/optimize', {
        selectedDate,
        preferences: calendarPreferences
      });

      setOptimizationSuggestions(response.data.suggestions || []);

      if (response.data.suggestions?.length > 0) {
        showSuccess(`${response.data.suggestions.length} optimization suggestions generated`);
      } else {
        showSuccess('Current scheduling is already optimized');
      }
    } catch (error) {
      console.error('Optimization failed:', error);
      showError('Failed to generate optimization suggestions');
    }
  }, [selectedDate, calendarPreferences, showError, showSuccess]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && selectedDate) {
      // Optimize calendar based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchCalendarAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, selectedDate, fetchCalendarAnalytics]);

  // Generate AI suggestions when calendar changes
  useEffect(() => {
    if (enableAIInsights && calendarPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, calendarPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      setCalendarProgress(25); // Start progress

      const response = await api.get('/api/calendar/ai-suggestions', {
        params: {
          selectedDate: selectedDate?.toISOString(),
          planTier: calendarFeatures.planTier
        }
      });

      setCalendarProgress(75); // Update progress
      setAiSuggestions(response.data.suggestions || []);
      setCalendarInsights(response.data.insights || null);

      // Create a new version for this suggestion set
      const newVersion = {
        id: Date.now(),
        name: `AI Calendar v${Date.now()}`,
        suggestions: response.data.suggestions || [],
        createdAt: new Date().toISOString()
      };
      setCalendarVersions(prev => [newVersion, ...prev.slice(0, 4)]); // Keep last 5 versions

      // Update scheduled events if provided
      if (response.data.scheduledEvents) {
        setScheduledEvents(response.data.scheduledEvents);
      }

      setCalendarProgress(100); // Complete progress

      if (calendarPreferences.showAnalytics) {
        showSuccess('AI calendar suggestions generated');
      }

      // Reset progress after delay
      setTimeout(() => setCalendarProgress(0), 2000);
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
      setCalendarProgress(0);
    }
  }, [selectedDate, calendarFeatures.planTier, calendarPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when calendar changes
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchCalendarStats();
    }
  }, [enableAdvancedFeatures, fetchCalendarStats]);

  // Fetch calendar stats function
  const fetchCalendarStats = useCallback(async () => {
    try {
      const response = await api.get('/api/calendar/stats');
      setCalendarStats(response.data);
    } catch (error) {
      console.error('Failed to fetch calendar stats:', error);
    }
  }, []);

  // Common time slots
  const commonTimeSlots = [
    { label: '9:00 AM', hours: 9, minutes: 0 },
    { label: '12:00 PM', hours: 12, minutes: 0 },
    { label: '3:00 PM', hours: 15, minutes: 0 },
    { label: '6:00 PM', hours: 18, minutes: 0 },
  ];

  // Fetch optimal posting times function
  const fetchOptimalTimes = useCallback(async () => {
    try {
      setLoadingOptimalTimes(true);
      const response = await api.get('/api/posting-time/recommendations');
      setOptimalTimes(response.data);
    } catch (error) {
      console.error('Error fetching optimal posting times:', error);
      showError('Failed to fetch optimal posting times');
    } finally {
      setLoadingOptimalTimes(false);
    }
  }, [showError]);

  // Fetch optimal posting times
  useEffect(() => {
    if (showOptimalTimes) {
      fetchOptimalTimes();
    }
  }, [showOptimalTimes, fetchOptimalTimes]);
  
  // Handle calendar open/close
  const handleCalendarOpen = (event) => {
    setCalendarAnchorEl(event.currentTarget);
  };
  
  const handleCalendarClose = () => {
    setCalendarAnchorEl(null);
  };
  
  // Handle time picker open/close
  const handleTimePickerOpen = (event) => {
    setTimePickerAnchorEl(event.currentTarget);
  };
  
  const handleTimePickerClose = () => {
    setTimePickerAnchorEl(null);
  };
  
  // Handle optimal times open/close
  const handleOptimalTimesOpen = (event) => {
    setOptimalTimesAnchorEl(event.currentTarget);
  };
  
  const handleOptimalTimesClose = () => {
    setOptimalTimesAnchorEl(null);
  };
  
  // Handle date selection
  const handleDateChange = useCallback((newDate) => {
    // Preserve the time from the current selection
    const hours = selectedDate ? selectedDate.getHours() : new Date().getHours();
    const minutes = selectedDate ? selectedDate.getMinutes() : new Date().getMinutes();

    const updatedDate = setMinutes(setHours(newDate, hours), minutes);
    onChange(updatedDate);
    handleCalendarClose();

    // Track date change in history
    const dateChangeRecord = {
      id: Date.now(),
      type: 'date_changed',
      date: updatedDate.toISOString(),
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCalendarHistory(prev => [dateChangeRecord, ...prev.slice(0, 99)]);

    // Call the calendar action callback if provided
    if (onCalendarAction) {
      onCalendarAction('date_selected', { date: updatedDate, source: 'calendar' });
    }

    // Always check for conflicts since all features are available
    handleConflictDetection();

    announceToScreenReader(`Date selected: ${format(updatedDate, 'EEEE, MMMM d, yyyy')}`);
  }, [selectedDate, onChange, subscription?.user_id, onCalendarAction, handleConflictDetection, announceToScreenReader]);
  
  // Handle time selection
  const handleTimeChange = (newTime) => {
    onChange(newTime);
    handleTimePickerClose();
  };
  
  // Handle quick date selection
  const handleQuickDateSelect = (date) => {
    // Preserve the time from the current selection
    const hours = selectedDate ? selectedDate.getHours() : new Date().getHours();
    const minutes = selectedDate ? selectedDate.getMinutes() : new Date().getMinutes();
    
    const updatedDate = setMinutes(setHours(date, hours), minutes);
    onChange(updatedDate);
  };
  
  // Handle common time slot selection
  const handleTimeSlotSelect = (hours, minutes) => {
    const updatedDate = setMinutes(setHours(selectedDate || new Date(), hours), minutes);
    onChange(updatedDate);
  };
  
  // Handle optimal time selection
  const handleOptimalTimeSelect = (day, timeSlot) => {
    // Extract hours from time slot (e.g., "08:00-09:59" -> 8)
    const startHour = parseInt(timeSlot.split(':')[0], 10);
    
    // Get current date
    const now = new Date();
    
    // Calculate target day (0 = Sunday, 6 = Saturday in JavaScript)
    const dayMap = {
      'Monday': 1,
      'Tuesday': 2,
      'Wednesday': 3,
      'Thursday': 4,
      'Friday': 5,
      'Saturday': 6,
      'Sunday': 0
    };
    
    const targetDay = dayMap[day];
    const currentDay = now.getDay();
    
    // Calculate days to add
    let daysToAdd = targetDay - currentDay;
    if (daysToAdd <= 0) {
      daysToAdd += 7; // Move to next week if target day is today or earlier
    }
    
    // Create target date
    const targetDate = new Date(now);
    targetDate.setDate(now.getDate() + daysToAdd);
    targetDate.setHours(startHour, 0, 0, 0);
    
    onChange(targetDate);
    
    if (onOptimalTimeSelect) {
      onOptimalTimeSelect(targetDate, day, timeSlot);
    }
    
    handleOptimalTimesClose();
  };
  
  return (
    <Box
      {...getAccessibilityProps()}
      ref={calendarRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >


      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          display: 'flex',
          alignItems: 'center',
          borderRadius: 2,
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          backgroundColor: alpha(ACE_COLORS.WHITE, 0.95),
          '&:hover': {
            borderColor: ACE_COLORS.PURPLE,
            boxShadow: `0 0 0 2px ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
          },
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
          <EventIcon sx={{ color: ACE_COLORS.PURPLE, mr: 1 }} />
          <Box>
            <Typography variant="body2" sx={{ color: alpha(ACE_COLORS.DARK, 0.7) }}>
              Scheduled for
            </Typography>
            <Typography variant="body1" fontWeight="medium" sx={{ color: ACE_COLORS.DARK }}>
              {selectedDate
                ? format(selectedDate, 'EEEE, MMMM d, yyyy h:mm a')
                : 'Not scheduled'}
            </Typography>
            <Chip
              label={calendarFeatures.planName}
              size="small"
              sx={{
                mt: 0.5,
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                fontWeight: 'bold',
                fontSize: '0.7rem'
              }}
            />
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="Select date">
            <IconButton size="small" onClick={handleCalendarOpen}>
              <CalendarTodayIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Select time">
            <IconButton size="small" onClick={handleTimePickerOpen}>
              <AccessTimeIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          {showOptimalTimes && (
            <Tooltip title="Optimal posting times">
              <IconButton
                size="small"
                onClick={handleOptimalTimesOpen}
                sx={{
                  color: ACE_COLORS.YELLOW,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                  },
                }}
              >
                <BoltIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Paper>
      
      <Box sx={{ mt: 1.5 }}>
        <Typography variant="body2" color="textSecondary" gutterBottom>
          Quick schedule
        </Typography>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {quickDateOptions.map((option) => (
            <Chip
              key={option.label}
              label={option.label}
              icon={option.icon}
              onClick={() => handleQuickDateSelect(option.date)}
              sx={{
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                },
                ...(selectedDate && isSameDay(selectedDate, option.date) && {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE,
                  fontWeight: 'medium',
                }),
              }}
            />
          ))}
        </Box>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
          {commonTimeSlots.map((slot) => (
            <Chip
              key={slot.label}
              label={slot.label}
              size="small"
              onClick={() => handleTimeSlotSelect(slot.hours, slot.minutes)}
              sx={{
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                },
                ...(selectedDate &&
                  selectedDate.getHours() === slot.hours &&
                  selectedDate.getMinutes() === slot.minutes && {
                    backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                    color: ACE_COLORS.YELLOW,
                    fontWeight: 'medium',
                  }),
              }}
            />
          ))}
        </Box>
      </Box>
      
      {/* Date Picker Popover */}
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Popover
          open={Boolean(calendarAnchorEl)}
          anchorEl={calendarAnchorEl}
          onClose={handleCalendarClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
        >
          {/* Calendar View Controls - Always available */}
          {calendarFeatures.hasMultiMonthView && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1, gap: 1 }}>
              <Chip
                label="Month"
                size="small"
                variant={calendarView === 'month' ? 'filled' : 'outlined'}
                onClick={() => setCalendarView('month')}
                sx={{
                  bgcolor: calendarView === 'month' ? alpha(ACE_COLORS.PURPLE, 0.1) : 'transparent',
                  color: calendarView === 'month' ? ACE_COLORS.PURPLE : ACE_COLORS.DARK
                }}
              />
              <Typography variant="caption" sx={{ color: ACE_COLORS.DARK, alignSelf: 'center' }}>
                {format(navigationDate, 'MMMM yyyy')}
              </Typography>
            </Box>
          )}

          <DateCalendar
            value={selectedDate || null}
            onChange={handleDateChange}
            referenceDate={navigationDate}
            onMonthChange={(newDate) => setNavigationDate(newDate)}
            disablePast
            sx={{
              '& .MuiPickersDay-root': {
                borderRadius: 1,
              },
              '& .MuiPickersDay-today': {
                border: `2px solid ${ACE_COLORS.PURPLE}`,
              },
              '& .Mui-selected': {
                backgroundColor: `${ACE_COLORS.PURPLE} !important`,
                color: ACE_COLORS.WHITE,
              },
            }}
          />
        </Popover>
        
        {/* Time Picker Popover */}
        <Popover
          open={Boolean(timePickerAnchorEl)}
          anchorEl={timePickerAnchorEl}
          onClose={handleTimePickerClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
        >
          <Box sx={{ p: 2, width: 240 }}>
            <TimePicker
              label="Select Time"
              value={selectedDate || null}
              onChange={handleTimeChange}
              sx={{ width: '100%' }}
            />
          </Box>
        </Popover>
      </LocalizationProvider>
      
      {/* Optimal Times Popover */}
      <Popover
        open={Boolean(optimalTimesAnchorEl)}
        anchorEl={optimalTimesAnchorEl}
        onClose={handleOptimalTimesClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        slotProps={{
          paper: {
            sx: {
              width: isMobile ? 280 : 320,
              maxHeight: isMobile ? 350 : 400,
              overflow: 'auto',
              backgroundColor: alpha(ACE_COLORS.WHITE, 0.95),
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
            }
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <BoltIcon sx={{ color: ACE_COLORS.YELLOW, mr: 1 }} />
            Optimal Posting Times
          </Typography>
          
          {loadingOptimalTimes ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          ) : !optimalTimes ? (
            <Typography variant="body2" color="textSecondary">
              No optimal time data available
            </Typography>
          ) : (
            <Box>
              {Object.entries(optimalTimes.combined_recommendations).map(([day, slots]) => (
                <Box key={day} sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {day}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {slots.map((slot, index) => (
                      <Chip
                        key={index}
                        label={slot.time_slot}
                        size="small"
                        onClick={() => handleOptimalTimeSelect(day, slot.time_slot)}
                        sx={{
                          borderRadius: 1,
                          backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                          color: ACE_COLORS.DARK,
                          '&:hover': {
                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2),
                          },
                        }}
                      />
                    ))}
                  </Box>
                </Box>
              ))}
            </Box>
          )}
        </Box>
      </Popover>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying calendar sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}

      {/* Calendar Progress Indicator */}
      {calendarProgress > 0 && calendarProgress < 100 && (
        <Box sx={{
          position: 'fixed',
          bottom: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          p: 2,
          borderRadius: 2,
          bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          zIndex: 9999,
          minWidth: 200
        }}>
          <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
            Processing calendar...
          </Typography>
          <LinearProgress
            variant="determinate"
            value={calendarProgress}
            sx={{
              '& .MuiLinearProgress-bar': {
                backgroundColor: ACE_COLORS.PURPLE
              }
            }}
          />
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
MiniCalendarWidget.propTypes = {
  // Core props
  selectedDate: PropTypes.instanceOf(Date),
  onChange: PropTypes.func.isRequired,
  onOptimalTimeSelect: PropTypes.func,
  showOptimalTimes: PropTypes.bool,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onCalendarAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

MiniCalendarWidget.displayName = 'MiniCalendarWidget';

export default MiniCalendarWidget;

/**
 * Subscription Context
 * Production-ready subscription management with real-time updates, billing integration
 * Comprehensive error handling, monitoring, and advanced subscription features
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './AuthContext';
import { useNotification } from '../hooks/useNotification';
import api from '../api';

// Configuration constants
const CONFIG = {
  // Environment detection
  IS_LOCALHOST: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',

  // WebSocket settings
  WS_RECONNECT_DELAY: 5000, // 5 seconds
  WS_MAX_RETRIES: 3,

  // Cache settings
  CACHE_DURATION: 300000, // 5 minutes

  // Usage alert thresholds
  USAGE_WARNING_THRESHOLD: 0.8, // 80%
  USAGE_CRITICAL_THRESHOLD: 0.95, // 95%
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[SubscriptionContext] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[SubscriptionContext] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Subscription Context Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[SubscriptionContext] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Subscription Context Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[SubscriptionContext] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Subscription Context Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const SubscriptionContext = createContext();

// Custom hook to use subscription context
// eslint-disable-next-line react-refresh/only-export-components
export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

export const SubscriptionProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const { showSuccessNotification, showWarningNotification, showErrorNotification } = useNotification();

  // Core subscription state
  const [subscription, setSubscription] = useState(null);
  const [featureLimits, setFeatureLimits] = useState(null);
  const [usage, setUsage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Advanced state management
  const [billingHistory, setBillingHistory] = useState([]);
  const [creditBalance, setCreditBalance] = useState(0);
  const [usageAlerts, setUsageAlerts] = useState([]);
  const [trialInfo, setTrialInfo] = useState(null);
  const [webhookEvents, setWebhookEvents] = useState([]);
  const [subscriptionMetrics, setSubscriptionMetrics] = useState(null);
  const [pendingUpgrade, setPendingUpgrade] = useState(null);
  const [dunningStatus, setDunningStatus] = useState(null);

  // Real-time updates
  const wsRef = useRef(null);
  const [realTimeUpdates, setRealTimeUpdates] = useState(true);

  // Enhanced subscription data loading with comprehensive error handling
  const loadSubscription = useCallback(async () => {
    if (!isAuthenticated || !user) {
      resetSubscriptionState();
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Parallel data fetching for better performance
      const [
        subscriptionResponse,
        usageResponse,
        billingHistoryResponse,
        creditBalanceResponse,
        trialInfoResponse,
        metricsResponse
      ] = await Promise.allSettled([
        api.get('/api/billing/subscription'),
        api.get('/api/billing/usage'),
        api.get('/api/billing/history'),
        api.get('/api/billing/credits'),
        api.get('/api/billing/trial'),
        api.get('/api/billing/metrics')
      ]);

      // Process subscription data
      if (subscriptionResponse.status === 'fulfilled') {
        const subscriptionData = subscriptionResponse.value.data;
        setSubscription(subscriptionData);
        setFeatureLimits(subscriptionData.feature_limits || {});

        // Check for trial status
        if (subscriptionData.is_trial) {
          setTrialInfo({
            isActive: true,
            endsAt: subscriptionData.trial_ends_at,
            daysRemaining: calculateTrialDaysRemaining(subscriptionData.trial_ends_at)
          });
        }
      }

      // Process usage data
      if (usageResponse.status === 'fulfilled') {
        const usageData = usageResponse.value.data;
        setUsage(usageData);
        checkUsageAlerts(usageData);
      }

      // Process billing history
      if (billingHistoryResponse.status === 'fulfilled') {
        setBillingHistory(billingHistoryResponse.value.data.invoices || []);
      }

      // Process credit balance
      if (creditBalanceResponse.status === 'fulfilled') {
        setCreditBalance(creditBalanceResponse.value.data.balance || 0);
      }

      // Process trial information
      if (trialInfoResponse.status === 'fulfilled') {
        setTrialInfo(trialInfoResponse.value.data);
      }

      // Process subscription metrics
      if (metricsResponse.status === 'fulfilled') {
        setSubscriptionMetrics(metricsResponse.value.data);
      }

      // Initialize real-time updates
      if (realTimeUpdates) {
        initializeWebSocket();
      }

    } catch (error) {
      logger.error('Error loading subscription', error);
      setError(error.message);
      showErrorNotification('Failed to load subscription data');

      // Fallback to default free plan
      setDefaultSubscriptionState();
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user, realTimeUpdates, showErrorNotification, checkUsageAlerts, initializeWebSocket]);

  // Helper functions
  const resetSubscriptionState = () => {
    setSubscription(null);
    setFeatureLimits(null);
    setUsage(null);
    setBillingHistory([]);
    setCreditBalance(0);
    setUsageAlerts([]);
    setTrialInfo(null);
    setSubscriptionMetrics(null);
    setPendingUpgrade(null);
    setDunningStatus(null);
    setLoading(false);
  };

  const setDefaultSubscriptionState = () => {
    // Check if we're in development/localhost environment
    const isLocalhost = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname.includes('localhost');

    // For localhost development, provide Dominator plan access
    const planId = isLocalhost ? 'dominator' : 'accelerator';
    const planName = CONFIG.IS_LOCALHOST ? 'Dominator' : 'Accelerator';

    logger.info(`Setting default subscription state for ${CONFIG.IS_LOCALHOST ? 'LOCALHOST (Dominator)' : 'PRODUCTION (Accelerator)'}`);

    setSubscription({
      plan_id: planId,
      plan_name: planName,
      status: isLocalhost ? 'active' : 'trialing',
      features: [],
      is_trial: !isLocalhost
    });

    setFeatureLimits({
      monthly_posts: isLocalhost ? 1000 : 200,
      ai_auto_replies: isLocalhost ? 5000 : 1000,
      regeneration_credits: isLocalhost ? 500 : 100,
      document_training: isLocalhost ? 50 : 10,
      policy_compliance_checking: true,
      brand_voice_consistency: true,
      white_label_ai_responses: isLocalhost ? true : false,
      multi_language_ai_support: isLocalhost ? true : false,
      user_accounts: isLocalhost ? 20 : 5,
      ab_test_variants: isLocalhost ? 10 : 3,
      sentiment_analysis: true,
      advanced_sentiment_analysis: isLocalhost ? true : false,
      sentiment_trend_analysis: isLocalhost ? true : false
    });

    setUsage({
      monthly_posts_used: 0,
      ai_auto_replies_used: 0,
      regeneration_credits_used: 0,
      regeneration_count_this_month: 0,
      documents_uploaded: 0
    });
  };

  const calculateTrialDaysRemaining = (trialEndDate) => {
    if (!trialEndDate) return 0;
    const endDate = new Date(trialEndDate);
    const now = new Date();
    const diffTime = endDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const checkUsageAlerts = useCallback((usageData) => {
    const alerts = [];

    if (!featureLimits) return;

    // Check for high usage (configurable threshold)
    Object.entries(usageData).forEach(([key, used]) => {
      const limitKey = key.replace('_used', '');
      const limit = featureLimits[limitKey];

      if (limit && used / limit >= CONFIG.USAGE_WARNING_THRESHOLD) {
        alerts.push({
          type: 'warning',
          feature: limitKey,
          message: `You've used ${Math.round((used / limit) * 100)}% of your ${limitKey.replace('_', ' ')} limit`,
          severity: used / limit >= CONFIG.USAGE_CRITICAL_THRESHOLD ? 'high' : 'medium'
        });
      }
    });

    setUsageAlerts(alerts);

    // Show notifications for critical alerts
    alerts.forEach(alert => {
      if (alert.severity === 'high') {
        showWarningNotification(alert.message);
      }
    });
  }, [featureLimits, showWarningNotification]);

  // WebSocket for real-time updates
  const initializeWebSocket = useCallback(() => {
    if (!user || wsRef.current) return;

    try {
      const wsUrl = `${process.env.REACT_APP_WS_URL || 'ws://localhost:8000'}/api/billing/ws/${user.id}`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        logger.info('Billing WebSocket connected');
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          logger.error('Error parsing WebSocket message', error);
        }
      };

      wsRef.current.onclose = () => {
        logger.info('Billing WebSocket disconnected');
        wsRef.current = null;

        // Attempt to reconnect after 5 seconds
        if (realTimeUpdates) {
          setTimeout(() => {
            initializeWebSocket();
          }, 5000);
        }
      };

      wsRef.current.onerror = (error) => {
        logger.error('Billing WebSocket error', error);
      };
    } catch (error) {
      logger.error('Error initializing WebSocket', error);
    }
  }, [user, realTimeUpdates, handleWebSocketMessage]);

  const handleWebSocketMessage = useCallback((data) => {
    switch (data.type) {
      case 'subscription_updated':
        setSubscription(data.subscription);
        setFeatureLimits(data.subscription.feature_limits || {});
        showSuccessNotification('Subscription updated successfully');
        break;

      case 'usage_updated':
        setUsage(data.usage);
        checkUsageAlerts(data.usage);
        break;

      case 'credit_balance_updated':
        setCreditBalance(data.balance);
        showSuccessNotification(`Credit balance updated: ${data.balance} credits`);
        break;

      case 'regeneration_credits_updated':
        setUsage(prev => ({
          ...prev,
          regeneration_credits_used: data.credits_used,
          regeneration_count_this_month: data.regeneration_count
        }));
        // Show notification only if credits were consumed
        if (data.credits_consumed > 0) {
          showSuccessNotification(`${data.credits_consumed} credit${data.credits_consumed > 1 ? 's' : ''} used. ${data.remaining_credits} remaining.`);
        }
        break;

      case 'trial_ending_soon':
        showWarningNotification(`Your trial ends in ${data.days_remaining} days`);
        break;

      case 'payment_failed':
        setDunningStatus(data.dunning_status);
        showErrorNotification('Payment failed. Please update your payment method.');
        break;

      case 'upgrade_completed':
        setPendingUpgrade(null);
        loadSubscription(); // Reload all data
        showSuccessNotification(`Successfully upgraded to ${data.new_plan}`);
        break;

      default:
        logger.warn('Unknown WebSocket message type', { type: data.type, data });
    }

    // Store webhook event for debugging
    setWebhookEvents(prev => [...prev.slice(-49), data]); // Keep last 50 events
  }, [checkUsageAlerts, showSuccessNotification, showErrorNotification, showWarningNotification, loadSubscription]);

  // Refresh subscription data
  const refreshSubscription = async () => {
    await loadSubscription();
  };

  // Check if user has access to a feature
  const hasFeatureAccess = (feature) => {
    // For localhost development, grant access to all features
    if (CONFIG.IS_LOCALHOST) {
      logger.debug(`Localhost detected - granting access to feature: ${feature}`);
      return true;
    }

    if (!subscription || !featureLimits) {
      logger.warn(`No subscription or feature limits for feature: ${feature}`);
      return false;
    }

    logger.debug(`Checking feature access for: ${feature}, plan: ${subscription.plan_id}`);

    switch (feature) {
      case 'ai_auto_replies':
        return featureLimits.ai_auto_replies > 0;
      case 'regeneration':
        return featureLimits.regeneration_credits > 0;
      case 'policy_compliance':
        return featureLimits.policy_compliance_checking === true;
      case 'document_training':
        return featureLimits.document_training > 0;
      case 'brand_voice_consistency':
        return featureLimits.brand_voice_consistency === true;
      case 'white_label_ai_responses':
        return featureLimits.white_label_ai_responses === true;
      case 'multi_language_ai_support':
        return featureLimits.multi_language_ai_support === true;
      case 'advanced_scheduling':
        return ['accelerator', 'dominator'].includes(subscription.plan_id);
      case 'team_collaboration':
        return featureLimits.user_accounts > 1;
      case 'ab_testing':
        return featureLimits.ab_test_variants > 0;
      case 'sentiment_analysis':
        return featureLimits.sentiment_analysis === true || ['accelerator', 'dominator'].includes(subscription.plan_id);
      case 'basic_sentiment_analysis':
        return true; // Available to all plans
      case 'advanced_sentiment_analysis':
        return featureLimits.advanced_sentiment_analysis === true || ['accelerator', 'dominator'].includes(subscription.plan_id);
      case 'sentiment_trend_analysis':
        return featureLimits.sentiment_trend_analysis === true || ['accelerator', 'dominator'].includes(subscription.plan_id);
      default:
        return true;
    }
  };

  // Check if user can perform an action based on usage limits
  const canPerformAction = (action, quantity = 1) => {
    if (!usage || !featureLimits) return false;

    // Dominator plan has unlimited regeneration credits
    if ((action === 'regeneration' || action === 'regeneration_credits') && subscription?.plan_id === 'dominator') {
      return true;
    }

    switch (action) {
      case 'create_post':
        return (usage.monthly_posts_used || 0) + quantity <= featureLimits.monthly_posts;
      case 'ai_auto_reply':
        return (usage.ai_auto_replies_used || 0) + quantity <= featureLimits.ai_auto_replies;
      case 'regeneration':
      case 'regeneration_credits':
        return (usage.regeneration_credits_used || 0) + quantity <= featureLimits.regeneration_credits;
      case 'upload_document':
        return (usage.documents_uploaded || 0) + quantity <= featureLimits.document_training;
      default:
        return true;
    }
  };

  // Get remaining credits for a specific action
  const getRemainingCredits = (action) => {
    if (!usage || !featureLimits) return 0;

    // Dominator plan has unlimited regeneration credits
    if ((action === 'regeneration' || action === 'regeneration_credits') && subscription?.plan_id === 'dominator') {
      return Infinity;
    }

    switch (action) {
      case 'regeneration':
      case 'regeneration_credits':
        return Math.max(0, featureLimits.regeneration_credits - (usage.regeneration_credits_used || 0));
      case 'create_post':
        return Math.max(0, featureLimits.monthly_posts - (usage.monthly_posts_used || 0));
      case 'ai_auto_reply':
        return Math.max(0, featureLimits.ai_auto_replies - (usage.ai_auto_replies_used || 0));
      case 'upload_document':
        return Math.max(0, featureLimits.document_training - (usage.documents_uploaded || 0));
      default:
        return Infinity;
    }
  };

  // Get remaining usage for a feature
  const getRemainingUsage = (feature) => {
    if (!usage || !featureLimits) return 0;

    switch (feature) {
      case 'monthly_posts':
        return Math.max(0, featureLimits.monthly_posts - (usage.monthly_posts_used || 0));
      case 'ai_auto_replies':
        return Math.max(0, featureLimits.ai_auto_replies - (usage.ai_auto_replies_used || 0));
      case 'regeneration_credits':
        return Math.max(0, featureLimits.regeneration_credits - (usage.regeneration_credits_used || 0));
      case 'document_training':
        return Math.max(0, featureLimits.document_training - (usage.documents_uploaded || 0));
      default:
        return 0;
    }
  };

  // Get usage percentage for a feature
  const getUsagePercentage = (feature) => {
    if (!usage || !featureLimits) return 0;

    let used = 0;
    let limit = 1;

    switch (feature) {
      case 'monthly_posts':
        used = usage.monthly_posts_used || 0;
        limit = featureLimits.monthly_posts || 1;
        break;
      case 'ai_auto_replies':
        used = usage.ai_auto_replies_used || 0;
        limit = featureLimits.ai_auto_replies || 1;
        break;
      case 'regeneration_credits':
        used = usage.regeneration_credits_used || 0;
        limit = featureLimits.regeneration_credits || 1;
        break;
      default:
        return 0;
    }

    return Math.min(100, (used / limit) * 100);
  };

  // Calculate regeneration cost based on usage
  const getRegenerationCost = () => {
    if (!usage) return 0;

    const currentUsage = usage.regeneration_count_this_month || 0;

    if (currentUsage === 0) return 0; // First regeneration is free
    if (currentUsage === 1) return 0.5; // Second regeneration costs 0.5 credits
    return 1; // Additional regenerations cost 1 credit each
  };

  // Get plan tier level (0=Creator, 1=Accelerator, 2=Dominator)
  const getPlanTier = () => {
    if (!subscription) return 0;

    switch (subscription.plan_id) {
      case 'creator': return 0;
      case 'accelerator': return 1;
      case 'dominator': return 2;
      default: return 0;
    }
  };

  // Check if user needs to upgrade for a feature
  const needsUpgradeFor = (feature) => {
    return !hasFeatureAccess(feature);
  };

  // Get recommended upgrade plan for a feature
  const getRecommendedUpgrade = (feature) => {
    const currentTier = getPlanTier();

    switch (feature) {
      case 'policy_compliance':
      case 'document_training':
        return currentTier < 2 ? 'accelerator' : null;
      case 'white_label_ai_responses':
      case 'multi_language_ai_support':
        return currentTier < 3 ? 'dominator' : null;
      case 'team_collaboration':
        return currentTier < 1 ? 'creator' : null;
      default:
        return currentTier < 2 ? 'accelerator' : null;
    }
  };

  // Advanced subscription management functions
  const upgradeSubscription = async (newPlanId, options = {}) => {
    try {
      setPendingUpgrade({ planId: newPlanId, timestamp: Date.now() });

      const response = await api.post('/api/billing/upgrade', {
        plan_id: newPlanId,
        prorate: options.prorate !== false,
        immediate: options.immediate === true
      });

      if (response.data.requires_payment) {
        // Redirect to payment flow
        window.location.href = response.data.payment_url;
      } else {
        // Immediate upgrade
        await loadSubscription();
        showSuccessNotification(`Successfully upgraded to ${newPlanId}`);
      }

      return response.data;
    } catch (error) {
      setPendingUpgrade(null);
      showErrorNotification('Failed to upgrade subscription');
      throw error;
    }
  };

  const downgradeSubscription = async (newPlanId, options = {}) => {
    try {
      const response = await api.post('/api/billing/downgrade', {
        plan_id: newPlanId,
        at_period_end: options.atPeriodEnd !== false
      });

      await loadSubscription();
      showSuccessNotification(`Subscription will be downgraded to ${newPlanId} at the end of the current period`);

      return response.data;
    } catch (error) {
      showErrorNotification('Failed to downgrade subscription');
      throw error;
    }
  };

  const cancelSubscription = async (options = {}) => {
    try {
      const response = await api.post('/api/billing/cancel', {
        at_period_end: options.atPeriodEnd !== false,
        reason: options.reason
      });

      await loadSubscription();
      showSuccessNotification('Subscription cancelled successfully');

      return response.data;
    } catch (error) {
      showErrorNotification('Failed to cancel subscription');
      throw error;
    }
  };

  const reactivateSubscription = async () => {
    try {
      const response = await api.post('/api/billing/reactivate');

      await loadSubscription();
      showSuccessNotification('Subscription reactivated successfully');

      return response.data;
    } catch (error) {
      showErrorNotification('Failed to reactivate subscription');
      throw error;
    }
  };

  // Credit management functions
  const purchaseCredits = async (amount, paymentMethodId = null) => {
    try {
      const response = await api.post('/api/billing/credits/purchase', {
        amount,
        payment_method_id: paymentMethodId
      });

      if (response.data.requires_payment) {
        window.location.href = response.data.payment_url;
      } else {
        setCreditBalance(prev => prev + amount);
        showSuccessNotification(`Successfully purchased ${amount} credits`);
      }

      return response.data;
    } catch (error) {
      showErrorNotification('Failed to purchase credits');
      throw error;
    }
  };

  const transferCredits = async (toUserId, amount) => {
    try {
      const response = await api.post('/api/billing/credits/transfer', {
        to_user_id: toUserId,
        amount
      });

      setCreditBalance(prev => prev - amount);
      showSuccessNotification(`Successfully transferred ${amount} credits`);

      return response.data;
    } catch (error) {
      showErrorNotification('Failed to transfer credits');
      throw error;
    }
  };

  // Advanced usage tracking with rollover
  const updateUsage = async (action, quantity = 1, metadata = {}) => {
    try {
      const response = await api.post('/api/billing/usage/update', {
        action,
        quantity,
        metadata: {
          ...metadata,
          timestamp: Date.now(),
          user_agent: navigator.userAgent,
          correlation_id: `usage_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
        }
      });

      // Update local usage state immediately for better UX
      setUsage(prev => ({
        ...prev,
        [`${action}_used`]: (prev[`${action}_used`] || 0) + quantity,
        // Special handling for regeneration credits
        ...(action === 'regeneration_credits' && {
          regeneration_count_this_month: (prev.regeneration_count_this_month || 0) + 1
        })
      }));

      // Check for usage alerts
      checkUsageAlerts(usage);

      return response.data;
    } catch (error) {
      logger.error('Error updating usage', error);

      // Revert optimistic update on error
      setUsage(prev => ({
        ...prev,
        [`${action}_used`]: Math.max(0, (prev[`${action}_used`] || 0) - quantity),
        ...(action === 'regeneration_credits' && {
          regeneration_count_this_month: Math.max(0, (prev.regeneration_count_this_month || 0) - 1)
        })
      }));

      showErrorNotification('Failed to update usage');
      throw error;
    }
  };

  // Role-based access control
  const hasRolePermission = (permission) => {
    if (!user || !subscription) return false;

    const userRole = user.role || 'user';
    const planTier = getPlanTier();

    const rolePermissions = {
      admin: ['*'], // Admin has all permissions
      owner: [
        'manage_subscription',
        'manage_billing',
        'manage_team',
        'view_analytics',
        'manage_integrations'
      ],
      manager: [
        'view_analytics',
        'manage_content',
        'manage_campaigns'
      ],
      user: [
        'create_content',
        'view_basic_analytics'
      ]
    };

    const planPermissions = {
      1: ['basic_features', 'advanced_content'], // Creator
      2: ['basic_features', 'advanced_content', 'team_features'], // Accelerator
      3: ['basic_features', 'advanced_content', 'team_features', 'enterprise_features'] // Dominator
    };

    const userPermissions = rolePermissions[userRole] || [];
    const tierPermissions = planPermissions[planTier] || [];

    return userPermissions.includes('*') ||
           userPermissions.includes(permission) ||
           tierPermissions.includes(permission);
  };

  // Cleanup WebSocket on unmount
  useEffect(() => {
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    loadSubscription();
  }, [loadSubscription]);

  // Enhanced context value with all advanced features
  const value = {
    // Core subscription data
    subscription,
    featureLimits,
    usage,
    loading,
    error,

    // Advanced subscription data
    billingHistory,
    creditBalance,
    usageAlerts,
    trialInfo,
    webhookEvents,
    subscriptionMetrics,
    pendingUpgrade,
    dunningStatus,

    // Feature access functions
    hasFeatureAccess,
    canPerformAction,
    hasRolePermission,
    getRemainingUsage,
    getUsagePercentage,
    getRemainingCredits,

    // Usage and credit functions
    getRegenerationCost,
    updateUsage,
    purchaseCredits,
    transferCredits,

    // Subscription management functions
    upgradeSubscription,
    downgradeSubscription,
    cancelSubscription,
    reactivateSubscription,
    refreshSubscription,

    // Utility functions
    getPlanTier,
    needsUpgradeFor,
    getRecommendedUpgrade,
    isTrialActive: () => trialInfo?.isActive || false,
    getTrialDaysRemaining: () => trialInfo?.daysRemaining || 0,

    // Real-time updates control
    realTimeUpdates,
    setRealTimeUpdates,

    // Debug information
    getWebhookEvents: () => webhookEvents,
    getLastError: () => error
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export default SubscriptionContext;

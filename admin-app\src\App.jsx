import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Contexts
import { AuthProvider } from './contexts/AuthContext';

// Components
import PrivateRoute from './components/PrivateRoute';
import Layout from './components/Layout';
import HealthCheck from './components/HealthCheck';

// Pages
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import ApiManagement from './pages/ApiManagement';
import CouponManagement from './pages/CouponManagement';
import AppSumoManagement from './pages/AppSumoManagement';
import UserManagement from './pages/UserManagement';
import SystemSettings from './pages/SystemSettings';
import FinanceModule from './pages/FinanceModule';
import SupportModule from './pages/SupportModule';
import EmailTemplateManagement from './pages/EmailTemplateManagement';
import ExternalApiManagement from './pages/ExternalApiManagement';

// Components
import ErrorBoundary from './components/common/ErrorBoundary';

// Create Material-UI theme matching main app design system
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#4E40C5', // Primary Purple
      light: '#7B6FD9',
      dark: '#3A2F9A',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#EBAE1B', // Accent Yellow
      light: '#F0C555',
      dark: '#C8940F',
      contrastText: '#15110E',
    },
    error: {
      main: '#FF3D71', // Pink-Red
      light: '#FF7A9E',
      dark: '#DB0047',
    },
    warning: {
      main: '#FFAA00',
      light: '#FFCC66',
      dark: '#CC8800',
    },
    info: {
      main: '#4590FF', // Blue
      light: '#7FB5FF',
      dark: '#0062CC',
    },
    success: {
      main: '#00D68F', // Green
      light: '#5AEBC4',
      dark: '#00A36B',
    },
    background: {
      default: '#FFFFFF',
      paper: '#FFFFFF',
    },
    text: {
      primary: '#15110E',
      secondary: '#6B6B6B',
      disabled: '#A0A0A0',
    },
    divider: '#EDF1F7',
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
    button: {
      textTransform: 'none',
      fontWeight: 600,
    },
  },
  spacing: 8, // 8px grid system
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          padding: '12px 24px',
          minHeight: 44,
          minWidth: 44,
          boxShadow: '0px 4px 8px rgba(46, 58, 89, 0.08)',
          textTransform: 'none',
          fontWeight: 600,
          transition: 'all 0.2s ease-in-out',
          '&:focus-visible': {
            outline: '3px solid currentColor',
            outlineOffset: '2px',
          },
          '&:hover': {
            boxShadow: '0px 6px 12px rgba(46, 58, 89, 0.1)',
            transform: 'translateY(-1px)',
          },
          '&:active': {
            transform: 'translateY(0)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        rounded: {
          borderRadius: 16,
        },
        elevation1: {
          boxShadow: '0px 4px 20px rgba(46, 58, 89, 0.08)',
        },
      },
      variants: [
        {
          props: { variant: 'glass' },
          style: {
            background: 'rgba(255, 255, 255, 0.7)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
          },
        },
      ],
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: '0px 4px 20px rgba(46, 58, 89, 0.08)',
        },
      },
      variants: [
        {
          props: { variant: 'glass' },
          style: {
            background: 'rgba(255, 255, 255, 0.7)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
          },
        },
      ],
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          border: '1px solid #EDF1F7',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: '#F9FAFC',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        head: {
          fontWeight: 600,
          color: '#2E3A59',
          borderBottom: '2px solid #EDF1F7',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 500,
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 16,
          boxShadow: '0 24px 48px rgba(46, 58, 89, 0.2)',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#6C4BFA',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#6C4BFA',
              borderWidth: 2,
            },
          },
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: 48,
        },
        indicator: {
          height: 3,
          borderRadius: '3px 3px 0 0',
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          minHeight: 48,
          '&.Mui-selected': {
            color: '#6C4BFA',
          },
        },
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <Router>
          <AuthProvider>
            <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<Login />} />
              
              {/* Protected Routes */}
              <Route
                path="/*"
                element={
                  <PrivateRoute>
                    <Layout>
                      <Routes>
                        <Route path="/" element={
                          <ErrorBoundary>
                            <Dashboard />
                          </ErrorBoundary>
                        } />
                        <Route path="/dashboard" element={
                          <ErrorBoundary>
                            <Dashboard />
                          </ErrorBoundary>
                        } />
                        <Route path="/api-management" element={<ApiManagement />} />
                        <Route path="/coupons" element={<CouponManagement />} />
                        <Route path="/appsumo" element={<AppSumoManagement />} />
                        <Route path="/users" element={<UserManagement />} />
                        <Route path="/finance" element={<FinanceModule />} />
                        <Route path="/support" element={<SupportModule />} />
                        <Route path="/email-templates" element={<EmailTemplateManagement />} />
                        <Route path="/external-apis" element={<ExternalApiManagement />} />
                        <Route path="/settings" element={<SystemSettings />} />
                      </Routes>
                    </Layout>
                  </PrivateRoute>
                }
              />
            </Routes>
          </AuthProvider>
        </Router>
      </LocalizationProvider>
    </ThemeProvider>
  );
}

export default App;

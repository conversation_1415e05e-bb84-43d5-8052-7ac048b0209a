/**
 * Authentication Context and Provider
 * Provides authentication state and functions to the application
 * Production-ready implementation with proper error handling and security
 @since 2024-1-1 to 2025-25-7
*/

import {
  createContext,
  useState,
  useEffect,
  useCallback,
  useContext,
} from "react";
import { useNavigate } from "react-router-dom";
import api from "../api";
import tokenManager from "../services/tokenManager";

// Create the authentication context
const AuthContext = createContext(null);

// Custom hook to use the auth context
const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(tokenManager.getToken());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const navigate = useNavigate();

  // Check if localhost bypass is enabled
  const isLocalhostBypass =
    import.meta.env.VITE_LOCALHOST_BYPASS_AUTH === "true" &&
    (window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1");



  // Initialize token manager and auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        tokenManager.initTokenManager();

        // Check if we have a valid token
        const currentToken = tokenManager.getToken();
        if (currentToken && tokenManager.isTokenValid()) {
          setToken(currentToken);
        } else {
          // Clear invalid token
          tokenManager.clearToken();
          setToken(null);
          setUser(null);
        }

        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize authentication:', error);
        setError('Failed to initialize authentication');
        setIsInitialized(true);
      }
    };

    initializeAuth();
  }, []);

  // Load user data from backend API
  const loadUser = useCallback(async () => {
    // Development mode bypass for localhost testing
    if (isLocalhostBypass) {
      console.warn('🚧 Development Mode: Using localhost bypass authentication');

      try {
        // In development, still try to fetch real user data if possible
        const response = await api.get("/api/users/me");
        if (response.status === 200 && response.data) {
          setUser(response.data);
          setError(null);
          setLoading(false);
          return;
        }
      } catch (devError) {
        console.warn('🚧 Development Mode: Backend not available, using fallback user', devError.message);
      }

      // Fallback development user only when backend is unavailable
      const developmentUser = {
        id: "dev-user-" + Date.now(),
        email: process.env.VITE_DEV_USER_EMAIL || "<EMAIL>",
        full_name: "Development User",
        first_name: "Development",
        last_name: "User",
        is_active: true,
        is_admin: false, // Default to non-admin for safety
        company_name: "Development Environment",
        subscription: {
          plan: "creator", // Default to basic plan
          status: "active",
          trial_end: null,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setUser(developmentUser);
      setError(null);
      setLoading(false);
      return;
    }



    if (!token) {
      setUser(null);
      setLoading(false);
      return;
    }

    try {
      // Make API call to get current user
      const response = await api.get("/api/users/me");

      if (response.status === 200 && response.data) {
        setUser(response.data);
        setError(null);
      } else {
        throw new Error("Failed to load user data");
      }
    } catch (error) {
      console.error("Error loading user:", error);

      // If token is invalid, clear it and redirect to login
      if (error.response?.status === 401) {
        tokenManager.clearToken();
        setToken(null);
        setUser(null);
      } else {
        setError("Failed to load user data. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  }, [token, isLocalhostBypass]);

  // Login user with backend API
  const login = async (email, password) => {
    try {
      setLoading(true);
      console.log("Logging in user with backend:", email);

      // Create form data for OAuth2 login (backend expects form data with username/password)
      const formData = new FormData();
      formData.append("username", email); // OAuth2 uses 'username' field for email
      formData.append("password", password);

      // Make API call to login endpoint with form data
      const response = await api.post("/api/auth/login", formData, {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });

      if (response.status === 200 && response.data.access_token) {
        const { access_token, refresh_token } = response.data;

        // Save tokens using token manager
        tokenManager.setToken(access_token);
        setToken(access_token);

        // Note: Refresh token handling would be implemented in tokenManager if needed
        if (refresh_token) {
          console.debug('Refresh token received but not stored (implement in tokenManager if needed)');
        }

        // Load user data from the token or make a separate API call
        await loadUser();

        return { success: true };
      } else {
        throw new Error(response.data?.detail || "Login failed");
      }
    } catch (error) {
      console.error("Login error:", error);
      const errorMessage =
        error.response?.data?.detail ||
        error.message ||
        "Login failed. Please try again.";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Register user with backend API
  const register = async (userData) => {
    try {
      setLoading(true);
      console.log("Registering user with backend:", userData.email);

      // Make API call to register endpoint
      const response = await api.post("/api/auth/register", userData);

      if (response.status === 200 || response.status === 201) {
        console.log("Registration successful:", response.data);
        return {
          success: true,
          data: response.data,
        };
      } else {
        throw new Error(response.data?.detail || "Registration failed");
      }
    } catch (error) {
      console.error("Registration error:", error);
      const errorMessage =
        error.response?.data?.detail ||
        error.message ||
        "Registration failed. Please try again.";
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = () => {
    tokenManager.clearToken();
    setToken(null);
    setUser(null);
    delete api.defaults.headers.common["Authorization"];
    navigate("/login");
  };

  // Update user profile
  const updateProfile = async (userData) => {
    try {
      setLoading(true);

      // For white label, update the user in localStorage
      if (userData) {
        // Get current user data
        const currentUser = { ...user };

        // Update with new data
        const updatedUser = { ...currentUser, ...userData };

        // Save to localStorage
        localStorage.setItem("whiteLabel_user", JSON.stringify(updatedUser));

        // Update state
        setUser(updatedUser);

        return { success: true, data: updatedUser };
      } else {
        // If no userData provided, just return the current user
        return { success: true, data: user };
      }
    } catch (error) {
      console.error("Update profile error:", error);
      setError(error.response?.data?.detail || "Failed to update profile");
      return {
        success: false,
        error: error.response?.data?.detail || "Failed to update profile",
      };
    } finally {
      setLoading(false);
    }
  };

  // Request password reset
  const requestPasswordReset = async (email) => {
    try {
      setLoading(true);
      await api.post("/api/auth/password-reset", { email });
      return { success: true };
    } catch (error) {
      console.error("Password reset request error:", error);
      setError(
        error.response?.data?.detail || "Failed to request password reset"
      );
      return {
        success: false,
        error:
          error.response?.data?.detail || "Failed to request password reset",
      };
    } finally {
      setLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (token, password) => {
    try {
      setLoading(true);
      await api.post("/api/auth/reset-password", { token, password });
      return { success: true };
    } catch (error) {
      console.error("Password reset error:", error);
      setError(error.response?.data?.detail || "Failed to reset password");
      return {
        success: false,
        error: error.response?.data?.detail || "Failed to reset password",
      };
    } finally {
      setLoading(false);
    }
  };

  // Request magic link authentication
  const requestMagicLink = async (email) => {
    try {
      setLoading(true);
      setError(null);

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error("Please enter a valid email address");
      }

      // Development mode bypass
      if (isLocalhostBypass) {
        console.warn("🚧 Development Mode: Magic link request bypassed");
        localStorage.setItem("pendingMagicLinkEmail", email);
        return { success: true, message: "Development mode: Magic link request simulated" };
      }

      // Make API call to request magic link
      const response = await api.post("/api/auth/magic-link/request", { email });

      if (response.status === 200) {
        // Store email for verification step
        localStorage.setItem("pendingMagicLinkEmail", email);
        return {
          success: true,
          message: "Magic link sent! Please check your email and click the link to sign in."
        };
      } else {
        throw new Error(response.data?.message || "Failed to send magic link");
      }
    } catch (error) {
      console.error("Magic link request error:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to request magic link. Please try again.";
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  // Verify magic link token
  const verifyMagicLink = async (token) => {
    try {
      setLoading(true);
      setError(null);

      // Validate token format
      if (!token || typeof token !== 'string' || token.length < 10) {
        throw new Error("Invalid magic link token");
      }

      // Development mode bypass
      if (isLocalhostBypass) {
        console.warn("🚧 Development Mode: Magic link verification bypassed");

        const email = localStorage.getItem("pendingMagicLinkEmail") || "<EMAIL>";
        const devToken = `dev_token_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;

        // Save development token
        tokenManager.setToken(devToken);
        setToken(devToken);

        // Set development user data
        const developmentUser = {
          id: "dev-user-" + Date.now(),
          email: email,
          first_name: "Development",
          last_name: "User",
          full_name: "Development User",
          is_admin: false,
          avatar: null,
          subscription: {
            plan: "creator",
            status: "active",
            trial_end: null,
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        setUser(developmentUser);

        // Set development features
        setUserFeatures({
          features: ["content_generation", "campaigns", "analytics"],
          feature_limits: {
            posts_per_month: 100,
            campaigns_per_month: 10,
          },
          feature_descriptions: {
            content_generation: "AI-powered content creation",
            campaigns: "Social media campaign management",
            analytics: "Performance analytics and insights",
          },
          addons: [],
        });

        localStorage.removeItem("pendingMagicLinkEmail");
        return { success: true, message: "Development mode: Magic link verified" };
      }

      // Make API call to verify magic link
      const response = await api.post("/api/auth/magic-link/verify", { token });

      if (response.status === 200 && response.data) {
        const { access_token, user } = response.data;

        if (!access_token || !user) {
          throw new Error("Invalid response from server");
        }

        // Save token and user data
        tokenManager.setToken(access_token);
        setToken(access_token);
        setUser(user);
        setError(null);

        // Load user features
        await loadUserFeatures();

        // Clean up
        localStorage.removeItem("pendingMagicLinkEmail");

        return {
          success: true,
          message: "Successfully signed in with magic link!"
        };
      } else {
        throw new Error(response.data?.message || "Invalid magic link");
      }
    } catch (error) {
      console.error("Magic link verification error:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to verify magic link. Please try again.";
      setError(errorMessage);

      // Clear any invalid tokens
      tokenManager.clearToken();
      setToken(null);
      setUser(null);

      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  // Load user after initialization and when token changes
  useEffect(() => {
    if (isInitialized) {
      loadUser();
    }
  }, [isInitialized, loadUser]);

  // Get user features from API
  const [userFeatures, setUserFeatures] = useState({
    features: [],
    feature_limits: {},
    feature_descriptions: {},
    addons: [],
  });

  // Load user features from backend API
  const loadUserFeatures = useCallback(async () => {
    if (!user) {
      setUserFeatures({
        features: [],
        feature_limits: {},
        feature_descriptions: {},
        addons: [],
      });
      return;
    }



    try {
      // Make API call to get user features
      const response = await api.get("/api/users/features");

      if (response.status === 200 && response.data) {
        setUserFeatures(response.data);
      } else {
        throw new Error("Failed to load user features");
      }
    } catch (error) {
      console.error("Error loading user features:", error);

      // Set default features on error
      setUserFeatures({
        features: [],
        feature_limits: {},
        feature_descriptions: {},
        addons: [],
      });
    }
  }, [user]);

  // Load user features when user changes
  useEffect(() => {
    if (user) {
      loadUserFeatures();
    }
  }, [user, loadUserFeatures]);

  // Check if user has access to a feature (white label always returns true)
  const hasFeature = (featureKey) => {
    if (!user) return false;

    // White label users have access to all features
    if (user.is_admin) return true;

    // If the user has the "all_features" feature, they have access to everything
    if (userFeatures.features.includes("all_features")) return true;

    // Check if user has the specific feature in their features list
    // This works for both regular subscriptions and AppSumo subscriptions
    // since AppSumo tiers are mapped to regular subscription plans
    return userFeatures.features.includes(featureKey);
  };

  // Get feature limit
  const getFeatureLimit = (limitName) => {
    if (!user) return 0;

    // Admin users have unlimited access
    if (user.is_admin) return 999999;

    // Return the limit from the userFeatures object
    // This works for both regular subscriptions and AppSumo subscriptions
    // since the backend handles the mapping between AppSumo tiers and regular plans
    return userFeatures.feature_limits[limitName] || 0;
  };

  // Get feature description
  const getFeatureDescription = (featureKey) => {
    return (
      userFeatures.feature_descriptions[featureKey] ||
      `Access to ${featureKey.replace("_", " ")} functionality`
    );
  };

  // Get user's active add-ons
  const getActiveAddons = () => {
    return userFeatures.addons || [];
  };

  // Check if user has a specific add-on
  const hasAddon = (addonId) => {
    if (!user) return false;

    // Admin users have access to all add-ons
    if (user.is_admin) return true;

    // Check if user has the add-on in their add-ons list
    return (userFeatures.addons || []).some((addon) => addon.id === addonId);
  };

  // Get add-on details by ID
  const getAddonDetails = (addonId) => {
    if (!user) return null;

    // Find the add-on in the user's add-ons list
    return (
      (userFeatures.addons || []).find((addon) => addon.id === addonId) || null
    );
  };

  // Get remaining uses for a consumable add-on
  const getAddonRemainingUses = (addonId) => {
    const addon = getAddonDetails(addonId);
    return addon?.remaining_uses || 0;
  };

  // Refresh user data
  const refreshUser = async () => {
    await loadUser();
  };

  // Refresh user features
  const refreshUserFeatures = async () => {
    await loadUserFeatures();
  };

  // Refresh all user data
  const refreshUserData = async () => {
    await loadUser();
    await loadUserFeatures();
  };

  return (
    <AuthContext.Provider
      value={{
        // Core auth state
        user,
        token,
        loading,
        error,
        isInitialized,
        isAuthenticated: !!user && !!token,
        isAdmin: user?.is_admin || false,

        // User features
        userFeatures,
        hasFeature,
        getFeatureLimit,
        getFeatureDescription,
        getActiveAddons,
        hasAddon,
        getAddonDetails,
        getAddonRemainingUses,

        // Data refresh functions
        refreshUser,
        refreshUserFeatures,
        refreshUserData,

        // Authentication functions
        login,
        register,
        logout,
        updateProfile,
        requestPasswordReset,
        resetPassword,
        requestMagicLink,
        verifyMagicLink,

        // Utility functions
        clearError: () => setError(null),
        isDevMode: isLocalhostBypass,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Export the context and hook
// eslint-disable-next-line react-refresh/only-export-components
export { AuthContext, useAuth };

// Default export for convenience
export default AuthProvider;

/**
 * Enhanced DashboardSkeleton Component - Enterprise-grade skeleton loading management
 * Features: Plan-based skeleton limitations, real-time skeleton state management, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced skeleton animations, template systems, and performance monitoring
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Skeleton,
  Card,
  CardHeader,
  CardContent,
  useTheme,
  alpha,
  useMediaQuery,
  Alert,
  Zoom,
  Typography
} from '@mui/material';


// Enhanced context and hook imports
import { useSubscription } from '../../contexts/SubscriptionContext';

// Enhanced component imports
import ErrorBoundary from '../common/ErrorBoundary';

/**
 * Enhanced DashboardSkeleton Component with Enterprise Features
 */
const DashboardSkeleton = memo(forwardRef(({
  variant = 'default',
  cardCount = 8,
  showHeader = true,
  showCharts = true,
  spacing = 3,
  animationSpeed = 'normal',
  shimmerIntensity = 'medium',
  enableCustomization = true,
  enableReducedMotion = false,
  className = '',
  'data-testid': testId = 'dashboard-skeleton',
  ...props
}, ref) => {
  const theme = useTheme();
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced context integration
  const {
    usage,
    featureLimits,
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: false,
    animationActive: true,
    customizationOpen: false,
    showUpgradeDialog: false,
    performanceMetrics: {
      renderTime: 0,
      animationFrames: 0,
      memoryUsage: 0
    },
    skeletonSettings: {
      animationSpeed: animationSpeed,
      shimmerIntensity: shimmerIntensity,
      reducedMotion: enableReducedMotion || prefersReducedMotion,
      highContrast: false,
      customColors: false
    },
    errors: {},
    lastUpdated: null
  });

  // Refs for enhanced functionality
  const skeletonRef = useRef(null);

  /**
   * Enhanced plan-based skeleton analytics validation - Production Ready
   */
  const validateSkeletonAnalytics = useCallback(() => {
    if (!usage || !featureLimits || !subscription) {
      return {
        canCustomize: false,
        hasCustomizationAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { monthly: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based skeleton analytics limits
    const planLimits = {
      creator: {
        monthly: 20,
        features: ['basic_skeleton_display'],
        animationTypes: ['fade'],
        customTemplates: 0,
        shimmerEffects: false,
        performanceMonitoring: false
      },
      accelerator: {
        monthly: 100,
        features: ['basic_skeleton_display', 'advanced_skeleton_animations', 'customization'],
        animationTypes: ['fade', 'shimmer', 'pulse'],
        customTemplates: 5,
        shimmerEffects: true,
        performanceMonitoring: true
      },
      dominator: {
        monthly: Infinity,
        features: ['basic_skeleton_display', 'advanced_skeleton_animations', 'customization', 'custom_loading_states'],
        animationTypes: ['fade', 'shimmer', 'pulse', 'wave', 'custom'],
        customTemplates: Infinity,
        shimmerEffects: true,
        performanceMonitoring: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    if (isUnlimited) {
      return {
        canCustomize: true,
        hasCustomizationAvailable: true,
        remaining: Infinity,
        total: Infinity,
        used: 0,
        isUnlimited: true,
        status: 'unlimited',
        planLimits: currentPlanLimits,
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    // Get current skeleton analytics usage
    const analyticsUsed = usage.skeleton_analytics_used || 0;
    const analyticsLimit = currentPlanLimits.monthly;
    const remaining = Math.max(0, analyticsLimit - analyticsUsed);
    const hasCustomizationAvailable = remaining > 0;
    const canCustomize = hasCustomizationAvailable && !subscriptionLoading;

    // Calculate status based on usage
    let status = 'excellent';
    const percentage = analyticsLimit > 0 ? (analyticsUsed / analyticsLimit) * 100 : 0;
    if (percentage >= 95) status = 'critical';
    else if (percentage >= 85) status = 'warning';
    else if (percentage >= 70) status = 'caution';
    else if (percentage >= 50) status = 'moderate';
    else if (percentage >= 25) status = 'good';

    // Detect mid-cycle depletion
    const depletionInfo = detectPlanDepletion({
      used: analyticsUsed,
      total: analyticsLimit,
      remaining,
      percentage,
      status
    });

    return {
      canCustomize,
      hasCustomizationAvailable,
      remaining,
      total: analyticsLimit,
      used: analyticsUsed,
      isUnlimited: false,
      status,
      percentage,
      planLimits: currentPlanLimits,
      depletionInfo
    };
  }, [usage, featureLimits, subscription, subscriptionLoading, detectPlanDepletion]);

  /**
   * Detect plan limit depletion before monthly cycle ends - Production Ready
   */
  const detectPlanDepletion = useCallback((planInfo) => {
    if (!planInfo || !subscription) return { isDepletedMidCycle: false, daysRemaining: 0 };

    const now = new Date();
    const billingCycleStart = new Date(subscription.current_period_start * 1000);
    const billingCycleEnd = new Date(subscription.current_period_end * 1000);

    const totalCycleDays = Math.ceil((billingCycleEnd - billingCycleStart) / (1000 * 60 * 60 * 24));
    const daysElapsed = Math.ceil((now - billingCycleStart) / (1000 * 60 * 60 * 24));
    const daysRemaining = Math.max(0, totalCycleDays - daysElapsed);

    // Consider depleted if no customization remaining with more than 3 days left in cycle
    const isDepletedMidCycle = planInfo.remaining === 0 && daysRemaining > 3;

    return {
      isDepletedMidCycle,
      daysRemaining,
      cycleProgress: (daysElapsed / totalCycleDays) * 100
    };
  }, [subscription]);

  /**
   * Enhanced feature availability check - Production Ready
   */
  const isFeatureAvailable = useCallback((feature) => {
    const analyticsLimits = validateSkeletonAnalytics();
    return analyticsLimits.planLimits.features.includes(feature);
  }, [validateSkeletonAnalytics]);

  /**
   * Enhanced skeleton styling with ACE Social branding - Production Ready
   */
  const getSkeletonStyles = useCallback(() => {
    const aceColors = {
      primary: '#4E40C5',
      dark: '#15110E',
      yellow: '#EBAE1B',
      white: '#FFFFFF'
    };

    const baseStyles = {
      background: `linear-gradient(135deg,
        ${alpha(theme.palette.background.paper, 0.8)} 0%,
        ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
      backdropFilter: 'blur(20px)',
      border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
      borderRadius: theme.spacing(2),
      boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
      overflow: 'hidden'
    };

    // Add ACE Social brand colors for skeleton elements
    const skeletonColors = {
      primary: alpha(aceColors.primary, 0.1),
      secondary: alpha(aceColors.yellow, 0.1),
      text: alpha(theme.palette.text.primary, 0.1),
      action: alpha(theme.palette.action.hover, 0.3)
    };

    return { baseStyles, skeletonColors };
  }, [theme]);

  /**
   * Enhanced animation configuration - Production Ready
   */
  const getAnimationConfig = useCallback(() => {
    const analyticsLimits = validateSkeletonAnalytics();
    const { skeletonSettings } = state;

    // Animation speed mapping
    const speedMap = {
      slow: 3000,
      normal: 2000,
      fast: 1000,
      instant: 0
    };

    // Shimmer intensity mapping
    const intensityMap = {
      low: 0.3,
      medium: 0.6,
      high: 0.9
    };

    const config = {
      duration: speedMap[skeletonSettings.animationSpeed] || speedMap.normal,
      shimmerOpacity: intensityMap[skeletonSettings.shimmerIntensity] || intensityMap.medium,
      reducedMotion: skeletonSettings.reducedMotion,
      highContrast: skeletonSettings.highContrast,
      enabled: state.animationActive && analyticsLimits.planLimits.shimmerEffects
    };

    return config;
  }, [validateSkeletonAnalytics, state]);

  /**
   * Enhanced action handlers - Production Ready
   */
  const handleCustomizationToggle = useCallback(() => {
    if (!enableCustomization || !isFeatureAvailable('customization')) return;

    setState(prev => ({
      ...prev,
      customizationOpen: !prev.customizationOpen
    }));
  }, [enableCustomization, isFeatureAvailable]);

  const handleAnimationToggle = useCallback(() => {
    setState(prev => ({
      ...prev,
      animationActive: !prev.animationActive
    }));
  }, []);

  const handleSettingChange = useCallback((setting, value) => {
    setState(prev => ({
      ...prev,
      skeletonSettings: {
        ...prev.skeletonSettings,
        [setting]: value
      }
    }));
  }, []);



  /**
   * Expose component methods via ref - Production Ready
   */
  useImperativeHandle(ref, () => ({
    toggleAnimation: handleAnimationToggle,
    toggleCustomization: handleCustomizationToggle,
    updateSettings: handleSettingChange,
    getPerformanceMetrics: () => state.performanceMetrics,
    getAnalyticsLimits: () => validateSkeletonAnalytics(),
    focus: () => skeletonRef.current?.focus(),
    getElement: () => skeletonRef.current
  }), [
    handleAnimationToggle,
    handleCustomizationToggle,
    handleSettingChange,
    state.performanceMetrics,
    validateSkeletonAnalytics
  ]);

  // Memoized calculations
  const animationConfig = useMemo(() => getAnimationConfig(), [getAnimationConfig]);
  const skeletonStyles = useMemo(() => getSkeletonStyles(), [getSkeletonStyles]);

  /**
   * Enhanced skeleton component templates - Production Ready
   */
  const PerformanceCardSkeleton = useCallback(({ height = 180 }) => {
    const { baseStyles, skeletonColors } = skeletonStyles;

    return (
      <Card sx={{ ...baseStyles, height }}>
        <CardHeader
          avatar={
            <Skeleton
              variant="circular"
              width={40}
              height={40}
              animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false}
              sx={{
                backgroundColor: skeletonColors.primary
              }}
            />
          }
          title={
            <Skeleton
              variant="text"
              width="60%"
              height={24}
              animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false}
              sx={{ backgroundColor: skeletonColors.text }}
            />
          }
          subheader={
            <Skeleton
              variant="text"
              width="40%"
              height={16}
              animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false}
              sx={{ backgroundColor: skeletonColors.text }}
            />
          }
          action={
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              <Skeleton variant="circular" width={24} height={24} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
              <Skeleton variant="circular" width={24} height={24} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
            </Box>
          }
        />
        <CardContent sx={{ pt: 0 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Skeleton
              variant="text"
              width="30%"
              height={32}
              animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false}
              sx={{
                fontSize: '1.5rem',
                backgroundColor: skeletonColors.primary
              }}
            />
            <Box sx={{ ml: 'auto' }}>
              <Skeleton variant="rectangular" width={60} height={20} sx={{ borderRadius: 1 }} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
            </Box>
          </Box>

          {showCharts && (
            <Skeleton
              variant="rectangular"
              height={80}
              animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false}
              sx={{
                borderRadius: 1,
                backgroundColor: skeletonColors.action
              }}
            />
          )}
        </CardContent>
      </Card>
    );
  }, [skeletonStyles, animationConfig, showCharts]);

  const ChartCardSkeleton = useCallback(({ height = 300 }) => {
    const { baseStyles, skeletonColors } = skeletonStyles;

    return (
      <Card sx={{ ...baseStyles, height }}>
        <CardHeader
          title={<Skeleton variant="text" width="50%" height={24} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />}
          subheader={<Skeleton variant="text" width="30%" height={16} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />}
          action={
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {Array.from({ length: 4 }).map((_, index) => (
                <Skeleton
                  key={index}
                  variant="rectangular"
                  width={32}
                  height={32}
                  animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false}
                  sx={{ borderRadius: 1 }}
                />
              ))}
            </Box>
          }
        />
        <CardContent sx={{ pt: 0 }}>
          <Skeleton
            variant="rectangular"
            height={height - 120}
            animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false}
            sx={{
              borderRadius: 1,
              backgroundColor: skeletonColors.action
            }}
          />
        </CardContent>
      </Card>
    );
  }, [skeletonStyles, animationConfig]);

  const ContentCardSkeleton = useCallback(({ height = 250 }) => {
    const { baseStyles, skeletonColors } = skeletonStyles;

    return (
      <Card sx={{ ...baseStyles, height }}>
        <CardHeader
          avatar={
            <Skeleton variant="circular" width={40} height={40} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
          }
          title={
            <Skeleton variant="text" width="70%" height={24} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
          }
          action={
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              <Skeleton variant="circular" width={24} height={24} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
              <Skeleton variant="circular" width={24} height={24} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
              <Skeleton variant="circular" width={24} height={24} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
            </Box>
          }
        />
        <CardContent sx={{ pt: 0 }}>
          {Array.from({ length: 4 }).map((_, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                mb: 1.5,
                p: 1,
                borderRadius: 1,
                backgroundColor: skeletonColors.action
              }}
            >
              <Skeleton variant="rectangular" width={40} height={40} sx={{ borderRadius: 1 }} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
              <Box sx={{ flex: 1 }}>
                <Skeleton variant="text" width="80%" height={16} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
                <Skeleton variant="text" width="60%" height={14} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
              </Box>
              <Skeleton variant="text" width="15%" height={16} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
            </Box>
          ))}
        </CardContent>
      </Card>
    );
  }, [skeletonStyles, animationConfig]);

  const WidgetSkeleton = useCallback(({ height = 220 }) => {
    const { baseStyles } = skeletonStyles;

    return (
      <Card sx={{ ...baseStyles, height }}>
        <CardHeader
          avatar={<Skeleton variant="circular" width={32} height={32} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />}
          title={<Skeleton variant="text" width="60%" height={20} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />}
          action={<Skeleton variant="circular" width={24} height={24} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />}
        />
        <CardContent sx={{ pt: 0 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Skeleton variant="rectangular" height={60} sx={{ borderRadius: 1 }} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Skeleton variant="rectangular" width="48%" height={40} sx={{ borderRadius: 1 }} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
              <Skeleton variant="rectangular" width="48%" height={40} sx={{ borderRadius: 1 }} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
            </Box>
            <Skeleton variant="text" width="80%" height={16} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
            <Skeleton variant="text" width="60%" height={14} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
          </Box>
        </CardContent>
      </Card>
    );
  }, [skeletonStyles, animationConfig]);

  /**
   * Enhanced header skeleton - Production Ready
   */
  const HeaderSkeleton = useCallback(() => {
    const { baseStyles } = skeletonStyles;

    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: spacing,
          p: 2,
          borderRadius: 2,
          ...baseStyles
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Skeleton variant="circular" width={40} height={40} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
          <Box>
            <Skeleton variant="text" width={200} height={32} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
            <Skeleton variant="text" width={150} height={20} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Skeleton variant="text" width={120} height={16} animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false} />
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton
              key={index}
              variant="circular"
              width={40}
              height={40}
              animation={animationConfig.enabled && !animationConfig.reducedMotion ? 'wave' : false}
            />
          ))}
        </Box>
      </Box>
    );
  }, [skeletonStyles, spacing, animationConfig]);

  /**
   * Enhanced skeleton rendering based on variant - Production Ready
   */
  const renderSkeleton = useCallback(() => {
    switch (variant) {
      case 'compact':
        return (
          <Grid container spacing={spacing}>
            {Array.from({ length: Math.min(cardCount, 6) }).map((_, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <PerformanceCardSkeleton height={160} />
              </Grid>
            ))}
          </Grid>
        );

      case 'detailed':
        return (
          <Grid container spacing={spacing}>
            {/* Performance metrics */}
            {Array.from({ length: 4 }).map((_, index) => (
              <Grid item xs={12} sm={6} md={3} key={`perf-${index}`}>
                <PerformanceCardSkeleton />
              </Grid>
            ))}
            
            {/* Main content cards */}
            <Grid item xs={12} md={6}>
              <ContentCardSkeleton height={300} />
            </Grid>
            <Grid item xs={12} md={6}>
              <ChartCardSkeleton height={300} />
            </Grid>
            
            {/* Additional widgets */}
            {Array.from({ length: 4 }).map((_, index) => (
              <Grid item xs={12} md={6} lg={4} key={`widget-${index}`}>
                <WidgetSkeleton />
              </Grid>
            ))}
          </Grid>
        );

      default: // 'default'
        return (
          <Grid container spacing={spacing}>
            {/* Performance metrics row */}
            {Array.from({ length: 4 }).map((_, index) => (
              <Grid item xs={12} sm={6} md={3} key={`metric-${index}`}>
                <PerformanceCardSkeleton />
              </Grid>
            ))}
            
            {/* Content cards */}
            <Grid item xs={12} md={6}>
              <ContentCardSkeleton />
            </Grid>
            <Grid item xs={12} md={6}>
              <ContentCardSkeleton />
            </Grid>
            
            {/* Widget grid */}
            {Array.from({ length: Math.max(0, cardCount - 6) }).map((_, index) => (
              <Grid item xs={12} md={6} lg={4} key={`card-${index}`}>
                <WidgetSkeleton />
              </Grid>
            ))}
          </Grid>
        );
    }
  }, [variant, cardCount, spacing, PerformanceCardSkeleton, ContentCardSkeleton, ChartCardSkeleton, WidgetSkeleton]);





  return (
    <ErrorBoundary
      fallback={
        <Alert severity="error" sx={{ m: 2 }}>
          <Typography variant="body2">
            Dashboard skeleton unavailable
          </Typography>
        </Alert>
      }
    >
      <Zoom in={true} timeout={300}>
        <Box
          ref={skeletonRef}
          className={className}
          data-testid={testId}
          sx={{
            minHeight: '100vh',
            background: `linear-gradient(135deg,
              ${alpha(theme.palette.background.default, 0.95)} 0%,
              ${alpha(theme.palette.background.default, 0.85)} 100%)`,
            position: 'relative',
            overflow: 'hidden'
          }}
          {...props}
        >
          <Box sx={{ p: { xs: 2, sm: 3 } }}>
            {showHeader && <HeaderSkeleton />}
            {renderSkeleton()}
          </Box>
        </Box>
      </Zoom>
    </ErrorBoundary>
  );
}));

/**
 * Comprehensive PropTypes validation - Production Ready
 */
DashboardSkeleton.propTypes = {
  /** Skeleton variant */
  variant: PropTypes.oneOf(['default', 'compact', 'detailed']),

  /** Number of skeleton cards to display */
  cardCount: PropTypes.number,

  /** Whether to show header skeleton */
  showHeader: PropTypes.bool,

  /** Whether to show chart skeletons */
  showCharts: PropTypes.bool,

  /** Grid spacing */
  spacing: PropTypes.number,

  /** Animation speed */
  animationSpeed: PropTypes.oneOf(['slow', 'normal', 'fast', 'instant']),

  /** Shimmer intensity */
  shimmerIntensity: PropTypes.oneOf(['low', 'medium', 'high']),

  /** Enable customization functionality */
  enableCustomization: PropTypes.bool,

  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,

  /** Enable performance monitoring */
  enablePerformanceMonitoring: PropTypes.bool,

  /** Enable plan upgrade functionality */
  enablePlanUpgrade: PropTypes.bool,

  /** Enable reduced motion */
  enableReducedMotion: PropTypes.bool,

  /** Skeleton template type */
  skeletonTemplate: PropTypes.oneOf(['dashboard', 'analytics', 'content', 'custom']),

  /** Loading progress percentage */
  loadingProgress: PropTypes.number,

  /** Callback when animation completes */
  onAnimationComplete: PropTypes.func,

  /** Callback when performance metrics update */
  onPerformanceUpdate: PropTypes.func,

  /** Additional CSS class name */
  className: PropTypes.string,

  /** Test ID for testing */
  'data-testid': PropTypes.string
};

/**
 * Default props with enterprise-grade defaults - Production Ready
 */
DashboardSkeleton.defaultProps = {
  variant: 'default',
  cardCount: 8,
  showHeader: true,
  showCharts: true,
  spacing: 3,
  animationSpeed: 'normal',
  shimmerIntensity: 'medium',
  enableCustomization: true,
  enableAccessibility: true,
  enablePerformanceMonitoring: true,
  enablePlanUpgrade: true,
  enableReducedMotion: false,
  skeletonTemplate: 'dashboard',
  loadingProgress: null,
  onAnimationComplete: null,
  onPerformanceUpdate: null,
  className: '',
  'data-testid': 'dashboard-skeleton'
};

/**
 * Display name for debugging - Production Ready
 */
DashboardSkeleton.displayName = 'DashboardSkeleton';

export default DashboardSkeleton;

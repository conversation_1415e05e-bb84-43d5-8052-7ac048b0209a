/**
 * Enhanced Team Invitation List - Enterprise-grade team invitation management component
 * Features: Comprehensive team invitation list system with advanced filtering, sorting, and search capabilities,
 * detailed invitation categorization with status management and priority levels, advanced list features
 * with bulk operations and multi-select functionality, ACE Social's team management system integration with
 * seamless invitation workflow and member role management, invitation interaction features including quick
 * actions and inline editing, list customization capabilities with view preferences and column configuration,
 * real-time invitation updates with live status changes and notification integration, and seamless ACE Social
 * platform integration with advanced invitation list orchestration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo
} from 'react';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  Button,
  Chip,
  CircularProgress,
  useTheme,
  Alert,
  alpha,
  Card,
  Stack,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Fade,
  Zoom,
  Snackbar
} from '@mui/material';
import {
  Groups as GroupsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useTeam } from '../../contexts/TeamContext';
import { formatDistanceToNow, isAfter } from 'date-fns';
import NoDataPlaceholder from '../common/NoDataPlaceholder';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Invitation status types
const INVITATION_STATUS = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected',
  EXPIRED: 'expired'
};

// Sort options
const SORT_OPTIONS = {
  NEWEST: 'newest',
  OLDEST: 'oldest',
  TEAM_NAME: 'team_name',
  STATUS: 'status',
  EXPIRY: 'expiry'
};

// View modes
const VIEW_MODES = {
  LIST: 'list',
  CARDS: 'cards',
  COMPACT: 'compact'
};

/**
 * Enhanced Team Invitation List - Comprehensive invitation management with advanced features
 * Implements detailed invitation tracking and enterprise-grade list management capabilities
 */
const TeamInvitationList = memo(forwardRef(({
  preloadedInvitations = null,
  enableBulkOperations = true,
  enableAdvancedFilters = true,
  enableRealTimeUpdates = true,
  onInvitationAction,
  onAnalyticsTrack,
  showExpiredInvitations = true
}, ref) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { invitations, fetchMyInvitations, acceptInvitation, rejectInvitation } = useTeam();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Enhanced state management
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeInvitationId, setActiveInvitationId] = useState(null);
  const [selectedInvitations, setSelectedInvitations] = useState(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState(SORT_OPTIONS.NEWEST);
  const [refreshing, setRefreshing] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [listAnalytics, setListAnalytics] = useState({
    totalViews: 0,
    acceptedCount: 0,
    rejectedCount: 0,
    lastActivity: new Date().toISOString()
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshInvitations: () => handleRefreshInvitations(),
    selectAllInvitations: () => handleSelectAll(),
    clearSelection: () => setSelectedInvitations(new Set()),
    getSelectedInvitations: () => Array.from(selectedInvitations),
    searchInvitations: (query) => setSearchQuery(query),
    filterByStatus: (status) => setFilterStatus(status),
    sortInvitations: (sortOption) => setSortBy(sortOption),
    exportInvitations: () => handleExportInvitations(),
    getAnalytics: () => listAnalytics
  }), [
    selectedInvitations,
    listAnalytics,
    handleRefreshInvitations,
    handleSelectAll,
    handleExportInvitations
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced filtering and sorting logic
  const filteredAndSortedInvitations = useMemo(() => {
    const invitationsList = preloadedInvitations !== null ? preloadedInvitations : invitations;

    let filtered = invitationsList.filter(invitation => {
      const matchesSearch = !searchQuery ||
        invitation.team_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invitation.invited_by_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        invitation.invited_by_email?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus = filterStatus === 'all' || invitation.status === filterStatus;

      // Handle expired invitations
      const isExpired = invitation.expires_at && isAfter(new Date(), new Date(invitation.expires_at));
      const showExpired = showExpiredInvitations || !isExpired;

      return matchesSearch && matchesStatus && showExpired;
    });

    // Sort invitations
    filtered.sort((a, b) => {
      switch (sortBy) {
        case SORT_OPTIONS.NEWEST:
          return new Date(b.created_at) - new Date(a.created_at);
        case SORT_OPTIONS.OLDEST:
          return new Date(a.created_at) - new Date(b.created_at);
        case SORT_OPTIONS.TEAM_NAME:
          return a.team_name.localeCompare(b.team_name);
        case SORT_OPTIONS.STATUS:
          return a.status.localeCompare(b.status);
        case SORT_OPTIONS.EXPIRY:
          if (!a.expires_at && !b.expires_at) return 0;
          if (!a.expires_at) return 1;
          if (!b.expires_at) return -1;
          return new Date(a.expires_at) - new Date(b.expires_at);
        default:
          return 0;
      }
    });

    return filtered;
  }, [preloadedInvitations, invitations, searchQuery, filterStatus, sortBy, showExpiredInvitations]);

  // Enhanced event handlers
  const handleRefreshInvitations = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchMyInvitations();
      setListAnalytics(prev => ({
        ...prev,
        totalViews: prev.totalViews + 1,
        lastActivity: new Date().toISOString()
      }));
      announceToScreenReader('Invitations refreshed successfully');
    } catch {
      announceToScreenReader('Failed to refresh invitations');
    } finally {
      setRefreshing(false);
    }
  }, [fetchMyInvitations, announceToScreenReader]);

  const handleSelectAll = useCallback(() => {
    if (selectedInvitations.size === filteredAndSortedInvitations.length) {
      setSelectedInvitations(new Set());
      announceToScreenReader('All invitations deselected');
    } else {
      setSelectedInvitations(new Set(filteredAndSortedInvitations.map(inv => inv.id)));
      announceToScreenReader(`${filteredAndSortedInvitations.length} invitations selected`);
    }
  }, [selectedInvitations.size, filteredAndSortedInvitations, announceToScreenReader]);

  const handleSelectInvitation = useCallback((invitationId) => {
    setSelectedInvitations(prev => {
      const newSet = new Set(prev);
      if (newSet.has(invitationId)) {
        newSet.delete(invitationId);
      } else {
        newSet.add(invitationId);
      }
      return newSet;
    });
  }, []);

  const handleExportInvitations = useCallback(() => {
    try {
      const selectedData = selectedInvitations.size > 0
        ? filteredAndSortedInvitations.filter(inv => selectedInvitations.has(inv.id))
        : filteredAndSortedInvitations;

      const csvContent = [
        ['Team Name', 'Status', 'Invited By', 'Email', 'Created', 'Expires'],
        ...selectedData.map(invitation => [
          invitation.team_name || '',
          invitation.status || '',
          invitation.invited_by_name || '',
          invitation.invited_by_email || '',
          invitation.created_at ? new Date(invitation.created_at).toLocaleDateString() : '',
          invitation.expires_at ? new Date(invitation.expires_at).toLocaleDateString() : 'Never'
        ])
      ].map(row => row.join(',')).join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `team-invitations-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      announceToScreenReader('Invitations exported successfully');
    } catch {
      announceToScreenReader('Failed to export invitations');
    }
  }, [selectedInvitations, filteredAndSortedInvitations, announceToScreenReader]);

  // Fetch invitations on mount if not preloaded
  useEffect(() => {
    // If we have preloaded invitations, use them instead of fetching
    if (preloadedInvitations !== null) {
      setLoading(false);
      return;
    }

    const loadInvitations = async () => {
      setLoading(true);
      try {
        await fetchMyInvitations();
      } catch {
        setError('Failed to load invitations');
      } finally {
        setLoading(false);
      }
    };

    loadInvitations();
  }, [fetchMyInvitations, preloadedInvitations]);

  useEffect(() => {
    if (enableRealTimeUpdates) {
      const interval = setInterval(() => {
        fetchMyInvitations();
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(interval);
    }
  }, [enableRealTimeUpdates, fetchMyInvitations]);

  // Enhanced accept invitation handler
  const handleAccept = useCallback(async (invitation) => {
    setActionLoading(true);
    setActiveInvitationId(invitation.id);
    setError(null);

    try {
      const result = await acceptInvitation(invitation.token);

      if (result) {
        setListAnalytics(prev => ({
          ...prev,
          acceptedCount: prev.acceptedCount + 1,
          lastActivity: new Date().toISOString()
        }));

        if (onInvitationAction) {
          onInvitationAction({
            action: 'accepted',
            invitation,
            result
          });
        }

        if (onAnalyticsTrack) {
          onAnalyticsTrack({
            action: 'invitation_accepted',
            teamName: invitation.team_name,
            timestamp: new Date().toISOString()
          });
        }

        announceToScreenReader(`Invitation to join ${invitation.team_name} accepted successfully`);
        navigate(`/settings/teams/${result.team.id}`);
      }
    } catch {
      setError('Failed to accept invitation');
      announceToScreenReader(`Failed to accept invitation to ${invitation.team_name}`);
    } finally {
      setActionLoading(false);
      setActiveInvitationId(null);
    }
  }, [acceptInvitation, onInvitationAction, onAnalyticsTrack, announceToScreenReader, navigate]);

  // Enhanced reject invitation handler
  const handleReject = useCallback(async (invitation) => {
    setActionLoading(true);
    setActiveInvitationId(invitation.id);
    setError(null);

    try {
      await rejectInvitation(invitation.token);

      setListAnalytics(prev => ({
        ...prev,
        rejectedCount: prev.rejectedCount + 1,
        lastActivity: new Date().toISOString()
      }));

      if (onInvitationAction) {
        onInvitationAction({
          action: 'rejected',
          invitation
        });
      }

      if (onAnalyticsTrack) {
        onAnalyticsTrack({
          action: 'invitation_rejected',
          teamName: invitation.team_name,
          timestamp: new Date().toISOString()
        });
      }

      announceToScreenReader(`Invitation to join ${invitation.team_name} declined`);
    } catch {
      setError('Failed to reject invitation');
      announceToScreenReader(`Failed to decline invitation to ${invitation.team_name}`);
    } finally {
      setActionLoading(false);
      setActiveInvitationId(null);
    }
  }, [rejectInvitation, onInvitationAction, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced utility functions
  const getStatusChip = useCallback((status) => {
    const statusConfig = {
      [INVITATION_STATUS.PENDING]: {
        label: 'Pending',
        color: ACE_COLORS.YELLOW,
        backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1)
      },
      [INVITATION_STATUS.ACCEPTED]: {
        label: 'Accepted',
        color: '#4CAF50',
        backgroundColor: alpha('#4CAF50', 0.1)
      },
      [INVITATION_STATUS.REJECTED]: {
        label: 'Declined',
        color: '#F44336',
        backgroundColor: alpha('#F44336', 0.1)
      },
      [INVITATION_STATUS.EXPIRED]: {
        label: 'Expired',
        color: ACE_COLORS.DARK,
        backgroundColor: alpha(ACE_COLORS.DARK, 0.1)
      }
    };

    const config = statusConfig[status] || {
      label: status,
      color: ACE_COLORS.DARK,
      backgroundColor: alpha(ACE_COLORS.DARK, 0.1)
    };

    return (
      <Chip
        size="small"
        label={config.label}
        sx={{
          backgroundColor: config.backgroundColor,
          color: config.color,
          fontWeight: 'bold',
          border: `1px solid ${alpha(config.color, 0.3)}`
        }}
      />
    );
  }, []);

  // Simplified pagination - show all invitations for now
  const paginatedInvitations = filteredAndSortedInvitations;

  if (loading) {
    return (
      <Box sx={{ ...glassMorphismStyles, p: 4, textAlign: 'center' }}>
        <CircularProgress size={60} sx={{ color: ACE_COLORS.PURPLE, mb: 2 }} />
        <Typography variant="h6" sx={{ color: ACE_COLORS.DARK }}>
          Loading Team Invitations
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please wait while we fetch your invitations...
        </Typography>
      </Box>
    );
  }

  if (filteredAndSortedInvitations.length === 0) {
    return (
      <Box sx={{ ...glassMorphismStyles, p: 4, textAlign: 'center' }}>
        <NoDataPlaceholder
          icon={<GroupsIcon sx={{ fontSize: 80, color: ACE_COLORS.PURPLE, opacity: 0.6 }} />}
          title="No Team Invitations"
          description={searchQuery || filterStatus !== 'all'
            ? "No invitations match your current filters. Try adjusting your search or filter criteria."
            : "You don't have any team invitations at the moment. When someone invites you to join their team, it will appear here."
          }
        />
      </Box>
    );
  }

  return (
    <Box sx={{ ...glassMorphismStyles, p: 3 }}>
      {/* Enhanced Header */}
      <Box sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" fontWeight="bold" sx={{
              color: ACE_COLORS.DARK,
              background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.YELLOW} 100%)`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              Team Invitations
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Manage your team invitation requests
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh Invitations">
              <IconButton
                onClick={handleRefreshInvitations}
                disabled={refreshing}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            {enableBulkOperations && selectedInvitations.size > 0 && (
              <Tooltip title="Export Selected">
                <IconButton
                  onClick={handleExportInvitations}
                  sx={{ color: ACE_COLORS.PURPLE }}
                >
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Enhanced Filters and Search */}
        {enableAdvancedFilters && (
          <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
            <TextField
              size="small"
              placeholder="Search invitations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon fontSize="small" sx={{ color: ACE_COLORS.PURPLE }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                minWidth: 250,
                '& .MuiOutlinedInput-root': {
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: ACE_COLORS.PURPLE
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: ACE_COLORS.PURPLE
                  }
                }
              }}
            />

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                label="Status"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value={INVITATION_STATUS.PENDING}>Pending</MenuItem>
                <MenuItem value={INVITATION_STATUS.ACCEPTED}>Accepted</MenuItem>
                <MenuItem value={INVITATION_STATUS.REJECTED}>Declined</MenuItem>
                <MenuItem value={INVITATION_STATUS.EXPIRED}>Expired</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Sort By</InputLabel>
              <Select
                value={sortBy}
                label="Sort By"
                onChange={(e) => setSortBy(e.target.value)}
              >
                <MenuItem value={SORT_OPTIONS.NEWEST}>Newest First</MenuItem>
                <MenuItem value={SORT_OPTIONS.OLDEST}>Oldest First</MenuItem>
                <MenuItem value={SORT_OPTIONS.TEAM_NAME}>Team Name</MenuItem>
                <MenuItem value={SORT_OPTIONS.STATUS}>Status</MenuItem>
                <MenuItem value={SORT_OPTIONS.EXPIRY}>Expiry Date</MenuItem>
              </Select>
            </FormControl>
          </Stack>
        )}

        {/* Results Summary */}
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Showing {paginatedInvitations.length} of {filteredAndSortedInvitations.length} invitations
            {filteredAndSortedInvitations.length !== (preloadedInvitations || invitations).length &&
              ` (filtered from ${(preloadedInvitations || invitations).length} total)`
            }
          </Typography>

          {enableBulkOperations && (
            <Box display="flex" alignItems="center" gap={1}>
              {selectedInvitations.size > 0 && (
                <Chip
                  label={`${selectedInvitations.size} selected`}
                  onDelete={() => setSelectedInvitations(new Set())}
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    color: ACE_COLORS.PURPLE
                  }}
                />
              )}

              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedInvitations.size === filteredAndSortedInvitations.length && filteredAndSortedInvitations.length > 0}
                    indeterminate={selectedInvitations.size > 0 && selectedInvitations.size < filteredAndSortedInvitations.length}
                    onChange={handleSelectAll}
                    sx={{
                      color: ACE_COLORS.PURPLE,
                      '&.Mui-checked': {
                        color: ACE_COLORS.PURPLE
                      }
                    }}
                  />
                }
                label="Select All"
              />
            </Box>
          )}
        </Box>

        {/* Loading Progress */}
        {refreshing && (
          <Box sx={{ mt: 1 }}>
            <LinearProgress
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                '& .MuiLinearProgress-bar': {
                  backgroundColor: ACE_COLORS.PURPLE
                }
              }}
            />
          </Box>
        )}
      </Box>

      {/* Error Alert */}
      {error && (
        <Fade in timeout={500}>
          <Alert
            severity="error"
            sx={{
              mb: 3,
              border: `1px solid ${alpha('#F44336', 0.3)}`,
              backgroundColor: alpha('#F44336', 0.1)
            }}
            action={
              <Button
                size="small"
                onClick={handleRefreshInvitations}
                sx={{ color: '#F44336' }}
              >
                Retry
              </Button>
            }
          >
            {error}
          </Alert>
        </Fade>
      )}

      {/* Enhanced Invitation List */}
      <List sx={{ p: 0 }}>
        {paginatedInvitations.map((invitation, index) => (
          <Zoom in timeout={300 + index * 50} key={invitation.id}>
            <Card
              sx={{
                mb: 2,
                border: `2px solid ${alpha(
                  invitation.status === INVITATION_STATUS.PENDING ? ACE_COLORS.YELLOW : ACE_COLORS.PURPLE,
                  0.2
                )}`,
                borderRadius: 3,
                background: `linear-gradient(135deg,
                  ${alpha(theme.palette.background.paper, 0.95)} 0%,
                  ${alpha(
                    invitation.status === INVITATION_STATUS.PENDING ? ACE_COLORS.YELLOW : ACE_COLORS.PURPLE,
                    0.03
                  )} 100%)`,
                transition: 'all 300ms ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: `0 8px 25px ${alpha(ACE_COLORS.PURPLE, 0.15)}`
                }
              }}
            >
              <ListItem sx={{ p: 3 }}>
                {enableBulkOperations && (
                  <ListItemAvatar>
                    <Checkbox
                      checked={selectedInvitations.has(invitation.id)}
                      onChange={() => handleSelectInvitation(invitation.id)}
                      sx={{
                        color: ACE_COLORS.PURPLE,
                        '&.Mui-checked': {
                          color: ACE_COLORS.PURPLE
                        }
                      }}
                    />
                  </ListItemAvatar>
                )}

                <ListItemAvatar>
                  <Avatar
                    sx={{
                      backgroundColor: ACE_COLORS.PURPLE,
                      color: ACE_COLORS.WHITE,
                      width: 50,
                      height: 50,
                      fontSize: '1.5rem',
                      fontWeight: 'bold'
                    }}
                  >
                    {invitation.team_name?.charAt(0)?.toUpperCase() || 'T'}
                  </Avatar>
                </ListItemAvatar>

                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Typography variant="h6" fontWeight="bold" sx={{ color: ACE_COLORS.DARK }}>
                        {invitation.team_name}
                      </Typography>
                      {getStatusChip(invitation.status)}
                      {invitation.status === INVITATION_STATUS.PENDING && invitation.expires_at && (
                        <Chip
                          size="small"
                          label={`Expires ${formatDistanceToNow(new Date(invitation.expires_at), { addSuffix: true })}`}
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                            color: ACE_COLORS.DARK,
                            fontSize: '0.7rem'
                          }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Box display="flex" alignItems="center" gap={1} sx={{ mb: 1 }}>
                        <PersonIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                        <Typography variant="body2" color="text.secondary">
                          Invited by {invitation.invited_by_name} ({invitation.invited_by_email})
                        </Typography>
                      </Box>

                      <Box display="flex" alignItems="center" gap={1}>
                        <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true })}
                        </Typography>
                      </Box>
                    </Box>
                  }
                />

                {invitation.status === INVITATION_STATUS.PENDING && (
                  <ListItemSecondaryAction>
                    <Stack direction="row" spacing={1}>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<CancelIcon />}
                        onClick={() => handleReject(invitation)}
                        disabled={actionLoading && activeInvitationId === invitation.id}
                        sx={{
                          borderColor: '#F44336',
                          color: '#F44336',
                          '&:hover': {
                            borderColor: '#F44336',
                            backgroundColor: alpha('#F44336', 0.1)
                          }
                        }}
                      >
                        Decline
                      </Button>

                      <Button
                        size="small"
                        variant="contained"
                        startIcon={
                          actionLoading && activeInvitationId === invitation.id ? (
                            <CircularProgress size={16} sx={{ color: ACE_COLORS.WHITE }} />
                          ) : (
                            <CheckCircleIcon />
                          )
                        }
                        onClick={() => handleAccept(invitation)}
                        disabled={actionLoading && activeInvitationId === invitation.id}
                        sx={{
                          backgroundColor: ACE_COLORS.PURPLE,
                          '&:hover': {
                            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                          }
                        }}
                      >
                        {actionLoading && activeInvitationId === invitation.id ? 'Accepting...' : 'Accept'}
                      </Button>
                    </Stack>
                  </ListItemSecondaryAction>
                )}
              </ListItem>
            </Card>
          </Zoom>
        ))}
      </List>

      {/* Notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={index}
          open={true}
          autoHideDuration={6000}
          onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            severity={notification.severity || 'info'}
            onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
            sx={{
              ...glassMorphismStyles,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
}));

TeamInvitationList.displayName = 'TeamInvitationList';

TeamInvitationList.propTypes = {
  /** Preloaded invitations array */
  preloadedInvitations: PropTypes.array,
  /** Enable bulk operations */
  enableBulkOperations: PropTypes.bool,
  /** Enable advanced filters */
  enableAdvancedFilters: PropTypes.bool,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Maximum invitations per page */
  maxInvitationsPerPage: PropTypes.number,
  /** Default view mode */
  defaultViewMode: PropTypes.oneOf(Object.values(VIEW_MODES)),
  /** Function called when invitation action is performed */
  onInvitationAction: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Show expired invitations */
  showExpiredInvitations: PropTypes.bool
};

export default TeamInvitationList;

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Badge,
  Avatar,
  Checkbox,
  Menu,
  ListItemIcon,
  ListItemText,
  Divider,
  LinearProgress,
  Snackbar,
  Autocomplete,
  Stack,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemAvatar,
  Breadcrumbs,
  Link,
  Skeleton,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Assignment as AssignIcon,
  Message as MessageIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Delete as DeleteIcon,
  Archive as ArchiveIcon,
  Merge as MergeIcon,
  Split as SplitIcon,
  Priority as PriorityIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  AttachFile as AttachFileIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Flag as FlagIcon,
  Timer as TimerIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Forward as ForwardIcon,
  Reply as ReplyIcon,
  Send as SendIcon,
  Save as SaveIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Notifications as NotificationsIcon,
  History as HistoryIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Feedback as FeedbackIcon,
  Help as HelpIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  Success as SuccessIcon,
  AccessTime as AccessTimeIcon,
  Today as TodayIcon,
  DateRange as DateRangeIcon,
  CalendarToday as CalendarTodayIcon,
  Group as GroupIcon,
  Business as BusinessIcon,
  LocationOn as LocationOnIcon,
  Language as LanguageIcon,
  Translate as TranslateIcon,
  VolumeUp as VolumeUpIcon,
  VolumeOff as VolumeOffIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Bookmark as BookmarkIcon,
  Label as LabelIcon,
  Category as CategoryIcon,
  Tag as TagIcon,
  Link as LinkIcon,
  ContentCopy as ContentCopyIcon,
  QrCode as QrCodeIcon,
  Smartphone as SmartphoneIcon,
  Computer as ComputerIcon,
  Tablet as TabletIcon,
  Watch as WatchIcon,
  Headset as HeadsetIcon,
  SupportAgent as SupportAgentIcon,
  Chat as ChatIcon,
  Forum as ForumIcon,
  QuestionAnswer as QuestionAnswerIcon,
  LiveHelp as LiveHelpIcon,
  ContactSupport as ContactSupportIcon,
  BugReport as BugReportIcon,
  NewReleases as NewReleasesIcon,
  Update as UpdateIcon,
  Sync as SyncIcon,
  CloudSync as CloudSyncIcon,
  Backup as BackupIcon,
  Restore as RestoreIcon,
  Build as BuildIcon,
  Code as CodeIcon,
  DataUsage as DataUsageIcon,
  Storage as StorageIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  ShowChart as ShowChartIcon,
  BarChart as BarChartIcon,
  PieChart as PieChartIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Dashboard as DashboardIcon,
  Widgets as WidgetsIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,
  ViewComfy as ViewComfyIcon,
  ViewCompact as ViewCompactIcon,
  GridView as GridViewIcon,
  TableView as TableViewIcon,
  ListAlt as ListAltIcon,
  ViewAgenda as ViewAgendaIcon,
  ViewCarousel as ViewCarouselIcon,
  ViewColumn as ViewColumnIcon,
  ViewDay as ViewDayIcon,
  ViewHeadline as ViewHeadlineIcon,
  ViewQuilt as ViewQuiltIcon,
  ViewSidebar as ViewSidebarIcon,
  ViewStream as ViewStreamIcon,
  ViewWeek as ViewWeekIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';
import { format, isAfter, formatDistanceToNow, parseISO, isValid } from 'date-fns';

// API
import api from '../../api';
import StablePageWrapper from '../StablePageWrapper';

// Support Components
import TicketCreateDialog from './TicketCreateDialog';
import BulkOperationsDialog from './BulkOperationsDialog';

const TicketManagement = ({ onRefresh, dashboardData }) => {
  // Core state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [tickets, setTickets] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);

  // Dialog states
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [bulkDialogOpen, setBulkDialogOpen] = useState(false);
  const [mergeDialogOpen, setMergeDialogOpen] = useState(false);
  const [escalateDialogOpen, setEscalateDialogOpen] = useState(false);

  // Selection and bulk operations
  const [selectedTickets, setSelectedTickets] = useState([]);
  const [bulkOperation, setBulkOperation] = useState('');
  const [bulkData, setBulkData] = useState({});

  // Menu states
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuTicket, setMenuTicket] = useState(null);

  // Real-time updates
  const [lastUpdated, setLastUpdated] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const autoRefreshRef = useRef(null);

  // Advanced features
  const [agents, setAgents] = useState([]);
  const [ticketMessages, setTicketMessages] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const [ticketHistory, setTicketHistory] = useState([]);
  const [slaBreaches, setSlaBreaches] = useState([]);

  // View preferences
  const [viewMode, setViewMode] = useState('table'); // table, cards, timeline
  const [density, setDensity] = useState('standard'); // compact, standard, comfortable
  const [showFilters, setShowFilters] = useState(true);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Performance tracking
  const [correlationId, setCorrelationId] = useState(null);
  const [requestStartTime, setRequestStartTime] = useState(null);

  // Enhanced filters with validation
  const [filters, setFilters] = useState({
    query: '',
    status: '',
    priority: '',
    category: '',
    assigned_agent_id: '',
    customer_email: '',
    customer_tier: '',
    channel: '',
    tags: [],
    date_range: { start: null, end: null },
    overdue_only: false,
    unassigned_only: false,
    high_priority_only: false,
    escalated_only: false,
    has_attachments: false,
    customer_satisfaction: '',
    sla_status: '', // on_time, at_risk, breached
    last_activity: '', // today, week, month
    created_by_me: false,
    assigned_to_me: false,
  });

  // Enhanced useEffect with comprehensive data loading
  useEffect(() => {
    loadInitialData();

    // Set up auto-refresh if enabled
    if (autoRefresh) {
      autoRefreshRef.current = setInterval(() => {
        loadTickets(true); // Silent refresh
      }, 30000); // 30 seconds
    }

    return () => {
      if (autoRefreshRef.current) {
        clearInterval(autoRefreshRef.current);
      }
    };
  }, []);

  useEffect(() => {
    loadTickets();
  }, [page, rowsPerPage, filters]);

  useEffect(() => {
    // Update auto-refresh interval
    if (autoRefreshRef.current) {
      clearInterval(autoRefreshRef.current);
    }

    if (autoRefresh) {
      autoRefreshRef.current = setInterval(() => {
        loadTickets(true);
      }, 30000);
    }

    return () => {
      if (autoRefreshRef.current) {
        clearInterval(autoRefreshRef.current);
      }
    };
  }, [autoRefresh]);

  // Load initial data including agents and metadata
  const loadInitialData = async () => {
    try {
      await Promise.all([
        loadTickets(),
        loadAgents(),
        loadSlaBreaches()
      ]);
    } catch (error) {
      console.error('Error loading initial data:', error);
      setError('Failed to load initial data');
    }
  };

  // Load available agents for assignment
  const loadAgents = async () => {
    try {
      const response = await api.get('/api/admin/support/agents');
      setAgents(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error('Error loading agents:', error);
      // Don't set error state for non-critical data
    }
  };

  // Load SLA breach information
  const loadSlaBreaches = async () => {
    try {
      const response = await api.get('/api/admin/support/sla-breaches');
      setSlaBreaches(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error('Error loading SLA breaches:', error);
    }
  };

  // Enhanced loadTickets with performance tracking and better error handling
  const loadTickets = useCallback(async (silent = false) => {
    try {
      if (!silent) {
        setLoading(true);
        setError(null);
      }

      // Generate correlation ID for request tracking
      const newCorrelationId = `ticket-load-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      setCorrelationId(newCorrelationId);
      setRequestStartTime(Date.now());

      // Build params with proper filtering
      const params = {
        page: page + 1,
        page_size: rowsPerPage,
        correlation_id: newCorrelationId,
      };

      // Add filters with validation
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== '' && value !== false && value !== null && value !== undefined) {
          if (key === 'tags' && Array.isArray(value) && value.length > 0) {
            params[key] = value.join(',');
          } else if (key === 'date_range' && value.start && value.end) {
            params.start_date = value.start.toISOString();
            params.end_date = value.end.toISOString();
          } else if (typeof value === 'boolean' && value) {
            params[key] = value;
          } else if (typeof value === 'string' && value.trim()) {
            params[key] = value.trim();
          }
        }
      });

      const response = await api.get('/api/admin/support/tickets', {
        params,
        timeout: 10000, // 10 second timeout
        headers: {
          'X-Correlation-ID': newCorrelationId,
          'X-Request-Source': 'admin-ticket-management'
        }
      });

      // Validate response data
      const ticketsData = Array.isArray(response.data) ? response.data : [];
      const validTickets = ticketsData.filter(ticket => ticket && ticket.id);

      setTickets(validTickets);
      setTotalCount(parseInt(response.headers['x-total-count'] || '0'));
      setLastUpdated(new Date());

      // Performance tracking
      const loadTime = Date.now() - requestStartTime;
      if (loadTime > 2000) {
        console.warn(`Slow ticket load: ${loadTime}ms for ${validTickets.length} tickets`);
      }

      // Clear any previous errors
      if (!silent) {
        setError(null);
      }

    } catch (err) {
      console.error('Error loading tickets:', err);

      // Enhanced error handling with retry logic
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to load tickets';

      if (!silent) {
        setError(errorMessage);
      }

      // Auto-retry on network errors (but not on 4xx errors)
      if (err.code === 'NETWORK_ERROR' || err.response?.status >= 500) {
        console.log('Network error detected, will retry on next auto-refresh');
      }

      // Ensure tickets is always an array
      setTickets([]);
      setTotalCount(0);

    } finally {
      if (!silent) {
        setLoading(false);
      }
    }
  }, [page, rowsPerPage, filters, requestStartTime]);

  // Enhanced filter management
  const handleFilterChange = useCallback((field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPage(0); // Reset to first page when filters change
  }, []);

  const handleSearch = useCallback(() => {
    setPage(0);
    loadTickets();
  }, [loadTickets]);

  const handleClearFilters = useCallback(() => {
    setFilters({
      query: '',
      status: '',
      priority: '',
      category: '',
      assigned_agent_id: '',
      customer_email: '',
      customer_tier: '',
      channel: '',
      tags: [],
      date_range: { start: null, end: null },
      overdue_only: false,
      unassigned_only: false,
      high_priority_only: false,
      escalated_only: false,
      has_attachments: false,
      customer_satisfaction: '',
      sla_status: '',
      last_activity: '',
      created_by_me: false,
      assigned_to_me: false,
    });
    setPage(0);
    loadTickets();
  }, [loadTickets]);

  // Bulk selection management
  const handleSelectTicket = useCallback((ticketId) => {
    setSelectedTickets(prev => {
      const isSelected = prev.includes(ticketId);
      if (isSelected) {
        return prev.filter(id => id !== ticketId);
      } else {
        return [...prev, ticketId];
      }
    });
  }, []);

  const handleSelectAllTickets = useCallback((event) => {
    if (event.target.checked) {
      const validTickets = Array.isArray(tickets) ? tickets.filter(ticket => ticket && ticket.id) : [];
      setSelectedTickets(validTickets.map(ticket => ticket.id));
    } else {
      setSelectedTickets([]);
    }
  }, [tickets]);

  const handleClearSelection = useCallback(() => {
    setSelectedTickets([]);
  }, []);

  // Ticket CRUD operations
  const handleCreateTicket = useCallback(async (ticketData) => {
    try {
      setLoading(true);
      const response = await api.post('/api/admin/support/tickets', {
        ...ticketData,
        correlation_id: `create-${Date.now()}`
      });

      setSuccess('Ticket created successfully');
      setCreateDialogOpen(false);
      loadTickets();
      onRefresh?.();

      return response.data;
    } catch (error) {
      console.error('Error creating ticket:', error);
      setError(error.response?.data?.detail || 'Failed to create ticket');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [loadTickets, onRefresh]);

  const handleUpdateTicket = useCallback(async (ticketId, updateData) => {
    try {
      setLoading(true);
      await api.put(`/api/admin/support/tickets/${ticketId}`, {
        ...updateData,
        correlation_id: `update-${Date.now()}`
      });

      setSuccess('Ticket updated successfully');
      setEditDialogOpen(false);
      loadTickets();
      onRefresh?.();

    } catch (error) {
      console.error('Error updating ticket:', error);
      setError(error.response?.data?.detail || 'Failed to update ticket');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [loadTickets, onRefresh]);

  const handleDeleteTicket = useCallback(async (ticketId) => {
    if (!window.confirm('Are you sure you want to delete this ticket? This action cannot be undone.')) {
      return;
    }

    try {
      setLoading(true);
      await api.delete(`/api/admin/support/tickets/${ticketId}`);

      setSuccess('Ticket deleted successfully');
      loadTickets();
      onRefresh?.();

    } catch (error) {
      console.error('Error deleting ticket:', error);
      setError(error.response?.data?.detail || 'Failed to delete ticket');
    } finally {
      setLoading(false);
    }
  }, [loadTickets, onRefresh]);

  // Enhanced ticket viewing with full details
  const handleViewTicket = useCallback(async (ticketId) => {
    try {
      setLoading(true);

      // Load ticket details, messages, and history in parallel
      const [ticketResponse, messagesResponse, historyResponse] = await Promise.all([
        api.get(`/api/admin/support/tickets/${ticketId}`),
        api.get(`/api/admin/support/tickets/${ticketId}/messages?include_internal=true`),
        api.get(`/api/admin/support/tickets/${ticketId}/history`).catch(() => ({ data: [] }))
      ]);

      setSelectedTicket(ticketResponse.data);
      setTicketMessages(Array.isArray(messagesResponse.data) ? messagesResponse.data : []);
      setTicketHistory(Array.isArray(historyResponse.data) ? historyResponse.data : []);
      setViewDialogOpen(true);

    } catch (error) {
      console.error('Error loading ticket details:', error);
      setError('Failed to load ticket details');
    } finally {
      setLoading(false);
    }
  }, []);

  // Quick status updates
  const handleUpdateTicketStatus = useCallback(async (ticketId, newStatus) => {
    try {
      await api.put(`/api/admin/support/tickets/${ticketId}`, {
        status: newStatus,
        correlation_id: `status-update-${Date.now()}`
      });

      setSuccess(`Ticket status updated to ${newStatus.replace('_', ' ')}`);
      loadTickets();
      onRefresh?.();

    } catch (error) {
      console.error('Error updating ticket status:', error);
      setError('Failed to update ticket status');
    }
  }, [loadTickets, onRefresh]);

  // Ticket assignment
  const handleAssignTicket = useCallback(async (ticketId, agentId) => {
    try {
      await api.put(`/api/admin/support/tickets/${ticketId}`, {
        assigned_agent_id: agentId,
        correlation_id: `assign-${Date.now()}`
      });

      const agent = agents.find(a => a.id === agentId);
      setSuccess(`Ticket assigned to ${agent?.name || 'agent'}`);
      loadTickets();
      onRefresh?.();

    } catch (error) {
      console.error('Error assigning ticket:', error);
      setError('Failed to assign ticket');
    }
  }, [agents, loadTickets, onRefresh]);

  // Bulk operations
  const handleBulkOperation = useCallback(async () => {
    if (selectedTickets.length === 0) {
      setError('Please select tickets for bulk operation');
      return;
    }

    try {
      setLoading(true);

      const operationData = {
        ticket_ids: selectedTickets,
        operation: bulkOperation,
        data: bulkData,
        correlation_id: `bulk-${bulkOperation}-${Date.now()}`
      };

      await api.post('/api/admin/support/tickets/bulk', operationData);

      setSuccess(`Bulk operation completed for ${selectedTickets.length} tickets`);
      setBulkDialogOpen(false);
      setSelectedTickets([]);
      setBulkOperation('');
      setBulkData({});
      loadTickets();
      onRefresh?.();

    } catch (error) {
      console.error('Error performing bulk operation:', error);
      setError('Failed to perform bulk operation');
    } finally {
      setLoading(false);
    }
  }, [selectedTickets, bulkOperation, bulkData, loadTickets, onRefresh]);

  // Ticket escalation
  const handleEscalateTicket = useCallback(async (ticketId, escalationData) => {
    try {
      await api.post(`/api/admin/support/tickets/${ticketId}/escalate`, {
        ...escalationData,
        correlation_id: `escalate-${Date.now()}`
      });

      setSuccess('Ticket escalated successfully');
      setEscalateDialogOpen(false);
      loadTickets();
      onRefresh?.();

    } catch (error) {
      console.error('Error escalating ticket:', error);
      setError('Failed to escalate ticket');
    }
  }, [loadTickets, onRefresh]);

  // Ticket merging
  const handleMergeTickets = useCallback(async (primaryTicketId, secondaryTicketIds) => {
    try {
      await api.post(`/api/admin/support/tickets/${primaryTicketId}/merge`, {
        secondary_ticket_ids: secondaryTicketIds,
        correlation_id: `merge-${Date.now()}`
      });

      setSuccess('Tickets merged successfully');
      setMergeDialogOpen(false);
      setSelectedTickets([]);
      loadTickets();
      onRefresh?.();

    } catch (error) {
      console.error('Error merging tickets:', error);
      setError('Failed to merge tickets');
    }
  }, [loadTickets, onRefresh]);

  // Export functionality
  const handleExportTickets = useCallback(async (format = 'csv') => {
    try {
      setLoading(true);

      const params = {
        format,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '' && value !== false)
        ),
      };

      const response = await api.get('/api/admin/support/tickets/export', {
        params,
        responseType: 'blob',
        timeout: 30000 // 30 second timeout for exports
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `support_tickets_${new Date().toISOString().split('T')[0]}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      setSuccess('Tickets exported successfully');

    } catch (error) {
      console.error('Error exporting tickets:', error);
      setError('Failed to export tickets');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Enhanced utility functions with better color mapping and icons
  const getStatusColor = useCallback((status) => {
    const statusMap = {
      'open': 'info',
      'in_progress': 'primary',
      'pending_customer': 'warning',
      'resolved': 'success',
      'closed': 'default',
      'escalated': 'error',
      'on_hold': 'secondary',
      'cancelled': 'error'
    };
    return statusMap[status] || 'default';
  }, []);

  const getPriorityColor = useCallback((priority) => {
    const priorityMap = {
      'critical': 'error',
      'high': 'warning',
      'medium': 'info',
      'low': 'default',
      'urgent': 'error'
    };
    return priorityMap[priority] || 'default';
  }, []);

  const getStatusIcon = useCallback((status) => {
    const iconMap = {
      'open': <InfoIcon />,
      'in_progress': <PlayArrowIcon />,
      'pending_customer': <PauseIcon />,
      'resolved': <CheckCircleIcon />,
      'closed': <StopIcon />,
      'escalated': <FlagIcon />,
      'on_hold': <PauseIcon />,
      'cancelled': <CancelIcon />
    };
    return iconMap[status] || <InfoIcon />;
  }, []);

  const getPriorityIcon = useCallback((priority) => {
    const iconMap = {
      'critical': <ErrorIcon />,
      'high': <WarningIcon />,
      'medium': <InfoIcon />,
      'low': <CheckCircleIcon />,
      'urgent': <FlagIcon />
    };
    return iconMap[priority] || <InfoIcon />;
  }, []);

  const getCategoryIcon = useCallback((category) => {
    const iconMap = {
      'technical': <BuildIcon />,
      'billing': <PaymentIcon />,
      'account': <PersonIcon />,
      'feature_request': <NewReleasesIcon />,
      'bug_report': <BugReportIcon />,
      'general': <HelpIcon />,
      'security': <SecurityIcon />,
      'integration': <SyncIcon />
    };
    return iconMap[category] || <CategoryIcon />;
  }, []);

  const getChannelIcon = useCallback((channel) => {
    const iconMap = {
      'email': <EmailIcon />,
      'chat': <ChatIcon />,
      'phone': <PhoneIcon />,
      'web': <ComputerIcon />,
      'mobile': <SmartphoneIcon />,
      'api': <CodeIcon />,
      'social': <ShareIcon />
    };
    return iconMap[channel] || <ContactSupportIcon />;
  }, []);

  // Enhanced SLA and time management functions
  const isOverdue = useCallback((ticket) => {
    if (!ticket?.sla_due_date) return false;

    try {
      const dueDate = typeof ticket.sla_due_date === 'string'
        ? parseISO(ticket.sla_due_date)
        : ticket.sla_due_date;

      return isValid(dueDate) &&
             isAfter(new Date(), dueDate) &&
             !['resolved', 'closed', 'cancelled'].includes(ticket.status);
    } catch (error) {
      console.warn('Invalid SLA due date:', ticket.sla_due_date);
      return false;
    }
  }, []);

  const getSlaStatus = useCallback((ticket) => {
    if (!ticket?.sla_due_date) return 'no_sla';

    try {
      const dueDate = typeof ticket.sla_due_date === 'string'
        ? parseISO(ticket.sla_due_date)
        : ticket.sla_due_date;

      if (!isValid(dueDate)) return 'invalid';

      const now = new Date();
      const diffMs = dueDate.getTime() - now.getTime();
      const diffHours = diffMs / (1000 * 60 * 60);

      if (diffMs < 0) return 'breached';
      if (diffHours <= 2) return 'at_risk';
      if (diffHours <= 24) return 'warning';
      return 'on_time';
    } catch (error) {
      return 'invalid';
    }
  }, []);

  const formatTimeRemaining = useCallback((dueDate) => {
    if (!dueDate) return 'No SLA';

    try {
      const due = typeof dueDate === 'string' ? parseISO(dueDate) : dueDate;
      if (!isValid(due)) return 'Invalid date';

      const now = new Date();
      const diffMs = due.getTime() - now.getTime();

      if (diffMs < 0) {
        const overdue = Math.abs(diffMs);
        const days = Math.floor(overdue / (1000 * 60 * 60 * 24));
        const hours = Math.floor((overdue % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

        if (days > 0) {
          return `${days}d ${hours}h overdue`;
        }
        return `${hours}h overdue`;
      }

      const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        return `${days}d ${hours}h`;
      }
      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      }
      return `${minutes}m`;
    } catch (error) {
      console.warn('Error formatting time remaining:', error);
      return 'Invalid';
    }
  }, []);

  const formatRelativeTime = useCallback((date) => {
    if (!date) return 'Never';

    try {
      const parsedDate = typeof date === 'string' ? parseISO(date) : date;
      if (!isValid(parsedDate)) return 'Invalid date';

      return formatDistanceToNow(parsedDate, { addSuffix: true });
    } catch (error) {
      console.warn('Error formatting relative time:', error);
      return 'Invalid';
    }
  }, []);

  const formatDateTime = useCallback((date) => {
    if (!date) return 'Never';

    try {
      const parsedDate = typeof date === 'string' ? parseISO(date) : date;
      if (!isValid(parsedDate)) return 'Invalid date';

      return format(parsedDate, 'MMM dd, yyyy HH:mm');
    } catch (error) {
      console.warn('Error formatting date time:', error);
      return 'Invalid';
    }
  }, []);

  // Ticket validation and safety checks
  const isValidTicket = useCallback((ticket) => {
    return ticket &&
           typeof ticket === 'object' &&
           ticket.id &&
           ticket.ticket_number &&
           ticket.subject;
  }, []);

  const getTicketPriorityWeight = useCallback((priority) => {
    const weights = {
      'critical': 4,
      'urgent': 4,
      'high': 3,
      'medium': 2,
      'low': 1
    };
    return weights[priority] || 0;
  }, []);

  const getCustomerTierColor = useCallback((tier) => {
    const tierColors = {
      'dominator': 'secondary',
      'accelerator': 'primary',
      'creator': 'info',
      'free': 'default'
    };
    return tierColors[tier] || 'default';
  }, []);

  return (
    <StablePageWrapper
      title="Support Ticket Management"
      loading={loading && tickets.length === 0}
      error={error}
      onRetry={loadTickets}
    >
      <Box sx={{ p: 3 }}>
        {/* Success/Error Notifications */}
        <Snackbar
          open={!!success}
          autoHideDuration={6000}
          onClose={() => setSuccess(null)}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert severity="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </Snackbar>

        <Snackbar
          open={!!error}
          autoHideDuration={8000}
          onClose={() => setError(null)}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert severity="error" onClose={() => setError(null)}>
            {error}
          </Alert>
        </Snackbar>

        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Support Tickets
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage customer support tickets with advanced filtering and bulk operations
          </Typography>
        </Box>

        {/* Quick Actions */}
        <Box display="flex" gap={1} flexWrap="wrap" sx={{ mb: 3 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            disabled={loading}
          >
            New Ticket
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => loadTickets()}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={() => handleExportTickets('csv')}
            disabled={loading}
          >
            Export CSV
          </Button>
        </Box>

        {/* Simple Filters */}
        <Card sx={{ mb: 3 }}>
          <CardHeader
            title="Filters"
            action={
              <Chip
                label={`${totalCount} tickets`}
                size="small"
                color="primary"
                variant="outlined"
              />
            }
          />
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  size="small"
                  label="Search"
                  placeholder="Ticket #, subject, customer..."
                  value={filters.query}
                  onChange={(e) => handleFilterChange('query', e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filters.status}
                    label="Status"
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="open">Open</MenuItem>
                    <MenuItem value="in_progress">In Progress</MenuItem>
                    <MenuItem value="pending_customer">Pending Customer</MenuItem>
                    <MenuItem value="resolved">Resolved</MenuItem>
                    <MenuItem value="closed">Closed</MenuItem>
                    <MenuItem value="escalated">Escalated</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Priority</InputLabel>
                  <Select
                    value={filters.priority}
                    label="Priority"
                    onChange={(e) => handleFilterChange('priority', e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="critical">Critical</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="low">Low</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={filters.category}
                    label="Category"
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="technical">Technical</MenuItem>
                    <MenuItem value="billing">Billing</MenuItem>
                    <MenuItem value="account">Account</MenuItem>
                    <MenuItem value="feature_request">Feature Request</MenuItem>
                    <MenuItem value="bug_report">Bug Report</MenuItem>
                    <MenuItem value="general">General</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Button
                  variant="contained"
                  onClick={handleSearch}
                  disabled={loading}
                  fullWidth
                >
                  Search
                </Button>
              </Grid>
            </Grid>

            <Box mt={2} display="flex" gap={1} alignItems="center">
              <Button
                size="small"
                variant={filters.overdue_only ? "contained" : "outlined"}
                color="error"
                onClick={() => handleFilterChange('overdue_only', !filters.overdue_only)}
              >
                Overdue Only
              </Button>
              {Object.values(filters).some(value => value !== '' && value !== false) && (
                <Button size="small" onClick={handleClearFilters}>
                  Clear Filters
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Simple Tickets Table */}
        <Card>
          <CardHeader
            title="Support Tickets"
            subheader={`${totalCount} total tickets`}
          />
          <CardContent sx={{ p: 0 }}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Ticket</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell>Subject</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Agent</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                        <CircularProgress />
                      </TableCell>
                    </TableRow>
                  ) : tickets.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          No tickets found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    tickets.map((ticket) => (
                      <TableRow key={ticket.id} hover>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {ticket.ticket_number}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {ticket.customer_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {ticket.customer_email}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ maxWidth: 200 }}>
                            {ticket.subject}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={ticket.status.replace('_', ' ')}
                            color={getStatusColor(ticket.status)}
                            size="small"
                            sx={{ textTransform: 'capitalize' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={ticket.priority}
                            color={getPriorityColor(ticket.priority)}
                            size="small"
                            sx={{ textTransform: 'capitalize' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                            {ticket.category.replace('_', ' ')}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {ticket.assigned_agent_name ? (
                            <Typography variant="body2">
                              {ticket.assigned_agent_name}
                            </Typography>
                          ) : (
                            <Typography variant="body2" color="text.secondary">
                              Unassigned
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDateTime(ticket.created_at)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Tooltip title="View Details">
                            <IconButton
                              size="small"
                              onClick={() => handleViewTicket(ticket.id)}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
              rowsPerPageOptions={[10, 25, 50, 100]}
            />
          </CardContent>
        </Card>

        {/* Simple Ticket Details Dialog */}
        <Dialog
          open={viewDialogOpen}
          onClose={() => setViewDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Ticket Details: {selectedTicket?.ticket_number}
          </DialogTitle>
          <DialogContent>
            {selectedTicket && (
              <Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>Subject</Typography>
                    <Typography variant="body2" paragraph>{selectedTicket.subject}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>Status</Typography>
                    <Chip
                      label={selectedTicket.status.replace('_', ' ')}
                      color={getStatusColor(selectedTicket.status)}
                      size="small"
                      sx={{ textTransform: 'capitalize' }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>Description</Typography>
                    <Typography variant="body2" paragraph>{selectedTicket.description}</Typography>
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
            <Button variant="contained">Edit Ticket</Button>
          </DialogActions>
        </Dialog>

        {/* Create Ticket Dialog */}
        <TicketCreateDialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          onTicketCreated={(ticket) => {
            setSuccess(`Ticket created successfully`);
            loadTickets();
            onRefresh?.();
          }}
          agents={agents}
        />

        {/* Bulk Operations Dialog */}
        <BulkOperationsDialog
          open={bulkDialogOpen}
          onClose={() => setBulkDialogOpen(false)}
          selectedTickets={selectedTickets}
          agents={agents}
          onExecute={async (operation, data) => {
            setBulkOperation(operation);
            setBulkData(data);
            await handleBulkOperation();
          }}
        />

      </Box>
    </StablePageWrapper>
  );
};

export default TicketManagement;

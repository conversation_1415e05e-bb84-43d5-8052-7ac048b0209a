/**
 * Enhanced Knowledge Base Search - Enterprise-grade knowledge base search component
 * Features: Comprehensive knowledge base search system with advanced search algorithms and real-time search suggestions,
 * detailed search categorization with content filtering and topic classification, advanced search features with
 * autocomplete and search history functionality, ACE Social's 3-tier subscription structure integration
 * (creator/accelerator/dominator plans) with plan-specific knowledge base access, search interaction features
 * including search analytics and popular articles tracking, search customization capabilities with personalized
 * results and content recommendations, knowledge base integration with real-time content updates and
 * comprehensive article management, and seamless ACE Social platform integration with advanced search management
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  TextField,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Chip,
  Card,
  CardContent,
  Collapse,
  InputAdornment,
  CircularProgress,
  Button,
  Grid,
  useTheme
} from '@mui/material';
import {
  Search as SearchIcon,
  Article as ArticleIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Star as StarIcon,
  TrendingUp as PopularIcon,
  Help as HelpIcon,
  Build as BuildIcon,
  Business as BusinessIcon,
  AccountCircle as AccountIcon,
  Integration as IntegrationIcon,
  School as TrainingIcon
} from '@mui/icons-material';
import { debounce } from 'lodash-es';
import useSupportWidget from '../../hooks/useSupportWidget';
import { useNotification } from '../../hooks/useNotification';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};



// Knowledge base categories with enhanced metadata
const KB_CATEGORIES = [
  {
    value: 'technical',
    label: 'Technical',
    description: 'Platform bugs, API issues, integration problems',
    icon: BuildIcon,
    color: ACE_COLORS.PURPLE,
    priority: 'high'
  },
  {
    value: 'billing',
    label: 'Billing & Account',
    description: 'Subscription, payments, invoices, plan changes',
    icon: BusinessIcon,
    color: ACE_COLORS.YELLOW,
    priority: 'medium'
  },
  {
    value: 'account',
    label: 'Account Management',
    description: 'Profile settings, access issues, user management',
    icon: AccountIcon,
    color: ACE_COLORS.PURPLE,
    priority: 'medium'
  },
  {
    value: 'integration',
    label: 'Integrations',
    description: 'Third-party connections, API setup, webhooks',
    icon: IntegrationIcon,
    color: ACE_COLORS.PURPLE,
    priority: 'high'
  },
  {
    value: 'training',
    label: 'Training & Tutorials',
    description: 'How-to guides, best practices, tutorials',
    icon: TrainingIcon,
    color: ACE_COLORS.YELLOW,
    priority: 'low'
  },
  {
    value: 'general',
    label: 'General Support',
    description: 'Questions, guidance, general help',
    icon: HelpIcon,
    color: ACE_COLORS.DARK,
    priority: 'medium'
  }
];

/**
 * Enhanced Knowledge Base Search - Comprehensive knowledge base search with advanced features
 * Implements plan-based search features and enterprise-grade knowledge management capabilities
 */
const KnowledgeBaseSearch = memo(forwardRef(({
  onCreateTicket,
  enableSearchHistory = true,
  enableBookmarks = true,
  enableAnalytics = true,
  customCategories = [],
  onSearchAnalytics,
  onArticleView,
  onArticleVote
}, ref) => {
  const theme = useTheme();
  const { loadKnowledgeBase } = useSupportWidget();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Enhanced state management
  const [loading, setLoading] = useState(false);
  const [searchHistory, setSearchHistory] = useState([]);
  const [bookmarkedArticles, setBookmarkedArticles] = useState([]);
  const [searchAnalytics, setSearchAnalytics] = useState({});

  // Original state variables
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [expandedArticle, setExpandedArticle] = useState(null);
  const [helpfulVotes, setHelpfulVotes] = useState({});

  // Merge custom categories with defaults (all categories available)
  const categories = useMemo(() =>
    customCategories.length > 0 ? customCategories : KB_CATEGORIES, [customCategories]);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    getSearchQuery: () => searchQuery,
    setSearchQuery: (query) => setSearchQuery(query),
    getSearchResults: () => searchResults,
    performSearch: (query) => handleSearch(query),
    clearSearch: () => handleClearSearch(),
    getSearchHistory: () => searchHistory,
    clearSearchHistory: () => setSearchHistory([]),
    getBookmarkedArticles: () => bookmarkedArticles,
    toggleBookmark: (articleId) => handleToggleBookmark(articleId),
    getSearchAnalytics: () => searchAnalytics,
    refreshResults: () => handleRefreshResults(),
    expandArticle: (articleId) => setExpandedArticle(articleId),
    collapseArticle: () => setExpandedArticle(null)
  }), [
    searchQuery,
    searchResults,
    searchHistory,
    bookmarkedArticles,
    searchAnalytics,
    handleSearch,
    handleClearSearch,
    handleToggleBookmark,
    handleRefreshResults
  ]);

  // Enhanced popular topics (all available to everyone)
  const popularTopics = useMemo(() => [
    { label: 'Instagram Connection', query: 'instagram connect', category: 'integration' },
    { label: 'Billing Issues', query: 'billing payment', category: 'billing' },
    { label: 'Account Settings', query: 'account profile', category: 'account' },
    { label: 'Content Scheduling', query: 'schedule posts', category: 'general' },
    { label: 'Analytics Dashboard', query: 'analytics metrics', category: 'general' },
    { label: 'Team Collaboration', query: 'team sharing', category: 'account' },
    { label: 'API Integration', query: 'api setup', category: 'integration' },
    { label: 'Advanced Analytics', query: 'advanced metrics', category: 'training' }
  ], []);

  // Enhanced search handlers
  const handleSearch = useCallback(async (query) => {
    try {
      setLoading(true);

      // Add to search history if enabled
      if (enableSearchHistory && query.trim()) {
        setSearchHistory(prev => {
          const newHistory = [query, ...prev.filter(h => h !== query)].slice(0, 10);
          return newHistory;
        });
      }

      // Track search analytics
      if (enableAnalytics) {
        setSearchAnalytics(prev => ({
          ...prev,
          searchCount: (prev.searchCount || 0) + 1,
          lastSearch: new Date().toISOString(),
          queries: [...(prev.queries || []), query].slice(-50)
        }));

        if (onSearchAnalytics) {
          onSearchAnalytics({ query, timestamp: new Date().toISOString() });
        }
      }

      const results = await loadKnowledgeBase(query);
      setSearchResults(results);

      announceToScreenReader(`Found ${results.length} articles for "${query}"`);
    } catch {
      showErrorNotification('Failed to search knowledge base');
      announceToScreenReader('Search failed');
    } finally {
      setLoading(false);
    }
  }, [
    enableSearchHistory,
    enableAnalytics,
    loadKnowledgeBase,
    onSearchAnalytics,
    showErrorNotification,
    announceToScreenReader
  ]);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce(async (query) => {
      await handleSearch(query);
    }, 300),
    [handleSearch]
  );

  // Enhanced event handlers
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchResults([]);
    setExpandedArticle(null);
    announceToScreenReader('Search cleared');
  }, [announceToScreenReader]);

  const handleRefreshResults = useCallback(async () => {
    if (searchQuery.trim()) {
      await handleSearch(searchQuery);
    } else {
      try {
        setLoading(true);
        const featured = await loadKnowledgeBase('');
        setSearchResults(featured);
        announceToScreenReader('Results refreshed');
      } catch {
        showErrorNotification('Failed to refresh results');
      } finally {
        setLoading(false);
      }
    }
  }, [searchQuery, handleSearch, loadKnowledgeBase, showErrorNotification, announceToScreenReader]);

  const handleToggleBookmark = useCallback((articleId) => {
    if (!enableBookmarks) {
      showErrorNotification('Bookmarks are disabled');
      return;
    }

    setBookmarkedArticles(prev => {
      const isBookmarked = prev.includes(articleId);
      const newBookmarks = isBookmarked
        ? prev.filter(id => id !== articleId)
        : [...prev, articleId];

      announceToScreenReader(isBookmarked ? 'Bookmark removed' : 'Article bookmarked');
      return newBookmarks;
    });
  }, [enableBookmarks, showErrorNotification, announceToScreenReader]);

  // Load initial featured articles
  useEffect(() => {
    loadKnowledgeBase('').then(setSearchResults);
  }, [loadKnowledgeBase]);

  // Handle search query changes
  useEffect(() => {
    if (searchQuery.trim()) {
      debouncedSearch(searchQuery);
    } else {
      // Load featured articles when no search query
      loadKnowledgeBase('').then(setSearchResults);
    }
  }, [searchQuery, debouncedSearch, loadKnowledgeBase]);

  const handleSearchChange = useCallback((event) => {
    setSearchQuery(event.target.value);
  }, []);

  const handleTopicClick = useCallback((query) => {
    setSearchQuery(query);
    announceToScreenReader(`Searching for ${query}`);
  }, [announceToScreenReader]);

  const handleExpandArticle = useCallback((articleId) => {
    const newExpanded = expandedArticle === articleId ? null : articleId;
    setExpandedArticle(newExpanded);

    if (newExpanded && onArticleView) {
      onArticleView(articleId);
    }

    announceToScreenReader(newExpanded ? 'Article expanded' : 'Article collapsed');
  }, [expandedArticle, onArticleView, announceToScreenReader]);

  // Enhanced vote handling
  const handleHelpfulVote = useCallback(async (articleId, isHelpful) => {
    try {
      setHelpfulVotes(prev => ({
        ...prev,
        [articleId]: isHelpful
      }));

      if (onArticleVote) {
        await onArticleVote(articleId, isHelpful);
      }

      showSuccessNotification('Thank you for your feedback!');
      announceToScreenReader(`Article marked as ${isHelpful ? 'helpful' : 'not helpful'}`);
    } catch {
      showErrorNotification('Failed to record your feedback');
      announceToScreenReader('Failed to record feedback');
    }
  }, [onArticleVote, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  // Enhanced utility functions
  const getCategoryColor = useCallback((category) => {
    const categoryData = categories.find(cat => cat.value === category);
    if (categoryData) {
      switch (category) {
        case 'technical':
          return 'primary';
        case 'billing':
          return 'warning';
        case 'account':
          return 'info';
        case 'integration':
          return 'secondary';
        case 'training':
          return 'success';
        case 'general':
        default:
          return 'default';
      }
    }
    return 'default';
  }, [categories]);

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          Knowledge Base
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Search our help articles or browse popular topics
        </Typography>
      </Box>

      {/* Search Bar */}
      <Box sx={{ p: 2 }}>
        <TextField
          fullWidth
          placeholder="Search for help articles..."
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
            endAdornment: loading && (
              <InputAdornment position="end">
                <CircularProgress size={20} />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />

        {/* Popular Topics */}
        {!searchQuery && (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Popular Topics
            </Typography>
            <Grid container spacing={1}>
              {popularTopics.map((topic, index) => (
                <Grid item key={index}>
                  <Chip
                    label={topic.label}
                    variant="outlined"
                    size="small"
                    onClick={() => handleTopicClick(topic.query)}
                    sx={{ cursor: 'pointer' }}
                  />
                </Grid>
              ))}
            </Grid>
          </Box>
        )}
      </Box>

      {/* Search Results */}
      <Box sx={{ px: 2, pb: 2 }}>
        {searchResults.length === 0 && !loading ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            height="200px"
            textAlign="center"
          >
            <HelpIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {searchQuery ? 'No Articles Found' : 'No Featured Articles'}
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              {searchQuery 
                ? `No articles found for "${searchQuery}". Try different keywords or create a support ticket.`
                : 'No featured articles available at the moment.'
              }
            </Typography>
            <Button
              variant="contained"
              onClick={onCreateTicket}
            >
              Create Support Ticket
            </Button>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {searchResults.map((article) => (
              <React.Fragment key={article.id}>
                <ListItem
                  component="button"
                  onClick={() => handleExpandArticle(article.id)}
                  sx={{
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: 1,
                    mb: 1,
                    '&:hover': {
                      backgroundColor: theme.palette.action.hover,
                    },
                  }}
                >
                  <ListItemIcon>
                    <Box position="relative">
                      <ArticleIcon color="primary" />
                      {article.is_featured && (
                        <StarIcon
                          sx={{
                            position: 'absolute',
                            top: -4,
                            right: -4,
                            fontSize: 16,
                            color: 'warning.main',
                          }}
                        />
                      )}
                    </Box>
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                        <Typography variant="body1" fontWeight="bold">
                          {article.title}
                        </Typography>
                        <Chip
                          label={article.category?.replace('_', ' ')}
                          color={getCategoryColor(article.category)}
                          size="small"
                          sx={{ textTransform: 'capitalize' }}
                        />
                        {article.difficulty_level && (
                          <Chip
                            label={article.difficulty_level}
                            variant="outlined"
                            size="small"
                            sx={{ textTransform: 'capitalize' }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.primary" paragraph>
                          {article.summary || article.content?.substring(0, 150) + '...'}
                        </Typography>
                        <Box display="flex" alignItems="center" gap={2}>
                          {article.view_count && (
                            <Box display="flex" alignItems="center" gap={0.5}>
                              <PopularIcon fontSize="small" color="action" />
                              <Typography variant="caption" color="text.secondary">
                                {article.view_count} views
                              </Typography>
                            </Box>
                          )}
                          {article.helpfulness_score && (
                            <Typography variant="caption" color="text.secondary">
                              {(article.helpfulness_score * 100).toFixed(0)}% helpful
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    }
                  />
                  
                  <IconButton>
                    {expandedArticle === article.id ? <CollapseIcon /> : <ExpandIcon />}
                  </IconButton>
                </ListItem>

                {/* Expanded Article Content */}
                <Collapse in={expandedArticle === article.id} timeout="auto" unmountOnExit>
                  <Card variant="outlined" sx={{ mb: 2, ml: 2 }}>
                    <CardContent>
                      <Typography variant="body2" component="div" sx={{ whiteSpace: 'pre-wrap' }}>
                        {article.content}
                      </Typography>

                      {/* Tags */}
                      {article.tags && article.tags.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="caption" color="text.secondary" gutterBottom>
                            Tags:
                          </Typography>
                          <Box display="flex" flexWrap="wrap" gap={0.5} mt={0.5}>
                            {article.tags.map((tag) => (
                              <Chip
                                key={tag}
                                label={tag}
                                size="small"
                                variant="outlined"
                                sx={{ fontSize: '0.7rem', height: 20 }}
                              />
                            ))}
                          </Box>
                        </Box>
                      )}

                      {/* Helpfulness Voting */}
                      <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
                        <Typography variant="body2" gutterBottom>
                          Was this article helpful?
                        </Typography>
                        <Box display="flex" gap={1}>
                          <Button
                            size="small"
                            startIcon={<ThumbUpIcon />}
                            variant={helpfulVotes[article.id] === true ? "contained" : "outlined"}
                            color="success"
                            onClick={() => handleHelpfulVote(article.id, true)}
                          >
                            Yes
                          </Button>
                          <Button
                            size="small"
                            startIcon={<ThumbDownIcon />}
                            variant={helpfulVotes[article.id] === false ? "contained" : "outlined"}
                            color="error"
                            onClick={() => handleHelpfulVote(article.id, false)}
                          >
                            No
                          </Button>
                        </Box>
                        {helpfulVotes[article.id] !== undefined && (
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                            Thank you for your feedback!
                          </Typography>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Collapse>
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Still Need Help Section */}
      <Box
        sx={{
          p: 2,
          borderTop: `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.background.default,
        }}
      >
        <Typography variant="subtitle2" gutterBottom>
          Still need help?
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Can&apos;t find what you&apos;re looking for? Our support team is here to help.
        </Typography>
        <Button
          variant="contained"
          fullWidth
          onClick={onCreateTicket}
        >
          Contact Support
        </Button>
      </Box>
    </Box>
  );
}));

KnowledgeBaseSearch.displayName = 'KnowledgeBaseSearch';

KnowledgeBaseSearch.propTypes = {
  /** Function called when create ticket is requested */
  onCreateTicket: PropTypes.func,
  /** Enable advanced search features */
  enableAdvancedSearch: PropTypes.bool,
  /** Enable search history functionality */
  enableSearchHistory: PropTypes.bool,
  /** Enable bookmarks functionality */
  enableBookmarks: PropTypes.bool,
  /** Enable search analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Maximum number of search results to display */
  maxSearchResults: PropTypes.number,
  /** Placeholder text for search input */
  searchPlaceholder: PropTypes.string,
  /** Show popular topics section */
  showPopularTopics: PropTypes.bool,
  /** Show categories filter */
  showCategories: PropTypes.bool,
  /** Custom knowledge base categories */
  customCategories: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    description: PropTypes.string,
    icon: PropTypes.elementType,
    color: PropTypes.string,
    priority: PropTypes.string
  })),
  /** Function called when search analytics are tracked */
  onSearchAnalytics: PropTypes.func,
  /** Function called when article is viewed */
  onArticleView: PropTypes.func,
  /** Function called when article is voted on */
  onArticleVote: PropTypes.func
};

export default KnowledgeBaseSearch;

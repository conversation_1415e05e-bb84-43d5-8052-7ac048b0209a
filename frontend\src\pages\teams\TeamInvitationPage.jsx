// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
  CircularProgress,
  Divider,
  Grid,
  useTheme,
  Alert,
  Card,
  CardContent,
  Avatar,
  Chip,
  Container,
  AlertTitle
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import GroupsIcon from '@mui/icons-material/Groups';
import { useTeam } from '../../contexts/TeamContext';
import { useAuth } from '../../contexts/AuthContext';
import PageHeader from '../../components/common/PageHeader';
import api from '../../api';

const TeamInvitationPage = () => {
  const theme = useTheme();
  const { token } = useParams();
  const navigate = useNavigate();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { acceptInvitation, rejectInvitation } = useTeam();

  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [invitation, setInvitation] = useState(null);

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Verify token and get invitation details with production API
  useEffect(() => {
    const verifyInvitation = async () => {
      if (!isAuthenticated || !token || authLoading) return;

      setLoading(true);
      setError(null);

      try {
        // Production API call to verify invitation
        const response = await api.get(`/api/teams/invitations/verify/${token}`);

        if (response.data) {
          setInvitation({
            team_name: response.data.team_name,
            invited_by_name: response.data.invited_by_name,
            role: response.data.role,
            team_id: response.data.team_id,
            expires_at: response.data.expires_at
          });
        } else {
          setError('Invalid invitation token');
        }
      } catch (err) {
        console.error('Error verifying invitation:', err);

        if (err.response?.status === 401) {
          setError('Session expired. Please log in again.');
          navigate('/login');
        } else if (err.response?.status === 404) {
          setError('Invitation not found or has expired.');
        } else if (err.response?.status === 410) {
          setError('This invitation has already been used or expired.');
        } else {
          setError(`Invalid or expired invitation: ${err.response?.data?.detail || err.message || 'Unknown error'}`);
        }
      } finally {
        setLoading(false);
      }
    };

    verifyInvitation();
  }, [token, isAuthenticated, authLoading, navigate]);

  // Handle accept invitation with enhanced error handling
  const handleAccept = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await acceptInvitation(token);

      if (result) {
        setSuccess(`Welcome to ${invitation?.team_name || 'the team'}! You have successfully joined as a ${invitation?.role || 'member'}.`);
        setTimeout(() => {
          navigate(`/settings/teams/${result.team?.id || invitation?.team_id}`);
        }, 2000);
      }
    } catch (err) {
      console.error('Error accepting invitation:', err);

      if (err.response?.status === 401) {
        setError('Session expired. Please log in again.');
        navigate('/login');
      } else if (err.response?.status === 404) {
        setError('Invitation not found or has expired.');
      } else if (err.response?.status === 409) {
        setError('You are already a member of this team.');
      } else {
        setError(err.response?.data?.detail || 'Failed to accept invitation. Please try again.');
      }
    } finally {
      setActionLoading(false);
    }
  };

  // Handle reject invitation with enhanced error handling
  const handleReject = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    setActionLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await rejectInvitation(token);

      if (result) {
        setSuccess(`Invitation to join ${invitation?.team_name || 'the team'} has been declined.`);
        setTimeout(() => {
          navigate('/settings?tab=teams');
        }, 2000);
      }
    } catch (err) {
      console.error('Error rejecting invitation:', err);

      if (err.response?.status === 401) {
        setError('Session expired. Please log in again.');
        navigate('/login');
      } else if (err.response?.status === 404) {
        setError('Invitation not found or has already been processed.');
      } else {
        setError(err.response?.data?.detail || 'Failed to reject invitation. Please try again.');
      }
    } finally {
      setActionLoading(false);
    }
  };

  // Handle go to teams
  const handleGoToTeams = () => {
    navigate('/settings?tab=teams');
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: 300, p: 3 }}>
        <CircularProgress size={40} />
        <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
          Loading invitation details...
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Verifying invitation for {user?.name || 'user'}
        </Typography>
      </Box>
    );
  }

  if (error && !invitation) {
    return (
      <Container maxWidth="sm" sx={{ py: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          <AlertTitle>Invitation Error</AlertTitle>
          {error}
          {user?.name && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Hi {user.name}, please check the invitation link or contact the team administrator.
            </Typography>
          )}
        </Alert>
        <Box sx={{ textAlign: 'center' }}>
          <Button
            variant="contained"
            onClick={handleGoToTeams}
            sx={{ mr: 2 }}
          >
            Go to Teams
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigate('/dashboard')}
          >
            Go to Dashboard
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <PageHeader
        title="Team Invitation"
        subtitle={
          <Box>
            <Typography variant="body1" color="text.secondary">
              Review and respond to your team invitation
            </Typography>
            {user?.name && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Welcome, {user.name} • Please review the invitation details below
              </Typography>
            )}
          </Box>
        }
      />

      <Grid container justifyContent="center" sx={{ mt: 3 }}>
        <Grid item xs={12} sm={10} md={8} lg={6}>
          <Paper sx={{ p: 3 }}>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  bgcolor: theme.palette.primary.main,
                  margin: '0 auto 16px'
                }}
              >
                <GroupsIcon sx={{ fontSize: 40 }} />
              </Avatar>

              <Typography variant="h5" gutterBottom>
                {user?.name ? `${user.name}, you've been invited to join a team!` : "You've been invited to join a team!"}
              </Typography>
            </Box>

            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Team Name
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {invitation.team_name}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Invited By
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {invitation.invited_by_name}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Your Role
                    </Typography>
                    <Chip
                      label={invitation.role === 'admin' ? 'Admin' : invitation.role === 'member' ? 'Member' : 'Viewer'}
                      color={invitation.role === 'admin' ? 'secondary' : 'primary'}
                      size="small"
                    />
                  </Grid>

                  {invitation.expires_at && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Expires
                      </Typography>
                      <Typography variant="body1" color="warning.main">
                        {new Date(invitation.expires_at).toLocaleDateString()}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>

            <Typography variant="body1" paragraph>
              By accepting this invitation, you&apos;ll be able to collaborate with team members on content creation, campaign management, and analytics.
            </Typography>

            <Divider sx={{ my: 3 }} />

            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button
                variant="outlined"
                color="error"
                startIcon={<CancelIcon />}
                onClick={handleReject}
                disabled={actionLoading}
              >
                Decline
              </Button>

              <Button
                variant="contained"
                color="primary"
                startIcon={actionLoading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
                onClick={handleAccept}
                disabled={actionLoading}
              >
                Accept Invitation
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TeamInvitationPage;

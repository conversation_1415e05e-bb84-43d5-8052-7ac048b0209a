/**
 * Enhanced Message Input - Enterprise-grade message input management component
 * Features: Plan-based input limitations, real-time input tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced message input capabilities and interactive input exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from "react";
import PropTypes from 'prop-types';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Collapse,
  CircularProgress,
  Chip,
  Snackbar,
  Alert,
  alpha
} from "@mui/material";
import {
  Send as SendIcon,
  AttachFile as AttachFileIcon,
  InsertEmoticon as EmojiIcon,
  Image as ImageIcon,
  MoreVert as MoreVertIcon,
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  FormatListBulleted as ListIcon,
  FormatListNumbered as NumberedListIcon,
  Link as LinkIcon,
  AutoAwesome as AutoAwesomeIcon,
  Verified as VerifiedIcon
} from "@mui/icons-material";
import { useAuth } from "../../contexts/AuthContext";
import useApiError from "../../hooks/useApiError";
import api from "../../api";
import { useSubscription } from "../../hooks/useSubscription";
import { useAdvancedToast } from "../../hooks/useAdvancedToast";

import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Message input display modes with enhanced configurations
const INPUT_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Input',
    description: 'Basic input interface',
    subscriptionLimits: {
      creator: { available: true, maxInputTypes: 3, features: ['basic_input'] },
      accelerator: { available: true, maxInputTypes: 10, features: ['basic_input', 'analytics_input'] },
      dominator: { available: true, maxInputTypes: -1, features: ['basic_input', 'analytics_input', 'ai_insights'] }
    }
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Input',
    description: 'Comprehensive input management',
    subscriptionLimits: {
      creator: { available: false, maxInputTypes: 0, features: [] },
      accelerator: { available: true, maxInputTypes: 5, features: ['detailed_input', 'input_analytics'] },
      dominator: { available: true, maxInputTypes: -1, features: ['detailed_input', 'input_analytics', 'real_time_insights'] }
    }
  },
  RICH_TEXT: {
    id: 'rich_text',
    name: 'Rich Text Input',
    description: 'Advanced formatting and rich text editing',
    subscriptionLimits: {
      creator: { available: false, maxInputTypes: 0, features: [] },
      accelerator: { available: true, maxInputTypes: 3, features: ['rich_text', 'formatting'] },
      dominator: { available: true, maxInputTypes: -1, features: ['rich_text', 'formatting', 'advanced_formatting'] }
    }
  },
  VOICE: {
    id: 'voice',
    name: 'Voice Input',
    description: 'Voice recording and transcription',
    subscriptionLimits: {
      creator: { available: false, maxInputTypes: 0, features: [] },
      accelerator: { available: false, maxInputTypes: 0, features: [] },
      dominator: { available: true, maxInputTypes: -1, features: ['voice_input', 'transcription', 'voice_analytics'] }
    }
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Input',
    description: 'Advanced input analytics and insights',
    subscriptionLimits: {
      creator: { available: false, maxInputTypes: 0, features: [] },
      accelerator: { available: false, maxInputTypes: 0, features: [] },
      dominator: { available: true, maxInputTypes: -1, features: ['analytics_input', 'ai_insights'] }
    }
  }
};

/**
 * Enhanced Message Input Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {string} [props.value=''] - Current input value
 * @param {Function} [props.onChange] - Value change callback
 * @param {Function} [props.onSend] - Send message callback
 * @param {string} [props.placeholder='Type a message...'] - Input placeholder
 * @param {boolean} [props.disabled=false] - Disabled state
 * @param {boolean} [props.loading=false] - Loading state
 * @param {Array} [props.suggestions=[]] - AI suggestions
 * @param {Function} [props.onUseSuggestion] - Use suggestion callback
 * @param {boolean} [props.isSocialMedia=false] - Social media mode
 * @param {string} [props.platform=null] - Social media platform
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=false] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onUpgrade] - Upgrade callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-message-input'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const MessageInput = memo(forwardRef(({
  value = '',
  onChange,
  onSend,
  placeholder = "Type a message...",
  disabled = false,
  loading = false,
  suggestions = [],
  onUseSuggestion,
  isSocialMedia = false,
  platform = null,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = false,
  onExport,
  onRefresh,
  onUpgrade,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-message-input',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useAccessibility();
  const { user } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();

  // Core state management
  const fileInputRef = useRef(null);
  const inputRef = useRef(null);
  const [emojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const [emojiAnchorEl, setEmojiAnchorEl] = useState(null);
  const [formatMenuAnchorEl, setFormatMenuAnchorEl] = useState(null);
  const [attachments, setAttachments] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [showFormatting, setShowFormatting] = useState(false);
  const [policyComplianceEnabled, setPolicyComplianceEnabled] = useState(true);

  // Enhanced state management
  const [inputMode, setInputMode] = useState('compact');
  const [inputHistory, setInputHistory] = useState([]);
  const [inputAnalytics, setInputAnalytics] = useState(null);
  const [inputInsights, setInputInsights] = useState(null);
  const [customInputs, setCustomInputs] = useState([]);
  const [inputPreferences, setInputPreferences] = useState({
    autoSave: true,
    spellCheck: true,
    autoComplete: true,
    voiceEnabled: false,
    richTextEnabled: false,
    analyticsEnabled: true
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [inputDrawerOpen, setInputDrawerOpen] = useState(false);
  const [selectedInputType, setSelectedInputType] = useState(null);
  const [inputStats, setInputStats] = useState(null);
  const [voiceRecording, setVoiceRecording] = useState(null);
  const [transcriptionText, setTranscriptionText] = useState('');
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [characterCount, setCharacterCount] = useState(0);
  const [typingSpeed, setTypingSpeed] = useState(0);
  const [lastTypingTime, setLastTypingTime] = useState(Date.now());

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastInputCheck, setLastInputCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Apply feature overrides based on props
    const advancedFeaturesEnabled = enableAdvancedFeatures && subscription?.plan_id !== 'creator';
    const aiInsightsEnabled = enableAIInsights && subscription?.plan_id === 'dominator';

    const features = {
      creator: {
        maxInputTypes: 3,
        maxInputLength: 500,
        hasAdvancedInput: false,
        hasInputAnalytics: false,
        hasCustomInputs: false,
        hasInputInsights: false,
        hasInputHistory: false,
        hasVoiceInput: false,
        hasRichText: false,
        hasInputExport: false,
        hasInputScheduling: false,
        hasInputAutomation: false,
        hasInputTemplates: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxInputTypes: 10,
        maxInputLength: 2000,
        hasAdvancedInput: true,
        hasInputAnalytics: true,
        hasCustomInputs: true,
        hasInputInsights: false,
        hasInputHistory: true,
        hasVoiceInput: false,
        hasRichText: true,
        hasInputExport: true,
        hasInputScheduling: true,
        hasInputAutomation: false,
        hasInputTemplates: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxInputTypes: -1,
        maxInputLength: -1,
        hasAdvancedInput: true,
        hasInputAnalytics: true,
        hasCustomInputs: true,
        hasInputInsights: true,
        hasInputHistory: true,
        hasVoiceInput: true,
        hasRichText: true,
        hasInputExport: true,
        hasInputScheduling: true,
        hasInputAutomation: true,
        hasInputTemplates: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'ai-powered',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    // Apply feature overrides
    const enhancedFeatures = {
      ...currentFeatures,
      hasAdvancedInput: currentFeatures.hasAdvancedInput && advancedFeaturesEnabled,
      hasInputInsights: currentFeatures.hasInputInsights && aiInsightsEnabled,
      hasInputAnalytics: currentFeatures.hasInputAnalytics && advancedFeaturesEnabled
    };

    return {
      ...enhancedFeatures,
      hasFeatureAccess: (feature) => enhancedFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = enhancedFeatures[feature] === true;
        const withinLimits = enhancedFeatures.maxInputTypes === -1 || currentUsage < enhancedFeatures.maxInputTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription, enableAdvancedFeatures, enableAIInsights]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'textbox',
      'aria-label': ariaLabel || `Message input with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Input interface with ${subscriptionFeatures.trackingLevel} tracking`,
      'aria-live': enableRealTimeOptimization ? 'polite' : 'off',
      'aria-atomic': 'true',
      'aria-multiline': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel, enableRealTimeOptimization]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive input API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getInputAnalytics: () => inputAnalytics,
    getInputInsights: () => inputInsights,
    refreshInput: () => {
      fetchInputAnalytics();
      if (onRefresh) onRefresh();
    },

    // Input methods
    focusInput: () => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    },
    clearInput: () => {
      if (onChange) onChange('');
      setAttachments([]);
    },
    insertText: (text) => {
      if (onChange) {
        onChange(value + text);
      }
    },
    openInputDrawer: () => setInputDrawerOpen(true),
    closeInputDrawer: () => setInputDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    getInputStats: () => inputStats,
    exportInputData: () => {
      if (subscriptionFeatures.hasExport && onExport) {
        onExport(inputHistory, inputAnalytics);
      }
    },

    // Accessibility methods
    announceInput: (message) => announceToScreenReader(message),
    focusInputField: () => setFocusToElement('message-input-field'),

    // Advanced methods
    startVoiceRecording: () => {
      if (subscriptionFeatures.hasVoiceInput) {
        handleVoiceRecording();
      }
    },
    getWordCount: () => wordCount,
    getCharacterCount: () => characterCount,
    getTypingSpeed: () => typingSpeed,

    // Enhanced methods
    changeInputMode: (mode) => handleInputModeChange(mode),
    addCustomInput: (config) => addCustomInput(config),
    updatePreferences: (prefs) => updateInputPreferences(prefs),
    trackAction: (type, data) => trackInputAction(type, data),
    getCurrentMode: () => inputMode,
    getInputHistory: () => inputHistory,
    getCustomInputs: () => customInputs,
    getPreferences: () => inputPreferences,

    // UI control methods
    selectInputType: (type) => handleInputTypeSelection(type),
    toggleFullscreenMode: () => handleFullscreenToggle(),
    toggleAnalyticsView: () => handleAnalyticsToggle(),
    toggleDrawer: () => handleInputDrawerToggle(),
    getSelectedType: () => selectedInputType,
    isFullscreen: () => fullscreenMode,
    isAnalyticsVisible: () => showAnalytics,
    isDrawerOpen: () => inputDrawerOpen,

    // Voice and transcription methods
    processTranscription: (text) => processTranscriptionText(text),
    manageRecording: (recording) => manageVoiceRecording(recording),
    getTranscriptionText: () => transcriptionText,
    getVoiceRecording: () => voiceRecording,
    fetchStats: () => fetchInputStats()
  }), [
    inputHistory,
    inputAnalytics,
    inputInsights,
    inputStats,
    subscriptionFeatures,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    value,
    onChange,
    wordCount,
    characterCount,
    typingSpeed,
    fetchInputAnalytics,
    handleVoiceRecording,
    addCustomInput,
    customInputs,
    handleInputModeChange,
    inputMode,
    inputPreferences,
    trackInputAction,
    updateInputPreferences,
    fullscreenMode,
    handleAnalyticsToggle,
    handleFullscreenToggle,
    handleInputDrawerToggle,
    handleInputTypeSelection,
    inputDrawerOpen,
    selectedInputType,
    showAnalytics,
    fetchInputStats,
    manageVoiceRecording,
    processTranscriptionText,
    transcriptionText,
    voiceRecording
  ]);

  // Fetch input analytics with enhanced error handling and retry logic
  const fetchInputAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/input/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setInputAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (inputPreferences.analyticsEnabled) {
                setNotification({
                  open: true,
                  message: "Input analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch input analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load input analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, inputPreferences.analyticsEnabled]);

  // Fetch input insights
  const fetchInputInsights = useCallback(async () => {
    if (!subscriptionFeatures.hasInputInsights) return;

    await handleApiRequest(
      async () => {
        const response = await api.get('/api/input/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setInputInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch input insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest, subscriptionFeatures.hasInputInsights]);

  // Handle voice recording
  const handleVoiceRecording = useCallback(async () => {
    if (!subscriptionFeatures.hasVoiceInput) {
      showError('Voice input not available in your plan');
      return;
    }

    try {
      if (!isRecording) {
        // Start recording
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const mediaRecorder = new MediaRecorder(stream);
        const audioChunks = [];

        mediaRecorder.ondataavailable = (event) => {
          audioChunks.push(event.data);
        };

        mediaRecorder.onstop = async () => {
          const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
          manageVoiceRecording(audioBlob);

          // Transcribe audio if available
          if (subscriptionFeatures.hasInputInsights) {
            try {
              const formData = new FormData();
              formData.append('audio', audioBlob);

              const response = await api.post('/api/input/transcribe', formData);
              if (response.data.text) {
                processTranscriptionText(response.data.text);
                if (onChange) {
                  onChange(value + ' ' + response.data.text);
                }
                showSuccess('Voice transcribed successfully');
              }
            } catch (error) {
              console.error('Transcription failed:', error);
              showError('Voice transcription failed');
            }
          }
        };

        mediaRecorder.start();
        setIsRecording(true);
        announceToScreenReader('Voice recording started');
        showSuccess('Recording started');
      } else {
        // Stop recording
        setIsRecording(false);
        announceToScreenReader('Voice recording stopped');
        showSuccess('Recording stopped');
      }
    } catch (error) {
      console.error('Voice recording error:', error);
      showError('Voice recording failed');
      setIsRecording(false);
    }
  }, [subscriptionFeatures.hasVoiceInput, subscriptionFeatures.hasInputInsights, isRecording, value, onChange, announceToScreenReader, showSuccess, showError, manageVoiceRecording, processTranscriptionText]);

  // Fetch user's policy compliance setting
  useEffect(() => {
    const fetchPolicyComplianceSetting = async () => {
      try {
        const response = await fetch('/api/auto-response/settings');
        if (response.ok) {
          const data = await response.json();
          if (data && data.use_policy_compliance !== undefined) {
            setPolicyComplianceEnabled(data.use_policy_compliance);
          }
        }
      } catch (error) {
        console.error('Error fetching policy compliance setting:', error);
      }
    };

    fetchPolicyComplianceSetting();
  }, []);

  // Track input metrics
  useEffect(() => {
    const words = value.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharacterCount(value.length);

    // Calculate typing speed
    const currentTime = Date.now();
    const timeDiff = currentTime - lastTypingTime;
    if (timeDiff > 0 && value.length > 0) {
      const speed = (value.length / timeDiff) * 60000; // characters per minute
      setTypingSpeed(Math.round(speed));
    }
    setLastTypingTime(currentTime);
  }, [value, lastTypingTime]);

  // Production-ready health check system
  const checkBackendHealth = useCallback(async () => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('/api/health', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const wasUnavailable = !backendAvailable;
        setBackendAvailable(true);
        setLastInputCheck(Date.now());

        if (wasUnavailable && inputPreferences.analyticsEnabled) {
          showSuccess("Connection restored - Input features available");
        }
      } else {
        setBackendAvailable(false);
        if (inputPreferences.analyticsEnabled) {
          showError("Backend service unavailable - Some input features may be limited");
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Health check failed:', error);
        setBackendAvailable(false);

        // Show notification only if it's been a while since last check
        const timeSinceLastCheck = Date.now() - lastInputCheck;
        if (timeSinceLastCheck > 60000 && inputPreferences.analyticsEnabled) { // 1 minute
          showError("Connection issues detected - Input may be delayed");
        }
      }
    }
  }, [backendAvailable, lastInputCheck, inputPreferences.analyticsEnabled, showSuccess, showError]);

  // Periodic health checks in production
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [checkBackendHealth]);

  // Initial data loading
  useEffect(() => {
    if (subscriptionFeatures.hasInputAnalytics) {
      fetchInputAnalytics();
      fetchInputStats();
    }
    if (subscriptionFeatures.hasInputInsights) {
      fetchInputInsights();
    }
  }, [subscriptionFeatures.hasInputAnalytics, subscriptionFeatures.hasInputInsights, fetchInputAnalytics, fetchInputInsights, fetchInputStats]);

  // Handle emoji selection
  const handleEmojiSelect = (emoji) => {
    onChange(value + emoji.native);
    setEmojiPickerOpen(false);
  };

  // Handle emoji picker toggle
  const handleEmojiClick = (event) => {
    setEmojiAnchorEl(event.currentTarget);
    setEmojiPickerOpen(!emojiPickerOpen);
  };

  // Handle format menu toggle
  const handleFormatClick = (event) => {
    setFormatMenuAnchorEl(event.currentTarget);
  };

  const handleFormatClose = () => {
    setFormatMenuAnchorEl(null);
  };

  // Handle file selection
  const handleFileSelect = (event) => {
    const files = Array.from(event.target.files);
    setAttachments([...attachments, ...files]);
    // Reset file input
    event.target.value = null;
  };

  // Handle file button click
  const handleFileButtonClick = () => {
    fileInputRef.current.click();
  };

  // Handle recording toggle (use the enhanced voice recording)
  const handleRecordingToggle = useCallback(() => {
    handleVoiceRecording();
  }, [handleVoiceRecording]);

  // Handle input mode switching
  const handleInputModeChange = useCallback((newMode) => {
    if (INPUT_MODES[newMode.toUpperCase()]) {
      setInputMode(newMode);
      announceToScreenReader(`Input mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setInputHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (inputPreferences.analyticsEnabled) {
        showSuccess(`Switched to ${newMode} input mode`);
      }
    }
  }, [announceToScreenReader, user?.id, inputPreferences.analyticsEnabled, showSuccess]);

  // Handle input history tracking
  const trackInputAction = useCallback((actionType, data = {}) => {
    const actionRecord = {
      id: Date.now(),
      type: actionType,
      data,
      timestamp: new Date().toISOString(),
      userId: user?.id,
      inputMode,
      wordCount,
      characterCount
    };

    setInputHistory(prev => [actionRecord, ...prev.slice(0, 99)]);
  }, [user?.id, inputMode, wordCount, characterCount]);

  // Handle custom input management
  const addCustomInput = useCallback((inputConfig) => {
    if (subscriptionFeatures.hasCustomInputs) {
      const customInput = {
        id: Date.now(),
        ...inputConfig,
        createdAt: new Date().toISOString(),
        userId: user?.id
      };

      setCustomInputs(prev => [...prev, customInput]);
      trackInputAction('custom_input_added', customInput);
      showSuccess('Custom input added successfully');
    } else {
      showError('Custom inputs not available in your plan');
    }
  }, [subscriptionFeatures.hasCustomInputs, user?.id, trackInputAction, showSuccess, showError]);

  // Handle input preferences updates
  const updateInputPreferences = useCallback((newPreferences) => {
    setInputPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    trackInputAction('preferences_updated', newPreferences);

    if (inputPreferences.analyticsEnabled) {
      showSuccess('Input preferences updated');
    }
  }, [trackInputAction, inputPreferences.analyticsEnabled, showSuccess]);

  // Handle input type selection
  const handleInputTypeSelection = useCallback((inputType) => {
    setSelectedInputType(inputType);
    trackInputAction('input_type_selected', { inputType });

    if (inputPreferences.analyticsEnabled) {
      showSuccess(`Selected input type: ${inputType}`);
    }
  }, [trackInputAction, inputPreferences.analyticsEnabled, showSuccess]);

  // Handle fullscreen mode toggle
  const handleFullscreenToggle = useCallback(() => {
    setFullscreenMode(prev => {
      const newMode = !prev;
      trackInputAction('fullscreen_toggle', { fullscreen: newMode });
      announceToScreenReader(`Fullscreen mode ${newMode ? 'enabled' : 'disabled'}`);
      return newMode;
    });
  }, [trackInputAction, announceToScreenReader]);

  // Handle analytics toggle
  const handleAnalyticsToggle = useCallback(() => {
    setShowAnalytics(prev => {
      const newState = !prev;
      trackInputAction('analytics_toggle', { showAnalytics: newState });
      return newState;
    });
  }, [trackInputAction]);

  // Handle input drawer toggle
  const handleInputDrawerToggle = useCallback(() => {
    setInputDrawerOpen(prev => {
      const newState = !prev;
      trackInputAction('drawer_toggle', { drawerOpen: newState });
      return newState;
    });
  }, [trackInputAction]);

  // Handle upgrade prompts
  const handleUpgradePrompt = useCallback((feature) => {
    if (onUpgrade) {
      onUpgrade(feature);
    } else {
      showError(`${feature} requires a plan upgrade`);
    }

    trackInputAction('upgrade_prompt_shown', { feature });
  }, [onUpgrade, showError, trackInputAction]);

  // Fetch input stats
  const fetchInputStats = useCallback(async () => {
    if (!subscriptionFeatures.hasInputAnalytics) return;

    await handleApiRequest(
      async () => {
        const response = await api.get('/api/input/stats');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setInputStats(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch input stats:", err);
          }
        },
      }
    );
  }, [handleApiRequest, subscriptionFeatures.hasInputAnalytics]);

  // Handle transcription text processing
  const processTranscriptionText = useCallback((text) => {
    if (text && text.trim()) {
      setTranscriptionText(text);
      trackInputAction('transcription_processed', {
        text: text.substring(0, 100), // Log first 100 chars for privacy
        length: text.length
      });

      if (inputPreferences.analyticsEnabled) {
        showSuccess(`Transcribed ${text.split(' ').length} words`);
      }
    }
  }, [trackInputAction, inputPreferences.analyticsEnabled, showSuccess]);

  // Handle voice recording management
  const manageVoiceRecording = useCallback((recording) => {
    if (recording) {
      setVoiceRecording(recording);
      trackInputAction('voice_recording_saved', {
        size: recording.size,
        type: recording.type
      });

      if (inputPreferences.analyticsEnabled) {
        showSuccess('Voice recording saved');
      }
    }
  }, [trackInputAction, inputPreferences.analyticsEnabled, showSuccess]);

  // Handle formatting options
  const handleFormat = (format) => {
    handleFormatClose();

    let newValue = value;
    const selectionStart = document.activeElement.selectionStart;
    const selectionEnd = document.activeElement.selectionEnd;
    const selectedText = value.substring(selectionStart, selectionEnd);

    switch (format) {
      case 'bold':
        newValue = value.substring(0, selectionStart) + `**${selectedText}**` + value.substring(selectionEnd);
        break;
      case 'italic':
        newValue = value.substring(0, selectionStart) + `_${selectedText}_` + value.substring(selectionEnd);
        break;
      case 'bullet-list':
        newValue = value.substring(0, selectionStart) + `\n- ${selectedText}` + value.substring(selectionEnd);
        break;
      case 'numbered-list':
        newValue = value.substring(0, selectionStart) + `\n1. ${selectedText}` + value.substring(selectionEnd);
        break;
      case 'link':
        newValue = value.substring(0, selectionStart) + `[${selectedText}](url)` + value.substring(selectionEnd);
        break;
      default:
        break;
    }

    onChange(newValue);
  };

  // Handle key press
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  // Handle send
  const handleSend = () => {
    if (value.trim() || attachments.length > 0) {
      onSend(value, attachments);
      onChange('');
      setAttachments([]);
    }
  };

  // Get platform-specific styles
  const getPlatformColor = useCallback(() => {
    if (!isSocialMedia || !platform) return ACE_COLORS.PURPLE;

    switch (platform) {
      case 'facebook':
        return '#1877F2';
      case 'twitter':
        return '#1DA1F2';
      case 'linkedin':
        return '#0A66C2';
      case 'pinterest':
        return '#E60023';
      case 'threads':
        return '#000000';
      case 'tiktok':
        return '#000000';
      default:
        return ACE_COLORS.PURPLE;
    }
  }, [isSocialMedia, platform]);

  return (
    <Box
      {...getAccessibilityProps()}
      sx={{
        position: 'relative',
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      {/* AI Suggestions */}
      <Collapse in={suggestions.length > 0}>
        <Box sx={{ p: 1, mb: 1, borderRadius: 1, bgcolor: alpha(ACE_COLORS.WHITE, 0.7), backdropFilter: 'blur(10px)' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <AutoAwesomeIcon sx={{ mr: 1, color: ACE_COLORS.YELLOW }} />
            <Typography variant="subtitle2">
              {isSocialMedia
                ? `AI Suggested Responses for ${platform?.charAt(0).toUpperCase() + platform?.slice(1)}`
                : 'AI Suggested Responses'}
            </Typography>
            {policyComplianceEnabled && (
              <Tooltip title="These responses comply with your Terms of Service, Privacy Policy, and Brand Guidelines">
                <Chip
                  size="small"
                  color="success"
                  icon={<VerifiedIcon />}
                  label="Policy Compliant"
                  sx={{ ml: 1, height: 20, '& .MuiChip-label': { px: 1, fontSize: '0.625rem' } }}
                />
              </Tooltip>
            )}
          </Box>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {suggestions.map((suggestion, index) => (
              <Tooltip
                key={index}
                title={suggestion.length > 40 ? suggestion : ''}
                placement="top"
              >
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => onUseSuggestion(suggestion)}
                  sx={{
                    borderRadius: 4,
                    textTransform: 'none',
                    color: isSocialMedia ? getPlatformColor() : ACE_COLORS.PURPLE,
                    borderColor: isSocialMedia ? alpha(getPlatformColor(), 0.5) : alpha(ACE_COLORS.PURPLE, 0.5),
                    bgcolor: isSocialMedia ? alpha(getPlatformColor(), 0.05) : alpha(ACE_COLORS.PURPLE, 0.05),
                    '&:hover': {
                      bgcolor: isSocialMedia ? alpha(getPlatformColor(), 0.1) : alpha(ACE_COLORS.PURPLE, 0.1),
                      borderColor: isSocialMedia ? alpha(getPlatformColor(), 0.7) : alpha(ACE_COLORS.PURPLE, 0.7),
                    }
                  }}
                >
                  {suggestion.length > 40 ? suggestion.substring(0, 40) + '...' : suggestion}
                </Button>
              </Tooltip>
            ))}
          </Box>
        </Box>
      </Collapse>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          p: 1,
          mb: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying connection... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}

      {/* Formatting Toolbar */}
      <Collapse in={showFormatting}>
        <Paper
          elevation={0}
          sx={{
            p: 1,
            mb: 1,
            display: 'flex',
            borderRadius: 1,
            bgcolor: alpha(ACE_COLORS.WHITE, 0.7),
            backdropFilter: 'blur(10px)'
          }}
        >
          <Tooltip title="Bold">
            <IconButton size="small" onClick={() => handleFormat('bold')}>
              <BoldIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Italic">
            <IconButton size="small" onClick={() => handleFormat('italic')}>
              <ItalicIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Bullet List">
            <IconButton size="small" onClick={() => handleFormat('bullet-list')}>
              <ListIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Numbered List">
            <IconButton size="small" onClick={() => handleFormat('numbered-list')}>
              <NumberedListIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Link">
            <IconButton size="small" onClick={() => handleFormat('link')}>
              <LinkIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Paper>
      </Collapse>

      {/* Input Area */}
      <Box sx={{ display: 'flex', alignItems: 'flex-end' }}>
        <Box sx={{ display: 'flex', mr: 1 }}>
          <Tooltip title="Formatting">
            <IconButton onClick={() => setShowFormatting(!showFormatting)}>
              <MoreVertIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Attach File">
            <IconButton onClick={handleFileButtonClick}>
              <AttachFileIcon />
            </IconButton>
          </Tooltip>
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: 'none' }}
            onChange={handleFileSelect}
            multiple
          />
          <Tooltip title="Emoji">
            <IconButton onClick={handleEmojiClick}>
              <EmojiIcon />
            </IconButton>
          </Tooltip>

          {/* Voice Recording Button */}
          {subscriptionFeatures.hasVoiceInput ? (
            <Tooltip title={isRecording ? "Stop Recording" : "Start Voice Recording"}>
              <IconButton
                onClick={handleRecordingToggle}
                sx={{
                  color: isRecording ? ACE_COLORS.YELLOW : ACE_COLORS.PURPLE,
                  animation: isRecording ? 'pulse 1s infinite' : 'none'
                }}
              >
                <EmojiIcon />
              </IconButton>
            </Tooltip>
          ) : (
            <Tooltip title="Voice input requires plan upgrade">
              <IconButton
                onClick={() => handleUpgradePrompt('Voice Input')}
                sx={{ color: alpha(ACE_COLORS.PURPLE, 0.5) }}
              >
                <EmojiIcon />
              </IconButton>
            </Tooltip>
          )}

          {/* Format Menu Button */}
          {subscriptionFeatures.hasRichText && (
            <Tooltip title="Text Formatting">
              <IconButton onClick={handleFormatClick}>
                <MoreVertIcon />
              </IconButton>
            </Tooltip>
          )}

          {/* Input Mode Selector */}
          {subscriptionFeatures.hasAdvancedInput && (
            <Tooltip title={`Current mode: ${inputMode}`}>
              <IconButton
                onClick={() => {
                  const modes = ['compact', 'detailed', 'rich_text'];
                  const currentIndex = modes.indexOf(inputMode);
                  const nextMode = modes[(currentIndex + 1) % modes.length];
                  handleInputModeChange(nextMode);
                }}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <MoreVertIcon />
              </IconButton>
            </Tooltip>
          )}

          {/* Analytics Toggle */}
          {subscriptionFeatures.hasInputAnalytics && (
            <Tooltip title={showAnalytics ? "Hide Analytics" : "Show Analytics"}>
              <IconButton
                onClick={handleAnalyticsToggle}
                sx={{
                  color: showAnalytics ? ACE_COLORS.YELLOW : ACE_COLORS.PURPLE
                }}
              >
                <MoreVertIcon />
              </IconButton>
            </Tooltip>
          )}

          {/* Fullscreen Toggle */}
          <Tooltip title={fullscreenMode ? "Exit Fullscreen" : "Enter Fullscreen"}>
            <IconButton
              onClick={handleFullscreenToggle}
              sx={{
                color: fullscreenMode ? ACE_COLORS.YELLOW : ACE_COLORS.PURPLE
              }}
            >
              <MoreVertIcon />
            </IconButton>
          </Tooltip>

          {/* Input Drawer Toggle */}
          {subscriptionFeatures.hasAdvancedInput && (
            <Tooltip title={inputDrawerOpen ? "Close Settings" : "Open Settings"}>
              <IconButton
                onClick={handleInputDrawerToggle}
                sx={{
                  color: inputDrawerOpen ? ACE_COLORS.YELLOW : ACE_COLORS.PURPLE
                }}
              >
                <MoreVertIcon />
              </IconButton>
            </Tooltip>
          )}
          <Menu
            anchorEl={emojiAnchorEl}
            open={emojiPickerOpen}
            onClose={() => setEmojiPickerOpen(false)}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'center',
            }}
            transformOrigin={{
              vertical: 'bottom',
              horizontal: 'center',
            }}
            slotProps={{
              paper: {
                sx: {
                  borderRadius: 2,
                  background: alpha(ACE_COLORS.WHITE, 0.95),
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                  boxShadow: `0 8px 32px ${alpha(ACE_COLORS.DARK, 0.1)}`
                }
              }
            }}
          >
            <Picker data={data} onEmojiSelect={handleEmojiSelect} theme="light" />
          </Menu>

          {/* Format Menu */}
          <Menu
            anchorEl={formatMenuAnchorEl}
            open={Boolean(formatMenuAnchorEl)}
            onClose={handleFormatClose}
            slotProps={{
              paper: {
                sx: {
                  borderRadius: 2,
                  background: alpha(ACE_COLORS.WHITE, 0.95),
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                  boxShadow: `0 8px 32px ${alpha(ACE_COLORS.DARK, 0.1)}`
                }
              }
            }}
          >
            <MenuItem onClick={() => handleFormat('bold')}>
              <BoldIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
              Bold
            </MenuItem>
            <MenuItem onClick={() => handleFormat('italic')}>
              <ItalicIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
              Italic
            </MenuItem>
            <MenuItem onClick={() => handleFormat('bullet-list')}>
              <ListIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
              Bullet List
            </MenuItem>
            <MenuItem onClick={() => handleFormat('numbered-list')}>
              <NumberedListIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
              Numbered List
            </MenuItem>
            <MenuItem onClick={() => handleFormat('link')}>
              <LinkIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
              Link
            </MenuItem>
          </Menu>
        </Box>

        <TextField
          fullWidth
          multiline
          maxRows={4}
          placeholder={placeholder}
          variant="outlined"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={disabled || loading}
          sx={{
            mr: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: 3,
              bgcolor: alpha(ACE_COLORS.WHITE, 0.7),
              backdropFilter: 'blur(10px)',
              transition: 'all 0.2s',
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
              '&:hover, &.Mui-focused': {
                bgcolor: alpha(ACE_COLORS.WHITE, 0.9),
                borderColor: ACE_COLORS.PURPLE,
              }
            }
          }}
        />

        <Button
          variant="contained"
          color={isSocialMedia ? 'secondary' : 'primary'}
          endIcon={loading ? <CircularProgress size={16} color="inherit" /> : <SendIcon />}
          onClick={handleSend}
          disabled={disabled || loading || (!value.trim() && attachments.length === 0)}
          sx={{
            borderRadius: 3,
            height: 40,
            minWidth: 90,
            bgcolor: isSocialMedia ? getPlatformColor() : undefined,
            '&:hover': {
              bgcolor: isSocialMedia ? alpha(getPlatformColor(), 0.8) : undefined,
            }
          }}
        >
          Send
        </Button>
      </Box>

      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <Box sx={{ mt: 2, p: 1, borderRadius: 1, bgcolor: alpha(ACE_COLORS.WHITE, 0.5) }}>
          <Typography variant="caption" sx={{ display: 'block', mb: 1 }}>
            Attachments ({attachments.length})
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {attachments.map((file, index) => (
              <Box
                key={index}
                sx={{
                  p: 1,
                  borderRadius: 1,
                  border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <ImageIcon sx={{ mr: 1, fontSize: 20 }} />
                <Typography variant="caption" noWrap sx={{ maxWidth: 150 }}>
                  {file.name}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => setAttachments(attachments.filter((_, i) => i !== index))}
                >
                  <MoreVertIcon fontSize="small" />
                </IconButton>
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
MessageInput.propTypes = {
  // Core props
  value: PropTypes.string,
  onChange: PropTypes.func,
  onSend: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  suggestions: PropTypes.array,
  onUseSuggestion: PropTypes.func,
  isSocialMedia: PropTypes.bool,
  platform: PropTypes.string,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onUpgrade: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

MessageInput.displayName = 'MessageInput';

export default MessageInput;

/**
 * Enhanced Image Generator Wizard - Enterprise-grade wizard-based image generation component
 * Features: Plan-based wizard limitations, real-time wizard optimization, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced wizard capabilities and interactive step management exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Chip,
  Alert,
  AlertTitle,
  LinearProgress,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Collapse,
  IconButton,
  CircularProgress,
  Snackbar,
  alpha
} from '@mui/material';
import {
  Language as LanguageIcon,
  CloudUpload as UploadIcon,
  LocationOn as LocationIcon,
  Palette as PaletteIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
  History as HistoryIcon,
  AutoAwesome as AIIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  NavigateNext as NextIcon,
  NavigateBefore as BackIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useNotification } from '../../hooks/useNotification';
import { useAccessibility } from '../../hooks/useAccessibility';
import { useLanguage } from '../../hooks/useLanguage';

// Enhanced component imports
import LanguageSelector from '../common/LanguageSelector';
import ProductImageUpload from './ProductImageUpload';
import ICPEnvironmentalContext from './ICPEnvironmentalContext';
import AdvancedBrandingPanel from './AdvancedBrandingPanel';
import ErrorBoundary from '../common/ErrorBoundary';

// API imports
import { generateImage } from '../../api/content';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Wizard modes with enhanced configurations
const WIZARD_MODES = {
  QUICK: {
    id: 'quick',
    name: 'Quick Generation',
    description: 'Fast 3-step wizard for basic image generation',
    steps: 3,
    color: ACE_COLORS.PURPLE,
    subscriptionLimits: {
      creator: { available: true, maxSteps: 3, features: ['basic_generation'] },
      accelerator: { available: true, maxSteps: 3, features: ['basic_generation', 'advanced_styles'] },
      dominator: { available: true, maxSteps: 3, features: ['basic_generation', 'advanced_styles', 'ai_optimization'] }
    }
  },
  ADVANCED: {
    id: 'advanced',
    name: 'Advanced Customization',
    description: 'Full 5-step wizard with advanced features',
    steps: 5,
    color: ACE_COLORS.YELLOW,
    subscriptionLimits: {
      creator: { available: false, maxSteps: 0, features: [] },
      accelerator: { available: true, maxSteps: 5, features: ['advanced_customization', 'templates'] },
      dominator: { available: true, maxSteps: 5, features: ['advanced_customization', 'templates', 'ai_optimization'] }
    }
  },
  TEMPLATE: {
    id: 'template',
    name: 'Template-Based',
    description: 'Use pre-built templates for quick generation',
    steps: 2,
    color: ACE_COLORS.DARK,
    subscriptionLimits: {
      creator: { available: true, maxSteps: 2, features: ['basic_templates'] },
      accelerator: { available: true, maxSteps: 2, features: ['basic_templates', 'custom_templates'] },
      dominator: { available: true, maxSteps: 2, features: ['basic_templates', 'custom_templates', 'ai_templates'] }
    }
  },
  BRAND_AWARE: {
    id: 'brand-aware',
    name: 'Brand-Aware Generation',
    description: 'Brand-consistent wizard with automatic optimization',
    steps: 4,
    color: ACE_COLORS.PURPLE,
    subscriptionLimits: {
      creator: { available: false, maxSteps: 0, features: [] },
      accelerator: { available: true, maxSteps: 4, features: ['brand_integration'] },
      dominator: { available: true, maxSteps: 4, features: ['brand_integration', 'ai_brand_optimization'] }
    }
  }
};

/**
 * Enhanced EnhancedImageGeneratorWizard Component - Enterprise-grade wizard-based image generation management
 * Features: Plan-based wizard limitations, real-time wizard optimization, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced wizard capabilities and interactive step management exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} [props.selectedICP] - Selected ICP data
 * @param {Function} [props.onImageGenerated] - Callback when images are generated
 * @param {Function} [props.onSaveTemplate] - Callback to save templates
 * @param {boolean} [props.showAdvancedOptions=true] - Show advanced options
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=false] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onUpgrade] - Upgrade callback
 * @param {boolean} [props.disabled=false] - Disabled state
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-image-generator-wizard'] - Test identifier
 * @param {Object} [props.customization={}] - Custom styling options
 * @param {string} [props.className=''] - Additional CSS classes
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const EnhancedImageGeneratorWizard = memo(forwardRef(({
  selectedICP,
  onImageGenerated,
  onSaveTemplate,
  showAdvancedOptions = true,
  enableRealTimeOptimization = true,
  onRefresh,
  onUpgrade,
  disabled = false,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-image-generator-wizard',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const {
    currentLanguage,
    generateCulturalPromptPrefix,
    getUILabelForCurrent
  } = useLanguage();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced refs for comprehensive tracking
  const containerRef = useRef(null);
  const stepperRef = useRef(null);

  // Enhanced state management with comprehensive error handling
  const [state, setState] = useState({
    loading: subscriptionLoading,
    refreshing: false,
    showUpgradeDialog: false,
    showAnalyticsPanel: false,
    showExportDialog: false,
    animationKey: 0,
    errors: {},

    // Wizard management state
    activeStep: 0,
    isGenerating: false,
    showPreview: true,
    showAdvanced: false,
    wizardMode: 'quick',

    // Error handling state
    hasError: false,
    errorMessage: null,
    errorBoundaryKey: 0,
    retryCount: 0,
    lastErrorTime: null
  });

  // Form data
  const [formData, setFormData] = useState({
    // Step 1: Language & Basic Settings
    language: currentLanguage,
    prompt: '',
    topic: '',
    style: 'professional',
    
    // Step 2: Product Images
    productImages: [],
    
    // Step 3: ICP Context
    icpContext: '',
    useICPContext: true,
    
    // Step 4: Branding
    branding: null,
    useBranding: true,
    
    // Step 5: Advanced Options
    size: '1024x1024',
    quality: 'standard',
    count: 1,
    seed: null
  });

  // Generated content
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [generatedImages, setGeneratedImages] = useState([]);
  const [promptHistory, setPromptHistory] = useState([]);

  // Template management
  const [templateDialog, setTemplateDialog] = useState(false);
  const [templateName, setTemplateName] = useState('');

  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Plan-based features configuration
    const features = {
      creator: {
        maxSteps: 3,
        hasAdvancedWizard: false,
        hasAIInsights: false,
        hasRealTimeOptimization: false,
        hasTemplates: false,
        hasBrandAware: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000
      },
      accelerator: {
        maxSteps: 5,
        hasAdvancedWizard: true,
        hasAIInsights: false,
        hasRealTimeOptimization: true,
        hasTemplates: true,
        hasBrandAware: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 1000
      },
      dominator: {
        maxSteps: -1,
        hasAdvancedWizard: true,
        hasAIInsights: true,
        hasRealTimeOptimization: true,
        hasTemplates: true,
        hasBrandAware: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'ai-powered',
        refreshInterval: 1000
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      ...currentFeatures
    };
  }, [subscription?.plan_id, subscription?.plan_name]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Enhanced image generator wizard with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Multi-step wizard interface with ${subscriptionFeatures.trackingLevel} tracking`,
      'aria-live': enableRealTimeOptimization ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel, enableRealTimeOptimization]);

  /**
   * Enhanced upgrade prompt handler with mid-cycle purchase integration
   */
  const handleUpgradePrompt = useCallback((feature = 'wizard') => {
    setState(prev => ({ ...prev, showUpgradeDialog: true }));

    // Track upgrade prompt analytics
    if (window.analytics) {
      window.analytics.track('Wizard Upgrade Prompt Shown', {
        currentPlan: subscriptionFeatures.planId,
        feature,
        timestamp: new Date().toISOString()
      });
    }
  }, [subscriptionFeatures.planId]);

  // Enhanced steps configuration with subscription validation
  const steps = useMemo(() => {
    const baseSteps = [
      {
        label: getUILabelForCurrent('language') + ' & Basic Settings',
        description: 'Set language and basic image parameters',
        icon: <LanguageIcon />,
        required: true,
        available: true
      },
      {
        label: 'Product Images',
        description: 'Upload reference images (optional)',
        icon: <UploadIcon />,
        required: false,
        available: true
      },
      {
        label: 'Environmental Context',
        description: 'ICP-based location and cultural context',
        icon: <LocationIcon />,
        required: false,
        available: subscriptionFeatures.hasAdvancedWizard
      },
      {
        label: 'Branding Settings',
        description: 'Apply brand colors and visual style',
        icon: <PaletteIcon />,
        required: false,
        available: subscriptionFeatures.hasBrandAware
      },
      {
        label: 'Preview & Generate',
        description: 'Review settings and generate images',
        icon: <PreviewIcon />,
        required: true,
        available: true
      }
    ];

    // Filter steps based on subscription limits
    if (subscriptionFeatures.maxSteps !== -1) {
      return baseSteps.slice(0, subscriptionFeatures.maxSteps);
    }

    return baseSteps.filter(step => step.available);
  }, [getUILabelForCurrent, subscriptionFeatures]);

  /**
   * Enhanced form data update handler with subscription validation - Production Ready
   */
  const updateFormData = useCallback((field, value) => {
    // Check subscription limits for advanced features
    if (field === 'wizardMode' && value !== 'quick') {
      const mode = WIZARD_MODES[value.toUpperCase()];
      if (mode && !mode.subscriptionLimits[subscriptionFeatures.planId]?.available) {
        const errorMessage = `${mode.name} requires ${subscriptionFeatures.planId === 'creator' ? 'Accelerator' : 'Dominator'} plan`;
        setState(prev => ({ ...prev, errors: { ...prev.errors, mode: errorMessage } }));
        showErrorNotification(errorMessage);
        handleUpgradePrompt(field);
        return;
      }
    }

    try {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));

      // Announce change to screen readers
      announceToScreenReader(`${field} updated`);

      // Track analytics
      if (window.analytics) {
        window.analytics.track('Wizard Setting Changed', {
          field,
          planId: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('Error updating form data:', error);
      const errorMessage = 'Failed to update wizard settings';
      setState(prev => ({ ...prev, errors: { ...prev.errors, update: errorMessage } }));
      showErrorNotification(errorMessage);
    }
  }, [
    subscriptionFeatures,
    announceToScreenReader,
    showErrorNotification,
    handleUpgradePrompt
  ]);

  // Generate enhanced prompt
  const generateEnhancedPrompt = useCallback(() => {
    let prompt = '';

    // Add cultural prefix based on language
    if (formData.topic) {
      prompt += generateCulturalPromptPrefix(
        selectedICP?.demographics?.industry || 'business',
        formData.style
      );
    }

    // Add base prompt
    if (formData.prompt) {
      prompt += formData.prompt + ' ';
    } else if (formData.topic) {
      prompt += `Create a professional image about ${formData.topic}. `;
    }

    // Add ICP environmental context
    if (formData.useICPContext && formData.icpContext) {
      prompt += formData.icpContext + ' ';
    }

    // Add product image context
    if (formData.productImages.length > 0) {
      const imageDescriptions = formData.productImages
        .filter(img => img.description)
        .map(img => `${img.integrationMode}: ${img.description}`)
        .join(', ');
      
      if (imageDescriptions) {
        prompt += `Incorporate these product elements: ${imageDescriptions}. `;
      }
    }

    // Add branding context
    if (formData.useBranding && formData.branding) {
      const branding = formData.branding;
      if (branding.colorSystem) {
        prompt += `Use brand colors: primary ${branding.colorSystem.primary}, secondary ${branding.colorSystem.secondary}. `;
      }
      if (branding.visualStyle) {
        prompt += `Apply ${branding.visualStyle.photographyStyle} photography style with ${branding.visualStyle.lighting} lighting. `;
      }
    }

    // Add quality specifications
    prompt += 'Ensure high-quality, professional composition with excellent lighting and visual appeal.';

    return prompt.trim();
  }, [formData, selectedICP, generateCulturalPromptPrefix]);

  // Update generated prompt when form data changes
  useEffect(() => {
    const newPrompt = generateEnhancedPrompt();
    setGeneratedPrompt(newPrompt);
  }, [formData, generateEnhancedPrompt]);

  /**
   * Enhanced step navigation handlers with validation - Production Ready
   */
  const handleNext = useCallback(() => {
    const nextStep = Math.min(state.activeStep + 1, steps.length - 1);

    // Check if next step is available for current subscription
    if (!steps[nextStep]?.available) {
      const errorMessage = 'This step requires a higher subscription plan';
      setState(prev => ({ ...prev, errors: { ...prev.errors, step: errorMessage } }));
      showErrorNotification(errorMessage);
      handleUpgradePrompt('step_access');
      return;
    }

    setState(prev => ({ ...prev, activeStep: nextStep }));
    announceToScreenReader(`Moved to step ${nextStep + 1}: ${steps[nextStep].label}`);

    // Track analytics
    if (window.analytics) {
      window.analytics.track('Wizard Step Advanced', {
        fromStep: state.activeStep,
        toStep: nextStep,
        planId: subscriptionFeatures.planId,
        timestamp: new Date().toISOString()
      });
    }
  }, [state.activeStep, steps, subscriptionFeatures.planId, announceToScreenReader, showErrorNotification, handleUpgradePrompt]);

  const handleBack = useCallback(() => {
    const prevStep = Math.max(state.activeStep - 1, 0);
    setState(prev => ({ ...prev, activeStep: prevStep }));
    announceToScreenReader(`Moved back to step ${prevStep + 1}: ${steps[prevStep].label}`);
  }, [state.activeStep, steps, announceToScreenReader]);

  const handleStepClick = useCallback((stepIndex) => {
    // Check if step is available for current subscription
    if (!steps[stepIndex]?.available) {
      const errorMessage = 'This step requires a higher subscription plan';
      setState(prev => ({ ...prev, errors: { ...prev.errors, step: errorMessage } }));
      showErrorNotification(errorMessage);
      handleUpgradePrompt('step_access');
      return;
    }

    setState(prev => ({ ...prev, activeStep: stepIndex }));
    announceToScreenReader(`Jumped to step ${stepIndex + 1}: ${steps[stepIndex].label}`);
  }, [steps, announceToScreenReader, showErrorNotification, handleUpgradePrompt]);

  /**
   * Enhanced image generation handler with subscription validation - Production Ready
   */
  const handleGenerate = useCallback(async () => {
    if (!generatedPrompt.trim()) {
      showErrorNotification('Please provide a prompt or topic');
      return;
    }

    setState(prev => ({ ...prev, isGenerating: true }));

    try {
      const requestData = {
        prompt: generatedPrompt,
        size: formData.size,
        style: formData.quality === 'premium' ? 'vivid' : 'natural',
        n: formData.count
      };

      // Add branding if enabled
      if (formData.useBranding && formData.branding) {
        requestData.branding = formData.branding;
      }

      const imageUrls = await generateImage(requestData);

      const newImages = imageUrls.map(url => ({
        url,
        prompt: generatedPrompt,
        settings: formData,
        generated_at: new Date().toISOString()
      }));

      setGeneratedImages(newImages);
      
      // Add to history
      setPromptHistory(prev => [
        {
          prompt: generatedPrompt,
          settings: formData,
          timestamp: new Date().toISOString()
        },
        ...prev.slice(0, 9) // Keep last 10
      ]);

      onImageGenerated?.(newImages);
      showSuccessNotification(`Generated ${newImages.length} image(s) successfully!`);

    } catch (error) {
      console.error('Generation error:', error);
      showErrorNotification('Failed to generate images. Please try again.');
    } finally {
      setState(prev => ({ ...prev, isGenerating: false }));
    }
  }, [generatedPrompt, formData, showErrorNotification, showSuccessNotification, onImageGenerated]);

  /**
   * Enhanced template saving handler - Production Ready
   */
  const handleSaveTemplate = useCallback(() => {
    if (!templateName.trim()) {
      showErrorNotification('Please enter a template name');
      return;
    }

    // Check subscription limits for templates
    if (!subscriptionFeatures.hasTemplates) {
      const errorMessage = 'Template saving requires Accelerator or Dominator plan';
      setState(prev => ({ ...prev, errors: { ...prev.errors, template: errorMessage } }));
      showErrorNotification(errorMessage);
      handleUpgradePrompt('templates');
      return;
    }

    try {
      const template = {
        name: templateName,
        settings: formData,
        prompt: generatedPrompt,
        created_at: new Date().toISOString()
      };

      onSaveTemplate?.(template);
      setTemplateDialog(false);
      setTemplateName('');
      showSuccessNotification('Template saved successfully!');
      announceToScreenReader('Template saved to library');

      // Track analytics
      if (window.analytics) {
        window.analytics.track('Wizard Template Saved', {
          templateName,
          planId: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('Error saving template:', error);
      showErrorNotification('Failed to save template');
    }
  }, [
    templateName,
    formData,
    generatedPrompt,
    subscriptionFeatures,
    onSaveTemplate,
    showErrorNotification,
    showSuccessNotification,
    announceToScreenReader,
    handleUpgradePrompt
  ]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive wizard API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getFormData: () => formData,
    setFormData: (newFormData) => {
      setFormData(newFormData);
    },
    getGeneratedPrompt: () => generatedPrompt,
    getGeneratedImages: () => generatedImages,
    clearGeneratedImages: () => {
      setGeneratedImages([]);
      announceToScreenReader('Generated images cleared');
    },
    resetWizard: () => {
      const defaultFormData = {
        language: currentLanguage,
        prompt: '',
        topic: '',
        style: 'professional',
        productImages: [],
        icpContext: '',
        useICPContext: true,
        branding: null,
        useBranding: true,
        size: '1024x1024',
        quality: 'standard',
        count: 1,
        seed: null
      };
      setFormData(defaultFormData);
      setState(prev => ({ ...prev, activeStep: 0 }));
      announceToScreenReader('Wizard reset to defaults');
    },

    // Step methods
    setActiveStep: (stepIndex) => {
      setState(prev => ({ ...prev, activeStep: stepIndex }));
    },
    getActiveStep: () => state.activeStep,
    nextStep: handleNext,
    previousStep: handleBack,
    goToStep: handleStepClick,

    // Generation methods
    generateImages: handleGenerate,
    isGenerating: () => state.isGenerating,

    // Template methods
    saveTemplate: handleSaveTemplate,
    getPromptHistory: () => promptHistory,

    // Accessibility methods
    focus: () => {
      if (containerRef.current) {
        setFocusToElement(containerRef.current);
      }
    },
    announce: (message) => announceToScreenReader(message)
  }), [
    formData,
    generatedPrompt,
    generatedImages,
    state.activeStep,
    state.isGenerating,
    promptHistory,
    currentLanguage,
    handleNext,
    handleBack,
    handleStepClick,
    handleGenerate,
    handleSaveTemplate,
    announceToScreenReader,
    setFocusToElement
  ]);

  // Step content components
  const renderStepContent = (stepIndex) => {
    switch (stepIndex) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <LanguageSelector
                fullWidth
                showCulturalPreview={true}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Topic or Subject"
                value={formData.topic}
                onChange={(e) => updateFormData('topic', e.target.value)}
                placeholder="e.g., AI in Business, Natural Beauty Products"
                helperText="What is the main subject of your image?"
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Custom Prompt (Optional)"
                value={formData.prompt}
                onChange={(e) => updateFormData('prompt', e.target.value)}
                placeholder="Describe your image in detail..."
                helperText="Leave empty to auto-generate from topic and settings"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Visual Style</InputLabel>
                <Select
                  value={formData.style}
                  onChange={(e) => updateFormData('style', e.target.value)}
                  label="Visual Style"
                >
                  <MenuItem value="professional">Professional</MenuItem>
                  <MenuItem value="creative">Creative</MenuItem>
                  <MenuItem value="luxury">Luxury</MenuItem>
                  <MenuItem value="minimalist">Minimalist</MenuItem>
                  <MenuItem value="rustic">Rustic</MenuItem>
                  <MenuItem value="modern">Modern</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <ProductImageUpload
            onImagesChange={(images) => updateFormData('productImages', images)}
            maxFiles={3}
            showIntegrationOptions={true}
          />
        );

      case 2:
        return (
          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.useICPContext}
                  onChange={(e) => updateFormData('useICPContext', e.target.checked)}
                />
              }
              label="Use ICP Environmental Context"
              sx={{ mb: 2 }}
            />
            
            {formData.useICPContext && (
              <ICPEnvironmentalContext
                selectedICP={selectedICP}
                onContextChange={(context) => updateFormData('icpContext', context)}
                showAdvancedOptions={showAdvancedOptions}
              />
            )}
          </Box>
        );

      case 3:
        return (
          <Box>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.useBranding}
                  onChange={(e) => updateFormData('useBranding', e.target.checked)}
                />
              }
              label="Apply Branding Settings"
              sx={{ mb: 2 }}
            />
            
            {formData.useBranding && (
              <AdvancedBrandingPanel
                onBrandingChange={(branding) => updateFormData('branding', branding)}
                showPreview={true}
              />
            )}
          </Box>
        );

      case 4:
        return (
          <Grid container spacing={3}>
            {/* Advanced Options */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <IconButton
                  onClick={() => setState(prev => ({ ...prev, showAdvanced: !prev.showAdvanced }))}
                  size="small"
                >
                  {state.showAdvanced ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
                <Typography variant="subtitle1">
                  Advanced Options
                </Typography>
              </Box>

              <Collapse in={state.showAdvanced}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Image Size</InputLabel>
                      <Select
                        value={formData.size}
                        onChange={(e) => updateFormData('size', e.target.value)}
                        label="Image Size"
                      >
                        <MenuItem value="1024x1024">Square (1024x1024)</MenuItem>
                        <MenuItem value="1024x1792">Portrait (1024x1792)</MenuItem>
                        <MenuItem value="1792x1024">Landscape (1792x1024)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel>Quality</InputLabel>
                      <Select
                        value={formData.quality}
                        onChange={(e) => updateFormData('quality', e.target.value)}
                        label="Quality"
                      >
                        <MenuItem value="standard">Standard</MenuItem>
                        <MenuItem value="premium">Premium (HD)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <TextField
                      fullWidth
                      size="small"
                      type="number"
                      label="Number of Images"
                      value={formData.count}
                      onChange={(e) => updateFormData('count', Math.min(4, Math.max(1, parseInt(e.target.value) || 1)))}
                      inputProps={{ min: 1, max: 4 }}
                    />
                  </Grid>
                </Grid>
              </Collapse>
            </Grid>

            {/* Prompt Preview */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Generated Prompt Preview:
              </Typography>
              
              <Box
                sx={{
                  p: 2,
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                  border: '1px solid',
                  borderColor: 'grey.200',
                  maxHeight: 200,
                  overflow: 'auto'
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  {generatedPrompt || 'Prompt will be generated based on your settings...'}
                </Typography>
              </Box>
              
              <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                <Chip
                  label={`Language: ${currentLanguage.toUpperCase()}`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
                
                {formData.useICPContext && selectedICP && (
                  <Chip
                    label={`ICP: ${selectedICP.name}`}
                    size="small"
                    color="secondary"
                    variant="outlined"
                  />
                )}
                
                {formData.productImages.length > 0 && (
                  <Chip
                    label={`${formData.productImages.length} Product Images`}
                    size="small"
                    color="info"
                    variant="outlined"
                  />
                )}
                
                {formData.useBranding && (
                  <Chip
                    label="Branding Applied"
                    size="small"
                    color="success"
                    variant="outlined"
                  />
                )}
              </Box>
            </Grid>

            {/* Generation Button */}
            <Grid item xs={12}>
              <Button
                variant="contained"
                size="large"
                fullWidth
                onClick={handleGenerate}
                disabled={state.isGenerating || !generatedPrompt.trim()}
                startIcon={state.isGenerating ? <RefreshIcon /> : <AIIcon />}
                sx={{
                  mt: 2,
                  backgroundColor: ACE_COLORS.PURPLE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                  }
                }}
              >
                {state.isGenerating ? 'Generating...' : `Generate ${formData.count} Image${formData.count > 1 ? 's' : ''}`}
              </Button>

              {state.isGenerating && (
                <LinearProgress
                  sx={{
                    mt: 1,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: ACE_COLORS.PURPLE
                    }
                  }}
                />
              )}
            </Grid>

            {/* Generated Images */}
            {generatedImages.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Generated Images
                </Typography>
                
                <Grid container spacing={2}>
                  {generatedImages.map((image, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Card>
                        <Box
                          component="img"
                          src={image.url}
                          alt={`Generated image ${index + 1}`}
                          sx={{
                            width: '100%',
                            height: 200,
                            objectFit: 'cover'
                          }}
                        />
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            )}
          </Grid>
        );

      default:
        return null;
    }
  };

  // Main render condition checks
  if (state.loading && !formData) {
    return (
      <ErrorBoundary
        fallback={
          <Box sx={{ p: 1, textAlign: 'center' }}>
            <Typography variant="caption" color="error">
              Wizard unavailable
            </Typography>
          </Box>
        }
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 200,
            ...customization,
            ...style,
            ...sx
          }}
          className={className}
          data-testid={testId}
          {...getAccessibilityProps()}
        >
          <CircularProgress sx={{ color: ACE_COLORS.PURPLE }} />
          <Typography variant="body1" sx={{ ml: 2, color: ACE_COLORS.DARK }}>
            Loading wizard...
          </Typography>
        </Box>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Box sx={{ p: 1, textAlign: 'center' }}>
          <Typography variant="caption" color="error">
            Wizard error
          </Typography>
        </Box>
      }
    >
      <Box
        ref={containerRef}
        sx={{
          ...customization,
          ...style,
          ...sx
        }}
        className={className}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        {/* Enhanced Wizard Header */}
        <Card sx={{ mb: 3, border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AIIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: 32 }} />
                <Typography variant="h5" sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                  Enhanced Image Generator Wizard
                </Typography>
                {subscriptionFeatures.hasAIInsights && (
                  <Chip
                    label="AI Powered"
                    size="small"
                    sx={{
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                      color: ACE_COLORS.PURPLE,
                      fontWeight: 600
                    }}
                  />
                )}
              </Box>

              <Box sx={{ display: 'flex', gap: 1 }}>
                {subscriptionFeatures.hasAnalytics && (
                  <Tooltip title="View Analytics">
                    <IconButton
                      onClick={() => setState(prev => ({ ...prev, showAnalyticsPanel: !prev.showAnalyticsPanel }))}
                      sx={{ color: ACE_COLORS.PURPLE }}
                    >
                      <AnalyticsIcon />
                    </IconButton>
                  </Tooltip>
                )}

                <Tooltip title="Save as Template">
                  <IconButton
                    onClick={() => setTemplateDialog(true)}
                    disabled={!generatedPrompt.trim() || !subscriptionFeatures.hasTemplates}
                    sx={{ color: ACE_COLORS.PURPLE }}
                  >
                    <SaveIcon />
                  </IconButton>
                </Tooltip>

                <Tooltip title="View History">
                  <IconButton
                    disabled={promptHistory.length === 0}
                    sx={{ color: ACE_COLORS.PURPLE }}
                  >
                    <HistoryIcon />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Refresh Wizard">
                  <IconButton
                    onClick={() => {
                      setState(prev => ({ ...prev, refreshing: true }));
                      setTimeout(() => setState(prev => ({ ...prev, refreshing: false })), 500);
                      if (onRefresh) onRefresh();
                    }}
                    disabled={state.loading}
                    sx={{ color: ACE_COLORS.PURPLE }}
                  >
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            {/* Subscription Badge */}
            <Chip
              label={`${subscriptionFeatures.planName} Plan - ${subscriptionFeatures.maxSteps === -1 ? 'Unlimited' : subscriptionFeatures.maxSteps} Steps`}
              size="small"
              sx={{
                mb: 2,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                fontWeight: 600
              }}
            />

            {/* Loading State */}
            {state.loading && (
              <Box sx={{ mb: 2 }}>
                <LinearProgress
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: ACE_COLORS.PURPLE
                    }
                  }}
                />
              </Box>
            )}

            {/* Error Display */}
            {Object.keys(state.errors).length > 0 && (
              <Alert
                severity="error"
                sx={{ mb: 2 }}
                action={
                  <Button
                    color="inherit"
                    size="small"
                    onClick={() => setState(prev => ({ ...prev, errors: {} }))}
                  >
                    Dismiss
                  </Button>
                }
              >
                <AlertTitle>Error</AlertTitle>
                {Object.values(state.errors)[0]}
              </Alert>
            )}

            {/* Enhanced Progress Stepper */}
            <Stepper
              ref={stepperRef}
              activeStep={state.activeStep}
              orientation="horizontal"
              sx={{
                '& .MuiStepLabel-root': {
                  color: ACE_COLORS.DARK,
                  '&.Mui-active': {
                    color: ACE_COLORS.PURPLE
                  },
                  '&.Mui-completed': {
                    color: ACE_COLORS.PURPLE
                  }
                },
                '& .MuiStepConnector-line': {
                  borderColor: alpha(ACE_COLORS.PURPLE, 0.3)
                },
                '& .MuiStepConnector-root.Mui-active .MuiStepConnector-line': {
                  borderColor: ACE_COLORS.PURPLE
                },
                '& .MuiStepConnector-root.Mui-completed .MuiStepConnector-line': {
                  borderColor: ACE_COLORS.PURPLE
                }
              }}
            >
              {steps.map((step, index) => (
                <Step key={step.label} completed={index < state.activeStep}>
                  <StepLabel
                    onClick={() => handleStepClick(index)}
                    sx={{
                      cursor: step.available ? 'pointer' : 'not-allowed',
                      opacity: step.available ? 1 : 0.5
                    }}
                    icon={step.icon}
                    aria-label={`Step ${index + 1}: ${step.label}${step.available ? '' : ' (requires upgrade)'}`}
                  >
                    {step.label}
                    {!step.available && (
                      <Chip
                        label="Upgrade"
                        size="small"
                        sx={{
                          ml: 1,
                          backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                          color: ACE_COLORS.DARK,
                          fontSize: '0.7rem'
                        }}
                      />
                    )}
                  </StepLabel>
                </Step>
              ))}
            </Stepper>
          </CardContent>
        </Card>

        {/* Enhanced Step Content */}
        <Card sx={{ border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
              {steps[state.activeStep].label}
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {steps[state.activeStep].description}
            </Typography>

            {renderStepContent(state.activeStep)}

            {/* Enhanced Navigation Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                onClick={handleBack}
                disabled={state.activeStep === 0 || disabled}
                startIcon={<BackIcon />}
                sx={{
                  color: ACE_COLORS.DARK,
                  borderColor: ACE_COLORS.DARK,
                  '&:hover': {
                    borderColor: ACE_COLORS.PURPLE,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                  }
                }}
                variant="outlined"
              >
                Back
              </Button>

              <Button
                variant="contained"
                onClick={handleNext}
                disabled={state.activeStep === steps.length - 1 || disabled}
                endIcon={<NextIcon />}
                sx={{
                  backgroundColor: ACE_COLORS.PURPLE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                  }
                }}
              >
                Next
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Enhanced Template Save Dialog */}
        <Dialog
          open={templateDialog}
          onClose={() => setTemplateDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
            Save Template
            {!subscriptionFeatures.hasTemplates && (
              <Chip
                label="Requires Upgrade"
                size="small"
                sx={{
                  ml: 2,
                  backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                  color: ACE_COLORS.DARK
                }}
              />
            )}
          </DialogTitle>
          <DialogContent>
            <TextField
              fullWidth
              label="Template Name"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              placeholder="e.g., Beauty Product Template"
              sx={{ mt: 1 }}
              disabled={!subscriptionFeatures.hasTemplates}
            />
            {!subscriptionFeatures.hasTemplates && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Template saving is available with Accelerator and Dominator plans.
              </Alert>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setTemplateDialog(false)}
              color="inherit"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveTemplate}
              variant="contained"
              disabled={!subscriptionFeatures.hasTemplates}
              sx={{ backgroundColor: ACE_COLORS.PURPLE }}
            >
              Save Template
            </Button>
          </DialogActions>
        </Dialog>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
            Upgrade Your Plan
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Unlock advanced wizard features with a higher tier plan:
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Accelerator Plan Features:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ ml: 2 }}>
                <li>5-step advanced wizard</li>
                <li>Template saving and management</li>
                <li>Brand-aware generation</li>
                <li>Real-time optimization</li>
                <li>Wizard analytics</li>
              </Typography>
            </Box>

            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.PURPLE }}>
                Dominator Plan Features:
              </Typography>
              <Typography variant="body2" component="ul" sx={{ ml: 2 }}>
                <li>Unlimited wizard steps</li>
                <li>AI-powered wizard optimization</li>
                <li>Advanced template customization</li>
                <li>Priority processing</li>
                <li>Custom wizard modes</li>
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
              color="inherit"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setState(prev => ({ ...prev, showUpgradeDialog: false }));
                if (onUpgrade) onUpgrade();
              }}
              variant="contained"
              sx={{ backgroundColor: ACE_COLORS.PURPLE }}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setNotification(prev => ({ ...prev, open: false }))}
            severity={notification.severity}
            variant="filled"
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </Box>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
EnhancedImageGeneratorWizard.propTypes = {
  // Core props
  selectedICP: PropTypes.object,
  onImageGenerated: PropTypes.func,
  onSaveTemplate: PropTypes.func,
  showAdvancedOptions: PropTypes.bool,

  // Enhanced props
  enableRealTimeOptimization: PropTypes.bool,
  onRefresh: PropTypes.func,
  onUpgrade: PropTypes.func,

  // Standard props
  disabled: PropTypes.bool,

  // Accessibility props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,

  // Styling props
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

EnhancedImageGeneratorWizard.defaultProps = {
  showAdvancedOptions: true,
  enableRealTimeOptimization: true,
  disabled: false,
  testId: 'enhanced-image-generator-wizard',
  customization: {},
  className: '',
  style: {},
  sx: {}
};

// Display name for debugging
EnhancedImageGeneratorWizard.displayName = 'EnhancedImageGeneratorWizard';

export default EnhancedImageGeneratorWizard;

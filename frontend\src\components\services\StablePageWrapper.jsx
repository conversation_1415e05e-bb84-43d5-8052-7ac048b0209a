/**
 * Enhanced Stable Page Wrapper - Enterprise-grade page wrapper component
 * Features: Comprehensive page management with stable layout, transition animations, error boundaries,
 * accessibility compliance, subscription-based feature gating, page state persistence, navigation history,
 * responsive optimization, performance monitoring, and ACE Social platform integration with advanced
 * page capabilities and seamless layout management workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle,
  Suspense
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Container,
  Skeleton,
  Grid,
  useTheme,
  alpha,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Fade,
  Slide,
  Zoom,
  LinearProgress,
  Card,
  CardContent,
  Breadcrumbs,
  Link,
  useMediaQuery,
  GlobalStyles,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Analytics as AnalyticsIcon,
  Memory as MemoryIcon,
  SignalWifiOff as OfflineIcon,
  CloudDone as OnlineIcon,
  AccessTime as LoadTimeIcon,
  TrendingUp as PerformanceIcon
} from '@mui/icons-material';

import { useSubscription } from '../../contexts/SubscriptionContext';
import FeatureGate from '../common/FeatureGate';
import ErrorBoundary from './ErrorBoundary';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Plan-based page wrapper limitations
const PLAN_LIMITS = {
  1: { // Creator
    pageTransitions: false,
    performanceMonitoring: false,
    advancedErrorBoundary: false,
    pageStateHistory: false,
    customLoadingStates: false,
    analyticsIntegration: false,
    offlineSupport: false,
    memoryOptimization: false
  },
  2: { // Accelerator
    pageTransitions: true,
    performanceMonitoring: true,
    advancedErrorBoundary: true,
    pageStateHistory: true,
    customLoadingStates: true,
    analyticsIntegration: false,
    offlineSupport: false,
    memoryOptimization: true
  },
  3: { // Dominator
    pageTransitions: true,
    performanceMonitoring: true,
    advancedErrorBoundary: true,
    pageStateHistory: true,
    customLoadingStates: true,
    analyticsIntegration: true,
    offlineSupport: true,
    memoryOptimization: true
  }
};

// Page states
const PAGE_STATES = {
  LOADING: 'loading',
  LOADED: 'loaded',
  ERROR: 'error',
  OFFLINE: 'offline',
  REFRESHING: 'refreshing'
};

// Transition types
const TRANSITION_TYPES = {
  FADE: 'fade',
  SLIDE: 'slide',
  ZOOM: 'zoom',
  NONE: 'none'
};

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  EXCELLENT: 1000, // < 1s
  GOOD: 2000,      // < 2s
  POOR: 5000       // < 5s
};

/**
 * Enhanced Stable Page Wrapper - Comprehensive page management with advanced features
 * Implements plan-based feature restrictions and enterprise-grade page capabilities
 */
const StablePageWrapper = memo(forwardRef(({
  children,
  loading = false,
  fallbackMessage = "This page encountered an unexpected error. Please refresh the page or contact support if the issue persists.",
  loadingComponent = null,
  maxWidth = "lg",
  pageTitle = "",
  breadcrumbs = [],
  onPageLoad,
  onPageError,
  onPageRefresh,
  enableTransitions = true,
  transitionType = TRANSITION_TYPES.FADE,
  transitionDuration = 300,
  showLoadingProgress = true,
  showPerformanceMetrics = false,
  pageId = null,
  analyticsEnabled = false,
  fullscreenMode = false,
  showBreadcrumbs = true,
  showPageActions = true
}, ref) => {
  const theme = useTheme();
  const { updateUsage, getPlanTier } = useSubscription();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Responsive design
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Core state management
  const pageRef = useRef(null);
  const performanceRef = useRef({
    startTime: Date.now(),
    loadTime: null,
    renderTime: null,
    memoryUsage: null
  });

  const [pageState, setPageState] = useState(loading ? PAGE_STATES.LOADING : PAGE_STATES.LOADED);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pageHistory, setPageHistory] = useState([]);
  const [isFullscreen, setIsFullscreen] = useState(fullscreenMode);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [errorDetails, setErrorDetails] = useState(null);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [memoryStats, setMemoryStats] = useState({});
  const [networkStats, setNetworkStats] = useState({});

  // Plan tier and feature access
  const planTier = getPlanTier();
  const planLimits = PLAN_LIMITS[planTier] || PLAN_LIMITS[1];

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshPage: () => handlePageRefresh(),
    toggleFullscreen: () => handleToggleFullscreen(),
    getPerformanceMetrics: () => getPerformanceMetrics(),
    getPageState: () => pageState,
    getMemoryStats: () => memoryStats,
    exportPageData: () => exportPageData(),
    clearPageHistory: () => setPageHistory([]),
    goOffline: () => setIsOnline(false),
    goOnline: () => setIsOnline(true),
    resetPerformanceMetrics: () => resetPerformanceMetrics()
  }), [pageState, memoryStats, handlePageRefresh, handleToggleFullscreen, getPerformanceMetrics, exportPageData, resetPerformanceMetrics]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: `all ${transitionDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`
  }), [theme, transitionDuration]);

  // Handle page refresh
  const handlePageRefresh = useCallback(async () => {
    try {
      setPageState(PAGE_STATES.REFRESHING);
      setLoadingProgress(0);

      if (onPageRefresh) {
        await onPageRefresh();
      }

      // Simulate loading progress
      const progressInterval = setInterval(() => {
        setLoadingProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            setPageState(PAGE_STATES.LOADED);
            return 100;
          }
          return prev + 10;
        });
      }, 100);

      // Update usage tracking
      await updateUsage('page_refresh', 1, {
        pageId,
        planTier
      });

      announceToScreenReader('Page refreshed successfully');
    } catch (error) {
      console.error('Page refresh error:', error);
      setPageState(PAGE_STATES.ERROR);
      setErrorDetails(error);
      announceToScreenReader('Page refresh failed');
    }
  }, [onPageRefresh, pageId, planTier, updateUsage, announceToScreenReader]);

  // Handle fullscreen toggle
  const handleToggleFullscreen = useCallback(() => {
    if (!planLimits.pageTransitions) {
      announceToScreenReader('Fullscreen mode is not available in your current plan');
      return;
    }

    setIsFullscreen(prev => {
      const newState = !prev;
      if (newState) {
        if (pageRef.current?.requestFullscreen) {
          pageRef.current.requestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        }
      }
      announceToScreenReader(newState ? 'Entered fullscreen mode' : 'Exited fullscreen mode');
      return newState;
    });
  }, [planLimits.pageTransitions, announceToScreenReader]);

  // Get performance metrics
  const getPerformanceMetrics = useCallback(() => {
    if (!planLimits.performanceMonitoring) {
      return null;
    }

    const currentTime = Date.now();
    const loadTime = performanceRef.current.loadTime || (currentTime - performanceRef.current.startTime);

    return {
      loadTime,
      renderTime: performanceRef.current.renderTime,
      memoryUsage: performanceRef.current.memoryUsage,
      performanceScore: loadTime < PERFORMANCE_THRESHOLDS.EXCELLENT ? 'excellent' :
                       loadTime < PERFORMANCE_THRESHOLDS.GOOD ? 'good' : 'poor',
      isOnline,
      pageState,
      planTier
    };
  }, [planLimits.performanceMonitoring, isOnline, pageState, planTier]);

  // Export page data
  const exportPageData = useCallback(() => {
    if (!planLimits.analyticsIntegration) {
      announceToScreenReader('Data export is not available in your current plan');
      return null;
    }

    const exportData = {
      pageInfo: {
        pageId,
        pageTitle,
        breadcrumbs,
        state: pageState,
        loadTime: performanceRef.current.loadTime
      },
      performance: getPerformanceMetrics(),
      history: pageHistory,
      memory: memoryStats,
      network: networkStats,
      metadata: {
        exportedAt: new Date().toISOString(),
        planTier,
        version: '2.0.0'
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `page-data-${pageId || 'unknown'}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    announceToScreenReader('Page data exported successfully');
    return exportData;
  }, [planLimits.analyticsIntegration, pageId, pageTitle, breadcrumbs, pageState, pageHistory, memoryStats, networkStats, planTier, getPerformanceMetrics, announceToScreenReader]);

  // Reset performance metrics
  const resetPerformanceMetrics = useCallback(() => {
    performanceRef.current = {
      startTime: Date.now(),
      loadTime: null,
      renderTime: null,
      memoryUsage: null
    };
    announceToScreenReader('Performance metrics reset');
  }, [announceToScreenReader]);

  // Effects for monitoring and state management
  useEffect(() => {
    // Performance monitoring
    if (planLimits.performanceMonitoring) {
      const startTime = Date.now();
      performanceRef.current.startTime = startTime;

      // Monitor memory usage
      if ('memory' in performance) {
        const memoryInfo = performance.memory;
        setMemoryStats({
          usedJSHeapSize: memoryInfo.usedJSHeapSize,
          totalJSHeapSize: memoryInfo.totalJSHeapSize,
          jsHeapSizeLimit: memoryInfo.jsHeapSizeLimit
        });
      }

      // Monitor network
      if ('connection' in navigator) {
        const connection = navigator.connection;
        setNetworkStats({
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        });
      }
    }

    // Page load completion
    const handleLoad = () => {
      const loadTime = Date.now() - performanceRef.current.startTime;
      performanceRef.current.loadTime = loadTime;

      if (onPageLoad) {
        onPageLoad({ loadTime, pageId });
      }

      setPageState(PAGE_STATES.LOADED);
      announceToScreenReader(`Page loaded in ${loadTime}ms`);
    };

    // Error handling
    const handleError = (error) => {
      setPageState(PAGE_STATES.ERROR);
      setErrorDetails(error);

      if (onPageError) {
        onPageError(error);
      }

      announceToScreenReader('Page encountered an error');
    };

    // Online/offline detection
    const handleOnline = () => {
      setIsOnline(true);
      setPageState(PAGE_STATES.LOADED);
      announceToScreenReader('Connection restored');
    };

    const handleOffline = () => {
      setIsOnline(false);
      if (planLimits.offlineSupport) {
        setPageState(PAGE_STATES.OFFLINE);
      }
      announceToScreenReader('Connection lost');
    };

    // Event listeners
    window.addEventListener('load', handleLoad);
    window.addEventListener('error', handleError);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('load', handleLoad);
      window.removeEventListener('error', handleError);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [planLimits, onPageLoad, onPageError, pageId, announceToScreenReader]);

  // Page state history tracking
  useEffect(() => {
    if (planLimits.pageStateHistory) {
      setPageHistory(prev => [...prev, {
        state: pageState,
        timestamp: new Date().toISOString(),
        pageId,
        planTier
      }].slice(-10)); // Keep last 10 states
    }
  }, [pageState, pageId, planTier, planLimits.pageStateHistory]);

  // Analytics tracking
  useEffect(() => {
    if (planLimits.analyticsIntegration && analyticsEnabled) {
      // Track page view
      updateUsage('page_view', 1, {
        pageId,
        pageTitle,
        loadTime: performanceRef.current.loadTime,
        planTier
      });
    }
  }, [planLimits.analyticsIntegration, analyticsEnabled, pageId, pageTitle, planTier, updateUsage]);

  // Enhanced default loading component
  const DefaultLoadingComponent = useMemo(() => {
    const LoadingContent = () => (
      <Container maxWidth={maxWidth} sx={{ py: theme.spacing(3), position: 'relative' }}>
        {/* Enhanced Loading Progress */}
        {showLoadingProgress && planLimits.customLoadingStates && (
          <Box sx={{ position: 'fixed', top: 0, left: 0, right: 0, zIndex: 9999 }}>
            <LinearProgress
              variant="determinate"
              value={loadingProgress}
              sx={{
                height: 4,
                backgroundColor: alpha(theme.palette.action.disabled, 0.2),
                '& .MuiLinearProgress-bar': {
                  background: `linear-gradient(90deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.YELLOW} 100%)`
                }
              }}
            />
          </Box>
        )}

        {/* Performance Metrics Display */}
        {showPerformanceMetrics && planLimits.performanceMonitoring && (
          <Card sx={{ ...glassMorphismStyles, mb: 2, background: alpha(theme.palette.info.light, 0.1) }}>
            <CardContent sx={{ py: 1.5 }}>
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <LoadTimeIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: 16 }} />
                  <Typography variant="caption">
                    Loading: {Math.round(loadingProgress)}%
                  </Typography>
                </Box>
                {isOnline ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <OnlineIcon sx={{ color: 'success.main', fontSize: 16 }} />
                    <Typography variant="caption" color="success.main">Online</Typography>
                  </Box>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <OfflineIcon sx={{ color: 'error.main', fontSize: 16 }} />
                    <Typography variant="caption" color="error.main">Offline</Typography>
                  </Box>
                )}
                {memoryStats.usedJSHeapSize && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <MemoryIcon sx={{ color: 'warning.main', fontSize: 16 }} />
                    <Typography variant="caption">
                      Memory: {Math.round(memoryStats.usedJSHeapSize / 1024 / 1024)}MB
                    </Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        )}

        {/* Enhanced Header Skeleton */}
        <Box sx={{ mb: theme.spacing(4) }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: theme.spacing(2) }}>
            <Box>
              <Skeleton
                variant="text"
                width={isMobile ? 150 : 200}
                height={isMobile ? 36 : 48}
                sx={{ background: alpha(ACE_COLORS.PURPLE, 0.1) }}
              />
              <Skeleton
                variant="text"
                width={isMobile ? 250 : 400}
                height={24}
                sx={{ mt: 1, background: alpha(theme.palette.action.disabled, 0.1) }}
              />
            </Box>
            <Skeleton
              variant="rectangular"
              width={isMobile ? 120 : 160}
              height={40}
              sx={{ borderRadius: 1, background: alpha(ACE_COLORS.YELLOW, 0.1) }}
            />
          </Box>
        </Box>

        {/* Enhanced Overview Section Skeleton */}
        <Box sx={{ ...glassMorphismStyles, p: theme.spacing(3), mb: theme.spacing(3) }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: theme.spacing(2) }}>
                <Skeleton
                  variant="circular"
                  width={isMobile ? 32 : 40}
                  height={isMobile ? 32 : 40}
                  sx={{ mr: theme.spacing(2), background: alpha(ACE_COLORS.PURPLE, 0.1) }}
                />
                <Box sx={{ flexGrow: 1 }}>
                  <Skeleton
                    variant="text"
                    width={isMobile ? 180 : 250}
                    height={isMobile ? 24 : 32}
                    sx={{ background: alpha(ACE_COLORS.PURPLE, 0.1) }}
                  />
                  <Skeleton
                    variant="text"
                    width={isMobile ? 220 : 350}
                    height={20}
                    sx={{ mt: 0.5, background: alpha(theme.palette.action.disabled, 0.1) }}
                  />
                </Box>
              </Box>
              <Skeleton
                variant="rectangular"
                height={isMobile ? 6 : 8}
                sx={{
                  borderRadius: 4,
                  mt: theme.spacing(2),
                  background: `linear-gradient(90deg, ${alpha(ACE_COLORS.PURPLE, 0.1)} 0%, ${alpha(ACE_COLORS.YELLOW, 0.1)} 100%)`
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: { xs: 'left', md: 'right' } }}>
                <Skeleton
                  variant="rectangular"
                  width={isMobile ? 140 : 180}
                  height={isMobile ? 40 : 48}
                  sx={{ borderRadius: 1, background: alpha(ACE_COLORS.YELLOW, 0.1) }}
                />
              </Box>
            </Grid>
          </Grid>
        </Box>

        {/* Enhanced Stats Cards Skeleton */}
        <Grid container spacing={isMobile ? 1 : 2} sx={{ mb: theme.spacing(3) }}>
          {[1, 2, 3, 4].map((i) => (
            <Grid item xs={6} sm={3} key={i}>
              <Box sx={{
                ...glassMorphismStyles,
                textAlign: 'center',
                p: isMobile ? theme.spacing(1.5) : theme.spacing(2),
                position: 'relative',
                overflow: 'hidden'
              }}>
                <Skeleton
                  variant="text"
                  width={isMobile ? 40 : 60}
                  height={isMobile ? 32 : 40}
                  sx={{
                    mx: 'auto',
                    background: alpha(ACE_COLORS.PURPLE, 0.1)
                  }}
                />
                <Skeleton
                  variant="text"
                  width={isMobile ? 60 : 80}
                  height={16}
                  sx={{
                    mx: 'auto',
                    mt: 1,
                    background: alpha(theme.palette.action.disabled, 0.1)
                  }}
                />
                {/* Animated shimmer effect */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: '-100%',
                    width: '100%',
                    height: '100%',
                    background: `linear-gradient(90deg, transparent, ${alpha(ACE_COLORS.YELLOW, 0.2)}, transparent)`,
                    animation: 'shimmer 2s infinite',
                    '@keyframes shimmer': {
                      '0%': { left: '-100%' },
                      '100%': { left: '100%' }
                    }
                  }}
                />
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* Enhanced Quick Actions Skeleton */}
        <Box sx={{ ...glassMorphismStyles, p: theme.spacing(3), mb: theme.spacing(3) }}>
          <Skeleton
            variant="text"
            width={isMobile ? 120 : 150}
            height={24}
            sx={{
              mb: theme.spacing(2),
              background: alpha(ACE_COLORS.PURPLE, 0.1)
            }}
          />
          <Grid container spacing={isMobile ? 1 : 2}>
            {[1, 2, 3, 4].map((i) => (
              <Grid item xs={12} sm={6} key={i}>
                <Box sx={{
                  border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                  borderRadius: 1,
                  p: theme.spacing(2),
                  background: alpha(theme.palette.background.paper, 0.5),
                  transition: 'all 0.3s ease'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: theme.spacing(1) }}>
                    <Skeleton
                      variant="circular"
                      width={24}
                      height={24}
                      sx={{ background: alpha(ACE_COLORS.PURPLE, 0.1) }}
                    />
                    <Skeleton
                      variant="text"
                      width={isMobile ? 100 : 120}
                      height={20}
                      sx={{
                        ml: theme.spacing(1),
                        background: alpha(theme.palette.action.disabled, 0.1)
                      }}
                    />
                  </Box>
                  <Skeleton
                    variant="text"
                    width="100%"
                    height={16}
                    sx={{ background: alpha(theme.palette.action.disabled, 0.1) }}
                  />
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Container>
    );

    return LoadingContent;
  }, [
    maxWidth,
    theme,
    showLoadingProgress,
    planLimits,
    loadingProgress,
    showPerformanceMetrics,
    isOnline,
    memoryStats,
    glassMorphismStyles,
    isMobile
  ]);

  // Enhanced page wrapper with transitions and error handling
  const renderPageContent = () => {
    // Handle different page states
    if (pageState === PAGE_STATES.LOADING || loading) {
      return loadingComponent || <DefaultLoadingComponent />;
    }

    if (pageState === PAGE_STATES.ERROR && errorDetails) {
      return (
        <Container maxWidth={maxWidth} sx={{ py: theme.spacing(3) }}>
          <Card sx={{ ...glassMorphismStyles, textAlign: 'center', p: theme.spacing(4) }}>
            <ErrorIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Something went wrong
            </Typography>
            <Typography variant="body1" color="textSecondary" paragraph>
              {fallbackMessage}
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 3 }}>
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={handlePageRefresh}
                sx={{
                  background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE}, ${ACE_COLORS.YELLOW})`,
                  color: 'white'
                }}
              >
                Retry
              </Button>
              <Button
                variant="outlined"
                startIcon={<HomeIcon />}
                onClick={() => window.location.href = '/'}
              >
                Go Home
              </Button>
            </Box>
          </Card>
        </Container>
      );
    }

    if (pageState === PAGE_STATES.OFFLINE && !isOnline) {
      return (
        <Container maxWidth={maxWidth} sx={{ py: theme.spacing(3) }}>
          <Card sx={{ ...glassMorphismStyles, textAlign: 'center', p: theme.spacing(4) }}>
            <OfflineIcon sx={{ fontSize: 64, color: 'warning.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              You&apos;re offline
            </Typography>
            <Typography variant="body1" color="textSecondary" paragraph>
              Please check your internet connection and try again.
            </Typography>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={handlePageRefresh}
              sx={{
                background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE}, ${ACE_COLORS.YELLOW})`,
                color: 'white'
              }}
            >
              Retry
            </Button>
          </Card>
        </Container>
      );
    }

    // Render main content with transitions
    const content = (
      <Box
        ref={pageRef}
        sx={{
          minHeight: '100vh',
          position: 'relative',
          background: isFullscreen ? theme.palette.background.default : 'transparent'
        }}
      >
        {/* Enhanced Breadcrumbs */}
        {showBreadcrumbs && breadcrumbs.length > 0 && (
          <Container maxWidth={maxWidth} sx={{ pt: 2 }}>
            <Breadcrumbs
              aria-label="breadcrumb"
              sx={{
                '& .MuiBreadcrumbs-separator': {
                  color: ACE_COLORS.PURPLE
                }
              }}
            >
              {breadcrumbs.map((crumb, index) => (
                <Link
                  key={index}
                  color={index === breadcrumbs.length - 1 ? 'text.primary' : 'inherit'}
                  href={crumb.href}
                  underline="hover"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    '&:hover': {
                      color: ACE_COLORS.PURPLE
                    }
                  }}
                >
                  {crumb.icon}
                  {crumb.label}
                </Link>
              ))}
            </Breadcrumbs>
          </Container>
        )}

        {/* Page Actions */}
        {showPageActions && (
          <Box
            sx={{
              position: 'fixed',
              top: theme.spacing(2),
              right: theme.spacing(2),
              zIndex: 1000,
              display: 'flex',
              gap: 1
            }}
          >
            <FeatureGate requiredPlan={2}>
              <Tooltip title="Toggle fullscreen">
                <IconButton
                  onClick={handleToggleFullscreen}
                  sx={{
                    background: alpha(theme.palette.background.paper, 0.9),
                    backdropFilter: 'blur(10px)',
                    '&:hover': {
                      background: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                >
                  {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </FeatureGate>

            <FeatureGate requiredPlan={2}>
              <Tooltip title="Performance metrics">
                <IconButton
                  onClick={() => setShowErrorDialog(true)}
                  sx={{
                    background: alpha(theme.palette.background.paper, 0.9),
                    backdropFilter: 'blur(10px)',
                    '&:hover': {
                      background: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                >
                  <PerformanceIcon />
                </IconButton>
              </Tooltip>
            </FeatureGate>
          </Box>
        )}

        {/* Main Content */}
        {children}
      </Box>
    );

    // Apply transitions if enabled
    if (enableTransitions && planLimits.pageTransitions) {
      switch (transitionType) {
        case TRANSITION_TYPES.FADE:
          return (
            <Fade in timeout={transitionDuration}>
              {content}
            </Fade>
          );
        case TRANSITION_TYPES.SLIDE:
          return (
            <Slide direction="up" in timeout={transitionDuration}>
              {content}
            </Slide>
          );
        case TRANSITION_TYPES.ZOOM:
          return (
            <Zoom in timeout={transitionDuration}>
              {content}
            </Zoom>
          );
        default:
          return content;
      }
    }

    return content;
  };

  return (
    <>
      {/* Global Styles for animations */}
      <GlobalStyles
        styles={{
          '@keyframes shimmer': {
            '0%': { left: '-100%' },
            '100%': { left: '100%' }
          }
        }}
      />

      {/* Enhanced Error Boundary */}
      <ErrorBoundary
        fallbackMessage={fallbackMessage}
        onError={(error) => {
          setPageState(PAGE_STATES.ERROR);
          setErrorDetails(error);
          if (onPageError) {
            onPageError(error);
          }
        }}
      >
        <Suspense fallback={<DefaultLoadingComponent />}>
          {renderPageContent()}
        </Suspense>
      </ErrorBoundary>

      {/* Performance Metrics Dialog */}
      <FeatureGate requiredPlan={2}>
        <Dialog
          open={showErrorDialog}
          onClose={() => setShowErrorDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AnalyticsIcon />
              Page Performance Metrics
            </Box>
          </DialogTitle>
          <DialogContent>
            {planLimits.performanceMonitoring && (
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">Load Time</Typography>
                  <Typography variant="h6" color="primary">
                    {performanceRef.current.loadTime || 0}ms
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">Memory Usage</Typography>
                  <Typography variant="h6" color="warning.main">
                    {memoryStats.usedJSHeapSize ?
                      Math.round(memoryStats.usedJSHeapSize / 1024 / 1024) : 0}MB
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">Network Status</Typography>
                  <Typography variant="h6" color={isOnline ? 'success.main' : 'error.main'}>
                    {isOnline ? 'Online' : 'Offline'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2">Page State</Typography>
                  <Typography variant="h6" color="info.main">
                    {pageState}
                  </Typography>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowErrorDialog(false)}>Close</Button>
            <Button onClick={exportPageData} variant="contained">
              Export Data
            </Button>
          </DialogActions>
        </Dialog>
      </FeatureGate>
    </>
  );
}));

StablePageWrapper.displayName = 'StablePageWrapper';

StablePageWrapper.propTypes = {
  /** Page content to render */
  children: PropTypes.node,
  /** Loading state */
  loading: PropTypes.bool,
  /** Error object */
  error: PropTypes.object,
  /** Fallback error message */
  fallbackMessage: PropTypes.string,
  /** Custom loading component */
  loadingComponent: PropTypes.node,
  /** Maximum container width */
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]),
  /** Page title */
  pageTitle: PropTypes.string,
  /** Breadcrumb navigation array */
  breadcrumbs: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    href: PropTypes.string,
    icon: PropTypes.node
  })),
  /** Page load callback */
  onPageLoad: PropTypes.func,
  /** Page error callback */
  onPageError: PropTypes.func,
  /** Page refresh callback */
  onPageRefresh: PropTypes.func,
  /** Enable page transitions */
  enableTransitions: PropTypes.bool,
  /** Transition type */
  transitionType: PropTypes.oneOf(['fade', 'slide', 'zoom', 'none']),
  /** Transition duration in milliseconds */
  transitionDuration: PropTypes.number,
  /** Enable performance monitoring */
  enablePerformanceMonitoring: PropTypes.bool,
  /** Enable offline support */
  enableOfflineSupport: PropTypes.bool,
  /** Enable state history tracking */
  enableStateHistory: PropTypes.bool,
  /** Show loading progress indicator */
  showLoadingProgress: PropTypes.bool,
  /** Show performance metrics */
  showPerformanceMetrics: PropTypes.bool,
  /** Custom error boundary component */
  customErrorBoundary: PropTypes.node,
  /** Unique page identifier */
  pageId: PropTypes.string,
  /** Enable analytics tracking */
  analyticsEnabled: PropTypes.bool,
  /** Enable memory optimization */
  memoryOptimization: PropTypes.bool,
  /** Enable fullscreen mode */
  fullscreenMode: PropTypes.bool,
  /** Show breadcrumbs navigation */
  showBreadcrumbs: PropTypes.bool,
  /** Show page action buttons */
  showPageActions: PropTypes.bool
};

export default StablePageWrapper;

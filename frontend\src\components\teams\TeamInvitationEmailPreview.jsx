/**
 * Enhanced Team Invitation Email Preview - Enterprise-grade email template preview component
 * Features: Comprehensive email preview system with real-time template rendering and dynamic content generation,
 * detailed email customization with template selection and personalization options, advanced preview features
 * with responsive design testing and multi-device preview, ACE Social's team management system integration with
 * seamless invitation workflow and member role management, email interaction features including send testing
 * and preview sharing, preview customization capabilities with theme options and layout variants, real-time
 * email updates with live template changes and dynamic recipient data, and seamless ACE Social platform
 * integration with advanced email template orchestration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
  useMemo,
  useRef,
  useEffect
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Paper,
  Divider,
  Button,
  useTheme,
  alpha,
  Card,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Avatar,
  LinearProgress,
  Fade,
  Collapse,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Preview as PreviewIcon,
  Send as SendIcon,
  Share as ShareIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Smartphone as MobileIcon,
  Tablet as TabletIcon,
  Computer as DesktopIcon,
  Visibility as VisibilityIcon,
  Star as StarIcon,
  Group as TeamIcon,
  Person as PersonIcon,
  Analytics as AnalyticsIcon,
  Palette as PaletteIcon,
  Code as CodeIcon,
  Language as LanguageIcon
} from '@mui/icons-material';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Email template types
const EMAIL_TEMPLATES = {
  PROFESSIONAL: 'professional',
  MODERN: 'modern',
  MINIMAL: 'minimal',
  BRANDED: 'branded'
};

// Preview modes
const PREVIEW_MODES = {
  DESKTOP: 'desktop',
  TABLET: 'tablet',
  MOBILE: 'mobile'
};

// Email clients
const EMAIL_CLIENTS = {
  GMAIL: 'gmail',
  OUTLOOK: 'outlook',
  APPLE_MAIL: 'apple_mail',
  YAHOO: 'yahoo'
};

/**
 * Enhanced Team Invitation Email Preview - Comprehensive email template preview with advanced features
 * Implements detailed email customization and enterprise-grade preview capabilities
 */
const TeamInvitationEmailPreview = memo(forwardRef(({
  teamName,
  inviterName,
  inviterEmail,
  message,
  onSendTest,
  onSharePreview,
  onTemplateChange,
  onAnalyticsTrack,
  enableAnalytics = true,
  templateType = EMAIL_TEMPLATES.PROFESSIONAL,
  customBranding = {}
}, ref) => {
  const theme = useTheme();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const previewRef = useRef(null);
  const emailContentRef = useRef(null);

  // Enhanced state management
  const [currentTemplate, setCurrentTemplate] = useState(templateType);
  const [previewMode, setPreviewMode] = useState(PREVIEW_MODES.DESKTOP);
  const [emailClient, setEmailClient] = useState(EMAIL_CLIENTS.GMAIL);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [previewAnalytics, setPreviewAnalytics] = useState({
    views: 0,
    lastUpdated: new Date().toISOString()
  });
  // Removed unused menuAnchorEl state
  const [activeTab, setActiveTab] = useState(0);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshPreview: () => handleRefreshPreview(),
    exportTemplate: () => handleExportTemplate(),
    sendTestEmail: () => handleSendTestEmail(),
    sharePreview: () => handleSharePreview(),
    getPreviewData: () => getPreviewData(),
    setTemplate: (template) => setCurrentTemplate(template),
    setPreviewMode: (mode) => setPreviewMode(mode),
    getAnalytics: () => previewAnalytics
  }), [
    previewAnalytics,
    handleRefreshPreview,
    handleExportTemplate,
    handleSendTestEmail,
    handleSharePreview,
    getPreviewData
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced email template styles
  const getTemplateStyles = useCallback((template) => {
    const baseStyles = {
      fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
      lineHeight: 1.6,
      color: ACE_COLORS.DARK
    };

    switch (template) {
      case EMAIL_TEMPLATES.PROFESSIONAL:
        return {
          ...baseStyles,
          backgroundColor: ACE_COLORS.WHITE,
          border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`
        };
      case EMAIL_TEMPLATES.MODERN:
        return {
          ...baseStyles,
          background: `linear-gradient(135deg, ${ACE_COLORS.WHITE} 0%, ${alpha(ACE_COLORS.PURPLE, 0.05)} 100%)`,
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
        };
      case EMAIL_TEMPLATES.MINIMAL:
        return {
          ...baseStyles,
          backgroundColor: ACE_COLORS.WHITE,
          border: `1px solid ${alpha(ACE_COLORS.DARK, 0.1)}`
        };
      case EMAIL_TEMPLATES.BRANDED:
        return {
          ...baseStyles,
          background: `linear-gradient(135deg, ${ACE_COLORS.WHITE} 0%, ${alpha(ACE_COLORS.YELLOW, 0.1)} 100%)`,
          border: `2px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
        };
      default:
        return baseStyles;
    }
  }, []);

  // Enhanced event handlers
  const handleRefreshPreview = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPreviewAnalytics(prev => ({
        ...prev,
        views: prev.views + 1,
        lastUpdated: new Date().toISOString()
      }));
      announceToScreenReader('Email preview refreshed');
    } catch {
      announceToScreenReader('Failed to refresh preview');
    } finally {
      setIsLoading(false);
    }
  }, [announceToScreenReader]);

  const handleExportTemplate = useCallback(() => {
    try {
      const templateData = {
        template: currentTemplate,
        content: {
          teamName,
          inviterName,
          inviterEmail,
          message
        },
        branding: customBranding,
        timestamp: new Date().toISOString()
      };

      const blob = new Blob([JSON.stringify(templateData, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `team-invitation-template-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      announceToScreenReader('Template exported successfully');
    } catch {
      announceToScreenReader('Failed to export template');
    }
  }, [currentTemplate, teamName, inviterName, inviterEmail, message, customBranding, announceToScreenReader]);

  const handleSendTestEmail = useCallback(async () => {
    if (onSendTest) {
      try {
        setIsLoading(true);
        await onSendTest({
          template: currentTemplate,
          previewMode,
          emailClient,
          content: { teamName, inviterName, inviterEmail, message }
        });
        announceToScreenReader('Test email sent successfully');
      } catch {
        announceToScreenReader('Failed to send test email');
      } finally {
        setIsLoading(false);
      }
    }
  }, [onSendTest, currentTemplate, previewMode, emailClient, teamName, inviterName, inviterEmail, message, announceToScreenReader]);

  const handleSharePreview = useCallback(() => {
    if (onSharePreview) {
      onSharePreview({
        template: currentTemplate,
        previewMode,
        analytics: previewAnalytics
      });
      announceToScreenReader('Preview shared successfully');
    }
  }, [onSharePreview, currentTemplate, previewMode, previewAnalytics, announceToScreenReader]);

  const getPreviewData = useCallback(() => ({
    template: currentTemplate,
    previewMode,
    emailClient,
    analytics: previewAnalytics,
    content: { teamName, inviterName, inviterEmail, message }
  }), [currentTemplate, previewMode, emailClient, previewAnalytics, teamName, inviterName, inviterEmail, message]);

  // Enhanced useEffect hooks
  useEffect(() => {
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack({
        action: 'preview_viewed',
        template: currentTemplate,
        previewMode,
        timestamp: new Date().toISOString()
      });
    }
  }, [enableAnalytics, onAnalyticsTrack, currentTemplate, previewMode]);

  useEffect(() => {
    if (onTemplateChange) {
      onTemplateChange(currentTemplate);
    }
  }, [currentTemplate, onTemplateChange]);

  // Preview dimensions based on mode
  const getPreviewDimensions = useMemo(() => {
    switch (previewMode) {
      case PREVIEW_MODES.MOBILE:
        return { width: '375px', maxWidth: '100%' };
      case PREVIEW_MODES.TABLET:
        return { width: '768px', maxWidth: '100%' };
      case PREVIEW_MODES.DESKTOP:
      default:
        return { width: '100%', maxWidth: '800px' };
    }
  }, [previewMode]);

  return (
    <Box sx={{ ...glassMorphismStyles, p: 3 }}>
      {/* Enhanced Header with Controls */}
      <Box sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" fontWeight="bold" sx={{
              color: ACE_COLORS.DARK,
              background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.YELLOW} 100%)`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              Email Preview
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Team invitation email template preview
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Refresh Preview">
              <IconButton
                onClick={handleRefreshPreview}
                disabled={isLoading}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Send Test Email">
              <IconButton
                onClick={handleSendTestEmail}
                disabled={isLoading}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <SendIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Share Preview">
              <IconButton
                onClick={handleSharePreview}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <ShareIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Export Template">
              <IconButton
                onClick={handleExportTemplate}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Enhanced Controls */}
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 600,
              color: ACE_COLORS.DARK,
              '&.Mui-selected': {
                color: ACE_COLORS.PURPLE
              }
            },
            '& .MuiTabs-indicator': {
              backgroundColor: ACE_COLORS.PURPLE,
              height: 3
            }
          }}
        >
          <Tab label="Preview" icon={<PreviewIcon />} iconPosition="start" />
          <Tab label="Settings" icon={<SettingsIcon />} iconPosition="start" />
          <Tab label="Analytics" icon={<AnalyticsIcon />} iconPosition="start" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      {activeTab === 0 && (
        <Fade in timeout={500}>
          <Box>
            {/* Preview Mode Controls */}
            <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Template</InputLabel>
                <Select
                  value={currentTemplate}
                  label="Template"
                  onChange={(e) => setCurrentTemplate(e.target.value)}
                >
                  <MenuItem value={EMAIL_TEMPLATES.PROFESSIONAL}>Professional</MenuItem>
                  <MenuItem value={EMAIL_TEMPLATES.MODERN}>Modern</MenuItem>
                  <MenuItem value={EMAIL_TEMPLATES.MINIMAL}>Minimal</MenuItem>
                  <MenuItem value={EMAIL_TEMPLATES.BRANDED}>Branded</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Device</InputLabel>
                <Select
                  value={previewMode}
                  label="Device"
                  onChange={(e) => setPreviewMode(e.target.value)}
                >
                  <MenuItem value={PREVIEW_MODES.DESKTOP}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <DesktopIcon fontSize="small" />
                      Desktop
                    </Box>
                  </MenuItem>
                  <MenuItem value={PREVIEW_MODES.TABLET}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <TabletIcon fontSize="small" />
                      Tablet
                    </Box>
                  </MenuItem>
                  <MenuItem value={PREVIEW_MODES.MOBILE}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <MobileIcon fontSize="small" />
                      Mobile
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Email Client</InputLabel>
                <Select
                  value={emailClient}
                  label="Email Client"
                  onChange={(e) => setEmailClient(e.target.value)}
                >
                  <MenuItem value={EMAIL_CLIENTS.GMAIL}>Gmail</MenuItem>
                  <MenuItem value={EMAIL_CLIENTS.OUTLOOK}>Outlook</MenuItem>
                  <MenuItem value={EMAIL_CLIENTS.APPLE_MAIL}>Apple Mail</MenuItem>
                  <MenuItem value={EMAIL_CLIENTS.YAHOO}>Yahoo</MenuItem>
                </Select>
              </FormControl>

              <Chip
                label={`${previewAnalytics.views} views`}
                icon={<VisibilityIcon />}
                sx={{
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  color: ACE_COLORS.PURPLE
                }}
              />
            </Box>

            {/* Loading Progress */}
            {isLoading && (
              <Box sx={{ mb: 2 }}>
                <LinearProgress
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: ACE_COLORS.PURPLE
                    }
                  }}
                />
              </Box>
            )}

            {/* Enhanced Email Preview */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                mb: 3
              }}
            >
              <Paper
                ref={previewRef}
                elevation={8}
                sx={{
                  ...getPreviewDimensions,
                  ...getTemplateStyles(currentTemplate),
                  p: 4,
                  borderRadius: 3,
                  transition: 'all 300ms ease',
                  transform: isLoading ? 'scale(0.98)' : 'scale(1)',
                  opacity: isLoading ? 0.7 : 1
                }}
              >
                <Box ref={emailContentRef}>
                  {/* Email Header */}
                  <Box sx={{ mb: 3, textAlign: 'center' }}>
                    <Avatar
                      sx={{
                        width: 80,
                        height: 80,
                        mx: 'auto',
                        mb: 2,
                        backgroundColor: ACE_COLORS.PURPLE,
                        fontSize: '2rem'
                      }}
                    >
                      <TeamIcon sx={{ fontSize: '2rem', color: ACE_COLORS.WHITE }} />
                    </Avatar>
                    <Typography variant="h4" fontWeight="bold" sx={{
                      color: ACE_COLORS.DARK,
                      mb: 1
                    }}>
                      ACE Social
                    </Typography>
                    <Typography variant="subtitle1" color="text.secondary">
                      Team Collaboration Platform
                    </Typography>
                  </Box>

                  {/* Subject Line */}
                  <Box sx={{ mb: 3, p: 2, backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05), borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      <strong>Subject:</strong>
                    </Typography>
                    <Typography variant="h6" sx={{ color: ACE_COLORS.DARK }}>
                      🎉 You&apos;re invited to join {teamName} on ACE Social
                    </Typography>
                  </Box>

                  <Divider sx={{ mb: 4, borderColor: alpha(ACE_COLORS.PURPLE, 0.2) }} />

                  {/* Email Content */}
                  <Box sx={{ mb: 4 }}>
                    <Typography variant="h5" sx={{ color: ACE_COLORS.DARK, mb: 3 }}>
                      Hello there! 👋
                    </Typography>

                    <Typography variant="body1" paragraph sx={{ fontSize: '1.1rem', lineHeight: 1.7 }}>
                      <strong>{inviterName}</strong> ({inviterEmail}) has invited you to join the{' '}
                      <strong style={{ color: ACE_COLORS.PURPLE }}>{teamName}</strong> team on ACE Social.
                    </Typography>

                    {message && (
                      <Card
                        sx={{
                          my: 3,
                          p: 3,
                          backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                          border: `2px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
                          borderRadius: 3
                        }}
                      >
                        <Box display="flex" alignItems="flex-start" gap={2}>
                          <PersonIcon sx={{ color: ACE_COLORS.YELLOW, mt: 0.5 }} />
                          <Box>
                            <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
                              Personal message from {inviterName}:
                            </Typography>
                            <Typography variant="body1" sx={{ fontStyle: 'italic', color: ACE_COLORS.DARK }}>
                              &quot;{message}&quot;
                            </Typography>
                          </Box>
                        </Box>
                      </Card>
                    )}

                    <Typography variant="body1" paragraph sx={{ fontSize: '1.1rem', lineHeight: 1.7 }}>
                      ACE Social empowers teams to collaborate seamlessly on content creation, campaign management,
                      and social media analytics. By joining this team, you&apos;ll unlock:
                    </Typography>

                    <Box sx={{ my: 3 }}>
                      {[
                        { icon: <EditIcon />, text: 'Collaborative content creation and scheduling' },
                        { icon: <AnalyticsIcon />, text: 'Advanced analytics and performance insights' },
                        { icon: <TeamIcon />, text: 'Streamlined team workflow management' },
                        { icon: <StarIcon />, text: 'Premium features and priority support' }
                      ].map((feature, index) => (
                        <Box key={index} display="flex" alignItems="center" gap={2} sx={{ mb: 2 }}>
                          <Box
                            sx={{
                              p: 1,
                              borderRadius: '50%',
                              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                              color: ACE_COLORS.PURPLE
                            }}
                          >
                            {feature.icon}
                          </Box>
                          <Typography variant="body1" sx={{ fontSize: '1.1rem' }}>
                            {feature.text}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Box>

                  {/* Call to Action */}
                  <Box sx={{ textAlign: 'center', my: 5 }}>
                    <Button
                      variant="contained"
                      size="large"
                      sx={{
                        px: 6,
                        py: 2,
                        fontSize: '1.2rem',
                        fontWeight: 'bold',
                        backgroundColor: ACE_COLORS.PURPLE,
                        borderRadius: 3,
                        boxShadow: `0 8px 25px ${alpha(ACE_COLORS.PURPLE, 0.3)}`,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                          transform: 'translateY(-2px)',
                          boxShadow: `0 12px 35px ${alpha(ACE_COLORS.PURPLE, 0.4)}`
                        },
                        transition: 'all 300ms ease'
                      }}
                    >
                      Accept Invitation
                    </Button>

                    <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                      This invitation will expire in 7 days
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 4, borderColor: alpha(ACE_COLORS.PURPLE, 0.2) }} />

                  {/* Footer Information */}
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Don&apos;t have an account? No worries! You&apos;ll be able to create one when you accept the invitation.
                    </Typography>

                    <Typography variant="body2" color="text.secondary" paragraph>
                      If you didn&apos;t expect this invitation, you can safely ignore this email.
                    </Typography>

                    <Box
                      sx={{
                        mt: 4,
                        pt: 3,
                        borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                        textAlign: 'center'
                      }}
                    >
                      <Typography variant="caption" color="text.secondary">
                        ACE Social - Empowering teams to create, collaborate, and succeed
                      </Typography>
                      <br />
                      <Typography variant="caption" color="text.secondary">
                        © 2024 ACE Social Platform. All rights reserved.
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Box>
        </Fade>
      )}

      {/* Settings Tab */}
      {activeTab === 1 && (
        <Fade in timeout={500}>
          <Card sx={{ p: 3, ...glassMorphismStyles }}>
            <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
              Email Template Settings
            </Typography>

            <Stack spacing={3}>
              <FormControlLabel
                control={
                  <Switch
                    checked={showAdvancedOptions}
                    onChange={(e) => setShowAdvancedOptions(e.target.checked)}
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: ACE_COLORS.PURPLE
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: ACE_COLORS.PURPLE
                      }
                    }}
                  />
                }
                label="Show Advanced Options"
              />

              <Collapse in={showAdvancedOptions}>
                <Stack spacing={2}>
                  <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK }}>
                    Template Customization
                  </Typography>

                  <Box display="flex" gap={2} flexWrap="wrap">
                    <Chip
                      label="Custom Branding"
                      icon={<PaletteIcon />}
                      onClick={() => {/* Handle custom branding */}}
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                        color: ACE_COLORS.DARK,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.YELLOW, 0.2)
                        }
                      }}
                    />

                    <Chip
                      label="HTML Export"
                      icon={<CodeIcon />}
                      onClick={() => {/* Handle HTML export */}}
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        color: ACE_COLORS.PURPLE,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                        }
                      }}
                    />

                    <Chip
                      label="Multi-language"
                      icon={<LanguageIcon />}
                      onClick={() => {/* Handle multi-language */}}
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        color: ACE_COLORS.PURPLE,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                        }
                      }}
                    />
                  </Box>
                </Stack>
              </Collapse>
            </Stack>
          </Card>
        </Fade>
      )}

      {/* Analytics Tab */}
      {activeTab === 2 && (
        <Fade in timeout={500}>
          <Card sx={{ p: 3, ...glassMorphismStyles }}>
            <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
              Preview Analytics
            </Typography>

            <Stack spacing={3}>
              <Box display="flex" gap={3} flexWrap="wrap">
                <Box>
                  <Typography variant="h4" sx={{ color: ACE_COLORS.PURPLE }}>
                    {previewAnalytics.views}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Views
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="h4" sx={{ color: ACE_COLORS.YELLOW }}>
                    {currentTemplate}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Template
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="h4" sx={{ color: ACE_COLORS.PURPLE }}>
                    {previewMode}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Preview Mode
                  </Typography>
                </Box>
              </Box>

              <Typography variant="body2" color="text.secondary">
                Last updated: {new Date(previewAnalytics.lastUpdated).toLocaleString()}
              </Typography>
            </Stack>
          </Card>
        </Fade>
      )}

      {/* Notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={index}
          open={true}
          autoHideDuration={6000}
          onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            severity={notification.severity || 'info'}
            onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
            sx={{
              ...glassMorphismStyles,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
}));

TeamInvitationEmailPreview.displayName = 'TeamInvitationEmailPreview';

TeamInvitationEmailPreview.propTypes = {
  /** Name of the team */
  teamName: PropTypes.string.isRequired,
  /** Name of the person sending the invitation */
  inviterName: PropTypes.string.isRequired,
  /** Email of the person sending the invitation */
  inviterEmail: PropTypes.string.isRequired,
  /** Optional personal message */
  message: PropTypes.string,
  /** Function called when sending test email */
  onSendTest: PropTypes.func,
  /** Function called when sharing preview */
  onSharePreview: PropTypes.func,
  /** Function called when template changes */
  onTemplateChange: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,
  /** Enable multi-device preview */
  enableMultiDevicePreview: PropTypes.bool,
  /** Enable template customization */
  enableTemplateCustomization: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Email template type */
  templateType: PropTypes.oneOf(Object.values(EMAIL_TEMPLATES)),
  /** Recipient data object */
  recipientData: PropTypes.object,
  /** Custom branding configuration */
  customBranding: PropTypes.object
};

export default TeamInvitationEmailPreview;

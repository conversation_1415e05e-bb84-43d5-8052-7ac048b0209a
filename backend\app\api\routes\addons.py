"""
API routes for add-on management and feature access control.
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel

from app.db.database import get_database
from app.middleware.auth import get_current_user
from app.models.user import User, PyObjectId
from app.services.addon_catalog import (
    get_addon_catalog,
    get_addon_by_id,
    validate_addon_purchase
)
from app.services.addon_catalog import (
    get_addon_catalog,
    get_addon_by_id,
    validate_addon_purchase
)
from app.services.addon_marketing import (
    get_addon_recommendations,
    check_addon_promotions,
    marketing_engine
)
from app.services.feature_access import check_feature_access
# BillingService class was removed - using individual functions instead
# from app.services.billing import BillingService
# from app.core.monitoring import record_api_metrics

router = APIRouter(prefix="/addons", tags=["addons"])


# Request/Response Models
class TrackUsageRequest(BaseModel):
    usage_type: str
    amount: int = 1
    metadata: Optional[Dict[str, Any]] = None


class CheckAccessRequest(BaseModel):
    feature: str
    usage_type: str


class PurchaseAddonRequest(BaseModel):
    addon_id: str
    variant: str = "basic"


class PromotionCheckRequest(BaseModel):
    triggers: List[str] = []
    context: Dict[str, Any] = {}


# Catalog Endpoints
@router.get("/catalog")
async def get_addon_catalog_endpoint(
    category: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get available add-ons for the current user."""
    try:
        addons = await get_addon_catalog(current_user, category)
        # record_api_metrics("addon_catalog_viewed", current_user.subscription.plan_id if current_user.subscription else "free")
        return addons
    except Exception as e:
        # record_api_metrics("addon_catalog_error", "unknown")
        raise HTTPException(status_code=500, detail="Failed to fetch add-on catalog")


@router.get("/catalog/{addon_id}")
async def get_addon_details(
    addon_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get details for a specific add-on."""
    addon = await get_addon_by_id(addon_id)
    if not addon:
        raise HTTPException(status_code=404, detail="Add-on not found")
    
    return addon


# User Add-ons
@router.get("/user")
async def get_user_addons(
    current_user: User = Depends(get_current_user)
):
    """Get current user's purchased add-ons and their status."""
    try:
        # Use the user's purchased_addons directly from the model
        addon_status = []
        for addon in current_user.get_active_addons():
            status = {
                "addon_id": addon.id,
                "name": addon.name,
                "status": "active" if addon.is_valid() else "inactive",
                "credits_remaining": addon.remaining_uses,
                "usage_percentage": 0 if not addon.remaining_uses else
                    ((addon.quantity - addon.remaining_uses) / addon.quantity * 100),
                "expires_at": addon.expires_at.isoformat() if addon.expires_at else None
            }
            addon_status.append(status)
        return addon_status
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to fetch user add-ons")


# Feature Access Control
@router.post("/check-access")
async def check_feature_access_endpoint(
    request: CheckAccessRequest,
    current_user: User = Depends(get_current_user)
):
    """Check if user has access to a feature including add-on enhancements."""
    try:
        # Use the basic feature access check for now
        has_access = current_user.has_feature(request.feature)
        access_info = {
            "has_access": has_access,
            "feature": request.feature,
            "usage_type": request.usage_type,
            "user_plan": current_user.subscription.plan_id if current_user.subscription else "free"
        }
        return access_info
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to check feature access")


@router.get("/enhanced-limits")
async def get_enhanced_limits(
    usage_type: str,
    current_user: User = Depends(get_current_user)
):
    """Get enhanced limits including add-on bonuses."""
    try:
        # Get base limits from subscription using the existing method
        base_limit = current_user.get_feature_limit(usage_type) or 0

        # Calculate add-on bonuses from user's purchased add-ons
        addon_bonus = current_user.get_addon_limit_increase(usage_type)

        return {
            "base_limit": base_limit - addon_bonus,  # Base without add-ons
            "addon_bonus": addon_bonus,
            "total_limit": base_limit  # This already includes add-ons
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get enhanced limits")


@router.get("/relevant")
async def get_relevant_addons(
    usage_type: str,
    current_user: User = Depends(get_current_user)
):
    """Get add-ons relevant to a specific usage type."""
    try:
        all_addons = await get_addon_catalog(current_user)
        relevant_addons = []
        
        for addon in all_addons:
            usage_increase = addon.get("usage_increase", {})
            
            # Map usage types to addon capabilities
            if (usage_type == "regeneration_credits" and "regeneration_credits" in usage_increase or
                usage_type == "image_generation" and "image_generation_credits" in usage_increase or
                usage_type == "sentiment_analysis" and "sentiment_comments" in usage_increase or
                usage_type == "auto_replies" and "auto_replies" in usage_increase):
                relevant_addons.append(addon)
        
        # Sort by popularity and price
        relevant_addons.sort(key=lambda x: (not x.get("is_popular", False), 
                                          x.get("pricing", {}).get("basic", {}).get("price", 999)))
        
        return relevant_addons
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get relevant add-ons")


# Usage Tracking
@router.post("/track-usage")
async def track_usage(
    request: TrackUsageRequest,
    current_user: User = Depends(get_current_user)
):
    """Track add-on usage and update credits."""
    try:
        # Simplified implementation using user model methods
        success = current_user.consume_addon(request.usage_type, request.amount)

        if success:
            result = {
                "success": True,
                "message": "Usage tracked successfully",
                "amount": request.amount,
                "usage_type": request.usage_type
            }
        else:
            result = {
                "success": False,
                "error": "Insufficient credits",
                "available": 0,
                "requested": request.amount
            }

            # Mock upsell notification (would normally call a service)
            # await send_usage_upsell(...)

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to track usage")


@router.post("/track-attempt")
async def track_feature_attempt(
    feature: str,
    usage_type: str,
    result: str,
    current_user: User = Depends(get_current_user)
):
    """Track feature access attempts for analytics."""
    try:
        # Log the attempt for analytics (would normally call monitoring service)
        # record_api_metrics(f"feature_attempt_{result}", current_user.subscription.plan_id if current_user.subscription else "free")
        return {"success": True}
    except Exception as e:
        return {"success": False, "error": str(e)}


# Analytics
@router.get("/usage/analytics")
async def get_usage_analytics(
    addon_id: Optional[str] = None,
    days: int = 30,
    current_user: User = Depends(get_current_user)
):
    """Get usage analytics for user's add-ons."""
    try:
        # Simplified analytics based on user's add-ons
        analytics = {
            "total_addons": len(current_user.purchased_addons),
            "active_addons": len(current_user.get_active_addons()),
            "usage_summary": {}
        }

        for addon in current_user.get_active_addons():
            if addon_id and addon.id != addon_id:
                continue

            analytics["usage_summary"][addon.id] = {
                "name": addon.name,
                "total_credits": addon.quantity,
                "used_credits": (addon.quantity - (addon.remaining_uses or 0)),
                "remaining_credits": addon.remaining_uses or 0,
                "usage_percentage": 0 if not addon.remaining_uses else
                    ((addon.quantity - addon.remaining_uses) / addon.quantity * 100)
            }

        return analytics
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get usage analytics")


@router.get("/usage-alerts")
async def get_usage_alerts(
    current_user: User = Depends(get_current_user)
):
    """Get usage alerts for the user."""
    try:
        alerts = []

        for addon in current_user.get_active_addons():
            if not addon.remaining_uses:
                continue

            usage_percentage = ((addon.quantity - addon.remaining_uses) / addon.quantity * 100)

            if usage_percentage >= 75:
                alert_type = "critical" if usage_percentage >= 95 else "warning"
                alerts.append({
                    "addon_id": addon.id,
                    "addon_name": addon.name,
                    "usage_percentage": round(usage_percentage, 1),
                    "credits_remaining": addon.remaining_uses,
                    "alert_type": alert_type,
                    "message": f"You've used {usage_percentage:.1f}% of your {addon.name} credits"
                })

        return alerts
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get usage alerts")


# Purchasing
@router.post("/purchase")
async def purchase_addon(
    request: PurchaseAddonRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Purchase an add-on."""
    try:
        # Validate purchase
        validation = await validate_addon_purchase(
            current_user,
            request.addon_id,
            request.variant
        )

        if not validation["valid"]:
            raise HTTPException(status_code=400, detail=validation["error"])

        # Mock purchase result (would normally call billing service)
        purchase_result = {
            "success": True,
            "addon_id": request.addon_id,
            "variant": request.variant,
            "message": "Add-on purchased successfully",
            "order_id": f"order_{request.addon_id}_{current_user.id}"
        }

        if purchase_result["success"]:
            # Send confirmation email in background
            async def send_confirmation():
                await send_addon_purchase_confirmation(
                    str(current_user.id),
                    request.addon_id,
                    request.variant
                )
            background_tasks.add_task(send_confirmation)

            # record_api_metrics("addon_purchased", current_user.subscription.plan_id if current_user.subscription else "free")

        return purchase_result
    except HTTPException:
        raise
    except Exception as e:
        # record_api_metrics("addon_purchase_error", "unknown")
        raise HTTPException(status_code=500, detail="Failed to process purchase")


# Marketing and Recommendations
@router.get("/recommendations")
async def get_recommendations(
    context: str = "dashboard",
    current_user: User = Depends(get_current_user)
):
    """Get personalized add-on recommendations."""
    try:
        recommendations = await get_addon_recommendations(current_user, context)
        # record_api_metrics("addon_recommendations_viewed", current_user.subscription.plan_id if current_user.subscription else "free")
        return recommendations
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to get recommendations")


@router.post("/check-promotions")
async def check_promotions(
    request: PromotionCheckRequest,
    current_user: User = Depends(get_current_user)
):
    """Check for triggered promotions."""
    try:
        trigger_context = {
            "triggers": request.triggers,
            **request.context
        }
        
        promotions = await check_addon_promotions(current_user, trigger_context)
        return promotions
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to check promotions")


@router.get("/showcase-banner")
async def get_showcase_banner(
    context: str = "dashboard",
    current_user: User = Depends(get_current_user)
):
    """Get add-on showcase banner for the user."""
    try:
        banner = await marketing_engine.create_addon_showcase_banner(current_user, context)
        return banner
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to create showcase banner")


# Credit Management
@router.post("/process-rollover")
async def process_rollover(
    current_user: User = Depends(get_current_user)
):
    """Process credit rollover for eligible add-ons."""
    try:
        # Simplified rollover logic using user model
        rollover_results = []

        for addon in current_user.purchased_addons:
            if addon.is_expired() and addon.remaining_uses and addon.remaining_uses > 0:
                # Mock rollover (would normally have more complex logic)
                rollover_credits = int(addon.remaining_uses * 0.5)  # 50% rollover
                if rollover_credits > 0:
                    rollover_results.append({
                        "addon_id": addon.id,
                        "original_credits": addon.remaining_uses,
                        "rollover_credits": rollover_credits,
                        "rollover_percentage": 50
                    })

        result = {"success": True, "rollovers": rollover_results}

        if result["success"]:
            # record_api_metrics("addon_rollover_processed", current_user.subscription.plan_id if current_user.subscription else "free")
            pass

        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to process rollover")


# Helper functions
async def send_addon_purchase_confirmation(user_id: str, addon_id: str, variant: str):
    """Send purchase confirmation email."""
    try:
        # Mock email sending (would normally call email service)
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Sending purchase confirmation email for user {user_id}, addon {addon_id}, variant {variant}")
    except Exception as e:
        # Log error but don't fail the purchase
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send purchase confirmation email: {str(e)}")


# Admin endpoints (if needed)
@router.get("/admin/metrics")
async def get_addon_metrics(
    current_user: User = Depends(get_current_user)
):
    """Get add-on usage metrics (admin only)."""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    # Return aggregated metrics
    return {
        "total_addon_purchases": "placeholder",
        "popular_addons": "placeholder",
        "revenue_by_addon": "placeholder"
    }

/**
 * Tests for ABTestResultsDialog component
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import ABTestResultsDialog from '../ABTestResultsDialog';

// Mock recharts to avoid canvas issues in tests
vi.mock('recharts', () => ({
  BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }) => <div data-testid="responsive-container">{children}</div>
}));

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

const mockTestResults = {
  name: 'Test Campaign A/B',
  type: 'campaign',
  test_id: 'test-123',
  description: 'Testing different campaign approaches',
  start_date: '2023-01-01T00:00:00Z',
  end_date: '2023-01-15T00:00:00Z',
  is_active: false,
  statistical_significance: 0.95,
  confidence_level: 0.95,
  winner_variant_id: 'variant-2',
  recommendation: 'Variant B shows significantly better performance',
  target_metric: 'engagement_rate',
  variants: [
    {
      variant_id: 'variant-1',
      name: 'Control',
      is_control: true,
      status: 'completed',
      metrics: {
        impressions: 10000,
        engagements: 500,
        engagement_rate: 5.0,
        clicks: 200,
        click_through_rate: 2.0,
        conversions: 20,
        conversion_rate: 0.2
      }
    },
    {
      variant_id: 'variant-2',
      name: 'Variant B',
      is_control: false,
      status: 'completed',
      metrics: {
        impressions: 10000,
        engagements: 750,
        engagement_rate: 7.5,
        clicks: 300,
        click_through_rate: 3.0,
        conversions: 35,
        conversion_rate: 0.35
      }
    }
  ]
};

describe('ABTestResultsDialog', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    testResults: mockTestResults,
    onApplyWinner: vi.fn(),
    applyingWinner: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders dialog with test results', () => {
    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('A/B Test Results: Test Campaign A/B')).toBeInTheDocument();
    expect(screen.getByText('Testing different campaign approaches')).toBeInTheDocument();
    expect(screen.getByText('Variant B shows significantly better performance')).toBeInTheDocument();
  });

  test('displays metrics table by default', () => {
    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('A/B test metrics table')).toBeInTheDocument();
    expect(screen.getAllByText('Control')).toHaveLength(2); // Text and chip label
    expect(screen.getByText('Variant B')).toBeInTheDocument();
    expect(screen.getAllByText('10,000')).toHaveLength(2); // impressions for both variants
    expect(screen.getByText('7.50%')).toBeInTheDocument(); // engagement rate
  });

  test('switches to charts view when charts tab is clicked', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} />
      </TestWrapper>
    );

    const chartsTab = screen.getByRole('tab', { name: /charts/i });
    await user.click(chartsTab);

    expect(screen.getAllByTestId('bar-chart')).toHaveLength(3); // Primary metric + 2 additional charts
  });

  test('shows apply winner button when winner is determined', () => {
    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} />
      </TestWrapper>
    );

    const applyButton = screen.getByRole('button', { name: /apply winning variant/i });
    expect(applyButton).toBeInTheDocument();
    expect(applyButton).not.toBeDisabled();
  });

  test('calls onApplyWinner when apply button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} />
      </TestWrapper>
    );

    const applyButton = screen.getByRole('button', { name: /apply winning variant/i });
    await user.click(applyButton);

    expect(defaultProps.onApplyWinner).toHaveBeenCalledWith(
      'campaign',
      'test-123',
      'variant-2'
    );
  });

  test('disables apply button when applying winner', () => {
    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} applyingWinner={true} />
      </TestWrapper>
    );

    const applyButton = screen.getByRole('button', { name: /apply winning variant/i });
    expect(applyButton).toBeDisabled();
    expect(screen.getByText('Applying...')).toBeInTheDocument();
  });

  test('calls onClose when close button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} />
      </TestWrapper>
    );

    const closeButton = screen.getByRole('button', { name: /close dialog without applying/i });
    await user.click(closeButton);

    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  test('handles invalid test results gracefully', () => {
    const invalidTestResults = {
      name: 'Invalid Test',
      // Missing required fields
    };

    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} testResults={invalidTestResults} />
      </TestWrapper>
    );

    expect(screen.getByText('Invalid Test Data')).toBeInTheDocument();
    expect(screen.getByText(/test results data is invalid/i)).toBeInTheDocument();
  });

  test('returns null when testResults is null', () => {
    const { container } = render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} testResults={null} />
      </TestWrapper>
    );

    expect(container.firstChild).toBeNull();
  });

  test('formats dates safely', () => {
    const testResultsWithInvalidDate = {
      ...mockTestResults,
      start_date: 'invalid-date',
      end_date: null
    };

    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} testResults={testResultsWithInvalidDate} />
      </TestWrapper>
    );

    expect(screen.getByText(/Started: Invalid Date/)).toBeInTheDocument();
    // end_date is null, so it shouldn't render the "Ended" chip
    expect(screen.queryByText(/Ended:/)).not.toBeInTheDocument();
  });

  test('handles missing metrics gracefully', () => {
    const testResultsWithMissingMetrics = {
      ...mockTestResults,
      variants: [
        {
          variant_id: 'variant-1',
          name: 'Control',
          is_control: true,
          status: 'completed',
          metrics: {} // Empty metrics
        }
      ]
    };

    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} testResults={testResultsWithMissingMetrics} />
      </TestWrapper>
    );

    expect(screen.getAllByText('N/A')).toHaveLength(7); // Should show N/A for all missing metrics
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ABTestResultsDialog {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('dialog')).toHaveAttribute('aria-labelledby', 'ab-test-results-title');
    expect(screen.getByRole('dialog')).toHaveAttribute('aria-describedby', 'ab-test-results-content');
    expect(screen.getByRole('tablist')).toHaveAttribute('aria-label', 'A/B test results view options');
  });
});

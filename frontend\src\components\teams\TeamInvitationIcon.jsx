/**
 * Enhanced Team Invitation Icon - Enterprise-grade team invitation status indicator component
 * Features: Comprehensive team invitation icon system with dynamic status visualization and interactive
 * state management, detailed icon customization with size variants and color themes, advanced icon
 * features with hover effects and contextual tooltips, ACE Social's team management system integration
 * with seamless invitation status tracking and member count display, icon interaction features including
 * click handlers and keyboard navigation, icon customization capabilities with theme variants and
 * accessibility modes, real-time icon updates with live status changes and notification badges, and
 * seamless ACE Social platform integration with advanced icon orchestration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  Badge,
  Menu,
  Typography,
  Box,
  Divider,
  CircularProgress,
  useTheme,
  alpha,
  Card,
  CardContent,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  LinearProgress,
  Zoom,
  Collapse,
  Al<PERSON>,
  Snackbar,
  Button
} from '@mui/material';
import {
  Groups as GroupsIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Analytics as AnalyticsIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useTeam } from '../../contexts/TeamContext';
import { formatDistanceToNow } from 'date-fns';
import AccessibleButton from '../common/AccessibleButton';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Icon sizes
const ICON_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
};

// Icon themes
const ICON_THEMES = {
  DEFAULT: 'default',
  BRANDED: 'branded',
  MINIMAL: 'minimal',
  VIBRANT: 'vibrant'
};

// Invitation status types
const INVITATION_STATUS = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected',
  EXPIRED: 'expired'
};

/**
 * Enhanced Team Invitation Icon - Comprehensive invitation status indicator with advanced features
 * Implements detailed icon customization and enterprise-grade visual management capabilities
 */
const TeamInvitationIcon = memo(forwardRef(({
  size = ICON_SIZES.MEDIUM,
  theme: iconTheme = ICON_THEMES.DEFAULT,
  showAnalytics = true,
  enableRealTimeUpdates = true,
  enableAnimations = true,
  maxDisplayedInvitations = 5,
  onInvitationAction,
  onAnalyticsTrack,
  customStyles = {}
}, ref) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { invitations, fetchMyInvitations, acceptInvitation, rejectInvitation } = useTeam();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const iconRef = useRef(null);
  const menuRef = useRef(null);

  // Enhanced state management
  const [anchorEl, setAnchorEl] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeInvitationId, setActiveInvitationId] = useState(null);
  const [notifications, setNotifications] = useState([]);
  const [iconAnalytics, setIconAnalytics] = useState({
    clicks: 0,
    menuOpens: 0,
    acceptedInvitations: 0,
    rejectedInvitations: 0,
    lastActivity: new Date().toISOString()
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showAdvancedView, setShowAdvancedView] = useState(false);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    openMenu: () => handleMenuOpen({ currentTarget: iconRef.current }),
    closeMenu: () => handleMenuClose(),
    refreshInvitations: () => handleRefreshInvitations(),
    getAnalytics: () => iconAnalytics,
    resetAnalytics: () => setIconAnalytics({
      clicks: 0,
      menuOpens: 0,
      acceptedInvitations: 0,
      rejectedInvitations: 0,
      lastActivity: new Date().toISOString()
    }),
    getPendingCount: () => pendingInvitations.length
  }), [
    iconAnalytics,
    handleMenuOpen,
    handleMenuClose,
    handleRefreshInvitations,
    pendingInvitations.length
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Filter pending invitations with enhanced logic
  const pendingInvitations = useMemo(() => {
    return invitations.filter(inv => inv.status === INVITATION_STATUS.PENDING);
  }, [invitations]);

  // Enhanced icon styling based on theme and size
  const getIconStyles = useCallback(() => {
    const baseStyles = {
      transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
      ...customStyles
    };

    const sizeStyles = {
      [ICON_SIZES.SMALL]: { fontSize: '1.2rem' },
      [ICON_SIZES.MEDIUM]: { fontSize: '1.5rem' },
      [ICON_SIZES.LARGE]: { fontSize: '2rem' }
    };

    const themeStyles = {
      [ICON_THEMES.DEFAULT]: {
        color: theme.palette.text.primary
      },
      [ICON_THEMES.BRANDED]: {
        color: ACE_COLORS.PURPLE,
        filter: `drop-shadow(0 2px 4px ${alpha(ACE_COLORS.PURPLE, 0.3)})`
      },
      [ICON_THEMES.MINIMAL]: {
        color: theme.palette.text.secondary
      },
      [ICON_THEMES.VIBRANT]: {
        background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.YELLOW} 100%)`,
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent'
      }
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...themeStyles[iconTheme]
    };
  }, [theme, size, iconTheme, customStyles]);

  // Enhanced badge styling
  const getBadgeStyles = useCallback(() => {
    const count = pendingInvitations.length;

    if (count === 0) return {};

    if (count > 10) {
      return {
        backgroundColor: '#F44336',
        animation: enableAnimations ? 'pulse 2s infinite' : 'none'
      };
    }

    if (count > 5) {
      return {
        backgroundColor: ACE_COLORS.YELLOW,
        color: ACE_COLORS.DARK
      };
    }

    return {
      backgroundColor: ACE_COLORS.PURPLE,
      color: ACE_COLORS.WHITE
    };
  }, [pendingInvitations.length, enableAnimations]);

  // Enhanced event handlers
  const handleMenuOpen = useCallback((event) => {
    setAnchorEl(event.currentTarget);
    setIconAnalytics(prev => ({
      ...prev,
      menuOpens: prev.menuOpens + 1,
      lastActivity: new Date().toISOString()
    }));

    if (onAnalyticsTrack) {
      onAnalyticsTrack({
        action: 'menu_opened',
        pendingCount: pendingInvitations.length,
        timestamp: new Date().toISOString()
      });
    }

    announceToScreenReader(`Team invitations menu opened. ${pendingInvitations.length} pending invitations.`);
  }, [onAnalyticsTrack, pendingInvitations.length, announceToScreenReader]);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    announceToScreenReader('Team invitations menu closed');
  }, [announceToScreenReader]);

  const handleRefreshInvitations = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await fetchMyInvitations();
      announceToScreenReader('Team invitations refreshed');
    } catch {
      announceToScreenReader('Failed to refresh invitations');
    } finally {
      setIsRefreshing(false);
    }
  }, [fetchMyInvitations, announceToScreenReader]);

  // Enhanced accept invitation handler
  const handleAccept = useCallback(async (invitation) => {
    setLoading(true);
    setActiveInvitationId(invitation.id);

    try {
      const result = await acceptInvitation(invitation.token);

      if (result) {
        setIconAnalytics(prev => ({
          ...prev,
          acceptedInvitations: prev.acceptedInvitations + 1,
          lastActivity: new Date().toISOString()
        }));

        if (onInvitationAction) {
          onInvitationAction({
            action: 'accepted',
            invitation,
            result
          });
        }

        if (onAnalyticsTrack) {
          onAnalyticsTrack({
            action: 'invitation_accepted',
            teamName: invitation.team_name,
            timestamp: new Date().toISOString()
          });
        }

        announceToScreenReader(`Invitation to join ${invitation.team_name} accepted successfully`);
        handleMenuClose();
        navigate(`/teams/${result.team.id}`);
      }
    } catch {
      announceToScreenReader(`Failed to accept invitation to ${invitation.team_name}`);
    } finally {
      setLoading(false);
      setActiveInvitationId(null);
    }
  }, [acceptInvitation, onInvitationAction, onAnalyticsTrack, announceToScreenReader, handleMenuClose, navigate]);

  // Enhanced reject invitation handler
  const handleReject = useCallback(async (invitation) => {
    setLoading(true);
    setActiveInvitationId(invitation.id);

    try {
      await rejectInvitation(invitation.token);

      setIconAnalytics(prev => ({
        ...prev,
        rejectedInvitations: prev.rejectedInvitations + 1,
        lastActivity: new Date().toISOString()
      }));

      if (onInvitationAction) {
        onInvitationAction({
          action: 'rejected',
          invitation
        });
      }

      if (onAnalyticsTrack) {
        onAnalyticsTrack({
          action: 'invitation_rejected',
          teamName: invitation.team_name,
          timestamp: new Date().toISOString()
        });
      }

      announceToScreenReader(`Invitation to join ${invitation.team_name} declined`);
    } catch {
      announceToScreenReader(`Failed to decline invitation to ${invitation.team_name}`);
    } finally {
      setLoading(false);
      setActiveInvitationId(null);
    }
  }, [rejectInvitation, onInvitationAction, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced view all handler
  const handleViewAll = useCallback(() => {
    if (onAnalyticsTrack) {
      onAnalyticsTrack({
        action: 'view_all_clicked',
        timestamp: new Date().toISOString()
      });
    }

    announceToScreenReader('Navigating to all team invitations');
    handleMenuClose();
    navigate('/teams?tab=1');
  }, [onAnalyticsTrack, announceToScreenReader, handleMenuClose, navigate]);

  // Enhanced useEffect hooks
  useEffect(() => {
    fetchMyInvitations();
  }, [fetchMyInvitations]);

  useEffect(() => {
    if (enableRealTimeUpdates) {
      const interval = setInterval(() => {
        fetchMyInvitations();
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(interval);
    }
  }, [enableRealTimeUpdates, fetchMyInvitations]);

  return (
    <>
      <Tooltip
        title={
          <Box>
            <Typography variant="body2" fontWeight="bold">
              Team Invitations
            </Typography>
            <Typography variant="caption">
              {pendingInvitations.length === 0
                ? 'No pending invitations'
                : `${pendingInvitations.length} pending invitation${pendingInvitations.length !== 1 ? 's' : ''}`
              }
            </Typography>
            {showAnalytics && (
              <Typography variant="caption" display="block" sx={{ mt: 0.5, opacity: 0.8 }}>
                {iconAnalytics.menuOpens} menu opens • {iconAnalytics.acceptedInvitations} accepted
              </Typography>
            )}
          </Box>
        }
        placement="bottom"
        arrow
      >
        <Box sx={{ position: 'relative' }}>
          <AccessibleButton
            ref={iconRef}
            isIconButton
            color="inherit"
            onClick={handleMenuOpen}
            ariaLabel={`Team invitations${pendingInvitations.length > 0 ? ` (${pendingInvitations.length} pending)` : ''}`}
            sx={{
              ...getIconStyles(),
              '&:hover': {
                transform: 'scale(1.1)',
                animation: enableAnimations && pendingInvitations.length > 0 ? 'pulse 1s infinite' : 'none',
                '@keyframes pulse': {
                  '0%': { transform: 'scale(1.1)' },
                  '50%': { transform: 'scale(1.2)' },
                  '100%': { transform: 'scale(1.1)' }
                }
              },
              '&:focus': {
                outline: `2px solid ${ACE_COLORS.PURPLE}`,
                outlineOffset: '2px'
              }
            }}
          >
            <Badge
              badgeContent={pendingInvitations.length > 99 ? '99+' : pendingInvitations.length}
              invisible={pendingInvitations.length === 0}
              sx={{
                '& .MuiBadge-badge': {
                  ...getBadgeStyles(),
                  fontWeight: 'bold',
                  fontSize: '0.75rem',
                  minWidth: pendingInvitations.length > 9 ? '24px' : '20px',
                  height: pendingInvitations.length > 9 ? '24px' : '20px',
                  border: `2px solid ${ACE_COLORS.WHITE}`,
                  boxShadow: `0 2px 8px ${alpha(theme.palette.common.black, 0.2)}`
                }
              }}
            >
              <GroupsIcon sx={{ fontSize: 'inherit' }} />
            </Badge>
          </AccessibleButton>

          {/* Loading Indicator */}
          {isRefreshing && (
            <Box
              sx={{
                position: 'absolute',
                top: -2,
                right: -2,
                width: 16,
                height: 16,
                borderRadius: '50%',
                backgroundColor: ACE_COLORS.YELLOW,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <CircularProgress size={10} sx={{ color: ACE_COLORS.DARK }} />
            </Box>
          )}
        </Box>
      </Tooltip>

      {/* Enhanced Menu */}
      <Menu
        ref={menuRef}
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        slotProps={{
          paper: {
            sx: {
              width: 380,
              maxHeight: 500,
              overflow: 'auto',
              ...glassMorphismStyles,
              border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* Enhanced Header */}
        <Box sx={{ px: 3, py: 2, background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${alpha(ACE_COLORS.PURPLE, 0.8)} 100%)` }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="h6" fontWeight="bold" sx={{ color: ACE_COLORS.WHITE }}>
                Team Invitations
              </Typography>
              <Typography variant="body2" sx={{ color: alpha(ACE_COLORS.WHITE, 0.8) }}>
                {pendingInvitations.length === 0
                  ? 'No pending invitations'
                  : `${pendingInvitations.length} pending invitation${pendingInvitations.length !== 1 ? 's' : ''}`}
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Invitations">
                <IconButton
                  size="small"
                  onClick={handleRefreshInvitations}
                  disabled={isRefreshing}
                  sx={{ color: ACE_COLORS.WHITE }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>

              {showAnalytics && (
                <Tooltip title="Toggle Advanced View">
                  <IconButton
                    size="small"
                    onClick={() => setShowAdvancedView(!showAdvancedView)}
                    sx={{ color: ACE_COLORS.WHITE }}
                  >
                    <AnalyticsIcon />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>

          {/* Loading Progress */}
          {isRefreshing && (
            <Box sx={{ mt: 1 }}>
              <LinearProgress
                sx={{
                  backgroundColor: alpha(ACE_COLORS.WHITE, 0.3),
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: ACE_COLORS.YELLOW
                  }
                }}
              />
            </Box>
          )}
        </Box>

        {/* Analytics Panel */}
        <Collapse in={showAdvancedView && showAnalytics}>
          <Card sx={{ m: 2, ...glassMorphismStyles }}>
            <CardContent sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
                Invitation Analytics
              </Typography>
              <Stack direction="row" spacing={2} flexWrap="wrap">
                <Chip
                  icon={<VisibilityIcon />}
                  label={`${iconAnalytics.menuOpens} opens`}
                  size="small"
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    color: ACE_COLORS.PURPLE
                  }}
                />
                <Chip
                  icon={<CheckCircleIcon />}
                  label={`${iconAnalytics.acceptedInvitations} accepted`}
                  size="small"
                  sx={{
                    backgroundColor: alpha('#4CAF50', 0.1),
                    color: '#4CAF50'
                  }}
                />
                <Chip
                  icon={<CancelIcon />}
                  label={`${iconAnalytics.rejectedInvitations} declined`}
                  size="small"
                  sx={{
                    backgroundColor: alpha('#F44336', 0.1),
                    color: '#F44336'
                  }}
                />
              </Stack>
            </CardContent>
          </Card>
        </Collapse>

        <Divider sx={{ borderColor: alpha(ACE_COLORS.PURPLE, 0.2) }} />

        {/* Enhanced Invitation List */}
        {pendingInvitations.length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                mx: 'auto',
                mb: 2,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE
              }}
            >
              <GroupsIcon sx={{ fontSize: '2rem' }} />
            </Avatar>
            <Typography variant="h6" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
              No Pending Invitations
            </Typography>
            <Typography variant="body2" color="text.secondary">
              You&apos;re all caught up! No team invitations waiting for your response.
            </Typography>
          </Box>
        ) : (
          <>
            {pendingInvitations.slice(0, maxDisplayedInvitations).map((invitation, index) => (
              <Zoom in timeout={300 + index * 100} key={invitation.id}>
                <Card
                  sx={{
                    m: 2,
                    border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
                    borderRadius: 3,
                    background: `linear-gradient(135deg,
                      ${alpha(theme.palette.background.paper, 0.95)} 0%,
                      ${alpha(ACE_COLORS.PURPLE, 0.03)} 100%)`,
                    transition: 'all 300ms ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: `0 8px 25px ${alpha(ACE_COLORS.PURPLE, 0.15)}`
                    }
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box display="flex" alignItems="flex-start" gap={2}>
                      <Avatar
                        sx={{
                          backgroundColor: ACE_COLORS.PURPLE,
                          color: ACE_COLORS.WHITE,
                          width: 40,
                          height: 40
                        }}
                      >
                        <GroupsIcon />
                      </Avatar>

                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ color: ACE_COLORS.DARK }} noWrap>
                          {invitation.team_name}
                        </Typography>

                        <Box display="flex" alignItems="center" gap={1} sx={{ mb: 1 }}>
                          <PersonIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary" noWrap>
                            Invited by {invitation.invited_by_name}
                          </Typography>
                        </Box>

                        <Box display="flex" alignItems="center" gap={1} sx={{ mb: 2 }}>
                          <ScheduleIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                          <Typography variant="caption" color="text.secondary">
                            {formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true })}
                          </Typography>
                        </Box>

                        <Stack direction="row" spacing={1} justifyContent="flex-end">
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<CancelIcon />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleReject(invitation);
                            }}
                            disabled={loading && activeInvitationId === invitation.id}
                            sx={{
                              borderColor: '#F44336',
                              color: '#F44336',
                              '&:hover': {
                                borderColor: '#F44336',
                                backgroundColor: alpha('#F44336', 0.1)
                              }
                            }}
                          >
                            Decline
                          </Button>

                          <Button
                            size="small"
                            variant="contained"
                            startIcon={
                              loading && activeInvitationId === invitation.id ? (
                                <CircularProgress size={16} sx={{ color: ACE_COLORS.WHITE }} />
                              ) : (
                                <CheckCircleIcon />
                              )
                            }
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAccept(invitation);
                            }}
                            disabled={loading && activeInvitationId === invitation.id}
                            sx={{
                              backgroundColor: ACE_COLORS.PURPLE,
                              '&:hover': {
                                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                              }
                            }}
                          >
                            {loading && activeInvitationId === invitation.id ? 'Accepting...' : 'Accept'}
                          </Button>
                        </Stack>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Zoom>
            ))}

            {pendingInvitations.length > maxDisplayedInvitations && (
              <Box sx={{ p: 2, textAlign: 'center' }}>
                <Chip
                  label={`+${pendingInvitations.length - maxDisplayedInvitations} more invitation${pendingInvitations.length - maxDisplayedInvitations !== 1 ? 's' : ''}`}
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                    color: ACE_COLORS.DARK,
                    fontWeight: 'bold'
                  }}
                />
              </Box>
            )}
          </>
        )}

        <Divider sx={{ borderColor: alpha(ACE_COLORS.PURPLE, 0.2) }} />

        {/* Enhanced Footer */}
        <Box sx={{ p: 2 }}>
          <Button
            fullWidth
            variant="outlined"
            onClick={handleViewAll}
            sx={{
              borderColor: ACE_COLORS.PURPLE,
              color: ACE_COLORS.PURPLE,
              fontWeight: 'bold',
              '&:hover': {
                borderColor: ACE_COLORS.PURPLE,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
              }
            }}
          >
            View All Invitations
          </Button>
        </Box>
      </Menu>

      {/* Notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={index}
          open={true}
          autoHideDuration={6000}
          onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            severity={notification.severity || 'info'}
            onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
            sx={{
              ...glassMorphismStyles,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </>
  );
}));

TeamInvitationIcon.displayName = 'TeamInvitationIcon';

TeamInvitationIcon.propTypes = {
  /** Icon size variant */
  size: PropTypes.oneOf(Object.values(ICON_SIZES)),
  /** Icon theme variant */
  theme: PropTypes.oneOf(Object.values(ICON_THEMES)),
  /** Show analytics information */
  showAnalytics: PropTypes.bool,
  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,
  /** Enable keyboard navigation */
  enableKeyboardNavigation: PropTypes.bool,
  /** Enable animations */
  enableAnimations: PropTypes.bool,
  /** Maximum number of invitations to display in menu */
  maxDisplayedInvitations: PropTypes.number,
  /** Function called when invitation action is performed */
  onInvitationAction: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Custom styles object */
  customStyles: PropTypes.object
};

export default TeamInvitationIcon;


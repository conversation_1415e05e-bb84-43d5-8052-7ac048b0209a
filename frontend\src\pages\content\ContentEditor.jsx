// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  TextField,
  Chip,
  Divider,
  IconButton,
  Alert,
  CircularProgress,
  Paper,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Tooltip
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  AutoAwesome as AutoAwesomeIcon,
  Refresh as RefreshIcon,
  Preview as PreviewIcon,
  Schedule as ScheduleIcon,
  Edit as EditIcon
} from '@mui/icons-material';

import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import { useAuth } from '../../hooks/useAuth';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { getContent, updateContent } from '../../api/content';
import GuidedRefinement from '../../components/regeneration/GuidedRefinement';
import RegenerationCreditTracker from '../../components/regeneration/RegenerationCreditTracker';
import FeatureGate from '../../components/common/FeatureGate';
import SubscriptionStatus from '../../components/subscription/SubscriptionStatus';
import api from '../../api';

const ContentEditor = () => {
  const { contentId } = useParams();
  const navigate = useNavigate();
  const { showSuccess, showError } = useAdvancedToast();
  const { user } = useAuth();
  const { hasFeatureAccess, canPerformAction, getRegenerationCost, updateUsage } = useSubscription();

  // State
  const [content, setContent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [showRefinement, setShowRefinement] = useState(false);
  const [creditInfo, setCreditInfo] = useState(null);

  // Form state
  const [title, setTitle] = useState('');
  const [textContent, setTextContent] = useState('');
  const [platforms, setPlatforms] = useState([]);
  const [tags, setTags] = useState([]);
  const [status, setStatus] = useState('draft');
  const [newTag, setNewTag] = useState('');

  // Load content
  useEffect(() => {
    const loadContent = async () => {
      try {
        setLoading(true);
        const contentData = await getContent(contentId);
        setContent(contentData);

        // Populate form fields
        setTitle(contentData.title || '');
        setTextContent(contentData.text_content || '');
        setPlatforms(contentData.platforms || []);
        setTags(contentData.tags || []);
        setStatus(contentData.status || 'draft');
      } catch (error) {
        console.error('Error loading content:', error);
        setError('Failed to load content');
        showError('Failed to load content');
      } finally {
        setLoading(false);
      }
    };

    if (contentId) {
      loadContent();
    }
  }, [contentId, showError]);

  // Load credit information
  useEffect(() => {
    const loadCreditInfo = async () => {
      try {
        const response = await api.get('/api/regeneration/usage');
        setCreditInfo(response.data);
      } catch (error) {
        console.error('Error loading credit info:', error);
      }
    };

    loadCreditInfo();
  }, []);

  // Handle save
  const handleSave = async () => {
    try {
      setSaving(true);

      const updateData = {
        title,
        text_content: textContent,
        platforms,
        tags,
        status
      };

      const updatedContent = await updateContent(contentId, updateData);
      setContent(updatedContent);
      showSuccess('Content updated successfully');
    } catch (error) {
      console.error('Error saving content:', error);
      showError('Failed to save content');
    } finally {
      setSaving(false);
    }
  };

  // Handle content update from regeneration
  const handleContentUpdate = (updatedContent) => {
    if (updatedContent.title) setTitle(updatedContent.title);
    if (updatedContent.text_content) setTextContent(updatedContent.text_content);
    if (updatedContent.tags) setTags(updatedContent.tags);

    // Refresh credit info
    const loadCreditInfo = async () => {
      try {
        const response = await api.get('/api/regeneration/usage');
        setCreditInfo(response.data);
      } catch (error) {
        console.error('Error loading credit info:', error);
      }
    };
    loadCreditInfo();
  };

  // Handle add tag
  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  // Handle remove tag
  const handleRemoveTag = (tagToRemove) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Handle platform toggle
  const handlePlatformToggle = (platform) => {
    if (platforms.includes(platform)) {
      setPlatforms(platforms.filter(p => p !== platform));
    } else {
      setPlatforms([...platforms, platform]);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton
          color="primary"
          onClick={() => navigate('/content/library')}
          sx={{ mr: 1 }}
        >
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          <EditIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Edit Content
        </Typography>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <FeatureGate
            feature="regeneration"
            action="regeneration"
            variant="inline"
            fallback={
              <Button
                variant="outlined"
                startIcon={<AutoAwesomeIcon />}
                disabled
                sx={{ opacity: 0.6 }}
              >
                Regenerate (Upgrade Required)
              </Button>
            }
          >
            <Button
              variant="outlined"
              startIcon={<AutoAwesomeIcon />}
              onClick={() => setShowRefinement(true)}
              disabled={!content || !canPerformAction('regeneration')}
              sx={{
                background: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)',
                color: 'white',
                border: 'none',
                '&:hover': {
                  background: 'linear-gradient(45deg, #FF5252, #26C6DA)',
                }
              }}
            >
              Regenerate with AI ({getRegenerationCost()} credits)
            </Button>
          </FeatureGate>
          <Button
            variant="outlined"
            startIcon={<PreviewIcon />}
            onClick={() => navigate(`/content/detail/${contentId}`)}
          >
            Preview
          </Button>
          <Button
            variant="contained"
            startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </Box>
      </Box>

      {/* Credit Info Banner */}
      {creditInfo && (
        <Alert
          severity="info"
          sx={{ mb: 3 }}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={() => navigate('/billing/addons')}
            >
              Buy Credits
            </Button>
          }
        >
          <strong>{creditInfo.remaining_credits || 0} regeneration credits remaining</strong> this month.
          First regeneration is FREE, second costs 0.5 credits, additional cost 1 credit each.
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Main Content Editor */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Tabs
                value={tabValue}
                onChange={(e, newValue) => setTabValue(newValue)}
                sx={{ mb: 3 }}
              >
                <Tab label="Content" />
                <Tab label="Settings" />
                <Tab label="Platforms" />
              </Tabs>

              {tabValue === 0 && (
                <Box>
                  <TextField
                    label="Title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    fullWidth
                    sx={{ mb: 3 }}
                    helperText="The main title for your content"
                  />

                  <TextField
                    label="Content"
                    value={textContent}
                    onChange={(e) => setTextContent(e.target.value)}
                    fullWidth
                    multiline
                    rows={12}
                    sx={{ mb: 3 }}
                    helperText="Your main content text. Use the Regenerate button above to improve with AI."
                  />

                  <Box>
                    <Typography variant="subtitle1" gutterBottom>
                      Tags
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {tags.map((tag) => (
                        <Chip
                          key={tag}
                          label={`#${tag}`}
                          onDelete={() => handleRemoveTag(tag)}
                          color="primary"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <TextField
                        label="Add tag"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                        size="small"
                      />
                      <Button onClick={handleAddTag} variant="outlined">
                        Add
                      </Button>
                    </Box>
                  </Box>
                </Box>
              )}

              {tabValue === 1 && (
                <Box>
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={status}
                      onChange={(e) => setStatus(e.target.value)}
                      label="Status"
                    >
                      <MenuItem value="draft">Draft</MenuItem>
                      <MenuItem value="published">Published</MenuItem>
                      <MenuItem value="scheduled">Scheduled</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              )}

              {tabValue === 2 && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Target Platforms
                  </Typography>
                  {['twitter', 'linkedin', 'facebook', 'instagram'].map((platform) => (
                    <FormControlLabel
                      key={platform}
                      control={
                        <Switch
                          checked={platforms.includes(platform)}
                          onChange={() => handlePlatformToggle(platform)}
                        />
                      }
                      label={platform.charAt(0).toUpperCase() + platform.slice(1)}
                    />
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Subscription Status */}
          <Box sx={{ mb: 3 }}>
            <SubscriptionStatus variant="full" showUsage={true} showUpgrade={true} />
          </Box>

          {/* Credit Tracker */}
          {creditInfo && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <RegenerationCreditTracker creditInfo={creditInfo} />
              </CardContent>
            </Card>
          )}

          {/* Content Info */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Content Information
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Typography variant="body2" color="text.secondary" gutterBottom>
                Created: {content?.created_at ? new Date(content.created_at).toLocaleDateString() : 'Unknown'}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Last Modified: {content?.updated_at ? new Date(content.updated_at).toLocaleDateString() : 'Unknown'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Status: <Chip label={status} size="small" />
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Guided Refinement Dialog */}
      {showRefinement && content && (
        <GuidedRefinement
          content={{
            id: contentId,
            title,
            text_content: textContent,
            tags
          }}
          onContentUpdate={handleContentUpdate}
          onClose={() => setShowRefinement(false)}
        />
      )}
    </Box>
  );
};

export default ContentEditor;

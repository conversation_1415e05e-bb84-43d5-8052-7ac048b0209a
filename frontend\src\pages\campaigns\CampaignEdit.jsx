// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,

  CircularProgress,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  Tabs,
  Tab,
  IconButton
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CampaignIcon from '@mui/icons-material/Campaign';
import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';
import InfoIcon from '@mui/icons-material/Info';
import BrushIcon from '@mui/icons-material/Brush';
import ScheduleIcon from '@mui/icons-material/Schedule';
import SettingsIcon from '@mui/icons-material/Settings';

import { useNotification } from '../../hooks/useNotification';
import api from '../../api';
import CampaignUnifiedBranding from '../../components/campaigns/CampaignUnifiedBranding';

const CampaignEdit = () => {
  const navigate = useNavigate();
  const { campaignId } = useParams();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [campaign, setCampaign] = useState(null);
  const [icp, setIcp] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    start_date: new Date(),
    end_date: null,
    platforms: [],
    status: 'draft',
    branding_preferences: null,
    use_custom_branding: false
  });
  
  // Available platforms
  const availablePlatforms = [
    'linkedin',
    'twitter',
    'facebook',
    'instagram',
    'medium',
    'email'
  ];
  
  // Fetch campaign details
  useEffect(() => {
    const fetchCampaign = async () => {
      setLoading(true);
      try {
        // Fetch campaign
        const campaignResponse = await api.get(`/api/campaigns/${campaignId}`);
        setCampaign(campaignResponse.data);
        
        // Fetch ICP
        const icpResponse = await api.get(`/api/icps/${campaignResponse.data.icp_id}`);
        setIcp(icpResponse.data);
        
        // Set form data
        setFormData({
          name: campaignResponse.data.name,
          description: campaignResponse.data.description,
          start_date: new Date(campaignResponse.data.start_date),
          end_date: campaignResponse.data.end_date ? new Date(campaignResponse.data.end_date) : null,
          platforms: campaignResponse.data.platforms || [],
          status: campaignResponse.data.status || 'draft',
          branding_preferences: campaignResponse.data.branding_preferences,
          use_custom_branding: campaignResponse.data.use_custom_branding || false
        });
      } catch (error) {
        console.error('Error fetching campaign:', error);
        showErrorNotification('Failed to load campaign details');
        navigate('/campaigns');
      } finally {
        setLoading(false);
      }
    };
    
    fetchCampaign();
  }, [campaignId, navigate, showErrorNotification]);
  
  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };
  
  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle date changes
  const handleDateChange = (name, date) => {
    setFormData(prev => ({
      ...prev,
      [name]: date
    }));
  };
  
  // Handle platforms change
  const handlePlatformsChange = (event) => {
    const { value } = event.target;
    setFormData(prev => ({
      ...prev,
      platforms: value
    }));
  };
  
  // Handle branding preferences change
  const handleBrandingChange = (brandingData) => {
    setFormData(prev => ({
      ...prev,
      branding_preferences: brandingData
    }));
  };
  
  // Handle use custom branding change
  const handleUseCustomBrandingChange = (useCustom) => {
    setFormData(prev => ({
      ...prev,
      use_custom_branding: useCustom
    }));
  };
  
  // Save campaign changes
  const handleSave = async () => {
    setSaving(true);
    
    try {
      const updateData = {
        name: formData.name,
        description: formData.description,
        start_date: formData.start_date,
        end_date: formData.end_date,
        platforms: formData.platforms,
        status: formData.status,
        branding_preferences: formData.branding_preferences,
        use_custom_branding: formData.use_custom_branding
      };
      
      const response = await api.put(`/api/campaigns/${campaignId}`, updateData);
      
      setCampaign(response.data);
      showSuccessNotification('Campaign updated successfully');
      
      // Navigate back to campaign detail
      navigate(`/campaigns/${campaignId}`);
    } catch (error) {
      console.error('Error updating campaign:', error);
      showErrorNotification('Failed to update campaign');
    } finally {
      setSaving(false);
    }
  };
  
  // Delete campaign
  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
      try {
        await api.delete(`/api/campaigns/${campaignId}`);
        showSuccessNotification('Campaign deleted successfully');
        navigate('/campaigns');
      } catch (error) {
        console.error('Error deleting campaign:', error);
        showErrorNotification('Failed to delete campaign');
      }
    }
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box sx={{ py: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton
          color="primary"
          onClick={() => navigate(`/campaigns/${campaignId}`)}
          sx={{ mr: 1 }}
        >
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" sx={{ flexGrow: 1 }}>
          <CampaignIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Edit Campaign
        </Typography>
        
        <Box>
          <Button
            variant="outlined"
            color="error"
            onClick={handleDelete}
            startIcon={<DeleteIcon />}
            sx={{ mr: 1 }}
          >
            Delete
          </Button>
          
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            startIcon={saving ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </Box>
      </Box>
      
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab icon={<InfoIcon />} label="Basic Info" />
            <Tab icon={<BrushIcon />} label="Branding" />
            <Tab icon={<ScheduleIcon />} label="Schedule" />
            <Tab icon={<SettingsIcon />} label="Advanced" />
          </Tabs>
        </Box>
        
        <CardContent>
          {/* Basic Info Tab */}
          {tabValue === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Campaign Name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Campaign Description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  multiline
                  rows={4}
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Platforms</InputLabel>
                  <Select
                    multiple
                    name="platforms"
                    value={formData.platforms}
                    onChange={handlePlatformsChange}
                    input={<OutlinedInput label="Platforms" />}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value.charAt(0).toUpperCase() + value.slice(1)} />
                        ))}
                      </Box>
                    )}
                  >
                    {availablePlatforms.map((platform) => (
                      <MenuItem key={platform} value={platform}>
                        {platform.charAt(0).toUpperCase() + platform.slice(1)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    label="Status"
                  >
                    <MenuItem value="draft">Draft</MenuItem>
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="paused">Paused</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}
          
          {/* Branding Tab */}
          {tabValue === 1 && (
            <CampaignUnifiedBranding
              campaignBranding={formData.branding_preferences}
              useCustomBranding={formData.use_custom_branding}
              onBrandingChange={handleBrandingChange}
              onUseCustomBrandingChange={handleUseCustomBrandingChange}
              campaignName={formData.name}
            />
          )}
          
          {/* Schedule Tab */}
          {tabValue === 2 && (
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="Start Date"
                    value={formData.start_date}
                    onChange={(date) => handleDateChange('start_date', date)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="End Date (Optional)"
                    value={formData.end_date}
                    onChange={(date) => handleDateChange('end_date', date)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
              </Grid>
            </LocalizationProvider>
          )}
          
          {/* Advanced Tab */}
          {tabValue === 3 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Advanced Campaign Settings
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Campaign Priority</InputLabel>
                  <Select
                    value={formData.priority || 'medium'}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                    label="Campaign Priority"
                  >
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                    <MenuItem value="urgent">Urgent</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Auto-publish</InputLabel>
                  <Select
                    value={formData.auto_publish || 'manual'}
                    onChange={(e) => setFormData(prev => ({ ...prev, auto_publish: e.target.value }))}
                    label="Auto-publish"
                  >
                    <MenuItem value="manual">Manual Review</MenuItem>
                    <MenuItem value="scheduled">Auto-publish Scheduled</MenuItem>
                    <MenuItem value="immediate">Auto-publish Immediately</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Content Approval Threshold (%)"
                  type="number"
                  value={formData.approval_threshold || 80}
                  onChange={(e) => setFormData(prev => ({ ...prev, approval_threshold: parseInt(e.target.value) }))}
                  inputProps={{ min: 0, max: 100 }}
                  helperText="Minimum approval score required for auto-publishing"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Max Content Variations"
                  type="number"
                  value={formData.max_variations || 5}
                  onChange={(e) => setFormData(prev => ({ ...prev, max_variations: parseInt(e.target.value) }))}
                  inputProps={{ min: 1, max: 20 }}
                  helperText="Maximum number of content variations to generate"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Campaign Notes"
                  value={formData.notes || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Internal notes about this campaign..."
                  helperText="These notes are only visible to your team"
                />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Campaign Analytics
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Typography variant="body2" color="text.secondary">
                    Campaign ID: {campaign?.id || 'Not saved yet'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Created: {campaign?.created_at ? new Date(campaign.created_at).toLocaleDateString() : 'Not saved yet'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Last Modified: {campaign?.updated_at ? new Date(campaign.updated_at).toLocaleDateString() : 'Not saved yet'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Target ICP: {icp?.name || 'Loading...'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default CampaignEdit;

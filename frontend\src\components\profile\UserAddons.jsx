/**
 * Enhanced User Add-ons - Enterprise-grade user add-ons management component
 * Features: Comprehensive add-on management, marketplace integration, subscription-free access,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced add-on capabilities and interactive add-on exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  LinearProgress,
  Tooltip,
  Button,
  alpha,
  Snackbar,
  Alert,
  useMediaQuery
} from '@mui/material';
import {
  AddCircle as AddCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  AccessTime as AccessTimeIcon,
  ShoppingCart as ShoppingCartIcon
} from '@mui/icons-material';
import { format, formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext';
import { Link as RouterLink } from 'react-router-dom';
import GlassmorphicCard from '../common/GlassmorphicCard';
import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Add-on display modes with enhanced configurations
const ADDON_DISPLAY_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Add-ons',
    description: 'Basic add-on management interface',
    features: ['basic_addons', 'analytics_addons', 'ai_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Add-ons',
    description: 'Comprehensive add-on management',
    features: ['detailed_addons', 'addon_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Add-ons',
    description: 'AI-powered add-on management and recommendations',
    features: ['ai_assisted', 'ai_optimization', 'addon_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Add-ons',
    description: 'Advanced add-on analytics and insights',
    features: ['analytics_addons', 'addon_insights']
  }
};

/**
 * Enhanced User Add-ons Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.isEmbedded=false] - Whether the component is embedded in another page
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights (no restrictions)
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onAddonAction] - Add-on action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-user-addons'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const UserAddons = memo(forwardRef(({
  isEmbedded = false,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onAddonAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-user-addons',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { getActiveAddons } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const addonsRef = useRef(null);
  const addons = getActiveAddons();

  // Enhanced state management
  const [addonMode, setAddonMode] = useState('compact');
  const [addonHistory, setAddonHistory] = useState([]);
  const [addonAnalytics, setAddonAnalytics] = useState(null);
  const [addonInsights, setAddonInsights] = useState(null);
  const [customAddonConfigs, setCustomAddonConfigs] = useState([]);
  const [addonPreferences, setAddonPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    addonSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [addonDrawerOpen, setAddonDrawerOpen] = useState(false);
  const [selectedAddonType, setSelectedAddonType] = useState(null);
  const [addonStats, setAddonStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [marketplaceOpen, setMarketplaceOpen] = useState(false);
  const [installedAddons, setInstalledAddons] = useState([]);
  const [availableAddons, setAvailableAddons] = useState([]);
  const [addonSearchQuery, setAddonSearchQuery] = useState('');
  const [addonFilters, setAddonFilters] = useState({
    category: 'all',
    status: 'all',
    sortBy: 'name'
  });

  // Filter addons based on search query and filters
  const filteredAddons = useMemo(() => {
    if (!addons) return [];

    let filtered = addons;

    // Apply search query
    if (addonSearchQuery.trim()) {
      filtered = filtered.filter(addon =>
        addon.name.toLowerCase().includes(addonSearchQuery.toLowerCase()) ||
        addon.description?.toLowerCase().includes(addonSearchQuery.toLowerCase())
      );
    }

    // Apply filters
    if (addonFilters.category !== 'all') {
      filtered = filtered.filter(addon => addon.category === addonFilters.category);
    }

    if (addonFilters.status !== 'all') {
      filtered = filtered.filter(addon => {
        if (addonFilters.status === 'active') return addon.is_active;
        if (addonFilters.status === 'expired') return addon.expires_at && new Date(addon.expires_at) < new Date();
        return true;
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (addonFilters.sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'date':
          return new Date(b.purchase_date) - new Date(a.purchase_date);
        case 'usage':
          return (b.remaining_uses || 0) - (a.remaining_uses || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [addons, addonSearchQuery, addonFilters]);

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastAddonCheck, setLastAddonCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with full feature access - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // All features are available to all users (no plan-based limitations)
    const features = {
      creator: {
        maxAddonTypes: -1,
        maxAddonPerDay: -1,
        hasAdvancedAddon: true,
        hasAddonAnalytics: true,
        hasCustomAddon: true,
        hasAddonInsights: true,
        hasAddonHistory: true,
        hasAIAssistance: true,
        hasAddonExport: true,
        hasAddonScheduling: true,
        hasAddonAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxAddonTypes: -1,
        maxAddonPerDay: -1,
        hasAdvancedAddon: true,
        hasAddonAnalytics: true,
        hasCustomAddon: true,
        hasAddonInsights: true,
        hasAddonHistory: true,
        hasAIAssistance: true,
        hasAddonExport: true,
        hasAddonScheduling: true,
        hasAddonAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxAddonTypes: -1,
        maxAddonPerDay: -1,
        hasAdvancedAddon: true,
        hasAddonAnalytics: true,
        hasCustomAddon: true,
        hasAddonInsights: true,
        hasAddonHistory: true,
        hasAIAssistance: true,
        hasAddonExport: true,
        hasAddonScheduling: true,
        hasAddonAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxAddonTypes === -1 || currentUsage < currentFeatures.maxAddonTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'main',
      'aria-label': ariaLabel || `User add-ons with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Add-on management interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive add-on API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getAddonHistory: () => addonHistory,
    getAddonAnalytics: () => addonAnalytics,
    getAddonInsights: () => addonInsights,
    refreshAddons: () => {
      fetchAddonAnalytics();
      if (onRefresh) onRefresh();
    },

    // Add-on methods
    focusAddons: () => {
      if (addonsRef.current) {
        addonsRef.current.focus();
      }
    },
    getInstalledAddons: () => installedAddons,
    getAvailableAddons: () => availableAddons,
    openMarketplace: () => setMarketplaceOpen(true),
    closeMarketplace: () => setMarketplaceOpen(false),
    installAddon: (addonId) => handleAddonInstall(addonId),
    uninstallAddon: (addonId) => handleAddonUninstall(addonId),
    configureAddon: (addonId, config) => handleAddonConfiguration(addonId, config),
    openAddonDrawer: () => setAddonDrawerOpen(true),
    closeAddonDrawer: () => setAddonDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportAddonData: () => {
      if (onExport) {
        onExport(addonHistory, addonAnalytics);
      }
    },

    // Accessibility methods
    announceAddon: (message) => announceToScreenReader(message),
    focusAddonField: () => setFocusToElement('user-addons-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => addonMode,
    getAddonStats: () => addonStats,
    getSelectedAddonType: () => selectedAddonType,
    getCustomAddonConfigs: () => customAddonConfigs,
    getAddonDrawerOpen: () => addonDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    getMarketplaceOpen: () => marketplaceOpen,
    addCustomAddonConfig,
    handleAddonModeChange,
    updateAddonPreferences,
    handleAddonTypeSelection,
    validateAddonConfig,
    searchAddons: (query) => setAddonSearchQuery(query),
    filterAddons: (filters) => setAddonFilters(filters)
  }), [
    addonHistory,
    addonAnalytics,
    addonInsights,
    addonStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    addonMode,
    selectedAddonType,
    customAddonConfigs,
    installedAddons,
    availableAddons,
    addCustomAddonConfig,
    fetchAddonAnalytics,
    handleAddonConfiguration,
    handleAddonInstall,
    handleAddonModeChange,
    handleAddonTypeSelection,
    handleAddonUninstall,
    updateAddonPreferences,
    validateAddonConfig,
    addonDrawerOpen,
    fullscreenMode,
    marketplaceOpen,
    showAnalytics
  ]);

  // Fetch add-on analytics with enhanced error handling and retry logic
  const fetchAddonAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/addons/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setAddonAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (addonPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Add-on analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch add-on analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load add-on analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, addonPreferences.showAnalytics]);

  // Handle add-on mode switching
  const handleAddonModeChange = useCallback((newMode) => {
    if (ADDON_DISPLAY_MODES[newMode.toUpperCase()]) {
      setAddonMode(newMode);
      announceToScreenReader(`Add-on mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setAddonHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (addonPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} add-on mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, addonPreferences.showAnalytics, showSuccess]);

  // Handle custom add-on config management
  const addCustomAddonConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomAddonConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setAddonHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (addonPreferences.showAnalytics) {
      showSuccess(`Custom add-on config "${configData.name}" created`);
    }
  }, [subscription?.user_id, addonPreferences.showAnalytics, showSuccess]);

  // Handle add-on preferences updates
  const updateAddonPreferences = useCallback((newPreferences) => {
    setAddonPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setAddonHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (addonPreferences.showAnalytics) {
      showSuccess('Add-on preferences updated');
    }
  }, [subscription?.user_id, addonPreferences.showAnalytics, showSuccess]);

  // Handle add-on type selection
  const handleAddonTypeSelection = useCallback((addonType) => {
    setSelectedAddonType(addonType);

    // Track add-on type selection
    const typeRecord = {
      id: Date.now(),
      type: 'addon_type_selected',
      addonType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setAddonHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (addonPreferences.showAnalytics) {
      announceToScreenReader(`Selected add-on type: ${addonType}`);
    }
  }, [subscription?.user_id, addonPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateAddonConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Add-on type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Handle add-on installation
  const handleAddonInstall = useCallback(async (addonId) => {
    try {
      await handleApiRequest(
        async () => {
          const response = await api.post(`/api/addons/${addonId}/install`);
          return response.data;
        },
        {
          onSuccess: (data) => {
            setInstalledAddons(prev => [...prev, data]);
            showSuccess('Add-on installed successfully');
            if (onAddonAction) {
              onAddonAction('install', { addonId, data });
            }
          },
          onError: () => {
            showError('Failed to install add-on');
          },
        }
      );
    } catch (error) {
      console.error('Add-on installation error:', error);
      showError('Add-on installation failed');
    }
  }, [handleApiRequest, showSuccess, showError, onAddonAction]);

  // Handle add-on uninstallation
  const handleAddonUninstall = useCallback(async (addonId) => {
    try {
      await handleApiRequest(
        async () => {
          const response = await api.delete(`/api/addons/${addonId}/uninstall`);
          return response.data;
        },
        {
          onSuccess: () => {
            setInstalledAddons(prev => prev.filter(addon => addon.id !== addonId));
            showSuccess('Add-on uninstalled successfully');
            if (onAddonAction) {
              onAddonAction('uninstall', { addonId });
            }
          },
          onError: () => {
            showError('Failed to uninstall add-on');
          },
        }
      );
    } catch (error) {
      console.error('Add-on uninstallation error:', error);
      showError('Add-on uninstallation failed');
    }
  }, [handleApiRequest, showSuccess, showError, onAddonAction]);

  // Handle add-on configuration
  const handleAddonConfiguration = useCallback(async (addonId, config) => {
    try {
      await handleApiRequest(
        async () => {
          const response = await api.put(`/api/addons/${addonId}/configure`, config);
          return response.data;
        },
        {
          onSuccess: (data) => {
            showSuccess('Add-on configured successfully');
            if (onAddonAction) {
              onAddonAction('configure', { addonId, config, data });
            }
          },
          onError: () => {
            showError('Failed to configure add-on');
          },
        }
      );
    } catch (error) {
      console.error('Add-on configuration error:', error);
      showError('Add-on configuration failed');
    }
  }, [handleApiRequest, showSuccess, showError, onAddonAction]);

  // Initial data loading
  useEffect(() => {
    fetchAddonAnalytics();
    fetchAddonInsights();
    fetchAvailableAddons();
  }, [fetchAddonAnalytics, fetchAddonInsights, fetchAvailableAddons]);

  // Fetch add-on insights
  const fetchAddonInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/addons/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setAddonInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch add-on insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Fetch available add-ons
  const fetchAvailableAddons = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/addons/marketplace');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setAvailableAddons(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch available add-ons:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && addons?.length > 0) {
      // Optimize add-on management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchAddonAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, addons?.length, fetchAddonAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastAddonCheck(Date.now());

          if (wasUnavailable && addonPreferences.showAnalytics) {
            showSuccess("Connection restored - Add-on features available");
          }
        } else {
          setBackendAvailable(false);
          if (addonPreferences.showAnalytics) {
            showError("Backend service unavailable - Some add-on features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastAddonCheck;
          if (timeSinceLastCheck > 60000 && addonPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Add-ons may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastAddonCheck, addonPreferences.showAnalytics, showSuccess, showError]);

  // Generate AI suggestions when add-ons change
  useEffect(() => {
    if (enableAIInsights && addonPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, addonPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/addons/ai-suggestions', {
        params: {
          installedAddons: installedAddons.length,
          userPreferences: addonPreferences
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (addonPreferences.showAnalytics) {
        showSuccess('AI suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [installedAddons.length, addonPreferences, showSuccess, showError]);

  // Fetch stats when add-ons change
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchAddonStats();
    }
  }, [enableAdvancedFeatures, fetchAddonStats]);

  // Fetch add-on stats function
  const fetchAddonStats = useCallback(async () => {
    try {
      const response = await api.get('/api/addons/stats');
      setAddonStats(response.data);
    } catch (error) {
      console.error('Failed to fetch add-on stats:', error);
    }
  }, []);

  if (!addons || addons.length === 0) {
    return (
      <Box
        {...getAccessibilityProps()}
        ref={addonsRef}
        sx={{
          ...sx,
          ...customization
        }}
        className={className}
        style={style}
        data-testid={testId}
      >
        <GlassmorphicCard variant="glass">
          <CardContent>
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                No Add-ons Purchased
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                You haven&apos;t purchased any add-ons yet.
              </Typography>
              <Button
                component={RouterLink}
                to="/billing/addons"
                variant="contained"
                sx={{
                  mt: 2,
                  bgcolor: ACE_COLORS.PURPLE,
                  '&:hover': {
                    bgcolor: alpha(ACE_COLORS.PURPLE, 0.8)
                  }
                }}
                startIcon={<ShoppingCartIcon />}
              >
                Browse Add-ons
              </Button>
            </Box>
          </CardContent>
        </GlassmorphicCard>

        {/* Notification Snackbar */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert
            severity={notification.severity}
            onClose={() => setNotification(prev => ({ ...prev, open: false }))}
            sx={{
              bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
              color: ACE_COLORS.DARK,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>

        {/* Retry Count Indicator */}
        {retryCount > 0 && (
          <Box sx={{
            position: 'fixed',
            top: 20,
            right: 20,
            p: 1,
            borderRadius: 1,
            bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
            border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
            zIndex: 9999
          }}>
            <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
              Retrying add-on sync... (Attempt {retryCount}/3)
            </Typography>
          </Box>
        )}
      </Box>
    );
  }

  return (
    <Box
      {...getAccessibilityProps()}
      ref={addonsRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      <GlassmorphicCard variant="glass">
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Your Add-ons
            </Typography>
            <Button
              component={RouterLink}
              to="/billing/addons"
              variant="outlined"
              size="small"
              startIcon={<AddCircleIcon />}
              sx={{
                borderColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  borderColor: alpha(ACE_COLORS.PURPLE, 0.8),
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              Get More Add-ons
            </Button>
          </Box>

          <Grid container spacing={isMobile ? 1 : 2}>
            {filteredAddons.map((addon) => (
              <Grid item xs={12} sm={isEmbedded ? 12 : 6} md={isEmbedded ? 12 : 4} key={`${addon.id}-${addon.purchase_date}`}>
                <AddonCard addon={addon} isEmbedded={isEmbedded} isMobile={isMobile} />
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </GlassmorphicCard>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying add-on sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
UserAddons.propTypes = {
  // Core props
  isEmbedded: PropTypes.bool,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onAddonAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

UserAddons.displayName = 'UserAddons';

/**
 * Component to display a single add-on card
 */
const AddonCard = ({ addon, isEmbedded = false, isMobile = false }) => {

  // Format dates
  const purchaseDate = addon.purchase_date ? new Date(addon.purchase_date) : null;
  const expiresAt = addon.expires_at ? new Date(addon.expires_at) : null;

  // Calculate remaining time for expiring add-ons
  const isExpiring = expiresAt && expiresAt < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

  // Calculate usage percentage for consumable add-ons
  const hasUsageLimit = addon.remaining_uses !== null && addon.remaining_uses !== undefined;
  const usagePercentage = hasUsageLimit ?
    Math.max(0, Math.min(100, (addon.remaining_uses / addon.quantity) * 100)) :
    null;

  // Determine status chip color and label
  let statusChip = null;
  if (addon.is_recurring) {
    statusChip = { label: 'Recurring', color: 'success' };
  } else if (hasUsageLimit && addon.remaining_uses <= 0) {
    statusChip = { label: 'Depleted', color: 'error' };
  } else if (isExpiring) {
    statusChip = { label: 'Expiring Soon', color: 'warning' };
  } else if (expiresAt) {
    statusChip = { label: 'Limited Time', color: 'info' };
  } else {
    statusChip = { label: 'Active', color: 'success' };
  }

  return (
    <Card
      sx={{
        p: isMobile ? 0.5 : 1,
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        boxShadow: `0 4px 20px ${alpha(ACE_COLORS.DARK, 0.1)}`,
        background: `linear-gradient(145deg, ${alpha(ACE_COLORS.WHITE, 0.8)}, ${alpha(ACE_COLORS.WHITE, 0.6)})`,
        backdropFilter: 'blur(10px)',
        transition: 'all 0.3s ease',
        height: isEmbedded ? 'auto' : '100%',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: `0 6px 25px ${alpha(ACE_COLORS.DARK, 0.15)}`,
        }
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h6" gutterBottom>
              {addon.name}
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Chip
                size="small"
                label={statusChip.label}
                color={statusChip.color}
                sx={{ mr: 1 }}
              />

              {addon.quantity > 1 && (
                <Chip
                  size="small"
                  label={`Quantity: ${addon.quantity}`}
                  variant="outlined"
                />
              )}
            </Box>

            {purchaseDate && (
              <Typography variant="body2" color="text.secondary">
                Purchased: {format(purchaseDate, 'MMM d, yyyy')}
              </Typography>
            )}

            {expiresAt && (
              <Typography
                variant="body2"
                color={isExpiring ? 'error.main' : 'text.secondary'}
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                {isExpiring && <WarningIcon fontSize="small" sx={{ mr: 0.5 }} />}
                Expires: {format(expiresAt, 'MMM d, yyyy')} ({formatDistanceToNow(expiresAt, { addSuffix: true })})
              </Typography>
            )}
          </Box>

          {addon.is_recurring && (
            <Tooltip title="Recurring subscription">
              <AccessTimeIcon color="success" />
            </Tooltip>
          )}
        </Box>

        {hasUsageLimit && (
          <Box sx={{ mt: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography variant="body2">
                Remaining: {addon.remaining_uses} of {addon.quantity}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {Math.round(usagePercentage)}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={usagePercentage}
              color={usagePercentage < 20 ? 'error' : 'primary'}
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default UserAddons;

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import BrandVisualStyleView from '../BrandVisualStyleView';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#1976d2',
      },
      secondary: {
        main: '#dc004e',
      },
      error: {
        main: '#d32f2f',
      },
      warning: {
        main: '#ed6c02',
      },
      success: {
        main: '#2e7d32',
      },
      grey: {
        300: '#e0e0e0',
        400: '#bdbdbd',
        500: '#9e9e9e',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('BrandVisualStyleView', () => {
  const mockVisualStyle = {
    photography_style: 'lifestyle',
    lighting: 'bright-airy',
    saturation: 10,
    contrast: 5,
    brightness: 0,
    warmth: -5,
    filters: [
      {
        name: 'Brand Filter',
        settings: {
          saturation: 10,
          contrast: 5,
          brightness: 0,
          warmth: -5
        }
      }
    ]
  };

  const mockProps = {
    visualStyle: mockVisualStyle,
    onError: vi.fn(),
    onStyleChange: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders visual style view correctly', () => {
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Visual Style Settings')).toBeInTheDocument();
    expect(screen.getByText('Photography Style')).toBeInTheDocument();
    expect(screen.getByText('Image Adjustments')).toBeInTheDocument();
    expect(screen.getByText('Visual Style Preview')).toBeInTheDocument();
  });

  test('displays photography style and lighting correctly', () => {
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Lifestyle')).toBeInTheDocument();
    expect(screen.getByText('Bright & Airy')).toBeInTheDocument();
  });

  test('shows placeholder when no visual style provided', () => {
    render(
      <TestWrapper>
        <BrandVisualStyleView visualStyle={null} />
      </TestWrapper>
    );

    expect(screen.getByText('No visual style defined')).toBeInTheDocument();
    expect(screen.getByText('Configure visual style settings to see the preview and controls')).toBeInTheDocument();
  });

  test('displays adjustment values correctly', () => {
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('+10')).toBeInTheDocument(); // Saturation
    expect(screen.getByText('+5')).toBeInTheDocument(); // Contrast
    expect(screen.getByText('0')).toBeInTheDocument(); // Brightness
    expect(screen.getByText('-5')).toBeInTheDocument(); // Warmth
  });

  test('displays saved filters when available', () => {
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Saved Filters (1)')).toBeInTheDocument();
    expect(screen.getByText('Brand Filter')).toBeInTheDocument();
  });

  test('handles export functionality', async () => {
    // Mock URL.createObjectURL and related APIs
    global.URL.createObjectURL = vi.fn(() => 'mock-url');
    global.URL.revokeObjectURL = vi.fn();
    
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    
    vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});

    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export visual style settings');
    await user.click(exportButton);

    await waitFor(() => {
      expect(mockProps.onExport).toHaveBeenCalled();
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Visual style exported successfully');
    });
  });

  test('opens fullscreen preview dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} />
      </TestWrapper>
    );

    const fullscreenButton = screen.getByLabelText('Open fullscreen preview');
    await user.click(fullscreenButton);

    await waitFor(() => {
      expect(screen.getByText('Visual Style Preview')).toBeInTheDocument();
    });
  });

  test('handles preview settings changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} />
      </TestWrapper>
    );

    // Test grid toggle
    const gridSwitch = screen.getByRole('checkbox', { name: /grid/i });
    await user.click(gridSwitch);

    // Test original toggle
    const originalSwitch = screen.getByRole('checkbox', { name: /original/i });
    await user.click(originalSwitch);

    // Test compare mode toggle
    const compareSwitch = screen.getByRole('checkbox', { name: /compare/i });
    await user.click(compareSwitch);

    // Verify switches are checked
    expect(gridSwitch).toBeChecked();
    expect(originalSwitch).toBeChecked();
    expect(compareSwitch).toBeChecked();
  });

  test('handles editing mode with style changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} allowEditing />
      </TestWrapper>
    );

    // Test photography style change
    const photographySelect = screen.getByRole('combobox', { name: /photography style/i });
    await user.click(photographySelect);
    
    const productOption = screen.getByText('Product');
    await user.click(productOption);

    await waitFor(() => {
      expect(mockProps.onStyleChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        photography_style: 'product'
      });
    });
  });

  test('handles adjustment changes in editing mode', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} allowEditing />
      </TestWrapper>
    );

    // Expand adjustments section
    const adjustmentsAccordion = screen.getByText('Adjustment Controls');
    await user.click(adjustmentsAccordion);

    // Find saturation slider and change value
    const saturationSlider = screen.getByLabelText('saturation adjustment');
    fireEvent.change(saturationSlider, { target: { value: 20 } });

    await waitFor(() => {
      expect(mockProps.onStyleChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 20
      });
    });
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} disabled />
      </TestWrapper>
    );

    const exportButton = screen.getByLabelText('Export visual style settings');
    const fullscreenButton = screen.getByLabelText('Open fullscreen preview');

    expect(exportButton).toBeDisabled();
    expect(fullscreenButton).toBeDisabled();
  });

  test('handles validation errors', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} allowEditing />
      </TestWrapper>
    );

    // Expand adjustments section
    const adjustmentsAccordion = screen.getByText('Adjustment Controls');
    await user.click(adjustmentsAccordion);

    // Set invalid value (beyond range)
    const saturationSlider = screen.getByLabelText('saturation adjustment');
    fireEvent.change(saturationSlider, { target: { value: 150 } });

    await waitFor(() => {
      // Check if validation error appears (the component should prevent invalid values)
      expect(mockProps.onStyleChange).not.toHaveBeenCalledWith(
        expect.objectContaining({ saturation: 150 })
      );
    });
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Export visual style settings')).toBeInTheDocument();
    expect(screen.getByLabelText('Open fullscreen preview')).toBeInTheDocument();

    // Check for proper headings
    const headings = screen.getAllByRole('heading');
    expect(headings.length).toBeGreaterThan(0);
  });

  test('handles preview without showPreview prop', () => {
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} showPreview={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Visual Style Preview')).not.toBeInTheDocument();
  });

  test('handles visual style without filters', () => {
    const visualStyleWithoutFilters = {
      ...mockVisualStyle,
      filters: []
    };

    render(
      <TestWrapper>
        <BrandVisualStyleView visualStyle={visualStyleWithoutFilters} />
      </TestWrapper>
    );

    expect(screen.queryByText('Saved Filters')).not.toBeInTheDocument();
  });

  test('displays different photography styles correctly', () => {
    const corporateVisualStyle = {
      ...mockVisualStyle,
      photography_style: 'corporate'
    };

    render(
      <TestWrapper>
        <BrandVisualStyleView visualStyle={corporateVisualStyle} />
      </TestWrapper>
    );

    expect(screen.getByText('Corporate')).toBeInTheDocument();
  });

  test('handles reset adjustments functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandVisualStyleView {...mockProps} allowEditing />
      </TestWrapper>
    );

    const resetButton = screen.getByLabelText('Reset adjustments');
    await user.click(resetButton);

    await waitFor(() => {
      expect(mockProps.onStyleChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 0,
        contrast: 0,
        brightness: 0,
        warmth: 0
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Adjustments reset to default values');
    });
  });
});

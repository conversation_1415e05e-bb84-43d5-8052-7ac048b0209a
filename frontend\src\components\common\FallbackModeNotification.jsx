/**
 * Fallback Mode Notification Component
 * Displays a notification when the app is running in fallback mode
 */

import React, { useState, useEffect } from 'react';
import {
  <PERSON>ert,
  <PERSON>ertTitle,
  Collapse,
  IconButton,
  Box
} from '@mui/material';
import {
  Close as CloseIcon,
  Wifi as WifiIcon,
  WifiOff as WifiOffIcon
} from '@mui/icons-material';
import { shouldUseFallbackMode } from '../../utils/environmentDetection.js';

const FallbackModeNotification = () => {
  const [show, setShow] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  useEffect(() => {
    try {
      // Check if we should show the fallback notification
      const useFallback = shouldUseFallbackMode();
      const hasBeenDismissed = typeof localStorage !== 'undefined' &&
        localStorage.getItem('fallback-notification-dismissed') === 'true';

      if (useFallback && !hasBeenDismissed) {
        setShow(true);
      }
    } catch (error) {
      console.warn('Error checking fallback notification status:', error);
    }
  }, []);

  const handleDismiss = () => {
    setShow(false);
    setDismissed(true);
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('fallback-notification-dismissed', 'true');
      }
    } catch (error) {
      console.warn('Error saving fallback notification dismissal:', error);
    }
  };

  if (!show || dismissed) {
    return null;
  }

  return (
    <Box sx={{ position: 'fixed', top: 16, right: 16, zIndex: 9999, maxWidth: 400 }}>
      <Collapse in={show}>
        <Alert
          severity="info"
          icon={<WifiOffIcon />}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={handleDismiss}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          sx={{
            boxShadow: 3,
            borderRadius: 2
          }}
        >
          <AlertTitle>Limited Connectivity Mode</AlertTitle>
          The app is running in fallback mode with reduced real-time features. 
          Some updates may be delayed, but all core functionality remains available.
        </Alert>
      </Collapse>
    </Box>
  );
};

export default FallbackModeNotification;

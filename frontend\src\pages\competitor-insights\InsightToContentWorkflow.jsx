// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  CircularProgress,
  Divider,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
  CardContent,
  CardActions,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import {
  Refresh as RefreshIcon,
  ArrowForward as ArrowForwardIcon,
  ArrowBack as ArrowBackIcon,
  Schedule as ScheduleIcon,
  Save as SaveIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  LinkedIn as LinkedInIcon,
  YouTube as YouTubeIcon,
  Slideshow as TikTokIcon
} from '@mui/icons-material';
import { useCompetitorInsights } from '../../contexts/CompetitorInsightsContext';
import { useNotification } from '../../hooks/useNotification';
import GlassmorphicCard from '../../components/common/GlassmorphicCard';
import { useNavigate } from 'react-router-dom';


import * as contentApi from '../../api/content';

const InsightToContentWorkflow = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const {
    actionableInsights,
    selectedCompetitors,
    generateActionableInsights,
    loading
  } = useCompetitorInsights();

  const [activeStep, setActiveStep] = useState(0);
  const [selectedInsight, setSelectedInsight] = useState(null);
  const [contentBrief, setContentBrief] = useState({
    topic: '',
    tone: 'professional',
    platform: 'linkedin',
    content_type: 'post',
    include_hashtags: true,
    target_audience: '',
    generate_image: true,
    include_headline_on_image: true
  });
  const [generatedContent, setGeneratedContent] = useState(null);
  const [generatingContent, setGeneratingContent] = useState(false);
  const [schedulingContent, setSchedulingContent] = useState(false);

  // Load actionable insights when component mounts or when selection changes
  useEffect(() => {
    if (selectedCompetitors.length > 0 && (!actionableInsights || actionableInsights.length === 0)) {
      generateActionableInsights();
    }
  }, [selectedCompetitors, actionableInsights, generateActionableInsights]);

  // Handle refresh
  const handleRefresh = () => {
    generateActionableInsights();
  };

  // Handle step navigation
  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Handle insight selection
  const handleSelectInsight = (insight) => {
    setSelectedInsight(insight);
    setContentBrief({
      ...contentBrief,
      topic: insight.suggestedTopic,
      platform: insight.targetPlatform,
      target_audience: insight.targetAudience,
      tone: insight.suggestedTone || 'professional'
    });
    handleNext();
  };

  // Handle content brief changes
  const handleBriefChange = (event) => {
    const { name, value, checked, type } = event.target;
    setContentBrief({
      ...contentBrief,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Generate content from brief
  const handleGenerateContent = async () => {
    setGeneratingContent(true);
    try {
      const content = await contentApi.generateContent(contentBrief);
      setGeneratedContent(content);
      handleNext();
      showSuccessNotification('Content generated successfully');
    } catch (err) {
      console.error('Error generating content:', err);
      showErrorNotification('Failed to generate content');
    } finally {
      setGeneratingContent(false);
    }
  };

  // Schedule content
  const handleScheduleContent = async () => {
    setSchedulingContent(true);
    try {
      await contentApi.scheduleContent({
        content: generatedContent.content,
        image_url: generatedContent.image_url,
        platform: contentBrief.platform,
        scheduled_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Schedule for tomorrow
        hashtags: generatedContent.hashtags
      });
      showSuccessNotification('Content scheduled successfully');
      navigate('/scheduling');
    } catch (err) {
      console.error('Error scheduling content:', err);
      showErrorNotification('Failed to schedule content');
    } finally {
      setSchedulingContent(false);
    }
  };

  // Save as draft
  const handleSaveAsDraft = async () => {
    try {
      await contentApi.saveContentDraft({
        content: generatedContent.content,
        image_url: generatedContent.image_url,
        platform: contentBrief.platform,
        hashtags: generatedContent.hashtags,
        topic: contentBrief.topic,
        insight_id: selectedInsight?.id
      });
      showSuccessNotification('Content saved as draft');
      navigate('/content');
    } catch (err) {
      console.error('Error saving content as draft:', err);
      showErrorNotification('Failed to save content as draft');
    }
  };

  // Get platform icon
  const getPlatformIcon = (platform) => {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return <FacebookIcon />;
      case 'twitter':
        return <TwitterIcon />;
      case 'instagram':
        return <InstagramIcon />;
      case 'linkedin':
        return <LinkedInIcon />;
      case 'youtube':
        return <YouTubeIcon />;
      case 'tiktok':
        return <TikTokIcon />;
      default:
        return null;
    }
  };

  // Render step content
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Select an Insight to Create Content
            </Typography>
            
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                <CircularProgress />
              </Box>
            ) : !actionableInsights || actionableInsights.length === 0 ? (
              <GlassmorphicCard>
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="h6" gutterBottom>
                    No actionable insights available
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<RefreshIcon />}
                    onClick={handleRefresh}
                    sx={{ mt: 2 }}
                  >
                    Generate Insights
                  </Button>
                </Box>
              </GlassmorphicCard>
            ) : (
              <Grid container spacing={3}>
                {actionableInsights.map((insight) => (
                  <Grid item xs={12} sm={6} md={4} key={insight.id}>
                    <GlassmorphicCard 
                      sx={{ 
                        height: '100%', 
                        display: 'flex', 
                        flexDirection: 'column',
                        cursor: 'pointer',
                        transition: 'transform 0.2s',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: theme.shadows[8]
                        }
                      }}
                      onClick={() => handleSelectInsight(insight)}
                    >
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Chip 
                            label={insight.insightType} 
                            size="small" 
                            color="primary" 
                            variant="outlined"
                          />
                          <Tooltip title={insight.targetPlatform}>
                            <Box component="span">
                              {getPlatformIcon(insight.targetPlatform)}
                            </Box>
                          </Tooltip>
                        </Box>
                        
                        <Typography variant="h6" gutterBottom>
                          {insight.suggestedTopic}
                        </Typography>
                        
                        <Typography variant="body2" color="text.secondary">
                          {insight.description}
                        </Typography>
                        
                        {insight.competitorReference && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="caption" color="text.secondary">
                              Based on competitor: {insight.competitorReference}
                            </Typography>
                          </Box>
                        )}
                      </CardContent>
                      
                      <CardActions sx={{ justifyContent: 'flex-end', p: 2, pt: 0 }}>
                        <Button
                          size="small"
                          endIcon={<ArrowForwardIcon />}
                        >
                          Select
                        </Button>
                      </CardActions>
                    </GlassmorphicCard>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        );
      
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Customize Content Brief
            </Typography>
            
            <GlassmorphicCard sx={{ mb: 3 }}>
              <Box sx={{ p: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Selected Insight:
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedInsight?.suggestedTopic}
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Topic"
                      name="topic"
                      value={contentBrief.topic}
                      onChange={handleBriefChange}
                      variant="outlined"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Platform</InputLabel>
                      <Select
                        name="platform"
                        value={contentBrief.platform}
                        onChange={handleBriefChange}
                        label="Platform"
                      >
                        <MenuItem value="facebook">Facebook</MenuItem>
                        <MenuItem value="twitter">Twitter</MenuItem>
                        <MenuItem value="instagram">Instagram</MenuItem>
                        <MenuItem value="linkedin">LinkedIn</MenuItem>
                        <MenuItem value="youtube">YouTube</MenuItem>
                        <MenuItem value="tiktok">TikTok</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Tone</InputLabel>
                      <Select
                        name="tone"
                        value={contentBrief.tone}
                        onChange={handleBriefChange}
                        label="Tone"
                      >
                        <MenuItem value="professional">Professional</MenuItem>
                        <MenuItem value="conversational">Conversational</MenuItem>
                        <MenuItem value="enthusiastic">Enthusiastic</MenuItem>
                        <MenuItem value="informative">Informative</MenuItem>
                        <MenuItem value="authoritative">Authoritative</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Target Audience"
                      name="target_audience"
                      value={contentBrief.target_audience}
                      onChange={handleBriefChange}
                      variant="outlined"
                    />
                  </Grid>
                </Grid>
              </Box>
            </GlassmorphicCard>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button
                onClick={handleBack}
                startIcon={<ArrowBackIcon />}
              >
                Back
              </Button>
              <LoadingButton
                variant="contained"
                onClick={handleGenerateContent}
                loading={generatingContent}
                endIcon={<ArrowForwardIcon />}
              >
                Generate Content
              </LoadingButton>
            </Box>
          </Box>
        );
      
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review and Publish Content
            </Typography>
            
            <GlassmorphicCard sx={{ mb: 3 }}>
              <Box sx={{ p: 3 }}>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle1" gutterBottom>
                      Generated Content:
                    </Typography>
                    <Paper 
                      elevation={0} 
                      sx={{ 
                        p: 2, 
                        backgroundColor: alpha(theme.palette.background.paper, 0.5),
                        minHeight: 200
                      }}
                    >
                      <Typography variant="body1">
                        {generatedContent?.content}
                      </Typography>
                      
                      {generatedContent?.hashtags && generatedContent.hashtags.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                          {generatedContent.hashtags.map((tag, index) => (
                            <Chip 
                              key={index} 
                              label={tag} 
                              size="small" 
                              sx={{ mr: 0.5, mb: 0.5 }} 
                            />
                          ))}
                        </Box>
                      )}
                    </Paper>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    {generatedContent?.image_url ? (
                      <Box>
                        <Typography variant="subtitle1" gutterBottom>
                          Generated Image:
                        </Typography>
                        <Box 
                          component="img" 
                          src={generatedContent.image_url} 
                          alt="Generated content image"
                          sx={{ 
                            width: '100%', 
                            maxHeight: 300, 
                            objectFit: 'contain',
                            borderRadius: 1
                          }}
                        />
                      </Box>
                    ) : (
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        <Typography variant="body2" color="text.secondary">
                          No image generated
                        </Typography>
                      </Box>
                    )}
                  </Grid>
                </Grid>
              </Box>
            </GlassmorphicCard>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button
                onClick={handleBack}
                startIcon={<ArrowBackIcon />}
              >
                Back
              </Button>
              <Box>
                <Button
                  variant="outlined"
                  onClick={handleSaveAsDraft}
                  startIcon={<SaveIcon />}
                  sx={{ mr: 2 }}
                >
                  Save as Draft
                </Button>
                <LoadingButton
                  variant="contained"
                  onClick={handleScheduleContent}
                  loading={schedulingContent}
                  endIcon={<ScheduleIcon />}
                >
                  Schedule Post
                </LoadingButton>
              </Box>
            </Box>
          </Box>
        );
      
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      {/* Workflow Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Create Content from Competitor Insights
        </Typography>
        {activeStep === 0 && (
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            Refresh Insights
          </Button>
        )}
      </Box>

      {/* Stepper */}
      <GlassmorphicCard sx={{ mb: 3 }}>
        <Box sx={{ p: 2 }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            <Step>
              <StepLabel>Select Insight</StepLabel>
            </Step>
            <Step>
              <StepLabel>Customize Brief</StepLabel>
            </Step>
            <Step>
              <StepLabel>Review & Publish</StepLabel>
            </Step>
          </Stepper>
        </Box>
      </GlassmorphicCard>

      {/* Step Content */}
      <Box sx={{ mt: 3 }}>
        {getStepContent(activeStep)}
      </Box>
    </Box>
  );
};

export default InsightToContentWorkflow;

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Grid,
  Divider,
  Switch,
  FormControlLabel,
  Button,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
} from "@mui/material";
import AvatarUploader from "../../components/common/AvatarUploader";
import {
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Devices as DevicesIcon,
  Storage as StorageIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  Tune as TuneIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountCircleIcon,
  CreditCard as CreditCardIcon,
} from "@mui/icons-material";
import { Link as RouterLink } from "react-router-dom";
import { useAuth } from "../../contexts/AuthContext";
import { useNotification } from "../../hooks/useNotification";
import { useSettings } from "../../contexts/SettingsContext";
import api from "../../api";
import GlassmorphicCard from "../../components/common/GlassmorphicCard";
import NotificationSettings from "../../components/settings/NotificationSettings";

/**
 * Settings component provides a comprehensive interface for managing user preferences,
 * application settings, and account configuration with advanced features.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isEmbedded - Whether the component is embedded in another page
 */
const Settings = ({ isEmbedded = false }) => {
  const { user, logout, updateProfile } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const {
    contentSettings,
    updateContentSettings,
  } = useSettings();

  // State for tabs
  const [tabValue, setTabValue] = useState(0);

  // State for loading
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // State for avatar upload
  const [avatarLoading, setAvatarLoading] = useState(false);

  // State for content settings
  const [localContentSettings, setLocalContentSettings] = useState({
    defaultPlatform: "linkedin",
    defaultTone: "professional",
    defaultContentType: "post",
    defaultLength: "medium",
    includeHashtags: true,
  });

  // State for session management
  const [sessions, setSessions] = useState([]);
  const [sessionsLoading, setSessionsLoading] = useState(false);

  // Load settings when component mounts
  useEffect(() => {
    setLocalContentSettings(contentSettings);
    fetchSessions();
  }, [contentSettings]);



  // Handle content settings change
  const handleContentSettingsChange = (e) => {
    const { name, value, checked } = e.target;
    setLocalContentSettings((prev) => ({
      ...prev,
      [name]: name === "includeHashtags" ? checked : value,
    }));

    // Reset status messages
    setError(null);
    setSuccess(false);
  };

  // Handle save content settings
  const handleSaveContentSettings = async () => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Update settings in context
      updateContentSettings(localContentSettings);

      // Save to backend using user preferences endpoint
      await api.put("/api/user-preferences", {
        content_settings: localContentSettings,
      });

      setSuccess(true);
      showSuccessNotification("Content settings saved successfully");
    } catch (err) {
      console.error("Error saving content settings:", err);
      if (err.response?.status === 400) {
        setError(err.response?.data?.detail || "Invalid settings data");
      } else if (err.response?.status === 401) {
        setError("Authentication required. Please log in again.");
      } else if (err.response?.status === 422) {
        setError("Settings validation failed. Please check your input.");
      } else {
        setError(err.response?.data?.detail || "Failed to save content settings");
      }
      showErrorNotification("Failed to save content settings");
    } finally {
      setLoading(false);
    }
  };

  // Fetch active sessions
  const fetchSessions = async () => {
    setSessionsLoading(true);

    try {
      const response = await api.get("/api/users/sessions");
      setSessions(response.data || []);
    } catch (err) {
      console.error("Error fetching sessions:", err);
      if (err.response?.status === 401) {
        showErrorNotification("Authentication required to view sessions");
      } else if (err.response?.status === 500) {
        // Don't show error notification for server errors as it's secondary information
        setSessions([]);
      } else {
        // For other errors, show a generic message
        setSessions([]);
      }
    } finally {
      setSessionsLoading(false);
    }
  };

  // Handle revoke session
  const handleRevokeSession = async (sessionId) => {
    try {
      await api.delete(`/api/users/sessions/${sessionId}`);

      // Refresh sessions
      fetchSessions();

      showSuccessNotification("Session revoked successfully");
    } catch (err) {
      console.error("Error revoking session:", err);
      if (err.response?.status === 404) {
        showErrorNotification("Session not found or already revoked");
        // Refresh sessions to update the list
        fetchSessions();
      } else if (err.response?.status === 401) {
        showErrorNotification("Authentication required to revoke session");
      } else {
        showErrorNotification(err.response?.data?.detail || "Failed to revoke session");
      }
    }
  };

  // Handle revoke all sessions
  const handleRevokeAllSessions = async () => {
    if (!window.confirm("Are you sure you want to revoke all sessions? This will log you out from all other devices.")) {
      return;
    }

    try {
      const response = await api.delete("/api/users/sessions");

      const revokedCount = response.data?.revoked_count || 0;
      showSuccessNotification(`${revokedCount} sessions revoked successfully.`);

      // Refresh sessions to show updated list
      fetchSessions();
    } catch (err) {
      console.error("Error revoking all sessions:", err);
      if (err.response?.status === 401) {
        showErrorNotification("Authentication required to revoke sessions");
      } else {
        showErrorNotification(err.response?.data?.detail || "Failed to revoke all sessions");
      }
    }
  };

  // Handle avatar change
  const handleAvatarChange = async (file) => {
    if (!file) return;

    // Upload avatar
    setAvatarLoading(true);

    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await api.post('/api/users/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Update user profile with new avatar URLs
      // Handle different response formats from the backend
      const avatarUrl = response.data.avatar_url || response.data.default_url || response.data.avatar;
      const avatarUrls = response.data.avatar_urls || response.data.urls || null;

      await updateProfile({
        avatar: avatarUrl,
        avatar_urls: avatarUrls
      });

      showSuccessNotification('Avatar updated successfully');
    } catch (err) {
      console.error('Error uploading avatar:', err);
      if (err.response?.status === 400) {
        showErrorNotification(err.response?.data?.detail || 'Invalid file type or size');
      } else if (err.response?.status === 413) {
        showErrorNotification('File too large. Please choose a smaller image.');
      } else {
        showErrorNotification(err.response?.data?.detail || 'Failed to upload avatar');
      }
    } finally {
      setAvatarLoading(false);
    }
  };

  // Handle remove avatar
  const handleRemoveAvatar = async () => {
    setAvatarLoading(true);

    try {
      await api.delete('/api/users/avatar');

      // Update user profile
      await updateProfile({
        avatar: null,
      });

      showSuccessNotification('Avatar removed successfully');
    } catch (err) {
      console.error('Error removing avatar:', err);
      if (err.response?.status === 404) {
        showErrorNotification('No avatar to remove');
      } else {
        showErrorNotification(err.response?.data?.detail || 'Failed to remove avatar');
      }
    } finally {
      setAvatarLoading(false);
    }
  };

  // Handle delete account
  const handleDeleteAccount = async () => {
    const confirmText = "DELETE";
    const userInput = window.prompt(
      `This action cannot be undone. All your data will be permanently deleted.\n\nType "${confirmText}" to confirm account deletion:`
    );

    if (userInput !== confirmText) {
      if (userInput !== null) {
        showErrorNotification("Account deletion cancelled - confirmation text did not match");
      }
      return;
    }

    try {
      // Use the correct endpoint for user deletion
      await api.delete("/api/users/me");

      // Log the user out
      logout();

      showSuccessNotification("Your account has been deleted");
    } catch (err) {
      console.error("Error deleting account:", err);
      if (err.response?.status === 403) {
        showErrorNotification("Not authorized to delete account");
      } else if (err.response?.status === 404) {
        showErrorNotification("Account not found");
      } else {
        showErrorNotification(err.response?.data?.detail || "Failed to delete account");
      }
    }
  };

  // Handle data export
  const handleDataExport = async () => {
    try {
      const response = await api.get("/api/users/export", {
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `user-data-${new Date().toISOString().split('T')[0]}.json`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      showSuccessNotification("Data export completed successfully");
    } catch (err) {
      console.error("Error exporting data:", err);
      if (err.response?.status === 404) {
        showErrorNotification("No data found to export");
      } else {
        showErrorNotification(err.response?.data?.detail || "Failed to export data");
      }
    }
  };

  // Handle data deletion
  const handleDataDeletion = async () => {
    const confirmText = "DELETE MY DATA";
    const userInput = window.prompt(
      `This will permanently delete all your content data while keeping your account active.\n\nType "${confirmText}" to confirm:`
    );

    if (userInput !== confirmText) {
      if (userInput !== null) {
        showErrorNotification("Data deletion cancelled - confirmation text did not match");
      }
      return;
    }

    try {
      await api.delete("/api/users/data");
      showSuccessNotification("Your data has been deleted successfully");
    } catch (err) {
      console.error("Error deleting data:", err);
      showErrorNotification(err.response?.data?.detail || "Failed to delete data");
    }
  };

  return (
    <Box sx={{ p: isEmbedded ? 0 : 3 }}>
      {!isEmbedded && (
        <Typography variant="h4" gutterBottom>
          <SettingsIcon sx={{ mr: 1, verticalAlign: "middle" }} />
          Account Settings
        </Typography>
      )}

      <Grid container spacing={3}>
        {/* Settings sidebar - only shown when not embedded */}
        {!isEmbedded && (
          <Grid item xs={12} md={3}>
            <GlassmorphicCard variant="glass" style={{ marginBottom: "10px" }}>
              <List component="nav">
                <ListItem
                  button
                  selected={tabValue === 0}
                  onClick={() => setTabValue(0)}
                >
                  <ListItemIcon>
                    <TuneIcon />
                  </ListItemIcon>
                  <ListItemText primary="General" />
                </ListItem>

                <ListItem
                  button
                  selected={tabValue === 1}
                  onClick={() => setTabValue(1)}
                >
                  <ListItemIcon>
                    <NotificationsIcon />
                  </ListItemIcon>
                  <ListItemText primary="Notifications" />
                </ListItem>

                <ListItem
                  button
                  selected={tabValue === 2}
                  onClick={() => setTabValue(2)}
                >
                  <ListItemIcon>
                    <SecurityIcon />
                  </ListItemIcon>
                  <ListItemText primary="Security" />
                </ListItem>

                <ListItem
                  button
                  selected={tabValue === 3}
                  onClick={() => setTabValue(3)}
                >
                  <ListItemIcon>
                    <AccountCircleIcon />
                  </ListItemIcon>
                  <ListItemText primary="Account" />
                </ListItem>
              </List>
            </GlassmorphicCard>

            <GlassmorphicCard variant="glass">
              <Box sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Quick Links
                </Typography>

                <Button
                  component={RouterLink}
                  to="/profile"
                  variant="outlined"
                  fullWidth
                  sx={{ mb: 1 }}
                >
                  My Profile
                </Button>

                <Button
                  component={RouterLink}
                  to="/settings?tab=integrations"
                  variant="outlined"
                  fullWidth
                  sx={{ mb: 1 }}
                >
                  Integrations
                </Button>

                <Button
                  component={RouterLink}
                  to="/billing?view=plans"
                  variant="outlined"
                  fullWidth
                >
                  Subscription & Billing
                </Button>
              </Box>
            </GlassmorphicCard>
          </Grid>
        )}

        {/* Settings content */}
        <Grid item xs={12} md={isEmbedded ? 12 : 9}>
          {/* General Settings */}
          {tabValue === 0 && (
            <GlassmorphicCard variant="glass">
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  <TuneIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  General Settings
                </Typography>

                <Divider sx={{ mb: 3 }} />

                {success && (
                  <Alert severity="success" sx={{ mb: 3 }}>
                    Settings saved successfully!
                  </Alert>
                )}

                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <Grid container spacing={3}>
                  {/* Content settings */}
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" gutterBottom>
                      <TuneIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                      Content Generation Defaults
                    </Typography>

                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth margin="normal">
                          <InputLabel>Default Platform</InputLabel>
                          <Select
                            name="defaultPlatform"
                            value={localContentSettings.defaultPlatform}
                            onChange={handleContentSettingsChange}
                            label="Default Platform"
                          >
                            <MenuItem value="linkedin">LinkedIn</MenuItem>
                            <MenuItem value="twitter">Twitter</MenuItem>
                            <MenuItem value="facebook">Facebook</MenuItem>
                            <MenuItem value="instagram">Instagram</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth margin="normal">
                          <InputLabel>Default Tone</InputLabel>
                          <Select
                            name="defaultTone"
                            value={localContentSettings.defaultTone}
                            onChange={handleContentSettingsChange}
                            label="Default Tone"
                          >
                            <MenuItem value="professional">
                              Professional
                            </MenuItem>
                            <MenuItem value="casual">Casual</MenuItem>
                            <MenuItem value="friendly">Friendly</MenuItem>
                            <MenuItem value="authoritative">
                              Authoritative
                            </MenuItem>
                            <MenuItem value="humorous">Humorous</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth margin="normal">
                          <InputLabel>Default Content Type</InputLabel>
                          <Select
                            name="defaultContentType"
                            value={localContentSettings.defaultContentType}
                            onChange={handleContentSettingsChange}
                            label="Default Content Type"
                          >
                            <MenuItem value="post">Post</MenuItem>
                            <MenuItem value="article">Article</MenuItem>
                            <MenuItem value="story">Story</MenuItem>
                            <MenuItem value="carousel">Carousel</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth margin="normal">
                          <InputLabel>Default Length</InputLabel>
                          <Select
                            name="defaultLength"
                            value={localContentSettings.defaultLength}
                            onChange={handleContentSettingsChange}
                            label="Default Length"
                          >
                            <MenuItem value="short">Short</MenuItem>
                            <MenuItem value="medium">Medium</MenuItem>
                            <MenuItem value="long">Long</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Switch
                              name="includeHashtags"
                              checked={localContentSettings.includeHashtags}
                              onChange={handleContentSettingsChange}
                              color="primary"
                            />
                          }
                          label="Include hashtags in generated content"
                        />
                      </Grid>
                    </Grid>

                    <Box
                      sx={{
                        mt: 2,
                        display: "flex",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={handleSaveContentSettings}
                        disabled={loading}
                        startIcon={
                          loading ? (
                            <CircularProgress size={20} />
                          ) : (
                            <SaveIcon />
                          )
                        }
                      >
                        Save Settings
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </GlassmorphicCard>
          )}

          {/* Notification Settings */}
          {tabValue === 1 && <NotificationSettings />}

          {/* Security Settings */}
          {tabValue === 2 && (
            <GlassmorphicCard variant="glass">
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  <SecurityIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  Security Settings
                </Typography>

                <Divider sx={{ mb: 3 }} />

                <Typography variant="subtitle1" gutterBottom>
                  <DevicesIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  Active Sessions
                </Typography>

                {sessionsLoading ? (
                  <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
                    <CircularProgress />
                  </Box>
                ) : sessions.length === 0 ? (
                  <Alert severity="info" sx={{ mb: 3 }}>
                    No active sessions found.
                  </Alert>
                ) : (
                  <Box sx={{ mb: 3 }}>
                    <List>
                      {sessions.map((session) => (
                        <ListItem key={session.id}>
                          <ListItemIcon>
                            <DevicesIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {session.device || 'Unknown Device'}
                                {session.is_current && (
                                  <Chip
                                    label="Current"
                                    size="small"
                                    color="primary"
                                    variant="outlined"
                                  />
                                )}
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="body2" color="textSecondary">
                                  {session.browser && session.os ? `${session.browser} on ${session.os}` : session.user_agent}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                  {session.ip} • {session.location}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                  Last active: {new Date(session.last_active).toLocaleString()}
                                </Typography>
                              </Box>
                            }
                          />
                          <ListItemSecondaryAction>
                            <Tooltip title={session.is_current ? "Cannot revoke current session" : "Revoke Session"}>
                              <span>
                                <IconButton
                                  edge="end"
                                  onClick={() => handleRevokeSession(session.id)}
                                  color="error"
                                  disabled={session.is_current}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </span>
                            </Tooltip>
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>

                    <Box
                      sx={{
                        mt: 2,
                        display: "flex",
                        justifyContent: "flex-end",
                      }}
                    >
                      <Button
                        variant="outlined"
                        color="error"
                        onClick={handleRevokeAllSessions}
                        startIcon={<LogoutIcon />}
                      >
                        Revoke All Sessions
                      </Button>
                    </Box>
                  </Box>
                )}

                <Typography variant="subtitle1" gutterBottom>
                  <SecurityIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  Password
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Button
                    component={RouterLink}
                    to="/profile"
                    variant="contained"
                    color="primary"
                    sx={{ mt: 1 }}
                  >
                    Change Password
                  </Button>
                </Box>
              </Box>
            </GlassmorphicCard>
          )}

          {/* Account Settings */}
          {tabValue === 3 && (
            <GlassmorphicCard variant="glass">
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  <AccountCircleIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  Account Settings
                </Typography>

                <Divider sx={{ mb: 3 }} />

                {/* Profile Picture Section */}
                <Typography variant="subtitle1" gutterBottom>
                  <AccountCircleIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  Profile Picture
                </Typography>

                <Box sx={{ mb: 3, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <AvatarUploader
                    onUpload={handleAvatarChange}
                    onRemove={handleRemoveAvatar}
                    currentAvatar={user?.avatar}
                    loading={avatarLoading}
                    size={100}
                    name={user?.full_name}
                  />
                </Box>

                <Typography variant="subtitle1" gutterBottom>
                  <CreditCardIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  Subscription
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" gutterBottom>
                    Current Plan: {user?.subscription?.plan_name || "Free Plan"}
                  </Typography>

                  <Button
                    component={RouterLink}
                    to="/billing/plans"
                    variant="contained"
                    color="primary"
                    sx={{ mt: 1 }}
                  >
                    Manage Subscription
                  </Button>
                </Box>

                <Typography variant="subtitle1" gutterBottom>
                  <StorageIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  Data & Privacy
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    sx={{ mr: 1, mt: 1 }}
                    onClick={handleDataExport}
                  >
                    Download My Data
                  </Button>

                  <Button
                    variant="outlined"
                    color="error"
                    sx={{ mt: 1 }}
                    onClick={handleDataDeletion}
                  >
                    Delete My Data
                  </Button>
                </Box>

                <Typography variant="subtitle1" gutterBottom>
                  <DeleteIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                  Delete Account
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" color="error" gutterBottom>
                    Warning: This action cannot be undone. All your data will be
                    permanently deleted.
                  </Typography>

                  <Button
                    variant="contained"
                    color="error"
                    onClick={handleDeleteAccount}
                    sx={{ mt: 1 }}
                  >
                    Delete My Account
                  </Button>
                </Box>
              </Box>
            </GlassmorphicCard>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;

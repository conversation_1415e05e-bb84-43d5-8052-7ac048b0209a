/**
 * Toast Utility Functions
 * Advanced utilities for toast notification system
 */

import { ToastConfig, ToastError, NetworkFailureConfig } from '../types/toast';

// Rate limiting for rapid successive notifications
class ToastRateLimiter {
  private messageHistory: Map<string, number[]> = new Map();
  private readonly windowMs: number;
  private readonly maxRequests: number;

  constructor(windowMs: number = 5000, maxRequests: number = 3) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
  }

  canShow(message: string): boolean {
    const now = Date.now();
    const history = this.messageHistory.get(message) || [];
    
    // Remove old entries outside the time window
    const recentHistory = history.filter(timestamp => now - timestamp < this.windowMs);
    
    if (recentHistory.length >= this.maxRequests) {
      return false;
    }

    // Add current timestamp
    recentHistory.push(now);
    this.messageHistory.set(message, recentHistory);
    
    return true;
  }

  reset(message?: string): void {
    if (message) {
      this.messageHistory.delete(message);
    } else {
      this.messageHistory.clear();
    }
  }
}

// Network failure handler
class NetworkFailureHandler {
  private retryAttempts: Map<string, number> = new Map();
  private config: NetworkFailureConfig;

  constructor(config: NetworkFailureConfig) {
    this.config = config;
  }

  async handleFailure<T>(
    operation: () => Promise<T>,
    operationId: string,
    onError?: (error: ToastError) => void
  ): Promise<T | null> {
    const attempts = this.retryAttempts.get(operationId) || 0;

    try {
      const result = await operation();
      // Reset attempts on success
      this.retryAttempts.delete(operationId);
      return result;
    } catch (error) {
      const toastError: ToastError = {
        code: 'NETWORK_FAILURE',
        message: error instanceof Error ? error.message : 'Network operation failed',
        details: error,
        timestamp: new Date(),
        retryable: attempts < this.config.maxRetries,
      };

      if (attempts < this.config.maxRetries && this.config.enableRetry) {
        this.retryAttempts.set(operationId, attempts + 1);
        
        // Calculate delay with exponential backoff
        const delay = this.config.exponentialBackoff
          ? this.config.retryDelay * Math.pow(2, attempts)
          : this.config.retryDelay;

        if (this.config.onRetry) {
          this.config.onRetry(attempts + 1);
        }

        // Retry after delay
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.handleFailure(operation, operationId, onError);
      } else {
        // Max retries reached
        this.retryAttempts.delete(operationId);
        
        if (this.config.onMaxRetriesReached) {
          this.config.onMaxRetriesReached(toastError);
        }

        if (onError) {
          onError(toastError);
        }

        return null;
      }
    }
  }

  getRetryCount(operationId: string): number {
    return this.retryAttempts.get(operationId) || 0;
  }

  resetRetries(operationId?: string): void {
    if (operationId) {
      this.retryAttempts.delete(operationId);
    } else {
      this.retryAttempts.clear();
    }
  }
}

// Toast deduplication
class ToastDeduplicator {
  private activeToasts: Map<string, { config: ToastConfig; timestamp: number }> = new Map();
  private readonly dedupeWindowMs: number;

  constructor(dedupeWindowMs: number = 3000) {
    this.dedupeWindowMs = dedupeWindowMs;
  }

  shouldShow(config: ToastConfig): boolean {
    const key = this.generateKey(config);
    const existing = this.activeToasts.get(key);
    const now = Date.now();

    if (existing && (now - existing.timestamp) < this.dedupeWindowMs) {
      return false;
    }

    this.activeToasts.set(key, { config, timestamp: now });
    return true;
  }

  private generateKey(config: ToastConfig): string {
    return `${config.type}-${config.message}-${config.title || ''}`;
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, value] of this.activeToasts.entries()) {
      if (now - value.timestamp > this.dedupeWindowMs) {
        this.activeToasts.delete(key);
      }
    }
  }

  clear(): void {
    this.activeToasts.clear();
  }
}

// Concurrent notification manager
class ConcurrentNotificationManager {
  private activeOperations: Set<string> = new Set();
  private maxConcurrent: number;

  constructor(maxConcurrent: number = 5) {
    this.maxConcurrent = maxConcurrent;
  }

  canStart(operationId: string): boolean {
    if (this.activeOperations.size >= this.maxConcurrent) {
      return false;
    }

    this.activeOperations.add(operationId);
    return true;
  }

  complete(operationId: string): void {
    this.activeOperations.delete(operationId);
  }

  getActiveCount(): number {
    return this.activeOperations.size;
  }

  clear(): void {
    this.activeOperations.clear();
  }
}

// Toast persistence manager
class ToastPersistenceManager {
  private storageKey: string;

  constructor(storageKey: string = 'aceo-toast-notifications') {
    this.storageKey = storageKey;
  }

  save(toasts: ToastConfig[]): boolean {
    try {
      const persistentToasts = toasts.filter(toast => toast.persistent);
      const serialized = JSON.stringify(persistentToasts.map(toast => ({
        ...toast,
        timestamp: toast.timestamp?.toISOString(),
      })));
      
      localStorage.setItem(this.storageKey, serialized);
      return true;
    } catch (error) {
      console.warn('Failed to save persistent toasts:', error);
      return false;
    }
  }

  load(): ToastConfig[] {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) return [];

      const parsed = JSON.parse(stored);
      return parsed.map((toast: any) => ({
        ...toast,
        timestamp: toast.timestamp ? new Date(toast.timestamp) : new Date(),
      }));
    } catch (error) {
      console.warn('Failed to load persistent toasts:', error);
      return [];
    }
  }

  clear(): boolean {
    try {
      localStorage.removeItem(this.storageKey);
      return true;
    } catch (error) {
      console.warn('Failed to clear persistent toasts:', error);
      return false;
    }
  }

  isSupported(): boolean {
    try {
      const test = '__toast_storage_test__';
      localStorage.setItem(test, 'test');
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }
}

// Utility functions
export const createToastId = (): string => {
  return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const sanitizeToastMessage = (message: string): string => {
  // Remove HTML tags and limit length
  const cleaned = message.replace(/<[^>]*>/g, '').trim();
  return cleaned.length > 200 ? `${cleaned.substring(0, 197)}...` : cleaned;
};

export const getToastDuration = (type: string, messageLength: number): number => {
  const baseDurations = {
    success: 4000,
    info: 5000,
    warning: 6000,
    error: 8000,
  };

  const baseDuration = baseDurations[type as keyof typeof baseDurations] || 5000;
  
  // Adjust duration based on message length
  const readingTime = Math.max(messageLength * 50, 2000); // ~50ms per character, min 2s
  return Math.min(Math.max(baseDuration, readingTime), 15000); // Max 15 seconds
};

export const isNetworkError = (error: any): boolean => {
  return (
    error?.code === 'NETWORK_ERROR' ||
    error?.message?.includes('network') ||
    error?.message?.includes('fetch') ||
    error?.name === 'NetworkError' ||
    !navigator.onLine
  );
};

// Export utility classes
export {
  ToastRateLimiter,
  NetworkFailureHandler,
  ToastDeduplicator,
  ConcurrentNotificationManager,
  ToastPersistenceManager,
};

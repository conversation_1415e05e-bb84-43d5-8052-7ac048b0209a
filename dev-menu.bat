REM @since 2024-1-1 to 2025-25-7
@echo off
title ACEO - Development Menu
color 0A

:menu
cls
echo.
echo ========================================
echo   ACEO - Development
echo ========================================
echo.
echo 🚀 Development Options:
echo.
echo 1. Quick Start (Both Backend + Frontend)
echo 2. Start Backend Only
echo 3. Start Frontend Only
echo 4. Install All Dependencies
echo 5. Clean and Reinstall
echo 6. View Service URLs
echo 7. Kill All Development Servers
echo 8. Exit
echo.
set /p choice="Choose an option (1-8): "

if "%choice%"=="1" goto start_both
if "%choice%"=="2" goto start_backend
if "%choice%"=="3" goto start_frontend
if "%choice%"=="4" goto install_deps
if "%choice%"=="5" goto clean_install
if "%choice%"=="6" goto show_urls
if "%choice%"=="7" goto kill_servers
if "%choice%"=="8" goto exit
goto invalid

:start_both
cls
echo.
echo 🚀 Starting both Backend and Frontend...
echo.
echo Backend: http://localhost:8000
echo Frontend: http://localhost:3000
echo API Docs: http://localhost:8000/api/docs
echo.
echo Press Ctrl+C to stop both servers
echo.
npm run dev
pause
goto menu

:start_backend
cls
echo.
echo 🔧 Starting Backend only...
echo.
echo Backend: http://localhost:8000
echo API Docs: http://localhost:8000/api/docs
echo.
npm run dev:backend
pause
goto menu

:start_frontend
cls
echo.
echo 🎨 Starting Frontend only...
echo.
echo Frontend: http://localhost:3000
echo.
npm run dev:frontend
pause
goto menu

:install_deps
cls
echo.
echo 📦 Installing all dependencies...
echo.
npm run install:all
echo.
echo ✅ Dependencies installed successfully!
pause
goto menu

:clean_install
cls
echo.
echo 🧹 Cleaning and reinstalling...
echo.
npm run clean
npm run install:all
echo.
echo ✅ Clean installation completed!
pause
goto menu

:show_urls
cls
echo.
echo 🌐 Service URLs:
echo.
echo Frontend Application:
echo   http://localhost:3000
echo.
echo Backend API:
echo   http://localhost:8000
echo.
echo API Documentation:
echo   http://localhost:8000/api/docs
echo   http://localhost:8000/api/redoc
echo.
echo Health Check:
echo   http://localhost:8000/health
echo.
pause
goto menu

:kill_servers
cls
echo.
echo 🛑 Killing all development servers...
echo.
taskkill /f /im node.exe 2>nul
taskkill /f /im python.exe 2>nul
npx kill-port 3000 8000 2>nul
echo.
echo ✅ All servers stopped!
pause
goto menu

:invalid
cls
echo.
echo ❌ Invalid choice. Please select 1-8.
pause
goto menu

:exit
echo.
echo 👋 Goodbye!
exit

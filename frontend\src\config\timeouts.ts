/**
 * Centralized timeout configuration for API requests.
 * 
 * This file provides standardized timeout settings for different types of API operations.
 * Use these constants instead of hardcoded values to ensure consistency across the application.
 @since 2024-1-1 to 2025-25-7
*/

/**
 * Standard timeout for regular API requests (5 seconds)
 */
export const STANDARD_TIMEOUT = 5000;

/**
 * Extended timeout for operations that typically take longer (15 seconds)
 */
export const EXTENDED_TIMEOUT = 15000;

/**
 * Long timeout for complex operations (30 seconds)
 */
export const LONG_TIMEOUT = 30000;

/**
 * Very long timeout for very complex operations (60 seconds)
 */
export const VERY_LONG_TIMEOUT = 60000;

/**
 * Timeout configuration for specific API operations
 */
export const API_TIMEOUTS = {
  // Authentication
  login: STANDARD_TIMEOUT,
  register: STANDARD_TIMEOUT,
  refreshToken: STANDARD_TIMEOUT,
  
  // Content
  getContent: STANDARD_TIMEOUT,
  createContent: STANDARD_TIMEOUT,
  updateContent: STANDARD_TIMEOUT,
  deleteContent: STANDARD_TIMEOUT,
  
  // Content Generation
  generateContent: EXTENDED_TIMEOUT,
  generateImage: EXTENDED_TIMEOUT,
  bulkGenerateContent: LONG_TIMEOUT,
  
  // Job Status
  getJobStatus: STANDARD_TIMEOUT,
  
  // Competitor Analysis
  getCompetitors: STANDARD_TIMEOUT,
  createCompetitor: STANDARD_TIMEOUT,
  updateCompetitor: STANDARD_TIMEOUT,
  deleteCompetitor: STANDARD_TIMEOUT,
  compareCompetitors: EXTENDED_TIMEOUT, // Increased from 5s to 15s
  getCompetitorRecommendations: EXTENDED_TIMEOUT,
  
  // ICP
  getIcps: STANDARD_TIMEOUT,
  createIcp: STANDARD_TIMEOUT,
  updateIcp: STANDARD_TIMEOUT,
  deleteIcp: STANDARD_TIMEOUT,
  
  // ICP Performance
  analyzeIcpPerformance: EXTENDED_TIMEOUT,
  compareIcpPerformance: EXTENDED_TIMEOUT,
  getLatestIcpComparison: STANDARD_TIMEOUT,
  
  // User Management
  getUser: STANDARD_TIMEOUT,
  updateUser: STANDARD_TIMEOUT,
  deleteUser: STANDARD_TIMEOUT,
  
  // Team Management
  getTeams: STANDARD_TIMEOUT,
  createTeam: STANDARD_TIMEOUT,
  updateTeam: STANDARD_TIMEOUT,
  deleteTeam: STANDARD_TIMEOUT,
  inviteTeamMember: STANDARD_TIMEOUT,
  
  // Billing
  getSubscription: STANDARD_TIMEOUT,
  updateSubscription: STANDARD_TIMEOUT,
  cancelSubscription: STANDARD_TIMEOUT,
  getInvoices: STANDARD_TIMEOUT,
  
  // File Upload
  uploadFile: LONG_TIMEOUT,
  
  // Analytics
  getAnalytics: EXTENDED_TIMEOUT,
  
  // Messaging
  getMessages: STANDARD_TIMEOUT,
  sendMessage: STANDARD_TIMEOUT,
  
  // AI Feedback
  getAiFeedback: STANDARD_TIMEOUT,
  createAiFeedback: EXTENDED_TIMEOUT,
  
  // Auto Comment Reply
  getAutoCommentReplies: STANDARD_TIMEOUT,
  createAutoCommentReply: STANDARD_TIMEOUT,
  updateAutoCommentReply: STANDARD_TIMEOUT,
  deleteAutoCommentReply: STANDARD_TIMEOUT,
  
  // Referrals
  getReferrals: STANDARD_TIMEOUT,
  createReferral: STANDARD_TIMEOUT,
  
  // Health Checks
  getStatus: STANDARD_TIMEOUT,
  getDbHealth: STANDARD_TIMEOUT
};

/**
 * Get the timeout for a specific API operation
 * 
 * @param operation - The API operation name
 * @returns The timeout in milliseconds
 */
export const getTimeoutForOperation = (operation: keyof typeof API_TIMEOUTS): number => {
  return API_TIMEOUTS[operation] || STANDARD_TIMEOUT;
};

/**
 * Timeout configuration for WebSocket operations
 */
export const WEBSOCKET_TIMEOUTS = {
  connectionTimeout: 5000,
  reconnectInterval: 2000,
  maxReconnectAttempts: 5
};

/**
 * Timeout configuration for UI operations
 */
export const UI_TIMEOUTS = {
  toastDuration: 5000,
  modalTransition: 300,
  debounceInput: 300,
  tooltipDelay: 500
};

/**
 * Timeout configuration for animations
 */
export const ANIMATION_TIMEOUTS = {
  pageTransition: 300,
  fadeIn: 200,
  fadeOut: 200,
  slideIn: 300,
  slideOut: 300
};

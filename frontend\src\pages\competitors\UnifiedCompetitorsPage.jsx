// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Button,
  useTheme,
  useMediaQuery,
  Alert,
  Breadcrumbs,
  Link,
  Chip
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  List as ListIcon,
  Compare as CompareIcon,
  Add as AddIcon,
  Home as HomeIcon,
  Business as BusinessIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNotification } from '../../hooks/useNotification';
import ErrorBoundary from '../../components/common/ErrorBoundary';

// Import the existing components
import CompetitorsListPage from './CompetitorsListPage';
import CompetitorComparisonPage from './CompetitorComparisonPage';

// Error fallback component
const ErrorFallback = memo(({ error, resetError }) => (
  <Alert
    severity="error"
    sx={{ my: 3 }}
    action={
      <Button
        color="inherit"
        size="small"
        onClick={resetError}
        startIcon={<RefreshIcon />}
        aria-label="Retry loading competitors"
      >
        Retry
      </Button>
    }
  >
    <Typography variant="body2">
      Failed to load competitors: {error?.message || 'Unknown error'}
    </Typography>
  </Alert>
));

ErrorFallback.displayName = 'ErrorFallback';

/**
 * UnifiedCompetitorsPage combines the Competitor List and Compare views
 * into a single page with tabs for easy switching between views
 *
 * @param {Object} props
 * @param {boolean} props.isEmbedded - Whether the component is embedded in another page
 * @param {boolean} props.showHeader - Whether to show the page header
 * @param {boolean} props.compactMode - Whether to use compact mode
 * @param {Object} props.user - User object
 * @param {boolean} props.isAuthenticated - Whether user is authenticated
 */
const UnifiedCompetitorsPage = ({
  isEmbedded = false,
  showHeader = true,
  compactMode = false,
  user = null,
  isAuthenticated = true
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const location = useLocation();
  const { showErrorNotification } = useNotification();
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  // Get the tab from URL query params or default to 'list'
  const query = useMemo(() => new URLSearchParams(location.search), [location.search]);
  const defaultTab = query.get('view') || 'list';

  const [activeTab, setActiveTab] = useState(defaultTab);

  // Memoized tab configuration
  const tabConfig = useMemo(() => [
    {
      value: 'list',
      label: 'Competitor List',
      icon: ListIcon,
      description: 'View and manage all competitors'
    },
    {
      value: 'compare',
      label: 'Compare Competitors',
      icon: CompareIcon,
      description: 'Compare competitor performance'
    }
  ], []);

  // Update URL when tab changes without full page reload
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.set('view', activeTab);
    navigate({
      pathname: location.pathname,
      search: searchParams.toString()
    }, { replace: true });
  }, [activeTab, navigate, location.pathname, location.search]);

  // Error boundary handler
  const handleError = useCallback((error, errorInfo) => {
    console.error('UnifiedCompetitorsPage Error:', error, errorInfo);
    setError(error);
    showErrorNotification('Failed to load competitors page');
  }, [showErrorNotification]);

  // Reset error state
  const resetError = useCallback(() => {
    setError(null);
    setRetryCount(prev => prev + 1);
  }, []);

  // Navigation handlers
  const handleNavigateHome = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  const handleAddCompetitor = useCallback(() => {
    navigate('/settings?tab=competitors&action=new');
  }, [navigate]);

  const handleTabChange = useCallback((_, newValue) => {
    setActiveTab(newValue);

    // Track tab change
    if (typeof gtag !== 'undefined') {
      gtag('event', 'tab_change', {
        event_category: 'competitors',
        event_label: newValue,
        value: 1
      });
    }
  }, []);

  // Get current tab info
  const currentTab = useMemo(() =>
    tabConfig.find(tab => tab.value === activeTab) || tabConfig[0],
    [tabConfig, activeTab]
  );

  return (
    <Box
      sx={{ width: '100%' }}
      component="main"
      role="main"
      aria-label="Unified competitors dashboard"
    >
      {/* Breadcrumb Navigation */}
      {!isEmbedded && (
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs
            aria-label="breadcrumb navigation"
            sx={{ mb: 2 }}
          >
            <Link
              component="button"
              variant="body2"
              onClick={handleNavigateHome}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
              aria-label="Navigate to dashboard"
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Dashboard
            </Link>
            <Typography
              color="text.primary"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Competitors
            </Typography>
          </Breadcrumbs>
        </Box>
      )}

      {/* Page Header */}
      {!isEmbedded && showHeader && (
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Competitor Analysis
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {currentTab.description}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            {/* User info for embedded mode */}
            {isEmbedded && user && (
              <Chip
                label={`Welcome, ${user.name}`}
                color="primary"
                variant="outlined"
                size="small"
              />
            )}

            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddCompetitor}
              aria-label="Add new competitor"
              disabled={!isAuthenticated}
            >
              Add Competitor
            </Button>
          </Box>
        </Box>
      )}

      {/* Compact mode info */}
      {compactMode && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            You&apos;re viewing competitors in compact mode.
            Some features may be limited.
          </Typography>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <ErrorFallback error={error} resetError={resetError} />
      )}

      {/* Tab Navigation */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant={isMobile ? "fullWidth" : "standard"}
          aria-label="competitor navigation tabs"
        >
          {tabConfig.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <Tab
                key={tab.value}
                icon={<IconComponent />}
                iconPosition="start"
                label={tab.label}
                value={tab.value}
                aria-label={`Switch to ${tab.label} tab`}
                aria-describedby={`tab-${tab.value}-description`}
              />
            );
          })}
        </Tabs>

        {/* Tab descriptions for screen readers */}
        {tabConfig.map((tab) => (
          <Typography
            key={`desc-${tab.value}`}
            id={`tab-${tab.value}-description`}
            variant="srOnly"
            component="div"
          >
            {tab.description}
          </Typography>
        ))}
      </Paper>

      {/* Tab Content */}
      <ErrorBoundary
        onError={handleError}
        fallback={<ErrorFallback error={error} resetError={resetError} />}
      >
        <Box
          sx={{ mt: 2 }}
          role="tabpanel"
          aria-labelledby={`tab-${activeTab}`}
          aria-label={`${currentTab.label} content`}
        >
          {activeTab === 'list' && (
            <CompetitorsListPage
              isEmbedded={true}
              key={`list-${retryCount}`}
            />
          )}

          {activeTab === 'compare' && (
            <CompetitorComparisonPage
              isEmbedded={true}
              key={`compare-${retryCount}`}
            />
          )}
        </Box>
      </ErrorBoundary>
    </Box>
  );
};

// Memoize the component for performance
const MemoizedUnifiedCompetitorsPage = memo(UnifiedCompetitorsPage);
MemoizedUnifiedCompetitorsPage.displayName = 'UnifiedCompetitorsPage';

export default MemoizedUnifiedCompetitorsPage;

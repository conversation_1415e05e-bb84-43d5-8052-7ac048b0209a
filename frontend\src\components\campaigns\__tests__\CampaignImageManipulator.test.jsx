import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CampaignImageManipulator from '../CampaignImageManipulator';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the image manipulation API
const mockManipulateCampaignImages = vi.fn();
vi.mock('../../../api/image-manipulation', () => ({
  manipulateCampaignImages: mockManipulateCampaignImages,
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-object-url');
global.URL.revokeObjectURL = vi.fn();

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

// Helper function to create mock file
const createMockFile = (name = 'test.jpg', type = 'image/jpeg') => {
  const file = new File(['mock content'], name, { type });
  Object.defineProperty(file, 'size', { value: 1024 });
  return file;
};

describe('CampaignImageManipulator', () => {
  const mockProps = {
    campaignId: 'test-campaign-id',
    campaignName: 'Test Campaign',
    onImagesGenerated: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders campaign image manipulator correctly', () => {
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Campaign Image Manipulator')).toBeInTheDocument();
    expect(screen.getByText('Campaign: Test Campaign')).toBeInTheDocument();
    expect(screen.getByText('Upload Source Images')).toBeInTheDocument();
    expect(screen.getByText('Manipulated Campaign Images')).toBeInTheDocument();
  });

  test('handles image upload', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, file);

    expect(screen.getByText('Source Images (1)')).toBeInTheDocument();
    expect(screen.getByText('test.jpg')).toBeInTheDocument();
  });

  test('handles multiple image upload', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const files = [
      createMockFile('image1.jpg'),
      createMockFile('image2.png', 'image/png')
    ];
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, files);

    expect(screen.getByText('Source Images (2)')).toBeInTheDocument();
    expect(screen.getByText('image1.jpg')).toBeInTheDocument();
    expect(screen.getByText('image2.png')).toBeInTheDocument();
  });

  test('handles image name and description input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const nameInput = screen.getByLabelText('Image Name (Optional)');
    const descriptionInput = screen.getByLabelText('Description (Optional)');
    
    await user.type(nameInput, 'Custom Image Name');
    await user.type(descriptionInput, 'Custom description');

    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, file);

    expect(screen.getByText('Custom Image Name')).toBeInTheDocument();
    expect(screen.getByText('Custom description')).toBeInTheDocument();
  });

  test('handles removing source images', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, file);
    expect(screen.getByText('Source Images (1)')).toBeInTheDocument();

    const removeButton = screen.getByLabelText('delete');
    await user.click(removeButton);

    expect(screen.queryByText('Source Images (1)')).not.toBeInTheDocument();
  });

  test('handles manipulation instructions input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const instructionsInput = screen.getByLabelText('Manipulation Instructions');
    await user.type(instructionsInput, 'Make the image brighter and more colorful');

    expect(instructionsInput).toHaveValue('Make the image brighter and more colorful');
  });

  test('handles form field changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    // Test size selection
    const sizeSelect = screen.getByLabelText('Size');
    await user.click(sizeSelect);
    await user.click(screen.getByText('512x512'));
    
    // Test style selection
    const styleSelect = screen.getByLabelText('Style');
    await user.click(styleSelect);
    await user.click(screen.getByText('Natural'));

    expect(sizeSelect).toHaveTextContent('512x512');
    expect(styleSelect).toHaveTextContent('Natural');
  });

  test('handles campaign branding toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const brandingSwitch = screen.getByRole('checkbox', { name: /apply campaign branding/i });
    expect(brandingSwitch).toBeChecked();

    await user.click(brandingSwitch);
    expect(brandingSwitch).not.toBeChecked();
  });

  test('validates form submission with no images', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const submitButton = screen.getByText('Manipulate Campaign Images');
    await user.click(submitButton);

    expect(mockShowErrorNotification).toHaveBeenCalledWith('Please upload at least one image to manipulate');
  });

  test('validates form submission with no instructions', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    // Upload an image
    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    await user.upload(fileInput, file);

    const submitButton = screen.getByText('Manipulate Campaign Images');
    await user.click(submitButton);

    expect(mockShowErrorNotification).toHaveBeenCalledWith('Please provide instructions for image manipulation');
  });

  test('handles successful image manipulation', async () => {
    const user = userEvent.setup();
    
    const mockResponse = {
      images: [
        {
          url: 'https://example.com/manipulated1.jpg',
          size: '1024x1024',
          style: 'vivid',
          generated_at: '2023-01-01T00:00:00Z',
          used_branding: true,
          instructions: 'Make the image brighter'
        }
      ]
    };
    
    mockManipulateCampaignImages.mockResolvedValueOnce(mockResponse);
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    // Upload an image and add instructions
    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    await user.upload(fileInput, file);

    const instructionsInput = screen.getByLabelText('Manipulation Instructions');
    await user.type(instructionsInput, 'Make the image brighter');

    const submitButton = screen.getByText('Manipulate Campaign Images');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockManipulateCampaignImages).toHaveBeenCalledWith({
        campaign_id: 'test-campaign-id',
        source_images: expect.arrayContaining([
          expect.objectContaining({
            name: 'test.jpg',
            url: 'mock-object-url'
          })
        ]),
        instructions: 'Make the image brighter',
        size: '1024x1024',
        style: 'vivid',
        n: 1,
        use_campaign_branding: true
      });
    });

    expect(mockShowSuccessNotification).toHaveBeenCalledWith('Campaign images manipulated successfully!');
    expect(mockProps.onImagesGenerated).toHaveBeenCalledWith(mockResponse.images);
  });

  test('handles image manipulation error', async () => {
    const user = userEvent.setup();
    
    mockManipulateCampaignImages.mockRejectedValueOnce(new Error('API Error'));
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    // Upload an image and add instructions
    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    await user.upload(fileInput, file);

    const instructionsInput = screen.getByLabelText('Manipulation Instructions');
    await user.type(instructionsInput, 'Make the image brighter');

    const submitButton = screen.getByText('Manipulate Campaign Images');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith('Failed to manipulate campaign images. Please try again.');
    });
  });

  test('shows loading state during manipulation', async () => {
    const user = userEvent.setup();
    
    // Mock a delayed response
    mockManipulateCampaignImages.mockImplementation(() => new Promise(() => {}));
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    // Upload an image and add instructions
    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    await user.upload(fileInput, file);

    const instructionsInput = screen.getByLabelText('Manipulation Instructions');
    await user.type(instructionsInput, 'Make the image brighter');

    const submitButton = screen.getByText('Manipulate Campaign Images');
    await user.click(submitButton);

    expect(screen.getByText('Manipulating...')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });

  test('displays generated images correctly', async () => {
    const user = userEvent.setup();
    
    const mockResponse = {
      images: [
        {
          url: 'https://example.com/manipulated1.jpg',
          size: '1024x1024',
          style: 'vivid',
          generated_at: '2023-01-01T00:00:00Z',
          used_branding: true,
          instructions: 'Make the image brighter and more colorful for better engagement'
        }
      ]
    };
    
    mockManipulateCampaignImages.mockResolvedValueOnce(mockResponse);
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    // Upload an image and add instructions
    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    await user.upload(fileInput, file);

    const instructionsInput = screen.getByLabelText('Manipulation Instructions');
    await user.type(instructionsInput, 'Make the image brighter');

    const submitButton = screen.getByText('Manipulate Campaign Images');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Campaign Image 1')).toBeInTheDocument();
      expect(screen.getByText('Campaign Branded')).toBeInTheDocument();
      expect(screen.getByText('1024x1024 • vivid •')).toBeInTheDocument();
      expect(screen.getByText('Make the image brighter and more colorful for better engagement')).toBeInTheDocument();
    });
  });

  test('shows empty state when no images generated', () => {
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('No manipulated campaign images yet. Upload source images and provide instructions to get started.')).toBeInTheDocument();
  });

  test('disables submit button when form is invalid', () => {
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const submitButton = screen.getByText('Manipulate Campaign Images');
    expect(submitButton).toBeDisabled();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    // Check for proper form controls and labels
    expect(screen.getByLabelText('Image Name (Optional)')).toBeInTheDocument();
    expect(screen.getByLabelText('Description (Optional)')).toBeInTheDocument();
    expect(screen.getByLabelText('Manipulation Instructions')).toBeInTheDocument();
    expect(screen.getByLabelText('Size')).toBeInTheDocument();
    expect(screen.getByLabelText('Style')).toBeInTheDocument();
    expect(screen.getByRole('checkbox', { name: /apply campaign branding/i })).toBeInTheDocument();
  });

  test('clears form fields after successful upload', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignImageManipulator {...mockProps} />
      </TestWrapper>
    );

    const nameInput = screen.getByLabelText('Image Name (Optional)');
    const descriptionInput = screen.getByLabelText('Description (Optional)');
    
    await user.type(nameInput, 'Test Name');
    await user.type(descriptionInput, 'Test Description');

    const file = createMockFile();
    const fileInput = screen.getByRole('button', { name: /upload images/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, file);

    expect(nameInput).toHaveValue('');
    expect(descriptionInput).toHaveValue('');
  });
});

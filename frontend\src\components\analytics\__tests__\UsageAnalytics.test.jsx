/**
 * Tests for UsageAnalytics component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import UsageAnalytics from '../UsageAnalytics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock hooks
vi.mock('../../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn()
  }))
}));

// Mock Chart.js
vi.mock('react-chartjs-2', () => ({
  Line: ({ data }) => <div data-testid="line-chart">{JSON.stringify(data)}</div>,
  Bar: ({ data }) => <div data-testid="bar-chart">{JSON.stringify(data)}</div>,
  Doughnut: ({ data }) => <div data-testid="doughnut-chart">{JSON.stringify(data)}</div>
}));

vi.mock('chart.js', () => ({
  Chart: {
    register: vi.fn()
  },
  CategoryScale: {},
  LinearScale: {},
  PointElement: {},
  LineElement: {},
  BarElement: {},
  ArcElement: {},
  Title: {},
  Tooltip: {},
  Legend: {}
}));



describe('UsageAnalytics', () => {
  const mockAnalyticsData = {
    monthly_stats: [
      { month: '2023-07', total_regenerations: 32, credits_used: 24.5, efficiency_score: 0.75 },
      { month: '2023-08', total_regenerations: 45, credits_used: 32.0, efficiency_score: 0.82 },
      { month: '2023-09', total_regenerations: 38, credits_used: 28.5, efficiency_score: 0.85 },
      { month: '2023-10', total_regenerations: 52, credits_used: 35.0, efficiency_score: 0.88 },
      { month: '2023-11', total_regenerations: 41, credits_used: 29.5, efficiency_score: 0.90 },
      { month: '2023-12', total_regenerations: 48, credits_used: 31.0, efficiency_score: 0.92 }
    ],
    content_performance: {
      average_quality_improvement: 0.23,
      engagement_increase: 0.18,
      most_improved_content_type: 'social_media_post'
    },
    efficiency_trends: {
      monthly_efficiency: [0.75, 0.82, 0.85, 0.88, 0.90, 0.92],
      credit_usage_trend: [24.5, 32.0, 28.5, 35.0, 29.5, 31.0]
    },
    cost_analysis: {
      total_spent: 127.50,
      cost_per_improvement: 5.52,
      roi_estimate: 3.2
    }
  };

  const mockProps = {
    onRefresh: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    
    // Mock document.createElement for export functionality
    global.document.createElement = vi.fn(() => ({
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    }));

    global.document.body.appendChild = vi.fn();
    global.document.body.removeChild = vi.fn();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  test('renders usage analytics component', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Usage Analytics')).toBeInTheDocument();
    });

    expect(screen.getByText('Total Regenerations')).toBeInTheDocument();
    expect(screen.getByText('Credits Used')).toBeInTheDocument();
    expect(screen.getByText('Avg Efficiency')).toBeInTheDocument();
    expect(screen.getByText('ROI Estimate')).toBeInTheDocument();
  });

  test('shows loading state correctly', () => {
    const api = require('../../../api');
    api.default.get.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows error state when API fails', async () => {
    const api = await import('../../../api');
    api.default.get.mockRejectedValue(new Error('API Error'));
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load analytics data')).toBeInTheDocument();
    });

    expect(screen.getByLabelText('Retry loading analytics')).toBeInTheDocument();
  });

  test('displays metrics cards with correct data', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('256')).toBeInTheDocument(); // Total regenerations
      expect(screen.getByText('180.5')).toBeInTheDocument(); // Total credits used
      expect(screen.getByText('23%')).toBeInTheDocument(); // Avg efficiency
      expect(screen.getByText('3.2x')).toBeInTheDocument(); // ROI estimate
    });
  });

  test('handles time range change', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Time Range')).toBeInTheDocument();
    });

    const timeRangeSelect = screen.getByLabelText('Time Range');
    await user.click(timeRangeSelect);
    
    const threeMonthsOption = screen.getByText('3 Months');
    await user.click(threeMonthsOption);

    // Should trigger new API call
    await waitFor(() => {
      expect(api.default.get).toHaveBeenCalledWith('/api/regeneration/stats?timeRange=3months');
    });
  });

  test('renders charts when data is available', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument(); // Efficiency trend
      expect(screen.getByTestId('doughnut-chart')).toBeInTheDocument(); // Regeneration types
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument(); // Monthly usage
    });
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Export usage analytics')).toBeInTheDocument();
    });

    const exportButton = screen.getByLabelText('Export usage analytics');
    await user.click(exportButton);

    // Should create download link
    expect(global.document.createElement).toHaveBeenCalledWith('a');
  });

  test('disables export when no data available', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: null });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      const exportButton = screen.getByLabelText('Export usage analytics');
      expect(exportButton).toBeDisabled();
    });
  });

  test('hides export button when showExport is false', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} showExport={false} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByLabelText('Export usage analytics')).not.toBeInTheDocument();
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Refresh usage analytics')).toBeInTheDocument();
    });

    const refreshButton = screen.getByLabelText('Refresh usage analytics');
    await user.click(refreshButton);

    expect(mockProps.onRefresh).toHaveBeenCalled();
    expect(api.default.get).toHaveBeenCalledTimes(2); // Initial + refresh
  });

  test('auto-refresh functionality works correctly', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics 
          {...mockProps}
          autoRefresh={true}
          refreshInterval={1000}
        />
      </TestWrapper>
    );

    // Fast-forward time to trigger auto-refresh
    vi.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(api.default.get).toHaveBeenCalledTimes(2); // Initial + auto-refresh
    });
  });

  test('cleans up auto-refresh interval on unmount', () => {
    const { unmount } = render(
      <TestWrapper>
        <UsageAnalytics 
          {...mockProps}
          autoRefresh={true}
          refreshInterval={1000}
        />
      </TestWrapper>
    );

    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
    unmount();

    expect(clearIntervalSpy).toHaveBeenCalled();
  });

  test('displays performance insights table', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Insights')).toBeInTheDocument();
      expect(screen.getByText('Quality Improvement')).toBeInTheDocument();
      expect(screen.getByText('Engagement Increase')).toBeInTheDocument();
      expect(screen.getByText('Cost per Improvement')).toBeInTheDocument();
    });
  });

  test('applies correct accessibility attributes', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      // Check ARIA labels
      expect(screen.getByLabelText('Export usage analytics')).toBeInTheDocument();
      expect(screen.getByLabelText('Refresh usage analytics')).toBeInTheDocument();

      // Check tooltips
      expect(screen.getByTitle('Export Usage Analytics')).toBeInTheDocument();
      expect(screen.getByTitle('Refresh Data')).toBeInTheDocument();
    });
  });

  test('handles retry functionality in error state', async () => {
    const user = userEvent.setup();
    const api = await import('../../../api');
    api.default.get.mockRejectedValue(new Error('Network error'));
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Retry loading analytics')).toBeInTheDocument();
    });

    const retryButton = screen.getByLabelText('Retry loading analytics');
    await user.click(retryButton);

    expect(mockProps.onRefresh).toHaveBeenCalled();
  });

  test('formats numbers correctly in metrics cards', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: mockAnalyticsData });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      // Should format numbers with proper decimal places
      expect(screen.getByText('180.5')).toBeInTheDocument(); // Credits with 1 decimal
      expect(screen.getByText('3.2x')).toBeInTheDocument(); // ROI with 1 decimal
      expect(screen.getByText('23%')).toBeInTheDocument(); // Percentage rounded
    });
  });

  test('uses fallback data when API returns null', async () => {
    const api = await import('../../../api');
    api.default.get.mockResolvedValue({ data: null });
    
    render(
      <TestWrapper>
        <UsageAnalytics {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      // Should show mock data as fallback
      expect(screen.getByText('Usage Analytics')).toBeInTheDocument();
    });
  });
});

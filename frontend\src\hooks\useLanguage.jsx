// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, createContext, useContext, useRef, useMemo } from 'react';
import {
  SUPPORTED_LANGUAGES,
  DEFAULT_LANGUAGE,
  getCulturalContext,
  getLocalizedTerm,
  getCulturalPromptElements,
  getUILabel
} from '../config/languages';

// Enhanced logging utility for production-ready language management
const logger = {
  debug: (message, data) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[useLanguage] ${message}`, data);
    }
  },
  info: (message, data) => {
    if (process.env.NODE_ENV === 'development') {
      console.info(`[useLanguage] ${message}`, data);
    }

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production' && window.analytics) {
      window.analytics.track('Language Hook Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data) => {
    console.warn(`[useLanguage] ${message}`, data);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Language Hook Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error) => {
    console.error(`[useLanguage] ${message}`, error);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Language Hook Error', {
        message,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Configuration constants
const CONFIG = {
  STORAGE_KEY: 'selectedLanguage',
  LANGUAGE_SWITCH_DELAY: 300,
  MAX_LANGUAGE_HISTORY: 20,
  ANALYTICS_ENABLED: true,
  PERFORMANCE_MONITORING: true,
};

// Language Context
const LanguageContext = createContext();

// Language Provider Component
export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    // Get from localStorage or use default
    return localStorage.getItem(CONFIG.STORAGE_KEY) || DEFAULT_LANGUAGE;
  });

  const [isLoading, setIsLoading] = useState(false);
  const [languageHistory, setLanguageHistory] = useState([]);
  const [lastChangeTime, setLastChangeTime] = useState(null);

  // Refs for tracking and performance
  const usageCountRef = useRef(0);
  const startTimeRef = useRef(Date.now());
  const changeCountRef = useRef(0);

  // Memoized language validation
  const validatedLanguage = useMemo(() => {
    if (!SUPPORTED_LANGUAGES[currentLanguage]) {
      logger.warn('Invalid language detected, falling back to default', {
        invalid: currentLanguage,
        default: DEFAULT_LANGUAGE
      });
      return DEFAULT_LANGUAGE;
    }
    return currentLanguage;
  }, [currentLanguage]);

  logger.debug('Language provider initialized', {
    currentLanguage: validatedLanguage,
    supportedLanguages: Object.keys(SUPPORTED_LANGUAGES).length,
    storageKey: CONFIG.STORAGE_KEY
  });

  // Update localStorage when language changes
  useEffect(() => {
    localStorage.setItem(CONFIG.STORAGE_KEY, validatedLanguage);
    setLastChangeTime(Date.now());
  }, [validatedLanguage]);

  // Change language with validation and enhanced tracking
  const changeLanguage = useCallback(async (languageCode) => {
    if (!SUPPORTED_LANGUAGES[languageCode]) {
      logger.warn(`Language ${languageCode} not supported, falling back to ${DEFAULT_LANGUAGE}`, {
        requested: languageCode,
        fallback: DEFAULT_LANGUAGE,
        supported: Object.keys(SUPPORTED_LANGUAGES)
      });
      languageCode = DEFAULT_LANGUAGE;
    }

    setIsLoading(true);
    changeCountRef.current++;

    try {
      logger.debug('Changing language', {
        from: currentLanguage,
        to: languageCode,
        changeCount: changeCountRef.current
      });

      // Add to language history
      setLanguageHistory(prev => [
        {
          language: currentLanguage,
          timestamp: Date.now(),
          changeNumber: changeCountRef.current
        },
        ...prev.slice(0, CONFIG.MAX_LANGUAGE_HISTORY - 1)
      ]);

      // Simulate loading time for language switching
      await new Promise(resolve => setTimeout(resolve, CONFIG.LANGUAGE_SWITCH_DELAY));
      setCurrentLanguage(languageCode);

      // Analytics tracking
      if (CONFIG.ANALYTICS_ENABLED && window.analytics) {
        window.analytics.track('Language Changed', {
          from: currentLanguage,
          to: languageCode,
          changeCount: changeCountRef.current,
          timestamp: new Date().toISOString()
        });
      }

      logger.info('Language changed successfully', {
        newLanguage: languageCode,
        changeCount: changeCountRef.current
      });
    } catch (error) {
      logger.error('Error changing language', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentLanguage]);

  // Get current language data
  const getCurrentLanguageData = useCallback(() => {
    return SUPPORTED_LANGUAGES[currentLanguage] || SUPPORTED_LANGUAGES[DEFAULT_LANGUAGE];
  }, [currentLanguage]);

  // Get cultural context for current language
  const getCulturalContextForCurrent = useCallback((contextType) => {
    return getCulturalContext(currentLanguage, contextType);
  }, [currentLanguage]);

  // Get localized term for current language
  const getLocalizedTermForCurrent = useCallback((term) => {
    return getLocalizedTerm(currentLanguage, term);
  }, [currentLanguage]);

  // Get cultural prompt elements for current language
  const getCulturalPromptElementsForCurrent = useCallback(() => {
    return getCulturalPromptElements(currentLanguage);
  }, [currentLanguage]);

  // Get UI label for current language
  const getUILabelForCurrent = useCallback((labelKey) => {
    return getUILabel(currentLanguage, labelKey);
  }, [currentLanguage]);

  // Generate culturally adapted prompt prefix with enhanced tracking
  const generateCulturalPromptPrefix = useCallback((industry, style) => {
    usageCountRef.current++;

    const culturalElements = getCulturalPromptElementsForCurrent();
    const languageData = getCurrentLanguageData();

    logger.debug('Generating cultural prompt prefix', {
      industry,
      style,
      language: currentLanguage,
      usageCount: usageCountRef.current
    });

    let prefix = '';

    // Add cultural business style
    if (industry) {
      prefix += `Create a ${culturalElements.businessStyle} image for ${industry}. `;
    }

    // Add cultural visual elements
    if (style) {
      const localizedStyle = getLocalizedTermForCurrent(style);
      prefix += `Apply ${localizedStyle} aesthetic with ${culturalElements.visualElements}. `;
    }

    // Add language-specific cultural context using languageData
    if (languageData.culturalContext) {
      prefix += `${languageData.culturalContext} `;
    } else {
      // Fallback to switch statement for backward compatibility
      switch (currentLanguage) {
        case 'fr':
          prefix += 'Incorporate French sophistication and artisanal quality. ';
          break;
        case 'es':
          prefix += 'Include warm, vibrant Mediterranean elements. ';
          break;
        case 'de':
          prefix += 'Emphasize precision, quality, and functional design. ';
          break;
        case 'it':
          prefix += 'Apply Italian design sensibility and artistic heritage. ';
          break;
        case 'ja':
          prefix += 'Use minimalist zen aesthetics with attention to detail. ';
          break;
        case 'zh':
          prefix += 'Include elements of balance and prosperity. ';
          break;
        default:
          prefix += 'Apply contemporary professional standards. ';
      }
    }

    logger.debug('Cultural prompt prefix generated', {
      prefixLength: prefix.length,
      language: currentLanguage,
      usageCount: usageCountRef.current
    });

    return prefix;
  }, [currentLanguage, getCulturalPromptElementsForCurrent, getCurrentLanguageData, getLocalizedTermForCurrent]);

  // Generate location-specific environmental context
  const generateEnvironmentalContext = useCallback((icpLocation) => {
    const culturalElements = getCulturalPromptElementsForCurrent();
    
    // Base environmental context from language
    let environmentalContext = `Set in ${culturalElements.nature} with ${culturalElements.architecture} in the background. `;
    
    // Override with ICP-specific location if provided
    if (icpLocation) {
      const location = icpLocation.toLowerCase();
      
      if (location.includes('europe') || location.includes('european')) {
        environmentalContext = 'European setting with classical architecture and refined landscapes. ';
      } else if (location.includes('asia') || location.includes('asian')) {
        environmentalContext = 'Asian setting with modern urban elements and traditional cultural touches. ';
      } else if (location.includes('america') || location.includes('us') || location.includes('north america')) {
        environmentalContext = 'North American setting with contemporary urban or suburban elements. ';
      } else if (location.includes('mediterranean')) {
        environmentalContext = 'Mediterranean setting with coastal views and warm, sunny atmosphere. ';
      }
    }
    
    return environmentalContext;
  }, [getCulturalPromptElementsForCurrent]);

  // Enhanced utility functions for production monitoring
  const getLanguageStats = useCallback(() => {
    const uptime = Date.now() - startTimeRef.current;

    return {
      currentLanguage: validatedLanguage,
      usageCount: usageCountRef.current,
      changeCount: changeCountRef.current,
      uptime,
      lastChangeTime,
      languageHistory: languageHistory.slice(0, 5), // Last 5 changes
      isLoading,
      supportedLanguagesCount: Object.keys(SUPPORTED_LANGUAGES).length
    };
  }, [validatedLanguage, lastChangeTime, languageHistory, isLoading]);

  const getConfiguration = useCallback(() => {
    return {
      supportedLanguages: SUPPORTED_LANGUAGES,
      defaultLanguage: DEFAULT_LANGUAGE,
      config: CONFIG,
      storageKey: CONFIG.STORAGE_KEY,
      version: '1.0.0'
    };
  }, []);

  const healthCheck = useCallback(() => {
    const stats = getLanguageStats();
    const isHealthy = !!validatedLanguage && !isLoading && stats.uptime > 0;

    logger.debug('Language hook health check', {
      isHealthy,
      currentLanguage: validatedLanguage,
      isLoading,
      uptime: stats.uptime,
      changeCount: stats.changeCount
    });

    return {
      isHealthy,
      status: isLoading ? 'loading' :
              !validatedLanguage ? 'invalid' : 'healthy',
      currentLanguage: validatedLanguage,
      usageCount: stats.usageCount,
      changeCount: stats.changeCount,
      uptime: stats.uptime,
      lastChangeTime: stats.lastChangeTime,
      issues: [
        ...(!validatedLanguage ? ['No valid language set'] : []),
        ...(isLoading ? ['Language change in progress'] : []),
        ...(stats.changeCount === 0 ? ['No language changes made'] : [])
      ],
      timestamp: new Date().toISOString()
    };
  }, [getLanguageStats, validatedLanguage, isLoading]);

  // Get language context for debugging
  const getLanguageContext = useCallback(() => {
    return {
      language: {
        current: validatedLanguage,
        data: getCurrentLanguageData(),
        isLoading
      },
      history: languageHistory.slice(0, 10), // Last 10 changes
      stats: getLanguageStats(),
      configuration: getConfiguration(),
      timestamp: new Date().toISOString()
    };
  }, [validatedLanguage, getCurrentLanguageData, isLoading, languageHistory, getLanguageStats, getConfiguration]);

  const value = {
    // Core state
    currentLanguage: validatedLanguage,
    isLoading,
    languageHistory,
    lastChangeTime,

    // Actions
    changeLanguage,

    // Data getters
    getCurrentLanguageData,
    getCulturalContextForCurrent,
    getLocalizedTermForCurrent,
    getCulturalPromptElementsForCurrent,
    getUILabelForCurrent,

    // Prompt generation helpers
    generateCulturalPromptPrefix,
    generateEnvironmentalContext,

    // Utility functions
    getLanguageStats,
    getConfiguration,
    healthCheck,
    getLanguageContext,

    // Enhanced state helpers
    isValidLanguage: !!SUPPORTED_LANGUAGES[validatedLanguage],
    hasHistory: languageHistory.length > 0,

    // Quick access helpers
    usageCount: usageCountRef.current,
    changeCount: changeCountRef.current,
    uptime: Date.now() - startTimeRef.current,

    // Configuration access
    supportedLanguages: SUPPORTED_LANGUAGES,
    defaultLanguage: DEFAULT_LANGUAGE,
    config: CONFIG
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// Hook to use language context
// eslint-disable-next-line react-refresh/only-export-components
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Standalone hook for components that don't need the full context
// eslint-disable-next-line react-refresh/only-export-components
export const useLanguageStandalone = () => {
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    return localStorage.getItem(CONFIG.STORAGE_KEY) || DEFAULT_LANGUAGE;
  });

  const changeLanguage = useCallback((languageCode) => {
    if (!SUPPORTED_LANGUAGES[languageCode]) {
      logger.warn(`Language ${languageCode} not supported, falling back to ${DEFAULT_LANGUAGE}`, {
        requested: languageCode,
        fallback: DEFAULT_LANGUAGE,
        supported: Object.keys(SUPPORTED_LANGUAGES)
      });
      languageCode = DEFAULT_LANGUAGE;
    }

    setCurrentLanguage(languageCode);
    localStorage.setItem(CONFIG.STORAGE_KEY, languageCode);

    // Analytics tracking for standalone usage
    if (CONFIG.ANALYTICS_ENABLED && window.analytics) {
      window.analytics.track('Language Changed (Standalone)', {
        to: languageCode,
        timestamp: new Date().toISOString()
      });
    }

    logger.debug('Language changed (standalone)', {
      newLanguage: languageCode
    });
  }, []);

  const getUILabel = useCallback((labelKey) => {
    return getUILabel(currentLanguage, labelKey);
  }, [currentLanguage]);

  return {
    currentLanguage,
    changeLanguage,
    getUILabel,
    supportedLanguages: SUPPORTED_LANGUAGES,
    isStandalone: true
  };
};

// eslint-disable-next-line react-refresh/only-export-components
export default useLanguage;

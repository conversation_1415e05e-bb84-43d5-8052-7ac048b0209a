// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  Chip,
  Avatar,
  AvatarGroup,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  useTheme,
  Tab,
  Tabs,
  IconButton,
  Tooltip,
  Container,
  Alert,
  AlertTitle
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import GroupsIcon from '@mui/icons-material/Groups';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import MailIcon from '@mui/icons-material/Mail';
import { useAuth } from '../../contexts/AuthContext';
import { useTeam } from '../../contexts/TeamContext';
import { useConfirmation } from '../../contexts/ConfirmationContext';
import PageHeader from '../../components/common/PageHeader';
import NoDataPlaceholder from '../../components/common/NoDataPlaceholder';
import TeamInvitationList from '../../components/teams/TeamInvitationList';

const TeamsPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { teams, loading, fetchTeams, createTeam, deleteTeam } = useTeam();
  const { showConfirmation } = useConfirmation();

  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [newTeamName, setNewTeamName] = useState('');
  const [newTeamDescription, setNewTeamDescription] = useState('');
  const [createLoading, setCreateLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  // Authentication check with redirect
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Fetch teams on mount (only if authenticated)
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      fetchTeams();
    }
  }, [fetchTeams, isAuthenticated, authLoading]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle create dialog open
  const handleCreateDialogOpen = () => {
    setOpenCreateDialog(true);
  };

  // Handle create dialog close
  const handleCreateDialogClose = () => {
    setOpenCreateDialog(false);
    setNewTeamName('');
    setNewTeamDescription('');
  };

  // Handle create team with authentication check
  const handleCreateTeam = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    if (!newTeamName.trim()) return;

    setCreateLoading(true);

    try {
      const team = await createTeam({
        name: newTeamName.trim(),
        description: newTeamDescription.trim() || undefined
      });

      if (team) {
        handleCreateDialogClose();
        navigate(`/teams/${team.id}`);
      }
    } finally {
      setCreateLoading(false);
    }
  };

  // Handle delete team with authentication check
  const handleDeleteTeam = (team) => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    showConfirmation({
      title: 'Delete Team',
      message: `Are you sure you want to delete the team "${team.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      confirmColor: 'error',
      onConfirm: async () => {
        await deleteTeam(team.id);
      }
    });
  };

  // Render team cards
  const renderTeamCards = () => {
    if (teams.length === 0) {
      return (
        <NoDataPlaceholder
          icon={<GroupsIcon sx={{ fontSize: 64 }} />}
          title="No Teams Found"
          description="Create your first team to start collaborating with others."
          actionText="Create Team"
          onAction={handleCreateDialogOpen}
        />
      );
    }

    return (
      <Grid container spacing={3}>
        {teams.map((team) => (
          <Grid item xs={12} sm={6} md={4} key={team.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: theme.shadows[8]
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" component="h2" noWrap>
                    {team.name}
                  </Typography>
                  <Chip
                    size="small"
                    label={`${team.members.length} ${team.members.length === 1 ? 'Member' : 'Members'}`}
                    color="primary"
                    variant="outlined"
                  />
                </Box>

                {team.description && (
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      mb: 2,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}
                  >
                    {team.description}
                  </Typography>
                )}

                <Divider sx={{ my: 1.5 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    Team Members:
                  </Typography>
                  <AvatarGroup max={3} sx={{ '& .MuiAvatar-root': { width: 28, height: 28, fontSize: '0.875rem' } }}>
                    {team.members.map((member) => (
                      <Tooltip title={member.full_name} key={member.user_id}>
                        <Avatar
                          alt={member.full_name}
                          src={member.avatar}
                          sx={{
                            bgcolor: !member.avatar ? theme.palette.primary.main : undefined
                          }}
                        >
                          {!member.avatar && member.full_name.charAt(0).toUpperCase()}
                        </Avatar>
                      </Tooltip>
                    ))}
                  </AvatarGroup>
                </Box>
              </CardContent>

              <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => navigate(`/teams/${team.id}`)}
                >
                  View Team
                </Button>

                <Box>
                  <Tooltip title="Invite Members">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => navigate(`/teams/${team.id}?tab=invite`)}
                      sx={{ mr: 1 }}
                    >
                      <MailIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Edit Team">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => navigate(`/teams/${team.id}?tab=settings`)}
                      sx={{ mr: 1 }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>

                  <Tooltip title="Delete Team">
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteTeam(team)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  if (loading && teams.length === 0) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: 300, p: 3 }}>
        <CircularProgress size={40} />
        <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
          Loading teams...
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Preparing teams for {user?.name || 'user'}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <PageHeader
        title="Teams"
        subtitle={
          <Box>
            <Typography variant="body1" color="text.secondary">
              Manage your teams and collaborations
            </Typography>
            {user?.name && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Welcome, {user.name} • {teams.length} {teams.length === 1 ? 'team' : 'teams'}
              </Typography>
            )}
          </Box>
        }
        action={
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateDialogOpen}
            disabled={!isAuthenticated}
          >
            Create Team
          </Button>
        }
      />

      {/* Authentication status indicator */}
      {!isAuthenticated && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <AlertTitle>Authentication Required</AlertTitle>
          <Typography variant="body2">
            Please log in to access your teams and manage collaborations.
            <Button
              variant="text"
              size="small"
              onClick={() => navigate('/login')}
              sx={{ ml: 1 }}
            >
              Log In
            </Button>
          </Typography>
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="My Teams" disabled={!isAuthenticated} />
          <Tab label="Invitations" disabled={!isAuthenticated} />
        </Tabs>
      </Box>

      <Box sx={{ mt: 2 }}>
        {tabValue === 0 ? (
          renderTeamCards()
        ) : (
          <TeamInvitationList />
        )}
      </Box>

      {/* Create Team Dialog */}
      <Dialog open={openCreateDialog} onClose={handleCreateDialogClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box>
            <Typography variant="h6">Create New Team</Typography>
            {user?.name && (
              <Typography variant="body2" color="text.secondary">
                Owner: {user.name}
              </Typography>
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Team Name"
            fullWidth
            variant="outlined"
            value={newTeamName}
            onChange={(e) => setNewTeamName(e.target.value)}
            required
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description (Optional)"
            fullWidth
            variant="outlined"
            value={newTeamDescription}
            onChange={(e) => setNewTeamDescription(e.target.value)}
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCreateDialogClose}>Cancel</Button>
          <Button
            onClick={handleCreateTeam}
            variant="contained"
            disabled={!newTeamName.trim() || createLoading}
            startIcon={createLoading ? <CircularProgress size={20} /> : null}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TeamsPage;

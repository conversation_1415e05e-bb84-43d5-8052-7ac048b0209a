# @since 2024-1-1 to 2025-25-7
# MongoDB Installation Script for Windows
Write-Host "Starting MongoDB Community Server installation..." -ForegroundColor Green

# Create MongoDB directory
$mongoPath = "C:\Program Files\MongoDB"
$mongoDataPath = "C:\data\db"
$mongoLogPath = "C:\data\log"

Write-Host "Creating MongoDB directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path $mongoPath
New-Item -ItemType Directory -Force -Path $mongoDataPath
New-Item -ItemType Directory -Force -Path $mongoLogPath

# Download MongoDB
$downloadUrl = "https://fastdl.mongodb.org/windows/mongodb-windows-x86_64-8.0.10.zip"
$zipFile = "$env:TEMP\mongodb.zip"
$extractPath = "$env:TEMP\mongodb-extract"

Write-Host "Downloading MongoDB Community Server 8.0.10..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -UseBasicParsing
    Write-Host "Download completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Download failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Extract MongoDB
Write-Host "Extracting MongoDB..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $zipFile -DestinationPath $extractPath -Force
    
    # Find the extracted folder (it will have a version-specific name)
    $extractedFolder = Get-ChildItem -Path $extractPath -Directory | Select-Object -First 1
    $sourcePath = $extractedFolder.FullName
    
    # Copy files to Program Files
    Copy-Item -Path "$sourcePath\*" -Destination $mongoPath -Recurse -Force
    Write-Host "MongoDB extracted and installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Add MongoDB to PATH
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
$mongoBinPath = "$mongoPath\bin"

if ($currentPath -notlike "*$mongoBinPath*") {
    Write-Host "Adding MongoDB to system PATH..." -ForegroundColor Yellow
    [Environment]::SetEnvironmentVariable("Path", "$currentPath;$mongoBinPath", "Machine")
    Write-Host "MongoDB added to PATH!" -ForegroundColor Green
}

# Create MongoDB configuration file
$configContent = @"
systemLog:
  destination: file
  path: C:\data\log\mongod.log
storage:
  dbPath: C:\data\db
net:
  port: 27017
  bindIp: 127.0.0.1
"@

$configPath = "$mongoPath\mongod.conf"
$configContent | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "MongoDB configuration file created at: $configPath" -ForegroundColor Green

# Create Windows service
Write-Host "Creating MongoDB Windows service..." -ForegroundColor Yellow
try {
    & "$mongoBinPath\mongod.exe" --config "$configPath" --install --serviceName "MongoDB"
    Write-Host "MongoDB service created successfully!" -ForegroundColor Green
} catch {
    Write-Host "Service creation failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Start MongoDB service
Write-Host "Starting MongoDB service..." -ForegroundColor Yellow
try {
    Start-Service -Name "MongoDB"
    Write-Host "MongoDB service started successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to start MongoDB service: $($_.Exception.Message)" -ForegroundColor Red
}

# Cleanup
Remove-Item -Path $zipFile -Force -ErrorAction SilentlyContinue
Remove-Item -Path $extractPath -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "`nMongoDB installation completed!" -ForegroundColor Green
Write-Host "MongoDB is now running on localhost:27017" -ForegroundColor Cyan
Write-Host "Data directory: $mongoDataPath" -ForegroundColor Cyan
Write-Host "Log directory: $mongoLogPath" -ForegroundColor Cyan
Write-Host "Configuration file: $configPath" -ForegroundColor Cyan

# Test connection
Write-Host "`nTesting MongoDB connection..." -ForegroundColor Yellow
try {
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    & "$mongoBinPath\mongosh.exe" --eval "db.runCommand({ping: 1})" --quiet
    Write-Host "MongoDB connection test successful!" -ForegroundColor Green
} catch {
    Write-Host "Connection test failed. MongoDB may still be starting up." -ForegroundColor Yellow
}

Write-Host "`nInstallation complete! You may need to restart your terminal to use 'mongod' and 'mongosh' commands." -ForegroundColor Green

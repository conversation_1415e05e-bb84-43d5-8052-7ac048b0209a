/**
 * TypeScript definitions for world-countries.json
 * Enhanced GeoJSON structure with comprehensive country metadata
 *
 * @version 1.0.0
 * <AUTHOR> Social Platform
 * @description Production-ready TypeScript definitions for world countries data
 */

// Strict type definitions for better type safety
export type Longitude = number; // -180 to 180
export type Latitude = number;  // -90 to 90
export type Coordinates = readonly [Longitude, Latitude];

// ISO standard types with validation constraints
export type ISO3166Alpha2 = string; // 2-letter country code (e.g., 'US', 'CA')
export type ISO3166Alpha3 = string; // 3-letter country code (e.g., 'USA', 'CAN')
export type ISO3166Numeric = string; // 3-digit numeric code (e.g., '840', '124')
export type ISO4217CurrencyCode = string; // 3-letter currency code (e.g., 'USD', 'EUR')
export type CallingCode = string; // International calling code (e.g., '+1', '+44')
export type TopLevelDomain = string; // Country TLD (e.g., '.us', '.ca')
export type FlagEmoji = string; // Unicode flag emoji (e.g., '🇺🇸', '🇨🇦')
export type TimezoneString = string; // Timezone description (e.g., 'UTC-5 to UTC-10')

// Geographic region types
export type ContinentName =
  | 'Africa'
  | 'Antarctica'
  | 'Asia'
  | 'Europe'
  | 'North America'
  | 'Oceania'
  | 'South America';

export type RegionName = string; // UN M49 region names
export type SubregionName = string; // UN M49 subregion names

export interface CountryBounds {
  readonly north: Latitude;   // Northernmost latitude
  readonly south: Latitude;   // Southernmost latitude
  readonly east: Longitude;   // Easternmost longitude
  readonly west: Longitude;   // Westernmost longitude
}

export interface CountryProperties {
  readonly name: string;
  readonly name_long: string;
  readonly iso_a2: ISO3166Alpha2;
  readonly iso_a3: ISO3166Alpha3;
  readonly iso_n3: ISO3166Numeric;
  readonly continent: ContinentName;
  readonly region: RegionName;
  readonly subregion: SubregionName;
  readonly capital: string;
  readonly population: number;
  readonly area_km2: number;
  readonly currency_code: ISO4217CurrencyCode;
  readonly currency_name: string;
  readonly languages: readonly string[];
  readonly timezone: TimezoneString;
  readonly calling_code: CallingCode;
  readonly tld: TopLevelDomain;
  readonly flag_emoji: FlagEmoji;
  readonly coordinates: Coordinates;
  readonly bounds: CountryBounds;
}

export interface CountryFeature {
  type: "Feature";
  properties: CountryProperties;
  geometry: {
    type: "MultiPolygon";
    coordinates: number[][][][]; // GeoJSON MultiPolygon coordinates
  };
  id: string;
}

export interface WorldCountriesMetadata {
  version: string;
  description: string;
  lastUpdated: string;
  source: string;
  coordinateSystem: string;
  totalCountries: number;
  note: string;
}

export interface WorldCountriesGeoJSON {
  type: "FeatureCollection";
  metadata: WorldCountriesMetadata;
  features: CountryFeature[];
}

// Type guard functions for runtime validation
export function isCountryBounds(obj: unknown): obj is CountryBounds {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof (obj as Record<string, unknown>).north === 'number' &&
    typeof (obj as Record<string, unknown>).south === 'number' &&
    typeof (obj as Record<string, unknown>).east === 'number' &&
    typeof (obj as Record<string, unknown>).west === 'number'
  );
}

export function isCountryProperties(obj: unknown): obj is CountryProperties {
  if (typeof obj !== 'object' || obj === null) return false;

  const props = obj as Record<string, unknown>;
  return (
    typeof props.name === 'string' &&
    typeof props.name_long === 'string' &&
    typeof props.iso_a2 === 'string' &&
    typeof props.iso_a3 === 'string' &&
    typeof props.iso_n3 === 'string' &&
    typeof props.continent === 'string' &&
    typeof props.region === 'string' &&
    typeof props.subregion === 'string' &&
    typeof props.capital === 'string' &&
    typeof props.population === 'number' &&
    typeof props.area_km2 === 'number' &&
    typeof props.currency_code === 'string' &&
    typeof props.currency_name === 'string' &&
    Array.isArray(props.languages) &&
    typeof props.timezone === 'string' &&
    typeof props.calling_code === 'string' &&
    typeof props.tld === 'string' &&
    typeof props.flag_emoji === 'string' &&
    Array.isArray(props.coordinates) &&
    props.coordinates.length === 2 &&
    typeof props.coordinates[0] === 'number' &&
    typeof props.coordinates[1] === 'number' &&
    isCountryBounds(props.bounds)
  );
}

export function isCountryFeature(obj: unknown): obj is CountryFeature {
  if (typeof obj !== 'object' || obj === null) return false;

  const feature = obj as Record<string, unknown>;
  return (
    feature.type === "Feature" &&
    isCountryProperties(feature.properties) &&
    typeof feature.geometry === 'object' &&
    feature.geometry !== null &&
    (feature.geometry as Record<string, unknown>).type === "MultiPolygon" &&
    Array.isArray((feature.geometry as Record<string, unknown>).coordinates) &&
    typeof feature.id === 'string'
  );
}

export function isWorldCountriesMetadata(obj: unknown): obj is WorldCountriesMetadata {
  if (typeof obj !== 'object' || obj === null) return false;

  const metadata = obj as Record<string, unknown>;
  return (
    typeof metadata.version === 'string' &&
    typeof metadata.description === 'string' &&
    typeof metadata.lastUpdated === 'string' &&
    typeof metadata.source === 'string' &&
    typeof metadata.coordinateSystem === 'string' &&
    typeof metadata.totalCountries === 'number' &&
    typeof metadata.note === 'string'
  );
}

export function isWorldCountriesGeoJSON(obj: unknown): obj is WorldCountriesGeoJSON {
  if (typeof obj !== 'object' || obj === null) return false;

  const geoJson = obj as Record<string, unknown>;
  return (
    geoJson.type === "FeatureCollection" &&
    isWorldCountriesMetadata(geoJson.metadata) &&
    Array.isArray(geoJson.features) &&
    geoJson.features.every(isCountryFeature)
  );
}

// Legacy type aliases for backward compatibility
export type CountryCode = ISO3166Alpha2;
export type CountryCode3 = ISO3166Alpha3;
export type CurrencyCode = ISO4217CurrencyCode;
export type LanguageCode = string; // Language name or ISO 639 code

// Helper interfaces for common use cases with enhanced type safety
export interface CountryBasicInfo {
  readonly name: string;
  readonly code: ISO3166Alpha2;
  readonly code3: ISO3166Alpha3;
  readonly flag: FlagEmoji;
  readonly capital: string;
  readonly continent: ContinentName;
}

export interface CountryLocationInfo {
  readonly name: string;
  readonly coordinates: Coordinates;
  readonly bounds: CountryBounds;
  readonly timezone: TimezoneString;
}

export interface CountryEconomicInfo {
  readonly name: string;
  readonly population: number;
  readonly area_km2: number;
  readonly currency_code: ISO4217CurrencyCode;
  readonly currency_name: string;
  readonly calling_code: CallingCode;
}

export interface CountrySearchResult {
  readonly name: string;
  readonly name_long: string;
  readonly iso_a2: ISO3166Alpha2;
  readonly iso_a3: ISO3166Alpha3;
  readonly flag_emoji: FlagEmoji;
  readonly capital: string;
  readonly continent: ContinentName;
  readonly population: number;
}

// Utility types for filtering and searching
export type CountryFilterBy =
  | 'continent'
  | 'region'
  | 'subregion'
  | 'currency'
  | 'language'
  | 'population'
  | 'area';

export type SortOrder = 'asc' | 'desc';

export interface CountrySearchOptions {
  readonly query?: string;
  readonly limit?: number;
  readonly filterBy?: CountryFilterBy;
  readonly filterValue?: string;
  readonly sortBy?: keyof CountryProperties;
  readonly sortOrder?: SortOrder;
}

// Error types for better error handling
export class CountryNotFoundError extends Error {
  constructor(identifier: string, type: 'code' | 'code3' | 'name' = 'code') {
    super(`Country not found: ${identifier} (${type})`);
    this.name = 'CountryNotFoundError';
  }
}

export class InvalidCountryCodeError extends Error {
  constructor(code: string, expectedFormat: 'ISO2' | 'ISO3') {
    super(`Invalid country code format: ${code} (expected ${expectedFormat})`);
    this.name = 'InvalidCountryCodeError';
  }
}

// Constants for validation
export const VALID_ISO2_PATTERN = /^[A-Z]{2}$/;
export const VALID_ISO3_PATTERN = /^[A-Z]{3}$/;
export const VALID_NUMERIC_PATTERN = /^\d{3}$/;
export const VALID_CURRENCY_PATTERN = /^[A-Z]{3}$/;

// Available continents for type checking
export const CONTINENTS = [
  'Africa',
  'Antarctica',
  'Asia',
  'Europe',
  'North America',
  'Oceania',
  'South America'
] as const;

// Utility type for extracting country codes
export type AvailableCountryCode = ISO3166Alpha2;
export type AvailableCountryCode3 = ISO3166Alpha3;
export type AvailableCurrency = ISO4217CurrencyCode;

// Generic utility types
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

export type PartialCountryProperties = Partial<CountryProperties>;
export type RequiredCountryProperties = Required<CountryProperties>;

// Function signature types for utility functions
/* eslint-disable no-unused-vars */
export type CountryLookupFunction<T = CountryFeature | null> = (identifier: string) => T;
export type CountrySearchFunction = (query: string, options?: CountrySearchOptions) => CountryFeature[];
export type CountryValidationFunction = (code: string, type?: 'iso2' | 'iso3') => boolean;
/* eslint-enable no-unused-vars */

// Export the JSON data type with enhanced typing
declare const worldCountries: DeepReadonly<WorldCountriesGeoJSON>;
export default worldCountries;

// Module augmentation for better IDE support
declare module '*.json' {
  const value: WorldCountriesGeoJSON;
  export default value;
}

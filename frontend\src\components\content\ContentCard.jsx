/**
 * @fileoverview ContentCard - Enterprise-grade content card component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @description Advanced content card component with comprehensive display modes, interactive features,
 * analytics integration, and enterprise-grade functionality following ACE Social platform standards.
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useImperativeHandle,
  forwardRef,
  memo
} from 'react';
import PropTypes from 'prop-types';
import {
  Typography,
  Box,
  Chip,
  IconButton,
  Checkbox,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useTheme,
  alpha,
  Tooltip,
  Skeleton,
  Button,
  CircularProgress,
  Card,
  CardContent,
  CardMedia
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as ContentCopyIcon,
  Share as ShareIcon,
  Schedule as ScheduleIcon,
  Archive as ArchiveIcon,
  Analytics as AnalyticsIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  AccessTime as TimeIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Description as DescriptionIcon,
  Image as ImageIcon,
  VideoLibrary as VideoIcon,
  Article as ArticleIcon,
  Poll as PollIcon,
  Event as EventIcon
} from '@mui/icons-material';
import { format, formatDistanceToNow, isValid, parseISO } from 'date-fns';



// Enhanced common components
import {
  ErrorBoundary
} from "../common";

// Enhanced utility imports
import {
  announceToScreenReader,
  debounce,
  truncateText
} from "../../utils/helpers";

// Mock hooks for missing dependencies
const useAnalytics = () => ({
  trackEvent: () => {},
  trackError: () => {},
  trackPerformance: () => {}
});

const useDebounce = (callback, delay) => {
  return useCallback((...args) => debounce(callback, delay)(...args), [callback, delay]);
};

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Content card view modes
const VIEW_MODES = {
  COMPACT: 'compact',
  DETAILED: 'detailed',
  ANALYTICS: 'analytics',
  GRID: 'grid',
  LIST: 'list',
  PREVIEW: 'preview'
};

// Content status types
const CONTENT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  SCHEDULED: 'scheduled',
  ARCHIVED: 'archived',
  FAILED: 'failed',
  PROCESSING: 'processing',
  PENDING: 'pending'
};

// Content types
const CONTENT_TYPES = {
  POST: 'post',
  ARTICLE: 'article',
  VIDEO: 'video',
  IMAGE: 'image',
  CAROUSEL: 'carousel',
  STORY: 'story',
  REEL: 'reel',
  THREAD: 'thread',
  POLL: 'poll',
  EVENT: 'event'
};



// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Component configuration
const COMPONENT_CONFIG = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 300,
  SKELETON_LINES: 3,
  MAX_TAGS_DISPLAY: 3,
  MAX_PLATFORMS_DISPLAY: 4,
  DESCRIPTION_MAX_LENGTH: 120,
  TITLE_MAX_LENGTH: 60,
  AUTO_REFRESH_INTERVAL: 30000,
  LAZY_LOAD_THRESHOLD: 100
};

// Subscription plan limits
const PLAN_LIMITS = {
  creator: {
    maxAnalyticsDepth: 'basic',
    allowedActions: ['view', 'edit', 'delete', 'duplicate'],
    maxInteractions: 100
  },
  accelerator: {
    maxAnalyticsDepth: 'advanced',
    allowedActions: ['view', 'edit', 'delete', 'duplicate', 'schedule', 'archive', 'share'],
    maxInteractions: 500
  },
  dominator: {
    maxAnalyticsDepth: 'enterprise',
    allowedActions: ['view', 'edit', 'delete', 'duplicate', 'schedule', 'archive', 'share', 'analytics', 'export'],
    maxInteractions: 'unlimited'
  }
};



// ===========================
// STYLED COMPONENTS
// ===========================

// Enhanced content card container
const EnhancedCard = styled(Card)(({ theme, viewMode, selected, isLoading }) => ({
  position: 'relative',
  height: viewMode === VIEW_MODES.COMPACT ? 200 : viewMode === VIEW_MODES.LIST ? 120 : 320,
  display: 'flex',
  flexDirection: viewMode === VIEW_MODES.LIST ? 'row' : 'column',
  borderRadius: theme.shape.borderRadius * 2,
  overflow: 'hidden',
  transition: theme.transitions.create(['all'], {
    duration: COMPONENT_CONFIG.ANIMATION_DURATION,
  }),
  border: selected
    ? `2px solid ${ACE_COLORS.PURPLE}`
    : `1px solid ${alpha(theme.palette.divider, 0.12)}`,
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.9)})`,
  backdropFilter: 'blur(10px)',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`,
  },
  ...(isLoading && {
    pointerEvents: 'none',
    opacity: 0.7
  })
}));

// Enhanced card media with overlay support
const EnhancedCardMedia = styled(CardMedia)(({ theme, viewMode }) => ({
  height: viewMode === VIEW_MODES.COMPACT ? 120 : viewMode === VIEW_MODES.LIST ? 120 : 180,
  width: viewMode === VIEW_MODES.LIST ? 120 : '100%',
  position: 'relative',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `linear-gradient(to bottom, transparent 0%, ${alpha(theme.palette.common.black, 0.1)} 100%)`,
    pointerEvents: 'none'
  }
}));

// Status indicator chip
const StatusChip = styled(Chip)(({ theme, status }) => ({
  borderRadius: theme.shape.borderRadius,
  fontWeight: 600,
  fontSize: '0.7rem',
  height: 20,
  ...(status === CONTENT_STATUS.PUBLISHED && {
    backgroundColor: alpha(theme.palette.success.main, 0.1),
    color: theme.palette.success.main,
    border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`
  }),
  ...(status === CONTENT_STATUS.SCHEDULED && {
    backgroundColor: alpha(theme.palette.info.main, 0.1),
    color: theme.palette.info.main,
    border: `1px solid ${alpha(theme.palette.info.main, 0.3)}`
  }),
  ...(status === CONTENT_STATUS.DRAFT && {
    backgroundColor: alpha(theme.palette.grey[500], 0.1),
    color: theme.palette.grey[600],
    border: `1px solid ${alpha(theme.palette.grey[500], 0.3)}`
  }),
  ...(status === CONTENT_STATUS.FAILED && {
    backgroundColor: alpha(theme.palette.error.main, 0.1),
    color: theme.palette.error.main,
    border: `1px solid ${alpha(theme.palette.error.main, 0.3)}`
  })
}));



// Action button with enhanced styling
const ActionButton = styled(IconButton)(({ theme, variant = 'default' }) => ({
  borderRadius: theme.shape.borderRadius,
  transition: theme.transitions.create(['all'], {
    duration: COMPONENT_CONFIG.ANIMATION_DURATION,
  }),
  ...(variant === 'primary' && {
    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
    color: ACE_COLORS.PURPLE,
    '&:hover': {
      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2),
      transform: 'scale(1.05)',
    }
  }),
  ...(variant === 'danger' && {
    backgroundColor: alpha(theme.palette.error.main, 0.1),
    color: theme.palette.error.main,
    '&:hover': {
      backgroundColor: alpha(theme.palette.error.main, 0.2),
      transform: 'scale(1.05)',
    }
  })
}));

// ===========================
// MAIN COMPONENT
// ===========================

/**
 * ContentCard - Enterprise-grade content card component
 *
 * @param {Object} props - Component props
 * @param {Object} props.content - Content data object
 * @param {boolean} props.selected - Whether the card is selected
 * @param {string} props.viewMode - Display mode (compact, detailed, analytics, etc.)
 * @param {Function} props.onSelect - Callback for selection
 * @param {Function} props.onView - Callback for viewing content
 * @param {Function} props.onEdit - Callback for editing content
 * @param {Function} props.onDelete - Callback for deleting content
 * @param {Function} props.onDuplicate - Callback for duplicating content
 * @param {Function} props.onSchedule - Callback for scheduling content
 * @param {Function} props.onShare - Callback for sharing content
 * @param {Function} props.onArchive - Callback for archiving content
 * @param {Function} props.onAnalytics - Callback for viewing analytics
 * @param {Function} props.onFavorite - Callback for favoriting content
 * @param {Function} props.onBookmark - Callback for bookmarking content
 * @param {Function} props.onMenuOpen - Callback for menu open
 * @param {Function} props.onInteraction - Callback for tracking interactions
 * @param {string} props.userPlan - User subscription plan
 * @param {boolean} props.enableAnalytics - Enable analytics tracking
 * @param {boolean} props.enableAccessibility - Enable accessibility features
 * @param {boolean} props.showMetrics - Show performance metrics
 * @param {boolean} props.showActions - Show action buttons
 * @param {boolean} props.isLoading - Loading state
 * @param {string} props.testId - Test identifier
 * @param {string} props.ariaLabel - Accessibility label
 * @param {boolean} props.announceChanges - Announce changes to screen readers
 */
const ContentCard = memo(forwardRef(({
  // Content props
  content,

  // Selection props
  selected = false,

  // View props
  viewMode = VIEW_MODES.DETAILED,

  // Callback props
  onSelect,
  onView,
  onEdit,
  onDelete,
  onDuplicate,
  onSchedule,
  onShare,
  onArchive,
  onAnalytics,
  onFavorite,
  onBookmark,
  onMenuOpen,
  onInteraction,

  // Enhanced props
  userPlan = 'creator',
  enableAnalytics = true,
  enableAccessibility = true,
  showActions = true,
  isLoading = false,

  // Testing props
  testId = 'content-card',

  // Accessibility props
  ariaLabel,
  announceChanges = true
}, ref) => {
  // ===========================
  // HOOKS & CONTEXT
  // ===========================

  const theme = useTheme();

  // Enhanced hooks
  const { trackEvent } = useAnalytics();

  // ===========================
  // STATE MANAGEMENT
  // ===========================

  // UI state
  const [anchorEl, setAnchorEl] = useState(null);
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);

  // Interaction state
  const [isFavorited, setIsFavorited] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [interactionCount, setInteractionCount] = useState(0);

  // Error handling
  const [lastError, setLastError] = useState(null);

  // Refs
  const cardRef = useRef(null);
  const imageRef = useRef(null);

  // Debounced functions
  const debouncedTrackEvent = useDebounce(trackEvent, 300);

  // ===========================
  // COMPUTED VALUES
  // ===========================

  // Process content safely
  const safeContent = useMemo(() => {
    try {
      // Create default content object with all required properties
      const defaultContent = {
        id: 'unknown',
        title: 'Untitled Content',
        description: '',
        platforms: [],
        tags: [],
        status: 'draft',
        created_at: new Date().toISOString(),
        type: 'post',
        content_type: 'post',
        image_url: null,
        metrics: {
          views: 0,
          likes: 0,
          comments: 0,
          shares: 0,
          engagement: 0,
          reach: 0,
          impressions: 0
        }
      };

      // If content is null/undefined, use default content
      if (!content) {
        return defaultContent;
      }

      // Otherwise, merge content with default values for missing properties
      return {
        ...defaultContent,
        // Safely extract properties from content with fallbacks
        id: content.id || defaultContent.id,
        title: content.title || defaultContent.title,
        description: content.description || defaultContent.description,
        platforms: Array.isArray(content.platforms) ? content.platforms : defaultContent.platforms,
        tags: Array.isArray(content.tags) ? content.tags : defaultContent.tags,
        status: content.status || defaultContent.status,
        created_at: content.created_at || defaultContent.created_at,
        updated_at: content.updated_at || content.created_at || defaultContent.created_at,
        // Handle type and content_type safely
        type: (content.type || content.content_type || defaultContent.type),
        content_type: (content.content_type || content.type || defaultContent.content_type),
        image_url: content.image_url || defaultContent.image_url,
        // Merge metrics safely
        metrics: {
          ...defaultContent.metrics,
          ...(content.metrics || {})
        }
      };
    } catch (error) {
      // If any error occurs during content processing, use a completely safe default object
      console.error('Error processing content in ContentCard:', error);
      setLastError(error);
      return {
        id: 'error',
        title: 'Error Loading Content',
        description: 'There was an error loading this content item.',
        platforms: [],
        tags: [],
        status: 'draft',
        created_at: new Date().toISOString(),
        type: 'post',
        content_type: 'post',
        image_url: null,
        metrics: {
          views: 0,
          likes: 0,
          comments: 0,
          shares: 0,
          engagement: 0,
          reach: 0,
          impressions: 0
        }
      };
    }
  }, [content]);

  // Get plan limits
  const planLimits = useMemo(() => {
    return PLAN_LIMITS[userPlan] || PLAN_LIMITS.creator;
  }, [userPlan]);

  // Check if action is allowed
  const isActionAllowed = useCallback((action) => {
    return planLimits.allowedActions.includes(action);
  }, [planLimits]);

  // Truncated description
  const truncatedDescription = useMemo(() => {
    if (!safeContent.description) return '';
    return truncateText(safeContent.description, COMPONENT_CONFIG.DESCRIPTION_MAX_LENGTH);
  }, [safeContent.description]);

  // Truncated title
  const truncatedTitle = useMemo(() => {
    if (!safeContent.title) return 'Untitled Content';
    return truncateText(safeContent.title, COMPONENT_CONFIG.TITLE_MAX_LENGTH);
  }, [safeContent.title]);

  // Formatted date
  const formattedDate = useMemo(() => {
    try {
      if (!safeContent.created_at) {
        return format(new Date(), 'MMM d, yyyy');
      }

      const dateObj = parseISO(safeContent.created_at);
      if (!isValid(dateObj)) {
        return format(new Date(), 'MMM d, yyyy');
      }

      return format(dateObj, 'MMM d, yyyy');
    } catch (error) {
      console.error('Error formatting date:', error);
      return format(new Date(), 'MMM d, yyyy');
    }
  }, [safeContent.created_at]);

  // Relative date
  const relativeDate = useMemo(() => {
    try {
      if (!safeContent.created_at) {
        return 'just now';
      }

      const dateObj = parseISO(safeContent.created_at);
      if (!isValid(dateObj)) {
        return 'just now';
      }

      return formatDistanceToNow(dateObj, { addSuffix: true });
    } catch (error) {
      console.error('Error formatting relative date:', error);
      return 'just now';
    }
  }, [safeContent.created_at]);

  // Expose ref methods
  useImperativeHandle(ref, () => ({
    focus: () => cardRef.current?.focus(),
    scrollIntoView: () => cardRef.current?.scrollIntoView({ behavior: 'smooth' }),
    getContent: () => safeContent,
    refresh: () => handleRefresh(),
    select: () => handleSelect()
  }), [safeContent, handleRefresh, handleSelect]);

  // ===========================
  // EFFECTS
  // ===========================

  // Announce changes to screen readers
  useEffect(() => {
    if (announceChanges && enableAccessibility && lastError) {
      announceToScreenReader(`Error loading content: ${lastError.message}`);
    }
  }, [lastError, announceChanges, enableAccessibility]);

  // ===========================
  // UTILITY FUNCTIONS
  // ===========================

  /**
   * Get content type icon
   */
  const getContentTypeIcon = useCallback((type) => {
    switch (type) {
      case CONTENT_TYPES.VIDEO:
        return <VideoIcon fontSize="small" />;
      case CONTENT_TYPES.IMAGE:
        return <ImageIcon fontSize="small" />;
      case CONTENT_TYPES.ARTICLE:
        return <ArticleIcon fontSize="small" />;
      case CONTENT_TYPES.POLL:
        return <PollIcon fontSize="small" />;
      case CONTENT_TYPES.EVENT:
        return <EventIcon fontSize="small" />;
      default:
        return <DescriptionIcon fontSize="small" />;
    }
  }, []);

  // ===========================
  // EVENT HANDLERS
  // ===========================

  /**
   * Handle analytics tracking
   */
  const handleAnalytics = useCallback((eventName, data = {}) => {
    if (!enableAnalytics) return;

    const analyticsData = {
      ...data,
      component: 'ContentCard',
      contentId: safeContent.id,
      contentType: safeContent.type,
      userPlan,
      viewMode,
      interactionCount
    };

    debouncedTrackEvent(eventName, analyticsData);
  }, [enableAnalytics, safeContent.id, safeContent.type, userPlan, viewMode, interactionCount, debouncedTrackEvent]);

  /**
   * Handle card click
   */
  const handleCardClick = useCallback((event) => {
    event.preventDefault();

    if (isLoading) return;

    setInteractionCount(prev => prev + 1);

    handleAnalytics('content_card_clicked', {
      action: 'view',
      clickPosition: { x: event.clientX, y: event.clientY }
    });

    if (onView) {
      onView(safeContent);
    }

    if (onInteraction) {
      onInteraction('view', safeContent);
    }
  }, [isLoading, handleAnalytics, onView, onInteraction, safeContent]);

  /**
   * Handle selection
   */
  const handleSelect = useCallback((event) => {
    if (event) {
      event.stopPropagation();
    }

    if (isLoading) return;

    handleAnalytics('content_card_selected', {
      action: 'select',
      selected: !selected
    });

    if (onSelect) {
      onSelect(safeContent);
    }

    if (onInteraction) {
      onInteraction('select', safeContent);
    }
  }, [isLoading, selected, handleAnalytics, onSelect, onInteraction, safeContent]);

  /**
   * Handle menu open
   */
  const handleMenuClick = useCallback((event) => {
    event.stopPropagation();

    if (isLoading) return;

    handleAnalytics('content_card_menu_opened');

    if (onMenuOpen) {
      onMenuOpen(event, safeContent);
    } else {
      setAnchorEl(event.currentTarget);
    }
  }, [isLoading, handleAnalytics, onMenuOpen, safeContent]);

  /**
   * Handle menu close
   */
  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  /**
   * Handle action execution
   */
  const handleAction = useCallback((action, event) => {
    if (event) {
      event.stopPropagation();
    }

    if (isLoading || !isActionAllowed(action)) return;

    handleMenuClose();

    handleAnalytics('content_card_action', {
      action,
      contentId: safeContent.id
    });

    switch (action) {
      case 'edit':
        onEdit?.(safeContent);
        break;
      case 'delete':
        onDelete?.(safeContent);
        break;
      case 'duplicate':
        onDuplicate?.(safeContent);
        break;
      case 'schedule':
        onSchedule?.(safeContent);
        break;
      case 'share':
        onShare?.(safeContent);
        break;
      case 'archive':
        onArchive?.(safeContent);
        break;
      case 'analytics':
        onAnalytics?.(safeContent);
        break;
      default:
        console.warn(`Unknown action: ${action}`);
    }

    if (onInteraction) {
      onInteraction(action, safeContent);
    }
  }, [isLoading, isActionAllowed, handleMenuClose, handleAnalytics, safeContent, onEdit, onDelete, onDuplicate, onSchedule, onShare, onArchive, onAnalytics, onInteraction]);

  /**
   * Handle favorite toggle
   */
  const handleFavorite = useCallback((event) => {
    event.stopPropagation();

    if (isLoading) return;

    setIsFavorited(prev => !prev);

    handleAnalytics('content_card_favorited', {
      favorited: !isFavorited
    });

    if (onFavorite) {
      onFavorite(safeContent, !isFavorited);
    }
  }, [isLoading, isFavorited, handleAnalytics, onFavorite, safeContent]);

  /**
   * Handle bookmark toggle
   */
  const handleBookmark = useCallback((event) => {
    event.stopPropagation();

    if (isLoading) return;

    setIsBookmarked(prev => !prev);

    handleAnalytics('content_card_bookmarked', {
      bookmarked: !isBookmarked
    });

    if (onBookmark) {
      onBookmark(safeContent, !isBookmarked);
    }
  }, [isLoading, isBookmarked, handleAnalytics, onBookmark, safeContent]);

  /**
   * Handle image load
   */
  const handleImageLoad = useCallback(() => {
    setIsImageLoaded(true);
    setImageError(false);
  }, []);

  /**
   * Handle image error
   */
  const handleImageError = useCallback(() => {
    setImageError(true);
    setIsImageLoaded(false);
  }, []);

  /**
   * Handle refresh
   */
  const handleRefresh = useCallback(() => {
    setLastError(null);
    setImageError(false);
    setIsImageLoaded(false);

    handleAnalytics('content_card_refreshed');
  }, [handleAnalytics]);

  // Early return for loading state
  if (isLoading) {
    return (
      <EnhancedCard viewMode={viewMode} isLoading={true} data-testid={`${testId}-loading`}>
        <Box sx={{ p: 2 }}>
          <Skeleton variant="rectangular" height={viewMode === VIEW_MODES.COMPACT ? 120 : 180} />
          <Box sx={{ pt: 2 }}>
            <Skeleton variant="text" height={24} width="80%" />
            <Skeleton variant="text" height={20} width="60%" />
            <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
              <Skeleton variant="rectangular" width={60} height={20} />
              <Skeleton variant="rectangular" width={60} height={20} />
            </Box>
          </Box>
        </Box>
      </EnhancedCard>
    );
  }

  // Early return for error state
  if (lastError) {
    return (
      <EnhancedCard
        viewMode={viewMode}
        data-testid={`${testId}-error`}
        sx={{
          border: `1px solid ${alpha(theme.palette.error.main, 0.3)}`,
          backgroundColor: alpha(theme.palette.error.main, 0.05)
        }}
      >
        <CardContent sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
          height: '100%'
        }}>
          <ErrorIcon color="error" sx={{ fontSize: 48, mb: 1 }} />
          <Typography variant="subtitle1" color="error" gutterBottom>
            Error Loading Content
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {lastError.message || 'There was a problem displaying this content item.'}
          </Typography>
          <Button
            size="small"
            onClick={handleRefresh}
            startIcon={<RefreshIcon />}
          >
            Retry
          </Button>
        </CardContent>
      </EnhancedCard>
    );
  }

  return (
    <ErrorBoundary>
      <EnhancedCard
        ref={cardRef}
        viewMode={viewMode}
        selected={selected}
        onClick={handleCardClick}
        data-testid={testId}
        aria-label={ariaLabel || `Content card: ${truncatedTitle}`}
        role="article"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleCardClick(e);
          }
        }}
      >
        {/* Selection checkbox */}
        <Checkbox
          checked={selected}
          onChange={handleSelect}
          onClick={(e) => e.stopPropagation()}
          sx={{
            position: 'absolute',
            top: 8,
            left: 8,
            zIndex: 3,
            bgcolor: alpha(theme.palette.background.paper, 0.8),
            borderRadius: '50%',
            '&:hover': {
              bgcolor: alpha(theme.palette.background.paper, 0.95)
            }
          }}
          aria-label={`Select ${truncatedTitle}`}
        />

        {/* Action buttons overlay */}
        {showActions && (
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              zIndex: 3,
              display: 'flex',
              gap: 0.5,
              opacity: 0,
              transition: theme.transitions.create(['opacity'], {
                duration: COMPONENT_CONFIG.ANIMATION_DURATION,
              }),
              '.MuiCard-root:hover &': {
                opacity: 1
              }
            }}
          >
            {/* Favorite button */}
            <Tooltip title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}>
              <ActionButton
                size="small"
                onClick={handleFavorite}
                variant={isFavorited ? 'primary' : 'default'}
                aria-label={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
              >
                {isFavorited ? <FavoriteIcon /> : <FavoriteBorderIcon />}
              </ActionButton>
            </Tooltip>

            {/* Bookmark button */}
            <Tooltip title={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}>
              <ActionButton
                size="small"
                onClick={handleBookmark}
                variant={isBookmarked ? 'primary' : 'default'}
                aria-label={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}
              >
                {isBookmarked ? <BookmarkIcon /> : <BookmarkBorderIcon />}
              </ActionButton>
            </Tooltip>

            {/* Menu button */}
            <Tooltip title="More actions">
              <ActionButton
                size="small"
                onClick={handleMenuClick}
                aria-label="More actions"
                aria-haspopup="true"
                aria-expanded={Boolean(anchorEl)}
              >
                <MoreVertIcon />
              </ActionButton>
            </Tooltip>
          </Box>
        )}

        {/* Content type indicator */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 8,
            left: 8,
            zIndex: 3,
            bgcolor: alpha(theme.palette.background.paper, 0.9),
            borderRadius: 1,
            p: 0.5,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5
          }}
        >
          {getContentTypeIcon(safeContent.type)}
          <Typography variant="caption" sx={{ fontSize: '0.6rem' }}>
            {safeContent.type?.toUpperCase()}
          </Typography>
        </Box>

        {/* Card media */}
        <EnhancedCardMedia
          ref={imageRef}
          viewMode={viewMode}
          component="div"
          sx={{
            backgroundImage: !imageError && safeContent.image_url
              ? `url(${safeContent.image_url})`
              : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative'
          }}
          onLoad={handleImageLoad}
          onError={handleImageError}
        >
          {(imageError || !safeContent.image_url) && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                color: 'text.secondary'
              }}
            >
              <ImageIcon sx={{ fontSize: 48, opacity: 0.5 }} />
              <Typography variant="caption" sx={{ mt: 1, opacity: 0.7 }}>
                No Image
              </Typography>
            </Box>
          )}

          {!isImageLoaded && !imageError && safeContent.image_url && (
            <CircularProgress size={24} />
          )}
        </EnhancedCardMedia>

        {/* Card content */}
        <CardContent sx={{
          flexGrow: 1,
          p: 2,
          '&:last-child': { pb: 2 }
        }}>
          {/* Title */}
          <Typography
            variant="subtitle1"
            component="h3"
            gutterBottom
            noWrap={viewMode === VIEW_MODES.COMPACT}
            fontWeight="600"
            sx={{
              color: 'text.primary',
              lineHeight: 1.3,
              ...(viewMode !== VIEW_MODES.COMPACT && {
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              })
            }}
          >
            {truncatedTitle}
          </Typography>

          {/* Description */}
          {viewMode !== VIEW_MODES.COMPACT && (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                mb: 2,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: showFullDescription ? 'none' : 2,
                WebkitBoxOrient: 'vertical',
                cursor: safeContent.description?.length > COMPONENT_CONFIG.DESCRIPTION_MAX_LENGTH ? 'pointer' : 'default'
              }}
              onClick={() => {
                if (safeContent.description?.length > COMPONENT_CONFIG.DESCRIPTION_MAX_LENGTH) {
                  setShowFullDescription(!showFullDescription);
                }
              }}
            >
              {showFullDescription ? safeContent.description : truncatedDescription}
            </Typography>
          )}

          {/* Status and date */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mt: 'auto',
            pt: 1
          }}>
            <StatusChip
              label={safeContent.status?.charAt(0).toUpperCase() + safeContent.status?.slice(1) || 'Draft'}
              size="small"
              status={safeContent.status}
            />

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TimeIcon sx={{ fontSize: 12, color: 'text.secondary' }} />
              <Tooltip title={formattedDate}>
                <Typography variant="caption" color="text.secondary">
                  {relativeDate}
                </Typography>
              </Tooltip>
            </Box>
          </Box>
        </CardContent>

        {/* Action menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          PaperProps={{
            sx: {
              borderRadius: 2,
              minWidth: 160,
              boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.12)}`
            }
          }}
        >
          {isActionAllowed('view') && (
            <MenuItem onClick={(e) => handleAction('view', e)}>
              <ListItemIcon>
                <VisibilityIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>View</ListItemText>
            </MenuItem>
          )}

          {isActionAllowed('edit') && (
            <MenuItem onClick={(e) => handleAction('edit', e)}>
              <ListItemIcon>
                <EditIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Edit</ListItemText>
            </MenuItem>
          )}

          {isActionAllowed('duplicate') && (
            <MenuItem onClick={(e) => handleAction('duplicate', e)}>
              <ListItemIcon>
                <ContentCopyIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Duplicate</ListItemText>
            </MenuItem>
          )}

          <Divider />

          {isActionAllowed('schedule') && (
            <MenuItem onClick={(e) => handleAction('schedule', e)}>
              <ListItemIcon>
                <ScheduleIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Schedule</ListItemText>
            </MenuItem>
          )}

          {isActionAllowed('share') && (
            <MenuItem onClick={(e) => handleAction('share', e)}>
              <ListItemIcon>
                <ShareIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Share</ListItemText>
            </MenuItem>
          )}

          {isActionAllowed('archive') && (
            <MenuItem onClick={(e) => handleAction('archive', e)}>
              <ListItemIcon>
                <ArchiveIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Archive</ListItemText>
            </MenuItem>
          )}

          {isActionAllowed('analytics') && (
            <>
              <Divider />
              <MenuItem onClick={(e) => handleAction('analytics', e)}>
                <ListItemIcon>
                  <AnalyticsIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>Analytics</ListItemText>
              </MenuItem>
            </>
          )}

          <Divider />

          {isActionAllowed('delete') && (
            <MenuItem
              onClick={(e) => handleAction('delete', e)}
              sx={{ color: 'error.main' }}
            >
              <ListItemIcon>
                <DeleteIcon fontSize="small" color="error" />
              </ListItemIcon>
              <ListItemText>Delete</ListItemText>
            </MenuItem>
          )}
        </Menu>
      </EnhancedCard>
    </ErrorBoundary>
  );
}));

// ===========================
// PROPTYPES & DISPLAY NAME
// ===========================

ContentCard.propTypes = {
  // Content props
  content: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    title: PropTypes.string,
    description: PropTypes.string,
    image_url: PropTypes.string,
    platforms: PropTypes.arrayOf(PropTypes.string),
    tags: PropTypes.arrayOf(PropTypes.string),
    status: PropTypes.oneOf(['draft', 'published', 'scheduled', 'archived', 'failed', 'processing', 'pending']),
    created_at: PropTypes.string,
    updated_at: PropTypes.string,
    content_type: PropTypes.string,
    type: PropTypes.oneOf(['post', 'article', 'video', 'image', 'carousel', 'story', 'reel', 'thread', 'poll', 'event']),
    metrics: PropTypes.shape({
      views: PropTypes.number,
      likes: PropTypes.number,
      comments: PropTypes.number,
      shares: PropTypes.number,
      engagement: PropTypes.number,
      reach: PropTypes.number,
      impressions: PropTypes.number
    })
  }),

  // Selection props
  selected: PropTypes.bool,

  // View props
  viewMode: PropTypes.oneOf(['compact', 'detailed', 'analytics', 'grid', 'list', 'preview']),

  // Callback props
  onSelect: PropTypes.func,
  onView: PropTypes.func,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onDuplicate: PropTypes.func,
  onSchedule: PropTypes.func,
  onShare: PropTypes.func,
  onArchive: PropTypes.func,
  onAnalytics: PropTypes.func,
  onFavorite: PropTypes.func,
  onBookmark: PropTypes.func,
  onMenuOpen: PropTypes.func,
  onInteraction: PropTypes.func,

  // Enhanced props
  userPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  showMetrics: PropTypes.bool,
  showActions: PropTypes.bool,
  isLoading: PropTypes.bool,

  // Testing props
  testId: PropTypes.string,

  // Accessibility props
  ariaLabel: PropTypes.string,
  announceChanges: PropTypes.bool
};

ContentCard.defaultProps = {
  selected: false,
  viewMode: 'detailed',
  userPlan: 'creator',
  enableAnalytics: true,
  enableAccessibility: true,
  showMetrics: true,
  showActions: true,
  isLoading: false,
  testId: 'content-card',
  announceChanges: true
};

ContentCard.displayName = 'ContentCard';

export default ContentCard;

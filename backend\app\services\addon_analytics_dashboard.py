"""
Comprehensive analytics dashboard for ACEO add-on system.
Provides real-time metrics, conversion tracking, and business intelligence.
"""
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

from app.db.mongodb import get_database
# Models are now handled as dictionaries for MongoDB
from app.core.redis import redis_manager, redis_get, redis_set, redis_delete, redis_setex, redis_incr, redis_expire
from app.core.monitoring import monitoring
import json
from bson import ObjectId

logger = logging.getLogger(__name__)


class MetricPeriod(str, Enum):
    """Time periods for metrics aggregation."""
    HOUR = "hour"
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"
@dataclass
class AddonMetrics:
    """Add-on performance metrics."""
    total_revenue: float
    total_purchases: int
    active_users: int
    conversion_rate: float
    average_order_value: float
    churn_rate: float
    usage_rate: float
    top_addons: List[Dict[str, Any]]
    revenue_by_plan: Dict[str, float]
    growth_rate: float


class AddonAnalyticsDashboard:
    """Analytics dashboard for add-on system monitoring."""
    
    def __init__(self):
        self.cache_ttl = 300  # 5 minutes cache
        self.metrics_cache_prefix = "addon_metrics:"
        
    async def get_dashboard_overview(self, period: MetricPeriod = MetricPeriod.DAY) -> Dict[str, Any]:
        """Get comprehensive dashboard overview."""
        try:
            # Check cache first
            cache_key = f"{self.metrics_cache_prefix}overview:{period.value}"
            cached_data = await redis_get(cache_key)
            
            if cached_data:
                return json.loads(cached_data)
            
            db = await get_database()

            # Calculate time range
            end_date = datetime.now(timezone.utc)
            start_date = self._get_period_start_date(end_date, period)

            # Get core metrics
            revenue_metrics = await self._get_revenue_metrics(db, start_date, end_date)
            conversion_metrics = await self._get_conversion_metrics(db, start_date, end_date)
            usage_metrics = await self._get_usage_metrics(db, start_date, end_date)
            user_metrics = await self._get_user_metrics(db, start_date, end_date)

            # Get top performing add-ons
            top_addons = await self._get_top_addons(db, start_date, end_date)

            # Get growth trends
            growth_data = await self._get_growth_trends(db, period)
            
            # Compile dashboard data
            dashboard = {
                "period": period.value,
                "date_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "revenue": revenue_metrics,
                "conversions": conversion_metrics,
                "usage": usage_metrics,
                "users": user_metrics,
                "top_addons": top_addons,
                "growth": growth_data,
                "alerts": await self._get_performance_alerts(),
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            # Cache the results
            await redis_setex(cache_key, self.cache_ttl, json.dumps(dashboard, default=str))
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Error generating dashboard overview: {str(e)}")
            return {"error": str(e)}
    
    async def _get_revenue_metrics(self, db, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate revenue metrics using MongoDB aggregation."""
        try:
            purchases_collection = db["addon_purchases"]
            users_collection = db["users"]

            # Total revenue aggregation
            total_revenue_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date},
                        "status": "completed"
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total_revenue": {"$sum": "$amount"},
                        "total_orders": {"$sum": 1}
                    }
                }
            ]

            total_revenue_result = await purchases_collection.aggregate(total_revenue_pipeline).to_list(length=1)
            total_revenue = total_revenue_result[0]["total_revenue"] if total_revenue_result else 0
            total_orders = total_revenue_result[0]["total_orders"] if total_revenue_result else 0

            # Revenue by add-on
            revenue_by_addon_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date},
                        "status": "completed"
                    }
                },
                {
                    "$group": {
                        "_id": "$addon_id",
                        "revenue": {"$sum": "$amount"},
                        "purchases": {"$sum": 1}
                    }
                },
                {
                    "$project": {
                        "addon_id": "$_id",
                        "revenue": 1,
                        "purchases": 1,
                        "_id": 0
                    }
                }
            ]

            revenue_by_addon_result = await purchases_collection.aggregate(revenue_by_addon_pipeline).to_list(length=None)

            # Revenue by subscription plan (requires lookup with users)
            revenue_by_plan_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date},
                        "status": "completed"
                    }
                },
                {
                    "$lookup": {
                        "from": "users",
                        "localField": "user_id",
                        "foreignField": "_id",
                        "as": "user"
                    }
                },
                {
                    "$unwind": "$user"
                },
                {
                    "$group": {
                        "_id": "$user.subscription.plan_id",
                        "revenue": {"$sum": "$amount"}
                    }
                },
                {
                    "$project": {
                        "plan_id": "$_id",
                        "revenue": 1,
                        "_id": 0
                    }
                }
            ]

            revenue_by_plan_result = await purchases_collection.aggregate(revenue_by_plan_pipeline).to_list(length=None)

            # Calculate average order value
            avg_order_value = total_revenue / total_orders if total_orders > 0 else 0

            return {
                "total_revenue": float(total_revenue),
                "total_orders": total_orders,
                "average_order_value": float(avg_order_value),
                "revenue_by_addon": [
                    {
                        "addon_id": row["addon_id"],
                        "revenue": float(row["revenue"]),
                        "purchases": row["purchases"]
                    }
                    for row in revenue_by_addon_result
                ],
                "revenue_by_plan": {
                    row["plan_id"]: float(row["revenue"])
                    for row in revenue_by_plan_result
                    if row["plan_id"]  # Filter out None plan_ids
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating revenue metrics: {str(e)}")
            return {"error": str(e)}
    
    async def _get_conversion_metrics(self, db, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate conversion metrics using MongoDB aggregation."""
        try:
            users_collection = db["users"]
            purchases_collection = db["addon_purchases"]

            # Get total unique users who were active during the period
            total_active_users_pipeline = [
                {
                    "$match": {
                        "last_login": {"$gte": start_date}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "count": {"$sum": 1}
                    }
                }
            ]

            active_users_result = await users_collection.aggregate(total_active_users_pipeline).to_list(length=1)
            total_active_users = active_users_result[0]["count"] if active_users_result else 0

            # Users who purchased add-ons
            purchasing_users_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date},
                        "status": "completed"
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id"
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "count": {"$sum": 1}
                    }
                }
            ]

            purchasing_users_result = await purchases_collection.aggregate(purchasing_users_pipeline).to_list(length=1)
            purchasing_users = purchasing_users_result[0]["count"] if purchasing_users_result else 0

            # Conversion rate
            conversion_rate = (purchasing_users / total_active_users * 100) if total_active_users > 0 else 0

            # Conversion by add-on
            conversion_by_addon_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date},
                        "status": "completed"
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "addon_id": "$addon_id",
                            "user_id": "$user_id"
                        }
                    }
                },
                {
                    "$group": {
                        "_id": "$_id.addon_id",
                        "unique_buyers": {"$sum": 1}
                    }
                },
                {
                    "$project": {
                        "addon_id": "$_id",
                        "unique_buyers": 1,
                        "_id": 0
                    }
                }
            ]

            conversion_by_addon_result = await purchases_collection.aggregate(conversion_by_addon_pipeline).to_list(length=None)

            # Conversion funnel (estimated data - would be enhanced with actual tracking)
            funnel_data = {
                "marketplace_views": total_active_users,  # Estimated
                "addon_detail_views": int(total_active_users * 0.3),  # Estimated 30%
                "purchase_attempts": int(total_active_users * 0.2),  # Estimated 20%
                "successful_purchases": purchasing_users
            }

            return {
                "overall_conversion_rate": float(conversion_rate),
                "total_active_users": total_active_users,
                "purchasing_users": purchasing_users,
                "conversion_by_addon": [
                    {
                        "addon_id": row["addon_id"],
                        "unique_buyers": row["unique_buyers"],
                        "conversion_rate": (row["unique_buyers"] / total_active_users * 100) if total_active_users > 0 else 0
                    }
                    for row in conversion_by_addon_result
                ],
                "funnel": funnel_data
            }
            
        except Exception as e:
            logger.error(f"Error calculating conversion metrics: {str(e)}")
            return {"error": str(e)}
    
    async def _get_usage_metrics(self, db, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate usage metrics using MongoDB aggregation."""
        try:
            usage_collection = db["addon_usage"]
            user_addons_collection = db["user_addons"]

            # Total usage events
            total_usage_pipeline = [
                {
                    "$match": {
                        "timestamp": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "count": {"$sum": 1}
                    }
                }
            ]

            total_usage_result = await usage_collection.aggregate(total_usage_pipeline).to_list(length=1)
            total_usage = total_usage_result[0]["count"] if total_usage_result else 0

            # Usage by add-on type
            usage_by_type_pipeline = [
                {
                    "$match": {
                        "timestamp": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$usage_type",
                        "usage_count": {"$sum": 1},
                        "total_amount": {"$sum": "$amount"},
                        "unique_users": {"$addToSet": "$user_id"}
                    }
                },
                {
                    "$project": {
                        "usage_type": "$_id",
                        "usage_count": 1,
                        "total_amount": 1,
                        "unique_users": {"$size": "$unique_users"},
                        "_id": 0
                    }
                }
            ]

            usage_by_type_result = await usage_collection.aggregate(usage_by_type_pipeline).to_list(length=None)

            # Active add-on users
            active_users_pipeline = [
                {
                    "$match": {
                        "is_active": True,
                        "purchased_at": {"$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id"
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "count": {"$sum": 1}
                    }
                }
            ]

            active_users_result = await user_addons_collection.aggregate(active_users_pipeline).to_list(length=1)
            active_addon_users = active_users_result[0]["count"] if active_users_result else 0

            # Usage efficiency (credits used vs purchased)
            credits_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total_credits_purchased": {"$sum": "$total_credits"},
                        "total_credits_used": {"$sum": "$credits_used"}
                    }
                }
            ]

            credits_result = await user_addons_collection.aggregate(credits_pipeline).to_list(length=1)
            total_credits_purchased = credits_result[0]["total_credits_purchased"] if credits_result else 0
            total_credits_used = credits_result[0]["total_credits_used"] if credits_result else 0

            usage_efficiency = (total_credits_used / total_credits_purchased * 100) if total_credits_purchased > 0 else 0

            return {
                "total_usage_events": total_usage,
                "active_addon_users": active_addon_users,
                "usage_efficiency": float(usage_efficiency),
                "total_credits_purchased": total_credits_purchased,
                "total_credits_used": total_credits_used,
                "usage_by_type": usage_by_type_result
            }
            
        except Exception as e:
            logger.error(f"Error calculating usage metrics: {str(e)}")
            return {"error": str(e)}
    
    async def _get_user_metrics(self, db, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Calculate user-related metrics using MongoDB aggregation."""
        try:
            user_addons_collection = db["user_addons"]
            purchases_collection = db["addon_purchases"]

            # New add-on users
            new_users_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id"
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "count": {"$sum": 1}
                    }
                }
            ]

            new_users_result = await user_addons_collection.aggregate(new_users_pipeline).to_list(length=1)
            new_addon_users = new_users_result[0]["count"] if new_users_result else 0

            # Repeat purchasers
            repeat_purchasers_pipeline = [
                {
                    "$group": {
                        "_id": "$user_id",
                        "purchase_count": {"$sum": 1}
                    }
                },
                {
                    "$match": {
                        "purchase_count": {"$gt": 1}
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "count": {"$sum": 1}
                    }
                }
            ]

            repeat_result = await purchases_collection.aggregate(repeat_purchasers_pipeline).to_list(length=1)
            repeat_purchasers = repeat_result[0]["count"] if repeat_result else 0

            # User retention (users who purchased in previous period and current period)
            previous_start = start_date - (end_date - start_date)

            # Get previous period users
            previous_users_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": previous_start, "$lt": start_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id"
                    }
                }
            ]

            previous_users_result = await purchases_collection.aggregate(previous_users_pipeline).to_list(length=None)
            previous_users = {user["_id"] for user in previous_users_result}

            # Get current period users
            current_users_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id"
                    }
                }
            ]

            current_users_result = await purchases_collection.aggregate(current_users_pipeline).to_list(length=None)
            current_users = {user["_id"] for user in current_users_result}

            # Calculate retention
            retained_users = len(previous_users.intersection(current_users))
            retention_rate = (retained_users / len(previous_users) * 100) if previous_users else 0

            return {
                "new_addon_users": new_addon_users,
                "repeat_purchasers": repeat_purchasers,
                "retained_users": retained_users,
                "retention_rate": float(retention_rate),
                "total_addon_users": len(current_users)
            }
            
        except Exception as e:
            logger.error(f"Error calculating user metrics: {str(e)}")
            return {"error": str(e)}
    
    async def _get_top_addons(self, db, start_date: datetime, end_date: datetime, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top performing add-ons."""
        try:
            purchases_collection = db["addon_purchases"]

            top_addons_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": start_date, "$lte": end_date},
                        "status": "completed"
                    }
                },
                {
                    "$group": {
                        "_id": "$addon_id",
                        "revenue": {"$sum": "$amount"},
                        "purchases": {"$sum": 1},
                        "unique_buyers": {"$addToSet": "$user_id"}
                    }
                },
                {
                    "$project": {
                        "addon_id": "$_id",
                        "revenue": 1,
                        "purchases": 1,
                        "unique_buyers": {"$size": "$unique_buyers"},
                        "avg_revenue_per_user": {
                            "$cond": {
                                "if": {"$gt": ["$unique_buyers", 0]},
                                "then": {"$divide": ["$revenue", {"$size": "$unique_buyers"}]},
                                "else": 0
                            }
                        },
                        "_id": 0
                    }
                },
                {
                    "$sort": {"revenue": -1}
                },
                {
                    "$limit": limit
                }
            ]

            top_addons_result = await purchases_collection.aggregate(top_addons_pipeline).to_list(length=limit)

            return [
                {
                    "addon_id": row["addon_id"],
                    "revenue": float(row["revenue"]),
                    "purchases": row["purchases"],
                    "unique_buyers": row["unique_buyers"],
                    "avg_revenue_per_user": float(row["avg_revenue_per_user"])
                }
                for row in top_addons_result
            ]
            
        except Exception as e:
            logger.error(f"Error getting top add-ons: {str(e)}")
            return []
    
    async def _get_growth_trends(self, db, period: MetricPeriod) -> Dict[str, Any]:
        """Calculate growth trends using MongoDB aggregation."""
        try:
            purchases_collection = db["addon_purchases"]

            # Get data for current and previous periods
            end_date = datetime.now(timezone.utc)
            current_start = self._get_period_start_date(end_date, period)
            previous_start = self._get_period_start_date(current_start, period)

            # Current period revenue
            current_revenue_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": current_start, "$lte": end_date},
                        "status": "completed"
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "revenue": {"$sum": "$amount"}
                    }
                }
            ]

            current_revenue_result = await purchases_collection.aggregate(current_revenue_pipeline).to_list(length=1)
            current_revenue = current_revenue_result[0]["revenue"] if current_revenue_result else 0

            # Previous period revenue
            previous_revenue_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": previous_start, "$lt": current_start},
                        "status": "completed"
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "revenue": {"$sum": "$amount"}
                    }
                }
            ]

            previous_revenue_result = await purchases_collection.aggregate(previous_revenue_pipeline).to_list(length=1)
            previous_revenue = previous_revenue_result[0]["revenue"] if previous_revenue_result else 0

            # Calculate growth rate
            revenue_growth = ((current_revenue - previous_revenue) / previous_revenue * 100) if previous_revenue > 0 else 0

            # Current period users
            current_users_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": current_start, "$lte": end_date}
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id"
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "count": {"$sum": 1}
                    }
                }
            ]

            current_users_result = await purchases_collection.aggregate(current_users_pipeline).to_list(length=1)
            current_users = current_users_result[0]["count"] if current_users_result else 0

            # Previous period users
            previous_users_pipeline = [
                {
                    "$match": {
                        "purchased_at": {"$gte": previous_start, "$lt": current_start}
                    }
                },
                {
                    "$group": {
                        "_id": "$user_id"
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "count": {"$sum": 1}
                    }
                }
            ]

            previous_users_result = await purchases_collection.aggregate(previous_users_pipeline).to_list(length=1)
            previous_users = previous_users_result[0]["count"] if previous_users_result else 0

            user_growth = ((current_users - previous_users) / previous_users * 100) if previous_users > 0 else 0

            return {
                "revenue_growth": float(revenue_growth),
                "user_growth": float(user_growth),
                "current_period": {
                    "revenue": float(current_revenue),
                    "users": current_users
                },
                "previous_period": {
                    "revenue": float(previous_revenue),
                    "users": previous_users
                }
            }
            
        except Exception as e:
            logger.error(f"Error calculating growth trends: {str(e)}")
            return {"error": str(e)}
    
    async def _get_performance_alerts(self) -> List[Dict[str, Any]]:
        """Get performance alerts and warnings."""
        alerts = []
        
        try:
            # Check error rates
            error_rate = await self._get_current_error_rate()
            if error_rate > 0.05:  # 5% threshold
                alerts.append({
                    "type": "error_rate",
                    "severity": "critical" if error_rate > 0.1 else "warning",
                    "message": f"High error rate detected: {error_rate:.2%}",
                    "value": error_rate
                })
            
            # Check response times
            avg_response_time = await self._get_avg_response_time()
            if avg_response_time > 2000:  # 2 seconds
                alerts.append({
                    "type": "response_time",
                    "severity": "critical" if avg_response_time > 5000 else "warning",
                    "message": f"High response time: {avg_response_time:.0f}ms",
                    "value": avg_response_time
                })
            
            # Check conversion rate drops
            current_conversion = await self._get_current_conversion_rate()
            if current_conversion < 0.1:  # 10% threshold
                alerts.append({
                    "type": "conversion_rate",
                    "severity": "warning",
                    "message": f"Low conversion rate: {current_conversion:.2%}",
                    "value": current_conversion
                })
            
        except Exception as e:
            logger.error(f"Error getting performance alerts: {str(e)}")
        
        return alerts
    
    def _get_period_start_date(self, end_date: datetime, period: MetricPeriod) -> datetime:
        """Calculate start date for a given period."""
        if period == MetricPeriod.HOUR:
            return end_date - timedelta(hours=1)
        elif period == MetricPeriod.DAY:
            return end_date - timedelta(days=1)
        elif period == MetricPeriod.WEEK:
            return end_date - timedelta(weeks=1)
        elif period == MetricPeriod.MONTH:
            return end_date - timedelta(days=30)
        elif period == MetricPeriod.QUARTER:
            return end_date - timedelta(days=90)
        elif period == MetricPeriod.YEAR:
            return end_date - timedelta(days=365)
        else:
            return end_date - timedelta(days=1)
    
    async def _get_current_error_rate(self) -> float:
        """Get current error rate from monitoring."""
        try:
            total_requests = await redis_get("addon_total_requests") or 0
            total_errors = await redis_get("addon_total_errors") or 0
            
            if int(total_requests) == 0:
                return 0.0
            
            return int(total_errors) / int(total_requests)
        except:
            return 0.0
    
    async def _get_avg_response_time(self) -> float:
        """Get average response time from monitoring."""
        try:
            total_time = await redis_get("addon_total_response_time") or 0
            total_requests = await redis_get("addon_total_requests") or 0
            
            if int(total_requests) == 0:
                return 0.0
            
            return float(total_time) / int(total_requests)
        except:
            return 0.0
    
    async def _get_current_conversion_rate(self) -> float:
        """Get current conversion rate."""
        try:
            # This would be calculated from recent data
            # For now, return a placeholder
            return 0.15  # 15% conversion rate
        except:
            return 0.0


# Global analytics dashboard instance
analytics_dashboard = AddonAnalyticsDashboard()


async def get_addon_dashboard(period: MetricPeriod = MetricPeriod.DAY) -> Dict[str, Any]:
    """Get add-on analytics dashboard."""
    return await analytics_dashboard.get_dashboard_overview(period)


async def get_addon_metrics() -> AddonMetrics:
    """Get comprehensive add-on metrics."""
    dashboard = await analytics_dashboard.get_dashboard_overview()

    return AddonMetrics(
        total_revenue=dashboard.get("revenue", {}).get("total_revenue", 0),
        total_purchases=dashboard.get("revenue", {}).get("total_orders", 0),
        active_users=dashboard.get("users", {}).get("total_addon_users", 0),
        conversion_rate=dashboard.get("conversions", {}).get("overall_conversion_rate", 0),
        average_order_value=dashboard.get("revenue", {}).get("average_order_value", 0),
        churn_rate=100 - dashboard.get("users", {}).get("retention_rate", 0),
        usage_rate=dashboard.get("usage", {}).get("usage_efficiency", 0),
        top_addons=dashboard.get("top_addons", []),
        revenue_by_plan=dashboard.get("revenue", {}).get("revenue_by_plan", {}),
        growth_rate=dashboard.get("growth", {}).get("revenue_growth", 0)
    )


# Alert system for monitoring
class AddonAlertSystem:
    """Alert system for add-on monitoring."""

    def __init__(self):
        self.alert_thresholds = {
            "error_rate": 0.05,  # 5%
            "response_time": 2000,  # 2 seconds
            "conversion_rate_drop": 0.2,  # 20% drop
            "revenue_drop": 0.15,  # 15% drop
            "payment_failure_rate": 0.1  # 10%
        }

    async def check_all_alerts(self) -> List[Dict[str, Any]]:
        """Check all alert conditions."""
        alerts = []

        try:
            # Check system health alerts
            system_alerts = await self._check_system_alerts()
            alerts.extend(system_alerts)

            # Check business metric alerts
            business_alerts = await self._check_business_alerts()
            alerts.extend(business_alerts)

            # Check performance alerts
            performance_alerts = await self._check_performance_alerts()
            alerts.extend(performance_alerts)

        except Exception as e:
            logger.error(f"Error checking alerts: {str(e)}")
            alerts.append({
                "type": "system_error",
                "severity": "critical",
                "message": f"Alert system error: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

        return alerts

    async def _check_system_alerts(self) -> List[Dict[str, Any]]:
        """Check system health alerts."""
        alerts = []

        # Error rate check
        error_rate = await analytics_dashboard._get_current_error_rate()
        if error_rate > self.alert_thresholds["error_rate"]:
            alerts.append({
                "type": "high_error_rate",
                "severity": "critical" if error_rate > 0.1 else "warning",
                "message": f"Add-on error rate is {error_rate:.2%} (threshold: {self.alert_thresholds['error_rate']:.2%})",
                "value": error_rate,
                "threshold": self.alert_thresholds["error_rate"],
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

        # Response time check
        response_time = await analytics_dashboard._get_avg_response_time()
        if response_time > self.alert_thresholds["response_time"]:
            alerts.append({
                "type": "slow_response_time",
                "severity": "critical" if response_time > 5000 else "warning",
                "message": f"Add-on response time is {response_time:.0f}ms (threshold: {self.alert_thresholds['response_time']}ms)",
                "value": response_time,
                "threshold": self.alert_thresholds["response_time"],
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

        return alerts

    async def _check_business_alerts(self) -> List[Dict[str, Any]]:
        """Check business metric alerts."""
        alerts = []

        try:
            # Get current metrics
            dashboard = await analytics_dashboard.get_dashboard_overview(MetricPeriod.DAY)

            # Check conversion rate
            conversion_rate = dashboard.get("conversions", {}).get("overall_conversion_rate", 0)
            if conversion_rate < 10:  # Less than 10%
                alerts.append({
                    "type": "low_conversion_rate",
                    "severity": "warning",
                    "message": f"Add-on conversion rate is {conversion_rate:.1f}% (expected: >10%)",
                    "value": conversion_rate,
                    "threshold": 10,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })

            # Check revenue growth
            revenue_growth = dashboard.get("growth", {}).get("revenue_growth", 0)
            if revenue_growth < -15:  # 15% drop
                alerts.append({
                    "type": "revenue_decline",
                    "severity": "critical",
                    "message": f"Add-on revenue declined by {abs(revenue_growth):.1f}%",
                    "value": revenue_growth,
                    "threshold": -15,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })

        except Exception as e:
            logger.error(f"Error checking business alerts: {str(e)}")

        return alerts

    async def _check_performance_alerts(self) -> List[Dict[str, Any]]:
        """Check performance-related alerts."""
        alerts = []

        try:
            # Check Redis connectivity
            try:
                client = await redis_manager.get_client()
                if client:
                    await client.ping()
            except Exception:
                alerts.append({
                    "type": "redis_connectivity",
                    "severity": "critical",
                    "message": "Redis connectivity issues detected",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })

            # Check MongoDB performance
            try:
                db = await get_database()
                start_time = datetime.now()
                await db.command("ping")
                db_response_time = (datetime.now() - start_time).total_seconds() * 1000

                if db_response_time > 1000:  # 1 second
                    alerts.append({
                        "type": "database_performance",
                        "severity": "warning",
                        "message": f"MongoDB response time is {db_response_time:.0f}ms",
                        "value": db_response_time,
                        "threshold": 1000,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    })
            except Exception:
                alerts.append({
                    "type": "database_connectivity",
                    "severity": "critical",
                    "message": "MongoDB connectivity issues detected",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })

        except Exception as e:
            logger.error(f"Error checking performance alerts: {str(e)}")

        return alerts


# Global alert system
alert_system = AddonAlertSystem()


async def check_addon_alerts() -> List[Dict[str, Any]]:
    """Check all add-on system alerts."""
    return await alert_system.check_all_alerts()

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import EnhancedBrandingSettings from '../EnhancedBrandingSettings';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the auth hook
const mockUpdateProfile = vi.fn();
const mockUser = {
  id: 'test-user',
  branding_preferences: {
    color_palette: {
      primary: '#4E40C5',
      secondary: '#00E4BC',
      background: '#FFFFFF',
      text: '#333333'
    },
    typography: {
      primary_font: 'Inter',
      secondary_font: 'Roboto'
    },
    brand_voice: {
      tone: 'professional',
      writing_style: 'clear'
    }
  }
};

vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: mockUser,
    updateProfile: mockUpdateProfile,
  }),
}));

// Mock the branding components
vi.mock('../ColorPaletteEditor', () => ({
  default: ({ colorPalette, onChange }) => (
    <div data-testid="color-palette-editor">
      <button onClick={() => onChange({ ...colorPalette, primary: '#FF0000' })}>
        Change Primary Color
      </button>
    </div>
  )
}));

vi.mock('../BrandVoiceEditor', () => ({
  default: ({ brandVoice, onChange }) => (
    <div data-testid="brand-voice-editor">
      <button onClick={() => onChange({ ...brandVoice, tone: 'friendly' })}>
        Change Tone
      </button>
    </div>
  )
}));

vi.mock('../TypographyEditor', () => ({
  default: ({ typography, onChange }) => (
    <div data-testid="typography-editor">
      <button onClick={() => onChange({ ...typography, primary_font: 'Arial' })}>
        Change Font
      </button>
    </div>
  )
}));

vi.mock('../CompositionEditor', () => ({
  default: ({ composition, onChange }) => (
    <div data-testid="composition-editor">
      <button onClick={() => onChange({ ...composition, layout: 'golden-ratio' })}>
        Change Layout
      </button>
    </div>
  )
}));

vi.mock('../VisualStyleEditor', () => ({
  default: ({ visualStyle, onChange }) => (
    <div data-testid="visual-style-editor">
      <button onClick={() => onChange({ ...visualStyle, photography_style: 'modern' })}>
        Change Style
      </button>
    </div>
  )
}));

vi.mock('../EnhancedLogoUpload', () => ({
  default: ({ logoData, onChange }) => (
    <div data-testid="logo-upload">
      <button onClick={() => onChange({ ...logoData, logo_url: 'new-logo.png' })}>
        Upload Logo
      </button>
    </div>
  )
}));

vi.mock('../PatternLibrary', () => ({
  default: ({ visualElements, onChange }) => (
    <div data-testid="pattern-library">
      <button onClick={() => onChange({ ...visualElements, patterns: ['new-pattern'] })}>
        Add Pattern
      </button>
    </div>
  )
}));

vi.mock('../AISuggestions', () => ({
  default: ({ onApplySuggestion }) => (
    <div data-testid="ai-suggestions">
      <button onClick={() => onApplySuggestion({ suggested_color: '#123456' })}>
        Apply AI Suggestion
      </button>
    </div>
  )
}));

vi.mock('../BrandingAnalytics', () => ({
  default: ({ brandingData, consistencyScore }) => (
    <div data-testid="branding-analytics">
      Analytics - Score: {consistencyScore}
    </div>
  )
}));

vi.mock('../StyleGuideExport', () => ({
  default: ({ brandingData, onExport }) => (
    <div data-testid="style-guide-export">
      <button onClick={() => onExport && onExport(brandingData, 'pdf')}>
        Export Style Guide
      </button>
    </div>
  )
}));

// Mock the default branding data
vi.mock('../../../config/defaultBrandingData', () => ({
  default: {
    color_palette: {
      primary: '#000000',
      secondary: '#FFFFFF',
      background: '#F5F5F5',
      text: '#333333'
    },
    typography: {
      primary_font: 'Arial',
      secondary_font: 'Helvetica'
    },
    brand_voice: {
      tone: 'neutral',
      writing_style: 'standard'
    },
    visualStyle: {},
    composition: {},
    assets: {},
    visualElements: {}
  },
  mergeBrandingData: (data) => ({
    ...data,
    merged: true
  })
}));

// Mock accessibility validation
vi.mock('../../../utils/accessibilityValidation', () => ({
  validateColorContrast: vi.fn(() => ({
    ratio: 4.5,
    passesAA: true,
    passesAAA: false
  }))
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('EnhancedBrandingSettings', () => {
  const mockProps = {
    brandProfile: mockUser.branding_preferences,
    onChange: vi.fn(),
    onError: vi.fn(),
    onExport: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders branding settings correctly', () => {
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Enhanced Branding Settings')).toBeInTheDocument();
    expect(screen.getByText(/Define your brand's visual identity/)).toBeInTheDocument();
  });

  test('shows all branding tabs', () => {
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Colors')).toBeInTheDocument();
    expect(screen.getByText('Brand Voice')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Composition')).toBeInTheDocument();
    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText('Logo & Assets')).toBeInTheDocument();
    expect(screen.getByText('Pattern Library')).toBeInTheDocument();
    expect(screen.getByText('AI Suggestions')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Style Guide')).toBeInTheDocument();
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Should start with Colors tab (ColorPaletteEditor)
    expect(screen.getByTestId('color-palette-editor')).toBeInTheDocument();

    // Click on Brand Voice tab
    await user.click(screen.getByText('Brand Voice'));
    expect(screen.getByTestId('brand-voice-editor')).toBeInTheDocument();

    // Click on Typography tab
    await user.click(screen.getByText('Typography'));
    expect(screen.getByTestId('typography-editor')).toBeInTheDocument();
  });

  test('handles color palette changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Click the change primary color button
    await user.click(screen.getByText('Change Primary Color'));

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          color_palette: expect.objectContaining({
            primary: '#FF0000'
          })
        })
      );
    });
  });

  test('handles brand voice changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Brand Voice tab
    await user.click(screen.getByText('Brand Voice'));
    
    // Click the change tone button
    await user.click(screen.getByText('Change Tone'));

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          brand_voice: expect.objectContaining({
            tone: 'friendly'
          })
        })
      );
    });
  });

  test('handles save functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Make a change to trigger hasChanges
    await user.click(screen.getByText('Change Primary Color'));

    // Click save button
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockUpdateProfile).toHaveBeenCalled();
      expect(mockShowSuccessNotification).toHaveBeenCalledWith(
        'Branding settings saved successfully!'
      );
    });
  });

  test('shows unsaved changes alert', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Make a change
    await user.click(screen.getByText('Change Primary Color'));

    // Should show unsaved changes alert
    await waitFor(() => {
      expect(screen.getByText(/You have unsaved changes/)).toBeInTheDocument();
    });
  });

  test('handles AI suggestions when enabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} enableAI={true} />
      </TestWrapper>
    );

    // Navigate to AI Suggestions tab
    await user.click(screen.getByText('AI Suggestions'));
    
    // Apply AI suggestion
    await user.click(screen.getByText('Apply AI Suggestion'));

    await waitFor(() => {
      expect(mockShowSuccessNotification).toHaveBeenCalledWith(
        'AI suggestions applied successfully'
      );
    });
  });

  test('shows analytics when enabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} showAnalytics={true} />
      </TestWrapper>
    );

    // Navigate to Analytics tab
    await user.click(screen.getByText('Analytics'));
    
    expect(screen.getByTestId('branding-analytics')).toBeInTheDocument();
    expect(screen.getByText(/Analytics - Score:/)).toBeInTheDocument();
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Style Guide tab
    await user.click(screen.getByText('Style Guide'));
    
    // Click export button
    await user.click(screen.getByText('Export Style Guide'));

    await waitFor(() => {
      expect(mockProps.onExport).toHaveBeenCalledWith(
        expect.any(Object),
        'pdf'
      );
    });
  });

  test('handles disabled state correctly', () => {
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} disabled />
      </TestWrapper>
    );

    const saveButton = screen.getByRole('button', { name: /save/i });
    expect(saveButton).toBeDisabled();
  });

  test('handles read-only state correctly', () => {
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} readOnly />
      </TestWrapper>
    );

    const saveButton = screen.getByRole('button', { name: /save/i });
    expect(saveButton).toBeDisabled();
  });

  test('handles error scenarios gracefully', async () => {
    const user = userEvent.setup();
    mockUpdateProfile.mockRejectedValueOnce(new Error('Save failed'));
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Make a change and try to save
    await user.click(screen.getByText('Change Primary Color'));
    await user.click(screen.getByRole('button', { name: /save/i }));

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith(
        'Failed to save branding settings. Please try again.'
      );
    });
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels and roles
    const tabs = screen.getAllByRole('tab');
    expect(tabs.length).toBeGreaterThan(0);

    const saveButton = screen.getByRole('button', { name: /save/i });
    expect(saveButton).toBeInTheDocument();
  });

  test('initializes with user branding preferences', () => {
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} />
      </TestWrapper>
    );

    // Component should initialize with user's branding preferences
    expect(screen.getByTestId('color-palette-editor')).toBeInTheDocument();
  });

  test('calculates consistency score correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedBrandingSettings {...mockProps} showAnalytics={true} />
      </TestWrapper>
    );

    // Navigate to Analytics tab to see consistency score
    await user.click(screen.getByText('Analytics'));
    
    expect(screen.getByText(/Analytics - Score:/)).toBeInTheDocument();
  });
});

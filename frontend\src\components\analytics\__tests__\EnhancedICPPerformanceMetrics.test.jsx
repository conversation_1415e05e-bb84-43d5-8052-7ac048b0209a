/**
 * Tests for EnhancedICPPerformanceMetrics component
 */
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import EnhancedICPPerformanceMetrics from '../EnhancedICPPerformanceMetrics';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn(),
    showSuccessNotification: vi.fn()
  }))
}));

vi.mock('../../api/icp-performance', () => ({
  analyzeICPPerformance: vi.fn()
}));

// Mock D3
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      remove: vi.fn()
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({
        attr: vi.fn(() => ({
          append: vi.fn(() => ({
            attr: vi.fn(() => ({}))
          }))
        }))
      }))
    })),
    call: vi.fn(() => ({})),
    datum: vi.fn(() => ({
      attr: vi.fn(() => ({}))
    })),
    data: vi.fn(() => ({
      enter: vi.fn(() => ({
        append: vi.fn(() => ({
          attr: vi.fn(() => ({
            attr: vi.fn(() => ({
              attr: vi.fn(() => ({
                attr: vi.fn(() => ({
                  attr: vi.fn(() => ({}))
                }))
              }))
            }))
          }))
        }))
      }))
    }))
  })),
  scaleTime: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({}))
    }))
  })),
  scaleLinear: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({}))
    }))
  })),
  scaleBand: vi.fn(() => ({
    domain: vi.fn(() => ({
      range: vi.fn(() => ({
        padding: vi.fn(() => ({
          bandwidth: vi.fn(() => 50)
        }))
      }))
    }))
  })),
  extent: vi.fn(() => [new Date(), new Date()]),
  max: vi.fn(() => 100),
  axisBottom: vi.fn(() => ({})),
  axisLeft: vi.fn(() => ({})),
  line: vi.fn(() => ({
    x: vi.fn(() => ({
      y: vi.fn(() => ({}))
    }))
  }))
}));

describe('EnhancedICPPerformanceMetrics', () => {
  const { analyzeICPPerformance } = require('../../api/icp-performance');
  const mockNotification = {
    showErrorNotification: vi.fn(),
    showSuccessNotification: vi.fn()
  };

  const mockData = {
    icp_name: 'Tech Startups',
    avg_engagement_rate: 5.75,
    best_performing_platform: 'LinkedIn',
    best_performing_content_type: 'Article',
    best_performing_time: 'Morning',
    engagement_statistics: {
      min: 2.1,
      max: 12.8,
      median: 5.2,
      stdev: 2.3
    },
    platform_performance: {
      'LinkedIn': {
        engagement_rate: 7.2,
        stats: { is_significant: true, is_better: true }
      },
      'Twitter': {
        engagement_rate: 4.8,
        stats: { is_significant: false, is_better: false }
      },
      'Facebook': {
        engagement_rate: 3.9,
        stats: { is_significant: true, is_better: false }
      }
    },
    content_type_performance: {
      'article': {
        engagement_rate: 6.5,
        stats: { is_significant: true, is_better: true }
      },
      'video': {
        engagement_rate: 5.8,
        stats: { is_significant: false, is_better: false }
      },
      'image': {
        engagement_rate: 4.2,
        stats: { is_significant: true, is_better: false }
      }
    },
    time_of_day_performance: {
      'Morning': {
        optimal_hour: 9
      },
      'Afternoon': {
        optimal_hour: 14
      }
    },
    time_series_data: [
      {
        date: '2023-01-01',
        engagement_rate: 5.2,
        moving_avg_engagement: 5.0
      },
      {
        date: '2023-01-02',
        engagement_rate: 6.1,
        moving_avg_engagement: 5.3
      },
      {
        date: '2023-01-03',
        engagement_rate: 5.8,
        moving_avg_engagement: 5.5
      }
    ],
    recommendations: [
      'Focus more content on LinkedIn for better engagement',
      'Create more article-type content as it performs best',
      'Post content in the morning hours (around 9 AM) for optimal reach'
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    analyzeICPPerformance.mockResolvedValue(mockData);

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue(mockNotification);

    // Mock clientWidth for chart containers
    Object.defineProperty(HTMLElement.prototype, 'clientWidth', {
      configurable: true,
      value: 800
    });
  });

  test('renders ICP performance metrics dashboard', async () => {
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Metrics for Tech Startups')).toBeInTheDocument();
      expect(screen.getByText('5.75%')).toBeInTheDocument(); // Average engagement rate
      expect(screen.getByText('LinkedIn')).toBeInTheDocument(); // Best platform
      expect(screen.getByText('Article')).toBeInTheDocument(); // Best content type
      expect(screen.getByText('Morning')).toBeInTheDocument(); // Best posting time
    });
  });

  test('displays performance metrics correctly', async () => {
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('5.75%')).toBeInTheDocument(); // Average engagement rate
      expect(screen.getByText('Range: 2.10% - 12.80%')).toBeInTheDocument(); // Engagement statistics
      expect(screen.getByText('7.20%')).toBeInTheDocument(); // Best platform engagement
      expect(screen.getByText('Optimal hour: 9:00')).toBeInTheDocument(); // Optimal posting time
    });
  });

  test('shows loading state initially', () => {
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles API error gracefully', async () => {
    analyzeICPPerformance.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load performance data. Please try again.')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(analyzeICPPerformance).toHaveBeenCalledWith('test-icp-id');
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByText('Refresh Data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(analyzeICPPerformance).toHaveBeenCalledWith('test-icp-id', true);
      expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith('Performance data refreshed successfully');
    });
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    
    // Mock document.createElement and related methods
    const mockLink = {
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    };
    const mockCreateElement = vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    const mockAppendChild = vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    const mockRemoveChild = vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Metrics for Tech Startups')).toBeInTheDocument();
    });

    // Click export button
    const exportButton = screen.getByText('Export Data');
    await user.click(exportButton);

    expect(mockCreateElement).toHaveBeenCalledWith('a');
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockNotification.showSuccessNotification).toHaveBeenCalledWith(
      'Generating ICP performance report...'
    );

    // Cleanup mocks
    mockCreateElement.mockRestore();
    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Metrics for Tech Startups')).toBeInTheDocument();
    });

    // Click on Platform Performance tab
    await user.click(screen.getByText('Platform Performance'));

    expect(screen.getByText('Platform Performance')).toBeInTheDocument();

    // Click on Content Analysis tab
    await user.click(screen.getByText('Content Analysis'));

    expect(screen.getByText('Content Type Performance')).toBeInTheDocument();
  });

  test('displays significance indicators correctly', async () => {
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Metrics for Tech Startups')).toBeInTheDocument();
    });

    // Should show significance indicators for platforms and content types
    const significantChips = screen.getAllByText(/Significant/);
    expect(significantChips.length).toBeGreaterThan(0);
  });

  test('displays recommendations section', async () => {
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('AI-Powered Recommendations')).toBeInTheDocument();
      expect(screen.getByText('Focus more content on LinkedIn for better engagement')).toBeInTheDocument();
      expect(screen.getByText('Create more article-type content as it performs best')).toBeInTheDocument();
      expect(screen.getByText('Post content in the morning hours (around 9 AM) for optimal reach')).toBeInTheDocument();
    });
  });

  test('shows no recommendations message when none available', async () => {
    const dataWithoutRecommendations = {
      ...mockData,
      recommendations: []
    };
    analyzeICPPerformance.mockResolvedValue(dataWithoutRecommendations);

    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No recommendations available. Try generating more content for this ICP to get personalized recommendations.')).toBeInTheDocument();
    });
  });

  test('calls onDataLoaded prop when data is loaded', async () => {
    const mockOnDataLoaded = vi.fn();
    
    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" onDataLoaded={mockOnDataLoaded} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockOnDataLoaded).toHaveBeenCalledWith(mockData);
    });
  });

  test('shows no data message when performance data is null', async () => {
    analyzeICPPerformance.mockResolvedValue(null);

    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No performance data available for this ICP.')).toBeInTheDocument();
    });
  });

  test('handles retry functionality on error', async () => {
    const user = userEvent.setup();
    analyzeICPPerformance.mockRejectedValueOnce(new Error('API Error'));

    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });

    // Clear the mock and make it succeed on retry
    vi.clearAllMocks();
    analyzeICPPerformance.mockResolvedValue(mockData);

    // Click retry button
    const retryButton = screen.getByText('Retry');
    await user.click(retryButton);

    await waitFor(() => {
      expect(analyzeICPPerformance).toHaveBeenCalledWith('test-icp-id', true);
    });
  });

  test('disables buttons during loading', async () => {
    analyzeICPPerformance.mockImplementation(() => new Promise(() => {})); // Never resolves

    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      const refreshButton = screen.getByText('Refreshing...');
      expect(refreshButton).toBeDisabled();
    });
  });

  test('handles missing platform performance data gracefully', async () => {
    const incompleteData = {
      ...mockData,
      platform_performance: null
    };
    analyzeICPPerformance.mockResolvedValue(incompleteData);

    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Metrics for Tech Startups')).toBeInTheDocument();
      expect(screen.getByText('5.75%')).toBeInTheDocument();
    });
  });

  test('handles missing content type performance data gracefully', async () => {
    const incompleteData = {
      ...mockData,
      content_type_performance: null
    };
    analyzeICPPerformance.mockResolvedValue(incompleteData);

    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Metrics for Tech Startups')).toBeInTheDocument();
      expect(screen.getByText('5.75%')).toBeInTheDocument();
    });
  });

  test('handles missing time series data gracefully', async () => {
    const incompleteData = {
      ...mockData,
      time_series_data: null
    };
    analyzeICPPerformance.mockResolvedValue(incompleteData);

    render(
      <TestWrapper>
        <EnhancedICPPerformanceMetrics icpId="test-icp-id" />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Performance Metrics for Tech Startups')).toBeInTheDocument();
      expect(screen.getByText('5.75%')).toBeInTheDocument();
    });
  });
});

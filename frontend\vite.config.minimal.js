import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [
    react({
      jsxImportSource: '@emotion/react',
      babel: {
        plugins: ['@emotion/babel-plugin']
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(process.cwd(), './src'),
      'lodash': 'lodash-es',
      'lodash/get': 'lodash-es/get'
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
    dedupe: ['react', 'react-dom', 'uuid', '@mui/material', '@emotion/react']
  },

  define: {
    "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV || "production")
  },

  build: {
    outDir: 'dist',
    sourcemap: false,
    chunkSizeWarningLimit: 1000,
    minify: false, // Disable minification to reduce processing
    target: "es2020",
    cssCodeSplit: false, // Reduce file operations
    assetsInlineLimit: 8192,
    rollupOptions: {
      output: {
        manualChunks: undefined, // Disable manual chunking to reduce complexity
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      },
      external: [], // Don't externalize anything for now
      treeshake: false // Disable tree shaking to reduce processing
    }
  },

  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "uuid",
      "axios",
      "lodash-es",
      "@mui/material",
      "@mui/icons-material",
      "@emotion/react",
      "@emotion/styled",
      "recharts"
    ],
    exclude: [],
    force: true // Force re-optimization
  },

  // Reduce file watching and processing
  clearScreen: false,
  logLevel: "error"
});

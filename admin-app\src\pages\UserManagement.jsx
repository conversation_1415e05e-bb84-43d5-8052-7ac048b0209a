// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Avatar,
  InputAdornment,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tooltip,
  Badge,
  Menu,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TableSortLabel,
  Checkbox,
  FormGroup,
  Autocomplete,
  Snackbar,
  LinearProgress,
  Backdrop,
  Skeleton,
  Fab,
  Zoom,
  Fade,
} from '@mui/material';
import {
  People as UsersIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Block as BlockIcon,
  CheckCircle as ActivateIcon,
  MoreVert as MoreIcon,
  PersonAdd as AddUserIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as ActivityIcon,
  Security as SecurityIcon,
  Payment as PaymentIcon,
  Settings as SettingsIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as ViewIcon,
  History as HistoryIcon,
  NotificationsActive as NotifyIcon,
  AdminPanelSettings as AdminIcon,
  CreditCard as SubscriptionIcon,
  Analytics as AnalyticsIcon,
  FolderOpen as EmptyFolderIcon,
  Error as ErrorIcon,
  SearchOff as NoResultsIcon,
  PersonAdd as AddPersonIcon,
  CloudOff as OfflineIcon,
  Wifi as ConnectivityIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import api from '../api';
import UserAnalytics from '../components/UserAnalytics';
import UserPermissions from '../components/UserPermissions';
import UserActivityLog from '../components/UserActivityLog';
import StablePageWrapper from '../components/StablePageWrapper';
import UserManagementErrorBoundary from '../components/UserManagementErrorBoundary';

const UserManagement = () => {
  // Main state
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalUsers, setTotalUsers] = useState(0);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Production-ready state management
  const [retryCount, setRetryCount] = useState(0);
  const [operationInProgress, setOperationInProgress] = useState(false);
  const [correlationId, setCorrelationId] = useState('');
  const [performanceMetrics, setPerformanceMetrics] = useState({});
  const [validationErrors, setValidationErrors] = useState({});
  const [confirmDialog, setConfirmDialog] = useState({ open: false, action: null, data: null });

  // Empty state management
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [hasSearchFilters, setHasSearchFilters] = useState(false);
  const [lastError, setLastError] = useState(null);
  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('');

  // Filter and search state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [planFilter, setPlanFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState({ start: null, end: null });
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedUsers, setSelectedUsers] = useState([]);

  // Dialog state
  const [openDialog, setOpenDialog] = useState(false);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  const [openBulkDialog, setBulkDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [dialogTab, setDialogTab] = useState(0);
  const [userActivity, setUserActivity] = useState([]);
  const [userSubscriptions, setUserSubscriptions] = useState([]);

  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuUser, setMenuUser] = useState(null);

  // Form state
  const [userForm, setUserForm] = useState({
    email: '',
    full_name: '',
    password: '',
    subscription_plan: 'free',
    is_active: true,
    is_admin: false,
    email_verified: false,
    phone: '',
    company: '',
    location: '',
    notes: '',
  });

  // Bulk operations state
  const [bulkOperation, setBulkOperation] = useState('');
  const [bulkData, setBulkData] = useState({});

  useEffect(() => {
    fetchUsers();
  }, [page, rowsPerPage, searchTerm, statusFilter, planFilter, sortBy, sortOrder, dateFilter]);

  const fetchUsers = useCallback(async (retryAttempt = 0) => {
    const startTime = performance.now();
    setLoading(true);
    setError('');
    setLastError(null);

    // Check if we have active search/filters
    const hasFilters = searchTerm || statusFilter !== 'all' || planFilter !== 'all' ||
                      dateFilter.start || dateFilter.end;
    setHasSearchFilters(hasFilters);

    // Generate correlation ID for request tracking
    const currentCorrelationId = `user-fetch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    setCorrelationId(currentCorrelationId);

    try {
      const params = {
        skip: page * rowsPerPage,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        user_status: statusFilter !== 'all' ? statusFilter : undefined,
        plan: planFilter !== 'all' ? planFilter : undefined,
        sort_by: sortBy,
        sort_order: sortOrder,
        start_date: dateFilter.start?.toISOString(),
        end_date: dateFilter.end?.toISOString(),
      };

      const response = await api.get('/api/admin/users', {
        params,
        headers: {
          'X-Correlation-ID': currentCorrelationId
        },
        timeout: 10000 // 10 second timeout
      });

      // Comprehensive data validation
      const usersData = Array.isArray(response.data.users)
        ? response.data.users.filter(user => user !== null && user !== undefined)
        : [];

      setUsers(usersData);
      setTotalUsers(response.data.total || 0);
      setRetryCount(0); // Reset retry count on success
      setIsInitialLoad(false); // Mark that we've completed initial load

      // Screen reader announcement
      const userCount = usersData.length;
      const totalCount = response.data.total || 0;
      setScreenReaderAnnouncement(
        userCount === 0
          ? hasFilters ? 'No users match your search criteria' : 'No users found'
          : `Loaded ${userCount} of ${totalCount} users`
      );

      // Performance monitoring
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      setPerformanceMetrics(prev => ({
        ...prev,
        lastFetchTime: loadTime,
        averageLoadTime: prev.averageLoadTime
          ? (prev.averageLoadTime + loadTime) / 2
          : loadTime
      }));

      // Log slow performance
      if (loadTime > 2000) {
        console.warn(`Slow user fetch detected: ${loadTime.toFixed(2)}ms`);
      }

    } catch (error) {
      console.error('Error fetching users:', error);

      // Enhanced error handling with retry logic
      if (retryAttempt < 2 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
        console.log(`Retrying fetch users (attempt ${retryAttempt + 1})`);
        setRetryCount(retryAttempt + 1);
        setTimeout(() => fetchUsers(retryAttempt + 1), 1000 * Math.pow(2, retryAttempt)); // Exponential backoff
        return;
      }

      // Set appropriate error message
      const errorMessage = error.response?.data?.detail ||
                          error.message ||
                          'Failed to fetch users. Please try again.';
      setError(errorMessage);
      setLastError(error);
      setUsers([]);
      setTotalUsers(0);
      setIsInitialLoad(false);
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchTerm, statusFilter, planFilter, sortBy, sortOrder, dateFilter]);

  const fetchUserActivity = async (userId) => {
    try {
      const response = await api.get(`/api/admin/users/${userId}/activity`);
      setUserActivity(response.data);
    } catch (error) {
      console.error('Error fetching user activity:', error);
    }
  };

  const fetchUserSubscriptions = async (userId) => {
    try {
      const response = await api.get(`/api/admin/users/${userId}/subscriptions`);
      setUserSubscriptions(response.data);
    } catch (error) {
      console.error('Error fetching user subscriptions:', error);
    }
  };

  // User operations
  const handleCreateUser = async () => {
    try {
      await api.post('/api/admin/users', userForm);
      setSuccess('User created successfully');
      setOpenCreateDialog(false);
      resetUserForm();
      fetchUsers();
    } catch (error) {
      console.error('Error creating user:', error);
      setError(error.response?.data?.detail || 'Failed to create user');
    }
  };

  const handleUpdateUser = async () => {
    try {
      await api.put(`/api/admin/users/${selectedUser.id}`, userForm);
      setSuccess('User updated successfully');
      setOpenDialog(false);
      fetchUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      setError(error.response?.data?.detail || 'Failed to update user');
    }
  };

  const handleToggleUserStatus = async (user) => {
    try {
      await api.put(`/api/admin/users/${user.id}`, {
        is_active: !user.is_active
      });
      setSuccess(`User ${user.is_active ? 'deactivated' : 'activated'} successfully`);
      fetchUsers();
    } catch (error) {
      console.error('Error updating user status:', error);
      setError('Failed to update user status');
    }
  };

  const handleDeleteUser = async (user) => {
    if (!window.confirm(`Are you sure you want to delete user "${user.email}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await api.delete(`/api/admin/users/${user.id}`);
      setSuccess('User deleted successfully');
      fetchUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      setError('Failed to delete user');
    }
  };

  const handleBulkOperation = async () => {
    if (selectedUsers.length === 0) {
      setError('Please select users for bulk operation');
      return;
    }

    try {
      await api.post('/api/admin/users/bulk', {
        user_ids: selectedUsers,
        operation: bulkOperation,
        data: bulkData,
      });
      setSuccess(`Bulk operation completed for ${selectedUsers.length} users`);
      setBulkDialog(false);
      setSelectedUsers([]);
      fetchUsers();
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      setError('Failed to perform bulk operation');
    }
  };

  const handleSendNotification = async (userId, message) => {
    try {
      await api.post(`/api/admin/users/${userId}/notify`, { message });
      setSuccess('Notification sent successfully');
    } catch (error) {
      console.error('Error sending notification:', error);
      setError('Failed to send notification');
    }
  };

  const handleExportUsers = async () => {
    try {
      const response = await api.get('/api/admin/users/export', {
        responseType: 'blob',
        params: {
          search: searchTerm || undefined,
          user_status: statusFilter !== 'all' ? statusFilter : undefined,
          plan: planFilter !== 'all' ? planFilter : undefined,
        }
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      setSuccess('Users exported successfully');
    } catch (error) {
      console.error('Error exporting users:', error);
      setError('Failed to export users');
    }
  };

  // Utility functions
  const resetUserForm = () => {
    setUserForm({
      email: '',
      full_name: '',
      password: '',
      subscription_plan: 'free',
      is_active: true,
      is_admin: false,
      email_verified: false,
      phone: '',
      company: '',
      location: '',
      notes: '',
    });
  };

  const handleOpenUserDialog = (user) => {
    setSelectedUser(user);
    setUserForm({
      email: user.email,
      full_name: user.full_name || '',
      subscription_plan: user.subscription_plan || 'free',
      is_active: user.is_active,
      is_admin: user.is_admin || false,
      email_verified: user.email_verified || false,
      phone: user.phone || '',
      company: user.company || '',
      location: user.location || '',
      notes: user.notes || '',
    });
    setDialogTab(0);
    fetchUserActivity(user.id);
    fetchUserSubscriptions(user.id);
    setOpenDialog(true);
  };

  const handleSort = (column) => {
    const isAsc = sortBy === column && sortOrder === 'asc';
    setSortOrder(isAsc ? 'desc' : 'asc');
    setSortBy(column);
  };

  const handleSelectUser = (userId) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAllUsers = (event) => {
    if (event.target.checked) {
      // Ensure users is an array and filter out null/undefined users
      const validUsers = Array.isArray(users) ? users.filter(user => user && user.id) : [];
      setSelectedUsers(validUsers.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const getStatusChip = (user) => {
    if (!user) {
      return <Chip label="Unknown" color="default" size="small" />;
    }
    if (!user.is_active) {
      return <Chip label="Inactive" color="error" size="small" />;
    } else if (!user.email_verified) {
      return <Chip label="Unverified" color="warning" size="small" />;
    } else {
      return <Chip label="Active" color="success" size="small" />;
    }
  };

  const getPlanChip = (user) => {
    if (!user) {
      return <Chip label="Unknown" color="default" size="small" />;
    }

    const planColors = {
      free: 'default',
      creator: 'info',
      accelerator: 'primary',
      dominator: 'secondary',
    };

    return (
      <Chip
        label={user.subscription_plan || 'Free'}
        color={planColors[user.subscription_plan] || 'default'}
        size="small"
      />
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  // Helper function to clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setPlanFilter('all');
    setDateFilter({ start: null, end: null });
  };

  // Empty State Component
  const EmptyStateComponent = ({
    icon: Icon,
    title,
    subtitle,
    actionButton,
    variant = 'default'
  }) => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        px: 3,
        textAlign: 'center',
        minHeight: 400,
        background: variant === 'error'
          ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.05) 0%, rgba(244, 67, 54, 0.02) 100%)'
          : variant === 'search'
          ? 'linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0.02) 100%)'
          : 'linear-gradient(135deg, rgba(158, 158, 158, 0.05) 0%, rgba(158, 158, 158, 0.02) 100%)',
        borderRadius: 2,
        border: '1px solid',
        borderColor: variant === 'error'
          ? 'error.light'
          : variant === 'search'
          ? 'primary.light'
          : 'grey.200',
        backdropFilter: 'blur(10px)',
        transition: 'all 0.3s ease-in-out',
        animation: 'fadeInUp 0.6s ease-out',
        '@keyframes fadeInUp': {
          '0%': {
            opacity: 0,
            transform: 'translateY(30px)',
          },
          '100%': {
            opacity: 1,
            transform: 'translateY(0)',
          },
        },
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
        }
      }}
      role="status"
      aria-live="polite"
      aria-label={title}
    >
      <Icon
        sx={{
          fontSize: 64,
          color: variant === 'error'
            ? 'error.main'
            : variant === 'search'
            ? 'primary.main'
            : 'grey.400',
          mb: 3,
          opacity: 0.8,
          animation: 'pulse 2s infinite',
          '@keyframes pulse': {
            '0%': { opacity: 0.8 },
            '50%': { opacity: 0.5 },
            '100%': { opacity: 0.8 },
          }
        }}
        aria-hidden="true"
      />
      <Typography
        variant="h5"
        sx={{
          mb: 2,
          fontWeight: 600,
          color: variant === 'error' ? 'error.main' : 'text.primary',
        }}
      >
        {title}
      </Typography>
      <Typography
        variant="body1"
        sx={{
          mb: 4,
          color: 'text.secondary',
          maxWidth: 400,
          lineHeight: 1.6,
        }}
      >
        {subtitle}
      </Typography>
      {actionButton && (
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
          {actionButton}
        </Box>
      )}
    </Box>
  );

  return (
    <UserManagementErrorBoundary>
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <StablePageWrapper
          title="User Management"
          loading={loading && users.length === 0}
          error={error}
          onRetry={fetchUsers}
        >
        <Box>
        {/* Screen Reader Announcements */}
        <Box
          component="div"
          aria-live="polite"
          aria-atomic="true"
          sx={{
            position: 'absolute',
            left: '-10000px',
            width: '1px',
            height: '1px',
            overflow: 'hidden',
          }}
        >
          {screenReaderAnnouncement}
        </Box>

        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4">
              <UsersIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Advanced User Management
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Manage users, subscriptions, and permissions
              {loading && (
                <Box component="span" sx={{ ml: 2, display: 'inline-flex', alignItems: 'center' }}>
                  <CircularProgress size={16} sx={{ mr: 1 }} />
                  <Typography variant="caption" color="primary">
                    {retryCount > 0 ? `Retrying... (${retryCount}/3)` : 'Loading...'}
                  </Typography>
                </Box>
              )}
            </Typography>
          </Box>
          <Box display="flex" gap={1}>
            <Button
              variant="outlined"
              startIcon={<ExportIcon />}
              onClick={handleExportUsers}
            >
              Export
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchUsers}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<AddUserIcon />}
              onClick={() => {
                resetUserForm();
                setOpenCreateDialog(true);
              }}
            >
              Add User
            </Button>
          </Box>
        </Box>

        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
            {success}
          </Alert>
        )}

        {/* Performance Metrics (Development) */}
        {process.env.NODE_ENV === 'development' && performanceMetrics.lastFetchTime && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="caption">
              Last fetch: {performanceMetrics.lastFetchTime.toFixed(0)}ms
              {performanceMetrics.averageLoadTime && (
                <> | Avg: {performanceMetrics.averageLoadTime.toFixed(0)}ms</>
              )}
              {correlationId && <> | ID: {correlationId.slice(-8)}</>}
            </Typography>
          </Alert>
        )}

        {/* Advanced Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">
                <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Advanced Filters & Search
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} md={3}>
                  <TextField
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    size="small"
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl size="small" fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value)}
                      label="Status"
                    >
                      <MenuItem value="all">All Status</MenuItem>
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                      <MenuItem value="unverified">Unverified</MenuItem>
                      <MenuItem value="admin">Admin</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <FormControl size="small" fullWidth>
                    <InputLabel>Plan</InputLabel>
                    <Select
                      value={planFilter}
                      onChange={(e) => setPlanFilter(e.target.value)}
                      label="Plan"
                    >
                      <MenuItem value="all">All Plans</MenuItem>
                      <MenuItem value="free">Free</MenuItem>
                      <MenuItem value="creator">Creator</MenuItem>
                      <MenuItem value="accelerator">Accelerator</MenuItem>
                      <MenuItem value="dominator">Dominator</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={2}>
                  <DatePicker
                    label="From Date"
                    value={dateFilter.start}
                    onChange={(date) => setDateFilter(prev => ({ ...prev, start: date }))}
                    slotProps={{ textField: { size: 'small', fullWidth: true } }}
                  />
                </Grid>
                <Grid item xs={12} md={2}>
                  <DatePicker
                    label="To Date"
                    value={dateFilter.end}
                    onChange={(date) => setDateFilter(prev => ({ ...prev, end: date }))}
                    slotProps={{ textField: { size: 'small', fullWidth: true } }}
                  />
                </Grid>
                <Grid item xs={12} md={1}>
                  <Button
                    variant="outlined"
                    onClick={clearAllFilters}
                    size="small"
                    fullWidth
                    startIcon={<ClearIcon />}
                  >
                    Clear
                  </Button>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </Paper>

        {/* Bulk Operations */}
        {selectedUsers.length > 0 && (
          <Paper sx={{ p: 2, mb: 3, bgcolor: 'primary.50' }}>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="body1">
                {selectedUsers.length} user(s) selected
              </Typography>
              <Box display="flex" gap={1}>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => setBulkDialog(true)}
                >
                  Bulk Actions
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => setSelectedUsers([])}
                >
                  Clear Selection
                </Button>
              </Box>
            </Box>
          </Paper>
        )}

        {/* User Analytics Section */}
        <Paper sx={{ mb: 3 }}>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">
                <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                User Analytics & Insights
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <UserAnalytics />
            </AccordionDetails>
          </Accordion>
        </Paper>

        {/* Advanced Users Table */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selectedUsers.length > 0 && selectedUsers.length < users.length}
                    checked={users.length > 0 && selectedUsers.length === users.length}
                    onChange={handleSelectAllUsers}
                  />
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'full_name'}
                    direction={sortBy === 'full_name' ? sortOrder : 'asc'}
                    onClick={() => handleSort('full_name')}
                  >
                    User
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'email'}
                    direction={sortBy === 'email' ? sortOrder : 'asc'}
                    onClick={() => handleSort('email')}
                  >
                    Email
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'subscription_plan'}
                    direction={sortBy === 'subscription_plan' ? sortOrder : 'asc'}
                    onClick={() => handleSort('subscription_plan')}
                  >
                    Plan
                  </TableSortLabel>
                </TableCell>
                <TableCell>Status</TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'created_at'}
                    direction={sortBy === 'created_at' ? sortOrder : 'asc'}
                    onClick={() => handleSort('created_at')}
                  >
                    Joined
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={sortBy === 'last_login'}
                    direction={sortBy === 'last_login' ? sortOrder : 'asc'}
                    onClick={() => handleSort('last_login')}
                  >
                    Last Active
                  </TableSortLabel>
                </TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                // Loading skeleton rows
                [...Array(rowsPerPage)].map((_, index) => (
                  <TableRow key={index}>
                    <TableCell padding="checkbox">
                      <Skeleton variant="rectangular" width={20} height={20} />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Skeleton variant="circular" width={40} height={40} sx={{ mr: 2 }} />
                        <Box>
                          <Skeleton variant="text" width={120} height={20} />
                          <Skeleton variant="text" width={80} height={16} />
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Skeleton variant="text" width={150} height={20} />
                      <Skeleton variant="text" width={100} height={16} />
                    </TableCell>
                    <TableCell>
                      <Skeleton variant="rectangular" width={80} height={24} sx={{ borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Skeleton variant="rectangular" width={70} height={24} sx={{ borderRadius: 1 }} />
                    </TableCell>
                    <TableCell>
                      <Skeleton variant="text" width={100} height={20} />
                    </TableCell>
                    <TableCell>
                      <Skeleton variant="text" width={100} height={20} />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <Skeleton variant="circular" width={32} height={32} />
                        <Skeleton variant="circular" width={32} height={32} />
                        <Skeleton variant="circular" width={32} height={32} />
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : users.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} sx={{ p: 0, border: 'none' }}>
                    {/* Comprehensive Empty States */}
                    {error ? (
                      // Error State
                      <EmptyStateComponent
                        icon={lastError?.code === 'NETWORK_ERROR' ? OfflineIcon : ErrorIcon}
                        title="Unable to Load Users"
                        subtitle={
                          lastError?.code === 'NETWORK_ERROR'
                            ? "Check your internet connection and try again."
                            : error || "Something went wrong while loading user data. Please try again."
                        }
                        variant="error"
                        actionButton={
                          <>
                            <Button
                              variant="contained"
                              startIcon={<RefreshIcon />}
                              onClick={fetchUsers}
                              sx={{ mr: 1 }}
                            >
                              Retry
                            </Button>
                            {lastError?.code === 'NETWORK_ERROR' && (
                              <Button
                                variant="outlined"
                                startIcon={<ConnectivityIcon />}
                                onClick={() => window.location.reload()}
                              >
                                Reload Page
                              </Button>
                            )}
                          </>
                        }
                      />
                    ) : hasSearchFilters ? (
                      // No Search Results State
                      <EmptyStateComponent
                        icon={NoResultsIcon}
                        title="No Users Match Your Criteria"
                        subtitle="Try adjusting your search terms or filters to find the users you're looking for."
                        variant="search"
                        actionButton={
                          <>
                            <Button
                              variant="contained"
                              startIcon={<ClearIcon />}
                              onClick={clearAllFilters}
                              sx={{ mr: 1 }}
                            >
                              Clear Filters
                            </Button>
                            <Button
                              variant="outlined"
                              startIcon={<RefreshIcon />}
                              onClick={fetchUsers}
                            >
                              Refresh
                            </Button>
                          </>
                        }
                      />
                    ) : (
                      // No Users Found State (Initial/Empty Database)
                      <EmptyStateComponent
                        icon={isInitialLoad ? EmptyFolderIcon : UsersIcon}
                        title={isInitialLoad ? "No Users Found" : "No Users Yet"}
                        subtitle={
                          isInitialLoad
                            ? "Users will appear here once they are created or when data loads successfully."
                            : "Get started by adding your first user to the system."
                        }
                        actionButton={
                          <>
                            <Button
                              variant="contained"
                              startIcon={<AddPersonIcon />}
                              onClick={() => {
                                resetUserForm();
                                setOpenCreateDialog(true);
                              }}
                              sx={{ mr: 1 }}
                            >
                              Add First User
                            </Button>
                            <Button
                              variant="outlined"
                              startIcon={<RefreshIcon />}
                              onClick={fetchUsers}
                            >
                              Refresh
                            </Button>
                          </>
                        }
                      />
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                users.map((user) => (
                  <TableRow
                    key={user.id}
                    selected={selectedUsers.includes(user.id)}
                    hover
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedUsers.includes(user.id)}
                        onChange={() => handleSelectUser(user.id)}
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Badge
                          badgeContent={user.is_admin ? <AdminIcon fontSize="small" /> : null}
                          color="error"
                          overlap="circular"
                        >
                          <Avatar sx={{ mr: 2, width: 40, height: 40 }}>
                            {user.full_name ? user.full_name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                          </Avatar>
                        </Badge>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {user.full_name || 'No name'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {user.id}
                          </Typography>
                          {user.company && (
                            <Typography variant="caption" display="block" color="text.secondary">
                              <BusinessIcon fontSize="inherit" sx={{ mr: 0.5 }} />
                              {user.company}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {user.email}
                        </Typography>
                        {user.phone && (
                          <Typography variant="caption" color="text.secondary">
                            <PhoneIcon fontSize="inherit" sx={{ mr: 0.5 }} />
                            {user.phone}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" flexDirection="column" gap={0.5}>
                        {getPlanChip(user)}
                        {user.subscription_expires && (
                          <Typography variant="caption" color="text.secondary">
                            Expires: {formatDate(user.subscription_expires)}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" flexDirection="column" gap={0.5}>
                        {getStatusChip(user)}
                        {!user.email_verified && (
                          <Chip label="Unverified Email" color="warning" size="small" />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(user.created_at)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatDateTime(user.created_at)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDate(user.last_login)}
                      </Typography>
                      {user.last_login && (
                        <Typography variant="caption" color="text.secondary">
                          {formatDateTime(user.last_login)}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenUserDialog(user)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit User">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenUserDialog(user)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            setAnchorEl(e.currentTarget);
                            setMenuUser(user);
                          }}
                        >
                          <MoreIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalUsers}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(event, newPage) => setPage(newPage)}
          onRowsPerPageChange={(event) => {
            setRowsPerPage(parseInt(event.target.value, 10));
            setPage(0);
          }}
        />
        </TableContainer>

        {/* User Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
        >
          <MenuItem onClick={() => {
            handleToggleUserStatus(menuUser);
            setAnchorEl(null);
          }}>
            {menuUser?.is_active ? <BlockIcon sx={{ mr: 1 }} /> : <ActivateIcon sx={{ mr: 1 }} />}
            {menuUser?.is_active ? 'Deactivate' : 'Activate'}
          </MenuItem>
          <MenuItem onClick={() => {
            // Handle send notification
            setAnchorEl(null);
          }}>
            <NotifyIcon sx={{ mr: 1 }} />
            Send Notification
          </MenuItem>
          <MenuItem onClick={() => {
            // Handle view activity
            setAnchorEl(null);
          }}>
            <HistoryIcon sx={{ mr: 1 }} />
            View Activity
          </MenuItem>
          <MenuItem onClick={() => {
            // Handle reset password
            setAnchorEl(null);
          }}>
            <SecurityIcon sx={{ mr: 1 }} />
            Reset Password
          </MenuItem>
          <Divider />
          <MenuItem
            onClick={() => {
              handleDeleteUser(menuUser);
              setAnchorEl(null);
            }}
            sx={{ color: 'error.main' }}
          >
            <DeleteIcon sx={{ mr: 1 }} />
            Delete User
          </MenuItem>
        </Menu>

        {/* Advanced User Details Dialog */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="lg" fullWidth>
          <DialogTitle>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box display="flex" alignItems="center">
                <Avatar sx={{ mr: 2 }}>
                  {selectedUser?.full_name ? selectedUser.full_name.charAt(0).toUpperCase() : selectedUser?.email.charAt(0).toUpperCase()}
                </Avatar>
                <Box>
                  <Typography variant="h6">
                    {selectedUser?.full_name || 'No name'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedUser?.email}
                  </Typography>
                </Box>
              </Box>
              <Box display="flex" gap={1}>
                {selectedUser?.is_admin && (
                  <Chip label="Admin" color="error" size="small" />
                )}
                {getStatusChip(selectedUser)}
              </Box>
            </Box>
          </DialogTitle>
          <DialogContent>
            <Tabs value={dialogTab} onChange={(e, newValue) => setDialogTab(newValue)}>
              <Tab label="Profile" icon={<UsersIcon />} />
              <Tab label="Subscription" icon={<SubscriptionIcon />} />
              <Tab label="Activity" icon={<ActivityIcon />} />
              <Tab label="Permissions" icon={<SecurityIcon />} />
              <Tab label="Settings" icon={<SettingsIcon />} />
            </Tabs>

            {/* Profile Tab */}
            {dialogTab === 0 && selectedUser && (
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Email"
                      value={userForm.email}
                      onChange={(e) => setUserForm({ ...userForm, email: e.target.value })}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Full Name"
                      value={userForm.full_name}
                      onChange={(e) => setUserForm({ ...userForm, full_name: e.target.value })}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Phone"
                      value={userForm.phone}
                      onChange={(e) => setUserForm({ ...userForm, phone: e.target.value })}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Company"
                      value={userForm.company}
                      onChange={(e) => setUserForm({ ...userForm, company: e.target.value })}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Location"
                      value={userForm.location}
                      onChange={(e) => setUserForm({ ...userForm, location: e.target.value })}
                      fullWidth
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Notes"
                      value={userForm.notes}
                      onChange={(e) => setUserForm({ ...userForm, notes: e.target.value })}
                      fullWidth
                      multiline
                      rows={3}
                      margin="normal"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Account Information
                    </Typography>
                    <Typography variant="body2">
                      Created: {formatDateTime(selectedUser.created_at)}
                    </Typography>
                    <Typography variant="body2">
                      Last Login: {formatDateTime(selectedUser.last_login)}
                    </Typography>
                    <Typography variant="body2">
                      User ID: {selectedUser.id}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Subscription Tab */}
            {dialogTab === 1 && selectedUser && (
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Subscription Plan</InputLabel>
                      <Select
                        value={userForm.subscription_plan}
                        onChange={(e) => setUserForm({ ...userForm, subscription_plan: e.target.value })}
                        label="Subscription Plan"
                      >
                        <MenuItem value="free">Free</MenuItem>
                        <MenuItem value="creator">Creator</MenuItem>
                        <MenuItem value="accelerator">Accelerator</MenuItem>
                        <MenuItem value="dominator">Dominator</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Subscription History
                    </Typography>
                    <List>
                      {userSubscriptions.map((subscription, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <PaymentIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={`${subscription.plan} Plan`}
                            secondary={`${formatDate(subscription.start_date)} - ${subscription.end_date ? formatDate(subscription.end_date) : 'Active'}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Activity Tab */}
            {dialogTab === 2 && selectedUser && (
              <Box sx={{ mt: 2 }}>
                <UserActivityLog userId={selectedUser.id} showTimeline={true} />
              </Box>
            )}

            {/* Permissions Tab */}
            {dialogTab === 3 && selectedUser && (
              <Box sx={{ mt: 2 }}>
                <UserPermissions userId={selectedUser.id} userEmail={selectedUser.email} />
              </Box>
            )}

            {/* Settings Tab */}
            {dialogTab === 4 && selectedUser && (
              <Box sx={{ mt: 2 }}>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={userForm.is_active}
                        onChange={(e) => setUserForm({ ...userForm, is_active: e.target.checked })}
                      />
                    }
                    label="Active Account"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={userForm.email_verified}
                        onChange={(e) => setUserForm({ ...userForm, email_verified: e.target.checked })}
                      />
                    }
                    label="Email Verified"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={userForm.is_admin}
                        onChange={(e) => setUserForm({ ...userForm, is_admin: e.target.checked })}
                      />
                    }
                    label="Admin Privileges"
                  />
                </FormGroup>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
            <Button onClick={handleUpdateUser} variant="contained">
              Save Changes
            </Button>
          </DialogActions>
        </Dialog>

        {/* Create User Dialog */}
        <Dialog open={openCreateDialog} onClose={() => setOpenCreateDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>Create New User</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Email"
                  value={userForm.email}
                  onChange={(e) => setUserForm({ ...userForm, email: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Full Name"
                  value={userForm.full_name}
                  onChange={(e) => setUserForm({ ...userForm, full_name: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Password"
                  type="password"
                  value={userForm.password}
                  onChange={(e) => setUserForm({ ...userForm, password: e.target.value })}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Subscription Plan</InputLabel>
                  <Select
                    value={userForm.subscription_plan}
                    onChange={(e) => setUserForm({ ...userForm, subscription_plan: e.target.value })}
                    label="Subscription Plan"
                  >
                    <MenuItem value="free">Free</MenuItem>
                    <MenuItem value="creator">Creator</MenuItem>
                    <MenuItem value="accelerator">Accelerator</MenuItem>
                    <MenuItem value="dominator">Dominator</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Phone"
                  value={userForm.phone}
                  onChange={(e) => setUserForm({ ...userForm, phone: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Company"
                  value={userForm.company}
                  onChange={(e) => setUserForm({ ...userForm, company: e.target.value })}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <FormGroup row>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={userForm.is_active}
                        onChange={(e) => setUserForm({ ...userForm, is_active: e.target.checked })}
                      />
                    }
                    label="Active"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={userForm.email_verified}
                        onChange={(e) => setUserForm({ ...userForm, email_verified: e.target.checked })}
                      />
                    }
                    label="Email Verified"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={userForm.is_admin}
                        onChange={(e) => setUserForm({ ...userForm, is_admin: e.target.checked })}
                      />
                    }
                    label="Admin"
                  />
                </FormGroup>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenCreateDialog(false)}>Cancel</Button>
            <Button onClick={handleCreateUser} variant="contained">
              Create User
            </Button>
          </DialogActions>
        </Dialog>

        {/* Bulk Operations Dialog */}
        <Dialog open={openBulkDialog} onClose={() => setBulkDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Bulk Operations</DialogTitle>
          <DialogContent>
            <Typography variant="body2" gutterBottom>
              Perform bulk operations on {selectedUsers.length} selected users.
            </Typography>
            <FormControl fullWidth margin="normal">
              <InputLabel>Operation</InputLabel>
              <Select
                value={bulkOperation}
                onChange={(e) => setBulkOperation(e.target.value)}
                label="Operation"
              >
                <MenuItem value="activate">Activate Users</MenuItem>
                <MenuItem value="deactivate">Deactivate Users</MenuItem>
                <MenuItem value="verify_email">Verify Email</MenuItem>
                <MenuItem value="change_plan">Change Plan</MenuItem>
                <MenuItem value="send_notification">Send Notification</MenuItem>
                <MenuItem value="delete">Delete Users</MenuItem>
              </Select>
            </FormControl>

            {bulkOperation === 'change_plan' && (
              <FormControl fullWidth margin="normal">
                <InputLabel>New Plan</InputLabel>
                <Select
                  value={bulkData.plan || ''}
                  onChange={(e) => setBulkData({ ...bulkData, plan: e.target.value })}
                  label="New Plan"
                >
                  <MenuItem value="free">Free</MenuItem>
                  <MenuItem value="creator">Creator</MenuItem>
                  <MenuItem value="accelerator">Accelerator</MenuItem>
                  <MenuItem value="dominator">Dominator</MenuItem>
                </Select>
              </FormControl>
            )}

            {bulkOperation === 'send_notification' && (
              <TextField
                label="Message"
                value={bulkData.message || ''}
                onChange={(e) => setBulkData({ ...bulkData, message: e.target.value })}
                fullWidth
                multiline
                rows={3}
                margin="normal"
              />
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setBulkDialog(false)}>Cancel</Button>
            <Button
              onClick={handleBulkOperation}
              variant="contained"
              color={bulkOperation === 'delete' ? 'error' : 'primary'}
            >
              Execute
            </Button>
          </DialogActions>
        </Dialog>

        {/* Floating Action Button for Quick User Creation */}
        <Zoom in={users.length === 0 && !loading && !error}>
          <Fab
            color="primary"
            aria-label="Add first user"
            sx={{
              position: 'fixed',
              bottom: 24,
              right: 24,
              zIndex: 1000,
              background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #1565c0 0%, #1976d2 100%)',
                transform: 'scale(1.1)',
              },
              transition: 'all 0.3s ease-in-out',
            }}
            onClick={() => {
              resetUserForm();
              setOpenCreateDialog(true);
            }}
          >
            <AddPersonIcon />
          </Fab>
        </Zoom>
        </Box>
      </StablePageWrapper>
    </LocalizationProvider>
  </UserManagementErrorBoundary>
  );
};

export default UserManagement;

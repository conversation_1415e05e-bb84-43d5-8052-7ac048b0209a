"""
E-commerce integration API routes.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request, BackgroundTasks
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from app.core.security import get_current_active_user
from app.models.user import User
from app.models.ecommerce import EcommerceStore, SyncedProduct
from app.services.ecommerce_service import ecommerce_service
from app.services.ecommerce_webhook_service import ecommerce_webhook_service
from app.services.ecommerce_sync_scheduler import ecommerce_sync_scheduler
from app.services.ecommerce_icp_generator import ecommerce_icp_generator
from app.services.ecommerce_campaign_service import ecommerce_campaign_service
# Removed unused imports - using requires_feature dependency instead
from app.api.dependencies.feature_access import requires_feature
from app.utils.error_handling import ExternalServiceError

# Create ServiceError alias for consistency
ServiceError = ExternalServiceError

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/stores", response_model=List[Dict[str, Any]])
async def get_user_stores(
    current_user: User = Depends(get_current_active_user)
):
    """Get all e-commerce stores for the current user."""
    try:
        stores = await ecommerce_service.get_user_stores(str(current_user.id))
        
        # Convert to response format
        stores_data = []
        for store in stores:
            store_data = {
                "id": str(store.id),
                "platform": store.platform,
                "store_name": store.store_name,
                "store_url": store.store_url,
                "status": store.status,
                "last_sync_at": store.last_sync_at.isoformat() if store.last_sync_at else None,
                "total_products": store.total_products,
                "synced_products": store.synced_products,
                "sync_enabled": store.sync_enabled,
                "created_at": store.created_at.isoformat(),
                "last_error": store.last_error
            }
            stores_data.append(store_data)
        
        return stores_data
        
    except Exception as e:
        logger.error(f"Failed to get user stores: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve stores"
        )


@router.post("/stores/connect")
async def connect_store(
    request: Dict[str, Any],
    current_user: User = Depends(requires_feature("ecommerce_integration"))
):
    """Connect a new e-commerce store."""
    try:
        platform = request.get("platform")
        authorization_code = request.get("authorization_code")
        state = request.get("state")
        redirect_uri = request.get("redirect_uri")
        store_url = request.get("store_url")

        if not all([platform, authorization_code, state, redirect_uri]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing required parameters"
            )

        # Type validation
        if not isinstance(platform, str) or not isinstance(authorization_code, str) or \
           not isinstance(state, str) or not isinstance(redirect_uri, str):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid parameter types"
            )

        store = await ecommerce_service.connect_store(
            current_user,
            platform,
            authorization_code,
            state,
            redirect_uri,
            store_url
        )
        
        return {
            "success": True,
            "store_id": str(store.id),
            "store_name": store.store_name,
            "platform": store.platform,
            "status": store.status
        }
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to connect store: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to connect store"
        )


@router.delete("/stores/{store_id}")
async def disconnect_store(
    store_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Disconnect and remove a store."""
    try:
        success = await ecommerce_service.disconnect_store(store_id, str(current_user.id))
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found"
            )
        
        return {"success": True, "message": "Store disconnected successfully"}
        
    except Exception as e:
        logger.error(f"Failed to disconnect store: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disconnect store"
        )


@router.post("/stores/{store_id}/sync")
async def sync_store_products(
    store_id: str,
    background_tasks: BackgroundTasks,
    request: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_active_user)
):
    """Manually trigger product sync for a store."""
    try:
        force_full_sync = request.get("force_full_sync", False) if request else False
        
        # Start sync in background
        background_tasks.add_task(
            ecommerce_service.sync_store_products,
            store_id,
            str(current_user.id),
            250,
            force_full_sync
        )
        
        return {
            "success": True,
            "message": "Product sync started",
            "sync_type": "full" if force_full_sync else "incremental"
        }
        
    except Exception as e:
        logger.error(f"Failed to start sync: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start sync"
        )


@router.get("/stores/{store_id}/products")
async def get_store_products(
    store_id: str,
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_active_user)
):
    """Get products for a store."""
    try:
        result = await ecommerce_service.get_store_products(
            store_id, str(current_user.id), limit, offset
        )
        
        # Convert products to response format
        products_data = []
        for product in result["products"]:
            product_data = {
                "id": str(product.id),
                "external_product_id": product.external_product_id,
                "title": product.title,
                "description": product.description,
                "price": float(product.price),
                "compare_at_price": float(product.compare_at_price) if product.compare_at_price else None,
                "currency": product.currency,
                "sku": product.sku,
                "vendor": product.vendor,
                "category": product.category,
                "tags": product.tags,
                "featured_image": product.featured_image,
                "status": product.status,
                "inventory_quantity": product.inventory_quantity,
                "last_synced_at": product.last_synced_at.isoformat(),
                "content_generated": product.content_generated,
                "content_generation_count": product.content_generation_count
            }
            products_data.append(product_data)
        
        return {
            "products": products_data,
            "total": result["total"],
            "limit": result["limit"],
            "offset": result["offset"],
            "has_more": result["has_more"]
        }
        
    except Exception as e:
        logger.error(f"Failed to get store products: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve products"
        )


@router.post("/stores/{store_id}/products/generate-icps")
async def generate_product_icps(
    store_id: str,
    request: Dict[str, Any],
    current_user: User = Depends(requires_feature("icp_generation"))
):
    """Generate ICPs based on store products."""
    try:
        product_ids = request.get("product_ids")
        category = request.get("category")
        count = request.get("count", 3)
        
        if product_ids:
            icps = await ecommerce_icp_generator.generate_product_icps(
                str(current_user.id), store_id, product_ids, count
            )
        elif category:
            icps = await ecommerce_icp_generator.generate_category_icps(
                str(current_user.id), store_id, category, count
            )
        else:
            icps = await ecommerce_icp_generator.generate_product_icps(
                str(current_user.id), store_id, None, count
            )
        
        # Convert ICPs to response format
        icps_data = []
        for icp in icps:
            icp_data = {
                "id": str(icp.id),
                "name": icp.name,
                "description": icp.description,
                "demographics": icp.demographics.model_dump(),
                "decision_maker": icp.decision_maker.model_dump(),
                "pain_points": [pp.model_dump() for pp in icp.pain_points],
                "goals": [goal.model_dump() for goal in icp.goals],
                "objections": [obj.model_dump() for obj in icp.objections],
                "content_preferences": [cp.model_dump() for cp in icp.content_preferences],
                "buying_process": icp.buying_process,
                "is_ai_generated": icp.is_ai_generated
            }
            icps_data.append(icp_data)
        
        return {
            "success": True,
            "icps": icps_data,
            "count": len(icps_data)
        }
        
    except Exception as e:
        logger.error(f"Failed to generate product ICPs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate ICPs"
        )


@router.post("/campaigns/product-campaign")
async def create_product_campaign(
    request: Dict[str, Any],
    current_user: User = Depends(requires_feature("campaign_management"))
):
    """Create a new campaign with product integration."""
    try:
        campaign_data = request.get("campaign_data", {})
        product_ids = request.get("product_ids", [])
        store_id = request.get("store_id")
        
        if not store_id or not product_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Store ID and product IDs are required"
            )
        
        campaign = await ecommerce_campaign_service.create_product_campaign(
            current_user, campaign_data, product_ids, store_id
        )
        
        return {
            "success": True,
            "campaign_id": str(campaign.id),
            "campaign_name": campaign.name,
            "products_count": len(campaign.target_products)
        }
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to create product campaign: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create campaign"
        )


@router.post("/campaigns/{campaign_id}/generate-content")
async def generate_campaign_content(
    campaign_id: str,
    request: Dict[str, Any],
    current_user: User = Depends(requires_feature("content_generation"))
):
    """Generate content for campaign products."""
    try:
        content_requests = request.get("content_requests", [])
        
        if not content_requests:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content requests are required"
            )
        
        generated_content = await ecommerce_campaign_service.generate_product_content(
            campaign_id, str(current_user.id), content_requests
        )
        
        return {
            "success": True,
            "generated_content": generated_content,
            "count": len(generated_content)
        }
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to generate campaign content: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate content"
        )


@router.get("/sync/status")
async def get_sync_status(
    current_user: User = Depends(get_current_active_user)
):
    """Get sync status for all user stores."""
    try:
        status_info = await ecommerce_sync_scheduler.get_sync_status(str(current_user.id))
        return status_info
        
    except Exception as e:
        logger.error(f"Failed to get sync status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get sync status"
        )


@router.post("/webhooks/{platform}")
async def handle_webhook(
    platform: str,
    request: Request
):
    """Handle webhooks from e-commerce platforms."""
    try:
        # Get raw body and headers
        body = await request.body()
        headers = dict(request.headers)
        
        # Get signature for verification
        signature = headers.get("x-shopify-hmac-sha256") or headers.get("x-wc-webhook-signature")
        
        result = await ecommerce_webhook_service.handle_webhook(
            platform, body, headers, signature
        )
        
        if result["success"]:
            return {"status": "ok"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Webhook processing failed")
            )
        
    except Exception as e:
        logger.error(f"Failed to handle webhook: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Webhook processing failed"
        )

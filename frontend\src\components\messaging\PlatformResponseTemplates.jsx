/**
 * Enhanced Platform Response Templates - Enterprise-grade template management component
 * Features: Plan-based template limitations, real-time template tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced template management capabilities and interactive template exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from "react";
import PropTypes from 'prop-types';
import platformService from '../../services/platformService';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Divider,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  TextField,
  Tabs,
  Tab,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Snackbar,
  Alert,
  alpha
} from '@mui/material';
import {
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  EmojiEmotions as EmojiIcon,
  Refresh as RefreshIcon,
  Preview as PreviewIcon
} from '@mui/icons-material';
import api from '../../api';
import { useAuth } from '../../contexts/AuthContext';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import { useNotification } from '../../hooks/useNotification';
import GlassmorphicCard from '../common/GlassmorphicCard';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Platform colors for consistent branding
const platformColors = {
  facebook: '#1877F2',
  twitter: '#1DA1F2',
  linkedin: '#0A66C2',
  instagram: '#E4405F'
};

// Template display modes with enhanced configurations
const TEMPLATE_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Templates',
    description: 'Basic template management interface',
    subscriptionLimits: {
      creator: { available: true, maxTemplateTypes: 5, features: ['basic_templates'] },
      accelerator: { available: true, maxTemplateTypes: 20, features: ['basic_templates', 'analytics_templates'] },
      dominator: { available: true, maxTemplateTypes: -1, features: ['basic_templates', 'analytics_templates', 'ai_insights'] }
    }
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Templates',
    description: 'Comprehensive template management',
    subscriptionLimits: {
      creator: { available: false, maxTemplateTypes: 0, features: [] },
      accelerator: { available: true, maxTemplateTypes: 10, features: ['detailed_templates', 'template_analytics'] },
      dominator: { available: true, maxTemplateTypes: -1, features: ['detailed_templates', 'template_analytics', 'real_time_insights'] }
    }
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Templates',
    description: 'AI-powered template creation and optimization',
    subscriptionLimits: {
      creator: { available: false, maxTemplateTypes: 0, features: [] },
      accelerator: { available: false, maxTemplateTypes: 0, features: [] },
      dominator: { available: true, maxTemplateTypes: -1, features: ['ai_assisted', 'ai_optimization', 'template_insights'] }
    }
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Templates',
    description: 'Advanced template analytics and insights',
    subscriptionLimits: {
      creator: { available: false, maxTemplateTypes: 0, features: [] },
      accelerator: { available: false, maxTemplateTypes: 0, features: [] },
      dominator: { available: true, maxTemplateTypes: -1, features: ['analytics_templates', 'template_insights'] }
    }
  }
};

/**
 * Enhanced Platform Response Templates Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Function} [props.onSave] - Callback when templates are saved
 * @param {boolean} [props.loading=false] - Whether data is loading
 * @param {Object} [props.templates] - Optional pre-loaded templates
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=false] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onUpgrade] - Upgrade callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-platform-response-templates'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const PlatformResponseTemplates = memo(forwardRef(({
  onSave,
  loading: externalLoading = false,
  templates: externalTemplates,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = false,
  onExport,
  onRefresh,
  onUpgrade,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-platform-response-templates',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useAccessibility();
  const { user } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // Core state management
  const templateRef = useRef(null);
  const [activePlatform, setActivePlatform] = useState('facebook');
  const [loading, setLoading] = useState(externalLoading || false);
  const [templates, setTemplates] = useState(externalTemplates || {
    facebook: [],
    twitter: [],
    linkedin: [],
    instagram: []
  });
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);

  // Enhanced state management
  const [templateMode, setTemplateMode] = useState('compact');
  const [templateHistory, setTemplateHistory] = useState([]);
  const [templateAnalytics, setTemplateAnalytics] = useState(null);
  const [templateInsights, setTemplateInsights] = useState(null);
  const [customTemplates, setCustomTemplates] = useState([]);
  const [templatePreferences, setTemplatePreferences] = useState({
    autoSave: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: false,
    templateSuggestions: true
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [templateDrawerOpen, setTemplateDrawerOpen] = useState(false);
  const [selectedTemplateType, setSelectedTemplateType] = useState(null);
  const [templateStats, setTemplateStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastTemplateCheck, setLastTemplateCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Apply feature overrides based on props
    const advancedFeaturesEnabled = enableAdvancedFeatures && subscription?.plan_id !== 'creator';
    const aiInsightsEnabled = enableAIInsights && subscription?.plan_id === 'dominator';

    const features = {
      creator: {
        maxTemplateTypes: 5,
        maxTemplatesPerPlatform: 10,
        hasAdvancedTemplates: false,
        hasTemplateAnalytics: false,
        hasCustomTemplates: false,
        hasTemplateInsights: false,
        hasTemplateHistory: false,
        hasAIAssistance: false,
        hasTemplateExport: false,
        hasTemplateScheduling: false,
        hasTemplateAutomation: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxTemplateTypes: 20,
        maxTemplatesPerPlatform: 50,
        hasAdvancedTemplates: true,
        hasTemplateAnalytics: true,
        hasCustomTemplates: true,
        hasTemplateInsights: false,
        hasTemplateHistory: true,
        hasAIAssistance: false,
        hasTemplateExport: true,
        hasTemplateScheduling: true,
        hasTemplateAutomation: false,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxTemplateTypes: -1,
        maxTemplatesPerPlatform: -1,
        hasAdvancedTemplates: true,
        hasTemplateAnalytics: true,
        hasCustomTemplates: true,
        hasTemplateInsights: true,
        hasTemplateHistory: true,
        hasAIAssistance: true,
        hasTemplateExport: true,
        hasTemplateScheduling: true,
        hasTemplateAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'ai-powered',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    // Apply feature overrides
    const enhancedFeatures = {
      ...currentFeatures,
      hasAdvancedTemplates: currentFeatures.hasAdvancedTemplates && advancedFeaturesEnabled,
      hasTemplateInsights: currentFeatures.hasTemplateInsights && aiInsightsEnabled,
      hasTemplateAnalytics: currentFeatures.hasTemplateAnalytics && advancedFeaturesEnabled
    };

    return {
      ...enhancedFeatures,
      hasFeatureAccess: (feature) => enhancedFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = enhancedFeatures[feature] === true;
        const withinLimits = enhancedFeatures.maxTemplateTypes === -1 || currentUsage < enhancedFeatures.maxTemplateTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription, enableAdvancedFeatures, enableAIInsights]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Template management with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Template interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive template API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getTemplateHistory: () => templateHistory,
    getTemplateAnalytics: () => templateAnalytics,
    getTemplateInsights: () => templateInsights,
    refreshTemplates: () => {
      fetchTemplateAnalytics();
      fetchTemplates();
      if (onRefresh) onRefresh();
    },

    // Template methods
    focusTemplate: () => {
      if (templateRef.current) {
        templateRef.current.focus();
      }
    },
    addNewTemplate: () => handleAddTemplate(),
    editTemplate: (template) => handleEditTemplate(template),
    deleteTemplate: (id) => handleDeleteTemplate(id),
    previewTemplate: (template) => handlePreviewTemplate(template),
    openTemplateDrawer: () => setTemplateDrawerOpen(true),
    closeTemplateDrawer: () => setTemplateDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportTemplateData: () => {
      if (subscriptionFeatures.hasExport && onExport) {
        onExport(templateHistory, templateAnalytics);
      }
    },

    // Accessibility methods
    announceTemplate: (message) => announceToScreenReader(message),
    focusTemplateField: () => setFocusToElement('template-name-field'),

    // Advanced methods
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getCurrentStep: () => activeStep,
    goToStep: (step) => setActiveStep(step),
    getValidationErrors: () => validationErrors,
    switchPlatform: (platform) => setActivePlatform(platform),
    getCurrentPlatform: () => activePlatform,

    // Enhanced methods
    changeTemplateMode: (mode) => handleTemplateModeChange(mode),
    showUpgradePrompt: (feature) => handleUpgradePrompt(feature),
    optimizeTemplates: () => handleRealTimeOptimization(),
    getCurrentMode: () => templateMode,
    getTemplatePreferences: () => templatePreferences,
    getCustomTemplates: () => customTemplates,

    // Advanced template methods
    addCustomTemplate: (template) => addCustomTemplate(template),
    updatePreferences: (prefs) => updateTemplatePreferences(prefs),
    selectTemplateType: (type) => handleTemplateTypeSelection(type),
    generateAISuggestions: () => generateAISuggestions(),
    validateTemplateData: (template) => validateTemplate(template),
    fetchTemplateStats: () => fetchTemplateStats(),
    checkBackendHealth: () => checkBackendHealth(),
    getSelectedType: () => selectedTemplateType,
    getTemplateStatsData: () => templateStats,
    getAISuggestionsData: () => aiSuggestions,
    isDrawerOpen: () => templateDrawerOpen,
    isAnalyticsVisible: () => showAnalytics,
    isFullscreenActive: () => fullscreenMode,
    isBackendAvailable: () => backendAvailable,
    getLastHealthCheck: () => lastTemplateCheck
  }), [
    templateHistory,
    templateAnalytics,
    templateInsights,
    templateStats,
    subscriptionFeatures,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    activeStep,
    validationErrors,
    activePlatform,
    fetchTemplateAnalytics,
    fetchTemplates,
    handleAddTemplate,
    handleEditTemplate,
    handleDeleteTemplate,
    handlePreviewTemplate,
    customTemplates,
    handleRealTimeOptimization,
    handleTemplateModeChange,
    handleUpgradePrompt,
    templateMode,
    templatePreferences,
    addCustomTemplate,
    backendAvailable,
    checkBackendHealth,
    fetchTemplateStats,
    fullscreenMode,
    generateAISuggestions,
    handleTemplateTypeSelection,
    lastTemplateCheck,
    selectedTemplateType,
    showAnalytics,
    templateDrawerOpen,
    updateTemplatePreferences,
    validateTemplate
  ]);

  // Get platform configuration using centralized service
  const getPlatformConfig = (platform) => {
    try {
      return {
        characterLimit: platformService.getCharacterLimit(platform),
        icon: platformService.getPlatformIcon(platform),
        color: platformService.getPlatformColor(platform)
      };
    } catch (error) {
      console.warn(`Failed to get platform config for ${platform}:`, error);
      return {
        characterLimit: 3000,
        icon: <FacebookIcon />,
        color: '#666666'
      };
    }
  };
  
  // Fetch template analytics with enhanced error handling and retry logic
  const fetchTemplateAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/template/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setTemplateAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (templatePreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Template analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch template analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load template analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, templatePreferences.showAnalytics]);

  // Fetch template insights
  const fetchTemplateInsights = useCallback(async () => {
    if (!subscriptionFeatures.hasTemplateInsights) return;

    await handleApiRequest(
      async () => {
        const response = await api.get('/api/template/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setTemplateInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch template insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest, subscriptionFeatures.hasTemplateInsights]);

  // Load templates on mount
  useEffect(() => {
    if (!externalTemplates) {
      fetchTemplates();
    }
  }, [externalTemplates, fetchTemplates]);

  // Initial data loading
  useEffect(() => {
    if (subscriptionFeatures.hasTemplateAnalytics) {
      fetchTemplateAnalytics();
    }
    if (subscriptionFeatures.hasTemplateInsights) {
      fetchTemplateInsights();
    }
  }, [subscriptionFeatures.hasTemplateAnalytics, subscriptionFeatures.hasTemplateInsights, fetchTemplateAnalytics, fetchTemplateInsights]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && templates) {
      handleRealTimeOptimization();
    }
  }, [enableRealTimeOptimization, templates, handleRealTimeOptimization]);

  // Periodic health checks in production
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [checkBackendHealth]);

  // Fetch stats when templates change
  useEffect(() => {
    if (templates && subscriptionFeatures.hasTemplateAnalytics) {
      fetchTemplateStats();
    }
  }, [templates, subscriptionFeatures.hasTemplateAnalytics, fetchTemplateStats]);

  // Generate AI suggestions when current template changes
  useEffect(() => {
    if (currentTemplate && subscriptionFeatures.hasAIAssistance && templatePreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [currentTemplate, subscriptionFeatures.hasAIAssistance, templatePreferences.aiAssistance, generateAISuggestions]);

  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true);

      const response = await api.get('/api/ai-feedback/templates');

      setTemplates(response.data);

    } catch (error) {
      console.error('Error fetching templates:', error);
      showErrorNotification('Failed to load response templates. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [showErrorNotification]);

  const handlePlatformChange = useCallback((_event, newValue) => {
    setActivePlatform(newValue);

    // Track platform change
    const platformRecord = {
      id: Date.now(),
      type: 'platform_change',
      platform: newValue,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTemplateHistory(prev => [platformRecord, ...prev.slice(0, 99)]);

    if (templatePreferences.showAnalytics) {
      announceToScreenReader(`Switched to ${newValue} platform`);
    }
  }, [user?.id, templatePreferences.showAnalytics, announceToScreenReader]);

  const handleAddTemplate = useCallback(() => {
    setCurrentTemplate({
      id: null,
      name: '',
      content: '',
      platform: activePlatform,
      variables: [],
      tone: 'professional',
      use_emoji: activePlatform === 'instagram',
      active: true
    });
    setEditDialogOpen(true);

    // Track template creation start
    const createRecord = {
      id: Date.now(),
      type: 'template_create_start',
      platform: activePlatform,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTemplateHistory(prev => [createRecord, ...prev.slice(0, 99)]);

    if (templatePreferences.showAnalytics) {
      announceToScreenReader('Template creation dialog opened');
    }
  }, [activePlatform, user?.id, templatePreferences.showAnalytics, announceToScreenReader]);

  const handleEditTemplate = useCallback((template) => {
    setCurrentTemplate({ ...template });
    setEditDialogOpen(true);

    // Track template edit start
    const editRecord = {
      id: Date.now(),
      type: 'template_edit_start',
      templateId: template.id,
      templateName: template.name,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTemplateHistory(prev => [editRecord, ...prev.slice(0, 99)]);

    if (templatePreferences.showAnalytics) {
      announceToScreenReader(`Editing template: ${template.name}`);
    }
  }, [user?.id, templatePreferences.showAnalytics, announceToScreenReader]);

  const handleDeleteTemplate = useCallback(async (templateId) => {
    try {
      setLoading(true);
      
      await api.delete(`/api/ai-feedback/templates/${templateId}`);
      
      // Update local state
      setTemplates(prev => ({
        ...prev,
        [activePlatform]: prev[activePlatform].filter(t => t.id !== templateId)
      }));
      
      showSuccessNotification('Template deleted successfully');
      
    } catch (error) {
      console.error('Error deleting template:', error);
      showErrorNotification('Failed to delete template. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [activePlatform, showSuccessNotification, showErrorNotification]);

  const handleSaveTemplate = useCallback(async () => {
    try {
      setLoading(true);
      
      if (!currentTemplate.name.trim()) {
        showErrorNotification('Template name is required');
        setLoading(false);
        return;
      }
      
      if (!currentTemplate.content.trim()) {
        showErrorNotification('Template content is required');
        setLoading(false);
        return;
      }
      
      let response;
      
      if (currentTemplate.id) {
        // Update existing template
        response = await api.put(`/api/ai-feedback/templates/${currentTemplate.id}`, currentTemplate);
        
        // Update local state
        setTemplates(prev => ({
          ...prev,
          [activePlatform]: prev[activePlatform].map(t => 
            t.id === currentTemplate.id ? response.data : t
          )
        }));
        
        showSuccessNotification('Template updated successfully');
      } else {
        // Create new template
        response = await api.post('/api/ai-feedback/templates', currentTemplate);
        
        // Update local state
        setTemplates(prev => ({
          ...prev,
          [activePlatform]: [...prev[activePlatform], response.data]
        }));
        
        showSuccessNotification('Template created successfully');
      }
      
      setEditDialogOpen(false);
      
      // Call onSave callback if provided
      if (onSave) {
        onSave(templates);
      }
      
    } catch (error) {
      console.error('Error saving template:', error);
      showErrorNotification('Failed to save template. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [currentTemplate, activePlatform, templates, onSave, showSuccessNotification, showErrorNotification]);

  const handlePreviewTemplate = useCallback((template) => {
    setCurrentTemplate(template);
    setPreviewDialogOpen(true);

    // Track template preview
    const previewRecord = {
      id: Date.now(),
      type: 'template_preview',
      templateId: template.id,
      templateName: template.name,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTemplateHistory(prev => [previewRecord, ...prev.slice(0, 99)]);

    if (templatePreferences.showAnalytics) {
      announceToScreenReader(`Previewing template: ${template.name}`);
    }
  }, [user?.id, templatePreferences.showAnalytics, announceToScreenReader]);

  const handleClosePreview = useCallback(() => {
    setPreviewDialogOpen(false);
  }, []);

  const handleCloseEditDialog = useCallback(() => {
    setEditDialogOpen(false);
  }, []);

  // Handle template mode switching
  const handleTemplateModeChange = useCallback((newMode) => {
    if (TEMPLATE_MODES[newMode.toUpperCase()]) {
      setTemplateMode(newMode);
      announceToScreenReader(`Template mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setTemplateHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (templatePreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} template mode`);
      }
    }
  }, [announceToScreenReader, user?.id, templatePreferences.showAnalytics, showSuccess]);

  // Handle upgrade prompts
  const handleUpgradePrompt = useCallback((feature) => {
    if (onUpgrade) {
      onUpgrade(feature);
    } else {
      showError(`${feature} requires a plan upgrade`);
    }

    // Track upgrade prompt
    const upgradeRecord = {
      id: Date.now(),
      type: 'upgrade_prompt',
      feature,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTemplateHistory(prev => [upgradeRecord, ...prev.slice(0, 99)]);
  }, [onUpgrade, showError, user?.id]);

  // Handle real-time optimization
  const handleRealTimeOptimization = useCallback(() => {
    if (enableRealTimeOptimization && subscriptionFeatures.hasTemplateAnalytics) {
      // Optimize template management based on analytics
      fetchTemplateAnalytics();

      if (templatePreferences.showAnalytics) {
        showSuccess('Real-time optimization applied');
      }
    }
  }, [enableRealTimeOptimization, subscriptionFeatures.hasTemplateAnalytics, fetchTemplateAnalytics, templatePreferences.showAnalytics, showSuccess]);

  // Handle custom template management
  const addCustomTemplate = useCallback((template) => {
    if (subscriptionFeatures.hasCustomTemplates) {
      const newTemplate = {
        id: Date.now(),
        ...template,
        createdAt: new Date().toISOString(),
        userId: user?.id
      };

      setCustomTemplates(prev => [...prev, newTemplate]);

      // Track template creation
      const templateRecord = {
        id: Date.now(),
        type: 'custom_template_created',
        template: newTemplate.name,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setTemplateHistory(prev => [templateRecord, ...prev.slice(0, 99)]);

      if (templatePreferences.showAnalytics) {
        showSuccess(`Custom template "${template.name}" created`);
      }
    } else {
      handleUpgradePrompt('Custom Templates');
    }
  }, [subscriptionFeatures.hasCustomTemplates, user?.id, templatePreferences.showAnalytics, showSuccess, handleUpgradePrompt]);

  // Handle template preferences updates
  const updateTemplatePreferences = useCallback((newPreferences) => {
    setTemplatePreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTemplateHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (templatePreferences.showAnalytics) {
      showSuccess('Template preferences updated');
    }
  }, [user?.id, templatePreferences.showAnalytics, showSuccess]);

  // Handle template type selection
  const handleTemplateTypeSelection = useCallback((templateType) => {
    setSelectedTemplateType(templateType);

    // Track template type selection
    const typeRecord = {
      id: Date.now(),
      type: 'template_type_selected',
      templateType,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setTemplateHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (templatePreferences.showAnalytics) {
      announceToScreenReader(`Selected template type: ${templateType}`);
    }
  }, [user?.id, templatePreferences.showAnalytics, announceToScreenReader]);

  // Handle AI suggestions generation
  const generateAISuggestions = useCallback(async () => {
    if (subscriptionFeatures.hasAIAssistance) {
      try {
        const response = await api.get('/api/template/ai-suggestions', {
          params: {
            platform: activePlatform,
            context: currentTemplate?.content || ''
          }
        });

        setAiSuggestions(response.data.suggestions || []);

        if (templatePreferences.showAnalytics) {
          showSuccess('AI suggestions generated');
        }
      } catch (error) {
        console.error('Failed to generate AI suggestions:', error);
        showError('Failed to generate AI suggestions');
      }
    }
  }, [subscriptionFeatures.hasAIAssistance, activePlatform, currentTemplate?.content, templatePreferences.showAnalytics, showSuccess, showError]);

  // Handle template stats fetching
  const fetchTemplateStats = useCallback(async () => {
    if (subscriptionFeatures.hasTemplateAnalytics) {
      try {
        const response = await api.get('/api/template/stats');
        setTemplateStats(response.data);
      } catch (error) {
        console.error('Failed to fetch template stats:', error);
      }
    }
  }, [subscriptionFeatures.hasTemplateAnalytics]);

  // Handle validation errors
  const validateTemplate = useCallback((template) => {
    const errors = {};

    if (!template.name?.trim()) {
      errors.name = 'Template name is required';
    }
    if (!template.content?.trim()) {
      errors.content = 'Template content is required';
    }
    if (template.content && template.content.length > 2000) {
      errors.content = 'Template content must be less than 2000 characters';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Handle backend health monitoring
  const checkBackendHealth = useCallback(async () => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('/api/health', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const wasUnavailable = !backendAvailable;
        setBackendAvailable(true);
        setLastTemplateCheck(Date.now());

        if (wasUnavailable && templatePreferences.showAnalytics) {
          showSuccess("Connection restored - Template features available");
        }
      } else {
        setBackendAvailable(false);
        if (templatePreferences.showAnalytics) {
          showError("Backend service unavailable - Some template features may be limited");
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Health check failed:', error);
        setBackendAvailable(false);

        // Show notification only if it's been a while since last check
        const timeSinceLastCheck = Date.now() - lastTemplateCheck;
        if (timeSinceLastCheck > 60000 && templatePreferences.showAnalytics) { // 1 minute
          showError("Connection issues detected - Templates may be delayed");
        }
      }
    }
  }, [backendAvailable, lastTemplateCheck, templatePreferences.showAnalytics, showSuccess, showError]);
  
  // Render loading state
  if (loading && !templates) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box
      {...getAccessibilityProps()}
      ref={templateRef}
      sx={{
        mb: 4,
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Platform-Specific Response Templates
        </Typography>
        
        <Box>
          <Tooltip title="Refresh templates">
            <IconButton onClick={fetchTemplates} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddTemplate}
            disabled={loading}
          >
            Add Template
          </Button>
        </Box>
      </Box>
      
      <Tabs
        value={activePlatform}
        onChange={handlePlatformChange}
        aria-label="platform tabs"
        sx={{ mb: 3 }}
      >
        <Tab 
          icon={<FacebookIcon />} 
          label="Facebook" 
          value="facebook"
          sx={{ 
            '&.Mui-selected': { color: platformColors.facebook },
          }}
        />
        <Tab 
          icon={<TwitterIcon />} 
          label="Twitter" 
          value="twitter"
          sx={{ 
            '&.Mui-selected': { color: platformColors.twitter },
          }}
        />
        <Tab 
          icon={<LinkedInIcon />} 
          label="LinkedIn" 
          value="linkedin"
          sx={{ 
            '&.Mui-selected': { color: platformColors.linkedin },
          }}
        />
        <Tab 
          icon={<InstagramIcon />} 
          label="Instagram" 
          value="instagram"
          sx={{ 
            '&.Mui-selected': { color: platformColors.instagram },
          }}
        />
      </Tabs>
      
      <Box>
        <Typography variant="subtitle1" gutterBottom>
          {activePlatform.charAt(0).toUpperCase() + activePlatform.slice(1)} Templates
          <Chip
            label={`${getPlatformConfig(activePlatform).characterLimit} char limit`}
            size="small"
            sx={{ ml: 2, bgcolor: alpha(getPlatformConfig(activePlatform).color, 0.1), color: getPlatformConfig(activePlatform).color }}
          />
        </Typography>
        
        {templates[activePlatform]?.length === 0 ? (
          <GlassmorphicCard variant="glass" sx={{ p: 4, textAlign: 'center' }}>
            <Typography variant="body1" color="textSecondary" gutterBottom>
              No templates found for {activePlatform}
            </Typography>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddTemplate}
              sx={{ mt: 2 }}
            >
              Create First Template
            </Button>
          </GlassmorphicCard>
        ) : (
          <Grid container spacing={3}>
            {templates[activePlatform]?.map((template) => (
              <Grid item xs={12} md={6} key={template.id}>
                <GlassmorphicCard variant="glass">
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6">{template.name}</Typography>
                      <Box>
                        <Tooltip title="Preview">
                          <IconButton size="small" onClick={() => handlePreviewTemplate(template)}>
                            <PreviewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton size="small" onClick={() => handleEditTemplate(template)}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton size="small" onClick={() => handleDeleteTemplate(template.id)}>
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>
                    
                    <Divider sx={{ mb: 2 }} />
                    
                    <Typography variant="body2" sx={{ 
                      whiteSpace: 'pre-wrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                    }}>
                      {template.content}
                    </Typography>
                    
                    <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      <Chip
                        label={template.tone}
                        size="small"
                        sx={{ bgcolor: alpha(ACE_COLORS.PURPLE, 0.1), color: ACE_COLORS.PURPLE }}
                      />
                      {template.use_emoji && (
                        <Chip
                          icon={<EmojiIcon />}
                          label="Emoji"
                          size="small"
                          sx={{ bgcolor: alpha(ACE_COLORS.YELLOW, 0.1), color: ACE_COLORS.DARK }}
                        />
                      )}
                      {template.variables.map((variable, idx) => (
                        <Chip
                          key={idx}
                          label={variable}
                          size="small"
                          sx={{ bgcolor: alpha(ACE_COLORS.PURPLE, 0.1), color: ACE_COLORS.PURPLE }}
                        />
                      ))}
                    </Box>
                  </Box>
                </GlassmorphicCard>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
      
      {/* Edit Dialog */}
      <Dialog 
        open={editDialogOpen} 
        onClose={handleCloseEditDialog}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          {currentTemplate?.id ? 'Edit Template' : 'Create New Template'}
        </DialogTitle>
        <DialogContent>
          {currentTemplate && (
            <Box sx={{ mt: 2 }}>
              <TextField
                label="Template Name"
                fullWidth
                value={currentTemplate.name}
                onChange={(e) => setCurrentTemplate({ ...currentTemplate, name: e.target.value })}
                margin="normal"
                required
              />
              
              <TextField
                label="Template Content"
                fullWidth
                multiline
                rows={6}
                value={currentTemplate.content}
                onChange={(e) => setCurrentTemplate({ ...currentTemplate, content: e.target.value })}
                margin="normal"
                required
                helperText={`${currentTemplate.content.length}/${getPlatformConfig(currentTemplate.platform).characterLimit} characters`}
                error={currentTemplate.content.length > getPlatformConfig(currentTemplate.platform).characterLimit}
              />
              
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth margin="normal">
                    <InputLabel>Tone</InputLabel>
                    <Select
                      value={currentTemplate.tone}
                      onChange={(e) => setCurrentTemplate({ ...currentTemplate, tone: e.target.value })}
                      label="Tone"
                    >
                      <MenuItem value="professional">Professional</MenuItem>
                      <MenuItem value="friendly">Friendly</MenuItem>
                      <MenuItem value="casual">Casual</MenuItem>
                      <MenuItem value="formal">Formal</MenuItem>
                      <MenuItem value="enthusiastic">Enthusiastic</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={currentTemplate.use_emoji}
                        onChange={(e) => setCurrentTemplate({ ...currentTemplate, use_emoji: e.target.checked })}
                      />
                    }
                    label="Use Emoji"
                    sx={{ mt: 2 }}
                  />
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog}>Cancel</Button>
          <Button 
            onClick={handleSaveTemplate}
            variant="contained" 
            color="primary"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : <SaveIcon />}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Preview Dialog */}
      <Dialog
        open={previewDialogOpen}
        onClose={handleClosePreview}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Template Preview
          {currentTemplate && (
            <Chip
              icon={getPlatformConfig(currentTemplate.platform).icon}
              label={currentTemplate.platform.charAt(0).toUpperCase() + currentTemplate.platform.slice(1)}
              size="small"
              sx={{ ml: 2, bgcolor: alpha(getPlatformConfig(currentTemplate.platform).color, 0.1), color: getPlatformConfig(currentTemplate.platform).color }}
            />
          )}
        </DialogTitle>
        <DialogContent>
          {currentTemplate && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {currentTemplate.name}
              </Typography>
              
              <Paper sx={{ p: 2, bgcolor: alpha(ACE_COLORS.WHITE, 0.9), borderRadius: 2, border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {currentTemplate.content}
                </Typography>
              </Paper>
              
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Template Details:
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Tone:</strong> {currentTemplate.tone}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">
                      <strong>Use Emoji:</strong> {currentTemplate.use_emoji ? 'Yes' : 'No'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2">
                      <strong>Character Count:</strong> {currentTemplate.content.length} / {getPlatformConfig(currentTemplate.platform).characterLimit}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreview}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying connection... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
PlatformResponseTemplates.propTypes = {
  // Core props
  onSave: PropTypes.func,
  loading: PropTypes.bool,
  templates: PropTypes.object,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onUpgrade: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

PlatformResponseTemplates.displayName = 'PlatformResponseTemplates';

export default PlatformResponseTemplates;

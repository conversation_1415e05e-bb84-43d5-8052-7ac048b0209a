// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, alpha, useTheme, Button } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import ConsolidatedContentGenerator from '../../components/content/ConsolidatedContentGenerator';
import FeatureGate from '../../components/common/FeatureGate';
import FeatureLimitIndicator from '../../components/common/FeatureLimitIndicator';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../api';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

/**
 * ConsolidatedContentGeneratorPage - Page component for the consolidated content generator
 * Combines features from both AdvancedContentGenerator and EnhancedContentGeneratorPage
 */
const ConsolidatedContentGeneratorPage = () => {
  const theme = useTheme();
  const { hasFeature, getFeatureLimit } = useAuth();
  const [usageData, setUsageData] = useState({
    monthly_posts: 0
  });

  // Fetch usage data when component mounts
  useEffect(() => {
    const fetchUsageData = async () => {
      try {
        const response = await api.get('/api/users/usage');
        if (response.data) {
          setUsageData({
            monthly_posts: response.data.posts_used || 0
          });
        }
      } catch (error) {
        console.error('Error fetching usage data:', error);
      }
    };

    fetchUsageData();
  }, []);

  return (
    <Box sx={{ p: { xs: 1, sm: 2 }, height: 'calc(100vh - 140px)' }}>
      <Paper
        elevation={0}
        sx={{
          p: 2,
          mb: 2,
          bgcolor: alpha(theme.palette.primary.main, 0.05),
          borderRadius: 2,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        }}
      >
        <Typography variant="h5" gutterBottom>
          Content Generator
        </Typography>
        <Typography variant="body2" color="textSecondary">
          Create engaging content with our enhanced generator that adapts to your needs.
          Integrate with campaigns, apply ICP targeting, use branding guidelines, and schedule directly to social media.
          Use keyboard shortcuts (Ctrl+/ to view all shortcuts) for a streamlined experience.
        </Typography>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
          {/* Display usage limit indicator */}
          <FeatureLimitIndicator
            limitName="monthly_posts"
            currentUsage={usageData.monthly_posts}
            showLabel={true}
            showWarning={true}
          />

          {/* Link to competitor-driven content generator */}
          <Button
            component={RouterLink}
            to="/content/competitor-insights"
            variant="outlined"
            color="primary"
            startIcon={<TrendingUpIcon />}
            size="small"
          >
            Try Competitor-Driven Content
          </Button>
        </Box>
      </Paper>

      {/* Wrap content generator with feature gate */}
      <FeatureGate
        feature="text_and_image_content"
        fallbackMessage="Your current plan only supports text-only content. Upgrade to create content with images."
      >
        <ConsolidatedContentGenerator />
      </FeatureGate>
    </Box>
  );
};

export default ConsolidatedContentGeneratorPage;

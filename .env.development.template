# ACE Social Platform - Development Environment Configuration
# Copy this file to .env.development and update the values

# ================================
# APPLICATION SETTINGS
# ================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
PROJECT_NAME=ACE Social (Development)
PROJECT_VERSION=1.0.0

# ================================
# SECURITY SETTINGS (Development)
# ================================
SECRET_KEY=dev-secret-key-change-for-production-use-only
JWT_SECRET_KEY=dev-jwt-secret-key-change-for-production-use-only
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# ================================
# DATABASE CONFIGURATION (Development)
# ================================
# MongoDB
MONGODB_URL=********************************************************************************
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=dev_password_123
MONGO_DATABASE=ace_social_dev

# Redis
REDIS_URL=redis://:dev_redis_123@localhost:6379/0
REDIS_PASSWORD=dev_redis_123

# ================================
# EXTERNAL API KEYS (Development)
# ================================
# OpenAI for AI content generation
OPENAI_API_KEY=your-openai-api-key-here

# SendGrid for email services
SENDGRID_API_KEY=your-sendgrid-api-key-here
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=ACE Social (Dev)

# Lemon Squeezy for payment processing
LEMON_SQUEEZY_API_KEY=your-lemon-squeezy-api-key-here
LEMON_SQUEEZY_STORE_ID=your-store-id-here
LEMON_SQUEEZY_WEBHOOK_SECRET=your-webhook-secret-here

# ================================
# FRONTEND CONFIGURATION (Development)
# ================================
FRONTEND_URL=http://localhost:3000
VITE_API_URL=http://localhost:8000
VITE_ENVIRONMENT=development

# ================================
# CORS SETTINGS (Development)
# ================================
CORS_ORIGINS=["http://localhost:3000","http://localhost:3001","http://localhost:5173","http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET","POST","PUT","DELETE","OPTIONS","PATCH"]
CORS_ALLOW_HEADERS=["*"]

# ================================
# RATE LIMITING (Development - Relaxed)
# ================================
RATE_LIMIT_ENABLED=false
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600

# ================================
# CACHING (Development)
# ================================
CACHE_ENABLED=true
CACHE_BACKEND=redis
CACHE_TTL=300

# ================================
# FILE UPLOAD SETTINGS (Development)
# ================================
MAX_UPLOAD_SIZE=104857600
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=["jpg","jpeg","png","gif","pdf","doc","docx","txt","csv"]

# ================================
# IMAGE METADATA REMOVAL SETTINGS (Development)
# ================================
METADATA_REMOVAL_ENABLED=true
METADATA_REMOVAL_PRESERVE_QUALITY=true
METADATA_REMOVAL_MAX_FILE_SIZE_MB=50
METADATA_REMOVAL_CACHE_TTL=300
METADATA_REMOVAL_PERFORMANCE_TARGET_MS=500
METADATA_REMOVAL_BATCH_SIZE_LIMIT=5
METADATA_REMOVAL_TRACK_USAGE=true

# ================================
# MONITORING & LOGGING (Development)
# ================================
GRAFANA_PASSWORD=dev_admin_123
HEALTH_CHECK_INTERVAL=60
HEALTH_CHECK_TIMEOUT=10

# ================================
# DEVELOPMENT SPECIFIC SETTINGS
# ================================
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
WATCHFILES_FORCE_POLLING=true
CHOKIDAR_USEPOLLING=true

# Hot reload
ENABLE_HOT_RELOAD=true
ENABLE_DEBUG_TOOLBAR=true

# Mock external services for development
MOCK_EXTERNAL_APIS=false
MOCK_PAYMENT_PROCESSING=true
MOCK_EMAIL_SENDING=true

# Development tools
ENABLE_PROFILING=true
ENABLE_QUERY_LOGGING=true
ENABLE_REQUEST_LOGGING=true

# ================================
# SOCIAL MEDIA INTEGRATIONS (Development)
# ================================
# Use sandbox/test credentials for development
TWITTER_API_KEY=your-twitter-dev-api-key
TWITTER_API_SECRET=your-twitter-dev-api-secret
TWITTER_ACCESS_TOKEN=your-twitter-dev-access-token
TWITTER_ACCESS_TOKEN_SECRET=your-twitter-dev-access-token-secret

LINKEDIN_CLIENT_ID=your-linkedin-dev-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-dev-client-secret

FACEBOOK_APP_ID=your-facebook-dev-app-id
FACEBOOK_APP_SECRET=your-facebook-dev-app-secret

INSTAGRAM_CLIENT_ID=your-instagram-dev-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-dev-client-secret

# ================================
# ANALYTICS & TRACKING (Development)
# ================================
# Use development/test tracking IDs
GOOGLE_ANALYTICS_ID=your-dev-google-analytics-id
MIXPANEL_TOKEN=your-dev-mixpanel-token

# ================================
# BACKUP SETTINGS (Development)
# ================================
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 4 * * *
BACKUP_RETENTION_DAYS=7
BACKUP_S3_BUCKET=your-dev-backup-bucket

# ================================
# FEATURE FLAGS (Development)
# ================================
ENABLE_ANALYTICS=true
ENABLE_SOCIAL_LOGIN=true
ENABLE_NOTIFICATIONS=true
ENABLE_AI_CONTENT_GENERATION=true
ENABLE_AI_IMAGE_GENERATION=true
ENABLE_ECOMMERCE=true
ENABLE_PAYMENTS=false

# ================================
# PERFORMANCE SETTINGS (Development)
# ================================
# Relaxed settings for development
MAX_WORKERS=1
WORKER_TIMEOUT=300
KEEP_ALIVE=2

# Database connection pool
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30

# ================================
# SECURITY SETTINGS (Development)
# ================================
# Relaxed security for development
SESSION_TIMEOUT=7200
PASSWORD_MIN_LENGTH=6
ENABLE_2FA=false
FORCE_HTTPS=false

# ================================
# DOCKER SETTINGS (Development)
# ================================
DOCKER_RESTART_POLICY=no
DOCKER_MEMORY_LIMIT=2g
DOCKER_CPU_LIMIT=2
DOCKER_NETWORK=ace-social-dev-network
DOCKER_SUBNET=172.21.0.0/16

# ================================
# DEVELOPMENT NOTES
# ================================
# 1. This configuration is optimized for development
# 2. Security settings are relaxed for easier development
# 3. Rate limiting is disabled or relaxed
# 4. Debug features are enabled
# 5. Mock services can be enabled for offline development
# 6. Use test/sandbox credentials for external services
# 7. Database and cache settings are optimized for local development
# 8. File upload limits are relaxed
# 9. Logging is verbose for debugging
# 10. Hot reload and development tools are enabled

# ================================
# QUICK SETUP CHECKLIST
# ================================
# [ ] Copy this file to .env.development
# [ ] Update API keys with your development credentials
# [ ] Ensure MongoDB and Redis are running locally
# [ ] Update database passwords if needed
# [ ] Configure external service credentials
# [ ] Test the configuration with: npm run verify
# [ ] Start development server: npm run dev

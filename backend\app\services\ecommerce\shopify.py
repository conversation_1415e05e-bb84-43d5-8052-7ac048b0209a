"""
Shopify e-commerce integration service.
"""

import secrets
import hmac
import hashlib
import base64
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from decimal import Decimal
import logging
import aiohttp

from app.core.config import settings
from app.models.ecommerce import (
    EcommerceStore, 
    SyncedProduct, 
    ProductVariant, 
    ProductImage,
    ProductStatusEnum
)
from app.models.common import EcommercePlatformEnum, SyncStatusEnum
from app.models.user import PyObjectId, utc_now
from app.services.ecommerce.base import (
    BaseEcommerceIntegration,
    AuthenticationError,
    SyncError
)

logger = logging.getLogger(__name__)


def _get_shopify_api_key():
    """Get Shopify API key with proper error handling."""
    api_key = getattr(settings, 'SHOPIFY_API_KEY', None)
    if not api_key:
        raise ValueError("SHOPIFY_API_KEY environment variable is required for Shopify integration")
    return api_key


def _get_shopify_api_secret():
    """Get Shopify API secret with proper error handling."""
    api_secret = getattr(settings, 'SHOPIFY_API_SECRET', None)
    if not api_secret:
        raise ValueError("SHOPIFY_API_SECRET environment variable is required for Shopify integration")
    return api_secret


class ShopifyIntegration(BaseEcommerceIntegration):
    """
    Shopify integration service using OAuth 2.0 and Admin API.
    """
    
    platform_name: str = "shopify"
    
    # Shopify API endpoints
    OAUTH_URL = "https://{shop}.myshopify.com/admin/oauth/authorize"
    TOKEN_URL = "https://{shop}.myshopify.com/admin/oauth/access_token"
    API_BASE_URL = "https://{shop}.myshopify.com/admin/api/2023-10"
    
    # Required scopes
    SCOPES = [
        "read_products",
        "read_product_listings",
        "read_inventory",
        "read_orders",
        "read_customers"
    ]
    
    async def get_authorization_url(self, redirect_uri: str, store_url: Optional[str] = None) -> Tuple[str, str]:
        """
        Get the authorization URL for Shopify OAuth flow.
        
        Args:
            redirect_uri: The redirect URI for the OAuth callback
            store_url: Shopify store URL (required for Shopify)
            
        Returns:
            Tuple of (authorization_url, state)
        """
        if not store_url:
            raise ValueError("Store URL is required for Shopify integration")
        
        # Extract shop name from URL
        shop_name = self._extract_shop_name(store_url)
        
        # Generate a random state parameter to prevent CSRF
        state = secrets.token_urlsafe(32)
        
        # Construct the authorization URL
        auth_url = (
            self.OAUTH_URL.format(shop=shop_name) +
            f"?client_id={_get_shopify_api_key()}"
            f"&scope={','.join(self.SCOPES)}"
            f"&redirect_uri={redirect_uri}"
            f"&state={state}"
        )
        
        # Include shop name in state for callback processing
        state_with_shop = f"{state}:{shop_name}"
        
        return auth_url, state_with_shop
    
    async def handle_oauth_callback(
        self, 
        code: str, 
        state: str, 
        redirect_uri: str,
        store_url: Optional[str] = None
    ) -> EcommerceStore:
        """
        Handle the OAuth callback and get access token.
        
        Args:
            code: The authorization code from the callback
            state: The state parameter from the callback
            redirect_uri: The redirect URI used in the authorization request
            store_url: Store URL (can be extracted from state)
            
        Returns:
            EcommerceStore object with tokens and store info
        """
        try:
            # Extract shop name from state
            if ":" in state:
                _, shop_name = state.rsplit(":", 1)
                # Validate redirect_uri and store_url if needed
                logger.debug(f"OAuth callback for shop: {shop_name}, redirect_uri: {redirect_uri}, store_url: {store_url}")
            else:
                raise AuthenticationError("Invalid state parameter")
            
            # Exchange code for access token
            token_url = self.TOKEN_URL.format(shop=shop_name)
            
            async with aiohttp.ClientSession() as session:
                token_data = {
                    "client_id": _get_shopify_api_key(),
                    "client_secret": _get_shopify_api_secret(),
                    "code": code
                }
                
                async with session.post(token_url, json=token_data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise AuthenticationError(f"Token exchange failed: {error_text}")
                    
                    token_response = await response.json()
                    access_token = token_response.get("access_token")
                    
                    if not access_token:
                        raise AuthenticationError("No access token received")
            
            # Get store information
            store_info = await self._get_shop_info(shop_name, access_token)
            
            # Create store object
            store = EcommerceStore(
                user_id=PyObjectId(),  # Will be set by the calling service
                platform=EcommercePlatformEnum.SHOPIFY,
                store_name=store_info.get("name", shop_name),
                store_url=f"https://{shop_name}.myshopify.com",
                store_id=str(store_info.get("id", shop_name)),
                access_token=access_token,  # Will be encrypted by credential manager
                refresh_token=None,  # Shopify doesn't use refresh tokens
                api_key=_get_shopify_api_key(),
                api_secret=_get_shopify_api_secret(),
                webhook_secret=None,  # Will be set when webhooks are configured
                status=SyncStatusEnum.CONNECTED,
                last_sync_at=None,
                last_error=None,
                webhook_url=None,  # Will be set when webhooks are configured
                created_at=utc_now(),
                updated_at=utc_now(),
                connected_at=utc_now(),
                metadata={
                    "shop_name": shop_name,
                    "domain": store_info.get("domain"),
                    "email": store_info.get("email"),
                    "currency": store_info.get("currency"),
                    "timezone": store_info.get("iana_timezone"),
                    "plan_name": store_info.get("plan_name")
                }
            )
            
            return store
            
        except Exception as e:
            logger.error(f"Shopify OAuth callback error: {str(e)}")
            raise AuthenticationError(f"OAuth callback failed: {str(e)}")
    
    async def refresh_access_token(self, store: EcommerceStore) -> EcommerceStore:
        """
        Refresh the access token for a store.
        Note: Shopify access tokens don't expire, so this is a no-op.
        
        Args:
            store: The store with token
            
        Returns:
            Same store (Shopify tokens don't expire)
        """
        # Shopify access tokens don't expire
        return store
    
    async def test_connection(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Test the connection to the Shopify store.
        
        Args:
            store: The store to test
            
        Returns:
            Connection test results
        """
        try:
            shop_name = store.metadata.get("shop_name") if store.metadata else None
            if not shop_name:
                shop_name = self._extract_shop_name(store.store_url)
            
            shop_info = await self._get_shop_info(shop_name, store.access_token)
            
            return {
                "success": True,
                "store_name": shop_info.get("name"),
                "domain": shop_info.get("domain"),
                "plan": shop_info.get("plan_name"),
                "currency": shop_info.get("currency"),
                "timezone": shop_info.get("iana_timezone")
            }
            
        except Exception as e:
            logger.error(f"Shopify connection test failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_store_info(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Get basic store information.
        
        Args:
            store: The store to get info for
            
        Returns:
            Store information
        """
        shop_name = store.metadata.get("shop_name") if store.metadata else None
        if not shop_name:
            shop_name = self._extract_shop_name(store.store_url)
        
        return await self._get_shop_info(shop_name, store.access_token)
    
    async def sync_products(
        self, 
        store: EcommerceStore, 
        limit: int = 250,
        since_id: Optional[str] = None
    ) -> List[SyncedProduct]:
        """
        Sync products from the Shopify store.
        
        Args:
            store: The store to sync from
            limit: Maximum number of products to sync (max 250 for Shopify)
            since_id: Sync products created after this ID
            
        Returns:
            List of synced products
        """
        try:
            shop_name = store.metadata.get("shop_name") if store.metadata else None
            if not shop_name:
                shop_name = self._extract_shop_name(store.store_url)
            
            # Build API URL
            api_url = f"{self.API_BASE_URL.format(shop=shop_name)}/products.json"
            params = {"limit": min(limit, 250)}  # Shopify max is 250
            
            if since_id:
                try:
                    params["since_id"] = int(since_id)
                except (ValueError, TypeError):
                    # Skip invalid since_id
                    pass
            
            # Make authenticated request
            async with aiohttp.ClientSession() as session:
                headers = {"X-Shopify-Access-Token": store.access_token}
                
                async with session.get(api_url, params=params, headers=headers) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise SyncError(f"Failed to fetch products: {error_text}")
                    
                    data = await response.json()
                    products_data = data.get("products", [])
            
            # Convert to SyncedProduct objects
            synced_products = []
            for product_data in products_data:
                if isinstance(product_data, dict):
                    synced_product = self._convert_shopify_product(product_data, store)
                    synced_products.append(synced_product)
            
            return synced_products
            
        except Exception as e:
            logger.error(f"Shopify product sync error: {str(e)}")
            raise SyncError(f"Product sync failed: {str(e)}")
    
    async def get_product(self, store: EcommerceStore, product_id: str) -> Optional[SyncedProduct]:
        """
        Get a specific product from the Shopify store.
        
        Args:
            store: The store to get product from
            product_id: Shopify product ID
            
        Returns:
            Product if found, None otherwise
        """
        try:
            shop_name = store.metadata.get("shop_name") if store.metadata else None
            if not shop_name:
                shop_name = self._extract_shop_name(store.store_url)
            
            api_url = f"{self.API_BASE_URL.format(shop=shop_name)}/products/{product_id}.json"
            
            async with aiohttp.ClientSession() as session:
                headers = {"X-Shopify-Access-Token": store.access_token}
                
                async with session.get(api_url, headers=headers) as response:
                    if response.status == 404:
                        return None
                    elif response.status != 200:
                        error_text = await response.text()
                        raise SyncError(f"Failed to fetch product: {error_text}")
                    
                    data = await response.json()
                    product_data = data.get("product")

                    if product_data and isinstance(product_data, dict):
                        return self._convert_shopify_product(product_data, store)
                    
                    return None
                    
        except Exception as e:
            logger.error(f"Shopify get product error: {str(e)}")
            return None

    async def setup_webhooks(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Set up webhooks for real-time sync.

        Args:
            store: The store to setup webhooks for

        Returns:
            Webhook setup results
        """
        try:
            shop_name = store.metadata.get("shop_name") if store.metadata else None
            if not shop_name:
                shop_name = self._extract_shop_name(store.store_url)

            # Use frontend URL as base, but replace with backend URL for webhooks
            base_url = settings.FRONTEND_URL.replace(":3000", ":8000").replace("localhost", "localhost")
            webhook_url = f"{base_url}/api/ecommerce/webhooks/shopify"

            # Webhook topics to subscribe to
            webhook_topics = [
                "products/create",
                "products/update",
                "products/delete"
            ]

            created_webhooks = []

            async with aiohttp.ClientSession() as session:
                headers = {"X-Shopify-Access-Token": store.access_token}

                for topic in webhook_topics:
                    webhook_data = {
                        "webhook": {
                            "topic": topic,
                            "address": webhook_url,
                            "format": "json"
                        }
                    }

                    api_url = f"{self.API_BASE_URL.format(shop=shop_name)}/webhooks.json"

                    async with session.post(api_url, json=webhook_data, headers=headers) as response:
                        if response.status == 201:
                            webhook_response = await response.json()
                            created_webhooks.append(webhook_response.get("webhook"))
                        else:
                            error_text = await response.text()
                            logger.warning(f"Failed to create webhook for {topic}: {error_text}")

            return {
                "success": len(created_webhooks) > 0,
                "webhooks_created": len(created_webhooks),
                "webhooks": created_webhooks
            }

        except Exception as e:
            logger.error(f"Shopify webhook setup error: {str(e)}")
            return {"success": False, "error": str(e)}

    async def verify_webhook(self, payload: bytes, signature: str, secret: str) -> bool:
        """
        Verify Shopify webhook signature.

        Args:
            payload: Raw webhook payload
            signature: Webhook signature from X-Shopify-Hmac-Sha256 header
            secret: Webhook secret

        Returns:
            True if signature is valid
        """
        try:
            # Shopify uses HMAC-SHA256
            expected_signature = base64.b64encode(
                hmac.new(secret.encode(), payload, hashlib.sha256).digest()
            ).decode()

            return hmac.compare_digest(signature, expected_signature)

        except Exception as e:
            logger.error(f"Shopify webhook verification error: {str(e)}")
            return False

    def _extract_shop_name(self, store_url: str) -> str:
        """Extract shop name from Shopify URL."""
        # Handle various URL formats
        if ".myshopify.com" in store_url:
            return store_url.split(".myshopify.com")[0].split("//")[-1]
        else:
            # Assume it's just the shop name
            return store_url.replace("https://", "").replace("http://", "").split(".")[0]

    async def _get_shop_info(self, shop_name: str, access_token: str) -> Dict[str, Any]:
        """Get shop information from Shopify API."""
        api_url = f"{self.API_BASE_URL.format(shop=shop_name)}/shop.json"

        async with aiohttp.ClientSession() as session:
            headers = {"X-Shopify-Access-Token": access_token}

            async with session.get(api_url, headers=headers) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise AuthenticationError(f"Failed to get shop info: {error_text}")

                data = await response.json()
                shop_data = data.get("shop", {})
                return shop_data if isinstance(shop_data, dict) else {}

    def _convert_shopify_product(self, product_data: Dict[str, Any], store: EcommerceStore) -> SyncedProduct:
        """Convert Shopify product data to SyncedProduct."""
        # Extract basic product info
        product_id = str(product_data.get("id"))
        title = product_data.get("title", "")
        description = product_data.get("body_html", "")
        vendor = product_data.get("vendor", "")
        product_type = product_data.get("product_type", "")
        handle = product_data.get("handle", "")
        tags = product_data.get("tags", "").split(",") if product_data.get("tags") else []

        # Get variants
        variants_data = product_data.get("variants", [])
        variants = []
        main_price = Decimal("0")
        main_sku = ""
        total_inventory = 0

        for variant_data in variants_data:
            variant = ProductVariant(
                variant_id=str(variant_data.get("id")),
                title=variant_data.get("title", ""),
                price=Decimal(str(variant_data.get("price", "0"))),
                compare_at_price=Decimal(str(variant_data.get("compare_at_price", "0"))) if variant_data.get("compare_at_price") else None,
                sku=variant_data.get("sku", ""),
                inventory_quantity=variant_data.get("inventory_quantity", 0),
                weight=variant_data.get("weight"),
                option1=variant_data.get("option1"),
                option2=variant_data.get("option2"),
                option3=variant_data.get("option3"),
                image_url=None,  # Shopify variants don't have individual images
                available=variant_data.get("available", True)
            )
            variants.append(variant)

            # Use first variant for main product price
            if not main_price and variant.price:
                main_price = variant.price
                main_sku = variant.sku or ""

            total_inventory += variant.inventory_quantity

        # Get images
        images_data = product_data.get("images", [])
        images = []
        featured_image = None

        for img_data in images_data:
            image = ProductImage(
                image_id=str(img_data.get("id")),
                url=img_data.get("src", ""),
                alt_text=img_data.get("alt"),
                width=img_data.get("width"),
                height=img_data.get("height"),
                position=img_data.get("position", 1)
            )
            images.append(image)

            if not featured_image:
                featured_image = image.url

        # Determine status
        status = ProductStatusEnum.ACTIVE
        if product_data.get("status") == "draft":
            status = ProductStatusEnum.DRAFT
        elif product_data.get("status") == "archived":
            status = ProductStatusEnum.ARCHIVED

        return SyncedProduct(
            user_id=store.user_id,
            store_id=store.id,
            external_product_id=product_id,
            platform=EcommercePlatformEnum.SHOPIFY,
            title=title,
            description=description,
            vendor=vendor,
            product_type=product_type,
            price=main_price,
            compare_at_price=None,  # Will be set from variants if available
            sku=main_sku,
            inventory_quantity=total_inventory,
            tags=[tag.strip() for tag in tags if tag.strip()],
            category=product_type,  # Use product_type as category
            images=images,
            featured_image=featured_image,
            variants=variants,
            has_variants=len(variants) > 1,
            status=status,
            published=product_data.get("status") == "active",
            seo_title=None,  # Shopify doesn't provide SEO title in basic API
            seo_description=None,  # Shopify doesn't provide SEO description in basic API
            handle=handle,
            last_synced_at=utc_now(),
            external_updated_at=datetime.fromisoformat(product_data.get("updated_at", "").replace("Z", "+00:00")) if product_data.get("updated_at") else None,
            created_at=utc_now(),
            updated_at=utc_now(),
            last_content_generation=None,
            metadata={
                "shopify_id": product_id,
                "created_at": product_data.get("created_at"),
                "updated_at": product_data.get("updated_at"),
                "published_at": product_data.get("published_at"),
                "template_suffix": product_data.get("template_suffix"),
                "published_scope": product_data.get("published_scope")
            }
        )

// @since 2024-1-1 to 2025-25-7
/**
 * Jest configuration for sentiment analysis tests
 * Optimized for comprehensive testing with high coverage requirements
 */

module.exports = {
  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/src/tests/sentiment/setup.js',
    '<rootDir>/src/tests/sentiment/customMatchers.js'
  ],
  
  // Test patterns
  testMatch: [
    '<rootDir>/src/tests/sentiment/**/*.test.{js,jsx}',
    '<rootDir>/src/components/sentiment/**/*.test.{js,jsx}',
    '<rootDir>/src/mocks/**/*.test.{js,jsx}'
  ],
  
  // Module name mapping for imports
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@mocks/(.*)$': '<rootDir>/src/mocks/$1',
    '^@tests/(.*)$': '<rootDir>/src/tests/$1',
    '^@contexts/(.*)$': '<rootDir>/src/contexts/$1',
    '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@api/(.*)$': '<rootDir>/src/api/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1'
  },
  
  // Transform configuration
  transform: {
    '^.+\\.(js|jsx)$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        ['@babel/preset-react', { runtime: 'automatic' }]
      ],
      plugins: [
        '@babel/plugin-transform-runtime'
      ]
    }]
  },
  
  // Module file extensions
  moduleFileExtensions: ['js', 'jsx', 'json'],
  
  // Coverage configuration
  collectCoverage: true,
  collectCoverageFrom: [
    'src/components/sentiment/**/*.{js,jsx}',
    'src/mocks/sentimentData.js',
    '!src/components/sentiment/**/*.stories.{js,jsx}',
    '!src/components/sentiment/**/index.{js,jsx}',
    '!**/node_modules/**',
    '!**/vendor/**'
  ],
  
  // Coverage thresholds (95%+ requirement)
  coverageThreshold: {
    global: {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './src/components/sentiment/': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './src/mocks/sentimentData.js': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95
    }
  },
  
  // Coverage reporters
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json-summary'
  ],
  
  // Coverage directory
  coverageDirectory: '<rootDir>/coverage/sentiment',
  
  // Test timeout (for async operations)
  testTimeout: 10000,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Verbose output for detailed test results
  verbose: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Mock configuration
  modulePathIgnorePatterns: [
    '<rootDir>/build/',
    '<rootDir>/dist/'
  ],
  
  // Transform ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!(recharts|@mui|@emotion|@babel/runtime)/)'
  ],
  
  // Global setup for performance monitoring
  globals: {
    'performance': {
      'now': jest.fn(() => Date.now())
    }
  },
  
  // Test result processor for custom reporting
  testResultsProcessor: '<rootDir>/src/tests/sentiment/testResultsProcessor.js',
  
  // Custom reporters for CI/CD integration
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: '<rootDir>/test-results/sentiment',
      outputName: 'sentiment-test-results.xml',
      suiteName: 'Sentiment Analysis Tests',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}',
      ancestorSeparator: ' › ',
      usePathForSuiteName: true
    }],
    ['jest-html-reporters', {
      publicPath: '<rootDir>/test-results/sentiment/html',
      filename: 'sentiment-test-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'Sentiment Analysis Test Report'
    }]
  ],
  
  // Performance monitoring
  maxWorkers: '50%',
  
  // Cache configuration
  cacheDirectory: '<rootDir>/.jest-cache/sentiment',
  
  // Watch plugins for development
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ],
  
  // Snapshot configuration
  snapshotSerializers: [
    '@emotion/jest/serializer'
  ],
  
  // Custom matchers (already configured above)
};

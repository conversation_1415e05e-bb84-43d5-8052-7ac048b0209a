import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import CampaignCreator from '../CampaignCreator';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the API
vi.mock('../../../api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock the CampaignUnifiedBranding component
vi.mock('../CampaignUnifiedBranding', () => ({
  default: ({ campaignBranding, onChange }) => (
    <div data-testid="campaign-unified-branding">
      <span>Campaign Unified Branding Component</span>
      <button onClick={() => onChange({ colorSystem: { primary: '#FF0000' } })}>
        Change Branding
      </button>
    </div>
  ),
}));

// Mock react-router-dom hooks
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => ({ icpId: 'test-icp-id' }),
  };
});

// Test wrapper with theme and providers
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          {children}
        </LocalizationProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('CampaignCreator', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders campaign creator correctly', () => {
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    expect(screen.getByText('Create New Campaign')).toBeInTheDocument();
    expect(screen.getByText('Campaign Details')).toBeInTheDocument();
  });

  test('displays stepper with correct steps', () => {
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    expect(screen.getByText('Campaign Details')).toBeInTheDocument();
    expect(screen.getByText('Schedule & Platforms')).toBeInTheDocument();
    expect(screen.getByText('Branding Settings')).toBeInTheDocument();
    expect(screen.getByText('Content Generation')).toBeInTheDocument();
  });

  test('shows campaign details form in first step', () => {
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Campaign Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Description')).toBeInTheDocument();
    expect(screen.getByText('Next')).toBeInTheDocument();
  });

  test('handles campaign name input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    const nameInput = screen.getByLabelText('Campaign Name');
    await user.type(nameInput, 'Test Campaign');

    expect(nameInput).toHaveValue('Test Campaign');
  });

  test('handles description input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    const descriptionInput = screen.getByLabelText('Description');
    await user.type(descriptionInput, 'This is a test campaign description');

    expect(descriptionInput).toHaveValue('This is a test campaign description');
  });

  test('validates required fields before proceeding to next step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    // Should show validation errors for empty required fields
    expect(screen.getByText('Campaign name is required')).toBeInTheDocument();
    expect(screen.getByText('Description is required')).toBeInTheDocument();
  });

  test('proceeds to next step when form is valid', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Fill in required fields
    const nameInput = screen.getByLabelText('Campaign Name');
    const descriptionInput = screen.getByLabelText('Description');
    
    await user.type(nameInput, 'Test Campaign');
    await user.type(descriptionInput, 'This is a test campaign description');

    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    // Should proceed to schedule & platforms step
    expect(screen.getByText('Schedule & Platforms')).toBeInTheDocument();
    expect(screen.getByLabelText('Start Date')).toBeInTheDocument();
    expect(screen.getByLabelText('End Date')).toBeInTheDocument();
  });

  test('shows platform selection in second step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to second step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));

    expect(screen.getByText('Select Platforms')).toBeInTheDocument();
    expect(screen.getByText('LinkedIn')).toBeInTheDocument();
    expect(screen.getByText('Twitter')).toBeInTheDocument();
    expect(screen.getByText('Facebook')).toBeInTheDocument();
    expect(screen.getByText('Instagram')).toBeInTheDocument();
  });

  test('handles platform selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to second step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));

    // Select LinkedIn platform
    const linkedinChip = screen.getByText('LinkedIn');
    await user.click(linkedinChip);

    // LinkedIn should be selected (this would be visually indicated in the actual component)
    expect(linkedinChip).toBeInTheDocument();
  });

  test('shows goal configuration in second step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to second step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));

    expect(screen.getByText('Campaign Goals')).toBeInTheDocument();
    expect(screen.getByText('Add Goal')).toBeInTheDocument();
  });

  test('handles adding campaign goals', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to second step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));

    // Add a goal
    const addGoalButton = screen.getByText('Add Goal');
    await user.click(addGoalButton);

    // Should show goal form
    expect(screen.getByLabelText('Goal Type')).toBeInTheDocument();
    expect(screen.getByLabelText('Target Value')).toBeInTheDocument();
  });

  test('shows branding settings in third step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to third step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));
    
    // Skip platform/schedule validation for test
    await user.click(screen.getByText('Next'));

    expect(screen.getByTestId('campaign-unified-branding')).toBeInTheDocument();
    expect(screen.getByText('Campaign Unified Branding Component')).toBeInTheDocument();
  });

  test('handles branding changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to third step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));

    // Change branding
    const changeBrandingButton = screen.getByText('Change Branding');
    await user.click(changeBrandingButton);

    // Should handle branding change (tested via component implementation)
    expect(changeBrandingButton).toBeInTheDocument();
  });

  test('shows content generation in fourth step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to fourth step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));

    expect(screen.getByText('Content Generation')).toBeInTheDocument();
    expect(screen.getByLabelText('Number of Posts')).toBeInTheDocument();
    expect(screen.getByText('Create Campaign')).toBeInTheDocument();
  });

  test('handles post count input', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to fourth step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));

    const postCountInput = screen.getByLabelText('Number of Posts');
    await user.clear(postCountInput);
    await user.type(postCountInput, '10');

    expect(postCountInput).toHaveValue(10);
  });

  test('handles back navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate forward
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));

    // Navigate back
    const backButton = screen.getByText('Back');
    await user.click(backButton);

    // Should be back to first step
    expect(screen.getByText('Campaign Details')).toBeInTheDocument();
    expect(screen.getByLabelText('Campaign Name')).toHaveValue('Test Campaign');
  });

  test('shows loading state during campaign creation', async () => {
    const user = userEvent.setup();
    
    // Mock API to simulate loading
    const api = await import('../../../api');
    api.default.post.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Navigate to final step and create campaign
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));
    
    const createButton = screen.getByText('Create Campaign');
    await user.click(createButton);

    expect(screen.getByText('Creating Campaign...')).toBeInTheDocument();
  });

  test('validates form fields with proper error messages', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Try to proceed without filling required fields
    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    expect(screen.getByText('Campaign name is required')).toBeInTheDocument();
    expect(screen.getByText('Description is required')).toBeInTheDocument();
  });

  test('handles campaign creation success', async () => {
    const user = userEvent.setup();
    
    // Mock successful API response
    const api = await import('../../../api');
    api.default.post.mockResolvedValue({
      data: { id: 'campaign-123', name: 'Test Campaign' }
    });
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Fill form and create campaign
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));
    
    const createButton = screen.getByText('Create Campaign');
    await user.click(createButton);

    await waitFor(() => {
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Campaign created successfully!');
      expect(mockNavigate).toHaveBeenCalledWith('/campaigns');
    });
  });

  test('handles campaign creation error', async () => {
    const user = userEvent.setup();
    
    // Mock API error
    const api = await import('../../../api');
    api.default.post.mockRejectedValue(new Error('Creation failed'));
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Fill form and create campaign
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));
    await user.click(screen.getByText('Next'));
    
    const createButton = screen.getByText('Create Campaign');
    await user.click(createButton);

    await waitFor(() => {
      expect(mockShowErrorNotification).toHaveBeenCalledWith('Failed to create campaign. Please try again.');
    });
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Check for proper form controls and labels
    expect(screen.getByRole('textbox', { name: /campaign name/i })).toBeInTheDocument();
    expect(screen.getByRole('textbox', { name: /description/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /next/i })).toBeInTheDocument();
  });

  test('preserves form data when navigating between steps', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignCreator />
      </TestWrapper>
    );

    // Fill first step
    await user.type(screen.getByLabelText('Campaign Name'), 'Test Campaign');
    await user.type(screen.getByLabelText('Description'), 'Test description');
    await user.click(screen.getByText('Next'));

    // Go back and check data is preserved
    await user.click(screen.getByText('Back'));
    
    expect(screen.getByLabelText('Campaign Name')).toHaveValue('Test Campaign');
    expect(screen.getByLabelText('Description')).toHaveValue('Test description');
  });
});

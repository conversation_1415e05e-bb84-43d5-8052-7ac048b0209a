/**
 * Enhanced Regeneration Dashboard - Enterprise-grade regeneration dashboard management component
 * Features: Comprehensive regeneration dashboard management, real-time regeneration monitoring, plan-based limitations,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced regeneration capabilities and ACEO add-on marketplace integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  CardContent,
  Grid,
  LinearProgress,
  Chip,
  Button,
  Alert,
  CircularProgress,
  alpha,
  Snackbar,
  useMediaQuery
} from '@mui/material';
import {
  AutoFixHigh as RegenerateIcon,
  TrendingUp as TrendingUpIcon,
  Speed as SpeedIcon,
  Psychology as PsychologyIcon,
  Settings as SettingsIcon,
  ShoppingCart as ShoppingCartIcon
} from '@mui/icons-material';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';

import api from '../../api';
import { useNotification } from '../../hooks/useNotification';
import GlassmorphicCard from '../ui/GlassmorphicCard';
import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import { useAddons } from '../../hooks/useAddons';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Dashboard display modes with enhanced configurations
const DASHBOARD_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Dashboard',
    description: 'Basic dashboard interface',
    features: ['basic_dashboard', 'usage_analytics', 'regeneration_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Analytics Dashboard',
    description: 'Comprehensive dashboard management',
    features: ['detailed_dashboard', 'regeneration_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Dashboard',
    description: 'AI-powered dashboard optimization and suggestions',
    features: ['ai_assisted', 'ai_optimization', 'regeneration_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Dashboard',
    description: 'Advanced regeneration analytics and forecasting',
    features: ['analytics_dashboard', 'regeneration_insights']
  }
};

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend
);

/**
 * Enhanced Regeneration Dashboard Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onDashboardAction] - Dashboard action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-regeneration-dashboard'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const RegenerationDashboard = memo(forwardRef(({
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onDashboardAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-regeneration-dashboard',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const { showErrorNotification } = useNotification();
  const { getRelevantAddons } = useAddons();
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const dashboardRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [usageData, setUsageData] = useState(null);
  const [statsData, setStatsData] = useState(null);

  // Enhanced state management
  const [dashboardMode, setDashboardMode] = useState('compact');
  const [dashboardHistory, setDashboardHistory] = useState([]);
  const [dashboardAnalytics, setDashboardAnalytics] = useState(null);
  const [dashboardInsights, setDashboardInsights] = useState(null);
  const [customDashboardConfigs, setCustomDashboardConfigs] = useState([]);
  const [dashboardPreferences, setDashboardPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    dashboardSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [dashboardDrawerOpen, setDashboardDrawerOpen] = useState(false);
  const [selectedDashboardType, setSelectedDashboardType] = useState(null);
  const [dashboardStats, setDashboardStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [dashboardScore, setDashboardScore] = useState(0);
  const [dashboardProgress, setDashboardProgress] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [dashboardVersions, setDashboardVersions] = useState([]);
  const [dailyUsage, setDailyUsage] = useState(0);
  const [monthlyUsage, setMonthlyUsage] = useState(0);
  const [dashboardForecast, setDashboardForecast] = useState(null);

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastDashboardCheck, setLastDashboardCheck] = useState(Date.now());

  /**
   * ACE Social subscription integration with plan-based regeneration limitations - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // ACE Social platform plans with realistic regeneration limitations
    const features = {
      creator: {
        maxRegenerationsPerMonth: 50,
        maxDailyRegenerations: 5,
        regenerationCostMultiplier: 1.0,
        hasAdvancedDashboard: false,
        hasDashboardAnalytics: false,
        hasCustomDashboardConfigs: false,
        hasDashboardInsights: false,
        hasDashboardHistory: true,
        hasAIAssistance: false,
        hasDashboardExport: false,
        hasDashboardScheduling: false,
        hasDashboardAutomation: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1,
        allowedDashboardTypes: ['basic_dashboard'],
        maxHistoryDays: 7,
        canPurchaseAddons: true,
        addonDiscountPercent: 0
      },
      accelerator: {
        maxRegenerationsPerMonth: 200,
        maxDailyRegenerations: 20,
        regenerationCostMultiplier: 0.8,
        hasAdvancedDashboard: true,
        hasDashboardAnalytics: true,
        hasCustomDashboardConfigs: false,
        hasDashboardInsights: true,
        hasDashboardHistory: true,
        hasAIAssistance: true,
        hasDashboardExport: true,
        hasDashboardScheduling: false,
        hasDashboardAutomation: false,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 2000,
        planName: 'Accelerator',
        planTier: 2,
        allowedDashboardTypes: ['basic_dashboard', 'advanced_dashboard', 'analytics_dashboard'],
        maxHistoryDays: 30,
        canPurchaseAddons: true,
        addonDiscountPercent: 10
      },
      dominator: {
        maxRegenerationsPerMonth: -1, // Unlimited
        maxDailyRegenerations: -1, // Unlimited
        regenerationCostMultiplier: 0.5,
        hasAdvancedDashboard: true,
        hasDashboardAnalytics: true,
        hasCustomDashboardConfigs: true,
        hasDashboardInsights: true,
        hasDashboardHistory: true,
        hasAIAssistance: true,
        hasDashboardExport: true,
        hasDashboardScheduling: true,
        hasDashboardAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3,
        allowedDashboardTypes: ['basic_dashboard', 'advanced_dashboard', 'analytics_dashboard', 'ai_dashboard', 'custom_dashboard'],
        maxHistoryDays: -1, // Unlimited
        canPurchaseAddons: true,
        addonDiscountPercent: 20
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxRegenerationsPerMonth === -1 || currentUsage < currentFeatures.maxRegenerationsPerMonth;
        return hasAccess && withinLimits;
      },
      getRegenerationCost: (baseCredits) => {
        return Math.ceil(baseCredits * currentFeatures.regenerationCostMultiplier);
      },
      canUseDashboardType: (type) => {
        return currentFeatures.allowedDashboardTypes.includes(type);
      },
      getAddonDiscount: (originalPrice) => {
        return originalPrice * (1 - currentFeatures.addonDiscountPercent / 100);
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'main',
      'aria-label': ariaLabel || `Regeneration dashboard with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Dashboard interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: -1
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive dashboard API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getDashboardHistory: () => dashboardHistory,
    getDashboardAnalytics: () => dashboardAnalytics,
    getDashboardInsights: () => dashboardInsights,
    refreshDashboard: () => {
      fetchDashboardAnalytics();
      fetchRegenerationData();
      if (onRefresh) onRefresh();
    },

    // Dashboard methods
    focusDashboard: () => {
      if (dashboardRef.current) {
        dashboardRef.current.focus();
      }
    },
    getDashboardScore: () => dashboardScore,
    getDashboardProgress: () => dashboardProgress,
    getDailyUsage: () => dailyUsage,
    getMonthlyUsage: () => monthlyUsage,
    getDashboardForecast: () => dashboardForecast,
    openDashboardDrawer: () => setDashboardDrawerOpen(true),
    closeDashboardDrawer: () => setDashboardDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportDashboardData: () => {
      if (onExport) {
        onExport(dashboardHistory, dashboardAnalytics);
      }
    },

    // Accessibility methods
    announceDashboard: (message) => announceToScreenReader(message),
    focusDashboardField: () => setFocusToElement('dashboard-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => dashboardMode,
    getDashboardStats: () => dashboardStats,
    getSelectedDashboardType: () => selectedDashboardType,
    getCustomDashboardConfigs: () => customDashboardConfigs,
    getDashboardDrawerOpen: () => dashboardDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    getActiveTab: () => activeTab,
    setActiveTab: (tab) => setActiveTab(tab),
    addCustomDashboardConfig,
    handleDashboardModeChange,
    updateDashboardPreferences,
    handleDashboardTypeSelection,
    validateDashboardConfig,
    getDashboardVersions: () => dashboardVersions,
    switchToVersion: (versionId) => switchToDashboardVersion(versionId)
  }), [
    dashboardHistory,
    dashboardAnalytics,
    dashboardInsights,
    dashboardStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    dashboardMode,
    selectedDashboardType,
    customDashboardConfigs,
    dashboardScore,
    dashboardProgress,
    dailyUsage,
    monthlyUsage,
    dashboardForecast,
    dashboardVersions,
    addCustomDashboardConfig,
    handleDashboardModeChange,
    updateDashboardPreferences,
    handleDashboardTypeSelection,
    validateDashboardConfig,
    switchToDashboardVersion,
    activeTab,
    fullscreenMode,
    dashboardDrawerOpen,
    showAnalytics,
    fetchDashboardAnalytics,
    fetchRegenerationData
  ]);

  // Fetch dashboard analytics with enhanced error handling and retry logic
  const fetchDashboardAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/dashboard/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setDashboardAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (dashboardPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Dashboard analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch dashboard analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load dashboard analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, dashboardPreferences.showAnalytics]);

  useEffect(() => {
    fetchRegenerationData();
    fetchDashboardAnalytics();
    fetchDashboardInsights();
  }, [fetchDashboardAnalytics, fetchDashboardInsights, fetchRegenerationData]);

  const fetchRegenerationData = useCallback(async () => {
    setLoading(true);
    try {
      const [usageResponse, statsResponse] = await Promise.all([
        api.get('/api/regeneration/usage'),
        api.get('/api/regeneration/stats')
      ]);

      setUsageData(usageResponse.data);
      setStatsData(statsResponse.data);

      // Update usage tracking
      if (usageResponse.data) {
        setDailyUsage(usageResponse.data.daily_usage || 0);
        setMonthlyUsage(usageResponse.data.current_month_usage?.total_regenerations || 0);
        setDashboardScore(usageResponse.data.efficiency_metrics?.efficiency_score || 0);
        setDashboardProgress(usageResponse.data.usage_percentage || 0);
        setDashboardForecast(usageResponse.data.usage_forecast);

        // Create version tracking
        const newVersion = {
          id: Date.now(),
          name: `Dashboard State ${Date.now()}`,
          score: usageResponse.data.efficiency_metrics?.efficiency_score || 0,
          timestamp: new Date().toISOString()
        };
        setDashboardVersions(prev => [newVersion, ...prev.slice(0, 9)]); // Keep last 10 versions
      }

      // Track dashboard load
      const loadRecord = {
        id: Date.now(),
        type: 'dashboard_loaded',
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id,
        planTier: subscriptionFeatures.planTier
      };

      setDashboardHistory(prev => [loadRecord, ...prev.slice(0, 99)]);

    } catch (error) {
      console.error('Error fetching regeneration data:', error);
      showErrorNotification('Failed to load regeneration data');
    } finally {
      setLoading(false);
    }
  }, [showErrorNotification, subscription?.user_id, subscriptionFeatures.planTier]);

  // Fetch dashboard insights
  const fetchDashboardInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/dashboard/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setDashboardInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch dashboard insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Handle dashboard mode switching
  const handleDashboardModeChange = useCallback((newMode) => {
    if (DASHBOARD_MODES[newMode.toUpperCase()]) {
      setDashboardMode(newMode);
      announceToScreenReader(`Dashboard mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setDashboardHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (dashboardPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} dashboard mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, dashboardPreferences.showAnalytics, showSuccess]);

  // Handle custom dashboard config management
  const addCustomDashboardConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomDashboardConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setDashboardHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (dashboardPreferences.showAnalytics) {
      showSuccess(`Custom dashboard config "${configData.name}" created`);
    }
  }, [subscription?.user_id, dashboardPreferences.showAnalytics, showSuccess]);

  // Handle dashboard preferences updates
  const updateDashboardPreferences = useCallback((newPreferences) => {
    setDashboardPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setDashboardHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (dashboardPreferences.showAnalytics) {
      showSuccess('Dashboard preferences updated');
    }
  }, [subscription?.user_id, dashboardPreferences.showAnalytics, showSuccess]);

  // Handle dashboard type selection
  const handleDashboardTypeSelection = useCallback((dashboardType) => {
    setSelectedDashboardType(dashboardType);

    // Track dashboard type selection
    const typeRecord = {
      id: Date.now(),
      type: 'dashboard_type_selected',
      dashboardType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setDashboardHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (dashboardPreferences.showAnalytics) {
      announceToScreenReader(`Selected dashboard type: ${dashboardType}`);
    }
  }, [subscription?.user_id, dashboardPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateDashboardConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Dashboard type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Switch to dashboard version
  const switchToDashboardVersion = useCallback((versionId) => {
    const version = dashboardVersions.find(v => v.id === versionId);
    if (version) {
      setDashboardScore(version.score || 0);

      if (dashboardPreferences.showAnalytics) {
        showSuccess(`Switched to version ${version.name}`);
      }
    }
  }, [dashboardVersions, dashboardPreferences.showAnalytics, showSuccess]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && usageData) {
      // Optimize dashboard management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchDashboardAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, usageData, fetchDashboardAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastDashboardCheck(Date.now());

          if (wasUnavailable && dashboardPreferences.showAnalytics) {
            showSuccess("Connection restored - Dashboard features available");
          }
        } else {
          setBackendAvailable(false);
          if (dashboardPreferences.showAnalytics) {
            showError("Backend service unavailable - Some dashboard features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastDashboardCheck;
          if (timeSinceLastCheck > 60000 && dashboardPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Dashboard may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastDashboardCheck, dashboardPreferences.showAnalytics, showSuccess, showError]);

  // Generate AI suggestions when dashboard changes
  useEffect(() => {
    if (enableAIInsights && dashboardPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, dashboardPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/dashboard/ai-suggestions', {
        params: {
          currentUsage: monthlyUsage,
          planTier: subscriptionFeatures.planTier,
          efficiencyScore: dashboardScore
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (dashboardPreferences.showAnalytics) {
        showSuccess('AI dashboard suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [monthlyUsage, subscriptionFeatures.planTier, dashboardScore, dashboardPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when dashboard changes
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchDashboardStats();
    }
  }, [enableAdvancedFeatures, fetchDashboardStats]);

  // Fetch dashboard stats function
  const fetchDashboardStats = useCallback(async () => {
    try {
      const response = await api.get('/api/dashboard/stats');
      setDashboardStats(response.data);
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    }
  }, []);

  const getEfficiencyColor = useCallback((score) => {
    if (score >= 0.8) return ACE_COLORS.PURPLE;
    if (score >= 0.6) return ACE_COLORS.YELLOW;
    return ACE_COLORS.DARK;
  }, []);

  const getUsagePercentage = () => {
    if (!usageData) return 0;
    const total = usageData.remaining_credits + usageData.current_month_usage.total_credits_used;
    return total > 0 ? (usageData.current_month_usage.total_credits_used / total) * 100 : 0;
  };

  const chartData = useMemo(() => ({
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [
      {
        label: 'Efficiency Score',
        data: statsData?.efficiency_trends?.monthly_efficiency || [0.75, 0.82, 0.85, 0.88],
        borderColor: ACE_COLORS.PURPLE,
        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
        tension: 0.4,
      },
      {
        label: 'Credit Usage',
        data: statsData?.efficiency_trends?.credit_usage_trend || [45.2, 38.7, 32.5, 29.8],
        borderColor: ACE_COLORS.YELLOW,
        backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
        tension: 0.4,
        yAxisID: 'y1',
      },
    ],
  }), [statsData]);

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Regeneration Efficiency Trends',
      },
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        min: 0,
        max: 1,
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  if (loading) {
    return (
      <Box
        {...getAccessibilityProps()}
        ref={dashboardRef}
        sx={{
          ...sx,
          ...customization,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: 400
        }}
        className={className}
        style={style}
        data-testid={testId}
      >
        <CircularProgress sx={{ color: ACE_COLORS.PURPLE }} />
        <Typography variant="body2" sx={{ ml: 2, color: ACE_COLORS.DARK }}>
          Loading dashboard...
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      {...getAccessibilityProps()}
      ref={dashboardRef}
      sx={{
        ...sx,
        ...customization,
        p: 3
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: isMobile ? 'flex-start' : 'center', mb: 3, flexDirection: isMobile ? 'column' : 'row', gap: isMobile ? 2 : 0 }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
            <RegenerateIcon sx={{ mr: 1, verticalAlign: 'middle', color: ACE_COLORS.PURPLE }} />
            Regeneration Dashboard
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
            <Chip
              label={subscriptionFeatures.planName}
              size="small"
              sx={{
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                fontWeight: 'bold'
              }}
            />
            {subscriptionFeatures.maxRegenerationsPerMonth !== -1 && (
              <Typography variant="caption" color="text.secondary">
                {subscriptionFeatures.maxRegenerationsPerMonth - monthlyUsage} regenerations remaining this month
              </Typography>
            )}
            {subscriptionFeatures.maxRegenerationsPerMonth === -1 && (
              <Typography variant="caption" sx={{ color: ACE_COLORS.PURPLE, fontWeight: 'bold' }}>
                Unlimited regenerations
              </Typography>
            )}
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, flexDirection: isMobile ? 'column' : 'row', width: isMobile ? '100%' : 'auto' }}>
          {subscriptionFeatures.hasAdvancedDashboard && (
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              onClick={() => {
                // Track settings navigation
                const settingsRecord = {
                  id: Date.now(),
                  type: 'settings_navigation',
                  timestamp: new Date().toISOString(),
                  userId: subscription?.user_id,
                  planTier: subscriptionFeatures.planTier
                };

                setDashboardHistory(prev => [settingsRecord, ...prev.slice(0, 99)]);

                if (onDashboardAction) {
                  onDashboardAction('settings_opened', { planTier: subscriptionFeatures.planTier });
                }
              }}
              sx={{
                borderColor: ACE_COLORS.PURPLE,
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  borderColor: alpha(ACE_COLORS.PURPLE, 0.8),
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              Settings
            </Button>
          )}
          <Button
            variant="contained"
            startIcon={<ShoppingCartIcon />}
            onClick={async () => {
              try {
                // Get relevant addons for this user's plan
                const relevantAddons = await getRelevantAddons('regeneration_credits');

                // Track purchase navigation
                const purchaseRecord = {
                  id: Date.now(),
                  type: 'purchase_navigation',
                  timestamp: new Date().toISOString(),
                  userId: subscription?.user_id,
                  planTier: subscriptionFeatures.planTier
                };

                setDashboardHistory(prev => [purchaseRecord, ...prev.slice(0, 99)]);

                if (onDashboardAction) {
                  onDashboardAction('purchase_initiated', {
                    planTier: subscriptionFeatures.planTier,
                    relevantAddons
                  });
                }

                // Navigate to purchase page with context
                window.open('/addons?context=regeneration_dashboard', '_blank');
              } catch (error) {
                console.error('Error loading purchase options:', error);
                window.open('/addons', '_blank');
              }
            }}
            sx={{
              bgcolor: ACE_COLORS.PURPLE,
              '&:hover': {
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.8)
              }
            }}
          >
            Buy Credits {subscriptionFeatures.addonDiscountPercent > 0 && `(${subscriptionFeatures.addonDiscountPercent}% off)`}
          </Button>
        </Box>
      </Box>

      {/* Usage Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <GlassmorphicCard variant="glassPrimary">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Credits Remaining
              </Typography>
              <Typography variant="h3" color="primary">
                {usageData?.remaining_credits || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                + {usageData?.regeneration_buffer || 0} buffer credits
              </Typography>
            </CardContent>
          </GlassmorphicCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <GlassmorphicCard variant="glassSecondary">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                This Month Usage
              </Typography>
              <Typography variant="h3" color="secondary">
                {usageData?.current_month_usage?.total_regenerations || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {usageData?.current_month_usage?.total_credits_used || 0} credits used
              </Typography>
            </CardContent>
          </GlassmorphicCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <GlassmorphicCard variant="glassSuccess">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Efficiency Score
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="h3" sx={{ color: getEfficiencyColor(usageData?.efficiency_metrics?.efficiency_score || 0) }}>
                  {Math.round((usageData?.efficiency_metrics?.efficiency_score || 0) * 100)}%
                </Typography>
                <SpeedIcon sx={{ ml: 1, color: getEfficiencyColor(usageData?.efficiency_metrics?.efficiency_score || 0) }} />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Avg {usageData?.efficiency_metrics?.average_regenerations_per_content?.toFixed(1) || 0} per content
              </Typography>
            </CardContent>
          </GlassmorphicCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <GlassmorphicCard variant="glassWarning">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quality Improvement
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="h3" color="success.main">
                  +{Math.round((usageData?.efficiency_metrics?.quality_improvement_rate || 0) * 100)}%
                </Typography>
                <TrendingUpIcon sx={{ ml: 1, color: 'success.main' }} />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Content performance boost
              </Typography>
            </CardContent>
          </GlassmorphicCard>
        </Grid>
      </Grid>

      {/* Usage Progress */}
      <GlassmorphicCard variant="glass" sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Monthly Credit Usage
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ width: '100%', mr: 1 }}>
              <LinearProgress
                variant="determinate"
                value={getUsagePercentage()}
                sx={{
                  height: 10,
                  borderRadius: 5,
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 5,
                    backgroundColor: getUsagePercentage() > 80 ? ACE_COLORS.YELLOW : ACE_COLORS.PURPLE,
                  },
                }}
              />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ minWidth: 35 }}>
              {Math.round(getUsagePercentage())}%
            </Typography>
          </Box>
          <Typography variant="body2" color="text.secondary">
            {usageData?.current_month_usage?.total_credits_used || 0} of {(usageData?.remaining_credits || 0) + (usageData?.current_month_usage?.total_credits_used || 0)} credits used
          </Typography>
        </CardContent>
      </GlassmorphicCard>

      {/* Efficiency Chart */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <GlassmorphicCard variant="glass">
            <CardContent>
              <Line data={chartData} options={chartOptions} />
            </CardContent>
          </GlassmorphicCard>
        </Grid>

        <Grid item xs={12} md={4}>
          <GlassmorphicCard variant="glass">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <PsychologyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                AI Recommendations
              </Typography>
              {usageData?.recommendations?.map((recommendation, index) => (
                <Alert
                  key={index}
                  severity="info"
                  sx={{ mb: 1, '& .MuiAlert-message': { fontSize: '0.875rem' } }}
                >
                  {recommendation}
                </Alert>
              ))}
              
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Usage Forecast
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Projected monthly usage: {usageData?.usage_forecast?.projected_monthly_usage || 0} regenerations
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Credits needed: {usageData?.usage_forecast?.projected_credits_needed?.toFixed(1) || 0}
                </Typography>
              </Box>
            </CardContent>
          </GlassmorphicCard>
        </Grid>
      </Grid>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying dashboard sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}

      {/* Dashboard Progress Indicator */}
      {dashboardProgress > 0 && dashboardProgress < 100 && (
        <Box sx={{
          position: 'fixed',
          bottom: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          p: 2,
          borderRadius: 2,
          bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          zIndex: 9999,
          minWidth: 200
        }}>
          <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
            Processing dashboard...
          </Typography>
          <LinearProgress
            variant="determinate"
            value={dashboardProgress}
            sx={{
              '& .MuiLinearProgress-bar': {
                backgroundColor: ACE_COLORS.PURPLE
              }
            }}
          />
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
RegenerationDashboard.propTypes = {
  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onDashboardAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

RegenerationDashboard.displayName = 'RegenerationDashboard';

export default RegenerationDashboard;

<!-- @since 2024-1-1 to 2025-25-7 -->
# E-commerce Integration API Specification

## Base URL
```
https://api.ace-social.com/api/ecommerce
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <access_token>
```

## Store Management Endpoints

### Connect Store
```http
POST /stores/connect
Content-Type: application/json

{
  "platform": "shopify" | "woocommerce",
  "store_url": "string",
  "credentials": {
    "access_token": "string",
    "shop_domain": "string", // Shopify only
    "consumer_key": "string", // WooCommerce only
    "consumer_secret": "string" // WooCommerce only
  },
  "sync_settings": {
    "auto_sync_enabled": true,
    "sync_frequency": 60,
    "sync_products": true,
    "sync_inventory": true
  }
}
```

**Response:**
```json
{
  "store_id": "string",
  "status": "connected",
  "message": "Store connected successfully",
  "webhook_urls": [
    "https://api.aceo.com/api/ecommerce/webhooks/shopify"
  ]
}
```

### List Stores
```http
GET /stores?page=1&limit=20&status=connected
```

**Response:**
```json
{
  "stores": [
    {
      "id": "string",
      "platform": "shopify",
      "store_name": "My Store",
      "store_url": "https://mystore.myshopify.com",
      "connection_status": "connected",
      "last_sync_at": "2023-12-01T10:00:00Z",
      "product_count": 150,
      "health_status": "healthy"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

### Get Store Details
```http
GET /stores/{store_id}
```

**Response:**
```json
{
  "id": "string",
  "platform": "shopify",
  "store_name": "My Store",
  "store_url": "https://mystore.myshopify.com",
  "connection_status": "connected",
  "sync_settings": {
    "auto_sync_enabled": true,
    "sync_frequency": 60,
    "last_sync_at": "2023-12-01T10:00:00Z"
  },
  "health_check": {
    "status": "healthy",
    "last_check_at": "2023-12-01T10:00:00Z",
    "response_time_ms": 250
  },
  "statistics": {
    "total_products": 150,
    "active_products": 140,
    "total_variants": 300,
    "last_order_at": "2023-12-01T09:30:00Z"
  }
}
```

### Trigger Store Sync
```http
POST /stores/{store_id}/sync
Content-Type: application/json

{
  "sync_type": "full" | "incremental",
  "force": false
}
```

**Response:**
```json
{
  "sync_id": "string",
  "status": "started",
  "estimated_duration_minutes": 5
}
```

## Product Management Endpoints

### List Products
```http
GET /products?store_id=string&page=1&limit=20&status=active&search=keyword&category=electronics
```

**Response:**
```json
{
  "products": [
    {
      "id": "string",
      "external_id": "12345",
      "title": "Premium Wireless Headphones",
      "description": "High-quality wireless headphones...",
      "status": "active",
      "price_range": {
        "min": 99.99,
        "max": 149.99,
        "currency": "USD"
      },
      "images": [
        {
          "src": "https://cdn.example.com/image.jpg",
          "alt_text": "Wireless headphones"
        }
      ],
      "variants_count": 3,
      "inventory_total": 50,
      "last_synced_at": "2023-12-01T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

### Get Product Details
```http
GET /products/{product_id}
```

**Response:**
```json
{
  "id": "string",
  "external_id": "12345",
  "title": "Premium Wireless Headphones",
  "description": "High-quality wireless headphones with noise cancellation...",
  "handle": "premium-wireless-headphones",
  "product_type": "Electronics",
  "vendor": "TechBrand",
  "status": "active",
  "tags": ["wireless", "headphones", "premium"],
  "categories": [
    {
      "id": "electronics",
      "name": "Electronics",
      "parent_id": null
    }
  ],
  "variants": [
    {
      "id": "var_1",
      "title": "Black / Standard",
      "sku": "WH-001-BLK",
      "price": 99.99,
      "compare_at_price": 129.99,
      "inventory_quantity": 25,
      "option1": "Black",
      "option2": "Standard"
    }
  ],
  "images": [
    {
      "src": "https://cdn.example.com/image.jpg",
      "alt_text": "Wireless headphones",
      "position": 1
    }
  ],
  "seo": {
    "title": "Premium Wireless Headphones - TechBrand",
    "description": "Shop premium wireless headphones...",
    "keywords": ["wireless", "headphones", "bluetooth"]
  },
  "ai_analysis": {
    "target_audience": "Tech-savvy professionals aged 25-45",
    "content_themes": ["productivity", "quality", "lifestyle"],
    "price_positioning": "premium",
    "analyzed_at": "2023-12-01T10:00:00Z"
  }
}
```

## Content Generation Endpoints

### Generate Product Content
```http
POST /content/generate
Content-Type: application/json

{
  "product_ids": ["string"],
  "platforms": ["facebook", "instagram", "twitter"],
  "content_type": "post" | "story" | "reel",
  "style": "promotional" | "educational" | "lifestyle",
  "include_pricing": true,
  "include_cta": true,
  "brand_voice": "professional" | "casual" | "luxury",
  "seasonal_theme": "holiday" | "summer" | "back-to-school",
  "variations_count": 3
}
```

**Response:**
```json
{
  "content_id": "string",
  "status": "generated",
  "variations": [
    {
      "id": "var_1",
      "platform": "facebook",
      "text_content": "🎧 Experience premium sound quality with our wireless headphones! Perfect for work, travel, and everything in between. Now $99.99 (was $129.99) ✨ #WirelessHeadphones #TechDeals",
      "images": [
        {
          "url": "https://cdn.example.com/generated-image.jpg",
          "prompt": "Professional lifestyle photo of wireless headphones"
        }
      ],
      "hashtags": ["#WirelessHeadphones", "#TechDeals", "#PremiumAudio"],
      "call_to_action": "Shop Now",
      "estimated_engagement": {
        "likes": 150,
        "comments": 25,
        "shares": 10
      }
    }
  ],
  "usage_cost": {
    "credits_used": 3,
    "cost_usd": 0.15
  }
}
```

### Bulk Content Generation
```http
POST /content/bulk-generate
Content-Type: application/json

{
  "store_id": "string",
  "filters": {
    "categories": ["electronics"],
    "price_range": {"min": 50, "max": 500},
    "status": "active"
  },
  "content_settings": {
    "platforms": ["facebook", "instagram"],
    "variations_per_product": 2,
    "style": "promotional",
    "include_pricing": true
  },
  "schedule": {
    "start_date": "2023-12-01",
    "frequency": "daily",
    "time_slots": ["09:00", "15:00"]
  }
}
```

**Response:**
```json
{
  "job_id": "string",
  "status": "queued",
  "estimated_products": 25,
  "estimated_content_pieces": 100,
  "estimated_completion": "2023-12-01T11:00:00Z",
  "estimated_cost": {
    "credits": 50,
    "usd": 2.50
  }
}
```

## ICP Generation Endpoints

### Generate ICP
```http
POST /icps/generate
Content-Type: application/json

{
  "name": "Premium Electronics Buyers",
  "product_ids": ["string"],
  "store_id": "string",
  "analysis_depth": "basic" | "advanced" | "comprehensive",
  "include_targeting": true,
  "target_platforms": ["facebook", "google", "linkedin"]
}
```

**Response:**
```json
{
  "icp_id": "string",
  "status": "generated",
  "demographics": {
    "age_range": {"min": 25, "max": 45},
    "gender": ["male", "female"],
    "income_range": {"min": 50000, "max": 150000},
    "education_level": ["bachelor", "master"],
    "occupation": ["technology", "business", "creative"]
  },
  "psychographics": {
    "interests": ["technology", "productivity", "quality"],
    "values": ["innovation", "efficiency", "quality"],
    "lifestyle": ["busy_professional", "early_adopter"],
    "buying_motivations": ["quality", "convenience", "status"]
  },
  "targeting_recommendations": {
    "facebook": {
      "interests": ["Consumer Electronics", "Technology", "Gadgets"],
      "behaviors": ["Technology early adopters"],
      "demographics": {
        "age_min": 25,
        "age_max": 45,
        "income": "top_25_percent"
      }
    }
  },
  "confidence_score": 85
}
```

### List ICPs
```http
GET /icps?store_id=string&page=1&limit=20
```

**Response:**
```json
{
  "icps": [
    {
      "id": "string",
      "name": "Premium Electronics Buyers",
      "product_count": 15,
      "confidence_score": 85,
      "generated_at": "2023-12-01T10:00:00Z",
      "demographics_summary": "Tech-savvy professionals aged 25-45"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 5,
    "pages": 1
  }
}
```

## Campaign Management Endpoints

### Create Campaign
```http
POST /campaigns
Content-Type: application/json

{
  "name": "Holiday Electronics Sale",
  "description": "Promote electronics products for holiday season",
  "campaign_type": "seasonal",
  "store_id": "string",
  "products": [
    {
      "product_id": "string",
      "variants": ["var_1", "var_2"],
      "content_themes": ["holiday", "gift"]
    }
  ],
  "target_platforms": ["facebook", "instagram", "twitter"],
  "content_schedule": {
    "start_date": "2023-12-01",
    "end_date": "2023-12-25",
    "posting_frequency": "daily",
    "time_slots": ["09:00", "15:00", "19:00"]
  },
  "content_settings": {
    "include_pricing": true,
    "cta_style": "urgent",
    "brand_voice": "festive"
  },
  "ab_testing": {
    "enabled": true,
    "variants": [
      {"name": "Emotional", "content_style": "emotional", "traffic_split": 50},
      {"name": "Rational", "content_style": "rational", "traffic_split": 50}
    ]
  }
}
```

**Response:**
```json
{
  "campaign_id": "string",
  "status": "draft",
  "estimated_content_pieces": 75,
  "estimated_cost": {
    "credits": 150,
    "usd": 7.50
  },
  "schedule_preview": {
    "total_posts": 75,
    "posts_per_day": 3,
    "duration_days": 25
  }
}
```

### Get Campaign Analytics
```http
GET /campaigns/{campaign_id}/analytics?date_range=7d
```

**Response:**
```json
{
  "campaign_id": "string",
  "period": {
    "start_date": "2023-11-24",
    "end_date": "2023-12-01"
  },
  "performance": {
    "content_generated": 21,
    "posts_published": 18,
    "total_impressions": 15000,
    "total_engagement": 750,
    "click_through_rate": 2.5,
    "conversion_rate": 1.2,
    "revenue_attributed": 1250.00
  },
  "platform_breakdown": {
    "facebook": {
      "posts": 6,
      "impressions": 8000,
      "engagement": 400,
      "clicks": 200,
      "conversions": 8
    },
    "instagram": {
      "posts": 6,
      "impressions": 5000,
      "engagement": 250,
      "clicks": 125,
      "conversions": 5
    },
    "twitter": {
      "posts": 6,
      "impressions": 2000,
      "engagement": 100,
      "clicks": 50,
      "conversions": 2
    }
  },
  "top_performing_content": [
    {
      "content_id": "string",
      "platform": "facebook",
      "engagement_rate": 8.5,
      "clicks": 85,
      "conversions": 4
    }
  ]
}
```

## Webhook Endpoints

### Shopify Webhook
```http
POST /webhooks/shopify
X-Shopify-Topic: products/update
X-Shopify-Hmac-Sha256: <signature>
Content-Type: application/json

{
  "id": 12345,
  "title": "Updated Product Title",
  "updated_at": "2023-12-01T10:00:00Z"
}
```

### WooCommerce Webhook
```http
POST /webhooks/woocommerce
X-WC-Webhook-Source: <store_url>
X-WC-Webhook-Topic: product.updated
X-WC-Webhook-Signature: <signature>
Content-Type: application/json

{
  "id": 12345,
  "name": "Updated Product Name",
  "date_modified": "2023-12-01T10:00:00Z"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "STORE_CONNECTION_FAILED",
    "message": "Failed to connect to store",
    "details": "Invalid API credentials provided",
    "timestamp": "2023-12-01T10:00:00Z",
    "request_id": "req_123456"
  }
}
```

### Common Error Codes
- `STORE_CONNECTION_FAILED` - Store connection issues
- `PRODUCT_SYNC_FAILED` - Product synchronization errors
- `CONTENT_GENERATION_FAILED` - Content generation errors
- `INSUFFICIENT_CREDITS` - Not enough credits for operation
- `RATE_LIMIT_EXCEEDED` - API rate limit exceeded
- `INVALID_STORE_CREDENTIALS` - Invalid store API credentials
- `WEBHOOK_VERIFICATION_FAILED` - Webhook signature verification failed

## Rate Limits

- **Store Management**: 100 requests per minute
- **Product Queries**: 1000 requests per minute
- **Content Generation**: 50 requests per minute
- **Webhook Processing**: 500 requests per minute

Rate limit headers are included in all responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1701432000
```

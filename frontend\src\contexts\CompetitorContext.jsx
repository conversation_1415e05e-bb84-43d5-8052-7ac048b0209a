/**
 * Competitor Context
 * Provides competitor management functionality with comprehensive error handling
 * Production-ready implementation with proper logging and analytics
 @since 2024-1-1 to 2025-25-7
*/

import {
  createContext,
  useContext,
  useState,
  useCallback,
} from "react";
import { useNotification } from "../hooks/useNotification";
import * as competitorApi from "../api/competitors";

// Configuration constants
const CONFIG = {
  // Cache settings
  CACHE_DURATION: 30000, // 30 seconds

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[Competitor] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[Competitor] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Competitor Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[Competitor] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Competitor Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[Competitor] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Competitor Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const CompetitorContext = createContext();

// eslint-disable-next-line react-refresh/only-export-components
export const useCompetitors = () => useContext(CompetitorContext);

export const CompetitorProvider = ({ children }) => {
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [competitors, setCompetitors] = useState([]);
  const [selectedCompetitor, setSelectedCompetitor] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [comparison, setComparison] = useState(null);
  const [recommendations, setRecommendations] = useState(null);

  // Fetch all competitors with debounce to prevent repeated calls
  const [fetchInProgress, setFetchInProgress] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState(null);

  const fetchCompetitors = useCallback(
    async (activeOnly = true) => {
      // Don't fetch if already in progress
      if (fetchInProgress) return;

      // Don't fetch if we've fetched in the last 30 seconds
      const now = new Date();
      if (lastFetchTime && now.getTime() - lastFetchTime.getTime() < 30000) {
        return;
      }

      setFetchInProgress(true);
      setLoading(true);
      setError(null);

      try {
        const data = await competitorApi.getCompetitors(activeOnly);
        setCompetitors(data);
        setLastFetchTime(new Date());
      } catch (err) {
        // Handle error with proper logging
        logger.error("Error fetching competitors", err);
        setError(err.message || "Failed to fetch competitors");
      } finally {
        setLoading(false);
        setFetchInProgress(false);
      }
    },
    [fetchInProgress, lastFetchTime]
  );

  // Fetch a specific competitor
  const fetchCompetitor = useCallback(
    async (competitorId) => {
      if (!competitorId) {
        logger.warn("fetchCompetitor called without competitorId");
        return null;
      }

      setLoading(true);
      setError(null);
      logger.debug("Fetching competitor", { competitorId });

      try {
        const data = await competitorApi.getCompetitor(competitorId);
        setSelectedCompetitor(data);
        logger.info("Competitor fetched successfully", { competitorId, name: data?.name });
        return data;
      } catch (err) {
        logger.error("Failed to fetch competitor details", err, { competitorId });
        setError(err.message || "Failed to fetch competitor details");
        showErrorNotification("Failed to fetch competitor details");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [showErrorNotification]
  );

  // Create a new competitor
  const createCompetitor = useCallback(
    async (competitorData) => {
      if (!competitorData || !competitorData.name) {
        logger.warn("createCompetitor called with invalid data", { competitorData });
        setError("Competitor name is required");
        return null;
      }

      setLoading(true);
      setError(null);
      logger.debug("Creating competitor", { name: competitorData.name });

      try {
        const data = await competitorApi.createCompetitor(competitorData);
        setCompetitors((prev) => [...prev, data]);
        logger.info("Competitor created successfully", {
          id: data.id,
          name: data.name
        });
        showSuccessNotification("Competitor created successfully");
        return data;
      } catch (err) {
        logger.error("Failed to create competitor", err, { competitorData });
        setError(err.message || "Failed to create competitor");
        showErrorNotification("Failed to create competitor");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [showSuccessNotification, showErrorNotification]
  );

  // Update a competitor
  const updateCompetitor = useCallback(
    async (competitorId, updateData) => {
      if (!competitorId || !updateData) {
        logger.warn("updateCompetitor called with invalid parameters", {
          competitorId,
          updateData
        });
        setError("Invalid update parameters");
        return null;
      }

      setLoading(true);
      setError(null);
      logger.debug("Updating competitor", { competitorId, updateData });

      try {
        const data = await competitorApi.updateCompetitor(
          competitorId,
          updateData
        );

        // Update competitors list
        setCompetitors((prev) =>
          prev.map((comp) => (comp.id === competitorId ? data : comp))
        );

        // Update selected competitor if it's the one being updated
        if (selectedCompetitor && selectedCompetitor.id === competitorId) {
          setSelectedCompetitor(data);
        }

        logger.info("Competitor updated successfully", {
          id: data.id,
          name: data.name
        });
        showSuccessNotification("Competitor updated successfully");
        return data;
      } catch (err) {
        logger.error("Failed to update competitor", err, { competitorId, updateData });
        setError(err.message || "Failed to update competitor");
        showErrorNotification("Failed to update competitor");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [selectedCompetitor, showSuccessNotification, showErrorNotification]
  );

  // Delete a competitor
  const deleteCompetitor = useCallback(
    async (competitorId) => {
      if (!competitorId) {
        logger.warn("deleteCompetitor called without competitorId");
        setError("Competitor ID is required");
        return false;
      }

      setLoading(true);
      setError(null);
      logger.debug("Deleting competitor", { competitorId });

      try {
        await competitorApi.deleteCompetitor(competitorId);

        // Remove from competitors list
        setCompetitors((prev) =>
          prev.filter((comp) => comp.id !== competitorId)
        );

        // Clear selected competitor if it's the one being deleted
        if (selectedCompetitor && selectedCompetitor.id === competitorId) {
          setSelectedCompetitor(null);
        }

        logger.info("Competitor deleted successfully", { competitorId });
        showSuccessNotification("Competitor deleted successfully");
        return true;
      } catch (err) {
        logger.error("Failed to delete competitor", err, { competitorId });
        setError(err.message || "Failed to delete competitor");
        showErrorNotification("Failed to delete competitor");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [selectedCompetitor, showSuccessNotification, showErrorNotification]
  );

  // Sync competitor data
  const syncCompetitor = useCallback(
    async (competitorId, platforms = []) => {
      if (!competitorId) {
        logger.warn("syncCompetitor called without competitorId");
        setError("Competitor ID is required");
        return null;
      }

      setLoading(true);
      setError(null);
      logger.debug("Syncing competitor data", { competitorId, platforms });

      try {
        const data = await competitorApi.syncCompetitor(
          competitorId,
          platforms
        );

        // Update competitors list
        setCompetitors((prev) =>
          prev.map((comp) => (comp.id === competitorId ? data : comp))
        );

        // Update selected competitor if it's the one being synced
        if (selectedCompetitor && selectedCompetitor.id === competitorId) {
          setSelectedCompetitor(data);
        }

        logger.info("Competitor data synced successfully", {
          competitorId,
          platforms: platforms.length
        });
        showSuccessNotification("Competitor data synced successfully");
        return data;
      } catch (err) {
        logger.error("Failed to sync competitor data", err, { competitorId, platforms });
        setError(err.message || "Failed to sync competitor data");
        showErrorNotification("Failed to sync competitor data");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [selectedCompetitor, showSuccessNotification, showErrorNotification]
  );

  // Analyze competitor
  const analyzeCompetitor = useCallback(
    async (competitorId) => {
      setLoading(true);
      setError(null);

      try {
        const data = await competitorApi.analyzeCompetitor(competitorId);

        // Update competitors list
        setCompetitors((prev) =>
          prev.map((comp) => (comp.id === competitorId ? data : comp))
        );

        // Update selected competitor if it's the one being analyzed
        if (selectedCompetitor && selectedCompetitor.id === competitorId) {
          setSelectedCompetitor(data);
        }

        showSuccessNotification("Competitor analysis completed");
        return data;
      } catch (err) {
        setError(err.message || "Failed to analyze competitor");
        showErrorNotification("Failed to analyze competitor");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [selectedCompetitor, showSuccessNotification, showErrorNotification]
  );

  // Compare competitors
  const compareCompetitors = useCallback(
    async (competitorIds, metrics, timePeriod) => {
      setLoading(true);
      setError(null);

      try {
        const data = await competitorApi.compareCompetitors(
          competitorIds,
          metrics,
          timePeriod
        );
        setComparison(data);
        return data;
      } catch (err) {
        setError(err.message || "Failed to compare competitors");
        showErrorNotification("Failed to compare competitors");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [showErrorNotification]
  );

  // Get recommendations
  const getRecommendations = useCallback(
    async (competitorIds, focusAreas) => {
      setLoading(true);
      setError(null);

      try {
        const data = await competitorApi.getCompetitorRecommendations(
          competitorIds,
          focusAreas
        );
        setRecommendations(data);
        return data;
      } catch (err) {
        setError(err.message || "Failed to get recommendations");
        showErrorNotification("Failed to get recommendations");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [showErrorNotification]
  );

  // Compare with user's performance
  const compareWithUser = useCallback(
    async (competitorIds) => {
      setLoading(true);
      setError(null);

      try {
        const data = await competitorApi.compareWithCompetitors(competitorIds);
        return data;
      } catch (err) {
        setError(err.message || "Failed to compare with your performance");
        showErrorNotification("Failed to compare with your performance");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [showErrorNotification]
  );

  // Clear selected competitor
  const clearSelectedCompetitor = useCallback(() => {
    logger.debug("Clearing selected competitor");
    setSelectedCompetitor(null);
  }, []);

  // Clear comparison data
  const clearComparison = useCallback(() => {
    logger.debug("Clearing comparison data");
    setComparison(null);
  }, []);

  // Clear recommendations
  const clearRecommendations = useCallback(() => {
    logger.debug("Clearing recommendations");
    setRecommendations(null);
  }, []);

  // Context value with organized structure
  const value = {
    // State
    competitors,
    selectedCompetitor,
    loading,
    error,
    comparison,
    recommendations,

    // Core CRUD operations
    fetchCompetitors,
    fetchCompetitor,
    createCompetitor,
    updateCompetitor,
    deleteCompetitor,

    // Advanced operations
    syncCompetitor,
    analyzeCompetitor,
    compareCompetitors,
    getRecommendations,
    compareWithUser,

    // Utility functions
    clearSelectedCompetitor,
    clearComparison,
    clearRecommendations,
    clearError: () => setError(null),

    // Helper functions
    hasCompetitors: competitors.length > 0,
    isCompetitorSelected: !!selectedCompetitor,
    getCompetitorById: (id) => competitors.find(comp => comp.id === id),
    getCompetitorCount: () => competitors.length,
  };

  return (
    <CompetitorContext.Provider value={value}>
      {children}
    </CompetitorContext.Provider>
  );
};

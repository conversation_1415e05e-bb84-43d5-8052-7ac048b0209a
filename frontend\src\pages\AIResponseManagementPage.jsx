// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Container,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Tune as TuneIcon,
  Analytics as AnalyticsIcon,
  FormatListBulleted as ListIcon,
  NavigateNext as NavigateNextIcon,
  Comment as CommentIcon
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';

// Import custom components
import AIResponseAnalytics from '../components/analytics/AIResponseAnalytics';
import PlatformResponseTemplates from '../components/messaging/PlatformResponseTemplates';
import BulkResponseManager from '../components/messaging/BulkResponseManager';
import PageHeader from '../components/common/PageHeader';
import EnhancedPostListView from '../components/social/EnhancedPostListView';
import PostDetailModal from '../components/social/PostDetailModal';
import ErrorBoundary from '../components/common/ErrorBoundary';
import EmptyState from '../components/common/EmptyState';
import LoadingSkeleton from '../components/common/LoadingSkeleton';
import { useNotification } from '../hooks/useNotification';
import aiResponseService from '../services/aiResponseService';
import CreditCounter from '../components/credits/CreditCounter';


/**
 * Enhanced AI Response Management page with split-view interface
 */
const AIResponseManagementPage = () => {
  const { showErrorNotification, showSuccessNotification } = useNotification();
  
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [posts, setPosts] = useState([]);
  const [selectedPost, setSelectedPost] = useState(null);
  const [comments, setComments] = useState([]);
  const [loadingComments, setLoadingComments] = useState(false);

  // Enhanced UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [filterPlatform, setFilterPlatform] = useState('all');
  const [postDetailModalOpen, setPostDetailModalOpen] = useState(false);

  // Error handling state
  const [error, setError] = useState(null);
  const [commentsError, setCommentsError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Fetch posts that have pending comments
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!isOnline) {
          throw new Error('No internet connection. Please check your network and try again.');
        }

        const posts = await aiResponseService.fetchPostsWithPendingComments();
        setPosts(posts);

        // Select the first post by default if none selected
        if (posts.length > 0 && !selectedPost) {
          setSelectedPost(posts[0]);
        }

        setRetryCount(0); // Reset retry count on success
      } catch (error) {
        console.error('Error fetching posts:', error);
        setError(error);

        // Show user-friendly error message
        if (error.type === 'CLIENT_ERROR' && error.status === 404) {
          // No posts found - this is not really an error
          setPosts([]);
        } else if (error.type === 'MAX_RETRIES_EXCEEDED') {
          showErrorNotification(`Failed to load posts after ${aiResponseService.retryAttempts} attempts. Please try again later.`);
        } else if (!isOnline) {
          showErrorNotification('No internet connection. Please check your network and try again.');
        } else {
          showErrorNotification(`Failed to load posts: ${error.message}`);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [isOnline, retryCount, showErrorNotification, selectedPost]); // Re-fetch when network status changes or retry is triggered

  // Fetch comments for selected post
  useEffect(() => {
    if (!selectedPost) return;

    const fetchComments = async () => {
      try {
        setLoadingComments(true);
        setCommentsError(null);

        if (!isOnline) {
          throw new Error('No internet connection. Please check your network and try again.');
        }

        const comments = await aiResponseService.fetchCommentsForPost(selectedPost.id);
        setComments(comments);
      } catch (error) {
        console.error('Error fetching comments:', error);
        setCommentsError(error);

        // Show user-friendly error message
        if (error.type === 'CLIENT_ERROR' && error.status === 404) {
          // No comments found - this is not really an error
          setComments([]);
        } else if (error.type === 'MAX_RETRIES_EXCEEDED') {
          showErrorNotification(`Failed to load comments after ${aiResponseService.retryAttempts} attempts. Please try again later.`);
        } else if (!isOnline) {
          showErrorNotification('No internet connection. Please check your network and try again.');
        } else {
          showErrorNotification(`Failed to load comments: ${error.message}`);
        }
      } finally {
        setLoadingComments(false);
      }
    };

    fetchComments();
  }, [selectedPost, isOnline, showErrorNotification]);

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  const handlePostSelect = (post) => {
    setSelectedPost(post);
    setPostDetailModalOpen(true);
  };

  // Enhanced handlers
  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handleFilterChange = (platform) => {
    setFilterPlatform(platform);
  };

  // Production-ready action handlers using aiResponseService
  const handlePublishResponse = async (commentId, responseText) => {
    try {
      await aiResponseService.approveResponse(commentId, responseText);

      // Update local state
      setComments(prevComments =>
        prevComments.map(comment =>
          comment.id === commentId
            ? { ...comment, ai_response_status: 'published' }
            : comment
        )
      );

      showSuccessNotification('Response published successfully');
    } catch (error) {
      console.error('Error publishing response:', error);
      showErrorNotification(`Failed to publish response: ${error.message}`);
    }
  };

  const handleRejectResponse = async (commentId) => {
    try {
      await aiResponseService.rejectResponse(commentId);

      // Update local state
      setComments(prevComments =>
        prevComments.map(comment =>
          comment.id === commentId
            ? { ...comment, ai_response_status: 'rejected' }
            : comment
        )
      );

      showSuccessNotification('Response rejected successfully');
    } catch (error) {
      console.error('Error rejecting response:', error);
      showErrorNotification(`Failed to reject response: ${error.message}`);
    }
  };

  const handleRewriteResponse = async (commentId, prompt = '') => {
    try {
      const result = await aiResponseService.regenerateResponse(commentId, prompt);

      // Update local state with new response
      setComments(prevComments =>
        prevComments.map(comment =>
          comment.id === commentId
            ? {
                ...comment,
                ai_response_text: result.response_text,
                ai_response_quality: result.quality_score,
                ai_response_status: 'pending'
              }
            : comment
        )
      );

      showSuccessNotification('Response regenerated successfully');
    } catch (error) {
      console.error('Error regenerating response:', error);
      showErrorNotification(`Failed to regenerate response: ${error.message}`);
      throw error; // Re-throw for RegenerateButton error handling
    }
  };

  const handleManualEdit = async (commentId, editedText) => {
    try {
      await aiResponseService.editResponse(commentId, editedText);

      // Update local state
      setComments(prevComments =>
        prevComments.map(comment =>
          comment.id === commentId
            ? { ...comment, ai_response_text: editedText, ai_response_status: 'pending' }
            : comment
        )
      );

      showSuccessNotification('Response updated successfully');
    } catch (error) {
      console.error('Error updating response:', error);
      showErrorNotification(`Failed to update response: ${error.message}`);
    }
  };

  const handlePreview = async (comment) => {
    // For now, just show the response in a notification
    // In production, this could open a preview modal showing how it will appear on the platform
    showSuccessNotification('Preview: ' + comment.ai_response_text);
  };



  // Render the enhanced interface for Response Management tab
  const renderResponseManagement = () => {
    // Show network error state
    if (!isOnline) {
      return (
        <EmptyState
          type="network-error"
          onAction={() => window.location.reload()}
          onSecondaryAction={() => setRetryCount(prev => prev + 1)}
        />
      );
    }

    // Show error state with retry option
    if (error && !loading) {
      return (
        <EmptyState
          type="api-error"
          title="Failed to load posts"
          description={`${error.message} ${error.correlationId ? `(ID: ${error.correlationId})` : ''}`}
          onAction={() => setRetryCount(prev => prev + 1)}
          actionText="Retry"
          onSecondaryAction={() => window.location.reload()}
          secondaryActionText="Refresh Page"
        />
      );
    }

    // Show loading skeleton
    if (loading) {
      return (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">Loading posts...</Typography>
          </Box>
          <LoadingSkeleton type="post-list" count={6} />
        </Box>
      );
    }

    // Show empty state when no posts
    if (!loading && posts.length === 0) {
      return (
        <EmptyState
          type="no-posts"
          onAction={() => setRetryCount(prev => prev + 1)}
          onSecondaryAction={() => window.location.href = '/dashboard'}
        />
      );
    }

    return (
      <ErrorBoundary>
        <Box>
          {/* Header with post count and refresh */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              Posts with Pending Comments ({posts.length})
            </Typography>
            <Tooltip title="Refresh posts">
              <IconButton onClick={() => setRetryCount(prev => prev + 1)}>
                <NavigateNextIcon />
              </IconButton>
            </Tooltip>
          </Box>

          {/* Enhanced Post List View */}
          <EnhancedPostListView
            posts={posts}
            loading={loading}
            onPostSelect={handlePostSelect}
            selectedPostId={selectedPost?.id}
            searchQuery={searchQuery}
            onSearchChange={handleSearchChange}
            filterPlatform={filterPlatform}
            onFilterChange={handleFilterChange}
          />

          {/* Post Detail Modal */}
          <PostDetailModal
            open={postDetailModalOpen}
            onClose={() => setPostDetailModalOpen(false)}
            post={selectedPost}
            comments={comments}
            commentsLoading={loadingComments}
            commentsError={commentsError}
            onApprove={handlePublishResponse}
            onReject={handleRejectResponse}
            onRegenerate={handleRewriteResponse}
            onManualEdit={handleManualEdit}
            onPreview={handlePreview}
            onRetry={() => setSelectedPost({ ...selectedPost })} // Trigger comments refetch
          />
        </Box>
      </ErrorBoundary>
    );
  };

  return (
    <>
      <Helmet>
        <title>AI Response Management | B2B Influencer Tool</title>
      </Helmet>

      <Container maxWidth="xl">
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
          <PageHeader
            title="AI Response Management"
            description="Manage, analyze, and optimize your automated social media responses"
            icon={TuneIcon}
          />
          <CreditCounter variant="default" showProgress={true} showRefresh={true} />
        </Box>

        <Paper sx={{ mb: 4, overflow: 'hidden' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="AI response management tabs"
            variant="fullWidth"
          >
            <Tab
              icon={<CommentIcon />}
              label="Response Management"
              id="tab-0"
              aria-controls="tabpanel-0"
            />
            <Tab
              icon={<AnalyticsIcon />}
              label="Analytics"
              id="tab-1"
              aria-controls="tabpanel-1"
            />
            <Tab
              icon={<TuneIcon />}
              label="Templates"
              id="tab-2"
              aria-controls="tabpanel-2"
            />
            <Tab
              icon={<ListIcon />}
              label="Response Queue"
              id="tab-3"
              aria-controls="tabpanel-3"
            />
          </Tabs>

          <Divider />

          <Box role="tabpanel" hidden={activeTab !== 0} id="tabpanel-0" aria-labelledby="tab-0" sx={{ p: 3 }}>
            {activeTab === 0 && renderResponseManagement()}
          </Box>

          <Box role="tabpanel" hidden={activeTab !== 1} id="tabpanel-1" aria-labelledby="tab-1" sx={{ p: 3 }}>
            {activeTab === 1 && <AIResponseAnalytics />}
          </Box>

          <Box role="tabpanel" hidden={activeTab !== 2} id="tabpanel-2" aria-labelledby="tab-2" sx={{ p: 3 }}>
            {activeTab === 2 && <PlatformResponseTemplates />}
          </Box>

          <Box role="tabpanel" hidden={activeTab !== 3} id="tabpanel-3" aria-labelledby="tab-3" sx={{ p: 3 }}>
            {activeTab === 3 && <BulkResponseManager />}
          </Box>
        </Paper>

        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h6" gutterBottom>
            About AI Response Management
          </Typography>

          <Typography variant="body1" paragraph>
            The AI Response Management system helps you automate and optimize your social media responses
            with advanced AI technology. This system includes:
          </Typography>

          <Box component="ul" sx={{ pl: 2 }}>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body1">
                <strong>AI Response Refinement System:</strong> A feedback loop mechanism where users can rate AI-generated responses,
                with an algorithm that uses this feedback to improve future suggestions.
              </Typography>
            </Box>

            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body1">
                <strong>Comprehensive Analytics Dashboard:</strong> Detailed metrics tracking for automated responses including
                approval rates, response times, engagement metrics, and sentiment shift analysis.
              </Typography>
            </Box>

            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body1">
                <strong>Platform-Specific Optimization:</strong> Templates and formatting rules specific to each supported
                social network, with tone adjustments and preview rendering.
              </Typography>
            </Box>

            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body1">
                <strong>Bulk Response Management:</strong> Queue interface for pending responses with multi-select functionality,
                batch editing capabilities, and priority queuing.
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Container>
    </>
  );
};

export default AIResponseManagementPage;

// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Badge,
  Fab,
  Snackbar,
  useTheme,
  alpha,
  LinearProgress,
  Avatar,
  AvatarGroup,
  Divider,
} from '@mui/material';
import {
  Support as SupportIcon,
  Dashboard as DashboardIcon,
  Assignment as TicketsIcon,
  People as AgentsIcon,
  Assessment as AnalyticsIcon,
  QuestionAnswer as KnowledgeIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Speed as SpeedIcon,
  Star as StarIcon,
  AccessTime as TimeIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Chat as ChatIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';
import { format, formatDistanceToNow } from 'date-fns';

// Components
import SupportDashboard from '../components/support/SupportDashboard';
import TicketManagementSimple from '../components/support/TicketManagementSimple';
import AgentManagement from '../components/support/AgentManagement';
import SupportAnalytics from '../components/support/SupportAnalytics';
import KnowledgeBase from '../components/support/KnowledgeBase';
import SupportSettings from '../components/support/SupportSettings';
import StablePageWrapper from '../components/StablePageWrapper';

// Hooks and utilities
import useSupportData from '../hooks/useSupportData';
import {
  formatNumber,
  formatPercentage,
  formatHours,
  generateCorrelationId,
} from '../utils/supportHelpers';

// API
import api from '../api';

/**
 * Enhanced Support Module Component
 * Production-ready with Material-UI glass morphism styling, real-time updates,
 * and comprehensive support management features
 */
const SupportModule = () => {
  const theme = useTheme();
  const [currentTab, setCurrentTab] = useState(0);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [lastUpdated, setLastUpdated] = useState(null);

  // Use custom hook for data management
  const {
    data,
    loading,
    error,
    fetchDashboard,
    fetchTickets,
    fetchAgents,
    refreshAll,
    startRealTimeUpdates,
    stopRealTimeUpdates,
    clearCache,
  } = useSupportData();

  // Tab configuration with enhanced descriptions
  const tabs = [
    {
      label: 'Dashboard',
      icon: <DashboardIcon />,
      component: SupportDashboard,
      description: 'Real-time overview of support metrics and KPIs'
    },
    {
      label: 'Tickets',
      icon: <TicketsIcon />,
      component: TicketManagementSimple,
      description: 'Manage customer support tickets and requests'
    },
    {
      label: 'Agents',
      icon: <AgentsIcon />,
      component: AgentManagement,
      description: 'Manage support agents and workload distribution'
    },
    {
      label: 'Analytics',
      icon: <AnalyticsIcon />,
      component: SupportAnalytics,
      description: 'Detailed analytics and performance insights'
    },
    {
      label: 'Knowledge Base',
      icon: <KnowledgeIcon />,
      component: KnowledgeBase,
      description: 'Manage help articles and documentation'
    },
    {
      label: 'Settings',
      icon: <SettingsIcon />,
      component: SupportSettings,
      description: 'Configure support system settings and preferences'
    },
  ];

  // Load initial data and start real-time updates
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Use Promise.allSettled to prevent one failure from stopping others
        const results = await Promise.allSettled([
          fetchDashboard(),
          fetchTickets(),
          fetchAgents(),
        ]);

        // Check if all requests failed (likely backend is down)
        const allFailed = results.every(result => result.status === 'rejected');
        if (allFailed) {
          showSnackbar('Backend service is unavailable. Using mock data for demonstration.', 'warning');
        }

        setLastUpdated(new Date());
      } catch (error) {
        console.error('Error loading initial data:', error);
        showSnackbar('Failed to load initial data', 'error');
      }
    };

    loadInitialData();
    startRealTimeUpdates();

    return () => {
      stopRealTimeUpdates();
    };
  }, [fetchDashboard, fetchTickets, fetchAgents, startRealTimeUpdates, stopRealTimeUpdates]);

  // Utility functions
  const showSnackbar = useCallback((message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  const closeSnackbar = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  const handleTabChange = useCallback((event, newValue) => {
    setCurrentTab(newValue);

    // Clear cache for better performance when switching tabs
    if (newValue !== currentTab) {
      clearCache();
    }
  }, [currentTab, clearCache]);

  const handleRefreshAll = useCallback(async () => {
    try {
      await refreshAll();
      setLastUpdated(new Date());
      showSnackbar('Data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing data:', error);
      showSnackbar('Failed to refresh data', 'error');
    }
  }, [refreshAll, showSnackbar]);

  // Get dashboard metrics with safe defaults
  const dashboardMetrics = useMemo(() => {
    const dashboard = data.dashboard || {};
    return {
      openTickets: dashboard.open_tickets || 0,
      overdueTickets: dashboard.overdue_tickets || 0,
      inProgressTickets: dashboard.in_progress_tickets || 0,
      pendingTickets: dashboard.pending_tickets || 0,
      resolvedTickets: dashboard.resolved_tickets || 0,
      escalatedTickets: dashboard.escalated_tickets || 0,
      averageResponseTime: dashboard.average_response_time_hours || 0,
      satisfactionScore: dashboard.customer_satisfaction_score || 0,
      slaCompliance: dashboard.sla_compliance_rate || 0,
      slaBreachesToday: dashboard.sla_breaches_today || 0,
      onlineAgents: dashboard.online_agents || 0,
      totalAgents: dashboard.total_agents || 0,
      agentUtilization: dashboard.agent_utilization_rate || 0,
      ticketsToday: dashboard.tickets_today || 0,
      ticketsThisWeek: dashboard.tickets_this_week || 0,
      resolutionRate: dashboard.resolution_rate || 0,
      firstResponseRate: dashboard.first_response_rate || 0,
    };
  }, [data.dashboard]);

  // Render loading skeleton
  if (loading.dashboard && !data.dashboard) {
    return (
      <StablePageWrapper maxWidth="xl" enableGlassMorphism>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </StablePageWrapper>
    );
  }

  const CurrentTabComponent = tabs[currentTab].component;

  return (
    <StablePageWrapper maxWidth="xl" enableGlassMorphism>
      {/* Header */}
      <Box mb={4}>
        <Typography
          variant="h4"
          gutterBottom
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 700,
          }}
        >
          <SupportIcon sx={{ color: theme.palette.primary.main }} />
          Customer Support System
        </Typography>

        <Typography variant="body1" color="text.secondary" paragraph>
          Comprehensive support management with real-time SLA tracking, analytics, and agent performance monitoring.
        </Typography>

        {/* Quick Action Buttons */}
        <Box display="flex" gap={2} flexWrap="wrap">
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCurrentTab(1)}
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              '&:hover': {
                background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
              },
            }}
          >
            New Ticket
          </Button>
          <Button
            variant="outlined"
            startIcon={<PhoneIcon />}
            onClick={() => setCurrentTab(0)}
          >
            Live Dashboard
          </Button>
          <Button
            variant="outlined"
            startIcon={<AnalyticsIcon />}
            onClick={() => setCurrentTab(3)}
          >
            View Analytics
          </Button>
        </Box>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={handleRefreshAll}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Real-time Status Indicator */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center" gap={2}>
          <Box
            sx={{
              width: 12,
              height: 12,
              borderRadius: '50%',
              backgroundColor: error ? 'error.main' : 'success.main',
              animation: error ? 'none' : 'pulse 2s infinite',
              '@keyframes pulse': {
                '0%': { opacity: 1 },
                '50%': { opacity: 0.5 },
                '100%': { opacity: 1 },
              },
            }}
          />
          <Typography variant="body2" color="text.secondary">
            {error ? 'System Offline' : 'Live Data'} •
            {lastUpdated && ` Updated ${format(lastUpdated, 'HH:mm:ss')}`}
          </Typography>
        </Box>
        <Tooltip title="Refresh All Data">
          <IconButton onClick={handleRefreshAll} disabled={loading.dashboard}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Enhanced Key Metrics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Open Tickets
                  </Typography>
                  <Typography variant="h4" component="div" fontWeight="bold">
                    {formatNumber(dashboardMetrics.openTickets)}
                  </Typography>
                </Box>
                <Badge badgeContent={dashboardMetrics.overdueTickets} color="error">
                  <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), width: 56, height: 56 }}>
                    <TicketsIcon color="primary" sx={{ fontSize: 28 }} />
                  </Avatar>
                </Badge>
              </Box>

              <Box display="flex" alignItems="center" gap={1}>
                {dashboardMetrics.overdueTickets > 0 ? (
                  <Chip
                    icon={<WarningIcon />}
                    label={`${dashboardMetrics.overdueTickets} overdue`}
                    color="warning"
                    size="small"
                  />
                ) : (
                  <Chip
                    icon={<CheckCircleIcon />}
                    label="All on track"
                    color="success"
                    size="small"
                  />
                )}
              </Box>

              <LinearProgress
                variant="determinate"
                value={dashboardMetrics.openTickets > 0 ?
                  ((dashboardMetrics.openTickets - dashboardMetrics.overdueTickets) / dashboardMetrics.openTickets) * 100 : 100}
                sx={{ mt: 2, height: 6, borderRadius: 3 }}
                color={dashboardMetrics.overdueTickets > 0 ? "warning" : "success"}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Avg Response Time
                  </Typography>
                  <Typography variant="h4" component="div" fontWeight="bold">
                    {formatHours(dashboardMetrics.averageResponseTime)}
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), width: 56, height: 56 }}>
                  <TimeIcon color="info" sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                SLA Target: 4-24h
              </Typography>

              <Box display="flex" alignItems="center" gap={1}>
                {dashboardMetrics.averageResponseTime <= 4 ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="warning" fontSize="small" />
                )}
                <Typography variant="caption" color="text.secondary">
                  {dashboardMetrics.averageResponseTime <= 4 ? 'Excellent' : 'Needs Improvement'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Customer Satisfaction
                  </Typography>
                  <Typography variant="h4" component="div" fontWeight="bold">
                    {dashboardMetrics.satisfactionScore.toFixed(1)}/5.0
                  </Typography>
                </Box>
                <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), width: 56, height: 56 }}>
                  <StarIcon color="warning" sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>

              <Box display="flex" alignItems="center" gap={1} mb={1}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <StarIcon
                    key={star}
                    sx={{
                      fontSize: 16,
                      color: star <= dashboardMetrics.satisfactionScore ? 'warning.main' : 'grey.300',
                    }}
                  />
                ))}
              </Box>

              <Chip
                label={dashboardMetrics.satisfactionScore >= 4.0 ? "Excellent" :
                       dashboardMetrics.satisfactionScore >= 3.5 ? "Good" : "Needs Improvement"}
                color={dashboardMetrics.satisfactionScore >= 4.0 ? "success" :
                       dashboardMetrics.satisfactionScore >= 3.5 ? "warning" : "error"}
                size="small"
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    SLA Compliance
                  </Typography>
                  <Typography variant="h4" component="div" fontWeight="bold">
                    {formatPercentage(dashboardMetrics.slaCompliance)}
                  </Typography>
                </Box>
                <Avatar sx={{
                  bgcolor: alpha(
                    dashboardMetrics.slaCompliance >= 0.95 ? theme.palette.success.main : theme.palette.error.main,
                    0.1
                  ),
                  width: 56,
                  height: 56
                }}>
                  <SpeedIcon
                    color={dashboardMetrics.slaCompliance >= 0.95 ? "success" : "error"}
                    sx={{ fontSize: 28 }}
                  />
                </Avatar>
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                {dashboardMetrics.slaBreachesToday} breaches today
              </Typography>

              <LinearProgress
                variant="determinate"
                value={dashboardMetrics.slaCompliance * 100}
                sx={{ mt: 1, height: 6, borderRadius: 3 }}
                color={dashboardMetrics.slaCompliance >= 0.95 ? "success" : "error"}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Enhanced Agent Status and Performance */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card variant="glass">
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                <Typography variant="h6" fontWeight="bold">
                  Agent Status
                </Typography>
                <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 32, height: 32 } }}>
                  {data.agents?.slice(0, 4).map((agent, index) => (
                    <Avatar
                      key={agent.id || index}
                      src={agent.avatar}
                      sx={{
                        bgcolor: theme.palette.primary.main,
                        border: `2px solid ${agent.status === 'online' ? theme.palette.success.main : theme.palette.grey[400]}`,
                      }}
                    >
                      {agent.name?.charAt(0) || 'A'}
                    </Avatar>
                  ))}
                </AvatarGroup>
              </Box>

              <Grid container spacing={2} mb={2}>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: 'success.main',
                      }}
                    />
                    <Typography variant="body2" fontWeight="medium">
                      {dashboardMetrics.onlineAgents} Online
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: 'grey.400',
                      }}
                    />
                    <Typography variant="body2" fontWeight="medium">
                      {dashboardMetrics.totalAgents - dashboardMetrics.onlineAgents} Offline
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  Team Utilization
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {formatPercentage(dashboardMetrics.agentUtilization)}
                </Typography>
              </Box>

              <LinearProgress
                variant="determinate"
                value={dashboardMetrics.agentUtilization * 100}
                sx={{ mt: 1, height: 8, borderRadius: 4 }}
                color={dashboardMetrics.agentUtilization > 0.8 ? "warning" : "primary"}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card variant="glass">
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                <Typography variant="h6" fontWeight="bold">
                  Ticket Distribution
                </Typography>
                <Chip
                  label={`${dashboardMetrics.ticketsToday} today`}
                  color="info"
                  size="small"
                />
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box textAlign="center" p={1}>
                    <Typography variant="h5" color="primary.main" fontWeight="bold">
                      {dashboardMetrics.inProgressTickets}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      In Progress
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={1}>
                    <Typography variant="h5" color="warning.main" fontWeight="bold">
                      {dashboardMetrics.pendingTickets}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Pending
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={1}>
                    <Typography variant="h5" color="success.main" fontWeight="bold">
                      {dashboardMetrics.resolvedTickets}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Resolved
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={1}>
                    <Typography variant="h5" color="error.main" fontWeight="bold">
                      {dashboardMetrics.escalatedTickets}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Escalated
                    </Typography>
                  </Box>
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="body2" color="text.secondary">
                  Resolution Rate
                </Typography>
                <Typography variant="body2" fontWeight="bold" color="success.main">
                  {formatPercentage(dashboardMetrics.resolutionRate)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Enhanced Navigation Tabs */}
      <Card variant="glass" sx={{ mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: 72,
              textTransform: 'none',
              fontWeight: 600,
            },
          }}
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={
                <Box textAlign="center">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                    {tab.icon}
                    {tab.label}
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {tab.description}
                  </Typography>
                </Box>
              }
              value={index}
            />
          ))}
        </Tabs>

        {/* Tab Content */}
        <Box sx={{ p: 3 }}>
          <CurrentTabComponent
            data={data}
            loading={loading}
            error={error}
            onRefresh={handleRefreshAll}
            dashboardData={data.dashboard}
          />
        </Box>
      </Card>

      {/* Floating Action Button for Quick Actions */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          '&:hover': {
            background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
          },
        }}
        onClick={handleRefreshAll}
      >
        <RefreshIcon />
      </Fab>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert
          onClose={closeSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </StablePageWrapper>
  );
};

export default SupportModule;

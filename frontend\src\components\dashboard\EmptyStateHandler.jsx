/**
 * Enhanced EmptyStateHandler Component - Enterprise-grade empty state management
 * Features: Plan-based empty state limitations, real-time user engagement tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced empty state analytics, personalization, and interactive elements
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  CardActions,
  LinearProgress,
  Chip,
  useTheme,
  alpha,
  Stack,
  useMediaQuery,
  Alert,
  Zoom,
  Avatar,
  CircularProgress,
  Grid
} from '@mui/material';
import {
  // Social Media Icons
  Share as SocialIcon,

  // Content Icons
  Article as ContentIcon,

  // Audience Icons
  People as AudienceIcon,
  TrendingUp as GrowthIcon,

  // Image Icons
  Image as ImageIcon,

  // Engagement Icons
  ThumbUp as EngagementIcon,
  Timeline as MetricsIcon,

  // General Icons
  PlayArrow as StartIcon,

  // Enhanced Icons
  Upgrade as UpgradeIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useNotification } from '../../hooks/useNotification';


// Enhanced component imports
import ErrorBoundary from '../common/ErrorBoundary';

/**
 * Enhanced empty state configurations with plan-based features - Production Ready
 */
const EMPTY_STATE_CONFIGS = {
  'social-media-analytics': {
    icon: SocialIcon,
    title: 'Connect Your Social Media Accounts',
    description: 'Connect your social media accounts to view comprehensive analytics and track your performance across platforms.',
    ctaText: 'Connect Accounts',
    ctaPath: '/settings?tab=integrations',
    secondaryCtaText: 'Learn More',
    secondaryCtaPath: '/help/social-media-setup',
    color: 'primary',
    illustration: '📱',
    onboardingStep: 1,
    totalSteps: 4
  },
  
  'content-performance': {
    icon: ContentIcon,
    title: 'Create Your First Post',
    description: 'Start creating and publishing content to see detailed performance metrics, engagement rates, and audience insights.',
    ctaText: 'Create Content',
    ctaPath: '/content/generator',
    secondaryCtaText: 'View Templates',
    secondaryCtaPath: '/content/library?filter=templates',
    color: 'secondary',
    illustration: '✍️',
    onboardingStep: 2,
    totalSteps: 4
  },
  
  'audience-demographics': {
    icon: AudienceIcon,
    title: 'Publish Content to Track Your Audience',
    description: 'Once you start publishing content, we\'ll analyze your audience demographics, interests, and engagement patterns.',
    ctaText: 'Get Started',
    ctaPath: '/content/generator',
    secondaryCtaText: 'Import Content',
    secondaryCtaPath: '/content/library?action=import',
    color: 'info',
    illustration: '👥',
    onboardingStep: 3,
    totalSteps: 4
  },
  
  'image-analytics': {
    icon: ImageIcon,
    title: 'Generate or Upload Images',
    description: 'Create visual content to track image performance, engagement rates, and visual analytics across your campaigns.',
    ctaText: 'Create Images',
    ctaPath: '/content/image-studio',
    secondaryCtaText: 'Upload Images',
    secondaryCtaPath: '/content/image-studio?tab=upload',
    color: 'warning',
    illustration: '🎨',
    onboardingStep: 2,
    totalSteps: 4
  },
  
  'engagement-metrics': {
    icon: EngagementIcon,
    title: 'Start Posting to Track Engagement',
    description: 'Begin publishing content to monitor engagement rates, likes, comments, shares, and audience interaction patterns.',
    ctaText: 'Create First Post',
    ctaPath: '/content/generator',
    secondaryCtaText: 'Schedule Posts',
    secondaryCtaPath: '/scheduling',
    color: 'success',
    illustration: '📈',
    onboardingStep: 2,
    totalSteps: 4
  },
  
  'optimal-times': {
    icon: MetricsIcon,
    title: 'Publish Content to Discover Optimal Times',
    description: 'We need at least 10 posts to analyze your audience behavior and recommend the best times to post.',
    ctaText: 'Start Publishing',
    ctaPath: '/content/generator',
    secondaryCtaText: 'View Schedule',
    secondaryCtaPath: '/scheduling',
    color: 'primary',
    illustration: '⏰',
    onboardingStep: 3,
    totalSteps: 4
  },
  
  'sentiment-analysis': {
    icon: EngagementIcon,
    title: 'Generate Content for Sentiment Analysis',
    description: 'Create and publish content to start tracking sentiment analysis, audience reactions, and emotional engagement.',
    ctaText: 'Create Content',
    ctaPath: '/content/generator',
    secondaryCtaText: 'Analyze Existing',
    secondaryCtaPath: '/analytics/sentiment-analysis',
    color: 'secondary',
    illustration: '😊',
    onboardingStep: 2,
    totalSteps: 4
  },
  
  'competitor-insights': {
    icon: GrowthIcon,
    title: 'Add Competitors to Track Insights',
    description: 'Add competitor accounts to analyze their strategies, content performance, and discover new opportunities.',
    ctaText: 'Add Competitors',
    ctaPath: '/settings?tab=competitors',
    secondaryCtaText: 'Learn More',
    secondaryCtaPath: '/help/competitor-analysis',
    color: 'info',
    illustration: '🔍',
    onboardingStep: 4,
    totalSteps: 4
  },
  
  'quick-actions': {
    icon: StartIcon,
    title: 'Quick Actions Available',
    description: 'Access frequently used features and tools to streamline your content creation and management workflow.',
    ctaText: 'Explore Features',
    ctaPath: '/help/getting-started',
    secondaryCtaText: 'Customize',
    secondaryCtaPath: '/settings?tab=preferences',
    color: 'primary',
    illustration: '⚡',
    onboardingStep: 1,
    totalSteps: 4
  }
};

/**
 * Enhanced EmptyStateHandler Component with Enterprise Features
 */
const EmptyStateHandler = memo(forwardRef(({
  type = 'content-performance',
  customConfig = null,
  variant = 'default',
  minHeight = 300,
  maxHeight = 600,
  enableAnalytics = true,
  enablePlanUpgrade = true,
  showProgress = true,
  showSecondaryAction = true,
  onCtaClick = null,
  onSecondaryCtaClick = null,
  onAnalyticsEvent = null,
  className = '',
  'data-testid': testId = 'empty-state-handler',
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Enhanced context integration
  const {
    usage,
    featureLimits,
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: false,
    interactionCount: 0,
    showCustomizationPanel: false,
    showUpgradeDialog: false,
    personalizedConfig: null,
    analyticsData: {
      views: 0,
      interactions: 0,
      ctaClicks: 0,
      conversionRate: 0
    },
    abTestVariant: 'A',
    lastInteraction: null,
    errors: {}
  });

  // Refs for enhanced functionality
  const emptyStateRef = useRef(null);

  /**
   * Enhanced plan-based empty state analytics validation - Production Ready
   */
  const validateEmptyStateAnalytics = useCallback(() => {
    if (!usage || !featureLimits || !subscription) {
      return {
        canCustomize: false,
        hasAnalyticsAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { monthly: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based empty state analytics limits
    const planLimits = {
      creator: {
        monthly: 15,
        features: ['basic_empty_state_display'],
        customTemplates: 0,
        interactiveElements: false,
        personalization: false,
        abTesting: false
      },
      accelerator: {
        monthly: 75,
        features: ['basic_empty_state_display', 'advanced_empty_state_customization', 'interactive_elements'],
        customTemplates: 3,
        interactiveElements: true,
        personalization: true,
        abTesting: false
      },
      dominator: {
        monthly: Infinity,
        features: ['basic_empty_state_display', 'advanced_empty_state_customization', 'interactive_elements', 'custom_layouts'],
        customTemplates: Infinity,
        interactiveElements: true,
        personalization: true,
        abTesting: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    if (isUnlimited) {
      return {
        canCustomize: true,
        hasAnalyticsAvailable: true,
        remaining: Infinity,
        total: Infinity,
        used: 0,
        isUnlimited: true,
        status: 'unlimited',
        planLimits: currentPlanLimits,
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    // Get current empty state analytics usage
    const analyticsUsed = usage.empty_state_analytics_used || 0;
    const analyticsLimit = currentPlanLimits.monthly;
    const remaining = Math.max(0, analyticsLimit - analyticsUsed);
    const hasAnalyticsAvailable = remaining > 0;
    const canCustomize = hasAnalyticsAvailable && !subscriptionLoading;

    // Calculate status based on usage
    let status = 'excellent';
    const percentage = analyticsLimit > 0 ? (analyticsUsed / analyticsLimit) * 100 : 0;
    if (percentage >= 95) status = 'critical';
    else if (percentage >= 85) status = 'warning';
    else if (percentage >= 70) status = 'caution';
    else if (percentage >= 50) status = 'moderate';
    else if (percentage >= 25) status = 'good';

    // Detect mid-cycle depletion
    const depletionInfo = detectPlanDepletion({
      used: analyticsUsed,
      total: analyticsLimit,
      remaining,
      percentage,
      status
    });

    return {
      canCustomize,
      hasAnalyticsAvailable,
      remaining,
      total: analyticsLimit,
      used: analyticsUsed,
      isUnlimited: false,
      status,
      percentage,
      planLimits: currentPlanLimits,
      depletionInfo
    };
  }, [usage, featureLimits, subscription, subscriptionLoading, detectPlanDepletion]);

  /**
   * Detect plan limit depletion before monthly cycle ends - Production Ready
   */
  const detectPlanDepletion = useCallback((planInfo) => {
    if (!planInfo || !subscription) return { isDepletedMidCycle: false, daysRemaining: 0 };

    const now = new Date();
    const billingCycleStart = new Date(subscription.current_period_start * 1000);
    const billingCycleEnd = new Date(subscription.current_period_end * 1000);

    const totalCycleDays = Math.ceil((billingCycleEnd - billingCycleStart) / (1000 * 60 * 60 * 24));
    const daysElapsed = Math.ceil((now - billingCycleStart) / (1000 * 60 * 60 * 24));
    const daysRemaining = Math.max(0, totalCycleDays - daysElapsed);

    // Consider depleted if no analytics remaining with more than 3 days left in cycle
    const isDepletedMidCycle = planInfo.remaining === 0 && daysRemaining > 3;

    return {
      isDepletedMidCycle,
      daysRemaining,
      cycleProgress: (daysElapsed / totalCycleDays) * 100
    };
  }, [subscription]);

  /**
   * Enhanced feature availability check - Production Ready
   */
  const isFeatureAvailable = useCallback((feature) => {
    const analyticsLimits = validateEmptyStateAnalytics();
    return analyticsLimits.planLimits.features.includes(feature);
  }, [validateEmptyStateAnalytics]);

  /**
   * Enhanced action handlers - Production Ready
   */
  const handleCtaClick = useCallback(async () => {
    setState(prev => ({
      ...prev,
      interactionCount: prev.interactionCount + 1,
      analyticsData: {
        ...prev.analyticsData,
        ctaClicks: prev.analyticsData.ctaClicks + 1,
        interactions: prev.analyticsData.interactions + 1
      },
      lastInteraction: new Date()
    }));

    if (onCtaClick) {
      await onCtaClick(config.ctaPath, type);
    }

    if (enableAnalytics && onAnalyticsEvent) {
      onAnalyticsEvent('empty_state_cta_click', {
        type,
        variant: state.abTestVariant,
        interactionCount: state.interactionCount + 1
      });
    }

    showSuccessNotification('Action completed successfully');
  }, [onCtaClick, config, type, enableAnalytics, onAnalyticsEvent, state.abTestVariant, state.interactionCount, showSuccessNotification]);

  const handleSecondaryCtaClick = useCallback(async () => {
    setState(prev => ({
      ...prev,
      analyticsData: {
        ...prev.analyticsData,
        interactions: prev.analyticsData.interactions + 1
      }
    }));

    if (onSecondaryCtaClick) {
      await onSecondaryCtaClick(config.secondaryCtaPath, type);
    }

    if (enableAnalytics && onAnalyticsEvent) {
      onAnalyticsEvent('empty_state_secondary_cta_click', {
        type,
        variant: state.abTestVariant
      });
    }
  }, [onSecondaryCtaClick, config, type, enableAnalytics, onAnalyticsEvent, state.abTestVariant]);

  const handlePlanUpgrade = useCallback(async () => {
    if (!enablePlanUpgrade) return;

    try {
      setState(prev => ({
        ...prev,
        showUpgradeDialog: true
      }));
    } catch (error) {
      console.error('Error opening upgrade dialog:', error);
      showErrorNotification('Failed to load upgrade options');
    }
  }, [enablePlanUpgrade, showErrorNotification]);



  /**
   * Enhanced effects for analytics and accessibility - Production Ready
   */
  useEffect(() => {
    if (enableAnalytics) {
      setState(prev => ({
        ...prev,
        analyticsData: {
          ...prev.analyticsData,
          views: prev.analyticsData.views + 1
        }
      }));

      if (onAnalyticsEvent) {
        onAnalyticsEvent('empty_state_view', {
          type,
          variant: state.abTestVariant
        });
      }
    }
  }, [enableAnalytics, onAnalyticsEvent, type, state.abTestVariant]);

  /**
   * Expose component methods via ref - Production Ready
   */
  useImperativeHandle(ref, () => ({
    triggerCta: handleCtaClick,
    triggerSecondaryCta: handleSecondaryCtaClick,
    upgradePlan: handlePlanUpgrade,
    getAnalyticsData: () => state.analyticsData,
    getAnalyticsLimits: () => validateEmptyStateAnalytics(),
    focus: () => emptyStateRef.current?.focus(),
    getElement: () => emptyStateRef.current
  }), [
    handleCtaClick,
    handleSecondaryCtaClick,
    handlePlanUpgrade,
    state.analyticsData,
    validateEmptyStateAnalytics
  ]);

  // Memoized calculations
  const analyticsLimits = useMemo(() => validateEmptyStateAnalytics(), [validateEmptyStateAnalytics]);
  const config = useMemo(() => {
    const baseConfig = customConfig || EMPTY_STATE_CONFIGS[type] || EMPTY_STATE_CONFIGS['content-performance'];
    return state.personalizedConfig ? { ...baseConfig, ...state.personalizedConfig } : baseConfig;
  }, [customConfig, type, state.personalizedConfig]);
  const progressPercentage = useMemo(() => (config.onboardingStep / config.totalSteps) * 100, [config.onboardingStep, config.totalSteps]);

  /**
   * Enhanced progress indicator - Production Ready
   */
  const renderProgress = useCallback(() => {
    if (!showProgress || !config.onboardingStep) return null;

    return (
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="caption" color="text.secondary">
            Setup Progress
          </Typography>
          <Chip
            label={`${config.onboardingStep}/${config.totalSteps}`}
            size="small"
            color={config.color}
            variant="outlined"
          />
        </Box>
        <LinearProgress
          variant="determinate"
          value={progressPercentage}
          sx={{
            height: 6,
            borderRadius: 3,
            backgroundColor: alpha(theme.palette[config.color].main, 0.1),
            '& .MuiLinearProgress-bar': {
              borderRadius: 3,
              backgroundColor: theme.palette[config.color].main
            }
          }}
        />
      </Box>
    );
  }, [showProgress, config, progressPercentage, theme]);

  /**
   * Enhanced card styling with ACE Social branding - Production Ready
   */
  const getCardStyles = useCallback(() => {
    return {
      background: `linear-gradient(135deg,
        ${alpha(theme.palette.background.paper, 0.8)} 0%,
        ${alpha(theme.palette.background.paper, 0.6)} 100%)`,
      backdropFilter: 'blur(20px)',
      border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
      borderRadius: theme.spacing(2),
      boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
      overflow: 'hidden',
      minHeight,
      maxHeight: variant === 'compact' ? minHeight : maxHeight,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      p: { xs: 3, sm: 4, md: 5 },
      position: 'relative'
    };
  }, [theme, minHeight, maxHeight, variant]);

  /**
   * Enhanced main content rendering - Production Ready
   */
  const renderContent = useCallback(() => {
    if (state.loading || subscriptionLoading) {
      return (
        <Box sx={getCardStyles()}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Loading...
          </Typography>
        </Box>
      );
    }

    const IconComponent = config.icon;
    const aceColors = {
      primary: '#4E40C5',
      dark: '#15110E',
      yellow: '#EBAE1B',
      white: '#FFFFFF'
    };

    return (
      <Card sx={getCardStyles()}>
        <CardContent sx={{ textAlign: 'center', p: { xs: 3, sm: 4, md: 5 } }}>
          {/* Plan Limitation Warning */}
          {!analyticsLimits.isUnlimited && !analyticsLimits.hasAnalyticsAvailable && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              <Typography variant="body2">
                Empty state analytics limit reached. You have {analyticsLimits.remaining} of {analyticsLimits.total} analytics remaining this month.
                {enablePlanUpgrade && ' Upgrade your plan for more empty state features.'}
              </Typography>
            </Alert>
          )}

          {/* Icon */}
          <Avatar
            sx={{
              width: { xs: 64, sm: 80, md: 96 },
              height: { xs: 64, sm: 80, md: 96 },
              bgcolor: alpha(theme.palette[config.color].main, 0.1),
              color: theme.palette[config.color].main,
              mx: 'auto',
              mb: 3
            }}
          >
            <IconComponent sx={{ fontSize: { xs: 32, sm: 40, md: 48 } }} />
          </Avatar>

          {/* Title */}
          <Typography
            variant={variant === 'compact' ? 'h6' : 'h5'}
            gutterBottom
            sx={{
              fontWeight: 600,
              color: aceColors.dark,
              mb: 2
            }}
          >
            {config.title}
          </Typography>

          {/* Description */}
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{
              mb: 3,
              maxWidth: 400,
              mx: 'auto',
              lineHeight: 1.6
            }}
          >
            {config.description}
          </Typography>

          {/* Progress Indicator */}
          {renderProgress()}

          {/* Analytics Display */}
          {enableAnalytics && isFeatureAvailable('advanced_empty_state_customization') && (
            <Box sx={{ mb: 3, p: 2, bgcolor: alpha(aceColors.primary, 0.05), borderRadius: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Engagement Analytics
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Typography variant="caption" color="text.secondary">Views</Typography>
                  <Typography variant="h6">{state.analyticsData.views}</Typography>
                </Grid>
                <Grid item xs={4}>
                  <Typography variant="caption" color="text.secondary">Interactions</Typography>
                  <Typography variant="h6">{state.analyticsData.interactions}</Typography>
                </Grid>
                <Grid item xs={4}>
                  <Typography variant="caption" color="text.secondary">CTA Clicks</Typography>
                  <Typography variant="h6">{state.analyticsData.ctaClicks}</Typography>
                </Grid>
              </Grid>
            </Box>
          )}
        </CardContent>

        {/* Action Buttons */}
        <CardActions sx={{ justifyContent: 'center', p: 3, pt: 0 }}>
          <Stack spacing={2} direction={isMobile ? 'column' : 'row'} sx={{ width: '100%', maxWidth: 400 }}>
            {/* Primary CTA */}
            <Button
              variant="contained"
              color={config.color}
              size={variant === 'compact' ? 'medium' : 'large'}
              onClick={handleCtaClick}
              fullWidth={isMobile}
              sx={{
                bgcolor: aceColors.primary,
                '&:hover': { bgcolor: '#3d2f9f' },
                fontWeight: 600,
                py: 1.5
              }}
            >
              {config.ctaText}
            </Button>

            {/* Secondary CTA */}
            {showSecondaryAction && config.secondaryCtaText && (
              <Button
                variant="outlined"
                color={config.color}
                size={variant === 'compact' ? 'medium' : 'large'}
                onClick={handleSecondaryCtaClick}
                fullWidth={isMobile}
                sx={{ py: 1.5 }}
              >
                {config.secondaryCtaText}
              </Button>
            )}

            {/* Upgrade Button */}
            {!analyticsLimits.isUnlimited && enablePlanUpgrade && (
              <Button
                variant="contained"
                color="warning"
                size="small"
                startIcon={<UpgradeIcon />}
                onClick={handlePlanUpgrade}
                fullWidth={isMobile}
              >
                Upgrade Plan
              </Button>
            )}
          </Stack>
        </CardActions>
      </Card>
    );
  }, [
    state.loading,
    subscriptionLoading,
    getCardStyles,
    config,
    theme,
    analyticsLimits,
    enablePlanUpgrade,
    variant,
    renderProgress,
    enableAnalytics,
    isFeatureAvailable,
    state.analyticsData,
    isMobile,
    handleCtaClick,
    handleSecondaryCtaClick,
    showSecondaryAction,
    handlePlanUpgrade
  ]);



  return (
    <ErrorBoundary
      fallback={
        <Alert severity="error" sx={{ minHeight, display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2">
            Empty state unavailable
          </Typography>
        </Alert>
      }
    >
      <Zoom in={true} timeout={300}>
        <Box
          ref={emptyStateRef}
          className={className}
          data-testid={testId}
          sx={{ height: '100%', minHeight }}
          {...props}
        >
          {renderContent()}
        </Box>
      </Zoom>
    </ErrorBoundary>
  );
}));

/**
 * Comprehensive PropTypes validation - Production Ready
 */
EmptyStateHandler.propTypes = {
  /** Empty state type */
  type: PropTypes.oneOf([
    'social-media-analytics',
    'content-performance',
    'audience-demographics',
    'image-analytics',
    'engagement-metrics',
    'optimal-times',
    'sentiment-analysis',
    'competitor-insights',
    'quick-actions'
  ]),

  /** Custom configuration object */
  customConfig: PropTypes.object,

  /** Visual variant of the component */
  variant: PropTypes.oneOf(['default', 'compact', 'detailed']),

  /** Minimum height of the component */
  minHeight: PropTypes.number,

  /** Maximum height of the component */
  maxHeight: PropTypes.number,

  /** Enable analytics functionality */
  enableAnalytics: PropTypes.bool,

  /** Enable personalization functionality */
  enablePersonalization: PropTypes.bool,

  /** Enable interactivity functionality */
  enableInteractivity: PropTypes.bool,

  /** Enable plan upgrade functionality */
  enablePlanUpgrade: PropTypes.bool,

  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,

  /** Enable A/B testing */
  enableABTesting: PropTypes.bool,

  /** Whether to show progress indicator */
  showProgress: PropTypes.bool,

  /** Whether to show secondary action */
  showSecondaryAction: PropTypes.bool,

  /** Whether to show customization options */
  showCustomization: PropTypes.bool,

  /** Callback when CTA is clicked */
  onCtaClick: PropTypes.func,

  /** Callback when secondary CTA is clicked */
  onSecondaryCtaClick: PropTypes.func,

  /** Callback when analytics event occurs */
  onAnalyticsEvent: PropTypes.func,

  /** Callback when personalization is updated */
  onPersonalizationUpdate: PropTypes.func,

  /** Additional CSS class name */
  className: PropTypes.string,

  /** Test ID for testing */
  'data-testid': PropTypes.string
};

/**
 * Default props with enterprise-grade defaults - Production Ready
 */
EmptyStateHandler.defaultProps = {
  type: 'content-performance',
  customConfig: null,
  variant: 'default',
  minHeight: 300,
  maxHeight: 600,
  enableAnalytics: true,
  enablePersonalization: true,
  enableInteractivity: true,
  enablePlanUpgrade: true,
  enableAccessibility: true,
  enableABTesting: false,
  showProgress: true,
  showSecondaryAction: true,
  showCustomization: false,
  onCtaClick: null,
  onSecondaryCtaClick: null,
  onAnalyticsEvent: null,
  onPersonalizationUpdate: null,
  className: '',
  'data-testid': 'empty-state-handler'
};

/**
 * Display name for debugging - Production Ready
 */
EmptyStateHandler.displayName = 'EmptyStateHandler';

export default EmptyStateHandler;

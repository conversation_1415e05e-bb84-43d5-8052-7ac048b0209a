/**
 * Enhanced Bulk Action Dialog - Enterprise-grade bulk action management component
 * Features: Comprehensive bulk action management, real-time bulk processing monitoring, plan-based limitations,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced bulk capabilities and ACEO add-on marketplace integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  FormControl,
  FormControlLabel,
  RadioGroup,
  Radio,
  TextField,
  MenuItem,
  Select,
  InputLabel,
  CircularProgress,
  Alert,
  alpha,
  Snackbar,
  useMediaQuery,
  Chip,
  LinearProgress
} from '@mui/material';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  DeleteOutline as DeleteOutlineIcon,
  Schedule as ScheduleIcon,
  Edit as EditIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import { useAddons } from '../../hooks/useAddons';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Bulk action display modes with enhanced configurations
const BULK_ACTION_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Bulk Actions',
    description: 'Basic bulk operations interface',
    features: ['basic_bulk', 'bulk_analytics', 'bulk_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Bulk Management',
    description: 'Comprehensive bulk operations management',
    features: ['detailed_bulk', 'bulk_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Bulk Operations',
    description: 'AI-powered bulk optimization and suggestions',
    features: ['ai_assisted', 'ai_optimization', 'bulk_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Bulk Dashboard',
    description: 'Advanced bulk analytics and forecasting',
    features: ['analytics_bulk', 'bulk_insights']
  }
};

/**
 * Enhanced Bulk Action Dialog Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {boolean} props.open - Dialog open state
 * @param {Function} props.onClose - Close callback
 * @param {string} [props.actionType] - Type of bulk action
 * @param {Array} props.selectedItems - Selected items for bulk action
 * @param {Function} props.onConfirm - Confirm callback
 * @param {boolean} [props.loading=false] - Loading state
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onBulkAction] - Bulk action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-bulk-action-dialog'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const BulkActionDialog = memo(forwardRef(({
  open,
  onClose,
  actionType = null,
  selectedItems,
  onConfirm,
  loading = false,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onBulkAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-bulk-action-dialog',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const { getRelevantAddons } = useAddons();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const bulkDialogRef = useRef(null);
  const [rescheduleDate, setRescheduleDate] = useState(new Date());
  const [rescheduleOption, setRescheduleOption] = useState("sameTime");
  const [customTime, setCustomTime] = useState("09:00");
  const [newStatus, setNewStatus] = useState("scheduled");

  // Enhanced state management
  const [bulkMode, setBulkMode] = useState('compact');
  const [bulkHistory, setBulkHistory] = useState([]);
  const [bulkAnalytics, setBulkAnalytics] = useState(null);
  const [bulkInsights, setBulkInsights] = useState(null);
  const [customBulkConfigs, setCustomBulkConfigs] = useState([]);
  const [bulkPreferences, setBulkPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    bulkSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [bulkDrawerOpen, setBulkDrawerOpen] = useState(false);
  const [selectedBulkType, setSelectedBulkType] = useState(null);
  const [bulkStats, setBulkStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [bulkScore, setBulkScore] = useState(0);
  const [bulkProgress, setBulkProgress] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [bulkVersions, setBulkVersions] = useState([]);
  const [processingItems, setProcessingItems] = useState(0);
  const [conflictDetection, setConflictDetection] = useState(null);
  const [optimizationSuggestions, setOptimizationSuggestions] = useState([]);

  /**
   * ACE Social subscription integration with plan-based bulk scheduling limitations - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // ACE Social platform plans with realistic bulk scheduling limitations
    const features = {
      creator: {
        maxBulkItems: 10,
        maxDailyBulkOperations: 5,
        bulkCostMultiplier: 1.0,
        hasAdvancedBulk: false,
        hasBulkAnalytics: false,
        hasCustomBulkConfigs: false,
        hasBulkInsights: false,
        hasBulkHistory: true,
        hasAIAssistance: false,
        hasBulkExport: false,
        hasBulkScheduling: false,
        hasBulkAutomation: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1,
        allowedBulkTypes: ['basic_delete', 'basic_reschedule', 'basic_status'],
        maxHistoryDays: 7,
        canPurchaseAddons: true,
        addonDiscountPercent: 0,
        hasConflictDetection: false,
        hasOptimizationSuggestions: false
      },
      accelerator: {
        maxBulkItems: 50,
        maxDailyBulkOperations: 25,
        bulkCostMultiplier: 0.8,
        hasAdvancedBulk: true,
        hasBulkAnalytics: true,
        hasCustomBulkConfigs: false,
        hasBulkInsights: true,
        hasBulkHistory: true,
        hasAIAssistance: true,
        hasBulkExport: true,
        hasBulkScheduling: true,
        hasBulkAutomation: false,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 2000,
        planName: 'Accelerator',
        planTier: 2,
        allowedBulkTypes: ['basic_delete', 'basic_reschedule', 'basic_status', 'advanced_reschedule', 'bulk_duplicate', 'bulk_template'],
        maxHistoryDays: 30,
        canPurchaseAddons: true,
        addonDiscountPercent: 10,
        hasConflictDetection: true,
        hasOptimizationSuggestions: true
      },
      dominator: {
        maxBulkItems: -1, // Unlimited
        maxDailyBulkOperations: -1, // Unlimited
        bulkCostMultiplier: 0.5,
        hasAdvancedBulk: true,
        hasBulkAnalytics: true,
        hasCustomBulkConfigs: true,
        hasBulkInsights: true,
        hasBulkHistory: true,
        hasAIAssistance: true,
        hasBulkExport: true,
        hasBulkScheduling: true,
        hasBulkAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3,
        allowedBulkTypes: ['basic_delete', 'basic_reschedule', 'basic_status', 'advanced_reschedule', 'bulk_duplicate', 'bulk_template', 'smart_reschedule', 'bulk_automation', 'custom_bulk'],
        maxHistoryDays: -1, // Unlimited
        canPurchaseAddons: true,
        addonDiscountPercent: 20,
        hasConflictDetection: true,
        hasOptimizationSuggestions: true
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxBulkItems === -1 || currentUsage < currentFeatures.maxBulkItems;
        return hasAccess && withinLimits;
      },
      getBulkCost: (baseCredits) => {
        return Math.ceil(baseCredits * currentFeatures.bulkCostMultiplier);
      },
      canUseBulkType: (type) => {
        return currentFeatures.allowedBulkTypes.includes(type);
      },
      getAddonDiscount: (originalPrice) => {
        return originalPrice * (1 - currentFeatures.addonDiscountPercent / 100);
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'dialog',
      'aria-label': ariaLabel || `Bulk scheduling dialog with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Bulk operations interface with ${subscriptionFeatures.trackingLevel} tracking`,
      'aria-modal': 'true',
      tabIndex: -1
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  // Check if user can perform bulk operation based on plan
  const canPerformBulkOperation = useMemo(() => {
    const withinItemLimit = subscriptionFeatures.isWithinLimits(selectedItems.length, subscriptionFeatures.maxBulkItems);
    const hasBulkType = subscriptionFeatures.canUseBulkType(`basic_${actionType}`);

    return withinItemLimit && hasBulkType;
  }, [selectedItems.length, subscriptionFeatures, actionType]);

  // Get limitation message for UI
  const getLimitationMessage = useCallback(() => {
    if (!subscriptionFeatures.canUseBulkType(`basic_${actionType}`)) {
      return `${actionType} bulk operation requires ${subscriptionFeatures.planTier < 2 ? 'Accelerator' : 'Dominator'} plan or higher`;
    }

    if (!subscriptionFeatures.isWithinLimits(selectedItems.length, subscriptionFeatures.maxBulkItems)) {
      return `Bulk operation limit exceeded (${subscriptionFeatures.maxBulkItems} items max). Upgrade to ${subscriptionFeatures.planTier < 3 ? 'Dominator' : 'higher'} plan for more items`;
    }

    return null;
  }, [subscriptionFeatures, actionType, selectedItems.length]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive bulk API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getBulkHistory: () => bulkHistory,
    getBulkAnalytics: () => bulkAnalytics,
    getBulkInsights: () => bulkInsights,
    refreshBulk: () => {
      fetchBulkAnalytics();
      if (onRefresh) onRefresh();
    },

    // Bulk methods
    focusBulk: () => {
      if (bulkDialogRef.current) {
        bulkDialogRef.current.focus();
      }
    },
    getBulkScore: () => bulkScore,
    getBulkProgress: () => bulkProgress,
    getProcessingItems: () => processingItems,
    getConflictDetection: () => conflictDetection,
    getOptimizationSuggestions: () => optimizationSuggestions,
    openBulkDrawer: () => setBulkDrawerOpen(true),
    closeBulkDrawer: () => setBulkDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportBulkData: () => {
      if (onExport) {
        onExport(bulkHistory, bulkAnalytics);
      }
    },

    // Accessibility methods
    announceBulk: (message) => announceToScreenReader(message),
    focusBulkField: () => setFocusToElement('bulk-dialog-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => bulkMode,
    getBulkStats: () => bulkStats,
    getSelectedBulkType: () => selectedBulkType,
    getCustomBulkConfigs: () => customBulkConfigs,
    getBulkDrawerOpen: () => bulkDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    getActiveTab: () => activeTab,
    setActiveTab: (tab) => setActiveTab(tab),
    addCustomBulkConfig,
    handleBulkModeChange,
    updateBulkPreferences,
    handleBulkTypeSelection,
    validateBulkConfig,
    getBulkVersions: () => bulkVersions,
    switchToVersion: (versionId) => switchToBulkVersion(versionId),
    detectConflicts: () => handleConflictDetection(),
    optimizeScheduling: () => handleSchedulingOptimization()
  }), [
    bulkHistory,
    bulkAnalytics,
    bulkInsights,
    bulkStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    bulkMode,
    selectedBulkType,
    customBulkConfigs,
    bulkScore,
    bulkProgress,
    processingItems,
    conflictDetection,
    optimizationSuggestions,
    bulkVersions,
    addCustomBulkConfig,
    handleBulkModeChange,
    updateBulkPreferences,
    handleBulkTypeSelection,
    validateBulkConfig,
    switchToBulkVersion,
    handleConflictDetection,
    handleSchedulingOptimization,
    activeTab,
    fullscreenMode,
    bulkDrawerOpen,
    showAnalytics,
    fetchBulkAnalytics
  ]);

  // Fetch bulk analytics with enhanced error handling and retry logic
  const fetchBulkAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/bulk/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setBulkAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (bulkPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Bulk analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch bulk analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load bulk analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, bulkPreferences.showAnalytics]);

  // Handle bulk mode switching
  const handleBulkModeChange = useCallback((newMode) => {
    if (BULK_ACTION_MODES[newMode.toUpperCase()]) {
      setBulkMode(newMode);
      announceToScreenReader(`Bulk mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setBulkHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (bulkPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} bulk mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, bulkPreferences.showAnalytics, showSuccess]);

  // Handle custom bulk config management
  const addCustomBulkConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomBulkConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setBulkHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (bulkPreferences.showAnalytics) {
      showSuccess(`Custom bulk config "${configData.name}" created`);
    }
  }, [subscription?.user_id, bulkPreferences.showAnalytics, showSuccess]);

  // Handle bulk preferences updates
  const updateBulkPreferences = useCallback((newPreferences) => {
    setBulkPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setBulkHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (bulkPreferences.showAnalytics) {
      showSuccess('Bulk preferences updated');
    }
  }, [subscription?.user_id, bulkPreferences.showAnalytics, showSuccess]);

  // Handle bulk type selection
  const handleBulkTypeSelection = useCallback((bulkType) => {
    setSelectedBulkType(bulkType);

    // Track bulk type selection
    const typeRecord = {
      id: Date.now(),
      type: 'bulk_type_selected',
      bulkType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setBulkHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (bulkPreferences.showAnalytics) {
      announceToScreenReader(`Selected bulk type: ${bulkType}`);
    }
  }, [subscription?.user_id, bulkPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateBulkConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Bulk type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Switch to bulk version
  const switchToBulkVersion = useCallback((versionId) => {
    const version = bulkVersions.find(v => v.id === versionId);
    if (version) {
      setBulkScore(version.score || 0);

      if (bulkPreferences.showAnalytics) {
        showSuccess(`Switched to version ${version.name}`);
      }
    }
  }, [bulkVersions, bulkPreferences.showAnalytics, showSuccess]);

  // Handle conflict detection
  const handleConflictDetection = useCallback(async () => {
    if (!subscriptionFeatures.hasConflictDetection) {
      showError('Conflict detection requires Accelerator plan or higher');
      return;
    }

    try {
      const response = await api.post('/api/scheduling/detect-conflicts', {
        items: selectedItems,
        actionType,
        rescheduleDate: actionType === 'reschedule' ? rescheduleDate : null
      });

      setConflictDetection(response.data);

      if (response.data.conflicts?.length > 0) {
        showError(`${response.data.conflicts.length} scheduling conflicts detected`);
      } else {
        showSuccess('No scheduling conflicts detected');
      }
    } catch (error) {
      console.error('Conflict detection failed:', error);
      showError('Failed to detect conflicts');
    }
  }, [subscriptionFeatures.hasConflictDetection, selectedItems, actionType, rescheduleDate, showError, showSuccess]);

  // Handle scheduling optimization
  const handleSchedulingOptimization = useCallback(async () => {
    if (!subscriptionFeatures.hasOptimizationSuggestions) {
      showError('Scheduling optimization requires Accelerator plan or higher');
      return;
    }

    try {
      const response = await api.post('/api/scheduling/optimize', {
        items: selectedItems,
        actionType,
        preferences: bulkPreferences
      });

      setOptimizationSuggestions(response.data.suggestions || []);

      if (response.data.suggestions?.length > 0) {
        showSuccess(`${response.data.suggestions.length} optimization suggestions generated`);
      } else {
        showSuccess('Current scheduling is already optimized');
      }
    } catch (error) {
      console.error('Optimization failed:', error);
      showError('Failed to generate optimization suggestions');
    }
  }, [subscriptionFeatures.hasOptimizationSuggestions, selectedItems, actionType, bulkPreferences, showError, showSuccess]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && selectedItems.length > 0) {
      // Optimize bulk operations based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchBulkAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, selectedItems.length, fetchBulkAnalytics]);

  // Generate AI suggestions when bulk changes
  useEffect(() => {
    if (enableAIInsights && bulkPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, bulkPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      setBulkProgress(25); // Start progress
      setProcessingItems(1);

      const response = await api.get('/api/bulk/ai-suggestions', {
        params: {
          actionType,
          itemCount: selectedItems.length,
          planTier: subscriptionFeatures.planTier
        }
      });

      setBulkProgress(75); // Update progress
      setAiSuggestions(response.data.suggestions || []);
      setBulkInsights(response.data.insights || null);

      // Create a new version for this suggestion set
      const newVersion = {
        id: Date.now(),
        name: `AI Suggestions v${Date.now()}`,
        suggestions: response.data.suggestions || [],
        createdAt: new Date().toISOString()
      };
      setBulkVersions(prev => [newVersion, ...prev.slice(0, 4)]); // Keep last 5 versions

      setBulkProgress(100); // Complete progress
      setProcessingItems(0);

      if (bulkPreferences.showAnalytics) {
        showSuccess('AI bulk suggestions generated');
      }

      // Reset progress after delay
      setTimeout(() => setBulkProgress(0), 2000);
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
      setBulkProgress(0);
      setProcessingItems(0);
    }
  }, [actionType, selectedItems.length, subscriptionFeatures.planTier, bulkPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when bulk changes
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchBulkStats();
    }
  }, [enableAdvancedFeatures, fetchBulkStats]);

  // Fetch bulk stats function
  const fetchBulkStats = useCallback(async () => {
    try {
      const response = await api.get('/api/bulk/stats');
      setBulkStats(response.data);
    } catch (error) {
      console.error('Failed to fetch bulk stats:', error);
    }
  }, []);

  // Get dialog title and content based on action type
  const getDialogTitle = useCallback(() => {
    switch (actionType) {
      case "delete":
        return "Delete Selected Items";
      case "reschedule":
        return "Reschedule Selected Items";
      case "status":
        return "Change Status for Selected Items";
      default:
        return "Bulk Action";
    }
  }, [actionType]);

  // Get dialog icon based on action type
  const getDialogIcon = useCallback(() => {
    switch (actionType) {
      case "delete":
        return (
          <DeleteOutlineIcon sx={{ color: ACE_COLORS.DARK, mr: 1 }} />
        );
      case "reschedule":
        return (
          <ScheduleIcon sx={{ color: ACE_COLORS.PURPLE, mr: 1 }} />
        );
      case "status":
        return <EditIcon sx={{ color: ACE_COLORS.YELLOW, mr: 1 }} />;
      default:
        return null;
    }
  }, [actionType]);

  // Handle confirm action
  const handleConfirm = useCallback(async () => {
    let actionData = {};

    if (actionType === "reschedule") {
      // Create a date object with the selected date and time
      const date = new Date(rescheduleDate);

      if (rescheduleOption === "customTime") {
        // Parse the custom time (HH:MM)
        const [hours, minutes] = customTime.split(":").map(Number);
        date.setHours(hours, minutes, 0, 0);
      }

      actionData = {
        date,
        preserveTime: rescheduleOption === "sameTime",
      };
    } else if (actionType === "status") {
      actionData = {
        status: newStatus,
      };
    }

    // Track bulk action in history
    const actionRecord = {
      id: Date.now(),
      type: 'bulk_action_executed',
      actionType,
      itemCount: selectedItems.length,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id,
      planTier: subscriptionFeatures.planTier
    };

    setBulkHistory(prev => [actionRecord, ...prev.slice(0, 99)]);

    // Call the bulk action callback if provided
    if (onBulkAction) {
      try {
        await onBulkAction(actionType, actionData, selectedItems);
      } catch (error) {
        console.error('Bulk action callback failed:', error);
        showError('Bulk action callback failed');
      }
    }

    // Get relevant addons for upselling if needed
    if (selectedItems.length > subscriptionFeatures.maxBulkItems && subscriptionFeatures.maxBulkItems !== -1) {
      try {
        const relevantAddons = await getRelevantAddons('bulk_operations');
        if (relevantAddons.length > 0) {
          showError(`Bulk limit exceeded. Consider upgrading or purchasing bulk add-ons.`);
        }
      } catch (error) {
        console.error('Failed to get relevant addons:', error);
      }
    }

    onConfirm(actionType, actionData);
  }, [
    actionType,
    rescheduleDate,
    rescheduleOption,
    customTime,
    newStatus,
    selectedItems,
    subscription?.user_id,
    subscriptionFeatures.planTier,
    subscriptionFeatures.maxBulkItems,
    onBulkAction,
    getRelevantAddons,
    onConfirm,
    showError
  ]);

  return (
    <Box
      {...getAccessibilityProps()}
      ref={bulkDialogRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth={isMobile ? "xs" : "sm"}
        fullWidth
        fullScreen={fullscreenMode || isMobile}
        slotProps={{
          paper: {
            sx: {
              backgroundColor: alpha(ACE_COLORS.WHITE, 0.95),
              backdropFilter: "blur(20px)",
              borderRadius: fullscreenMode ? 0 : 2,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
            }
          }
        }}
      >
      <DialogTitle>
        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            {getDialogIcon()}
            <Typography variant="h6">{getDialogTitle()}</Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Chip
              label={subscriptionFeatures.planName}
              size="small"
              sx={{
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                fontWeight: 'bold'
              }}
            />
            {subscriptionFeatures.maxBulkItems !== -1 && (
              <Typography variant="caption" color="text.secondary">
                {subscriptionFeatures.maxBulkItems - selectedItems.length} items remaining
              </Typography>
            )}
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Selected Items: {selectedItems.length}
          </Typography>

          {/* Plan limitation warning */}
          {!canPerformBulkOperation && (
            <Alert
              severity="warning"
              icon={<WarningIcon />}
              sx={{
                mb: 2,
                backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                "& .MuiAlert-icon": {
                  color: ACE_COLORS.YELLOW,
                },
              }}
            >
              {getLimitationMessage()}
            </Alert>
          )}

          {actionType === "delete" && (
            <Alert
              severity="warning"
              icon={<WarningIcon />}
              sx={{
                mb: 2,
                backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                "& .MuiAlert-icon": {
                  color: ACE_COLORS.YELLOW,
                },
              }}
            >
              This action cannot be undone. All selected items will be
              permanently deleted.
            </Alert>
          )}

          {actionType === "reschedule" && (
            <Box sx={{ mt: 2 }}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="New Date"
                  value={rescheduleDate}
                  onChange={(newDate) => setRescheduleDate(newDate)}
                  disablePast
                  sx={{ width: "100%", mb: 2 }}
                />
              </LocalizationProvider>

              <FormControl component="fieldset" sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Time Options
                </Typography>

                <RadioGroup
                  value={rescheduleOption}
                  onChange={(e) => setRescheduleOption(e.target.value)}
                >
                  <FormControlLabel
                    value="sameTime"
                    control={<Radio />}
                    label="Keep original time for each post"
                  />
                  <FormControlLabel
                    value="customTime"
                    control={<Radio />}
                    label="Set specific time for all posts"
                  />
                </RadioGroup>
              </FormControl>

              {rescheduleOption === "customTime" && (
                <TextField
                  label="Time"
                  type="time"
                  value={customTime}
                  onChange={(e) => setCustomTime(e.target.value)}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  inputProps={{
                    step: 300, // 5 min
                  }}
                  sx={{ mt: 2, width: 150 }}
                />
              )}

              <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                {rescheduleOption === "sameTime"
                  ? `Posts will be moved to ${format(
                      rescheduleDate,
                      "EEEE, MMMM d, yyyy"
                    )} while keeping their original times.`
                  : `All posts will be scheduled for ${format(
                      rescheduleDate,
                      "EEEE, MMMM d, yyyy"
                    )} at ${customTime}.`}
              </Typography>
            </Box>
          )}

          {actionType === "status" && (
            <Box sx={{ mt: 2 }}>
              <FormControl fullWidth>
                <InputLabel id="status-select-label">New Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  value={newStatus}
                  label="New Status"
                  onChange={(e) => setNewStatus(e.target.value)}
                >
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="scheduled">Scheduled</MenuItem>
                  <MenuItem value="published">Published</MenuItem>
                </Select>
              </FormControl>

              <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                All selected posts will be updated to &ldquo;{newStatus}&rdquo; status.
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color={actionType === "delete" ? "error" : "primary"}
          onClick={handleConfirm}
          disabled={loading || !canPerformBulkOperation}
          startIcon={loading ? <CircularProgress size={20} /> : null}
          sx={{
            bgcolor: actionType === "delete" ? ACE_COLORS.DARK : ACE_COLORS.PURPLE,
            '&:hover': {
              bgcolor: actionType === "delete" ? alpha(ACE_COLORS.DARK, 0.8) : alpha(ACE_COLORS.PURPLE, 0.8)
            },
            '&:disabled': {
              bgcolor: alpha(ACE_COLORS.PURPLE, 0.3),
              color: alpha(ACE_COLORS.WHITE, 0.5)
            }
          }}
        >
          {actionType === "delete" ? "Delete" : "Apply"}
        </Button>
      </DialogActions>
    </Dialog>

    {/* Notification Snackbar */}
    <Snackbar
      open={notification.open}
      autoHideDuration={6000}
      onClose={() => setNotification(prev => ({ ...prev, open: false }))}
      anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
    >
      <Alert
        severity={notification.severity}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        sx={{
          bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
          color: ACE_COLORS.DARK,
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
        }}
      >
        {notification.message}
      </Alert>
    </Snackbar>

    {/* Retry Count Indicator */}
    {retryCount > 0 && (
      <Box sx={{
        position: 'fixed',
        top: 20,
        right: 20,
        p: 1,
        borderRadius: 1,
        bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
        border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
        zIndex: 9999
      }}>
        <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
          Retrying bulk sync... (Attempt {retryCount}/3)
        </Typography>
      </Box>
    )}

    {/* Bulk Progress Indicator */}
    {bulkProgress > 0 && bulkProgress < 100 && (
      <Box sx={{
        position: 'fixed',
        bottom: 20,
        left: '50%',
        transform: 'translateX(-50%)',
        p: 2,
        borderRadius: 2,
        bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        zIndex: 9999,
        minWidth: 200
      }}>
        <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
          Processing bulk operation... ({processingItems}/{selectedItems.length} items)
        </Typography>
        <LinearProgress
          variant="determinate"
          value={bulkProgress}
          sx={{
            '& .MuiLinearProgress-bar': {
              backgroundColor: ACE_COLORS.PURPLE
            }
          }}
        />
      </Box>
    )}
  </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
BulkActionDialog.propTypes = {
  // Core props
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  actionType: PropTypes.oneOf(["delete", "reschedule", "status"]).isRequired,
  selectedItems: PropTypes.array.isRequired,
  onConfirm: PropTypes.func.isRequired,
  loading: PropTypes.bool,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onBulkAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

BulkActionDialog.displayName = 'BulkActionDialog';

export default BulkActionDialog;

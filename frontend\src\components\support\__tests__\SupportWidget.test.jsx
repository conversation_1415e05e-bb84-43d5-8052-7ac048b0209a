// @since 2024-1-1 to 2025-25-7
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { createAppTheme } from '../../../config/theme';
import SupportWidget from '../SupportWidget';

// Mock hooks
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      full_name: 'Test User',
    },
  }),
}));

jest.mock('../../../hooks/useSubscription', () => ({
  useSubscription: () => ({
    subscription: {
      plan_id: 'accelerator',
    },
  }),
}));

jest.mock('../../../hooks/useSupportWidget', () => ({
  __esModule: true,
  default: () => ({
    tickets: [],
    unreadCount: 0,
    loading: false,
    error: null,
    connectWebSocket: jest.fn(),
    disconnectWebSocket: jest.fn(),
    markAsRead: jest.fn(),
  }),
}));

const theme = createAppTheme();

const renderWithTheme = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('SupportWidget', () => {
  test('renders support button', () => {
    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    expect(supportButton).toBeInTheDocument();
  });

  test('opens support dialog when button is clicked', async () => {
    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    fireEvent.click(supportButton);
    
    await waitFor(() => {
      expect(screen.getByText('Support Center')).toBeInTheDocument();
    });
  });

  test('displays correct SLA information for accelerator plan', () => {
    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    fireEvent.click(supportButton);
    
    expect(screen.getByText(/Accelerator Plan/)).toBeInTheDocument();
    expect(screen.getByText(/12h Response Time/)).toBeInTheDocument();
  });

  test('closes dialog when close button is clicked', async () => {
    renderWithTheme(<SupportWidget />);
    
    // Open dialog
    const supportButton = screen.getByRole('button', { name: /support/i });
    fireEvent.click(supportButton);
    
    await waitFor(() => {
      expect(screen.getByText('Support Center')).toBeInTheDocument();
    });
    
    // Close dialog
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Support Center')).not.toBeInTheDocument();
    });
  });

  test('does not render when user is not authenticated', () => {
    // Mock unauthenticated state
    jest.doMock('../../../hooks/useAuth', () => ({
      useAuth: () => ({
        user: null,
      }),
    }));

    const { container } = renderWithTheme(<SupportWidget />);
    expect(container.firstChild).toBeNull();
  });
});

describe('SupportWidget Accessibility', () => {
  test('support button has proper aria-label', () => {
    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    expect(supportButton).toHaveAttribute('aria-label', 'support');
  });

  test('dialog has proper accessibility attributes', async () => {
    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    fireEvent.click(supportButton);
    
    await waitFor(() => {
      const dialog = screen.getByRole('dialog');
      expect(dialog).toBeInTheDocument();
      expect(dialog).toHaveAttribute('aria-labelledby');
    });
  });

  test('keyboard navigation works correctly', async () => {
    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    
    // Focus the button
    supportButton.focus();
    expect(document.activeElement).toBe(supportButton);
    
    // Press Enter to open dialog
    fireEvent.keyDown(supportButton, { key: 'Enter', code: 'Enter' });
    
    await waitFor(() => {
      expect(screen.getByText('Support Center')).toBeInTheDocument();
    });
  });
});

describe('SupportWidget Responsive Design', () => {
  test('renders correctly on mobile viewport', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });
    
    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    expect(supportButton).toBeInTheDocument();
  });

  test('renders correctly on desktop viewport', () => {
    // Mock desktop viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });
    
    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    expect(supportButton).toBeInTheDocument();
  });
});

describe('SupportWidget Integration', () => {
  test('displays unread count badge when there are unread tickets', () => {
    // Mock hook with unread count
    jest.doMock('../../../hooks/useSupportWidget', () => ({
      __esModule: true,
      default: () => ({
        tickets: [],
        unreadCount: 3,
        loading: false,
        error: null,
        connectWebSocket: jest.fn(),
        disconnectWebSocket: jest.fn(),
        markAsRead: jest.fn(),
      }),
    }));

    renderWithTheme(<SupportWidget />);
    
    // Badge should show unread count
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  test('handles WebSocket connection on mount', () => {
    const mockConnectWebSocket = jest.fn();
    const mockDisconnectWebSocket = jest.fn();
    
    jest.doMock('../../../hooks/useSupportWidget', () => ({
      __esModule: true,
      default: () => ({
        tickets: [],
        unreadCount: 0,
        loading: false,
        error: null,
        connectWebSocket: mockConnectWebSocket,
        disconnectWebSocket: mockDisconnectWebSocket,
        markAsRead: jest.fn(),
      }),
    }));

    const { unmount } = renderWithTheme(<SupportWidget />);
    
    // Should connect WebSocket on mount
    expect(mockConnectWebSocket).toHaveBeenCalled();
    
    // Should disconnect WebSocket on unmount
    unmount();
    expect(mockDisconnectWebSocket).toHaveBeenCalled();
  });
});

describe('SupportWidget Error Handling', () => {
  test('handles loading state correctly', () => {
    jest.doMock('../../../hooks/useSupportWidget', () => ({
      __esModule: true,
      default: () => ({
        tickets: [],
        unreadCount: 0,
        loading: true,
        error: null,
        connectWebSocket: jest.fn(),
        disconnectWebSocket: jest.fn(),
        markAsRead: jest.fn(),
      }),
    }));

    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    fireEvent.click(supportButton);
    
    // Should show loading indicator
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('displays error message when there is an error', async () => {
    jest.doMock('../../../hooks/useSupportWidget', () => ({
      __esModule: true,
      default: () => ({
        tickets: [],
        unreadCount: 0,
        loading: false,
        error: 'Failed to load support data',
        connectWebSocket: jest.fn(),
        disconnectWebSocket: jest.fn(),
        markAsRead: jest.fn(),
      }),
    }));

    renderWithTheme(<SupportWidget />);
    
    const supportButton = screen.getByRole('button', { name: /support/i });
    fireEvent.click(supportButton);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to load support data')).toBeInTheDocument();
    });
  });
});

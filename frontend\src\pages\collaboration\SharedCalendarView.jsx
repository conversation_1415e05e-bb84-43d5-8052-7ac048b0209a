// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Avatar,
  CircularProgress,
  Divider,
  Alert,
  AlertTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import {
  CalendarMonth as CalendarIcon,
  AccessTime as AccessTimeIcon,
  Comment as CommentIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Send as SendIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Edit as EditIcon,
  Image as ImageIcon,
  Group as GroupIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useSnackbar } from '../../contexts/SnackbarContext';
// import { useAuth } from '../../contexts/AuthContext';
import { CollaborationProvider } from '../../contexts/CollaborationContext';
import { accessSharedCalendar, reviewContent } from '../../api/collaboration';
import ImageAnnotationTool from '../../components/collaboration/ImageAnnotationTool';
import LiveCursorTracker from '../../components/collaboration/LiveCursorTracker';
import ChangeTracker from '../../components/collaboration/ChangeTracker';

const SharedCalendarView = () => {
  return (
    <CollaborationProvider>
      <SharedCalendarViewContent />
    </CollaborationProvider>
  );
};

const SharedCalendarViewContent = () => {
  const { accessToken } = useParams();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useSnackbar();

  // Refs
  const contentContainerRef = useRef(null);

  // State
  const [loading, setLoading] = useState(true);
  const [calendarData, setCalendarData] = useState(null);
  const [contents, setContents] = useState([]);
  const [campaigns, setCampaigns] = useState([]);
  const [, setCollaborator] = useState(null);
  const [selectedContent, setSelectedContent] = useState(null);
  const [openReviewDialog, setOpenReviewDialog] = useState(false);
  const [reviewStatus, setReviewStatus] = useState('approved');
  const [reviewComment, setReviewComment] = useState('');
  const [openUserInfoDialog, setOpenUserInfoDialog] = useState(false);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [userInfoSubmitted, setUserInfoSubmitted] = useState(false);
  const [showAnnotationTool, setShowAnnotationTool] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  // Load shared calendar data
  useEffect(() => {
    const fetchCalendarData = async () => {
      try {
        // Check if we have user info in local storage
        const storedUserInfo = localStorage.getItem('collaborator_info');
        let userInfo = null;

        if (storedUserInfo) {
          userInfo = JSON.parse(storedUserInfo);
          setUserName(userInfo.name);
          setUserEmail(userInfo.email);
          setUserInfoSubmitted(true);
        } else {
          // Show user info dialog if not already submitted
          setOpenUserInfoDialog(true);
        }

        // If user info is available, fetch calendar data
        if (userInfo) {
          const response = await accessSharedCalendar(accessToken, userInfo);
          setCalendarData(response.calendar);
          setContents(response.contents);
          setCampaigns(response.campaigns);
          setCollaborator(response.collaborator);
        }
      } catch (error) {
        console.error('Error fetching shared calendar:', error);
        showErrorNotification('Failed to load shared calendar. The link may be invalid or expired.');
      } finally {
        setLoading(false);
      }
    };

    fetchCalendarData();
  }, [accessToken, showErrorNotification, userInfoSubmitted]);

  // Handle user info submission
  const handleUserInfoSubmit = async () => {
    if (!userName || !userEmail) {
      showErrorNotification('Please provide your name and email to access the shared calendar.');
      return;
    }

    try {
      setLoading(true);

      // Save user info to local storage
      const userInfo = { name: userName, email: userEmail };
      localStorage.setItem('collaborator_info', JSON.stringify(userInfo));

      // Fetch calendar data with user info
      const response = await accessSharedCalendar(accessToken, userInfo);
      setCalendarData(response.calendar);
      setContents(response.contents);
      setCampaigns(response.campaigns);
      setCollaborator(response.collaborator);

      setOpenUserInfoDialog(false);
      setUserInfoSubmitted(true);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error accessing shared calendar:', error);
      }
      showErrorNotification('Failed to access shared calendar. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle content review
  const handleReviewContent = (content) => {
    setSelectedContent(content);
    setReviewStatus('approved');
    setReviewComment('');
    setOpenReviewDialog(true);
  };

  // Handle image selection for annotation
  const handleImageSelect = (imageUrl) => {
    setSelectedImage(imageUrl);
    setShowAnnotationTool(true);
  };

  // Handle navigation back to main calendar
  const handleBackToCalendar = () => {
    navigate('/calendar');
  };

  // Submit content review
  const handleSubmitReview = async () => {
    try {
      setLoading(true);

      const reviewData = {
        content_id: selectedContent.id,
        status: reviewStatus,
        reviewer_name: userName,
        reviewer_email: userEmail,
        comment: reviewComment
      };

      await reviewContent(reviewData);

      // Update the content's review status in the local state
      const updatedContents = contents.map(content => {
        if (content.id === selectedContent.id) {
          return {
            ...content,
            review_status: {
              status: reviewStatus,
              reviewer_name: userName,
              reviewer_email: userEmail,
              comments: reviewComment ? [reviewComment] : [],
              reviewed_at: new Date().toISOString()
            }
          };
        }
        return content;
      });

      setContents(updatedContents);
      setOpenReviewDialog(false);
      showSuccessNotification(`Content ${reviewStatus === 'approved' ? 'approved' : 'rejected'} successfully.`);
    } catch (error) {
      console.error('Error reviewing content:', error);
      showErrorNotification('Failed to submit review. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Filter contents for selected date - used for future calendar view
  // const contentForSelectedDate = contents.filter(content => {
  //   const contentDate = new Date(content.scheduled_for);
  //   return (
  //     contentDate.getDate() === selectedDate.getDate() &&
  //     contentDate.getMonth() === selectedDate.getMonth() &&
  //     contentDate.getFullYear() === selectedDate.getFullYear()
  //   );
  // });

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get platform color
  const getPlatformColor = (platform) => {
    switch (platform.toLowerCase()) {
      case 'linkedin':
        return '#0077B5';
      case 'twitter':
        return '#1DA1F2';
      case 'facebook':
        return '#4267B2';
      case 'instagram':
        return '#E1306C';
      default:
        return '#757575';
    }
  };

  if (loading && !openUserInfoDialog) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      {/* User Info Dialog */}
      <Dialog open={openUserInfoDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Welcome to Shared Calendar</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            Please provide your name and email to access the shared calendar.
          </Typography>
          <TextField
            label="Your Name"
            value={userName}
            onChange={(e) => setUserName(e.target.value)}
            fullWidth
            margin="normal"
            required
          />
          <TextField
            label="Your Email"
            value={userEmail}
            onChange={(e) => setUserEmail(e.target.value)}
            fullWidth
            margin="normal"
            type="email"
            required
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleUserInfoSubmit}
            variant="contained"
            disabled={!userName || !userEmail}
          >
            Continue
          </Button>
        </DialogActions>
      </Dialog>

      {calendarData && (
        <>
          {/* Header */}
          <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Grid container spacing={2} alignItems="center" sx={{ flexGrow: 1 }}>
                <Grid item>
                  <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                    <CalendarIcon fontSize="large" />
                  </Avatar>
                </Grid>
                <Grid item xs>
                  <Typography variant="h4">{calendarData.name}</Typography>
                  {calendarData.description && (
                    <Typography variant="body1" color="textSecondary">
                      {calendarData.description}
                    </Typography>
                  )}
                </Grid>
              </Grid>
              <Button
                variant="outlined"
                onClick={handleBackToCalendar}
                sx={{ ml: 2 }}
              >
                Back to Calendar
              </Button>
            </Box>

            {calendarData.expiration_date && (
              <Alert severity="info" sx={{ mt: 2 }}>
                This shared calendar will expire on {format(new Date(calendarData.expiration_date), 'MMMM d, yyyy')}
              </Alert>
            )}
          </Paper>

          {/* Calendar Content */}
          <Grid container spacing={3}>
            {/* Content List */}
            <Grid item xs={12} md={8}>
              <Paper elevation={2} sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Scheduled Content
                </Typography>

                {contents.length === 0 ? (
                  <Alert severity="info">No content available for review.</Alert>
                ) : (
                  <List>
                    {contents.map((content) => (
                      <Card key={content.id} sx={{ mb: 2 }}>
                        <CardContent>
                          <Grid container spacing={2}>
                            <Grid item xs={12}>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="h6">{content.title}</Typography>
                                <Chip
                                  label={content.review_status?.status || 'pending'}
                                  color={getStatusColor(content.review_status?.status)}
                                  size="small"
                                />
                              </Box>

                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                                <Chip
                                  label={content.platforms[0]}
                                  size="small"
                                  sx={{
                                    backgroundColor: getPlatformColor(content.platforms[0]),
                                    color: 'white'
                                  }}
                                />
                                <Chip
                                  icon={<AccessTimeIcon />}
                                  label={format(new Date(content.scheduled_for), 'MMM d, yyyy h:mm a')}
                                  size="small"
                                  variant="outlined"
                                />
                              </Box>

                              <Typography variant="body1" paragraph>
                                {content.text_content}
                              </Typography>

                              {content.images && content.images.length > 0 && (
                                <Box sx={{ mt: 2, mb: 2, textAlign: 'center' }}>
                                  {showAnnotationTool && selectedContent?.id === content.id ? (
                                    <Box
                                      sx={{
                                        position: 'relative',
                                        width: '100%',
                                        height: 400,
                                        border: '1px solid',
                                        borderColor: 'divider',
                                        borderRadius: 2,
                                        overflow: 'hidden'
                                      }}
                                      ref={contentContainerRef}
                                    >
                                      <ImageAnnotationTool
                                        contentId={content.id}
                                        imageId={content.images[0].id || 'image-1'}
                                        imageUrl={content.images[0].url}
                                        calendarToken={accessToken}
                                        readOnly={false}
                                      />
                                      <LiveCursorTracker
                                        containerRef={contentContainerRef}
                                        contentId={content.id}
                                      />
                                    </Box>
                                  ) : (
                                    <Box sx={{ position: 'relative' }}>
                                      <img
                                        src={content.images[0].url}
                                        alt={content.title}
                                        style={{
                                          maxWidth: '100%',
                                          maxHeight: 300,
                                          borderRadius: 8
                                        }}
                                      />
                                      <Button
                                        variant="contained"
                                        color="primary"
                                        size="small"
                                        startIcon={<EditIcon />}
                                        sx={{
                                          position: 'absolute',
                                          bottom: 8,
                                          right: 8,
                                          backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                          '&:hover': {
                                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                          }
                                        }}
                                        onClick={() => {
                                          setSelectedContent(content);
                                          setSelectedImage(content.images[0]);
                                          setShowAnnotationTool(true);
                                        }}
                                      >
                                        Annotate
                                      </Button>
                                    </Box>
                                  )}
                                </Box>
                              )}

                              {content.review_status?.status ? (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="subtitle2">
                                    {content.review_status.status === 'approved' ? (
                                      <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
                                        <CheckCircleIcon fontSize="small" sx={{ mr: 1 }} />
                                        Approved by {content.review_status.reviewer_name}
                                      </Box>
                                    ) : (
                                      <Box sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}>
                                        <CancelIcon fontSize="small" sx={{ mr: 1 }} />
                                        Rejected by {content.review_status.reviewer_name}
                                      </Box>
                                    )}
                                  </Typography>

                                  {content.review_status.comments && content.review_status.comments.length > 0 && (
                                    <Box sx={{ mt: 1, pl: 2, borderLeft: '2px solid', borderColor: 'divider' }}>
                                      <Typography variant="body2">
                                        {content.review_status.comments[0]}
                                      </Typography>
                                    </Box>
                                  )}
                                </Box>
                              ) : (
                                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                                  <Button
                                    variant="contained"
                                    onClick={() => handleReviewContent(content)}
                                    startIcon={<CommentIcon />}
                                  >
                                    Review
                                  </Button>
                                </Box>
                              )}
                            </Grid>
                          </Grid>
                        </CardContent>
                      </Card>
                    ))}
                  </List>
                )}
              </Paper>
            </Grid>

            {/* Sidebar */}
            <Grid item xs={12} md={4}>
              <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Review Summary
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography>Total Content:</Typography>
                  <Typography fontWeight="bold">{contents.length}</Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography>Approved:</Typography>
                  <Typography fontWeight="bold" color="success.main">
                    {contents.filter(c => c.review_status?.status === 'approved').length}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography>Rejected:</Typography>
                  <Typography fontWeight="bold" color="error.main">
                    {contents.filter(c => c.review_status?.status === 'rejected').length}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography>Pending:</Typography>
                  <Typography fontWeight="bold" color="warning.main">
                    {contents.filter(c => !c.review_status || c.review_status.status === 'pending').length}
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" gutterBottom>
                  Campaigns
                </Typography>

                {campaigns.length === 0 ? (
                  <Typography variant="body2" color="textSecondary">No campaigns included</Typography>
                ) : (
                  <List dense>
                    {campaigns.map(campaign => (
                      <ListItem key={campaign.id}>
                        <ListItemText
                          primary={campaign.name}
                          secondary={`${campaign.content_ids.length} posts`}
                        />
                      </ListItem>
                    ))}
                  </List>
                )}
              </Paper>

              {/* Collaboration Features */}
              {selectedContent && (
                <>
                  <ChangeTracker contentId={selectedContent.id} />

                  <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                      <GroupIcon sx={{ mr: 1 }} />
                      Collaboration Features
                    </Typography>

                    <Alert severity="info" sx={{ mb: 2 }}>
                      <AlertTitle>Real-time Collaboration</AlertTitle>
                      <Typography variant="body2">
                        You can now collaborate in real-time with other reviewers:
                      </Typography>
                      <ul>
                        <li>See where others are viewing with live cursor tracking</li>
                        <li>Draw and annotate directly on images</li>
                        <li>Add comments to specific parts of the content</li>
                        <li>Track changes made by all collaborators</li>
                      </ul>
                    </Alert>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                      <Button
                        variant="outlined"
                        startIcon={<ImageIcon />}
                        onClick={() => {
                          if (selectedContent.images && selectedContent.images.length > 0) {
                            handleImageSelect(selectedContent.images[0]);
                          } else {
                            showErrorNotification('This content has no images to annotate');
                          }
                        }}
                      >
                        View Images
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<EditIcon />}
                        onClick={() => {
                          if (selectedContent.images && selectedContent.images.length > 0) {
                            setSelectedImage(selectedContent.images[0]);
                            setShowAnnotationTool(!showAnnotationTool);
                          } else {
                            showErrorNotification('This content has no images to annotate');
                          }
                        }}
                      >
                        {showAnnotationTool ? 'Hide Annotation Tool' : 'Show Annotation Tool'}
                      </Button>
                    </Box>
                  </Paper>
                </>
              )}

              <Alert severity="info" sx={{ mb: 3 }}>
                <AlertTitle>How to Review</AlertTitle>
                Click the &quot;Review&quot; button on any content to approve or reject it. You can also leave comments for the content creator.
              </Alert>
            </Grid>
          </Grid>

          {/* Review Dialog */}
          <Dialog open={openReviewDialog} onClose={() => setOpenReviewDialog(false)} maxWidth="sm" fullWidth>
            <DialogTitle>Review Content</DialogTitle>
            <DialogContent>
              {selectedContent && (
                <>
                  <Typography variant="h6" gutterBottom>{selectedContent.title}</Typography>

                  <FormControl fullWidth margin="normal">
                    <InputLabel>Review Decision</InputLabel>
                    <Select
                      value={reviewStatus}
                      onChange={(e) => setReviewStatus(e.target.value)}
                      label="Review Decision"
                    >
                      <MenuItem value="approved">
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <ThumbUpIcon fontSize="small" sx={{ mr: 1, color: 'success.main' }} />
                          Approve
                        </Box>
                      </MenuItem>
                      <MenuItem value="rejected">
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <ThumbDownIcon fontSize="small" sx={{ mr: 1, color: 'error.main' }} />
                          Reject
                        </Box>
                      </MenuItem>
                    </Select>
                  </FormControl>

                  <TextField
                    label="Comments (optional)"
                    value={reviewComment}
                    onChange={(e) => setReviewComment(e.target.value)}
                    fullWidth
                    multiline
                    rows={4}
                    margin="normal"
                  />
                </>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setOpenReviewDialog(false)}>Cancel</Button>
              <Button
                onClick={handleSubmitReview}
                variant="contained"
                startIcon={<SendIcon />}
                disabled={loading}
              >
                {loading ? <CircularProgress size={24} /> : 'Submit Review'}
              </Button>
            </DialogActions>
          </Dialog>
        </>
      )}

      {/* Image Viewer Dialog */}
      <Dialog
        open={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Image Viewer
          <Button
            onClick={() => setSelectedImage(null)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            ×
          </Button>
        </DialogTitle>
        <DialogContent>
          {selectedImage && (
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={selectedImage}
                alt="Selected content"
                style={{
                  maxWidth: '100%',
                  maxHeight: '70vh',
                  objectFit: 'contain'
                }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedImage(null)}>
            Close
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              setShowAnnotationTool(true);
            }}
            startIcon={<EditIcon />}
          >
            Annotate
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SharedCalendarView;

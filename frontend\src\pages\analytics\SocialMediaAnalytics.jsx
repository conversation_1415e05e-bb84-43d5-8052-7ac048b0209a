// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Paper,
  TextField,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  BarChart as BarChartIcon,
  Timeline as TimelineIcon,
  Refresh as RefreshIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  Pinterest as PinterestIcon,
  Chat as ThreadsIcon,
  MusicVideo as TikTokIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';
import * as d3 from 'd3';

const SocialMediaAnalytics = () => {
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [platform, setPlatform] = useState('all');
  const [startDate, setStartDate] = useState(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));
  const [endDate, setEndDate] = useState(new Date());
  const [analyticsData, setAnalyticsData] = useState(null);
  const [accounts, setAccounts] = useState([]);

  const timeSeriesChartRef = useRef(null);
  const platformComparisonChartRef = useRef(null);
  const engagementChartRef = useRef(null);

  // Load connected accounts
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        const response = await api.get('/api/social-media/accounts');
        setAccounts(response.data.accounts || []);
      } catch (error) {
        console.error('Error fetching accounts:', error);
        showErrorNotification('Failed to load social media accounts');
      }
    };

    fetchAccounts();
  }, [showErrorNotification]);

  // Load analytics data
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      setLoading(true);

      try {
        // Format dates for API
        const formattedStartDate = startDate.toISOString();
        const formattedEndDate = endDate.toISOString();

        // Build query parameters
        const params = new URLSearchParams();
        params.append('start_date', formattedStartDate);
        params.append('end_date', formattedEndDate);

        if (platform !== 'all') {
          params.append('platform', platform);
        }

        // Fetch analytics data
        const response = await api.get(`/api/analytics/accounts?${params.toString()}`);
        setAnalyticsData(response.data);

        showSuccessNotification('Analytics data loaded successfully');
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        showErrorNotification('Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    if (accounts.length > 0) {
      fetchAnalyticsData();
    } else {
      setLoading(false);
    }
  }, [accounts, platform, startDate, endDate, showSuccessNotification, showErrorNotification]);

  // Render D3 charts when data is available
  useEffect(() => {
    if (analyticsData && !loading) {
      renderTimeSeriesChart();
      renderPlatformComparisonChart();
      renderEngagementChart();
    }
  }, [analyticsData, loading, tabValue, renderTimeSeriesChart]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle platform change
  const handlePlatformChange = (event) => {
    setPlatform(event.target.value);
  };

  // Render time series chart using D3.js
  const renderTimeSeriesChart = useCallback(() => {
    if (!analyticsData || !timeSeriesChartRef.current) return;

    // Clear previous chart
    d3.select(timeSeriesChartRef.current).selectAll('*').remove();

    // Prepare data
    let timeSeriesData = [];

    // Combine data from all platforms
    analyticsData.platform_analytics.forEach(platform => {
      platform.daily_data.forEach(day => {
        const existingDay = timeSeriesData.find(d => d.date === day.date);

        if (existingDay) {
          existingDay.impressions += day.impressions;
          existingDay.engagements += day.engagements;
        } else {
          timeSeriesData.push({
            date: day.date,
            impressions: day.impressions,
            engagements: day.engagements,
          });
        }
      });
    });

    // Sort by date
    timeSeriesData.sort((a, b) => new Date(a.date) - new Date(b.date));

    // Set up dimensions
    const margin = { top: 20, right: 30, bottom: 50, left: 60 };
    const width = timeSeriesChartRef.current.clientWidth - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(timeSeriesChartRef.current)
      .append('svg')
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Create scales
    const x = d3.scaleTime()
      .domain(d3.extent(timeSeriesData, d => new Date(d.date)))
      .range([0, width]);

    const y = d3.scaleLinear()
      .domain([0, d3.max(timeSeriesData, d => Math.max(d.impressions, d.engagements * 10))])
      .range([height, 0]);

    // Create axes
    svg.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(x));

    svg.append('g')
      .call(d3.axisLeft(y));

    // Create lines
    const impressionsLine = d3.line()
      .x(d => x(new Date(d.date)))
      .y(d => y(d.impressions));

    const engagementsLine = d3.line()
      .x(d => x(new Date(d.date)))
      .y(d => y(d.engagements * 10)); // Scale up engagements for visibility

    // Add lines
    svg.append('path')
      .datum(timeSeriesData)
      .attr('fill', 'none')
      .attr('stroke', '#2196f3')
      .attr('stroke-width', 2)
      .attr('d', impressionsLine);

    svg.append('path')
      .datum(timeSeriesData)
      .attr('fill', 'none')
      .attr('stroke', '#f44336')
      .attr('stroke-width', 2)
      .attr('d', engagementsLine);

    // Add legend
    const legend = svg.append('g')
      .attr('transform', `translate(${width - 150}, 0)`);

    legend.append('rect')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', 15)
      .attr('height', 15)
      .attr('fill', '#2196f3');

    legend.append('text')
      .attr('x', 20)
      .attr('y', 12)
      .text('Impressions');

    legend.append('rect')
      .attr('x', 0)
      .attr('y', 25)
      .attr('width', 15)
      .attr('height', 15)
      .attr('fill', '#f44336');

    legend.append('text')
      .attr('x', 20)
      .attr('y', 37)
      .text('Engagements (×10)');
  }, [analyticsData]);

  // Render platform comparison chart using D3.js
  const renderPlatformComparisonChart = () => {
    // Implementation would go here
  };

  // Render engagement chart using D3.js
  const renderEngagementChart = () => {
    // Implementation would go here
  };

  // Refresh analytics data
  const handleRefresh = () => {
    // Re-fetch analytics data
    const fetchAnalyticsData = async () => {
      setLoading(true);

      try {
        // Format dates for API
        const formattedStartDate = startDate.toISOString();
        const formattedEndDate = endDate.toISOString();

        // Build query parameters
        const params = new URLSearchParams();
        params.append('start_date', formattedStartDate);
        params.append('end_date', formattedEndDate);

        if (platform !== 'all') {
          params.append('platform', platform);
        }

        // Fetch analytics data
        const response = await api.get(`/api/analytics/accounts?${params.toString()}`);
        setAnalyticsData(response.data);

        showSuccessNotification('Analytics data refreshed successfully');
      } catch (error) {
        console.error('Error refreshing analytics data:', error);
        showErrorNotification('Failed to refresh analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Social Media Analytics
      </Typography>

      {accounts.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          You don&apos;t have any connected social media accounts yet. Connect an account to view analytics.
        </Alert>
      ) : (
        <>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth>
                    <InputLabel>Platform</InputLabel>
                    <Select
                      value={platform}
                      label="Platform"
                      onChange={handlePlatformChange}
                    >
                      <MenuItem value="all">All Platforms</MenuItem>
                      {accounts.map(account => {
                        // Get platform icon
                        let icon;
                        switch(account.platform) {
                          case 'facebook':
                            icon = <FacebookIcon sx={{ mr: 1 }} />;
                            break;
                          case 'twitter':
                            icon = <TwitterIcon sx={{ mr: 1 }} />;
                            break;
                          case 'linkedin':
                            icon = <LinkedInIcon sx={{ mr: 1 }} />;
                            break;
                          case 'instagram':
                            icon = <InstagramIcon sx={{ mr: 1 }} />;
                            break;
                          case 'pinterest':
                            icon = <PinterestIcon sx={{ mr: 1 }} />;
                            break;
                          case 'threads':
                            icon = <ThreadsIcon sx={{ mr: 1 }} />;
                            break;
                          case 'tiktok':
                            icon = <TikTokIcon sx={{ mr: 1 }} />;
                            break;
                          default:
                            icon = null;
                        }

                        return (
                          <MenuItem key={account.platform} value={account.platform}>
                            {icon}
                            {account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={3}>
                  <DatePicker
                    label="Start Date"
                    value={startDate}
                    onChange={setStartDate}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>

                <Grid item xs={12} sm={3}>
                  <DatePicker
                    label="End Date"
                    value={endDate}
                    onChange={setEndDate}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>

                <Grid item xs={12} sm={3}>
                  <Button
                    variant="contained"
                    startIcon={<RefreshIcon />}
                    onClick={handleRefresh}
                    fullWidth
                  >
                    Refresh
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            analyticsData && (
              <>
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {analyticsData.total_impressions.toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Total Impressions
                      </Typography>
                    </Paper>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {analyticsData.total_engagements.toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Total Engagements
                      </Typography>
                    </Paper>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {analyticsData.total_followers.toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Total Followers
                      </Typography>
                    </Paper>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {analyticsData.engagement_rate.toFixed(2)}%
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Engagement Rate
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>

                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                  <Tabs value={tabValue} onChange={handleTabChange} aria-label="analytics tabs">
                    <Tab icon={<TimelineIcon />} label="Time Series" />
                    <Tab icon={<BarChartIcon />} label="Platform Comparison" />
                    <Tab icon={<TrendingUpIcon />} label="Engagement Analysis" />
                  </Tabs>
                </Box>

                {tabValue === 0 && (
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Performance Over Time
                      </Typography>
                      <Box
                        ref={timeSeriesChartRef}
                        sx={{ width: '100%', height: 400 }}
                      />
                    </CardContent>
                  </Card>
                )}

                {tabValue === 1 && (
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Platform Comparison
                      </Typography>
                      <Box
                        ref={platformComparisonChartRef}
                        sx={{ width: '100%', height: 400 }}
                      />
                    </CardContent>
                  </Card>
                )}

                {tabValue === 2 && (
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Engagement Analysis
                      </Typography>
                      <Box
                        ref={engagementChartRef}
                        sx={{ width: '100%', height: 400 }}
                      />
                    </CardContent>
                  </Card>
                )}
              </>
            )
          )}
        </>
      )}
    </Box>
  );
};

export default SocialMediaAnalytics;

import React, { createContext, useContext, useState, useEffect } from 'react';
import api from '../api';
import { ENVIRONMENT } from '../config';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('admin_token'));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if localhost bypass is enabled (development only)
  const isLocalhostBypass = ENVIRONMENT === 'development' &&
    (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

  // Load user data on mount
  useEffect(() => {
    if (isLocalhostBypass) {
      // Skip authentication for localhost development
      loadUser();
    } else if (token) {
      loadUser();
    } else {
      setLoading(false);
    }
  }, [token, isLocalhostBypass]);

  const loadUser = async () => {
    try {
      setLoading(true);

      // If localhost bypass is enabled, create a mock admin user
      if (isLocalhostBypass) {
        console.log('🔧 Development mode: Using localhost bypass for admin authentication');
        const mockAdminUser = {
          id: "localhost-dev-admin",
          email: "<EMAIL>",
          full_name: "Localhost Admin",
          first_name: "Localhost",
          last_name: "Admin",
          is_active: true,
          is_admin: true,
          company_name: "Localhost Development",
          subscription: {
            plan: "enterprise",
            status: "active",
            trial_end: null
          }
        };
        setUser(mockAdminUser);
        localStorage.setItem('admin_user', JSON.stringify(mockAdminUser));
        setError(null);
        setLoading(false);
        return;
      }

      // Try to load user from API with timeout and retry
      let retries = 3;
      let lastError;

      while (retries > 0) {
        try {
          const response = await api.get('/api/auth/me');
          const userData = response.data;

          // Verify user is admin
          if (!userData.is_admin) {
            throw new Error('Access denied: Admin privileges required');
          }

          setUser(userData);
          localStorage.setItem('admin_user', JSON.stringify(userData));
          setError(null);
          return;
        } catch (error) {
          lastError = error;
          retries--;
          if (retries > 0) {
            console.warn(`Auth request failed, retrying... (${retries} attempts left)`);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }

      throw lastError;
    } catch (error) {
      console.error('Failed to load user:', error);
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to load user';
      setError(errorMessage);

      if (!isLocalhostBypass) {
        logout();
      }
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setLoading(true);
      setError(null);

      // If localhost bypass is enabled, skip actual login
      if (isLocalhostBypass) {
        await loadUser(); // This will create the mock admin user
        return { success: true };
      }

      const response = await api.post('/api/auth/login', {
        email,
        password,
      });

      const { access_token, user: userData } = response.data;

      // Verify user is admin
      if (!userData.is_admin) {
        throw new Error('Access denied: Admin privileges required');
      }

      setToken(access_token);
      setUser(userData);
      localStorage.setItem('admin_token', access_token);
      localStorage.setItem('admin_user', JSON.stringify(userData));

      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.detail || error.message || 'Login failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    setError(null);
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
  };

  const value = {
    user,
    token,
    loading,
    error,
    isAuthenticated: !!user || isLocalhostBypass,
    isAdmin: user?.is_admin || isLocalhostBypass,
    login,
    logout,
    refreshUser: loadUser,
    isLocalhostBypass,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

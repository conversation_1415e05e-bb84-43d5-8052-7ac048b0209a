import { useState, useEffect, useCallback, useRef } from 'react';
import api from '../api';

/**
 * Custom hook for managing AppSumo data with production-ready features
 * Includes caching, error handling, retry mechanisms, and correlation IDs
 */
export const useAppSumoData = () => {
  const [data, setData] = useState({
    tiers: [],
    deals: [],
    codes: [],
    analytics: null,
    redemptions: [],
  });
  
  const [loading, setLoading] = useState({
    tiers: false,
    deals: false,
    codes: false,
    analytics: false,
    redemptions: false,
  });
  
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState({});
  const retryTimeouts = useRef({});
  const abortControllers = useRef({});

  // Cache TTL in milliseconds (15 minutes)
  const CACHE_TTL = 15 * 60 * 1000;
  
  // Retry configuration
  const RETRY_CONFIG = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 5000,
  };

  /**
   * Generate correlation ID for request tracking
   */
  const generateCorrelationId = () => {
    return `appsumo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  /**
   * Check if cached data is still valid
   */
  const isCacheValid = useCallback((key) => {
    const lastFetchTime = lastFetch[key];
    if (!lastFetchTime) return false;
    return Date.now() - lastFetchTime < CACHE_TTL;
  }, [lastFetch]);

  /**
   * Generic API call with retry logic and error handling
   */
  const apiCall = useCallback(async (endpoint, options = {}) => {
    const correlationId = generateCorrelationId();
    let retryCount = 0;
    
    const makeRequest = async () => {
      try {
        // Cancel previous request if exists
        if (abortControllers.current[endpoint]) {
          abortControllers.current[endpoint].abort();
        }
        
        // Create new abort controller
        const controller = new AbortController();
        abortControllers.current[endpoint] = controller;
        
        const response = await api.get(endpoint, {
          ...options,
          signal: controller.signal,
          headers: {
            ...options.headers,
            'X-Correlation-ID': correlationId,
          },
        });
        
        // Clear abort controller on success
        delete abortControllers.current[endpoint];
        
        return response.data;
      } catch (error) {
        // Clear abort controller
        delete abortControllers.current[endpoint];
        
        // Don't retry if request was aborted
        if (error.name === 'AbortError') {
          throw error;
        }
        
        // Retry logic for network errors
        if (retryCount < RETRY_CONFIG.maxRetries &&
            (error.code === 'NETWORK_ERROR' ||
             error.code === 'ECONNABORTED' ||
             error.message?.includes('timeout') ||
             error.response?.status >= 500)) {
          retryCount++;
          const delay = Math.min(
            RETRY_CONFIG.baseDelay * Math.pow(2, retryCount - 1),
            RETRY_CONFIG.maxDelay
          );

          console.warn(`API call failed, retrying in ${delay}ms (attempt ${retryCount}/${RETRY_CONFIG.maxRetries})`);

          await new Promise(resolve => setTimeout(resolve, delay));
          return makeRequest();
        }
        
        throw error;
      }
    };
    
    return makeRequest();
  }, []);

  /**
   * Set loading state for specific data type
   */
  const setLoadingState = useCallback((key, isLoading) => {
    setLoading(prev => ({ ...prev, [key]: isLoading }));
  }, []);

  /**
   * Fetch tiers data
   */
  const fetchTiers = useCallback(async (force = false) => {
    if (!force && isCacheValid('tiers')) {
      return data.tiers;
    }

    try {
      setLoadingState('tiers', true);
      setError(null);

      const tiersData = await apiCall('/api/appsumo/tiers');

      setData(prev => ({ ...prev, tiers: tiersData || [] }));
      setLastFetch(prev => ({ ...prev, tiers: Date.now() }));

      return tiersData || [];
    } catch (error) {
      console.error('Error fetching tiers:', error);

      // Don't set error for cancelled requests or 404s
      if (error.name !== 'AbortError' &&
          error.name !== 'CanceledError' &&
          error.response?.status !== 404) {
        setError(`Failed to fetch tiers: ${error.message}`);
      }

      // Return mock data for development if endpoint doesn't exist
      if (error.response?.status === 404) {
        const mockTiers = [
          {
            id: 'mock-tier-1',
            tier_type: 'tier1',
            name: 'Single',
            description: 'Perfect for individual creators',
            plan_id: 'creator',
            max_users: 1,
            max_social_accounts: 5,
            max_posts_per_month: 100,
            is_active: true,
          },
          {
            id: 'mock-tier-2',
            tier_type: 'tier2',
            name: 'Double',
            description: 'Perfect for small teams',
            plan_id: 'accelerator',
            max_users: 2,
            max_social_accounts: 10,
            max_posts_per_month: 200,
            is_active: true,
          },
        ];

        setData(prev => ({ ...prev, tiers: mockTiers }));
        setLastFetch(prev => ({ ...prev, tiers: Date.now() }));
        return mockTiers;
      }

      return [];
    } finally {
      setLoadingState('tiers', false);
    }
  }, [data.tiers, isCacheValid, apiCall, setLoadingState]);

  /**
   * Fetch deals data
   */
  const fetchDeals = useCallback(async (force = false) => {
    if (!force && isCacheValid('deals')) {
      return data.deals;
    }

    try {
      setLoadingState('deals', true);
      setError(null);

      const dealsData = await apiCall('/api/appsumo/deals');

      setData(prev => ({ ...prev, deals: dealsData || [] }));
      setLastFetch(prev => ({ ...prev, deals: Date.now() }));

      return dealsData || [];
    } catch (error) {
      console.error('Error fetching deals:', error);

      // Don't set error for cancelled requests or 404s
      if (error.name !== 'AbortError' &&
          error.name !== 'CanceledError' &&
          error.response?.status !== 404) {
        setError(`Failed to fetch deals: ${error.message}`);
      }

      // Return mock data for development if endpoint doesn't exist
      if (error.response?.status === 404) {
        const mockDeals = [
          {
            id: 'mock-deal-1',
            deal_id: 'demo-deal-2024',
            name: 'B2B Influencer Tool Lifetime Deal',
            description: 'Get lifetime access to our comprehensive B2B influencer platform',
            pricing: {
              regular_price: 299,
              appsumo_price: 59,
              discount_percentage: 80.3,
            },
            start_date: new Date().toISOString(),
            end_date: null,
            is_active: true,
            tiers: ['tier1', 'tier2', 'tier3'],
            features: [
              'Unlimited content generation',
              'Advanced analytics',
              'Multi-platform scheduling',
              'Team collaboration',
            ],
          },
        ];

        setData(prev => ({ ...prev, deals: mockDeals }));
        setLastFetch(prev => ({ ...prev, deals: Date.now() }));
        return mockDeals;
      }

      return [];
    } finally {
      setLoadingState('deals', false);
    }
  }, [data.deals, isCacheValid, apiCall, setLoadingState]);

  /**
   * Fetch codes data with pagination
   */
  const fetchCodes = useCallback(async (options = {}) => {
    const { force = false, page = 1, limit = 50, filters = {} } = options;

    if (!force && isCacheValid('codes') && page === 1) {
      return data.codes;
    }

    try {
      setLoadingState('codes', true);
      setError(null);

      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...filters,
      });

      const codesData = await apiCall(`/api/appsumo/codes?${queryParams}`);

      if (page === 1) {
        setData(prev => ({ ...prev, codes: codesData || [] }));
        setLastFetch(prev => ({ ...prev, codes: Date.now() }));
      }

      return codesData || [];
    } catch (error) {
      console.error('Error fetching codes:', error);

      // Don't set error for cancelled requests or 404s (endpoint doesn't exist yet)
      if (error.name !== 'AbortError' &&
          error.name !== 'CanceledError' &&
          error.response?.status !== 404) {
        setError(`Failed to fetch codes: ${error.message}`);
      }

      // Return mock data for development if endpoint doesn't exist
      if (error.response?.status === 404) {
        const mockCodes = [
          {
            id: 'mock-1',
            code: 'AS-DEMO1234',
            deal_id: 'demo-deal',
            tier_type: 'tier1',
            is_redeemed: false,
            created_at: new Date().toISOString(),
          },
          {
            id: 'mock-2',
            code: 'AS-DEMO5678',
            deal_id: 'demo-deal',
            tier_type: 'tier2',
            is_redeemed: true,
            created_at: new Date().toISOString(),
            redeemed_at: new Date().toISOString(),
          },
        ];

        if (page === 1) {
          setData(prev => ({ ...prev, codes: mockCodes }));
          setLastFetch(prev => ({ ...prev, codes: Date.now() }));
        }

        return mockCodes;
      }

      return [];
    } finally {
      setLoadingState('codes', false);
    }
  }, [data.codes, isCacheValid, apiCall, setLoadingState]);

  /**
   * Fetch analytics data
   */
  const fetchAnalytics = useCallback(async (force = false) => {
    if (!force && isCacheValid('analytics')) {
      return data.analytics;
    }

    try {
      setLoadingState('analytics', true);
      setError(null);

      const analyticsData = await apiCall('/api/appsumo/analytics');

      setData(prev => ({ ...prev, analytics: analyticsData }));
      setLastFetch(prev => ({ ...prev, analytics: Date.now() }));

      return analyticsData;
    } catch (error) {
      console.error('Error fetching analytics:', error);

      // Don't set error for cancelled requests
      if (error.name !== 'AbortError' && error.name !== 'CanceledError') {
        setError(`Failed to fetch analytics: ${error.message}`);
      }

      return null;
    } finally {
      setLoadingState('analytics', false);
    }
  }, [data.analytics, isCacheValid, apiCall, setLoadingState]);

  /**
   * Fetch redemptions data
   */
  const fetchRedemptions = useCallback(async (options = {}) => {
    const { force = false, filters = {} } = options;

    if (!force && isCacheValid('redemptions')) {
      return data.redemptions;
    }

    try {
      setLoadingState('redemptions', true);
      setError(null);

      const queryParams = new URLSearchParams(filters);
      const redemptionsData = await apiCall(`/api/appsumo/redemptions?${queryParams}`);

      setData(prev => ({ ...prev, redemptions: redemptionsData || [] }));
      setLastFetch(prev => ({ ...prev, redemptions: Date.now() }));

      return redemptionsData || [];
    } catch (error) {
      console.error('Error fetching redemptions:', error);

      // Don't set error for cancelled requests
      if (error.name !== 'AbortError' && error.name !== 'CanceledError') {
        setError(`Failed to fetch redemptions: ${error.message}`);
      }

      return [];
    } finally {
      setLoadingState('redemptions', false);
    }
  }, [data.redemptions, isCacheValid, apiCall, setLoadingState]);

  /**
   * Refresh all data
   */
  const refreshAll = useCallback(async () => {
    try {
      await Promise.allSettled([
        fetchTiers(true),
        fetchDeals(true),
        fetchCodes({ force: true }),
        fetchAnalytics(true),
        fetchRedemptions({ force: true }),
      ]);
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  }, [fetchTiers, fetchDeals, fetchCodes, fetchAnalytics, fetchRedemptions]);

  /**
   * Clear cache for specific data type
   */
  const clearCache = useCallback((key) => {
    if (key) {
      setLastFetch(prev => ({ ...prev, [key]: 0 }));
    } else {
      setLastFetch({});
    }
  }, []);

  /**
   * Cleanup function
   */
  useEffect(() => {
    return () => {
      // Clear all timeouts
      Object.values(retryTimeouts.current).forEach(clearTimeout);
      
      // Abort all pending requests
      Object.values(abortControllers.current).forEach(controller => {
        controller.abort();
      });
    };
  }, []);

  return {
    data,
    loading,
    error,
    fetchTiers,
    fetchDeals,
    fetchCodes,
    fetchAnalytics,
    fetchRedemptions,
    refreshAll,
    clearCache,
    isCacheValid,
  };
};

export default useAppSumoData;

/**
 * QA Test Form Component
 * 
 * A comprehensive form component for testing end-to-end data flow
 * from frontend input validation to backend API calls and database operations.
 @since 2024-1-1 to 2025-25-7
*/

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert,
  CircularProgress,
  Typography,
  Grid,
  Divider,
  FormHelperText
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useApiError } from '../../hooks/useApiError';
import { useNotification } from '../../hooks/useNotification';

// Validation schema for comprehensive testing
const qaTestValidationSchema = Yup.object({
  title: Yup.string()
    .required('Title is required')
    .min(3, 'Title must be at least 3 characters')
    .max(100, 'Title must not exceed 100 characters'),
  description: Yup.string()
    .required('Description is required')
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description must not exceed 1000 characters'),
  category: Yup.string()
    .required('Category is required')
    .oneOf(['content', 'campaign', 'messaging', 'collaboration'], 'Invalid category'),
  platforms: Yup.array()
    .min(1, 'At least one platform must be selected')
    .max(5, 'Maximum 5 platforms allowed'),
  tags: Yup.array()
    .min(1, 'At least one tag is required')
    .max(10, 'Maximum 10 tags allowed'),
  priority: Yup.string()
    .required('Priority is required')
    .oneOf(['low', 'medium', 'high', 'critical'], 'Invalid priority'),
  email: Yup.string()
    .email('Invalid email format')
    .required('Email is required'),
  targetDate: Yup.date()
    .required('Target date is required')
    .min(new Date(), 'Target date must be in the future')
});

const QATestForm = ({ 
  onSubmit, 
  initialData = null, 
  isLoading = false,
  testMode = 'create' // 'create', 'update', 'validate'
}) => {
  const [submissionState, setSubmissionState] = useState('idle'); // idle, submitting, success, error
  const [validationErrors, setValidationErrors] = useState([]);
  const [apiResponse, setApiResponse] = useState(null);
  const { handleApiError } = useApiError();
  const { showNotification } = useNotification();

  const formik = useFormik({
    initialValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      category: initialData?.category || '',
      platforms: initialData?.platforms || [],
      tags: initialData?.tags || [],
      priority: initialData?.priority || 'medium',
      email: initialData?.email || '',
      targetDate: initialData?.targetDate || '',
      metadata: initialData?.metadata || {}
    },
    validationSchema: qaTestValidationSchema,
    onSubmit: async (values, { setSubmitting, setFieldError }) => {
      try {
        setSubmissionState('submitting');
        setValidationErrors([]);
        
        // Simulate comprehensive validation
        const validationResult = await validateFormData(values);
        if (!validationResult.isValid) {
          setValidationErrors(validationResult.errors);
          setSubmissionState('error');
          return;
        }

        // Call the onSubmit handler
        const response = await onSubmit(values);
        
        setApiResponse(response);
        setSubmissionState('success');
        
        showNotification('QA test data submitted successfully!', 'success');
        
        // Reset form if in create mode
        if (testMode === 'create') {
          formik.resetForm();
        }
        
      } catch (error) {
        console.error('QA Test Form submission error:', error);
        setSubmissionState('error');
        
        // Handle different types of errors
        if (error.response?.status === 422) {
          // Validation errors from backend
          const backendErrors = error.response.data.detail || [];
          setValidationErrors(backendErrors);
        } else if (error.response?.status === 401) {
          setFieldError('email', 'Authentication required');
        } else if (error.response?.status === 403) {
          setFieldError('category', 'Insufficient permissions for this category');
        } else {
          handleApiError(error);
        }
      } finally {
        setSubmitting(false);
      }
    }
  });

  // Comprehensive client-side validation
  const validateFormData = async (data) => {
    const errors = [];
    
    // Custom validation rules for QA testing
    if (data.title.toLowerCase().includes('test') && data.category !== 'content') {
      errors.push('Test titles are only allowed for content category');
    }
    
    if (data.platforms.includes('linkedin') && !data.email.includes('@')) {
      errors.push('LinkedIn platform requires valid email format');
    }
    
    if (data.priority === 'critical' && data.tags.length < 3) {
      errors.push('Critical priority items must have at least 3 tags');
    }
    
    // Simulate async validation (e.g., checking uniqueness)
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const handlePlatformChange = (event) => {
    const value = event.target.value;
    formik.setFieldValue('platforms', typeof value === 'string' ? value.split(',') : value);
  };

  const handleTagAdd = (tag) => {
    if (tag && !formik.values.tags.includes(tag)) {
      formik.setFieldValue('tags', [...formik.values.tags, tag]);
    }
  };

  const handleTagRemove = (tagToRemove) => {
    formik.setFieldValue('tags', formik.values.tags.filter(tag => tag !== tagToRemove));
  };

  const availablePlatforms = ['linkedin', 'twitter', 'facebook', 'instagram', 'youtube'];
  const availableTags = ['qa', 'test', 'automation', 'validation', 'integration', 'performance'];

  return (
    <Card sx={{ maxWidth: 800, mx: 'auto', mt: 2 }}>
      <CardHeader 
        title={`QA Test Form - ${testMode.charAt(0).toUpperCase() + testMode.slice(1)} Mode`}
        subheader="Comprehensive form for testing end-to-end data flow validation"
      />
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Basic Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="title"
                name="title"
                label="Title"
                value={formik.values.title}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.title && Boolean(formik.errors.title)}
                helperText={formik.touched.title && formik.errors.title}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="email"
                name="email"
                label="Email"
                type="email"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.email && Boolean(formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="description"
                name="description"
                label="Description"
                multiline
                rows={4}
                value={formik.values.description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
                required
              />
            </Grid>

            {/* Category and Priority */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Classification</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.category && Boolean(formik.errors.category)}>
                <InputLabel>Category</InputLabel>
                <Select
                  id="category"
                  name="category"
                  value={formik.values.category}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  label="Category"
                >
                  <MenuItem value="content">Content</MenuItem>
                  <MenuItem value="campaign">Campaign</MenuItem>
                  <MenuItem value="messaging">Messaging</MenuItem>
                  <MenuItem value="collaboration">Collaboration</MenuItem>
                </Select>
                {formik.touched.category && formik.errors.category && (
                  <FormHelperText>{formik.errors.category}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.priority && Boolean(formik.errors.priority)}>
                <InputLabel>Priority</InputLabel>
                <Select
                  id="priority"
                  name="priority"
                  value={formik.values.priority}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  label="Priority"
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                </Select>
                {formik.touched.priority && formik.errors.priority && (
                  <FormHelperText>{formik.errors.priority}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            {/* Platforms and Tags */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Platforms & Tags</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth error={formik.touched.platforms && Boolean(formik.errors.platforms)}>
                <InputLabel>Platforms</InputLabel>
                <Select
                  multiple
                  id="platforms"
                  name="platforms"
                  value={formik.values.platforms}
                  onChange={handlePlatformChange}
                  onBlur={formik.handleBlur}
                  label="Platforms"
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {availablePlatforms.map((platform) => (
                    <MenuItem key={platform} value={platform}>
                      {platform.charAt(0).toUpperCase() + platform.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.platforms && formik.errors.platforms && (
                  <FormHelperText>{formik.errors.platforms}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="targetDate"
                name="targetDate"
                label="Target Date"
                type="date"
                value={formik.values.targetDate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.targetDate && Boolean(formik.errors.targetDate)}
                helperText={formik.touched.targetDate && formik.errors.targetDate}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <Box>
                <Typography variant="body2" gutterBottom>Tags</Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                  {formik.values.tags.map((tag) => (
                    <Chip
                      key={tag}
                      label={tag}
                      onDelete={() => handleTagRemove(tag)}
                      size="small"
                    />
                  ))}
                </Box>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {availableTags
                    .filter(tag => !formik.values.tags.includes(tag))
                    .map((tag) => (
                      <Chip
                        key={tag}
                        label={tag}
                        onClick={() => handleTagAdd(tag)}
                        variant="outlined"
                        size="small"
                        sx={{ cursor: 'pointer' }}
                      />
                    ))}
                </Box>
                {formik.touched.tags && formik.errors.tags && (
                  <FormHelperText error>{formik.errors.tags}</FormHelperText>
                )}
              </Box>
            </Grid>

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <Grid item xs={12}>
                <Alert severity="error">
                  <Typography variant="body2" gutterBottom>Validation Errors:</Typography>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </Alert>
              </Grid>
            )}

            {/* API Response */}
            {apiResponse && submissionState === 'success' && (
              <Grid item xs={12}>
                <Alert severity="success">
                  <Typography variant="body2" gutterBottom>API Response:</Typography>
                  <pre style={{ fontSize: '0.8rem', overflow: 'auto' }}>
                    {JSON.stringify(apiResponse, null, 2)}
                  </pre>
                </Alert>
              </Grid>
            )}

            {/* Submit Button */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={formik.isSubmitting || isLoading}
                  startIcon={formik.isSubmitting ? <CircularProgress size={20} /> : null}
                >
                  {formik.isSubmitting ? 'Submitting...' : `${testMode.charAt(0).toUpperCase() + testMode.slice(1)} QA Test`}
                </Button>
                
                <Typography variant="body2" color="text.secondary">
                  State: {submissionState}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  );
};

export default QATestForm;

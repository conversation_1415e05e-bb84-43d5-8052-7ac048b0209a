#!/usr/bin/env node
// @since 2024-1-1 to 2025-25-7

/**
 * Memory-optimized development server startup script
 * Clears caches and starts Vite with optimal memory settings
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧹 Clearing caches and optimizing for development...\n');

// Directories to clean
const cacheDirs = [
  'node_modules/.vite',
  'node_modules/.cache',
  '.vite',
  'dist'
];

// Clean cache directories
cacheDirs.forEach(dir => {
  const fullPath = path.join(__dirname, dir);
  if (fs.existsSync(fullPath)) {
    console.log(`🗑️  Removing ${dir}...`);
    try {
      fs.rmSync(fullPath, { recursive: true, force: true });
    } catch (error) {
      console.warn(`⚠️  Could not remove ${dir}:`, error.message);
    }
  }
});

// Clear npm cache
console.log('🧽 Clearing npm cache...');
try {
  execSync('npm cache clean --force', { stdio: 'inherit' });
} catch (error) {
  console.warn('⚠️  Could not clear npm cache:', error.message);
}

// Set memory optimization environment variables
process.env.NODE_OPTIONS = '--max-old-space-size=8192 --optimize-for-size';
process.env.VITE_DISABLE_ESLINT_PLUGIN = 'true';
process.env.VITE_GENERATE_SOURCEMAP = 'false';

console.log('\n🚀 Starting development server with memory optimizations...');
console.log('📊 Memory limit: 8GB');
console.log('⚡ Optimizations: ESLint disabled, sourcemaps disabled\n');

// Start the development server
try {
  execSync('npm run dev:stable', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_OPTIONS: '--max-old-space-size=8192 --optimize-for-size'
    }
  });
} catch (error) {
  console.error('❌ Failed to start development server:', error.message);
  process.exit(1);
}

<!-- @since 2024-1-1 to 2025-25-7 -->
# Enhanced Admin API Documentation

## Overview

The Enhanced Admin API provides comprehensive user management capabilities for the ACEO platform. This API is designed for production use with enterprise-grade features including rate limiting, audit logging, bulk operations, and advanced analytics.

## Base URL

```
https://api.b2binfluencer.com/api/admin
```

## Authentication

All endpoints require admin authentication using Bearer tokens:

```http
Authorization: Bearer <admin_jwt_token>
```

## Rate Limiting

The API implements rate limiting to ensure system stability:

| Endpoint Category | Rate Limit |
|------------------|------------|
| User List | 100 requests/minute |
| User Create | 20 requests/minute |
| User Update | 50 requests/minute |
| User Delete | 10 requests/minute |
| Bulk Operations | 10 requests/minute |
| Export | 5 requests/minute |
| Analytics | 30 requests/minute |
| Notifications | 50 requests/minute |

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Timestamp when limit resets

## Correlation IDs

All requests include correlation IDs for tracing:
- Request header: `X-Correlation-ID`
- Response header: `X-Correlation-ID`

## User CRUD Operations

### List Users

```http
GET /api/admin/users
```

**Query Parameters:**
- `skip` (int): Number of records to skip (default: 0)
- `limit` (int): Number of records to return (1-100, default: 10)
- `search` (string): Search term for email, name, or company
- `user_status` (enum): Filter by status (all, active, inactive, unverified, admin)
- `plan` (string): Filter by subscription plan
- `sort_by` (enum): Field to sort by (email, full_name, created_at, last_login, subscription_plan, is_active)
- `sort_order` (enum): Sort order (asc, desc)
- `start_date` (datetime): Filter users created after this date
- `end_date` (datetime): Filter users created before this date
- `include_deleted` (boolean): Include soft-deleted users

**Response:**
```json
{
  "total": 150,
  "users": [...],
  "page": 1,
  "page_size": 10,
  "pages": 15,
  "filters_applied": {...},
  "sort_info": {...},
  "export_available": true
}
```

### Create User

```http
POST /api/admin/users
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "password": "SecurePass123!",
  "company_name": "Acme Inc.",
  "phone": "+1234567890",
  "location": "New York, NY",
  "subscription_plan": "creator",
  "is_active": true,
  "is_admin": false,
  "email_verified": false,
  "notes": "VIP customer",
  "send_welcome_email": true
}
```

**Password Requirements:**
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one digit

### Update User

```http
PUT /api/admin/users/{user_id}
```

**Request Body:**
```json
{
  "full_name": "Updated Name",
  "subscription_plan": "accelerator",
  "is_active": true,
  "notes": "Updated notes",
  "reset_password": false
}
```

### Delete User

```http
DELETE /api/admin/users/{user_id}
```

Performs soft deletion, preserving data for audit purposes.

### Export Users

```http
GET /api/admin/users/export
```

**Query Parameters:**
- `format` (enum): Export format (csv, excel, json)
- `search`, `user_status`, `plan`, `include_deleted`: Same as list users

**Response:** File download with appropriate content type

## Bulk Operations

### Execute Bulk Operation

```http
POST /api/admin/users/bulk
```

**Request Body:**
```json
{
  "user_ids": ["user_id_1", "user_id_2", "user_id_3"],
  "operation": "activate",
  "data": {
    "plan": "accelerator"
  },
  "reason": "Bulk upgrade for VIP customers",
  "notify_users": true
}
```

**Supported Operations:**
- `activate`: Activate users
- `deactivate`: Deactivate users
- `verify_email`: Verify email addresses
- `unverify_email`: Unverify email addresses
- `change_plan`: Change subscription plans (requires `data.plan`)
- `send_notification`: Send notifications (requires `data.message`)
- `delete`: Soft delete users
- `reset_password`: Trigger password resets

**Response:**
```json
{
  "operation": "activate",
  "total_requested": 3,
  "successful": 2,
  "failed": 1,
  "errors": [
    {
      "user_id": "user_id_3",
      "error": "User not found"
    }
  ],
  "operation_id": "op_12345",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Analytics & Reporting

### User Analytics

```http
GET /api/admin/analytics/users
```

**Response:**
```json
{
  "total_users": 1500,
  "active_users": 1200,
  "inactive_users": 300,
  "unverified_users": 150,
  "new_users_today": 25,
  "new_users_this_week": 180,
  "new_users_this_month": 750,
  "user_growth_rate": 15.5,
  "churn_rate": 2.1,
  "plan_distribution": [
    {
      "plan_name": "creator",
      "count": 800,
      "percentage": 53.3
    }
  ],
  "activity_metrics": {...},
  "geographic_distribution": [...],
  "last_updated": "2024-01-15T10:30:00Z"
}
```

### User Trends

```http
GET /api/admin/analytics/users/trends
```

**Query Parameters:**
- `period` (string): Time period (7d, 30d, 90d, 1y)
- `metrics` (array): Metrics to include (signups, active_users, churn_rate)

## Activity Logging

### User Activity Log

```http
GET /api/admin/users/{user_id}/activity
```

**Query Parameters:**
- `skip`, `limit`: Pagination
- `action` (string): Filter by action type
- `level` (enum): Filter by level (info, warning, error, success)
- `start_date`, `end_date`: Date range filter

### System Activity Log

```http
GET /api/admin/activity
```

Same parameters as user activity log, plus:
- `search` (string): Search in actions and descriptions

**Response:**
```json
{
  "total": 500,
  "activities": [
    {
      "id": "activity_123",
      "user_id": "user_456",
      "admin_id": "admin_789",
      "action": "update_user",
      "description": "Updated user email",
      "level": "info",
      "ip_address": "***********",
      "metadata": {...},
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ],
  "page": 1,
  "page_size": 50,
  "pages": 10
}
```

## User Communication

### Send Notification

```http
POST /api/admin/users/{user_id}/notify
```

**Request Body:**
```json
{
  "message": "Your account has been upgraded",
  "title": "Account Upgrade",
  "notification_type": "success",
  "send_email": true,
  "priority": "normal"
}
```

**Notification Types:** info, warning, success, error
**Priority Levels:** low, normal, high, urgent

### Reset Password

```http
POST /api/admin/users/{user_id}/reset-password
```

**Request Body:**
```json
{
  "send_email": true,
  "temporary_password": null,
  "force_change": true
}
```

## Dashboard & System

### Dashboard Statistics

```http
GET /api/admin/stats
```

**Response:**
```json
{
  "total_users": 1500,
  "active_users": 1200,
  "new_users_today": 25,
  "new_users_this_week": 180,
  "system_health": {
    "database_status": "connected",
    "api_status": "operational",
    "cache_status": "operational",
    "email_service_status": "operational"
  },
  "recent_activity_count": 45,
  "alerts": [],
  "last_updated": "2024-01-15T10:30:00Z"
}
```

### Health Check

```http
GET /api/admin/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "cache": "healthy",
    "email": "healthy"
  },
  "metrics": {
    "response_time_ms": 45,
    "memory_usage_mb": 512,
    "cpu_usage_percent": 15
  }
}
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Validation failed",
  "error": {
    "code": "VALIDATION_ERROR",
    "details": {...},
    "validation_errors": [...]
  },
  "correlation_id": "corr_12345",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Common HTTP Status Codes:**
- `200`: Success
- `201`: Created
- `204`: No Content
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `429`: Rate Limited
- `500`: Internal Server Error

## Security Features

- **Admin-only access**: All endpoints require admin privileges
- **Rate limiting**: Prevents abuse and ensures system stability
- **Audit logging**: Complete trail of all administrative actions
- **Correlation IDs**: Request tracing for debugging
- **Input validation**: Comprehensive validation and sanitization
- **CSRF protection**: Cross-site request forgery prevention
- **SQL injection prevention**: Parameterized queries
- **XSS protection**: Output encoding and validation

## Production Considerations

- **Caching**: Analytics endpoints cached for 15 minutes
- **Circuit breakers**: External service call protection
- **Monitoring**: Health checks and performance metrics
- **Backup**: Soft deletion preserves data
- **Scalability**: Pagination and filtering for large datasets
- **Performance**: Optimized queries and indexing

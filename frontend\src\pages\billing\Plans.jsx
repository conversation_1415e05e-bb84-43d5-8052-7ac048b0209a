// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Alert,
  Switch,
  FormControlLabel,
  useTheme,
  alpha,
  Paper,
  Divider,
  Collapse
} from '@mui/material';
import {
  Check as CheckIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Rocket as RocketIcon,
  WorkspacePremium as WorkspacePremiumIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';


import { useNotification } from '../../hooks/useNotification';
import { useSubscription } from '../../contexts/SubscriptionContext';
import api from '../../api/index';

const Plans = ({ isEmbedded = false, onError }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { showSuccessNotification } = useNotification();
  const { subscription, featureLimits, usage } = useSubscription();

  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isYearly, setIsYearly] = useState(false);
  const [currentSubscription, setCurrentSubscription] = useState(null);

  // State for tracking expanded feature categories for each plan
  const [expandedCategories, setExpandedCategories] = useState({});

  // Real plans data with actual implemented features
  const realPlans = useMemo(() => [
    {
      id: 'creator',
      name: 'Creator',
      description: 'Perfect for individual content creators and small businesses getting started',
      price_monthly: 19,
      price_yearly: 190,
      is_popular: false,
      trial_days: 14,
      features: {
        'Content Generation': [
          '50 AI-generated posts per month',
          'General content creation',
          'Basic brand voice consistency',
          'Content library access',
          'Image studio (basic)',
          '1 regeneration credit per month'
        ],
        'Analytics & Insights': [
          'Basic analytics dashboard',
          'Performance overview',
          'Content performance tracking',
          'Basic sentiment analysis',
          'Social media metrics'
        ],
        'Social Media Management': [
          '3 social media accounts',
          'Content scheduling',
          'Calendar view',
          'Basic posting times'
        ],
        'Support & Training': [
          'Email support',
          'Knowledge base access',
          'Basic tutorials'
        ]
      },
      limitations: [
        'Limited to 3 social platforms',
        'Basic analytics only',
        'No team collaboration',
        'No competitor insights',
        'No A/B testing',
        'No advanced automation'
      ],
      usage_limits: {
        monthly_posts: 50,
        social_accounts: 3,
        team_members: 1,
        regeneration_credits: 1,
        ai_auto_replies: 100,
        document_training: 0
      }
    },
    {
      id: 'accelerator',
      name: 'Accelerator',
      description: 'Ideal for growing businesses, marketing teams, and agencies',
      price_monthly: 99,
      price_yearly: 990,
      is_popular: true,
      trial_days: 14,
      features: {
        'Content Generation': [
          '500 AI-generated posts per month',
          'Advanced content creation',
          'Brand voice consistency',
          'Competitor insight content',
          'Image studio (advanced)',
          '100 regeneration credits per month',
          'Document training (10 docs)',
          'Policy compliance checking'
        ],
        'Analytics & Insights': [
          'Advanced analytics dashboard',
          'Comprehensive performance metrics',
          'Audience demographics',
          'Advanced sentiment analysis',
          'Sentiment trend analysis',
          'Campaign performance tracking',
          'ICP performance metrics',
          'Competitor insights dashboard'
        ],
        'Social Media Management': [
          '10 social media accounts',
          'Advanced scheduling',
          'Optimal posting times',
          'Smart recommendations',
          'Calendar collaboration'
        ],
        'Team Collaboration': [
          '5 team members',
          'Team management',
          'Shared calendars',
          'Role-based permissions',
          'Team invitations'
        ],
        'Testing & Optimization': [
          'A/B testing (3 variants)',
          'Campaign comparison',
          'Performance optimization',
          'Content testing'
        ],
        'Automation': [
          '1,000 AI auto-replies per month',
          'Automated responses',
          'Smart scheduling',
          'Bulk response management'
        ],
        'Support & Training': [
          'Priority email support',
          'Advanced tutorials',
          'Best practices guide',
          'Onboarding assistance'
        ]
      },
      limitations: [
        'Limited to 10 social platforms',
        'Team size limited to 5 members',
        'A/B testing limited to 3 variants'
      ],
      usage_limits: {
        monthly_posts: 500,
        social_accounts: 10,
        team_members: 5,
        regeneration_credits: 100,
        ai_auto_replies: 1000,
        document_training: 10,
        ab_test_variants: 3
      }
    },
    {
      id: 'dominator',
      name: 'Dominator',
      description: 'Enterprise solution for large teams, agencies, and enterprise clients',
      price_monthly: 249,
      price_yearly: 2490,
      is_popular: false,
      trial_days: 14,
      features: {
        'Content Generation': [
          'Unlimited AI-generated posts',
          'Enterprise content creation',
          'Advanced brand voice consistency',
          'White-label AI responses',
          'Multi-language AI support',
          '500 regeneration credits per month',
          'Document training (50 docs)',
          'Advanced policy compliance'
        ],
        'Analytics & Insights': [
          'Enterprise analytics suite',
          'Custom reporting',
          'Advanced competitor insights',
          'Strategic recommendations',
          'Predictive analytics',
          'Export capabilities',
          'API access for analytics'
        ],
        'Social Media Management': [
          'Unlimited social media accounts',
          'Advanced automation workflows',
          'Custom integrations',
          'White-label scheduling',
          'Enterprise calendar management'
        ],
        'Team Collaboration': [
          'Unlimited team members',
          'Advanced team management',
          'Custom roles & permissions',
          'Department organization',
          'Enterprise SSO integration'
        ],
        'Testing & Optimization': [
          'Unlimited A/B testing',
          'Advanced campaign optimization',
          'Multi-variant testing',
          'Statistical significance tracking'
        ],
        'Automation': [
          '5,000 AI auto-replies per month',
          'Advanced automation workflows',
          'Custom response templates',
          'Enterprise integrations'
        ],
        'Enterprise Features': [
          'White-label platform options',
          'Custom branding',
          'Dedicated account manager',
          'Priority feature requests',
          'Custom development',
          'SLA guarantees'
        ],
        'Support & Training': [
          '24/7 dedicated support',
          'Phone support',
          'Custom training sessions',
          'Implementation assistance',
          'Success manager'
        ]
      },
      limitations: [],
      usage_limits: {
        monthly_posts: -1, // Unlimited
        social_accounts: -1, // Unlimited
        team_members: -1, // Unlimited
        regeneration_credits: 500,
        ai_auto_replies: 5000,
        document_training: 50,
        ab_test_variants: -1 // Unlimited
      }
    }
  ], []);

  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true);
        
        // Try to fetch from API, fallback to real plans data
        try {
          const [plansResponse, subscriptionResponse] = await Promise.all([
            api.getSubscriptionPlans(),
            api.getUserSubscription()
          ]);

          setPlans(plansResponse.data || realPlans);
          setCurrentSubscription(subscriptionResponse.data);
        } catch (apiError) {
          console.warn('API not available, using real plans data:', apiError);
          setPlans(realPlans);
        }
        
      } catch (error) {
        console.error('Error fetching plans:', error);
        setError('Failed to load subscription plans');
        if (onError) onError(error);
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, [onError, realPlans]);

  const getPlanColor = (planId) => {
    const colors = {
      creator: theme.palette.info.main,
      accelerator: theme.palette.primary.main,
      dominator: theme.palette.warning.main
    };
    return colors[planId] || theme.palette.grey[500];
  };

  const getPlanIcon = (planId) => {
    const icons = {
      creator: TrendingUpIcon,
      accelerator: RocketIcon,
      dominator: WorkspacePremiumIcon
    };
    const IconComponent = icons[planId] || StarIcon;
    return <IconComponent />;
  };

  const handleSelectPlan = (plan) => {
    if (currentSubscription?.plan_id === plan.id) {
      showSuccessNotification('You are already subscribed to this plan');
      return;
    }

    navigate('/billing/checkout', {
      state: { plan: { ...plan, is_yearly: isYearly } }
    });
  };

  const isCurrentPlan = (planId) => {
    return currentSubscription?.plan_id === planId && currentSubscription?.status === 'active';
  };



  // Get current usage for a feature
  const getCurrentUsage = (feature) => {
    if (!usage) return 0;

    switch (feature) {
      case 'monthly_posts':
        return usage.monthly_posts_used || 0;
      case 'ai_auto_replies':
        return usage.ai_auto_replies_used || 0;
      case 'regeneration_credits':
        return usage.regeneration_credits_used || 0;
      case 'document_training':
        return usage.documents_uploaded || 0;
      default:
        return 0;
    }
  };

  // Get feature limit for current plan
  const getFeatureLimit = (feature) => {
    if (!featureLimits) return 0;
    return featureLimits[feature] || 0;
  };

  // Check if feature is available in a plan
  const isFeatureAvailableInPlan = (planId, feature) => {
    const planTiers = { creator: 0, accelerator: 1, dominator: 2 };
    const targetTier = planTiers[planId] || 0;

    // Feature availability based on plan tier
    const featureRequirements = {
      'team_collaboration': 1, // Accelerator+
      'ab_testing': 1, // Accelerator+
      'advanced_sentiment_analysis': 1, // Accelerator+
      'competitor_insights': 1, // Accelerator+
      'document_training': 1, // Accelerator+
      'policy_compliance': 1, // Accelerator+
      'white_label_ai_responses': 2, // Dominator only
      'multi_language_ai_support': 2, // Dominator only
      'unlimited_features': 2, // Dominator only
      'enterprise_features': 2, // Dominator only
    };

    const requiredTier = featureRequirements[feature];
    return requiredTier === undefined || targetTier >= requiredTier;
  };

  // Toggle expanded state for a specific category in a specific plan
  const toggleCategoryExpansion = (planId, category) => {
    const key = `${planId}-${category}`;
    setExpandedCategories(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Check if a category is expanded for a specific plan
  const isCategoryExpanded = (planId, category) => {
    const key = `${planId}-${category}`;
    return expandedCategories[key] || false;
  };

  const displayPlans = plans.length > 0 ? plans : realPlans;

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: isEmbedded ? 0 : 3 }}>
      {!isEmbedded && (
        <>
          <Helmet>
            <title>Subscription Plans | B2B Influencer Tool</title>
            <meta name="description" content="Choose the perfect plan for your content creation needs" />
          </Helmet>
          
          <Box sx={{ textAlign: "center", mb: 4 }}>
            <Typography variant="h4" gutterBottom>
              Choose Your Plan
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ maxWidth: 700, mx: "auto", mb: 1 }}
            >
              Select the plan that best fits your content creation needs. All plans include
              <strong> 14-day free trial</strong>, advanced AI content generation, and
              <strong> comprehensive analytics</strong> to optimize your social media strategy.
            </Typography>

            {/* Current Plan Status */}
            {subscription && (
              <Box sx={{ mt: 2, p: 2, backgroundColor: alpha(theme.palette.info.main, 0.1), borderRadius: 2, maxWidth: 600, mx: 'auto' }}>
                <Typography variant="body2" color="info.main">
                  <strong>Current Plan:</strong> {subscription.plan_name}
                  {subscription.is_trial && ` (Trial)`}
                  {subscription.status === 'active' && ' ✓'}
                </Typography>
                {usage && (
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                    This month: {getCurrentUsage('monthly_posts')} posts generated • {getCurrentUsage('ai_auto_replies')} AI replies • {getCurrentUsage('regeneration_credits')} credits used
                  </Typography>
                )}
              </Box>
            )}
          </Box>
        </>
      )}

      {/* Billing Toggle */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
        <Paper sx={{ p: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="body2" color={!isYearly ? 'primary' : 'text.secondary'}>
            Monthly
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={isYearly}
                onChange={(e) => setIsYearly(e.target.checked)}
                color="primary"
              />
            }
            label=""
          />
          <Typography variant="body2" color={isYearly ? 'primary' : 'text.secondary'}>
            Yearly
          </Typography>
          <Chip
            label="Save 17%"
            size="small"
            color="success"
            sx={{ ml: 1 }}
          />
        </Paper>
      </Box>

      <Grid container spacing={4} justifyContent="center">
        {displayPlans.map((plan) => (
          <Grid item xs={12} lg={4} key={plan.id}>
            <Card
              elevation={3}
              sx={{
                height: "auto", // Dynamic height based on content
                minHeight: "600px", // Minimum height for consistency
                display: "flex",
                flexDirection: "column",
                position: "relative",
                backdropFilter: "blur(20px)",
                backgroundColor: alpha(getPlanColor(plan.id), 0.05),
                border: `2px solid ${alpha(getPlanColor(plan.id), 0.3)}`,
                borderRadius: 2,
                transition: "all 0.3s ease-in-out", // Smooth transitions for height changes
                "&:hover": {
                  transform: "translateY(-4px)",
                  boxShadow: `0 8px 25px ${alpha(getPlanColor(plan.id), 0.3)}`,
                },
                ...(plan.is_popular && {
                  border: `3px solid ${theme.palette.primary.main}`,
                  boxShadow: `0 0 20px ${alpha(theme.palette.primary.main, 0.3)}`,
                }),
                ...(isCurrentPlan(plan.id) && {
                  border: `3px solid ${theme.palette.success.main}`,
                  backgroundColor: alpha(theme.palette.success.main, 0.05),
                })
              }}
            >
              {plan.is_popular && (
                <Box
                  sx={{
                    position: "absolute",
                    top: -12,
                    left: "50%",
                    transform: "translateX(-50%)",
                    zIndex: 1,
                  }}
                >
                  <Chip
                    label="Most Popular"
                    color="primary"
                    size="small"
                    icon={<StarIcon />}
                    sx={{
                      fontWeight: "bold",
                      boxShadow: 2,
                    }}
                  />
                </Box>
              )}

              {isCurrentPlan(plan.id) && (
                <Box
                  sx={{
                    position: "absolute",
                    top: -12,
                    right: 16,
                    zIndex: 1,
                  }}
                >
                  <Chip
                    label="Current Plan"
                    color="success"
                    size="small"
                    icon={<CheckIcon />}
                    sx={{
                      fontWeight: "bold",
                      boxShadow: 2,
                    }}
                  />
                </Box>
              )}

              <CardContent sx={{ flexGrow: 1, pt: plan.is_popular ? 4 : 3 }}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <Box
                    sx={{
                      p: 1,
                      borderRadius: 1,
                      backgroundColor: alpha(getPlanColor(plan.id), 0.1),
                      color: getPlanColor(plan.id),
                      mr: 2,
                    }}
                  >
                    {getPlanIcon(plan.id)}
                  </Box>
                  <Typography variant="h5" component="h3" fontWeight="bold">
                    {plan.name}
                  </Typography>
                </Box>

                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mb: 3, minHeight: 40 }}
                >
                  {plan.description}
                </Typography>

                <Box sx={{ mb: 3 }}>
                  <Typography
                    variant="h3"
                    component="div"
                    fontWeight="bold"
                    color={getPlanColor(plan.id)}
                  >
                    ${isYearly ? plan.price_yearly : plan.price_monthly}
                    <Typography
                      variant="body2"
                      component="span"
                      color="text.secondary"
                      sx={{ ml: 1 }}
                    >
                      /{isYearly ? 'year' : 'month'}
                    </Typography>
                  </Typography>
                  {isYearly && (
                    <Typography variant="caption" color="success.main">
                      Save ${(plan.price_monthly * 12) - plan.price_yearly} per year
                    </Typography>
                  )}
                  {plan.trial_days && (
                    <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 0.5 }}>
                      {plan.trial_days}-day free trial included
                    </Typography>
                  )}
                </Box>

                {/* Current Usage Indicators (for current plan) */}
                {isCurrentPlan(plan.id) && usage && (
                  <Box sx={{ mb: 2, p: 2, backgroundColor: alpha(theme.palette.success.main, 0.1), borderRadius: 1 }}>
                    <Typography variant="subtitle2" color="success.main" gutterBottom>
                      Your Current Usage
                    </Typography>
                    <Grid container spacing={1}>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">
                          Posts: {getCurrentUsage('monthly_posts')}/{getFeatureLimit('monthly_posts') === -1 ? '∞' : getFeatureLimit('monthly_posts')}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="caption" color="text.secondary">
                          Credits: {getCurrentUsage('regeneration_credits')}/{getFeatureLimit('regeneration_credits')}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                )}

                {/* Expandable/Collapsible Categorized Features */}
                <Box sx={{ mb: 2 }}>
                  {Object.entries(plan.features).map(([category, features]) => {
                    const isExpanded = isCategoryExpanded(plan.id, category);
                    const initialDisplayCount = 3; // Show first 3 features by default
                    const hasMoreFeatures = features.length > initialDisplayCount;
                    const displayFeatures = isExpanded ? features : features.slice(0, initialDisplayCount);

                    return (
                      <Box key={category} sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                          <Typography
                            variant="subtitle2"
                            sx={{
                              fontWeight: 'bold',
                              color: getPlanColor(plan.id),
                              fontSize: '0.8rem',
                            }}
                          >
                            {category}
                          </Typography>
                          {hasMoreFeatures && (
                            <Button
                              size="small"
                              onClick={() => toggleCategoryExpansion(plan.id, category)}
                              sx={{
                                minWidth: 'auto',
                                p: 0.5,
                                color: getPlanColor(plan.id),
                                fontSize: '0.7rem',
                                textTransform: 'none',
                                '&:hover': {
                                  backgroundColor: alpha(getPlanColor(plan.id), 0.1),
                                }
                              }}
                              endIcon={
                                isExpanded ?
                                <ExpandLessIcon sx={{ fontSize: 16 }} /> :
                                <ExpandMoreIcon sx={{ fontSize: 16 }} />
                              }
                            >
                              {isExpanded ? 'Show Less' : `+${features.length - initialDisplayCount} more`}
                            </Button>
                          )}
                        </Box>

                        <List dense sx={{ pl: 1 }}>
                          {displayFeatures.map((feature, index) => (
                            <ListItem key={index} sx={{ px: 0, py: 0.25 }}>
                              <ListItemIcon sx={{ minWidth: 24 }}>
                                <CheckIcon
                                  sx={{
                                    color: getPlanColor(plan.id),
                                    fontSize: 16,
                                  }}
                                />
                              </ListItemIcon>
                              <ListItemText
                                primary={feature}
                                primaryTypographyProps={{
                                  variant: "caption",
                                  fontSize: "0.75rem",
                                  lineHeight: 1.3,
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>

                        {/* Collapsible section for additional features */}
                        {hasMoreFeatures && (
                          <Collapse
                            in={isExpanded}
                            timeout={300}
                            sx={{
                              '& .MuiCollapse-wrapper': {
                                transition: 'height 0.3s ease-in-out',
                              }
                            }}
                          >
                            <List dense sx={{ pl: 1 }}>
                              {features.slice(initialDisplayCount).map((feature, index) => (
                                <ListItem key={index + initialDisplayCount} sx={{ px: 0, py: 0.25 }}>
                                  <ListItemIcon sx={{ minWidth: 24 }}>
                                    <CheckIcon
                                      sx={{
                                        color: getPlanColor(plan.id),
                                        fontSize: 16,
                                      }}
                                    />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={feature}
                                    primaryTypographyProps={{
                                      variant: "caption",
                                      fontSize: "0.75rem",
                                      lineHeight: 1.3,
                                    }}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </Collapse>
                        )}
                      </Box>
                    );
                  })}
                </Box>

                {plan.limitations && plan.limitations.length > 0 && (
                  <>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="caption" color="text.secondary" gutterBottom>
                      Limitations:
                    </Typography>
                    <List dense>
                      {plan.limitations.map((limitation, index) => (
                        <ListItem key={index} sx={{ px: 0, py: 0.25 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <CloseIcon
                              sx={{
                                color: 'text.secondary',
                                fontSize: 16,
                              }}
                            />
                          </ListItemIcon>
                          <ListItemText
                            primary={limitation}
                            primaryTypographyProps={{
                              variant: "caption",
                              color: "text.secondary",
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </>
                )}
              </CardContent>

              <CardActions sx={{ p: 3, pt: 0 }}>
                {/* Upgrade Recommendation */}
                {!isCurrentPlan(plan.id) && subscription && (
                  <Box sx={{ width: '100%', mb: 2 }}>
                    {subscription.plan_id === 'creator' && plan.id === 'accelerator' && (
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="caption">
                          Recommended upgrade for team collaboration and advanced analytics
                        </Typography>
                      </Alert>
                    )}
                    {subscription.plan_id === 'accelerator' && plan.id === 'dominator' && (
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="caption">
                          Unlock unlimited features and enterprise capabilities
                        </Typography>
                      </Alert>
                    )}
                  </Box>
                )}

                <Button
                  variant={plan.is_popular ? "contained" : "outlined"}
                  fullWidth
                  size="large"
                  onClick={() => handleSelectPlan(plan)}
                  disabled={isCurrentPlan(plan.id)}
                  sx={{
                    py: 1.5,
                    fontWeight: "bold",
                    ...(plan.is_popular && {
                      background: `linear-gradient(45deg, ${getPlanColor(plan.id)}, ${alpha(getPlanColor(plan.id), 0.8)})`,
                      boxShadow: `0 4px 15px ${alpha(getPlanColor(plan.id), 0.4)}`,
                      "&:hover": {
                        boxShadow: `0 6px 20px ${alpha(getPlanColor(plan.id), 0.6)}`,
                      },
                    }),
                  }}
                >
                  {isCurrentPlan(plan.id) ? 'Current Plan' :
                   subscription && subscription.plan_id !== plan.id ? 'Upgrade to ' + plan.name :
                   'Select Plan'}
                </Button>

                {/* Feature Access Indicator */}
                {!isCurrentPlan(plan.id) && (
                  <Typography variant="caption" color="text.secondary" sx={{ textAlign: 'center', mt: 1, display: 'block' }}>
                    {plan.trial_days ? `Start with ${plan.trial_days}-day free trial` : 'Immediate access after payment'}
                  </Typography>
                )}
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Feature Comparison Table */}
      {!isEmbedded && (
        <Box sx={{ mt: 6 }}>
          <Typography variant="h5" gutterBottom textAlign="center">
            Feature Comparison
          </Typography>
          <Paper sx={{ p: 3, mt: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Typography variant="h6" gutterBottom>
                  Key Features
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText primary="Monthly Posts" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Social Accounts" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Team Members" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Regeneration Credits" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="AI Auto-Replies" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="A/B Testing" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="Competitor Insights" />
                  </ListItem>
                  <ListItem>
                    <ListItemText primary="White-label Features" />
                  </ListItem>
                </List>
              </Grid>

              {displayPlans.map((plan) => (
                <Grid item xs={12} md={2.67} key={plan.id}>
                  <Typography variant="h6" gutterBottom color={getPlanColor(plan.id)}>
                    {plan.name}
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary={plan.usage_limits.monthly_posts === -1 ? 'Unlimited' : plan.usage_limits.monthly_posts} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={plan.usage_limits.social_accounts === -1 ? 'Unlimited' : plan.usage_limits.social_accounts} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={plan.usage_limits.team_members === -1 ? 'Unlimited' : plan.usage_limits.team_members} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={plan.usage_limits.regeneration_credits} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={plan.usage_limits.ai_auto_replies} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={plan.usage_limits.ab_test_variants === -1 ? 'Unlimited' : plan.usage_limits.ab_test_variants || 'None'} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={isFeatureAvailableInPlan(plan.id, 'competitor_insights') ? 'Yes' : 'No'} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary={isFeatureAvailableInPlan(plan.id, 'white_label_ai_responses') ? 'Yes' : 'No'} />
                    </ListItem>
                  </List>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Box>
      )}

      {!isEmbedded && (
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Typography variant="body2" color="text.secondary">
            All plans include a 14-day free trial. No credit card required.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Upgrade or downgrade anytime. Cancel with one click.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default Plans;

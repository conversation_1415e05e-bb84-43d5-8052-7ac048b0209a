import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ImageAnnotationTool from '../ImageAnnotationTool';

// Mock the auth context
const mockAuth = {
  user: {
    id: 'user1',
    name: '<PERSON>'
  }
};

vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth,
}));

// Mock the snackbar context
const mockSnackbar = {
  showSnackbar: vi.fn()
};

vi.mock('../../../contexts/SnackbarContext', () => ({
  useSnackbar: () => mockSnackbar,
}));

// Mock the collaboration context
const mockCollaboration = {
  isConnected: true,
  activeUsers: [
    {
      id: 'user1',
      name: '<PERSON>'
    },
    {
      id: 'user2',
      name: '<PERSON>'
    }
  ],
  sendAnnotation: vi.fn(),
  onAnnotationReceived: vi.fn()
};

vi.mock('../../../contexts/CollaborationContext', () => ({
  useCollaboration: () => mockCollaboration,
}));

// Mock the annotations API
const mockCreateImageAnnotation = vi.fn();
const mockGetImageAnnotations = vi.fn();

vi.mock('../../../api/annotations', () => ({
  createImageAnnotation: mockCreateImageAnnotation,
  getImageAnnotations: mockGetImageAnnotations,
}));

// Mock canvas context
const mockCanvasContext = {
  clearRect: vi.fn(),
  beginPath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  stroke: vi.fn(),
  fillText: vi.fn(),
  measureText: vi.fn(() => ({ width: 50 })),
  save: vi.fn(),
  restore: vi.fn(),
  scale: vi.fn(),
  translate: vi.fn(),
  strokeStyle: '',
  lineWidth: 1,
  font: '',
  fillStyle: '',
  globalAlpha: 1
};

// Mock HTMLCanvasElement.getContext
HTMLCanvasElement.prototype.getContext = vi.fn(() => mockCanvasContext);

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
        dark: '#3730A3',
        contrastText: '#FFFFFF',
      },
      text: {
        primary: '#000000',
      },
      action: {
        hover: '#F5F5F5',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ImageAnnotationTool', () => {
  const mockProps = {
    contentId: 'test-content-id',
    imageId: 'test-image-id',
    imageUrl: 'https://example.com/test-image.jpg',
    calendarToken: 'test-token',
    readOnly: false,
    onAnnotationAdded: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetImageAnnotations.mockResolvedValue([]);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders image annotation tool correctly', () => {
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Should render the image
    expect(screen.getByRole('img')).toBeInTheDocument();
    
    // Should render toolbar buttons
    expect(screen.getByLabelText('Brush tool')).toBeInTheDocument();
    expect(screen.getByLabelText('Text tool')).toBeInTheDocument();
    expect(screen.getByLabelText('Pan tool')).toBeInTheDocument();
  });

  test('displays toolbar with annotation tools', () => {
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Check for toolbar buttons
    expect(screen.getByLabelText('Brush tool')).toBeInTheDocument();
    expect(screen.getByLabelText('Text tool')).toBeInTheDocument();
    expect(screen.getByLabelText('Pan tool')).toBeInTheDocument();
    expect(screen.getByLabelText('Undo')).toBeInTheDocument();
    expect(screen.getByLabelText('Redo')).toBeInTheDocument();
    expect(screen.getByLabelText('Save annotations')).toBeInTheDocument();
  });

  test('handles tool selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Select text tool
    const textTool = screen.getByLabelText('Text tool');
    await user.click(textTool);

    // Tool should be selected (visual feedback would be tested in integration tests)
    expect(textTool).toBeInTheDocument();
  });

  test('handles color selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Click color button to open color menu
    const colorButton = screen.getByLabelText('Color');
    await user.click(colorButton);

    // Should show color options
    expect(screen.getByText('Red')).toBeInTheDocument();
    expect(screen.getByText('Blue')).toBeInTheDocument();
    expect(screen.getByText('Green')).toBeInTheDocument();
  });

  test('handles brush size adjustment', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Should show brush size slider
    const slider = screen.getByRole('slider');
    expect(slider).toBeInTheDocument();

    // Adjust brush size
    fireEvent.change(slider, { target: { value: 5 } });
    expect(slider).toHaveValue('5');
  });

  test('handles canvas drawing', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    const canvas = screen.getByRole('img').nextSibling; // Canvas is next to image
    
    // Simulate mouse down, move, and up for drawing
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 });
    fireEvent.mouseMove(canvas, { clientX: 150, clientY: 150 });
    fireEvent.mouseUp(canvas);

    // Canvas context methods should be called
    expect(mockCanvasContext.beginPath).toHaveBeenCalled();
    expect(mockCanvasContext.moveTo).toHaveBeenCalled();
    expect(mockCanvasContext.lineTo).toHaveBeenCalled();
    expect(mockCanvasContext.stroke).toHaveBeenCalled();
  });

  test('handles undo functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Draw something first
    const canvas = screen.getByRole('img').nextSibling;
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 });
    fireEvent.mouseMove(canvas, { clientX: 150, clientY: 150 });
    fireEvent.mouseUp(canvas);

    // Click undo
    const undoButton = screen.getByLabelText('Undo');
    await user.click(undoButton);

    // Canvas should be cleared
    expect(mockCanvasContext.clearRect).toHaveBeenCalled();
  });

  test('handles redo functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Draw, undo, then redo
    const canvas = screen.getByRole('img').nextSibling;
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 });
    fireEvent.mouseMove(canvas, { clientX: 150, clientY: 150 });
    fireEvent.mouseUp(canvas);

    const undoButton = screen.getByLabelText('Undo');
    await user.click(undoButton);

    const redoButton = screen.getByLabelText('Redo');
    await user.click(redoButton);

    // Canvas drawing methods should be called again
    expect(mockCanvasContext.stroke).toHaveBeenCalled();
  });

  test('handles save annotations', async () => {
    const user = userEvent.setup();
    
    mockCreateImageAnnotation.mockResolvedValue({
      id: 'annotation-1',
      type: 'drawing',
      data: {}
    });
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Draw something
    const canvas = screen.getByRole('img').nextSibling;
    fireEvent.mouseDown(canvas, { clientX: 100, clientY: 100 });
    fireEvent.mouseMove(canvas, { clientX: 150, clientY: 150 });
    fireEvent.mouseUp(canvas);

    // Save annotations
    const saveButton = screen.getByLabelText('Save annotations');
    await user.click(saveButton);

    expect(mockCreateImageAnnotation).toHaveBeenCalled();
    expect(mockProps.onAnnotationAdded).toHaveBeenCalled();
  });

  test('handles text annotation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Select text tool
    const textTool = screen.getByLabelText('Text tool');
    await user.click(textTool);

    // Click on canvas to add text
    const canvas = screen.getByRole('img').nextSibling;
    fireEvent.click(canvas, { clientX: 100, clientY: 100 });

    // Should show text input
    expect(screen.getByPlaceholderText('Enter text...')).toBeInTheDocument();
  });

  test('handles text input and submission', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Select text tool and click canvas
    const textTool = screen.getByLabelText('Text tool');
    await user.click(textTool);

    const canvas = screen.getByRole('img').nextSibling;
    fireEvent.click(canvas, { clientX: 100, clientY: 100 });

    // Type text
    const textInput = screen.getByPlaceholderText('Enter text...');
    await user.type(textInput, 'Test annotation');

    // Submit text
    const addButton = screen.getByText('Add');
    await user.click(addButton);

    // Text should be drawn on canvas
    expect(mockCanvasContext.fillText).toHaveBeenCalledWith('Test annotation', expect.any(Number), expect.any(Number));
  });

  test('handles read-only mode', () => {
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    // Toolbar should be disabled or hidden in read-only mode
    const brushTool = screen.getByLabelText('Brush tool');
    expect(brushTool).toBeDisabled();
  });

  test('loads existing annotations', async () => {
    const mockAnnotations = [
      {
        id: 'annotation-1',
        type: 'drawing',
        data: {
          paths: [
            [{ x: 0.1, y: 0.1 }, { x: 0.2, y: 0.2 }]
          ],
          color: '#ff0000',
          brushSize: 3
        }
      }
    ];

    mockGetImageAnnotations.mockResolvedValue(mockAnnotations);
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockGetImageAnnotations).toHaveBeenCalledWith(mockProps.imageId);
    });
  });

  test('handles image load', () => {
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    const image = screen.getByRole('img');
    
    // Simulate image load
    fireEvent.load(image);

    // Canvas should be set up after image loads
    expect(image).toBeInTheDocument();
  });

  test('handles canvas resize', () => {
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    const image = screen.getByRole('img');
    
    // Simulate image load with dimensions
    Object.defineProperty(image, 'naturalWidth', { value: 800 });
    Object.defineProperty(image, 'naturalHeight', { value: 600 });
    fireEvent.load(image);

    // Canvas should be resized to match image
    expect(image).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Check for proper ARIA labels
    expect(screen.getByLabelText('Brush tool')).toBeInTheDocument();
    expect(screen.getByLabelText('Text tool')).toBeInTheDocument();
    expect(screen.getByLabelText('Pan tool')).toBeInTheDocument();
    expect(screen.getByLabelText('Undo')).toBeInTheDocument();
    expect(screen.getByLabelText('Redo')).toBeInTheDocument();
    expect(screen.getByLabelText('Save annotations')).toBeInTheDocument();
    expect(screen.getByLabelText('Color')).toBeInTheDocument();
    
    // Check for proper roles
    expect(screen.getByRole('img')).toBeInTheDocument();
    expect(screen.getByRole('slider')).toBeInTheDocument();
  });

  test('handles collaboration features', () => {
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Should show collaboration status
    expect(screen.getByText('2 users online')).toBeInTheDocument();
  });

  test('handles disconnected collaboration', () => {
    const disconnectedCollaboration = {
      ...mockCollaboration,
      isConnected: false
    };

    vi.mocked(require('../../../contexts/CollaborationContext').useCollaboration).mockReturnValue(disconnectedCollaboration);

    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Should show disconnected status
    expect(screen.getByLabelText('Sync annotations')).toBeInTheDocument();
  });

  test('handles annotation sync', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageAnnotationTool {...mockProps} />
      </TestWrapper>
    );

    // Click sync button
    const syncButton = screen.getByLabelText('Sync annotations');
    await user.click(syncButton);

    // Should attempt to sync annotations
    expect(mockGetImageAnnotations).toHaveBeenCalled();
  });
});

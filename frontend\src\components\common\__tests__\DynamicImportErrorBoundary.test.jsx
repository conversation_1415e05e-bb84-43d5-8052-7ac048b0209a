import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import DynamicImportErrorBoundary from '../DynamicImportErrorBoundary';

// Mock navigator.clipboard
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme and router
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      error: {
        main: '#F44336',
      },
      warning: {
        main: '#FF9800',
      },
      info: {
        main: '#2196F3',
      },
      text: {
        secondary: '#666666',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
  });
  
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        {children}
      </ThemeProvider>
    </BrowserRouter>
  );
};

// Component that throws an error for testing
const ThrowError = ({ shouldThrow = false, errorType = 'generic' }) => {
  if (shouldThrow) {
    const errors = {
      chunk: new Error('Loading chunk 123 failed'),
      network: new Error('Failed to fetch dynamically imported module'),
      timeout: new Error('Request timed out'),
      permission: new Error('Permission denied'),
      dynamic: new Error('Dynamic import failed')
    };
    throw errors[errorType] || new Error('Test error');
  }
  return <div>Working Component</div>;
};

describe('DynamicImportErrorBoundary', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'group').mockImplementation(() => {});
    vi.spyOn(console, 'groupEnd').mockImplementation(() => {});
    vi.spyOn(console, 'table').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders children when no error occurs', () => {
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={false} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Working Component')).toBeInTheDocument();
  });

  test('catches and displays error when child component throws', () => {
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} errorType="chunk" />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Component Loading Error')).toBeInTheDocument();
    expect(screen.getByText(/Failed to load application component/)).toBeInTheDocument();
  });

  test('categorizes chunk loading errors correctly', () => {
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} errorType="chunk" />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText(/Network connectivity issues/)).toBeInTheDocument();
    expect(screen.getByText(/Cached files being outdated/)).toBeInTheDocument();
  });

  test('categorizes network errors correctly', () => {
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} errorType="network" />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText(/Network Error/)).toBeInTheDocument();
    expect(screen.getByText(/check your internet connection/)).toBeInTheDocument();
  });

  test('displays correlation ID', () => {
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText(/Error ID:/)).toBeInTheDocument();
  });

  test('handles retry functionality', async () => {
    const user = userEvent.setup();
    
    const { rerender } = render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText('Component Loading Error')).toBeInTheDocument();

    const retryButton = screen.getByText(/Retry \(0\/3\)/);
    await user.click(retryButton);

    // Wait for retry delay
    await waitFor(() => {
      // Rerender with working component
      rerender(
        <TestWrapper>
          <DynamicImportErrorBoundary componentName="TestComponent">
            <ThrowError shouldThrow={false} />
          </DynamicImportErrorBoundary>
        </TestWrapper>
      );
    });

    expect(screen.getByText('Working Component')).toBeInTheDocument();
  });

  test('disables retry button after max retries', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent" maxRetries={2}>
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    // First retry
    const retryButton1 = screen.getByText(/Retry \(0\/2\)/);
    await user.click(retryButton1);

    await waitFor(() => {
      expect(screen.getByText(/Retry \(1\/2\)/)).toBeInTheDocument();
    });

    // Second retry
    const retryButton2 = screen.getByText(/Retry \(1\/2\)/);
    await user.click(retryButton2);

    await waitFor(() => {
      expect(screen.getByText('Max Retries Reached')).toBeInTheDocument();
    });

    const maxRetriesButton = screen.getByText('Max Retries Reached');
    expect(maxRetriesButton).toBeDisabled();
  });

  test('handles navigation to dashboard', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    const dashboardButton = screen.getByText('Go to Dashboard');
    await user.click(dashboardButton);

    // Navigation would be handled by React Router in real app
    expect(dashboardButton).toBeInTheDocument();
  });

  test('calls onError callback when error occurs', () => {
    const onError = vi.fn();
    
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent" onError={onError}>
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'Test error',
        componentName: 'TestComponent'
      })
    );
  });

  test('calls analytics callback when enabled', () => {
    const onAnalytics = vi.fn();
    
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary 
          componentName="TestComponent" 
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        >
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'DynamicImportErrorBoundary',
        action: 'error_caught'
      })
    );
  });

  test('handles custom navigation callback', async () => {
    const user = userEvent.setup();
    const onNavigateHome = vi.fn();
    
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary 
          componentName="TestComponent"
          onNavigateHome={onNavigateHome}
        >
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    const dashboardButton = screen.getByText('Go to Dashboard');
    await user.click(dashboardButton);

    expect(onNavigateHome).toHaveBeenCalled();
  });

  test('shows troubleshooting tips after retry', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    // Initially no troubleshooting tips
    expect(screen.queryByText('Troubleshooting Tips:')).not.toBeInTheDocument();

    // Click retry
    const retryButton = screen.getByText(/Retry \(0\/3\)/);
    await user.click(retryButton);

    // After retry, troubleshooting tips should appear
    await waitFor(() => {
      expect(screen.getByText('Troubleshooting Tips:')).toBeInTheDocument();
    });

    expect(screen.getByText(/Check your internet connection/)).toBeInTheDocument();
    expect(screen.getByText(/Clear your browser cache/)).toBeInTheDocument();
  });

  test('handles different error severities', () => {
    const { rerender } = render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} errorType="chunk" />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    // Critical error (chunk load)
    expect(screen.getByText('Component Loading Error')).toBeInTheDocument();

    // Test warning error (timeout)
    rerender(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} errorType="timeout" />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(screen.getByText(/took too long to load/)).toBeInTheDocument();
  });

  test('collects performance metrics', () => {
    // Mock performance API
    const mockPerformance = {
      getEntriesByType: vi.fn().mockReturnValue([{
        loadEventEnd: 1000,
        loadEventStart: 500,
        domContentLoadedEventEnd: 800,
        domContentLoadedEventStart: 600
      }]),
      memory: {
        usedJSHeapSize: 1000000,
        jsHeapSizeLimit: 2000000
      }
    };
    
    Object.defineProperty(window, 'performance', {
      value: mockPerformance,
      writable: true
    });

    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    expect(mockPerformance.getEntriesByType).toHaveBeenCalledWith('navigation');
  });

  test('handles reload functionality', async () => {
    const user = userEvent.setup();
    
    // Mock window.location.reload
    const mockReload = vi.fn();
    Object.defineProperty(window, 'location', {
      value: { reload: mockReload },
      writable: true
    });

    render(
      <TestWrapper>
        <DynamicImportErrorBoundary componentName="TestComponent">
          <ThrowError shouldThrow={true} />
        </DynamicImportErrorBoundary>
      </TestWrapper>
    );

    // The reload button would be in the enhanced fallback component
    // For now, we test that the component renders without errors
    expect(screen.getByText('Component Loading Error')).toBeInTheDocument();
  });
});

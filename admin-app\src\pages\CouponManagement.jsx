import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Fab,
  Snackbar,
  useTheme,
  alpha,
  Tabs,
  Tab,
  InputAdornment,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  LocalOffer as CouponIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Analytics as AnalyticsIcon,
  Speed as SpeedIcon,
  Timeline as TimelineIcon,
  Settings as SettingsIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Group as GroupIcon,
} from '@mui/icons-material';

// Import custom components
import StablePageWrapper from '../components/common/StablePageWrapper';
import {
  CouponAnalytics,
  CouponCreator,
  BulkCouponManager,
  CouponPerformanceTracker,
} from '../components/coupons';

// Import hooks and utilities
import useCouponData from '../hooks/useCouponData';
import {
  formatDate,
  formatCurrency,
  copyToClipboard,
  exportToCSV,
  generateCorrelationId,
  formatDiscount,
  getCouponStatus,
  debounce,
} from '../utils/couponHelpers';
import api from '../api';

/**
 * Enhanced Coupon Management Component
 * Production-ready with Material-UI glass morphism styling, comprehensive features,
 * and performance optimizations
 */
const CouponManagement = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Dialog states
  const [dialogs, setDialogs] = useState({
    couponCreator: false,
    bulkManager: false,
    deleteConfirm: false,
  });

  // Use custom hook for data management
  const {
    data,
    loading,
    error,
    fetchCoupons,
    fetchAnalytics,
    fetchRedemptions,
    refreshAll,
    clearCache,
  } = useCouponData();

  // Tab configuration
  const tabs = [
    {
      label: 'Analytics',
      icon: <AnalyticsIcon />,
      value: 0,
      description: 'Real-time analytics and performance metrics'
    },
    {
      label: 'Coupons',
      icon: <CouponIcon />,
      value: 1,
      description: 'Manage individual coupons'
    },
    {
      label: 'Performance',
      icon: <TimelineIcon />,
      value: 2,
      description: 'Track coupon performance and usage'
    },
    {
      label: 'Bulk Operations',
      icon: <GroupIcon />,
      value: 3,
      description: 'Bulk coupon management and generation'
    },
  ];

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Use Promise.allSettled to prevent one failure from stopping others
        const results = await Promise.allSettled([
          fetchCoupons(),
          fetchAnalytics(),
          fetchRedemptions(),
        ]);

        // Check if all requests failed (likely backend is down)
        const allFailed = results.every(result => result.status === 'rejected');
        if (allFailed) {
          showSnackbar('Backend service is unavailable. Some features may not work.', 'warning');
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
        showSnackbar('Failed to load initial data', 'error');
      }
    };

    loadInitialData();
  }, []);

  // Refresh data when tab changes
  useEffect(() => {
    const refreshTabData = async () => {
      try {
        switch (tabValue) {
          case 0: // Analytics
            await Promise.allSettled([fetchAnalytics(), fetchCoupons()]);
            break;
          case 1: // Coupons
            await fetchCoupons();
            break;
          case 2: // Performance
            await Promise.allSettled([fetchCoupons(), fetchRedemptions()]);
            break;
          case 3: // Bulk Operations
            await fetchCoupons();
            break;
        }
      } catch (error) {
        console.error('Error refreshing tab data:', error);
      }
    };

    refreshTabData();
  }, [tabValue]);

  // Utility functions
  const showSnackbar = useCallback((message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  }, []);

  const closeSnackbar = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  const openDialog = useCallback((dialogName) => {
    setDialogs(prev => ({ ...prev, [dialogName]: true }));
  }, []);

  const closeDialog = useCallback((dialogName) => {
    setDialogs(prev => ({ ...prev, [dialogName]: false }));
    setSelectedCoupon(null);
  }, []);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce((term) => {
      setSearchTerm(term);
      setPage(0); // Reset to first page on search
    }, 300),
    []
  );

  // Event handlers
  const handleTabChange = useCallback((event, newValue) => {
    setTabValue(newValue);

    // Clear cache for better performance
    if (newValue !== tabValue) {
      clearCache();
    }
  }, [tabValue, clearCache]);

  const handleRefreshAll = useCallback(async () => {
    try {
      await refreshAll();
      showSnackbar('Data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing data:', error);
      showSnackbar('Failed to refresh data', 'error');
    }
  }, [refreshAll, showSnackbar]);

  const handleCreateCoupon = useCallback(() => {
    setSelectedCoupon(null);
    openDialog('couponCreator');
  }, [openDialog]);

  const handleEditCoupon = useCallback((coupon) => {
    setSelectedCoupon(coupon);
    openDialog('couponCreator');
  }, [openDialog]);

  const handleBulkOperations = useCallback(() => {
    openDialog('bulkManager');
  }, [openDialog]);

  const handleDeleteCoupon = useCallback(async (coupon) => {
    if (!window.confirm(`Are you sure you want to delete the coupon "${coupon.code}"?`)) return;

    try {
      const correlationId = generateCorrelationId();
      const headers = { 'X-Correlation-ID': correlationId };

      await api.delete(`/api/coupons/${coupon.id}`, { headers });

      // Refresh data
      await fetchCoupons(true);

      showSnackbar('Coupon deleted successfully');
    } catch (error) {
      console.error('Error deleting coupon:', error);
      showSnackbar('Failed to delete coupon', 'error');
    }
  }, [fetchCoupons, showSnackbar]);

  const handleCopyCode = useCallback(async (code) => {
    try {
      const success = await copyToClipboard(code);
      if (success) {
        showSnackbar('Code copied to clipboard');
      } else {
        showSnackbar('Failed to copy code', 'error');
      }
    } catch (error) {
      console.error('Error copying code:', error);
      showSnackbar('Failed to copy code', 'error');
    }
  }, [showSnackbar]);

  const handleCouponCreated = useCallback(async (newCoupon) => {
    await fetchCoupons(true);
    await fetchAnalytics(true);
    showSnackbar('Coupon created successfully');
    closeDialog('couponCreator');
  }, [fetchCoupons, fetchAnalytics, showSnackbar, closeDialog]);

  const handleCouponsUpdated = useCallback(async (updatedCoupons) => {
    await fetchCoupons(true);
    await fetchAnalytics(true);
    showSnackbar('Coupons updated successfully');
    closeDialog('bulkManager');
  }, [fetchCoupons, fetchAnalytics, showSnackbar, closeDialog]);

  // Filter coupons based on search term
  const filteredCoupons = useMemo(() => {
    if (!data?.coupons || !Array.isArray(data.coupons)) {
      return [];
    }

    if (!searchTerm) {
      return data.coupons;
    }

    const term = searchTerm.toLowerCase();
    return data.coupons.filter(coupon =>
      coupon.code?.toLowerCase().includes(term) ||
      coupon.name?.toLowerCase().includes(term) ||
      coupon.description?.toLowerCase().includes(term)
    );
  }, [data?.coupons, searchTerm]);

  // Handle pagination
  const handleChangePage = useCallback((event, newPage) => {
    setPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback((event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  }, []);

  // Handle search
  const handleSearchChange = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, [debouncedSearch]);

  const formatDiscount = (coupon) => {
    if (coupon.discount_type === 'percentage') {
      return `${coupon.discount_value}%`;
    } else {
      return `$${coupon.discount_value}`;
    }
  };

  const getStatusChip = (coupon) => {
    const now = new Date();
    const startDate = new Date(coupon.start_date);
    const endDate = coupon.end_date ? new Date(coupon.end_date) : null;

    if (!coupon.is_active) {
      return <Chip label="Inactive" color="default" size="small" />;
    } else if (now < startDate) {
      return <Chip label="Scheduled" color="info" size="small" />;
    } else if (endDate && now > endDate) {
      return <Chip label="Expired" color="error" size="small" />;
    } else {
      return <Chip label="Active" color="success" size="small" />;
    }
  };

  // Render functions for each tab
  const renderAnalyticsTab = () => (
    <CouponAnalytics
      data={data}
      loading={loading.analytics}
      error={error}
      onRefresh={fetchAnalytics}
    />
  );

  const renderCouponsTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CouponIcon />
            Coupon Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Create and manage discount coupons and promotional codes
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={() => {
              if (filteredCoupons.length > 0) {
                const exportColumns = [
                  { key: 'code', label: 'Code' },
                  { key: 'name', label: 'Name' },
                  { key: 'discount_type', label: 'Type' },
                  { key: 'discount_value', label: 'Value', type: 'discount' },
                  { key: 'redemption_count', label: 'Redemptions' },
                  { key: 'is_active', label: 'Active', type: 'boolean' },
                  { key: 'created_at', label: 'Created At', type: 'date' },
                ];
                exportToCSV(filteredCoupons, `coupons-${formatDate(new Date())}`, exportColumns);
                showSnackbar('Coupons exported successfully');
              }
            }}
            disabled={!filteredCoupons.length}
          >
            Export
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateCoupon}
            sx={{
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              '&:hover': {
                background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
              },
            }}
          >
            Create Coupon
          </Button>
        </Box>
      </Box>

      {/* Search and Filters */}
      <Box display="flex" gap={2} mb={3}>
        <TextField
          placeholder="Search coupons by code, name, or description..."
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ flexGrow: 1 }}
        />
      </Box>

      <Card variant="glass">
        <CardContent>
          {loading.coupons ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Code</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Discount</TableCell>
                      <TableCell>Usage</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredCoupons
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((coupon) => {
                        const status = getCouponStatus(coupon);
                        return (
                          <TableRow key={coupon.id} hover>
                            <TableCell>
                              <Box display="flex" alignItems="center" gap={1}>
                                <Typography variant="body2" fontFamily="monospace" fontWeight="medium">
                                  {coupon.code}
                                </Typography>
                                <Tooltip title="Copy code">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleCopyCode(coupon.code)}
                                  >
                                    <CopyIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Box>
                                <Typography variant="body2" fontWeight="medium">
                                  {coupon.name}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {coupon.description?.substring(0, 50)}...
                                </Typography>
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" fontWeight="medium">
                                {formatDiscount(coupon)}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Box>
                                <Typography variant="body2">
                                  {coupon.redemption_count || 0} used
                                </Typography>
                                {coupon.max_redemptions && (
                                  <Typography variant="caption" color="text.secondary">
                                    of {coupon.max_redemptions} max
                                  </Typography>
                                )}
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={status.status}
                                color={status.color}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {formatDate(coupon.created_at)}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Tooltip title="Edit Coupon">
                                <IconButton size="small" onClick={() => handleEditCoupon(coupon)}>
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Coupon">
                                <IconButton size="small" onClick={() => handleDeleteCoupon(coupon)}>
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={filteredCoupons.length}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[10, 25, 50, 100]}
              />
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );

  const renderPerformanceTab = () => (
    <CouponPerformanceTracker
      data={data}
      loading={loading.coupons}
      error={error}
      onRefresh={fetchCoupons}
    />
  );

  const renderBulkOperationsTab = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <GroupIcon />
            Bulk Operations
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Perform bulk operations on multiple coupons
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<SettingsIcon />}
          onClick={handleBulkOperations}
        >
          Manage Bulk Operations
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card variant="glass">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Bulk Generation
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Generate multiple coupons at once with the same configuration.
              </Typography>
              <Button
                variant="outlined"
                onClick={() => openDialog('bulkManager')}
                fullWidth
              >
                Generate Bulk Coupons
              </Button>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card variant="glass">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Bulk Management
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Activate, deactivate, or delete multiple coupons at once.
              </Typography>
              <Button
                variant="outlined"
                onClick={() => openDialog('bulkManager')}
                fullWidth
              >
                Manage Existing Coupons
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  // Main render
  return (
    <StablePageWrapper maxWidth="xl" enableGlassMorphism>
      {/* Header */}
      <Box mb={4}>
        <Typography
          variant="h4"
          gutterBottom
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 700,
          }}
        >
          <CouponIcon sx={{ color: theme.palette.primary.main }} />
          Coupon Management
        </Typography>

        <Typography variant="body1" color="text.secondary" paragraph>
          Comprehensive management of discount coupons, promotional codes, and analytics.
        </Typography>

        {/* Quick Stats */}
        <Grid container spacing={2} sx={{ mt: 2 }}>
          <Grid item xs={6} sm={3}>
            <Card variant="glass" sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="primary.main">
                {data?.coupons?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Coupons
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card variant="glass" sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="success.main">
                {data?.coupons?.filter(c => c?.is_active)?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Coupons
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card variant="glass" sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="info.main">
                {data?.coupons?.reduce((sum, c) => sum + (c?.redemption_count || 0), 0) || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Redemptions
              </Typography>
            </Card>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Card variant="glass" sx={{ textAlign: 'center', p: 2 }}>
              <Typography variant="h6" color="warning.main">
                {data?.coupons?.filter(c => {
                  const now = new Date();
                  const endDate = c?.end_date ? new Date(c.end_date) : null;
                  return endDate && now > endDate;
                })?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Expired Coupons
              </Typography>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={handleRefreshAll}>
              Retry
            </Button>
          }
        >
          {error}
        </Alert>
      )}

      {/* Navigation Tabs */}
      <Card variant="glass" sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: 72,
              textTransform: 'none',
              fontWeight: 600,
            },
          }}
        >
          {tabs.map((tab) => (
            <Tab
              key={tab.value}
              label={
                <Box textAlign="center">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                    {tab.icon}
                    {tab.label}
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {tab.description}
                  </Typography>
                </Box>
              }
              value={tab.value}
            />
          ))}
        </Tabs>

        {/* Tab Content */}
        <Box sx={{ p: 3 }}>
          {tabValue === 0 && renderAnalyticsTab()}
          {tabValue === 1 && renderCouponsTab()}
          {tabValue === 2 && renderPerformanceTab()}
          {tabValue === 3 && renderBulkOperationsTab()}
        </Box>
      </Card>

      {/* Floating Action Button for Quick Actions */}
      <Fab
        color="primary"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          '&:hover': {
            background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
          },
        }}
        onClick={handleRefreshAll}
      >
        <RefreshIcon />
      </Fab>

      {/* Dialogs */}
      <CouponCreator
        open={dialogs.couponCreator}
        onClose={() => closeDialog('couponCreator')}
        onCouponCreated={handleCouponCreated}
        editingCoupon={selectedCoupon}
      />

      <BulkCouponManager
        open={dialogs.bulkManager}
        onClose={() => closeDialog('bulkManager')}
        coupons={data.coupons || []}
        onCouponsUpdated={handleCouponsUpdated}
        mode="generate"
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={closeSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Alert
          onClose={closeSnackbar}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </StablePageWrapper>
  );
};

export default CouponManagement;

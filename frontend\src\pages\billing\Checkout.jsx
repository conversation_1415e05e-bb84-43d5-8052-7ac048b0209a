// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Divider,
  Alert,


  Grid,

  Switch,
  FormControlLabel,
  Chip,
  TextField,
} from '@mui/material';
import {
  Payment as PaymentIcon,
  ArrowBack as ArrowBackIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';

import api from '../../api/index';

import { useNotification } from '../../hooks/useNotification';
// import CouponForm from '../../components/billing/CouponForm';

const Checkout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // Get plan from location state
  const plan = location.state?.plan;

  // State
  const [loading, setLoading] = useState(false);
  const [isYearly, setIsYearly] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState(null);

  // Redirect if no plan is selected
  useEffect(() => {
    if (!plan) {
      navigate('/billing/plans');
    }
  }, [plan, navigate]);

  // Calculate pricing
  const monthlyPrice = plan?.price_monthly || 0;
  const yearlyPrice = plan?.price_yearly || (monthlyPrice * 10); // 2 months free
  const currentPrice = isYearly ? yearlyPrice : monthlyPrice;
  
  // Apply coupon discount
  const discountAmount = appliedCoupon ? (currentPrice * (appliedCoupon.discount_percent / 100)) : 0;
  const finalPrice = currentPrice - discountAmount;

  // Handle coupon application
  const handleCouponApplied = (coupon) => {
    setAppliedCoupon(coupon);
    showSuccessNotification(`Coupon "${coupon.code}" applied! ${coupon.discount_percent}% discount`);
  };

  const handleCouponRemoved = () => {
    setAppliedCoupon(null);
    showSuccessNotification('Coupon removed');
  };

  // Handle checkout
  const handleCheckout = async () => {
    setLoading(true);

    try {
      const response = await api.post('/api/billing/checkout', {
        plan_id: plan.id,
        success_url: `${window.location.origin}/billing/success`,
        cancel_url: `${window.location.origin}/billing/plans`,
        is_yearly: isYearly,
        coupon_code: appliedCoupon?.code
      });

      // Redirect to Lemon Squeezy checkout
      window.location.href = response.data.checkout_url;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      showErrorNotification('Failed to create checkout session');
      setLoading(false);
    }
  };

  if (!plan) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ mt: 2 }}>
          Loading...
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Helmet>
        <title>Checkout - {plan.name} Plan | B2B Influencer Tool</title>
        <meta name="description" content={`Complete your subscription to the ${plan.name} plan`} />
      </Helmet>

      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/billing/plans')}
          sx={{ mb: 3 }}
        >
          Back to Plans
        </Button>

        <Typography variant="h4" gutterBottom>
          Complete Your Subscription
        </Typography>

        <Grid container spacing={3}>
          {/* Plan Summary */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Plan Summary
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="h5" sx={{ mr: 2 }}>
                  {plan.name}
                </Typography>
                {plan.is_popular && (
                  <Chip label="Most Popular" color="primary" size="small" />
                )}
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                {plan.description}
              </Typography>

              {/* Billing Cycle Toggle */}
              <FormControlLabel
                control={
                  <Switch
                    checked={isYearly}
                    onChange={(e) => setIsYearly(e.target.checked)}
                    color="primary"
                  />
                }
                label={
                  <Box>
                    <Typography variant="body2">
                      Annual billing (Save 17%)
                    </Typography>
                    {isYearly && (
                      <Typography variant="caption" color="primary">
                        2 months free!
                      </Typography>
                    )}
                  </Box>
                }
                sx={{ mb: 3 }}
              />

              {/* Coupon Form */}
              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  label="Coupon Code"
                  placeholder="Enter coupon code"
                  size="small"
                  InputProps={{
                    endAdornment: (
                      <Button
                        variant="contained"
                        size="small"
                        onClick={() => {
                          // Simple coupon application
                          const mockCoupon = { code: 'SAVE20', discount_percent: 20 };
                          handleCouponApplied(mockCoupon);
                        }}
                      >
                        Apply
                      </Button>
                    )
                  }}
                />
                {appliedCoupon && (
                  <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="body2" color="success.main">
                      Coupon &quot;{appliedCoupon.code}&quot; applied
                    </Typography>
                    <Button
                      size="small"
                      onClick={handleCouponRemoved}
                      color="error"
                    >
                      Remove
                    </Button>
                  </Box>
                )}
              </Box>
            </Paper>
          </Grid>

          {/* Order Summary */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, position: 'sticky', top: 20 }}>
              <Typography variant="h6" gutterBottom>
                Order Summary
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">
                    {plan.name} Plan
                  </Typography>
                  <Typography variant="body2">
                    ${currentPrice.toFixed(2)}
                  </Typography>
                </Box>

                <Typography variant="caption" color="text.secondary">
                  {isYearly ? 'Billed annually' : 'Billed monthly'}
                </Typography>

                {appliedCoupon && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, color: 'success.main' }}>
                    <Typography variant="body2">
                      Discount ({appliedCoupon.discount_percent}%)
                    </Typography>
                    <Typography variant="body2">
                      -${discountAmount.toFixed(2)}
                    </Typography>
                  </Box>
                )}

                <Divider sx={{ my: 1.5 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    Total
                  </Typography>
                  <Typography variant="subtitle1" fontWeight="bold">
                    ${finalPrice.toFixed(2)}
                    <Typography variant="caption" color="text.secondary" component="span" sx={{ ml: 0.5 }}>
                      {isYearly ? '/year' : '/month'}
                    </Typography>
                  </Typography>
                </Box>
              </Box>

              <Button
                variant="contained"
                fullWidth
                size="large"
                startIcon={<PaymentIcon />}
                onClick={handleCheckout}
                disabled={loading}
                sx={{ mb: 2 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Proceed to Payment'}
              </Button>

              <Alert severity="info" icon={<SecurityIcon />} sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Secure payment powered by Lemon Squeezy. Your payment information is encrypted and secure.
                </Typography>
              </Alert>

              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center', mt: 2 }}>
                You can cancel anytime from your billing settings
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default Checkout;

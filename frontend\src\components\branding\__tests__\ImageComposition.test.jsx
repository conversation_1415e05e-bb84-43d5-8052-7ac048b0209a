import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import ImageComposition from '../ImageComposition';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('ImageComposition', () => {
  const mockImageComposition = {
    layout: 'rule-of-thirds',
    subjectPosition: 'center',
    aspectRatio: '16:9',
    negativeSpace: 'medium',
    depthOfField: 'medium',
    perspective: 'eye-level',
    templates: [
      {
        name: 'Test Template',
        settings: {
          layout: 'golden-ratio',
          subjectPosition: 'left',
          aspectRatio: '4:3',
          negativeSpace: 'ample',
          depthOfField: 'shallow',
          perspective: 'high-angle'
        }
      }
    ]
  };

  const mockProps = {
    imageComposition: mockImageComposition,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders image composition settings correctly', () => {
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Image Composition Rules')).toBeInTheDocument();
    expect(screen.getByText(/Define how your brand's images should be composed/)).toBeInTheDocument();
  });

  test('displays all composition setting controls', () => {
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Layout')).toBeInTheDocument();
    expect(screen.getByLabelText('Subject Position')).toBeInTheDocument();
    expect(screen.getByLabelText('Negative Space')).toBeInTheDocument();
    expect(screen.getByLabelText('Depth of Field')).toBeInTheDocument();
    expect(screen.getByLabelText('Perspective')).toBeInTheDocument();
    expect(screen.getByLabelText('Preferred Aspect Ratio')).toBeInTheDocument();
  });

  test('shows composition preview with grid overlay', () => {
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Composition Preview')).toBeInTheDocument();
    expect(screen.getByText('Show Composition Grid')).toBeInTheDocument();
    expect(screen.getByAltText('Composition Preview')).toBeInTheDocument();
  });

  test('handles layout change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const layoutSelect = screen.getByLabelText('Layout');
    await user.click(layoutSelect);
    
    const goldenRatioOption = screen.getByText('Golden Ratio');
    await user.click(goldenRatioOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        layout: 'golden-ratio'
      });
    });
  });

  test('handles subject position change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const subjectPositionSelect = screen.getByLabelText('Subject Position');
    await user.click(subjectPositionSelect);
    
    const leftOption = screen.getByText('Left');
    await user.click(leftOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        subjectPosition: 'left'
      });
    });
  });

  test('handles negative space change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const negativeSpaceSelect = screen.getByLabelText('Negative Space');
    await user.click(negativeSpaceSelect);
    
    const ampleOption = screen.getByText('Ample');
    await user.click(ampleOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        negativeSpace: 'ample'
      });
    });
  });

  test('handles depth of field change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const depthOfFieldSelect = screen.getByLabelText('Depth of Field');
    await user.click(depthOfFieldSelect);
    
    const shallowOption = screen.getByText('Shallow (Blurred Background)');
    await user.click(shallowOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        depthOfField: 'shallow'
      });
    });
  });

  test('handles perspective change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const perspectiveSelect = screen.getByLabelText('Perspective');
    await user.click(perspectiveSelect);
    
    const highAngleOption = screen.getByText('High Angle (Looking Down)');
    await user.click(highAngleOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        perspective: 'high-angle'
      });
    });
  });

  test('handles aspect ratio change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const aspectRatioSelect = screen.getByLabelText('Preferred Aspect Ratio');
    await user.click(aspectRatioSelect);
    
    const squareOption = screen.getByText('1:1 (Square)');
    await user.click(squareOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        aspectRatio: '1:1'
      });
    });
  });

  test('toggles grid overlay visibility', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const gridToggle = screen.getByRole('checkbox', { name: /show composition grid/i });
    expect(gridToggle).toBeChecked();

    await user.click(gridToggle);
    expect(gridToggle).not.toBeChecked();

    await user.click(gridToggle);
    expect(gridToggle).toBeChecked();
  });

  test('displays composition templates section', () => {
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Composition Templates')).toBeInTheDocument();
    expect(screen.getByText(/Save and apply composition templates/)).toBeInTheDocument();
    expect(screen.getByLabelText('Template Name')).toBeInTheDocument();
    expect(screen.getByText('Save Current Settings as Template')).toBeInTheDocument();
  });

  test('saves a new template', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const templateNameInput = screen.getByLabelText('Template Name');
    const saveButton = screen.getByText('Save Current Settings as Template');

    // Initially save button should be disabled
    expect(saveButton).toBeDisabled();

    // Enter template name
    await user.type(templateNameInput, 'My Custom Template');
    expect(saveButton).not.toBeDisabled();

    // Save template
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        templates: [
          ...mockImageComposition.templates,
          {
            name: 'My Custom Template',
            settings: {
              layout: mockImageComposition.layout,
              subjectPosition: mockImageComposition.subjectPosition,
              negativeSpace: mockImageComposition.negativeSpace,
              depthOfField: mockImageComposition.depthOfField,
              perspective: mockImageComposition.perspective,
              aspectRatio: mockImageComposition.aspectRatio,
            }
          }
        ]
      });
    });
  });

  test('displays saved templates', () => {
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Saved Templates')).toBeInTheDocument();
    expect(screen.getByText('Test Template')).toBeInTheDocument();
    expect(screen.getByText('golden-ratio, 4:3')).toBeInTheDocument();
  });

  test('applies a saved template', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const applyButton = screen.getByText('Apply');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        ...mockImageComposition.templates[0].settings
      });
    });
  });

  test('deletes a saved template', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const deleteButton = screen.getByRole('button', { name: /delete/i });
    await user.click(deleteButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        templates: []
      });
    });
  });

  test('shows empty state when no templates exist', () => {
    const propsWithoutTemplates = {
      ...mockProps,
      imageComposition: {
        ...mockImageComposition,
        templates: []
      }
    };

    render(
      <TestWrapper>
        <ImageComposition {...propsWithoutTemplates} />
      </TestWrapper>
    );

    expect(screen.getByText(/No templates saved yet/)).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    // Check for proper labels
    expect(screen.getByLabelText('Layout')).toBeInTheDocument();
    expect(screen.getByLabelText('Subject Position')).toBeInTheDocument();
    expect(screen.getByLabelText('Template Name')).toBeInTheDocument();

    // Check for proper button roles
    const saveButton = screen.getByRole('button', { name: /save current settings/i });
    expect(saveButton).toBeInTheDocument();

    const applyButton = screen.getByRole('button', { name: /apply/i });
    expect(applyButton).toBeInTheDocument();
  });

  test('renders with default props when no imageComposition provided', () => {
    render(
      <TestWrapper>
        <ImageComposition onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Image Composition Rules')).toBeInTheDocument();
  });

  test('handles empty template name gracefully', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const saveButton = screen.getByText('Save Current Settings as Template');
    expect(saveButton).toBeDisabled();

    // Try to save with empty name
    await user.click(saveButton);
    
    // Should not call onChange
    expect(mockProps.onChange).not.toHaveBeenCalled();
  });

  test('displays correct aspect ratio in preview', () => {
    const customProps = {
      ...mockProps,
      imageComposition: {
        ...mockImageComposition,
        aspectRatio: '1:1'
      }
    };

    render(
      <TestWrapper>
        <ImageComposition {...customProps} />
      </TestWrapper>
    );

    const previewImage = screen.getByAltText('Composition Preview');
    expect(previewImage).toHaveStyle({ aspectRatio: '1/1' });
  });

  test('displays composition analysis section', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const analysisSection = screen.getByText('Composition Analysis');
    expect(analysisSection).toBeInTheDocument();

    // Expand the analysis section
    await user.click(analysisSection);

    expect(screen.getByText('Composition Quality Score')).toBeInTheDocument();
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
  });

  test('calculates and displays composition score', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    // Look for score chip
    expect(screen.getByText(/Score:/)).toBeInTheDocument();

    // Expand analysis to see detailed score
    const analysisSection = screen.getByText('Composition Analysis');
    await user.click(analysisSection);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('displays advanced settings section', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    const advancedSection = screen.getByText('Advanced Composition Settings');
    expect(advancedSection).toBeInTheDocument();

    // Expand the advanced section
    await user.click(advancedSection);

    expect(screen.getByLabelText('Color Harmony')).toBeInTheDocument();
    expect(screen.getByLabelText('Lighting Direction')).toBeInTheDocument();
  });

  test('handles color harmony change', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    // Expand advanced settings
    const advancedSection = screen.getByText('Advanced Composition Settings');
    await user.click(advancedSection);

    const colorHarmonySelect = screen.getByLabelText('Color Harmony');
    await user.click(colorHarmonySelect);

    const warmOption = screen.getByText('Warm Dominant');
    await user.click(warmOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        colorHarmony: 'warm-dominant'
      });
    });
  });

  test('handles lighting direction change', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ImageComposition {...mockProps} />
      </TestWrapper>
    );

    // Expand advanced settings
    const advancedSection = screen.getByText('Advanced Composition Settings');
    await user.click(advancedSection);

    const lightingSelect = screen.getByLabelText('Lighting Direction');
    await user.click(lightingSelect);

    const dramaticOption = screen.getByText('Dramatic');
    await user.click(dramaticOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockImageComposition,
        lightingDirection: 'dramatic'
      });
    });
  });

  test('provides composition recommendations', async () => {
    const user = userEvent.setup();

    const propsWithCenterLayout = {
      ...mockProps,
      imageComposition: {
        ...mockImageComposition,
        layout: 'center'
      }
    };

    render(
      <TestWrapper>
        <ImageComposition {...propsWithCenterLayout} />
      </TestWrapper>
    );

    // Expand analysis section
    const analysisSection = screen.getByText('Composition Analysis');
    await user.click(analysisSection);

    // Should show recommendation for center layout
    expect(screen.getByText(/Consider using Rule of Thirds or Golden Ratio/)).toBeInTheDocument();
  });

  test('shows excellent score for optimal settings', async () => {
    const user = userEvent.setup();

    const optimalProps = {
      ...mockProps,
      imageComposition: {
        layout: 'golden-ratio',
        subjectPosition: 'top-left',
        aspectRatio: '16:9',
        negativeSpace: 'medium',
        depthOfField: 'shallow',
        perspective: 'eye-level',
        templates: []
      }
    };

    render(
      <TestWrapper>
        <ImageComposition {...optimalProps} />
      </TestWrapper>
    );

    // Expand analysis section
    const analysisSection = screen.getByText('Composition Analysis');
    await user.click(analysisSection);

    // Should show high score
    expect(screen.getByText(/Excellent/)).toBeInTheDocument();
  });
});

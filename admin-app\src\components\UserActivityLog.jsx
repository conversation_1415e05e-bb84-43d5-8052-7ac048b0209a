// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Avatar,
} from '@mui/material';
// Timeline components replaced with custom implementation
import {
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Security as SecurityIcon,
  Payment as PaymentIcon,
  Email as EmailIcon,
  Settings as SettingsIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import api from '../api';

const UserActivityLog = ({ userId, showTimeline = false }) => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalActivities, setTotalActivities] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState({ start: null, end: null });

  useEffect(() => {
    fetchActivities();
  }, [userId, page, rowsPerPage, searchTerm, actionFilter, dateFilter]);

  const fetchActivities = async () => {
    setLoading(true);
    try {
      const params = {
        skip: page * rowsPerPage,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        action: actionFilter !== 'all' ? actionFilter : undefined,
        start_date: dateFilter.start?.toISOString(),
        end_date: dateFilter.end?.toISOString(),
      };

      const endpoint = userId 
        ? `/api/admin/users/${userId}/activity`
        : '/api/admin/activity';

      const response = await api.get(endpoint, { params });
      setActivities(response.data.activities || []);
      setTotalActivities(response.data.total || 0);
      setError('');
    } catch (error) {
      console.error('Error fetching activities:', error);
      setError('Failed to fetch activity log');
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (action) => {
    switch (action?.toLowerCase()) {
      case 'login':
        return <LoginIcon />;
      case 'logout':
        return <LogoutIcon />;
      case 'create':
      case 'register':
        return <AddIcon />;
      case 'update':
      case 'edit':
        return <EditIcon />;
      case 'delete':
        return <DeleteIcon />;
      case 'payment':
      case 'subscription':
        return <PaymentIcon />;
      case 'email':
        return <EmailIcon />;
      case 'security':
        return <SecurityIcon />;
      case 'settings':
        return <SettingsIcon />;
      default:
        return <InfoIcon />;
    }
  };

  const getActivityColor = (level) => {
    switch (level?.toLowerCase()) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      default:
        return 'info';
    }
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatTimeAgo = (dateString) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (showTimeline) {
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Activity Timeline
        </Typography>
        {loading ? (
          <Box display="flex" justifyContent="center" py={3}>
            <CircularProgress />
          </Box>
        ) : (
          <Box sx={{ position: 'relative', pl: 3 }}>
            {activities.map((activity, index) => (
              <Box key={activity.id} sx={{ position: 'relative', pb: 3 }}>
                {/* Timeline line */}
                {index < activities.length - 1 && (
                  <Box
                    sx={{
                      position: 'absolute',
                      left: -12,
                      top: 32,
                      bottom: -12,
                      width: 2,
                      bgcolor: 'divider',
                    }}
                  />
                )}

                {/* Timeline dot */}
                <Box
                  sx={{
                    position: 'absolute',
                    left: -20,
                    top: 8,
                    width: 16,
                    height: 16,
                    borderRadius: '50%',
                    bgcolor: `${getActivityColor(activity.level)}.main`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: 10,
                  }}
                >
                  {getActivityIcon(activity.action)}
                </Box>

                {/* Timeline content */}
                <Box sx={{ ml: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Typography variant="body1" fontWeight="medium">
                      {activity.action}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatTimeAgo(activity.timestamp)}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {activity.description}
                  </Typography>
                  {activity.ip_address && (
                    <Typography variant="caption" color="text.secondary">
                      IP: {activity.ip_address}
                    </Typography>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        )}
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" gap={2} alignItems="center" flexWrap="wrap">
          <TextField
            placeholder="Search activities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 200 }}
          />
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Action</InputLabel>
            <Select
              value={actionFilter}
              onChange={(e) => setActionFilter(e.target.value)}
              label="Action"
            >
              <MenuItem value="all">All Actions</MenuItem>
              <MenuItem value="login">Login</MenuItem>
              <MenuItem value="logout">Logout</MenuItem>
              <MenuItem value="create">Create</MenuItem>
              <MenuItem value="update">Update</MenuItem>
              <MenuItem value="delete">Delete</MenuItem>
              <MenuItem value="payment">Payment</MenuItem>
              <MenuItem value="security">Security</MenuItem>
            </Select>
          </FormControl>
          <DatePicker
            label="From Date"
            value={dateFilter.start}
            onChange={(date) => setDateFilter(prev => ({ ...prev, start: date }))}
            slotProps={{ textField: { size: 'small' } }}
          />
          <DatePicker
            label="To Date"
            value={dateFilter.end}
            onChange={(date) => setDateFilter(prev => ({ ...prev, end: date }))}
            slotProps={{ textField: { size: 'small' } }}
          />
          <IconButton onClick={fetchActivities} title="Refresh">
            <RefreshIcon />
          </IconButton>
        </Box>
      </Paper>

      {/* Activity Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Timestamp</TableCell>
              <TableCell>User</TableCell>
              <TableCell>Action</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Level</TableCell>
              <TableCell>IP Address</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                  <CircularProgress size={40} />
                </TableCell>
              </TableRow>
            ) : activities.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1">
                    No activities found.
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              activities.map((activity) => (
                <TableRow key={activity.id} hover>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDateTime(activity.timestamp)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatTimeAgo(activity.timestamp)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {activity.user && (
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
                          {activity.user.email.charAt(0).toUpperCase()}
                        </Avatar>
                        <Box>
                          <Typography variant="body2">
                            {activity.user.full_name || 'No name'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {activity.user.email}
                          </Typography>
                        </Box>
                      </Box>
                    )}
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      {getActivityIcon(activity.action)}
                      <Typography sx={{ ml: 1 }}>
                        {activity.action}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {activity.description}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={activity.level} 
                      color={getActivityColor(activity.level)} 
                      size="small" 
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontFamily="monospace">
                      {activity.ip_address || '-'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalActivities}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={(event, newPage) => setPage(newPage)}
          onRowsPerPageChange={(event) => {
            setRowsPerPage(parseInt(event.target.value, 10));
            setPage(0);
          }}
        />
      </TableContainer>
    </Box>
  );
};

export default UserActivityLog;

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import CompetitorComparisonPage from '../CompetitorComparisonPage';
import { NotificationProvider } from '../../../contexts/NotificationContext';
import { CompetitorProvider } from '../../../contexts/CompetitorContext';

// Mock the CompetitorComparison component
jest.mock('../../../components/competitors/CompetitorComparison', () => {
  return function MockCompetitorComparison({ isEmbedded, ...props }) {
    return (
      <div data-testid="competitor-comparison">
        <p>Competitor Comparison Component</p>
        <p>Embedded: {isEmbedded ? 'true' : 'false'}</p>
        {Object.entries(props).map(([key, value]) => (
          <p key={key}>{key}: {String(value)}</p>
        ))}
      </div>
    );
  };
});

// Mock useNotification hook
jest.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showErrorNotification: jest.fn(),
    showSuccessNotification: jest.fn()
  })
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({
    pathname: '/competitors/compare',
    search: ''
  })
}));

// Test wrapper component
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <NotificationProvider>
          <CompetitorProvider>
            {children}
          </CompetitorProvider>
        </NotificationProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('CompetitorComparisonPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    test('renders main heading and meta tags when not embedded', () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      expect(document.title).toBe('Competitor Comparison | ACE Social');
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByLabelText(/competitor comparison dashboard/i)).toBeInTheDocument();
    });

    test('does not render meta tags when embedded', () => {
      const originalTitle = document.title;
      
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={true} />
        </TestWrapper>
      );

      expect(document.title).toBe(originalTitle);
      expect(screen.getByRole('main')).toBeInTheDocument();
    });

    test('renders breadcrumb navigation when not embedded', () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      expect(screen.getByLabelText(/breadcrumb navigation/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/navigate to dashboard/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/navigate to competitors/i)).toBeInTheDocument();
    });

    test('does not render breadcrumb navigation when embedded', () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={true} />
        </TestWrapper>
      );

      expect(screen.queryByLabelText(/breadcrumb navigation/i)).not.toBeInTheDocument();
    });

    test('renders CompetitorComparison component with correct props', async () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('competitor-comparison')).toBeInTheDocument();
      });

      expect(screen.getByText('Embedded: false')).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    test('navigates to dashboard when home breadcrumb is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      const homeLink = screen.getByLabelText(/navigate to dashboard/i);
      await user.click(homeLink);

      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });

    test('navigates to competitors when competitors breadcrumb is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      const competitorsLink = screen.getByLabelText(/navigate to competitors/i);
      await user.click(competitorsLink);

      expect(mockNavigate).toHaveBeenCalledWith('/settings?tab=competitors');
    });
  });

  describe('Error Handling', () => {
    test('displays error message when error occurs', () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      // Simulate error by triggering error boundary
      const errorBoundary = screen.getByRole('main');
      
      // This would normally be triggered by a child component error
      // For testing purposes, we'll check that error handling structure exists
      expect(errorBoundary).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });

    test('retry button resets error state', async () => {
      const user = userEvent.setup();
      
      // This test would need to be implemented with a more complex setup
      // to actually trigger and test error recovery
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      // Verify component renders without errors initially
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels and roles', () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByLabelText(/competitor comparison dashboard/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/breadcrumb navigation/i)).toBeInTheDocument();
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      // Test tab navigation
      await user.tab();
      expect(document.activeElement).toHaveAttribute('aria-label', 'Navigate to dashboard');

      await user.tab();
      expect(document.activeElement).toHaveAttribute('aria-label', 'Navigate to competitors');
    });

    test('provides screen reader friendly content', () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      // Check for proper semantic structure
      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByLabelText(/competitor comparison dashboard/i)).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    test('component is memoized', () => {
      const { rerender } = render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      // Re-render with same props should not cause unnecessary re-renders
      rerender(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      // Component should be wrapped with memo
      expect(CompetitorComparisonPage.displayName).toBe('CompetitorComparisonPage');
    });

    test('handles prop changes efficiently', () => {
      const { rerender } = render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      // Change props and verify re-render
      rerender(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={true} />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('SEO and Meta Tags', () => {
    test('sets correct page title for non-embedded mode', () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      expect(document.title).toBe('Competitor Comparison | ACE Social');
    });

    test('includes proper meta description', () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      const metaDescription = document.querySelector('meta[name="description"]');
      expect(metaDescription).toHaveAttribute('content', expect.stringContaining('Compare competitor performance metrics'));
    });

    test('includes proper Open Graph tags', () => {
      render(
        <TestWrapper>
          <CompetitorComparisonPage isEmbedded={false} />
        </TestWrapper>
      );

      const ogTitle = document.querySelector('meta[property="og:title"]');
      const ogDescription = document.querySelector('meta[property="og:description"]');
      const ogType = document.querySelector('meta[property="og:type"]');

      expect(ogTitle).toHaveAttribute('content', 'Competitor Comparison | ACE Social');
      expect(ogDescription).toHaveAttribute('content', expect.stringContaining('Compare competitor performance metrics'));
      expect(ogType).toHaveAttribute('content', 'website');
    });
  });
});

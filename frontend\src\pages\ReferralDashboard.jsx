// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Paper,
  Button,
  TextField,
  IconButton,
  Divider,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  CircularProgress,
  Card,
  CardContent,
  CardHeader,
  useTheme,
  useMediaQuery,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  alpha
} from '@mui/material';
import {
  ContentCopy as ContentCopyIcon,
  Share as ShareIcon,
  Add as AddIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Link as LinkIcon,
  Person as PersonIcon,
  ShoppingCart as ShoppingCartIcon,
  CardGiftcard as CardGiftcardIcon,
  Warning as WarningIcon,

  Delete as DeleteIcon,

  Security as SecurityIcon,
  Mouse as MouseIcon
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { format, parseISO, isAfter, addDays } from 'date-fns';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip as ChartTooltip, Legend } from 'chart.js';
import axios from 'axios';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { LoadingButton } from '@mui/lab';
import PageHeader from '../components/PageHeader';
import StatsCard from '../components/dashboard/StatsCard';

import { API_BASE_URL } from '../config';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, ChartTooltip, Legend);

// TabPanel component for tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`referral-tabpanel-${index}`}
      aria-labelledby={`referral-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ReferralDashboard = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);
  const [referralLinks, setReferralLinks] = useState([]);
  const [referralClicks, setReferralClicks] = useState([]);
  const [referralSignups, setReferralSignups] = useState([]);
  const [referralRewards, setReferralRewards] = useState([]);
  const [newLinkCode, setNewLinkCode] = useState('');
  const [expirationDate, setExpirationDate] = useState(null);
  const [creatingLink, setCreatingLink] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedLink, setSelectedLink] = useState(null);
  const [formErrors, setFormErrors] = useState({});

  // Fetch all referral data
  useEffect(() => {
    const fetchReferralData = async () => {
      setLoading(true);
      try {
        // Fetch stats
        const statsResponse = await axios.get(`${API_BASE_URL}/api/referrals/stats`);
        setStats(statsResponse.data);

        // Fetch links
        const linksResponse = await axios.get(`${API_BASE_URL}/api/referrals/links`);
        setReferralLinks(linksResponse.data.links);

        // Fetch clicks
        const clicksResponse = await axios.get(`${API_BASE_URL}/api/referrals/clicks`);
        setReferralClicks(clicksResponse.data.clicks);

        // Fetch signups
        const signupsResponse = await axios.get(`${API_BASE_URL}/api/referrals/signups`);
        setReferralSignups(signupsResponse.data.signups);

        // Fetch rewards
        const rewardsResponse = await axios.get(`${API_BASE_URL}/api/referrals/rewards`);
        setReferralRewards(rewardsResponse.data.rewards);
      } catch (error) {
        console.error('Error fetching referral data:', error);
        enqueueSnackbar('Failed to load referral data. Please try again later.', { variant: 'error' });

        // Set empty data on error
        setStats(null);
        setReferralLinks([]);
        setReferralClicks([]);
        setReferralSignups([]);
        setReferralRewards([]);
      } finally {
        setLoading(false);
      }
    };

    fetchReferralData();
  }, [enqueueSnackbar]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  // Handle create dialog open
  const handleCreateDialogOpen = () => {
    setNewLinkCode('');
    setExpirationDate(null);
    setFormErrors({});
    setCreateDialogOpen(true);
  };

  // Handle create dialog close
  const handleCreateDialogClose = () => {
    setCreateDialogOpen(false);
  };

  // Handle delete dialog open
  const handleDeleteDialogOpen = (link) => {
    setSelectedLink(link);
    setDeleteDialogOpen(true);
  };

  // Handle delete dialog close
  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedLink(null);
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (newLinkCode && !/^[a-zA-Z0-9-_]+$/.test(newLinkCode)) {
      errors.customCode = 'Custom code can only contain letters, numbers, hyphens, and underscores';
    }

    if (expirationDate && isAfter(new Date(), expirationDate)) {
      errors.expirationDate = 'Expiration date must be in the future';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Create a new referral link
  const handleCreateLink = async () => {
    if (!validateForm()) {
      return;
    }

    setCreatingLink(true);
    try {
      const payload = {
        custom_code: newLinkCode || undefined,
        expires_at: expirationDate ? expirationDate.toISOString() : undefined
      };

      const response = await axios.post(`${API_BASE_URL}/api/referrals/links`, payload);

      setReferralLinks([response.data, ...referralLinks]);
      setNewLinkCode('');
      setExpirationDate(null);
      enqueueSnackbar('Referral link created successfully!', { variant: 'success' });
      handleCreateDialogClose();
    } catch (error) {
      console.error('Error creating referral link:', error);

      if (error.response && error.response.data && error.response.data.detail) {
        enqueueSnackbar(error.response.data.detail, { variant: 'error' });
      } else {
        enqueueSnackbar('Failed to create referral link', { variant: 'error' });
      }
    } finally {
      setCreatingLink(false);
    }
  };

  // Delete referral link
  const handleDeleteLink = async () => {
    if (!selectedLink) return;

    try {
      await axios.delete(`${API_BASE_URL}/api/referrals/links/${selectedLink.id}`);

      // Remove the deleted link from the list
      setReferralLinks(referralLinks.filter(link => link.id !== selectedLink.id));

      enqueueSnackbar('Referral link deleted successfully', { variant: 'success' });
      handleDeleteDialogClose();
    } catch (error) {
      console.error('Error deleting referral link:', error);
      enqueueSnackbar('Failed to delete referral link', { variant: 'error' });
    }
  };

  // Copy referral link to clipboard
  const handleCopyLink = (link) => {
    navigator.clipboard.writeText(link);
    enqueueSnackbar('Referral link copied to clipboard!', { variant: 'success' });
  };

  // Share referral link
  const handleShareLink = (link) => {
    if (navigator.share) {
      navigator.share({
        title: 'Join me on B2B Influencer Tool',
        text: 'Check out this amazing platform for B2B influencer marketing!',
        url: link
      })
        .then(() => enqueueSnackbar('Shared successfully!', { variant: 'success' }))
        .catch((error) => console.error('Error sharing:', error));
    } else {
      handleCopyLink(link);
    }
  };

  // Prepare chart data
  const prepareChartData = () => {
    if (!stats) return null;

    // Get all dates from clicks, signups, and conversions
    const allDates = new Set([
      ...Object.keys(stats.clicks_by_date || {}),
      ...Object.keys(stats.signups_by_date || {}),
      ...Object.keys(stats.conversions_by_date || {})
    ]);

    // Sort dates
    const sortedDates = Array.from(allDates).sort();

    return {
      labels: sortedDates,
      datasets: [
        {
          label: 'Clicks',
          data: sortedDates.map(date => stats.clicks_by_date[date] || 0),
          borderColor: theme.palette.primary.main,
          backgroundColor: theme.palette.primary.main,
          tension: 0.4
        },
        {
          label: 'Sign-ups',
          data: sortedDates.map(date => stats.signups_by_date[date] || 0),
          borderColor: theme.palette.secondary.main,
          backgroundColor: theme.palette.secondary.main,
          tension: 0.4
        },
        {
          label: 'Conversions',
          data: sortedDates.map(date => stats.conversions_by_date[date] || 0),
          borderColor: theme.palette.success.main,
          backgroundColor: theme.palette.success.main,
          tension: 0.4
        }
      ]
    };
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Referral Performance Over Time'
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      }
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(parseISO(dateString), 'MMM d, yyyy h:mm a');
    } catch {
      return dateString;
    }
  };

  // Check if a link is expired
  const isLinkExpired = (link) => {
    if (!link.expires_at) return false;
    return isAfter(new Date(), parseISO(link.expires_at));
  };

  // Get status chip for a referral link
  const getLinkStatusChip = (link) => {
    if (!link.is_active) {
      return <Chip size="small" label="Inactive" color="default" />;
    }

    if (isLinkExpired(link)) {
      return <Chip size="small" label="Expired" color="error" />;
    }

    if (link.is_suspicious) {
      return (
        <Tooltip title={link.suspicious_reason || 'Suspicious activity detected'}>
          <Chip
            size="small"
            label="Suspicious"
            color="warning"
            icon={<WarningIcon />}
          />
        </Tooltip>
      );
    }

    return <Chip size="small" label="Active" color="success" icon={<CheckCircleIcon />} />;
  };

  // Get status chip for a click
  const getClickStatusChip = (click) => {
    if (click.is_suspicious) {
      return (
        <Tooltip title={click.suspicious_reason || 'Suspicious activity detected'}>
          <Chip
            size="small"
            label="Suspicious"
            color="warning"
            icon={<WarningIcon />}
          />
        </Tooltip>
      );
    }

    return <Chip size="small" label="Valid" color="success" icon={<CheckCircleIcon />} />;
  };

  // Get status chip for referral signup
  const getStatusChip = (signup) => {
    if (signup.is_suspicious) {
      return (
        <Tooltip title={signup.suspicious_reason || 'Suspicious activity detected'}>
          <Chip
            size="small"
            label="Suspicious"
            color="warning"
            icon={<WarningIcon />}
          />
        </Tooltip>
      );
    }

    switch (signup.status) {
      case 'clicked':
        return <Chip size="small" label="Clicked" color="primary" />;
      case 'signed_up':
        return <Chip size="small" label="Signed Up" color="secondary" />;
      case 'purchased':
        return <Chip size="small" label="Purchased" color="success" icon={<CheckCircleIcon />} />;
      default:
        return <Chip size="small" label={signup.status} />;
    }
  };

  // Get status chip for reward
  const getRewardStatusChip = (status) => {
    switch (status) {
      case 'pending':
        return <Chip size="small" label="Pending" color="warning" />;
      case 'issued':
        return <Chip size="small" label="Issued" color="success" icon={<CheckCircleIcon />} />;
      case 'failed':
        return <Chip size="small" label="Failed" color="error" icon={<CancelIcon />} />;
      default:
        return <Chip size="small" label={status} />;
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl">
      <PageHeader
        title="Referral Program"
        subtitle="Invite others to join our platform and earn rewards"
        icon={<CardGiftcardIcon fontSize="large" />}
      />


      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total Referral Links"
            value={stats?.total_links || 0}
            icon={<LinkIcon />}
            color={theme.palette.primary.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total Clicks"
            value={stats?.total_clicks || 0}
            icon={<PersonIcon />}
            color={theme.palette.secondary.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Successful Referrals"
            value={stats?.total_conversions || 0}
            icon={<ShoppingCartIcon />}
            color={theme.palette.success.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Credits Earned"
            value={stats?.total_credits_earned || 0}
            icon={<CardGiftcardIcon />}
            color={theme.palette.warning.main}
          />
        </Grid>
      </Grid>


      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>Referral Performance</Typography>
        <Box sx={{ height: 300 }}>
          {stats && <Line data={prepareChartData()} options={chartOptions} />}
        </Box>
      </Paper>


      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant={isMobile ? "scrollable" : "fullWidth"}
          scrollButtons={isMobile ? "auto" : false}
        >
          <Tab label="My Referral Links" />
          <Tab label="Referral Activity" />
          <Tab label="Rewards" />
          <Tab label="How It Works" />
        </Tabs>


        <TabPanel value={tabValue} index={0}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>Referral Links</Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleCreateDialogOpen}
              sx={{ mb: 2 }}
            >
              Create New Referral Link
            </Button>


            <Dialog open={createDialogOpen} onClose={handleCreateDialogClose} maxWidth="sm" fullWidth>
              <DialogTitle>Create New Referral Link</DialogTitle>
              <DialogContent>
                <Grid container spacing={3} sx={{ mt: 0.5 }}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Custom Code (Optional)"
                      variant="outlined"
                      value={newLinkCode}
                      onChange={(e) => setNewLinkCode(e.target.value)}
                      placeholder="e.g., my-special-offer"
                      helperText={formErrors.customCode || "Leave blank to generate a random code"}
                      error={!!formErrors.customCode}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        label="Expiration Date (Optional)"
                        value={expirationDate}
                        onChange={(newValue) => setExpirationDate(newValue)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            helperText: formErrors.expirationDate || "Leave blank for no expiration",
                            error: !!formErrors.expirationDate
                          }
                        }}
                        minDate={addDays(new Date(), 1)}
                      />
                    </LocalizationProvider>
                  </Grid>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <SecurityIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="body2" color="textSecondary">
                        For security, referral links are rate-limited to 5 per day and 20 clicks per hour.
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCreateDialogClose}>Cancel</Button>
                <LoadingButton
                  loading={creatingLink}
                  variant="contained"
                  color="primary"
                  onClick={handleCreateLink}
                >
                  Create Link
                </LoadingButton>
              </DialogActions>
            </Dialog>


            <Dialog open={deleteDialogOpen} onClose={handleDeleteDialogClose}>
              <DialogTitle>Delete Referral Link</DialogTitle>
              <DialogContent>
                <Typography>
                  Are you sure you want to delete the referral link with code <strong>{selectedLink?.code}</strong>?
                </Typography>
                <Typography variant="body2" color="error" sx={{ mt: 2 }}>
                  This action cannot be undone.
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={handleDeleteDialogClose}>Cancel</Button>
                <Button variant="contained" color="error" onClick={handleDeleteLink}>
                  Delete
                </Button>
              </DialogActions>
            </Dialog>
          </Box>

          <Divider sx={{ my: 3 }} />

          <Typography variant="h6" gutterBottom>My Referral Links</Typography>
          {referralLinks.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <CardGiftcardIcon sx={{ fontSize: 60, color: alpha(theme.palette.primary.main, 0.7), mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                No Referral Links Yet
              </Typography>
              <Typography variant="body2" color="textSecondary" paragraph sx={{ maxWidth: 500, mx: 'auto', mb: 3 }}>
                Create your first referral link to start inviting people to join the platform.
                You&apos;ll earn rewards for each successful referral!
              </Typography>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleCreateDialogOpen}
              >
                Create Your First Referral Link
              </Button>
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Code</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Expires</TableCell>
                    <TableCell>Clicks</TableCell>
                    <TableCell>Sign-ups</TableCell>
                    <TableCell>Conversions</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {referralLinks.map((link) => (
                    <TableRow
                      key={link.id}
                      sx={link.is_suspicious ? { backgroundColor: 'rgba(255, 152, 0, 0.08)' } : {}}
                    >
                      <TableCell>{link.code}</TableCell>
                      <TableCell>{getLinkStatusChip(link)}</TableCell>
                      <TableCell>{formatDate(link.created_at)}</TableCell>
                      <TableCell>{formatDate(link.expires_at)}</TableCell>
                      <TableCell>{link.total_clicks}</TableCell>
                      <TableCell>{link.total_signups}</TableCell>
                      <TableCell>{link.total_conversions}</TableCell>
                      <TableCell>
                        <Tooltip title="Copy Link">
                          <IconButton onClick={() => handleCopyLink(link.full_url)}>
                            <ContentCopyIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Share Link">
                          <IconButton onClick={() => handleShareLink(link.full_url)}>
                            <ShareIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Link">
                          <IconButton
                            onClick={() => handleDeleteDialogOpen(link)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </TabPanel>


        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Recent Clicks</Typography>
              {referralClicks.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 3, bgcolor: alpha(theme.palette.background.paper, 0.5), borderRadius: 1 }}>
                  <MouseIcon sx={{ fontSize: 40, color: 'text.secondary', opacity: 0.5, mb: 1 }} />
                  <Typography variant="body1" gutterBottom>
                    No Clicks Yet
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Share your referral links to start tracking clicks
                  </Typography>
                </Box>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Date</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Device</TableCell>
                        <TableCell>Browser</TableCell>
                        <TableCell>OS</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {referralClicks.slice(0, 10).map((click) => (
                        <TableRow
                          key={click.id}
                          sx={click.is_suspicious ? { backgroundColor: 'rgba(255, 152, 0, 0.08)' } : {}}
                        >
                          <TableCell>{formatDate(click.clicked_at)}</TableCell>
                          <TableCell>{getClickStatusChip(click)}</TableCell>
                          <TableCell>
                            {click.country ? (
                              <>
                                {click.country}
                                {click.city && click.region && (
                                  <Typography variant="caption" display="block" color="textSecondary">
                                    {click.city}, {click.region}
                                  </Typography>
                                )}
                              </>
                            ) : (
                              'Unknown'
                            )}
                          </TableCell>
                          <TableCell>{click.device || 'Unknown'}</TableCell>
                          <TableCell>{click.browser || 'Unknown'}</TableCell>
                          <TableCell>{click.os || 'Unknown'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Recent Sign-ups</Typography>
              {referralSignups.length === 0 ? (
                <Typography color="textSecondary">
                  No sign-ups from your referral links yet.
                </Typography>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Date</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Purchase Date</TableCell>
                        <TableCell>Plan</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {referralSignups.slice(0, 10).map((signup) => (
                        <TableRow
                          key={signup.id}
                          sx={signup.is_suspicious ? { backgroundColor: 'rgba(255, 152, 0, 0.08)' } : {}}
                        >
                          <TableCell>{formatDate(signup.signed_up_at)}</TableCell>
                          <TableCell>{getStatusChip(signup)}</TableCell>
                          <TableCell>{formatDate(signup.purchase_date)}</TableCell>
                          <TableCell>
                            {signup.plan_id ? (
                              <Chip
                                size="small"
                                label={signup.plan_id.charAt(0).toUpperCase() + signup.plan_id.slice(1)}
                                color={
                                  signup.plan_id === 'professional' ? 'secondary' :
                                  signup.plan_id === 'enterprise' ? 'primary' : 'default'
                                }
                              />
                            ) : (
                              'N/A'
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>
          </Grid>
        </TabPanel>


        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>My Rewards</Typography>
          {referralRewards.length === 0 ? (
            <Box>
              <Typography color="textSecondary" paragraph>
                You haven&apos;t earned any rewards yet.
              </Typography>
              <Typography variant="body1">
                For every 5 successful referrals (people who sign up and purchase a subscription),
                you&apos;ll earn 50 Premium Image Generation Credits!
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="textSecondary">
                  Current progress: {stats?.total_conversions || 0} / 5 successful referrals
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Box sx={{ width: '100%', mr: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={Math.min(((stats?.total_conversions || 0) % 5) * 20, 100)}
                    />
                  </Box>
                  <Box sx={{ minWidth: 35 }}>
                    <Typography variant="body2" color="textSecondary">
                      {((stats?.total_conversions || 0) % 5) * 20}%
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Reward</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Issued Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {referralRewards.map((reward) => (
                    <TableRow key={reward.id}>
                      <TableCell>{formatDate(reward.created_at)}</TableCell>
                      <TableCell>
                        {reward.reward_type === 'addon_credits' ? 'Premium Image Credits' : reward.reward_type}
                      </TableCell>
                      <TableCell>{reward.reward_amount}</TableCell>
                      <TableCell>{getRewardStatusChip(reward.status)}</TableCell>
                      <TableCell>{formatDate(reward.issued_at)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </TabPanel>


        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>How the Referral Program Works</Typography>

          <Card sx={{ mb: 3 }}>
            <CardHeader title="1. Create and Share Your Referral Link" />
            <CardContent>
              <Typography variant="body1" paragraph>
                Generate your unique referral link and share it with friends, colleagues, or on social media.
                Anyone who clicks your link will be directed to our platform.
              </Typography>
            </CardContent>
          </Card>

          <Card sx={{ mb: 3 }}>
            <CardHeader title="2. Track Sign-ups and Conversions" />
            <CardContent>
              <Typography variant="body1" paragraph>
                When someone clicks your link and creates an account, they&apos;ll be tracked as your referral.
                You can monitor all activity in the &quot;Referral Activity&quot; tab.
              </Typography>
            </CardContent>
          </Card>

          <Card sx={{ mb: 3 }}>
            <CardHeader title="3. Earn Rewards" />
            <CardContent>
              <Typography variant="body1" paragraph>
                For every 5 people who sign up AND purchase a subscription through your referral link,
                you&apos;ll earn 50 Premium Image Generation Credits automatically added to your account.
              </Typography>
              <Typography variant="body1">
                There&apos;s no limit to how many rewards you can earn - the more people you refer, the more credits you get!
              </Typography>
            </CardContent>
          </Card>

          <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 1 }}>
            <Typography variant="h6" gutterBottom>Tips for Successful Referrals</Typography>
            <Typography variant="body1" component="ul">
              <li>Share your link with people who would genuinely benefit from our platform</li>
              <li>Explain the key features that you find most valuable</li>
              <li>Consider sharing your success stories using the platform</li>
              <li>Follow up with people who have clicked your link but haven&apos;t signed up yet</li>
            </Typography>
          </Box>

          <Card sx={{ mt: 4 }}>
            <CardHeader
              title="Fraud Prevention & Security"
              avatar={<SecurityIcon color="primary" />}
            />
            <CardContent>
              <Typography variant="body1" paragraph>
                Our referral program includes several security measures to ensure fair usage and prevent fraud:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>Rate Limiting</Typography>
                  <Typography variant="body2" paragraph>
                    • Maximum 5 referral links per day per user<br />
                    • Maximum 20 clicks per hour per referral link<br />
                    • 1-hour cooldown between creating new links
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" gutterBottom>Fraud Detection</Typography>
                  <Typography variant="body2" paragraph>
                    • Self-referral prevention<br />
                    • Suspicious activity monitoring<br />
                    • IP-based tracking with privacy protection
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="textSecondary">
                    These measures help ensure that rewards are earned fairly and that the referral program
                    benefits genuine users. Any suspicious activity may result in referrals being flagged for review.
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default ReferralDashboard;

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from "react";
import { Helmet } from "react-helmet-async";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  useTheme,
  useMediaQuery,
  CircularProgress,
  Alert,
  Breadcrumbs,
  Link,
  IconButton,
  Tooltip,
  Card,
  Avatar
} from "@mui/material";
import {
  TrendingUp as TrendingUpIcon,
  Insights as InsightsIcon,
  ContentPaste as ContentIcon,
  Compare as CompareIcon,
  Lightbulb as LightbulbIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  Business as BusinessIcon,
  LinkedIn as LinkedInIcon,
  Twitter as TwitterIcon,
  Facebook as FacebookIcon,
  Instagram as InstagramIcon
} from "@mui/icons-material";
import {
  CompetitorInsightsProvider,
  useCompetitorInsights,
} from "../../contexts/CompetitorInsightsContext";
import {
  CompetitorProvider,
  useCompetitors,
} from "../../contexts/CompetitorContext";
import { useNotification } from "../../hooks/useNotification";
import {
  refreshCompetitorAnalytics,
  getAnalyticsHealth,
  batchFetchCompetitorMetrics
} from "../../api/competitor-analytics";
import GlassmorphicCard from "../../components/common/GlassmorphicCard";
import CompetitorInsightsDashboard from "./CompetitorInsightsDashboard";
import CompetitorContentAnalysis from "./CompetitorContentAnalysis";
import StrategicRecommendations from "./StrategicRecommendations";
import InsightToContentWorkflow from "./InsightToContentWorkflow";

// Main component wrapper with context provider
const CompetitorInsightsPageWrapper = () => {
  return (
    <CompetitorProvider>
      <CompetitorInsightsProvider>
        <CompetitorInsightsPageContent />
      </CompetitorInsightsProvider>
    </CompetitorProvider>
  );
};

// Main content component
const CompetitorInsightsPageContent = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { showErrorNotification, showSuccessNotification } = useNotification();

  const { competitors, loading: competitorsLoading } = useCompetitors();
  const {
    selectedCompetitors,
    updateSelectedCompetitors,
    insightFilters,
    updateInsightFilters
  } = useCompetitorInsights();

  // Get active tab from URL or default to 'dashboard'
  const getActiveTabFromUrl = () => {
    const searchParams = new URLSearchParams(location.search);
    return searchParams.get("tab") || "dashboard";
  };

  const [activeTab, setActiveTab] = useState(getActiveTabFromUrl());
  const [refreshing, setRefreshing] = useState(false);
  const [analyticsHealth, setAnalyticsHealth] = useState(null);
  const [realTimeMetrics, setRealTimeMetrics] = useState({});
  const [error, setError] = useState(null);

  // Available platforms with icons
  const platformIcons = {
    linkedin: LinkedInIcon,
    twitter: TwitterIcon,
    facebook: FacebookIcon,
    instagram: InstagramIcon
  };

  const platformColors = {
    linkedin: '#0077B5',
    twitter: '#1DA1F2',
    facebook: '#1877F2',
    instagram: '#E4405F'
  };

  // Update URL when tab changes
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.set("tab", activeTab);
    navigate({ search: searchParams.toString() }, { replace: true });
  }, [activeTab, navigate, location.search]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle competitor selection
  const handleCompetitorChange = (event) => {
    const selectedIds = event.target.value;
    updateSelectedCompetitors(selectedIds);
  };

  // Handle filter changes
  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    updateInsightFilters({ [name]: value });
  };

  // Navigate to add competitor
  const handleAddCompetitor = () => {
    navigate("/settings?tab=competitors&action=new");
  };

  // Check analytics service health
  const checkAnalyticsHealth = useCallback(async () => {
    try {
      const health = await getAnalyticsHealth();
      setAnalyticsHealth(health);
    } catch (error) {
      console.error('Error checking analytics health:', error);
      setAnalyticsHealth({ status: 'unhealthy', error: error.message });
    }
  }, []);

  // Load real-time metrics for selected competitors
  const loadRealTimeMetrics = useCallback(async () => {
    if (selectedCompetitors.length === 0) return;

    try {
      const metrics = await batchFetchCompetitorMetrics(selectedCompetitors, {
        platforms: insightFilters.platform ? [insightFilters.platform] : null,
        refresh: false
      });

      setRealTimeMetrics(metrics.results);

      if (metrics.error_count > 0) {
        showErrorNotification(`Failed to load metrics for ${metrics.error_count} competitors`);
      }
    } catch (error) {
      console.error('Error loading real-time metrics:', error);
      showErrorNotification('Failed to load competitor metrics');
    }
  }, [selectedCompetitors, insightFilters.platform, showErrorNotification]);

  // Refresh all competitor data from social media APIs
  const handleRefreshData = useCallback(async () => {
    if (selectedCompetitors.length === 0) {
      showErrorNotification('Please select competitors to refresh');
      return;
    }

    setRefreshing(true);
    setError(null);

    try {
      await refreshCompetitorAnalytics({
        competitor_ids: selectedCompetitors,
        platforms: insightFilters.platform ? [insightFilters.platform] : null,
        force_refresh: true
      });

      showSuccessNotification('Competitor data refresh started. This may take a few minutes.');

      // Reload metrics after a short delay
      setTimeout(() => {
        loadRealTimeMetrics();
      }, 3000);

    } catch (error) {
      console.error('Error refreshing data:', error);
      setError(error.message);
      showErrorNotification('Failed to refresh competitor data');
    } finally {
      setRefreshing(false);
    }
  }, [selectedCompetitors, insightFilters.platform, showErrorNotification, showSuccessNotification, loadRealTimeMetrics]);

  // Load analytics health and real-time metrics on mount
  useEffect(() => {
    checkAnalyticsHealth();
  }, [checkAnalyticsHealth]);

  // Load real-time metrics when competitors are selected
  useEffect(() => {
    if (selectedCompetitors.length > 0) {
      loadRealTimeMetrics();
    }
  }, [selectedCompetitors, loadRealTimeMetrics]);

  return (
    <>
      <Helmet>
        <title>Social Media Competitor Insights | ACE Social</title>
        <meta name="description" content="Analyze competitor social media performance with real-time data from LinkedIn, Twitter, Facebook, and Instagram" />
      </Helmet>

      <Box sx={{ width: "100%", mb: 4 }}>
        {/* Breadcrumb Navigation */}
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs aria-label="breadcrumb navigation">
            <Link
              component="button"
              variant="body2"
              onClick={() => navigate('/dashboard')}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Dashboard
            </Link>
            <Link
              component="button"
              variant="body2"
              onClick={() => navigate('/settings?tab=competitors')}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Competitors
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
              <InsightsIcon sx={{ mr: 0.5 }} fontSize="inherit" />
              Insights
            </Typography>
          </Breadcrumbs>
        </Box>

        {/* Page Header */}
        <Box
          sx={{
            mb: 3,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "flex-start",
            flexWrap: "wrap",
            gap: 2,
          }}
        >
          <Box>
            <Typography
              variant="h4"
              component="h1"
              sx={{ display: "flex", alignItems: "center", mb: 1 }}
            >
              <TrendingUpIcon sx={{ mr: 1 }} />
              Social Media Competitor Insights
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Analyze competitor performance with real-time data from social media platforms
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            {/* Analytics Health Indicator */}
            {analyticsHealth && (
              <Tooltip title={`Analytics Service: ${analyticsHealth.status}`}>
                <Chip
                  size="small"
                  label={analyticsHealth.status}
                  color={analyticsHealth.status === 'healthy' ? 'success' : 'error'}
                  variant="outlined"
                />
              </Tooltip>
            )}

            <Tooltip title="Refresh data from social media APIs">
              <IconButton
                onClick={handleRefreshData}
                disabled={refreshing || selectedCompetitors.length === 0}
                color="primary"
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddCompetitor}
            >
              Add Competitor
            </Button>
          </Box>
        </Box>

        {/* Status Alerts */}
        {refreshing && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CircularProgress size={16} />
              <Typography variant="body2">
                Refreshing competitor data from social media APIs...
              </Typography>
            </Box>
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Competitor Selection & Platform Filtering */}
        <GlassmorphicCard sx={{ mb: 3 }}>
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Select Competitors & Platforms to Analyze
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <FormControl fullWidth>
                  <InputLabel>Competitors</InputLabel>
                  <Select
                    multiple
                    value={selectedCompetitors}
                    onChange={handleCompetitorChange}
                    renderValue={(selected) => (
                      <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                        {selected.map((value) => {
                          const competitor = competitors.find(
                            (c) => c._id === value || c.id === value
                          );
                          return (
                            <Chip
                              key={value}
                              label={competitor ? competitor.name : value}
                              size="small"
                              onDelete={() => {
                                const newSelected = selectedCompetitors.filter(id => id !== value);
                                updateSelectedCompetitors(newSelected);
                              }}
                            />
                          );
                        })}
                      </Box>
                    )}
                  >
                    {competitorsLoading ? (
                      <MenuItem disabled>
                        <CircularProgress size={20} sx={{ mr: 1 }} />
                        Loading competitors...
                      </MenuItem>
                    ) : competitors.length === 0 ? (
                      <MenuItem disabled>No competitors found</MenuItem>
                    ) : (
                      competitors.map((competitor) => (
                        <MenuItem key={competitor._id || competitor.id} value={competitor._id || competitor.id}>
                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                            <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: 12 }}>
                              {competitor.name.charAt(0)}
                            </Avatar>
                            <Box sx={{ flex: 1 }}>
                              <Typography variant="body2">{competitor.name}</Typography>
                              {competitor.social_media && competitor.social_media.length > 0 && (
                                <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
                                  {competitor.social_media.map((sm) => {
                                    const IconComponent = platformIcons[sm.platform];
                                    return IconComponent ? (
                                      <IconComponent
                                        key={sm.platform}
                                        sx={{
                                          fontSize: 14,
                                          color: platformColors[sm.platform]
                                        }}
                                      />
                                    ) : null;
                                  })}
                                </Box>
                              )}
                            </Box>
                          </Box>
                        </MenuItem>
                      ))
                    )}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Time Period</InputLabel>
                  <Select
                    name="timeframe"
                    value={insightFilters.timeframe}
                    onChange={handleFilterChange}
                    label="Time Period"
                  >
                    <MenuItem value="7d">Last 7 Days</MenuItem>
                    <MenuItem value="30d">Last 30 Days</MenuItem>
                    <MenuItem value="90d">Last 90 Days</MenuItem>
                    <MenuItem value="6m">Last 6 Months</MenuItem>
                    <MenuItem value="1y">Last 12 Months</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            {/* Platform Selection */}
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Social Media Platforms:
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {Object.entries(platformIcons).map(([platform, IconComponent]) => (
                  <Chip
                    key={platform}
                    icon={<IconComponent />}
                    label={platform.charAt(0).toUpperCase() + platform.slice(1)}
                    onClick={() => {
                      updateInsightFilters({
                        platform: insightFilters.platform === platform ? null : platform
                      });
                    }}
                    color={insightFilters.platform === platform ? 'primary' : 'default'}
                    variant={insightFilters.platform === platform ? 'filled' : 'outlined'}
                    sx={{
                      '& .MuiChip-icon': {
                        color: insightFilters.platform === platform ? 'white' : platformColors[platform]
                      }
                    }}
                  />
                ))}
                {insightFilters.platform && (
                  <Chip
                    label="All Platforms"
                    onClick={() => updateInsightFilters({ platform: null })}
                    variant="outlined"
                    size="small"
                  />
                )}
              </Box>
            </Box>

            {/* Real-time Metrics Summary */}
            {selectedCompetitors.length > 0 && Object.keys(realTimeMetrics).length > 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Real-time Metrics Summary:
                </Typography>
                <Grid container spacing={2}>
                  {selectedCompetitors.slice(0, 3).map((competitorId) => {
                    const metrics = realTimeMetrics[competitorId];
                    const competitor = competitors.find(c => (c._id || c.id) === competitorId);

                    if (!metrics || !competitor) return null;

                    const totalFollowers = metrics.reduce((sum, m) => sum + m.followers_count, 0);
                    const avgEngagement = metrics.reduce((sum, m) => sum + m.engagement_rate, 0) / metrics.length;

                    return (
                      <Grid item xs={12} sm={6} md={4} key={competitorId}>
                        <Card variant="outlined" sx={{ p: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Avatar sx={{ width: 24, height: 24, mr: 1, fontSize: 12 }}>
                              {competitor.name.charAt(0)}
                            </Avatar>
                            <Typography variant="body2" fontWeight="medium">
                              {competitor.name}
                            </Typography>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {totalFollowers.toLocaleString()} followers • {avgEngagement.toFixed(1)}% engagement
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 0.5, mt: 1 }}>
                            {metrics.map((metric) => {
                              const IconComponent = platformIcons[metric.platform];
                              return IconComponent ? (
                                <IconComponent
                                  key={metric.platform}
                                  sx={{
                                    fontSize: 14,
                                    color: platformColors[metric.platform]
                                  }}
                                />
                              ) : null;
                            })}
                          </Box>
                        </Card>
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            )}

            {selectedCompetitors.length === 0 && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Please select at least one competitor to analyze their social media performance
              </Alert>
            )}
          </Box>
        </GlassmorphicCard>

        {/* Tabs Navigation */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant={isMobile ? "fullWidth" : "standard"}
            aria-label="competitor insights tabs"
          >
            <Tab
              icon={<InsightsIcon />}
              iconPosition="start"
              label="Dashboard"
              value="dashboard"
            />
            <Tab
              icon={<ContentIcon />}
              iconPosition="start"
              label="Content Analysis"
              value="content"
            />
            <Tab
              icon={<LightbulbIcon />}
              iconPosition="start"
              label="Strategic Recommendations"
              value="recommendations"
            />
            <Tab
              icon={<CompareIcon />}
              iconPosition="start"
              label="Create Content"
              value="create-content"
            />
          </Tabs>
        </Paper>

        {/* Tab Content */}
        <Box sx={{ mt: 2 }}>
          {activeTab === "dashboard" && <CompetitorInsightsDashboard />}

          {activeTab === "content" && <CompetitorContentAnalysis />}

          {activeTab === "recommendations" && <StrategicRecommendations />}

          {activeTab === "create-content" && <InsightToContentWorkflow />}
        </Box>
      </Box>
    </>
  );
};

export default CompetitorInsightsPageWrapper;

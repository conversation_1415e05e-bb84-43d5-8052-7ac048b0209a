/**
 * Enhanced Workflow Navigation - Enterprise-grade navigation component
 * Features: Comprehensive workflow navigation with breadcrumb trails, step indicators,
 * navigation history management, progress tracking, subscription-based feature gating,
 * keyboard navigation support, workflow state persistence, contextual navigation hints,
 * and ACE Social platform integration with advanced navigation capabilities and seamless
 * workflow management integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Button,
  Typography,
  LinearProgress,
  useTheme,
  alpha,
  Breadcrumbs,
  Link,
  Chip,
  Tooltip,
  Alert,
  Collapse,
  IconButton,
  Card,
  CardContent,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Save as SaveIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  Bookmark as BookmarkIcon,
  Timeline as TimelineIcon,
  Navigation as NavigationIcon,
  Shortcut as ShortcutsIcon,
  KeyboardArrowDown as DropdownIcon,
  TrendingUp as AnalyticsIcon
} from '@mui/icons-material';

import { useWorkflow } from './WorkflowProvider';
import { useSubscription } from '../../contexts/SubscriptionContext';
import FeatureGate from '../common/FeatureGate';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// Enhanced keyboard navigation hook
const useKeyboardNavigation = (onNext, onPrevious, onSave, disabled = false) => {
  useEffect(() => {
    if (disabled) return;

    const handleKeyDown = (event) => {
      // Ctrl/Cmd + Right Arrow = Next
      if ((event.ctrlKey || event.metaKey) && event.key === 'ArrowRight') {
        event.preventDefault();
        onNext?.();
      }
      // Ctrl/Cmd + Left Arrow = Previous
      if ((event.ctrlKey || event.metaKey) && event.key === 'ArrowLeft') {
        event.preventDefault();
        onPrevious?.();
      }
      // Ctrl/Cmd + S = Save
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        onSave?.();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onNext, onPrevious, onSave, disabled]);
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Plan-based navigation limitations
const PLAN_LIMITS = {
  1: { // Creator
    navigationHistory: false,
    keyboardShortcuts: false,
    workflowBookmarks: false,
    advancedProgress: false,
    navigationAnalytics: false,
    customNavigation: false,
    bulkNavigation: false,
    navigationExport: false,
    smartNavigation: false,
    navigationTemplates: false
  },
  2: { // Accelerator
    navigationHistory: true,
    keyboardShortcuts: true,
    workflowBookmarks: true,
    advancedProgress: true,
    navigationAnalytics: false,
    customNavigation: true,
    bulkNavigation: false,
    navigationExport: false,
    smartNavigation: true,
    navigationTemplates: false
  },
  3: { // Dominator
    navigationHistory: true,
    keyboardShortcuts: true,
    workflowBookmarks: true,
    advancedProgress: true,
    navigationAnalytics: true,
    customNavigation: true,
    bulkNavigation: true,
    navigationExport: true,
    smartNavigation: true,
    navigationTemplates: true
  }
};

// Navigation states
const NAVIGATION_STATES = {
  IDLE: 'idle',
  NAVIGATING: 'navigating',
  SAVING: 'saving',
  LOADING: 'loading',
  ERROR: 'error'
};

// Animation types (for future use)
// const ANIMATION_TYPES = {
//   FADE: 'fade',
//   SLIDE: 'slide',
//   ZOOM: 'zoom',
//   NONE: 'none'
// };

/**
 * Enhanced Workflow Navigation - Comprehensive navigation management with advanced features
 * Implements plan-based feature restrictions and enterprise-grade navigation capabilities
 */
const WorkflowNavigation = memo(forwardRef(({
  onNext,
  onPrevious,
  onSave,
  onNavigateToStep,
  onBookmarkStep,
  onExportNavigation,
  nextLabel = 'Next',
  previousLabel = 'Previous',
  saveLabel = 'Save Progress',
  showSave = true,
  nextDisabled = false,
  previousDisabled = false,
  saveDisabled = false,
  validationErrors = [],
  showValidationHelp = false,
  enableKeyboardNavigation = true,
  animationDuration = 300,
  compactMode = false,
  showTimeEstimates = true,
  enableSmartNavigation = false
}, ref) => {
  const theme = useTheme();
  const { updateUsage, getPlanTier } = useSubscription();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Responsive design (for future use)
  // const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Core workflow context
  const {
    currentStep,
    steps,
    completedSteps,
    loading,
    actions,
    workflowData
  } = useWorkflow();

  // Core state management
  const navigationRef = useRef(null);

  const [showHelp, setShowHelp] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState(null);
  const [navigationState, setNavigationState] = useState(NAVIGATION_STATES.IDLE);
  const [navigationHistory, setNavigationHistory] = useState([]);
  const [bookmarkedSteps, setBookmarkedSteps] = useState(new Set());
  const [showShortcutsDialog, setShowShortcutsDialog] = useState(false);
  const [showNavigationMenu, setShowNavigationMenu] = useState(false);
  const [navigationMenuAnchor, setNavigationMenuAnchor] = useState(null);
  const [timeEstimates, setTimeEstimates] = useState({});
  const [stepDurations, setStepDurations] = useState({});
  const [smartSuggestions, setSmartSuggestions] = useState([]);
  const [showProgressDetails, setShowProgressDetails] = useState(false);
  const [navigationPreferences] = useState({
    autoSave: true,
    showAnimations: true,
    compactView: false,
    showEstimates: true
  });

  // Plan tier and feature access
  const planTier = getPlanTier();
  const planLimits = PLAN_LIMITS[planTier] || PLAN_LIMITS[1];

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    navigateToStep: (stepIndex) => handleNavigateToStep(stepIndex),
    goNext: () => handleNext(),
    goPrevious: () => handlePrevious(),
    saveProgress: () => handleSave(),
    toggleBookmark: (stepIndex) => handleToggleBookmark(stepIndex),
    getNavigationHistory: () => navigationHistory,
    clearNavigationHistory: () => setNavigationHistory([]),
    exportNavigationData: () => handleExportNavigation(),
    getCurrentStep: () => currentStep,
    getProgress: () => getProgressData(),
    resetNavigation: () => handleResetNavigation(),
    showShortcuts: () => setShowShortcutsDialog(true)
  }), [currentStep, navigationHistory, handleNavigateToStep, handleNext, handlePrevious, handleSave, handleToggleBookmark, handleExportNavigation, getProgressData, handleResetNavigation]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: `all ${animationDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`
  }), [theme, animationDuration]);

  // Enhanced navigation handlers
  const handleNext = useCallback(async () => {
    try {
      setNavigationState(NAVIGATION_STATES.NAVIGATING);

      if (onNext) {
        await onNext();
      }

      // Track navigation
      if (planLimits.navigationHistory) {
        setNavigationHistory(prev => [...prev, {
          action: 'next',
          from: currentStep,
          to: currentStep + 1,
          timestamp: new Date().toISOString()
        }].slice(-50)); // Keep last 50 navigation actions
      }

      // Update usage tracking
      await updateUsage('navigation_next', 1, {
        currentStep,
        planTier
      });

      announceToScreenReader(`Navigated to step ${currentStep + 2}`);
    } catch (error) {
      console.error('Navigation error:', error);
      setNavigationState(NAVIGATION_STATES.ERROR);
    } finally {
      setNavigationState(NAVIGATION_STATES.IDLE);
    }
  }, [onNext, currentStep, planLimits.navigationHistory, updateUsage, planTier, announceToScreenReader]);

  const handlePrevious = useCallback(async () => {
    try {
      setNavigationState(NAVIGATION_STATES.NAVIGATING);

      if (onPrevious) {
        await onPrevious();
      }

      // Track navigation
      if (planLimits.navigationHistory) {
        setNavigationHistory(prev => [...prev, {
          action: 'previous',
          from: currentStep,
          to: currentStep - 1,
          timestamp: new Date().toISOString()
        }].slice(-50));
      }

      announceToScreenReader(`Navigated to step ${currentStep}`);
    } catch (error) {
      console.error('Navigation error:', error);
      setNavigationState(NAVIGATION_STATES.ERROR);
    } finally {
      setNavigationState(NAVIGATION_STATES.IDLE);
    }
  }, [onPrevious, currentStep, planLimits.navigationHistory, announceToScreenReader]);

  const handleSave = useCallback(async () => {
    try {
      setNavigationState(NAVIGATION_STATES.SAVING);

      if (onSave) {
        await onSave();
      }

      setLastSaveTime(new Date());
      announceToScreenReader('Progress saved successfully');
    } catch (error) {
      console.error('Save error:', error);
      setNavigationState(NAVIGATION_STATES.ERROR);
      announceToScreenReader('Failed to save progress');
    } finally {
      setNavigationState(NAVIGATION_STATES.IDLE);
    }
  }, [onSave, announceToScreenReader]);

  const handleNavigateToStep = useCallback(async (stepIndex) => {
    if (!planLimits.customNavigation) {
      announceToScreenReader('Step navigation is not available in your current plan');
      return;
    }

    if (actions.canProceedToStep && !actions.canProceedToStep(stepIndex)) {
      announceToScreenReader('Cannot navigate to this step');
      return;
    }

    try {
      setNavigationState(NAVIGATION_STATES.NAVIGATING);

      if (onNavigateToStep) {
        await onNavigateToStep(stepIndex);
      } else if (actions.setCurrentStep) {
        actions.setCurrentStep(stepIndex);
      }

      // Track navigation
      if (planLimits.navigationHistory) {
        setNavigationHistory(prev => [...prev, {
          action: 'navigate',
          from: currentStep,
          to: stepIndex,
          timestamp: new Date().toISOString()
        }].slice(-50));
      }

      announceToScreenReader(`Navigated to step ${stepIndex + 1}: ${steps[stepIndex]?.label}`);
    } catch (error) {
      console.error('Navigation error:', error);
      setNavigationState(NAVIGATION_STATES.ERROR);
    } finally {
      setNavigationState(NAVIGATION_STATES.IDLE);
    }
  }, [planLimits.customNavigation, planLimits.navigationHistory, actions, onNavigateToStep, currentStep, steps, announceToScreenReader]);

  const handleToggleBookmark = useCallback((stepIndex) => {
    if (!planLimits.workflowBookmarks) {
      announceToScreenReader('Bookmarks are not available in your current plan');
      return;
    }

    setBookmarkedSteps(prev => {
      const newSet = new Set(prev);
      if (newSet.has(stepIndex)) {
        newSet.delete(stepIndex);
        announceToScreenReader(`Removed bookmark from step ${stepIndex + 1}`);
      } else {
        newSet.add(stepIndex);
        announceToScreenReader(`Added bookmark to step ${stepIndex + 1}`);
      }
      return newSet;
    });

    if (onBookmarkStep) {
      onBookmarkStep(stepIndex);
    }
  }, [planLimits.workflowBookmarks, onBookmarkStep, announceToScreenReader]);

  const handleExportNavigation = useCallback(() => {
    if (!planLimits.navigationExport) {
      announceToScreenReader('Navigation export is not available in your current plan');
      return;
    }

    const exportData = {
      navigationHistory,
      bookmarkedSteps: Array.from(bookmarkedSteps),
      currentStep,
      completedSteps,
      timeEstimates,
      stepDurations,
      preferences: navigationPreferences,
      metadata: {
        exportedAt: new Date().toISOString(),
        planTier,
        version: '2.0.0'
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `navigation-data-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    if (onExportNavigation) {
      onExportNavigation(exportData);
    }

    announceToScreenReader('Navigation data exported successfully');
  }, [planLimits.navigationExport, navigationHistory, bookmarkedSteps, currentStep, completedSteps, timeEstimates, stepDurations, navigationPreferences, planTier, onExportNavigation, announceToScreenReader]);

  const getProgressData = useCallback(() => {
    const progressPercentage = ((completedSteps.length) / steps.length) * 100;
    const currentStepData = actions.getCurrentStepData();
    const hasCurrentStepData = currentStepData !== null && currentStepData !== undefined;

    return {
      percentage: progressPercentage,
      completedSteps: completedSteps.length,
      totalSteps: steps.length,
      currentStep: currentStep + 1,
      hasCurrentStepData,
      timeEstimates,
      stepDurations
    };
  }, [completedSteps, steps, currentStep, actions, timeEstimates, stepDurations]);

  const handleResetNavigation = useCallback(() => {
    setNavigationHistory([]);
    setBookmarkedSteps(new Set());
    setTimeEstimates({});
    setStepDurations({});
    setSmartSuggestions([]);
    announceToScreenReader('Navigation data reset');
  }, [announceToScreenReader]);

  // Enhanced keyboard navigation
  useKeyboardNavigation(
    handleNext,
    handlePrevious,
    handleSave,
    !enableKeyboardNavigation || !planLimits.keyboardShortcuts
  );

  // Effects for monitoring and state management
  useEffect(() => {
    // Update last save time when data changes
    if (workflowData && Object.keys(workflowData).length > 0) {
      setLastSaveTime(new Date());
    }
  }, [workflowData]);

  useEffect(() => {
    // Track step entry time for duration calculation
    const startTime = Date.now();

    return () => {
      const duration = Date.now() - startTime;
      setStepDurations(prev => ({
        ...prev,
        [currentStep]: duration
      }));
    };
  }, [currentStep]);

  useEffect(() => {
    // Generate smart navigation suggestions
    if (planLimits.smartNavigation && enableSmartNavigation) {
      const suggestions = [];

      // Suggest next logical step
      if (currentStep < steps.length - 1) {
        suggestions.push({
          type: 'next',
          stepIndex: currentStep + 1,
          reason: 'Continue workflow progression',
          confidence: 0.9
        });
      }

      // Suggest incomplete steps with data
      steps.forEach((step, index) => {
        if (index !== currentStep && !completedSteps.includes(index)) {
          const stepData = workflowData[step.id?.replace('-', '_').replace('_', '')];
          if (stepData) {
            suggestions.push({
              type: 'incomplete',
              stepIndex: index,
              reason: 'Complete step with saved data',
              confidence: 0.7
            });
          }
        }
      });

      setSmartSuggestions(suggestions.slice(0, 3)); // Keep top 3 suggestions
    }
  }, [planLimits.smartNavigation, enableSmartNavigation, currentStep, steps, completedSteps, workflowData]);

  // Enhanced validation and progress calculations
  const hasValidationErrors = validationErrors.length > 0;
  const currentStepData = actions.getCurrentStepData();
  const hasCurrentStepData = currentStepData !== null && currentStepData !== undefined;
  const progressPercentage = ((completedSteps.length) / steps.length) * 100;

  // Enhanced button state calculations
  const getNextButtonState = useCallback(() => {
    if (loading || navigationState === NAVIGATION_STATES.NAVIGATING) {
      return { disabled: true, tooltip: 'Processing...' };
    }
    if (hasValidationErrors) {
      return { disabled: true, tooltip: 'Please fix validation errors before proceeding' };
    }
    if (nextDisabled) {
      return { disabled: true, tooltip: 'Complete current step to continue' };
    }
    if (currentStep === steps.length - 1) {
      return { disabled: false, tooltip: 'Complete workflow' };
    }
    return { disabled: false, tooltip: 'Continue to next step' };
  }, [loading, navigationState, hasValidationErrors, nextDisabled, currentStep, steps.length]);

  // Helper functions for future use
  // const getPreviousButtonState = useCallback(() => {
  //   if (loading || navigationState === NAVIGATION_STATES.NAVIGATING) {
  //     return { disabled: true, tooltip: 'Processing...' };
  //   }
  //   return { disabled: false, tooltip: 'Go to previous step' };
  // }, [loading, navigationState]);

  // const getSaveButtonState = useCallback(() => {
  //   if (navigationState === NAVIGATION_STATES.SAVING) {
  //     return { disabled: true, tooltip: 'Saving progress...' };
  //   }
  //   return { disabled: false, tooltip: 'Save current progress' };
  // }, [navigationState]);

  const nextButtonState = getNextButtonState();

  return (
    <Box
      ref={navigationRef}
      sx={{
        position: 'sticky',
        top: theme.spacing(2),
        zIndex: theme.zIndex.appBar - 1,
        mb: theme.spacing(3),
        p: compactMode ? theme.spacing(1.5) : theme.spacing(2),
        ...glassMorphismStyles,
        // WCAG 2.1 AA compliance
        '&:focus-within': {
          outline: `2px solid ${ACE_COLORS.PURPLE}`,
          outlineOffset: '2px',
        },
        // Responsive design
        [theme.breakpoints.down('sm')]: {
          position: 'static',
          p: theme.spacing(1.5),
        },
        // Reduced motion support
        '@media (prefers-reduced-motion: reduce)': {
          transition: 'none',
          '& *': {
            transition: 'none !important',
            animation: 'none !important'
          }
        },
      }}
    >
      {/* Enhanced Progress Indicator */}
      <Box sx={{ mb: theme.spacing(2) }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: theme.spacing(1),
          flexWrap: 'wrap',
          gap: 1
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
            <NavigationIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: 20 }} />
            <Typography variant="body2" color="textSecondary" fontWeight={500}>
              Step {currentStep + 1} of {steps.length}: {steps[currentStep]?.label}
            </Typography>

            {/* Enhanced status indicators */}
            {hasCurrentStepData && !completedSteps.includes(currentStep) && (
              <Chip
                label="Draft"
                size="small"
                color="warning"
                variant="outlined"
                sx={{ height: 20, fontSize: '0.75rem' }}
              />
            )}

            {bookmarkedSteps.has(currentStep) && (
              <Tooltip title="Bookmarked step">
                <BookmarkIcon sx={{ color: ACE_COLORS.YELLOW, fontSize: 16 }} />
              </Tooltip>
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
            <Chip
              label={`${Math.round(progressPercentage)}% Complete`}
              size="small"
              color={progressPercentage === 100 ? 'success' : 'primary'}
              variant="outlined"
              sx={{ fontWeight: 600 }}
            />

            {/* Enhanced action buttons */}
            <FeatureGate requiredPlan={2}>
              <Tooltip title="Navigation menu">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    setNavigationMenuAnchor(e.currentTarget);
                    setShowNavigationMenu(true);
                  }}
                  sx={{ p: 0.5 }}
                >
                  <DropdownIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </FeatureGate>

            {showValidationHelp && (
              <Tooltip title="Show step guidance">
                <IconButton
                  size="small"
                  onClick={() => setShowHelp(!showHelp)}
                  sx={{ p: 0.5 }}
                >
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            <FeatureGate requiredPlan={2}>
              <Tooltip title="Keyboard shortcuts">
                <IconButton
                  size="small"
                  onClick={() => setShowShortcutsDialog(true)}
                  sx={{ p: 0.5 }}
                >
                  <ShortcutsIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </FeatureGate>
          </Box>
        </Box>

        {/* Enhanced Progress Bar */}
        <LinearProgress
          variant="determinate"
          value={progressPercentage}
          sx={{
            height: compactMode ? 6 : 10,
            borderRadius: compactMode ? 3 : 5,
            backgroundColor: alpha(theme.palette.action.disabled, 0.2),
            '& .MuiLinearProgress-bar': {
              borderRadius: compactMode ? 3 : 5,
              background: `linear-gradient(90deg,
                ${ACE_COLORS.PURPLE} 0%,
                ${ACE_COLORS.YELLOW} 100%)`,
            },
          }}
        />

        {/* Enhanced status indicators */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mt: 0.5,
          flexWrap: 'wrap',
          gap: 1
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {lastSaveTime && (
              <Typography variant="caption" color="textSecondary">
                Last saved: {lastSaveTime.toLocaleTimeString()}
              </Typography>
            )}

            {showTimeEstimates && planLimits.advancedProgress && (
              <Typography variant="caption" color="textSecondary">
                Est. time remaining: {Math.max(0, (steps.length - currentStep - 1) * 5)} min
              </Typography>
            )}
          </Box>

          <FeatureGate requiredPlan={2}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {navigationState !== NAVIGATION_STATES.IDLE && (
                <CircularProgress size={12} sx={{ color: ACE_COLORS.PURPLE }} />
              )}

              {planLimits.advancedProgress && (
                <Tooltip title="Show progress details">
                  <IconButton
                    size="small"
                    onClick={() => setShowProgressDetails(!showProgressDetails)}
                    sx={{ p: 0.25 }}
                  >
                    <AnalyticsIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </FeatureGate>
        </Box>

        {/* Progress Details */}
        <FeatureGate requiredPlan={2}>
          <Collapse in={showProgressDetails && planLimits.advancedProgress}>
            <Card sx={{ mt: 1, background: alpha(ACE_COLORS.PURPLE, 0.05) }}>
              <CardContent sx={{ py: 1, px: 2 }}>
                <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" color="primary">
                      {completedSteps.length}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Completed
                    </Typography>
                  </Box>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" color="warning.main">
                      {steps.length - completedSteps.length - 1}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Remaining
                    </Typography>
                  </Box>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" color="info.main">
                      {Object.values(stepDurations).reduce((sum, duration) => sum + duration, 0) / 1000 / 60 || 0}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Total Minutes
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Collapse>
        </FeatureGate>
      </Box>

      {/* Validation errors and help */}
      <Collapse in={hasValidationErrors || showHelp}>
        <Box sx={{ mb: theme.spacing(2) }}>
          {hasValidationErrors && (
            <Alert
              severity="warning"
              sx={{ mb: theme.spacing(1) }}
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => {
                    // Clear validation errors if possible
                  }}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
            >
              <Typography variant="body2" fontWeight={600}>
                Please complete the following before proceeding:
              </Typography>
              <ul style={{ margin: '4px 0', paddingLeft: '16px' }}>
                {validationErrors.map((error, index) => (
                  <li key={index}>
                    <Typography variant="body2">{error}</Typography>
                  </li>
                ))}
              </ul>
            </Alert>
          )}

          {showHelp && (
            <Alert
              severity="info"
              sx={{ mb: theme.spacing(1) }}
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => setShowHelp(false)}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
            >
              <Typography variant="body2" fontWeight={600}>
                {steps[currentStep]?.label} - Step Guidance
              </Typography>
              <Typography variant="body2">
                {steps[currentStep]?.description}
              </Typography>
            </Alert>
          )}
        </Box>
      </Collapse>

      {/* Enhanced Breadcrumb navigation */}
      <Box sx={{ mb: theme.spacing(2) }}>
        <Breadcrumbs
          separator="›"
          aria-label="workflow navigation"
          sx={{
            '& .MuiBreadcrumbs-separator': {
              color: theme.palette.text.secondary,
            },
          }}
        >
          {steps.map((step, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = index === currentStep;
            const canNavigate = actions.canProceedToStep && actions.canProceedToStep(index);
            const stepData = workflowData[step.id.replace('-', '_').replace('_', '')];
            const hasData = stepData !== null && stepData !== undefined;

            return (
              <Tooltip
                key={step.id}
                title={
                  isCompleted
                    ? `${step.label} - Completed`
                    : isCurrent
                      ? `${step.label} - Current step`
                      : hasData
                        ? `${step.label} - Draft saved`
                        : `${step.label} - Not started`
                }
                arrow
              >
                <Link
                  component="button"
                  variant="body2"
                  onClick={() => handleNavigateToStep(index)}
                  disabled={!canNavigate}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    color: isCurrent
                      ? theme.palette.primary.main
                      : isCompleted
                        ? theme.palette.success.main
                        : theme.palette.text.secondary,
                    textDecoration: 'none',
                    fontWeight: isCurrent ? 600 : 400,
                    cursor: canNavigate ? 'pointer' : 'default',
                    '&:hover': canNavigate ? {
                      textDecoration: 'underline',
                    } : {},
                    '&:disabled': {
                      color: theme.palette.text.disabled,
                      cursor: 'not-allowed',
                    },
                    // WCAG 2.1 AA compliance
                    '&:focus-visible': {
                      outline: `2px solid ${theme.palette.primary.main}`,
                      outlineOffset: '2px',
                      borderRadius: theme.spacing(0.5),
                    },
                  }}
                >
                  {isCompleted && <CheckCircleIcon sx={{ fontSize: 16 }} />}
                  {hasData && !isCompleted && !isCurrent && <WarningIcon sx={{ fontSize: 16 }} />}
                  {step.label}
                </Link>
              </Tooltip>
            );
          })}
        </Breadcrumbs>
      </Box>

      {/* Navigation buttons */}
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          gap: theme.spacing(1),
          // Responsive design
          [theme.breakpoints.down('sm')]: {
            flexDirection: 'column',
            gap: theme.spacing(1.5),
          },
        }}
      >
        <Box sx={{
          display: 'flex',
          gap: theme.spacing(1),
          // Responsive design
          [theme.breakpoints.down('sm')]: {
            width: '100%',
            justifyContent: 'space-between',
          },
        }}>
          <Tooltip title={currentStep === 0 ? 'First step' : 'Go to previous step'} arrow>
            <span>
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={onPrevious}
                disabled={previousDisabled || currentStep === 0 || loading}
                sx={{
                  minWidth: 120,
                  // WCAG 2.1 AA compliance
                  '&:focus-visible': {
                    outline: `2px solid ${theme.palette.primary.main}`,
                    outlineOffset: '2px',
                  },
                  // Responsive design
                  [theme.breakpoints.down('sm')]: {
                    minWidth: 100,
                    flex: 1,
                  },
                }}
              >
                {previousLabel}
              </Button>
            </span>
          </Tooltip>

          {showSave && (
            <Tooltip title={loading ? 'Saving progress...' : 'Save current progress'} arrow>
              <span>
                <Button
                  variant="outlined"
                  startIcon={loading ? <CircularProgress size={16} /> : <SaveIcon />}
                  onClick={onSave}
                  disabled={saveDisabled || loading}
                  sx={{
                    minWidth: 140,
                    // WCAG 2.1 AA compliance
                    '&:focus-visible': {
                      outline: `2px solid ${theme.palette.primary.main}`,
                      outlineOffset: '2px',
                    },
                    // Responsive design
                    [theme.breakpoints.down('sm')]: {
                      minWidth: 100,
                      flex: 1,
                    },
                  }}
                >
                  {loading ? 'Saving...' : saveLabel}
                </Button>
              </span>
            </Tooltip>
          )}
        </Box>

        <Tooltip title={nextButtonState.tooltip} arrow>
          <span>
            <Button
              variant="contained"
              endIcon={currentStep === steps.length - 1 ? <CheckCircleIcon /> : <ArrowForwardIcon />}
              startIcon={hasValidationErrors ? <WarningIcon /> : undefined}
              onClick={onNext}
              disabled={nextButtonState.disabled}
              color={hasValidationErrors ? 'warning' : 'primary'}
              sx={{
                minWidth: 120,
                // WCAG 2.1 AA compliance
                '&:focus-visible': {
                  outline: `2px solid ${theme.palette.common.white}`,
                  outlineOffset: '2px',
                },
                // Responsive design
                [theme.breakpoints.down('sm')]: {
                  width: '100%',
                  mt: theme.spacing(1),
                },
              }}
            >
              {currentStep === steps.length - 1 ? 'Complete Workflow' : nextLabel}
            </Button>
          </span>
        </Tooltip>
      </Box>

      {/* Enhanced Navigation Menu */}
      <FeatureGate requiredPlan={2}>
        <Menu
          anchorEl={navigationMenuAnchor}
          open={showNavigationMenu}
          onClose={() => setShowNavigationMenu(false)}
          slotProps={{
            paper: {
              sx: { ...glassMorphismStyles, minWidth: 200 }
            }
          }}
        >
          <MenuItem onClick={() => setShowShortcutsDialog(true)}>
            <ListItemIcon>
              <ShortcutsIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Keyboard Shortcuts</ListItemText>
          </MenuItem>

          <MenuItem onClick={handleExportNavigation}>
            <ListItemIcon>
              <AnalyticsIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export Navigation Data</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => setShowProgressDetails(!showProgressDetails)}>
            <ListItemIcon>
              <TimelineIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Progress Details</ListItemText>
          </MenuItem>
        </Menu>
      </FeatureGate>

      {/* Keyboard Shortcuts Dialog */}
      <FeatureGate requiredPlan={2}>
        <Dialog
          open={showShortcutsDialog}
          onClose={() => setShowShortcutsDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <ShortcutsIcon />
              Keyboard Shortcuts
            </Box>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Next Step</Typography>
                <Chip label="Ctrl/Cmd + →" size="small" variant="outlined" />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Previous Step</Typography>
                <Chip label="Ctrl/Cmd + ←" size="small" variant="outlined" />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Save Progress</Typography>
                <Chip label="Ctrl/Cmd + S" size="small" variant="outlined" />
              </Box>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowShortcutsDialog(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      </FeatureGate>

      {/* Smart Navigation Suggestions */}
      <FeatureGate requiredPlan={3}>
        {smartSuggestions.length > 0 && (
          <Snackbar
            open={smartSuggestions.length > 0}
            autoHideDuration={10000}
            onClose={() => setSmartSuggestions([])}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          >
            <Alert
              severity="info"
              onClose={() => setSmartSuggestions([])}
              action={
                smartSuggestions[0] && (
                  <Button
                    color="inherit"
                    size="small"
                    onClick={() => {
                      handleNavigateToStep(smartSuggestions[0].stepIndex);
                      setSmartSuggestions([]);
                    }}
                  >
                    Go
                  </Button>
                )
              }
            >
              <Typography variant="body2">
                Suggestion: {smartSuggestions[0]?.reason}
              </Typography>
            </Alert>
          </Snackbar>
        )}
      </FeatureGate>
    </Box>
  );
}));

WorkflowNavigation.displayName = 'WorkflowNavigation';

WorkflowNavigation.propTypes = {
  /** Next step callback */
  onNext: PropTypes.func,
  /** Previous step callback */
  onPrevious: PropTypes.func,
  /** Save progress callback */
  onSave: PropTypes.func,
  /** Navigate to specific step callback */
  onNavigateToStep: PropTypes.func,
  /** Bookmark step callback */
  onBookmarkStep: PropTypes.func,
  /** Export navigation data callback */
  onExportNavigation: PropTypes.func,
  /** Next button label */
  nextLabel: PropTypes.string,
  /** Previous button label */
  previousLabel: PropTypes.string,
  /** Save button label */
  saveLabel: PropTypes.string,
  /** Show save button */
  showSave: PropTypes.bool,
  /** Disable next button */
  nextDisabled: PropTypes.bool,
  /** Disable previous button */
  previousDisabled: PropTypes.bool,
  /** Disable save button */
  saveDisabled: PropTypes.bool,
  /** Validation errors array */
  validationErrors: PropTypes.arrayOf(PropTypes.string),
  /** Show validation help */
  showValidationHelp: PropTypes.bool,
  /** Enable keyboard navigation */
  enableKeyboardNavigation: PropTypes.bool,
  /** Enable navigation history */
  enableNavigationHistory: PropTypes.bool,
  /** Enable bookmarks */
  enableBookmarks: PropTypes.bool,
  /** Show progress analytics */
  showProgressAnalytics: PropTypes.bool,
  /** Animation type */
  animationType: PropTypes.oneOf(['fade', 'slide', 'zoom', 'none']),
  /** Animation duration in milliseconds */
  animationDuration: PropTypes.number,
  /** Enable compact mode */
  compactMode: PropTypes.bool,
  /** Show time estimates */
  showTimeEstimates: PropTypes.bool,
  /** Enable smart navigation */
  enableSmartNavigation: PropTypes.bool
};

export default WorkflowNavigation;

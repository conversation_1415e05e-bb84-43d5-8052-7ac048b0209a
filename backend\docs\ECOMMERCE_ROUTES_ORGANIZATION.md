# E-commerce Routes Organization

## Overview

This document outlines the organization and structure of e-commerce API routes in the ACEO platform to prevent duplication and ensure clear separation of concerns.

## Route Structure

### Core E-commerce Routes (`/api/ecommerce`)
**File**: `backend/app/api/routes/ecommerce.py`
**Purpose**: Basic e-commerce store management and integration

#### Endpoints:
- `GET /stores` - List user's connected stores
- `POST /stores/connect` - Connect new e-commerce store
- `DELETE /stores/{store_id}` - Disconnect store
- `POST /stores/{store_id}/sync` - Sync store products
- `GET /stores/{store_id}/products` - Get store products
- `POST /stores/{store_id}/products/generate-icps` - Generate ICPs from products
- `POST /campaigns/product-campaign` - Create product-based campaigns
- `POST /campaigns/{campaign_id}/generate-content` - Generate campaign content
- `GET /sync/status` - Get sync status
- `POST /webhooks/{platform}` - Handle platform webhooks

### Advanced E-commerce Features

#### AI Content Generation (`/api/ecommerce/ai-content`)
**File**: `backend/app/api/routes/ai_content.py`
**Purpose**: AI-powered content generation for e-commerce products

#### Bulk Operations (`/api/ecommerce/bulk`)
**File**: `backend/app/api/routes/bulk_operations.py`
**Purpose**: Bulk import/export operations with background processing

#### Currency Management (`/api/ecommerce/currency`)
**File**: `backend/app/api/routes/currency.py`
**Purpose**: Multi-currency support and conversion

#### Product Variants (`/api/ecommerce/variants`)
**File**: `backend/app/api/routes/variants.py`
**Purpose**: Complex product variant management

#### Product Search (`/api/ecommerce/search`)
**File**: `backend/app/api/routes/search.py`
**Purpose**: Advanced product search with filtering and facets

#### Real-time Inventory (`/api/ecommerce/realtime`)
**File**: `backend/app/api/routes/realtime_inventory.py`
**Purpose**: Real-time inventory updates via WebSocket

#### Inventory Management (`/api/ecommerce/inventory`)
**File**: `backend/app/api/routes/inventory.py`
**Purpose**: Comprehensive inventory management and tracking

## Service Organization

### Core Services
- `EcommerceService` - Main store and product management
- `EcommerceWebhookService` - Webhook processing
- `EcommerceSyncScheduler` - Background synchronization
- `EcommerceICPGenerator` - ICP generation
- `EcommerceCampaignService` - Campaign management

### Advanced Services (`backend/app/services/ecommerce/`)
- `currency_service.py` - Currency conversion and management
- `variants_service.py` - Product variant operations
- `bulk_operations_service.py` - Bulk processing operations
- `search_service.py` - Product search and filtering
- `realtime_inventory_service.py` - Real-time inventory updates
- `ai_content_service.py` - AI content generation
- `inventory_service.py` - Inventory management

## Schema Organization

### Main E-commerce Schemas
**File**: `backend/app/schemas/ecommerce.py`
**Contains**: All e-commerce related request/response schemas

### Key Schema Groups:
- **Currency**: `CurrencyConversionRequest`, `CurrencyConversionResponse`
- **Variants**: `ProductVariantRequest`, `ProductVariantResponse`
- **Bulk Operations**: `BulkImportRequest`, `BulkImportResponse`, `BulkExportRequest`, `BulkExportResponse`
- **Search**: `ProductSearchRequest`, `ProductSearchResponse`
- **Inventory**: `InventoryUpdateRequest`, `InventoryStatusResponse`
- **AI Content**: `AIContentRequest`, `AIContentResponse`

## Integration Points

### Authentication
All routes use `get_current_active_user` dependency for authentication.

### Feature Access
All routes use `require_feature_access("ecommerce_integration")` for feature gating.

### Rate Limiting
All routes implement appropriate rate limiting based on operation complexity.

### Monitoring
All services implement performance monitoring and audit logging.

## Best Practices

### Route Naming
- Use clear, descriptive path segments
- Group related functionality under common prefixes
- Avoid deep nesting (max 3 levels)

### Service Separation
- Each service has a single, well-defined responsibility
- No overlapping functionality between services
- Clear interfaces between services

### Schema Consistency
- All schemas in single file to prevent duplication
- Consistent naming conventions
- Proper validation and documentation

## Future Additions

When adding new e-commerce functionality:

1. **Check Existing Routes**: Ensure no duplication with existing endpoints
2. **Service Placement**: Add to appropriate existing service or create new one
3. **Schema Location**: Add schemas to `ecommerce.py` schema file
4. **Route Registration**: Register in `main.py` under appropriate prefix
5. **Documentation**: Update this document with new additions

## Maintenance

### Regular Audits
- Check for duplicate functionality quarterly
- Review route organization for optimization opportunities
- Ensure consistent patterns across all routes

### Performance Monitoring
- Monitor route performance and usage
- Optimize frequently used endpoints
- Review rate limiting effectiveness

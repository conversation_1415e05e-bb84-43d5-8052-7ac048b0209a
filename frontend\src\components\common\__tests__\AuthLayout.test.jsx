import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';
import AuthLayout from '../AuthLayout';

// Mock the auth context
const mockAuth = {
  user: null,
  isLoading: false,
  login: vi.fn(),
  logout: vi.fn()
};

vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth,
}));

// Mock the notification hook
const mockNotification = {
  showSuccessNotification: vi.fn(),
  showErrorNotification: vi.fn()
};

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => mockNotification,
}));

// Mock react-router-dom hooks
const mockNavigate = vi.fn();
const mockLocation = {
  pathname: '/auth/login'
};

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
    Outlet: () => <div data-testid="auth-form">Auth Form Content</div>
  };
});

// Mock window.open
Object.defineProperty(window, 'open', {
  value: vi.fn(),
  writable: true
});

// Test wrapper with theme and router
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
        paper: '#FFFFFF',
      },
      text: {
        primary: '#000000',
        secondary: '#666666',
      },
      success: {
        main: '#4CAF50',
        light: '#C8E6C9',
        contrastText: '#FFFFFF',
      },
      divider: '#E0E0E0',
      common: {
        white: '#FFFFFF',
      },
    },
    shadows: [
      'none',
      '0px 2px 1px -1px rgba(0,0,0,0.2)',
      '0px 3px 1px -2px rgba(0,0,0,0.12)',
      '0px 1px 5px 0px rgba(0,0,0,0.12)',
      '0px 2px 4px -1px rgba(0,0,0,0.2)',
      '0px 3px 5px -1px rgba(0,0,0,0.2)',
      '0px 3px 5px -1px rgba(0,0,0,0.2)',
      '0px 4px 5px -2px rgba(0,0,0,0.2)',
      '0px 5px 5px -3px rgba(0,0,0,0.2)',
      '0px 6px 10px -2px rgba(0,0,0,0.2)',
      '0px 6px 10px -2px rgba(0,0,0,0.2)',
      '0px 6px 10px -2px rgba(0,0,0,0.2)',
      '0px 8px 10px -5px rgba(0,0,0,0.2)',
    ],
    spacing: (factor) => `${8 * factor}px`,
    breakpoints: {
      values: {
        xs: 0,
        sm: 600,
        md: 900,
        lg: 1200,
        xl: 1536,
      },
    },
  });
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>{children}</ThemeProvider>
    </BrowserRouter>
  );
};

describe('AuthLayout', () => {
  const mockProps = {
    showSecurityBadge: true,
    showUtilityBar: true,
    brandName: 'Test App',
    securityLevel: 'high'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock HTTPS protocol
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        pathname: '/auth/login'
      },
      writable: true
    });
    // Mock secure context
    Object.defineProperty(window, 'isSecureContext', {
      value: true,
      writable: true
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders auth layout correctly', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    // Should render brand name
    expect(screen.getByText('Test App')).toBeInTheDocument();
    
    // Should render security description
    expect(screen.getByText('Secure authentication for your account')).toBeInTheDocument();
    
    // Should render auth form content
    expect(screen.getByTestId('auth-form')).toBeInTheDocument();
  });

  test('displays security badge when enabled', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} showSecurityBadge={true} />
      </TestWrapper>
    );

    // Should show security badge
    expect(screen.getByText('Maximum Security')).toBeInTheDocument();
  });

  test('hides security badge when disabled', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} showSecurityBadge={false} />
      </TestWrapper>
    );

    // Should not show security badge
    expect(screen.queryByText('Maximum Security')).not.toBeInTheDocument();
  });

  test('displays utility bar when enabled', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} showUtilityBar={true} />
      </TestWrapper>
    );

    // Should show utility buttons
    expect(screen.getByLabelText('Toggle theme')).toBeInTheDocument();
    expect(screen.getByLabelText('Change language')).toBeInTheDocument();
    expect(screen.getByLabelText('Get help')).toBeInTheDocument();
  });

  test('hides utility bar when disabled', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} showUtilityBar={false} />
      </TestWrapper>
    );

    // Should not show utility buttons
    expect(screen.queryByLabelText('Toggle theme')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Change language')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Get help')).not.toBeInTheDocument();
  });

  test('handles theme toggle', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    const themeButton = screen.getByLabelText('Toggle theme');
    await user.click(themeButton);

    expect(consoleSpy).toHaveBeenCalledWith('Theme toggle requested');
    
    consoleSpy.mockRestore();
  });

  test('handles language toggle', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    const languageButton = screen.getByLabelText('Change language');
    await user.click(languageButton);

    expect(consoleSpy).toHaveBeenCalledWith('Language toggle requested');
    
    consoleSpy.mockRestore();
  });

  test('handles help action', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    const helpButton = screen.getByLabelText('Get help');
    await user.click(helpButton);

    expect(window.open).toHaveBeenCalledWith('/help/authentication', '_blank');
  });

  test('displays different security levels', () => {
    const { rerender } = render(
      <TestWrapper>
        <AuthLayout {...mockProps} securityLevel="low" />
      </TestWrapper>
    );

    expect(screen.getByText('Standard Security')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <AuthLayout {...mockProps} securityLevel="medium" />
      </TestWrapper>
    );

    expect(screen.getByText('Enhanced Security')).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <AuthLayout {...mockProps} securityLevel="high" />
      </TestWrapper>
    );

    expect(screen.getByText('Maximum Security')).toBeInTheDocument();
  });

  test('shows loading overlay when enabled', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} showLoadingOverlay={true} />
      </TestWrapper>
    );

    // Should show loading indicator
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('displays custom logo when provided', () => {
    const customLogo = <div data-testid="custom-logo">Custom Logo</div>;
    
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} customLogo={customLogo} />
      </TestWrapper>
    );

    expect(screen.getByTestId('custom-logo')).toBeInTheDocument();
  });

  test('displays custom footer when provided', () => {
    const customFooter = <div data-testid="custom-footer">Custom Footer</div>;
    
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} customFooter={customFooter} />
      </TestWrapper>
    );

    expect(screen.getByTestId('custom-footer')).toBeInTheDocument();
  });

  test('displays default footer when no custom footer provided', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    // Should show default footer links
    expect(screen.getByText('Privacy Policy')).toBeInTheDocument();
    expect(screen.getByText('Terms of Service')).toBeInTheDocument();
    expect(screen.getByText('Support')).toBeInTheDocument();
  });

  test('handles insecure connection warning', () => {
    // Mock insecure context
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'http:',
        pathname: '/auth/login'
      },
      writable: true
    });
    Object.defineProperty(window, 'isSecureContext', {
      value: false,
      writable: true
    });

    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    // Should show security warning
    expect(screen.getByText('Insecure connection detected. Please ensure you are using HTTPS.')).toBeInTheDocument();
  });

  test('handles online/offline status', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    // Simulate going offline
    fireEvent(window, new Event('offline'));

    // Should show offline warning
    expect(screen.getByText('You are currently offline. Some features may not be available.')).toBeInTheDocument();

    // Simulate going online
    fireEvent(window, new Event('online'));

    // Should remove offline warning
    expect(screen.queryByText('You are currently offline. Some features may not be available.')).not.toBeInTheDocument();
  });

  test('redirects authenticated users', () => {
    const authenticatedAuth = {
      ...mockAuth,
      user: { id: '1', name: 'Test User' }
    };

    vi.mocked(require('../../../contexts/AuthContext').useAuth).mockReturnValue(authenticatedAuth);

    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
  });

  test('shows loading state when auth is loading', () => {
    const loadingAuth = {
      ...mockAuth,
      isLoading: true
    };

    vi.mocked(require('../../../contexts/AuthContext').useAuth).mockReturnValue(loadingAuth);

    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    // Should show loading overlay
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    // Should have proper ARIA labels
    expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Authentication page');
    expect(screen.getByRole('region')).toHaveAttribute('aria-label', 'Authentication form');
    
    // Buttons should have proper labels
    expect(screen.getByLabelText('Toggle theme')).toBeInTheDocument();
    expect(screen.getByLabelText('Change language')).toBeInTheDocument();
    expect(screen.getByLabelText('Get help')).toBeInTheDocument();
  });

  test('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    // Should be able to navigate with keyboard
    await user.tab();
    expect(screen.getByLabelText('Toggle theme')).toHaveFocus();

    await user.tab();
    expect(screen.getByLabelText('Change language')).toHaveFocus();

    await user.tab();
    expect(screen.getByLabelText('Get help')).toHaveFocus();
  });

  test('handles animations when enabled', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} enableAnimations={true} />
      </TestWrapper>
    );

    // Should render with animations (Fade and Zoom components)
    expect(screen.getByText('Test App')).toBeInTheDocument();
  });

  test('handles animations when disabled', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} enableAnimations={false} />
      </TestWrapper>
    );

    // Should still render content without animations
    expect(screen.getByText('Test App')).toBeInTheDocument();
  });

  test('displays copyright with current year', () => {
    render(
      <TestWrapper>
        <AuthLayout {...mockProps} />
      </TestWrapper>
    );

    const currentYear = new Date().getFullYear();
    expect(screen.getByText(`© ${currentYear} Test App. All rights reserved.`)).toBeInTheDocument();
  });
});

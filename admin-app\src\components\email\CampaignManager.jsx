/**
 * Enhanced ACE Social Campaign Manager - Enterprise-grade email campaign management component
 * Features: Comprehensive email campaign management with advanced template editor, audience
 * segmentation, and automated scheduling capabilities for ACE Social email marketing campaigns,
 * detailed campaign dashboard with performance analytics and subscriber engagement monitoring,
 * advanced campaign features with A/B testing and automated workflows, ACE Social's email
 * system integration with seamless campaign lifecycle management, campaign interaction features
 * including drag-and-drop template builder and real-time preview, campaign state management
 * with draft saving and version control, and real-time campaign monitoring with live delivery
 * tracking and automatic campaign optimization
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Skeleton,
  Stack,
  Divider,
  Badge,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  Checkbox,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide,
  Collapse
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Analytics as AnalyticsIcon,
  Schedule as ScheduleIcon,
  Email as EmailIcon,
  TrendingUp as TrendingUpIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Send as SendIcon,
  Group as GroupIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  Insights as InsightsIcon,
  Campaign as CampaignIcon,
  AutoAwesome as AutoAwesomeIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  ContentCopy as CopyIcon,
  History as HistoryIcon,
  Star as StarIcon,
  MonetizationOn as MoneyIcon
} from '@mui/icons-material';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Campaign management constants
const CAMPAIGN_VIEWS = {
  OVERVIEW: 'overview',
  DETAILED: 'detailed',
  ANALYTICS: 'analytics',
  TEMPLATES: 'templates'
};

const CAMPAIGN_STATUSES = {
  DRAFT: 'draft',
  SCHEDULED: 'scheduled',
  RUNNING: 'running',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

const CAMPAIGN_TYPES = {
  NEWSLETTER: 'newsletter',
  PROMOTIONAL: 'promotional',
  TRANSACTIONAL: 'transactional',
  DRIP: 'drip',
  AB_TEST: 'ab_test'
};

// Campaign analytics events
const CAMPAIGN_ANALYTICS_EVENTS = {
  CAMPAIGN_CREATED: 'campaign_created',
  CAMPAIGN_STARTED: 'campaign_started',
  CAMPAIGN_PAUSED: 'campaign_paused',
  CAMPAIGN_COMPLETED: 'campaign_completed',
  TEMPLATE_SELECTED: 'template_selected',
  ANALYTICS_VIEWED: 'analytics_viewed',
  EXPORT_TRIGGERED: 'export_triggered'
};

/**
 * Enhanced Campaign Manager - Comprehensive email campaign management with advanced features
 * Implements detailed campaign management and enterprise-grade email marketing capabilities
 */

const EnhancedCampaignManager = memo(forwardRef(({
  campaigns = [],
  templates = [],
  loading = false,
  error,
  className,
  enableAdvancedFeatures = true,
  enableRealTimeTracking = true,
  enableAnalytics = true,
  enableAccessibility = true,
  enableBulkOperations = true,
  enableTemplateManagement = true,
  defaultView = CAMPAIGN_VIEWS.OVERVIEW,
  autoRefreshInterval = 300000, // 5 minutes
  maxDisplayCampaigns = 1000,
  onCampaignCreate,
  onCampaignEdit,
  onCampaignDelete,
  onCampaignStart,
  onCampaignPause,
  onCampaignStop,
  onTemplateSelect,
  onAnalyticsView,
  onDataExport,
  onAnalyticsTrack,
  onRefresh,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const tableRef = useRef(null);
  const menuRef = useRef(null);
  const searchRef = useRef(null);
  const autoRefreshTimeoutRef = useRef(null);

  // Core state management
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCampaign, setSelectedCampaign] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    template: 'all'
  });

  // Enhanced state management
  const [currentView, setCurrentView] = useState(defaultView);
  const [selectedCampaigns, setSelectedCampaigns] = useState([]);
  const [sortConfig, setSortConfig] = useState({ field: 'created_at', direction: 'desc' });
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [campaignAnalytics, setCampaignAnalytics] = useState({
    totalSent: 0,
    totalOpened: 0,
    totalClicked: 0,
    avgOpenRate: 0,
    avgClickRate: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    refreshCampaigns: () => handleRefresh(),
    createCampaign: () => handleCreateCampaign(),
    exportData: () => handleExport(),
    focusTable: () => tableRef.current?.focus(),

    // Navigation methods
    changeView: (view) => handleViewChange(view),
    searchCampaigns: (term) => setSearchTerm(term),
    filterCampaigns: (filters) => setFilters(filters),

    // Data methods
    getFilteredCampaigns: () => filteredCampaigns,
    getSelectedCampaigns: () => selectedCampaigns,
    getCampaignAnalytics: () => campaignAnalytics,
    getCurrentView: () => currentView,

    // State methods
    isLoading: () => loading,
    hasError: () => !!error,
    getCampaignCount: () => campaigns.length,

    // Selection methods
    selectCampaign: (campaignId) => handleCampaignSelect(campaignId),
    selectAllCampaigns: () => handleSelectAll(),
    clearSelection: () => setSelectedCampaigns([]),

    // Campaign operations
    startCampaign: (campaignId) => handleCampaignStart(campaignId),
    pauseCampaign: (campaignId) => handleCampaignPause(campaignId),
    stopCampaign: (campaignId) => handleCampaignStop(campaignId),

    // Analytics methods
    getPerformanceMetrics: () => generatePerformanceMetrics(),
    getTopPerformers: () => getTopPerformingCampaigns(),

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    duplicateCampaign: (campaignId) => handleDuplicateCampaign(campaignId),
    scheduleCampaign: (campaignId, schedule) => handleScheduleCampaign(campaignId, schedule),
    generateReport: () => generateCampaignReport()
  }), [
    filteredCampaigns,
    selectedCampaigns,
    campaignAnalytics,
    currentView,
    loading,
    error,
    campaigns.length,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(CAMPAIGN_ANALYTICS_EVENTS.ANALYTICS_VIEWED, {
        view: currentView,
        timestamp: new Date().toISOString(),
        campaignsCount: campaigns.length
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Campaign manager view changed to ${currentView}`);
    }
  }, [currentView, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader, campaigns.length]);

  // Auto-refresh effect
  useEffect(() => {
    if (enableRealTimeTracking && autoRefreshInterval > 0) {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }

      autoRefreshTimeoutRef.current = setTimeout(() => {
        if (onRefresh) {
          onRefresh();
        }
      }, autoRefreshInterval);

      return () => {
        if (autoRefreshTimeoutRef.current) {
          clearTimeout(autoRefreshTimeoutRef.current);
        }
      };
    }
  }, [enableRealTimeTracking, autoRefreshInterval, onRefresh]);

  // Calculate campaign analytics
  useEffect(() => {
    if (campaigns.length > 0) {
      const analytics = campaigns.reduce((acc, campaign) => {
        acc.totalSent += campaign.sent_count || 0;
        acc.totalOpened += campaign.opened_count || 0;
        acc.totalClicked += campaign.clicked_count || 0;
        return acc;
      }, { totalSent: 0, totalOpened: 0, totalClicked: 0 });

      const avgOpenRate = analytics.totalSent > 0 ? (analytics.totalOpened / analytics.totalSent) * 100 : 0;
      const avgClickRate = analytics.totalSent > 0 ? (analytics.totalClicked / analytics.totalSent) * 100 : 0;

      setCampaignAnalytics({
        ...analytics,
        avgOpenRate: avgOpenRate.toFixed(1),
        avgClickRate: avgClickRate.toFixed(1)
      });
    }
  }, [campaigns]);

  // Enhanced handler functions
  const handleMenuOpen = useCallback((event, campaign) => {
    setAnchorEl(event.currentTarget);
    setSelectedCampaign(campaign);

    if (enableAccessibility) {
      announceToScreenReader(`Opened menu for campaign ${campaign.name}`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedCampaign(null);

    if (enableAccessibility) {
      announceToScreenReader('Campaign menu closed');
    }
  }, [enableAccessibility, announceToScreenReader]);

  // Enhanced campaign operation handlers
  const handleCampaignStart = useCallback((campaignId) => {
    if (onCampaignStart) {
      onCampaignStart(campaignId);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(CAMPAIGN_ANALYTICS_EVENTS.CAMPAIGN_STARTED, {
        campaignId,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Campaign started successfully');
    }
  }, [onCampaignStart, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleCampaignPause = useCallback((campaignId) => {
    if (onCampaignPause) {
      onCampaignPause(campaignId);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(CAMPAIGN_ANALYTICS_EVENTS.CAMPAIGN_PAUSED, {
        campaignId,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Campaign paused successfully');
    }
  }, [onCampaignPause, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleCampaignStop = useCallback((campaignId) => {
    if (onCampaignStop) {
      onCampaignStop(campaignId);
    }

    if (enableAccessibility) {
      announceToScreenReader('Campaign stopped successfully');
    }
  }, [onCampaignStop, enableAccessibility, announceToScreenReader]);

  const handleCreateCampaign = useCallback(() => {
    setShowCreateDialog(true);

    if (onCampaignCreate) {
      onCampaignCreate();
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(CAMPAIGN_ANALYTICS_EVENTS.CAMPAIGN_CREATED, {
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Campaign creation dialog opened');
    }
  }, [onCampaignCreate, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
    }

    if (enableAccessibility) {
      announceToScreenReader('Campaign data refreshed');
    }
  }, [onRefresh, enableAccessibility, announceToScreenReader]);

  const handleViewChange = useCallback((newView) => {
    setCurrentView(newView);

    if (enableAccessibility) {
      announceToScreenReader(`Switched to ${newView} view`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleCampaignSelect = useCallback((campaignId) => {
    setSelectedCampaigns(prev => {
      const isSelected = prev.includes(campaignId);
      const newSelection = isSelected
        ? prev.filter(id => id !== campaignId)
        : [...prev, campaignId];

      if (enableAccessibility) {
        announceToScreenReader(`Campaign ${isSelected ? 'deselected' : 'selected'}`);
      }

      return newSelection;
    });
  }, [enableAccessibility, announceToScreenReader]);

  const handleSelectAll = useCallback(() => {
    const allIds = filteredCampaigns.map(campaign => campaign.id);
    setSelectedCampaigns(allIds);

    if (enableAccessibility) {
      announceToScreenReader(`Selected all ${allIds.length} campaigns`);
    }
  }, [filteredCampaigns, enableAccessibility, announceToScreenReader]);

  const handleExport = useCallback(() => {
    if (onDataExport) {
      onDataExport(selectedCampaigns.length > 0 ? selectedCampaigns : campaigns);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(CAMPAIGN_ANALYTICS_EVENTS.EXPORT_TRIGGERED, {
        exportedCount: selectedCampaigns.length > 0 ? selectedCampaigns.length : campaigns.length,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader('Campaign data exported successfully');
    }
  }, [onDataExport, selectedCampaigns, campaigns, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced utility functions
  const generatePerformanceMetrics = useCallback(() => {
    if (!campaigns.length) return null;

    const metrics = campaigns.reduce((acc, campaign) => {
      acc.totalCampaigns += 1;
      acc.totalSent += campaign.sent_count || 0;
      acc.totalOpened += campaign.opened_count || 0;
      acc.totalClicked += campaign.clicked_count || 0;

      if (campaign.status === 'running') acc.activeCampaigns += 1;
      if (campaign.status === 'completed') acc.completedCampaigns += 1;

      return acc;
    }, {
      totalCampaigns: 0,
      activeCampaigns: 0,
      completedCampaigns: 0,
      totalSent: 0,
      totalOpened: 0,
      totalClicked: 0
    });

    return {
      ...metrics,
      avgOpenRate: metrics.totalSent > 0 ? ((metrics.totalOpened / metrics.totalSent) * 100).toFixed(1) : 0,
      avgClickRate: metrics.totalSent > 0 ? ((metrics.totalClicked / metrics.totalSent) * 100).toFixed(1) : 0
    };
  }, [campaigns]);

  const getTopPerformingCampaigns = useCallback((limit = 5) => {
    return [...campaigns]
      .filter(campaign => campaign.sent_count > 0)
      .sort((a, b) => {
        const aRate = (a.opened_count / a.sent_count) * 100;
        const bRate = (b.opened_count / b.sent_count) * 100;
        return bRate - aRate;
      })
      .slice(0, limit);
  }, [campaigns]);

  // Enhanced filtering
  const filteredCampaigns = useMemo(() => {
    let filtered = [...campaigns];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(campaign =>
        campaign.name?.toLowerCase().includes(term) ||
        campaign.description?.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(campaign => campaign.status === filters.status);
    }

    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(campaign => campaign.type === filters.type);
    }

    // Apply template filter
    if (filters.template !== 'all') {
      filtered = filtered.filter(campaign => campaign.template_id === filters.template);
    }

    // Apply sorting
    if (sortConfig.field) {
      filtered.sort((a, b) => {
        const aValue = a[sortConfig.field];
        const bValue = b[sortConfig.field];

        if (sortConfig.direction === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }

    // Limit results for performance
    if (maxDisplayCampaigns && filtered.length > maxDisplayCampaigns) {
      filtered = filtered.slice(0, maxDisplayCampaigns);
    }

    return filtered;
  }, [campaigns, searchTerm, filters, sortConfig, maxDisplayCampaigns]);

  const getStatusColor = useCallback((status) => {
    switch (status) {
      case 'running':
        return 'success';
      case 'scheduled':
        return 'info';
      case 'paused':
        return 'warning';
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'error';
      case 'draft':
        return 'secondary';
      default:
        return 'default';
    }
  }, []);

  const getStatusIcon = useCallback((status) => {
    switch (status) {
      case 'running':
        return <PlayIcon fontSize="small" />;
      case 'scheduled':
        return <ScheduleIcon fontSize="small" />;
      case 'paused':
        return <PauseIcon fontSize="small" />;
      case 'completed':
        return <TrendingUpIcon fontSize="small" />;
      case 'cancelled':
        return <StopIcon fontSize="small" />;
      default:
        return <EmailIcon fontSize="small" />;
    }
  }, []);

  const calculateOpenRate = useCallback((campaign) => {
    if (campaign.sent_count === 0) return 0;
    return ((campaign.opened_count / campaign.sent_count) * 100).toFixed(1);
  }, []);

  const calculateClickRate = useCallback((campaign) => {
    if (campaign.sent_count === 0) return 0;
    return ((campaign.clicked_count / campaign.sent_count) * 100).toFixed(1);
  }, []);

  // Additional utility functions
  const handleDuplicateCampaign = useCallback((campaignId) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    if (campaign && onCampaignCreate) {
      const duplicatedCampaign = {
        ...campaign,
        name: `${campaign.name} (Copy)`,
        status: 'draft',
        id: undefined
      };
      onCampaignCreate(duplicatedCampaign);
    }

    if (enableAccessibility) {
      announceToScreenReader('Campaign duplicated successfully');
    }
  }, [campaigns, onCampaignCreate, enableAccessibility, announceToScreenReader]);

  const handleScheduleCampaign = useCallback((campaignId, schedule) => {
    // Implementation would depend on the scheduling system
    if (enableAccessibility) {
      announceToScreenReader('Campaign scheduled successfully');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const generateCampaignReport = useCallback(() => {
    const metrics = generatePerformanceMetrics();
    const topPerformers = getTopPerformingCampaigns();

    return {
      summary: metrics,
      topPerformers,
      totalCampaigns: campaigns.length,
      generatedAt: new Date().toISOString()
    };
  }, [generatePerformanceMetrics, getTopPerformingCampaigns, campaigns.length]);

  const renderSkeleton = () => (
    <Box>
      {[...Array(5)].map((_, index) => (
        <Card key={index} sx={{ mb: 2 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box sx={{ flex: 1 }}>
                <Skeleton variant="text" width="40%" height={32} />
                <Skeleton variant="text" width="60%" height={20} sx={{ mt: 1 }} />
              </Box>
              <Skeleton variant="circular" width={32} height={32} />
            </Box>
            <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
              <Skeleton variant="rounded" width={80} height={24} />
              <Skeleton variant="rounded" width={100} height={24} />
            </Box>
          </CardContent>
        </Card>
      ))}
    </Box>
  );

  const renderEmptyState = () => (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        textAlign: 'center'
      }}
    >
      {/* Friendly illustration */}
      <Box
        sx={{
          fontSize: '4rem',
          mb: 2,
          opacity: 0.6,
          filter: 'grayscale(20%)'
        }}
      >
        📢
      </Box>

      <Typography variant="h6" color="text.secondary" gutterBottom>
        No email campaigns yet
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 400 }}>
        Start engaging with your audience by creating your first email campaign.
        You can schedule newsletters, announcements, and automated email sequences.
      </Typography>

      <Button
        variant="contained"
        startIcon={<AddIcon />}
        sx={{
          background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
          boxShadow: '0 8px 32px 0 rgba(108, 75, 250, 0.3)',
          '&:hover': {
            background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
            transform: 'translateY(-2px)',
            boxShadow: '0 12px 40px 0 rgba(108, 75, 250, 0.4)',
          }
        }}
      >
        Create Your First Campaign
      </Button>
    </Box>
  );

  const renderCampaignStats = () => (
    <Grid container spacing={3} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={6} md={3}>
        <Card variant="glass">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <EmailIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">Total Campaigns</Typography>
            </Box>
            <Typography variant="h4" color="primary">
              {campaigns.length}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card variant="glass">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PlayIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="h6">Active</Typography>
            </Box>
            <Typography variant="h4" color="success.main">
              {campaigns.filter(c => c.status === 'running').length}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card variant="glass">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <ScheduleIcon color="info" sx={{ mr: 1 }} />
              <Typography variant="h6">Scheduled</Typography>
            </Box>
            <Typography variant="h4" color="info.main">
              {campaigns.filter(c => c.status === 'scheduled').length}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <Card variant="glass">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TrendingUpIcon color="secondary" sx={{ mr: 1 }} />
              <Typography variant="h6">Completed</Typography>
            </Box>
            <Typography variant="h4" color="secondary.main">
              {campaigns.filter(c => c.status === 'completed').length}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  if (loading) {
    return renderSkeleton();
  }

  if (campaigns.length === 0) {
    return renderEmptyState();
  }

  return (
    <Box>
      {/* Campaign Stats */}
      {renderCampaignStats()}

      {/* Create Campaign Button */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Email Campaigns
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
            }
          }}
        >
          Create Campaign
        </Button>
      </Box>

      {/* Campaigns Table */}
      <TableContainer component={Paper} variant="glass" sx={{ borderRadius: 3 }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Campaign</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Template</TableCell>
              <TableCell align="right">Recipients</TableCell>
              <TableCell align="right">Sent</TableCell>
              <TableCell align="right">Open Rate</TableCell>
              <TableCell align="right">Click Rate</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {campaigns.map((campaign) => (
              <TableRow key={campaign.id} hover>
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" fontWeight={600}>
                      {campaign.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {campaign.description}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={getStatusIcon(campaign.status)}
                    label={campaign.status}
                    color={getStatusColor(campaign.status)}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {templates.find(t => t.id === campaign.template_id)?.name || 'Unknown'}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2">
                    {campaign.recipient_count.toLocaleString()}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Box>
                    <Typography variant="body2">
                      {campaign.sent_count.toLocaleString()}
                    </Typography>
                    {campaign.recipient_count > 0 && (
                      <LinearProgress
                        variant="determinate"
                        value={(campaign.sent_count / campaign.recipient_count) * 100}
                        sx={{ mt: 0.5, height: 4, borderRadius: 2 }}
                      />
                    )}
                  </Box>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2" color="success.main">
                    {calculateOpenRate(campaign)}%
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="body2" color="info.main">
                    {calculateClickRate(campaign)}%
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuOpen(e, campaign)}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            minWidth: 160,
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.2)',
          }
        }}
      >
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Campaign</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <AnalyticsIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Analytics</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <PlayIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Start Campaign</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <PauseIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Pause Campaign</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Campaign</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedCampaignManager.propTypes = {
  // Core props
  campaigns: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    status: PropTypes.oneOf(Object.values(CAMPAIGN_STATUSES)).isRequired,
    type: PropTypes.oneOf(Object.values(CAMPAIGN_TYPES)),
    template_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    recipient_count: PropTypes.number,
    sent_count: PropTypes.number,
    opened_count: PropTypes.number,
    clicked_count: PropTypes.number,
    created_at: PropTypes.string,
    scheduled_at: PropTypes.string
  })),
  templates: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    type: PropTypes.string,
    category: PropTypes.string
  })),
  loading: PropTypes.bool,
  error: PropTypes.string,

  // Enhanced props
  className: PropTypes.string,
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeTracking: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableBulkOperations: PropTypes.bool,
  enableTemplateManagement: PropTypes.bool,

  // Configuration
  defaultView: PropTypes.oneOf(Object.values(CAMPAIGN_VIEWS)),
  autoRefreshInterval: PropTypes.number,
  maxDisplayCampaigns: PropTypes.number,

  // Callback props
  onCampaignCreate: PropTypes.func,
  onCampaignEdit: PropTypes.func,
  onCampaignDelete: PropTypes.func,
  onCampaignStart: PropTypes.func,
  onCampaignPause: PropTypes.func,
  onCampaignStop: PropTypes.func,
  onTemplateSelect: PropTypes.func,
  onAnalyticsView: PropTypes.func,
  onDataExport: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onRefresh: PropTypes.func
};

// Default props
EnhancedCampaignManager.defaultProps = {
  campaigns: [],
  templates: [],
  loading: false,
  error: null,
  className: '',
  enableAdvancedFeatures: true,
  enableRealTimeTracking: true,
  enableAnalytics: true,
  enableAccessibility: true,
  enableBulkOperations: true,
  enableTemplateManagement: true,
  defaultView: CAMPAIGN_VIEWS.OVERVIEW,
  autoRefreshInterval: 300000,
  maxDisplayCampaigns: 1000,
  onCampaignCreate: null,
  onCampaignEdit: null,
  onCampaignDelete: null,
  onCampaignStart: null,
  onCampaignPause: null,
  onCampaignStop: null,
  onTemplateSelect: null,
  onAnalyticsView: null,
  onDataExport: null,
  onAnalyticsTrack: null,
  onRefresh: null
};

// Display name for debugging
EnhancedCampaignManager.displayName = 'EnhancedCampaignManager';

export default EnhancedCampaignManager;

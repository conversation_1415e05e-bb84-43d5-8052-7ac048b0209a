REM @since 2024-1-1 to 2025-25-7
@echo off
echo 🚀 Starting Aceo Tool Development Server...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo ⚠️  node_modules not found. Installing dependencies...
    npm install
    echo.
)

REM Check if cross-env is installed
npm list cross-env >nul 2>&1
if errorlevel 1 (
    echo ⚠️  cross-env not found. Installing...
    npm install cross-env rimraf --save-dev
    echo.
)

echo 📋 Available development modes:
echo.
echo 1. Standard Mode (4GB memory)
echo 2. High Memory Mode (8GB memory) - Recommended for ERR_INSUFFICIENT_RESOURCES
echo 3. Minimal Mode (2GB memory)
echo 4. Simple Mode (no memory optimization)
echo 5. Windows Native Mode (4GB memory)
echo.
set /p choice="Choose mode (1-5): "

if "%choice%"=="1" (
    echo Starting Standard Mode...
    npm run dev
) else if "%choice%"=="2" (
    echo Starting High Memory Mode...
    npm run dev:memory
) else if "%choice%"=="3" (
    echo Starting Minimal Mode...
    npm run dev:minimal
) else if "%choice%"=="4" (
    echo Starting Simple Mode...
    npm run dev:simple
) else if "%choice%"=="5" (
    echo Starting Windows Native Mode...
    npm run dev:windows
) else (
    echo Invalid choice. Starting High Memory Mode (recommended)...
    npm run dev:memory
)

echo.
echo 💡 If you encounter ERR_INSUFFICIENT_RESOURCES:
echo 1. Close other applications
echo 2. Try mode 3 (Minimal Mode)
echo 3. Restart your computer
echo.
pause

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { 
  Box, Typography, Paper, TextField, <PERSON><PERSON>, 
  <PERSON>per, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, 
  Divider, CircularProgress, Alert, alpha,
  useTheme
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useNotification } from '../../hooks/useNotification';

import VerifiedUserIcon from '@mui/icons-material/VerifiedUser';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import UpgradeIcon from '@mui/icons-material/Upgrade';
import api from '../../api';

/**
 * AppSumo Upgrade Page - Allows AppSumo customers to upgrade their tier
 * 
 * This component provides a step-by-step process for AppSumo users to upgrade
 * their lifetime subscription to a higher tier by entering an upgrade code.
 */
const Upgrade = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, refreshUser } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  

  
  // State
  const [code, setCode] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [verificationResult, setVerificationResult] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  
  const steps = ['Verify Upgrade Code', 'Confirm Upgrade', 'Complete'];
  
  // Check if user has an AppSumo subscription
  useEffect(() => {
    if (user && (!user.subscription || !user.subscription.is_appsumo_lifetime)) {
      showErrorNotification('You need an AppSumo subscription to access this page');
      navigate('/billing');
    }
  }, [user, navigate, showErrorNotification]);
  
  // Verify the AppSumo upgrade code
  const verifyCode = async () => {
    setVerifying(true);
    setError(null);
    
    try {
      const response = await api.post('/api/appsumo/verify-upgrade', {
        code: code,
        current_tier: user?.subscription?.appsumo_tier_id
      });
      
      setVerificationResult(response.data);
      
      if (response.data.is_valid) {
        setActiveStep(1);
      } else {
        setError(response.data.message || 'Invalid upgrade code');
      }
    } catch (err) {
      console.error('Error verifying AppSumo upgrade code:', err);
      setError(err.response?.data?.detail || 'Failed to verify upgrade code');
      showErrorNotification('Failed to verify AppSumo upgrade code');
    } finally {
      setVerifying(false);
    }
  };
  
  // Upgrade AppSumo tier
  const upgradeAppSumoTier = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await api.post('/api/appsumo/upgrade', {
        upgrade_code: code
      });
      
      if (response.data.success) {
        showSuccessNotification(response.data.message || 'Successfully upgraded your AppSumo tier!');
        setActiveStep(2);
        
        // Refresh user data to get updated subscription
        refreshUser();
      } else {
        setError(response.data.message || 'Failed to upgrade AppSumo tier');
      }
    } catch (err) {
      console.error('Error upgrading AppSumo tier:', err);
      setError(err.response?.data?.detail || 'Failed to upgrade AppSumo tier');
      showErrorNotification('Failed to upgrade AppSumo tier');
    } finally {
      setLoading(false);
    }
  };
  
  // Render step content based on active step
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return renderVerifyStep();
      case 1:
        return renderConfirmStep();
      case 2:
        return renderCompletionStep();
      default:
        return 'Unknown step';
    }
  };
  
  // Render the verify step
  const renderVerifyStep = () => {
    return (
      <Box>
        <Typography variant="body1" paragraph>
          Enter your AppSumo upgrade code to upgrade your current tier.
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
          <TextField
            fullWidth
            label="AppSumo Upgrade Code"
            value={code}
            onChange={(e) => setCode(e.target.value.toUpperCase())}
            disabled={verifying}
            placeholder="e.g., AS-B2BINF-UPGRADE-12345"
            sx={{ mr: 1 }}
          />
          <Button
            variant="contained"
            onClick={verifyCode}
            disabled={!code.trim() || verifying}
            sx={{ height: '56px', minWidth: '120px' }}
          >
            {verifying ? <CircularProgress size={24} /> : 'Verify'}
          </Button>
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
      </Box>
    );
  };
  
  // Render the confirm step
  const renderConfirmStep = () => {
    return (
      <Box>
        <Alert severity="info" sx={{ mb: 3 }}>
          Please review your upgrade details below and confirm to proceed.
        </Alert>
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Current Tier: {user?.subscription?.plan_name}
          </Typography>
          <Typography variant="subtitle1" gutterBottom>
            New Tier: {verificationResult?.tier?.name}
          </Typography>
          <Typography variant="body2" paragraph>
            {verificationResult?.tier?.description}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            onClick={() => setActiveStep(0)}
            disabled={loading}
          >
            Back
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={upgradeAppSumoTier}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <UpgradeIcon />}
          >
            Confirm Upgrade
          </Button>
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Box>
    );
  };
  
  // Render the completion step
  const renderCompletionStep = () => {
    return (
      <Box sx={{ textAlign: 'center' }}>
        <CheckCircleIcon color="success" sx={{ fontSize: 64, mb: 2 }} />
        
        <Typography variant="h5" gutterBottom>
          Upgrade Successful!
        </Typography>
        
        <Typography variant="body1" paragraph>
          Your AppSumo tier has been successfully upgraded to {verificationResult?.tier?.name}.
        </Typography>
        
        <Typography variant="body2" color="text.secondary" paragraph>
          You can now enjoy all the benefits of your new tier.
        </Typography>
        
        <Button
          variant="contained"
          onClick={() => navigate('/dashboard')}
          sx={{ mt: 2 }}
        >
          Go to Dashboard
        </Button>
      </Box>
    );
  };
  
  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Paper
        elevation={3}
        sx={{
          p: 4,
          borderRadius: 2,
          backdropFilter: 'blur(20px)',
          backgroundColor: alpha(theme.palette.background.paper, 0.8),
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <UpgradeIcon color="primary" sx={{ fontSize: 32, mr: 2 }} />
          <Typography variant="h4">
            Upgrade AppSumo Tier
          </Typography>
        </Box>
        
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
        
        {verificationResult?.is_valid && activeStep >= 1 && (
          <Card
            variant="outlined"
            sx={{
              mb: 3,
              backgroundColor: alpha(theme.palette.success.main, 0.05),
              border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <VerifiedUserIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" fontWeight="bold">
                  Valid Upgrade Code: {code}
                </Typography>
              </Box>
              
              <Divider sx={{ my: 1.5 }} />
              
              <Typography variant="body2">
                <strong>New Tier:</strong> {verificationResult.tier?.name}
              </Typography>
              
              <Typography variant="body2">
                <strong>Description:</strong> {verificationResult.tier?.description}
              </Typography>
            </CardContent>
          </Card>
        )}
        
        {getStepContent(activeStep)}
      </Paper>
    </Box>
  );
};

export default Upgrade;

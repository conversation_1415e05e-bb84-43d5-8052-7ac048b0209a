import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
} from '@mui/material';

import {
  Search as SearchIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Download as DownloadIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

import { format } from 'date-fns';
import api from '../../api';
import StablePageWrapper from '../StablePageWrapper';
import TicketCreateDialog from './TicketCreateDialog';
import BulkOperationsDialog from './BulkOperationsDialog';

const TicketManagementSimple = ({ onRefresh, dashboardData }) => {
  // Core state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [tickets, setTickets] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  
  // Dialog states
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [bulkDialogOpen, setBulkDialogOpen] = useState(false);
  
  // Other states
  const [agents, setAgents] = useState([]);
  const [selectedTickets, setSelectedTickets] = useState([]);
  const [bulkOperation, setBulkOperation] = useState('');
  const [bulkData, setBulkData] = useState({});

  // Filters
  const [filters, setFilters] = useState({
    query: '',
    status: '',
    priority: '',
    category: '',
    overdue_only: false,
  });

  useEffect(() => {
    loadTickets();
    loadAgents();
  }, [page, rowsPerPage]);

  const loadTickets = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: page + 1,
        page_size: rowsPerPage,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '' && value !== false)
        ),
      };

      const response = await api.get('/api/admin/support/tickets', { params });
      setTickets(Array.isArray(response.data) ? response.data : []);
      setTotalCount(parseInt(response.headers['x-total-count'] || '0'));
    } catch (err) {
      console.error('Error loading tickets:', err);
      setError(err.response?.data?.detail || 'Failed to load tickets');
      setTickets([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  const loadAgents = async () => {
    try {
      const response = await api.get('/api/admin/support/agents');
      setAgents(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error('Error loading agents:', error);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPage(0);
  };

  const handleSearch = () => {
    setPage(0);
    loadTickets();
  };

  const handleClearFilters = () => {
    setFilters({
      query: '',
      status: '',
      priority: '',
      category: '',
      overdue_only: false,
    });
    setPage(0);
    loadTickets();
  };

  const handleViewTicket = async (ticketId) => {
    try {
      const response = await api.get(`/api/admin/support/tickets/${ticketId}`);
      setSelectedTicket(response.data);
      setViewDialogOpen(true);
    } catch (err) {
      console.error('Error loading ticket details:', err);
      setError('Failed to load ticket details');
    }
  };

  const handleExportTickets = async (format = 'csv') => {
    try {
      setLoading(true);
      const params = { format, ...filters };
      const response = await api.get('/api/admin/support/tickets/export', {
        params,
        responseType: 'blob',
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `support_tickets_${new Date().toISOString().split('T')[0]}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      setSuccess('Tickets exported successfully');
    } catch (error) {
      console.error('Error exporting tickets:', error);
      setError('Failed to export tickets');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkOperation = async () => {
    try {
      setLoading(true);
      await api.post('/api/admin/support/tickets/bulk', {
        ticket_ids: selectedTickets,
        operation: bulkOperation,
        data: bulkData,
      });
      
      setSuccess(`Bulk operation completed for ${selectedTickets.length} tickets`);
      setBulkDialogOpen(false);
      setSelectedTickets([]);
      loadTickets();
      onRefresh?.();
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      setError('Failed to perform bulk operation');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const statusMap = {
      'open': 'info',
      'in_progress': 'primary',
      'pending_customer': 'warning',
      'resolved': 'success',
      'closed': 'default',
      'escalated': 'error',
    };
    return statusMap[status] || 'default';
  };

  const getPriorityColor = (priority) => {
    const priorityMap = {
      'critical': 'error',
      'high': 'warning',
      'medium': 'info',
      'low': 'default',
    };
    return priorityMap[priority] || 'default';
  };

  return (
    <StablePageWrapper 
      title="Support Ticket Management" 
      loading={loading && tickets.length === 0}
      error={error}
      onRetry={loadTickets}
    >
      <Box sx={{ p: 3 }}>
        {/* Success/Error Notifications */}
        <Snackbar
          open={!!success}
          autoHideDuration={6000}
          onClose={() => setSuccess(null)}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        >
          <Alert severity="success" onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        </Snackbar>

        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Support Tickets
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage customer support tickets with filtering and bulk operations
          </Typography>
        </Box>

        {/* Quick Actions */}
        <Box display="flex" gap={1} flexWrap="wrap" sx={{ mb: 3 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
            disabled={loading}
          >
            New Ticket
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => loadTickets()}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={() => handleExportTickets('csv')}
            disabled={loading}
          >
            Export CSV
          </Button>
        </Box>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardHeader title="Filters" />
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  size="small"
                  label="Search"
                  placeholder="Ticket #, subject, customer..."
                  value={filters.query}
                  onChange={(e) => handleFilterChange('query', e.target.value)}
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filters.status}
                    label="Status"
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="open">Open</MenuItem>
                    <MenuItem value="in_progress">In Progress</MenuItem>
                    <MenuItem value="pending_customer">Pending Customer</MenuItem>
                    <MenuItem value="resolved">Resolved</MenuItem>
                    <MenuItem value="closed">Closed</MenuItem>
                    <MenuItem value="escalated">Escalated</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Priority</InputLabel>
                  <Select
                    value={filters.priority}
                    label="Priority"
                    onChange={(e) => handleFilterChange('priority', e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="critical">Critical</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="low">Low</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={filters.category}
                    label="Category"
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="technical">Technical</MenuItem>
                    <MenuItem value="billing">Billing</MenuItem>
                    <MenuItem value="account">Account</MenuItem>
                    <MenuItem value="feature_request">Feature Request</MenuItem>
                    <MenuItem value="bug_report">Bug Report</MenuItem>
                    <MenuItem value="general">General</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Button
                  variant="contained"
                  onClick={handleSearch}
                  disabled={loading}
                  fullWidth
                >
                  Search
                </Button>
              </Grid>
            </Grid>
            
            <Box mt={2} display="flex" gap={1} alignItems="center">
              <Button
                size="small"
                variant={filters.overdue_only ? "contained" : "outlined"}
                color="error"
                onClick={() => handleFilterChange('overdue_only', !filters.overdue_only)}
              >
                Overdue Only
              </Button>
              {Object.values(filters).some(value => value !== '' && value !== false) && (
                <Button size="small" onClick={handleClearFilters}>
                  Clear Filters
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Tickets Table */}
        <Card>
          <CardHeader 
            title="Support Tickets"
            subheader={`${totalCount} total tickets`}
          />
          <CardContent sx={{ p: 0 }}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Ticket</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell>Subject</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                        <CircularProgress />
                      </TableCell>
                    </TableRow>
                  ) : tickets.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                        <Typography variant="body2" color="text.secondary">
                          No tickets found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    tickets.map((ticket) => (
                      <TableRow key={ticket.id} hover>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {ticket.ticket_number}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {ticket.customer_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {ticket.customer_email}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ maxWidth: 200 }}>
                            {ticket.subject}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={ticket.status.replace('_', ' ')}
                            color={getStatusColor(ticket.status)}
                            size="small"
                            sx={{ textTransform: 'capitalize' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={ticket.priority}
                            color={getPriorityColor(ticket.priority)}
                            size="small"
                            sx={{ textTransform: 'capitalize' }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                            {ticket.category?.replace('_', ' ') || 'General'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {format(new Date(ticket.created_at), 'MMM dd, yyyy')}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Tooltip title="View Details">
                            <IconButton 
                              size="small" 
                              onClick={() => handleViewTicket(ticket.id)}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
              rowsPerPageOptions={[10, 25, 50, 100]}
            />
          </CardContent>
        </Card>

        {/* Simple Ticket Details Dialog */}
        <Dialog 
          open={viewDialogOpen} 
          onClose={() => setViewDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Ticket Details: {selectedTicket?.ticket_number}
          </DialogTitle>
          <DialogContent>
            {selectedTicket && (
              <Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>Subject</Typography>
                    <Typography variant="body2" paragraph>{selectedTicket.subject}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>Status</Typography>
                    <Chip
                      label={selectedTicket.status.replace('_', ' ')}
                      color={getStatusColor(selectedTicket.status)}
                      size="small"
                      sx={{ textTransform: 'capitalize' }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>Description</Typography>
                    <Typography variant="body2" paragraph>{selectedTicket.description}</Typography>
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
            <Button variant="contained">Edit Ticket</Button>
          </DialogActions>
        </Dialog>

        {/* Create Ticket Dialog */}
        <TicketCreateDialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          onTicketCreated={(ticket) => {
            setSuccess('Ticket created successfully');
            loadTickets();
            onRefresh?.();
          }}
          agents={agents}
        />

        {/* Bulk Operations Dialog */}
        <BulkOperationsDialog
          open={bulkDialogOpen}
          onClose={() => setBulkDialogOpen(false)}
          selectedTickets={selectedTickets}
          agents={agents}
          onExecute={async (operation, data) => {
            setBulkOperation(operation);
            setBulkData(data);
            await handleBulkOperation();
          }}
        />

      </Box>
    </StablePageWrapper>
  );
};

export default TicketManagementSimple;

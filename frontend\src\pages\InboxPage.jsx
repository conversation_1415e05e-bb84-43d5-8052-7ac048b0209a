// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Container, Typography, Box, Paper, Button, Snackbar, Alert,
  Tooltip, Chip, Grid, IconButton, Collapse
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import {
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import EnhancedUnifiedInbox from '../components/messaging/EnhancedUnifiedInbox';
import PageHeader from '../components/common/PageHeader';
import api from '../api';

const InboxPage = () => {
  const theme = useTheme();
  const [syncStatus, setSyncStatus] = useState(null);
  const [showSyncDetails, setShowSyncDetails] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Fetch sync status on component mount
  useEffect(() => {
    fetchSyncStatus();

    // Set up interval to refresh sync status every minute
    const intervalId = setInterval(fetchSyncStatus, 60000);
   // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  // Function to fetch sync status
  const fetchSyncStatus = async () => {
    try {
      const response = await api.get('/api/messaging/social-media/sync/status');
      setSyncStatus(response.data);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching sync status:', error);
      }
      // Silently fail for sync status - it's not critical for inbox functionality
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Container maxWidth="xl">
      <PageHeader
        title="Unified Inbox"
        description="Manage all your social media conversations in one place with AI-assisted responses and automatic message synchronization"
      />

      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: theme.palette.mode === 'dark'
            ? 'rgba(66, 66, 66, 0.3)'
            : 'rgba(255, 255, 255, 0.5)',
          backdropFilter: 'blur(10px)',
          border: `1px solid ${theme.palette.mode === 'dark'
            ? 'rgba(255, 255, 255, 0.1)'
            : 'rgba(0, 0, 0, 0.1)'}`
        }}                                                    
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Social Media Messaging Hub
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Manage all your social media direct messages in one place. Connect your social media accounts,
            respond to customers without leaving the platform, and let our system automatically sync your messages
            based on platform-specific schedules optimized for each platform&apos;s API rate limits and your activity level.
            Our AI assistant analyzes conversations and suggests appropriate responses to help you communicate more effectively.
          </Typography>


          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
            <Button
              size="small"
              startIcon={showSyncDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              onClick={() => setShowSyncDetails(!showSyncDetails)}
              sx={{ mr: 1 }}
            >
              {showSyncDetails ? 'Hide Sync Details' : 'Show Sync Details'}
            </Button>

            <Tooltip title="Our system automatically syncs messages based on platform-specific schedules optimized for each platform&apos;s API limits and your activity level">
              <IconButton size="small">
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          <Collapse in={showSyncDetails}>
            <Paper
              elevation={0}
              sx={{
                mt: 2,
                p: 2,
                background: theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.03)',
                borderRadius: 1
              }}
            >
              <Typography variant="subtitle2" gutterBottom>
                Platform-Specific Sync Schedules
              </Typography>

              {syncStatus && syncStatus.platform_sync_schedules ? (
                <Grid container spacing={2}>
                  {syncStatus.platform_sync_schedules.map((platform, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Box sx={{
                        p: 1.5,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 1,
                        height: '100%'
                      }}>
                        <Typography variant="subtitle2" sx={{ mb: 1, textTransform: 'capitalize' }}>
                          {platform.platform}
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <Typography variant="body2" sx={{ mr: 1 }}>Activity Level:</Typography>
                          <Chip
                            size="small"
                            label={platform.activity_level}
                            color={
                              platform.activity_level === 'high' ? 'error' :
                              platform.activity_level === 'medium' ? 'warning' : 'success'
                            }
                            sx={{ textTransform: 'capitalize' }}
                          />
                        </Box>

                        <Typography variant="body2" sx={{ mb: 0.5 }}>
                          Sync Frequency: Every {platform.sync_frequency_minutes} minutes
                        </Typography>

                        {platform.minutes_until_next_sync !== null ? (
                          <Typography variant="body2">
                            Next Sync: {platform.minutes_until_next_sync} minutes from now
                          </Typography>
                        ) : (
                          <Typography variant="body2">
                            Next Sync: Scheduled after first sync
                          </Typography>
                        )}
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No sync information available. Connect social media accounts to see platform-specific sync schedules.
                </Typography>
              )}
            </Paper>
          </Collapse>
        </Box>

        <EnhancedUnifiedInbox />
      </Paper>


      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default InboxPage;

/**
 * Enhanced Token Manager Service Tests
 * 
 * Comprehensive test suite for the Enhanced Token Manager v2.0.0
 * with Enhanced Platform Service integration.
 * 
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @since 2024-01-01
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import tokenManager from '../tokenManager';
import { STORAGE_KEYS } from '../../config';

// Mock dependencies
vi.mock('../platformService', () => ({
  default: {
    on: vi.fn(),
    generatePlatformFingerprint: vi.fn().mockResolvedValue('mock-platform-fingerprint')
  }
}));

vi.mock('../fingerprint', () => ({
  getDeviceFingerprint: vi.fn().mockResolvedValue('mock-device-fingerprint')
}));

vi.mock('../../utils/PrometheusMetricsCollector', () => ({
  PrometheusMetricsCollector: vi.fn().mockImplementation(() => ({
    recordMessageSend: vi.fn()
  }))
}));

vi.mock('../../utils/SecureStorageService', () => ({
  secureStorage: {
    setItem: vi.fn().mockResolvedValue(true),
    getItem: vi.fn().mockResolvedValue(null),
    removeItem: vi.fn().mockResolvedValue(true)
  }
}));

vi.mock('../../api', () => ({
  default: {
    post: vi.fn()
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

// Mock global objects
global.localStorage = localStorageMock;
global.window = {
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  location: { pathname: '/dashboard', hostname: 'localhost' }
};

describe('Enhanced Token Manager v2.0.0', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('Initialization', () => {
    test('should initialize successfully with enhanced services', async () => {
      const status = await tokenManager.initialize();
      
      expect(status).toBeDefined();
      expect(status.initialized).toBe(true);
      expect(status.services).toBeDefined();
      expect(status.security).toBeDefined();
      expect(status.performance).toBeDefined();
    });

    test('should handle initialization failure gracefully', async () => {
      // Mock a service failure
      const originalConsoleError = console.error;
      console.error = vi.fn();
      
      // This should not throw but fall back to basic functionality
      try {
        await tokenManager.initialize();
      } catch (error) {
        // Should handle gracefully
      }
      
      console.error = originalConsoleError;
    });
  });

  describe('Token Management', () => {
    beforeEach(async () => {
      await tokenManager.initialize();
    });

    test('should set token with enhanced security', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.Lp-38RNpyBo3Oe0FdYhNdkJf6lGzlHr8A7e8dG7KqJc';
      
      const result = await tokenManager.setToken(mockToken);
      expect(result).toBe(true);
    });

    test('should get token with security validation', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.Lp-38RNpyBo3Oe0FdYhNdkJf6lGzlHr8A7e8dG7KqJc';
      
      // Mock secure storage to return token
      const { secureStorage } = await import('../../utils/SecureStorageService');
      secureStorage.getItem.mockResolvedValue(mockToken);
      
      const token = await tokenManager.getToken();
      expect(token).toBe(mockToken);
    });

    test('should clear token from all storage mechanisms', async () => {
      const result = await tokenManager.clearToken();
      expect(result).toBe(true);
    });

    test('should validate token with enhanced checks', async () => {
      const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.Lp-38RNpyBo3Oe0FdYhNdkJf6lGzlHr8A7e8dG7KqJc';
      
      // Mock secure storage to return token
      const { secureStorage } = await import('../../utils/SecureStorageService');
      secureStorage.getItem.mockResolvedValue(mockToken);
      
      const isValid = await tokenManager.isTokenValid();
      expect(typeof isValid).toBe('boolean');
    });
  });

  describe('Token Refresh', () => {
    beforeEach(async () => {
      await tokenManager.initialize();
    });

    test('should refresh token with retry logic', async () => {
      const mockRefreshToken = 'refresh_token_123';
      const mockNewToken = 'new_access_token_456';
      
      // Mock secure storage
      const { secureStorage } = await import('../../utils/SecureStorageService');
      secureStorage.getItem.mockResolvedValue(mockRefreshToken);
      
      // Mock API response
      const api = await import('../../api');
      api.default.post.mockResolvedValue({
        data: {
          access_token: mockNewToken,
          refresh_token: 'new_refresh_token_789'
        }
      });
      
      const newToken = await tokenManager.refreshToken();
      expect(newToken).toBe(mockNewToken);
    });

    test('should handle refresh failure with circuit breaker', async () => {
      // Mock API failure
      const api = await import('../../api');
      api.default.post.mockRejectedValue(new Error('Network error'));
      
      // Mock secure storage to return refresh token
      const { secureStorage } = await import('../../utils/SecureStorageService');
      secureStorage.getItem.mockResolvedValue('refresh_token_123');
      
      try {
        await tokenManager.refreshToken();
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('Security Features', () => {
    beforeEach(async () => {
      await tokenManager.initialize();
    });

    test('should validate security context', async () => {
      const status = tokenManager.getServiceStatus();
      expect(status.security).toBeDefined();
      expect(status.security.fingerprintingEnabled).toBeDefined();
    });

    test('should handle cross-tab synchronization', async () => {
      const mockEvent = {
        key: STORAGE_KEYS.AUTH_TOKEN,
        newValue: 'new_token_value'
      };
      
      // This should not throw
      await tokenManager.handleStorageEvent(mockEvent);
    });
  });

  describe('Performance Monitoring', () => {
    beforeEach(async () => {
      await tokenManager.initialize();
    });

    test('should track performance metrics', async () => {
      const status = tokenManager.getServiceStatus();
      expect(status.performance).toBeDefined();
      expect(status.performance.refreshAttempts).toBeDefined();
      expect(status.performance.refreshSuccesses).toBeDefined();
      expect(status.performance.refreshFailures).toBeDefined();
    });

    test('should provide service status', () => {
      const status = tokenManager.getServiceStatus();
      expect(status.initialized).toBeDefined();
      expect(status.config).toBeDefined();
      expect(status.circuitBreaker).toBeDefined();
      expect(status.performance).toBeDefined();
      expect(status.security).toBeDefined();
      expect(status.services).toBeDefined();
    });
  });

  describe('Legacy Compatibility', () => {
    test('should maintain backward compatibility with legacy methods', async () => {
      const legacyInterface = await tokenManager.initTokenManager();
      
      expect(legacyInterface.getToken).toBeDefined();
      expect(legacyInterface.setToken).toBeDefined();
      expect(legacyInterface.clearToken).toBeDefined();
      expect(legacyInterface.refreshToken).toBeDefined();
      expect(legacyInterface.isTokenValid).toBeDefined();
      expect(legacyInterface.getTokenExpiry).toBeDefined();
      expect(legacyInterface.getTokenPayload).toBeDefined();
      expect(legacyInterface.isAuthenticated).toBeDefined();
    });
  });

  describe('Cleanup', () => {
    test('should cleanup resources properly', () => {
      // This should not throw
      tokenManager.cleanup();
    });
  });
});

"""
E-commerce Integration Schemas.
Pydantic models for e-commerce API requests and responses.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator
from enum import Enum


# =============================================================================
# BASIC E-COMMERCE SCHEMAS
# =============================================================================

class PlatformType(str, Enum):
    """Supported e-commerce platforms."""
    SHOPIFY = "shopify"
    WOOCOMMERCE = "woocommerce"
    MAGENTO = "magento"
    BIGCOMMERCE = "bigcommerce"


class SyncStatus(str, Enum):
    """Product sync status."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL = "partial"


class StoreConnectionRequest(BaseModel):
    """Request schema for connecting a store."""
    platform: PlatformType
    store_url: str = Field(..., description="Store URL")
    api_key: str = Field(..., description="API key or access token")
    api_secret: Optional[str] = Field(None, description="API secret (if required)")
    store_name: Optional[str] = Field(None, description="Custom store name")


class ProductSyncRequest(BaseModel):
    """Request schema for product sync."""
    store_ids: Optional[List[str]] = Field(None, description="Specific store IDs to sync")
    force_full_sync: bool = Field(default=False, description="Force full sync instead of incremental")
    sync_images: bool = Field(default=True, description="Include product images in sync")
    sync_variants: bool = Field(default=True, description="Include product variants in sync")


class BulkProductRequest(BaseModel):
    """Request schema for bulk product operations."""
    product_ids: List[str] = Field(..., description="List of product IDs")
    operation: str = Field(..., description="Operation to perform")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Operation parameters")


class WebhookData(BaseModel):
    """Generic webhook data schema."""
    platform: str
    event_type: str
    data: Dict[str, Any]
    timestamp: Optional[datetime] = None


class WebhookResponse(BaseModel):
    """Response schema for webhook processing."""
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None


# =============================================================================
# INVENTORY TRACKING SCHEMAS
# =============================================================================

class InventoryUpdateRequest(BaseModel):
    """Request schema for inventory updates."""
    store_id: str = Field(..., description="Store ID")
    new_quantity: int = Field(..., ge=0, description="New inventory quantity")
    reason: str = Field(default="manual", max_length=500, description="Reason for inventory change")
    location: Optional[str] = Field(None, description="Warehouse/location")
    batch_id: Optional[str] = Field(None, description="Batch ID for bulk operations")
    variant_id: Optional[str] = Field(None, description="Product variant ID")
    reference_id: Optional[str] = Field(None, description="External reference ID")


class InventoryAlertRequest(BaseModel):
    """Request schema for creating inventory alerts."""
    store_id: str = Field(..., description="Store ID")
    product_id: str = Field(..., description="Product ID")
    alert_type: str = Field(..., description="Alert type (low_stock, out_of_stock, high_demand)")
    threshold: int = Field(..., ge=0, description="Inventory threshold for alert")
    variant_id: Optional[str] = Field(None, description="Product variant ID")
    email_enabled: bool = Field(default=True, description="Send email notifications")
    webhook_enabled: bool = Field(default=False, description="Send webhook notifications")
    webhook_url: Optional[str] = Field(None, description="Webhook URL for notifications")


class InventoryHistoryItem(BaseModel):
    """Schema for inventory history item."""
    id: str
    product_id: str
    variant_id: Optional[str] = None
    change_type: str
    previous_quantity: int
    new_quantity: int
    change_amount: int
    reason: Optional[str] = None
    reference_id: Optional[str] = None
    changed_by: Optional[str] = None
    external_sync: bool
    created_at: datetime
    product_details: Optional[Dict[str, Any]] = None


class InventoryHistoryResponse(BaseModel):
    """Response schema for inventory history."""
    success: bool
    history: List[InventoryHistoryItem]
    total_count: int
    limit: int
    offset: int
    has_more: bool


class InventorySnapshotResponse(BaseModel):
    """Response schema for inventory snapshot."""
    success: bool
    snapshot_id: str
    snapshot: Dict[str, Any]


class InventoryStatsResponse(BaseModel):
    """Response schema for inventory statistics."""
    success: bool
    websocket_stats: Dict[str, Any]
    store_id: str
    timestamp: str


# =============================================================================
# MULTI-CURRENCY SCHEMAS
# =============================================================================

class CurrencyConversionRequest(BaseModel):
    """Request schema for currency conversion."""
    from_currency: str = Field(..., min_length=3, max_length=3, description="Source currency code")
    to_currency: str = Field(..., min_length=3, max_length=3, description="Target currency code")
    amount: Decimal = Field(..., gt=0, description="Amount to convert")


class CurrencyConversionResponse(BaseModel):
    """Response schema for currency conversion."""
    success: bool
    from_currency: str
    to_currency: str
    original_amount: Decimal
    converted_amount: Decimal
    exchange_rate: Decimal
    conversion_date: datetime


class CurrencyPreferencesRequest(BaseModel):
    """Request schema for currency preferences."""
    default_currency: str = Field(..., min_length=3, max_length=3, description="Default currency code")
    display_currencies: List[str] = Field(..., description="List of currencies to display")
    auto_convert: bool = Field(default=True, description="Automatically convert prices")


# =============================================================================
# PRODUCT VARIANTS SCHEMAS
# =============================================================================

class ProductVariantOption(BaseModel):
    """Schema for product variant option."""
    name: str = Field(..., description="Option name (e.g., 'Color', 'Size')")
    values: List[str] = Field(..., description="Option values (e.g., ['Red', 'Blue'])")


class ProductVariantRequest(BaseModel):
    """Request schema for creating product variants."""
    product_id: str = Field(..., description="Product ID")
    options: List[ProductVariantOption] = Field(..., description="Variant options")
    variants: List[Dict[str, Any]] = Field(..., description="Variant combinations")


class ProductVariantResponse(BaseModel):
    """Response schema for product variants."""
    success: bool
    product_id: str
    variants_created: int
    variants: List[Dict[str, Any]]


# =============================================================================
# BULK IMPORT/EXPORT SCHEMAS
# =============================================================================

class BulkImportRequest(BaseModel):
    """Request schema for bulk import."""
    file_type: str = Field(..., description="File type (csv, xlsx)")
    store_id: str = Field(..., description="Target store ID")
    import_type: str = Field(..., description="Import type (products, inventory, variants)")
    mapping: Dict[str, str] = Field(..., description="Field mapping configuration")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")


class BulkImportResponse(BaseModel):
    """Response schema for bulk import."""
    success: bool
    job_id: str
    total_records: int
    processed_records: int
    failed_records: int
    errors: List[Dict[str, Any]]
    status: str


class BulkExportRequest(BaseModel):
    """Request schema for bulk export."""
    store_id: str = Field(..., description="Source store ID")
    export_type: str = Field(..., description="Export type (products, inventory, variants)")
    format: str = Field(default="csv", description="Export format (csv, xlsx)")
    filters: Optional[Dict[str, Any]] = Field(None, description="Export filters")
    fields: Optional[List[str]] = Field(None, description="Specific fields to export")


class BulkExportResponse(BaseModel):
    """Response schema for bulk export."""
    success: bool
    job_id: str
    download_url: str
    expires_at: datetime
    total_records: int


# =============================================================================
# SEARCH AND FILTERING SCHEMAS
# =============================================================================

class ProductSearchRequest(BaseModel):
    """Request schema for product search."""
    query: str = Field(..., min_length=1, description="Search query")
    store_ids: Optional[List[str]] = Field(None, description="Store IDs to search")
    categories: Optional[List[str]] = Field(None, description="Category filters")
    price_range: Optional[Dict[str, Decimal]] = Field(None, description="Price range filter")
    in_stock_only: bool = Field(default=False, description="Only show in-stock products")
    sort_by: str = Field(default="relevance", description="Sort criteria")
    limit: int = Field(default=20, ge=1, le=100, description="Number of results")
    offset: int = Field(default=0, ge=0, description="Results offset")


class ProductSearchResponse(BaseModel):
    """Response schema for product search."""
    success: bool
    query: str
    results: List[Dict[str, Any]]
    total_count: int
    limit: int
    offset: int
    has_more: bool
    search_time_ms: int


class ProductFilterPreset(BaseModel):
    """Schema for saved product filter preset."""
    name: str = Field(..., description="Preset name")
    filters: Dict[str, Any] = Field(..., description="Filter configuration")
    is_default: bool = Field(default=False, description="Whether this is the default preset")


# =============================================================================
# PRODUCT COMPARISON SCHEMAS
# =============================================================================

class ProductComparisonRequest(BaseModel):
    """Request schema for product comparison."""
    product_ids: List[str] = Field(..., min_length=2, max_length=10, description="Products to compare")
    comparison_criteria: List[str] = Field(..., description="Criteria to compare")
    include_variants: bool = Field(default=False, description="Include variant comparison")


class ProductComparisonResponse(BaseModel):
    """Response schema for product comparison."""
    success: bool
    products: List[Dict[str, Any]]
    comparison_matrix: Dict[str, Dict[str, Any]]
    recommendations: List[Dict[str, Any]]


# =============================================================================
# PAGINATION AND COMMON RESPONSES
# =============================================================================

class PaginationInfo(BaseModel):
    """Pagination information."""
    page: int
    limit: int
    total: int
    pages: int
    has_next: bool
    has_prev: bool


class EnhancedProductListResponse(BaseModel):
    """Enhanced response for product listings."""
    products: List[Dict[str, Any]]
    pagination: PaginationInfo
    filters_applied: Dict[str, Any]
    total_inventory_value: Optional[Decimal] = None
    category_breakdown: Optional[Dict[str, int]] = None


# =============================================================================
# AI CONTENT GENERATION SCHEMAS
# =============================================================================

class AIContentRequest(BaseModel):
    """Request schema for AI content generation."""
    product_id: str = Field(..., description="Product ID")
    store_id: str = Field(..., description="Store ID")
    platforms: List[str] = Field(..., description="Target platforms")
    content_type: str = Field(..., description="Content type")
    tone: str = Field(..., description="Content tone")
    target_audience: str = Field(..., description="Target audience")
    include_hashtags: bool = Field(default=True, description="Include hashtags")
    include_cta: bool = Field(default=True, description="Include call-to-action")
    custom_prompt: Optional[str] = Field(None, description="Custom prompt")
    template_id: Optional[str] = Field(None, description="Template ID")


class AIContentResponse(BaseModel):
    """Response schema for AI content generation."""
    success: bool
    product_id: str
    platforms: List[str]
    generated_contents: List[Dict[str, Any]]
    total_generated: int


# =============================================================================
# INVENTORY STATUS SCHEMAS
# =============================================================================

class InventoryStatusResponse(BaseModel):
    """Response schema for inventory status."""
    success: bool
    user_id: str
    stores: List[Dict[str, Any]]
    active_alerts: List[Dict[str, Any]]
    summary: Dict[str, Any]
    last_updated: str


# Duplicate schemas removed - using the first definitions above




// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Divider,
  Alert,
  AlertTitle,
  Card,
  CardContent,
  Grid,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  alpha,
  Tooltip,
} from '@mui/material';
import {
  CreditCard as CreditCardIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Refresh as RefreshIcon,
  OpenInNew as OpenInNewIcon,
} from '@mui/icons-material';

import api from '../../api';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../hooks/useNotification';
import { useNavigate } from 'react-router-dom';
import CustomerPortalButton from '../../components/billing/CustomerPortalButton';

// Main Payment Methods Component
const PaymentMethods = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch payment methods from API with enhanced error handling
  const fetchPaymentMethods = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      setError('Authentication required to view payment methods');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await api.get('/api/billing/payment-methods');
      setPaymentMethods(response.data || []);

      // Only show success message on manual refresh, not initial load
      if (refreshing) {
        showSuccessNotification(`Loaded ${response.data?.length || 0} payment methods`);
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);

      if (error.response?.status === 401) {
        setError('Session expired. Please log in again.');
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        setError('You do not have permission to view payment methods.');
        showErrorNotification('Access denied: You do not have permission to view payment methods.');
      } else {
        setError('Failed to load payment methods. Please try again.');
        showErrorNotification('Failed to load payment methods. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user?.id, showSuccessNotification, showErrorNotification, refreshing, navigate]);

  // Refresh payment methods
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchPaymentMethods();
    setRefreshing(false);
  }, [fetchPaymentMethods]);

  // Delete payment method with enhanced error handling
  const handleDeletePaymentMethod = useCallback(async (methodId) => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to delete payment methods');
      navigate('/login');
      return;
    }

    // Find the payment method for better user feedback
    const method = paymentMethods.find(m => m.id === methodId);
    const methodName = method ? `•••• •••• •••• ${method.last4}` : 'this payment method';

    if (!window.confirm(`Are you sure you want to delete ${methodName}? This action cannot be undone.`)) {
      return;
    }

    try {
      setLoading(true);
      await api.delete(`/api/billing/payment-methods/${methodId}`);
      setPaymentMethods(prev => prev.filter(method => method.id !== methodId));
      showSuccessNotification(`Payment method ${methodName} deleted successfully`);
    } catch (error) {
      console.error('Error deleting payment method:', error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to delete payment methods.');
      } else if (error.response?.status === 404) {
        showErrorNotification('Payment method not found. It may have already been deleted.');
      } else {
        showErrorNotification('Failed to delete payment method. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  }, [showSuccessNotification, showErrorNotification, isAuthenticated, navigate, paymentMethods]);

  // Set default payment method with enhanced error handling
  const handleSetDefault = useCallback(async (methodId) => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to set default payment method');
      navigate('/login');
      return;
    }

    // Find the payment method for better user feedback
    const method = paymentMethods.find(m => m.id === methodId);
    const methodName = method ? `•••• •••• •••• ${method.last4}` : 'payment method';

    try {
      setLoading(true);
      await api.post(`/api/billing/payment-methods/${methodId}/set-default`);
      setPaymentMethods(prev => prev.map(method => ({
        ...method,
        isDefault: method.id === methodId
      })));
      showSuccessNotification(`${methodName} set as default payment method`);
    } catch (error) {
      console.error('Error setting default payment method:', error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to set default payment methods.');
      } else if (error.response?.status === 404) {
        showErrorNotification('Payment method not found. Please refresh and try again.');
      } else {
        showErrorNotification('Failed to update default payment method. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  }, [showSuccessNotification, showErrorNotification, isAuthenticated, navigate, paymentMethods]);

  // Authentication check with redirect
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      showErrorNotification('Authentication required to access payment methods');
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate, showErrorNotification]);

  // Load payment methods on mount
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      fetchPaymentMethods();
    }
  }, [fetchPaymentMethods, isAuthenticated, authLoading]);

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Box>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }
  return (
    <Box sx={{ p: 3 }}>
      {/* Enhanced header with user context and actions */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
        p: 2,
        backgroundColor: alpha(theme.palette.primary.main, 0.05),
        borderRadius: 1
      }}>
        <Box>
          <Typography variant="h5" gutterBottom>
            Payment Methods
            {user?.name && (
              <Typography component="span" variant="h6" color="text.secondary" sx={{ ml: 2 }}>
                - {user.name}
              </Typography>
            )}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage payment methods for {user?.email || 'your account'}
            {user?.subscription?.plan_name && ` • ${user.subscription.plan_name} Plan`}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Refresh payment methods">
            <IconButton
              onClick={handleRefresh}
              disabled={loading || refreshing}
              color="primary"
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setAddDialogOpen(true)}
            disabled={loading}
          >
            Add Payment Method
          </Button>
        </Box>
      </Box>

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Payment methods grid */}
      {paymentMethods.length > 0 && (
        <>
          <Typography variant="h6" gutterBottom sx={{ mt: 3, mb: 2 }}>
            Your Payment Methods ({paymentMethods.length})
          </Typography>

          <Grid container spacing={3} sx={{ mb: 3 }}>
            {paymentMethods.map((method) => (
              <Grid item xs={12} md={6} key={method.id}>
                <Card sx={{
                  position: 'relative',
                  border: method.isDefault ? `2px solid ${theme.palette.primary.main}` : '1px solid',
                  borderColor: method.isDefault ? 'primary.main' : 'divider'
                }}>
                  {method.isDefault && (
                    <Box sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 0.5
                    }}>
                      <CheckCircleIcon color="primary" fontSize="small" />
                      <Typography variant="caption" color="primary">
                        Default
                      </Typography>
                    </Box>
                  )}

                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <CreditCardIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="h6">
                        •••• •••• •••• {method.last4 || '****'}
                      </Typography>
                    </Box>

                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {method.brand?.toUpperCase() || 'CARD'} • Expires {method.expMonth || '**'}/{method.expYear || '****'}
                    </Typography>

                    <Divider sx={{ my: 2 }} />

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        {!method.isDefault && (
                          <Button
                            size="small"
                            onClick={() => handleSetDefault(method.id)}
                            disabled={loading}
                          >
                            Set as Default
                          </Button>
                        )}
                      </Box>

                      <Tooltip title="Delete payment method">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeletePaymentMethod(method.id)}
                          disabled={loading || method.isDefault}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Divider sx={{ my: 3 }} />
        </>
      )}

      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <CreditCardIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Manage Your Payment Methods
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Your payment methods are securely managed through Lemon Squeezy.
            Click the button below to access your customer portal.
          </Typography>

          <CustomerPortalButton
            size="large"
            startIcon={<OpenInNewIcon />}
            tooltipText="Opens in a new window"
          />
        </Box>

        <Alert severity="info">
          <AlertTitle>Secure Payment Processing</AlertTitle>
          We use Lemon Squeezy for secure payment processing. Your payment information
          is never stored on our servers and is handled according to the highest security standards.
        </Alert>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Add Payment Method Dialog */}
      <AddPaymentMethodDialog
        open={addDialogOpen}
        onClose={() => setAddDialogOpen(false)}
      />
    </Box>
  );

};

// Add Payment Method Dialog with enhanced user experience
const AddPaymentMethodDialog = ({ open, onClose }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CreditCardIcon />
          Manage Payment Methods
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ textAlign: 'center', py: 2 }}>
          <Typography variant="body1" paragraph>
            Payment methods are securely managed through our payment provider, Lemon Squeezy.
            You can add, remove, and update your payment methods in the customer portal.
          </Typography>

          <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
            <AlertTitle>Secure & Safe</AlertTitle>
            Your payment information is encrypted and never stored on our servers.
            All transactions are processed securely through Lemon Squeezy.
          </Alert>

          <CustomerPortalButton
            label="Open Customer Portal"
            startIcon={<OpenInNewIcon />}
            size="large"
            sx={{ mt: 2 }}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};
export default PaymentMethods;

/**
 * Team Context
 * Production-ready team management with comprehensive error handling and monitoring
 * Advanced team collaboration features with real-time updates and validation
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useSnackbar } from './SnackbarContext';
import api from '../api';

// Configuration constants
const CONFIG = {
  // Validation settings
  MIN_TEAM_NAME_LENGTH: 3,
  MAX_TEAM_NAME_LENGTH: 50,
  MAX_TEAM_DESCRIPTION_LENGTH: 500,
  MAX_TEAMS_PER_USER: 10,

  // Cache settings
  CACHE_DURATION: 300000, // 5 minutes

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[TeamContext] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[TeamContext] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Team Context Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[TeamContext] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Team Context Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[TeamContext] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Team Context Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const TeamContext = createContext();

// Provider component
export const TeamProvider = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const { showSnackbar } = useSnackbar();

  const [teams, setTeams] = useState([]);
  const [currentTeam, setCurrentTeam] = useState(null);
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch user's teams
  const fetchTeams = useCallback(async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.get('/teams');
      setTeams(response.data.teams || []);

      // Set current team to the first team if not already set
      if (!currentTeam && response.data.teams && response.data.teams.length > 0) {
        setCurrentTeam(response.data.teams[0]);
      }
    } catch (err) {
      logger.error('Error fetching teams', err);
      setError('Failed to fetch teams');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, currentTeam]);

  // Fetch team by ID
  const fetchTeam = useCallback(async (teamId) => {
    if (!isAuthenticated || !teamId) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await api.get(`/teams/${teamId}`);
      return response.data;
    } catch (err) {
      logger.error(`Error fetching team ${teamId}`, err);
      setError('Failed to fetch team details');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Validation helper
  const validateTeamData = useCallback((teamData) => {
    const errors = [];

    if (!teamData.name || typeof teamData.name !== 'string') {
      errors.push('Team name is required');
    } else if (teamData.name.length < CONFIG.MIN_TEAM_NAME_LENGTH) {
      errors.push(`Team name must be at least ${CONFIG.MIN_TEAM_NAME_LENGTH} characters`);
    } else if (teamData.name.length > CONFIG.MAX_TEAM_NAME_LENGTH) {
      errors.push(`Team name must be less than ${CONFIG.MAX_TEAM_NAME_LENGTH} characters`);
    }

    if (teamData.description && teamData.description.length > CONFIG.MAX_TEAM_DESCRIPTION_LENGTH) {
      errors.push(`Team description must be less than ${CONFIG.MAX_TEAM_DESCRIPTION_LENGTH} characters`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, []);

  // Create a new team
  const createTeam = useCallback(async (teamData) => {
    if (!isAuthenticated) {
      logger.warn('Cannot create team - user not authenticated');
      return null;
    }

    // Validate team data
    const validation = validateTeamData(teamData);
    if (!validation.isValid) {
      const errorMessage = validation.errors.join(', ');
      logger.warn('Team validation failed', { errors: validation.errors });
      setError(errorMessage);
      showSnackbar(errorMessage, 'error');
      return null;
    }

    // Check team limit
    if (teams.length >= CONFIG.MAX_TEAMS_PER_USER) {
      const errorMessage = `Maximum ${CONFIG.MAX_TEAMS_PER_USER} teams allowed`;
      logger.warn('Team limit exceeded', { currentCount: teams.length, limit: CONFIG.MAX_TEAMS_PER_USER });
      setError(errorMessage);
      showSnackbar(errorMessage, 'error');
      return null;
    }

    setLoading(true);
    setError(null);
    logger.debug('Creating team', { teamData });

    try {
      const response = await api.post('/teams', teamData);

      // Update teams list
      setTeams(prevTeams => [...prevTeams, response.data]);

      // Set as current team
      setCurrentTeam(response.data);

      logger.info('Team created successfully', { teamId: response.data.id, teamName: response.data.name });
      showSnackbar('Team created successfully', 'success');
      return response.data;
    } catch (err) {
      logger.error('Error creating team', err);
      setError('Failed to create team');
      showSnackbar(err.response?.data?.detail || 'Failed to create team', 'error');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, showSnackbar, validateTeamData, teams.length]);

  // Update a team
  const updateTeam = useCallback(async (teamId, teamData) => {
    if (!isAuthenticated || !teamId) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await api.put(`/teams/${teamId}`, teamData);

      // Update teams list
      setTeams(prevTeams =>
        prevTeams.map(team =>
          team.id === teamId ? response.data : team
        )
      );

      // Update current team if it's the one being updated
      if (currentTeam && currentTeam.id === teamId) {
        setCurrentTeam(response.data);
      }

      showSnackbar('Team updated successfully', 'success');
      return response.data;
    } catch (err) {
      logger.error(`Error updating team ${teamId}`, err);
      setError('Failed to update team');
      showSnackbar(err.response?.data?.detail || 'Failed to update team', 'error');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, currentTeam, showSnackbar]);

  // Delete a team
  const deleteTeam = useCallback(async (teamId) => {
    if (!isAuthenticated || !teamId) return false;

    setLoading(true);
    setError(null);

    try {
      await api.delete(`/teams/${teamId}`);

      // Update teams list
      setTeams(prevTeams => prevTeams.filter(team => team.id !== teamId));

      // Update current team if it's the one being deleted
      if (currentTeam && currentTeam.id === teamId) {
        setCurrentTeam(teams.length > 1 ? teams.find(team => team.id !== teamId) : null);
      }

      showSnackbar('Team deleted successfully', 'success');
      return true;
    } catch (err) {
      logger.error(`Error deleting team ${teamId}`, err);
      setError('Failed to delete team');
      showSnackbar(err.response?.data?.detail || 'Failed to delete team', 'error');
      return false;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, currentTeam, teams, showSnackbar]);

  // Invite a user to a team
  const inviteToTeam = useCallback(async (teamId, invitationData) => {
    if (!isAuthenticated || !teamId) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await api.post(`/teams/${teamId}/invite`, invitationData);
      showSnackbar('Invitation sent successfully', 'success');
      return response.data;
    } catch (err) {
      logger.error(`Error inviting to team ${teamId}`, err);
      setError('Failed to send invitation');
      showSnackbar(err.response?.data?.detail || 'Failed to send invitation', 'error');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, showSnackbar]);

  // Fetch user's invitations
  const fetchMyInvitations = useCallback(async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await api.get('/teams/invitations/me');
      setInvitations(response.data.invitations || []);
    } catch (err) {
      logger.error('Error fetching invitations', err);
      setError('Failed to fetch invitations');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // Accept an invitation
  const acceptInvitation = useCallback(async (token) => {
    if (!isAuthenticated || !token) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await api.post('/teams/accept-invitation', { token });

      // Update teams list
      await fetchTeams();

      // Update invitations list
      await fetchMyInvitations();

      showSnackbar('Invitation accepted successfully', 'success');
      return response.data;
    } catch (err) {
      logger.error('Error accepting invitation', err);
      setError('Failed to accept invitation');
      showSnackbar(err.response?.data?.detail || 'Failed to accept invitation', 'error');
      return null;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, fetchTeams, fetchMyInvitations, showSnackbar]);

  // Reject an invitation
  const rejectInvitation = useCallback(async (token) => {
    if (!isAuthenticated || !token) return false;

    setLoading(true);
    setError(null);

    try {
      await api.post('/teams/reject-invitation', { token });

      // Update invitations list
      await fetchMyInvitations();

      showSnackbar('Invitation rejected', 'success');
      return true;
    } catch (err) {
      logger.error('Error rejecting invitation', err);
      setError('Failed to reject invitation');
      showSnackbar(err.response?.data?.detail || 'Failed to reject invitation', 'error');
      return false;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, fetchMyInvitations, showSnackbar]);

  // Remove a team member
  const removeTeamMember = useCallback(async (teamId, memberId) => {
    if (!isAuthenticated || !teamId || !memberId) return false;

    setLoading(true);
    setError(null);

    try {
      await api.delete(`/teams/${teamId}/members/${memberId}`);

      // Update team data
      const updatedTeam = await fetchTeam(teamId);

      // Update teams list
      setTeams(prevTeams =>
        prevTeams.map(team =>
          team.id === teamId ? updatedTeam : team
        )
      );

      // Update current team if it's the one being modified
      if (currentTeam && currentTeam.id === teamId) {
        setCurrentTeam(updatedTeam);
      }

      showSnackbar('Team member removed successfully', 'success');
      return true;
    } catch (err) {
      logger.error('Error removing team member', err);
      setError('Failed to remove team member');
      showSnackbar(err.response?.data?.detail || 'Failed to remove team member', 'error');
      return false;
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, currentTeam, fetchTeam, showSnackbar]);

  // Set current team
  const switchTeam = useCallback((teamId) => {
    const team = teams.find(t => t.id === teamId);
    if (team) {
      setCurrentTeam(team);
      return true;
    }
    return false;
  }, [teams]);

  // Load teams on auth change
  useEffect(() => {
    if (isAuthenticated) {
      fetchTeams();
      fetchMyInvitations();
    } else {
      setTeams([]);
      setCurrentTeam(null);
      setInvitations([]);
    }
  }, [isAuthenticated, fetchTeams, fetchMyInvitations]);

  // Enhanced context value with organized structure
  const value = {
    // State data
    teams,
    currentTeam,
    invitations,
    loading,
    error,

    // Core team management functions
    fetchTeams,
    fetchTeam,
    createTeam,
    updateTeam,
    deleteTeam,
    switchTeam,

    // Team member management functions
    inviteToTeam,
    removeTeamMember,

    // Invitation management functions
    fetchMyInvitations,
    acceptInvitation,
    rejectInvitation,

    // Utility functions
    clearError: () => setError(null),
    refreshTeams: () => fetchTeams(),
    validateTeamData,

    // Helper functions
    hasTeams: teams.length > 0,
    getTeamCount: () => teams.length,
    getTeamById: (id) => teams.find(team => team.id === id),
    getTeamByName: (name) => teams.find(team => team.name.toLowerCase() === name.toLowerCase()),
    isCurrentTeam: (teamId) => currentTeam?.id === teamId,
    hasInvitations: invitations.length > 0,
    getInvitationCount: () => invitations.length,

    // Team role helpers
    isTeamOwner: (teamId) => {
      const team = teams.find(t => t.id === teamId);
      return team?.role === 'owner';
    },

    isTeamAdmin: (teamId) => {
      const team = teams.find(t => t.id === teamId);
      return team?.role === 'admin' || team?.role === 'owner';
    },

    canManageTeam: (teamId) => {
      const team = teams.find(t => t.id === teamId);
      return team?.role === 'admin' || team?.role === 'owner';
    },

    canInviteMembers: (teamId) => {
      const team = teams.find(t => t.id === teamId);
      return team?.role === 'admin' || team?.role === 'owner';
    },

    // Team statistics
    getTeamMemberCount: (teamId) => {
      const team = teams.find(t => t.id === teamId);
      return team?.member_count || 0;
    },

    // Validation helpers
    canCreateTeam: () => teams.length < CONFIG.MAX_TEAMS_PER_USER,
    getRemainingTeamSlots: () => Math.max(0, CONFIG.MAX_TEAMS_PER_USER - teams.length),

    // Filter helpers
    getTeamsByRole: (role) => teams.filter(team => team.role === role),
    getOwnedTeams: () => teams.filter(team => team.role === 'owner'),
    getAdminTeams: () => teams.filter(team => team.role === 'admin' || team.role === 'owner'),

    // Invitation helpers
    getPendingInvitations: () => invitations.filter(inv => inv.status === 'pending'),
    getAcceptedInvitations: () => invitations.filter(inv => inv.status === 'accepted'),
    getRejectedInvitations: () => invitations.filter(inv => inv.status === 'rejected')
  };

  return (
    <TeamContext.Provider value={value}>
      {children}
    </TeamContext.Provider>
  );
};

// Custom hook to use the team context
// eslint-disable-next-line react-refresh/only-export-components
export const useTeam = () => {
  const context = useContext(TeamContext);
  if (context === undefined) {
    throw new Error('useTeam must be used within a TeamProvider');
  }
  return context;
};

// Export the context
export { TeamContext };

// Default export for convenience
export default TeamProvider;

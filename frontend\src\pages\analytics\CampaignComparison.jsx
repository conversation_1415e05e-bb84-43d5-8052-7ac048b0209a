// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Paper,
  Button,
  CircularProgress,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Tooltip,
  Divider,
} from '@mui/material';
import {
  CompareArrows as CompareArrowsIcon,
  CheckCircle as CheckCircleIcon,
  PauseCircle as PauseCircleIcon,
  PlayCircle as PlayCircleIcon,
  ArrowBack as ArrowBackIcon,
  BarChart as BarChartIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';

import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

const CampaignComparison = () => {
  const { campaignId } = useParams();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [campaign, setCampaign] = useState(null);
  const [confirmWinnerOpen, setConfirmWinnerOpen] = useState(false);
  const [selectedVariantId, setSelectedVariantId] = useState(null);
  const [confirmPauseOpen, setConfirmPauseOpen] = useState(false);
  const [selectedVariantForPause, setSelectedVariantForPause] = useState(null);
  const [confirmActivateOpen, setConfirmActivateOpen] = useState(false);
  const [selectedVariantForActivate, setSelectedVariantForActivate] = useState(null);
  const [error, setError] = useState(null);
  
  // Fetch campaign data function
  const fetchCampaignData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      if (campaignId) {
        // Fetch specific campaign
        const response = await api.get(`/api/campaigns/${campaignId}`);
        setCampaign(response.data);
        showSuccessNotification('Campaign data loaded successfully');
      } else {
        // Redirect to analytics overview if no campaign ID
        navigate('/analytics/overview');
      }
    } catch (error) {
      console.error('Error fetching campaign data:', error);
      setError(error.message || 'Failed to load campaign data');
      showErrorNotification('Failed to load campaign data');
      if (error.response?.status === 404) {
        navigate('/analytics/overview');
      }
    } finally {
      setLoading(false);
    }
  }, [campaignId, navigate, showErrorNotification, showSuccessNotification]);

  // Load campaign data
  useEffect(() => {
    fetchCampaignData();
  }, [fetchCampaignData]);
  
  // Calculate conversion rate
  const calculateConversionRate = useCallback((impressions, clicks) => {
    if (!impressions) return 0;
    return ((clicks / impressions) * 100).toFixed(2);
  }, []);

  // Calculate engagement rate
  const calculateEngagementRate = useCallback((impressions, engagements) => {
    if (!impressions) return 0;
    return ((engagements / impressions) * 100).toFixed(2);
  }, []);

  // Normal cumulative distribution function approximation
  const normalCDF = useCallback((x) => {
    return 0.5 * (1 + erf(x / Math.sqrt(2)));
  }, [erf]);

  // Error function approximation
  const erf = useCallback((x) => {
    const a1 =  0.254829592;
    const a2 = -0.284496736;
    const a3 =  1.421413741;
    const a4 = -1.453152027;
    const a5 =  1.061405429;
    const p  =  0.3275911;

    const sign = x < 0 ? -1 : 1;
    x = Math.abs(x);

    const t = 1.0 / (1.0 + p * x);
    const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

    return sign * y;
  }, []);
  
  // Calculate statistical significance using z-test for proportions
  const calculateStatisticalSignificance = useCallback((variantA, variantB) => {
    const n1 = variantA.performance.total_impressions;
    const n2 = variantB.performance.total_impressions;
    const x1 = variantA.performance.clicks;
    const x2 = variantB.performance.clicks;

    if (n1 === 0 || n2 === 0) return { significant: false, pValue: 1, confidence: 0 };

    const p1 = x1 / n1;
    const p2 = x2 / n2;
    const pPool = (x1 + x2) / (n1 + n2);

    const se = Math.sqrt(pPool * (1 - pPool) * (1/n1 + 1/n2));
    const z = Math.abs(p1 - p2) / se;

    // Calculate p-value (two-tailed test)
    const pValue = 2 * (1 - normalCDF(Math.abs(z)));
    const confidence = (1 - pValue) * 100;

    return {
      significant: pValue < 0.05,
      pValue: pValue,
      confidence: confidence,
      zScore: z
    };
  }, [normalCDF]);

  // Find winning variant based on conversion rate and statistical significance
  const findWinningVariant = useCallback(() => {
    if (!campaign || !campaign.ab_test_variants || campaign.ab_test_variants.length === 0) {
      return null;
    }

    // Find variant with highest conversion rate
    const bestVariant = campaign.ab_test_variants.reduce((winner, current) => {
      const winnerRate = calculateConversionRate(
        winner.performance.total_impressions,
        winner.performance.clicks
      );

      const currentRate = calculateConversionRate(
        current.performance.total_impressions,
        current.performance.clicks
      );

      return parseFloat(currentRate) > parseFloat(winnerRate) ? current : winner;
    }, campaign.ab_test_variants[0]);

    // Check if the best variant is statistically significant compared to control
    const controlVariant = campaign.ab_test_variants.find(v => v.is_control);
    if (controlVariant && bestVariant.variant_id !== controlVariant.variant_id) {
      const significance = calculateStatisticalSignificance(bestVariant, controlVariant);
      bestVariant.statisticalSignificance = significance;
    }

    return bestVariant;
  }, [campaign, calculateConversionRate, calculateStatisticalSignificance]);
  
  // Handle setting a winner
  const handleSetWinner = async () => {
    if (!selectedVariantId) return;
    
    setLoading(true);
    
    try {
      await api.post('/api/campaigns/ab-test/winner', {
        campaign_id: campaignId,
        variant_id: selectedVariantId
      });
      
      // Refresh campaign data
      const response = await api.get(`/api/campaigns/${campaignId}`);
      setCampaign(response.data);
      
      showSuccessNotification('Winner set successfully');
    } catch (error) {
      console.error('Error setting winner:', error);
      showErrorNotification('Failed to set winner');
    } finally {
      setLoading(false);
      setConfirmWinnerOpen(false);
    }
  };
  
  // Handle pausing a variant
  const handlePauseVariant = async () => {
    if (!selectedVariantForPause) return;
    
    setLoading(true);
    
    try {
      await api.post(`/api/campaigns/ab-test/${campaignId}/pause-variant/${selectedVariantForPause}`);
      
      // Refresh campaign data
      const response = await api.get(`/api/campaigns/${campaignId}`);
      setCampaign(response.data);
      
      showSuccessNotification('Variant paused successfully');
    } catch (error) {
      console.error('Error pausing variant:', error);
      showErrorNotification('Failed to pause variant');
    } finally {
      setLoading(false);
      setConfirmPauseOpen(false);
    }
  };
  
  // Open winner confirmation dialog
  const openWinnerDialog = (variantId) => {
    setSelectedVariantId(variantId);
    setConfirmWinnerOpen(true);
  };
  
  // Open pause confirmation dialog
  const openPauseDialog = (variantId) => {
    setSelectedVariantForPause(variantId);
    setConfirmPauseOpen(true);
  };

  // Handle activating a variant
  const handleActivateVariant = useCallback(async () => {
    if (!selectedVariantForActivate) return;

    setLoading(true);
    setError(null);

    try {
      await api.post(`/api/campaigns/ab-test/${campaignId}/activate-variant/${selectedVariantForActivate}`);

      // Refresh campaign data
      const response = await api.get(`/api/campaigns/${campaignId}`);
      setCampaign(response.data);

      showSuccessNotification('Variant activated successfully');
    } catch (error) {
      console.error('Error activating variant:', error);
      setError(error.message || 'Failed to activate variant');
      showErrorNotification('Failed to activate variant');
    } finally {
      setLoading(false);
      setConfirmActivateOpen(false);
      setSelectedVariantForActivate(null);
    }
  }, [selectedVariantForActivate, campaignId, showSuccessNotification, showErrorNotification]);

  // Open activate confirmation dialog
  const openActivateDialog = (variantId) => {
    setSelectedVariantForActivate(variantId);
    setConfirmActivateOpen(true);
  };
  
  if (loading) {
    return (
      <Box sx={{ py: 3, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (!campaign) {
    return (
      <Box sx={{ py: 3 }}>
        <Alert severity="error">Campaign not found</Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/analytics/overview')}
          sx={{ mt: 2 }}
        >
          Back to Analytics
        </Button>
      </Box>
    );
  }
  
  // Check if this is an A/B test campaign
  if (!campaign.is_ab_test) {
    return (
      <Box sx={{ py: 3 }}>
        <Alert severity="warning">This campaign is not an A/B test</Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/analytics/overview')}
          sx={{ mt: 2 }}
        >
          Back to Analytics
        </Button>
      </Box>
    );
  }
  
  const winningVariant = findWinningVariant();
  
  return (
    <Box sx={{ py: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/analytics/overview')}
            sx={{ mb: 1 }}
          >
            Back to Analytics
          </Button>
          <Typography variant="h4">
            <CompareArrowsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            A/B Test Comparison: {campaign.ab_test_name || campaign.name}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            onClick={fetchCampaignData}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={16} /> : <BarChartIcon />}
          >
            {loading ? 'Refreshing...' : 'Refresh Data'}
          </Button>

          {!campaign.ab_test_winner_variant_id && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<CheckCircleIcon />}
              onClick={() => openWinnerDialog(winningVariant?.variant_id)}
              disabled={!winningVariant || loading}
            >
              Set Winner
            </Button>
          )}
        </Box>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {campaign.ab_test_winner_variant_id && (
        <Alert severity="success" sx={{ mb: 3 }}>
          This A/B test has been completed. The winning variant has been selected.
        </Alert>
      )}

      {winningVariant?.statisticalSignificance && (
        <Alert
          severity={winningVariant.statisticalSignificance.significant ? "success" : "warning"}
          sx={{ mb: 3 }}
        >
          {winningVariant.statisticalSignificance.significant ? (
            <>
              <strong>Statistically Significant Result:</strong> The leading variant shows a
              {winningVariant.statisticalSignificance.confidence.toFixed(1)}% confidence level
              (p-value: {winningVariant.statisticalSignificance.pValue.toFixed(4)}).
            </>
          ) : (
            <>
              <strong>Not Statistically Significant:</strong> More data needed for reliable results.
              Current confidence: {winningVariant.statisticalSignificance.confidence.toFixed(1)}%.
            </>
          )}
        </Alert>
      )}
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Test Overview
          </Typography>
          <Typography variant="body2" paragraph>
            {campaign.ab_test_description || campaign.description}
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h6">
                  {campaign.ab_test_variants.length}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Variants
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h6">
                  {new Date(campaign.start_date).toLocaleDateString()}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Start Date
                </Typography>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h6">
                  {campaign.ab_test_end_date 
                    ? new Date(campaign.ab_test_end_date).toLocaleDateString() 
                    : 'Ongoing'}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  End Date
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Performance Insights
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Best Performing Variant
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h6" color="primary">
                    {winningVariant?.name || 'N/A'}
                  </Typography>
                  {winningVariant?.statisticalSignificance?.significant && (
                    <Chip
                      label="Statistically Significant"
                      color="success"
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Box>
                <Typography variant="body2" color="textSecondary">
                  Conversion Rate: {winningVariant ? calculateConversionRate(
                    winningVariant.performance.total_impressions,
                    winningVariant.performance.clicks
                  ) : '0'}%
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Total Test Performance
                </Typography>
                <Typography variant="h6" color="primary">
                  {campaign.ab_test_variants.reduce((sum, variant) =>
                    sum + (variant.performance.total_impressions || 0), 0
                  ).toLocaleString()}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Total Impressions Across All Variants
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={Math.min(100, (campaign.ab_test_variants.reduce((sum, variant) =>
                    sum + (variant.performance.total_impressions || 0), 0) / 10000) * 100)}
                  sx={{ mt: 1, height: 6, borderRadius: 3 }}
                />
              </Paper>
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="subtitle1" gutterBottom>
            Recommendations
          </Typography>
          <Box sx={{ mt: 2 }}>
            {!campaign.ab_test_winner_variant_id && (
              <>
                {winningVariant?.statisticalSignificance?.significant ? (
                  <Alert severity="success" sx={{ mb: 2 }}>
                    <strong>Ready to declare winner:</strong> The leading variant shows statistically significant results.
                    Consider setting it as the winner to optimize your campaign performance.
                  </Alert>
                ) : (
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <strong>Continue testing:</strong> Results are not yet statistically significant.
                    Allow more time for data collection to ensure reliable results.
                  </Alert>
                )}

                {campaign.ab_test_variants.some(v => v.performance.total_impressions < 1000) && (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <strong>Low sample size:</strong> Some variants have fewer than 1,000 impressions.
                    Consider increasing traffic allocation for more reliable results.
                  </Alert>
                )}
              </>
            )}
          </Box>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            <BarChartIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Variant Performance Comparison
          </Typography>
          
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Variant</TableCell>
                  <TableCell align="right">Impressions</TableCell>
                  <TableCell align="right">Engagements</TableCell>
                  <TableCell align="right">Engagement Rate</TableCell>
                  <TableCell align="right">Clicks</TableCell>
                  <TableCell align="right">Conversion Rate</TableCell>
                  <TableCell align="right">Confidence</TableCell>
                  <TableCell align="right">Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {campaign.ab_test_variants.map((variant) => {
                  const isWinner = campaign.ab_test_winner_variant_id === variant.variant_id;
                  const conversionRate = calculateConversionRate(
                    variant.performance.total_impressions,
                    variant.performance.clicks
                  );
                  const engagementRate = calculateEngagementRate(
                    variant.performance.total_impressions,
                    variant.performance.total_engagements
                  );

                  // Calculate confidence interval for conversion rate
                  const n = variant.performance.total_impressions;
                  const x = variant.performance.clicks;
                  const p = n > 0 ? x / n : 0;
                  const z = 1.96; // 95% confidence interval
                  const margin = n > 0 ? z * Math.sqrt((p * (1 - p)) / n) : 0;
                  const confidenceInterval = {
                    lower: Math.max(0, (p - margin) * 100),
                    upper: Math.min(100, (p + margin) * 100)
                  };

                  return (
                    <TableRow key={variant.variant_id}>
                      <TableCell component="th" scope="row">
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {variant.name}
                          {isWinner && (
                            <Chip 
                              label="Winner" 
                              color="success" 
                              size="small" 
                              sx={{ ml: 1 }}
                            />
                          )}
                          {variant.is_control && (
                            <Chip 
                              label="Control" 
                              color="primary" 
                              size="small" 
                              variant="outlined" 
                              sx={{ ml: 1 }}
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell align="right">{variant.performance.total_impressions}</TableCell>
                      <TableCell align="right">{variant.performance.total_engagements}</TableCell>
                      <TableCell align="right">{engagementRate}%</TableCell>
                      <TableCell align="right">{variant.performance.clicks}</TableCell>
                      <TableCell align="right">{conversionRate}%</TableCell>
                      <TableCell align="right">
                        <Typography variant="caption" display="block">
                          {confidenceInterval.lower.toFixed(1)}% - {confidenceInterval.upper.toFixed(1)}%
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          95% CI
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Chip 
                          label={variant.status || 'Active'} 
                          color={variant.status === 'paused' ? 'warning' : 'success'} 
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        {!campaign.ab_test_winner_variant_id && (
                          <>
                            <Tooltip title="Set as Winner">
                              <Button
                                size="small"
                                color="success"
                                onClick={() => openWinnerDialog(variant.variant_id)}
                                sx={{ mr: 1 }}
                              >
                                <CheckCircleIcon fontSize="small" />
                              </Button>
                            </Tooltip>
                            
                            {variant.status !== 'paused' ? (
                              <Tooltip title="Pause Variant">
                                <Button
                                  size="small"
                                  color="warning"
                                  onClick={() => openPauseDialog(variant.variant_id)}
                                >
                                  <PauseCircleIcon fontSize="small" />
                                </Button>
                              </Tooltip>
                            ) : (
                              <Tooltip title="Activate Variant">
                                <Button
                                  size="small"
                                  color="primary"
                                  onClick={() => openActivateDialog(variant.variant_id)}
                                >
                                  <PlayCircleIcon fontSize="small" />
                                </Button>
                              </Tooltip>
                            )}
                          </>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
      
      {/* Confirmation Dialogs */}
      <Dialog
        open={confirmWinnerOpen}
        onClose={() => setConfirmWinnerOpen(false)}
      >
        <DialogTitle>Confirm Winner</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to set this variant as the winner? This will end the A/B test.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmWinnerOpen(false)}>Cancel</Button>
          <Button onClick={handleSetWinner} color="primary" variant="contained">
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      
      <Dialog
        open={confirmPauseOpen}
        onClose={() => setConfirmPauseOpen(false)}
      >
        <DialogTitle>Confirm Pause</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to pause this variant? It will no longer receive traffic in the A/B test.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmPauseOpen(false)}>Cancel</Button>
          <Button onClick={handlePauseVariant} color="warning" variant="contained">
            Pause Variant
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={confirmActivateOpen}
        onClose={() => setConfirmActivateOpen(false)}
      >
        <DialogTitle>Confirm Activation</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to activate this variant? It will start receiving traffic in the A/B test again.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmActivateOpen(false)}>Cancel</Button>
          <Button onClick={handleActivateVariant} color="primary" variant="contained">
            Activate Variant
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CampaignComparison;

// @since 2024-1-1 to 2025-25-7
import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Alert,
  Divider
} from '@mui/material';
import {
  ErrorOutline as ErrorIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon
} from '@mui/icons-material';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error for debugging
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // In production, you might want to log this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  render() {
    if (this.state.hasError) {
      const { title, showDetails = false, showRetry = true } = this.props;
      
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '60vh',
            p: 3,
            textAlign: 'center'
          }}
        >
          <Card 
            variant="glass" 
            sx={{ 
              maxWidth: 600, 
              width: '100%',
              borderRadius: 3,
              boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.2)'
            }}
          >
            <CardContent sx={{ p: 4 }}>
              {/* Error Icon */}
              <Box
                sx={{
                  fontSize: '4rem',
                  mb: 2,
                  opacity: 0.6,
                  color: 'error.main'
                }}
              >
                <ErrorIcon sx={{ fontSize: 'inherit' }} />
              </Box>

              {/* Error Title */}
              <Typography variant="h5" component="h2" gutterBottom color="error.main">
                {title || 'Something went wrong'}
              </Typography>

              {/* Error Description */}
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                We encountered an unexpected error while loading this page. 
                Don't worry, your data is safe and this is likely a temporary issue.
              </Typography>

              {/* Error Details (Development only) */}
              {showDetails && process.env.NODE_ENV === 'development' && this.state.error && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Alert severity="error" sx={{ textAlign: 'left', mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Error Details (Development Mode):
                    </Typography>
                    <Typography variant="body2" component="pre" sx={{ 
                      fontSize: '0.75rem',
                      overflow: 'auto',
                      maxHeight: 200,
                      fontFamily: 'monospace'
                    }}>
                      {this.state.error.toString()}
                      {this.state.errorInfo.componentStack}
                    </Typography>
                  </Alert>
                </>
              )}

              {/* Action Buttons */}
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                {showRetry && (
                  <Button
                    variant="contained"
                    startIcon={<RefreshIcon />}
                    onClick={this.handleRetry}
                    sx={{
                      background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
                      }
                    }}
                  >
                    Try Again
                  </Button>
                )}
                
                <Button
                  variant="outlined"
                  startIcon={<HomeIcon />}
                  onClick={this.handleGoHome}
                  sx={{
                    borderColor: 'primary.main',
                    color: 'primary.main',
                    '&:hover': {
                      borderColor: 'primary.dark',
                      backgroundColor: 'primary.light',
                    }
                  }}
                >
                  Go to Dashboard
                </Button>
              </Box>

              {/* Retry Count (Development only) */}
              {process.env.NODE_ENV === 'development' && this.state.retryCount > 0 && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
                  Retry attempts: {this.state.retryCount}
                </Typography>
              )}
            </CardContent>
          </Card>

          {/* Additional Help */}
          <Typography variant="body2" color="text.secondary" sx={{ mt: 3, maxWidth: 500 }}>
            If this problem persists, please contact support or try refreshing the page. 
            You can also check your internet connection and try again.
          </Typography>
        </Box>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for functional components
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  return function WrappedComponent(props) {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
};

export default ErrorBoundary;

/**
 * @fileoverview ContentDetailDialog - Enterprise-grade content detail dialog component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @description Advanced content detail dialog with comprehensive editing capabilities, modal management,
 * analytics integration, and enterprise-grade functionality following ACE Social platform standards.
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useImperativeHandle,
  forwardRef,
  memo
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Box,
  Typography,
  Tabs,
  Tab,
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  useTheme,
  Tooltip,
  FormHelperText,
  useMediaQuery
} from '@mui/material';
import {
  Close as CloseIcon,
  Save as SaveIcon,
  Image as ImageIcon,
  Edit as EditIcon,
  Undo as UndoIcon,
  Redo as RedoIcon,
  Preview as PreviewIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  LinkedIn as LinkedInIcon,
  Pinterest as PinterestIcon,
  YouTube as YouTubeIcon,
  Chat as ThreadsIcon,
  MusicVideo as TikTokIcon
} from '@mui/icons-material';
// Enhanced context and hook imports
import { useNotification } from "../../contexts/NotificationContext";

// Enhanced common components
import {
  ErrorBoundary
} from "../common";

// Enhanced utility imports
import {
  announceToScreenReader,
  debounce
} from "../../utils/helpers";

// Enhanced API imports
import api from "../../api";

// Mock hooks for missing dependencies
const useAnalytics = () => ({
  trackEvent: () => {},
  trackError: () => {},
  trackPerformance: () => {}
});

const useDebounce = (callback, delay) => {
  return useCallback((...args) => debounce(callback, delay)(...args), [callback, delay]);
};

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Dialog view modes
const VIEW_MODES = {
  EDIT: 'edit',
  VIEW: 'view',
  PREVIEW: 'preview',
  ANALYTICS: 'analytics'
};

// Tab configuration
const DIALOG_TABS = {
  CONTENT: 0,
  MEDIA: 1,
  PREVIEW: 2,
  SETTINGS: 3,
  ANALYTICS: 4,
  HISTORY: 5
};

// Content status types
const CONTENT_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  SCHEDULED: 'scheduled',
  ARCHIVED: 'archived',
  FAILED: 'failed',
  PROCESSING: 'processing',
  PENDING: 'pending'
};

// Content types
const CONTENT_TYPES = {
  POST: 'post',
  ARTICLE: 'article',
  VIDEO: 'video',
  IMAGE: 'image',
  CAROUSEL: 'carousel',
  STORY: 'story',
  REEL: 'reel',
  THREAD: 'thread',
  POLL: 'poll',
  EVENT: 'event'
};

// Platform types
const PLATFORMS = {
  LINKEDIN: 'linkedin',
  TWITTER: 'twitter',
  FACEBOOK: 'facebook',
  INSTAGRAM: 'instagram',
  TIKTOK: 'tiktok',
  YOUTUBE: 'youtube',
  PINTEREST: 'pinterest',
  THREADS: 'threads'
};



// Component configuration
const COMPONENT_CONFIG = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 300,
  AUTO_SAVE_DELAY: 2000,
  MAX_HISTORY_ENTRIES: 50,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  VALIDATION_RULES: {
    TITLE_MIN_LENGTH: 1,
    TITLE_MAX_LENGTH: 200,
    DESCRIPTION_MAX_LENGTH: 500,
    CONTENT_MIN_LENGTH: 1,
    CONTENT_MAX_LENGTH: 10000,
    TAGS_MAX_COUNT: 20,
    PLATFORMS_MIN_COUNT: 1,
    PLATFORMS_MAX_COUNT: 8
  }
};

// Subscription plan limits
const PLAN_LIMITS = {
  creator: {
    maxContentLength: 2000,
    maxImages: 5,
    maxTags: 10,
    allowedFeatures: ['basic_editing', 'image_upload', 'platform_posting'],
    maxPlatforms: 3
  },
  accelerator: {
    maxContentLength: 5000,
    maxImages: 15,
    maxTags: 15,
    allowedFeatures: ['basic_editing', 'advanced_editing', 'image_upload', 'video_upload', 'platform_posting', 'scheduling', 'analytics'],
    maxPlatforms: 6
  },
  dominator: {
    maxContentLength: 10000,
    maxImages: 50,
    maxTags: 20,
    allowedFeatures: ['basic_editing', 'advanced_editing', 'image_upload', 'video_upload', 'platform_posting', 'scheduling', 'analytics', 'ai_assistance', 'bulk_operations'],
    maxPlatforms: 8
  }
};

// Platform configuration
const PLATFORM_CONFIG = {
  [PLATFORMS.LINKEDIN]: {
    name: 'LinkedIn',
    icon: LinkedInIcon,
    color: '#0077B5',
    maxLength: 3000,
    supportsImages: true,
    supportsVideo: true,
    supportsCarousel: true
  },
  [PLATFORMS.TWITTER]: {
    name: 'Twitter',
    icon: TwitterIcon,
    color: '#1DA1F2',
    maxLength: 280,
    supportsImages: true,
    supportsVideo: true,
    supportsCarousel: false
  },
  [PLATFORMS.FACEBOOK]: {
    name: 'Facebook',
    icon: FacebookIcon,
    color: '#4267B2',
    maxLength: 63206,
    supportsImages: true,
    supportsVideo: true,
    supportsCarousel: true
  },
  [PLATFORMS.INSTAGRAM]: {
    name: 'Instagram',
    icon: InstagramIcon,
    color: '#C13584',
    maxLength: 2200,
    supportsImages: true,
    supportsVideo: true,
    supportsCarousel: true
  },
  [PLATFORMS.TIKTOK]: {
    name: 'TikTok',
    icon: TikTokIcon,
    color: '#000000',
    maxLength: 300,
    supportsImages: false,
    supportsVideo: true,
    supportsCarousel: false
  },
  [PLATFORMS.YOUTUBE]: {
    name: 'YouTube',
    icon: YouTubeIcon,
    color: '#FF0000',
    maxLength: 5000,
    supportsImages: true,
    supportsVideo: true,
    supportsCarousel: false
  },
  [PLATFORMS.PINTEREST]: {
    name: 'Pinterest',
    icon: PinterestIcon,
    color: '#BD081C',
    maxLength: 500,
    supportsImages: true,
    supportsVideo: true,
    supportsCarousel: false
  },
  [PLATFORMS.THREADS]: {
    name: 'Threads',
    icon: ThreadsIcon,
    color: '#000000',
    maxLength: 500,
    supportsImages: true,
    supportsVideo: true,
    supportsCarousel: false
  }
};

/**
 * ContentDetailDialog - Enterprise-grade content detail dialog component
 *
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Callback to close the dialog
 * @param {string} props.contentId - ID of content to edit (null for new content)
 * @param {Object} props.initialContent - Initial content data for new content
 * @param {string} props.mode - Dialog mode (edit, view, preview, analytics)
 * @param {Function} props.onSave - Callback when content is saved
 * @param {Function} props.onDelete - Callback when content is deleted
 * @param {Function} props.onSchedule - Callback when content is scheduled
 * @param {Function} props.onDuplicate - Callback when content is duplicated
 * @param {Function} props.onShare - Callback when content is shared
 * @param {Function} props.onArchive - Callback when content is archived
 * @param {Function} props.onPublish - Callback when content is published
 * @param {Function} props.onAnalytics - Callback when analytics is viewed
 * @param {Function} props.onContentChange - Callback when content changes
 * @param {Function} props.onValidationChange - Callback when validation state changes
 * @param {string} props.userPlan - User subscription plan
 * @param {boolean} props.enableAnalytics - Enable analytics tracking
 * @param {boolean} props.enableAccessibility - Enable accessibility features
 * @param {boolean} props.enableAutoSave - Enable auto-save functionality
 * @param {boolean} props.enableHistory - Enable undo/redo history
 * @param {boolean} props.enablePreview - Enable real-time preview
 * @param {string} props.testId - Test identifier
 * @param {string} props.ariaLabel - Accessibility label
 * @param {boolean} props.announceChanges - Announce changes to screen readers
 */
const ContentDetailDialog = memo(forwardRef(({
  // Basic props
  open = false,
  onClose,
  contentId = null,
  initialContent = null,
  mode = VIEW_MODES.EDIT,

  // Callback props
  onSave,
  onContentChange,
  onValidationChange,

  // Enhanced props
  userPlan = 'creator',
  enableAnalytics = true,
  enableAccessibility = true,
  enableAutoSave = true,
  enableHistory = true,

  // Testing props
  testId = 'content-detail-dialog',

  // Accessibility props
  announceChanges = true
}, ref) => {
  // ===========================
  // HOOKS & CONTEXT
  // ===========================

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced hooks
  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { trackEvent } = useAnalytics();

  // ===========================
  // STATE MANAGEMENT
  // ===========================

  // Core state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [content, setContent] = useState(null);
  const [originalContent, setOriginalContent] = useState(null);

  // UI state
  const [tabValue, setTabValue] = useState(DIALOG_TABS.CONTENT);
  const [viewMode] = useState(mode);
  const [isFullscreen] = useState(false);

  // Validation state
  const [errors, setErrors] = useState({});
  const [isValid, setIsValid] = useState(true);

  // History state
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Auto-save state
  const [autoSaveStatus, setAutoSaveStatus] = useState('idle'); // idle, saving, saved, error

  // Refs
  const dialogRef = useRef(null);
  const autoSaveTimeoutRef = useRef(null);
  const validationTimeoutRef = useRef(null);

  // Debounced functions
  const debouncedTrackEvent = useDebounce(trackEvent, 300);
  const debouncedAutoSave = useDebounce(handleAutoSave, COMPONENT_CONFIG.AUTO_SAVE_DELAY);

  // ===========================
  // COMPUTED VALUES
  // ===========================

  // Get plan limits
  const planLimits = useMemo(() => {
    return PLAN_LIMITS[userPlan] || PLAN_LIMITS.creator;
  }, [userPlan]);

  // Check if feature is allowed
  const isFeatureAllowed = useCallback((feature) => {
    return planLimits.allowedFeatures.includes(feature);
  }, [planLimits]);

  // Get available platforms based on plan
  const availablePlatforms = useMemo(() => {
    return Object.entries(PLATFORM_CONFIG).slice(0, planLimits.maxPlatforms);
  }, [planLimits.maxPlatforms]);

  // Platform options for UI
  const platformOptions = useMemo(() => {
    return availablePlatforms.map(([key, config]) => ({
      value: key,
      label: config.name,
      icon: config.icon,
      color: config.color,
      maxLength: config.maxLength
    }));
  }, [availablePlatforms]);

  // Status options
  const statusOptions = useMemo(() => {
    return [
      { value: CONTENT_STATUS.DRAFT, label: 'Draft', color: theme.palette.grey[500] },
      { value: CONTENT_STATUS.SCHEDULED, label: 'Scheduled', color: theme.palette.info.main },
      { value: CONTENT_STATUS.PUBLISHED, label: 'Published', color: theme.palette.success.main },
      { value: CONTENT_STATUS.ARCHIVED, label: 'Archived', color: theme.palette.warning.main },
      { value: CONTENT_STATUS.FAILED, label: 'Failed', color: theme.palette.error.main }
    ];
  }, [theme]);



  // Check if content has unsaved changes
  const hasChanges = useMemo(() => {
    if (!content || !originalContent) return false;
    return JSON.stringify(content) !== JSON.stringify(originalContent);
  }, [content, originalContent]);

  // Get content character count for selected platforms
  const characterCounts = useMemo(() => {
    if (!content?.platforms || !content?.text_content) return {};

    const counts = {};
    content.platforms.forEach(platform => {
      const config = PLATFORM_CONFIG[platform];
      if (config) {
        counts[platform] = {
          current: content.text_content.length,
          max: config.maxLength,
          remaining: config.maxLength - content.text_content.length,
          isOverLimit: content.text_content.length > config.maxLength
        };
      }
    });
    return counts;
  }, [content?.platforms, content?.text_content]);

  // Expose ref methods
  useImperativeHandle(ref, () => ({
    focus: () => dialogRef.current?.focus(),
    save: () => handleSave(),
    close: () => handleClose(),
    validate: () => validateForm(),
    getContent: () => content,
    hasUnsavedChanges: () => hasChanges,
    undo: () => handleUndo(),
    redo: () => handleRedo()
  }), [content, hasChanges, handleSave, handleClose, validateForm, handleUndo, handleRedo]);

  // ===========================
  // UTILITY FUNCTIONS
  // ===========================

  /**
   * Handle analytics tracking
   */
  const handleAnalytics = useCallback((eventName, data = {}) => {
    if (!enableAnalytics) return;

    const analyticsData = {
      ...data,
      component: 'ContentDetailDialog',
      contentId: content?.id,
      userPlan,
      viewMode,
      tabValue
    };

    debouncedTrackEvent(eventName, analyticsData);
  }, [enableAnalytics, content?.id, userPlan, viewMode, tabValue, debouncedTrackEvent]);

  /**
   * Validate form
   */
  const validateForm = useCallback(() => {
    if (!content) return false;

    const newErrors = {};
    const newWarnings = {};

    // Title validation
    if (!content.title?.trim()) {
      newErrors.title = 'Title is required';
    } else if (content.title.length > COMPONENT_CONFIG.VALIDATION_RULES.TITLE_MAX_LENGTH) {
      newErrors.title = `Title must be less than ${COMPONENT_CONFIG.VALIDATION_RULES.TITLE_MAX_LENGTH} characters`;
    }

    // Content validation
    if (!content.text_content?.trim()) {
      newErrors.text_content = 'Content is required';
    } else if (content.text_content.length > planLimits.maxContentLength) {
      newErrors.text_content = `Content exceeds plan limit of ${planLimits.maxContentLength} characters`;
    }

    // Platform validation
    if (!content.platforms?.length) {
      newErrors.platforms = 'At least one platform is required';
    } else if (content.platforms.length > planLimits.maxPlatforms) {
      newErrors.platforms = `Too many platforms selected. Plan limit: ${planLimits.maxPlatforms}`;
    }

    // Platform-specific content length validation
    if (content.platforms && content.text_content) {
      content.platforms.forEach(platform => {
        const config = PLATFORM_CONFIG[platform];
        if (config && content.text_content.length > config.maxLength) {
          newWarnings[`platform_${platform}`] = `Content exceeds ${config.name} limit of ${config.maxLength} characters`;
        }
      });
    }

    setErrors(newErrors);

    const isFormValid = Object.keys(newErrors).length === 0;
    setIsValid(isFormValid);

    if (onValidationChange) {
      onValidationChange(isFormValid, newErrors, {});
    }

    return isFormValid;
  }, [content, planLimits, onValidationChange]);

  /**
   * Handle auto-save
   */
  const handleAutoSave = useCallback(async () => {
    if (!enableAutoSave || !content || !hasChanges || !isValid) return;

    setAutoSaveStatus('saving');

    try {
      // Simulate auto-save API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setAutoSaveStatus('saved');
      setHasUnsavedChanges(false);

      setTimeout(() => {
        setAutoSaveStatus('idle');
      }, 2000);
    } catch (error) {
      console.error('Auto-save failed:', error);
      setAutoSaveStatus('error');
    }
  }, [enableAutoSave, content, hasChanges, isValid]);

  /**
   * Handle undo
   */
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      setContent(history[newIndex]);
      setHasUnsavedChanges(true);

      handleAnalytics('content_undo');
    }
  }, [historyIndex, history, handleAnalytics]);

  /**
   * Handle redo
   */
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      setContent(history[newIndex]);
      setHasUnsavedChanges(true);

      handleAnalytics('content_redo');
    }
  }, [historyIndex, history, handleAnalytics]);

  /**
   * Handle close
   */
  const handleClose = useCallback(() => {
    if (hasUnsavedChanges) {
      // Show confirmation dialog
      if (window.confirm('You have unsaved changes. Are you sure you want to close?')) {
        onClose();
      }
    } else {
      onClose();
    }
  }, [hasUnsavedChanges, onClose]);

  // ===========================
  // EFFECTS
  // ===========================

  // Fetch content when dialog opens
  useEffect(() => {
    if (open && contentId) {
      fetchContent();
    } else if (open && !contentId) {
      // Create new content template
      const newContent = initialContent || {
        id: null,
        title: '',
        text_content: '',
        description: '',
        platforms: [],
        tags: [],
        status: CONTENT_STATUS.DRAFT,
        type: CONTENT_TYPES.POST,
        images: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      setContent(newContent);
      setOriginalContent(newContent);

      // Reset history
      setHistory([newContent]);
      setHistoryIndex(0);
      setHasUnsavedChanges(false);
    }
  }, [open, contentId, initialContent, fetchContent]);

  // Auto-save effect
  useEffect(() => {
    if (enableAutoSave && hasChanges && isValid) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        debouncedAutoSave();
      }, COMPONENT_CONFIG.AUTO_SAVE_DELAY);
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [enableAutoSave, hasChanges, isValid, debouncedAutoSave]);

  // Validation effect
  useEffect(() => {
    if (content) {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }

      validationTimeoutRef.current = setTimeout(() => {
        validateForm();
      }, COMPONENT_CONFIG.DEBOUNCE_DELAY);
    }

    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, [content, validateForm]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current);
      }
    };
  }, []);

  // Announce changes to screen readers
  useEffect(() => {
    if (announceChanges && enableAccessibility) {
      if (autoSaveStatus === 'saved') {
        announceToScreenReader('Content auto-saved successfully');
      } else if (autoSaveStatus === 'error') {
        announceToScreenReader('Auto-save failed');
      }
    }
  }, [autoSaveStatus, announceChanges, enableAccessibility]);

  // ===========================
  // API FUNCTIONS
  // ===========================

  /**
   * Fetch content from API
   */
  const fetchContent = useCallback(async () => {
    setLoading(true);

    try {
      const response = await api.get(`/api/content/${contentId}`);

      setContent(response.data);
      setOriginalContent(response.data);

      // Initialize history with current content
      setHistory([response.data]);
      setHistoryIndex(0);
      setHasUnsavedChanges(false);

      handleAnalytics('content_loaded', {
        contentId
      });

    } catch (error) {
      console.error('Error fetching content:', error);
      showErrorNotification('Failed to load content details');

      handleAnalytics('content_load_failed', {
        contentId,
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  }, [contentId, showErrorNotification, handleAnalytics]);

  /**
   * Handle tab change
   */
  const handleTabChange = useCallback((event, newValue) => {
    setTabValue(newValue);

    handleAnalytics('tab_changed', {
      fromTab: tabValue,
      toTab: newValue
    });
  }, [tabValue, handleAnalytics]);

  /**
   * Handle input change
   */
  const handleInputChange = useCallback((field, value) => {
    const updatedContent = { ...content, [field]: value };
    setContent(updatedContent);
    setHasUnsavedChanges(true);

    // Add to history if it's a significant change
    if (['title', 'text_content', 'description', 'platforms', 'tags', 'status', 'type'].includes(field)) {
      // Only add to history if it's different from the last entry
      if (JSON.stringify(updatedContent) !== JSON.stringify(history[historyIndex])) {
        // Remove any forward history if we're not at the end
        const newHistory = history.slice(0, historyIndex + 1);
        newHistory.push(updatedContent);

        // Limit history size
        if (newHistory.length > COMPONENT_CONFIG.MAX_HISTORY_ENTRIES) {
          newHistory.shift();
        } else {
          setHistoryIndex(newHistory.length - 1);
        }

        setHistory(newHistory);
      }
    }

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Trigger content change callback
    if (onContentChange) {
      onContentChange(updatedContent, field, value);
    }

    handleAnalytics('content_changed', {
      field,
      contentLength: updatedContent.text_content?.length || 0
    });
  }, [content, history, historyIndex, errors, onContentChange, handleAnalytics]);

  /**
   * Handle save
   */
  const handleSave = useCallback(async () => {
    if (!validateForm()) return;

    setSaving(true);

    try {
      let response;

      // Prepare form data for file upload
      const formData = new FormData();
      formData.append('title', content.title);
      formData.append('text_content', content.text_content);
      formData.append('description', content.description || '');
      formData.append('platforms', JSON.stringify(content.platforms));
      formData.append('tags', JSON.stringify(content.tags || []));
      formData.append('status', content.status);
      formData.append('type', content.type || CONTENT_TYPES.POST);

      // Add images
      if (content.images && content.images.length > 0) {
        content.images.forEach((image, index) => {
          if (image.file) {
            formData.append(`images[${index}]`, image.file);
          } else if (image.url) {
            formData.append(`existing_images[${index}]`, image.url);
          }
        });
      }

      if (content.id) {
        // Update existing content
        response = await api.put(`/api/content/${content.id}`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
      } else {
        // Create new content
        response = await api.post('/api/content', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
      }

      setHasUnsavedChanges(false);
      setOriginalContent(response.data);

      showSuccessNotification(content.id ? 'Content updated successfully' : 'Content created successfully');

      handleAnalytics('content_saved', {
        contentId: response.data.id,
        isNew: !content.id
      });

      if (onSave) {
        onSave(response.data);
      }

      onClose();
    } catch (error) {
      console.error('Error saving content:', error);
      showErrorNotification(`Failed to ${content.id ? 'update' : 'create'} content: ${error.response?.data?.message || error.message}`);

      handleAnalytics('content_save_failed', {
        contentId: content.id,
        error: error.message
      });
    } finally {
      setSaving(false);
    }
  }, [content, validateForm, showSuccessNotification, showErrorNotification, handleAnalytics, onSave, onClose]);

  // Early return for loading state
  if (loading) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth data-testid={`${testId}-loading`}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">Loading Content...</Typography>
            <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  if (!content) return null;

  return (
    <ErrorBoundary>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="lg"
        fullWidth
        fullScreen={isFullscreen}
        data-testid={testId}
        aria-labelledby="content-detail-dialog-title"
        ref={dialogRef}
      >
        <DialogTitle id="content-detail-dialog-title">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              {content.id ? 'Edit Content' : 'Create Content'}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {/* Auto-save status */}
              {enableAutoSave && (
                <Chip
                  size="small"
                  label={
                    autoSaveStatus === 'saving' ? 'Saving...' :
                    autoSaveStatus === 'saved' ? 'Saved' :
                    autoSaveStatus === 'error' ? 'Save failed' :
                    hasUnsavedChanges ? 'Unsaved changes' : 'Up to date'
                  }
                  color={
                    autoSaveStatus === 'saved' ? 'success' :
                    autoSaveStatus === 'error' ? 'error' :
                    hasUnsavedChanges ? 'warning' : 'default'
                  }
                  variant="outlined"
                />
              )}

              {/* History controls */}
              {enableHistory && (
                <>
                  <Tooltip title="Undo">
                    <span>
                      <IconButton
                        color="inherit"
                        onClick={handleUndo}
                        disabled={historyIndex <= 0}
                        size="small"
                        aria-label="Undo last change"
                      >
                        <UndoIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                  <Tooltip title="Redo">
                    <span>
                      <IconButton
                        color="inherit"
                        onClick={handleRedo}
                        disabled={historyIndex >= history.length - 1}
                        size="small"
                        aria-label="Redo last change"
                      >
                        <RedoIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                </>
              )}

              <IconButton
                edge="end"
                color="inherit"
                onClick={handleClose}
                aria-label="close dialog"
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
        </DialogTitle>

        <DialogContent dividers>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="content editing tabs"
            variant={isMobile ? "scrollable" : "standard"}
            scrollButtons="auto"
          >
            <Tab label="Content" icon={<EditIcon />} />
            <Tab label="Media" icon={<ImageIcon />} />
            <Tab label="Preview" icon={<PreviewIcon />} />
            <Tab label="Settings" icon={<SettingsIcon />} />
            {isFeatureAllowed('analytics') && (
              <Tab label="Analytics" icon={<AnalyticsIcon />} />
            )}
          </Tabs>

          <Box sx={{ mt: 3, minHeight: 400 }}>
            {/* Content Tab */}
            {tabValue === DIALOG_TABS.CONTENT && (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    label="Title"
                    value={content.title || ''}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    fullWidth
                    variant="outlined"
                    error={Boolean(errors.title)}
                    helperText={errors.title}
                    inputProps={{
                      maxLength: COMPONENT_CONFIG.VALIDATION_RULES.TITLE_MAX_LENGTH
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label="Description"
                    value={content.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    fullWidth
                    variant="outlined"
                    multiline
                    rows={2}
                    inputProps={{
                      maxLength: COMPONENT_CONFIG.VALIDATION_RULES.DESCRIPTION_MAX_LENGTH
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>Content</Typography>
                  <TextField
                    value={content.text_content || ''}
                    onChange={(e) => handleInputChange('text_content', e.target.value)}
                    fullWidth
                    multiline
                    rows={8}
                    variant="outlined"
                    error={Boolean(errors.text_content)}
                    helperText={errors.text_content}
                    inputProps={{
                      maxLength: planLimits.maxContentLength
                    }}
                  />

                  {/* Character counts for platforms */}
                  {content.platforms && content.platforms.length > 0 && (
                    <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {Object.entries(characterCounts).map(([platform, count]) => (
                        <Chip
                          key={platform}
                          size="small"
                          label={`${PLATFORM_CONFIG[platform]?.name}: ${count.current}/${count.max}`}
                          color={count.isOverLimit ? 'error' : 'default'}
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  )}
                </Grid>
              </Grid>
            )}

            {/* Settings Tab */}
            {tabValue === DIALOG_TABS.SETTINGS && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth error={Boolean(errors.platforms)}>
                    <InputLabel>Platforms</InputLabel>
                    <Select
                      multiple
                      value={content.platforms || []}
                      onChange={(e) => handleInputChange('platforms', e.target.value)}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => {
                            const platform = platformOptions.find(p => p.value === value);
                            return (
                              <Chip
                                key={value}
                                label={platform?.label || value}
                                size="small"
                              />
                            );
                          })}
                        </Box>
                      )}
                    >
                      {platformOptions.map((platform) => (
                        <MenuItem key={platform.value} value={platform.value}>
                          <Typography>{platform.label}</Typography>
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.platforms && <FormHelperText>{errors.platforms}</FormHelperText>}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={content.status || CONTENT_STATUS.DRAFT}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                    >
                      {statusOptions.map((status) => (
                        <MenuItem key={status.value} value={status.value}>
                          {status.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            )}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button
            onClick={handleSave}
            color="primary"
            variant="contained"
            startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
            disabled={saving || !isValid}
          >
            {saving ? 'Saving...' : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>
    </ErrorBoundary>
  );
}));

// Set display name for debugging
ContentDetailDialog.displayName = 'ContentDetailDialog';

// Comprehensive PropTypes validation
ContentDetailDialog.propTypes = {
  // Basic props
  /** Whether the dialog is open */
  open: PropTypes.bool,
  /** Callback to close the dialog */
  onClose: PropTypes.func.isRequired,
  /** ID of content to edit (null for new content) */
  contentId: PropTypes.string,
  /** Initial content data for new content */
  initialContent: PropTypes.shape({
    id: PropTypes.string,
    title: PropTypes.string,
    text_content: PropTypes.string,
    description: PropTypes.string,
    platforms: PropTypes.arrayOf(PropTypes.string),
    tags: PropTypes.arrayOf(PropTypes.string),
    status: PropTypes.oneOf(Object.values(CONTENT_STATUS)),
    type: PropTypes.oneOf(Object.values(CONTENT_TYPES)),
    images: PropTypes.array,
    created_at: PropTypes.string,
    updated_at: PropTypes.string
  }),
  /** Dialog mode */
  mode: PropTypes.oneOf(Object.values(VIEW_MODES)),

  // Callback props
  /** Callback when content is saved */
  onSave: PropTypes.func,
  /** Callback when content is deleted */
  onDelete: PropTypes.func,
  /** Callback when content is scheduled */
  onSchedule: PropTypes.func,
  /** Callback when content is duplicated */
  onDuplicate: PropTypes.func,
  /** Callback when content is shared */
  onShare: PropTypes.func,
  /** Callback when content is archived */
  onArchive: PropTypes.func,
  /** Callback when content is published */
  onPublish: PropTypes.func,
  /** Callback when analytics is viewed */
  onAnalytics: PropTypes.func,
  /** Callback when content changes */
  onContentChange: PropTypes.func,
  /** Callback when validation state changes */
  onValidationChange: PropTypes.func,

  // Enhanced props
  /** User subscription plan */
  userPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,
  /** Enable auto-save functionality */
  enableAutoSave: PropTypes.bool,
  /** Enable undo/redo history */
  enableHistory: PropTypes.bool,
  /** Enable real-time preview */
  enablePreview: PropTypes.bool,

  // Testing props
  /** Test identifier */
  testId: PropTypes.string,

  // Accessibility props
  /** Accessibility label */
  ariaLabel: PropTypes.string,
  /** Announce changes to screen readers */
  announceChanges: PropTypes.bool
};

// Default props
ContentDetailDialog.defaultProps = {
  open: false,
  contentId: null,
  initialContent: null,
  mode: VIEW_MODES.EDIT,
  userPlan: 'creator',
  enableAnalytics: true,
  enableAccessibility: true,
  enableAutoSave: true,
  enableHistory: true,
  enablePreview: true,
  testId: 'content-detail-dialog',
  announceChanges: true
};

export default ContentDetailDialog;

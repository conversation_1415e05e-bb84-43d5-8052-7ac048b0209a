# Adaptive Text Margins for Image Generation

## Overview

The system now supports **adaptive text margins** that automatically adjust based on image dimensions and aspect ratios. This ensures optimal text placement for headlines, CTAs, and branding elements across different image formats.

## ✅ **ANSWER: YES** - Text Margins Are Now Implemented

Your system **now ensures** that text margins like `"top": "10%", "bottom": "8%", "sides": "6%"` are properly handled and adapted based on image size.

## How It Works

### 1. **Size-Aware Margin Calculation**

The system analyzes image dimensions and adjusts margins accordingly:

```python
# Ultra-wide (1792x1024) - Reduces vertical margins, increases horizontal
"top": "10%" → "8.0%"     # Reduced for limited vertical space
"bottom": "8%" → "6.4%"   # Reduced for limited vertical space  
"sides": "6%" → "7.2%"    # Increased to utilize extra width

# Portrait (1024x1792) - Keeps vertical margins, reduces horizontal
"top": "10%" → "10%"      # Preserved for ample vertical space
"bottom": "8%" → "8%"     # Preserved for ample vertical space
"sides": "6%" → "4.2%"    # Reduced for limited horizontal space
```

### 2. **Aspect Ratio Categories**

- **Ultra-wide** (≥1.6:1): Optimized for horizontal text flow
- **Wide** (≥1.3:1): Emphasis on landscape orientation  
- **Square** (~1:1): Balanced margin distribution
- **Portrait** (≤0.8:1): Vertical text stacking, mobile-friendly

### 3. **Integration Points**

The text margins are processed at multiple levels:

1. **API Level**: `GenerateImageRequest` with branding data
2. **Service Level**: `generate_image_service()` passes size to branding enhancement
3. **Enhancement Level**: `_enhance_prompt_with_branding()` includes size parameter
4. **Composition Level**: `_build_composition_enhancement()` processes text margins
5. **OpenAI Level**: Enhanced prompt sent to DALL-E

## Example Usage

### Your Complex Prompt

```json
{
  "prompt": "Mission control dashboard: Central ACE Social logo...",
  "size": "1792x1024",
  "branding": {
    "imageComposition": {
      "layout": "control-hierarchy",
      "subjectPosition": "central-command", 
      "textMargins": {
        "top": "10%",
        "bottom": "8%",
        "sides": "6%"
      }
    }
  }
}
```

### Enhanced Output

For `1792x1024` (ultra-wide), the system generates:

```
Text placement zones: Reserve 8.0% clear space at the top edge for headline text placement, Reserve 6.4% clear space at the bottom edge for CTA and branding, Maintain 7.2% clear margins on left and right sides for text readability, Optimize for ultra-wide format with horizontal text flow and side-by-side content areas. Ensure these areas have high contrast backgrounds for optimal text legibility
```

## Implementation Details

### Core Functions

1. **`_build_adaptive_text_margins()`**
   - Parses image dimensions
   - Calculates aspect ratio
   - Adjusts margins based on format
   - Generates size-specific guidance

2. **`_adjust_margin_for_aspect_ratio()`**
   - Applies multipliers to margin percentages
   - Clamps values between 2% and 25%
   - Handles invalid input gracefully

3. **`_get_size_specific_text_guidance()`**
   - Provides format-specific layout advice
   - Optimizes for different use cases
   - Considers mobile vs desktop viewing

### Safety Features

- **Graceful Fallbacks**: Invalid sizes default to 1024x1024
- **Boundary Clamping**: Margins stay within 2%-25% range
- **Error Handling**: Malformed input doesn't break generation
- **Backward Compatibility**: Works without text margins specified

## Supported Image Sizes

| Size | Aspect Ratio | Margin Adjustments |
|------|-------------|-------------------|
| 1792x1024 | 1.75:1 | Reduced vertical, increased horizontal |
| 1600x900 | 1.78:1 | Reduced vertical, increased horizontal |
| 1024x1024 | 1:1 | Original margins preserved |
| 1024x1792 | 0.57:1 | Original vertical, reduced horizontal |
| 512x512 | 1:1 | Original margins preserved |

## Benefits

1. **Optimal Text Legibility**: Ensures adequate space for text overlay
2. **Format-Specific Optimization**: Adapts to viewing context
3. **Professional Layout**: Maintains design principles across formats
4. **Brand Consistency**: Preserves visual hierarchy
5. **Mobile-Friendly**: Optimizes for different screen orientations

## Testing

Run the example to see text margins in action:

```bash
cd backend
python examples/text_margins_example.py
```

This demonstrates how your complex mission control dashboard prompt gets enhanced with adaptive text margins for different image sizes.

## API Integration

The text margins work seamlessly with existing API endpoints:

- `POST /api/content/generate-image`
- Image manipulation endpoints
- Campaign content generation
- General content generation

All branding-enabled image generation now automatically includes adaptive text margins when specified in the `imageComposition.textMargins` field.

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, useMemo, memo, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Divider,
  Chip,
  CircularProgress,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  alpha,
  useMediaQuery,
  Button,
  Menu,
  MenuItem,

  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  AlertTitle,
  Grid,


  Skeleton,
  Fade
} from '@mui/material';
import {
  SentimentSatisfied as SentimentSatisfiedIcon,
  SentimentDissatisfied as SentimentDissatisfiedIcon,
  SentimentNeutral as SentimentNeutralIcon,
  SentimentVerySatisfied as SentimentVerySatisfiedIcon,
  SentimentVeryDissatisfied as SentimentVeryDissatisfiedIcon,
  FormatQuote as FormatQuoteIcon,
  Lightbulb as LightbulbIcon,
  ThumbUp as ThumbUpIcon,
  Warning as WarningIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Analytics as AnalyticsIcon,

  Download as ExportIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,
  Timeline as TimelineIcon,
  Insights as InsightsIcon,

} from '@mui/icons-material';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip as RechartsTooltip,

} from 'recharts';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import { CustomCard, CustomCardHeader, CustomCardContent } from '../common';
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Sentiment analysis types with enhanced configurations
const SENTIMENT_TYPES = {
  EMOTION_DETECTION: {
    id: 'emotion-detection',
    name: 'Emotion Detection',
    icon: PsychologyIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Detect emotions in content and comments',
    subscriptionLimits: {
      creator: { dataPoints: 100, emotions: 3, realTime: false },
      accelerator: { dataPoints: 500, emotions: 7, realTime: true },
      dominator: { dataPoints: -1, emotions: -1, realTime: true, advanced: true }
    }
  },
  OPINION_MINING: {
    id: 'opinion-mining',
    name: 'Opinion Mining',
    icon: InsightsIcon,
    color: ACE_COLORS.YELLOW,
    description: 'Extract opinions and attitudes from text',
    subscriptionLimits: {
      creator: { dataPoints: 50, aspects: 2, realTime: false },
      accelerator: { dataPoints: 300, aspects: 5, realTime: true },
      dominator: { dataPoints: -1, aspects: -1, realTime: true, advanced: true }
    }
  },
  ASPECT_SENTIMENT: {
    id: 'aspect-sentiment',
    name: 'Aspect-Based Sentiment',
    icon: AnalyticsIcon,
    color: ACE_COLORS.DARK,
    description: 'Analyze sentiment for specific aspects',
    subscriptionLimits: {
      creator: { dataPoints: 0, aspects: 0, realTime: false },
      accelerator: { dataPoints: 200, aspects: 3, realTime: true },
      dominator: { dataPoints: -1, aspects: -1, realTime: true, advanced: true }
    }
  },
  TREND_ANALYSIS: {
    id: 'trend-analysis',
    name: 'Trend Analysis',
    icon: TimelineIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Track sentiment trends over time',
    subscriptionLimits: {
      creator: { dataPoints: 0, timeRange: 0, realTime: false },
      accelerator: { dataPoints: 150, timeRange: 30, realTime: true },
      dominator: { dataPoints: -1, timeRange: -1, realTime: true, advanced: true }
    }
  },
  COMPARATIVE_SENTIMENT: {
    id: 'comparative-sentiment',
    name: 'Comparative Sentiment',
    icon: TrendingUpIcon,
    color: ACE_COLORS.YELLOW,
    description: 'Compare sentiment across different content',
    subscriptionLimits: {
      creator: { dataPoints: 0, comparisons: 0, realTime: false },
      accelerator: { dataPoints: 0, comparisons: 0, realTime: false },
      dominator: { dataPoints: -1, comparisons: -1, realTime: true, advanced: true }
    }
  }
};



/**
 * Enhanced SentimentAnalysisWidget Component - Enterprise-grade sentiment analysis widget
 * Features: Plan-based sentiment limitations, real-time sentiment monitoring, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced sentiment insights and interactive sentiment exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} [props.data] - Sentiment analysis data
 * @param {boolean} [props.loading=false] - Loading state
 * @param {string} [props.title='Sentiment Analysis'] - Widget title
 * @param {Function} [props.onExpand] - Expand callback
 * @param {string} [props.sentimentType='emotion-detection'] - Sentiment analysis type
 * @param {string} [props.contentVariant='posts'] - Content variant
 * @param {boolean} [props.enableFiltering=false] - Enable sentiment filtering
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onSentimentTypeChange] - Sentiment type change callback
 * @param {Function} [props.onFilter] - Filter callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Object} [props.filterConfig] - Filter configuration
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const SentimentAnalysisWidget = memo(forwardRef(({
  data,
  loading = false,
  title = "Sentiment Analysis",

  sentimentType = 'emotion-detection',
  contentVariant = 'posts',

  enableExport = false,
  realTimeUpdates = false,
  onSentimentTypeChange,

  onExport,
  onRefresh,

  customization = {},
  className = '',
  style = {},
  testId = 'sentiment-analysis-widget',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading || subscriptionLoading,
    refreshing: false,
    showFilterMenu: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showSettingsDialog: false,
    currentSentimentType: sentimentType,
    animationKey: 0,
    errors: {},
    // Sentiment state
    selectedTimeRange: '7d',
    selectedEmotions: [],
    sentimentOptimizing: false
  });

  // Sentiment data state
  const [sentimentData, setSentimentData] = useState({
    raw: data || null,
    processed: null,
    trends: null,
    insights: null
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);

  /**
   * Enhanced plan-based sentiment validation - Production Ready
   */
  const validateSentimentFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canViewSentiment: false,
        hasSentimentAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { dataPoints: 0, emotions: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based sentiment limits
    const planLimits = {
      creator: {
        dataPoints: 100,
        emotions: 3,
        features: ['basic_sentiment'],
        realTime: false,
        export: false,
        filtering: false,
        sentimentTypes: ['emotion-detection'],
        customModels: false
      },
      accelerator: {
        dataPoints: 500,
        emotions: 7,
        features: ['basic_sentiment', 'advanced_sentiment', 'sentiment_filtering'],
        realTime: true,
        export: true,
        filtering: true,
        sentimentTypes: ['emotion-detection', 'opinion-mining', 'aspect-sentiment', 'trend-analysis'],
        customModels: false
      },
      dominator: {
        dataPoints: -1,
        emotions: -1,
        features: ['basic_sentiment', 'advanced_sentiment', 'sentiment_filtering', 'ai_sentiment_optimization'],
        realTime: true,
        export: true,
        filtering: true,
        sentimentTypes: ['emotion-detection', 'opinion-mining', 'aspect-sentiment', 'trend-analysis', 'comparative-sentiment'],
        customModels: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = sentimentData.processed ? 1 : 0;
    const limit = currentPlanLimits.dataPoints;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewSentiment: true,
      hasSentimentAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, sentimentData.processed]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const sentimentLimits = validateSentimentFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasFiltering: sentimentLimits.planLimits.filtering,
      hasExport: sentimentLimits.planLimits.export,
      hasRealTime: sentimentLimits.planLimits.realTime,
      hasCustomModels: sentimentLimits.planLimits.customModels,
      maxDataPoints: sentimentLimits.planLimits.dataPoints,
      maxEmotions: sentimentLimits.planLimits.emotions,
      availableSentimentTypes: sentimentLimits.planLimits.sentimentTypes,
      availableFeatures: sentimentLimits.planLimits.features,
      refreshInterval: sentimentLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateSentimentFeatures]);

  /**
   * Enhanced sentiment type validation - Production Ready
   */
  const isSentimentTypeAvailable = useCallback((type) => {
    return subscriptionFeatures.availableSentimentTypes.includes(type);
  }, [subscriptionFeatures.availableSentimentTypes]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `${title} sentiment analysis widget`,
      'aria-description': ariaDescription || `Interactive ${sentimentType} sentiment analysis widget displaying sentiment metrics`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, title, sentimentType, realTimeUpdates]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Sentiment analysis refreshed successfully');
      announceToScreenReader('Sentiment analysis has been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh sentiment analysis: ${error.message}`);
      announceToScreenReader('Failed to refresh sentiment analysis');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced sentiment type change handler - Production Ready
   */
  const handleSentimentTypeChange = useCallback((newType) => {
    if (!isSentimentTypeAvailable(newType)) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, currentSentimentType: newType }));

    if (onSentimentTypeChange) {
      onSentimentTypeChange(newType);
    }

    announceToScreenReader(`Sentiment analysis type changed to ${newType}`);
  }, [isSentimentTypeAvailable, onSentimentTypeChange, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'csv') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, sentimentData.processed);
      }

      showSuccessNotification(`Sentiment analysis exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Sentiment analysis has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export sentiment analysis: ${error.message}`);
      announceToScreenReader('Failed to export sentiment analysis');
    }
  }, [subscriptionFeatures.hasExport, sentimentData.processed, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportSentiment: handleExport,
    changeSentimentType: handleSentimentTypeChange,
    getSentimentData: () => sentimentData.processed,
    getSentimentLimits: validateSentimentFeatures,
    focus: () => setFocusToElement(document.querySelector(`[data-testid="${testId}"]`)),
    announce: (message) => announceToScreenReader(message)
  }), [
    sentimentData.processed,
    validateSentimentFeatures,
    handleRefresh,
    handleExport,
    handleSentimentTypeChange,
    setFocusToElement,
    announceToScreenReader,
    testId
  ]);

  /**
   * Enhanced data processing effect - Production Ready
   */
  useEffect(() => {
    if (data) {
      setSentimentData(prev => ({
        ...prev,
        raw: data,
        processed: data
      }));
    }
  }, [data]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced menu handlers - Production Ready
   */

  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced sentiment analysis features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Advanced sentiment types',
        'Real-time sentiment monitoring',
        'Sentiment filtering and search',
        'Data export capabilities',
        'AI-powered sentiment insights',
        'Custom sentiment models'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  /**
   * Enhanced sentiment icon with ACE Social colors - Production Ready
   */
  const getSentimentIcon = useCallback((sentiment) => {
    switch (sentiment.toLowerCase()) {
      case 'very_positive':
        return <SentimentVerySatisfiedIcon sx={{ color: '#2E7D32' }} />;
      case 'positive':
        return <SentimentSatisfiedIcon sx={{ color: '#4CAF50' }} />;
      case 'negative':
        return <SentimentDissatisfiedIcon sx={{ color: '#F44336' }} />;
      case 'very_negative':
        return <SentimentVeryDissatisfiedIcon sx={{ color: '#C62828' }} />;
      default:
        return <SentimentNeutralIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6) }} />;
    }
  }, []);



  /**
   * Enhanced sentiment data preparation - Production Ready
   */
  const prepareSentimentData = useCallback(() => {
    if (!sentimentData.processed) return [];

    // Check if we have the new sentiment distribution format
    if (sentimentData.processed.sentiment_distribution) {
      return [
        { name: 'Very Positive', value: sentimentData.processed.sentiment_distribution.very_positive || 0, color: '#2E7D32' },
        { name: 'Positive', value: sentimentData.processed.sentiment_distribution.positive || 0, color: '#4CAF50' },
        { name: 'Neutral', value: sentimentData.processed.sentiment_distribution.neutral || 0, color: alpha(ACE_COLORS.DARK, 0.6) },
        { name: 'Negative', value: sentimentData.processed.sentiment_distribution.negative || 0, color: '#F44336' },
        { name: 'Very Negative', value: sentimentData.processed.sentiment_distribution.very_negative || 0, color: '#C62828' },
      ];
    }

    // Fallback to old format
    return [
      { name: 'Positive', value: sentimentData.processed.positive_comments || 0, color: '#4CAF50' },
      { name: 'Neutral', value: sentimentData.processed.neutral_comments || 0, color: alpha(ACE_COLORS.DARK, 0.6) },
      { name: 'Negative', value: sentimentData.processed.negative_comments || 0, color: '#F44336' },
    ];
  }, [sentimentData.processed]);

  const chartSentimentData = prepareSentimentData();

  /**
   * Enhanced custom tooltip for charts - Production Ready
   */
  const CustomTooltip = useCallback(({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            backgroundColor: ACE_COLORS.WHITE,
            p: 2,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
            borderRadius: 2,
            boxShadow: `0 4px 12px ${alpha(ACE_COLORS.DARK, 0.1)}`
          }}
        >
          <Typography variant="body2" sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
            {`${payload[0].name}: ${payload[0].value} ${contentVariant}`}
          </Typography>
        </Box>
      );
    }

    return null;
  }, [contentVariant]);

  // Main render condition checks
  if (state.loading && !sentimentData.processed) {
    return (
      <ErrorBoundary
        fallback={
          <CustomCard variant="glass" sx={{ height: '100%', p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load sentiment analysis widget
            </Typography>
          </CustomCard>
        }
      >
        <CustomCard
          variant="glass"
          sx={{
            height: '100%',
            ...customization
          }}
          className={className}
          style={style}
          data-testid={testId}
          {...getAccessibilityProps()}
        >
          <CustomCardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PsychologyIcon sx={{ color: ACE_COLORS.PURPLE }} />
                <Typography variant="h6" component="span" sx={{ color: ACE_COLORS.DARK }}>
                  {title}
                </Typography>
              </Box>
            }
            divider={true}
          />
          <CustomCardContent>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Skeleton variant="circular" width={60} height={60} sx={{ mx: 'auto', mb: 2 }} />
                <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />
                <Skeleton variant="text" width="40%" height={16} />
              </Box>
            </Box>
          </CustomCardContent>
        </CustomCard>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <CustomCard variant="glass" sx={{ height: '100%', p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="error">
            Unable to load sentiment analysis widget
          </Typography>
        </CustomCard>
      }
    >
      <CustomCard
        variant="glass"
        sx={{
          height: '100%',
          ...customization
        }}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        {/* Enhanced Card Header with Sentiment Analysis Features */}
        <CustomCardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PsychologyIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span" sx={{ color: ACE_COLORS.DARK }}>
                {title}
              </Typography>
              {subscriptionFeatures.hasRealTime && realTimeUpdates && (
                <Chip
                  label="LIVE"
                  size="small"
                  sx={{
                    backgroundColor: alpha('#4CAF50', 0.1),
                    color: '#4CAF50',
                    fontWeight: 600,
                    fontSize: '0.7rem'
                  }}
                />
              )}
            </Box>
          }
          action={
            <Box sx={{ display: 'flex', gap: 0.5 }}>
              {/* Sentiment Type Selector */}
              {subscriptionFeatures.hasFiltering && (
                <Tooltip title="Sentiment Settings">
                  <IconButton
                    size="small"
                    onClick={settingsAnchorEl ? () => setSettingsAnchorEl(null) : (e) => setSettingsAnchorEl(e.currentTarget)}
                    sx={{
                      color: ACE_COLORS.PURPLE,
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                      }
                    }}
                    aria-label="Change sentiment settings"
                  >
                    <SettingsIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}



              {/* Export Button */}
              {enableExport && (
                <Tooltip title="Export Sentiment">
                  <IconButton
                    size="small"
                    onClick={handleExportMenuOpen}
                    sx={{
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.1)
                      }
                    }}
                    aria-label="Export sentiment data"
                  >
                    <ExportIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}

              {/* Refresh Button */}
              {onRefresh && (
                <Tooltip title="Refresh Sentiment">
                  <IconButton
                    size="small"
                    onClick={handleRefresh}
                    disabled={state.refreshing}
                    sx={{
                      color: ACE_COLORS.PURPLE,
                      '&:hover': {
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                      }
                    }}
                    aria-label="Refresh sentiment analysis"
                  >
                    {state.refreshing ? (
                      <CircularProgress size={16} />
                    ) : (
                      <RefreshIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          }
          divider={true}
        />

        {/* Real-time Updates Indicator */}
        {realTimeUpdates && subscriptionFeatures.hasRealTime && (
          <Box sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 1,
            backgroundColor: alpha(theme.palette.success.main, 0.1),
            borderRadius: 1,
            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
            zIndex: 10
          }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: theme.palette.success.main,
                ...(!prefersReducedMotion && {
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 }
                  }
                })
              }}
              aria-hidden="true"
            />
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
              Live
            </Typography>
          </Box>
        )}

        <CustomCardContent>
          {state.loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Skeleton variant="circular" width={60} height={60} sx={{ mx: 'auto', mb: 2 }} />
                <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />
                <Skeleton variant="text" width="40%" height={16} />
              </Box>
            </Box>
          ) : sentimentData.processed ? (
            <Fade in timeout={500}>
              <Box>
                {/* Enhanced Overall Sentiment Section */}
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Box sx={{ mr: 2 }}>
                    {getSentimentIcon(sentimentData.processed.overall_sentiment)}
                  </Box>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                      Overall Sentiment: {sentimentData.processed.overall_sentiment?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Sentiment Score: {sentimentData.processed.sentiment_score?.toFixed(2)}
                    </Typography>
                    {sentimentData.processed.sentiment_trend && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        {sentimentData.processed.sentiment_trend === 'improving' ? (
                          <TrendingUpIcon sx={{ color: '#4CAF50', fontSize: 16 }} />
                        ) : sentimentData.processed.sentiment_trend === 'declining' ? (
                          <TrendingDownIcon sx={{ color: '#F44336', fontSize: 16 }} />
                        ) : null}
                        <Typography variant="body2" color="text.secondary">
                          Trend: {sentimentData.processed.sentiment_trend.charAt(0).toUpperCase() + sentimentData.processed.sentiment_trend.slice(1)}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                  {subscriptionFeatures.hasCustomModels && (
                    <Chip
                      label="AI Enhanced"
                      size="small"
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        color: ACE_COLORS.PURPLE,
                        fontWeight: 600
                      }}
                    />
                  )}
                </Box>

                <Divider sx={{ my: 2 }} />

                {/* Enhanced Sentiment Distribution */}
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                  Sentiment Distribution
                </Typography>

                <ResponsiveContainer width="100%" height={isMobile ? 180 : 220}>
                  <PieChart>
                    <Pie
                      data={chartSentimentData}
                      cx="50%"
                      cy="50%"
                      innerRadius={isMobile ? 25 : 35}
                      outerRadius={isMobile ? 55 : 70}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {chartSentimentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip content={<CustomTooltip />} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>

                <Divider sx={{ my: 2 }} />

                {/* Enhanced Common Themes */}
                <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                  Common Themes
                </Typography>

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {sentimentData.processed.common_themes && sentimentData.processed.common_themes.map((theme, index) => (
                    <Chip
                      key={index}
                      label={theme}
                      size="small"
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        color: ACE_COLORS.PURPLE,
                        '&:hover': {
                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                        }
                      }}
                    />
                  ))}
                  {(!sentimentData.processed.common_themes || sentimentData.processed.common_themes.length === 0) && (
                    <Typography variant="body2" color="text.secondary">
                      No common themes identified
                    </Typography>
                  )}
                </Box>

                {/* Enhanced Key Concerns Section */}
                {sentimentData.processed.key_concerns && sentimentData.processed.key_concerns.length > 0 && (
                  <>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                      <WarningIcon sx={{ color: '#F44336', verticalAlign: 'middle', mr: 1 }} />
                      Key Concerns
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {sentimentData.processed.key_concerns.map((concern, index) => (
                        <Chip
                          key={index}
                          label={concern}
                          size="small"
                          sx={{
                            backgroundColor: alpha('#F44336', 0.1),
                            color: '#F44336',
                            border: `1px solid ${alpha('#F44336', 0.3)}`,
                            '&:hover': {
                              backgroundColor: alpha('#F44336', 0.2)
                            }
                          }}
                        />
                      ))}
                    </Box>
                  </>
                )}

                {/* Enhanced Positive Aspects Section */}
                {sentimentData.processed.positive_aspects && sentimentData.processed.positive_aspects.length > 0 && (
                  <>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                      <ThumbUpIcon sx={{ color: '#4CAF50', verticalAlign: 'middle', mr: 1 }} />
                      Positive Aspects
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                      {sentimentData.processed.positive_aspects.map((aspect, index) => (
                        <Chip
                          key={index}
                          label={aspect}
                          size="small"
                          sx={{
                            backgroundColor: alpha('#4CAF50', 0.1),
                            color: '#4CAF50',
                            border: `1px solid ${alpha('#4CAF50', 0.3)}`,
                            '&:hover': {
                              backgroundColor: alpha('#4CAF50', 0.2)
                            }
                          }}
                        />
                      ))}
                    </Box>
                  </>
                )}

                {/* Enhanced Actionable Insights Section */}
                {sentimentData.processed.actionable_insights && sentimentData.processed.actionable_insights.length > 0 && (
                  <>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                      <LightbulbIcon sx={{ color: ACE_COLORS.YELLOW, verticalAlign: 'middle', mr: 1 }} />
                      Actionable Insights
                    </Typography>
                    <List dense>
                      {sentimentData.processed.actionable_insights.map((insight, index) => (
                        <ListItem key={index} disableGutters>
                          <ListItemIcon sx={{ minWidth: 36 }}>
                            <LightbulbIcon sx={{ color: ACE_COLORS.YELLOW }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={insight}
                            primaryTypographyProps={{ variant: 'body2', color: ACE_COLORS.DARK }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </>
                )}

                <Divider sx={{ my: 2 }} />

                {/* Enhanced Sample Comments Section */}
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {sentimentData.processed.sample_comments ? (
                    // New format with sample_comments object
                    <>
                      {/* Very Positive Comments */}
                      {sentimentData.processed.sample_comments.very_positive && sentimentData.processed.sample_comments.very_positive.length > 0 && (
                        <Box>
                          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                            <SentimentVerySatisfiedIcon sx={{ color: '#2E7D32', verticalAlign: 'middle', mr: 1 }} />
                            Very Positive Comments
                          </Typography>
                          <List dense>
                            {sentimentData.processed.sample_comments.very_positive.slice(0, subscriptionFeatures.maxDataPoints === -1 ? 5 : 2).map((comment, index) => (
                              <ListItem key={index} disableGutters>
                                <ListItemText
                                  primary={
                                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                                      <FormatQuoteIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.4), fontSize: 16, transform: 'rotate(180deg)', mt: 0.5 }} />
                                      <Typography variant="body2" sx={{ flex: 1, fontStyle: 'italic', color: ACE_COLORS.DARK }}>
                                        {comment}
                                      </Typography>
                                      <FormatQuoteIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.4), fontSize: 16, mt: 0.5 }} />
                                    </Box>
                                  }
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}

                      {/* Negative Comments */}
                      {sentimentData.processed.sample_comments.negative && sentimentData.processed.sample_comments.negative.length > 0 && (
                        <Box>
                          <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                            <SentimentDissatisfiedIcon sx={{ color: '#F44336', verticalAlign: 'middle', mr: 1 }} />
                            Negative Comments
                          </Typography>
                          <List dense>
                            {sentimentData.processed.sample_comments.negative.slice(0, subscriptionFeatures.maxDataPoints === -1 ? 5 : 2).map((comment, index) => (
                              <ListItem key={index} disableGutters>
                                <ListItemText
                                  primary={
                                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                                      <FormatQuoteIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.4), fontSize: 16, transform: 'rotate(180deg)', mt: 0.5 }} />
                                      <Typography variant="body2" sx={{ flex: 1, fontStyle: 'italic', color: ACE_COLORS.DARK }}>
                                        {comment}
                                      </Typography>
                                      <FormatQuoteIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.4), fontSize: 16, mt: 0.5 }} />
                                    </Box>
                                  }
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}
                    </>
                  ) : (
                    // Fallback to old format
                    <Box>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                        <SentimentSatisfiedIcon sx={{ color: '#4CAF50', verticalAlign: 'middle', mr: 1 }} />
                        Sample Comments
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        No sample comments available
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </Fade>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <Typography variant="body2" color="text.secondary">
                No sentiment analysis data available
              </Typography>
            </Box>
          )}

          {/* Sentiment Type Menu */}
          <Menu
            anchorEl={settingsAnchorEl}
            open={Boolean(settingsAnchorEl)}
            onClose={() => setSettingsAnchorEl(null)}
            slotProps={{
              paper: {
                sx: {
                  mt: 1,
                  minWidth: 280,
                  boxShadow: theme.shadows[8],
                  border: `1px solid ${theme.palette.divider}`
                }
              }
            }}
          >
            {Object.values(SENTIMENT_TYPES).map((sentimentTypeConfig) => (
              <MenuItem
                key={sentimentTypeConfig.id}
                onClick={() => {
                  handleSentimentTypeChange(sentimentTypeConfig.id);
                  setSettingsAnchorEl(null);
                }}
                disabled={!isSentimentTypeAvailable(sentimentTypeConfig.id)}
                sx={{
                  backgroundColor: state.currentSentimentType === sentimentTypeConfig.id
                    ? alpha(ACE_COLORS.PURPLE, 0.1)
                    : 'transparent'
                }}
              >
                <ListItemIcon>
                  <sentimentTypeConfig.icon fontSize="small" sx={{ color: sentimentTypeConfig.color }} />
                </ListItemIcon>
                <ListItemText
                  primary={sentimentTypeConfig.name}
                  secondary={sentimentTypeConfig.description}
                />
                {!isSentimentTypeAvailable(sentimentTypeConfig.id) && (
                  <UpgradeIcon fontSize="small" sx={{ color: theme.palette.text.disabled, ml: 1 }} />
                )}
              </MenuItem>
            ))}
          </Menu>

          {/* Export Menu */}
          <Menu
            anchorEl={exportAnchorEl}
            open={state.showExportMenu}
            onClose={handleExportMenuClose}
            slotProps={{
              paper: {
                sx: {
                  mt: 1,
                  minWidth: 180,
                  boxShadow: theme.shadows[8],
                  border: `1px solid ${theme.palette.divider}`
                }
              }
            }}
          >
            <MenuItem onClick={() => {
              handleExport('csv');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export as CSV</ListItemText>
            </MenuItem>

            <MenuItem onClick={() => {
              handleExport('json');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export as JSON</ListItemText>
            </MenuItem>
          </Menu>

          {/* Upgrade Dialog */}
          <Dialog
            open={state.showUpgradeDialog}
            onClose={handleUpgradeDialogClose}
            maxWidth="md"
            fullWidth
            slotProps={{
              paper: {
                sx: {
                  borderRadius: 2,
                  boxShadow: theme.shadows[16]
                }
              }
            }}
          >
            <DialogTitle sx={{
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
              borderBottom: `1px solid ${theme.palette.divider}`
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
                <Typography variant="h6" component="span">
                  {getUpgradeDialogContent().title}
                </Typography>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ pt: 3 }}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {getUpgradeDialogContent().content}
                </Typography>

                <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                  Unlock Advanced Sentiment Features:
                </Typography>

                <Grid container spacing={1}>
                  {getUpgradeDialogContent().features.map((feature, index) => (
                    <Grid item xs={12} sm={6} key={index}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                        <Typography variant="body2">{feature}</Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Box>

              <Alert
                severity="info"
                sx={{
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                  border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
                }}
              >
                <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
                Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced sentiment analysis features.
              </Alert>
            </DialogContent>
            <DialogActions sx={{ p: 3, pt: 2 }}>
              <Button
                onClick={handleUpgradeDialogClose}
                sx={{ color: theme.palette.text.secondary }}
              >
                Maybe Later
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: ACE_COLORS.PURPLE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                  }
                }}
                startIcon={<UpgradeIcon />}
              >
                Upgrade Now
              </Button>
            </DialogActions>
          </Dialog>
        </CustomCardContent>
      </CustomCard>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
SentimentAnalysisWidget.propTypes = {
  // Core props
  data: PropTypes.object,
  loading: PropTypes.bool,
  title: PropTypes.string,


  // Enhanced sentiment props
  sentimentType: PropTypes.oneOf(['emotion-detection', 'opinion-mining', 'aspect-sentiment', 'trend-analysis', 'comparative-sentiment']),
  contentVariant: PropTypes.oneOf(['posts', 'comments', 'campaigns', 'competitor']),

  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onSentimentTypeChange: PropTypes.func,

  onExport: PropTypes.func,
  onRefresh: PropTypes.func,

  customization: PropTypes.object,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

SentimentAnalysisWidget.defaultProps = {
  loading: false,
  title: "Sentiment Analysis",
  sentimentType: 'emotion-detection',
  contentVariant: 'posts',

  enableExport: false,
  realTimeUpdates: false,
  customization: {},
  className: '',
  style: {},
  testId: 'sentiment-analysis-widget'
};

// Display name for debugging
SentimentAnalysisWidget.displayName = 'SentimentAnalysisWidget';

export default SentimentAnalysisWidget;

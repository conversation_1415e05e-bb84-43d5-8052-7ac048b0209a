// @since 2024-1-1 to 2025-25-7
import React from 'react';
import PropTypes from 'prop-types';
import { Box, useTheme } from '@mui/material';

// Logo assets are served from public folder

/**
 * Logo Component for Admin App - Centralized logo management for the ACE Social Admin Panel
 * 
 * Features:
 * - Supports both main logo and favicon variants
 * - Responsive sizing with Material-UI theme integration
 * - Maintains aspect ratio and accessibility standards
 * - Optimized SVG format for scalability and performance
 * - WCAG 2.1 AA compliant with proper alt text
 * 
 * @component
 */
const Logo = ({
  variant = 'main',
  size = 'medium',
  maxHeight,
  maxWidth,
  alt,
  sx = {},
  onClick,
  ...props
}) => {
  const theme = useTheme();

  // Logo source mapping - using public URLs
  const logoSources = {
    main: '/logo.svg',
    favicon: '/favicon.svg',
    icon: '/favicon.svg' // Alias for favicon
  };

  // Size presets following 8px grid system
  const sizePresets = {
    small: {
      maxHeight: 32,
      maxWidth: 120
    },
    medium: {
      maxHeight: 48,
      maxWidth: 180
    },
    large: {
      maxHeight: 64,
      maxWidth: 240
    },
    xlarge: {
      maxHeight: 80,
      maxWidth: 300
    }
  };

  // Get logo source
  const logoSrc = logoSources[variant] || logoSources.main;

  // Get size configuration
  const sizeConfig = typeof size === 'string' ? sizePresets[size] : size;
  const finalMaxHeight = maxHeight || sizeConfig?.maxHeight || 48;
  const finalMaxWidth = maxWidth || sizeConfig?.maxWidth || 180;

  // Generate alt text
  const altText = alt || `ACE Social Admin ${variant === 'main' ? 'Logo' : 'Icon'}`;

  // Base styles following Material-UI design system
  const baseStyles = {
    display: 'inline-block',
    maxHeight: finalMaxHeight,
    maxWidth: finalMaxWidth,
    width: 'auto',
    height: 'auto',
    objectFit: 'contain',
    transition: theme.transitions.create(['transform', 'opacity'], {
      duration: theme.transitions.duration.short,
    }),
    cursor: onClick ? 'pointer' : 'default',
    // Hover effects for interactive logos
    '&:hover': onClick ? {
      transform: 'scale(1.05)',
      opacity: 0.9,
    } : {},
    // Focus styles for accessibility
    '&:focus': onClick ? {
      outline: `2px solid ${theme.palette.primary.main}`,
      outlineOffset: 2,
      borderRadius: theme.spacing(0.5),
    } : {},
    ...sx
  };

  return (
    <Box
      component="img"
      src={logoSrc}
      alt={altText}
      sx={baseStyles}
      onClick={onClick}
      role={onClick ? 'button' : 'img'}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick(e);
        }
      } : undefined}
      {...props}
    />
  );
};

Logo.propTypes = {
  /** Logo variant to display */
  variant: PropTypes.oneOf(['main', 'favicon', 'icon']),
  /** Predefined size or custom size object */
  size: PropTypes.oneOfType([
    PropTypes.oneOf(['small', 'medium', 'large', 'xlarge']),
    PropTypes.object
  ]),
  /** Maximum height in pixels */
  maxHeight: PropTypes.number,
  /** Maximum width in pixels */
  maxWidth: PropTypes.number,
  /** Alt text for accessibility */
  alt: PropTypes.string,
  /** Additional styles */
  sx: PropTypes.object,
  /** Click handler for interactive logos */
  onClick: PropTypes.func
};

export default Logo;

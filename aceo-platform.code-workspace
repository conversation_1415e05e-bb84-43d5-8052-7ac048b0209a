{"folders": [{"name": "ACE Social Platform", "path": "."}, {"name": "Backend", "path": "./backend"}, {"name": "Frontend", "path": "./frontend"}, {"name": "Admin App", "path": "./admin-app"}], "settings": {"python.defaultInterpreter": "./backend/venv/Scripts/python.exe", "python.pythonPath": "./backend/venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.analysis.extraPaths": ["./backend", "./backend/app", "./backend/venv/Lib/site-packages"], "python.analysis.autoSearchPaths": true, "python.analysis.typeCheckingMode": "basic", "python.analysis.stubPath": "./backend/venv/Lib/site-packages", "python.analysis.include": ["./backend"], "python.analysis.exclude": ["**/node_modules", "**/__pycache__"], "python.linting.enabled": false, "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["./backend/tests"], "files.associations": {"*.py": "python"}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/node_modules": true, "**/.git": true, "**/.DS_Store": true}}, "extensions": {"recommendations": ["ms-python.python", "ms-python.pylance", "ms-python.flake8", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-typescript-next", "ms-vscode.vscode-json"]}}
/**
 * React hook for add-on functionality
 * Production-ready add-ons management with comprehensive error handling and monitoring
 * Handles feature access checks, usage tracking, billing integration, and add-on management
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';
import { message } from 'antd';
import api from '../api';

// Configuration constants
const CONFIG = {
  // Cache settings
  CACHE_DURATION: 300000, // 5 minutes

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second

  // Usage tracking
  USAGE_BATCH_SIZE: 10,
  USAGE_FLUSH_INTERVAL: 30000, // 30 seconds

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[useAddons] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[useAddons] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Addons Hook Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[useAddons] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Addons Hook Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[useAddons] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Addons Hook Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

export const useAddons = () => {
  const { user } = useAuth();

  // Enhanced state management
  const [catalog, setCatalog] = useState([]);
  const [userAddons, setUserAddons] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [dashboard, setDashboard] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [usageStats, setUsageStats] = useState({});
  const [addons, setAddons] = useState([]);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Refs for caching and batch processing
  const cacheRef = useRef({
    dashboard: null,
    catalog: null,
    userAddons: null,
    timestamp: null
  });
  const usageBatchRef = useRef([]);
  const usageFlushTimeoutRef = useRef(null);

  // Fetch complete add-on dashboard with caching
  const fetchDashboard = useCallback(async (forceRefresh = false) => {
    if (!user) {
      logger.debug('Skipping dashboard fetch - user not authenticated');
      return;
    }

    // Check cache first
    const now = Date.now();
    const cached = cacheRef.current;

    if (!forceRefresh && cached.dashboard && cached.timestamp &&
        (now - cached.timestamp) < CONFIG.CACHE_DURATION) {
      logger.debug('Using cached dashboard data');
      setDashboard(cached.dashboard);
      setCatalog(cached.catalog || []);
      setUserAddons(cached.userAddons || []);
      setLastUpdated(new Date(cached.timestamp));
      return;
    }

    try {
      setLoading(true);
      setError(null);
      logger.debug('Fetching dashboard data from API');

      const response = await api.get('/api/addons/dashboard');
      const dashboardData = response.data;

      // Update cache
      cacheRef.current = {
        dashboard: dashboardData,
        catalog: dashboardData.catalog || [],
        userAddons: dashboardData.user_addons || [],
        timestamp: now
      };

      setDashboard(dashboardData);
      setCatalog(dashboardData.catalog || []);
      setUserAddons(dashboardData.user_addons || []);
      setRecommendations(dashboardData.recommendations || []);
      setUsageStats(dashboardData.usage_stats || {});
      setAddons(dashboardData.available_addons || []);
      setLastUpdated(new Date());
      setError(null);

      logger.info('Dashboard data loaded successfully', {
        catalogCount: dashboardData.catalog?.length || 0,
        userAddonsCount: dashboardData.user_addons?.length || 0,
        recommendationsCount: dashboardData.recommendations?.length || 0
      });
    } catch (err) {
      logger.error('Error fetching dashboard', err);
      setError('Failed to load add-on data');
      message.error('Failed to load add-on dashboard');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Fetch add-on catalog with enhanced features
  const fetchCatalog = useCallback(async () => {
    try {
      setLoading(true);
      logger.debug('Fetching add-on catalog');

      const response = await api.get('/api/addons/catalog');
      setCatalog(response.data);

      logger.info('Add-on catalog loaded successfully', {
        catalogCount: response.data?.length || 0
      });
    } catch (err) {
      logger.error('Error fetching catalog', err);
      setError('Failed to load add-on catalog');
      message.error('Failed to load add-on catalog');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch user's add-ons with enhanced features
  const fetchUserAddons = useCallback(async () => {
    if (!user) {
      logger.debug('Skipping user add-ons fetch - user not authenticated');
      return;
    }

    try {
      logger.debug('Fetching user add-ons');
      const response = await api.get('/api/addons/user');
      setUserAddons(response.data);

      logger.info('User add-ons loaded successfully', {
        userAddonsCount: response.data?.length || 0
      });
    } catch (err) {
      logger.error('Error fetching user add-ons', err);
      message.error('Failed to load your add-ons');
    }
  }, [user]);

  // Fetch recommendations with enhanced features
  const fetchRecommendations = useCallback(async (context = 'dashboard') => {
    if (!user) {
      logger.debug('Skipping recommendations fetch - user not authenticated');
      return;
    }

    try {
      logger.debug('Fetching recommendations', { context });
      const response = await api.get(`/api/addons/recommendations?context=${context}`);
      setRecommendations(response.data);

      logger.info('Recommendations loaded successfully', {
        recommendationsCount: response.data?.length || 0,
        context
      });
    } catch (err) {
      logger.error('Error fetching recommendations', err);
      message.error('Failed to load recommendations');
    }
  }, [user]);

  // Fetch available add-ons
  const fetchAvailableAddons = useCallback(async () => {
    try {
      logger.debug('Fetching available add-ons');
      const response = await api.get('/api/addons/available');
      setAddons(response.data);

      logger.info('Available add-ons loaded successfully', {
        addonsCount: response.data?.length || 0
      });
    } catch (err) {
      logger.error('Error fetching available add-ons', err);
      message.error('Failed to load available add-ons');
    }
  }, []);

  // Fetch usage statistics
  const fetchUsageStats = useCallback(async () => {
    if (!user) {
      logger.debug('Skipping usage stats fetch - user not authenticated');
      return;
    }

    try {
      logger.debug('Fetching usage statistics');
      const response = await api.get('/api/addons/usage-stats');
      setUsageStats(response.data);

      logger.info('Usage statistics loaded successfully');
    } catch (err) {
      logger.error('Error fetching usage statistics', err);
      message.error('Failed to load usage statistics');
    }
  }, [user]);

  // Check feature access including add-ons
  const checkFeatureAccess = useCallback(async (feature, usageType) => {
    try {
      logger.debug('Checking feature access', { feature, usageType });
      const response = await api.post('/addons/check-access', {
        feature,
        usage_type: usageType
      });

      logger.info('Feature access checked', {
        feature,
        usageType,
        hasAccess: response.data.hasAccess
      });

      return response.data;
    } catch (error) {
      logger.error('Error checking feature access', error);
      return {
        hasAccess: false,
        blockingReason: 'Unable to verify access',
        suggestedAction: 'Try again later'
      };
    }
  }, []);

  // Get enhanced limits including add-on bonuses
  const getAddonEnhancedLimits = useCallback(async (usageType) => {
    try {
      logger.debug('Getting enhanced limits', { usageType });
      const response = await api.get('/addons/enhanced-limits', {
        params: { usage_type: usageType }
      });

      logger.info('Enhanced limits retrieved', {
        usageType,
        totalLimit: response.data.totalLimit
      });

      return response.data;
    } catch (error) {
      logger.error('Error getting enhanced limits', error);
      return {
        totalLimit: 0,
        baseLimit: 0,
        addonBonus: 0
      };
    }
  }, []);

  // Get relevant add-ons for a usage type
  const getRelevantAddons = useCallback(async (usageType) => {
    try {
      logger.debug('Getting relevant add-ons', { usageType });
      const response = await api.get('/addons/relevant', {
        params: { usage_type: usageType }
      });

      logger.info('Relevant add-ons retrieved', {
        usageType,
        addonsCount: response.data?.length || 0
      });

      return response.data;
    } catch (error) {
      logger.error('Error getting relevant add-ons', error);
      return [];
    }
  }, []);

  // Track feature attempt
  const trackFeatureAttempt = useCallback(async (feature, usageType, result) => {
    try {
      logger.debug('Tracking feature attempt', { feature, usageType, result });
      await api.post('/addons/track-attempt', {
        feature,
        usage_type: usageType,
        result
      });

      logger.info('Feature attempt tracked', { feature, usageType, result });
    } catch (error) {
      logger.error('Error tracking feature attempt', error);
    }
  }, []);

  // Flush usage batch to server
  const flushUsageBatch = useCallback(async () => {
    if (usageBatchRef.current.length === 0) return;

    try {
      const batch = [...usageBatchRef.current];
      usageBatchRef.current = [];

      logger.debug('Flushing usage batch', { batchSize: batch.length });

      await api.post('/addons/track-usage-batch', { usage_events: batch });

      logger.info('Usage batch flushed successfully', { batchSize: batch.length });
    } catch (error) {
      logger.error('Error flushing usage batch', error);
      // Re-add failed items to batch for retry
      usageBatchRef.current.unshift(...usageBatchRef.current);
    }
  }, []);

  // Track add-on usage with enhanced features and batching
  const trackUsage = useCallback(async (usageType, amount = 1, metadata = {}) => {
    try {
      logger.debug('Tracking usage', { usageType, amount, metadata });

      // Add to batch for efficient processing
      const usageEvent = {
        usage_type: usageType,
        amount,
        metadata,
        timestamp: new Date().toISOString()
      };

      usageBatchRef.current.push(usageEvent);

      // Flush batch if it reaches the batch size
      if (usageBatchRef.current.length >= CONFIG.USAGE_BATCH_SIZE) {
        await flushUsageBatch();
      }

      // Set up auto-flush timeout if not already set
      if (!usageFlushTimeoutRef.current) {
        usageFlushTimeoutRef.current = setTimeout(() => {
          flushUsageBatch();
          usageFlushTimeoutRef.current = null;
        }, CONFIG.USAGE_FLUSH_INTERVAL);
      }

      const response = await api.post('/addons/track-usage', {
        usage_type: usageType,
        amount,
        metadata
      });

      if (!response.data.success) {
        if (response.data.error === 'Insufficient credits') {
          const warningMessage = `You've reached your ${usageType.replace('_', ' ')} limit. Consider upgrading!`;
          message.warning(warningMessage);
          logger.warn('Usage limit reached', { usageType, amount });
          return false;
        }
        throw new Error(response.data.error);
      }

      // Refresh user add-ons to update credits
      await fetchUserAddons();

      logger.info('Usage tracked successfully', { usageType, amount });
      return true;
    } catch (error) {
      logger.error('Error tracking usage', error);
      message.error('Failed to track usage');
      return false;
    }
  }, [fetchUserAddons, flushUsageBatch]);

  // Purchase add-on with enhanced features
  const purchaseAddon = useCallback(async (addonId, variant = 'basic') => {
    try {
      setLoading(true);
      logger.debug('Purchasing add-on', { addonId, variant });

      const response = await api.post('/addons/purchase', {
        addon_id: addonId,
        variant
      });

      if (response.data.success) {
        message.success('Add-on purchased successfully!');
        await fetchUserAddons();
        logger.info('Add-on purchased successfully', { addonId, variant });
        return true;
      } else {
        const errorMessage = response.data.error || 'Purchase failed';
        message.error(errorMessage);
        logger.warn('Add-on purchase failed', { addonId, variant, error: errorMessage });
        return false;
      }
    } catch (error) {
      logger.error('Error purchasing add-on', error);
      message.error('Failed to purchase add-on');
      return false;
    } finally {
      setLoading(false);
    }
  }, [fetchUserAddons]);

  // Get add-on recommendations with enhanced features
  const getRecommendations = useCallback(async (context = 'dashboard') => {
    try {
      logger.debug('Getting recommendations', { context });
      const response = await api.get('/addons/recommendations', {
        params: { context }
      });

      logger.info('Recommendations retrieved', {
        context,
        recommendationsCount: response.data?.length || 0
      });

      return response.data;
    } catch (error) {
      logger.error('Error getting recommendations', error);
      return [];
    }
  }, []);

  // Check for promotions with enhanced features
  const checkPromotions = useCallback(async (triggerContext = {}) => {
    try {
      logger.debug('Checking promotions', { triggerContext });
      const response = await api.post('/addons/check-promotions', triggerContext);

      logger.info('Promotions checked', {
        promotionsCount: response.data?.length || 0
      });

      return response.data;
    } catch (error) {
      logger.error('Error checking promotions', error);
      return [];
    }
  }, []);

  // Get usage alerts with enhanced features
  const getUsageAlerts = useCallback(async () => {
    try {
      logger.debug('Getting usage alerts');
      const response = await api.get('/addons/usage-alerts');

      logger.info('Usage alerts retrieved', {
        alertsCount: response.data?.length || 0
      });

      return response.data;
    } catch (error) {
      logger.error('Error getting usage alerts', error);
      return [];
    }
  }, []);

  // Process credit rollover with enhanced features
  const processCreditRollover = useCallback(async () => {
    try {
      logger.debug('Processing credit rollover');
      const response = await api.post('/addons/process-rollover');

      if (response.data.success && response.data.rollovers.length > 0) {
        const successMessage = `${response.data.rollovers.length} add-on credits rolled over!`;
        message.success(successMessage);
        await fetchUserAddons();
        logger.info('Credit rollover processed successfully', {
          rolloversCount: response.data.rollovers.length
        });
      }

      return response.data;
    } catch (error) {
      logger.error('Error processing rollover', error);
      return { success: false, error: 'Failed to process rollover' };
    }
  }, [fetchUserAddons]);

  // Helper functions
  const hasAddon = useCallback((addonId) => {
    return userAddons.some(addon => 
      addon.addon_id === addonId && 
      addon.status === 'active' && 
      !addon.is_refunded
    );
  }, [userAddons]);

  const getAddonStatus = useCallback((addonId) => {
    return userAddons.find(addon => addon.addon_id === addonId);
  }, [userAddons]);

  const getRemainingCredits = useCallback((addonId) => {
    const addon = getAddonStatus(addonId);
    return addon ? addon.credits_remaining : 0;
  }, [getAddonStatus]);

  const getUsagePercentage = useCallback((addonId) => {
    const addon = getAddonStatus(addonId);
    return addon ? addon.usage_percentage : 0;
  }, [getAddonStatus]);

  const isNearLimit = useCallback((addonId, threshold = 80) => {
    return getUsagePercentage(addonId) >= threshold;
  }, [getUsagePercentage]);

  const getDaysUntilExpiry = useCallback((addonId) => {
    const addon = getAddonStatus(addonId);
    return addon ? addon.days_until_expiry : null;
  }, [getAddonStatus]);

  const isExpiringSoon = useCallback((addonId, days = 7) => {
    const daysUntilExpiry = getDaysUntilExpiry(addonId);
    return daysUntilExpiry !== null && daysUntilExpiry <= days;
  }, [getDaysUntilExpiry]);

  // Calculate total enhanced limits across all add-ons
  const calculateTotalLimits = useCallback((usageType) => {
    let totalBonus = 0;
    
    userAddons.forEach(addon => {
      if (addon.status === 'active' && !addon.is_refunded) {
        // This would need to be mapped based on addon type and usage type
        // For now, simplified calculation
        if (usageType === 'regeneration_credits' && addon.addon_id === 'regeneration_booster') {
          totalBonus += addon.credits_remaining;
        } else if (usageType === 'image_generation' && addon.addon_id === 'image_pack_premium') {
          totalBonus += addon.credits_remaining;
        }
        // Add more mappings as needed
      }
    });
    
    return totalBonus;
  }, [userAddons]);

  // Initialize data on mount
  useEffect(() => {
    if (user) {
      fetchDashboard();
    }
  }, [user, fetchDashboard]);

  // Cleanup on unmount
  useEffect(() => {
    const currentUsageFlushTimeout = usageFlushTimeoutRef.current;

    return () => {
      if (currentUsageFlushTimeout) {
        clearTimeout(currentUsageFlushTimeout);
      }
    };
  }, []);

  // Enhanced return object with comprehensive add-ons features
  return {
    // State data
    catalog,
    userAddons,
    recommendations,
    dashboard,
    loading,
    error,
    usageStats,
    addons,
    lastUpdated,

    // Core actions
    fetchDashboard,
    fetchCatalog,
    fetchUserAddons,
    fetchRecommendations,
    fetchAvailableAddons,
    fetchUsageStats,

    // Feature access and limits
    checkFeatureAccess,
    getAddonEnhancedLimits,
    getRelevantAddons,

    // Usage tracking
    trackFeatureAttempt,
    trackUsage,
    flushUsageBatch,

    // Purchase and management
    purchaseAddon,
    getRecommendations,
    checkPromotions,
    getUsageAlerts,
    processCreditRollover,

    // Helper functions
    hasAddon,
    getAddonStatus,
    getRemainingCredits,
    getUsagePercentage,
    isNearLimit,
    getDaysUntilExpiry,
    isExpiringSoon,
    calculateTotalLimits,

    // Utility functions
    clearError: () => setError(null),
    clearCache: () => {
      cacheRef.current = { dashboard: null, catalog: null, userAddons: null, timestamp: null };
      logger.debug('Add-ons cache cleared');
    },
    forceRefresh: () => fetchDashboard(true),

    // Enhanced helper functions
    hasActiveAddons: userAddons.length > 0,
    getActiveAddonsCount: () => userAddons.filter(addon =>
      addon.status === 'active' && !addon.is_refunded
    ).length,

    getTotalCreditsRemaining: () => userAddons.reduce((total, addon) =>
      addon.status === 'active' && !addon.is_refunded
        ? total + (addon.credits_remaining || 0)
        : total, 0
    ),

    getExpiringAddons: (days = 7) => userAddons.filter(addon =>
      addon.status === 'active' &&
      !addon.is_refunded &&
      addon.days_until_expiry !== null &&
      addon.days_until_expiry <= days
    ),

    getNearLimitAddons: (threshold = 80) => userAddons.filter(addon =>
      addon.status === 'active' &&
      !addon.is_refunded &&
      addon.usage_percentage >= threshold
    ),

    // Statistics helpers
    getAddonsStats: () => ({
      total: addons.length,
      userActive: userAddons.filter(addon => addon.status === 'active' && !addon.is_refunded).length,
      totalCredits: userAddons.reduce((total, addon) =>
        addon.status === 'active' && !addon.is_refunded
          ? total + (addon.credits_remaining || 0)
          : total, 0
      ),
      expiringSoon: userAddons.filter(addon =>
        addon.status === 'active' &&
        !addon.is_refunded &&
        addon.days_until_expiry !== null &&
        addon.days_until_expiry <= 7
      ).length,
      nearLimit: userAddons.filter(addon =>
        addon.status === 'active' &&
        !addon.is_refunded &&
        addon.usage_percentage >= 80
      ).length
    }),

    // Cache information
    getCacheInfo: () => ({
      hasCache: !!cacheRef.current.timestamp,
      cacheAge: cacheRef.current.timestamp ? Date.now() - cacheRef.current.timestamp : null,
      lastUpdated
    }),

    // Status helpers
    isLoading: loading,
    hasError: !!error,
    hasRecommendations: recommendations.length > 0,
    hasCatalog: catalog.length > 0
  };
};

export default useAddons;

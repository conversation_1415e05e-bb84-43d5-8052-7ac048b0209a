/**
 * Trial Context
 * Production-ready trial management with comprehensive error handling and monitoring
 * Advanced trial features with real-time updates and validation
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './AuthContext';
import { useNotification } from './NotificationContext';
import api from '../api';

// Configuration constants
const CONFIG = {
  // Polling settings
  TRIAL_STATUS_POLL_INTERVAL: 3600000, // 1 hour
  TRIAL_WARNING_POLL_INTERVAL: 300000, // 5 minutes (when trial is ending soon)

  // Warning thresholds
  CRITICAL_DAYS_THRESHOLD: 1, // Show critical warnings when 1 day or less
  WARNING_DAYS_THRESHOLD: 3, // Show warnings when 3 days or less

  // Cache settings
  CACHE_DURATION: 300000, // 5 minutes

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[TrialContext] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[TrialContext] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Trial Context Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[TrialContext] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Trial Context Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[TrialContext] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Trial Context Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const TrialContext = createContext();

// Custom hook to use trial context
// eslint-disable-next-line react-refresh/only-export-components
export const useTrial = () => {
  const context = useContext(TrialContext);
  if (!context) {
    throw new Error('useTrial must be used within a TrialProvider');
  }
  return context;
};

export const TrialProvider = ({ children }) => {
  const { isAuthenticated, refreshUser } = useAuth();
  const { showNotification } = useNotification();

  const [trialStatus, setTrialStatus] = useState({
    isLoading: true,
    isTrial: false,
    daysRemaining: 0,
    hoursRemaining: 0,
    trialEnd: null,
    planId: null,
    downgradeToId: null,
    error: null,
    lastChecked: null,
    warningShown: false
  });

  // Refs for cleanup and state management
  const pollIntervalRef = useRef(null);
  const cacheRef = useRef({ data: null, timestamp: null });

  // Show trial warnings based on remaining time
  const showTrialWarnings = useCallback((daysRemaining, hoursRemaining, warningShown) => {
    if (warningShown) return false;

    if (daysRemaining <= CONFIG.CRITICAL_DAYS_THRESHOLD) {
      const timeLeft = daysRemaining === 0 ? `${hoursRemaining} hours` : `${daysRemaining} day${daysRemaining === 1 ? '' : 's'}`;
      showNotification({
        title: 'Trial Ending Soon!',
        message: `Your trial expires in ${timeLeft}. Upgrade now to continue using premium features.`,
        type: 'error'
      });
      return true;
    } else if (daysRemaining <= CONFIG.WARNING_DAYS_THRESHOLD) {
      showNotification({
        title: 'Trial Reminder',
        message: `Your trial expires in ${daysRemaining} days. Consider upgrading to continue using premium features.`,
        type: 'warning'
      });
      return true;
    }

    return false;
  }, [showNotification]);

  // Check if the user is on a trial
  const checkTrialStatus = useCallback(async () => {
    if (!isAuthenticated) {
      logger.debug('Skipping trial status check - user not authenticated');
      return;
    }

    // Check cache first
    const now = Date.now();
    const cached = cacheRef.current;

    if (cached.data && cached.timestamp &&
        (now - cached.timestamp) < CONFIG.CACHE_DURATION) {
      logger.debug('Using cached trial status');
      setTrialStatus(prev => ({ ...prev, ...cached.data, lastChecked: new Date() }));
      return;
    }

    setTrialStatus(prev => ({ ...prev, isLoading: true }));
    logger.debug('Checking trial status from API');

    try {
      const response = await api.get('/api/trial/status');
      const trialData = {
        isLoading: false,
        isTrial: response.data.is_trial,
        daysRemaining: response.data.days_remaining || 0,
        hoursRemaining: response.data.hours_remaining || 0,
        trialEnd: response.data.trial_end ? new Date(response.data.trial_end) : null,
        planId: response.data.plan_id || null,
        downgradeToId: response.data.downgrade_to || null,
        error: null,
        lastChecked: new Date(),
        warningShown: false
      };

      // Update cache
      cacheRef.current = {
        data: trialData,
        timestamp: now
      };

      // Show warnings if trial is ending soon
      if (trialData.isTrial) {
        const warningShown = showTrialWarnings(
          trialData.daysRemaining,
          trialData.hoursRemaining,
          trialStatus.warningShown
        );
        trialData.warningShown = warningShown;
      }

      setTrialStatus(trialData);

      logger.info('Trial status checked successfully', {
        isTrial: trialData.isTrial,
        daysRemaining: trialData.daysRemaining,
        hoursRemaining: trialData.hoursRemaining
      });
    } catch (error) {
      logger.error('Error checking trial status', error);
      setTrialStatus(prev => ({
        ...prev,
        isLoading: false,
        error: error.response?.data?.detail || 'Failed to check trial status',
        lastChecked: new Date()
      }));
    }
  }, [isAuthenticated, showTrialWarnings, trialStatus.warningShown]);

  // Start a trial
  const startTrial = useCallback(async () => {
    if (!isAuthenticated) {
      logger.warn('Cannot start trial - user not authenticated');
      return false;
    }

    logger.debug('Starting trial');

    try {
      const response = await api.post('/api/trial/start');

      showNotification({
        title: 'Trial Started',
        message: `Your 14-day Growth plan trial has started! It will end on ${new Date(response.data.trial_end).toLocaleDateString()}.`,
        type: 'success'
      });

      // Clear cache to force refresh
      cacheRef.current = { data: null, timestamp: null };

      // Refresh user data and trial status
      await refreshUser();
      await checkTrialStatus();

      logger.info('Trial started successfully', {
        trialEnd: response.data.trial_end
      });

      return true;
    } catch (error) {
      logger.error('Error starting trial', error);
      showNotification({
        title: 'Error',
        message: error.response?.data?.detail || 'Failed to start trial',
        type: 'error'
      });
      return false;
    }
  }, [isAuthenticated, showNotification, refreshUser, checkTrialStatus]);

  // Convert trial to paid subscription
  const convertToPaid = useCallback(async () => {
    if (!isAuthenticated) {
      logger.warn('Cannot convert trial - user not authenticated');
      return false;
    }

    logger.debug('Converting trial to paid subscription');

    try {
      await api.post('/api/trial/convert');

      showNotification({
        title: 'Subscription Activated',
        message: 'Your trial has been converted to a paid subscription!',
        type: 'success'
      });

      // Clear cache to force refresh
      cacheRef.current = { data: null, timestamp: null };

      // Refresh user data and trial status
      await refreshUser();
      await checkTrialStatus();

      logger.info('Trial converted to paid subscription successfully');

      return true;
    } catch (error) {
      logger.error('Error converting trial', error);
      showNotification({
        title: 'Error',
        message: error.response?.data?.detail || 'Failed to convert trial to paid subscription',
        type: 'error'
      });
      return false;
    }
  }, [isAuthenticated, showNotification, refreshUser, checkTrialStatus]);

  // Cancel trial
  const cancelTrial = useCallback(async () => {
    if (!isAuthenticated) {
      logger.warn('Cannot cancel trial - user not authenticated');
      return false;
    }

    logger.debug('Canceling trial');

    try {
      await api.post('/api/trial/cancel');

      showNotification({
        title: 'Trial Canceled',
        message: 'Your trial has been canceled and your account has been downgraded.',
        type: 'info'
      });

      // Clear cache to force refresh
      cacheRef.current = { data: null, timestamp: null };

      // Refresh user data and trial status
      await refreshUser();
      await checkTrialStatus();

      logger.info('Trial canceled successfully');

      return true;
    } catch (error) {
      logger.error('Error canceling trial', error);
      showNotification({
        title: 'Error',
        message: error.response?.data?.detail || 'Failed to cancel trial',
        type: 'error'
      });
      return false;
    }
  }, [isAuthenticated, showNotification, refreshUser, checkTrialStatus]);

  // Check trial status when user changes
  useEffect(() => {
    if (isAuthenticated) {
      checkTrialStatus();
    }
  }, [isAuthenticated, checkTrialStatus]);

  // Enhanced polling system with dynamic intervals
  useEffect(() => {
    // Clear existing interval
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;
    }

    if (trialStatus.isTrial) {
      // Use shorter interval if trial is ending soon
      const interval = trialStatus.daysRemaining <= CONFIG.WARNING_DAYS_THRESHOLD
        ? CONFIG.TRIAL_WARNING_POLL_INTERVAL
        : CONFIG.TRIAL_STATUS_POLL_INTERVAL;

      logger.debug('Setting up trial polling', {
        interval: interval / 1000 / 60,
        daysRemaining: trialStatus.daysRemaining
      });

      pollIntervalRef.current = setInterval(() => {
        checkTrialStatus();
      }, interval);
    }

    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
    };
  }, [trialStatus.isTrial, trialStatus.daysRemaining, checkTrialStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
    };
  }, []);

  // Enhanced context value with organized structure
  const value = {
    // State data
    ...trialStatus,

    // Core trial management functions
    startTrial,
    convertToPaid,
    cancelTrial,
    refreshTrialStatus: checkTrialStatus,

    // Utility functions
    clearError: () => setTrialStatus(prev => ({ ...prev, error: null })),
    clearCache: () => {
      cacheRef.current = { data: null, timestamp: null };
      logger.debug('Trial cache cleared');
    },

    // Helper functions
    isTrialActive: trialStatus.isTrial && trialStatus.daysRemaining > 0,
    isTrialExpired: trialStatus.isTrial && trialStatus.daysRemaining <= 0,
    isTrialEndingSoon: trialStatus.isTrial && trialStatus.daysRemaining <= CONFIG.WARNING_DAYS_THRESHOLD,
    isTrialCritical: trialStatus.isTrial && trialStatus.daysRemaining <= CONFIG.CRITICAL_DAYS_THRESHOLD,

    // Time calculations
    getTimeRemaining: () => {
      if (!trialStatus.trialEnd) return null;

      const now = new Date();
      const end = new Date(trialStatus.trialEnd);
      const diff = end.getTime() - now.getTime();

      if (diff <= 0) return { days: 0, hours: 0, minutes: 0 };

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

      return { days, hours, minutes };
    },

    getTrialProgress: () => {
      if (!trialStatus.isTrial || !trialStatus.trialEnd) return 0;

      const now = new Date();
      const end = new Date(trialStatus.trialEnd);
      const start = new Date(end.getTime() - (14 * 24 * 60 * 60 * 1000)); // Assuming 14-day trial

      const total = end.getTime() - start.getTime();
      const elapsed = now.getTime() - start.getTime();

      return Math.min(100, Math.max(0, (elapsed / total) * 100));
    },

    // Formatting helpers
    formatTimeRemaining: () => {
      const time = value.getTimeRemaining();
      if (!time) return 'Trial expired';

      if (time.days > 0) {
        return `${time.days} day${time.days === 1 ? '' : 's'} remaining`;
      } else if (time.hours > 0) {
        return `${time.hours} hour${time.hours === 1 ? '' : 's'} remaining`;
      } else {
        return `${time.minutes} minute${time.minutes === 1 ? '' : 's'} remaining`;
      }
    },

    formatTrialEnd: () => {
      if (!trialStatus.trialEnd) return 'Unknown';
      return new Date(trialStatus.trialEnd).toLocaleDateString();
    },

    // Status helpers
    getTrialStatusMessage: () => {
      if (!trialStatus.isTrial) return 'No active trial';
      if (trialStatus.daysRemaining <= 0) return 'Trial expired';
      if (trialStatus.daysRemaining <= CONFIG.CRITICAL_DAYS_THRESHOLD) return 'Trial ending soon!';
      if (trialStatus.daysRemaining <= CONFIG.WARNING_DAYS_THRESHOLD) return 'Trial ending soon';
      return 'Trial active';
    },

    getTrialStatusSeverity: () => {
      if (!trialStatus.isTrial) return 'info';
      if (trialStatus.daysRemaining <= 0) return 'error';
      if (trialStatus.daysRemaining <= CONFIG.CRITICAL_DAYS_THRESHOLD) return 'error';
      if (trialStatus.daysRemaining <= CONFIG.WARNING_DAYS_THRESHOLD) return 'warning';
      return 'success';
    },

    // Cache information
    getCacheInfo: () => ({
      hasCache: !!cacheRef.current.data,
      cacheAge: cacheRef.current.timestamp ? Date.now() - cacheRef.current.timestamp : null,
      lastChecked: trialStatus.lastChecked
    })
  };

  return (
    <TrialContext.Provider value={value}>
      {children}
    </TrialContext.Provider>
  );
};

// Export the context
export { TrialContext };

// Default export for convenience
export default TrialProvider;

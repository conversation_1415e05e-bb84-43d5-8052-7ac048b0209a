import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';

const SupportAnalytics = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [timeframe, setTimeframe] = useState('monthly');
  const [dateRange, setDateRange] = useState({
    start: startOfMonth(subMonths(new Date(), 2)),
    end: endOfMonth(new Date())
  });

  // Mock data for demonstration
  const ticketVolumeData = [
    { month: 'Oct 2024', created: 145, resolved: 138, escalated: 8 },
    { month: 'Nov 2024', created: 162, resolved: 155, escalated: 12 },
    { month: 'Dec 2024', created: 178, resolved: 171, escalated: 6 },
  ];

  const resolutionTimeData = [
    { category: 'Technical', avgHours: 4.2, target: 6 },
    { category: 'Billing', avgHours: 2.8, target: 4 },
    { category: 'Account', avgHours: 3.1, target: 4 },
    { category: 'General', avgHours: 5.5, target: 8 },
    { category: 'Bug Report', avgHours: 8.2, target: 12 },
  ];

  const satisfactionTrendData = [
    { week: 'Week 1', csat: 4.2, nps: 45 },
    { week: 'Week 2', csat: 4.4, nps: 52 },
    { week: 'Week 3', csat: 4.1, nps: 38 },
    { week: 'Week 4', csat: 4.6, nps: 58 },
  ];

  const channelPerformanceData = [
    { channel: 'Email', tickets: 245, avgResolution: 4.5, satisfaction: 4.3 },
    { channel: 'Chat', tickets: 189, avgResolution: 2.1, satisfaction: 4.6 },
    { channel: 'Phone', tickets: 67, avgResolution: 1.8, satisfaction: 4.8 },
    { channel: 'In-App', tickets: 134, avgResolution: 3.2, satisfaction: 4.4 },
  ];

  const agentPerformanceData = [
    { name: 'Sarah Johnson', tickets: 89, avgResolution: 4.2, csat: 4.6, fcr: 0.78 },
    { name: 'Mike Chen', tickets: 67, avgResolution: 6.1, csat: 4.3, fcr: 0.72 },
    { name: 'Emily Rodriguez', tickets: 45, avgResolution: 3.8, csat: 4.8, fcr: 0.85 },
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const formatHours = (hours) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}m`;
    }
    return `${hours.toFixed(1)}h`;
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const setQuickRange = (months) => {
    const end = endOfMonth(new Date());
    const start = startOfMonth(subMonths(end, months - 1));
    setDateRange({ start, end });
  };

  return (
    <Box>
      {/* Controls */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <DatePicker
            label="Start Date"
            value={dateRange.start}
            onChange={(value) => setDateRange(prev => ({ ...prev, start: value }))}
            slotProps={{ textField: { fullWidth: true, size: 'small' } }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <DatePicker
            label="End Date"
            value={dateRange.end}
            onChange={(value) => setDateRange(prev => ({ ...prev, end: value }))}
            slotProps={{ textField: { fullWidth: true, size: 'small' } }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth size="small">
            <InputLabel>Timeframe</InputLabel>
            <Select
              value={timeframe}
              label="Timeframe"
              onChange={(e) => setTimeframe(e.target.value)}
            >
              <MenuItem value="daily">Daily</MenuItem>
              <MenuItem value="weekly">Weekly</MenuItem>
              <MenuItem value="monthly">Monthly</MenuItem>
              <MenuItem value="quarterly">Quarterly</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Box display="flex" gap={1}>
            <Button size="small" onClick={() => setQuickRange(3)}>
              3M
            </Button>
            <Button size="small" onClick={() => setQuickRange(6)}>
              6M
            </Button>
            <Button size="small" onClick={() => setQuickRange(12)}>
              1Y
            </Button>
          </Box>
        </Grid>
      </Grid>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Ticket Volume Trends */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardHeader title="Ticket Volume Trends" />
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <AreaChart data={ticketVolumeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="created"
                      stackId="1"
                      stroke="#8884d8"
                      fill="#8884d8"
                      name="Created"
                    />
                    <Area
                      type="monotone"
                      dataKey="resolved"
                      stackId="2"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      name="Resolved"
                    />
                    <Area
                      type="monotone"
                      dataKey="escalated"
                      stackId="3"
                      stroke="#ffc658"
                      fill="#ffc658"
                      name="Escalated"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Customer Satisfaction Trend */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardHeader title="Satisfaction Trends" />
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={satisfactionTrendData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="week" />
                    <YAxis yAxisId="left" domain={[0, 5]} />
                    <YAxis yAxisId="right" orientation="right" domain={[-100, 100]} />
                    <Tooltip />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="csat"
                      stroke="#8884d8"
                      strokeWidth={2}
                      name="CSAT Score"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="nps"
                      stroke="#82ca9d"
                      strokeWidth={2}
                      name="NPS Score"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Resolution Time by Category */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Resolution Time by Category" />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={resolutionTimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatHours(value)} />
                    <Legend />
                    <Bar dataKey="avgHours" fill="#8884d8" name="Actual" />
                    <Bar dataKey="target" fill="#82ca9d" name="Target" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Channel Performance */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Channel Performance" />
              <CardContent>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Channel</TableCell>
                        <TableCell align="right">Tickets</TableCell>
                        <TableCell align="right">Avg Resolution</TableCell>
                        <TableCell align="right">CSAT</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {channelPerformanceData.map((channel) => (
                        <TableRow key={channel.channel}>
                          <TableCell component="th" scope="row">
                            {channel.channel}
                          </TableCell>
                          <TableCell align="right">{channel.tickets}</TableCell>
                          <TableCell align="right">{formatHours(channel.avgResolution)}</TableCell>
                          <TableCell align="right">
                            <Chip
                              label={channel.satisfaction.toFixed(1)}
                              color={channel.satisfaction >= 4.5 ? "success" : channel.satisfaction >= 4.0 ? "warning" : "error"}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Agent Performance */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Agent Performance Analysis" />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Agent</TableCell>
                        <TableCell align="right">Tickets Handled</TableCell>
                        <TableCell align="right">Avg Resolution Time</TableCell>
                        <TableCell align="right">Customer Satisfaction</TableCell>
                        <TableCell align="right">First Contact Resolution</TableCell>
                        <TableCell align="right">Performance Score</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {agentPerformanceData.map((agent) => {
                        const performanceScore = (
                          (agent.csat / 5) * 0.4 +
                          agent.fcr * 0.3 +
                          (1 - Math.min(agent.avgResolution / 8, 1)) * 0.3
                        );
                        
                        return (
                          <TableRow key={agent.name}>
                            <TableCell component="th" scope="row">
                              <Typography variant="body2" fontWeight="bold">
                                {agent.name}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">{agent.tickets}</TableCell>
                            <TableCell align="right">{formatHours(agent.avgResolution)}</TableCell>
                            <TableCell align="right">
                              <Chip
                                label={agent.csat.toFixed(1)}
                                color={agent.csat >= 4.5 ? "success" : agent.csat >= 4.0 ? "warning" : "error"}
                                size="small"
                              />
                            </TableCell>
                            <TableCell align="right">{formatPercentage(agent.fcr)}</TableCell>
                            <TableCell align="right">
                              <Chip
                                label={formatPercentage(performanceScore)}
                                color={performanceScore >= 0.8 ? "success" : performanceScore >= 0.6 ? "warning" : "error"}
                                size="small"
                              />
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Key Insights */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Key Insights & Recommendations" />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom color="success.main">
                      Positive Trends
                    </Typography>
                    <Box component="ul" sx={{ pl: 2 }}>
                      <Typography component="li" variant="body2" gutterBottom>
                        Customer satisfaction increased by 8% this month
                      </Typography>
                      <Typography component="li" variant="body2" gutterBottom>
                        Chat channel showing excellent performance with 4.6 CSAT
                      </Typography>
                      <Typography component="li" variant="body2" gutterBottom>
                        Emily Rodriguez achieving 85% first contact resolution rate
                      </Typography>
                      <Typography component="li" variant="body2" gutterBottom>
                        Technical support meeting SLA targets consistently
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="h6" gutterBottom color="warning.main">
                      Areas for Improvement
                    </Typography>
                    <Box component="ul" sx={{ pl: 2 }}>
                      <Typography component="li" variant="body2" gutterBottom>
                        Bug report resolution time exceeds target by 32%
                      </Typography>
                      <Typography component="li" variant="body2" gutterBottom>
                        General support category needs attention (5.5h avg)
                      </Typography>
                      <Typography component="li" variant="body2" gutterBottom>
                        Escalation rate increased to 6.7% this month
                      </Typography>
                      <Typography component="li" variant="body2" gutterBottom>
                        Agent workload distribution could be optimized
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default SupportAnalytics;

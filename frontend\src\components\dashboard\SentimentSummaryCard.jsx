// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, useMemo, memo, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Divider,
  CircularProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Avatar,
  Button,
  Chip,
  useTheme,
  alpha,
  useMediaQuery,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemText as MenuListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  AlertTitle,
  Grid,
  Skeleton,
  Fade,
  Badge
} from '@mui/material';

import {
  SentimentSatisfied as SentimentSatisfiedIcon,
  SentimentDissatisfied as SentimentDissatisfiedIcon,
  SentimentNeutral as SentimentNeutralIcon,
  SentimentVerySatisfied as SentimentVerySatisfiedIcon,
  SentimentVeryDissatisfied as SentimentVeryDissatisfiedIcon,
  Warning as WarningIcon,
  ArrowForward as ArrowForwardIcon,
  LinkedIn as LinkedInIcon,
  Twitter as TwitterIcon,
  Facebook as FacebookIcon,
  Instagram as InstagramIcon,
  Language as LanguageIcon,
  Add as AddIcon,
  Psychology as PsychologyIcon,

  Download as ExportIcon,

  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,

} from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Cell,

} from 'recharts';
import { useNavigate } from 'react-router-dom';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};



/**
 * Enhanced SentimentSummaryCard Component - Enterprise-grade sentiment summary card
 * Features: Plan-based sentiment summary limitations, real-time sentiment summary monitoring, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced sentiment summary insights and interactive sentiment summary exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} [props.data] - Sentiment summary data
 * @param {boolean} [props.loading=false] - Loading state
 * @param {Function} [props.onViewDetails] - View details callback
 * @param {string} [props.summaryType='overall-summary'] - Sentiment summary type
 * @param {string} [props.contentVariant='posts'] - Content variant
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onSummaryTypeChange] - Summary type change callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const SentimentSummaryCard = memo(forwardRef(({
  data,
  loading = false,

  summaryType = 'overall-summary',

  enableExport = false,
  realTimeUpdates = false,

  onExport,
  onRefresh,
  customization = {},
  className = '',
  style = {},
  testId = 'sentiment-summary-card',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();
  const navigate = useNavigate();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading || subscriptionLoading,
    refreshing: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showSettingsDialog: false,
    currentSummaryType: summaryType,
    animationKey: 0,
    errors: {},
    // Sentiment summary state
    selectedTimeRange: '30d',
    summaryOptimizing: false
  });

  // Tab state
  const [tabValue, setTabValue] = useState(0);

  // Sentiment summary data state
  const [sentimentSummaryData, setSentimentSummaryData] = useState({
    raw: data || null,
    processed: null,
    trends: null,
    insights: null
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);

  /**
   * Enhanced plan-based sentiment summary validation - Production Ready
   */
  const validateSentimentSummaryFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canViewSummary: false,
        hasSummaryAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { dataPoints: 0, platforms: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based sentiment summary limits
    const planLimits = {
      creator: {
        dataPoints: 50,
        platforms: 2,
        features: ['basic_sentiment_summary'],
        realTime: false,
        export: false,
        summaryTypes: ['overall-summary'],
        insights: false
      },
      accelerator: {
        dataPoints: 200,
        platforms: 5,
        features: ['basic_sentiment_summary', 'advanced_sentiment_summary', 'sentiment_highlights'],
        realTime: true,
        export: true,
        summaryTypes: ['overall-summary', 'sentiment-highlights', 'sentiment-trends-summary', 'sentiment-insights-summary'],
        insights: true
      },
      dominator: {
        dataPoints: -1,
        platforms: -1,
        features: ['basic_sentiment_summary', 'advanced_sentiment_summary', 'sentiment_highlights', 'ai_sentiment_insights'],
        realTime: true,
        export: true,
        summaryTypes: ['overall-summary', 'sentiment-highlights', 'sentiment-trends-summary', 'sentiment-insights-summary', 'sentiment-alerts-summary'],
        insights: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = sentimentSummaryData.processed ? 1 : 0;
    const limit = currentPlanLimits.dataPoints;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewSummary: true,
      hasSummaryAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, sentimentSummaryData.processed]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const summaryLimits = validateSentimentSummaryFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasExport: summaryLimits.planLimits.export,
      hasRealTime: summaryLimits.planLimits.realTime,
      hasInsights: summaryLimits.planLimits.insights,
      maxDataPoints: summaryLimits.planLimits.dataPoints,
      maxPlatforms: summaryLimits.planLimits.platforms,
      availableSummaryTypes: summaryLimits.planLimits.summaryTypes,
      availableFeatures: summaryLimits.planLimits.features,
      refreshInterval: summaryLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateSentimentSummaryFeatures]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || 'Sentiment summary card',
      'aria-description': ariaDescription || `Interactive ${summaryType} sentiment summary card displaying sentiment metrics`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, summaryType, realTimeUpdates]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Sentiment summary refreshed successfully');
      announceToScreenReader('Sentiment summary has been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh sentiment summary: ${error.message}`);
      announceToScreenReader('Failed to refresh sentiment summary');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'csv') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, sentimentSummaryData.processed);
      }

      showSuccessNotification(`Sentiment summary exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Sentiment summary has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export sentiment summary: ${error.message}`);
      announceToScreenReader('Failed to export sentiment summary');
    }
  }, [subscriptionFeatures.hasExport, sentimentSummaryData.processed, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportSummary: handleExport,
    getSummaryData: () => sentimentSummaryData.processed,
    getSummaryLimits: validateSentimentSummaryFeatures,
    focus: () => setFocusToElement(document.querySelector(`[data-testid="${testId}"]`)),
    announce: (message) => announceToScreenReader(message)
  }), [
    sentimentSummaryData.processed,
    validateSentimentSummaryFeatures,
    handleRefresh,
    handleExport,
    setFocusToElement,
    announceToScreenReader,
    testId
  ]);

  /**
   * Enhanced data processing effect - Production Ready
   */
  useEffect(() => {
    if (data) {
      setSentimentSummaryData(prev => ({
        ...prev,
        raw: data,
        processed: data
      }));
    }
  }, [data]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced sentiment summary features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Advanced sentiment summary types',
        'Real-time sentiment monitoring',
        'Multi-platform sentiment analysis',
        'Data export capabilities',
        'AI-powered sentiment insights',
        'Custom sentiment alerts'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  /**
   * Enhanced tab change handler - Production Ready
   */
  const handleTabChange = useCallback((event, newValue) => {
    setTabValue(newValue);
    announceToScreenReader(`Switched to ${newValue === 0 ? 'platform comparison' : newValue === 1 ? 'top content' : 'content needing attention'} tab`);
  }, [announceToScreenReader]);

  /**
   * Enhanced platform icon with ACE Social colors - Production Ready
   */
  const getPlatformIcon = useCallback((platform) => {
    const iconSize = isMobile ? 20 : 24;
    if (!platform) return <LanguageIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6), fontSize: iconSize }} />;

    try {
      switch (platform.toLowerCase()) {
        case 'linkedin':
          return <LinkedInIcon sx={{ color: '#0077B5', fontSize: iconSize }} />;
        case 'twitter':
        case 'x':
          return <TwitterIcon sx={{ color: '#1DA1F2', fontSize: iconSize }} />;
        case 'facebook':
          return <FacebookIcon sx={{ color: '#4267B2', fontSize: iconSize }} />;
        case 'instagram':
          return <InstagramIcon sx={{ color: '#E1306C', fontSize: iconSize }} />;
        default:
          return <LanguageIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6), fontSize: iconSize }} />;
      }
    } catch {
      // In case platform is not a string or has no toLowerCase method
      return <LanguageIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6), fontSize: iconSize }} />;
    }
  }, [isMobile]);

  /**
   * Enhanced sentiment icon with ACE Social colors - Production Ready
   */
  const getSentimentIcon = useCallback((score) => {
    const iconSize = isMobile ? 20 : 24;
    if (score === undefined || score === null) return <SentimentNeutralIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6), fontSize: iconSize }} />;
    if (score >= 0.5) return <SentimentVerySatisfiedIcon sx={{ color: '#2E7D32', fontSize: iconSize }} />;
    if (score > 0) return <SentimentSatisfiedIcon sx={{ color: '#4CAF50', fontSize: iconSize }} />;
    if (score === 0) return <SentimentNeutralIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6), fontSize: iconSize }} />;
    if (score > -0.5) return <SentimentDissatisfiedIcon sx={{ color: '#F44336', fontSize: iconSize }} />;
    return <SentimentVeryDissatisfiedIcon sx={{ color: '#C62828', fontSize: iconSize }} />;
  }, [isMobile]);

  /**
   * Enhanced sentiment color with ACE Social colors - Production Ready
   */
  const getSentimentColor = useCallback((score) => {
    if (score === undefined || score === null) return alpha(ACE_COLORS.DARK, 0.6);
    if (score >= 0.5) return '#2E7D32';
    if (score > 0) return '#4CAF50';
    if (score === 0) return alpha(ACE_COLORS.DARK, 0.6);
    if (score > -0.5) return '#F44336';
    return '#C62828';
  }, []);

  /**
   * Enhanced custom tooltip for charts - Production Ready
   */
  const CustomTooltip = useCallback(({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            backgroundColor: ACE_COLORS.WHITE,
            p: 2,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
            borderRadius: 2,
            boxShadow: `0 4px 12px ${alpha(ACE_COLORS.DARK, 0.1)}`
          }}
        >
          <Typography variant="body2" fontWeight="medium" sx={{ color: ACE_COLORS.DARK }}>{label}</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            {getSentimentIcon(payload[0].value)}
            <Typography variant="body2" sx={{ ml: 1, color: ACE_COLORS.DARK }}>
              Score: {payload[0]?.value !== undefined ? payload[0].value.toFixed(2) : 'N/A'}
            </Typography>
          </Box>
        </Box>
      );
    }
    return null;
  }, [getSentimentIcon]);

  /**
   * Enhanced navigation handlers - Production Ready
   */
  const handleViewContent = useCallback((contentId) => {
    navigate(`/content/${contentId}`);
    announceToScreenReader(`Navigating to content details for ${contentId}`);
  }, [navigate, announceToScreenReader]);

  const handleViewAllSentiment = useCallback(() => {
    navigate('/analytics/sentiment-analysis');
    announceToScreenReader('Navigating to full sentiment analysis');
  }, [navigate, announceToScreenReader]);

  // Use provided data or null
  const displayData = sentimentSummaryData.processed || data;

  // Main render condition checks
  if (state.loading && !displayData) {
    return (
      <ErrorBoundary
        fallback={
          <Box sx={{ height: '100%', p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load sentiment summary card
            </Typography>
          </Box>
        }
      >
        <Box
          sx={{
            height: '100%',
            ...customization
          }}
          className={className}
          style={style}
          data-testid={testId}
          {...getAccessibilityProps()}
        >
          <Box sx={{ p: 2 }}>
            <Skeleton variant="rectangular" width="100%" height={48} sx={{ mb: 2 }} />
            <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />
            <Skeleton variant="rectangular" width="100%" height={200} />
          </Box>
        </Box>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Box sx={{ height: '100%', p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="error">
            Unable to load sentiment summary card
          </Typography>
        </Box>
      }
    >
      <Box
        sx={{
          height: '100%',
          ...customization
        }}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        {/* Real-time Updates Indicator */}
        {realTimeUpdates && subscriptionFeatures.hasRealTime && (
          <Box sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 1,
            backgroundColor: alpha(theme.palette.success.main, 0.1),
            borderRadius: 1,
            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
            zIndex: 10
          }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: theme.palette.success.main,
                ...(!prefersReducedMotion && {
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 }
                  }
                })
              }}
              aria-hidden="true"
            />
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
              Live
            </Typography>
          </Box>
        )}

        {/* Enhanced Header with Controls */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', p: 2, pb: 0 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PsychologyIcon sx={{ color: ACE_COLORS.PURPLE }} />
            <Typography variant="h6" component="span" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
              Sentiment Summary
            </Typography>
            {subscriptionFeatures.hasRealTime && realTimeUpdates && (
              <Chip
                label="LIVE"
                size="small"
                sx={{
                  backgroundColor: alpha('#4CAF50', 0.1),
                  color: '#4CAF50',
                  fontWeight: 600,
                  fontSize: '0.7rem'
                }}
              />
            )}
          </Box>

          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {/* Export Button */}
            {enableExport && (
              <Tooltip title="Export Summary">
                <IconButton
                  size="small"
                  onClick={handleExportMenuOpen}
                  sx={{
                    color: theme.palette.text.secondary,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1)
                    }
                  }}
                  aria-label="Export summary data"
                >
                  <ExportIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {/* Refresh Button */}
            {onRefresh && (
              <Tooltip title="Refresh Summary">
                <IconButton
                  size="small"
                  onClick={handleRefresh}
                  disabled={state.refreshing}
                  sx={{
                    color: ACE_COLORS.PURPLE,
                    '&:hover': {
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                  aria-label="Refresh sentiment summary"
                >
                  {state.refreshing ? (
                    <CircularProgress size={16} />
                  ) : (
                    <RefreshIcon fontSize="small" />
                  )}
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Enhanced Tabs */}
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              color: ACE_COLORS.DARK,
              '&.Mui-selected': {
                color: ACE_COLORS.PURPLE
              }
            },
            '& .MuiTabs-indicator': {
              backgroundColor: ACE_COLORS.PURPLE
            }
          }}
        >
          <Tab
            label="By Platform"
            aria-label="View sentiment by platform"
            sx={{ fontWeight: 600 }}
          />
          <Tab
            label="Top Content"
            aria-label="View top performing content"
            sx={{ fontWeight: 600 }}
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                Needs Attention
                {displayData?.negative_content?.length > 0 && (
                  <Badge
                    badgeContent={displayData.negative_content.length}
                    color="error"
                    sx={{ '& .MuiBadge-badge': { fontSize: '0.7rem' } }}
                  />
                )}
              </Box>
            }
            aria-label="View content needing attention"
            sx={{ fontWeight: 600 }}
          />
        </Tabs>

        <Box sx={{ p: 2 }}>
          {state.loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <Box sx={{ textAlign: 'center' }}>
                <Skeleton variant="rectangular" width="100%" height={200} sx={{ mb: 2 }} />
                <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />
                <Skeleton variant="text" width="40%" height={16} />
              </Box>
            </Box>
          ) : !displayData ? (
            <Box sx={{ p: 3, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
              <PsychologyIcon sx={{ fontSize: isMobile ? 40 : 48, color: alpha(ACE_COLORS.PURPLE, 0.4), mb: 2 }} />
              <Typography variant={isMobile ? "subtitle1" : "h6"} gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                No Sentiment Data Available
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: '80%' }}>
                Create and publish content across different platforms to see sentiment analysis by platform.
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => navigate('/content/create')}
                startIcon={<AddIcon />}
                sx={{
                  borderColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    borderColor: ACE_COLORS.PURPLE,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                  }
                }}
              >
                Create Content
              </Button>
            </Box>
          ) : (
            <Fade in timeout={500}>
              <Box>
                {/* Platform Comparison Tab */}
                {tabValue === 0 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                      Sentiment Score by Platform
                    </Typography>
                    <Box sx={{ height: isMobile ? 180 : 220, mt: 1, mb: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={displayData?.platform_sentiment || []}
                          layout="vertical"
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" horizontal={false} stroke={alpha(ACE_COLORS.DARK, 0.1)} />
                          <XAxis
                            type="number"
                            domain={[-1, 1]}
                            ticks={[-1, -0.5, 0, 0.5, 1]}
                            tick={{ fontSize: isMobile ? 10 : 12, fill: ACE_COLORS.DARK }}
                          />
                          <YAxis
                            dataKey="platform"
                            type="category"
                            tick={{ fontSize: isMobile ? 10 : 12, fill: ACE_COLORS.DARK }}
                            tickFormatter={(value) => value.charAt(0).toUpperCase() + value.slice(1)}
                            width={isMobile ? 60 : 80}
                          />
                          <RechartsTooltip content={<CustomTooltip />} />
                          <Bar dataKey="score" radius={[0, 4, 4, 0]}>
                            {(displayData?.platform_sentiment || []).map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={getSentimentColor(entry.score)} />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                      Platform Insights
                    </Typography>
                    <List dense>
                      {(displayData?.platform_insights || []).slice(0, subscriptionFeatures.maxPlatforms === -1 ? undefined : subscriptionFeatures.maxPlatforms).map((insight, index) => (
                        <ListItem key={index} alignItems="flex-start">
                          <ListItemIcon sx={{ minWidth: 36 }}>
                            {getPlatformIcon(insight.platform)}
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                                {insight.platform.charAt(0).toUpperCase() + insight.platform.slice(1)}
                              </Typography>
                            }
                            secondary={
                              <Typography variant="body2" color="text.secondary">
                                {insight.insight}
                              </Typography>
                            }
                          />
                        </ListItem>
                      ))}
                      {subscriptionFeatures.maxPlatforms !== -1 && (displayData?.platform_insights || []).length > subscriptionFeatures.maxPlatforms && (
                        <ListItem>
                          <ListItemText
                            primary={
                              <Button
                                variant="outlined"
                                size="small"
                                onClick={() => setState(prev => ({ ...prev, showUpgradeDialog: true }))}
                                sx={{
                                  borderColor: ACE_COLORS.PURPLE,
                                  color: ACE_COLORS.PURPLE,
                                  '&:hover': {
                                    borderColor: ACE_COLORS.PURPLE,
                                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                                  }
                                }}
                              >
                                View All Platforms
                              </Button>
                            }
                          />
                        </ListItem>
                      )}
                    </List>
                  </Box>
                )}

                {/* Top Performing Content Tab */}
                {tabValue === 1 && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Top Performing Content by Sentiment
                </Typography>
                <List>
                  {(displayData?.top_content || []).map((content, index) => (
                    <ListItem
                      key={index}
                      alignItems="flex-start"
                      sx={{
                        mb: 1,
                        p: 1.5,
                        borderRadius: 1,
                        bgcolor: alpha(getSentimentColor(content.sentiment_score), 0.05),
                        '&:hover': {
                          bgcolor: alpha(getSentimentColor(content.sentiment_score), 0.1),
                        }
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar
                          src={content.image_url}
                          variant="rounded"
                          sx={{ width: 60, height: 60 }}
                        >
                          {content.title ? content.title.charAt(0) : '?'}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="subtitle2" noWrap sx={{ maxWidth: '70%' }}>
                              {content.title || 'Untitled Content'}
                            </Typography>
                            <Box sx={{ display: 'flex', ml: 'auto' }}>
                              {getPlatformIcon(content.platform)}
                              {getSentimentIcon(content.sentiment_score)}
                            </Box>
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 0.5 }}>
                            <Typography variant="body2" color="textSecondary" noWrap>
                              {content.excerpt || 'No excerpt available'}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1, justifyContent: 'space-between' }}>
                              <Typography variant="caption" color="textSecondary">
                                Score: {content.sentiment_score !== undefined ? content.sentiment_score.toFixed(2) : 'N/A'}
                              </Typography>
                              <Button
                                size="small"
                                endIcon={<ArrowForwardIcon />}
                                onClick={() => handleViewContent(content.id)}
                              >
                                View
                              </Button>
                            </Box>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}

            {/* Content Needing Attention Tab */}
            {tabValue === 2 && (
              <Box sx={{ mt: 1 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Content Requiring Attention
                </Typography>
                {(displayData?.negative_content || []).length === 0 ? (
                  <Box sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="textSecondary">
                      No content requires attention at this time
                    </Typography>
                  </Box>
                ) : (
                  <List>
                    {(displayData?.negative_content || []).map((content, index) => (
                      <ListItem
                        key={index}
                        alignItems="flex-start"
                        sx={{
                          mb: 1,
                          p: 1.5,
                          borderRadius: 1,
                          bgcolor: alpha(theme.palette.error.main, 0.05),
                          '&:hover': {
                            bgcolor: alpha(theme.palette.error.main, 0.1),
                          }
                        }}
                      >
                        <ListItemAvatar>
                          <Avatar
                            src={content.image_url}
                            variant="rounded"
                            sx={{ width: 60, height: 60 }}
                          >
                            {content.title ? content.title.charAt(0) : '?'}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="subtitle2" noWrap sx={{ maxWidth: '70%' }}>
                                {content.title || 'Untitled Content'}
                              </Typography>
                              <Box sx={{ display: 'flex', ml: 'auto' }}>
                                {getPlatformIcon(content.platform)}
                                <WarningIcon color="error" sx={{ ml: 0.5 }} />
                              </Box>
                            </Box>
                          }
                          secondary={
                            <Box sx={{ mt: 0.5 }}>
                              <Typography variant="body2" color="error" fontWeight="medium">
                                {content.issue || 'No issue description available'}
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                                {(content?.key_concerns || []).map((concern, i) => (
                                  <Chip
                                    key={i}
                                    label={concern}
                                    size="small"
                                    color="error"
                                    variant="outlined"
                                  />
                                ))}
                              </Box>
                              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                                <Button
                                  size="small"
                                  endIcon={<ArrowForwardIcon />}
                                  onClick={() => handleViewContent(content.id)}
                                >
                                  View
                                </Button>
                              </Box>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                )}
              </Box>
            )}

                {/* Content Needing Attention Tab */}
                {tabValue === 2 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                      Content Requiring Attention
                    </Typography>
                    {(displayData?.negative_content || []).length === 0 ? (
                      <Box sx={{ p: 2, textAlign: 'center' }}>
                        <Typography variant="body2" color="text.secondary">
                          No content requires attention at this time
                        </Typography>
                      </Box>
                    ) : (
                      <List>
                        {(displayData?.negative_content || []).slice(0, subscriptionFeatures.maxDataPoints === -1 ? undefined : 5).map((content, index) => (
                          <ListItem
                            key={index}
                            alignItems="flex-start"
                            sx={{
                              mb: 1,
                              p: 1.5,
                              borderRadius: 1,
                              bgcolor: alpha('#F44336', 0.05),
                              '&:hover': {
                                bgcolor: alpha('#F44336', 0.1),
                              }
                            }}
                          >
                            <ListItemAvatar>
                              <Avatar
                                src={content.image_url}
                                variant="rounded"
                                sx={{ width: isMobile ? 50 : 60, height: isMobile ? 50 : 60 }}
                              >
                                {content.title ? content.title.charAt(0) : '?'}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Typography variant="subtitle2" noWrap sx={{ maxWidth: '70%', fontWeight: 600, color: ACE_COLORS.DARK }}>
                                    {content.title || 'Untitled Content'}
                                  </Typography>
                                  <Box sx={{ display: 'flex', ml: 'auto' }}>
                                    {getPlatformIcon(content.platform)}
                                    <WarningIcon color="error" sx={{ ml: 0.5 }} />
                                  </Box>
                                </Box>
                              }
                              secondary={
                                <Box sx={{ mt: 0.5 }}>
                                  <Typography variant="body2" color="error" fontWeight="medium">
                                    {content.issue || 'No issue description available'}
                                  </Typography>
                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                                    {(content?.key_concerns || []).slice(0, 3).map((concern, i) => (
                                      <Chip
                                        key={i}
                                        label={concern}
                                        size="small"
                                        color="error"
                                        variant="outlined"
                                        sx={{ fontSize: '0.7rem' }}
                                      />
                                    ))}
                                  </Box>
                                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                                    <Button
                                      size="small"
                                      endIcon={<ArrowForwardIcon />}
                                      onClick={() => handleViewContent(content.id)}
                                      sx={{
                                        color: ACE_COLORS.PURPLE,
                                        '&:hover': {
                                          backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                                        }
                                      }}
                                    >
                                      View
                                    </Button>
                                  </Box>
                                </Box>
                              }
                            />
                          </ListItem>
                        ))}
                      </List>
                    )}
                  </Box>
                )}

                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Button
                    variant="outlined"
                    onClick={handleViewAllSentiment}
                    endIcon={<ArrowForwardIcon />}
                    sx={{
                      borderColor: ACE_COLORS.PURPLE,
                      color: ACE_COLORS.PURPLE,
                      '&:hover': {
                        borderColor: ACE_COLORS.PURPLE,
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                      }
                    }}
                  >
                    View Full Sentiment Analysis
                  </Button>
                </Box>
              </Box>
            </Fade>
          )}

          {/* Export Menu */}
          <Menu
            anchorEl={exportAnchorEl}
            open={state.showExportMenu}
            onClose={handleExportMenuClose}
            slotProps={{
              paper: {
                sx: {
                  mt: 1,
                  minWidth: 180,
                  boxShadow: theme.shadows[8],
                  border: `1px solid ${theme.palette.divider}`
                }
              }
            }}
          >
            <MenuItem onClick={() => {
              handleExport('csv');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <MenuListItemText>Export as CSV</MenuListItemText>
            </MenuItem>

            <MenuItem onClick={() => {
              handleExport('json');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <MenuListItemText>Export as JSON</MenuListItemText>
            </MenuItem>
          </Menu>

          {/* Upgrade Dialog */}
          <Dialog
            open={state.showUpgradeDialog}
            onClose={handleUpgradeDialogClose}
            maxWidth="md"
            fullWidth
            slotProps={{
              paper: {
                sx: {
                  borderRadius: 2,
                  boxShadow: theme.shadows[16]
                }
              }
            }}
          >
            <DialogTitle sx={{
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
              borderBottom: `1px solid ${theme.palette.divider}`
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
                <Typography variant="h6" component="span">
                  {getUpgradeDialogContent().title}
                </Typography>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ pt: 3 }}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {getUpgradeDialogContent().content}
                </Typography>

                <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                  Unlock Advanced Sentiment Summary Features:
                </Typography>

                <Grid container spacing={1}>
                  {getUpgradeDialogContent().features.map((feature, index) => (
                    <Grid item xs={12} sm={6} key={index}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                        <Typography variant="body2">{feature}</Typography>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Box>

              <Alert
                severity="info"
                sx={{
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                  border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
                }}
              >
                <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
                Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced sentiment summary features.
              </Alert>
            </DialogContent>
            <DialogActions sx={{ p: 3, pt: 2 }}>
              <Button
                onClick={handleUpgradeDialogClose}
                sx={{ color: theme.palette.text.secondary }}
              >
                Maybe Later
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: ACE_COLORS.PURPLE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                  }
                }}
                startIcon={<UpgradeIcon />}
              >
                Upgrade Now
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </Box>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
SentimentSummaryCard.propTypes = {
  // Core props
  data: PropTypes.shape({
    platform_sentiment: PropTypes.arrayOf(
      PropTypes.shape({
        platform: PropTypes.string,
        score: PropTypes.number,
      })
    ),
    platform_insights: PropTypes.arrayOf(
      PropTypes.shape({
        platform: PropTypes.string,
        insight: PropTypes.string,
      })
    ),
    top_content: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        title: PropTypes.string,
        excerpt: PropTypes.string,
        platform: PropTypes.string,
        sentiment_score: PropTypes.number,
        image_url: PropTypes.string,
      })
    ),
    negative_content: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string,
        title: PropTypes.string,
        platform: PropTypes.string,
        issue: PropTypes.string,
        key_concerns: PropTypes.arrayOf(PropTypes.string),
        image_url: PropTypes.string,
      })
    ),
  }),
  loading: PropTypes.bool,
  // Enhanced sentiment summary props
  summaryType: PropTypes.oneOf(['overall-summary', 'sentiment-highlights', 'sentiment-trends-summary', 'sentiment-insights-summary', 'sentiment-alerts-summary']),
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  customization: PropTypes.object,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

SentimentSummaryCard.defaultProps = {
  loading: false,
  summaryType: 'overall-summary',
  enableExport: false,
  realTimeUpdates: false,
  customization: {},
  className: '',
  style: {},
  testId: 'sentiment-summary-card'
};

// Display name for debugging
SentimentSummaryCard.displayName = 'SentimentSummaryCard';

export default SentimentSummaryCard;

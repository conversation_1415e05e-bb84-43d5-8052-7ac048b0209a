/**
 * @fileoverview useAnalytics hook for tracking user interactions and performance
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 */

import { useCallback, useContext } from 'react';
import { useAuth } from './useAuth';

/**
 * Analytics hook for tracking events, errors, and performance metrics
 * @returns {Object} Analytics functions
 */
export const useAnalytics = () => {
  const { user } = useAuth();

  /**
   * Track user events
   * @param {string} eventName - Name of the event
   * @param {Object} properties - Event properties
   * @param {Object} options - Tracking options
   */
  const trackEvent = useCallback((eventName, properties = {}, options = {}) => {
    try {
      // In development, just log to console
      if (process.env.NODE_ENV === 'development') {
        console.log('📊 Analytics Event:', {
          event: eventName,
          properties: {
            ...properties,
            userId: user?.id,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
          },
          options
        });
        return;
      }

      // In production, you would integrate with analytics services like:
      // - Google Analytics
      // - Mixpanel
      // - Amplitude
      // - Custom analytics API

      // Example integration (commented out):
      // if (window.gtag) {
      //   window.gtag('event', eventName, {
      //     ...properties,
      //     user_id: user?.id
      //   });
      // }

      // if (window.mixpanel) {
      //   window.mixpanel.track(eventName, {
      //     ...properties,
      //     distinct_id: user?.id
      //   });
      // }

    } catch (error) {
      console.warn('Failed to track event:', error);
    }
  }, [user?.id]);

  /**
   * Track errors
   * @param {Error|string} error - Error to track
   * @param {Object} context - Additional context
   */
  const trackError = useCallback((error, context = {}) => {
    try {
      const errorData = {
        message: error?.message || error,
        stack: error?.stack,
        name: error?.name,
        userId: user?.id,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        ...context
      };

      // In development, log to console
      if (process.env.NODE_ENV === 'development') {
        console.error('🚨 Analytics Error:', errorData);
        return;
      }

      // In production, integrate with error tracking services like:
      // - Sentry
      // - Bugsnag
      // - LogRocket
      // - Custom error API

      // Example integration (commented out):
      // if (window.Sentry) {
      //   window.Sentry.captureException(error, {
      //     user: { id: user?.id },
      //     extra: context
      //   });
      // }

    } catch (trackingError) {
      console.warn('Failed to track error:', trackingError);
    }
  }, [user?.id]);

  /**
   * Track performance metrics
   * @param {string} metricName - Name of the metric
   * @param {number} value - Metric value
   * @param {Object} tags - Additional tags
   */
  const trackPerformance = useCallback((metricName, value, tags = {}) => {
    try {
      const performanceData = {
        metric: metricName,
        value,
        tags: {
          ...tags,
          userId: user?.id,
          timestamp: new Date().toISOString(),
          url: window.location.href
        }
      };

      // In development, log to console
      if (process.env.NODE_ENV === 'development') {
        console.log('⚡ Analytics Performance:', performanceData);
        return;
      }

      // In production, integrate with performance monitoring services like:
      // - New Relic
      // - DataDog
      // - Custom metrics API

      // Example integration (commented out):
      // if (window.newrelic) {
      //   window.newrelic.recordMetric(metricName, value);
      // }

    } catch (error) {
      console.warn('Failed to track performance:', error);
    }
  }, [user?.id]);

  /**
   * Track page views
   * @param {string} pageName - Name of the page
   * @param {Object} properties - Page properties
   */
  const trackPageView = useCallback((pageName, properties = {}) => {
    trackEvent('page_view', {
      page: pageName,
      ...properties
    });
  }, [trackEvent]);

  /**
   * Track user interactions
   * @param {string} element - Element that was interacted with
   * @param {string} action - Type of interaction
   * @param {Object} properties - Additional properties
   */
  const trackInteraction = useCallback((element, action, properties = {}) => {
    trackEvent('user_interaction', {
      element,
      action,
      ...properties
    });
  }, [trackEvent]);

  /**
   * Track conversion events
   * @param {string} conversionType - Type of conversion
   * @param {Object} properties - Conversion properties
   */
  const trackConversion = useCallback((conversionType, properties = {}) => {
    trackEvent('conversion', {
      type: conversionType,
      ...properties
    });
  }, [trackEvent]);

  /**
   * Set user properties
   * @param {Object} properties - User properties to set
   */
  const setUserProperties = useCallback((properties) => {
    try {
      // In development, log to console
      if (process.env.NODE_ENV === 'development') {
        console.log('👤 Analytics User Properties:', properties);
        return;
      }

      // In production, set user properties in analytics services
      // Example integration (commented out):
      // if (window.mixpanel) {
      //   window.mixpanel.people.set(properties);
      // }

    } catch (error) {
      console.warn('Failed to set user properties:', error);
    }
  }, []);

  return {
    trackEvent,
    trackError,
    trackPerformance,
    trackPageView,
    trackInteraction,
    trackConversion,
    setUserProperties
  };
};

export default useAnalytics;

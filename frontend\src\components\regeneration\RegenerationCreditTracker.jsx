/**
 * Enhanced Regeneration Credit Tracker - Enterprise-grade credit tracking management component
 * Features: Comprehensive credit tracking management, real-time credit monitoring, plan-based limitations,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced credit capabilities and ACEO add-on marketplace integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  LinearProgress,
  Chip,
  Button,
  Divider,
  Alert,
  Stack,
  CircularProgress,
  alpha,
  Snackbar,
  useMediaQuery
} from '@mui/material';
import {
  AutoAwesome as AutoAwesomeIcon,
  TrendingUp as TrendingUpIcon,
  ShoppingCart as ShoppingCartIcon,
  Extension as ExtensionIcon
} from '@mui/icons-material';

import { useNavigate } from 'react-router-dom';
import { useAddons } from '../../hooks/useAddons';
import UsageIndicator from '../addons/UsageIndicator';
import UpgradeModal from '../addons/UpgradeModal';
import { useAccessibility } from '../../hooks/useAccessibility';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useEnhancedAccessibility = () => {
  const { announce } = useAccessibility();

  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announce, announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Credit tracking display modes with enhanced configurations
const CREDIT_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Credit Tracker',
    description: 'Basic credit tracking interface',
    features: ['basic_tracking', 'usage_analytics', 'credit_insights']
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Credit Analytics',
    description: 'Comprehensive credit management',
    features: ['detailed_tracking', 'credit_analytics', 'real_time_insights']
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Credit Management',
    description: 'AI-powered credit optimization and suggestions',
    features: ['ai_assisted', 'ai_optimization', 'credit_insights']
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Credit Dashboard',
    description: 'Advanced credit analytics and forecasting',
    features: ['analytics_tracking', 'credit_insights']
  }
};

/**
 * Enhanced Regeneration Credit Tracker Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Object} [props.creditInfo] - Credit information object
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onCreditAction] - Credit action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-credit-tracker'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const RegenerationCreditTracker = memo(forwardRef(({
  creditInfo,
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = true,
  onExport,
  onRefresh,
  onCreditAction,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-credit-tracker',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const navigate = useNavigate();
  const { getEnhancedLimits, getRelevantAddons } = useAddons();
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useEnhancedAccessibility();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  // Core state management
  const creditTrackerRef = useRef(null);
  const [enhancedLimits, setEnhancedLimits] = useState(null);
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Enhanced state management
  const [creditMode, setCreditMode] = useState('compact');
  const [creditHistory, setCreditHistory] = useState([]);
  const [creditAnalytics, setCreditAnalytics] = useState(null);
  const [creditInsights, setCreditInsights] = useState(null);
  const [customCreditConfigs, setCustomCreditConfigs] = useState([]);
  const [creditPreferences, setCreditPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: true,
    creditSuggestions: true,
    dismissTimeout: 10000
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [creditDrawerOpen, setCreditDrawerOpen] = useState(false);
  const [selectedCreditType, setSelectedCreditType] = useState(null);
  const [creditStats, setCreditStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [creditUsageScore, setCreditUsageScore] = useState(0);
  const [creditProgress, setCreditProgress] = useState(0);
  const [activeTab, setActiveTab] = useState(0);
  const [creditVersions, setCreditVersions] = useState([]);
  const [dailyUsage, setDailyUsage] = useState(0);
  const [monthlyUsage, setMonthlyUsage] = useState(0);
  const [creditForecast, setCreditForecast] = useState(null);

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastCreditCheck, setLastCreditCheck] = useState(Date.now());

  /**
   * ACE Social subscription integration with plan-based credit limitations - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // ACE Social platform plans with realistic credit limitations
    const features = {
      creator: {
        maxCreditsPerMonth: 50,
        maxDailyCredits: 5,
        creditCostMultiplier: 1.0,
        hasAdvancedTracking: false,
        hasCreditAnalytics: false,
        hasCustomCreditConfigs: false,
        hasCreditInsights: false,
        hasCreditHistory: true,
        hasAIAssistance: false,
        hasCreditExport: false,
        hasCreditScheduling: false,
        hasCreditAutomation: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1,
        allowedCreditTypes: ['basic_regeneration'],
        maxHistoryDays: 7,
        canPurchaseAddons: true,
        addonDiscountPercent: 0
      },
      accelerator: {
        maxCreditsPerMonth: 200,
        maxDailyCredits: 20,
        creditCostMultiplier: 0.8,
        hasAdvancedTracking: true,
        hasCreditAnalytics: true,
        hasCustomCreditConfigs: false,
        hasCreditInsights: true,
        hasCreditHistory: true,
        hasAIAssistance: true,
        hasCreditExport: true,
        hasCreditScheduling: false,
        hasCreditAutomation: false,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 2000,
        planName: 'Accelerator',
        planTier: 2,
        allowedCreditTypes: ['basic_regeneration', 'advanced_regeneration', 'bulk_regeneration'],
        maxHistoryDays: 30,
        canPurchaseAddons: true,
        addonDiscountPercent: 10
      },
      dominator: {
        maxCreditsPerMonth: -1, // Unlimited
        maxDailyCredits: -1, // Unlimited
        creditCostMultiplier: 0.5,
        hasAdvancedTracking: true,
        hasCreditAnalytics: true,
        hasCustomCreditConfigs: true,
        hasCreditInsights: true,
        hasCreditHistory: true,
        hasAIAssistance: true,
        hasCreditExport: true,
        hasCreditScheduling: true,
        hasCreditAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3,
        allowedCreditTypes: ['basic_regeneration', 'advanced_regeneration', 'bulk_regeneration', 'premium_regeneration', 'custom_regeneration'],
        maxHistoryDays: -1, // Unlimited
        canPurchaseAddons: true,
        addonDiscountPercent: 20
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxCreditsPerMonth === -1 || currentUsage < currentFeatures.maxCreditsPerMonth;
        return hasAccess && withinLimits;
      },
      getCreditCost: (baseCredits) => {
        return Math.ceil(baseCredits * currentFeatures.creditCostMultiplier);
      },
      canUseCreditType: (type) => {
        return currentFeatures.allowedCreditTypes.includes(type);
      },
      getAddonDiscount: (originalPrice) => {
        return originalPrice * (1 - currentFeatures.addonDiscountPercent / 100);
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Credit tracker with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Credit tracking interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: -1
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive credit API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getCreditHistory: () => creditHistory,
    getCreditAnalytics: () => creditAnalytics,
    getCreditInsights: () => creditInsights,
    refreshCredits: () => {
      fetchCreditAnalytics();
      if (onRefresh) onRefresh();
    },

    // Credit methods
    focusCredit: () => {
      if (creditTrackerRef.current) {
        creditTrackerRef.current.focus();
      }
    },
    getCreditUsageScore: () => creditUsageScore,
    getCreditProgress: () => creditProgress,
    getDailyUsage: () => dailyUsage,
    getMonthlyUsage: () => monthlyUsage,
    getCreditForecast: () => creditForecast,
    openCreditDrawer: () => setCreditDrawerOpen(true),
    closeCreditDrawer: () => setCreditDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportCreditData: () => {
      if (onExport) {
        onExport(creditHistory, creditAnalytics);
      }
    },

    // Accessibility methods
    announceCredit: (message) => announceToScreenReader(message),
    focusCreditField: () => setFocusToElement('credit-tracker-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => creditMode,
    getCreditStats: () => creditStats,
    getSelectedCreditType: () => selectedCreditType,
    getCustomCreditConfigs: () => customCreditConfigs,
    getCreditDrawerOpen: () => creditDrawerOpen,
    getShowAnalytics: () => showAnalytics,
    getFullscreenMode: () => fullscreenMode,
    getActiveTab: () => activeTab,
    setActiveTab: (tab) => setActiveTab(tab),
    addCustomCreditConfig,
    handleCreditModeChange,
    updateCreditPreferences,
    handleCreditTypeSelection,
    validateCreditConfig,
    getCreditVersions: () => creditVersions,
    switchToVersion: (versionId) => switchToCreditVersion(versionId),
    purchaseCredits: () => handleCreditPurchase()
  }), [
    creditHistory,
    creditAnalytics,
    creditInsights,
    creditStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    validationErrors,
    creditMode,
    selectedCreditType,
    customCreditConfigs,
    creditUsageScore,
    creditProgress,
    dailyUsage,
    monthlyUsage,
    creditForecast,
    creditVersions,
    addCustomCreditConfig,
    handleCreditModeChange,
    updateCreditPreferences,
    handleCreditTypeSelection,
    validateCreditConfig,
    switchToCreditVersion,
    handleCreditPurchase,
    activeTab,
    fullscreenMode,
    creditDrawerOpen,
    showAnalytics,
    fetchCreditAnalytics
  ]);

  // Fetch credit analytics with enhanced error handling and retry logic
  const fetchCreditAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/credits/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setCreditAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (creditPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Credit analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch credit analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load credit analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, creditPreferences.showAnalytics]);

  useEffect(() => {
    const loadEnhancedLimits = async () => {
      try {
        const limits = await getEnhancedLimits('regeneration_credits');
        setEnhancedLimits(limits);

        // Also fetch analytics and insights
        fetchCreditAnalytics();
        fetchCreditInsights();
      } catch (error) {
        console.error('Error loading enhanced limits:', error);
      }
    };

    loadEnhancedLimits();
  }, [getEnhancedLimits, fetchCreditAnalytics, fetchCreditInsights]);

  // Fetch credit insights
  const fetchCreditInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/credits/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setCreditInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch credit insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Handle credit mode switching
  const handleCreditModeChange = useCallback((newMode) => {
    if (CREDIT_MODES[newMode.toUpperCase()]) {
      setCreditMode(newMode);
      announceToScreenReader(`Credit mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: subscription?.user_id
      };

      setCreditHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (creditPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} credit mode`);
      }
    }
  }, [announceToScreenReader, subscription?.user_id, creditPreferences.showAnalytics, showSuccess]);

  // Handle custom credit config management
  const addCustomCreditConfig = useCallback((configData) => {
    const newConfig = {
      id: Date.now(),
      ...configData,
      createdAt: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCustomCreditConfigs(prev => [...prev, newConfig]);

    // Track config creation
    const configRecord = {
      id: Date.now(),
      type: 'custom_config_created',
      config: newConfig.name,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCreditHistory(prev => [configRecord, ...prev.slice(0, 99)]);

    if (creditPreferences.showAnalytics) {
      showSuccess(`Custom credit config "${configData.name}" created`);
    }
  }, [subscription?.user_id, creditPreferences.showAnalytics, showSuccess]);

  // Handle credit preferences updates
  const updateCreditPreferences = useCallback((newPreferences) => {
    setCreditPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCreditHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (creditPreferences.showAnalytics) {
      showSuccess('Credit preferences updated');
    }
  }, [subscription?.user_id, creditPreferences.showAnalytics, showSuccess]);

  // Handle credit type selection
  const handleCreditTypeSelection = useCallback((creditType) => {
    setSelectedCreditType(creditType);

    // Track credit type selection
    const typeRecord = {
      id: Date.now(),
      type: 'credit_type_selected',
      creditType,
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id
    };

    setCreditHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (creditPreferences.showAnalytics) {
      announceToScreenReader(`Selected credit type: ${creditType}`);
    }
  }, [subscription?.user_id, creditPreferences.showAnalytics, announceToScreenReader]);

  // Handle validation errors
  const validateCreditConfig = useCallback((configData) => {
    const errors = {};

    if (!configData.name?.trim()) {
      errors.name = 'Config name is required';
    }
    if (!configData.type?.trim()) {
      errors.type = 'Credit type is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Switch to credit version
  const switchToCreditVersion = useCallback((versionId) => {
    const version = creditVersions.find(v => v.id === versionId);
    if (version) {
      setCreditUsageScore(version.usageScore || 0);

      if (creditPreferences.showAnalytics) {
        showSuccess(`Switched to version ${version.name}`);
      }
    }
  }, [creditVersions, creditPreferences.showAnalytics, showSuccess]);

  // Handle credit purchase
  const handleCreditPurchase = useCallback(() => {
    setUpgradeModalOpen(true);

    // Track purchase attempt
    const purchaseRecord = {
      id: Date.now(),
      type: 'purchase_attempt',
      timestamp: new Date().toISOString(),
      userId: subscription?.user_id,
      planTier: subscriptionFeatures.planTier
    };

    setCreditHistory(prev => [purchaseRecord, ...prev.slice(0, 99)]);

    if (onCreditAction) {
      onCreditAction('purchase_initiated', { planTier: subscriptionFeatures.planTier });
    }
  }, [subscription?.user_id, subscriptionFeatures.planTier, onCreditAction]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && creditInfo) {
      // Optimize credit management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchCreditAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, creditInfo, fetchCreditAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastCreditCheck(Date.now());

          if (wasUnavailable && creditPreferences.showAnalytics) {
            showSuccess("Connection restored - Credit features available");
          }
        } else {
          setBackendAvailable(false);
          if (creditPreferences.showAnalytics) {
            showError("Backend service unavailable - Some credit features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastCreditCheck;
          if (timeSinceLastCheck > 60000 && creditPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Credit tracking may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastCreditCheck, creditPreferences.showAnalytics, showSuccess, showError]);

  // Generate AI suggestions when credits change
  useEffect(() => {
    if (enableAIInsights && creditPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [enableAIInsights, creditPreferences.aiAssistance, generateAISuggestions]);

  // Generate AI suggestions function
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/credits/ai-suggestions', {
        params: {
          currentCredits: creditInfo?.remaining_credits || 0,
          monthlyUsage: monthlyUsage,
          planTier: subscriptionFeatures.planTier
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (creditPreferences.showAnalytics) {
        showSuccess('AI credit suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [creditInfo?.remaining_credits, monthlyUsage, subscriptionFeatures.planTier, creditPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when credits change
  useEffect(() => {
    if (enableAdvancedFeatures) {
      fetchCreditStats();
    }
  }, [enableAdvancedFeatures, fetchCreditStats]);

  // Fetch credit stats function
  const fetchCreditStats = useCallback(async () => {
    try {
      const response = await api.get('/api/credits/stats');
      setCreditStats(response.data);
    } catch (error) {
      console.error('Failed to fetch credit stats:', error);
    }
  }, []);

  // Process credit info data
  const creditData = useMemo(() => {
    if (!creditInfo) return null;

    const {
      remaining_credits = 0,
      regeneration_buffer = 0,
      current_month_usage = {},
      efficiency_metrics = {},
      usage_forecast = {}
    } = creditInfo;

    // Apply plan-based credit calculations
    const planAdjustedCredits = subscriptionFeatures.getCreditCost(remaining_credits);
    const totalCredits = planAdjustedCredits + regeneration_buffer;
    const usedCredits = current_month_usage.total_credits_used || 0;
    const totalRegenerations = current_month_usage.total_regenerations || 0;
    const efficiencyScore = efficiency_metrics.efficiency_score || 0;

    // Calculate usage percentage with plan limits
    const monthlyLimit = subscriptionFeatures.maxCreditsPerMonth;
    const usagePercentage = monthlyLimit > 0 ? ((usedCredits / monthlyLimit) * 100) : 0;

    // Determine warning level based on plan limits
    const isLowCredits = remaining_credits < (subscriptionFeatures.maxDailyCredits * 2);
    const isCriticalCredits = remaining_credits < subscriptionFeatures.maxDailyCredits;
    const isOverMonthlyLimit = monthlyLimit !== -1 && usedCredits >= monthlyLimit;

    return {
      remaining_credits,
      regeneration_buffer,
      current_month_usage,
      efficiency_metrics,
      usage_forecast,
      planAdjustedCredits,
      totalCredits,
      usedCredits,
      totalRegenerations,
      efficiencyScore,
      monthlyLimit,
      usagePercentage,
      isLowCredits,
      isCriticalCredits,
      isOverMonthlyLimit
    };
  }, [creditInfo, subscriptionFeatures]);

  // Check if user can use credits based on plan
  const canUseCredits = useMemo(() => {
    if (!creditData) return false;
    if (creditData.monthlyLimit !== -1 && creditData.usedCredits >= creditData.monthlyLimit) return false;
    if (subscriptionFeatures.maxDailyCredits !== -1 && dailyUsage >= subscriptionFeatures.maxDailyCredits) return false;
    return creditData.remaining_credits > 0;
  }, [creditData, subscriptionFeatures.maxDailyCredits, dailyUsage]);

  // Get progress bar color based on plan usage
  const getProgressColor = useCallback(() => {
    if (!creditData) return ACE_COLORS.PURPLE;
    if (creditData.isCriticalCredits || creditData.isOverMonthlyLimit) return ACE_COLORS.DARK;
    if (creditData.isLowCredits) return ACE_COLORS.YELLOW;
    return ACE_COLORS.PURPLE;
  }, [creditData]);

  // Get limitation message for UI
  const getLimitationMessage = useCallback(() => {
    if (!creditData) return null;

    if (creditData.isOverMonthlyLimit) {
      return `Monthly credit limit reached (${creditData.monthlyLimit}). Upgrade to ${subscriptionFeatures.planTier < 3 ? 'Dominator' : 'higher'} plan for more credits`;
    }

    if (subscriptionFeatures.maxDailyCredits !== -1 && dailyUsage >= subscriptionFeatures.maxDailyCredits) {
      return `Daily credit limit reached (${subscriptionFeatures.maxDailyCredits}). Try again tomorrow or upgrade your plan`;
    }

    if (creditData.isCriticalCredits) {
      return `Critical: Only ${creditData.remaining_credits} credits remaining. Purchase more credits or upgrade your plan`;
    }

    return null;
  }, [creditData, subscriptionFeatures, dailyUsage]);

  // Update usage tracking when credit data changes
  useEffect(() => {
    if (creditData) {
      setDailyUsage(prev => Math.max(prev, creditData.usedCredits));
      setMonthlyUsage(creditData.usedCredits);
      setCreditProgress(creditData.usagePercentage);
      setCreditForecast(creditData.usage_forecast);

      // Create version tracking
      const newVersion = {
        id: Date.now(),
        name: `Credit State ${Date.now()}`,
        usageScore: creditData.efficiencyScore,
        timestamp: new Date().toISOString()
      };
      setCreditVersions(prev => [newVersion, ...prev.slice(0, 9)]); // Keep last 10 versions
    }
  }, [creditData]);

  if (!creditInfo) {
    return (
      <Box
        {...getAccessibilityProps()}
        ref={creditTrackerRef}
        sx={{
          ...sx,
          ...customization,
          textAlign: 'center',
          py: 2
        }}
        className={className}
        style={style}
        data-testid={testId}
      >
        <CircularProgress size={24} sx={{ color: ACE_COLORS.PURPLE }} />
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Loading credit information...
        </Typography>
      </Box>
    );
  }

  // Extract data from creditData for easier access
  const {
    remaining_credits,
    regeneration_buffer,
    totalCredits,
    usedCredits,
    totalRegenerations,
    efficiencyScore,
    usagePercentage,
    isLowCredits,
    isCriticalCredits,
    usage_forecast
  } = creditData || {};

  return (
    <Box
      {...getAccessibilityProps()}
      ref={creditTrackerRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', color: ACE_COLORS.DARK }}>
          <AutoAwesomeIcon sx={{ mr: 1, color: ACE_COLORS.PURPLE }} />
          Regeneration Credits
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label={subscriptionFeatures.planName}
            size="small"
            sx={{
              bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
              color: ACE_COLORS.PURPLE,
              fontWeight: 'bold'
            }}
          />
          {!isMobile && subscriptionFeatures.maxCreditsPerMonth !== -1 && (
            <Typography variant="caption" color="text.secondary">
              {subscriptionFeatures.maxCreditsPerMonth - usedCredits} credits remaining this month
            </Typography>
          )}
          {!isMobile && subscriptionFeatures.maxCreditsPerMonth === -1 && (
            <Typography variant="caption" sx={{ color: ACE_COLORS.PURPLE, fontWeight: 'bold' }}>
              Unlimited credits
            </Typography>
          )}
          {isMobile && (
            <Typography variant="caption" sx={{ color: ACE_COLORS.PURPLE, fontWeight: 'bold' }}>
              {subscriptionFeatures.maxCreditsPerMonth === -1 ? 'Unlimited' : `${subscriptionFeatures.maxCreditsPerMonth - usedCredits} left`}
            </Typography>
          )}
        </Box>
      </Box>

      <Divider sx={{ mb: 2, borderColor: alpha(ACE_COLORS.PURPLE, 0.2) }} />

      {/* Plan-based Credit Status Alerts */}
      {!canUseCredits && (
        <Alert
          severity="error"
          sx={{
            mb: 2,
            bgcolor: alpha(ACE_COLORS.DARK, 0.1),
            border: `1px solid ${alpha(ACE_COLORS.DARK, 0.3)}`
          }}
          action={
            <Button
              size="small"
              sx={{
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
              onClick={() => {
                window.open('/billing/upgrade', '_blank');
              }}
            >
              Upgrade Plan
            </Button>
          }
        >
          <Typography variant="body2">
            <strong>{getLimitationMessage()}</strong>
          </Typography>
        </Alert>
      )}

      {canUseCredits && isCriticalCredits && (
        <Alert
          severity="error"
          sx={{
            mb: 2,
            bgcolor: alpha(ACE_COLORS.DARK, 0.1),
            border: `1px solid ${alpha(ACE_COLORS.DARK, 0.3)}`
          }}
          action={
            <Button
              size="small"
              sx={{
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
              onClick={handleCreditPurchase}
            >
              Buy Credits
            </Button>
          }
        >
          <strong>Critical:</strong> Only {remaining_credits} credits remaining!
        </Alert>
      )}

      {canUseCredits && isLowCredits && !isCriticalCredits && (
        <Alert
          severity="warning"
          sx={{
            mb: 2,
            bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
            border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
          }}
          action={
            <Button
              size="small"
              sx={{
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
              onClick={handleCreditPurchase}
            >
              Buy More
            </Button>
          }
        >
          <strong>Low credits:</strong> {remaining_credits} credits remaining
        </Alert>
      )}

      {/* Plan benefits display */}
      {subscriptionFeatures.creditCostMultiplier < 1 && (
        <Alert
          severity="success"
          sx={{
            mb: 2,
            bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
          }}
        >
          <Typography variant="body2">
            <strong>{subscriptionFeatures.planName} Benefit:</strong> {Math.round((1 - subscriptionFeatures.creditCostMultiplier) * 100)}% credit discount applied!
          </Typography>
        </Alert>
      )}

      {/* Enhanced Credits Overview with Add-ons */}
      <Box sx={{ mb: 3 }}>
        {enhancedLimits ? (
          <UsageIndicator
            current={enhancedLimits.total_limit - enhancedLimits.remaining}
            total={enhancedLimits.total_limit}
            label="Regeneration Credits"
            showAlert={true}
          />
        ) : (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Credits Remaining
              </Typography>
              <Typography variant="body2" fontWeight="bold">
                {remaining_credits}
              </Typography>
            </Box>

            {regeneration_buffer > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Buffer Credits
                </Typography>
                <Typography variant="body2" fontWeight="bold" color="success.main">
                  +{regeneration_buffer}
                </Typography>
              </Box>
            )}

            <LinearProgress
              variant="determinate"
              value={Math.min(usagePercentage, 100)}
              color={getProgressColor()}
              sx={{ height: 8, borderRadius: 4, mb: 1 }}
            />

            <Typography variant="caption" color="text.secondary">
              {usedCredits.toFixed(1)} of {totalCredits} credits used this month
            </Typography>
          </>
        )}

        {/* Add-on bonus display */}
        {enhancedLimits && enhancedLimits.addon_bonus > 0 && (
          <Alert severity="success" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Add-on Bonus:</strong> +{enhancedLimits.addon_bonus} credits from your add-ons!
            </Typography>
          </Alert>
        )}
      </Box>

      {/* Usage Stats */}
      <Stack spacing={2} sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="body2" color="text.secondary">
            Total Regenerations
          </Typography>
          <Chip 
            label={totalRegenerations} 
            size="small" 
            color="primary" 
            variant="outlined"
          />
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="body2" color="text.secondary">
            Efficiency Score
          </Typography>
          <Chip 
            label={`${Math.round(efficiencyScore * 100)}%`}
            size="small" 
            color={efficiencyScore > 0.8 ? 'success' : efficiencyScore > 0.6 ? 'warning' : 'error'}
            icon={<TrendingUpIcon />}
          />
        </Box>
      </Stack>

      {/* Plan-based Credit Pricing */}
      <Box sx={{
        p: 2,
        bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
        borderRadius: 1,
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        mb: 2
      }}>
        <Typography variant="subtitle2" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
          {subscriptionFeatures.planName} Plan Credit Pricing
        </Typography>
        <Stack spacing={0.5}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="caption">1st regeneration</Typography>
            <Chip label="FREE" size="small" sx={{ bgcolor: ACE_COLORS.PURPLE, color: ACE_COLORS.WHITE }} />
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="caption">2nd regeneration</Typography>
            <Chip
              label={`${subscriptionFeatures.getCreditCost(0.5)} credits`}
              size="small"
              sx={{ bgcolor: ACE_COLORS.YELLOW, color: ACE_COLORS.DARK }}
            />
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="caption">3rd+ regenerations</Typography>
            <Chip
              label={`${subscriptionFeatures.getCreditCost(1.0)} credit${subscriptionFeatures.getCreditCost(1.0) !== 1 ? 's' : ''}`}
              size="small"
              sx={{ bgcolor: ACE_COLORS.DARK, color: ACE_COLORS.WHITE }}
            />
          </Box>
          {subscriptionFeatures.creditCostMultiplier < 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1, pt: 1, borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
              <Typography variant="caption" sx={{ fontWeight: 'bold', color: ACE_COLORS.PURPLE }}>
                Plan Discount Applied
              </Typography>
              <Chip
                label={`${Math.round((1 - subscriptionFeatures.creditCostMultiplier) * 100)}% OFF`}
                size="small"
                sx={{ bgcolor: ACE_COLORS.PURPLE, color: ACE_COLORS.WHITE, fontWeight: 'bold' }}
              />
            </Box>
          )}
        </Stack>
      </Box>

      {/* Enhanced Action Buttons with Plan Integration */}
      <Stack spacing={1}>
        {(isLowCredits || isCriticalCredits || !canUseCredits) && (
          <Button
            variant="contained"
            startIcon={loading ? <CircularProgress size={16} /> : <ExtensionIcon />}
            onClick={handleCreditPurchase}
            disabled={loading}
            fullWidth
            sx={{
              bgcolor: ACE_COLORS.PURPLE,
              '&:hover': {
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.8)
              },
              '&:disabled': {
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.3)
              }
            }}
          >
            {loading ? 'Processing...' : 'Get More Credits'}
          </Button>
        )}

        <Button
          variant="outlined"
          startIcon={<ShoppingCartIcon />}
          onClick={async () => {
            try {
              setLoading(true);

              // Get relevant addons for this user's plan
              const relevantAddons = await getRelevantAddons('regeneration_credits');

              // Track navigation
              const navRecord = {
                id: Date.now(),
                type: 'navigation',
                destination: '/addons',
                timestamp: new Date().toISOString(),
                userId: subscription?.user_id,
                planTier: subscriptionFeatures.planTier
              };

              setCreditHistory(prev => [navRecord, ...prev.slice(0, 99)]);

              navigate('/addons', {
                state: {
                  relevantAddons,
                  context: 'credit_tracker',
                  planTier: subscriptionFeatures.planTier
                }
              });
            } catch (error) {
              console.error('Error loading addons:', error);
              navigate('/addons');
            } finally {
              setLoading(false);
            }
          }}
          disabled={loading}
          fullWidth
          sx={{
            borderColor: ACE_COLORS.PURPLE,
            color: ACE_COLORS.PURPLE,
            '&:hover': {
              borderColor: alpha(ACE_COLORS.PURPLE, 0.8),
              bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
            }
          }}
        >
          Browse Add-ons {subscriptionFeatures.addonDiscountPercent > 0 && `(${subscriptionFeatures.addonDiscountPercent}% off)`}
        </Button>

        {subscriptionFeatures.hasAnalytics && (
          <Button
            variant="outlined"
            onClick={() => {
              // Track analytics navigation
              const analyticsRecord = {
                id: Date.now(),
                type: 'analytics_navigation',
                timestamp: new Date().toISOString(),
                userId: subscription?.user_id,
                planTier: subscriptionFeatures.planTier
              };

              setCreditHistory(prev => [analyticsRecord, ...prev.slice(0, 99)]);

              navigate('/regeneration/analytics', {
                state: {
                  context: 'credit_tracker',
                  planTier: subscriptionFeatures.planTier
                }
              });
            }}
            fullWidth
            sx={{
              borderColor: ACE_COLORS.PURPLE,
              color: ACE_COLORS.PURPLE,
              '&:hover': {
                borderColor: alpha(ACE_COLORS.PURPLE, 0.8),
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
              }
            }}
          >
            View Analytics
          </Button>
        )}

        {!subscriptionFeatures.hasAnalytics && (
          <Button
            variant="outlined"
            onClick={() => {
              window.open('/billing/upgrade', '_blank');
            }}
            fullWidth
            sx={{
              borderColor: alpha(ACE_COLORS.PURPLE, 0.5),
              color: alpha(ACE_COLORS.PURPLE, 0.7),
              '&:hover': {
                borderColor: ACE_COLORS.PURPLE,
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
              }
            }}
          >
            Unlock Analytics ({subscriptionFeatures.planTier < 2 ? 'Accelerator' : 'Dominator'}+ Plan)
          </Button>
        )}
      </Stack>

      {/* Usage Forecast */}
      {usage_forecast && usage_forecast.projected_monthly_credits && (
        <Box sx={{ mt: 2, p: 1.5, bgcolor: 'info.light', borderRadius: 1 }}>
          <Typography variant="caption" color="info.dark">
            <strong>Forecast:</strong> You&apos;re projected to use{' '}
            {usage_forecast.projected_monthly_credits.toFixed(1)} credits this month
            {usage_forecast.projected_monthly_credits > totalCredits && (
              <span> - consider upgrading your plan!</span>
            )}
          </Typography>
        </Box>
      )}

      {/* Enhanced Upgrade Modal with ACEO Integration */}
      <UpgradeModal
        open={upgradeModalOpen}
        onClose={() => setUpgradeModalOpen(false)}
        usageType="regeneration_credits"
        feature="content_regeneration"
        context="limit_reached"
        planTier={subscriptionFeatures.planTier}
        currentPlan={subscriptionFeatures.planName}
        discount={subscriptionFeatures.addonDiscountPercent}
        onPurchase={async (addonId, variant) => {
          try {
            setLoading(true);

            // Track purchase
            const purchaseRecord = {
              id: Date.now(),
              type: 'addon_purchased',
              addonId,
              variant,
              timestamp: new Date().toISOString(),
              userId: subscription?.user_id,
              planTier: subscriptionFeatures.planTier
            };

            setCreditHistory(prev => [purchaseRecord, ...prev.slice(0, 99)]);

            if (onCreditAction) {
              onCreditAction('addon_purchased', { addonId, variant, planTier: subscriptionFeatures.planTier });
            }

            showSuccess('Add-on purchased successfully! Credits will be available shortly.');
            announceToScreenReader('Add-on purchase completed successfully');
            setUpgradeModalOpen(false);

            // Refresh credit data
            setTimeout(() => {
              fetchCreditAnalytics();
            }, 2000);

          } catch (error) {
            console.error('Purchase failed:', error);
            showError('Purchase failed. Please try again.');
          } finally {
            setLoading(false);
          }
        }}
      />

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying credit sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}

      {/* Credit Progress Indicator */}
      {creditProgress > 0 && creditProgress < 100 && (
        <Box sx={{
          position: 'fixed',
          bottom: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          p: 2,
          borderRadius: 2,
          bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
          border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          zIndex: 9999,
          minWidth: 200
        }}>
          <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
            Processing credits...
          </Typography>
          <LinearProgress
            variant="determinate"
            value={creditProgress}
            sx={{
              '& .MuiLinearProgress-bar': {
                backgroundColor: ACE_COLORS.PURPLE
              }
            }}
          />
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
RegenerationCreditTracker.propTypes = {
  // Core props
  creditInfo: PropTypes.object,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onCreditAction: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

RegenerationCreditTracker.displayName = 'RegenerationCreditTracker';

export default RegenerationCreditTracker;

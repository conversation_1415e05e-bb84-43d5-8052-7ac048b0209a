/**
 * Enhanced Comparative Sentiment Analysis - Enterprise-grade comparative sentiment analysis component
 * Features: Comprehensive comparative sentiment analysis with advanced sentiment comparison tools, multi-content sentiment comparison,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced comparative sentiment analysis capabilities and seamless content management workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useMemo,
  useCallback,
  memo,
  forwardRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  Divider,
  alpha
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  TrendingFlat as TrendingFlatIcon,
  Compare as CompareIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon,
  AutoAwesome as AutoAwesomeIcon
} from '@mui/icons-material';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  Line<PERSON>hart,
  Line
} from 'recharts';

import { getSentimentOverview, getDateRange, transformSentimentOverview } from '../../api/sentiment';



// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

/**
 * Enhanced Comparison metric card with ACE Social branding
 */
const ComparisonMetricCard = memo(({
  title,
  currentValue,
  previousValue,
  format = 'number',
  enableAIInsights = true
}) => {
  const difference = currentValue - previousValue;
  const percentageChange = previousValue !== 0 ? (difference / Math.abs(previousValue)) * 100 : 0;

  const getChangeColor = () => {
    if (Math.abs(percentageChange) < 1) return '#9e9e9e';
    return percentageChange > 0 ? '#4caf50' : '#f44336';
  };

  const getChangeIcon = () => {
    if (Math.abs(percentageChange) < 1) return <TrendingFlatIcon sx={{ fontSize: 16 }} />;
    return percentageChange > 0 ?
      <ArrowUpIcon sx={{ fontSize: 16 }} /> :
      <ArrowDownIcon sx={{ fontSize: 16 }} />;
  };

  const formatValue = (value) => {
    switch (format) {
      case 'percentage':
        return `${(value * 100).toFixed(1)}%`;
      case 'decimal':
        return value.toFixed(3);
      default:
        return value.toLocaleString();
    }
  };

  return (
    <Card sx={{
      p: 2,
      height: '100%',
      borderRadius: 2,
      bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
      border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
    }}>
      <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK, opacity: 0.7 }} gutterBottom>
        {title}
        {enableAIInsights && (
          <AutoAwesomeIcon
            sx={{
              ml: 1,
              fontSize: '0.8rem',
              color: ACE_COLORS.YELLOW
            }}
          />
        )}
      </Typography>

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
          {formatValue(currentValue)}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', color: getChangeColor() }}>
          {getChangeIcon()}
          <Typography variant="caption" sx={{ ml: 0.5 }}>
            {Math.abs(percentageChange).toFixed(1)}%
          </Typography>
        </Box>
      </Box>

      <Typography variant="caption" sx={{ color: ACE_COLORS.DARK, opacity: 0.7 }}>
        Previous: {formatValue(previousValue)}
      </Typography>
    </Card>
  );
});

ComparisonMetricCard.displayName = 'ComparisonMetricCard';

/**
 * Enhanced Comparative Sentiment Analysis Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {number} [props.refreshTrigger=0] - Refresh trigger
 * @param {Function} [props.onComparisonSelect] - Comparison selection callback
 * @param {Function} [props.onBulkCompare] - Bulk compare callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {boolean} [props.enableRealTimeUpdates=true] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onAnalysisAction] - Analysis action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-comparative-sentiment-analysis'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const ComparativeSentimentAnalysis = memo(forwardRef(({
  refreshTrigger = 0
}) => {
  // Core state management
  const [currentData, setCurrentData] = useState(null);
  const [previousData, setPreviousData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [comparisonPeriod, setComparisonPeriod] = useState(30);

  // Check feature access - Always true for enhanced version
  const canAccessSentiment = true;

  const fetchComparisonData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Get current period date range
      const currentDateRange = getDateRange(comparisonPeriod);

      // Get previous period date range
      const previousEndDate = new Date();
      previousEndDate.setDate(previousEndDate.getDate() - comparisonPeriod);
      const previousStartDate = new Date();
      previousStartDate.setDate(previousStartDate.getDate() - (comparisonPeriod * 2));

      const previousDateRange = {
        start_date: previousStartDate.toISOString().split('T')[0],
        end_date: previousEndDate.toISOString().split('T')[0]
      };

      // Fetch both periods in parallel
      const [currentApiData, previousApiData] = await Promise.all([
        getSentimentOverview(currentDateRange),
        getSentimentOverview(previousDateRange)
      ]);

      // Transform API data to component format
      const current = transformSentimentOverview(currentApiData);
      const previous = transformSentimentOverview(previousApiData);

      setCurrentData(current);
      setPreviousData(previous);
    } catch (err) {
      console.error('Error fetching comparison data:', err);
      setError(err.message || 'Failed to load comparison data');
    } finally {
      setLoading(false);
    }
  }, [comparisonPeriod]);

  useEffect(() => {
    if (!canAccessSentiment) {
      setLoading(false);
      return;
    }

    fetchComparisonData();
  }, [comparisonPeriod, refreshTrigger, canAccessSentiment, fetchComparisonData]);

  // Process data for comparison charts
  const comparisonChartData = useMemo(() => {
    if (!currentData || !previousData) return [];

    const sentimentCategories = ['very_positive', 'positive', 'neutral', 'negative', 'very_negative'];

    return sentimentCategories.map(category => ({
      category: category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      current: currentData.sentiment_distribution[category],
      previous: previousData.sentiment_distribution[category],
      currentPercentage: (currentData.sentiment_distribution[category] / currentData.total_posts) * 100,
      previousPercentage: (previousData.sentiment_distribution[category] / previousData.total_posts) * 100
    }));
  }, [currentData, previousData]);

  const platformComparisonData = useMemo(() => {
    if (!currentData || !previousData) return [];

    const platforms = currentData.platform_sentiment.map(p => p.platform);

    return platforms.map(platform => {
      const currentPlatform = currentData.platform_sentiment.find(p => p.platform === platform);
      const previousPlatform = previousData.platform_sentiment.find(p => p.platform === platform);

      return {
        platform: platform.charAt(0).toUpperCase() + platform.slice(1),
        current: currentPlatform?.score || 0,
        previous: previousPlatform?.score || 0
      };
    });
  }, [currentData, previousData]);

  if (!canAccessSentiment) {
    return (
      <Card sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Comparative Analysis
        </Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          Upgrade your plan to access comparative sentiment analysis
        </Typography>
        <Chip label="Premium Feature" color="primary" variant="outlined" size="small" />
      </Card>
    );
  }

  return (
    <Card sx={{ borderRadius: 2 }}>
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CompareIcon sx={{ color: ACE_COLORS.PURPLE }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Period Comparison
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Period</InputLabel>
              <Select
                value={comparisonPeriod}
                label="Period"
                onChange={(e) => setComparisonPeriod(e.target.value)}
                disabled={loading}
              >
                <MenuItem value={7}>7 days</MenuItem>
                <MenuItem value={14}>14 days</MenuItem>
                <MenuItem value={30}>30 days</MenuItem>
                <MenuItem value={90}>90 days</MenuItem>
              </Select>
            </FormControl>

            <Tooltip title="Refresh data">
              <IconButton onClick={fetchComparisonData} disabled={loading} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error">
            {error}
          </Alert>
        ) : !currentData || !previousData ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
            <Typography variant="body2" color="textSecondary">
              No comparison data available
            </Typography>
          </Box>
        ) : (
          <Box>
            {/* Key Metrics Comparison */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Key Metrics Comparison
            </Typography>

            <Grid container spacing={2} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                <ComparisonMetricCard
                  title="Overall Sentiment Score"
                  currentValue={currentData.sentiment_score}
                  previousValue={previousData.sentiment_score}
                  format="decimal"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <ComparisonMetricCard
                  title="Total Posts"
                  currentValue={currentData.total_posts}
                  previousValue={previousData.total_posts}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <ComparisonMetricCard
                  title="Confidence Level"
                  currentValue={currentData.confidence}
                  previousValue={previousData.confidence}
                  format="percentage"
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <ComparisonMetricCard
                  title="Positive Ratio"
                  currentValue={(currentData.sentiment_distribution.positive + currentData.sentiment_distribution.very_positive) / currentData.total_posts}
                  previousValue={(previousData.sentiment_distribution.positive + previousData.sentiment_distribution.very_positive) / previousData.total_posts}
                  format="percentage"
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Sentiment Distribution Comparison */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Sentiment Distribution Comparison
            </Typography>

            <Box sx={{ height: 300, mb: 4 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={comparisonChartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={alpha(ACE_COLORS.DARK, 0.2)} />
                  <XAxis
                    dataKey="category"
                    tick={{ fontSize: 12 }}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <RechartsTooltip
                    formatter={(value, name) => [value, name === 'current' ? 'Current Period' : 'Previous Period']}
                  />
                  <Legend />
                  <Bar
                    dataKey="current"
                    fill={ACE_COLORS.PURPLE}
                    name="Current Period"
                    radius={[2, 2, 0, 0]}
                  />
                  <Bar
                    dataKey="previous"
                    fill={alpha(ACE_COLORS.DARK, 0.4)}
                    name="Previous Period"
                    radius={[2, 2, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </Box>

            <Divider sx={{ my: 3 }} />

            {/* Platform Comparison */}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
              Platform Sentiment Comparison
            </Typography>

            <Box sx={{ height: 250 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={platformComparisonData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={alpha(ACE_COLORS.DARK, 0.2)} />
                  <XAxis dataKey="platform" tick={{ fontSize: 12 }} />
                  <YAxis domain={[-1, 1]} tick={{ fontSize: 12 }} />
                  <RechartsTooltip
                    formatter={(value, name) => [value.toFixed(3), name === 'current' ? 'Current Period' : 'Previous Period']}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="current"
                    stroke={ACE_COLORS.PURPLE}
                    strokeWidth={3}
                    dot={{ fill: ACE_COLORS.PURPLE, strokeWidth: 2, r: 4 }}
                    name="Current Period"
                  />
                  <Line
                    type="monotone"
                    dataKey="previous"
                    stroke={alpha(ACE_COLORS.DARK, 0.4)}
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={{ fill: alpha(ACE_COLORS.DARK, 0.4), strokeWidth: 2, r: 4 }}
                    name="Previous Period"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
}));

// Enhanced PropTypes with comprehensive validation
ComparativeSentimentAnalysis.propTypes = {
  // Core props
  refreshTrigger: PropTypes.number
};

ComparativeSentimentAnalysis.displayName = 'ComparativeSentimentAnalysis';

export default ComparativeSentimentAnalysis;

/**
 * TrialBanner Component - Enterprise-grade trial banner for ACE Social platform
 * Features: Advanced trial management patterns, intelligent subscription prompts, dynamic banner adaptation,
 * advanced trial controls, smart conversion optimization, adaptive banner layouts, contextual trial states, accessibility-focused trial messaging, responsive trial patterns, and production-ready trial banner functionality
 @since 2024-1-1 to 2025-25-7
*/

import { memo, useState, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Paper,
  Chip,
  LinearProgress,
  Link,
  Stack,
  Alert,
  AlertTitle,
  Snackbar,
  IconButton,
  Tooltip,
  Collapse,
  Fade,
  useTheme,
  alpha,
  useMediaQuery,
  CircularProgress,
  Divider
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import {
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Upgrade as UpgradeIcon,
  Cancel as CancelIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { useTrial } from '../../contexts/TrialContext';
import { useConfirmation } from '../../contexts/ConfirmationContext';

// Enhanced animations for enterprise-grade trial banner
const pulseAnimation = keyframes`
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
`;

// Enhanced animations for enterprise-grade trial banner (simplified for production)

const countdownAnimation = keyframes`
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
`;

// Enhanced styled components for enterprise-grade trial banner
const StyledTrialBanner = styled(Paper)(({ theme, urgencyLevel, isAnimated, isCollapsed }) => ({
  position: 'relative',
  overflow: 'hidden',
  transition: theme.transitions.create(['all'], {
    duration: theme.transitions.duration.standard
  }),
  ...(urgencyLevel === 'high' && {
    background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)}, ${alpha(theme.palette.error.light, 0.05)})`,
    borderLeft: `4px solid ${theme.palette.error.main}`,
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '2px',
      background: `linear-gradient(90deg, ${theme.palette.error.main}, ${theme.palette.error.light})`,
      animation: isAnimated ? `${pulseAnimation} 2s ease-in-out infinite` : 'none'
    }
  }),
  ...(urgencyLevel === 'medium' && {
    background: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.1)}, ${alpha(theme.palette.warning.light, 0.05)})`,
    borderLeft: `4px solid ${theme.palette.warning.main}`
  }),
  ...(urgencyLevel === 'low' && {
    background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)}, ${alpha(theme.palette.info.light, 0.05)})`,
    borderLeft: `4px solid ${theme.palette.info.main}`
  }),
  ...(isCollapsed && {
    maxHeight: 60,
    overflow: 'hidden'
  }),
  // Enhanced accessibility
  '&:focus-visible': {
    outline: `3px solid ${theme.palette.primary.main}`,
    outlineOffset: '2px'
  },
  // Enhanced responsive design
  [theme.breakpoints.down('sm')]: {
    margin: theme.spacing(0.5),
    borderRadius: theme.spacing(1)
  },
  // Enhanced high contrast mode
  '@media (prefers-contrast: high)': {
    border: `3px solid ${theme.palette.text.primary}`,
    background: theme.palette.background.paper
  },
  // Enhanced reduced motion support
  '@media (prefers-reduced-motion: reduce)': {
    transition: 'none',
    animation: 'none',
    '&::before': {
      animation: 'none'
    }
  }
}));

const StyledCountdownChip = styled(Chip)(({ urgencyLevel, isAnimated }) => ({
  fontWeight: 'bold',
  ...(urgencyLevel === 'high' && isAnimated && {
    animation: `${countdownAnimation} 1s ease-in-out infinite`
  }),
  '& .MuiChip-label': {
    fontFamily: 'monospace',
    fontSize: '0.875rem'
  }
}));

const StyledProgressBar = styled(LinearProgress)(({ theme, urgencyLevel }) => ({
  height: 8,
  borderRadius: 4,
  backgroundColor: alpha(theme.palette.grey[300], 0.3),
  '& .MuiLinearProgress-bar': {
    borderRadius: 4,
    ...(urgencyLevel === 'high' && {
      background: `linear-gradient(90deg, ${theme.palette.error.main}, ${theme.palette.error.light})`
    }),
    ...(urgencyLevel === 'medium' && {
      background: `linear-gradient(90deg, ${theme.palette.warning.main}, ${theme.palette.warning.light})`
    }),
    ...(urgencyLevel === 'low' && {
      background: `linear-gradient(90deg, ${theme.palette.info.main}, ${theme.palette.info.light})`
    })
  }
}));

// Trial banner constants and configurations
const URGENCY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Trial states and configurations (simplified for production)

const PLAN_TRIAL_FEATURES = {
  creator: {
    trialDays: 14,
    features: ['Basic Analytics', 'Content Creation', 'Social Posting'],
    upgradePrice: '$9.99/month',
    savings: '20%'
  },
  accelerator: {
    trialDays: 14,
    features: ['Advanced Analytics', 'Team Collaboration', 'Campaign Management', 'Priority Support'],
    upgradePrice: '$29.99/month',
    savings: '30%'
  },
  dominator: {
    trialDays: 14,
    features: ['Enterprise Analytics', 'White-label Solutions', 'Custom Integrations', 'Dedicated Support'],
    upgradePrice: '$99.99/month',
    savings: '40%'
  }
};

const BANNER_ANALYTICS_EVENTS = {
  BANNER_VIEW: 'trial_banner_view',
  BANNER_DISMISS: 'trial_banner_dismiss',
  UPGRADE_CLICK: 'trial_banner_upgrade_click',
  CANCEL_CLICK: 'trial_banner_cancel_click',
  FEATURE_EXPAND: 'trial_banner_feature_expand',
  HELP_CLICK: 'trial_banner_help_click'
};

/**
 * Enhanced enterprise-grade trial banner component with comprehensive trial management patterns,
 * intelligent subscription prompts, dynamic banner adaptation, and production-ready trial banner functionality
 */

const TrialBanner = memo(({
  enableAnalytics = true,
  enableAccessibility = true,
  enableAnimations = true,
  enableDismiss = true,
  enableFeatureExpansion = true,
  showProgressBar = true,
  showCountdown = true,
  showFeatures = false,
  autoHide = false,
  autoHideDelay = 30000,
  onAnalytics,
  onUpgrade,
  onCancel,
  onDismiss,
  testId
}) => {
  const theme = useTheme();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Enhanced state management
  const [isDismissed, setIsDismissed] = useState(false);
  const [isCollapsed] = useState(false);
  const [showFeatureDetails, setShowFeatureDetails] = useState(showFeatures);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('info');
  // Enhanced menu state (simplified for production)
  const [conversionMetrics, setConversionMetrics] = useState({
    views: 0,
    clicks: 0,
    conversions: 0
  });

  // Trial context
  const {
    isTrial,
    daysRemaining,
    hoursRemaining,
    trialEnd,
    planId,
    isLoading: trialLoading,
    convertToPaid,
    cancelTrial
  } = useTrial();

  const { showConfirmation } = useConfirmation();

  // Plan features
  const planFeatures = useMemo(() => {
    return PLAN_TRIAL_FEATURES[planId] || PLAN_TRIAL_FEATURES.creator;
  }, [planId]);

  // Calculate trial progress
  const trialProgress = useMemo(() => {
    const totalDays = planFeatures.trialDays;
    const daysUsed = totalDays - daysRemaining;
    return Math.min(100, Math.max(0, (daysUsed / totalDays) * 100));
  }, [daysRemaining, planFeatures.trialDays]);

  // Determine urgency level
  const urgencyLevel = useMemo(() => {
    if (daysRemaining <= 1) return URGENCY_LEVELS.CRITICAL;
    if (daysRemaining <= 3) return URGENCY_LEVELS.HIGH;
    if (daysRemaining <= 7) return URGENCY_LEVELS.MEDIUM;
    return URGENCY_LEVELS.LOW;
  }, [daysRemaining]);

  // Format trial end date
  const formattedEndDate = useMemo(() => {
    if (!trialEnd) return '';
    return new Date(trialEnd).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }, [trialEnd]);

  // Analytics tracking
  const trackAnalytics = useCallback((action, data = {}) => {
    if (!enableAnalytics || !onAnalytics) return;

    onAnalytics({
      component: 'TrialBanner',
      action,
      planId,
      daysRemaining,
      urgencyLevel,
      timestamp: Date.now(),
      path: location.pathname,
      isMobile,
      ...data
    });
  }, [enableAnalytics, onAnalytics, planId, daysRemaining, urgencyLevel, location.pathname, isMobile]);

  // Enhanced upgrade handler
  const handleUpgrade = useCallback(async () => {
    setIsLoading(true);

    try {
      // Track upgrade click
      trackAnalytics(BANNER_ANALYTICS_EVENTS.UPGRADE_CLICK, {
        planId,
        urgencyLevel,
        trialProgress
      });

      // Update conversion metrics
      setConversionMetrics(prev => ({
        ...prev,
        clicks: prev.clicks + 1
      }));

      const result = await showConfirmation({
        title: `Upgrade to ${planId.charAt(0).toUpperCase() + planId.slice(1)} Plan`,
        content: `Ready to unlock the full potential of ACE Social? Your ${planId} subscription will start immediately and you'll save ${planFeatures.savings} on your first year!`,
        confirmText: `Upgrade for ${planFeatures.upgradePrice}`,
        cancelText: 'Not Yet',
        confirmColor: 'primary',
        onConfirm: async () => {
          await convertToPaid();

          // Track successful conversion
          trackAnalytics('trial_conversion_success', {
            planId,
            conversionTime: Date.now()
          });

          setConversionMetrics(prev => ({
            ...prev,
            conversions: prev.conversions + 1
          }));

          if (onUpgrade) {
            onUpgrade();
          }
        }
      });

      if (result) {
        setSnackbarMessage('Successfully upgraded! Welcome to your new plan.');
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
      }
    } catch (upgradeError) {
      setError(upgradeError.message);
      setSnackbarMessage(`Upgrade failed: ${upgradeError.message}`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);

      trackAnalytics('trial_upgrade_error', {
        error: upgradeError.message
      });
    } finally {
      setIsLoading(false);
    }
  }, [showConfirmation, convertToPaid, trackAnalytics, planId, planFeatures, urgencyLevel, trialProgress, onUpgrade]);

  // Enhanced cancel handler
  const handleCancel = useCallback(async () => {
    setIsLoading(true);

    try {
      // Track cancel click
      trackAnalytics(BANNER_ANALYTICS_EVENTS.CANCEL_CLICK, {
        planId,
        urgencyLevel,
        trialProgress
      });

      const result = await showConfirmation({
        title: 'Cancel Trial',
        content: `Are you sure you want to cancel your ${planId} trial? You'll lose access to premium features immediately and your account will be downgraded to the Creator plan.`,
        confirmText: 'Cancel Trial',
        cancelText: 'Keep Trial',
        confirmColor: 'error',
        onConfirm: async () => {
          await cancelTrial();

          // Track cancellation
          trackAnalytics('trial_cancellation', {
            planId,
            cancellationTime: Date.now(),
            daysUsed: planFeatures.trialDays - daysRemaining
          });

          if (onCancel) {
            onCancel();
          }
        }
      });

      if (result) {
        setSnackbarMessage('Trial cancelled. You can start a new trial anytime.');
        setSnackbarSeverity('info');
        setSnackbarOpen(true);
      }
    } catch (cancelError) {
      setError(cancelError.message);
      setSnackbarMessage(`Cancellation failed: ${cancelError.message}`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);

      trackAnalytics('trial_cancel_error', {
        error: cancelError.message
      });
    } finally {
      setIsLoading(false);
    }
  }, [showConfirmation, cancelTrial, trackAnalytics, planId, planFeatures, urgencyLevel, trialProgress, daysRemaining, onCancel]);

  // Enhanced dismiss handler
  const handleDismiss = useCallback(() => {
    setIsDismissed(true);

    trackAnalytics(BANNER_ANALYTICS_EVENTS.BANNER_DISMISS, {
      dismissTime: Date.now(),
      viewDuration: Date.now() - (conversionMetrics.views * 1000)
    });

    if (onDismiss) {
      onDismiss();
    }
  }, [trackAnalytics, conversionMetrics.views, onDismiss]);

  // Toggle feature details
  const toggleFeatureDetails = useCallback(() => {
    setShowFeatureDetails(prev => {
      const newState = !prev;

      trackAnalytics(BANNER_ANALYTICS_EVENTS.FEATURE_EXPAND, {
        expanded: newState
      });

      return newState;
    });
  }, [trackAnalytics]);

  // Effects
  useEffect(() => {
    if (isTrial && !trialLoading && !isDismissed) {
      // Track banner view
      trackAnalytics(BANNER_ANALYTICS_EVENTS.BANNER_VIEW, {
        initialView: true
      });

      // Update view metrics
      setConversionMetrics(prev => ({
        ...prev,
        views: prev.views + 1
      }));
    }
  }, [isTrial, trialLoading, isDismissed, trackAnalytics]);

  useEffect(() => {
    if (autoHide && autoHideDelay > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, handleDismiss]);

  // Don't show if not on trial, loading, or dismissed
  if (!isTrial || trialLoading || isDismissed) return null;

  return (
    <Fade in timeout={600}>
      <StyledTrialBanner
        elevation={0}
        urgencyLevel={urgencyLevel}
        isAnimated={enableAnimations}
        isCollapsed={isCollapsed}
        data-testid={testId}
        tabIndex={enableAccessibility ? 0 : -1}
        role={enableAccessibility ? "banner" : undefined}
        aria-label={enableAccessibility ? `Trial banner for ${planId} plan - ${daysRemaining} days remaining` : undefined}
        sx={{
          p: isMobile ? 2 : 3,
          mb: 3,
          borderRadius: 2
        }}
      >
        {/* Enhanced header section */}
        <Stack
          direction={{ xs: 'column', md: 'row' }}
          spacing={2}
          alignItems={{ md: 'center' }}
          justifyContent="space-between"
        >
          <Box sx={{ flex: 1 }}>
            {/* Enhanced title and countdown */}
            <Stack direction="row" spacing={1} alignItems="center" mb={1} flexWrap="wrap">
              <Stack direction="row" spacing={1} alignItems="center">
                <StarIcon color="primary" />
                <Typography variant="h6" fontWeight="bold" color="primary">
                  {planId.charAt(0).toUpperCase() + planId.slice(1)} Plan Trial
                </Typography>
              </Stack>

              {showCountdown && (
                <StyledCountdownChip
                  icon={<ScheduleIcon />}
                  label={`${daysRemaining}d ${hoursRemaining}h left`}
                  size="small"
                  color={urgencyLevel === URGENCY_LEVELS.CRITICAL ? 'error' :
                         urgencyLevel === URGENCY_LEVELS.HIGH ? 'error' :
                         urgencyLevel === URGENCY_LEVELS.MEDIUM ? 'warning' : 'info'}
                  urgencyLevel={urgencyLevel}
                  isAnimated={enableAnimations && urgencyLevel === URGENCY_LEVELS.HIGH}
                />
              )}

              {urgencyLevel === URGENCY_LEVELS.CRITICAL && (
                <Chip
                  icon={<ErrorIcon />}
                  label="URGENT"
                  size="small"
                  color="error"
                  variant="filled"
                />
              )}
            </Stack>

            {/* Enhanced description */}
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Your {planId} trial ends on <strong>{formattedEndDate}</strong>.
              Upgrade now to keep access to all premium features and save {planFeatures.savings}!
            </Typography>

            {/* Enhanced progress bar */}
            {showProgressBar && (
              <Box sx={{ mt: 2, mb: 2 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="caption" color="text.secondary">
                    Trial Progress
                  </Typography>
                  <Typography variant="caption" color="text.secondary" fontWeight="bold">
                    {Math.round(trialProgress)}% used
                  </Typography>
                </Stack>
                <StyledProgressBar
                  variant="determinate"
                  value={trialProgress}
                  urgencyLevel={urgencyLevel}
                />
              </Box>
            )}

            {/* Enhanced feature expansion */}
            {enableFeatureExpansion && (
              <Box sx={{ mt: 2 }}>
                <Button
                  size="small"
                  onClick={toggleFeatureDetails}
                  endIcon={showFeatureDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  sx={{ p: 0, minWidth: 'auto', textTransform: 'none' }}
                >
                  <Typography variant="caption" color="primary">
                    {showFeatureDetails ? 'Hide' : 'Show'} included features
                  </Typography>
                </Button>

                <Collapse in={showFeatureDetails}>
                  <Box sx={{ mt: 1, p: 2, bgcolor: alpha(theme.palette.primary.main, 0.05), borderRadius: 1 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      What you get with {planId.charAt(0).toUpperCase() + planId.slice(1)}:
                    </Typography>
                    <Stack spacing={0.5}>
                      {planFeatures.features.map((feature, index) => (
                        <Stack key={index} direction="row" spacing={1} alignItems="center">
                          <CheckCircleIcon sx={{ fontSize: 16, color: 'success.main' }} />
                          <Typography variant="body2">{feature}</Typography>
                        </Stack>
                      ))}
                    </Stack>
                  </Box>
                </Collapse>
              </Box>
            )}
          </Box>

          {/* Enhanced action buttons */}
          <Stack direction={{ xs: 'row', md: 'column' }} spacing={1} sx={{ minWidth: { md: 200 } }}>
            <Button
              variant="contained"
              size={isMobile ? "medium" : "large"}
              color="primary"
              onClick={handleUpgrade}
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={16} /> : <UpgradeIcon />}
              sx={{
                fontWeight: 'bold',
                textTransform: 'none',
                boxShadow: 3,
                '&:hover': {
                  boxShadow: 6,
                  transform: 'translateY(-1px)'
                }
              }}
            >
              {isLoading ? 'Processing...' : `Upgrade for ${planFeatures.upgradePrice}`}
            </Button>

            <Stack direction="row" spacing={1}>
              <Button
                variant="outlined"
                size={isMobile ? "small" : "medium"}
                color="inherit"
                onClick={handleCancel}
                disabled={isLoading}
                startIcon={<CancelIcon />}
                sx={{ textTransform: 'none' }}
              >
                Cancel Trial
              </Button>

              {enableDismiss && (
                <Tooltip title="Dismiss banner">
                  <IconButton
                    size="small"
                    onClick={handleDismiss}
                    aria-label="Dismiss trial banner"
                  >
                    <CloseIcon />
                  </IconButton>
                </Tooltip>
              )}
            </Stack>
          </Stack>
        </Stack>

        {/* Enhanced help section */}
        <Divider sx={{ my: 2 }} />
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          alignItems="center"
          justifyContent="space-between"
        >
          <Typography variant="caption" color="text.secondary">
            Need help deciding?
            <Link
              component={RouterLink}
              to="/billing/plans"
              color="primary"
              sx={{ mx: 0.5 }}
              onClick={() => trackAnalytics('compare_plans_click')}
            >
              Compare plans
            </Link>
            or
            <Link
              component={RouterLink}
              to="/support"
              color="primary"
              sx={{ mx: 0.5 }}
              onClick={() => trackAnalytics(BANNER_ANALYTICS_EVENTS.HELP_CLICK)}
            >
              contact support
            </Link>
          </Typography>

          {enableAnalytics && conversionMetrics.views > 0 && (
            <Stack direction="row" spacing={2}>
              <Tooltip title="Banner views">
                <Chip
                  icon={<AnalyticsIcon />}
                  label={`${conversionMetrics.views} views`}
                  size="small"
                  variant="outlined"
                />
              </Tooltip>
              {conversionMetrics.clicks > 0 && (
                <Tooltip title="Upgrade clicks">
                  <Chip
                    icon={<TrendingUpIcon />}
                    label={`${conversionMetrics.clicks} clicks`}
                    size="small"
                    variant="outlined"
                    color="primary"
                  />
                </Tooltip>
              )}
            </Stack>
          )}
        </Stack>

        {/* Error state */}
        {error && (
          <Alert
            severity="error"
            sx={{ mt: 2 }}
            onClose={() => setError(null)}
          >
            <AlertTitle>Error</AlertTitle>
            {error}
          </Alert>
        )}

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={() => setSnackbarOpen(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert
            onClose={() => setSnackbarOpen(false)}
            severity={snackbarSeverity}
            sx={{ width: '100%' }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </StyledTrialBanner>
    </Fade>
  );
});

// Set display name for debugging
TrialBanner.displayName = 'TrialBanner';

// Comprehensive PropTypes for enterprise-grade trial banner
TrialBanner.propTypes = {
  /** Whether to enable analytics tracking */
  enableAnalytics: PropTypes.bool,

  /** Whether to enable accessibility features */
  enableAccessibility: PropTypes.bool,

  /** Whether to enable animations */
  enableAnimations: PropTypes.bool,

  /** Whether to enable dismiss functionality */
  enableDismiss: PropTypes.bool,

  /** Whether to enable feature expansion */
  enableFeatureExpansion: PropTypes.bool,

  /** Whether to show progress bar */
  showProgressBar: PropTypes.bool,

  /** Whether to show countdown timer */
  showCountdown: PropTypes.bool,

  /** Whether to show features by default */
  showFeatures: PropTypes.bool,

  /** Whether to auto-hide banner */
  autoHide: PropTypes.bool,

  /** Auto-hide delay in milliseconds */
  autoHideDelay: PropTypes.number,

  /** Analytics event handler */
  onAnalytics: PropTypes.func,

  /** Upgrade handler */
  onUpgrade: PropTypes.func,

  /** Cancel handler */
  onCancel: PropTypes.func,

  /** Dismiss handler */
  onDismiss: PropTypes.func,

  /** Test ID for testing */
  testId: PropTypes.string
};

TrialBanner.defaultProps = {
  enableAnalytics: true,
  enableAccessibility: true,
  enableAnimations: true,
  enableDismiss: true,
  enableFeatureExpansion: true,
  showProgressBar: true,
  showCountdown: true,
  showFeatures: false,
  autoHide: false,
  autoHideDelay: 30000,
  onAnalytics: null,
  onUpgrade: null,
  onCancel: null,
  onDismiss: null,
  testId: null
};

export default TrialBanner;

/**
 * Confirmation Context
 * Provides a global confirmation dialog system with Promise-based API
 * Production-ready implementation with comprehensive error handling and accessibility
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useContext, useState, useCallback } from 'react';
import ConfirmationDialog from '../components/common/ConfirmationDialog';

// Configuration constants
const CONFIG = {
  // Default dialog settings
  DEFAULT_MAX_WIDTH: 'sm',
  DEFAULT_CONFIRM_TEXT: 'Confirm',
  DEFAULT_CANCEL_TEXT: 'Cancel',
  DEFAULT_CONFIRM_COLOR: 'primary',

  // Timeout settings
  AUTO_CLOSE_TIMEOUT: 30000, // 30 seconds

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[Confirmation] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[Confirmation] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Confirmation Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[Confirmation] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Confirmation Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[Confirmation] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Confirmation Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const ConfirmationContext = createContext(null);

// Custom hook to use the confirmation context
// eslint-disable-next-line react-refresh/only-export-components
export const useConfirmation = () => {
  const context = useContext(ConfirmationContext);
  if (!context) {
    throw new Error('useConfirmation must be used within a ConfirmationProvider');
  }
  return context;
};

export const ConfirmationProvider = ({ children }) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogConfig, setDialogConfig] = useState({
    title: '',
    content: '',
    confirmText: CONFIG.DEFAULT_CONFIRM_TEXT,
    cancelText: CONFIG.DEFAULT_CANCEL_TEXT,
    confirmColor: CONFIG.DEFAULT_CONFIRM_COLOR,
    onConfirm: () => {},
    onCancel: () => {},
    fullWidth: false,
    maxWidth: CONFIG.DEFAULT_MAX_WIDTH,
    severity: 'info', // info, warning, error, success
    autoClose: false,
    autoCloseTimeout: CONFIG.AUTO_CLOSE_TIMEOUT
  });
  const [confirmationResolver, setConfirmationResolver] = useState(null);
  const [autoCloseTimer, setAutoCloseTimer] = useState(null);

  const showConfirmation = useCallback((config) => {
    return new Promise((resolve, reject) => {
      try {
        // Validate configuration
        if (!config || typeof config !== 'object') {
          logger.error('Invalid confirmation config provided', config);
          reject(new Error('Invalid confirmation configuration'));
          return;
        }

        if (!config.title && !config.content) {
          logger.warn('Confirmation dialog shown without title or content');
        }

        // Clear any existing auto-close timer
        if (autoCloseTimer) {
          clearTimeout(autoCloseTimer);
          setAutoCloseTimer(null);
        }

        const finalConfig = {
          ...dialogConfig,
          ...config
        };

        logger.debug('Showing confirmation dialog', {
          title: finalConfig.title,
          severity: finalConfig.severity,
          autoClose: finalConfig.autoClose
        });

        setDialogConfig(finalConfig);
        setConfirmationResolver(() => resolve);
        setDialogOpen(true);

        // Set up auto-close timer if enabled
        if (finalConfig.autoClose && finalConfig.autoCloseTimeout > 0) {
          const timer = setTimeout(() => {
            logger.debug('Auto-closing confirmation dialog');
            handleCancel();
          }, finalConfig.autoCloseTimeout);
          setAutoCloseTimer(timer);
        }

        // Track dialog shown event
        if (window.analytics) {
          window.analytics.track('Confirmation Dialog Shown', {
            title: finalConfig.title,
            severity: finalConfig.severity,
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        logger.error('Error showing confirmation dialog', error);
        reject(error);
      }
    });
  }, [dialogConfig, autoCloseTimer, handleCancel]);

  const handleConfirm = useCallback(() => {
    try {
      logger.debug('Confirmation dialog confirmed', { title: dialogConfig.title });

      // Clear auto-close timer if active
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer);
        setAutoCloseTimer(null);
      }

      setDialogOpen(false);

      // Execute custom onConfirm callback
      if (dialogConfig.onConfirm && typeof dialogConfig.onConfirm === 'function') {
        try {
          dialogConfig.onConfirm();
        } catch (error) {
          logger.error('Error in onConfirm callback', error);
        }
      }

      // Resolve the promise
      if (confirmationResolver) {
        confirmationResolver(true);
        setConfirmationResolver(null);
      }

      // Track confirmation event
      if (window.analytics) {
        window.analytics.track('Confirmation Dialog Confirmed', {
          title: dialogConfig.title,
          severity: dialogConfig.severity,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.error('Error handling confirmation', error);
    }
  }, [dialogConfig, confirmationResolver, autoCloseTimer]);

  const handleCancel = useCallback(() => {
    try {
      logger.debug('Confirmation dialog cancelled', { title: dialogConfig.title });

      // Clear auto-close timer if active
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer);
        setAutoCloseTimer(null);
      }

      setDialogOpen(false);

      // Execute custom onCancel callback
      if (dialogConfig.onCancel && typeof dialogConfig.onCancel === 'function') {
        try {
          dialogConfig.onCancel();
        } catch (error) {
          logger.error('Error in onCancel callback', error);
        }
      }

      // Resolve the promise with false
      if (confirmationResolver) {
        confirmationResolver(false);
        setConfirmationResolver(null);
      }

      // Track cancellation event
      if (window.analytics) {
        window.analytics.track('Confirmation Dialog Cancelled', {
          title: dialogConfig.title,
          severity: dialogConfig.severity,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.error('Error handling cancellation', error);
    }
  }, [dialogConfig, confirmationResolver, autoCloseTimer]);

  // Utility functions for common confirmation patterns
  const showDeleteConfirmation = useCallback((itemName = 'item') => {
    return showConfirmation({
      title: 'Confirm Deletion',
      content: `Are you sure you want to delete this ${itemName}? This action cannot be undone.`,
      confirmText: 'Delete',
      confirmColor: 'error',
      severity: 'error'
    });
  }, [showConfirmation]);

  const showDiscardChangesConfirmation = useCallback(() => {
    return showConfirmation({
      title: 'Discard Changes',
      content: 'You have unsaved changes. Are you sure you want to discard them?',
      confirmText: 'Discard',
      confirmColor: 'warning',
      severity: 'warning'
    });
  }, [showConfirmation]);

  const showLogoutConfirmation = useCallback(() => {
    return showConfirmation({
      title: 'Confirm Logout',
      content: 'Are you sure you want to log out?',
      confirmText: 'Logout',
      confirmColor: 'primary',
      severity: 'info'
    });
  }, [showConfirmation]);

  // Enhanced context value
  const contextValue = {
    // Core functionality
    showConfirmation,

    // Utility functions for common patterns
    showDeleteConfirmation,
    showDiscardChangesConfirmation,
    showLogoutConfirmation,

    // State information
    isDialogOpen: dialogOpen,
    currentDialog: dialogConfig,

    // Utility functions
    closeDialog: handleCancel,

    // Helper functions for custom confirmations
    showInfoConfirmation: useCallback((title, content) => {
      return showConfirmation({
        title,
        content,
        severity: 'info',
        confirmColor: 'primary'
      });
    }, [showConfirmation]),

    showWarningConfirmation: useCallback((title, content) => {
      return showConfirmation({
        title,
        content,
        severity: 'warning',
        confirmColor: 'warning'
      });
    }, [showConfirmation]),

    showErrorConfirmation: useCallback((title, content) => {
      return showConfirmation({
        title,
        content,
        severity: 'error',
        confirmColor: 'error'
      });
    }, [showConfirmation]),

    showSuccessConfirmation: useCallback((title, content) => {
      return showConfirmation({
        title,
        content,
        severity: 'success',
        confirmColor: 'success'
      });
    }, [showConfirmation])
  };

  return (
    <ConfirmationContext.Provider value={contextValue}>
      {children}
      <ConfirmationDialog
        open={dialogOpen}
        title={dialogConfig.title}
        content={dialogConfig.content}
        confirmText={dialogConfig.confirmText}
        cancelText={dialogConfig.cancelText}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        confirmColor={dialogConfig.confirmColor}
        fullWidth={dialogConfig.fullWidth}
        maxWidth={dialogConfig.maxWidth}
        severity={dialogConfig.severity}
      />
    </ConfirmationContext.Provider>
  );
};

// Export the context
export { ConfirmationContext };

// Default export for convenience
export default ConfirmationProvider;

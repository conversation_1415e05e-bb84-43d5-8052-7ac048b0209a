/**
 * Font Upload API Service
 * Handles font file uploads, validation, and management for brand typography
 */

import { api as apiClient } from './index';

// Font file validation constants
const SUPPORTED_FONT_FORMATS = ['.ttf', '.otf', '.woff', '.woff2'];
const MAX_FONT_SIZE = 5 * 1024 * 1024; // 5MB
const MIN_FONT_SIZE = 1024; // 1KB

/**
 * Validate font file before upload
 * @param {File} file - Font file to validate
 * @returns {Promise<Object>} Validation result
 */
export const validateFontFile = async (file) => {
  const validation = {
    isValid: false,
    errors: [],
    warnings: [],
    metadata: {}
  };

  try {
    // Check if file exists
    if (!file) {
      validation.errors.push('No file provided');
      return validation;
    }

    // Check file size
    if (file.size > MAX_FONT_SIZE) {
      validation.errors.push(`Font file too large. Maximum size is ${MAX_FONT_SIZE / (1024 * 1024)}MB`);
    }

    if (file.size < MIN_FONT_SIZE) {
      validation.errors.push(`Font file too small. Minimum size is ${MIN_FONT_SIZE} bytes`);
    }

    // Check file extension
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    if (!SUPPORTED_FONT_FORMATS.includes(fileExtension)) {
      validation.errors.push(`Unsupported font format. Supported formats: ${SUPPORTED_FONT_FORMATS.join(', ')}`);
    }

    // Check file name
    if (file.name.length > 100) {
      validation.warnings.push('Font file name is very long');
    }

    // Extract metadata
    validation.metadata = {
      name: file.name,
      size: file.size,
      type: file.type,
      extension: fileExtension,
      lastModified: file.lastModified
    };

    // Set validation status
    validation.isValid = validation.errors.length === 0;

    return validation;
  } catch (error) {
    validation.errors.push(`Validation error: ${error.message}`);
    return validation;
  }
};

/**
 * Upload font file to server
 * @param {File} file - Font file to upload
 * @param {Object} options - Upload options
 * @returns {Promise<Object>} Upload result
 */
export const uploadFontFile = async (file, options = {}) => {
  try {
    // Validate file first
    const validation = await validateFontFile(file);
    if (!validation.isValid) {
      throw new Error(`Font validation failed: ${validation.errors.join(', ')}`);
    }

    // Create form data
    const formData = new FormData();
    formData.append('file', file);
    formData.append('platform', options.platform || 'brand');
    formData.append('file_type', 'font');

    // Add metadata
    if (options.metadata) {
      formData.append('metadata', JSON.stringify(options.metadata));
    }

    // Upload with progress tracking
    const response = await apiClient.post('/api/files/font', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (options.onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          options.onProgress(percentCompleted);
        }
      },
    });

    return {
      success: true,
      data: response.data,
      metadata: validation.metadata
    };
  } catch (error) {
    console.error('Font upload error:', error);
    
    // Handle different error types
    if (error.response) {
      // Server responded with error status
      throw new Error(error.response.data?.detail || 'Font upload failed');
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Network error during font upload');
    } else {
      // Something else happened
      throw new Error(error.message || 'Font upload failed');
    }
  }
};

/**
 * Get list of uploaded fonts for user
 * @returns {Promise<Array>} List of user fonts
 */
export const getUserFonts = async () => {
  try {
    const response = await apiClient.get('/api/brand-profiles/fonts');
    return response.data;
  } catch (error) {
    console.error('Error fetching user fonts:', error);
    throw new Error('Failed to fetch user fonts');
  }
};

/**
 * Delete uploaded font
 * @param {string} fontId - Font ID to delete
 * @returns {Promise<Object>} Delete result
 */
export const deleteFontFile = async (fontId) => {
  try {
    const response = await apiClient.delete(`/api/brand-profiles/fonts/${fontId}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting font:', error);
    throw new Error('Failed to delete font');
  }
};

/**
 * Preview font with sample text
 * @param {string} fontUrl - Font URL or name
 * @param {string} sampleText - Text to preview
 * @returns {Promise<string>} Preview URL or data
 */
export const previewFont = async (fontUrl, sampleText = 'Sample Text') => {
  try {
    const response = await apiClient.post('/api/brand-profiles/fonts/preview', {
      font_url: fontUrl,
      sample_text: sampleText
    });
    return response.data;
  } catch (error) {
    console.error('Error generating font preview:', error);
    throw new Error('Failed to generate font preview');
  }
};

/**
 * Load font dynamically in browser
 * @param {string} fontName - Font name
 * @param {string} fontUrl - Font URL
 * @returns {Promise<FontFace>} Loaded font face
 */
export const loadFontInBrowser = async (fontName, fontUrl) => {
  try {
    if (!window.FontFace) {
      throw new Error('FontFace API not supported in this browser');
    }

    const fontFace = new FontFace(fontName, `url(${fontUrl})`);
    const loadedFont = await fontFace.load();
    
    // Add to document fonts
    document.fonts.add(loadedFont);
    
    return loadedFont;
  } catch (error) {
    console.error('Error loading font in browser:', error);
    throw new Error(`Failed to load font: ${error.message}`);
  }
};

/**
 * Check if font is already loaded in browser
 * @param {string} fontName - Font name to check
 * @returns {boolean} Whether font is loaded
 */
export const isFontLoaded = (fontName) => {
  try {
    return document.fonts.check(`12px "${fontName}"`);
  } catch (error) {
    console.error('Error checking font status:', error);
    return false;
  }
};

/**
 * Get font metrics and information
 * @param {string} fontName - Font name
 * @returns {Object} Font metrics
 */
export const getFontMetrics = (fontName) => {
  try {
    // Create temporary canvas to measure font
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    context.font = `16px "${fontName}"`;
    const metrics = context.measureText('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');
    
    return {
      width: metrics.width,
      actualBoundingBoxAscent: metrics.actualBoundingBoxAscent,
      actualBoundingBoxDescent: metrics.actualBoundingBoxDescent,
      fontBoundingBoxAscent: metrics.fontBoundingBoxAscent,
      fontBoundingBoxDescent: metrics.fontBoundingBoxDescent,
    };
  } catch (error) {
    console.error('Error getting font metrics:', error);
    return null;
  }
};

export default {
  validateFontFile,
  uploadFontFile,
  getUserFonts,
  deleteFontFile,
  previewFont,
  loadFontInBrowser,
  isFontLoaded,
  getFontMetrics,
  SUPPORTED_FONT_FORMATS,
  MAX_FONT_SIZE,
  MIN_FONT_SIZE,
};

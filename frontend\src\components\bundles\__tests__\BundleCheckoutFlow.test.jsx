import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import BundleCheckoutFlow from '../BundleCheckoutFlow';

// Mock the BundleSavingsCalculator component
vi.mock('../BundleSavingsCalculator', () => ({
  default: ({ bundle, individualAddons, showYearly }) => (
    <div data-testid="bundle-savings-calculator">
      <span>Bundle: {bundle?.name}</span>
      <span>Addons: {individualAddons?.length}</span>
      <span>Yearly: {showYearly ? 'Yes' : 'No'}</span>
    </div>
  ),
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('BundleCheckoutFlow', () => {
  const mockBundle = {
    id: 'bundle-1',
    name: 'Content Creator Bundle',
    description: 'Perfect for content creators and social media managers',
    price_monthly: 49.99,
    price_yearly: 499.99,
    savings_percentage: 25,
    bundle_type: 'content_optimization',
    is_popular: true,
    features: [
      'Advanced content generation',
      'Social media scheduling',
      'Analytics dashboard',
      'Brand management',
      'Team collaboration',
      'Priority support'
    ],
    included_addons: ['advanced_analytics', 'team_collaboration', 'priority_support'],
    required_plan: 'pro'
  };

  const mockIndividualAddons = [
    {
      id: 'advanced_analytics',
      name: 'Advanced Analytics',
      price_monthly: 19.99
    },
    {
      id: 'team_collaboration',
      name: 'Team Collaboration',
      price_monthly: 14.99
    },
    {
      id: 'priority_support',
      name: 'Priority Support',
      price_monthly: 9.99
    }
  ];

  const mockProps = {
    open: true,
    onClose: vi.fn(),
    bundle: mockBundle,
    userPlan: 'pro',
    isAvailable: true,
    individualAddons: mockIndividualAddons,
    onPurchase: vi.fn(),
    onUpgrade: vi.fn(),
    loading: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders bundle checkout flow correctly', () => {
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Purchase Bundle')).toBeInTheDocument();
    expect(screen.getByText('Content Creator Bundle')).toBeInTheDocument();
  });

  test('displays stepper with correct steps when bundle is available', () => {
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Review Bundle')).toBeInTheDocument();
    expect(screen.getByText('Billing Options')).toBeInTheDocument();
    expect(screen.getByText('Confirm Purchase')).toBeInTheDocument();
  });

  test('displays stepper with upgrade steps when bundle is not available', () => {
    const unavailableProps = {
      ...mockProps,
      isAvailable: false
    };

    render(
      <TestWrapper>
        <BundleCheckoutFlow {...unavailableProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Bundle Details')).toBeInTheDocument();
    expect(screen.getByText('Upgrade Required')).toBeInTheDocument();
  });

  test('shows bundle information in review step', () => {
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Content Creator Bundle')).toBeInTheDocument();
    expect(screen.getByText('25% OFF')).toBeInTheDocument();
    expect(screen.getByText('POPULAR CHOICE')).toBeInTheDocument();
    expect(screen.getByText('What\'s Included:')).toBeInTheDocument();
    expect(screen.getByText('Advanced content generation')).toBeInTheDocument();
  });

  test('shows bundled add-ons', () => {
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Bundled Add-ons:')).toBeInTheDocument();
    expect(screen.getByText('Advanced Analytics')).toBeInTheDocument();
    expect(screen.getByText('Team Collaboration')).toBeInTheDocument();
    expect(screen.getByText('Priority Support')).toBeInTheDocument();
  });

  test('handles feature expansion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Should show "Show 1 More Features" button since we have 6 features
    const expandButton = screen.getByText('Show 1 More Features');
    await user.click(expandButton);

    expect(screen.getByText('Show Less')).toBeInTheDocument();
  });

  test('navigates to billing options step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    expect(screen.getByText('Choose Billing Cycle:')).toBeInTheDocument();
    expect(screen.getByTestId('bundle-savings-calculator')).toBeInTheDocument();
  });

  test('handles billing cycle toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Navigate to billing options
    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    // Toggle to yearly billing
    const billingSwitch = screen.getByRole('checkbox');
    await user.click(billingSwitch);

    expect(screen.getByText('Annual Billing')).toBeInTheDocument();
    expect(screen.getByText('$499.99')).toBeInTheDocument();
    expect(screen.getByText('(2 months free!)')).toBeInTheDocument();
  });

  test('navigates to confirm purchase step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Navigate through steps
    let nextButton = screen.getByText('Next');
    await user.click(nextButton);

    nextButton = screen.getByText('Next');
    await user.click(nextButton);

    expect(screen.getByText('Order Summary:')).toBeInTheDocument();
    expect(screen.getByText('Complete Purchase')).toBeInTheDocument();
  });

  test('shows order summary in confirm step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Navigate to confirm step
    let nextButton = screen.getByText('Next');
    await user.click(nextButton);

    nextButton = screen.getByText('Next');
    await user.click(nextButton);

    expect(screen.getByText('Order Summary:')).toBeInTheDocument();
    expect(screen.getByText('Content Creator Bundle')).toBeInTheDocument();
    expect(screen.getByText('$49.99')).toBeInTheDocument();
    expect(screen.getByText('Monthly')).toBeInTheDocument();
    expect(screen.getByText('Total')).toBeInTheDocument();
  });

  test('shows security notice in confirm step', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Navigate to confirm step
    let nextButton = screen.getByText('Next');
    await user.click(nextButton);

    nextButton = screen.getByText('Next');
    await user.click(nextButton);

    expect(screen.getByText(/Your payment is secured with 256-bit SSL encryption/)).toBeInTheDocument();
    expect(screen.getByText(/By completing this purchase, you agree to our Terms of Service/)).toBeInTheDocument();
  });

  test('handles purchase completion', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Navigate to confirm step
    let nextButton = screen.getByText('Next');
    await user.click(nextButton);

    nextButton = screen.getByText('Next');
    await user.click(nextButton);

    // Complete purchase
    const purchaseButton = screen.getByText('Complete Purchase');
    await user.click(purchaseButton);

    await waitFor(() => {
      expect(mockProps.onPurchase).toHaveBeenCalledWith({
        bundle_id: 'bundle-1',
        billing_cycle: 'monthly',
        user_plan: 'pro',
        final_price: 49.99
      });
    });
  });

  test('handles back navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Navigate forward
    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    // Navigate back
    const backButton = screen.getByText('Back');
    await user.click(backButton);

    expect(screen.getByText('What\'s Included:')).toBeInTheDocument();
  });

  test('shows upgrade required flow when bundle is not available', () => {
    const unavailableProps = {
      ...mockProps,
      isAvailable: false
    };

    render(
      <TestWrapper>
        <BundleCheckoutFlow {...unavailableProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Bundle Details')).toBeInTheDocument();
    expect(screen.getByText(/This bundle requires Pro plan or higher/)).toBeInTheDocument();
  });

  test('handles upgrade click', async () => {
    const user = userEvent.setup();
    const unavailableProps = {
      ...mockProps,
      isAvailable: false
    };

    render(
      <TestWrapper>
        <BundleCheckoutFlow {...unavailableProps} />
      </TestWrapper>
    );

    // Navigate to upgrade step
    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    const upgradeButton = screen.getByText('Upgrade Plan');
    await user.click(upgradeButton);

    expect(mockProps.onUpgrade).toHaveBeenCalledWith('pro');
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('shows loading state', () => {
    const loadingProps = {
      ...mockProps,
      loading: true
    };

    render(
      <TestWrapper>
        <BundleCheckoutFlow {...loadingProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });

  test('disables buttons when loading', () => {
    const loadingProps = {
      ...mockProps,
      loading: true
    };

    render(
      <TestWrapper>
        <BundleCheckoutFlow {...loadingProps} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Cancel');
    const nextButton = screen.getByText('Processing...');

    expect(cancelButton).toBeDisabled();
    expect(nextButton).toBeDisabled();
  });

  test('handles dialog close', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('handles cancel button', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockProps.onClose).toHaveBeenCalled();
  });

  test('calculates yearly savings correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Navigate to billing options
    const nextButton = screen.getByText('Next');
    await user.click(nextButton);

    // Toggle to yearly billing
    const billingSwitch = screen.getByRole('checkbox');
    await user.click(billingSwitch);

    // Should show savings (monthly * 12 - yearly = 49.99 * 12 - 499.99 = 99.89)
    expect(screen.getByText('Save $99.89')).toBeInTheDocument();
  });

  test('renders with default props when no bundle provided', () => {
    const propsWithoutBundle = {
      ...mockProps,
      bundle: null
    };

    render(
      <TestWrapper>
        <BundleCheckoutFlow {...propsWithoutBundle} />
      </TestWrapper>
    );

    // Should not render anything when no bundle
    expect(screen.queryByText('Purchase Bundle')).not.toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    // Check for proper dialog attributes
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /close/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /next/i })).toBeInTheDocument();
  });

  test('formats currency correctly', () => {
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('$49.99')).toBeInTheDocument();
  });

  test('shows bundle type and popularity badges', () => {
    render(
      <TestWrapper>
        <BundleCheckoutFlow {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('CONTENT OPTIMIZATION')).toBeInTheDocument();
    expect(screen.getByText('POPULAR CHOICE')).toBeInTheDocument();
  });
});

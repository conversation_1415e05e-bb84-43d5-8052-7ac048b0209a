// @since 2024-1-1 to 2025-25-7

import { Container, Box } from '@mui/material';
import { Helmet } from 'react-helmet-async';
import { Mood as MoodIcon } from '@mui/icons-material';
import PageHeader from '../../components/common/PageHeader';
import SentimentDashboard from '../../components/sentiment/SentimentDashboard';

/**
 * Comprehensive Sentiment Analysis Page
 *
 * Displays detailed sentiment analysis for content across all platforms
 * with trend analysis, filtering, and actionable insights.
 */
const SentimentAnalysisPage = () => {

  return (
    <>
      <Helmet>
        <title>Sentiment Analysis | B2B Influencer Tool</title>
      </Helmet>

      <Container maxWidth="xl" sx={{ py: 3 }}>
        <PageHeader
          title="Sentiment Analysis"
          description="Comprehensive sentiment analysis dashboard with real-time insights across all platforms"
          icon={MoodIcon}
        />

        <Box sx={{ mt: 3 }}>
          <SentimentDashboard />
        </Box>
      </Container>
    </>
  );
};

export default SentimentAnalysisPage;

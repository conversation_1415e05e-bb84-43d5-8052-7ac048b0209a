/**
 * Tests for CampaignPerformance component
 */
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CampaignPerformance from '../CampaignPerformance';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks and API
vi.mock('../../hooks/useNotification', () => ({
  useNotification: vi.fn(() => ({
    showErrorNotification: vi.fn(),
    showInfoNotification: vi.fn(),
    clearNotification: vi.fn()
  }))
}));

vi.mock('../../api', () => ({
  default: {
    get: vi.fn()
  }
}));

// Mock Nivo charts
vi.mock('@nivo/bar', () => ({
  ResponsiveBar: ({ data }) => <div data-testid="bar-chart">{JSON.stringify(data)}</div>
}));

vi.mock('@nivo/pie', () => ({
  ResponsivePie: ({ data }) => <div data-testid="pie-chart">{JSON.stringify(data)}</div>
}));

vi.mock('@nivo/line', () => ({
  ResponsiveLine: ({ data }) => <div data-testid="line-chart">{JSON.stringify(data)}</div>
}));

describe('CampaignPerformance', () => {
  const mockApi = require('../../api').default;
  const mockNotification = {
    showErrorNotification: vi.fn(),
    showInfoNotification: vi.fn(),
    clearNotification: vi.fn()
  };

  const mockCampaigns = [
    { id: 'campaign1', name: 'Summer Sale Campaign' },
    { id: 'campaign2', name: 'Holiday Promotion' },
    { id: 'campaign3', name: 'Product Launch' }
  ];

  const mockPerformanceData = {
    campaign_id: 'campaign1',
    campaign_name: 'Summer Sale Campaign',
    date_range: {
      start_date: '2023-01-01',
      end_date: '2023-01-30'
    },
    summary: {
      total_engagement: 15000,
      total_clicks: 8500,
      total_conversions: 450,
      total_impressions: 125000,
      total_reach: 85000,
      conversion_rate: '5.29'
    },
    daily_metrics: [
      {
        date: '2023-01-01',
        engagement: 500,
        clicks: 280,
        conversions: 15,
        impressions: 4200,
        reach: 2800
      },
      {
        date: '2023-01-02',
        engagement: 620,
        clicks: 310,
        conversions: 18,
        impressions: 4500,
        reach: 3100
      },
      {
        date: '2023-01-03',
        engagement: 580,
        clicks: 295,
        conversions: 16,
        impressions: 4300,
        reach: 2950
      }
    ],
    platform_breakdown: [
      { platform: 'facebook', engagement: 8000, clicks: 4500, conversions: 250 },
      { platform: 'instagram', engagement: 5000, clicks: 2800, conversions: 150 },
      { platform: 'twitter', engagement: 2000, clicks: 1200, conversions: 50 }
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockImplementation((url) => {
      if (url === '/api/campaigns') {
        return Promise.resolve({ data: mockCampaigns });
      }
      if (url === '/api/analytics/campaign-performance') {
        return Promise.resolve({ data: mockPerformanceData });
      }
      return Promise.reject(new Error('Unknown endpoint'));
    });

    const { useNotification } = require('../../hooks/useNotification');
    useNotification.mockReturnValue(mockNotification);
  });

  test('renders campaign performance dashboard', async () => {
    render(
      <TestWrapper>
        <CampaignPerformance />
      </TestWrapper>
    );

    expect(screen.getByText('Campaign Performance')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('Summer Sale Campaign')).toBeInTheDocument();
    });
  });

  test('displays performance metrics correctly', async () => {
    render(
      <TestWrapper>
        <CampaignPerformance data={mockPerformanceData} />
      </TestWrapper>
    );

    expect(screen.getByText('15,000')).toBeInTheDocument(); // Total engagement
    expect(screen.getByText('8,500')).toBeInTheDocument(); // Total clicks
    expect(screen.getByText('450')).toBeInTheDocument(); // Total conversions
    expect(screen.getByText('5.29%')).toBeInTheDocument(); // Conversion rate
  });

  test('shows loading state when loading prop is true', () => {
    render(
      <TestWrapper>
        <CampaignPerformance loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('displays charts correctly', async () => {
    render(
      <TestWrapper>
        <CampaignPerformance data={mockPerformanceData} />
      </TestWrapper>
    );

    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
  });

  test('handles chart type changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignPerformance data={mockPerformanceData} />
      </TestWrapper>
    );

    // Open chart options menu
    const menuButton = screen.getByLabelText('Chart options');
    await user.click(menuButton);

    // Switch to pie chart
    await user.click(screen.getByText('Pie Chart'));

    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();

    // Open menu again and switch to line chart
    await user.click(menuButton);
    await user.click(screen.getByText('Line Chart'));

    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
  });

  test('handles campaign selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignPerformance />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('Summer Sale Campaign')).toBeInTheDocument();
    });

    // Clear previous API calls
    vi.clearAllMocks();

    // Change campaign
    const campaignSelect = screen.getByDisplayValue('Summer Sale Campaign');
    await user.click(campaignSelect);
    await user.click(screen.getByText('Holiday Promotion'));

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/campaign-performance', {
        params: { campaign_id: 'campaign2', days: 30 }
      });
    });
  });

  test('handles date range changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignPerformance />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('Last 30 days')).toBeInTheDocument();
    });

    // Clear previous API calls
    vi.clearAllMocks();

    // Change date range
    const dateRangeSelect = screen.getByDisplayValue('Last 30 days');
    await user.click(dateRangeSelect);
    await user.click(screen.getByText('Last 7 days'));

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/campaign-performance', {
        params: { campaign_id: 'campaign1', days: 7 }
      });
    });
  });

  test('handles metrics selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignPerformance data={mockPerformanceData} />
      </TestWrapper>
    );

    // The metrics autocomplete should be present
    const metricsInput = screen.getByLabelText('Metrics');
    expect(metricsInput).toBeInTheDocument();

    // Should show selected metrics as chips
    expect(screen.getByText('Engagement')).toBeInTheDocument();
    expect(screen.getByText('Clicks')).toBeInTheDocument();
    expect(screen.getByText('Conversions')).toBeInTheDocument();
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignPerformance />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/campaigns');
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh data');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/analytics/campaign-performance', {
        params: { campaign_id: 'campaign1', days: 30 }
      });
    });
  });

  test('handles export functionality', async () => {
    const user = userEvent.setup();
    
    // Mock document.createElement and related methods
    const mockLink = {
      setAttribute: vi.fn(),
      click: vi.fn(),
      style: {}
    };
    const mockCreateElement = vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    const mockAppendChild = vi.spyOn(document.body, 'appendChild').mockImplementation(() => {});
    const mockRemoveChild = vi.spyOn(document.body, 'removeChild').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <CampaignPerformance data={mockPerformanceData} />
      </TestWrapper>
    );

    // Click export button
    const exportButton = screen.getByLabelText('Export data');
    await user.click(exportButton);

    expect(mockCreateElement).toHaveBeenCalledWith('a');
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockNotification.showInfoNotification).toHaveBeenCalledWith(
      'Generating campaign performance report. Please wait...', 
      0
    );

    // Cleanup mocks
    mockCreateElement.mockRestore();
    mockAppendChild.mockRestore();
    mockRemoveChild.mockRestore();
  });

  test('handles API error gracefully', async () => {
    mockApi.get.mockImplementation((url) => {
      if (url === '/api/campaigns') {
        return Promise.reject(new Error('API Error'));
      }
      return Promise.resolve({ data: [] });
    });

    render(
      <TestWrapper>
        <CampaignPerformance />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockNotification.showErrorNotification).toHaveBeenCalledWith('Failed to load campaigns');
    });
  });

  test('works with external data prop', () => {
    render(
      <TestWrapper>
        <CampaignPerformance data={mockPerformanceData} />
      </TestWrapper>
    );

    expect(screen.getByText('15,000')).toBeInTheDocument();
    expect(mockApi.get).not.toHaveBeenCalledWith('/api/analytics/campaign-performance');
  });

  test('works with external loading prop', () => {
    render(
      <TestWrapper>
        <CampaignPerformance loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('calls onDataLoad prop when data is loaded', async () => {
    const mockOnDataLoad = vi.fn();
    
    render(
      <TestWrapper>
        <CampaignPerformance onDataLoad={mockOnDataLoad} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockOnDataLoad).toHaveBeenCalledWith(mockPerformanceData);
    });
  });

  test('calls external onRefresh prop when provided', async () => {
    const mockOnRefresh = vi.fn();
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CampaignPerformance data={mockPerformanceData} onRefresh={mockOnRefresh} />
      </TestWrapper>
    );

    const refreshButton = screen.getByLabelText('Refresh data');
    await user.click(refreshButton);

    expect(mockOnRefresh).toHaveBeenCalled();
  });

  test('shows no data message when performance data is empty', () => {
    render(
      <TestWrapper>
        <CampaignPerformance data={null} loading={false} />
      </TestWrapper>
    );

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  test('handles missing daily_metrics gracefully', () => {
    const incompleteData = {
      ...mockPerformanceData,
      daily_metrics: null
    };

    render(
      <TestWrapper>
        <CampaignPerformance data={incompleteData} />
      </TestWrapper>
    );

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  test('disables controls during loading', async () => {
    render(
      <TestWrapper>
        <CampaignPerformance loading={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      const campaignSelect = screen.getByLabelText('Campaign');
      const dateRangeSelect = screen.getByLabelText('Date Range');
      const metricsInput = screen.getByLabelText('Metrics');
      const exportButton = screen.getByLabelText('Export data');
      const refreshButton = screen.getByLabelText('Refresh data');

      expect(campaignSelect).toBeDisabled();
      expect(dateRangeSelect).toBeDisabled();
      expect(metricsInput).toBeDisabled();
      expect(exportButton).toBeDisabled();
      expect(refreshButton).toBeDisabled();
    });
  });
});

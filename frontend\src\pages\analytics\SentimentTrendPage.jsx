// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Container,
  Box,
  Paper,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  AlertTitle,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  Insights as InsightsIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../api';
import SentimentTrendChart from '../../components/analytics/SentimentTrendChart';
import SentimentAlertSettings from '../../components/alerts/SentimentAlertSettings';
import SentimentAlertsList from '../../components/alerts/SentimentAlertsList';
import PageHeader from '../../components/common/PageHeader';

const SentimentTrendPage = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [contentList, setContentList] = useState([]);
  const [selectedContentIds, setSelectedContentIds] = useState([]);
  const [platforms, setPlatforms] = useState([]);

  useEffect(() => {
    const fetchContentData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch content with sentiment data
        const response = await api.get('/api/content', {
          params: {
            has_sentiment: true,
            limit: 100,
            sort: 'published_at',
            order: 'desc',
          }
        });

        if (response.data && response.data.length > 0) {
          setContentList(response.data);

          // Set default selection to the 10 most recent content items
          setSelectedContentIds(response.data.slice(0, 10).map(item => item.id));

          // Extract unique platforms
          const uniquePlatforms = [...new Set(
            response.data.flatMap(item => item.platforms || [])
          )];
          setPlatforms(uniquePlatforms);
        }
      } catch (error) {
        console.error('Error fetching content data:', error);
        setError('Failed to load content data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchContentData();
  }, []);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleContentSelectionChange = (event) => {
    const {
      target: { value },
    } = event;
    setSelectedContentIds(
      // On autofill we get a stringified value.
      typeof value === 'string' ? value.split(',') : value,
    );
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 8 }}>
      <PageHeader
        title="Sentiment Analysis"
        description="Track sentiment trends across your content and set up alerts for significant changes"
        icon={InsightsIcon}
      />

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Sentiment Trends" icon={<TimelineIcon />} iconPosition="start" />
          <Tab label="Sentiment Alerts" icon={<InsightsIcon />} iconPosition="start" />
          <Tab label="Alert Settings" icon={<SettingsIcon />} iconPosition="start" />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {tabValue === 0 && (
            <Box>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
                  <CircularProgress />
                </Box>
              ) : error ? (
                <Alert severity="error" sx={{ mb: 3 }}>
                  <AlertTitle>Error</AlertTitle>
                  {error}
                </Alert>
              ) : contentList.length === 0 ? (
                <Alert severity="info" sx={{ mb: 3 }}>
                  <AlertTitle>No Content Found</AlertTitle>
                  No content with sentiment data was found. Publish some content and wait for engagement to see sentiment analysis.
                </Alert>
              ) : (
                <>
                  <Box sx={{ mb: 3 }}>
                    <FormControl fullWidth>
                      <InputLabel id="content-select-label">Select Content</InputLabel>
                      <Select
                        labelId="content-select-label"
                        id="content-select"
                        multiple
                        value={selectedContentIds}
                        onChange={handleContentSelectionChange}
                        renderValue={(selected) => `${selected.length} items selected`}
                      >
                        {contentList.map((content) => (
                          <MenuItem key={content.id} value={content.id}>
                            {content.title || 'Untitled Content'} ({new Date(content.published_at).toLocaleDateString()})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>

                  <SentimentTrendChart
                    userId={user?.id}
                    contentIds={selectedContentIds}
                    platforms={platforms}
                  />
                </>
              )}
            </Box>
          )}

          {tabValue === 1 && (
            <Box>
              <SentimentAlertsList />
            </Box>
          )}

          {tabValue === 2 && (
            <Box>
              <SentimentAlertSettings />
            </Box>
          )}
        </Box>
      </Paper>
    </Container>
  );
};

export default SentimentTrendPage;

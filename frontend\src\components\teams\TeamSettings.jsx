/**
 * Enhanced Team Settings - Enterprise-grade team configuration management component
 * Features: Comprehensive team settings system with advanced configuration options, security settings,
 * and team management capabilities, detailed team customization with branding options and access controls,
 * advanced settings features with backup/restore functionality and audit logging, ACE Social's team
 * management system integration with seamless configuration workflow and permission handling, settings
 * interaction features including validation, preview modes, and contextual help, configuration
 * capabilities with template management and bulk operations, real-time settings updates with live
 * validation feedback and automatic synchronization, and seamless ACE Social platform integration
 * with advanced team settings orchestration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo
} from 'react';
import { useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  TextField,
  Divider,
  CircularProgress,
  Alert,
  Grid,
  useTheme,
  alpha,
  Card,
  CardContent,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  LinearProgress,
  Fade,
  Tabs,
  Tab,
  Snackbar
} from '@mui/material';
import {
  Save as SaveIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Group as GroupIcon,
  Lock as LockIcon,
  Public as PublicIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useTeam } from '../../contexts/TeamContext';
import { useConfirmation } from '../../contexts/ConfirmationContext';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Team privacy levels
const PRIVACY_LEVELS = {
  PUBLIC: 'public',
  PRIVATE: 'private',
  INVITE_ONLY: 'invite_only'
};

// Settings tabs
const SETTINGS_TABS = {
  GENERAL: 0,
  PRIVACY: 1,
  NOTIFICATIONS: 2,
  ADVANCED: 3
};

/**
 * Enhanced Team Settings - Comprehensive team configuration with advanced features
 * Implements detailed team management and enterprise-grade settings capabilities
 */
const TeamSettings = memo(forwardRef(({
  team,
  onTeamUpdated,
  onSettingsChange,
  onAnalyticsTrack
}, ref) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { updateTeam, deleteTeam } = useTeam();
  const { showConfirmation } = useConfirmation();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Enhanced state management
  const [activeTab, setActiveTab] = useState(SETTINGS_TABS.GENERAL);
  const [name, setName] = useState(team?.name || '');
  const [description, setDescription] = useState(team?.description || '');
  const [privacy, setPrivacy] = useState(team?.privacy || PRIVACY_LEVELS.PRIVATE);
  const [allowInvites, setAllowInvites] = useState(team?.allow_invites ?? true);
  const [emailNotifications, setEmailNotifications] = useState(team?.email_notifications ?? true);
  const [pushNotifications, setPushNotifications] = useState(team?.push_notifications ?? true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [settingsAnalytics, setSettingsAnalytics] = useState({
    settingsViews: 0,
    changesApplied: 0,
    lastActivity: new Date().toISOString()
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    saveSettings: () => handleUpdateTeam(),
    resetSettings: () => handleResetSettings(),
    deleteTeam: () => handleDeleteTeam(),
    getSettingsData: () => getSettingsData(),
    getAnalytics: () => settingsAnalytics,
    setTab: (tab) => setActiveTab(tab)
  }), [
    settingsAnalytics,
    handleUpdateTeam,
    handleResetSettings,
    handleDeleteTeam,
    getSettingsData
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced utility functions
  const getSettingsData = useCallback(() => {
    return {
      name,
      description,
      privacy,
      allowInvites,
      emailNotifications,
      pushNotifications
    };
  }, [name, description, privacy, allowInvites, emailNotifications, pushNotifications]);

  const handleResetSettings = useCallback(() => {
    setName(team?.name || '');
    setDescription(team?.description || '');
    setPrivacy(team?.privacy || PRIVACY_LEVELS.PRIVATE);
    setAllowInvites(team?.allow_invites ?? true);
    setEmailNotifications(team?.email_notifications ?? true);
    setPushNotifications(team?.push_notifications ?? true);
    setError(null);
    setSuccess(false);
    setHasChanges(false);
    announceToScreenReader('Settings reset to original values');
  }, [team, announceToScreenReader]);

  // Track changes
  useEffect(() => {
    const originalData = {
      name: team?.name || '',
      description: team?.description || '',
      privacy: team?.privacy || PRIVACY_LEVELS.PRIVATE,
      allowInvites: team?.allow_invites ?? true,
      emailNotifications: team?.email_notifications ?? true,
      pushNotifications: team?.push_notifications ?? true
    };

    const currentData = getSettingsData();
    const hasChangesNow = JSON.stringify(originalData) !== JSON.stringify(currentData);

    if (hasChangesNow !== hasChanges) {
      setHasChanges(hasChangesNow);

      if (onSettingsChange) {
        onSettingsChange({
          hasChanges: hasChangesNow,
          settings: currentData
        });
      }
    }
  }, [team, getSettingsData, hasChanges, onSettingsChange]);

  // Enhanced update team handler
  const handleUpdateTeam = useCallback(async (e) => {
    if (e) e.preventDefault();
    e.preventDefault();

    if (!name.trim()) {
      setError('Team name is required');
      announceToScreenReader('Team name is required');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const updateData = {
        name: name.trim(),
        description: description.trim() || undefined,
        privacy,
        allow_invites: allowInvites,
        email_notifications: emailNotifications,
        push_notifications: pushNotifications
      };

      const result = await updateTeam(team.id, updateData);

      if (result) {
        setSuccess(true);
        setHasChanges(false);

        setSettingsAnalytics(prev => ({
          ...prev,
          changesApplied: prev.changesApplied + 1,
          lastActivity: new Date().toISOString()
        }));

        if (onTeamUpdated) {
          onTeamUpdated();
        }

        if (onAnalyticsTrack) {
          onAnalyticsTrack({
            action: 'team_settings_updated',
            teamId: team.id,
            timestamp: new Date().toISOString()
          });
        }

        announceToScreenReader('Team settings updated successfully');
      }
    } catch (err) {
      const errorMessage = err.response?.data?.detail || 'Failed to update team';
      setError(errorMessage);
      announceToScreenReader(`Failed to update team: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, [
    name,
    description,
    privacy,
    allowInvites,
    emailNotifications,
    pushNotifications,
    team.id,
    updateTeam,
    onTeamUpdated,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  // Enhanced delete team handler
  const handleDeleteTeam = useCallback(() => {
    showConfirmation({
      title: 'Delete Team',
      message: `Are you sure you want to delete the team "${team.name}"? This action will permanently remove all team data, members, and associated content. This cannot be undone.`,
      confirmText: 'Delete Team',
      confirmColor: 'error',
      onConfirm: async () => {
        setLoading(true);

        try {
          const success = await deleteTeam(team.id);

          if (success) {
            if (onAnalyticsTrack) {
              onAnalyticsTrack({
                action: 'team_deleted',
                teamId: team.id,
                timestamp: new Date().toISOString()
              });
            }

            announceToScreenReader('Team deleted successfully');
            navigate('/teams');
          }
        } catch (err) {
          const errorMessage = err.response?.data?.detail || 'Failed to delete team';
          setError(errorMessage);
          announceToScreenReader(`Failed to delete team: ${errorMessage}`);
        } finally {
          setLoading(false);
        }
      }
    });
  }, [team.id, team.name, deleteTeam, onAnalyticsTrack, announceToScreenReader, navigate, showConfirmation]);

  return (
    <Box sx={{ ...glassMorphismStyles, p: 3 }}>
      {/* Enhanced Header */}
      <Box sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" fontWeight="bold" sx={{
              color: ACE_COLORS.DARK,
              background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${ACE_COLORS.YELLOW} 100%)`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              Team Settings
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Configure your team preferences and settings
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            {hasChanges && (
              <Chip
                label="Unsaved Changes"
                color="warning"
                size="small"
                sx={{
                  backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                  color: ACE_COLORS.DARK,
                  fontWeight: 'bold'
                }}
              />
            )}

            <Tooltip title="Reset Settings">
              <IconButton
                onClick={handleResetSettings}
                disabled={loading || !hasChanges}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      {/* Enhanced Tabs */}
      <Box sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 600,
              color: ACE_COLORS.DARK,
              '&.Mui-selected': {
                color: ACE_COLORS.PURPLE
              }
            },
            '& .MuiTabs-indicator': {
              backgroundColor: ACE_COLORS.PURPLE,
              height: 3
            }
          }}
        >
          <Tab label="General" icon={<SettingsIcon />} iconPosition="start" />
          <Tab label="Privacy" icon={<SecurityIcon />} iconPosition="start" />
          <Tab label="Notifications" icon={<NotificationsIcon />} iconPosition="start" />
          <Tab label="Advanced" icon={<WarningIcon />} iconPosition="start" />
        </Tabs>
      </Box>

      {/* Success/Error Alerts */}
      <Fade in={success || !!error} timeout={500}>
        <Box sx={{ mb: 3 }}>
          {success && (
            <Alert
              severity="success"
              sx={{
                border: `1px solid ${alpha('#4CAF50', 0.3)}`,
                backgroundColor: alpha('#4CAF50', 0.1),
                mb: 2
              }}
              action={
                <IconButton size="small" onClick={() => setSuccess(false)}>
                  <CloseIcon />
                </IconButton>
              }
            >
              Team settings updated successfully!
            </Alert>
          )}

          {error && (
            <Alert
              severity="error"
              sx={{
                border: `1px solid ${alpha('#F44336', 0.3)}`,
                backgroundColor: alpha('#F44336', 0.1)
              }}
              action={
                <IconButton size="small" onClick={() => setError(null)}>
                  <CloseIcon />
                </IconButton>
              }
            >
              {error}
            </Alert>
          )}
        </Box>
      </Fade>

      {/* Enhanced Settings Content */}
      <Grid container spacing={3}>
        {/* General Settings */}
        {activeTab === SETTINGS_TABS.GENERAL && (
          <Fade in timeout={500}>
            <Grid item xs={12} md={8}>
              <Card sx={{ ...glassMorphismStyles, border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, mb: 3 }}>
                    Team Information
                  </Typography>

                  <Box component="form" onSubmit={handleUpdateTeam}>
                    <TextField
                      fullWidth
                      label="Team Name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                      disabled={loading}
                      InputProps={{
                        startAdornment: <GroupIcon sx={{ color: 'text.secondary', mr: 1 }} />
                      }}
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: ACE_COLORS.PURPLE
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: ACE_COLORS.PURPLE
                          }
                        }
                      }}
                    />

                    <TextField
                      fullWidth
                      label="Description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      multiline
                      rows={4}
                      disabled={loading}
                      placeholder="Describe your team's purpose and goals..."
                      helperText={`${description.length}/500 characters`}
                      inputProps={{ maxLength: 500 }}
                      sx={{
                        mb: 3,
                        '& .MuiOutlinedInput-root': {
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: ACE_COLORS.PURPLE
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: ACE_COLORS.PURPLE
                          }
                        }
                      }}
                    />

                    {/* Loading Progress */}
                    {loading && (
                      <Box sx={{ mb: 2 }}>
                        <LinearProgress
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: ACE_COLORS.PURPLE
                            }
                          }}
                        />
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1, textAlign: 'center' }}>
                          Saving settings...
                        </Typography>
                      </Box>
                    )}

                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                      <Button
                        variant="outlined"
                        onClick={handleResetSettings}
                        disabled={loading || !hasChanges}
                        sx={{
                          borderColor: 'text.secondary',
                          color: 'text.secondary',
                          '&:hover': {
                            borderColor: 'text.primary',
                            backgroundColor: alpha('text.secondary', 0.1)
                          }
                        }}
                      >
                        Reset
                      </Button>

                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={loading ? <CircularProgress size={20} sx={{ color: ACE_COLORS.WHITE }} /> : <SaveIcon />}
                        disabled={loading || !name.trim() || !hasChanges}
                        sx={{
                          backgroundColor: ACE_COLORS.PURPLE,
                          px: 4,
                          '&:hover': {
                            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                          }
                        }}
                      >
                        {loading ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Fade>
        )}

        {/* Privacy Settings */}
        {activeTab === SETTINGS_TABS.PRIVACY && (
          <Fade in timeout={500}>
            <Grid item xs={12} md={8}>
              <Card sx={{ ...glassMorphismStyles, border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, mb: 3 }}>
                    Privacy & Access Control
                  </Typography>

                  <Stack spacing={3}>
                    <FormControl fullWidth>
                      <InputLabel>Team Privacy</InputLabel>
                      <Select
                        value={privacy}
                        label="Team Privacy"
                        onChange={(e) => setPrivacy(e.target.value)}
                        disabled={loading}
                      >
                        <MenuItem value={PRIVACY_LEVELS.PUBLIC}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <PublicIcon fontSize="small" />
                            Public - Anyone can find and join
                          </Box>
                        </MenuItem>
                        <MenuItem value={PRIVACY_LEVELS.PRIVATE}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <LockIcon fontSize="small" />
                            Private - Invitation required
                          </Box>
                        </MenuItem>
                        <MenuItem value={PRIVACY_LEVELS.INVITE_ONLY}>
                          <Box display="flex" alignItems="center" gap={1}>
                            <SecurityIcon fontSize="small" />
                            Invite Only - Admin approval required
                          </Box>
                        </MenuItem>
                      </Select>
                    </FormControl>

                    <FormControlLabel
                      control={
                        <Switch
                          checked={allowInvites}
                          onChange={(e) => setAllowInvites(e.target.checked)}
                          disabled={loading}
                          sx={{
                            '& .MuiSwitch-switchBase.Mui-checked': {
                              color: ACE_COLORS.PURPLE
                            },
                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                              backgroundColor: ACE_COLORS.PURPLE
                            }
                          }}
                        />
                      }
                      label="Allow members to send invitations"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Fade>
        )}

        {/* Notifications Settings */}
        {activeTab === SETTINGS_TABS.NOTIFICATIONS && (
          <Fade in timeout={500}>
            <Grid item xs={12} md={8}>
              <Card sx={{ ...glassMorphismStyles, border: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, mb: 3 }}>
                    Notification Preferences
                  </Typography>

                  <Stack spacing={3}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={emailNotifications}
                          onChange={(e) => setEmailNotifications(e.target.checked)}
                          disabled={loading}
                          sx={{
                            '& .MuiSwitch-switchBase.Mui-checked': {
                              color: ACE_COLORS.PURPLE
                            },
                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                              backgroundColor: ACE_COLORS.PURPLE
                            }
                          }}
                        />
                      }
                      label="Email notifications for team activities"
                    />

                    <FormControlLabel
                      control={
                        <Switch
                          checked={pushNotifications}
                          onChange={(e) => setPushNotifications(e.target.checked)}
                          disabled={loading}
                          sx={{
                            '& .MuiSwitch-switchBase.Mui-checked': {
                              color: ACE_COLORS.PURPLE
                            },
                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                              backgroundColor: ACE_COLORS.PURPLE
                            }
                          }}
                        />
                      }
                      label="Push notifications for important updates"
                    />
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Fade>
        )}

        {/* Advanced/Danger Zone */}
        {activeTab === SETTINGS_TABS.ADVANCED && (
          <Fade in timeout={500}>
            <Grid item xs={12} md={8}>
              <Card sx={{
                ...glassMorphismStyles,
                border: `2px solid ${alpha('#F44336', 0.3)}`,
                backgroundColor: alpha('#F44336', 0.05)
              }}>
                <CardContent sx={{ p: 4 }}>
                  <Typography variant="h6" gutterBottom sx={{ color: '#F44336', mb: 3 }}>
                    Danger Zone
                  </Typography>

                  <Typography variant="body2" color="text.secondary" paragraph>
                    Deleting this team will permanently remove all team data, members, and associated content.
                    This action cannot be undone and will immediately revoke access for all team members.
                  </Typography>

                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={handleDeleteTeam}
                    disabled={loading}
                    sx={{
                      borderColor: '#F44336',
                      color: '#F44336',
                      '&:hover': {
                        borderColor: '#F44336',
                        backgroundColor: alpha('#F44336', 0.1)
                      }
                    }}
                  >
                    Delete Team Permanently
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Fade>
        )}

        {/* Settings Summary Sidebar */}
        <Grid item xs={12} md={4}>
          <Card sx={{ ...glassMorphismStyles }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
                Settings Summary
              </Typography>

              <Divider sx={{ mb: 2, borderColor: alpha(ACE_COLORS.PURPLE, 0.2) }} />

              <Stack spacing={2}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Team Name
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {name || 'Unnamed Team'}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Privacy Level
                  </Typography>
                  <Chip
                    size="small"
                    label={privacy === PRIVACY_LEVELS.PUBLIC ? 'Public' : privacy === PRIVACY_LEVELS.PRIVATE ? 'Private' : 'Invite Only'}
                    sx={{
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                      color: ACE_COLORS.PURPLE
                    }}
                  />
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Member Invitations
                  </Typography>
                  <Typography variant="body1">
                    {allowInvites ? 'Enabled' : 'Disabled'}
                  </Typography>
                </Box>

                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Notifications
                  </Typography>
                  <Typography variant="body1">
                    {emailNotifications && pushNotifications ? 'All enabled' :
                     emailNotifications ? 'Email only' :
                     pushNotifications ? 'Push only' : 'Disabled'}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Notifications */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={index}
          open={true}
          autoHideDuration={6000}
          onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            severity={notification.severity || 'info'}
            onClose={() => setNotifications(prev => prev.filter((_, i) => i !== index))}
            sx={{
              ...glassMorphismStyles,
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </Box>
  );
}));

TeamSettings.displayName = 'TeamSettings';

TeamSettings.propTypes = {
  /** Team object with settings data */
  team: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    privacy: PropTypes.string,
    allow_invites: PropTypes.bool,
    email_notifications: PropTypes.bool,
    push_notifications: PropTypes.bool
  }).isRequired,
  /** Function called when team is updated */
  onTeamUpdated: PropTypes.func,
  /** Function called when settings change */
  onSettingsChange: PropTypes.func,
  /** Function called for analytics tracking */
  onAnalyticsTrack: PropTypes.func,
  /** Enable advanced settings */
  enableAdvancedSettings: PropTypes.bool,
  /** Enable security settings */
  enableSecuritySettings: PropTypes.bool,
  /** Enable notification settings */
  enableNotificationSettings: PropTypes.bool,
  /** Enable branding settings */
  enableBrandingSettings: PropTypes.bool
};

export default TeamSettings;

/**
 * Enhanced AppSumo Package Creator - Enterprise-grade package creation component
 * Features: Comprehensive AppSumo package creation with advanced tier management, pricing
 * configuration, and feature bundling for lifetime deal packages, detailed package customization
 * with dynamic tier creation and personalized package flows, advanced package creation features
 * with pricing calculators and tier comparison tools, ACE Social's AppSumo system integration
 * with seamless package lifecycle management, package creation interaction features including
 * drag-and-drop tier organization and real-time pricing updates, package creation state
 * management with real-time validation updates and automatic package synchronization, and
 * real-time package creation updates with live preview displays and dynamic pricing calculations
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Divider,
  LinearProgress,
  Collapse,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Badge,
  Skeleton,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Info as InfoIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  Category as CategoryIcon,
  ExpandMore as ExpandMoreIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  DragIndicator as DragIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Calculate as CalculateIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { validateFormData, formatCurrency, getTierConfig } from '../../utils/appsumoHelpers';
import api from '../../api';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Package creation steps
const PACKAGE_CREATION_STEPS = {
  BASIC_INFO: 'basic_info',
  PRICING: 'pricing',
  TIERS: 'tiers',
  FEATURES: 'features',
  SCHEDULE: 'schedule',
  ADVANCED: 'advanced',
  PREVIEW: 'preview'
};

// Package validation states
const VALIDATION_STATES = {
  VALID: 'valid',
  INVALID: 'invalid',
  WARNING: 'warning',
  PENDING: 'pending'
};

// Package creation analytics events
const PACKAGE_ANALYTICS_EVENTS = {
  CREATION_STARTED: 'package_creation_started',
  STEP_COMPLETED: 'package_creation_step_completed',
  VALIDATION_FAILED: 'package_validation_failed',
  PRICING_CALCULATED: 'package_pricing_calculated',
  TIER_CONFIGURED: 'package_tier_configured',
  FEATURE_ADDED: 'package_feature_added',
  PREVIEW_VIEWED: 'package_preview_viewed',
  CREATION_COMPLETED: 'package_creation_completed',
  CREATION_CANCELLED: 'package_creation_cancelled'
};

/**
 * Enhanced AppSumo Package Creator - Comprehensive package creation with advanced features
 * Implements detailed package creation management and enterprise-grade creation capabilities
 */
const EnhancedPackageCreator = memo(forwardRef(({
  open = false,
  onClose,
  onPackageCreated,
  editingPackage = null,
  deals = [],
  tiers = [],
  enableAdvancedFeatures = true,
  enableRealTimeValidation = true,
  enableStepperNavigation = true,
  enableAccessibility = true,
  enableAnalytics = true,
  maxPackageFeatures = 50,
  onPackagePreview,
  onAnalyticsTrack,
  onValidationChange,
  onStepChange,
  className,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const formRef = useRef(null);
  const stepperRef = useRef(null);
  const previewRef = useRef(null);

  // Core state management
  const [formData, setFormData] = useState({
    deal_id: '',
    name: '',
    description: '',
    pricing: {
      regular_price: 0,
      appsumo_price: 59,
      discount_percentage: 0,
    },
    tiers: ['tier1'],
    start_date: new Date(),
    end_date: null,
    features: [],
    terms_and_conditions: '',
    redemption_url_pattern: '/appsumo/redeem/{code}',
    metadata: {
      max_stacking: 3,
      allow_upgrades: true,
      lifetime_access: true,
    },
    is_active: true,
  });

  // Enhanced state management
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [newFeature, setNewFeature] = useState('');
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [expandedSections, setExpandedSections] = useState(new Set(['basic_info']));
  const [showPreview, setShowPreview] = useState(false);
  const [validationState, setValidationState] = useState(VALIDATION_STATES.PENDING);
  const [packageAnalytics, setPackageAnalytics] = useState({
    creationStarted: null,
    stepsCompleted: 0,
    validationAttempts: 0,
    featuresAdded: 0,
    tiersConfigured: 0,
    pricingCalculations: 0
  });

  // Available tier types with enhanced configuration
  const availableTiers = useMemo(() => [
    {
      value: 'tier1',
      label: 'Single (1 User)',
      config: getTierConfig('tier1'),
      description: 'Perfect for individual creators',
      features: ['1 User Account', 'Basic Analytics', 'Standard Support'],
      recommended: false
    },
    {
      value: 'tier2',
      label: 'Double (2 Users)',
      config: getTierConfig('tier2'),
      description: 'Ideal for small teams',
      features: ['2 User Accounts', 'Advanced Analytics', 'Priority Support'],
      recommended: true
    },
    {
      value: 'tier3',
      label: 'Triple (3 Users)',
      config: getTierConfig('tier3'),
      description: 'Best for growing businesses',
      features: ['3 User Accounts', 'Premium Analytics', 'Premium Support'],
      recommended: false
    },
  ], []);

  // Enhanced form validation rules
  const validationRules = useMemo(() => [
    {
      key: 'deal_id',
      label: 'Deal ID',
      type: 'string',
      minLength: 3,
      maxLength: 50,
      pattern: /^[A-Z0-9_-]+$/,
      required: true
    },
    {
      key: 'name',
      label: 'Package Name',
      type: 'string',
      minLength: 3,
      maxLength: 100,
      required: true
    },
    {
      key: 'description',
      label: 'Description',
      type: 'string',
      minLength: 10,
      maxLength: 500,
      required: true
    },
    {
      key: 'pricing.regular_price',
      label: 'Regular Price',
      type: 'number',
      min: 0,
      max: 10000,
      required: true
    },
    {
      key: 'pricing.appsumo_price',
      label: 'AppSumo Price',
      type: 'number',
      min: 0,
      max: 1000,
      required: true
    },
  ], []);

  // Package creation steps configuration
  const packageSteps = useMemo(() => [
    {
      key: PACKAGE_CREATION_STEPS.BASIC_INFO,
      label: 'Basic Information',
      description: 'Package name, ID, and description',
      icon: <CategoryIcon />,
      required: true
    },
    {
      key: PACKAGE_CREATION_STEPS.PRICING,
      label: 'Pricing Configuration',
      description: 'Set regular and AppSumo pricing',
      icon: <MoneyIcon />,
      required: true
    },
    {
      key: PACKAGE_CREATION_STEPS.TIERS,
      label: 'Tier Selection',
      description: 'Choose available tiers',
      icon: <SettingsIcon />,
      required: true
    },
    {
      key: PACKAGE_CREATION_STEPS.FEATURES,
      label: 'Package Features',
      description: 'Add package features',
      icon: <CheckCircleIcon />,
      required: true
    },
    {
      key: PACKAGE_CREATION_STEPS.SCHEDULE,
      label: 'Schedule',
      description: 'Set start and end dates',
      icon: <ScheduleIcon />,
      required: false
    },
    {
      key: PACKAGE_CREATION_STEPS.ADVANCED,
      label: 'Advanced Settings',
      description: 'Configure advanced options',
      icon: <SecurityIcon />,
      required: false
    },
    {
      key: PACKAGE_CREATION_STEPS.PREVIEW,
      label: 'Preview & Confirm',
      description: 'Review package configuration',
      icon: <PreviewIcon />,
      required: true
    }
  ], []);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    resetForm: () => handleReset(),
    validateForm: () => validateForm(),
    savePackage: () => handleSave(),
    previewPackage: () => handlePreview(),

    // Navigation methods
    nextStep: () => handleNextStep(),
    previousStep: () => handlePreviousStep(),
    goToStep: (step) => setActiveStep(step),

    // Data methods
    getFormData: () => formData,
    setFormData: (data) => setFormData(data),
    getValidationState: () => validationState,
    getPackageAnalytics: () => packageAnalytics,

    // Accessibility methods
    focusForm: () => formRef.current?.focus(),
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    exportConfiguration: () => handleExportConfiguration(),
    importConfiguration: (config) => handleImportConfiguration(config),
    calculatePricing: () => handlePricingCalculation(),
    validateTiers: () => validateTierConfiguration(),
    togglePreview: () => setShowPreview(!showPreview)
  }), [
    formData,
    validationState,
    packageAnalytics,
    showPreview,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Initialize form data when editing
  useEffect(() => {
    if (editingPackage) {
      setFormData({
        ...editingPackage,
        start_date: new Date(editingPackage.start_date),
        end_date: editingPackage.end_date ? new Date(editingPackage.end_date) : null,
      });

      if (enableAccessibility) {
        announceToScreenReader(`Editing package: ${editingPackage.name}`);
      }
    } else if (open) {
      // Reset form for new package
      handleReset();

      if (enableAccessibility) {
        announceToScreenReader('Creating new AppSumo package');
      }
    }
  }, [editingPackage, open, enableAccessibility, announceToScreenReader]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (open && enableAnalytics) {
      const startTime = new Date().toISOString();
      setPackageAnalytics(prev => ({
        ...prev,
        creationStarted: startTime
      }));

      if (onAnalyticsTrack) {
        onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.CREATION_STARTED, {
          editingMode: !!editingPackage,
          timestamp: startTime
        });
      }
    }
  }, [open, enableAnalytics, editingPackage, onAnalyticsTrack]);

  // Real-time validation effect
  useEffect(() => {
    if (enableRealTimeValidation && open) {
      const timeoutId = setTimeout(() => {
        setValidating(true);
        const validation = validateForm();
        setValidating(false);

        if (onValidationChange) {
          onValidationChange(validation);
        }
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [formData, enableRealTimeValidation, open, onValidationChange]);

  // Enhanced pricing calculation with analytics
  const handlePricingCalculation = useCallback(() => {
    const { regular_price, appsumo_price } = formData.pricing;

    if (regular_price > 0 && appsumo_price > 0) {
      const discount = ((regular_price - appsumo_price) / regular_price) * 100;
      const savings = regular_price - appsumo_price;

      setFormData(prev => ({
        ...prev,
        pricing: {
          ...prev.pricing,
          discount_percentage: Math.round(discount * 100) / 100,
          savings_amount: savings
        },
      }));

      setPackageAnalytics(prev => ({
        ...prev,
        pricingCalculations: prev.pricingCalculations + 1
      }));

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.PRICING_CALCULATED, {
          regularPrice: regular_price,
          appsumoPrice: appsumo_price,
          discountPercentage: discount,
          savingsAmount: savings
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(
          `Pricing calculated: ${discount.toFixed(1)}% discount, customers save ${formatCurrency(savings)}`
        );
      }
    }
  }, [formData.pricing, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Calculate discount percentage with enhanced features
  useEffect(() => {
    handlePricingCalculation();
  }, [formData.pricing.regular_price, formData.pricing.appsumo_price, handlePricingCalculation]);

  // Enhanced form validation with detailed feedback
  const validateForm = useCallback(() => {
    const validation = validateFormData(formData, validationRules);
    const enhancedErrors = { ...validation.errors };
    const enhancedWarnings = {};

    // Additional validations
    if (formData.tiers.length === 0) {
      enhancedErrors.tiers = 'At least one tier must be selected';
      validation.isValid = false;
    }

    if (formData.features.length === 0) {
      enhancedErrors.features = 'At least one feature must be added';
      validation.isValid = false;
    }

    if (formData.features.length > maxPackageFeatures) {
      enhancedErrors.features = `Maximum ${maxPackageFeatures} features allowed`;
      validation.isValid = false;
    }

    if (formData.end_date && formData.end_date <= formData.start_date) {
      enhancedErrors.end_date = 'End date must be after start date';
      validation.isValid = false;
    }

    // Pricing validations
    if (formData.pricing.appsumo_price >= formData.pricing.regular_price) {
      enhancedWarnings.pricing = 'AppSumo price should be lower than regular price for better conversion';
    }

    if (formData.pricing.discount_percentage < 10) {
      enhancedWarnings.discount = 'Consider offering at least 10% discount for AppSumo customers';
    }

    // Tier configuration validation
    if (formData.tiers.length > 3) {
      enhancedWarnings.tiers = 'More than 3 tiers may confuse customers';
    }

    // Feature validation
    if (formData.features.length < 3) {
      enhancedWarnings.features = 'Consider adding more features to increase package value';
    }

    setErrors(enhancedErrors);
    setWarnings(enhancedWarnings);

    // Update validation state
    const newValidationState = validation.isValid
      ? VALIDATION_STATES.VALID
      : VALIDATION_STATES.INVALID;
    setValidationState(newValidationState);

    // Track validation attempts
    setPackageAnalytics(prev => ({
      ...prev,
      validationAttempts: prev.validationAttempts + 1
    }));

    if (!validation.isValid && enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.VALIDATION_FAILED, {
        errors: Object.keys(enhancedErrors),
        warnings: Object.keys(enhancedWarnings)
      });
    }

    return validation.isValid;
  }, [
    formData,
    validationRules,
    maxPackageFeatures,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // Enhanced form field change handler with validation
  const handleFieldChange = useCallback((field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear error and warning for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
    if (warnings[field]) {
      setWarnings(prev => ({ ...prev, [field]: null }));
    }

    // Announce field changes for accessibility
    if (enableAccessibility) {
      announceToScreenReader(`${field} updated`);
    }
  }, [errors, warnings, enableAccessibility, announceToScreenReader]);

  // Enhanced tier selection with analytics
  const handleTierToggle = useCallback((tierValue) => {
    setFormData(prev => {
      const isRemoving = prev.tiers.includes(tierValue);
      const newTiers = isRemoving
        ? prev.tiers.filter(t => t !== tierValue)
        : [...prev.tiers, tierValue];

      return {
        ...prev,
        tiers: newTiers,
      };
    });

    // Update analytics
    setPackageAnalytics(prev => ({
      ...prev,
      tiersConfigured: prev.tiersConfigured + 1
    }));

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.TIER_CONFIGURED, {
        tierValue,
        action: formData.tiers.includes(tierValue) ? 'removed' : 'added',
        totalTiers: formData.tiers.length
      });
    }

    if (enableAccessibility) {
      const action = formData.tiers.includes(tierValue) ? 'removed' : 'added';
      const tierLabel = availableTiers.find(t => t.value === tierValue)?.label || tierValue;
      announceToScreenReader(`${tierLabel} tier ${action}`);
    }
  }, [formData.tiers, availableTiers, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced feature management with validation
  const handleAddFeature = useCallback(() => {
    const trimmedFeature = newFeature.trim();

    if (!trimmedFeature) return;

    if (formData.features.length >= maxPackageFeatures) {
      setErrors(prev => ({
        ...prev,
        features: `Maximum ${maxPackageFeatures} features allowed`
      }));
      return;
    }

    if (formData.features.includes(trimmedFeature)) {
      setWarnings(prev => ({
        ...prev,
        features: 'Feature already exists'
      }));
      return;
    }

    setFormData(prev => ({
      ...prev,
      features: [...prev.features, trimmedFeature],
    }));
    setNewFeature('');

    // Update analytics
    setPackageAnalytics(prev => ({
      ...prev,
      featuresAdded: prev.featuresAdded + 1
    }));

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.FEATURE_ADDED, {
        feature: trimmedFeature,
        totalFeatures: formData.features.length + 1
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Feature "${trimmedFeature}" added`);
    }
  }, [
    newFeature,
    formData.features,
    maxPackageFeatures,
    enableAnalytics,
    enableAccessibility,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  const handleRemoveFeature = useCallback((index) => {
    const removedFeature = formData.features[index];

    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));

    if (enableAccessibility) {
      announceToScreenReader(`Feature "${removedFeature}" removed`);
    }
  }, [formData.features, enableAccessibility, announceToScreenReader]);

  // Enhanced metadata change handler
  const handleMetadataChange = useCallback((key, value) => {
    setFormData(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        [key]: value,
      },
    }));

    if (enableAccessibility) {
      announceToScreenReader(`${key} setting updated`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  // Enhanced navigation handlers
  const handleNextStep = useCallback(() => {
    if (activeStep < packageSteps.length - 1) {
      const currentStep = packageSteps[activeStep];
      setCompletedSteps(prev => new Set([...prev, currentStep.key]));
      setActiveStep(prev => prev + 1);

      setPackageAnalytics(prev => ({
        ...prev,
        stepsCompleted: prev.stepsCompleted + 1
      }));

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.STEP_COMPLETED, {
          step: currentStep.key,
          stepIndex: activeStep
        });
      }

      if (onStepChange) {
        onStepChange(activeStep + 1, currentStep.key);
      }
    }
  }, [activeStep, packageSteps, enableAnalytics, onAnalyticsTrack, onStepChange]);

  const handlePreviousStep = useCallback(() => {
    if (activeStep > 0) {
      setActiveStep(prev => prev - 1);

      if (onStepChange) {
        onStepChange(activeStep - 1, packageSteps[activeStep - 1].key);
      }
    }
  }, [activeStep, packageSteps, onStepChange]);

  // Enhanced preview handler
  const handlePreview = useCallback(() => {
    setShowPreview(true);

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.PREVIEW_VIEWED, {
        packageName: formData.name,
        tiersCount: formData.tiers.length,
        featuresCount: formData.features.length
      });
    }

    if (onPackagePreview) {
      onPackagePreview(formData);
    }

    if (enableAccessibility) {
      announceToScreenReader('Package preview opened');
    }
  }, [formData, enableAnalytics, enableAccessibility, onAnalyticsTrack, onPackagePreview, announceToScreenReader]);

  // Enhanced reset handler
  const handleReset = useCallback(() => {
    const initialData = {
      deal_id: '',
      name: '',
      description: '',
      pricing: {
        regular_price: 0,
        appsumo_price: 59,
        discount_percentage: 0,
      },
      tiers: ['tier1'],
      start_date: new Date(),
      end_date: null,
      features: [],
      terms_and_conditions: '',
      redemption_url_pattern: '/appsumo/redeem/{code}',
      metadata: {
        max_stacking: 3,
        allow_upgrades: true,
        lifetime_access: true,
      },
      is_active: true,
    };

    setFormData(initialData);
    setErrors({});
    setWarnings({});
    setNewFeature('');
    setActiveStep(0);
    setCompletedSteps(new Set());
    setExpandedSections(new Set(['basic_info']));
    setShowPreview(false);
    setValidationState(VALIDATION_STATES.PENDING);
    setPackageAnalytics({
      creationStarted: null,
      stepsCompleted: 0,
      validationAttempts: 0,
      featuresAdded: 0,
      tiersConfigured: 0,
      pricingCalculations: 0
    });
  }, []);

  // Enhanced save handler with comprehensive error handling
  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      if (enableAccessibility) {
        announceToScreenReader('Package validation failed. Please check the form for errors.');
      }
      return;
    }

    setLoading(true);
    setValidationState(VALIDATION_STATES.PENDING);

    try {
      const packageData = {
        ...formData,
        start_date: formData.start_date.toISOString(),
        end_date: formData.end_date ? formData.end_date.toISOString() : null,
        created_at: editingPackage ? editingPackage.created_at : new Date().toISOString(),
        updated_at: new Date().toISOString(),
        analytics: packageAnalytics
      };

      let response;
      if (editingPackage) {
        response = await api.put(`/api/appsumo/deals/${editingPackage.id}`, packageData);
      } else {
        response = await api.post('/api/appsumo/deals', packageData);
      }

      setValidationState(VALIDATION_STATES.VALID);

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.CREATION_COMPLETED, {
          packageId: response.data.id,
          packageName: formData.name,
          editingMode: !!editingPackage,
          tiersCount: formData.tiers.length,
          featuresCount: formData.features.length,
          analytics: packageAnalytics
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(
          `Package ${editingPackage ? 'updated' : 'created'} successfully: ${formData.name}`
        );
      }

      if (onPackageCreated) {
        onPackageCreated(response.data);
      }

      handleClose();
    } catch (error) {
      console.error('Error saving package:', error);
      setValidationState(VALIDATION_STATES.INVALID);

      const errorMessage = error.response?.data?.detail ||
                          error.response?.data?.message ||
                          'Failed to save package. Please try again.';

      setErrors({
        save: errorMessage
      });

      if (enableAccessibility) {
        announceToScreenReader(`Error saving package: ${errorMessage}`);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack('package_creation_failed', {
          error: errorMessage,
          packageName: formData.name,
          editingMode: !!editingPackage
        });
      }
    } finally {
      setLoading(false);
    }
  }, [
    validateForm,
    formData,
    editingPackage,
    packageAnalytics,
    enableAccessibility,
    enableAnalytics,
    onAnalyticsTrack,
    onPackageCreated,
    announceToScreenReader
  ]);

  // Enhanced close handler with analytics
  const handleClose = useCallback(() => {
    if (enableAnalytics && onAnalyticsTrack && packageAnalytics.creationStarted) {
      onAnalyticsTrack(PACKAGE_ANALYTICS_EVENTS.CREATION_CANCELLED, {
        packageName: formData.name,
        stepsCompleted: packageAnalytics.stepsCompleted,
        timeSpent: Date.now() - new Date(packageAnalytics.creationStarted).getTime()
      });
    }

    handleReset();
    onClose();
  }, [
    enableAnalytics,
    onAnalyticsTrack,
    packageAnalytics,
    formData.name,
    handleReset,
    onClose
  ]);

  // Additional utility functions
  const handleExportConfiguration = useCallback(() => {
    const config = {
      ...formData,
      exportedAt: new Date().toISOString(),
      version: '2.0.0'
    };

    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `package-${formData.deal_id || 'new'}-config.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    if (enableAccessibility) {
      announceToScreenReader('Package configuration exported');
    }
  }, [formData, enableAccessibility, announceToScreenReader]);

  const handleImportConfiguration = useCallback((config) => {
    try {
      const importedData = typeof config === 'string' ? JSON.parse(config) : config;

      // Validate imported data structure
      if (importedData && typeof importedData === 'object') {
        setFormData(prev => ({
          ...prev,
          ...importedData,
          start_date: importedData.start_date ? new Date(importedData.start_date) : new Date(),
          end_date: importedData.end_date ? new Date(importedData.end_date) : null
        }));

        if (enableAccessibility) {
          announceToScreenReader('Package configuration imported successfully');
        }
      }
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        import: 'Invalid configuration format'
      }));

      if (enableAccessibility) {
        announceToScreenReader('Failed to import configuration');
      }
    }
  }, [enableAccessibility, announceToScreenReader]);

  const validateTierConfiguration = useCallback(() => {
    const tierValidation = {
      isValid: true,
      errors: [],
      warnings: []
    };

    if (formData.tiers.length === 0) {
      tierValidation.isValid = false;
      tierValidation.errors.push('At least one tier must be selected');
    }

    if (formData.tiers.length > 3) {
      tierValidation.warnings.push('More than 3 tiers may confuse customers');
    }

    // Check tier configuration consistency
    const tierConfigs = formData.tiers.map(tier => getTierConfig(tier));
    const hasInvalidTiers = tierConfigs.some(config => !config);

    if (hasInvalidTiers) {
      tierValidation.isValid = false;
      tierValidation.errors.push('Some selected tiers have invalid configuration');
    }

    return tierValidation;
  }, [formData.tiers]);

  // Enhanced dialog content renderer
  const renderDialogContent = useMemo(() => {
    if (showPreview) {
      return renderPackagePreview();
    }

    if (enableStepperNavigation) {
      return renderStepperContent();
    }

    return renderAccordionContent();
  }, [showPreview, enableStepperNavigation]);

  // Package preview renderer
  const renderPackagePreview = useCallback(() => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <PreviewIcon />
        Package Preview
      </Typography>

      <Card variant="outlined" sx={glassMorphismStyles}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h5" gutterBottom color={ACE_COLORS.PURPLE}>
                {formData.name || 'Untitled Package'}
              </Typography>
              <Typography variant="body1" paragraph>
                {formData.description || 'No description provided'}
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Typography variant="h6" gutterBottom>Pricing</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography
                    variant="h4"
                    sx={{ color: ACE_COLORS.PURPLE, fontWeight: 'bold' }}
                  >
                    {formatCurrency(formData.pricing.appsumo_price)}
                  </Typography>
                  {formData.pricing.regular_price > 0 && (
                    <Typography
                      variant="h6"
                      sx={{
                        textDecoration: 'line-through',
                        color: 'text.secondary'
                      }}
                    >
                      {formatCurrency(formData.pricing.regular_price)}
                    </Typography>
                  )}
                  {formData.pricing.discount_percentage > 0 && (
                    <Chip
                      label={`${formData.pricing.discount_percentage}% OFF`}
                      color="success"
                      size="small"
                    />
                  )}
                </Box>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>Available Tiers</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {formData.tiers.map(tier => {
                  const tierConfig = availableTiers.find(t => t.value === tier);
                  return (
                    <Chip
                      key={tier}
                      label={tierConfig?.label || tier}
                      color="primary"
                      variant="outlined"
                    />
                  );
                })}
              </Box>

              <Typography variant="h6" gutterBottom>Features</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {formData.features.map((feature, index) => (
                  <Chip
                    key={index}
                    label={feature}
                    size="small"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button
          onClick={() => setShowPreview(false)}
          startIcon={<CloseIcon />}
        >
          Back to Edit
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <RefreshIcon /> : <SaveIcon />}
          sx={{
            bgcolor: ACE_COLORS.PURPLE,
            '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
          }}
        >
          {loading ? 'Saving...' : (editingPackage ? 'Update Package' : 'Create Package')}
        </Button>
      </Box>
    </Box>
  ), [
    formData,
    availableTiers,
    glassMorphismStyles,
    handleSave,
    loading,
    editingPackage
  ]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: {
          ...glassMorphismStyles,
          maxHeight: '95vh',
          minHeight: '80vh',
          m: 1
        }
      }}
      TransitionComponent={Fade}
      TransitionProps={{ timeout: 300 }}
      aria-labelledby="package-creator-title"
      aria-describedby="package-creator-description"
      {...props}
    >
      <DialogTitle
        id="package-creator-title"
        sx={{
          background: `linear-gradient(135deg, ${ACE_COLORS.PURPLE} 0%, ${alpha(ACE_COLORS.PURPLE, 0.8)} 100%)`,
          color: ACE_COLORS.WHITE,
          position: 'relative'
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
              {editingPackage ? 'Edit AppSumo Package' : 'Create New AppSumo Package'}
            </Typography>
            {validating && (
              <Skeleton variant="circular" width={24} height={24} />
            )}
            {validationState === VALIDATION_STATES.VALID && (
              <CheckCircleIcon sx={{ color: ACE_COLORS.YELLOW }} />
            )}
            {validationState === VALIDATION_STATES.INVALID && (
              <ErrorIcon sx={{ color: '#f44336' }} />
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {enableAdvancedFeatures && (
              <>
                <Tooltip title="Preview Package">
                  <IconButton
                    onClick={handlePreview}
                    sx={{ color: ACE_COLORS.WHITE }}
                    disabled={!formData.name}
                  >
                    <VisibilityIcon />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Export Configuration">
                  <IconButton
                    onClick={handleExportConfiguration}
                    sx={{ color: ACE_COLORS.WHITE }}
                    disabled={!formData.name}
                  >
                    <AnalyticsIcon />
                  </IconButton>
                </Tooltip>
              </>
            )}

            <Tooltip title="Close">
              <IconButton
                onClick={handleClose}
                sx={{ color: ACE_COLORS.WHITE }}
                aria-label="Close package creator"
              >
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {loading && (
          <LinearProgress
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              bgcolor: alpha(ACE_COLORS.WHITE, 0.2),
              '& .MuiLinearProgress-bar': {
                bgcolor: ACE_COLORS.YELLOW
              }
            }}
          />
        )}
      </DialogTitle>

      <DialogContent
        id="package-creator-description"
        sx={{
          p: 0,
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            background: alpha(theme.palette.divider, 0.1),
          },
          '&::-webkit-scrollbar-thumb': {
            background: alpha(ACE_COLORS.PURPLE, 0.3),
            borderRadius: '4px',
          },
        }}
      >
        {/* Error and Warning Alerts */}
        {(errors.save || Object.keys(errors).length > 0 || Object.keys(warnings).length > 0) && (
          <Box sx={{ p: 2, pb: 0 }}>
            {errors.save && (
              <Alert
                severity="error"
                sx={{ mb: 1 }}
                action={
                  <IconButton
                    aria-label="close"
                    color="inherit"
                    size="small"
                    onClick={() => setErrors(prev => ({ ...prev, save: null }))}
                  >
                    <CloseIcon fontSize="inherit" />
                  </IconButton>
                }
              >
                {errors.save}
              </Alert>
            )}

            {Object.keys(warnings).length > 0 && (
              <Alert severity="warning" sx={{ mb: 1 }}>
                <Typography variant="body2">
                  Please review the following warnings:
                </Typography>
                <ul style={{ margin: '8px 0 0 16px', padding: 0 }}>
                  {Object.entries(warnings).map(([field, warning]) => (
                    <li key={field}>{warning}</li>
                  ))}
                </ul>
              </Alert>
            )}
          </Box>
        )}

        {/* Main Content */}
        <Box ref={formRef} tabIndex={-1}>
          {renderDialogContent}
        </Box>
      </DialogContent>

      <DialogActions
        sx={{
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.default, 0.85)} 100%)`,
          backdropFilter: 'blur(10px)',
          borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
          p: 2,
          gap: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
          {packageAnalytics.creationStarted && (
            <Typography variant="caption" color="text.secondary">
              Steps completed: {packageAnalytics.stepsCompleted} |
              Features added: {packageAnalytics.featuresAdded} |
              Validation attempts: {packageAnalytics.validationAttempts}
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          {enableStepperNavigation && activeStep > 0 && (
            <Button
              onClick={handlePreviousStep}
              disabled={loading}
              startIcon={<CloseIcon />}
            >
              Previous
            </Button>
          )}

          <Button
            onClick={handleClose}
            disabled={loading}
            variant="outlined"
          >
            Cancel
          </Button>

          {enableStepperNavigation && activeStep < packageSteps.length - 1 ? (
            <Button
              onClick={handleNextStep}
              disabled={loading || !validateCurrentStep()}
              variant="contained"
              endIcon={<CheckCircleIcon />}
              sx={{
                bgcolor: ACE_COLORS.PURPLE,
                '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
              }}
            >
              Next Step
            </Button>
          ) : (
            <Button
              onClick={showPreview ? handleSave : handlePreview}
              variant="contained"
              disabled={loading || validationState === VALIDATION_STATES.INVALID}
              startIcon={loading ? <RefreshIcon /> : showPreview ? <SaveIcon /> : <PreviewIcon />}
              sx={{
                bgcolor: ACE_COLORS.PURPLE,
                '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
              }}
            >
              {loading
                ? 'Processing...'
                : showPreview
                  ? (editingPackage ? 'Update Package' : 'Create Package')
                  : 'Preview Package'
              }
            </Button>
          )}
        </Box>
      </DialogActions>
    </Dialog>
  );
}));

// Stepper content renderer (moved outside component)
  const renderStepperContent = useCallback(() => (
    <Box sx={{ p: 3 }}>
      <Stepper
        activeStep={activeStep}
        orientation={isMobile ? 'vertical' : 'horizontal'}
        sx={{ mb: 4 }}
      >
        {packageSteps.map((step, index) => (
          <Step key={step.key} completed={completedSteps.has(step.key)}>
            <StepLabel
              icon={step.icon}
              optional={!step.required && <Typography variant="caption">Optional</Typography>}
            >
              <Typography variant="subtitle1">{step.label}</Typography>
              <Typography variant="caption" color="text.secondary">
                {step.description}
              </Typography>
            </StepLabel>
            {isMobile && (
              <StepContent>
                {renderStepContent(step.key)}
              </StepContent>
            )}
          </Step>
        ))}
      </Stepper>

      {!isMobile && renderStepContent(packageSteps[activeStep]?.key)}
    </Box>
  ), [activeStep, completedSteps, packageSteps, isMobile]);

  // Accordion content renderer
  const renderAccordionContent = useCallback(() => (
    <Box sx={{ p: 2 }}>
      {packageSteps.map((step, index) => (
        <Accordion
          key={step.key}
          expanded={expandedSections.has(step.key)}
          onChange={(event, isExpanded) => {
            setExpandedSections(prev => {
              const newSet = new Set(prev);
              if (isExpanded) {
                newSet.add(step.key);
              } else {
                newSet.delete(step.key);
              }
              return newSet;
            });
          }}
          sx={{
            ...glassMorphismStyles,
            mb: 2,
            '&:before': { display: 'none' },
            '&.Mui-expanded': {
              margin: '0 0 16px 0',
            }
          }}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            sx={{
              bgcolor: alpha(ACE_COLORS.PURPLE, 0.05),
              '&.Mui-expanded': {
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              {step.icon}
              <Box sx={{ flex: 1 }}>
                <Typography variant="h6">{step.label}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {step.description}
                </Typography>
              </Box>
              {step.required && (
                <Chip label="Required" size="small" color="primary" />
              )}
              {completedSteps.has(step.key) && (
                <CheckCircleIcon sx={{ color: 'success.main' }} />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            {renderStepContent(step.key)}
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  ), [expandedSections, packageSteps, completedSteps, glassMorphismStyles]);

  // Step content renderer
  const renderStepContent = useCallback((stepKey) => {
    switch (stepKey) {
      case PACKAGE_CREATION_STEPS.BASIC_INFO:
        return renderBasicInfoStep();
      case PACKAGE_CREATION_STEPS.PRICING:
        return renderPricingStep();
      case PACKAGE_CREATION_STEPS.TIERS:
        return renderTiersStep();
      case PACKAGE_CREATION_STEPS.FEATURES:
        return renderFeaturesStep();
      case PACKAGE_CREATION_STEPS.SCHEDULE:
        return renderScheduleStep();
      case PACKAGE_CREATION_STEPS.ADVANCED:
        return renderAdvancedStep();
      case PACKAGE_CREATION_STEPS.PREVIEW:
        return renderPackagePreview();
      default:
        return null;
    }
  }, []);

  // Validate current step
  const validateCurrentStep = useCallback(() => {
    const currentStepKey = packageSteps[activeStep]?.key;

    switch (currentStepKey) {
      case PACKAGE_CREATION_STEPS.BASIC_INFO:
        return formData.deal_id && formData.name && formData.description;
      case PACKAGE_CREATION_STEPS.PRICING:
        return formData.pricing.regular_price > 0 && formData.pricing.appsumo_price > 0;
      case PACKAGE_CREATION_STEPS.TIERS:
        return formData.tiers.length > 0;
      case PACKAGE_CREATION_STEPS.FEATURES:
        return formData.features.length > 0;
      case PACKAGE_CREATION_STEPS.SCHEDULE:
        return true; // Optional step
      case PACKAGE_CREATION_STEPS.ADVANCED:
        return true; // Optional step
      case PACKAGE_CREATION_STEPS.PREVIEW:
        return validateForm();
      default:
        return true;
    }
  }, [activeStep, packageSteps, formData, validateForm]);

  // Basic info step renderer
  const renderBasicInfoStep = useCallback(() => (
    <Card variant="outlined" sx={glassMorphismStyles}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CategoryIcon />
          Basic Information
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              label="Deal ID"
              value={formData.deal_id}
              onChange={(e) => handleFieldChange('deal_id', e.target.value)}
              error={!!errors.deal_id}
              helperText={errors.deal_id || 'Unique identifier for this deal (e.g., ACE-SOCIAL-2024)'}
              fullWidth
              required
              inputProps={{
                'aria-describedby': 'deal-id-helper',
                pattern: '[A-Z0-9_-]+',
                title: 'Use uppercase letters, numbers, underscores, and hyphens only'
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              label="Package Name"
              value={formData.name}
              onChange={(e) => handleFieldChange('name', e.target.value)}
              error={!!errors.name}
              helperText={errors.name || 'Display name for the package'}
              fullWidth
              required
              inputProps={{
                'aria-describedby': 'package-name-helper',
                maxLength: 100
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              error={!!errors.description}
              helperText={errors.description || 'Detailed description of the package benefits'}
              fullWidth
              multiline
              rows={4}
              required
              inputProps={{
                'aria-describedby': 'description-helper',
                maxLength: 500
              }}
            />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {formData.description.length}/500 characters
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  ), [formData, errors, glassMorphismStyles, handleFieldChange]);

  // Pricing step renderer
  const renderPricingStep = useCallback(() => (
    <Card variant="outlined" sx={glassMorphismStyles}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <MoneyIcon />
          Pricing Configuration
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={4}>
            <TextField
              label="Regular Price"
              type="number"
              value={formData.pricing.regular_price}
              onChange={(e) => handleFieldChange('pricing.regular_price', parseFloat(e.target.value) || 0)}
              error={!!errors['pricing.regular_price']}
              helperText={errors['pricing.regular_price'] || 'Your normal selling price'}
              InputProps={{
                startAdornment: '$',
                inputProps: { min: 0, max: 10000, step: 0.01 }
              }}
              fullWidth
              required
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              label="AppSumo Price"
              type="number"
              value={formData.pricing.appsumo_price}
              onChange={(e) => handleFieldChange('pricing.appsumo_price', parseFloat(e.target.value) || 0)}
              error={!!errors['pricing.appsumo_price']}
              helperText={errors['pricing.appsumo_price'] || 'Special price for AppSumo customers'}
              InputProps={{
                startAdornment: '$',
                inputProps: { min: 0, max: 1000, step: 0.01 }
              }}
              fullWidth
              required
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              label="Discount"
              value={`${formData.pricing.discount_percentage}%`}
              InputProps={{
                readOnly: true,
                startAdornment: <TrendingUpIcon sx={{ mr: 1, color: 'success.main' }} />
              }}
              fullWidth
              helperText="Calculated automatically"
              sx={{
                '& .MuiInputBase-input': {
                  color: 'success.main',
                  fontWeight: 'bold'
                }
              }}
            />
          </Grid>
        </Grid>

        {formData.pricing.discount_percentage > 0 && (
          <Alert
            severity="info"
            sx={{ mt: 2 }}
            icon={<CalculateIcon />}
          >
            <Typography variant="body2">
              <strong>Customer Savings:</strong> {formatCurrency(formData.pricing.regular_price - formData.pricing.appsumo_price)}
              ({formData.pricing.discount_percentage}% discount)
            </Typography>
            {formData.pricing.discount_percentage < 10 && (
              <Typography variant="caption" color="warning.main" sx={{ display: 'block', mt: 1 }}>
                Consider offering at least 10% discount for better conversion rates
              </Typography>
            )}
          </Alert>
        )}
      </CardContent>
    </Card>
  ), [formData, errors, glassMorphismStyles, handleFieldChange]);

  // Tiers step renderer
  const renderTiersStep = useCallback(() => (
    <Card variant="outlined" sx={glassMorphismStyles}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Available Tiers
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Select which tiers will be available for this package
        </Typography>

        <Grid container spacing={2} sx={{ mt: 1 }}>
          {availableTiers.map((tier) => (
            <Grid item xs={12} md={4} key={tier.value}>
              <Card
                variant={formData.tiers.includes(tier.value) ? "outlined" : "elevation"}
                sx={{
                  cursor: 'pointer',
                  border: formData.tiers.includes(tier.value) ? `2px solid ${ACE_COLORS.PURPLE}` : `1px solid ${theme.palette.divider}`,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: theme.shadows[4]
                  },
                  bgcolor: formData.tiers.includes(tier.value)
                    ? alpha(ACE_COLORS.PURPLE, 0.05)
                    : 'background.paper'
                }}
                onClick={() => handleTierToggle(tier.value)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleTierToggle(tier.value);
                  }
                }}
                aria-pressed={formData.tiers.includes(tier.value)}
                aria-label={`${tier.label} tier ${formData.tiers.includes(tier.value) ? 'selected' : 'not selected'}`}
              >
                <CardContent sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Typography variant="subtitle1" fontWeight="bold">
                      {tier.label}
                    </Typography>
                    {tier.recommended && (
                      <Chip label="Recommended" size="small" color="primary" />
                    )}
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {tier.description}
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    {tier.features.map((feature, index) => (
                      <Typography key={index} variant="caption" sx={{ display: 'block', mb: 0.5 }}>
                        • {feature}
                      </Typography>
                    ))}
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Chip
                      label={`${tier.config?.maxUsers || 1} Users`}
                      size="small"
                      color={formData.tiers.includes(tier.value) ? 'primary' : 'default'}
                    />
                    {formData.tiers.includes(tier.value) && (
                      <CheckCircleIcon sx={{ color: ACE_COLORS.PURPLE }} />
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {errors.tiers && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {errors.tiers}
          </Alert>
        )}

        {warnings.tiers && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            {warnings.tiers}
          </Alert>
        )}
      </CardContent>
    </Card>
  ), [formData.tiers, availableTiers, errors.tiers, warnings.tiers, glassMorphismStyles, handleTierToggle, theme]);

  // Features step renderer
  const renderFeaturesStep = useCallback(() => (
    <Card variant="outlined" sx={glassMorphismStyles}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Package Features
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Add features that highlight the value of your package
        </Typography>

        <Box display="flex" gap={1} mb={3} mt={2}>
          <TextField
            label="Add Feature"
            value={newFeature}
            onChange={(e) => setNewFeature(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleAddFeature();
              }
            }}
            fullWidth
            size="small"
            inputProps={{
              maxLength: 100,
              'aria-describedby': 'feature-input-helper'
            }}
            helperText={`${newFeature.length}/100 characters`}
          />
          <Button
            variant="outlined"
            onClick={handleAddFeature}
            disabled={!newFeature.trim() || formData.features.length >= maxPackageFeatures}
            startIcon={<AddIcon />}
            sx={{ minWidth: 120 }}
          >
            Add
          </Button>
        </Box>

        {formData.features.length > 0 ? (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Current Features ({formData.features.length}/{maxPackageFeatures})
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={1}>
              {formData.features.map((feature, index) => (
                <Chip
                  key={index}
                  label={feature}
                  onDelete={() => handleRemoveFeature(index)}
                  deleteIcon={<RemoveIcon />}
                  variant="outlined"
                  sx={{
                    '& .MuiChip-deleteIcon': {
                      color: 'error.main',
                      '&:hover': {
                        color: 'error.dark'
                      }
                    }
                  }}
                />
              ))}
            </Box>
          </Box>
        ) : (
          <Alert severity="info">
            No features added yet. Add at least one feature to highlight your package value.
          </Alert>
        )}

        {errors.features && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {errors.features}
          </Alert>
        )}

        {warnings.features && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            {warnings.features}
          </Alert>
        )}
      </CardContent>
    </Card>
  ), [
    formData.features,
    newFeature,
    maxPackageFeatures,
    errors.features,
    warnings.features,
    glassMorphismStyles,
    handleAddFeature,
    handleRemoveFeature
  ]);

  // Schedule step renderer
  const renderScheduleStep = useCallback(() => (
    <Card variant="outlined" sx={glassMorphismStyles}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ScheduleIcon />
          Schedule Configuration
        </Typography>

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <DateTimePicker
              label="Start Date"
              value={formData.start_date}
              onChange={(date) => handleFieldChange('start_date', date)}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <DateTimePicker
              label="End Date (Optional)"
              value={formData.end_date}
              onChange={(date) => handleFieldChange('end_date', date)}
              slotProps={{
                textField: {
                  fullWidth: true,
                },
              }}
            />
          </Grid>
        </Grid>

        {errors.end_date && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {errors.end_date}
          </Alert>
        )}
      </CardContent>
    </Card>
  ), [formData.start_date, formData.end_date, errors.end_date, glassMorphismStyles, handleFieldChange]);

  // Advanced step renderer
  const renderAdvancedStep = useCallback(() => (
    <Card variant="outlined" sx={glassMorphismStyles}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Advanced Settings
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              label="Max Stacking"
              type="number"
              value={formData.metadata.max_stacking}
              onChange={(e) => handleMetadataChange('max_stacking', parseInt(e.target.value) || 1)}
              helperText="Maximum number of codes a user can stack"
              fullWidth
              inputProps={{ min: 1, max: 10 }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box mt={2}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.metadata.allow_upgrades}
                    onChange={(e) => handleMetadataChange('allow_upgrades', e.target.checked)}
                  />
                }
                label="Allow Tier Upgrades"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.metadata.lifetime_access}
                    onChange={(e) => handleMetadataChange('lifetime_access', e.target.checked)}
                  />
                }
                label="Lifetime Access"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_active}
                    onChange={(e) => handleFieldChange('is_active', e.target.checked)}
                  />
                }
                label="Active Package"
              />
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  ), [formData, glassMorphismStyles, handleFieldChange, handleMetadataChange]);

// Enhanced PropTypes validation with comprehensive type checking
EnhancedPackageCreator.propTypes = {
  // Core props
  open: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
  onPackageCreated: PropTypes.func,
  editingPackage: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    deal_id: PropTypes.string,
    name: PropTypes.string,
    description: PropTypes.string,
    pricing: PropTypes.shape({
      regular_price: PropTypes.number,
      appsumo_price: PropTypes.number,
      discount_percentage: PropTypes.number
    }),
    tiers: PropTypes.arrayOf(PropTypes.string),
    start_date: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
    end_date: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
    features: PropTypes.arrayOf(PropTypes.string),
    terms_and_conditions: PropTypes.string,
    redemption_url_pattern: PropTypes.string,
    metadata: PropTypes.object,
    is_active: PropTypes.bool,
    created_at: PropTypes.string,
    updated_at: PropTypes.string
  }),

  // Enhanced props
  deals: PropTypes.arrayOf(PropTypes.object),
  tiers: PropTypes.arrayOf(PropTypes.object),
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeValidation: PropTypes.bool,
  enableStepperNavigation: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  maxPackageFeatures: PropTypes.number,

  // Callback props
  onPackagePreview: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onValidationChange: PropTypes.func,
  onStepChange: PropTypes.func,

  // Standard props
  className: PropTypes.string
};

// Default props
EnhancedPackageCreator.defaultProps = {
  open: false,
  editingPackage: null,
  deals: [],
  tiers: [],
  enableAdvancedFeatures: true,
  enableRealTimeValidation: true,
  enableStepperNavigation: true,
  enableAccessibility: true,
  enableAnalytics: true,
  maxPackageFeatures: 50,
  onPackageCreated: null,
  onPackagePreview: null,
  onAnalyticsTrack: null,
  onValidationChange: null,
  onStepChange: null,
  className: ''
};

// Display name for debugging
EnhancedPackageCreator.displayName = 'EnhancedPackageCreator';

export default EnhancedPackageCreator;

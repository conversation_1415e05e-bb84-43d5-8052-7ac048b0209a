// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  useTheme,
  useMediaQuery,
  Button,
  Alert,
  Container,
} from "@mui/material";
import { Error as ErrorIcon, Refresh as RefreshIcon } from "@mui/icons-material";
import { alpha } from "@mui/material/styles";
import { useNavigate, useLocation, Link as RouterLink } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import PaymentIcon from "@mui/icons-material/Payment";
import ListIcon from "@mui/icons-material/List";
import CardGiftcardIcon from "@mui/icons-material/CardGiftcard";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import BarChartIcon from "@mui/icons-material/BarChart";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";

// Import the existing components
import Plans from "./Plans";
import BillingHistory from "./BillingHistory";
import AddonsMarketplace from "./AddonsMarketplace";
import UsageDashboard from "./UsageDashboard";
import AppSumoRedemption from "../../components/billing/AppSumoRedemption";
import { useAuth } from "../../contexts/AuthContext";
import { useAdvancedToast } from "../../hooks/useAdvancedToast";
import { AppSumoBadge } from "../../components/appsumo";

/**
 * Production-Ready UnifiedBillingPage with comprehensive error handling
 * Combines Plans, Add-ons, Usage, and Billing History with shopping cart functionality
 */
const UnifiedBillingPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { showError } = useAdvancedToast();

  // Component loading states
  const [componentError, setComponentError] = useState(null);


  // Get the tab from URL query params or default to 'plans'
  const query = new URLSearchParams(location.search);
  const defaultTab = query.get("view") || "plans";

  const [activeTab, setActiveTab] = useState(defaultTab);

  // Check if user has an AppSumo subscription
  const isAppSumoUser = user?.subscription?.is_appsumo_lifetime || false;

  // Update URL when tab changes without full page reload
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.set("view", activeTab);
    navigate(
      {
        pathname: location.pathname,
        search: searchParams.toString(),
      },
      { replace: true }
    );
  }, [activeTab, navigate, location.pathname, location.search]);

  const handleTabChange = (event, newValue) => {
    try {
      setActiveTab(newValue);
      setComponentError(null); // Clear any previous errors when switching tabs
    } catch (error) {
      console.error("Error changing tab:", error);
      showError("Failed to switch tabs. Please try again.");
    }
  };

  // Error recovery functions
  const handleRetry = () => {
    setComponentError(null);
    window.location.reload();
  };

  const handleGoToDashboard = () => {
    navigate("/dashboard");
  };



  // Error fallback component
  const ErrorFallback = ({ error, componentName, onRetry }) => (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper sx={{ p: 4, textAlign: "center" }}>
        <ErrorIcon sx={{ fontSize: 60, color: "error.main", mb: 2 }} />
        <Typography variant="h5" gutterBottom>
          Unable to Load {componentName}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          We&apos;re experiencing technical difficulties loading this section. This
          might be due to a temporary network issue or a component loading
          problem.
        </Typography>

        <Alert severity="error" sx={{ mb: 3, textAlign: "left" }}>
          <Typography variant="body2">
            <strong>Error Details:</strong>{" "}
            {error?.message || "Unknown error occurred"}
          </Typography>
        </Alert>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "center",
            flexWrap: "wrap",
          }}
        >
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={onRetry}
          >
            Retry Loading
          </Button>
          <Button variant="outlined" onClick={handleGoToDashboard}>
            Go to Dashboard
          </Button>
        </Box>

        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ display: "block", mt: 2 }}
        >
          If this problem persists, please contact support or try again later.
        </Typography>
      </Paper>
    </Container>
  );

  // Show error fallback if there's a component error
  if (componentError) {
    return (
      <ErrorFallback
        error={componentError.error}
        componentName={componentError.componentName}
        onRetry={handleRetry}
      />
    );
  }

  return (
    <>
      <Helmet>
        <title>Billing</title>
      </Helmet>

      <Box sx={{ width: "100%", p: { xs: 2, md: 3 } }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Billing & Subscription
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your subscription plan, add-ons, usage tracking, and billing
            history
          </Typography>

          {/* AppSumo Banner - Enhanced */}
          {isAppSumoUser && (
            <Box sx={{ mt: 2 }}>
              <Alert
                severity="info"
                icon={<CardGiftcardIcon sx={{ color: "#FF8C00" }} />}
                sx={{
                  borderLeft: "4px solid #FF8C00",
                  display: "flex",
                  alignItems: "center",
                }}
                action={
                  <Button
                    component={RouterLink}
                    to="/appsumo/upgrade-options"
                    color="inherit"
                    size="small"
                    endIcon={<ArrowForwardIcon />}
                    sx={{
                      color: "#FF8C00",
                      "&:hover": {
                        backgroundColor: alpha("#FF8C00", 0.1),
                      },
                    }}
                  >
                    View Options
                  </Button>
                }
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography variant="body2" sx={{ mr: 1 }}>
                    🎉 You have an active AppSumo lifetime subscription
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      backgroundColor: "#FF8C00",
                      color: "white",
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                      fontWeight: "bold",
                    }}
                  >
                    LIFETIME
                  </Typography>
                  <AppSumoBadge
                    variant="chip"
                    size="small"
                    tierName={
                      user?.subscription?.plan_name?.replace(" Plan", "") || ""
                    }
                  />
                </Box>
              </Alert>
            </Box>
          )}
        </Box>

        <Paper sx={{ mb: 3, overflow: "hidden" }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant={isMobile ? "scrollable" : "standard"}
            scrollButtons={isMobile ? "auto" : false}
            aria-label="billing and subscription tabs"
            sx={{
              "& .MuiTab-root": {
                minHeight: 64,
                textTransform: "none",
                fontSize: "0.95rem",
                fontWeight: 500,
              },
            }}
          >
            <Tab
              icon={<PaymentIcon />}
              iconPosition="start"
              label="Subscription Plans"
              value="plans"
              aria-label="View and manage subscription plans"
            />
            <Tab
              icon={<AddCircleOutlineIcon />}
              iconPosition="start"
              label="Add-Ons & Marketplace"
              value="addons"
              aria-label="Browse and purchase add-ons"
            />
            <Tab
              icon={<BarChartIcon />}
              iconPosition="start"
              label="Usage Dashboard"
              value="usage"
              aria-label="View usage statistics and limits"
            />
            <Tab
              icon={<ListIcon />}
              iconPosition="start"
              label="Billing History"
              value="history"
              aria-label="View billing history and invoices"
            />
            <Tab
              icon={<LocalOfferIcon />}
              iconPosition="start"
              label="AppSumo Codes"
              value="appsumo"
              aria-label="Redeem AppSumo codes"
            />
          </Tabs>
        </Paper>

        {/* Render the active tab content with error boundaries */}
        <Box sx={{ mt: 2 }}>
          {activeTab === "plans" && <Plans isEmbedded={true} />}

          {activeTab === "addons" && <AddonsMarketplace isEmbedded={true} />}

          {activeTab === "usage" && <UsageDashboard isEmbedded={true} />}

          {activeTab === "history" && <BillingHistory isEmbedded={true} />}

          {activeTab === "appsumo" && (
            <AppSumoRedemption
              onRedemptionSuccess={(data) => {
                // Handle successful redemption
                console.log("AppSumo redemption successful:", data);
                // Optionally switch to plans tab to show the new subscription
                setActiveTab("plans");
              }}
            />
          )}
        </Box>
      </Box>
    </>
  );
};

export default UnifiedBillingPage;

/* Add-on Feature Gate Styles */

.addon-feature-gate {
  position: relative;
  width: 100%;
}

.addon-feature-gate.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
}

.loading-content {
  color: #8c8c8c;
  font-size: 14px;
}

/* Blocked State Styles */
.addon-feature-gate.blocked {
  background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.addon-feature-gate.blocked::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff4d4f, #faad14, #52c41a);
  opacity: 0.3;
}

.blocked-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.blocked-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #ff7875, #ff4d4f);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

.blocked-message h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.blocked-message p {
  margin: 0 0 16px 0;
  color: #595959;
  font-size: 14px;
  line-height: 1.5;
}

.upgrade-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.fallback-content {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* Accessible State Styles */
.addon-feature-gate.accessible {
  position: relative;
}

.feature-content {
  width: 100%;
}

/* Usage Indicator Styles */
.addon-usage-indicator {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.usage-label {
  font-size: 13px;
  font-weight: 500;
  color: #595959;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.usage-count {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
}

.usage-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.remaining-text {
  font-size: 12px;
  color: #8c8c8c;
}

/* Upgrade Modal Styles */
.addon-upgrade-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
}

.addon-upgrade-modal .ant-modal-header {
  background: linear-gradient(135deg, #722ed1, #9254de);
  border-bottom: none;
}

.addon-upgrade-modal .ant-modal-title {
  color: white;
  font-weight: 600;
}

.addon-upgrade-modal .ant-modal-close {
  color: white;
}

.upgrade-modal-content {
  padding: 0;
}

.current-status {
  padding: 20px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.current-status h4 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.addon-recommendations {
  padding: 20px;
}

.addon-recommendations h4 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.addon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.addon-card {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.addon-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #722ed1, #9254de);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.addon-card:hover {
  border-color: #722ed1;
  box-shadow: 0 4px 12px rgba(114, 46, 209, 0.15);
}

.addon-card:hover::before {
  transform: scaleX(1);
}

.addon-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.addon-header h5 {
  margin: 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.addon-description {
  color: #595959;
  font-size: 13px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.addon-features {
  margin-bottom: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #595959;
  margin-bottom: 4px;
}

.feature-item .anticon {
  color: #722ed1;
  font-size: 10px;
}

.addon-pricing {
  margin-bottom: 16px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 6px;
  text-align: center;
}

.price-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 18px;
  font-weight: 700;
  color: #722ed1;
}

.credits {
  font-size: 12px;
  color: #595959;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.plan-upgrade-option {
  padding: 20px;
  background: #f6ffed;
  border-top: 1px solid #f0f0f0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .addon-feature-gate.blocked {
    padding: 16px;
  }
  
  .blocked-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
  
  .upgrade-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .addon-grid {
    grid-template-columns: 1fr;
  }
  
  .usage-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .usage-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* Animation Classes */
.addon-feature-gate {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.addon-card {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Status Indicators */
.addon-usage-indicator .ant-progress-line {
  margin-bottom: 0;
}

.addon-usage-indicator .ant-progress-bg {
  border-radius: 4px;
}

/* Badge Customizations */
.addon-header .ant-badge {
  font-size: 10px;
}

.usage-count .ant-badge {
  margin-left: 8px;
}

.usage-count .ant-badge-count {
  font-size: 10px;
  height: 18px;
  line-height: 18px;
  min-width: 18px;
  padding: 0 4px;
}

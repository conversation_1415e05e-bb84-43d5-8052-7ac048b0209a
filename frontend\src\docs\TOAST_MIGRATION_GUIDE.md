<!-- @since 2024-1-1 to 2025-25-7 -->
# Toast Notification System Migration Guide

This guide will help you migrate from the existing notification system to the new production-ready toast notification system.

## Overview

The new toast notification system provides:
- ✅ Production-ready reliability with comprehensive error handling
- ✅ Advanced queuing and stacking with collision detection
- ✅ Full accessibility support (WCAG 2.1 AA compliant)
- ✅ Modern responsive design with smooth animations
- ✅ TypeScript support with comprehensive type definitions
- ✅ Network failure handling with automatic retries
- ✅ Rate limiting and deduplication
- ✅ Persistence across page refreshes
- ✅ Comprehensive test coverage

## Migration Steps

### 1. Update Imports

**Before:**
```javascript
import { useNotification } from '../hooks/useNotification';
import Notification from '../components/common/Notification';
```

**After:**
```javascript
import { useAdvancedToast } from '../hooks/useAdvancedToast';
import EnhancedToastProvider from '../components/toast/EnhancedToastProvider';
```

### 2. Update Provider Setup

**Before:**
```jsx
<NotificationProvider>
  <App />
</NotificationProvider>
```

**After:**
```jsx
<EnhancedToastProvider
  position="bottom-right"
  maxVisible={5}
  enableStacking={true}
  enableKeyboardNavigation={true}
>
  <App />
</EnhancedToastProvider>
```

### 3. Update Hook Usage

**Before:**
```javascript
const { showSuccessNotification, showErrorNotification } = useNotification();

showSuccessNotification('Operation completed');
showErrorNotification('Something went wrong');
```

**After:**
```javascript
const { showSuccess, showError, showProgress } = useAdvancedToast();

// Basic usage
showSuccess('Operation completed');
showError('Something went wrong');

// Advanced usage with options
showSuccess('Operation completed', {
  title: 'Success',
  duration: 5000,
  actions: [
    { label: 'View Details', onClick: () => navigate('/details') }
  ]
});

// Progress toast for async operations
showProgress(
  () => api.saveData(data),
  {
    loadingMessage: 'Saving data...',
    successMessage: 'Data saved successfully',
    errorMessage: 'Failed to save data'
  }
);
```

### 4. Network Operations

**Before:**
```javascript
try {
  await api.saveData(data);
  showSuccessNotification('Data saved');
} catch (error) {
  showErrorNotification('Failed to save data');
}
```

**After:**
```javascript
// Automatic network error handling with retries
showNetworkToast(
  () => api.saveData(data),
  {
    type: 'success',
    message: 'Data saved successfully'
  }
);
```

### 5. Advanced Features

#### Persistent Notifications
```javascript
showError('Critical system error', {
  persistent: true,
  priority: 'critical',
  actions: [
    { label: 'Retry', onClick: retryOperation },
    { label: 'Report', onClick: reportIssue }
  ]
});
```

#### Undo Functionality
```javascript
showSuccess('Item deleted', {
  onUndo: () => restoreItem(itemId),
  duration: 10000
});
```

#### Batch Notifications
```javascript
const notifications = [
  { type: 'success', message: 'File 1 uploaded' },
  { type: 'success', message: 'File 2 uploaded' },
  { type: 'warning', message: 'File 3 skipped' }
];

showBatch(notifications, 500); // 500ms delay between each
```

## Configuration Options

### Provider Options
```javascript
<EnhancedToastProvider
  position="bottom-right"           // Toast position
  maxVisible={5}                    // Max visible toasts
  stackSpacing={8}                  // Spacing between stacked toasts
  enableStacking={true}             // Enable toast stacking
  enableKeyboardNavigation={true}   // Enable keyboard navigation
  options={{
    maxQueue: 20,                   // Max queued toasts
    animationDuration: 300,         // Animation duration
    enablePersistence: false,       // Enable persistence
    persistenceKey: 'app-toasts'    // LocalStorage key
  }}
/>
```

### Hook Options
```javascript
const toast = useAdvancedToast({
  enableRateLimiting: true,         // Prevent spam
  enableDeduplication: true,        // Prevent duplicates
  enableNetworkRetry: true,         // Auto-retry network failures
  maxConcurrentOperations: 5,       // Max concurrent operations
  rateLimitWindow: 5000,            // Rate limit window (ms)
  rateLimitMax: 3,                  // Max toasts per window
  dedupeWindow: 3000,               // Deduplication window (ms)
  networkRetryConfig: {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true
  }
});
```

## Breaking Changes

### 1. Method Names
- `showSuccessNotification` → `showSuccess`
- `showErrorNotification` → `showError`
- `showWarningNotification` → `showWarning`
- `showInfoNotification` → `showInfo`
- `clearNotification` → `dismissToast` or `dismissAll`

### 2. Configuration Structure
The new system uses a more structured configuration approach:

**Before:**
```javascript
showNotification('Message', 'success', 6000);
```

**After:**
```javascript
showToast({
  type: 'success',
  message: 'Message',
  duration: 6000
});
```

### 3. Event Handlers
Action callbacks now receive more context:

**Before:**
```javascript
showNotification('Message', 'info', 6000, () => handleAction());
```

**After:**
```javascript
showInfo('Message', {
  actions: [
    {
      label: 'Action',
      onClick: () => handleAction(),
      variant: 'contained',
      color: 'primary'
    }
  ]
});
```

## Accessibility Improvements

The new system includes comprehensive accessibility features:

### Screen Reader Support
- Automatic announcements to screen readers
- Proper ARIA live regions (`polite` for info, `assertive` for errors)
- Descriptive labels and roles

### Keyboard Navigation
- Full keyboard navigation support
- `Escape` to dismiss toasts
- `Enter` to trigger primary action
- `Ctrl+U` to trigger undo
- Arrow keys to navigate between toasts

### Visual Accessibility
- High contrast mode support
- Reduced motion support
- Sufficient color contrast ratios
- Focus indicators

## Performance Considerations

### Rate Limiting
The new system automatically prevents notification spam:
```javascript
// These will be rate-limited
for (let i = 0; i < 10; i++) {
  showSuccess('Spam message'); // Only first 3 will show
}
```

### Memory Management
- Automatic cleanup of dismissed toasts
- Queue size limits to prevent memory leaks
- Efficient re-rendering with React optimizations

### Network Optimization
- Automatic retry with exponential backoff
- Concurrent operation limits
- Network status awareness

## Testing

The new system includes comprehensive test utilities:

```javascript
import { renderWithToast, mockToastProvider } from '../tests/toast/testUtils';

test('should show success toast', async () => {
  const { showSuccess } = renderWithToast(<MyComponent />);
  
  fireEvent.click(screen.getByText('Save'));
  
  await waitFor(() => {
    expect(screen.getByText('Saved successfully')).toBeInTheDocument();
  });
});
```

## Troubleshooting

### Common Issues

1. **Toasts not appearing**
   - Ensure `EnhancedToastProvider` wraps your app
   - Check console for rate limiting warnings
   - Verify toast position doesn't conflict with other UI elements

2. **Accessibility warnings**
   - Ensure proper ARIA labels are set
   - Check color contrast ratios
   - Verify keyboard navigation works

3. **Performance issues**
   - Reduce `maxVisible` if showing too many toasts
   - Enable rate limiting to prevent spam
   - Check for memory leaks in long-running apps

### Debug Mode
Enable debug logging:
```javascript
const toast = useAdvancedToast({
  debug: true // Logs all toast operations
});
```

## Support

For questions or issues with the migration:
1. Check the comprehensive test suite for usage examples
2. Review the TypeScript definitions for available options
3. Consult the accessibility documentation for WCAG compliance
4. Use the debug mode to troubleshoot issues

The new toast system is designed to be backward-compatible where possible, but provides significant improvements in reliability, accessibility, and user experience.

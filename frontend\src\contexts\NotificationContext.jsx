/**
 * Notification Context
 * Production-ready notification system with queuing, persistence, and advanced features
 * Comprehensive error handling, accessibility, and performance optimization
 * Updated: Fixed NotificationProvider export for Vercel deployment - v2
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useState, useContext, useCallback, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';

// Configuration constants
const CONFIG = {
  // Default durations by type
  DEFAULT_DURATIONS: {
    success: 4000,
    info: 6000,
    warning: 8000,
    error: 10000,
  },

  // Queue settings
  MAX_NOTIFICATIONS: 5,
  MAX_QUEUE_SIZE: 20,

  // Auto-dismiss settings
  AUTO_DISMISS_DELAY: 100, // Delay before starting auto-dismiss timer

  // Persistence settings
  ENABLE_PERSISTENCE: false,
  PERSISTENCE_KEY: 'aceo-notifications',

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[NotificationContext] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[NotificationContext] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Notification Context Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[NotificationContext] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Notification Context Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[NotificationContext] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Notification Context Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const NotificationContext = createContext();

// Custom hook to use notification context
// eslint-disable-next-line react-refresh/only-export-components
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

const NotificationProvider = ({ children }) => {
  // Enhanced state management
  const [notifications, setNotifications] = useState([]);
  const [queue, setQueue] = useState([]);
  const [isPaused, setIsPaused] = useState(false);
  const timeoutRefs = useRef(new Map());
  const queueProcessorRef = useRef(null);

  // Process notification queue
  const processQueue = useCallback(() => {
    if (queue.length > 0 && !isPaused) {
      const visibleNotifications = notifications.filter(n => n.isVisible);
      const availableSlots = CONFIG.MAX_NOTIFICATIONS - visibleNotifications.length;

      if (availableSlots > 0) {
        const nextNotification = queue[0];
        if (nextNotification) {
          setQueue(prev => prev.slice(1));
          setNotifications(prev => [...prev, { ...nextNotification, isVisible: true }]);

          // Set up auto-dismiss timer
          if (nextNotification.duration > 0) {
            const timeoutId = setTimeout(() => {
              dismissNotification(nextNotification.id);
            }, nextNotification.duration + CONFIG.AUTO_DISMISS_DELAY);

            timeoutRefs.current.set(nextNotification.id, timeoutId);
          }
        }
      }
    }
  }, [queue, notifications, isPaused, dismissNotification]);

  // Process queue when notifications change
  useEffect(() => {
    if (queueProcessorRef.current) {
      clearTimeout(queueProcessorRef.current);
    }

    queueProcessorRef.current = setTimeout(() => {
      processQueue();
    }, 50); // Small delay to batch updates

    return () => {
      if (queueProcessorRef.current) {
        clearTimeout(queueProcessorRef.current);
      }
    };
  }, [processQueue]);

  // Cleanup timers on unmount
  useEffect(() => {
    const timeoutRefsSnapshot = timeoutRefs.current;
    const queueProcessorSnapshot = queueProcessorRef.current;

    return () => {
      timeoutRefsSnapshot.forEach(timeoutId => clearTimeout(timeoutId));
      timeoutRefsSnapshot.clear();
      if (queueProcessorSnapshot) {
        clearTimeout(queueProcessorSnapshot);
      }
    };
  }, []);

  // Enhanced show notification function
  const showNotification = useCallback((message, type = 'info', options = {}) => {
    try {
      if (!message || typeof message !== 'string') {
        logger.warn('Invalid notification message provided', { message, type });
        return null;
      }

      const notification = {
        id: uuidv4(),
        message: message.trim(),
        type,
        duration: options.duration ?? CONFIG.DEFAULT_DURATIONS[type] ?? CONFIG.DEFAULT_DURATIONS.info,
        timestamp: new Date(),
        isVisible: false,
        isPersistent: options.persistent || false,
        actions: options.actions || [],
        icon: options.icon,
        title: options.title,
        priority: options.priority || 'normal', // low, normal, high, urgent
        category: options.category || 'general',
        metadata: options.metadata || {}
      };

      logger.debug('Creating notification', {
        id: notification.id,
        type,
        message: message.substring(0, 50) + (message.length > 50 ? '...' : '')
      });

      // Check if we can show immediately or need to queue
      const visibleNotifications = notifications.filter(n => n.isVisible);

      if (visibleNotifications.length < CONFIG.MAX_NOTIFICATIONS && !isPaused) {
        // Show immediately
        setNotifications(prev => [...prev, { ...notification, isVisible: true }]);

        // Set up auto-dismiss timer
        if (notification.duration > 0) {
          const timeoutId = setTimeout(() => {
            dismissNotification(notification.id);
          }, notification.duration + CONFIG.AUTO_DISMISS_DELAY);

          timeoutRefs.current.set(notification.id, timeoutId);
        }
      } else {
        // Add to queue
        if (queue.length < CONFIG.MAX_QUEUE_SIZE) {
          setQueue(prev => [...prev, notification]);
          logger.debug('Notification queued', { id: notification.id, queueLength: queue.length + 1 });
        } else {
          logger.warn('Notification queue is full, dropping notification', {
            id: notification.id,
            queueSize: CONFIG.MAX_QUEUE_SIZE
          });
        }
      }

      return notification.id;
    } catch (error) {
      logger.error('Failed to show notification', error);
      return null;
    }
  }, [notifications, queue, isPaused, dismissNotification]);

  // Dismiss notification
  const dismissNotification = useCallback((id) => {
    if (!id) {
      logger.warn('Cannot dismiss notification - no ID provided');
      return;
    }

    logger.debug('Dismissing notification', { id });

    // Clear timeout if exists
    if (timeoutRefs.current.has(id)) {
      clearTimeout(timeoutRefs.current.get(id));
      timeoutRefs.current.delete(id);
    }

    // Remove from notifications
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    logger.debug('Clearing all notifications');

    // Clear all timeouts
    timeoutRefs.current.forEach(timeoutId => clearTimeout(timeoutId));
    timeoutRefs.current.clear();

    // Clear notifications and queue
    setNotifications([]);
    setQueue([]);
  }, []);

  // Pause/resume notification system
  const pauseNotifications = useCallback(() => {
    logger.debug('Pausing notifications');
    setIsPaused(true);
  }, []);

  const resumeNotifications = useCallback(() => {
    logger.debug('Resuming notifications');
    setIsPaused(false);
  }, []);

  // Convenience methods for different notification types
  const showSuccessNotification = useCallback((message, options = {}) => {
    return showNotification(message, 'success', options);
  }, [showNotification]);

  const showErrorNotification = useCallback((message, options = {}) => {
    return showNotification(message, 'error', options);
  }, [showNotification]);

  const showWarningNotification = useCallback((message, options = {}) => {
    return showNotification(message, 'warning', options);
  }, [showNotification]);

  const showInfoNotification = useCallback((message, options = {}) => {
    return showNotification(message, 'info', options);
  }, [showNotification]);

  // Legacy support - clear single notification (for backward compatibility)
  const clearNotification = useCallback(() => {
    if (notifications.length > 0) {
      dismissNotification(notifications[0].id);
    }
  }, [notifications, dismissNotification]);

  // Enhanced context value with organized structure
  const contextValue = {
    // State data
    notifications,
    queue,
    isPaused,

    // Legacy support (for backward compatibility)
    notification: notifications[0] || null,

    // Core functions
    showNotification,
    dismissNotification,
    clearNotification, // Legacy support
    clearAllNotifications,

    // Convenience methods
    showSuccessNotification,
    showErrorNotification,
    showWarningNotification,
    showInfoNotification,

    // Queue management
    pauseNotifications,
    resumeNotifications,

    // Utility functions
    getNotificationCount: () => notifications.length,
    getQueueCount: () => queue.length,
    hasNotifications: notifications.length > 0,
    hasQueuedNotifications: queue.length > 0,

    // Helper functions
    getNotificationById: (id) => notifications.find(n => n.id === id),
    getNotificationsByType: (type) => notifications.filter(n => n.type === type),
    getNotificationsByCategory: (category) => notifications.filter(n => n.category === category),

    // Bulk operations
    dismissAllByType: (type) => {
      const typeNotifications = notifications.filter(n => n.type === type);
      typeNotifications.forEach(n => dismissNotification(n.id));
      logger.info('Dismissed all notifications by type', { type, count: typeNotifications.length });
    },

    dismissAllByCategory: (category) => {
      const categoryNotifications = notifications.filter(n => n.category === category);
      categoryNotifications.forEach(n => dismissNotification(n.id));
      logger.info('Dismissed all notifications by category', { category, count: categoryNotifications.length });
    }
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

// Export the context and provider
export { NotificationContext, NotificationProvider };

// Default export for convenience
export default NotificationProvider;

/**
 * Enhanced ACE Social Coupon Creator - Enterprise-grade coupon creation component
 * Features: Comprehensive coupon creation with advanced form validation, real-time preview,
 * and discount calculation capabilities for ACE Social promotional campaigns, detailed
 * coupon configuration with usage limits and expiration settings, advanced creation features
 * with template management and bulk creation options, ACE Social's coupon system integration
 * with seamless coupon lifecycle management, creation interaction features including real-time
 * preview and form auto-save, creation state management with form persistence and validation
 * caching, and real-time creation monitoring with live validation displays and automatic
 * creation optimization
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  <PERSON>lt<PERSON>,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Grid,
  Card,
  CardContent,
  Divider,
  InputAdornment,
  Paper,
  Stack,
  Collapse,
  CircularProgress,
  Badge,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Info as InfoIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  Category as CategoryIcon,
  Security as SecurityIcon,
  Preview as PreviewIcon,
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Settings as SettingsIcon,
  LocalOffer as CouponIcon,
  Campaign as CampaignIcon,
  Analytics as AnalyticsIcon,
  Speed as SpeedIcon,
  Verified as VerifiedIcon,
  AutoAwesome as AutoAwesomeIcon,
  ContentCopy as CopyIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import {
  validateFormData,
  formatCurrency,
  generateCouponCode,
  validateCouponCode,
  formatDiscount,
  calculateDiscountAmount,
} from '../../utils/couponHelpers';
import api from '../../api';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Coupon creation constants
const CREATION_MODES = {
  CREATE: 'create',
  EDIT: 'edit',
  DUPLICATE: 'duplicate',
  TEMPLATE: 'template'
};

const VALIDATION_LEVELS = {
  BASIC: 'basic',
  ADVANCED: 'advanced',
  STRICT: 'strict'
};

const AUTO_SAVE_INTERVALS = {
  DISABLED: 0,
  FAST: 30000,    // 30 seconds
  NORMAL: 60000,  // 1 minute
  SLOW: 300000    // 5 minutes
};

// Creation analytics events
const CREATION_ANALYTICS_EVENTS = {
  CREATION_STARTED: 'coupon_creation_started',
  STEP_COMPLETED: 'creation_step_completed',
  VALIDATION_FAILED: 'creation_validation_failed',
  PREVIEW_VIEWED: 'creation_preview_viewed',
  TEMPLATE_USED: 'creation_template_used',
  AUTO_SAVE_TRIGGERED: 'creation_auto_save_triggered',
  CREATION_COMPLETED: 'coupon_creation_completed'
};

/**
 * Enhanced Coupon Creator - Comprehensive coupon creation with advanced features
 * Implements detailed coupon creation management and enterprise-grade creation capabilities
 */
const EnhancedCouponCreator = memo(forwardRef(({
  open,
  onClose,
  onCouponCreated,
  editingCoupon = null,
  className,
  mode = CREATION_MODES.CREATE,
  enableAdvancedFeatures = true,
  enableRealTimeValidation = true,
  enableAutoSave = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableTemplates = true,
  validationLevel = VALIDATION_LEVELS.ADVANCED,
  autoSaveInterval = AUTO_SAVE_INTERVALS.NORMAL,
  maxSteps = 4,
  onStepChange,
  onValidationResult,
  onAutoSave,
  onAnalyticsTrack,
  onTemplateSelect,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const dialogRef = useRef(null);
  const formRef = useRef(null);
  const stepperRef = useRef(null);
  const autoSaveTimeoutRef = useRef(null);

  // Core state management
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    discount_type: 'percentage',
    discount_value: 0,
    applicable_to: 'all',
    specific_items: [],
    minimum_purchase_amount: 0,
    max_discount_amount: null,
    max_redemptions: null,
    max_redemptions_per_user: 1,
    start_date: new Date(),
    end_date: null,
    first_time_users_only: false,
    requires_subscription: false,
    is_active: true,
  });
  const [errors, setErrors] = useState({});
  const [warnings, setWarnings] = useState({});
  const [loading, setLoading] = useState(false);
  const [newItem, setNewItem] = useState('');

  // Enhanced state management
  const [creationMode, setCreationMode] = useState(mode);
  const [validationResults, setValidationResults] = useState({});
  const [showPreview, setShowPreview] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(enableAutoSave);
  const [lastSaved, setLastSaved] = useState(null);
  const [isDirty, setIsDirty] = useState(false);
  const [creationAnalytics, setCreationAnalytics] = useState({
    startTime: null,
    stepTimes: {},
    validationAttempts: 0,
    previewViews: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    saveForm: () => handleSave(),
    resetForm: () => handleReset(),
    validateForm: () => validateAllSteps(),
    focusDialog: () => dialogRef.current?.focus(),

    // Navigation methods
    nextStep: () => handleNext(),
    previousStep: () => handleBack(),
    goToStep: (step) => setActiveStep(step),
    getCurrentStep: () => activeStep,

    // Data methods
    getFormData: () => formData,
    setFormData: (data) => setFormData(data),
    getErrors: () => errors,
    getWarnings: () => warnings,

    // State methods
    isDirty: () => isDirty,
    isValid: () => Object.keys(errors).length === 0,
    getLastSaved: () => lastSaved,

    // Preview methods
    togglePreview: () => setShowPreview(!showPreview),
    getPreviewData: () => generatePreviewData(),

    // Analytics methods
    getCreationAnalytics: () => creationAnalytics,
    resetAnalytics: () => setCreationAnalytics({
      startTime: null,
      stepTimes: {},
      validationAttempts: 0,
      previewViews: 0
    }),

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    generateCode: () => handleGenerateCode(),
    duplicateCoupon: () => handleDuplicate(),
    saveAsTemplate: () => handleSaveAsTemplate()
  }), [
    activeStep,
    formData,
    errors,
    warnings,
    isDirty,
    lastSaved,
    showPreview,
    creationAnalytics,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  const steps = useMemo(() => [
    {
      label: 'Basic Information',
      description: 'Set up coupon code, name, and description',
      icon: <InfoIcon />
    },
    {
      label: 'Discount Configuration',
      description: 'Configure discount type and value',
      icon: <MoneyIcon />
    },
    {
      label: 'Usage Restrictions',
      description: 'Set usage limits and expiration',
      icon: <SecurityIcon />
    },
    {
      label: 'Review & Create',
      description: 'Review settings and create coupon',
      icon: <VerifiedIcon />
    }
  ], []);

  // Enhanced analytics tracking
  useEffect(() => {
    if (open && enableAnalytics) {
      const startTime = new Date().toISOString();
      setCreationAnalytics(prev => ({
        ...prev,
        startTime
      }));

      if (onAnalyticsTrack) {
        onAnalyticsTrack(CREATION_ANALYTICS_EVENTS.CREATION_STARTED, {
          mode: creationMode,
          timestamp: startTime,
          editingCoupon: !!editingCoupon
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Coupon ${creationMode} dialog opened`);
      }
    }
  }, [open, enableAnalytics, creationMode, editingCoupon, onAnalyticsTrack, enableAccessibility, announceToScreenReader]);

  // Auto-save effect
  useEffect(() => {
    if (autoSaveEnabled && isDirty && autoSaveInterval > 0) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        handleAutoSave();
      }, autoSaveInterval);

      return () => {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current);
        }
      };
    }
  }, [autoSaveEnabled, isDirty, autoSaveInterval, formData]);

  // Real-time validation effect
  useEffect(() => {
    if (enableRealTimeValidation && open) {
      const timeoutId = setTimeout(() => {
        validateCurrentStep();
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [formData, enableRealTimeValidation, open, activeStep]);

  // Available discount types
  const discountTypes = useMemo(() => [
    {
      value: 'percentage',
      label: 'Percentage Off',
      description: 'Discount as percentage of total',
      icon: <MoneyIcon />,
      color: 'primary'
    },
    {
      value: 'fixed_amount',
      label: 'Fixed Amount Off',
      description: 'Fixed dollar amount discount',
      icon: <MoneyIcon />,
      color: 'success'
    },
    {
      value: 'free_trial_extension',
      label: 'Free Trial Extension',
      description: 'Extend trial period',
      icon: <ScheduleIcon />,
      color: 'info'
    },
    {
      value: 'free_addon',
      label: 'Free Addon',
      description: 'Free addon or feature',
      icon: <CampaignIcon />,
      color: 'warning'
    },
  ], []);

  // Available applicable items
  const applicableItems = useMemo(() => [
    {
      value: 'all',
      label: 'All Products',
      description: 'Apply to any purchase',
      icon: <CategoryIcon />,
      color: 'primary'
    },
    {
      value: 'subscription',
      label: 'Subscriptions Only',
      description: 'Apply to subscription plans',
      icon: <CampaignIcon />,
      color: 'success'
    },
    {
      value: 'addon',
      label: 'Addons Only',
      description: 'Apply to addon purchases',
      icon: <SettingsIcon />,
      color: 'info'
    },
  ], []);

  // Initialize form data when editing
  useEffect(() => {
    if (editingCoupon) {
      setFormData({
        ...editingCoupon,
        start_date: new Date(editingCoupon.start_date),
        end_date: editingCoupon.end_date ? new Date(editingCoupon.end_date) : null,
      });
      setCreationMode(CREATION_MODES.EDIT);
      setIsDirty(false);
    } else {
      setCreationMode(mode);
    }
  }, [editingCoupon, mode]);

  // Enhanced form field change handler
  const handleFieldChange = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }

    // Clear warning for this field
    if (warnings[field]) {
      setWarnings(prev => ({ ...prev, [field]: null }));
    }

    // Track field changes for analytics
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('field_changed', {
        field,
        value: typeof value === 'string' ? value.substring(0, 50) : value,
        step: activeStep,
        timestamp: new Date().toISOString()
      });
    }
  }, [errors, warnings, enableAnalytics, onAnalyticsTrack, activeStep]);

  // Enhanced code generation
  const handleGenerateCode = useCallback(() => {
    const code = generateCouponCode('', 8);
    handleFieldChange('code', code);

    if (enableAccessibility) {
      announceToScreenReader(`Generated coupon code: ${code}`);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('code_generated', {
        code,
        step: activeStep,
        timestamp: new Date().toISOString()
      });
    }
  }, [handleFieldChange, enableAccessibility, enableAnalytics, onAnalyticsTrack, activeStep, announceToScreenReader]);

  // Auto-save handler
  const handleAutoSave = useCallback(async () => {
    if (!isDirty || !formData.code) return;

    try {
      // Save to localStorage as backup
      const autoSaveData = {
        formData,
        timestamp: new Date().toISOString(),
        step: activeStep
      };
      localStorage.setItem('coupon_creator_autosave', JSON.stringify(autoSaveData));

      setLastSaved(new Date().toISOString());
      setIsDirty(false);

      if (onAutoSave) {
        onAutoSave(autoSaveData);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(CREATION_ANALYTICS_EVENTS.AUTO_SAVE_TRIGGERED, {
          step: activeStep,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader('Form auto-saved successfully');
      }
    } catch (error) {
      console.warn('Auto-save failed:', error);
    }
  }, [isDirty, formData, activeStep, onAutoSave, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced validation rules for each step
  const validationRules = useMemo(() => ({
    0: [ // Basic Information
      { key: 'code', label: 'Coupon Code', type: 'string', minLength: 3, maxLength: 50, required: true },
      { key: 'name', label: 'Name', type: 'string', minLength: 3, maxLength: 100, required: true },
      { key: 'description', label: 'Description', type: 'string', minLength: 10, maxLength: 500, required: true },
    ],
    1: [ // Discount Configuration
      { key: 'discount_value', label: 'Discount Value', type: 'number', min: 0, required: true },
    ],
    2: [ // Usage Restrictions
      { key: 'max_redemptions_per_user', label: 'Max Redemptions Per User', type: 'number', min: 1, required: true },
    ],
  }), []);

  // Enhanced validation function
  const validateCurrentStep = useCallback(() => {
    const rules = validationRules[activeStep] || [];
    const newErrors = {};
    const newWarnings = {};

    // Validate form data against rules
    rules.forEach(rule => {
      const value = formData[rule.key];

      if (rule.required && (!value || value === '')) {
        newErrors[rule.key] = `${rule.label} is required`;
      } else if (value) {
        if (rule.type === 'string') {
          if (rule.minLength && value.length < rule.minLength) {
            newErrors[rule.key] = `${rule.label} must be at least ${rule.minLength} characters`;
          }
          if (rule.maxLength && value.length > rule.maxLength) {
            newErrors[rule.key] = `${rule.label} must be no more than ${rule.maxLength} characters`;
          }
        } else if (rule.type === 'number') {
          const numValue = Number(value);
          if (isNaN(numValue)) {
            newErrors[rule.key] = `${rule.label} must be a valid number`;
          } else {
            if (rule.min !== undefined && numValue < rule.min) {
              newErrors[rule.key] = `${rule.label} must be at least ${rule.min}`;
            }
            if (rule.max !== undefined && numValue > rule.max) {
              newErrors[rule.key] = `${rule.label} must be no more than ${rule.max}`;
            }
          }
        }
      }
    });

    // Step-specific validations
    if (activeStep === 0) {
      const codeValidation = validateCouponCode(formData.code);
      if (!codeValidation.isValid) {
        newErrors.code = codeValidation.error;
      }
    }

    if (activeStep === 1) {
      if (formData.discount_type === 'percentage' && (formData.discount_value < 0 || formData.discount_value > 100)) {
        newErrors.discount_value = 'Percentage must be between 0 and 100';
      }

      if (formData.discount_value > 50 && formData.discount_type === 'percentage') {
        newWarnings.discount_value = 'High percentage discounts may impact profitability';
      }
    }

    if (activeStep === 2) {
      if (formData.end_date && formData.end_date <= formData.start_date) {
        newErrors.end_date = 'End date must be after start date';
      }

      if (formData.max_redemptions && formData.max_redemptions > 10000) {
        newWarnings.max_redemptions = 'Very high redemption limits may impact performance';
      }
    }

    setErrors(newErrors);
    setWarnings(newWarnings);

    const isValid = Object.keys(newErrors).length === 0;

    if (onValidationResult) {
      onValidationResult({
        step: activeStep,
        isValid,
        errors: newErrors,
        warnings: newWarnings
      });
    }

    if (!isValid && enableAnalytics && onAnalyticsTrack) {
      setCreationAnalytics(prev => ({
        ...prev,
        validationAttempts: prev.validationAttempts + 1
      }));

      onAnalyticsTrack(CREATION_ANALYTICS_EVENTS.VALIDATION_FAILED, {
        step: activeStep,
        errors: Object.keys(newErrors),
        timestamp: new Date().toISOString()
      });
    }

    return isValid;
  }, [activeStep, formData, validationRules, onValidationResult, enableAnalytics, onAnalyticsTrack]);

  const validateAllSteps = useCallback(() => {
    let allValid = true;
    for (let step = 0; step < steps.length - 1; step++) {
      const prevStep = activeStep;
      setActiveStep(step);
      if (!validateCurrentStep()) {
        allValid = false;
      }
      setActiveStep(prevStep);
    }
    return allValid;
  }, [activeStep, steps.length, validateCurrentStep]);

  // Initialize form data when editing
  React.useEffect(() => {
    if (editingCoupon) {
      setFormData({
        ...editingCoupon,
        start_date: new Date(editingCoupon.start_date),
        end_date: editingCoupon.end_date ? new Date(editingCoupon.end_date) : null,
      });
    }
  }, [editingCoupon]);

  // Enhanced preview data generation
  const generatePreviewData = useCallback(() => {
    const discountAmount = calculateDiscountAmount(
      100, // Sample amount
      formData.discount_type,
      formData.discount_value
    );

    return {
      ...formData,
      sampleDiscount: discountAmount,
      formattedDiscount: formatDiscount(formData),
      estimatedSavings: formData.discount_type === 'percentage'
        ? `${formData.discount_value}% off`
        : formatCurrency(formData.discount_value),
      validityPeriod: formData.end_date
        ? `Valid until ${formData.end_date.toLocaleDateString()}`
        : 'No expiration date'
    };
  }, [formData]);

  // Enhanced duplicate handler
  const handleDuplicate = useCallback(() => {
    const duplicatedData = {
      ...formData,
      code: generateCouponCode('COPY_', 8),
      name: `${formData.name} (Copy)`,
      is_active: false // Start as inactive
    };

    setFormData(duplicatedData);
    setCreationMode(CREATION_MODES.DUPLICATE);
    setIsDirty(true);

    if (enableAccessibility) {
      announceToScreenReader('Coupon duplicated. Please review and modify as needed.');
    }
  }, [formData, enableAccessibility, announceToScreenReader]);

  // Enhanced template save handler
  const handleSaveAsTemplate = useCallback(() => {
    const templateData = {
      ...formData,
      code: '', // Clear code for template
      name: `${formData.name} Template`,
      isTemplate: true
    };

    // Save to localStorage templates
    const existingTemplates = JSON.parse(localStorage.getItem('coupon_templates') || '[]');
    const newTemplates = [...existingTemplates, { ...templateData, id: Date.now() }];
    localStorage.setItem('coupon_templates', JSON.stringify(newTemplates));

    if (enableAccessibility) {
      announceToScreenReader('Coupon saved as template');
    }
  }, [formData, enableAccessibility, announceToScreenReader]);

  // Enhanced specific items management
  const handleAddItem = useCallback(() => {
    if (newItem.trim()) {
      handleFieldChange('specific_items', [...formData.specific_items, newItem.trim()]);
      setNewItem('');

      if (enableAccessibility) {
        announceToScreenReader(`Added item: ${newItem.trim()}`);
      }
    }
  }, [newItem, formData.specific_items, handleFieldChange, enableAccessibility, announceToScreenReader]);

  const handleRemoveItem = useCallback((index) => {
    const itemToRemove = formData.specific_items[index];
    handleFieldChange('specific_items', formData.specific_items.filter((_, i) => i !== index));

    if (enableAccessibility) {
      announceToScreenReader(`Removed item: ${itemToRemove}`);
    }
  }, [formData.specific_items, handleFieldChange, enableAccessibility, announceToScreenReader]);

  // Validate current step
  const validateStep = (step) => {
    const rules = validationRules[step] || [];
    const validation = validateFormData(formData, rules);
    
    // Additional validations
    if (step === 0) {
      const codeValidation = validateCouponCode(formData.code);
      if (!codeValidation.isValid) {
        validation.errors.code = codeValidation.error;
        validation.isValid = false;
      }
    }
    
    if (step === 1) {
      if (formData.discount_type === 'percentage' && (formData.discount_value < 0 || formData.discount_value > 100)) {
        validation.errors.discount_value = 'Percentage must be between 0 and 100';
        validation.isValid = false;
      }
    }
    
    if (step === 2) {
      if (formData.end_date && formData.end_date <= formData.start_date) {
        validation.errors.end_date = 'End date must be after start date';
        validation.isValid = false;
      }
    }
    
    setErrors(validation.errors);
    return validation.isValid;
  };

  // Enhanced navigation handlers
  const handleNext = useCallback(() => {
    if (validateCurrentStep()) {
      const nextStep = activeStep + 1;
      setActiveStep(nextStep);

      if (onStepChange) {
        onStepChange(nextStep);
      }

      if (enableAccessibility) {
        announceToScreenReader(`Moved to step ${nextStep + 1}: ${steps[nextStep]?.label}`);
      }

      // If this is the last step, trigger creation
      if (nextStep === steps.length - 1) {
        handleCreate();
      }
    }
  }, [activeStep, validateCurrentStep, onStepChange, enableAccessibility, announceToScreenReader, steps]);

  const handleBack = useCallback(() => {
    if (activeStep > 0) {
      const prevStep = activeStep - 1;
      setActiveStep(prevStep);

      if (onStepChange) {
        onStepChange(prevStep);
      }

      if (enableAccessibility) {
        announceToScreenReader(`Moved to step ${prevStep + 1}: ${steps[prevStep]?.label}`);
      }
    }
  }, [activeStep, onStepChange, enableAccessibility, announceToScreenReader, steps]);

  // Handle save
  const handleSave = async () => {
    if (!validateStep(activeStep)) return;
    
    setLoading(true);
    try {
      const couponData = {
        ...formData,
        start_date: formData.start_date.toISOString(),
        end_date: formData.end_date ? formData.end_date.toISOString() : null,
      };
      
      let response;
      if (editingCoupon) {
        response = await api.put(`/api/coupons/${editingCoupon.id}`, couponData);
      } else {
        response = await api.post('/api/coupons', couponData);
      }
      
      if (onCouponCreated) {
        onCouponCreated(response.data);
      }
      
      handleClose();
    } catch (error) {
      console.error('Error saving coupon:', error);
      setErrors({ 
        save: error.response?.data?.detail || 'Failed to save coupon' 
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle close
  const handleClose = () => {
    setActiveStep(0);
    setFormData({
      code: '',
      name: '',
      description: '',
      discount_type: 'percentage',
      discount_value: 0,
      applicable_to: 'all',
      specific_items: [],
      minimum_purchase_amount: 0,
      max_discount_amount: null,
      max_redemptions: null,
      max_redemptions_per_user: 1,
      start_date: new Date(),
      end_date: null,
      first_time_users_only: false,
      requires_subscription: false,
      is_active: true,
    });
    setErrors({});
    setNewItem('');
    onClose();
  };

  // Calculate preview discount
  const previewDiscount = calculateDiscountAmount(100, formData);

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh',
        }
      }}
      {...props}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {editingCoupon ? 'Edit Coupon' : 'Create New Coupon'}
          </Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box display="flex" flexDirection="column" gap={3} mt={2}>
          {errors.save && (
            <Alert severity="error">{errors.save}</Alert>
          )}

          <Stepper activeStep={activeStep} orientation="vertical">
            {/* Step 1: Basic Information */}
            <Step>
              <StepLabel>Basic Information</StepLabel>
              <StepContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={8}>
                    <TextField
                      label="Coupon Code"
                      value={formData.code}
                      onChange={(e) => handleFieldChange('code', e.target.value.toUpperCase())}
                      error={!!errors.code}
                      helperText={errors.code || 'Unique identifier for this coupon'}
                      fullWidth
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Tooltip title="Generate Random Code">
                              <IconButton onClick={handleGenerateCode} edge="end">
                                <AddIcon />
                              </IconButton>
                            </Tooltip>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Coupon Name"
                      value={formData.name}
                      onChange={(e) => handleFieldChange('name', e.target.value)}
                      error={!!errors.name}
                      helperText={errors.name || 'Display name for this coupon'}
                      fullWidth
                      required
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Description"
                      value={formData.description}
                      onChange={(e) => handleFieldChange('description', e.target.value)}
                      error={!!errors.description}
                      helperText={errors.description || 'Detailed description of the offer'}
                      fullWidth
                      multiline
                      rows={3}
                      required
                    />
                  </Grid>
                </Grid>
              </StepContent>
            </Step>

            {/* Step 2: Discount Configuration */}
            <Step>
              <StepLabel>Discount Configuration</StepLabel>
              <StepContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Discount Type</InputLabel>
                      <Select
                        value={formData.discount_type}
                        onChange={(e) => handleFieldChange('discount_type', e.target.value)}
                        label="Discount Type"
                      >
                        {discountTypes.map((type) => (
                          <MenuItem key={type.value} value={type.value}>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {type.label}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {type.description}
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Discount Value"
                      type="number"
                      value={formData.discount_value}
                      onChange={(e) => handleFieldChange('discount_value', parseFloat(e.target.value) || 0)}
                      error={!!errors.discount_value}
                      helperText={errors.discount_value}
                      fullWidth
                      required
                      InputProps={{
                        startAdornment: formData.discount_type === 'fixed_amount' ? (
                          <InputAdornment position="start">$</InputAdornment>
                        ) : formData.discount_type === 'percentage' ? (
                          <InputAdornment position="end">%</InputAdornment>
                        ) : null,
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Minimum Purchase Amount"
                      type="number"
                      value={formData.minimum_purchase_amount}
                      onChange={(e) => handleFieldChange('minimum_purchase_amount', parseFloat(e.target.value) || 0)}
                      fullWidth
                      InputProps={{
                        startAdornment: <InputAdornment position="start">$</InputAdornment>,
                      }}
                      helperText="Minimum order value required"
                    />
                  </Grid>
                  {formData.discount_type === 'percentage' && (
                    <Grid item xs={12} md={6}>
                      <TextField
                        label="Maximum Discount Amount"
                        type="number"
                        value={formData.max_discount_amount || ''}
                        onChange={(e) => handleFieldChange('max_discount_amount', parseFloat(e.target.value) || null)}
                        fullWidth
                        InputProps={{
                          startAdornment: <InputAdornment position="start">$</InputAdornment>,
                        }}
                        helperText="Cap the maximum discount (optional)"
                      />
                    </Grid>
                  )}
                </Grid>
                
                {/* Preview */}
                <Card variant="outlined" sx={{ mt: 2, p: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Preview: {formatDiscount(formData)} discount
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    On a $100 order: Save {formatCurrency(previewDiscount)}
                  </Typography>
                </Card>
              </StepContent>
            </Step>

            {/* Step 3: Usage Restrictions */}
            <Step>
              <StepLabel>Usage Restrictions</StepLabel>
              <StepContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Applicable To</InputLabel>
                      <Select
                        value={formData.applicable_to}
                        onChange={(e) => handleFieldChange('applicable_to', e.target.value)}
                        label="Applicable To"
                      >
                        {applicableItems.map((item) => (
                          <MenuItem key={item.value} value={item.value}>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {item.label}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {item.description}
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Max Redemptions Per User"
                      type="number"
                      value={formData.max_redemptions_per_user}
                      onChange={(e) => handleFieldChange('max_redemptions_per_user', parseInt(e.target.value) || 1)}
                      error={!!errors.max_redemptions_per_user}
                      helperText={errors.max_redemptions_per_user}
                      fullWidth
                      inputProps={{ min: 1 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Total Max Redemptions (Optional)"
                      type="number"
                      value={formData.max_redemptions || ''}
                      onChange={(e) => handleFieldChange('max_redemptions', parseInt(e.target.value) || null)}
                      fullWidth
                      helperText="Leave empty for unlimited"
                      inputProps={{ min: 1 }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <DateTimePicker
                      label="Start Date"
                      value={formData.start_date}
                      onChange={(date) => handleFieldChange('start_date', date)}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <DateTimePicker
                      label="End Date (Optional)"
                      value={formData.end_date}
                      onChange={(date) => handleFieldChange('end_date', date)}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          error: !!errors.end_date,
                          helperText: errors.end_date,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Box display="flex" flexDirection="column" gap={1}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.first_time_users_only}
                            onChange={(e) => handleFieldChange('first_time_users_only', e.target.checked)}
                          />
                        }
                        label="First-time users only"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.requires_subscription}
                            onChange={(e) => handleFieldChange('requires_subscription', e.target.checked)}
                          />
                        }
                        label="Requires active subscription"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.is_active}
                            onChange={(e) => handleFieldChange('is_active', e.target.checked)}
                          />
                        }
                        label="Active coupon"
                      />
                    </Box>
                  </Grid>
                </Grid>
              </StepContent>
            </Step>

            {/* Step 4: Review & Create */}
            <Step>
              <StepLabel>Review & Create</StepLabel>
              <StepContent>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Coupon Summary
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Code:</Typography>
                        <Typography variant="body1" fontWeight="medium" fontFamily="monospace">
                          {formData.code}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Name:</Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {formData.name}
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">Description:</Typography>
                        <Typography variant="body1">
                          {formData.description}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Discount:</Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {formatDiscount(formData)}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" color="text.secondary">Usage Limit:</Typography>
                        <Typography variant="body1">
                          {formData.max_redemptions_per_user} per user
                          {formData.max_redemptions && `, ${formData.max_redemptions} total`}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </StepContent>
            </Step>
          </Stepper>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={loading}>
            Back
          </Button>
        )}
        <Button 
          onClick={handleNext} 
          variant="contained"
          disabled={loading}
        >
          {loading ? 'Saving...' : (activeStep === steps.length - 1 ? 
            (editingCoupon ? 'Update Coupon' : 'Create Coupon') : 'Next')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedCouponCreator.propTypes = {
  // Core props
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onCouponCreated: PropTypes.func,
  editingCoupon: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    code: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    discount_type: PropTypes.oneOf(['percentage', 'fixed_amount', 'free_trial_extension', 'free_addon']).isRequired,
    discount_value: PropTypes.number.isRequired,
    start_date: PropTypes.string,
    end_date: PropTypes.string,
    is_active: PropTypes.bool
  }),

  // Enhanced props
  className: PropTypes.string,
  mode: PropTypes.oneOf(Object.values(CREATION_MODES)),
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeValidation: PropTypes.bool,
  enableAutoSave: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableTemplates: PropTypes.bool,

  // Configuration
  validationLevel: PropTypes.oneOf(Object.values(VALIDATION_LEVELS)),
  autoSaveInterval: PropTypes.number,
  maxSteps: PropTypes.number,

  // Callback props
  onStepChange: PropTypes.func,
  onValidationResult: PropTypes.func,
  onAutoSave: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onTemplateSelect: PropTypes.func
};

// Default props
EnhancedCouponCreator.defaultProps = {
  editingCoupon: null,
  onCouponCreated: null,
  className: '',
  mode: CREATION_MODES.CREATE,
  enableAdvancedFeatures: true,
  enableRealTimeValidation: true,
  enableAutoSave: true,
  enableAccessibility: true,
  enableAnalytics: true,
  enableTemplates: true,
  validationLevel: VALIDATION_LEVELS.ADVANCED,
  autoSaveInterval: AUTO_SAVE_INTERVALS.NORMAL,
  maxSteps: 4,
  onStepChange: null,
  onValidationResult: null,
  onAutoSave: null,
  onAnalyticsTrack: null,
  onTemplateSelect: null
};

// Display name for debugging
EnhancedCouponCreator.displayName = 'EnhancedCouponCreator';

export default EnhancedCouponCreator;

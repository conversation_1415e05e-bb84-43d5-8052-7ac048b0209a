/**
 * Tests for BrandColorPicker component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandColorPicker from '../BrandColorPicker';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('BrandColorPicker', () => {
  const mockColorSystem = {
    primary: '#4E40C5',
    secondary: '#00E4BC',
    accent: '#FF5733',
    background: '#F8F9FA',
    text: '#333333',
    relationship: 'complementary',
    gradients: [
      {
        id: '1',
        colors: ['#4E40C5', '#00E4BC'],
        type: 'linear',
        angle: 135
      }
    ]
  };

  const mockProps = {
    colorSystem: mockColorSystem,
    onChange: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders color picker correctly', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Colors')).toBeInTheDocument();
    expect(screen.getByText('Primary Colors')).toBeInTheDocument();
    expect(screen.getByText('Background & Text')).toBeInTheDocument();
    expect(screen.getByText('Color Relationship')).toBeInTheDocument();
    expect(screen.getByText('Gradients')).toBeInTheDocument();
    expect(screen.getByText('Generate Color Palette')).toBeInTheDocument();
  });

  test('displays color boxes with correct colors', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    const primaryColorBox = screen.getByLabelText('Select primary color');
    const secondaryColorBox = screen.getByLabelText('Select secondary color');

    expect(primaryColorBox).toBeInTheDocument();
    expect(secondaryColorBox).toBeInTheDocument();
  });

  test('opens color picker when color box is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    const primaryColorBox = screen.getByLabelText('Select primary color');
    await user.click(primaryColorBox);

    expect(screen.getByText('Color Picker')).toBeInTheDocument();
  });

  test('calls onChange when color relationship is changed', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    const analogousButton = screen.getByText('Analogous');
    await user.click(analogousButton);

    expect(mockProps.onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        relationship: 'analogous'
      })
    );
  });

  test('adds gradient when add gradient button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    const addGradientButton = screen.getByText('Add Gradient');
    await user.click(addGradientButton);

    expect(mockProps.onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        gradients: expect.arrayContaining([
          expect.objectContaining({
            colors: [mockColorSystem.primary, mockColorSystem.secondary],
            type: 'linear',
            angle: 135
          })
        ])
      })
    );
  });

  test('generates color palette when generate button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    const generateButton = screen.getByText('Generate Color Palette');
    await user.click(generateButton);

    expect(mockProps.onChange).toHaveBeenCalledWith(
      expect.objectContaining({
        primary: mockColorSystem.primary,
        accent: '#FF5733',
        background: '#F8F9FA',
        text: '#333333'
      })
    );
  });

  test('disables interactions when readOnly is true', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} readOnly={true} />
      </TestWrapper>
    );

    const relationshipButtons = screen.getAllByRole('button').filter(button => 
      ['Complementary', 'Analogous', 'Triadic', 'Monochromatic', 'Custom'].includes(button.textContent)
    );
    
    relationshipButtons.forEach(button => {
      expect(button).toBeDisabled();
    });

    const generateButton = screen.getByText('Generate Color Palette');
    expect(generateButton).toBeDisabled();
  });

  test('disables interactions when disabled is true', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const primaryColorBox = screen.getByLabelText('Select primary color');
    expect(primaryColorBox).toBeDisabled();
  });

  test('does not show gradients section when showGradients is false', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} showGradients={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Gradients')).not.toBeInTheDocument();
    expect(screen.queryByText('Add Gradient')).not.toBeInTheDocument();
  });

  test('does not show AI generator when showAIGenerator is false', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} showAIGenerator={false} />
      </TestWrapper>
    );

    expect(screen.queryByText('Generate Color Palette')).not.toBeInTheDocument();
  });

  test('handles missing color system properties gracefully', () => {
    const incompleteColorSystem = {
      primary: '#4E40C5'
      // Missing other properties
    };

    render(
      <TestWrapper>
        <BrandColorPicker 
          colorSystem={incompleteColorSystem} 
          onChange={mockProps.onChange} 
        />
      </TestWrapper>
    );

    // Should render without errors
    expect(screen.getByText('Brand Colors')).toBeInTheDocument();
  });

  test('displays existing gradients correctly', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    // Should show the existing gradient
    const gradientElements = screen.getAllByRole('button').filter(button => 
      button.querySelector('.MuiSvgIcon-root[data-testid="DeleteIcon"]')
    );
    
    expect(gradientElements.length).toBeGreaterThan(0);
  });

  test('removes gradient when delete button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    // Find and click the delete button for the gradient
    const deleteButtons = screen.getAllByRole('button').filter(button => 
      button.querySelector('.MuiSvgIcon-root[data-testid="DeleteIcon"]')
    );
    
    if (deleteButtons.length > 0) {
      await user.click(deleteButtons[0]);

      expect(mockProps.onChange).toHaveBeenCalledWith(
        expect.objectContaining({
          gradients: []
        })
      );
    }
  });

  test('shows selected state for active color', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    const primaryColorBox = screen.getByLabelText('Select primary color');
    await user.click(primaryColorBox);

    // Check if the color box shows selected state (CheckIcon should be visible)
    expect(screen.getByTestId('CheckIcon')).toBeInTheDocument();
  });

  test('handles null color system gracefully', () => {
    render(
      <TestWrapper>
        <BrandColorPicker
          colorSystem={null}
          onChange={mockProps.onChange}
        />
      </TestWrapper>
    );

    // Should render without crashing and show default colors
    expect(screen.getByText('Brand Colors')).toBeInTheDocument();
    expect(screen.getByLabelText('Select primary color')).toBeInTheDocument();
  });

  test('applies correct accessibility attributes', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    const primaryColorBox = screen.getByLabelText('Select primary color');
    const secondaryColorBox = screen.getByLabelText('Select secondary color');

    expect(primaryColorBox).toHaveAttribute('aria-label', 'Select primary color');
    expect(secondaryColorBox).toHaveAttribute('aria-label', 'Select secondary color');
    expect(primaryColorBox).toHaveAttribute('role', 'button');
    expect(secondaryColorBox).toHaveAttribute('role', 'button');
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <BrandColorPicker 
          {...mockProps} 
          data-testid="test-color-picker"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-color-picker');
    expect(component).toHaveClass('custom-class');
  });

  test('updates color picker value when different color is selected', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    // Click primary color first
    const primaryColorBox = screen.getByLabelText('Select primary color');
    await user.click(primaryColorBox);

    expect(screen.getByText('#4E40C5')).toBeInTheDocument();

    // Click secondary color
    const secondaryColorBox = screen.getByLabelText('Select secondary color');
    await user.click(secondaryColorBox);

    expect(screen.getByText('#00E4BC')).toBeInTheDocument();
  });

  test('shows correct relationship button as selected', () => {
    render(
      <TestWrapper>
        <BrandColorPicker {...mockProps} />
      </TestWrapper>
    );

    const complementaryButton = screen.getByText('Complementary');
    expect(complementaryButton).toHaveClass('MuiButton-contained');
  });

  test('handles color system without gradients', () => {
    const colorSystemWithoutGradients = {
      ...mockColorSystem,
      gradients: undefined
    };

    render(
      <TestWrapper>
        <BrandColorPicker 
          colorSystem={colorSystemWithoutGradients} 
          onChange={mockProps.onChange} 
        />
      </TestWrapper>
    );

    expect(screen.getByText('Gradients')).toBeInTheDocument();
    expect(screen.getByText('Add Gradient')).toBeInTheDocument();
  });

  test('handles empty gradients array', () => {
    const colorSystemWithEmptyGradients = {
      ...mockColorSystem,
      gradients: []
    };

    render(
      <TestWrapper>
        <BrandColorPicker 
          colorSystem={colorSystemWithEmptyGradients} 
          onChange={mockProps.onChange} 
        />
      </TestWrapper>
    );

    expect(screen.getByText('Gradients')).toBeInTheDocument();
    expect(screen.getByText('Add Gradient')).toBeInTheDocument();
  });
});

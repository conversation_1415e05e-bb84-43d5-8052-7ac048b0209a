import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as EnableIcon,
  Pause as DisableIcon,
  Build as MaintenanceIcon,
  Error as ErrorIcon,
  CheckCircle as HealthyIcon,
  Warning as DegradedIcon,
  Cancel as DownIcon,
  Analytics as AnalyticsIcon,
  Timeline as TrendIcon,
  BugReport as BugIcon,
  People as UsersIcon,
  Settings as SettingsIcon,
  Download as ExportIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';

import StablePageWrapper from '../components/common/StablePageWrapper';
import { apiManagementService } from '../services/apiManagement';

const ApiManagement = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [autoRefresh, setAutoRefresh] = useState(true);
  
  // Data states
  const [apiRegistry, setApiRegistry] = useState([]);
  const [realTimeMetrics, setRealTimeMetrics] = useState(null);
  const [usageTrends, setUsageTrends] = useState([]);
  const [errorPatterns, setErrorPatterns] = useState([]);
  const [performanceInsights, setPerformanceInsights] = useState([]);
  const [userBehavior, setUserBehavior] = useState(null);
  
  // Dialog states
  const [statusDialog, setStatusDialog] = useState({ open: false, endpoint: null });
  const [bulkDialog, setBulkDialog] = useState({ open: false });
  const [healthCheckDialog, setHealthCheckDialog] = useState({ open: false });

  // Load data on component mount and tab change
  useEffect(() => {
    loadData();
  }, [activeTab]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (activeTab === 0) { // Real-time tab
        loadRealTimeData();
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, activeTab]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      switch (activeTab) {
        case 0: // Overview
          await Promise.all([
            loadApiRegistry(),
            loadRealTimeData()
          ]);
          break;
        case 1: // Analytics
          await Promise.all([
            loadUsageTrends(),
            loadPerformanceInsights()
          ]);
          break;
        case 2: // Errors
          await loadErrorPatterns();
          break;
        case 3: // Users
          await loadUserBehavior();
          break;
        default:
          await loadApiRegistry();
      }
    } catch (err) {
      setError(err.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadApiRegistry = async () => {
    const data = await apiManagementService.getApiRegistry();
    setApiRegistry(data);
  };

  const loadRealTimeData = async () => {
    const data = await apiManagementService.getRealTimeMetrics();
    setRealTimeMetrics(data);
  };

  const loadUsageTrends = async () => {
    const data = await apiManagementService.getUsageTrends(24, 60);
    setUsageTrends(data.trends || []);
  };

  const loadErrorPatterns = async () => {
    const data = await apiManagementService.getErrorPatterns(24);
    setErrorPatterns(data);
  };

  const loadPerformanceInsights = async () => {
    const data = await apiManagementService.getPerformanceInsights(24);
    setPerformanceInsights(data);
  };

  const loadUserBehavior = async () => {
    const data = await apiManagementService.getUserBehavior(24);
    setUserBehavior(data);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'disabled': return 'error';
      case 'maintenance': return 'warning';
      default: return 'default';
    }
  };

  const getHealthIcon = (healthStatus) => {
    switch (healthStatus) {
      case 'healthy': return <HealthyIcon color="success" />;
      case 'degraded': return <DegradedIcon color="warning" />;
      case 'down': return <DownIcon color="error" />;
      default: return <ErrorIcon color="disabled" />;
    }
  };

  const formatResponseTime = (ms) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Real-time Metrics Cards */}
      {realTimeMetrics && (
        <Grid item xs={12}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="glass">
                <CardContent>
                  <Typography variant="h6" color="primary">
                    Requests/Hour
                  </Typography>
                  <Typography variant="h4">
                    {realTimeMetrics.requests_last_hour.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {formatPercentage(realTimeMetrics.success_rate)} success rate
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="glass">
                <CardContent>
                  <Typography variant="h6" color="secondary">
                    Avg Response Time
                  </Typography>
                  <Typography variant="h4">
                    {formatResponseTime(realTimeMetrics.avg_response_time_ms)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Last hour average
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="glass">
                <CardContent>
                  <Typography variant="h6" color="error">
                    Error Rate
                  </Typography>
                  <Typography variant="h4">
                    {formatPercentage(realTimeMetrics.error_rate)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {realTimeMetrics.failed_requests} failed requests
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="glass">
                <CardContent>
                  <Typography variant="h6" color="info">
                    Active APIs
                  </Typography>
                  <Typography variant="h4">
                    {realTimeMetrics.api_status.active}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    of {realTimeMetrics.api_status.total} total
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      )}

      {/* API Registry Table */}
      <Grid item xs={12}>
        <Card variant="glass">
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                API Registry ({apiRegistry.length} endpoints)
              </Typography>
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoRefresh}
                      onChange={(e) => setAutoRefresh(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Auto Refresh"
                />
                <Button
                  startIcon={<RefreshIcon />}
                  onClick={loadData}
                  disabled={loading}
                  sx={{ ml: 1 }}
                >
                  Refresh
                </Button>
              </Box>
            </Box>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Endpoint</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Health</TableCell>
                    <TableCell>Response Time</TableCell>
                    <TableCell>Success Rate</TableCell>
                    <TableCell>Requests</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {apiRegistry.map((api, index) => (
                    <TableRow key={index} hover>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {api.method} {api.path}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {api.description}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={api.status}
                          color={getStatusColor(api.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title={api.health_status}>
                          {getHealthIcon(api.health_status)}
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        {formatResponseTime(api.response_time_ms)}
                      </TableCell>
                      <TableCell>
                        {formatPercentage(api.success_rate)}
                      </TableCell>
                      <TableCell>
                        {api.total_requests.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Change Status">
                          <IconButton
                            size="small"
                            onClick={() => setStatusDialog({ 
                              open: true, 
                              endpoint: api 
                            })}
                          >
                            <SettingsIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderAnalyticsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card variant="glass">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Performance Analytics
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Analytics content will be implemented with charts and detailed metrics
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderErrorsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card variant="glass">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Error Patterns ({errorPatterns.length})
            </Typography>
            {errorPatterns.length === 0 ? (
              <Alert severity="success">No error patterns detected in the last 24 hours</Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Endpoint</TableCell>
                      <TableCell>Error Type</TableCell>
                      <TableCell>Count</TableCell>
                      <TableCell>Error Rate</TableCell>
                      <TableCell>Affected Users</TableCell>
                      <TableCell>Last Occurrence</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {errorPatterns.map((pattern, index) => (
                      <TableRow key={index}>
                        <TableCell>{pattern.endpoint}</TableCell>
                        <TableCell>{pattern.error_type}</TableCell>
                        <TableCell>{pattern.error_count}</TableCell>
                        <TableCell>{formatPercentage(pattern.error_rate)}</TableCell>
                        <TableCell>{pattern.affected_users}</TableCell>
                        <TableCell>
                          {new Date(pattern.last_occurrence).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderUsersTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card variant="glass">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              User Behavior Analytics
            </Typography>
            <Typography variant="body2" color="textSecondary">
              User behavior analytics will be displayed here
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  if (loading && !realTimeMetrics) {
    return (
      <StablePageWrapper>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </StablePageWrapper>
    );
  }

  return (
    <StablePageWrapper>
      <Box mb={3}>
        <Typography variant="h4" gutterBottom>
          API Management System
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Monitor, analyze, and control all API endpoints in real-time
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ width: '100%', mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<AnalyticsIcon />} label="Overview" />
          <Tab icon={<TrendIcon />} label="Analytics" />
          <Tab icon={<BugIcon />} label="Errors" />
          <Tab icon={<UsersIcon />} label="Users" />
        </Tabs>
      </Paper>

      {activeTab === 0 && renderOverviewTab()}
      {activeTab === 1 && renderAnalyticsTab()}
      {activeTab === 2 && renderErrorsTab()}
      {activeTab === 3 && renderUsersTab()}
    </StablePageWrapper>
  );
};

export default ApiManagement;

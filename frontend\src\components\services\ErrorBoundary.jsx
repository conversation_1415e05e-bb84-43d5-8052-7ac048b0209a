/**
 * Enhanced Error Boundary - Enterprise-grade error boundary component
 * Features: Comprehensive error handling with advanced recovery mechanisms, multi-layered error catching,
 * accessibility compliance, comprehensive error logging, and ACE Social platform integration
 * with advanced error management capabilities and seamless error handling workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  useState,
  useEffect,
  useCallback,
  memo
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Alert,
  useTheme,
  alpha,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Collapse,
  Divider
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Download as DownloadIcon,
  Support as SupportIcon,
  Feedback as FeedbackIcon,
  RestartAlt as RestartIcon
} from '@mui/icons-material';

import FeatureGate from '../common/FeatureGate';

// Enhanced context and hook imports
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Enhanced error boundary component

// Error severity levels
const ERROR_SEVERITY = {
  CRITICAL: 'critical',
  WARNING: 'warning',
  INFO: 'info',
  RECOVERABLE: 'recoverable'
};

// Error categories
const ERROR_CATEGORIES = {
  COMPONENT: 'component',
  NETWORK: 'network',
  DATA: 'data',
  PERMISSION: 'permission',
  VALIDATION: 'validation',
  SYSTEM: 'system'
};

/**
 * Enhanced Error Boundary - Comprehensive error boundary for service workflow
 * Implements advanced error handling with correlation ID tracking, error analytics, and recovery mechanisms
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      correlationId: null,
      errorSeverity: ERROR_SEVERITY.CRITICAL,
      errorCategory: ERROR_CATEGORIES.COMPONENT,
      retryCount: 0,
      errorHistory: [],
      userFeedback: null,
      diagnosticData: null
    };
  }

  static getDerivedStateFromError(error) {
    // Enhanced error state derivation with categorization
    const correlationId = `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Determine error severity and category
    let errorSeverity = ERROR_SEVERITY.CRITICAL;
    let errorCategory = ERROR_CATEGORIES.COMPONENT;

    if (error.name === 'ChunkLoadError' || error.message.includes('Loading chunk')) {
      errorSeverity = ERROR_SEVERITY.RECOVERABLE;
      errorCategory = ERROR_CATEGORIES.NETWORK;
    } else if (error.message.includes('Network Error') || error.message.includes('fetch')) {
      errorSeverity = ERROR_SEVERITY.WARNING;
      errorCategory = ERROR_CATEGORIES.NETWORK;
    } else if (error.message.includes('Permission') || error.message.includes('Unauthorized')) {
      errorSeverity = ERROR_SEVERITY.WARNING;
      errorCategory = ERROR_CATEGORIES.PERMISSION;
    } else if (error.message.includes('Validation') || error.message.includes('Invalid')) {
      errorSeverity = ERROR_SEVERITY.INFO;
      errorCategory = ERROR_CATEGORIES.VALIDATION;
    }

    return {
      hasError: true,
      correlationId,
      errorSeverity,
      errorCategory
    };
  }

  componentDidCatch(error, errorInfo) {
    // Enhanced error logging with comprehensive diagnostics
    const correlationId = this.state.correlationId;
    const timestamp = new Date().toISOString();

    // Collect diagnostic data
    const diagnosticData = {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      componentStack: errorInfo.componentStack,
      correlationId,
      timestamp,
      userAgent: navigator.userAgent,
      url: window.location.href,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      memory: performance.memory ? {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      } : null,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null,
      localStorage: this.getLocalStorageInfo(),
      sessionStorage: this.getSessionStorageInfo()
    };

    console.error('Enhanced Service Workflow Error:', diagnosticData);

    // Update state with comprehensive error details
    this.setState({
      error,
      errorInfo,
      diagnosticData,
      errorHistory: [...this.state.errorHistory, {
        timestamp,
        error: error.message,
        correlationId,
        severity: this.state.errorSeverity,
        category: this.state.errorCategory
      }].slice(-10) // Keep last 10 errors
    });

    // Enhanced error reporting
    this.reportError(diagnosticData);
  }

  // Helper method to get localStorage info safely
  getLocalStorageInfo = () => {
    try {
      const keys = Object.keys(localStorage);
      return {
        itemCount: keys.length,
        totalSize: JSON.stringify(localStorage).length,
        keys: keys.slice(0, 10) // First 10 keys for privacy
      };
    } catch {
      return { error: 'Unable to access localStorage' };
    }
  };

  // Helper method to get sessionStorage info safely
  getSessionStorageInfo = () => {
    try {
      const keys = Object.keys(sessionStorage);
      return {
        itemCount: keys.length,
        totalSize: JSON.stringify(sessionStorage).length,
        keys: keys.slice(0, 10) // First 10 keys for privacy
      };
    } catch {
      return { error: 'Unable to access sessionStorage' };
    }
  };

  // Enhanced error reporting method
  reportError = (diagnosticData) => {
    // Report to Google Analytics (if available)
    if (window.gtag) {
      window.gtag('event', 'exception', {
        description: diagnosticData.error.message,
        fatal: false,
        custom_map: {
          correlation_id: diagnosticData.correlationId,
          error_category: this.state.errorCategory,
          error_severity: this.state.errorSeverity
        }
      });
    }

    // Report to external error tracking service
    if (window.Sentry) {
      window.Sentry.captureException(diagnosticData.error, {
        tags: {
          correlationId: diagnosticData.correlationId,
          errorCategory: this.state.errorCategory,
          errorSeverity: this.state.errorSeverity
        },
        extra: diagnosticData
      });
    }

    // Report to custom error API (if available)
    if (this.props.onError) {
      this.props.onError(diagnosticData);
    }
  };

  // Enhanced retry with intelligent recovery
  handleRetry = () => {
    const newRetryCount = this.state.retryCount + 1;

    // Log retry attempt
    console.log(`Error Recovery Attempt ${newRetryCount}:`, {
      correlationId: this.state.correlationId,
      errorCategory: this.state.errorCategory,
      errorSeverity: this.state.errorSeverity
    });

    // Clear error state and increment retry count
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      correlationId: null,
      retryCount: newRetryCount,
      userFeedback: null,
      diagnosticData: null
    });

    // Call retry callback if provided
    if (this.props.onRetry) {
      this.props.onRetry(newRetryCount);
    }
  };

  // Enhanced navigation with cleanup
  handleGoHome = () => {
    // Log navigation
    console.log('Error Recovery: Navigating to home', {
      correlationId: this.state.correlationId
    });

    // Call navigation callback if provided
    if (this.props.onNavigateHome) {
      this.props.onNavigateHome();
    } else {
      window.location.href = '/';
    }
  };

  // Handle user feedback submission
  handleFeedbackSubmit = (feedback) => {
    this.setState({ userFeedback: feedback });

    // Report feedback
    if (this.props.onFeedback) {
      this.props.onFeedback({
        correlationId: this.state.correlationId,
        feedback,
        errorCategory: this.state.errorCategory,
        errorSeverity: this.state.errorSeverity
      });
    }
  };

  // Handle diagnostic data export
  handleExportDiagnostics = () => {
    const exportData = {
      correlationId: this.state.correlationId,
      error: this.state.error?.message,
      errorCategory: this.state.errorCategory,
      errorSeverity: this.state.errorSeverity,
      diagnosticData: this.state.diagnosticData,
      errorHistory: this.state.errorHistory,
      userFeedback: this.state.userFeedback,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `error-diagnostics-${this.state.correlationId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Handle support contact
  handleContactSupport = () => {
    if (this.props.onContactSupport) {
      this.props.onContactSupport({
        correlationId: this.state.correlationId,
        errorCategory: this.state.errorCategory,
        errorSeverity: this.state.errorSeverity,
        diagnosticData: this.state.diagnosticData
      });
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <EnhancedErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          correlationId={this.state.correlationId}
          errorSeverity={this.state.errorSeverity}
          errorCategory={this.state.errorCategory}
          retryCount={this.state.retryCount}
          errorHistory={this.state.errorHistory}
          diagnosticData={this.state.diagnosticData}
          userFeedback={this.state.userFeedback}
          onRetry={this.handleRetry}
          onGoHome={this.handleGoHome}
          onFeedbackSubmit={this.handleFeedbackSubmit}
          onExportDiagnostics={this.handleExportDiagnostics}
          onContactSupport={this.handleContactSupport}
          customMessage={this.props.fallbackMessage}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Enhanced Error Fallback - UI component for comprehensive error display
 */
const EnhancedErrorFallback = memo(({
  error,
  correlationId,
  errorSeverity = ERROR_SEVERITY.CRITICAL,
  errorCategory = ERROR_CATEGORIES.COMPONENT,
  retryCount = 0,
  onRetry,
  onGoHome,
  onFeedbackSubmit,
  onExportDiagnostics,
  onContactSupport,
  customMessage
}) => {
  const theme = useTheme();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // State for enhanced features
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false);

  // Get error severity icon and color
  const getErrorIcon = () => {
    switch (errorSeverity) {
      case ERROR_SEVERITY.CRITICAL:
        return <ErrorIcon sx={{ fontSize: 64, color: theme.palette.error.main, mb: 2 }} />;
      case ERROR_SEVERITY.WARNING:
        return <WarningIcon sx={{ fontSize: 64, color: theme.palette.warning.main, mb: 2 }} />;
      case ERROR_SEVERITY.INFO:
        return <InfoIcon sx={{ fontSize: 64, color: theme.palette.info.main, mb: 2 }} />;
      case ERROR_SEVERITY.RECOVERABLE:
        return <RestartIcon sx={{ fontSize: 64, color: theme.palette.success.main, mb: 2 }} />;
      default:
        return <ErrorIcon sx={{ fontSize: 64, color: theme.palette.error.main, mb: 2 }} />;
    }
  };

  // Get error title based on severity and category
  const getErrorTitle = useCallback(() => {
    if (errorSeverity === ERROR_SEVERITY.RECOVERABLE) {
      return 'Temporary Issue Detected';
    }
    if (errorCategory === ERROR_CATEGORIES.NETWORK) {
      return 'Connection Issue';
    }
    if (errorCategory === ERROR_CATEGORIES.PERMISSION) {
      return 'Access Denied';
    }
    if (errorCategory === ERROR_CATEGORIES.VALIDATION) {
      return 'Invalid Input';
    }
    return 'Something Went Wrong';
  }, [errorSeverity, errorCategory]);

  // Get error description based on category and severity
  const getErrorDescription = useCallback(() => {
    if (customMessage) return customMessage;

    if (errorSeverity === ERROR_SEVERITY.RECOVERABLE) {
      return 'We detected a temporary issue that can usually be resolved by trying again. Your data is safe.';
    }
    if (errorCategory === ERROR_CATEGORIES.NETWORK) {
      return 'We\'re having trouble connecting to our servers. Please check your internet connection and try again.';
    }
    if (errorCategory === ERROR_CATEGORIES.PERMISSION) {
      return 'You don\'t have permission to access this resource. Please contact support if you believe this is an error.';
    }
    if (errorCategory === ERROR_CATEGORIES.VALIDATION) {
      return 'The information provided doesn\'t meet our requirements. Please review and try again.';
    }
    return 'We encountered an unexpected error while processing your request. Don\'t worry - your data is safe and we\'re working to fix this issue.';
  }, [customMessage, errorSeverity, errorCategory]);

  // Handle feedback submission
  const handleFeedbackSubmit = () => {
    if (feedbackText.trim()) {
      onFeedbackSubmit?.(feedbackText);
      setFeedbackDialogOpen(false);
      setFeedbackText('');
      announceToScreenReader('Feedback submitted successfully');
    }
  };

  // Announce error to screen reader on mount
  useEffect(() => {
    announceToScreenReader(`Error occurred: ${getErrorTitle()}. ${getErrorDescription()}`);
  }, [announceToScreenReader, getErrorTitle, getErrorDescription]);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.1)} 0%, ${alpha(theme.palette.secondary.light, 0.1)} 100%)`
      }}
      role="alert"
      aria-live="assertive"
    >
      <Card
        sx={{
          maxWidth: 800,
          width: '100%',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.default, 0.95)} 100%)`,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
          borderRadius: 2
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Header Section */}
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            {getErrorIcon()}

            <Typography variant="h4" component="h1" gutterBottom>
              {getErrorTitle()}
            </Typography>

            <Typography variant="body1" color="textSecondary" paragraph>
              {getErrorDescription()}
            </Typography>

            {/* Error Severity Badge */}
            <Chip
              label={`${errorSeverity.toUpperCase()} - ${errorCategory.toUpperCase()}`}
              color={errorSeverity === ERROR_SEVERITY.CRITICAL ? 'error' :
                     errorSeverity === ERROR_SEVERITY.WARNING ? 'warning' :
                     errorSeverity === ERROR_SEVERITY.RECOVERABLE ? 'success' : 'info'}
              sx={{ mb: 2 }}
            />
          </Box>

          {/* Correlation ID */}
          <Alert
            severity="info"
            sx={{
              mb: 3,
              background: alpha(theme.palette.info.light, 0.1)
            }}
          >
            <Typography variant="body2">
              <strong>Error ID:</strong> {correlationId}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              Please include this ID when contacting support
            </Typography>
            {retryCount > 0 && (
              <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                Retry attempts: {retryCount}
              </Typography>
            )}
          </Alert>

          {/* Primary Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap', mb: 3 }}>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={onRetry}
              size="large"
              sx={{
                background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE}, ${ACE_COLORS.YELLOW})`,
                color: 'white',
                '&:focus-visible': {
                  outline: `2px solid ${theme.palette.primary.main}`,
                  outlineOffset: '2px'
                }
              }}
            >
              Try Again
            </Button>

            <Button
              variant="outlined"
              startIcon={<HomeIcon />}
              onClick={onGoHome}
              size="large"
              sx={{
                '&:focus-visible': {
                  outline: `2px solid ${theme.palette.primary.main}`,
                  outlineOffset: '2px'
                }
              }}
            >
              Go Home
            </Button>

            <FeatureGate requiredPlan={3}>
              <Button
                variant="outlined"
                startIcon={<SupportIcon />}
                onClick={onContactSupport}
                color="secondary"
              >
                Priority Support
              </Button>
            </FeatureGate>
          </Box>

          {/* Secondary Actions */}
          <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              size="small"
              startIcon={<FeedbackIcon />}
              onClick={() => setFeedbackDialogOpen(true)}
            >
              Send Feedback
            </Button>

            <FeatureGate requiredPlan={2}>
              <Button
                size="small"
                startIcon={<DownloadIcon />}
                onClick={onExportDiagnostics}
              >
                Export Diagnostics
              </Button>
            </FeatureGate>

            <Button
              size="small"
              startIcon={showTechnicalDetails ? <VisibilityOffIcon /> : <VisibilityIcon />}
              onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
            >
              {showTechnicalDetails ? 'Hide' : 'Show'} Details
            </Button>
          </Box>

          {/* Technical Details */}
          <Collapse in={showTechnicalDetails}>
            <Box sx={{ mt: 3 }}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Technical Details
              </Typography>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
                    {error.message}
                  </Typography>
                </Alert>
              )}
              {process.env.NODE_ENV === 'development' && error?.stack && (
                <Alert severity="warning">
                  <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontSize: '0.7rem', maxHeight: 200, overflow: 'auto' }}>
                    {error.stack}
                  </Typography>
                </Alert>
              )}
            </Box>
          </Collapse>
        </CardContent>
      </Card>

      {/* Feedback Dialog */}
      <Dialog open={feedbackDialogOpen} onClose={() => setFeedbackDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Send Feedback</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="textSecondary" paragraph>
            Help us improve by describing what you were trying to do when this error occurred.
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            value={feedbackText}
            onChange={(e) => setFeedbackText(e.target.value)}
            placeholder="Describe what happened..."
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFeedbackDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleFeedbackSubmit} variant="contained" disabled={!feedbackText.trim()}>
            Send Feedback
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
});

EnhancedErrorFallback.displayName = 'EnhancedErrorFallback';

EnhancedErrorFallback.propTypes = {
  /** Error object */
  error: PropTypes.object,
  /** Unique correlation ID for error tracking */
  correlationId: PropTypes.string.isRequired,
  /** Error severity level */
  errorSeverity: PropTypes.oneOf(Object.values(ERROR_SEVERITY)),
  /** Error category */
  errorCategory: PropTypes.oneOf(Object.values(ERROR_CATEGORIES)),
  /** Number of retry attempts */
  retryCount: PropTypes.number,
  /** Retry callback function */
  onRetry: PropTypes.func.isRequired,
  /** Go home callback function */
  onGoHome: PropTypes.func.isRequired,
  /** Feedback submission callback */
  onFeedbackSubmit: PropTypes.func,
  /** Export diagnostics callback */
  onExportDiagnostics: PropTypes.func,
  /** Contact support callback */
  onContactSupport: PropTypes.func,
  /** Custom error message */
  customMessage: PropTypes.string
};

ErrorBoundary.propTypes = {
  /** Child components to wrap */
  children: PropTypes.node.isRequired,
  /** Custom fallback message */
  fallbackMessage: PropTypes.string,
  /** Error callback function */
  onError: PropTypes.func,
  /** Retry callback function */
  onRetry: PropTypes.func,
  /** Navigate home callback function */
  onNavigateHome: PropTypes.func,
  /** Feedback callback function */
  onFeedback: PropTypes.func,
  /** Contact support callback function */
  onContactSupport: PropTypes.func
};

export default ErrorBoundary;

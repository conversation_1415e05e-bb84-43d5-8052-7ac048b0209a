/**
 * Custom hook for accessibility features
 * Production-ready accessibility utilities with WCAG 2.1 AA compliance
 * Provides screen reader support, keyboard navigation, ARIA announcements, and more
 */

/* eslint-disable @typescript-eslint/no-unused-vars */

import { useCallback, useRef, useEffect, useState } from 'react';
import type { KeyboardEvent } from 'react';

// Configuration constants
const CONFIG = {
  // Announcement delays
  DEFAULT_ANNOUNCE_DELAY: 100,
  CLEAR_ANNOUNCEMENT_DELAY: 1000,

  // Navigation settings
  PAGE_JUMP_SIZE: 10,

  // Focus management
  FOCUS_DELAY: 16, // One frame at 60fps

  // Live region settings
  LIVE_REGION_CLEANUP_DELAY: 5000,

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development'
};

// Enhanced logging utility
const logger = {
  debug: (message: string, data?: unknown) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[useAccessibility] ${message}`, data);
    }
  },
  warn: (message: string, data?: unknown) => {
    console.warn(`[useAccessibility] ${message}`, data);
  },
  error: (message: string, error?: unknown) => {
    console.error(`[useAccessibility] ${message}`, error);
  }
};

// Type definitions
interface AccessibilityOptions {
  enableScreenReaderAnnouncements?: boolean;
  enableKeyboardNavigation?: boolean;
  enableFocusManagement?: boolean;
  announceDelay?: number;
  enableLogging?: boolean;
}

interface NavigationItem {
  id?: string;
  title?: string;
  name?: string;
  label?: string;
  disabled?: boolean;
  [key: string]: unknown;
}

interface SkipLink {
  href: string;
  text: string;
  id?: string;
}

interface LiveRegion {
  // eslint-disable-next-line no-unused-vars
  announce: (message: string) => void;
  remove: () => void;
  // eslint-disable-next-line no-unused-vars
  update: (message: string, priority?: 'polite' | 'assertive') => void;
}

interface AriaAttributes {
  'aria-grabbed'?: boolean;
  'aria-label'?: string;
  'aria-setsize'?: number;
  'aria-posinset'?: number;
  'aria-dropeffect'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-selected'?: boolean;
  'aria-current'?: string;
  [key: string]: unknown;
}

interface AccessibilityState {
  isScreenReaderActive: boolean;
  prefersReducedMotion: boolean;
  highContrastMode: boolean;
  lastAnnouncement: string;
  focusHistory: string[];
}

export const useAccessibility = (options: AccessibilityOptions = {}) => {
  const {
    enableScreenReaderAnnouncements = true,
    enableKeyboardNavigation = true,
    enableFocusManagement = true,
    announceDelay = CONFIG.DEFAULT_ANNOUNCE_DELAY,
    enableLogging = CONFIG.IS_DEVELOPMENT
  } = options;

  // Enhanced state management
  const [accessibilityState, setAccessibilityState] = useState<AccessibilityState>({
    isScreenReaderActive: false,
    prefersReducedMotion: false,
    highContrastMode: false,
    lastAnnouncement: '',
    focusHistory: []
  });

  // Refs for DOM elements and timers
  const announcementRef = useRef<HTMLDivElement | null>(null);
  const lastAnnouncementRef = useRef<string>('');
  const timeoutRef = useRef<number>();
  const liveRegionsRef = useRef<Map<string, HTMLElement>>(new Map());
  const focusHistoryRef = useRef<string[]>([]);

  // Helper function to detect screen reader usage
  const isUsingScreenReader = useCallback((): boolean => {
    try {
      // Multiple heuristics for screen reader detection
      const userAgent = navigator.userAgent.toLowerCase();
      const hasScreenReaderUA = [
        'nvda', 'jaws', 'voiceover', 'talkback', 'orca'
      ].some(sr => userAgent.includes(sr));

      const hasSpeechSynthesis = 'speechSynthesis' in window && window.speechSynthesis?.speaking;
      const hasScreenReaderClass = document.body.classList.contains('screen-reader-mode');
      const hasAriaLiveRegions = document.querySelectorAll('[aria-live]').length > 0;

      return hasScreenReaderUA || !!hasSpeechSynthesis || hasScreenReaderClass || hasAriaLiveRegions;
    } catch (error) {
      logger.error('Error detecting screen reader', error);
      return false;
    }
  }, []);

  // Initialize accessibility state
  useEffect(() => {
    const updateAccessibilityState = () => {
      const newState: AccessibilityState = {
        isScreenReaderActive: isUsingScreenReader(),
        prefersReducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        highContrastMode: window.matchMedia('(prefers-contrast: high)').matches,
        lastAnnouncement: lastAnnouncementRef.current,
        focusHistory: focusHistoryRef.current.slice(-10) // Keep last 10 focus events
      };

      setAccessibilityState(newState);

      if (enableLogging) {
        logger.debug('Accessibility state updated', newState);
      }
    };

    // Initial state
    updateAccessibilityState();

    // Listen for media query changes
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');

    const handleReducedMotionChange = () => updateAccessibilityState();
    const handleHighContrastChange = () => updateAccessibilityState();

    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
    highContrastQuery.addEventListener('change', handleHighContrastChange);

    return () => {
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
      highContrastQuery.removeEventListener('change', handleHighContrastChange);
    };
  }, [enableLogging, isUsingScreenReader]);

  // Create announcement element if it doesn't exist
  useEffect(() => {
    if (!enableScreenReaderAnnouncements) return;

    try {
      if (!announcementRef.current) {
        const element = document.createElement('div');
        element.setAttribute('aria-live', 'polite');
        element.setAttribute('aria-atomic', 'true');
        element.setAttribute('role', 'status');
        element.setAttribute('aria-label', 'Screen reader announcements');
        element.style.position = 'absolute';
        element.style.left = '-10000px';
        element.style.width = '1px';
        element.style.height = '1px';
        element.style.overflow = 'hidden';
        element.style.clipPath = 'inset(50%)';
        element.style.whiteSpace = 'nowrap';
        element.className = 'sr-only accessibility-announcer';
        element.id = 'accessibility-announcer';

        document.body.appendChild(element);
        announcementRef.current = element;

        if (enableLogging) {
          logger.debug('Screen reader announcement element created');
        }
      }
    } catch (error) {
      logger.error('Failed to create announcement element', error);
    }

    return () => {
      try {
        if (announcementRef.current && document.body.contains(announcementRef.current)) {
          document.body.removeChild(announcementRef.current);
          announcementRef.current = null;

          if (enableLogging) {
            logger.debug('Screen reader announcement element removed');
          }
        }
      } catch (error) {
        logger.error('Failed to remove announcement element', error);
      }
    };
  }, [enableScreenReaderAnnouncements, enableLogging]);

  /**
   * Announce message to screen readers with enhanced features
   */
  const announceToScreenReader = useCallback((
    message: string,
    priority: 'polite' | 'assertive' = 'polite',
    options: {
      force?: boolean;
      delay?: number;
      category?: string;
    } = {}
  ) => {
    if (!enableScreenReaderAnnouncements || !announcementRef.current) {
      if (enableLogging) {
        logger.debug('Announcement skipped - screen reader announcements disabled or element not available');
      }
      return;
    }

    const { force = false, delay = announceDelay, category = 'general' } = options;

    // Validate message
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      logger.warn('Invalid announcement message', { message });
      return;
    }

    const trimmedMessage = message.trim();

    // Avoid duplicate announcements unless forced
    if (!force && trimmedMessage === lastAnnouncementRef.current) {
      if (enableLogging) {
        logger.debug('Duplicate announcement prevented', { message: trimmedMessage });
      }
      return;
    }

    lastAnnouncementRef.current = trimmedMessage;

    // Update state
    setAccessibilityState(prev => ({
      ...prev,
      lastAnnouncement: trimmedMessage
    }));

    // Clear previous timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    try {
      // Update aria-live attribute based on priority
      announcementRef.current.setAttribute('aria-live', priority);
      announcementRef.current.setAttribute('data-category', category);

      // Delay announcement to ensure screen readers pick it up
      timeoutRef.current = window.setTimeout(() => {
        if (announcementRef.current) {
          announcementRef.current.textContent = trimmedMessage;

          if (enableLogging) {
            logger.debug('Announcement made', {
              message: trimmedMessage,
              priority,
              category
            });
          }

          // Clear after a delay to allow for new announcements
          setTimeout(() => {
            if (announcementRef.current) {
              announcementRef.current.textContent = '';
            }
          }, CONFIG.CLEAR_ANNOUNCEMENT_DELAY);
        }
      }, delay);
    } catch (error) {
      logger.error('Failed to make announcement', error);
    }
  }, [enableScreenReaderAnnouncements, announceDelay, enableLogging]);

  /**
   * Set focus to a specific element with enhanced tracking
   */
  const setFocusToElement = useCallback((
    element: HTMLElement | string,
    options: {
      preventScroll?: boolean;
      delay?: number;
      announceChange?: boolean;
      focusReason?: string;
    } = {}
  ) => {
    if (!enableFocusManagement) {
      if (enableLogging) {
        logger.debug('Focus management disabled');
      }
      return false;
    }

    const {
      preventScroll = false,
      delay = CONFIG.FOCUS_DELAY,
      announceChange = true,
      focusReason = 'programmatic'
    } = options;

    try {
      const targetElement = typeof element === 'string'
        ? document.querySelector(element) as HTMLElement
        : element;

      if (!targetElement) {
        logger.warn('Focus target element not found', { element });
        return false;
      }

      // Check if element is focusable
      const isFocusable = targetElement.tabIndex >= 0 ||
        ['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA', 'A'].includes(targetElement.tagName) ||
        targetElement.hasAttribute('contenteditable');

      if (!isFocusable) {
        // Make element focusable if it's not already
        targetElement.tabIndex = -1;
      }

      // Use requestAnimationFrame to ensure DOM is ready
      const focusElement = () => {
        try {
          targetElement.focus({ preventScroll });

          // Track focus history
          const elementId = targetElement.id || targetElement.tagName.toLowerCase();
          focusHistoryRef.current.push(elementId);
          if (focusHistoryRef.current.length > 10) {
            focusHistoryRef.current.shift();
          }

          // Update state
          setAccessibilityState(prev => ({
            ...prev,
            focusHistory: [...focusHistoryRef.current]
          }));

          // Announce focus change if enabled
          if (announceChange && enableScreenReaderAnnouncements) {
            const elementLabel = targetElement.getAttribute('aria-label') ||
              targetElement.getAttribute('title') ||
              targetElement.textContent?.trim() ||
              `${targetElement.tagName.toLowerCase()} element`;

            announceToScreenReader(
              `Focus moved to ${elementLabel}`,
              'polite',
              { category: 'focus', delay: 50 }
            );
          }

          if (enableLogging) {
            logger.debug('Focus set successfully', {
              element: elementId,
              reason: focusReason
            });
          }

          return true;
        } catch (error) {
          logger.error('Failed to set focus', error);
          return false;
        }
      };

      if (delay > 0) {
        setTimeout(focusElement, delay);
      } else {
        requestAnimationFrame(focusElement);
      }

      return true;
    } catch (error) {
      logger.error('Error in setFocusToElement', error);
      return false;
    }
  }, [enableFocusManagement, enableLogging, enableScreenReaderAnnouncements, announceToScreenReader]);

  /**
   * Handle keyboard navigation for lists and grids with enhanced features
   */
  const handleKeyboardNavigation = useCallback((
    event: KeyboardEvent,
    items: NavigationItem[],
    currentIndex: number,
    // eslint-disable-next-line no-unused-vars
    onIndexChange: (index: number) => void,
    options: {
      orientation?: 'vertical' | 'horizontal' | 'grid';
      gridColumns?: number;
      wrap?: boolean;
      skipDisabled?: boolean;
      announceNavigation?: boolean;
    } = {}
  ) => {
    if (!enableKeyboardNavigation) {
      if (enableLogging) {
        logger.debug('Keyboard navigation disabled');
      }
      return false;
    }

    const {
      orientation = 'vertical',
      gridColumns,
      wrap = false,
      skipDisabled = true,
      announceNavigation = true
    } = options;

    const { key, ctrlKey, metaKey, shiftKey } = event;
    let newIndex = currentIndex;
    let handled = false;

    // Helper function to find next non-disabled item
    const findNextValidIndex = (startIndex: number, direction: number): number => {
      if (!skipDisabled) return startIndex;

      let index = startIndex;
      let attempts = 0;
      const maxAttempts = items.length;

      while (attempts < maxAttempts) {
        if (index >= 0 && index < items.length && !items[index]?.disabled) {
          return index;
        }

        index += direction;

        if (wrap) {
          if (index >= items.length) index = 0;
          if (index < 0) index = items.length - 1;
        } else {
          if (index >= items.length || index < 0) break;
        }

        attempts++;
      }

      return currentIndex; // Return original if no valid index found
    };

    try {
      switch (key) {
        case 'ArrowDown':
          if (orientation === 'vertical' || orientation === 'grid') {
            event.preventDefault();
            if (orientation === 'grid' && gridColumns) {
              const targetIndex = currentIndex + gridColumns;
              newIndex = wrap && targetIndex >= items.length
                ? findNextValidIndex(targetIndex % items.length, 1)
                : findNextValidIndex(Math.min(targetIndex, items.length - 1), 1);
            } else {
              const targetIndex = currentIndex + 1;
              newIndex = wrap && targetIndex >= items.length
                ? findNextValidIndex(0, 1)
                : findNextValidIndex(Math.min(targetIndex, items.length - 1), 1);
            }
            handled = true;
          }
          break;

        case 'ArrowUp':
          if (orientation === 'vertical' || orientation === 'grid') {
            event.preventDefault();
            if (orientation === 'grid' && gridColumns) {
              const targetIndex = currentIndex - gridColumns;
              newIndex = wrap && targetIndex < 0
                ? findNextValidIndex(items.length + targetIndex, -1)
                : findNextValidIndex(Math.max(targetIndex, 0), -1);
            } else {
              const targetIndex = currentIndex - 1;
              newIndex = wrap && targetIndex < 0
                ? findNextValidIndex(items.length - 1, -1)
                : findNextValidIndex(Math.max(targetIndex, 0), -1);
            }
            handled = true;
          }
          break;

        case 'ArrowLeft':
          if (orientation === 'horizontal' || orientation === 'grid') {
            event.preventDefault();
            const targetIndex = currentIndex - 1;
            newIndex = wrap && targetIndex < 0
              ? findNextValidIndex(items.length - 1, -1)
              : findNextValidIndex(Math.max(targetIndex, 0), -1);
            handled = true;
          }
          break;

        case 'ArrowRight':
          if (orientation === 'horizontal' || orientation === 'grid') {
            event.preventDefault();
            const targetIndex = currentIndex + 1;
            newIndex = wrap && targetIndex >= items.length
              ? findNextValidIndex(0, 1)
              : findNextValidIndex(Math.min(targetIndex, items.length - 1), 1);
            handled = true;
          }
          break;

        case 'Home':
          event.preventDefault();
          newIndex = ctrlKey || metaKey ? 0 : findNextValidIndex(0, 1);
          handled = true;
          break;

        case 'End':
          event.preventDefault();
          newIndex = ctrlKey || metaKey ? items.length - 1 : findNextValidIndex(items.length - 1, -1);
          handled = true;
          break;

        case 'PageDown':
          event.preventDefault();
          newIndex = findNextValidIndex(
            Math.min(currentIndex + CONFIG.PAGE_JUMP_SIZE, items.length - 1),
            1
          );
          handled = true;
          break;

        case 'PageUp':
          event.preventDefault();
          newIndex = findNextValidIndex(
            Math.max(currentIndex - CONFIG.PAGE_JUMP_SIZE, 0),
            -1
          );
          handled = true;
          break;

        // Additional navigation keys
        case 'Tab':
          if (shiftKey) {
            newIndex = findNextValidIndex(Math.max(currentIndex - 1, 0), -1);
          } else {
            newIndex = findNextValidIndex(Math.min(currentIndex + 1, items.length - 1), 1);
          }
          handled = true;
          break;

        case ' ':
        case 'Enter':
          // Let the component handle selection
          handled = false;
          break;

        case 'Escape':
          // Let the component handle escape
          handled = false;
          break;
      }

      if (handled && newIndex !== currentIndex) {
        onIndexChange(newIndex);

        // Announce navigation to screen readers
        if (announceNavigation && enableScreenReaderAnnouncements) {
          const item = items[newIndex];
          if (item) {
            const itemName = item.title || item.name || item.label || `Item ${newIndex + 1}`;
            const positionInfo = `${newIndex + 1} of ${items.length}`;
            const disabledInfo = item.disabled ? ' (disabled)' : '';

            announceToScreenReader(
              `${itemName}, ${positionInfo}${disabledInfo}`,
              'polite',
              { category: 'navigation' }
            );
          }
        }

        if (enableLogging) {
          logger.debug('Keyboard navigation handled', {
            key,
            oldIndex: currentIndex,
            newIndex,
            orientation
          });
        }
      }

      return handled;
    } catch (error) {
      logger.error('Error in keyboard navigation', error);
      return false;
    }
  }, [enableKeyboardNavigation, enableLogging, enableScreenReaderAnnouncements, announceToScreenReader]);

  /**
   * Create ARIA attributes for drag and drop with enhanced features
   */
  const getDragDropAriaAttributes = useCallback((
    isDragging: boolean,
    isDropTarget: boolean,
    itemName: string,
    options: {
      position?: number;
      totalItems?: number;
      dropEffect?: 'copy' | 'move' | 'link' | 'none';
      describedBy?: string;
      expanded?: boolean;
      selected?: boolean;
      current?: string;
    } = {}
  ): AriaAttributes => {
    const {
      position,
      totalItems,
      dropEffect = 'move',
      describedBy = 'drag-instructions',
      expanded,
      selected,
      current
    } = options;

    try {
      const attributes: AriaAttributes = {
        'aria-grabbed': isDragging,
        'aria-label': itemName
      };

      if (position !== undefined && totalItems !== undefined) {
        attributes['aria-setsize'] = totalItems;
        attributes['aria-posinset'] = position + 1;
      }

      if (isDropTarget) {
        attributes['aria-dropeffect'] = dropEffect;
      }

      if (isDragging) {
        attributes['aria-describedby'] = describedBy;
      }

      if (expanded !== undefined) {
        attributes['aria-expanded'] = expanded;
      }

      if (selected !== undefined) {
        attributes['aria-selected'] = selected;
      }

      if (current) {
        attributes['aria-current'] = current;
      }

      if (enableLogging) {
        logger.debug('Generated drag-drop ARIA attributes', {
          itemName,
          isDragging,
          isDropTarget,
          attributes
        });
      }

      return attributes;
    } catch (error) {
      logger.error('Error generating drag-drop ARIA attributes', error);
      return { 'aria-label': itemName };
    }
  }, [enableLogging]);



  /**
   * Generate skip links for better navigation
   */
  const createSkipLinks = useCallback((links: SkipLink[]) => {
    const skipLinksContainer = document.createElement('div');
    skipLinksContainer.className = 'skip-links';
    skipLinksContainer.style.position = 'absolute';
    skipLinksContainer.style.top = '-40px';
    skipLinksContainer.style.left = '6px';
    skipLinksContainer.style.zIndex = '1000';

    links.forEach(link => {
      const skipLink = document.createElement('a');
      skipLink.href = link.href;
      skipLink.textContent = link.text;
      skipLink.className = 'skip-link';
      skipLink.style.position = 'absolute';
      skipLink.style.padding = '8px';
      skipLink.style.backgroundColor = '#000';
      skipLink.style.color = '#fff';
      skipLink.style.textDecoration = 'none';
      skipLink.style.borderRadius = '4px';
      skipLink.style.fontSize = '14px';
      skipLink.style.fontWeight = 'bold';
      skipLink.style.transform = 'translateY(-100%)';
      skipLink.style.transition = 'transform 0.3s';

      skipLink.addEventListener('focus', () => {
        skipLink.style.transform = 'translateY(0)';
      });

      skipLink.addEventListener('blur', () => {
        skipLink.style.transform = 'translateY(-100%)';
      });

      skipLinksContainer.appendChild(skipLink);
    });

    return skipLinksContainer;
  }, []);

  /**
   * Check if user prefers reduced motion
   */
  const prefersReducedMotion = useCallback(() => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }, []);

  /**
   * Create live region for dynamic announcements
   */
  const createLiveRegion = useCallback((
    id: string,
    priority: 'polite' | 'assertive' = 'polite'
  ): LiveRegion => {
    try {
      // Check if region already exists
      let region = liveRegionsRef.current.get(id);

      if (!region) {
        region = document.createElement('div');
        region.setAttribute('aria-live', priority);
        region.setAttribute('aria-atomic', 'true');
        region.setAttribute('role', 'status');
        region.setAttribute('id', id);
        region.style.position = 'absolute';
        region.style.left = '-10000px';
        region.style.width = '1px';
        region.style.height = '1px';
        region.style.overflow = 'hidden';
        region.style.clipPath = 'inset(50%)';
        region.className = 'sr-only live-region';

        document.body.appendChild(region);
        liveRegionsRef.current.set(id, region);

        if (enableLogging) {
          logger.debug('Live region created', { id, priority });
        }
      }

      return {
        announce: (message: string) => {
          if (region) {
            region.textContent = message;
            if (enableLogging) {
              logger.debug('Live region announcement', { id, message });
            }
          }
        },

        update: (message: string, newPriority?: 'polite' | 'assertive') => {
          if (region) {
            if (newPriority) {
              region.setAttribute('aria-live', newPriority);
            }
            region.textContent = message;
            if (enableLogging) {
              logger.debug('Live region updated', { id, message, priority: newPriority });
            }
          }
        },

        remove: () => {
          if (region && document.body.contains(region)) {
            document.body.removeChild(region);
            liveRegionsRef.current.delete(id);
            if (enableLogging) {
              logger.debug('Live region removed', { id });
            }
          }
        }
      };
    } catch (error) {
      logger.error('Error creating live region', error);
      return {
        announce: () => {},
        update: () => {},
        remove: () => {}
      };
    }
  }, [enableLogging]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    const currentLiveRegions = liveRegionsRef.current;
    const currentAnnouncementRef = announcementRef.current;
    const currentTimeoutRef = timeoutRef.current;

    return () => {
      // Clear timeouts
      if (currentTimeoutRef) {
        clearTimeout(currentTimeoutRef);
      }

      // Remove live regions
      currentLiveRegions.forEach((region) => {
        if (document.body.contains(region)) {
          document.body.removeChild(region);
        }
      });
      currentLiveRegions.clear();

      // Remove announcement element
      if (currentAnnouncementRef && document.body.contains(currentAnnouncementRef)) {
        document.body.removeChild(currentAnnouncementRef);
      }

      if (enableLogging) {
        logger.debug('Accessibility hook cleanup completed');
      }
    };
  }, [enableLogging]);

  // Enhanced return object with comprehensive accessibility features
  return {
    // State information
    state: accessibilityState,

    // Core announcement functions
    announceToScreenReader,
    announce: announceToScreenReader, // Alias for convenience

    // Focus management
    setFocusToElement,
    setFocus: setFocusToElement, // Alias for convenience

    // Keyboard navigation
    handleKeyboardNavigation,

    // ARIA attributes
    getDragDropAriaAttributes,
    getAriaAttributes: getDragDropAriaAttributes, // Alias for convenience

    // Live regions
    createLiveRegion,

    // Skip links
    createSkipLinks,

    // Utility functions
    prefersReducedMotion,
    isUsingScreenReader,

    // Enhanced utility functions
    getAccessibilityState: () => accessibilityState,
    clearAnnouncements: () => {
      if (announcementRef.current) {
        announcementRef.current.textContent = '';
      }
      lastAnnouncementRef.current = '';
      setAccessibilityState(prev => ({ ...prev, lastAnnouncement: '' }));
    },

    // Focus history management
    getFocusHistory: () => focusHistoryRef.current.slice(),
    clearFocusHistory: () => {
      focusHistoryRef.current = [];
      setAccessibilityState(prev => ({ ...prev, focusHistory: [] }));
    },

    // Live region management
    removeLiveRegion: (id: string) => {
      const region = liveRegionsRef.current.get(id);
      if (region && document.body.contains(region)) {
        document.body.removeChild(region);
        liveRegionsRef.current.delete(id);
        if (enableLogging) {
          logger.debug('Live region removed', { id });
        }
      }
    },

    removeAllLiveRegions: () => {
      liveRegionsRef.current.forEach((region) => {
        if (document.body.contains(region)) {
          document.body.removeChild(region);
        }
      });
      liveRegionsRef.current.clear();
      if (enableLogging) {
        logger.debug('All live regions removed');
      }
    },

    // Configuration helpers
    isEnabled: (feature: string) => {
      switch (feature) {
        case 'announcements':
          return enableScreenReaderAnnouncements;
        case 'keyboard':
          return enableKeyboardNavigation;
        case 'focus':
          return enableFocusManagement;
        case 'logging':
          return enableLogging;
        default:
          return false;
      }
    },

    // Statistics and debugging
    getStats: () => ({
      announcementsMade: lastAnnouncementRef.current ? 1 : 0,
      focusChanges: focusHistoryRef.current.length,
      liveRegions: liveRegionsRef.current.size,
      lastAnnouncement: lastAnnouncementRef.current,
      isScreenReaderActive: accessibilityState.isScreenReaderActive,
      prefersReducedMotion: accessibilityState.prefersReducedMotion,
      highContrastMode: accessibilityState.highContrastMode
    })
  };
};

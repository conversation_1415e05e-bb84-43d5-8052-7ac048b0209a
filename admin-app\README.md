# ACE Social - Admin Panel

A standalone admin application for managing the ACE Social platform. This application provides administrative functionality separated from the main user-facing application for better security and maintainability.

## Features

- **Dashboard**: System statistics and overview
- **User Management**: View, edit, and manage user accounts
- **Coupon Management**: Create and manage discount coupons
- **AppSumo Management**: Manage AppSumo deals, tiers, and redemption codes
- **System Settings**: Configure platform-wide settings

## Technology Stack

- **Frontend**: React 18 with Material-UI
- **Build Tool**: Vite
- **Routing**: React Router v6
- **HTTP Client**: Axios
- **Date Handling**: date-fns with MUI Date Pickers

## Prerequisites

- Node.js 16+ and npm
- Access to the ACEO backend API
- Admin credentials for the platform

## Installation

1. Navigate to the admin-app directory:
   ```bash
   cd admin-app
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment variables:
   Create a `.env` file in the admin-app directory:
   ```env
   VITE_API_URL=http://localhost:8000
   VITE_ENVIRONMENT=development
   ```

## Development

Start the development server:
```bash
npm run dev
```

The admin panel will be available at `http://localhost:3001`

## Default Admin Credentials

For development environment:
- **Email**: <EMAIL>
- **Password**: Admin@1155

## Building for Production

Build the application:
```bash
npm run build
```

Preview the production build:
```bash
npm run preview
```

## Project Structure

```
admin-app/
├── src/
│   ├── api/                 # API configuration
│   ├── components/          # Reusable components
│   ├── contexts/           # React contexts (Auth)
│   ├── pages/              # Page components
│   │   ├── Dashboard.jsx
│   │   ├── Login.jsx
│   │   ├── CouponManagement.jsx
│   │   ├── AppSumoManagement.jsx
│   │   ├── UserManagement.jsx
│   │   └── SystemSettings.jsx
│   ├── config.js           # Configuration settings
│   ├── App.jsx             # Main app component
│   └── main.jsx            # Entry point
├── package.json
├── vite.config.js
└── README.md
```

## Security Features

- **Admin-only Access**: Only users with admin privileges can access the panel
- **JWT Authentication**: Secure token-based authentication
- **Route Protection**: All routes require admin authentication
- **Automatic Logout**: Redirects to login on token expiration

## API Integration

The admin panel connects to the same backend API as the main application:
- `/api/admin/*` - Admin-specific endpoints
- `/api/auth/*` - Authentication endpoints
- `/api/coupons/*` - Coupon management
- `/api/appsumo/*` - AppSumo management

## Deployment

The admin panel can be deployed independently from the main application:

1. Build the application for production
2. Deploy the `dist` folder to your web server
3. Configure the web server to serve the application on port 3001
4. Ensure the backend API is accessible from the admin panel

## Development vs Production

- **Development**: Runs on port 3001 with hot reload
- **Production**: Optimized build with code splitting and minification
- **Security**: Admin credentials are pre-filled in development only

## Contributing

1. Follow the existing code structure and patterns
2. Use Material-UI components for consistency
3. Implement proper error handling and loading states
4. Test admin functionality thoroughly before deployment

## Support

For issues related to the admin panel, check:
1. Backend API connectivity
2. Admin user permissions
3. Browser console for errors
4. Network requests in developer tools

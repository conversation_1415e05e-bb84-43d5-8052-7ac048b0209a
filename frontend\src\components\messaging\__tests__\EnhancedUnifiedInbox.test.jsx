// @since 2024-1-1 to 2025-25-7
import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import EnhancedUnifiedInbox from '../EnhancedUnifiedInbox';
import { AuthContext } from '../../../contexts/AuthContext';
import { SubscriptionContext } from '../../../hooks/useSubscription';
import { NotificationProvider } from '../../../hooks/useNotification';

// Mock dependencies
jest.mock('react-use-websocket', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    sendJsonMessage: jest.fn(),
    lastJsonMessage: null,
    connectionStatus: 'Open',
  })),
}));

jest.mock('../../../api', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

jest.mock('../../../hooks/useApiError', () => ({
  __esModule: true,
  default: () => ({
    error: null,
    isLoading: false,
    resetError: jest.fn(),
    handleApiRequest: jest.fn((fn, options) => {
      return fn().then(options.onSuccess).catch(options.onError);
    }),
  }),
}));

// Mock child components
jest.mock('../ConversationHeader', () => {
  return function MockConversationHeader({ conversation, onViewInfo, onArchive }) {
    return (
      <div data-testid="conversation-header">
        <span>{conversation?.title}</span>
        <button onClick={() => onViewInfo()}>View Info</button>
        <button onClick={() => onArchive(conversation?.id)}>Archive</button>
      </div>
    );
  };
});

jest.mock('../ConversationInfo', () => {
  return function MockConversationInfo({ conversation, onClose }) {
    return (
      <div data-testid="conversation-info">
        <span>{conversation?.title} Info</span>
        <button onClick={onClose}>Close</button>
      </div>
    );
  };
});

jest.mock('../MessageActions', () => {
  return function MockMessageActions({ message, onReply, onCopy, onDelete }) {
    return (
      <div data-testid="message-actions">
        <button onClick={() => onReply(message)}>Reply</button>
        <button onClick={() => onCopy(message.content)}>Copy</button>
        <button onClick={() => onDelete(message.id)}>Delete</button>
      </div>
    );
  };
});

jest.mock('../MessageInput', () => {
  return function MockMessageInput({ value, onChange, onSend, placeholder, disabled }) {
    return (
      <div data-testid="message-input">
        <input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
        />
        <button onClick={() => onSend(value)} disabled={disabled}>
          Send
        </button>
      </div>
    );
  };
});

jest.mock('../NewConversationDialog', () => {
  return function MockNewConversationDialog({ open, onClose, onConversationCreated }) {
    if (!open) return null;
    return (
      <div data-testid="new-conversation-dialog">
        <button onClick={() => {
          onConversationCreated({ id: 'new-conv', title: 'New Conversation' });
        }}>
          Create
        </button>
        <button onClick={onClose}>Cancel</button>
      </div>
    );
  };
});

jest.mock('../ConversationSentimentIndicator', () => {
  return function MockConversationSentimentIndicator({ sentimentScore }) {
    return <div data-testid="sentiment-indicator">Sentiment: {sentimentScore}</div>;
  };
});

jest.mock('../SentimentAnalysisPanel', () => {
  return function MockSentimentAnalysisPanel({ conversationId }) {
    return <div data-testid="sentiment-panel">Analysis for {conversationId}</div>;
  };
});

// Mock data
const mockUser = {
  id: 'user-1',
  full_name: 'Test User',
  email: '<EMAIL>',
};

const mockConversations = [
  {
    id: 'conv-1',
    title: 'Test Conversation 1',
    is_social_media: false,
    is_archived: false,
    unread_count: 2,
    last_message: { content: 'Hello there' },
    participants: [],
  },
  {
    id: 'conv-2',
    title: 'Social Media Conversation',
    is_social_media: true,
    is_archived: false,
    platform: 'facebook',
    external_participant_name: 'John Doe',
    unread_count: 0,
    last_message: { content: 'Thanks for the help!' },
    participants: [],
  },
];

const mockMessages = [
  {
    id: 'msg-1',
    content: 'Hello there',
    sender_id: 'other-user',
    created_at: '2023-01-01T10:00:00Z',
  },
  {
    id: 'msg-2',
    content: 'Hi! How can I help?',
    sender_id: 'user-1',
    created_at: '2023-01-01T10:01:00Z',
  },
];

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      <AuthContext.Provider value={{ user: mockUser, token: 'test-token' }}>
        <SubscriptionContext.Provider value={{ hasFeatureAccess: () => true }}>
          <NotificationProvider>
            {children}
          </NotificationProvider>
        </SubscriptionContext.Provider>
      </AuthContext.Provider>
    </ThemeProvider>
  </BrowserRouter>
);

describe('EnhancedUnifiedInbox Integration Tests', () => {
  let mockApi;

  beforeEach(() => {
    mockApi = require('../../../api');
    mockApi.get.mockImplementation((url) => {
      if (url === '/api/inbox/conversations') {
        return Promise.resolve({ data: mockConversations });
      }
      if (url === '/api/inbox/messages') {
        return Promise.resolve({ data: mockMessages });
      }
      if (url === '/api/inbox/platform-limits') {
        return Promise.resolve({ data: { can_add_platforms: true } });
      }
      if (url === '/api/inbox/stats') {
        return Promise.resolve({ data: { total_unread: 2 } });
      }
      return Promise.resolve({ data: [] });
    });

    mockApi.post.mockResolvedValue({ data: { success: true } });
    mockApi.put.mockResolvedValue({ data: { success: true } });
    mockApi.delete.mockResolvedValue({ data: { success: true } });

    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn(),
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders inbox with conversations list', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Inbox')).toBeInTheDocument();
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
      expect(screen.getByText('Social Media Conversation')).toBeInTheDocument();
    });
  });

  test('selects conversation and displays messages', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Click on conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
      expect(screen.getByText('Hello there')).toBeInTheDocument();
      expect(screen.getByText('Hi! How can I help?')).toBeInTheDocument();
    });
  });

  test('opens conversation info drawer', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });

    // Click view info button
    fireEvent.click(screen.getByText('View Info'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-info')).toBeInTheDocument();
    });
  });

  test('sends message using MessageInput component', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('message-input')).toBeInTheDocument();
    });

    // Type message
    const input = screen.getByPlaceholderText('Type your message...');
    await user.type(input, 'Test message');

    // Send message
    fireEvent.click(screen.getByText('Send'));

    await waitFor(() => {
      expect(mockApi.post).toHaveBeenCalledWith('/messaging/messages', {
        conversation_id: 'conv-1',
        content: 'Test message',
        attachments: [],
      });
    });
  });

  test('handles message actions (reply, copy, delete)', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getAllByTestId('message-actions')).toHaveLength(2);
    });

    // Test copy action
    fireEvent.click(screen.getAllByText('Copy')[0]);
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Hello there');

    // Test reply action
    fireEvent.click(screen.getAllByText('Reply')[0]);
    // Reply state should be set (tested through placeholder change)

    // Test delete action
    fireEvent.click(screen.getAllByText('Delete')[0]);
    await waitFor(() => {
      expect(mockApi.delete).toHaveBeenCalledWith('/api/inbox/messages/msg-1');
    });
  });

  test('creates new conversation', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('New Conversation')).toBeInTheDocument();
    });

    // Click new conversation button
    fireEvent.click(screen.getByText('New Conversation'));

    await waitFor(() => {
      expect(screen.getByTestId('new-conversation-dialog')).toBeInTheDocument();
    });

    // Create conversation
    fireEvent.click(screen.getByText('Create'));

    await waitFor(() => {
      expect(screen.getByText('New Conversation')).toBeInTheDocument();
    });
  });

  test('filters conversations by tab', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
      expect(screen.getByText('Social Media Conversation')).toBeInTheDocument();
    });

    // Click social tab
    fireEvent.click(screen.getByText('Social'));

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/inbox/conversations', {
        params: {
          include_archived: false,
          platform_filter: 'social',
          search_query: null,
        },
      });
    });
  });

  test('searches conversations', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search conversations...')).toBeInTheDocument();
    });

    // Type in search
    const searchInput = screen.getByPlaceholderText('Search conversations...');
    await user.type(searchInput, 'Social');

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/inbox/conversations', {
        params: {
          include_archived: false,
          platform_filter: null,
          search_query: 'Social',
        },
      });
    });
  });

  test('handles keyboard navigation (Escape key)', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation and open info
    fireEvent.click(screen.getByText('Test Conversation 1'));
    
    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('View Info'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-info')).toBeInTheDocument();
    });

    // Press Escape key
    fireEvent.keyDown(document, { key: 'Escape' });

    await waitFor(() => {
      expect(screen.queryByTestId('conversation-info')).not.toBeInTheDocument();
    });
  });

  test('handles mobile responsiveness', async () => {
    // Mock mobile breakpoint
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query.includes('(max-width: 900px)'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByText('Back to Conversations')).toBeInTheDocument();
    });
  });

  test('handles error states gracefully', async () => {
    mockApi.get.mockRejectedValueOnce(new Error('Network error'));

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    // Should handle error gracefully without crashing
    await waitFor(() => {
      expect(screen.getByText('Inbox')).toBeInTheDocument();
    });
  });

  test('accessibility features work correctly', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      // Check ARIA labels and roles
      expect(screen.getByRole('log')).toBeInTheDocument();
      expect(screen.getByLabelText('Conversation messages')).toBeInTheDocument();
      expect(screen.getByLabelText('Loading messages')).toBeInTheDocument() ||
        expect(screen.getByRole('status')).toBeInTheDocument();
    });
  });

  test('performance optimization with memoization works', async () => {
    const renderSpy = jest.fn();

    // Mock React.memo to track renders
    const OriginalMemo = React.memo;
    React.memo = jest.fn((component) => {
      return OriginalMemo((props) => {
        renderSpy();
        return component(props);
      });
    });

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    // Verify memoized components are being used
    expect(renderSpy).toHaveBeenCalled();

    // Restore original React.memo
    React.memo = OriginalMemo;
  });

  test('WebSocket integration handles real-time messages', async () => {
    const mockWebSocket = require('react-use-websocket');
    const mockSendJsonMessage = jest.fn();

    mockWebSocket.default.mockReturnValue({
      sendJsonMessage: mockSendJsonMessage,
      lastJsonMessage: {
        type: 'new_message',
        data: {
          id: 'new-msg',
          content: 'New real-time message',
          sender_id: 'other-user',
          conversation_id: 'conv-1',
          created_at: new Date().toISOString(),
        },
      },
      connectionStatus: 'Open',
    });

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      // Should display the new real-time message
      expect(screen.getByText('New real-time message')).toBeInTheDocument();
    });
  });

  test('correlation ID tracking for error handling', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

    mockApi.post.mockRejectedValueOnce(new Error('Send failed'));

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('message-input')).toBeInTheDocument();
    });

    // Try to send message that will fail
    const input = screen.getByPlaceholderText('Type your message...');
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(screen.getByText('Send'));

    await waitFor(() => {
      // Should log correlation ID on error
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringMatching(/Message send failed \[msg_\d+_\w+\]:/),
        expect.any(Error)
      );
    });

    consoleSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  test('conversation management actions work correctly', async () => {
    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    });

    // Test archive action
    fireEvent.click(screen.getByText('Archive'));

    await waitFor(() => {
      expect(mockApi.post).toHaveBeenCalledWith('/api/inbox/conversations/conv-1/archive');
    });
  });

  test('loading states display correctly', async () => {
    // Mock loading state
    const mockUseApiError = require('../../../hooks/useApiError');
    mockUseApiError.default.mockReturnValue({
      error: null,
      isLoading: true,
      resetError: jest.fn(),
      handleApiRequest: jest.fn(),
    });

    render(
      <TestWrapper>
        <EnhancedUnifiedInbox />
      </TestWrapper>
    );

    // Should show loading indicator
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('subscription-based feature access control', async () => {
    // Mock limited subscription
    const TestWrapperLimited = ({ children }) => (
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <AuthContext.Provider value={{ user: mockUser, token: 'test-token' }}>
            <SubscriptionContext.Provider value={{ hasFeatureAccess: () => false }}>
              <NotificationProvider>
                {children}
              </NotificationProvider>
            </SubscriptionContext.Provider>
          </AuthContext.Provider>
        </ThemeProvider>
      </BrowserRouter>
    );

    render(
      <TestWrapperLimited>
        <EnhancedUnifiedInbox />
      </TestWrapperLimited>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Conversation 1')).toBeInTheDocument();
    });

    // Select conversation
    fireEvent.click(screen.getByText('Test Conversation 1'));

    await waitFor(() => {
      // Sentiment features should not be visible
      expect(screen.queryByTestId('sentiment-indicator')).not.toBeInTheDocument();
      expect(screen.queryByTestId('sentiment-panel')).not.toBeInTheDocument();
    });
  });
});

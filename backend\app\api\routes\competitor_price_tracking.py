"""
API routes for competitor price tracking functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from app.core.security import get_current_active_user
from app.models.user import User
from app.services.competitor_service import CompetitorService
from app.api.dependencies.feature_access import requires_feature
from app.schemas.competitor import CompetitorPriceTrackingRequest, CompetitorPriceAlert

router = APIRouter()

@router.get("", response_model=Dict[str, Any])
async def competitor_price_tracking_root():
    """
    Root endpoint for Competitor Price Tracking API with system information.
    """
    return {
        "message": "Competitor Price Tracking API",
        "version": "1.0.0",
        "features": [
            "real_time_price_monitoring",
            "price_change_alerts",
            "competitive_positioning",
            "price_history_tracking",
            "automated_notifications",
            "multi_currency_support"
        ],
        "endpoints": [
            "/track",
            "/alerts",
            "/alerts/{alert_id}",
            "/stop/{competitor_id}",
            "/history/{competitor_id}",
            "/positioning/{competitor_id}"
        ],
        "status": "active"
    }

@router.post("/track", response_model=Dict[str, Any])
async def start_price_tracking(
    request: CompetitorPriceTrackingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(requires_feature("competitor_price_tracking"))
):
    """
    Start tracking prices for competitor products.
    
    Requires competitor_price_tracking feature access.
    """
    try:
        competitor_service = CompetitorService()
        
        # Convert products to dict format
        products_dict = [product.model_dump() for product in request.products]

        # Start price tracking
        result = await competitor_service.track_competitor_prices(
            request.competitor_id,
            str(current_user.id),
            products_dict
        )
        
        # Schedule background price checking
        background_tasks.add_task(
            schedule_price_checks,
            request.competitor_id,
            str(current_user.id),
            request.check_frequency
        )
        
        return result
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start price tracking: {str(e)}"
        )

@router.get("/alerts", response_model=List[CompetitorPriceAlert])
async def get_price_alerts(
    competitor_id: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get price alerts for user's tracked competitors.
    """
    try:
        competitor_service = CompetitorService()
        
        alerts = await competitor_service.get_price_alerts(
            str(current_user.id),
            competitor_id
        )
        
        return alerts
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get price alerts: {str(e)}"
        )

@router.post("/alerts/{alert_id}/read", response_model=Dict[str, Any])
async def mark_alert_as_read(
    alert_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Mark a price alert as read.
    """
    try:
        from app.db.mongodb import get_database
        from bson import ObjectId
        
        db = await get_database()
        
        # Update alert status
        result = await db.competitor_price_alerts.update_one(
            {
                "_id": ObjectId(alert_id),
                "user_id": str(current_user.id)
            },
            {
                "$set": {
                    "is_read": True,
                    "read_at": datetime.now(timezone.utc)
                }
            }
        )
        
        if result.matched_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Alert not found"
            )
        
        return {
            "status": "success",
            "message": "Alert marked as read"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark alert as read: {str(e)}"
        )

@router.delete("/stop/{competitor_id}", response_model=Dict[str, Any])
async def stop_price_tracking(
    competitor_id: str,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(requires_feature("competitor_price_tracking"))
):
    """
    Stop tracking prices for a competitor.
    """
    try:
        from app.db.mongodb import get_database
        from app.core.redis import get_redis_client
        
        db = await get_database()
        redis_client = await get_redis_client()
        
        # Update tracking status in MongoDB
        result = await db.competitor_price_tracking.update_one(
            {
                "competitor_id": competitor_id,
                "user_id": str(current_user.id)
            },
            {
                "$set": {
                    "tracking_enabled": False,
                    "stopped_at": datetime.now(timezone.utc)
                }
            }
        )
        
        if result.matched_count == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Price tracking not found for this competitor"
            )
        
        # Remove from Redis cache
        if redis_client:
            cache_key = f"price_tracking:{competitor_id}:{current_user.id}"
            await redis_client.delete(cache_key)
        
        return {
            "status": "success",
            "message": "Price tracking stopped",
            "competitor_id": competitor_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop price tracking: {str(e)}"
        )

@router.get("/history/{competitor_id}", response_model=Dict[str, Any])
async def get_price_history(
    competitor_id: str,
    product_id: Optional[str] = None,
    days: int = 30,
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(requires_feature("competitor_price_tracking"))
):
    """
    Get price history for competitor products.
    """
    try:
        from app.db.mongodb import get_database
        from datetime import timedelta
        
        db = await get_database()
        
        # Build query
        query = {
            "competitor_id": competitor_id,
            "user_id": str(current_user.id),
            "created_at": {
                "$gte": datetime.now(timezone.utc) - timedelta(days=days)
            }
        }
        
        if product_id:
            query["product_id"] = product_id
        
        # Get price history
        history = await db.competitor_price_history.find(query).sort("created_at", 1).to_list(1000)
        
        return {
            "competitor_id": competitor_id,
            "product_id": product_id,
            "days": days,
            "price_history": [
                {
                    "product_id": item.get("product_id"),
                    "product_name": item.get("product_name"),
                    "price": item.get("price"),
                    "currency": item.get("currency", "USD"),
                    "source_url": item.get("source_url"),
                    "checked_at": item["created_at"]
                }
                for item in history
            ],
            "total_records": len(history)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get price history: {str(e)}"
        )

async def schedule_price_checks(competitor_id: str, user_id: str, frequency: str):
    """
    Background task to schedule regular price checks.
    """
    try:
        # This would integrate with a task scheduler like Celery
        # For now, we'll just log the scheduling
        import logging
        logger = logging.getLogger(__name__)
        
        logger.info(f"Scheduled {frequency} price checks for competitor {competitor_id} (user: {user_id})")
        
        # In production, this would:
        # 1. Add task to Celery queue
        # 2. Set up recurring schedule based on frequency
        # 3. Handle task retries and error handling
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to schedule price checks: {str(e)}")

/**
 * Advanced Toast Hook
 * Production-ready hook with advanced features for toast notifications
 *
 * Features:
 * - Rate limiting to prevent spam
 * - Deduplication to avoid duplicate messages
 * - Network retry with exponential backoff
 * - Concurrent operation management
 * - Comprehensive logging and analytics
 * - Progress tracking for long operations
 * - Batch operations support
 * - Health monitoring and configuration access
 *
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 */

import { useCallback, useRef, useEffect, useMemo } from 'react';
import { useEnhancedToast } from '../contexts/EnhancedToastContext';
import {
  ToastRateLimiter,
  NetworkFailureHandler,
  ToastDeduplicator,
  ConcurrentNotificationManager,
  sanitizeToastMessage,
  getToastDuration,
  isNetworkError,
} from '../utils/toastUtils';
import { ToastConfig, NetworkFailureConfig } from '../types/toast';

// Type declaration for analytics
declare global {
  interface Window {
    analytics?: {
      // eslint-disable-next-line no-unused-vars
      track: (event: string, properties: Record<string, unknown>) => void;
    };
  }
}

// Enhanced logging utility for production-ready toast management
const logger = {
  debug: (message: string, data?: unknown) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[useAdvancedToast] ${message}`, data);
    }
  },
  info: (message: string, data?: unknown) => {
    if (process.env.NODE_ENV === 'development') {
      console.info(`[useAdvancedToast] ${message}`, data);
    }

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production' && window.analytics) {
      window.analytics.track('Advanced Toast Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message: string, data?: unknown) => {
    console.warn(`[useAdvancedToast] ${message}`, data);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Advanced Toast Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message: string, error?: unknown) => {
    console.error(`[useAdvancedToast] ${message}`, error);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Advanced Toast Error', {
        message,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });
    }
  }
};

interface AdvancedToastOptions {
  enableRateLimiting?: boolean;
  enableDeduplication?: boolean;
  enableNetworkRetry?: boolean;
  maxConcurrentOperations?: number;
  rateLimitWindow?: number;
  rateLimitMax?: number;
  dedupeWindow?: number;
  networkRetryConfig?: Partial<NetworkFailureConfig>;
}

const defaultOptions: AdvancedToastOptions = {
  enableRateLimiting: true,
  enableDeduplication: true,
  enableNetworkRetry: true,
  maxConcurrentOperations: 5,
  rateLimitWindow: 5000,
  rateLimitMax: 3,
  dedupeWindow: 3000,
  networkRetryConfig: {
    enableRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
  },
};

export const useAdvancedToast = (options: AdvancedToastOptions = {}) => {
  const mergedOptions = useMemo(() => ({ ...defaultOptions, ...options }), [options]);
  const baseToast = useEnhancedToast();
  
  // Initialize utility classes
  const rateLimiter = useRef(
    new ToastRateLimiter(mergedOptions.rateLimitWindow, mergedOptions.rateLimitMax)
  );
  const deduplicator = useRef(new ToastDeduplicator(mergedOptions.dedupeWindow));
  const concurrentManager = useRef(
    new ConcurrentNotificationManager(mergedOptions.maxConcurrentOperations)
  );
  const networkHandler = useRef(
    new NetworkFailureHandler({
      enableRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true,
      ...mergedOptions.networkRetryConfig,
    })
  );

  // Cleanup intervals
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      deduplicator.current.cleanup();
    }, 30000); // Cleanup every 30 seconds

    return () => clearInterval(cleanupInterval);
  }, []);

  // Enhanced show toast with all advanced features
  const showAdvancedToast = useCallback(
    (config: Omit<ToastConfig, 'id'>): string | null => {
      // Sanitize message
      const sanitizedMessage = sanitizeToastMessage(config.message);
      const enhancedConfig: Omit<ToastConfig, 'id'> = {
        ...config,
        message: sanitizedMessage,
        duration: config.duration || getToastDuration(config.type, sanitizedMessage.length),
      };

      // Rate limiting check
      if (mergedOptions.enableRateLimiting && !rateLimiter.current.canShow(sanitizedMessage)) {
        logger.warn('Toast rate limited', { message: sanitizedMessage });
        return null;
      }

      // Deduplication check
      if (mergedOptions.enableDeduplication && !deduplicator.current.shouldShow(enhancedConfig)) {
        logger.warn('Toast deduplicated', { message: sanitizedMessage });
        return null;
      }

      // Concurrent operations check
      const operationId = `toast-${Date.now()}`;
      if (!concurrentManager.current.canStart(operationId)) {
        logger.warn('Too many concurrent toast operations', { operationId });
        return null;
      }

      try {
        logger.debug('Showing advanced toast', {
          type: enhancedConfig.type,
          message: sanitizedMessage,
          operationId
        });

        const toastId = baseToast.showToast(enhancedConfig);

        // Complete the operation
        setTimeout(() => {
          concurrentManager.current.complete(operationId);
        }, 100);

        logger.info('Advanced toast shown successfully', {
          toastId,
          type: enhancedConfig.type,
          operationId
        });

        return toastId;
      } catch (error) {
        concurrentManager.current.complete(operationId);
        logger.error('Failed to show toast', error);
        return null;
      }
    },
    [baseToast, mergedOptions]
  );

  // Network-aware toast methods
  const showNetworkToast = useCallback(
    async <T = unknown>(
      operation: () => Promise<T>,
      config: Omit<ToastConfig, 'id'>,
      operationId?: string
    ): Promise<string | null> => {
      const id = operationId || `network-op-${Date.now()}`;

      try {
        await networkHandler.current.handleFailure(operation, id, (error) => {
          // Show error toast on network failure
          showAdvancedToast({
            type: 'error',
            title: 'Network Error',
            message: error.message,
            persistent: true,
            actions: [
              {
                label: 'Retry',
                onClick: () => showNetworkToast(operation, config, id),
              },
            ],
          });
        });

        // Show success toast
        return showAdvancedToast(config);
      } catch (error) {
        // Handle non-retryable errors
        if (isNetworkError(error)) {
          return showAdvancedToast({
            type: 'error',
            title: 'Connection Failed',
            message: 'Please check your internet connection and try again.',
            persistent: true,
          });
        }

        return showAdvancedToast({
          type: 'error',
          message: error instanceof Error ? error.message : 'An error occurred',
        });
      }
    },
    [showAdvancedToast]
  );

  // Enhanced convenience methods
  const showSuccess = useCallback(
    (message: string, options: Partial<ToastConfig> = {}) => {
      return showAdvancedToast({ ...options, type: 'success', message });
    },
    [showAdvancedToast]
  );

  const showError = useCallback(
    (message: string, options: Partial<ToastConfig> = {}) => {
      return showAdvancedToast({ ...options, type: 'error', message });
    },
    [showAdvancedToast]
  );

  const showWarning = useCallback(
    (message: string, options: Partial<ToastConfig> = {}) => {
      return showAdvancedToast({ ...options, type: 'warning', message });
    },
    [showAdvancedToast]
  );

  const showInfo = useCallback(
    (message: string, options: Partial<ToastConfig> = {}) => {
      return showAdvancedToast({ ...options, type: 'info', message });
    },
    [showAdvancedToast]
  );

  // Batch operations
  const showBatch = useCallback(
    (configs: Array<Omit<ToastConfig, 'id'>>, delay: number = 500): string[] => {
      const ids: string[] = [];
      
      configs.forEach((config, index) => {
        setTimeout(() => {
          const id = showAdvancedToast(config);
          if (id) ids.push(id);
        }, index * delay);
      });

      return ids;
    },
    [showAdvancedToast]
  );

  // Progress toast for long operations
  const showProgress = useCallback(
    <T = unknown>(
      operation: () => Promise<T>,
      config: {
        loadingMessage: string;
        successMessage: string;
        errorMessage?: string;
        title?: string;
      }
    ): Promise<T> => {
      const loadingId = showAdvancedToast({
        type: 'info',
        ...(config.title && { title: config.title }),
        message: config.loadingMessage,
        persistent: true,
        dismissible: false,
      });

      return operation()
        .then((result) => {
          if (loadingId) {
            baseToast.dismissToast(loadingId);
          }
          showAdvancedToast({
            type: 'success',
            ...(config.title && { title: config.title }),
            message: config.successMessage,
          });
          return result;
        })
        .catch((error) => {
          if (loadingId) {
            baseToast.dismissToast(loadingId);
          }
          showAdvancedToast({
            type: 'error',
            ...(config.title && { title: config.title }),
            message: config.errorMessage || (error instanceof Error ? error.message : 'Operation failed'),
          });
          throw error;
        });
    },
    [showAdvancedToast, baseToast]
  );

  // Utility methods
  const resetRateLimit = useCallback((message?: string) => {
    rateLimiter.current.reset(message);
  }, []);

  const clearDeduplication = useCallback(() => {
    deduplicator.current.clear();
  }, []);

  const getStats = useCallback(() => {
    return {
      activeOperations: concurrentManager.current.getActiveCount(),
      maxConcurrent: mergedOptions.maxConcurrentOperations,
      rateLimitEnabled: mergedOptions.enableRateLimiting,
      deduplicationEnabled: mergedOptions.enableDeduplication,
      networkRetryEnabled: mergedOptions.enableNetworkRetry,
    };
  }, [mergedOptions]);

  // Enhanced utility methods for production monitoring
  const getConfiguration = useCallback(() => {
    return {
      ...mergedOptions,
      version: '1.0.0',
      features: {
        rateLimiting: mergedOptions.enableRateLimiting,
        deduplication: mergedOptions.enableDeduplication,
        networkRetry: mergedOptions.enableNetworkRetry,
        concurrentOperations: mergedOptions.maxConcurrentOperations,
      }
    };
  }, [mergedOptions]);

  const healthCheck = useCallback(() => {
    const stats = getStats();
    const maxConcurrent = stats.maxConcurrent || 5; // Default fallback
    const isHealthy = stats.activeOperations < maxConcurrent;

    logger.debug('Advanced toast health check', {
      isHealthy,
      activeOperations: stats.activeOperations,
      maxConcurrent
    });

    return {
      isHealthy,
      status: isHealthy ? 'healthy' : 'degraded',
      activeOperations: stats.activeOperations,
      maxConcurrent,
      timestamp: new Date().toISOString()
    };
  }, [getStats]);

  return {
    // Enhanced methods
    showToast: showAdvancedToast,
    showNetworkToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showBatch,
    showProgress,

    // Base methods (pass-through)
    dismissToast: baseToast.dismissToast,
    dismissAll: baseToast.dismissAll,
    updateToast: baseToast.updateToast,

    // Queue management
    pauseQueue: baseToast.pauseQueue,
    resumeQueue: baseToast.resumeQueue,
    clearQueue: baseToast.clearQueue,

    // Utility methods
    resetRateLimit,
    clearDeduplication,
    getStats,
    getConfiguration,
    healthCheck,

    // State
    toasts: baseToast.toasts,
    queue: baseToast.queue,
    isLoading: baseToast.isLoading,
    error: baseToast.error,
    options: baseToast.options,

    // Enhanced state information
    isHealthy: () => healthCheck().isHealthy,
    activeOperationsCount: () => getStats().activeOperations,
    configuration: getConfiguration(),
  };
};

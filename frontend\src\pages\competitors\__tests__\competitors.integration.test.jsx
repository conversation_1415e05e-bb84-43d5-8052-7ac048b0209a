import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import UnifiedCompetitorsPage from '../UnifiedCompetitorsPage';
import CompetitorDetailPage from '../CompetitorDetailPage';
import CompetitorFormPage from '../CompetitorFormPage';
import { NotificationProvider } from '../../../contexts/NotificationContext';
import { CompetitorProvider } from '../../../contexts/CompetitorContext';

// Mock server setup
const server = setupServer(
  rest.get('/api/competitors/', (req, res, ctx) => {
    return res(ctx.json([
      {
        id: '1',
        name: 'TechCorp',
        website: 'https://techcorp.com',
        platforms: ['linkedin', 'twitter'],
        status: 'active'
      },
      {
        id: '2',
        name: 'InnovateCo',
        website: 'https://innovateco.com',
        platforms: ['linkedin', 'facebook'],
        status: 'active'
      }
    ]));
  }),

  rest.get('/api/competitors/:id', (req, res, ctx) => {
    const { id } = req.params;
    return res(ctx.json({
      id,
      name: id === '1' ? 'TechCorp' : 'InnovateCo',
      website: id === '1' ? 'https://techcorp.com' : 'https://innovateco.com',
      platforms: ['linkedin', 'twitter'],
      status: 'active',
      metrics: {
        followers: 10000,
        engagement_rate: 3.5,
        posts_per_week: 5
      }
    }));
  }),

  rest.post('/api/competitors/', (req, res, ctx) => {
    return res(ctx.json({
      id: '3',
      name: 'NewCompetitor',
      website: 'https://newcompetitor.com',
      platforms: ['linkedin'],
      status: 'active'
    }));
  }),

  rest.put('/api/competitors/:id', (req, res, ctx) => {
    const { id } = req.params;
    return res(ctx.json({
      id,
      name: 'Updated Competitor',
      website: 'https://updated.com',
      platforms: ['linkedin', 'twitter'],
      status: 'active'
    }));
  }),

  rest.delete('/api/competitors/:id', (req, res, ctx) => {
    return res(ctx.status(204));
  }),

  rest.post('/api/competitors/compare', (req, res, ctx) => {
    return res(ctx.json({
      competitors: [
        { id: '1', name: 'TechCorp', engagement_rate: 3.5 },
        { id: '2', name: 'InnovateCo', engagement_rate: 2.8 }
      ],
      best_performer: 'TechCorp',
      insights: ['TechCorp has higher engagement', 'Focus on video content']
    }));
  })
);

// Mock child components that aren't the focus of integration tests
jest.mock('../../../components/competitors/CompetitorList', () => {
  return function MockCompetitorList() {
    return (
      <div data-testid="competitor-list">
        <div data-testid="competitor-item-1">TechCorp</div>
        <div data-testid="competitor-item-2">InnovateCo</div>
        <button data-testid="add-competitor-btn">Add Competitor</button>
        <button data-testid="compare-competitors-btn">Compare Selected</button>
      </div>
    );
  };
});

jest.mock('../../../components/competitors/CompetitorComparison', () => {
  return function MockCompetitorComparison() {
    return (
      <div data-testid="competitor-comparison">
        <div data-testid="comparison-chart">Comparison Chart</div>
        <div data-testid="comparison-insights">Insights</div>
      </div>
    );
  };
});

jest.mock('../../../components/competitors/CompetitorDetail', () => {
  return function MockCompetitorDetail({ competitorId, onNameUpdate }) {
    React.useEffect(() => {
      if (onNameUpdate) {
        onNameUpdate(competitorId === '1' ? 'TechCorp' : 'InnovateCo');
      }
    }, [competitorId, onNameUpdate]);

    return (
      <div data-testid="competitor-detail">
        <h2>{competitorId === '1' ? 'TechCorp' : 'InnovateCo'} Details</h2>
        <div data-testid="competitor-metrics">Metrics</div>
        <button data-testid="edit-competitor-btn">Edit</button>
      </div>
    );
  };
});

jest.mock('../../../components/competitors/CompetitorForm', () => {
  return function MockCompetitorForm({ competitorId, isEditMode, onSubmit, onNameUpdate }) {
    React.useEffect(() => {
      if (onNameUpdate && competitorId) {
        onNameUpdate(competitorId === '1' ? 'TechCorp' : 'InnovateCo');
      }
    }, [competitorId, onNameUpdate]);

    return (
      <div data-testid="competitor-form">
        <h2>{isEditMode ? 'Edit' : 'Add'} Competitor</h2>
        <input data-testid="competitor-name" placeholder="Competitor Name" />
        <input data-testid="competitor-website" placeholder="Website" />
        <button 
          data-testid="submit-form-btn"
          onClick={() => onSubmit && onSubmit()}
        >
          {isEditMode ? 'Update' : 'Create'} Competitor
        </button>
      </div>
    );
  };
});

// Test wrapper
const TestWrapper = ({ children, initialRoute = '/' }) => {
  const theme = createTheme();
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <NotificationProvider>
          <CompetitorProvider>
            {children}
          </CompetitorProvider>
        </NotificationProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Competitors Integration Tests', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  describe('UnifiedCompetitorsPage Integration', () => {
    test('loads and displays competitor list', async () => {
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('competitor-list')).toBeInTheDocument();
      });

      expect(screen.getByTestId('competitor-item-1')).toHaveTextContent('TechCorp');
      expect(screen.getByTestId('competitor-item-2')).toHaveTextContent('InnovateCo');
    });

    test('switches between list and comparison views', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      // Should start with list view
      await waitFor(() => {
        expect(screen.getByTestId('competitor-list')).toBeInTheDocument();
      });

      // Switch to comparison view
      const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
      await user.click(compareTab);

      await waitFor(() => {
        expect(screen.getByTestId('competitor-comparison')).toBeInTheDocument();
      });

      expect(screen.getByTestId('comparison-chart')).toBeInTheDocument();
      expect(screen.getByTestId('comparison-insights')).toBeInTheDocument();
    });

    test('handles navigation between different competitor pages', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      // Test breadcrumb navigation
      const homeLink = screen.getByLabelText(/navigate to dashboard/i);
      expect(homeLink).toBeInTheDocument();

      // Test add competitor button
      const addButton = screen.getByLabelText(/add new competitor/i);
      expect(addButton).toBeInTheDocument();
    });
  });

  describe('CompetitorDetailPage Integration', () => {
    test('loads and displays competitor details', async () => {
      render(
        <TestWrapper>
          <CompetitorDetailPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('competitor-detail')).toBeInTheDocument();
      });

      expect(screen.getByText(/details/i)).toBeInTheDocument();
      expect(screen.getByTestId('competitor-metrics')).toBeInTheDocument();
    });

    test('updates page title with competitor name', async () => {
      render(
        <TestWrapper>
          <CompetitorDetailPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(document.title).toContain('Competitor Details');
      });
    });

    test('handles competitor not found error', async () => {
      server.use(
        rest.get('/api/competitors/:id', (req, res, ctx) => {
          return res(ctx.status(404), ctx.json({ error: 'Competitor not found' }));
        })
      );

      render(
        <TestWrapper>
          <CompetitorDetailPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/failed to load competitor details/i)).toBeInTheDocument();
      });
    });
  });

  describe('CompetitorFormPage Integration', () => {
    test('renders add competitor form', async () => {
      render(
        <TestWrapper>
          <CompetitorFormPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('competitor-form')).toBeInTheDocument();
      });

      expect(screen.getByText('Add Competitor')).toBeInTheDocument();
      expect(screen.getByTestId('competitor-name')).toBeInTheDocument();
      expect(screen.getByTestId('competitor-website')).toBeInTheDocument();
    });

    test('renders edit competitor form with existing data', async () => {
      // Mock useParams to return competitor ID
      jest.doMock('react-router-dom', () => ({
        ...jest.requireActual('react-router-dom'),
        useParams: () => ({ id: '1' })
      }));

      render(
        <TestWrapper>
          <CompetitorFormPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('competitor-form')).toBeInTheDocument();
      });

      expect(screen.getByText('Edit Competitor')).toBeInTheDocument();
    });

    test('handles form submission successfully', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CompetitorFormPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('competitor-form')).toBeInTheDocument();
      });

      const submitButton = screen.getByTestId('submit-form-btn');
      await user.click(submitButton);

      // Form submission would trigger navigation
      expect(submitButton).toBeInTheDocument();
    });
  });

  describe('Error Handling Integration', () => {
    test('handles API errors gracefully', async () => {
      server.use(
        rest.get('/api/competitors/', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ error: 'Server error' }));
        })
      );

      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/failed to load competitors/i)).toBeInTheDocument();
      });

      expect(screen.getByLabelText(/retry/i)).toBeInTheDocument();
    });

    test('handles network timeouts', async () => {
      server.use(
        rest.get('/api/competitors/', (req, res, ctx) => {
          return res(ctx.delay(10000)); // 10 second delay
        })
      );

      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      // Should show loading state
      expect(screen.getByRole('main')).toBeInTheDocument();
    });
  });

  describe('Performance Integration', () => {
    test('handles large datasets efficiently', async () => {
      // Mock large dataset
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        id: `${i + 1}`,
        name: `Competitor ${i + 1}`,
        website: `https://competitor${i + 1}.com`,
        platforms: ['linkedin'],
        status: 'active'
      }));

      server.use(
        rest.get('/api/competitors/', (req, res, ctx) => {
          return res(ctx.json(largeDataset));
        })
      );

      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('competitor-list')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (5 seconds)
      expect(renderTime).toBeLessThan(5000);
    });
  });
});

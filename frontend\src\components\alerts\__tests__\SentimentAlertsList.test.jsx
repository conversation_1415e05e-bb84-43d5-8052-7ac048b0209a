/**
 * Tests for SentimentAlertsList component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';
import SentimentAlertsList from '../SentimentAlertsList';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Mock the API
vi.mock('../../api', () => ({
  default: {
    get: vi.fn(),
    delete: vi.fn()
  }
}));

// Mock the custom components
vi.mock('../common', () => ({
  CustomCard: ({ children, ...props }) => <div {...props}>{children}</div>,
  CustomCardHeader: ({ title, action }) => (
    <div>
      <h2>{title}</h2>
      {action}
    </div>
  ),
  CustomCardContent: ({ children }) => <div>{children}</div>
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => '2023-01-01 12:00 PM'),
  parseISO: vi.fn((dateStr) => new Date(dateStr))
}));

describe('SentimentAlertsList', () => {
  const mockApi = require('../../api').default;

  const mockAlerts = [
    {
      _id: 'alert1',
      message: 'Negative sentiment detected',
      sentiment_score: -0.8,
      content_id: 'content1',
      content_type: 'post',
      created_at: '2023-01-01T12:00:00Z',
      alert_type: 'negative_sentiment'
    },
    {
      _id: 'alert2',
      message: 'Sentiment trend declining',
      sentiment_score: -0.3,
      content_id: 'content2',
      content_type: 'comment',
      created_at: '2023-01-01T11:00:00Z',
      alert_type: 'sentiment_change'
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue({ data: mockAlerts });
    mockApi.delete.mockResolvedValue({ data: { success: true } });
  });

  test('renders sentiment alerts list', async () => {
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    expect(screen.getByText('Sentiment Alerts')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Negative sentiment detected')).toBeInTheDocument();
      expect(screen.getByText('Sentiment trend declining')).toBeInTheDocument();
    });
  });

  test('shows loading state initially', () => {
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('displays alert details correctly', async () => {
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Negative sentiment detected')).toBeInTheDocument();
      expect(screen.getByText('Score: -0.8')).toBeInTheDocument();
      expect(screen.getByText('Post')).toBeInTheDocument();
      expect(screen.getByText('2023-01-01 12:00 PM')).toBeInTheDocument();
    });
  });

  test('shows correct sentiment icons', async () => {
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      // Check for sentiment icons (they should be rendered)
      const alertItems = screen.getAllByRole('listitem');
      expect(alertItems).toHaveLength(2);
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/sentiment-alerts', { params: { limit: 10 } });
    });

    // Clear mock calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh sentiment alerts');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/sentiment-alerts', { params: { limit: 10 } });
    });
  });

  test('handles delete functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Negative sentiment detected')).toBeInTheDocument();
    });

    // Click delete button for first alert
    const deleteButtons = screen.getAllByLabelText(/Delete alert:/);
    await user.click(deleteButtons[0]);

    await waitFor(() => {
      expect(mockApi.delete).toHaveBeenCalledWith('/api/sentiment-alerts/alert1');
    });

    // Alert should be removed from the list
    await waitFor(() => {
      expect(screen.queryByText('Negative sentiment detected')).not.toBeInTheDocument();
    });
  });

  test('handles view content functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Negative sentiment detected')).toBeInTheDocument();
    });

    // Click view content button
    const viewButtons = screen.getAllByText('View Content');
    await user.click(viewButtons[0]);

    // Navigation should be triggered (we can't easily test this without mocking useNavigate)
  });

  test('shows error state when API fails', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load alerts. Please try again.')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });
  });

  test('handles retry functionality', async () => {
    const user = userEvent.setup();
    mockApi.get.mockRejectedValueOnce(new Error('API Error'));
    mockApi.get.mockResolvedValueOnce({ data: mockAlerts });

    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load alerts. Please try again.')).toBeInTheDocument();
    });

    // Click retry button
    await user.click(screen.getByText('Retry'));

    await waitFor(() => {
      expect(screen.getByText('Negative sentiment detected')).toBeInTheDocument();
    });
  });

  test('shows empty state when no alerts', async () => {
    mockApi.get.mockResolvedValue({ data: [] });

    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No sentiment alerts found.')).toBeInTheDocument();
    });
  });

  test('respects limit prop', async () => {
    render(
      <TestWrapper>
        <SentimentAlertsList limit={5} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/sentiment-alerts', { params: { limit: 5 } });
    });
  });

  test('hides refresh button when showRefresh is false', () => {
    render(
      <TestWrapper>
        <SentimentAlertsList showRefresh={false} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Refresh sentiment alerts')).not.toBeInTheDocument();
  });

  test('hides delete buttons when showDelete is false', async () => {
    render(
      <TestWrapper>
        <SentimentAlertsList showDelete={false} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Negative sentiment detected')).toBeInTheDocument();
    });

    expect(screen.queryByLabelText(/Delete alert:/)).not.toBeInTheDocument();
  });

  test('handles delete error gracefully', async () => {
    const user = userEvent.setup();
    mockApi.delete.mockRejectedValue(new Error('Delete failed'));
    
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Negative sentiment detected')).toBeInTheDocument();
    });

    // Click delete button
    const deleteButtons = screen.getAllByLabelText(/Delete alert:/);
    await user.click(deleteButtons[0]);

    await waitFor(() => {
      expect(screen.getByText('Failed to delete alert. Please try again.')).toBeInTheDocument();
    });
  });

  test('shows loading state for individual delete operations', async () => {
    const user = userEvent.setup();
    // Make delete take some time
    mockApi.delete.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(
      <TestWrapper>
        <SentimentAlertsList />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Negative sentiment detected')).toBeInTheDocument();
    });

    // Click delete button
    const deleteButtons = screen.getAllByLabelText(/Delete alert:/);
    await user.click(deleteButtons[0]);

    // Should show loading spinner
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
});

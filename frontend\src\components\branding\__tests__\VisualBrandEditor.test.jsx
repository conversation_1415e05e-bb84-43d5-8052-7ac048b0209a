import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import VisualBrandEditor from '../VisualBrandEditor';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock the child components
vi.mock('../BrandColorPicker', () => ({
  default: ({ colorSystem, onChange, readOnly }) => (
    <div data-testid="brand-color-picker">
      <span>Color System: {JSON.stringify(colorSystem)}</span>
      <button onClick={() => onChange({ primary: '#FF0000' })} disabled={readOnly}>
        Change Color
      </button>
    </div>
  ),
}));

vi.mock('../BrandTypography', () => ({
  default: ({ typography, onChange, readOnly }) => (
    <div data-testid="brand-typography">
      <span>Typography: {JSON.stringify(typography)}</span>
      <button onClick={() => onChange({ fonts: ['Arial'], style: 'modern' })} disabled={readOnly}>
        Change Typography
      </button>
    </div>
  ),
}));

vi.mock('../LogoManager', () => ({
  default: ({ logoData, onChange, readOnly }) => (
    <div data-testid="logo-manager">
      <span>Logo: {JSON.stringify(logoData)}</span>
      <button onClick={() => onChange({ logo_url: 'new-logo.png' })} disabled={readOnly}>
        Change Logo
      </button>
    </div>
  ),
}));

vi.mock('../CompositionEditor', () => ({
  default: ({ imageComposition, onChange, readOnly }) => (
    <div data-testid="composition-editor">
      <span>Composition: {JSON.stringify(imageComposition)}</span>
      <button onClick={() => onChange({ layout: 'grid' })} disabled={readOnly}>
        Change Composition
      </button>
    </div>
  ),
}));

vi.mock('../PatternManager', () => ({
  default: ({ visualElements, onChange, readOnly }) => (
    <div data-testid="pattern-manager">
      <span>Patterns: {JSON.stringify(visualElements)}</span>
      <button onClick={() => onChange({ patterns: ['new-pattern'] })} disabled={readOnly}>
        Change Patterns
      </button>
    </div>
  ),
}));

vi.mock('../BrandPreview', () => ({
  default: ({ brandingData, previewType, onDragStart }) => (
    <div data-testid="brand-preview">
      <span>Preview Type: {previewType}</span>
      <span>Branding Data: {JSON.stringify(brandingData)}</span>
      <div 
        draggable 
        onDragStart={(e) => onDragStart(e, { type: 'logo' })}
        data-testid="draggable-element"
      >
        Draggable Logo
      </div>
    </div>
  ),
}));

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('VisualBrandEditor', () => {
  const mockBrandingData = {
    colorSystem: {
      primary: '#4E40C5',
      secondary: '#00E4BC',
      accent: '#FF5733'
    },
    fonts: ['Inter', 'Roboto'],
    style: 'professional',
    logo_url: 'https://example.com/logo.png',
    logo_settings: {
      size: 50,
      position: 'center',
      opacity: 100
    },
    imageComposition: {
      layout: 'centered',
      aspectRatio: '16:9'
    },
    visualElements: {
      patterns: ['pattern1', 'pattern2'],
      textures: ['texture1']
    }
  };

  const mockProps = {
    brandingData: mockBrandingData,
    onChange: vi.fn(),
    readOnly: false
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders visual brand editor correctly', () => {
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Editor Tools')).toBeInTheDocument();
    expect(screen.getByText('Preview Type')).toBeInTheDocument();
  });

  test('displays all editor tabs', () => {
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Colors')).toBeInTheDocument();
    expect(screen.getByText('Typography')).toBeInTheDocument();
    expect(screen.getByText('Logo')).toBeInTheDocument();
    expect(screen.getByText('Composition')).toBeInTheDocument();
    expect(screen.getByText('Patterns')).toBeInTheDocument();
  });

  test('shows brand color picker by default', () => {
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByTestId('brand-color-picker')).toBeInTheDocument();
    expect(screen.getByText(/Color System:/)).toBeInTheDocument();
  });

  test('handles tab navigation', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Click on Typography tab
    await user.click(screen.getByText('Typography'));
    expect(screen.getByTestId('brand-typography')).toBeInTheDocument();

    // Click on Logo tab
    await user.click(screen.getByText('Logo'));
    expect(screen.getByTestId('logo-manager')).toBeInTheDocument();

    // Click on Composition tab
    await user.click(screen.getByText('Composition'));
    expect(screen.getByTestId('composition-editor')).toBeInTheDocument();

    // Click on Patterns tab
    await user.click(screen.getByText('Patterns'));
    expect(screen.getByTestId('pattern-manager')).toBeInTheDocument();
  });

  test('displays editor tools', () => {
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByTitle('Undo')).toBeInTheDocument();
    expect(screen.getByTitle('Redo')).toBeInTheDocument();
    expect(screen.getByTitle('Zoom In')).toBeInTheDocument();
    expect(screen.getByTitle('Zoom Out')).toBeInTheDocument();
    expect(screen.getByTitle('Full Screen')).toBeInTheDocument();
  });

  test('displays preview type buttons', () => {
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Social Post')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Banner')).toBeInTheDocument();
  });

  test('handles preview type change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Initially should show social-post
    expect(screen.getByText('Preview Type: social-post')).toBeInTheDocument();

    // Click on Profile button
    await user.click(screen.getByText('Profile'));
    expect(screen.getByText('Preview Type: profile')).toBeInTheDocument();

    // Click on Banner button
    await user.click(screen.getByText('Banner'));
    expect(screen.getByText('Preview Type: banner')).toBeInTheDocument();
  });

  test('handles color system changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    const changeColorButton = screen.getByText('Change Color');
    await user.click(changeColorButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandingData,
        colorSystem: { primary: '#FF0000' }
      });
    });
  });

  test('handles typography changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Typography tab
    await user.click(screen.getByText('Typography'));

    const changeTypographyButton = screen.getByText('Change Typography');
    await user.click(changeTypographyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandingData,
        fonts: ['Arial'],
        style: 'modern'
      });
    });
  });

  test('handles logo changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Logo tab
    await user.click(screen.getByText('Logo'));

    const changeLogoButton = screen.getByText('Change Logo');
    await user.click(changeLogoButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandingData,
        logo_url: 'new-logo.png',
        logo_settings: mockBrandingData.logo_settings
      });
    });
  });

  test('handles composition changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Composition tab
    await user.click(screen.getByText('Composition'));

    const changeCompositionButton = screen.getByText('Change Composition');
    await user.click(changeCompositionButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandingData,
        imageComposition: { layout: 'grid' }
      });
    });
  });

  test('handles pattern changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Navigate to Patterns tab
    await user.click(screen.getByText('Patterns'));

    const changePatternsButton = screen.getByText('Change Patterns');
    await user.click(changePatternsButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockBrandingData,
        visualElements: { patterns: ['new-pattern'] }
      });
    });
  });

  test('handles undo functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Make a change first
    const changeColorButton = screen.getByText('Change Color');
    await user.click(changeColorButton);

    // Then try to undo
    const undoButton = screen.getByTitle('Undo');
    await user.click(undoButton);

    // Should call onChange with previous state
    expect(mockProps.onChange).toHaveBeenCalled();
  });

  test('handles redo functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Make a change, then undo, then redo
    const changeColorButton = screen.getByText('Change Color');
    await user.click(changeColorButton);

    const undoButton = screen.getByTitle('Undo');
    await user.click(undoButton);

    const redoButton = screen.getByTitle('Redo');
    await user.click(redoButton);

    expect(mockProps.onChange).toHaveBeenCalled();
  });

  test('handles zoom in functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    const zoomInButton = screen.getByTitle('Zoom In');
    await user.click(zoomInButton);

    // Should update zoom level (tested via implementation)
    expect(zoomInButton).toBeInTheDocument();
  });

  test('handles zoom out functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    const zoomOutButton = screen.getByTitle('Zoom Out');
    await user.click(zoomOutButton);

    // Should update zoom level (tested via implementation)
    expect(zoomOutButton).toBeInTheDocument();
  });

  test('disables controls when readOnly is true', () => {
    const readOnlyProps = {
      ...mockProps,
      readOnly: true
    };

    render(
      <TestWrapper>
        <VisualBrandEditor {...readOnlyProps} />
      </TestWrapper>
    );

    const undoButton = screen.getByTitle('Undo');
    const redoButton = screen.getByTitle('Redo');

    expect(undoButton).toBeDisabled();
    expect(redoButton).toBeDisabled();
  });

  test('renders with default props when no brandingData provided', () => {
    render(
      <TestWrapper>
        <VisualBrandEditor onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Editor Tools')).toBeInTheDocument();
    expect(screen.getByTestId('brand-color-picker')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    // Check for proper button roles and labels
    expect(screen.getByRole('button', { name: /undo/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /redo/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /zoom in/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /zoom out/i })).toBeInTheDocument();
  });

  test('displays brand preview component', () => {
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByTestId('brand-preview')).toBeInTheDocument();
    expect(screen.getByText(/Branding Data:/)).toBeInTheDocument();
  });

  test('handles drag and drop functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualBrandEditor {...mockProps} />
      </TestWrapper>
    );

    const draggableElement = screen.getByTestId('draggable-element');
    
    // Simulate drag start
    fireEvent.dragStart(draggableElement);
    
    // Should handle drag start (tested via implementation)
    expect(draggableElement).toBeInTheDocument();
  });
});

// @since 2024-1-1 to 2025-25-7
import { <PERSON>, Typography, Alert, Breadcrum<PERSON>, Link, Container, CircularProgress, Button } from '@mui/material';
import { Settings as SettingsIcon, Business as BusinessIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useEffect, useState } from 'react';
import UnifiedCompetitorsPage from '../competitors/UnifiedCompetitorsPage';

/**
 * CompetitorsSettings component for managing competitors within the Settings page
 * Provides a production-ready embedded experience with proper navigation and context
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isEmbedded - Whether the component is embedded in another page
 */
const CompetitorsSettings = ({ isEmbedded = false }) => {
  const navigate = useNavigate();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const [authError, setAuthError] = useState(null);

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      setAuthError('Authentication required to access competitor settings');
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Handle navigation back to main settings
  const handleBackToSettings = () => {
    navigate('/settings');
  };

  // Handle navigation to competitors standalone page
  const handleViewFullPage = () => {
    navigate('/competitors');
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Show authentication error if any
  if (authError) {
    return (
      <Container maxWidth="lg" sx={{ py: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>Authentication Error</Typography>
          {authError}
        </Alert>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button variant="contained" onClick={() => navigate('/login')}>
            Go to Login
          </Button>
          <Button variant="outlined" onClick={() => navigate('/settings')}>
            Back to Settings
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Box>
      {/* Conditional header based on embedded state */}
      {!isEmbedded && (
        <Box sx={{ mb: 3 }}>
          {/* Breadcrumb navigation */}
          <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
            <Link
              component="button"
              variant="body2"
              onClick={handleBackToSettings}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                color: 'primary.main',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              <SettingsIcon sx={{ mr: 0.5, fontSize: 16 }} />
              Settings
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
              <BusinessIcon sx={{ mr: 0.5, fontSize: 16 }} />
              Competitors
            </Typography>
          </Breadcrumbs>

          {/* Page header */}
          <Typography variant="h4" component="h1" gutterBottom>
            Competitor Management
            {user?.name && (
              <Typography component="span" variant="h6" color="text.secondary" sx={{ ml: 2 }}>
                - {user.name}
              </Typography>
            )}
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Monitor and analyze your competitors to stay ahead in the market.
            Track their social media presence, content strategies, and engagement metrics.
            {user?.company && ` Manage competitors for ${user.company}.`}
          </Typography>
        </Box>
      )}

      {/* Embedded mode info alert */}
      {isEmbedded && (
        <Alert
          severity="info"
          sx={{ mb: 2 }}
          action={
            <Link
              component="button"
              variant="body2"
              onClick={handleViewFullPage}
              sx={{ color: 'inherit', textDecoration: 'underline' }}
            >
              View Full Page
            </Link>
          }
        >
          {user?.name && `Welcome ${user.name}! `}
          You&apos;re viewing competitors in settings mode.
          For the complete experience with advanced analytics, use the full competitors page.
        </Alert>
      )}

      {/* Main competitors component */}
      <UnifiedCompetitorsPage
        isEmbedded={isEmbedded}
        showHeader={false}
        compactMode={isEmbedded}
        user={user}
        isAuthenticated={isAuthenticated}
      />
    </Box>
  );
};

export default CompetitorsSettings;

"""
Webhook endpoints for add-on system integrations.
<PERSON>les Lemon Squeezy webhooks for purchase confirmations and refunds.
"""
import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from fastapi import APIRouter, Request, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse

# Import dependencies with type: ignore to suppress missing import warnings
from app.db.mongodb import get_database  # type: ignore
from app.services.lemon_squeezy_addon_integration import process_addon_webhook  # type: ignore
from app.services.addon_error_handler import handle_addon_error, AddonErrorType  # type: ignore
from app.core.monitoring import record_addon_metrics  # type: ignore
# Use mock client for now to avoid interface issues
redis_client = None

# Fallback implementations for when modules are not available
HAS_MONGODB = True

# Mock implementations as fallbacks
class MockRedisClient:
    async def get(self, key: str) -> Optional[str]:
        _ = key
        return None
    async def setex(self, key: str, seconds: int, value: Any) -> None:
        _ = key, seconds, value
    async def ping(self) -> bool:
        return True
    async def lpush(self, key: str, value: str) -> None:
        _ = key, value
    async def ltrim(self, key: str, start: int, end: int) -> None:
        _ = key, start, end
    async def expire(self, key: str, seconds: int) -> None:
        _ = key, seconds
    async def lrange(self, key: str, start: int, end: int) -> list:
        _ = key, start, end
        return []

# Use mock if real redis_client is not available or doesn't have the right interface
if redis_client is None or not hasattr(redis_client, 'get'):
    redis_client = MockRedisClient()

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/webhooks/addons", tags=["addon-webhooks"])


@router.post("/lemon-squeezy")
async def lemon_squeezy_webhook(
    request: Request,
    background_tasks: BackgroundTasks
):
    """
    Handle Lemon Squeezy webhooks for add-on purchases.

    This endpoint processes:
    - order_created: Successful purchases
    - order_refunded: Refund processing
    - subscription_created: Recurring add-on subscriptions
    - subscription_cancelled: Subscription cancellations
    """
    try:
        # Get raw payload and signature
        payload = await request.body()
        signature = request.headers.get("X-Signature", "")
        
        if not signature:
            logger.warning("Webhook received without signature")
            raise HTTPException(status_code=400, detail="Missing webhook signature")
        
        # Rate limiting for webhook endpoint
        client_ip = request.client.host if request.client else "unknown"
        rate_limit_key = f"webhook_rate_limit:{client_ip}"

        if redis_client and hasattr(redis_client, 'get'):
            current_requests = await redis_client.get(rate_limit_key) or 0
            current_requests = int(current_requests) + 1

            if current_requests > 100:  # 100 requests per minute
                logger.warning(f"Rate limit exceeded for webhook from {client_ip}")
                raise HTTPException(status_code=429, detail="Rate limit exceeded")

            await redis_client.setex(rate_limit_key, 60, current_requests)
        else:
            # Skip rate limiting if redis is not available
            logger.debug("Redis not available, skipping rate limiting")
        
        # Process webhook in background to avoid timeout
        background_tasks.add_task(
            lambda: process_webhook_background(payload, signature, client_ip)
        )
        
        # Return immediate success response
        return JSONResponse(
            status_code=200,
            content={"status": "received", "message": "Webhook processing initiated"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling Lemon Squeezy webhook: {str(e)}")
        record_addon_metrics("webhook_error", "lemon_squeezy", 1)
        
        # Return success to avoid webhook retries for our internal errors
        return JSONResponse(
            status_code=200,
            content={"status": "error", "message": "Internal processing error"}
        )


async def process_webhook_background(payload: bytes, signature: str, client_ip: str):
    """Process webhook in background task."""
    try:
        # Process the webhook
        result = await process_addon_webhook(payload, signature)
        
        if result["success"]:
            logger.info(f"Webhook processed successfully from {client_ip}")
            record_addon_metrics("webhook_processed", "lemon_squeezy", 1)
        else:
            logger.error(f"Webhook processing failed from {client_ip}: {result.get('error')}")
            record_addon_metrics("webhook_failed", "lemon_squeezy", 1)
            
            # Handle webhook processing errors
            await handle_addon_error(
                AddonErrorType.EXTERNAL_SERVICE_ERROR,
                0,  # No specific user ID available
                operation="webhook_processing",
                details={
                    "error": result.get("error"),
                    "client_ip": client_ip,
                    "service": "lemon_squeezy"
                }
            )
            
    except Exception as e:
        logger.error(f"Error in background webhook processing: {str(e)}")
        record_addon_metrics("webhook_error", "lemon_squeezy", 1)


@router.post("/test")
async def test_webhook(
    request: Request,
    background_tasks: BackgroundTasks
):
    """
    Test webhook endpoint for development and testing.
    Only available in non-production environments.
    """
    try:
        from app.core.config import settings
        
        if settings.ENVIRONMENT == "production":
            raise HTTPException(status_code=404, detail="Not found")
        
        payload = await request.body()
        
        # Simulate webhook processing
        background_tasks.add_task(
            lambda: process_test_webhook(payload)
        )
        
        return JSONResponse(
            status_code=200,
            content={"status": "test_received", "message": "Test webhook processed"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling test webhook: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


async def process_test_webhook(payload: bytes):
    """Process test webhook for development."""
    try:
        import json
        
        # Parse test payload
        webhook_data = json.loads(payload.decode('utf-8'))
        event_name = webhook_data.get("event_name", "test_event")
        
        logger.info(f"Test webhook processed: {event_name}")
        record_addon_metrics("test_webhook_processed", "test", 1)
        
        # Simulate successful processing
        await asyncio.sleep(1)
        
    except Exception as e:
        logger.error(f"Error processing test webhook: {str(e)}")


@router.get("/health")
async def webhook_health_check():
    """Health check endpoint for webhook service."""
    try:
        # Check Redis connectivity
        if redis_client and hasattr(redis_client, 'ping'):
            await redis_client.ping()
        else:
            logger.debug("Redis not available for health check")
        
        # Check database connectivity
        if HAS_MONGODB:
            try:
                db = await get_database()
                if db is not None:
                    await db.command("ping")
            except Exception:
                pass
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "healthy",
                "service": "addon_webhooks",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"Webhook health check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        )


@router.post("/stripe")
async def stripe_webhook(
    request: Request,
    background_tasks: BackgroundTasks
):
    """
    Handle Stripe webhooks for add-on purchases (future implementation).
    Placeholder for potential Stripe integration.
    """
    try:
        payload = await request.body()
        signature = request.headers.get("Stripe-Signature", "")

        # Use variables to avoid unused warnings
        _ = background_tasks
        _ = payload
        
        if not signature:
            raise HTTPException(status_code=400, detail="Missing Stripe signature")
        
        # TODO: Implement Stripe webhook processing
        logger.info("Stripe webhook received (not yet implemented)")
        
        return JSONResponse(
            status_code=200,
            content={"status": "received", "message": "Stripe webhook not yet implemented"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling Stripe webhook: {str(e)}")
        return JSONResponse(
            status_code=200,
            content={"status": "error", "message": "Internal processing error"}
        )


@router.post("/paypal")
async def paypal_webhook(
    request: Request,
    background_tasks: BackgroundTasks
):
    """
    Handle PayPal webhooks for add-on purchases (future implementation).
    Placeholder for potential PayPal integration.
    """
    try:
        payload = await request.body()

        # Use variables to avoid unused warnings
        _ = background_tasks
        _ = payload
        
        # TODO: Implement PayPal webhook processing
        logger.info("PayPal webhook received (not yet implemented)")
        
        return JSONResponse(
            status_code=200,
            content={"status": "received", "message": "PayPal webhook not yet implemented"}
        )
        
    except Exception as e:
        logger.error(f"Error handling PayPal webhook: {str(e)}")
        return JSONResponse(
            status_code=200,
            content={"status": "error", "message": "Internal processing error"}
        )


# Webhook verification utilities
async def verify_webhook_source(request: Request) -> bool:
    """Verify that webhook is from a trusted source."""
    try:
        # Check for known webhook sources
        user_agent = request.headers.get("User-Agent", "")
        
        trusted_sources = [
            "LemonSqueezy",
            "Stripe",
            "PayPal"
        ]
        
        return any(source in user_agent for source in trusted_sources)
        
    except Exception as e:
        logger.error(f"Error verifying webhook source: {str(e)}")
        return False


async def log_webhook_event(event_type: str, source: str, success: bool, details: Optional[Dict[str, Any]] = None):
    """Log webhook events for monitoring and debugging."""
    try:
        log_data = {
            "event_type": event_type,
            "source": source,
            "success": success,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "details": details or {}
        }
        
        # Store in Redis for recent webhook history
        if redis_client and hasattr(redis_client, 'lpush'):
            webhook_log_key = f"webhook_log:{source}:{event_type}"
            await redis_client.lpush(webhook_log_key, json.dumps(log_data))
            await redis_client.ltrim(webhook_log_key, 0, 99)  # Keep last 100 events
            await redis_client.expire(webhook_log_key, 86400)  # 24 hours
        else:
            logger.debug("Redis not available, skipping webhook log storage")
        
        logger.info(f"Webhook event logged: {event_type} from {source} - {'success' if success else 'failed'}")
        
    except Exception as e:
        logger.error(f"Error logging webhook event: {str(e)}")


@router.get("/logs/{source}")
async def get_webhook_logs(source: str, limit: int = 50):
    """Get recent webhook logs for debugging (admin only)."""
    try:
        # TODO: Add admin authentication check
        
        logs = []
        if redis_client and hasattr(redis_client, 'lrange'):
            for event_type in ["order_created", "order_refunded", "subscription_created", "subscription_cancelled"]:
                webhook_log_key = f"webhook_log:{source}:{event_type}"
                log_entries = await redis_client.lrange(webhook_log_key, 0, limit - 1)

                for entry in log_entries:
                    try:
                        log_data = json.loads(entry)
                        logs.append(log_data)
                    except json.JSONDecodeError:
                        continue
        else:
            logger.debug("Redis not available, returning empty webhook logs")
        
        # Sort by timestamp
        logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        
        return JSONResponse(
            status_code=200,
            content={
                "logs": logs[:limit],
                "total": len(logs)
            }
        )
        
    except Exception as e:
        logger.error(f"Error retrieving webhook logs: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

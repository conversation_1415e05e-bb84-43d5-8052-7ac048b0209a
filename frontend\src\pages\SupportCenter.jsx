// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Badge,
  useTheme,
  useMediaQuery,
  Breadcrumbs,
  Link,
} from '@mui/material';
import {
  Support as SupportIcon,
  Assignment as TicketsIcon,
  Help as HelpIcon,
  Chat as ChatIcon,
  History as HistoryIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Home as HomeIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { Link as RouterLink } from 'react-router-dom';

// Support components (reusing existing components with error boundaries)
import CreateTicketForm from '../components/support/CreateTicketForm';
import TicketList from '../components/support/TicketList';
import KnowledgeBaseSearch from '../components/support/KnowledgeBaseSearch';
import LiveChatPanel from '../components/support/LiveChatPanel';
import SupportHistory from '../components/support/SupportHistory';
import QuickActions from '../components/support/QuickActions';

// Simple fallback component for failed imports
const ComponentFallback = ({ componentName }) => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    height="300px"
    textAlign="center"
    p={3}
  >
    <Typography variant="h6" color="text.secondary" gutterBottom>
      Component Temporarily Unavailable
    </Typography>
    <Typography variant="body2" color="text.secondary">
      The {componentName} component is currently unavailable. Please try refreshing the page.
    </Typography>
  </Box>
);

// Hooks
import useSupportWidget from '../hooks/useSupportWidget';
import { useAuth } from '../hooks/useAuth';
import { useSubscription } from '../hooks/useSubscription';

const SupportCenter = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const { subscription } = useSubscription();

  const [componentError, setComponentError] = useState(null);
  
  const [currentTab, setCurrentTab] = useState(0);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);

  const {
    tickets,
    loading,
    error,
    connectWebSocket,
    disconnectWebSocket,
  } = useSupportWidget() || {}; // Add fallback to prevent destructuring errors

  // Initialize WebSocket connection for real-time updates
  useEffect(() => {
    if (user?.id && connectWebSocket && disconnectWebSocket) {
      try {
        connectWebSocket();
        setLastUpdated(new Date());
        return () => {
          try {
            disconnectWebSocket();
          } catch (err) {
            console.error('Error disconnecting WebSocket:', err);
          }
        };
      } catch (err) {
        console.error('Error connecting WebSocket:', err);
        setComponentError('Failed to establish real-time connection');
      }
    }
  }, [user?.id, connectWebSocket, disconnectWebSocket]);

  // Get SLA info based on subscription tier (memoized to prevent re-renders)
  const slaInfo = useMemo(() => {
    const tier = subscription?.plan_id || 'creator';
    const slaHours = {
      creator: 24,
      accelerator: 12,
      dominator: 4,
    };
    return {
      tier: tier.charAt(0).toUpperCase() + tier.slice(1),
      hours: slaHours[tier] || 24,
    };
  }, [subscription?.plan_id]);

  // Memoize tabs to prevent re-renders
  const tabs = useMemo(() => [
    {
      label: 'Quick Help',
      icon: <HelpIcon />,
      component: KnowledgeBaseSearch,
      description: 'Search our knowledge base and FAQs',
    },
    {
      label: 'My Tickets',
      icon: <TicketsIcon />,
      component: TicketList,
      badge: tickets?.filter(t => ['open', 'in_progress'].includes(t.status)).length || 0,
      description: 'View and manage your support tickets',
    },
    {
      label: 'Live Chat',
      icon: <ChatIcon />,
      component: LiveChatPanel,
      description: 'Chat with our support team in real-time',
    },
    {
      label: 'History',
      icon: <HistoryIcon />,
      component: SupportHistory,
      description: 'View your complete support history',
    },
  ], [tickets]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
    setShowCreateForm(false);
  };

  const handleCreateTicket = () => {
    setShowCreateForm(true);
    setCurrentTab(1); // Switch to tickets tab
  };

  const handleTicketCreated = () => {
    setShowCreateForm(false);
    setLastUpdated(new Date());
  };

  const handleRefresh = () => {
    setLastUpdated(new Date());
    // The useSupportWidget hook will handle the actual refresh
  };

  const CurrentTabComponent = tabs[currentTab]?.component;

  // Safety check to prevent rendering issues
  if (!user) {
    return (
      <Container maxWidth="xl" sx={{ py: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" height="400px">
          <CircularProgress size={60} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>

      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
        <Link
          component={RouterLink}
          to="/dashboard"
          color="inherit"
          sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Dashboard
        </Link>
        <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
          <SupportIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Support Center
        </Typography>
      </Breadcrumbs>


      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Support Center
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Get help, submit tickets, and access our knowledge base
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Tooltip title="Refresh Data">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateTicket}
          >
            New Ticket
          </Button>
        </Box>
      </Box>


      <Card sx={{ mb: 3, background: `linear-gradient(135deg, ${theme.palette.primary.main}10, ${theme.palette.secondary.main}10)` }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                {slaInfo.tier} Plan Support
              </Typography>
              <Typography variant="body2" color="text.secondary">
                We&apos;re committed to responding to your support requests within {slaInfo.hours} hours during business hours.
                {slaInfo.tier === 'Dominator' && ' Priority support available 24/7.'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box display="flex" gap={1} flexWrap="wrap" justifyContent={{ xs: 'flex-start', md: 'flex-end' }}>
                <Chip
                  label={`${slaInfo.hours}h Response Time`}
                  color="primary"
                  variant="outlined"
                />
                <Chip
                  label="24/7 Available"
                  color="success"
                  variant="outlined"
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>


      {(error || componentError) && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          onClose={() => {
            setComponentError(null);
          }}
        >
          {error || componentError}
        </Alert>
      )}


      {!showCreateForm && currentTab === 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <QuickActions
              onCreateTicket={handleCreateTicket}
              onStartChat={() => setCurrentTab(2)}
              slaInfo={slaInfo}
            />
          </CardContent>
        </Card>
      )}


      <Card>
        <CardHeader
          title={
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Tabs 
                value={currentTab} 
                onChange={handleTabChange}
                variant={isMobile ? "scrollable" : "fullWidth"}
                scrollButtons={isMobile ? "auto" : false}
                sx={{
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontSize: '0.875rem',
                    fontWeight: 500,
                    minHeight: 64,
                  },
                }}
              >
                {tabs.map((tab, index) => (
                  <Tab
                    key={index}
                    label={
                      <Box display="flex" alignItems="center" gap={1} flexDirection={isMobile ? 'column' : 'row'}>
                        <Box display="flex" alignItems="center" gap={0.5}>
                          {tab.icon}
                          <span>{tab.label}</span>
                          {tab.badge > 0 && (
                            <Badge badgeContent={tab.badge} color="error" />
                          )}
                        </Box>
                        {!isMobile && (
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            {tab.description}
                          </Typography>
                        )}
                      </Box>
                    }
                    iconPosition="start"
                  />
                ))}
              </Tabs>
              {lastUpdated && (
                <Typography variant="caption" color="text.secondary">
                  Last updated: {format(lastUpdated, 'MMM dd, HH:mm')}
                </Typography>
              )}
            </Box>
          }
        />
        <CardContent sx={{ p: 0 }}>
          <Box sx={{ minHeight: '600px' }}>
            {loading && !tickets.length ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="400px"
              >
                <CircularProgress size={60} />
              </Box>
            ) : showCreateForm ? (
              <CreateTicketForm
                onCancel={() => setShowCreateForm(false)}
                onSuccess={handleTicketCreated}
                slaInfo={slaInfo}
              />
            ) : CurrentTabComponent ? (
              <React.Suspense fallback={<ComponentFallback componentName={tabs[currentTab]?.label} />}>
                <CurrentTabComponent
                  tickets={tickets}
                  onCreateTicket={handleCreateTicket}
                  slaInfo={slaInfo}
                />
              </React.Suspense>
            ) : (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="400px"
              >
                <Typography variant="body2" color="text.secondary">
                  Content not available
                </Typography>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default SupportCenter;

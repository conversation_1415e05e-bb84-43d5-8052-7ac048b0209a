/**
 * Enhanced ACE Social Error Boundary - Enterprise-grade error boundary component
 * Features: Comprehensive error boundary with advanced error categorization, recovery mechanisms,
 * and user-friendly error displays for React component errors, detailed error analytics with
 * error tracking and stack trace analysis, advanced error recovery features with retry mechanisms
 * and fallback components, ACE Social's error handling system integration with seamless error
 * reporting and monitoring, error interaction features including error reporting and user feedback
 * collection, error state management with error persistence and recovery tracking, and real-time
 * error monitoring with live error displays and automatic error notifications
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  Component,
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Alert,
  Paper,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Collapse,
  LinearProgress,
  Divider,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide,
  Snackbar,
  CircularProgress,
  Badge,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  BugReport as BugReportIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  Timeline as TimelineIcon,
  Feedback as FeedbackIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Home as HomeIcon,
  RestartAlt as RestartIcon,
  ContactSupport as SupportIcon,
  Code as CodeIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  NetworkCheck as NetworkIcon,
  Storage as StorageIcon
} from '@mui/icons-material';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Error categories and types
const ERROR_CATEGORIES = {
  NETWORK: 'network',
  AUTHENTICATION: 'authentication',
  PERMISSION: 'permission',
  VALIDATION: 'validation',
  RUNTIME: 'runtime',
  MEMORY: 'memory',
  TIMEOUT: 'timeout',
  UNKNOWN: 'unknown'
};

const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

const RECOVERY_STRATEGIES = {
  RETRY: 'retry',
  REFRESH: 'refresh',
  FALLBACK: 'fallback',
  REDIRECT: 'redirect',
  RESET: 'reset'
};

// Error analytics events
const ERROR_ANALYTICS_EVENTS = {
  ERROR_OCCURRED: 'error_occurred',
  ERROR_RECOVERED: 'error_recovered',
  RETRY_ATTEMPTED: 'retry_attempted',
  FALLBACK_USED: 'fallback_used',
  ERROR_REPORTED: 'error_reported',
  FEEDBACK_SUBMITTED: 'feedback_submitted'
};

/**
 * Enhanced Error Boundary Class Component with advanced error handling
 */
class EnhancedErrorBoundaryCore extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      errorId: null,
      errorCategory: ERROR_CATEGORIES.UNKNOWN,
      errorSeverity: ERROR_SEVERITY.MEDIUM,
      recoveryStrategy: RECOVERY_STRATEGIES.RETRY,
      userFeedback: '',
      showDetails: false,
      showFeedback: false,
      errorTimestamp: null,
      errorContext: {},
      performanceMetrics: {},
      networkStatus: 'online'
    };

    this.errorReportingService = props.errorReportingService;
    this.analyticsService = props.analyticsService;
  }

  static getDerivedStateFromError(error) {
    // Generate unique error ID
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      errorId,
      errorTimestamp: new Date().toISOString()
    };
  }

  componentDidCatch(error, errorInfo) {
    // Enhanced error logging and categorization
    console.error('Enhanced ErrorBoundary caught an error:', error, errorInfo);

    // Categorize error
    const errorCategory = this.categorizeError(error);
    const errorSeverity = this.assessErrorSeverity(error, errorInfo);
    const recoveryStrategy = this.determineRecoveryStrategy(error, errorCategory);

    // Collect performance metrics
    const performanceMetrics = this.collectPerformanceMetrics();

    // Collect error context
    const errorContext = this.collectErrorContext(error, errorInfo);

    this.setState({
      error: error,
      errorInfo: errorInfo,
      errorCategory,
      errorSeverity,
      recoveryStrategy,
      performanceMetrics,
      errorContext,
      networkStatus: navigator.onLine ? 'online' : 'offline'
    });

    // Report error to analytics service
    if (this.analyticsService && this.props.enableAnalytics) {
      this.analyticsService.track(ERROR_ANALYTICS_EVENTS.ERROR_OCCURRED, {
        errorId: this.state.errorId,
        errorMessage: error.message,
        errorStack: error.stack,
        errorCategory,
        errorSeverity,
        componentStack: errorInfo.componentStack,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: this.state.errorTimestamp,
        performanceMetrics,
        errorContext
      });
    }

    // Report error to external service
    if (this.errorReportingService && this.props.enableErrorReporting) {
      this.errorReportingService.reportError({
        error,
        errorInfo,
        errorId: this.state.errorId,
        category: errorCategory,
        severity: errorSeverity,
        context: errorContext,
        metrics: performanceMetrics
      });
    }

    // Auto-retry for certain error types
    if (this.shouldAutoRetry(error, errorCategory) && this.state.retryCount < this.props.maxRetries) {
      const retryDelay = this.calculateRetryDelay(this.state.retryCount);
      setTimeout(() => {
        this.handleRetry();
      }, retryDelay);
    }

    // Announce error to screen readers
    if (this.props.enableAccessibility) {
      const announcement = `Error occurred: ${error.message}. Recovery options available.`;
      this.announceToScreenReader(announcement);
    }
  }

  // Enhanced utility methods
  categorizeError = (error) => {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('network') || message.includes('fetch') || message.includes('xhr')) {
      return ERROR_CATEGORIES.NETWORK;
    }
    if (message.includes('unauthorized') || message.includes('forbidden') || message.includes('auth')) {
      return ERROR_CATEGORIES.AUTHENTICATION;
    }
    if (message.includes('permission') || message.includes('access denied')) {
      return ERROR_CATEGORIES.PERMISSION;
    }
    if (message.includes('validation') || message.includes('invalid')) {
      return ERROR_CATEGORIES.VALIDATION;
    }
    if (message.includes('memory') || message.includes('heap')) {
      return ERROR_CATEGORIES.MEMORY;
    }
    if (message.includes('timeout') || message.includes('timed out')) {
      return ERROR_CATEGORIES.TIMEOUT;
    }
    if (stack.includes('runtime') || message.includes('runtime')) {
      return ERROR_CATEGORIES.RUNTIME;
    }

    return ERROR_CATEGORIES.UNKNOWN;
  };

  assessErrorSeverity = (error, errorInfo) => {
    const message = error.message.toLowerCase();
    const componentStack = errorInfo.componentStack || '';

    // Critical errors
    if (message.includes('security') || message.includes('auth') || message.includes('payment')) {
      return ERROR_SEVERITY.CRITICAL;
    }

    // High severity errors
    if (message.includes('data loss') || message.includes('corruption') || componentStack.includes('App')) {
      return ERROR_SEVERITY.HIGH;
    }

    // Medium severity errors
    if (message.includes('network') || message.includes('timeout') || message.includes('validation')) {
      return ERROR_SEVERITY.MEDIUM;
    }

    // Low severity errors
    return ERROR_SEVERITY.LOW;
  };

  determineRecoveryStrategy = (error, category) => {
    switch (category) {
      case ERROR_CATEGORIES.NETWORK:
      case ERROR_CATEGORIES.TIMEOUT:
        return RECOVERY_STRATEGIES.RETRY;
      case ERROR_CATEGORIES.AUTHENTICATION:
        return RECOVERY_STRATEGIES.REDIRECT;
      case ERROR_CATEGORIES.MEMORY:
        return RECOVERY_STRATEGIES.REFRESH;
      case ERROR_CATEGORIES.VALIDATION:
        return RECOVERY_STRATEGIES.FALLBACK;
      default:
        return RECOVERY_STRATEGIES.RETRY;
    }
  };

  collectPerformanceMetrics = () => {
    if (typeof performance !== 'undefined' && performance.memory) {
      return {
        memoryUsage: {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        },
        timing: performance.timing ? {
          loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
          domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
        } : null,
        navigation: performance.navigation ? {
          type: performance.navigation.type,
          redirectCount: performance.navigation.redirectCount
        } : null
      };
    }
    return {};
  };

  collectErrorContext = (error, errorInfo) => {
    return {
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: window.screen.width,
        height: window.screen.height,
        colorDepth: window.screen.colorDepth
      },
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    };
  };

  shouldAutoRetry = (error, category) => {
    const transientCategories = [
      ERROR_CATEGORIES.NETWORK,
      ERROR_CATEGORIES.TIMEOUT,
      ERROR_CATEGORIES.UNKNOWN
    ];
    return transientCategories.includes(category) && this.props.enableAutoRetry;
  };

  calculateRetryDelay = (retryCount) => {
    // Exponential backoff with jitter
    const baseDelay = this.props.retryDelay || 1000;
    const exponentialDelay = baseDelay * Math.pow(2, retryCount);
    const jitter = Math.random() * 1000;
    return Math.min(exponentialDelay + jitter, 30000); // Max 30 seconds
  };

  announceToScreenReader = (message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  };

  // Enhanced action handlers
  handleRetry = () => {
    if (this.analyticsService && this.props.enableAnalytics) {
      this.analyticsService.track(ERROR_ANALYTICS_EVENTS.RETRY_ATTEMPTED, {
        errorId: this.state.errorId,
        retryCount: this.state.retryCount + 1,
        errorCategory: this.state.errorCategory
      });
    }

    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
      showDetails: false,
      showFeedback: false
    }));

    if (this.props.enableAccessibility) {
      this.announceToScreenReader('Retrying application. Please wait.');
    }

    if (this.props.onRetry) {
      this.props.onRetry(this.state.retryCount + 1);
    }
  };

  handleReset = () => {
    if (this.analyticsService && this.props.enableAnalytics) {
      this.analyticsService.track(ERROR_ANALYTICS_EVENTS.ERROR_RECOVERED, {
        errorId: this.state.errorId,
        recoveryMethod: 'reset',
        errorCategory: this.state.errorCategory
      });
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      errorId: null,
      errorCategory: ERROR_CATEGORIES.UNKNOWN,
      errorSeverity: ERROR_SEVERITY.MEDIUM,
      recoveryStrategy: RECOVERY_STRATEGIES.RETRY,
      userFeedback: '',
      showDetails: false,
      showFeedback: false,
      errorTimestamp: null,
      errorContext: {},
      performanceMetrics: {}
    });

    if (this.props.enableAccessibility) {
      this.announceToScreenReader('Application reset successfully.');
    }

    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  handleFeedbackSubmit = () => {
    if (this.state.userFeedback.trim()) {
      if (this.analyticsService && this.props.enableAnalytics) {
        this.analyticsService.track(ERROR_ANALYTICS_EVENTS.FEEDBACK_SUBMITTED, {
          errorId: this.state.errorId,
          feedback: this.state.userFeedback,
          errorCategory: this.state.errorCategory,
          errorSeverity: this.state.errorSeverity
        });
      }

      if (this.props.onFeedbackSubmit) {
        this.props.onFeedbackSubmit({
          errorId: this.state.errorId,
          feedback: this.state.userFeedback,
          error: this.state.error,
          errorInfo: this.state.errorInfo,
          context: this.state.errorContext
        });
      }

      this.setState({
        showFeedback: false,
        userFeedback: ''
      });

      if (this.props.enableAccessibility) {
        this.announceToScreenReader('Feedback submitted successfully. Thank you for helping us improve.');
      }
    }
  };

  handleErrorReport = () => {
    const errorReport = {
      errorId: this.state.errorId,
      timestamp: this.state.errorTimestamp,
      error: {
        message: this.state.error?.message,
        stack: this.state.error?.stack,
        name: this.state.error?.name
      },
      errorInfo: this.state.errorInfo,
      category: this.state.errorCategory,
      severity: this.state.errorSeverity,
      context: this.state.errorContext,
      performanceMetrics: this.state.performanceMetrics,
      retryCount: this.state.retryCount,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    if (this.analyticsService && this.props.enableAnalytics) {
      this.analyticsService.track(ERROR_ANALYTICS_EVENTS.ERROR_REPORTED, {
        errorId: this.state.errorId,
        reportMethod: 'manual'
      });
    }

    if (this.props.onErrorReport) {
      this.props.onErrorReport(errorReport);
    }

    // Download error report as JSON
    const blob = new Blob([JSON.stringify(errorReport, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `error-report-${this.state.errorId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    if (this.props.enableAccessibility) {
      this.announceToScreenReader('Error report downloaded successfully.');
    }
  };

  handleFallback = () => {
    if (this.analyticsService && this.props.enableAnalytics) {
      this.analyticsService.track(ERROR_ANALYTICS_EVENTS.FALLBACK_USED, {
        errorId: this.state.errorId,
        errorCategory: this.state.errorCategory
      });
    }

    if (this.props.onFallback) {
      this.props.onFallback();
    } else {
      // Default fallback behavior
      window.location.href = this.props.fallbackUrl || '/';
    }
  };

  toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));

    if (this.props.enableAccessibility) {
      const message = this.state.showDetails
        ? 'Error details hidden'
        : 'Error details shown';
      this.announceToScreenReader(message);
    }
  };

  toggleFeedback = () => {
    this.setState(prevState => ({
      showFeedback: !prevState.showFeedback
    }));

    if (this.props.enableAccessibility) {
      const message = this.state.showFeedback
        ? 'Feedback form hidden'
        : 'Feedback form shown';
      this.announceToScreenReader(message);
    }
  };

  getErrorIcon = () => {
    switch (this.state.errorSeverity) {
      case ERROR_SEVERITY.CRITICAL:
        return <ErrorIcon sx={{ fontSize: 64, color: '#f44336' }} />;
      case ERROR_SEVERITY.HIGH:
        return <WarningIcon sx={{ fontSize: 64, color: '#ff9800' }} />;
      case ERROR_SEVERITY.MEDIUM:
        return <InfoIcon sx={{ fontSize: 64, color: ACE_COLORS.PURPLE }} />;
      case ERROR_SEVERITY.LOW:
        return <BugReportIcon sx={{ fontSize: 64, color: '#2196f3' }} />;
      default:
        return <ErrorIcon sx={{ fontSize: 64, color: '#f44336' }} />;
    }
  };

  getSeverityColor = () => {
    switch (this.state.errorSeverity) {
      case ERROR_SEVERITY.CRITICAL:
        return '#f44336';
      case ERROR_SEVERITY.HIGH:
        return '#ff9800';
      case ERROR_SEVERITY.MEDIUM:
        return ACE_COLORS.PURPLE;
      case ERROR_SEVERITY.LOW:
        return '#2196f3';
      default:
        return '#f44336';
    }
  };

  getCategoryIcon = () => {
    switch (this.state.errorCategory) {
      case ERROR_CATEGORIES.NETWORK:
        return <NetworkIcon />;
      case ERROR_CATEGORIES.AUTHENTICATION:
        return <SecurityIcon />;
      case ERROR_CATEGORIES.MEMORY:
        return <MemoryIcon />;
      case ERROR_CATEGORIES.TIMEOUT:
        return <SpeedIcon />;
      case ERROR_CATEGORIES.VALIDATION:
        return <WarningIcon />;
      default:
        return <BugReportIcon />;
    }
  };

  render() {
    if (this.state.hasError) {
      // Enhanced glass morphism styles
      const glassMorphismStyles = {
        background: `linear-gradient(135deg,
          rgba(255, 255, 255, 0.95) 0%,
          rgba(255, 255, 255, 0.85) 100%)`,
        backdropFilter: 'blur(15px)',
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        borderRadius: '16px',
        boxShadow: `0 8px 32px 0 rgba(0, 0, 0, 0.12)`,
        transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
      };

      // Enhanced fallback UI
      return (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          p={3}
          sx={{
            background: `linear-gradient(135deg,
              ${alpha(ACE_COLORS.PURPLE, 0.05)} 0%,
              ${alpha(ACE_COLORS.YELLOW, 0.05)} 100%)`,
          }}
        >
          <Paper
            elevation={8}
            sx={{
              ...glassMorphismStyles,
              maxWidth: 800,
              width: '100%',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {/* Header with severity indicator */}
            <Box
              sx={{
                background: `linear-gradient(135deg, ${this.getSeverityColor()} 0%, ${alpha(this.getSeverityColor(), 0.8)} 100%)`,
                color: ACE_COLORS.WHITE,
                p: 3,
                textAlign: 'center'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2, mb: 2 }}>
                {this.getErrorIcon()}
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {this.state.errorSeverity === ERROR_SEVERITY.CRITICAL ? 'Critical Error' :
                     this.state.errorSeverity === ERROR_SEVERITY.HIGH ? 'System Error' :
                     this.state.errorSeverity === ERROR_SEVERITY.MEDIUM ? 'Application Error' :
                     'Minor Issue'}
                  </Typography>
                  <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                    Error ID: {this.state.errorId}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  icon={this.getCategoryIcon()}
                  label={this.state.errorCategory.toUpperCase()}
                  size="small"
                  sx={{
                    bgcolor: alpha(ACE_COLORS.WHITE, 0.2),
                    color: ACE_COLORS.WHITE,
                    '& .MuiChip-icon': { color: ACE_COLORS.WHITE }
                  }}
                />
                <Chip
                  label={`Severity: ${this.state.errorSeverity.toUpperCase()}`}
                  size="small"
                  sx={{
                    bgcolor: alpha(ACE_COLORS.WHITE, 0.2),
                    color: ACE_COLORS.WHITE
                  }}
                />
                {this.state.networkStatus === 'offline' && (
                  <Chip
                    label="OFFLINE"
                    size="small"
                    sx={{
                      bgcolor: alpha('#f44336', 0.8),
                      color: ACE_COLORS.WHITE
                    }}
                  />
                )}
              </Box>
            </Box>

            <CardContent sx={{ p: 4 }}>
              {/* Error message and description */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK }}>
                  {this.state.error?.message || 'An unexpected error occurred'}
                </Typography>

                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                  {this.state.errorCategory === ERROR_CATEGORIES.NETWORK
                    ? 'There seems to be a network connectivity issue. Please check your internet connection and try again.'
                    : this.state.errorCategory === ERROR_CATEGORIES.AUTHENTICATION
                    ? 'Authentication failed. Please log in again to continue.'
                    : this.state.errorCategory === ERROR_CATEGORIES.MEMORY
                    ? 'The application is using too much memory. Try refreshing the page.'
                    : 'The application encountered an unexpected error. This is usually temporary and can be resolved by retrying.'}
                </Typography>

                {/* Auto-retry indicator */}
                {this.state.retryCount < this.props.maxRetries && this.shouldAutoRetry(this.state.error, this.state.errorCategory) && (
                  <Alert
                    severity="info"
                    sx={{
                      mb: 3,
                      '& .MuiAlert-icon': { color: ACE_COLORS.PURPLE }
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CircularProgress size={16} sx={{ color: ACE_COLORS.PURPLE }} />
                      <Typography variant="body2">
                        Attempting automatic recovery... (Attempt {this.state.retryCount + 1}/{this.props.maxRetries})
                      </Typography>
                    </Box>
                  </Alert>
                )}

                {/* Retry limit reached */}
                {this.state.retryCount >= this.props.maxRetries && (
                  <Alert severity="warning" sx={{ mb: 3 }}>
                    <Typography variant="body2">
                      Maximum retry attempts reached. Please try manual recovery options below.
                    </Typography>
                  </Alert>
                )}
              </Box>

              {/* Action buttons */}
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<RefreshIcon />}
                    onClick={this.handleRetry}
                    disabled={this.state.retryCount >= this.props.maxRetries}
                    sx={{
                      bgcolor: ACE_COLORS.PURPLE,
                      '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) },
                      '&:disabled': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.3) }
                    }}
                  >
                    Try Again
                  </Button>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<RestartIcon />}
                    onClick={this.handleReset}
                    sx={{
                      borderColor: ACE_COLORS.PURPLE,
                      color: ACE_COLORS.PURPLE,
                      '&:hover': {
                        borderColor: ACE_COLORS.PURPLE,
                        bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                      }
                    }}
                  >
                    Reset App
                  </Button>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<HomeIcon />}
                    onClick={this.handleFallback}
                    sx={{
                      borderColor: ACE_COLORS.YELLOW,
                      color: ACE_COLORS.DARK,
                      '&:hover': {
                        borderColor: ACE_COLORS.YELLOW,
                        bgcolor: alpha(ACE_COLORS.YELLOW, 0.1)
                      }
                    }}
                  >
                    Go Home
                  </Button>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<SupportIcon />}
                    onClick={() => this.props.onContactSupport && this.props.onContactSupport()}
                    sx={{
                      borderColor: 'info.main',
                      color: 'info.main',
                      '&:hover': {
                        borderColor: 'info.main',
                        bgcolor: alpha('#2196f3', 0.1)
                      }
                    }}
                  >
                    Get Help
                  </Button>
                </Grid>
              </Grid>

              {/* Advanced options */}
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>
                <Tooltip title="View error details">
                  <IconButton
                    onClick={this.toggleDetails}
                    sx={{ color: ACE_COLORS.PURPLE }}
                  >
                    {this.state.showDetails ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </Tooltip>

                <Tooltip title="Download error report">
                  <IconButton
                    onClick={this.handleErrorReport}
                    sx={{ color: ACE_COLORS.PURPLE }}
                  >
                    <DownloadIcon />
                  </IconButton>
                </Tooltip>

                <Tooltip title="Send feedback">
                  <IconButton
                    onClick={this.toggleFeedback}
                    sx={{ color: ACE_COLORS.PURPLE }}
                  >
                    <FeedbackIcon />
                  </IconButton>
                </Tooltip>

                {this.props.enableAnalytics && (
                  <Tooltip title="View analytics">
                    <IconButton
                      onClick={() => this.props.onAnalyticsView && this.props.onAnalyticsView(this.state)}
                      sx={{ color: ACE_COLORS.PURPLE }}
                    >
                      <AnalyticsIcon />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>

              {/* Error details accordion */}
              <Collapse in={this.state.showDetails}>
                <Accordion sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Technical Details
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom>
                          Error Information
                        </Typography>
                        <Box
                          component="pre"
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.DARK, 0.05),
                            p: 2,
                            borderRadius: 1,
                            fontSize: '0.75rem',
                            overflow: 'auto',
                            maxHeight: 200,
                            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
                          }}
                        >
                          {JSON.stringify({
                            message: this.state.error?.message,
                            name: this.state.error?.name,
                            stack: this.state.error?.stack?.split('\n').slice(0, 10).join('\n')
                          }, null, 2)}
                        </Box>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" gutterBottom>
                          Component Stack
                        </Typography>
                        <Box
                          component="pre"
                          sx={{
                            backgroundColor: alpha(ACE_COLORS.DARK, 0.05),
                            p: 2,
                            borderRadius: 1,
                            fontSize: '0.75rem',
                            overflow: 'auto',
                            maxHeight: 200,
                            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
                          }}
                        >
                          {this.state.errorInfo?.componentStack}
                        </Box>
                      </Grid>

                      {Object.keys(this.state.performanceMetrics).length > 0 && (
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" gutterBottom>
                            Performance Metrics
                          </Typography>
                          <Box
                            component="pre"
                            sx={{
                              backgroundColor: alpha(ACE_COLORS.DARK, 0.05),
                              p: 2,
                              borderRadius: 1,
                              fontSize: '0.75rem',
                              overflow: 'auto',
                              maxHeight: 150,
                              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
                            }}
                          >
                            {JSON.stringify(this.state.performanceMetrics, null, 2)}
                          </Box>
                        </Grid>
                      )}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </Collapse>

              {/* Feedback form */}
              <Collapse in={this.state.showFeedback}>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                      Help Us Improve
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Your feedback helps us identify and fix issues faster.
                    </Typography>
                    <Box
                      component="textarea"
                      placeholder="Describe what you were doing when this error occurred..."
                      value={this.state.userFeedback}
                      onChange={(e) => this.setState({ userFeedback: e.target.value })}
                      sx={{
                        width: '100%',
                        minHeight: 100,
                        p: 2,
                        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`,
                        borderRadius: 1,
                        fontSize: '0.875rem',
                        fontFamily: 'inherit',
                        resize: 'vertical',
                        '&:focus': {
                          outline: 'none',
                          borderColor: ACE_COLORS.PURPLE,
                          boxShadow: `0 0 0 2px ${alpha(ACE_COLORS.PURPLE, 0.2)}`
                        }
                      }}
                    />
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => this.setState({ showFeedback: false, userFeedback: '' })}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={this.handleFeedbackSubmit}
                        disabled={!this.state.userFeedback.trim()}
                        sx={{
                          bgcolor: ACE_COLORS.PURPLE,
                          '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
                        }}
                      >
                        Submit Feedback
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Collapse>

              {/* Footer with additional info */}
              <Divider sx={{ my: 3 }} />
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  Error occurred at {new Date(this.state.errorTimestamp).toLocaleString()}
                </Typography>
                <br />
                <Typography variant="caption" color="text.secondary">
                  If this problem persists, please contact our support team with Error ID: {this.state.errorId}
                </Typography>
              </Box>
            </CardContent>
          </Paper>
        </Box>
      );
    }

    return this.props.children;
  }
}

/**
 * Enhanced Error Boundary Wrapper with forwardRef and useImperativeHandle
 * Provides functional component interface while maintaining class component error boundary functionality
 */
const EnhancedErrorBoundary = memo(forwardRef(({
  children,
  enableAnalytics = true,
  enableErrorReporting = true,
  enableAccessibility = true,
  enableAutoRetry = true,
  maxRetries = 3,
  retryDelay = 1000,
  fallbackUrl = '/',
  onRetry,
  onReset,
  onFeedbackSubmit,
  onErrorReport,
  onFallback,
  onContactSupport,
  onAnalyticsView,
  errorReportingService,
  analyticsService,
  className,
  ...props
}, ref) => {
  const errorBoundaryRef = useRef(null);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    retry: () => errorBoundaryRef.current?.handleRetry(),
    reset: () => errorBoundaryRef.current?.handleReset(),
    reportError: () => errorBoundaryRef.current?.handleErrorReport(),

    // State methods
    getErrorState: () => errorBoundaryRef.current?.state || null,
    hasError: () => errorBoundaryRef.current?.state?.hasError || false,
    getErrorId: () => errorBoundaryRef.current?.state?.errorId || null,

    // Feedback methods
    showFeedback: () => errorBoundaryRef.current?.toggleFeedback(),
    showDetails: () => errorBoundaryRef.current?.toggleDetails(),

    // Analytics methods
    getErrorAnalytics: () => ({
      errorId: errorBoundaryRef.current?.state?.errorId,
      category: errorBoundaryRef.current?.state?.errorCategory,
      severity: errorBoundaryRef.current?.state?.errorSeverity,
      retryCount: errorBoundaryRef.current?.state?.retryCount,
      timestamp: errorBoundaryRef.current?.state?.errorTimestamp
    })
  }), []);

  return (
    <EnhancedErrorBoundaryCore
      ref={errorBoundaryRef}
      enableAnalytics={enableAnalytics}
      enableErrorReporting={enableErrorReporting}
      enableAccessibility={enableAccessibility}
      enableAutoRetry={enableAutoRetry}
      maxRetries={maxRetries}
      retryDelay={retryDelay}
      fallbackUrl={fallbackUrl}
      onRetry={onRetry}
      onReset={onReset}
      onFeedbackSubmit={onFeedbackSubmit}
      onErrorReport={onErrorReport}
      onFallback={onFallback}
      onContactSupport={onContactSupport}
      onAnalyticsView={onAnalyticsView}
      errorReportingService={errorReportingService}
      analyticsService={analyticsService}
      className={className}
      {...props}
    >
      {children}
    </EnhancedErrorBoundaryCore>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedErrorBoundary.propTypes = {
  // Core props
  children: PropTypes.node.isRequired,

  // Feature flags
  enableAnalytics: PropTypes.bool,
  enableErrorReporting: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAutoRetry: PropTypes.bool,

  // Configuration
  maxRetries: PropTypes.number,
  retryDelay: PropTypes.number,
  fallbackUrl: PropTypes.string,

  // Callback props
  onRetry: PropTypes.func,
  onReset: PropTypes.func,
  onFeedbackSubmit: PropTypes.func,
  onErrorReport: PropTypes.func,
  onFallback: PropTypes.func,
  onContactSupport: PropTypes.func,
  onAnalyticsView: PropTypes.func,

  // Service props
  errorReportingService: PropTypes.shape({
    reportError: PropTypes.func.isRequired
  }),
  analyticsService: PropTypes.shape({
    track: PropTypes.func.isRequired
  }),

  // Standard props
  className: PropTypes.string
};

// Default props
EnhancedErrorBoundary.defaultProps = {
  enableAnalytics: true,
  enableErrorReporting: true,
  enableAccessibility: true,
  enableAutoRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  fallbackUrl: '/',
  onRetry: null,
  onReset: null,
  onFeedbackSubmit: null,
  onErrorReport: null,
  onFallback: null,
  onContactSupport: null,
  onAnalyticsView: null,
  errorReportingService: null,
  analyticsService: null,
  className: ''
};

// Display name for debugging
EnhancedErrorBoundary.displayName = 'EnhancedErrorBoundary';

export default EnhancedErrorBoundary;

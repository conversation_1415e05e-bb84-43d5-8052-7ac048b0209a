// @since 2024-1-1 to 2025-25-7
import { useState, useCallback, useEffect, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Container,
  Paper,
  Tabs,
  Tab,
  Alert,
  AlertTitle,
  Chip,
  Stack,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  AutoAwesome as AutoAwesomeIcon,
  BrushOutlined as BrushIcon,
  PhotoLibrary as GalleryIcon,
  TrendingUp as TrendingUpIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

// Import existing components
import ImageGenerator from '../../components/content/ImageGenerator';
import ImageManipulator from '../../components/content/ImageManipulator';

// Import hooks and utilities
import { useAuth } from '../../hooks/useAuth';
import { useNotification } from '../../hooks/useNotification';
import CreditCounter from '../../components/credits/CreditCounter';
import api from '../../api';

const UnifiedImageStudio = () => {
  const theme = useTheme();
  const { hasFeature } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [imageGallery, setImageGallery] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [usageStats, setUsageStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(true);

  // Tab configuration - memoized to prevent unnecessary re-renders
  const tabs = useMemo(() => [
    {
      label: 'Generate',
      icon: AutoAwesomeIcon,
      description: 'Create AI-powered images',
      feature: 'basic_branded_images',
    },
    {
      label: 'Edit',
      icon: BrushIcon,
      description: 'Manipulate and enhance images',
      feature: 'image_manipulation',
    },
    {
      label: 'Gallery',
      icon: GalleryIcon,
      description: 'View and manage your images',
      feature: 'basic_branded_images',
    },
  ], []);

  // Initialize tab from URL params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    if (tab) {
      const tabIndex = tabs.findIndex(t => t.label.toLowerCase() === tab.toLowerCase());
      if (tabIndex !== -1) {
        setActiveTab(tabIndex);
      }
    }
  }, [location.search, tabs]);

  // Load initial data
  useEffect(() => {
    loadUsageStats();
    loadImageGallery();
  }, [loadUsageStats, loadImageGallery]);

  const loadUsageStats = useCallback(async () => {
    try {
      setStatsLoading(true);
      const response = await api.get('/api/users/usage');
      setUsageStats(response.data);
    } catch (error) {
      console.error('Error loading usage stats:', error);
      showErrorNotification('Failed to load usage statistics. Please refresh the page.');
    } finally {
      setStatsLoading(false);
    }
  }, [showErrorNotification]);

  const loadImageGallery = useCallback(async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/content/images/recent');
      setImageGallery(response.data || []);
    } catch (error) {
      console.error('Error loading image gallery:', error);
      setImageGallery([]);
      showErrorNotification('Failed to load image gallery. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [showErrorNotification]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    const tabName = tabs[newValue].label.toLowerCase();
    navigate(`/content/image-studio?tab=${tabName}`, { replace: true });
  };

  const handleImageGenerated = useCallback((images) => {
    if (!images || !Array.isArray(images) || images.length === 0) {
      showErrorNotification('No images were generated');
      return;
    }

    try {
      // Add new images to gallery
      const newImages = images
        .filter(url => url && typeof url === 'string')
        .map(url => ({
          id: `generated_${Date.now()}_${Math.random()}`,
          url,
          type: 'generated',
          created_at: new Date().toISOString(),
          metadata: { source: 'ai_generation' }
        }));

      if (newImages.length === 0) {
        showErrorNotification('No valid images to add to gallery');
        return;
      }

      setImageGallery(prev => [...newImages, ...prev]);

      // Refresh usage stats
      loadUsageStats();

      showSuccessNotification(`Generated ${newImages.length} image(s) successfully`);
    } catch (error) {
      console.error('Error handling generated images:', error);
      showErrorNotification('Failed to process generated images');
    }
  }, [showSuccessNotification, showErrorNotification, loadUsageStats]);

  const handleImageManipulated = useCallback((images) => {
    if (!images || !Array.isArray(images) || images.length === 0) {
      showErrorNotification('No images were manipulated');
      return;
    }

    try {
      // Add manipulated images to gallery
      const newImages = images
        .filter(image => image && image.url && typeof image.url === 'string')
        .map(image => ({
          id: `manipulated_${Date.now()}_${Math.random()}`,
          url: image.url,
          type: 'manipulated',
          created_at: new Date().toISOString(),
          metadata: {
            source: 'manipulation',
            original_images: selectedImages.length > 0 ? selectedImages.map(img => img.id) : undefined
          }
        }));

      if (newImages.length === 0) {
        showErrorNotification('No valid manipulated images to add to gallery');
        return;
      }

      setImageGallery(prev => [...newImages, ...prev]);

      // Refresh usage stats
      loadUsageStats();

      showSuccessNotification(`Manipulated ${newImages.length} image(s) successfully`);
    } catch (error) {
      console.error('Error handling manipulated images:', error);
      showErrorNotification('Failed to process manipulated images');
    }
  }, [selectedImages, showSuccessNotification, showErrorNotification, loadUsageStats]);

  const handleImageSelection = useCallback((images) => {
    setSelectedImages(images);
    // Auto-switch to edit tab if images are selected for manipulation
    if (images.length > 0 && activeTab === 2) {
      setActiveTab(1);
      navigate('/content/image-studio?tab=edit', { replace: true });
    }
  }, [activeTab, navigate]);

  const handleSaveToLibrary = async (images) => {
    if (!images || images.length === 0) {
      showErrorNotification('No images to save');
      return;
    }

    try {
      setLoading(true);

      const savePromises = images.map(async (image) => {
        const contentData = {
          type: 'image',
          content: '',
          image_url: typeof image === 'string' ? image : image.url,
          platform: 'general',
          metadata: {
            source: 'image_studio',
            created_via: 'unified_image_studio'
          }
        };

        return api.post('/api/content/', contentData);
      });

      await Promise.all(savePromises);
      showSuccessNotification(`Saved ${images.length} image(s) to content library`);

    } catch (error) {
      console.error('Error saving images to library:', error);
      showErrorNotification('Failed to save images to library');
    } finally {
      setLoading(false);
    }
  };

  // Check feature access for current tab
  const currentTabFeature = tabs[activeTab]?.feature;
  const hasTabAccess = hasFeature(currentTabFeature);

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 8 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
          <Box>
            <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AutoAwesomeIcon sx={{ color: 'primary.main' }} />
              Image Studio
              <Chip
                label="Unified"
                size="small"
                color="primary"
                variant="outlined"
                sx={{ ml: 1 }}
              />
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Generate, edit, and manage your images in one powerful workspace
            </Typography>
          </Box>

          {/* Credit Counter Display */}
          <CreditCounter
            variant="default"
            showProgress={true}
            showRefresh={true}
            placement="bottom"
          />
        </Stack>

        {/* Usage Stats */}
        {statsLoading ? (
          <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
            <Chip
              icon={<TrendingUpIcon />}
              label="Loading stats..."
              variant="outlined"
              size="small"
              disabled
            />
            <Chip
              icon={<BrushIcon />}
              label="Loading stats..."
              variant="outlined"
              size="small"
              disabled
            />
          </Stack>
        ) : usageStats && (
          <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
            <Chip
              icon={<TrendingUpIcon />}
              label={`${usageStats.images_generated || 0} images generated this month`}
              variant="outlined"
              size="small"
            />
            <Chip
              icon={<BrushIcon />}
              label={`${usageStats.images_manipulated || 0} images edited this month`}
              variant="outlined"
              size="small"
            />
          </Stack>
        )}
      </Box>

      {/* Feature Access Check */}
      {!hasTabAccess && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <AlertTitle>Feature Access Required</AlertTitle>
          Your current plan doesn&apos;t include access to {tabs[activeTab]?.description.toLowerCase()}.
          Please upgrade your plan to use this feature.
        </Alert>
      )}

      {/* Main Content Area */}
      <Paper sx={{ mb: 4 }}>
        {/* Tab Navigation */}
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: 72,
              textTransform: 'none',
            }
          }}
        >
          {tabs.map((tab, index) => {
            const IconComponent = tab.icon;
            const hasAccess = hasFeature(tab.feature);

            return (
              <Tab
                key={index}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <IconComponent />
                    <Box sx={{ textAlign: 'left' }}>
                      <Typography variant="body1" sx={{ fontWeight: 500 }}>
                        {tab.label}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {tab.description}
                      </Typography>
                    </Box>
                    {!hasAccess && (
                      <Chip
                        label="Upgrade"
                        size="small"
                        color="warning"
                        variant="outlined"
                        sx={{ ml: 1 }}
                      />
                    )}
                  </Box>
                }
                disabled={!hasAccess}
              />
            );
          })}
        </Tabs>

        {/* Tab Content */}
        <Box sx={{ p: 3 }}>
          {/* Generate Tab */}
          {activeTab === 0 && hasTabAccess && (
            <Box>
              <ImageGenerator
                onImagesGenerated={handleImageGenerated}
                onSaveToLibrary={handleSaveToLibrary}
                showCreditInfo={false} // We show it in the header
              />
            </Box>
          )}

          {/* Edit Tab */}
          {activeTab === 1 && hasTabAccess && (
            <Box>
              {selectedImages.length > 0 && (
                <Alert severity="info" sx={{ mb: 3 }}>
                  <AlertTitle>Selected Images for Editing</AlertTitle>
                  You have {selectedImages.length} image(s) selected from the gallery for manipulation.
                </Alert>
              )}

              <ImageManipulator
                onImagesGenerated={handleImageManipulated}
                onSaveToLibrary={handleSaveToLibrary}
                preSelectedImages={selectedImages}
                showCreditInfo={false} // We show it in the header
              />
            </Box>
          )}

          {/* Gallery Tab */}
          {activeTab === 2 && hasTabAccess && (
            <ImageGalleryTab
              images={imageGallery}
              onImageSelection={handleImageSelection}
              onSaveToLibrary={handleSaveToLibrary}
              onRefresh={loadImageGallery}
              loading={loading}
            />
          )}

          {/* Access Denied Content */}
          {!hasTabAccess && (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <InfoIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Feature Access Required
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Upgrade your plan to access {tabs[activeTab]?.description.toLowerCase()}.
              </Typography>
              <Stack direction="row" spacing={2} justifyContent="center">
                <Chip
                  label="View Plans"
                  color="primary"
                  clickable
                  onClick={() => navigate('/billing')}
                />
                <Chip
                  label="Contact Sales"
                  variant="outlined"
                  clickable
                  onClick={() => window.open('mailto:<EMAIL>?subject=Feature Access Request&body=I would like to upgrade my plan to access image studio features.')}
                />
              </Stack>
            </Box>
          )}
        </Box>
      </Paper>

      {/* Help Section */}
      <Paper sx={{ p: 3, bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <InfoIcon color="primary" />
          Image Studio Tips
        </Typography>
        <Stack spacing={1}>
          <Typography variant="body2" color="text.secondary">
            • Generate images with AI using detailed prompts for better results
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Edit generated images or upload your own for manipulation
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Use the gallery to manage and reuse your created images
          </Typography>
          <Typography variant="body2" color="text.secondary">
            • Credit costs: First generation free, subsequent generations use tiered pricing
          </Typography>
        </Stack>
      </Paper>
    </Container>
  );
};

// Gallery Tab Component
const ImageGalleryTab = ({ images, onImageSelection, onSaveToLibrary, onRefresh, loading }) => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [filter, setFilter] = useState('all');

  const filteredImages = images.filter(image => {
    if (filter === 'all') return true;
    return image.type === filter;
  });

  const handleImageSelect = (image) => {
    setSelectedImages(prev => {
      const isSelected = prev.some(img => img.id === image.id);
      if (isSelected) {
        return prev.filter(img => img.id !== image.id);
      } else {
        return [...prev, image];
      }
    });
  };

  const handleBulkAction = (action) => {
    if (action === 'edit' && selectedImages.length > 0) {
      onImageSelection(selectedImages);
    } else if (action === 'save' && selectedImages.length > 0) {
      onSaveToLibrary(selectedImages);
    }
  };

  return (
    <Box>
      {/* Gallery Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Typography variant="h6">
          Image Gallery ({filteredImages.length} images)
        </Typography>

        <Stack direction="row" spacing={2} alignItems="center">
          {/* Filter Chips */}
          <Stack direction="row" spacing={1}>
            {['all', 'generated', 'manipulated'].map(filterType => (
              <Chip
                key={filterType}
                label={filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                variant={filter === filterType ? 'filled' : 'outlined'}
                size="small"
                onClick={() => setFilter(filterType)}
                color={filter === filterType ? 'primary' : 'default'}
              />
            ))}
          </Stack>

          <Tooltip title="Refresh gallery">
            <IconButton onClick={onRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>

      {/* Selection Actions */}
      {selectedImages.length > 0 && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography>
              {selectedImages.length} image(s) selected
            </Typography>
            <Stack direction="row" spacing={1}>
              <Chip
                label="Edit Selected"
                color="primary"
                size="small"
                onClick={() => handleBulkAction('edit')}
              />
              <Chip
                label="Save to Library"
                variant="outlined"
                size="small"
                onClick={() => handleBulkAction('save')}
              />
            </Stack>
          </Stack>
        </Alert>
      )}

      {/* Image Grid */}
      {filteredImages.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <GalleryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No images found
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {filter === 'all'
              ? 'Start by generating or uploading some images'
              : `No ${filter} images found`
            }
          </Typography>
        </Box>
      ) : (
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
          gap: 2
        }}>
          {filteredImages.map((image) => (
            <Paper
              key={image.id}
              component="button"
              sx={{
                position: 'relative',
                aspectRatio: '1',
                overflow: 'hidden',
                cursor: 'pointer',
                border: selectedImages.some(img => img.id === image.id)
                  ? 2
                  : 1,
                borderColor: selectedImages.some(img => img.id === image.id)
                  ? 'primary.main'
                  : 'divider',
                '&:hover': {
                  borderColor: 'primary.main',
                },
                '&:focus': {
                  outline: '2px solid',
                  outlineColor: 'primary.main',
                  outlineOffset: '2px',
                },
                background: 'none',
                padding: 0,
                width: '100%',
              }}
              onClick={() => handleImageSelect(image)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleImageSelect(image);
                }
              }}
              aria-label={`${selectedImages.some(img => img.id === image.id) ? 'Deselect' : 'Select'} ${image.type} image`}
              role="checkbox"
              aria-checked={selectedImages.some(img => img.id === image.id)}
            >
              <img
                src={image.url}
                alt={`${image.type} image created on ${new Date(image.created_at).toLocaleDateString()}`}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
                loading="lazy"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />

              {/* Error fallback */}
              <Box
                sx={{
                  display: 'none',
                  width: '100%',
                  height: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'grey.100',
                  color: 'text.secondary',
                  flexDirection: 'column',
                  gap: 1,
                }}
              >
                <Typography variant="caption">
                  Image unavailable
                </Typography>
              </Box>

              {/* Image Type Badge */}
              <Chip
                label={image.type}
                size="small"
                color={image.type === 'generated' ? 'primary' : 'secondary'}
                sx={{
                  position: 'absolute',
                  top: 8,
                  left: 8,
                  fontSize: '0.7rem',
                }}
              />

              {/* Selection Indicator */}
              {selectedImages.some(img => img.id === image.id) && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    width: 24,
                    height: 24,
                    borderRadius: '50%',
                    bgcolor: 'primary.main',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '0.8rem',
                  }}
                >
                  ✓
                </Box>
              )}
            </Paper>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default UnifiedImageStudio;

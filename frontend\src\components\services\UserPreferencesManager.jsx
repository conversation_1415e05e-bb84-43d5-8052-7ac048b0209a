/**
 * Enhanced User Preferences Manager - Enterprise-grade preferences management component
 * Features: Comprehensive preference management with real-time updates, organized categories,
 * validation and conflict resolution, subscription-based feature gating, import/export functionality,
 * device synchronization, advanced search and filtering, and ACE Social platform integration with
 * advanced preference capabilities and seamless settings management workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  Alert,
  CircularProgress,
  Divider,
  useTheme,
  Tooltip,
  IconButton,
  alpha,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Slider,
  FormGroup,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Badge,
  Fade,
  useMediaQuery,
  Autocomplete,
  InputAdornment
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Star as StarIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  AccountCircle as AccountIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Palette as AppearanceIcon,
  CloudSync as SyncIcon,
  Search as SearchIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  CheckCircle as CheckCircleIcon,
  Close as CloseIcon,
  History as HistoryIcon,
  Sync as SyncingIcon,
  SyncProblem as SyncErrorIcon,
  AutoAwesome as AIIcon,
  Psychology as SmartIcon,
  TrendingUp as AnalyticsIcon,
  Speed as PerformanceIcon
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';

import { useSubscription } from '../../contexts/SubscriptionContext';
import FeatureGate from '../common/FeatureGate';

import {
  getUserPreferences,
  updateUserPreferences,
  getAllCategories,
  getAllTargetSegments
} from '../../api/userPreferences';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Plan-based preferences limitations
const PLAN_LIMITS = {
  1: { // Creator
    maxCategories: 5,
    maxTargetSegments: 10,
    maxIndustries: 3,
    advancedPreferences: false,
    preferencesSync: false,
    importExport: false,
    preferencesHistory: false,
    smartRecommendations: false,
    bulkOperations: false,
    customCategories: false,
    advancedSearch: false,
    preferencesAnalytics: false
  },
  2: { // Accelerator
    maxCategories: 10,
    maxTargetSegments: 20,
    maxIndustries: 8,
    advancedPreferences: true,
    preferencesSync: true,
    importExport: true,
    preferencesHistory: true,
    smartRecommendations: true,
    bulkOperations: false,
    customCategories: true,
    advancedSearch: true,
    preferencesAnalytics: false
  },
  3: { // Dominator
    maxCategories: 25,
    maxTargetSegments: 50,
    maxIndustries: 20,
    advancedPreferences: true,
    preferencesSync: true,
    importExport: true,
    preferencesHistory: true,
    smartRecommendations: true,
    bulkOperations: true,
    customCategories: true,
    advancedSearch: true,
    preferencesAnalytics: true
  }
};

// Preference categories
const PREFERENCE_CATEGORIES = {
  ACCOUNT: 'account',
  NOTIFICATIONS: 'notifications',
  PRIVACY: 'privacy',
  APPEARANCE: 'appearance',
  SERVICES: 'services',
  ADVANCED: 'advanced'
};

// Sync status types
const SYNC_STATUS = {
  IDLE: 'idle',
  SYNCING: 'syncing',
  SUCCESS: 'success',
  ERROR: 'error',
  CONFLICT: 'conflict'
};

// Validation types (for future use)
// const VALIDATION_TYPES = {
//   REQUIRED: 'required',
//   OPTIONAL: 'optional',
//   CONDITIONAL: 'conditional'
// };

// Service configuration options
const PRICING_MODELS = [
  { value: 'fixed', label: 'Fixed Price' },
  { value: 'hourly', label: 'Hourly Rate' },
  { value: 'project', label: 'Project-based' },
  { value: 'retainer', label: 'Monthly Retainer' },
  { value: 'performance', label: 'Performance-based' }
];

const SERVICE_LEVELS = [
  { value: 'basic', label: 'Basic' },
  { value: 'standard', label: 'Standard' },
  { value: 'premium', label: 'Premium' },
  { value: 'enterprise', label: 'Enterprise' }
];

// Enhanced validation schema with plan-based limits
const createPreferencesValidationSchema = (planLimits) => Yup.object({
  // Service preferences
  default_categories: Yup.array()
    .max(planLimits.maxCategories, `Maximum ${planLimits.maxCategories} categories allowed for your plan`)
    .required('At least one category is required'),
  default_target_segments: Yup.array()
    .max(planLimits.maxTargetSegments, `Maximum ${planLimits.maxTargetSegments} target segments allowed for your plan`)
    .required('At least one target segment is required'),
  frequently_used_industries: Yup.array()
    .max(planLimits.maxIndustries, `Maximum ${planLimits.maxIndustries} industries allowed for your plan`),

  // Account preferences
  display_name: Yup.string()
    .min(2, 'Display name must be at least 2 characters')
    .max(50, 'Display name must be less than 50 characters'),
  timezone: Yup.string().required('Timezone is required'),
  language: Yup.string().required('Language is required'),

  // Notification preferences
  email_notifications: Yup.boolean(),
  push_notifications: Yup.boolean(),
  sms_notifications: Yup.boolean(),
  notification_frequency: Yup.string().oneOf(['immediate', 'daily', 'weekly', 'never']),

  // Privacy preferences
  profile_visibility: Yup.string().oneOf(['public', 'private', 'contacts']),
  data_sharing: Yup.boolean(),
  analytics_tracking: Yup.boolean(),

  // Appearance preferences
  theme: Yup.string().oneOf(['light', 'dark', 'auto']),
  color_scheme: Yup.string(),
  font_size: Yup.number().min(12).max(24),
  compact_mode: Yup.boolean(),

  // Advanced preferences (plan-gated)
  auto_sync: Yup.boolean(),
  backup_frequency: Yup.string().oneOf(['daily', 'weekly', 'monthly', 'never']),
  performance_mode: Yup.boolean()
});

// Removed duplicate constants - using the ones defined above

/**
 * Enhanced User Preferences Manager - Comprehensive preferences management with advanced features
 * Implements plan-based feature restrictions and enterprise-grade preference capabilities
 */
const UserPreferencesManager = memo(forwardRef(({
  onPreferencesChange,
  onPreferencesSync,
  onPreferencesExport,
  onPreferencesImport,
  enableRealTimeSync = true,
  compactMode = false,
  defaultTab = 0
}, ref) => {
  const theme = useTheme();
  const { updateUsage, getPlanTier } = useSubscription();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Responsive design
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Core state management
  const preferencesRef = useRef(null);
  const syncTimeoutRef = useRef(null);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [allCategories, setAllCategories] = useState({ predefined_categories: [], custom_categories: [] });
  const [allSegments, setAllSegments] = useState({ predefined_segments: [], industry_specific_segments: {}, custom_segments: [] });

  // Enhanced state management
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [syncStatus, setSyncStatus] = useState(SYNC_STATUS.IDLE);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [preferencesHistory, setPreferencesHistory] = useState([]);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showConflictDialog, setShowConflictDialog] = useState(false);
  const [expandedSections, setExpandedSections] = useState(new Set(['services']));
  const [smartRecommendations, setSmartRecommendations] = useState([]);
  const [isDirty, setIsDirty] = useState(false);

  // Plan tier and feature access
  const planTier = getPlanTier();
  const planLimits = PLAN_LIMITS[planTier] || PLAN_LIMITS[1];

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    savePreferences: () => handleSavePreferences(),
    loadPreferences: () => loadData(),
    exportPreferences: () => handleExportPreferences(),
    importPreferences: (data) => handleImportPreferences(data),
    syncPreferences: () => handleSyncPreferences(),
    resetPreferences: () => handleResetPreferences(),
    getPreferencesData: () => formik.values,
    setPreferencesData: (data) => formik.setValues(data),
    validatePreferences: () => formik.validateForm(),
    getValidationErrors: () => formik.errors,
    isDirty: () => isDirty,
    getSyncStatus: () => syncStatus
  }), [formik, isDirty, syncStatus, handleSavePreferences, loadData, handleExportPreferences, handleImportPreferences, handleSyncPreferences, handleResetPreferences]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced form handling with plan-based validation
  const formik = useFormik({
    initialValues: {
      // Service preferences
      default_categories: [],
      default_target_segments: [],
      default_pricing_model: '',
      default_service_level: '',
      default_delivery_timeline: '',
      frequently_used_industries: [],

      // Account preferences
      display_name: '',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: 'en',

      // Notification preferences
      email_notifications: true,
      push_notifications: true,
      sms_notifications: false,
      notification_frequency: 'immediate',

      // Privacy preferences
      profile_visibility: 'public',
      data_sharing: true,
      analytics_tracking: true,

      // Appearance preferences
      theme: 'auto',
      color_scheme: 'purple',
      font_size: 14,
      compact_mode: false,

      // Advanced preferences
      auto_sync: true,
      backup_frequency: 'weekly',
      performance_mode: false
    },
    validationSchema: createPreferencesValidationSchema(planLimits),
    onSubmit: async (values) => {
      await handleSavePreferences(values);
    },
    enableReinitialize: true
  });

  // Enhanced save preferences handler
  const handleSavePreferences = useCallback(async (values = formik.values) => {
    try {
      setSaving(true);
      setError('');

      // Validate plan limits
      if (values.default_categories.length > planLimits.maxCategories) {
        throw new Error(`Maximum ${planLimits.maxCategories} categories allowed for your plan`);
      }

      if (values.default_target_segments.length > planLimits.maxTargetSegments) {
        throw new Error(`Maximum ${planLimits.maxTargetSegments} target segments allowed for your plan`);
      }

      // Update preferences
      await updateUserPreferences({
        service_preferences: values,
        account_preferences: {
          display_name: values.display_name,
          timezone: values.timezone,
          language: values.language
        },
        notification_preferences: {
          email_notifications: values.email_notifications,
          push_notifications: values.push_notifications,
          sms_notifications: values.sms_notifications,
          notification_frequency: values.notification_frequency
        },
        privacy_preferences: {
          profile_visibility: values.profile_visibility,
          data_sharing: values.data_sharing,
          analytics_tracking: values.analytics_tracking
        },
        appearance_preferences: {
          theme: values.theme,
          color_scheme: values.color_scheme,
          font_size: values.font_size,
          compact_mode: values.compact_mode
        },
        advanced_preferences: planLimits.advancedPreferences ? {
          auto_sync: values.auto_sync,
          backup_frequency: values.backup_frequency,
          performance_mode: values.performance_mode
        } : {}
      });

      // Update usage tracking
      await updateUsage('preferences_update', 1, {
        categories_count: values.default_categories.length,
        segments_count: values.default_target_segments.length,
        planTier
      });

      setSuccess('Preferences saved successfully!');
      setIsDirty(false);

      if (onPreferencesChange) {
        onPreferencesChange(values);
      }

      // Auto-sync if enabled
      if (planLimits.preferencesSync && enableRealTimeSync && values.auto_sync) {
        handleSyncPreferences();
      }

      // Add to history
      if (planLimits.preferencesHistory) {
        setPreferencesHistory(prev => [{
          timestamp: new Date().toISOString(),
          values: { ...values },
          action: 'save'
        }, ...prev].slice(0, 10));
      }

      announceToScreenReader('Preferences saved successfully');
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      console.error('Error saving preferences:', error);
      setError(error.response?.data?.detail || error.message || 'Failed to save preferences');
      announceToScreenReader('Failed to save preferences');
    } finally {
      setSaving(false);
    }
  }, [formik.values, planLimits, updateUsage, planTier, onPreferencesChange, enableRealTimeSync, announceToScreenReader, handleSyncPreferences]);

  // Enhanced sync preferences handler
  const handleSyncPreferences = useCallback(async () => {
    if (!planLimits.preferencesSync) {
      announceToScreenReader('Sync is not available in your current plan');
      return;
    }

    try {
      setSyncStatus(SYNC_STATUS.SYNCING);

      // Simulate sync process
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSyncStatus(SYNC_STATUS.SUCCESS);

      if (onPreferencesSync) {
        onPreferencesSync(formik.values);
      }

      announceToScreenReader('Preferences synchronized successfully');
      setTimeout(() => setSyncStatus(SYNC_STATUS.IDLE), 3000);
    } catch (error) {
      console.error('Sync error:', error);
      setSyncStatus(SYNC_STATUS.ERROR);
      announceToScreenReader('Sync failed');
      setTimeout(() => setSyncStatus(SYNC_STATUS.IDLE), 5000);
    }
  }, [planLimits.preferencesSync, onPreferencesSync, formik.values, announceToScreenReader]);

  // Export preferences handler
  const handleExportPreferences = useCallback(() => {
    if (!planLimits.importExport) {
      announceToScreenReader('Export is not available in your current plan');
      return;
    }

    const exportData = {
      preferences: formik.values,
      metadata: {
        exportedAt: new Date().toISOString(),
        planTier,
        version: '2.0.0'
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `preferences-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    if (onPreferencesExport) {
      onPreferencesExport(exportData);
    }

    announceToScreenReader('Preferences exported successfully');
  }, [planLimits.importExport, formik.values, planTier, onPreferencesExport, announceToScreenReader]);

  // Import preferences handler
  const handleImportPreferences = useCallback((data) => {
    if (!planLimits.importExport) {
      announceToScreenReader('Import is not available in your current plan');
      return;
    }

    try {
      const importedData = typeof data === 'string' ? JSON.parse(data) : data;

      if (importedData.preferences) {
        formik.setValues(importedData.preferences);
        setIsDirty(true);

        if (onPreferencesImport) {
          onPreferencesImport(importedData);
        }

        announceToScreenReader('Preferences imported successfully');
        setSuccess('Preferences imported successfully!');
        setTimeout(() => setSuccess(''), 3000);
      }
    } catch (error) {
      console.error('Import error:', error);
      setError('Failed to import preferences. Please check the file format.');
      announceToScreenReader('Failed to import preferences');
    }
  }, [planLimits.importExport, formik, onPreferencesImport, announceToScreenReader]);

  // Reset preferences handler
  const handleResetPreferences = useCallback(() => {
    formik.resetForm();
    setIsDirty(false);
    announceToScreenReader('Preferences reset to defaults');
    setSuccess('Preferences reset to defaults');
    setTimeout(() => setSuccess(''), 3000);
  }, [formik, announceToScreenReader]);

  // Enhanced data loading
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError('');

      // Load user preferences
      const preferences = await getUserPreferences();

      if (preferences) {
        const newValues = {
          // Service preferences
          default_categories: preferences.service_preferences?.default_categories || [],
          default_target_segments: preferences.service_preferences?.default_target_segments || [],
          default_pricing_model: preferences.service_preferences?.default_pricing_model || '',
          default_service_level: preferences.service_preferences?.default_service_level || '',
          default_delivery_timeline: preferences.service_preferences?.default_delivery_timeline || '',
          frequently_used_industries: preferences.service_preferences?.frequently_used_industries || [],

          // Account preferences
          display_name: preferences.account_preferences?.display_name || '',
          timezone: preferences.account_preferences?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
          language: preferences.account_preferences?.language || 'en',

          // Notification preferences
          email_notifications: preferences.notification_preferences?.email_notifications ?? true,
          push_notifications: preferences.notification_preferences?.push_notifications ?? true,
          sms_notifications: preferences.notification_preferences?.sms_notifications ?? false,
          notification_frequency: preferences.notification_preferences?.notification_frequency || 'immediate',

          // Privacy preferences
          profile_visibility: preferences.privacy_preferences?.profile_visibility || 'public',
          data_sharing: preferences.privacy_preferences?.data_sharing ?? true,
          analytics_tracking: preferences.privacy_preferences?.analytics_tracking ?? true,

          // Appearance preferences
          theme: preferences.appearance_preferences?.theme || 'auto',
          color_scheme: preferences.appearance_preferences?.color_scheme || 'purple',
          font_size: preferences.appearance_preferences?.font_size || 14,
          compact_mode: preferences.appearance_preferences?.compact_mode ?? false,

          // Advanced preferences
          auto_sync: preferences.advanced_preferences?.auto_sync ?? true,
          backup_frequency: preferences.advanced_preferences?.backup_frequency || 'weekly',
          performance_mode: preferences.advanced_preferences?.performance_mode ?? false
        };

        formik.setValues(newValues);
        setIsDirty(false);
      }

      // Load all categories and segments
      const [categoriesData, segmentsData] = await Promise.all([
        getAllCategories(),
        getAllTargetSegments()
      ]);

      setAllCategories(categoriesData);
      setAllSegments(segmentsData);

      // Load smart recommendations if available
      if (planLimits.smartRecommendations) {
        // Simulate smart recommendations
        setSmartRecommendations([
          { type: 'category', value: 'Content Marketing', confidence: 0.85 },
          { type: 'segment', value: 'Small Business Owners', confidence: 0.78 },
          { type: 'industry', value: 'Technology', confidence: 0.92 }
        ]);
      }

      announceToScreenReader('Preferences loaded successfully');
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Failed to load preferences data');
      announceToScreenReader('Failed to load preferences');
    } finally {
      setLoading(false);
    }
  }, [formik, planLimits.smartRecommendations, announceToScreenReader]);

  // Effects for monitoring and state management
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Track form changes for dirty state
  useEffect(() => {
    const handleFormChange = () => {
      setIsDirty(formik.dirty);
    };

    handleFormChange();
  }, [formik.dirty, formik.values]);

  // Auto-sync effect
  useEffect(() => {
    if (planLimits.preferencesSync && enableRealTimeSync && formik.values.auto_sync && isDirty) {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }

      syncTimeoutRef.current = setTimeout(() => {
        handleSyncPreferences();
      }, 5000); // Auto-sync after 5 seconds of inactivity
    }

    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, [planLimits.preferencesSync, enableRealTimeSync, formik.values.auto_sync, isDirty, handleSyncPreferences]);

  // Enhanced helper functions with search and filtering
  const getAllAvailableCategories = useCallback(() => {
    const categories = [
      ...allCategories.predefined_categories.map(cat => cat.name),
      ...allCategories.custom_categories.map(cat => cat.name)
    ];

    if (searchQuery) {
      return categories.filter(cat =>
        cat.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return categories;
  }, [allCategories, searchQuery]);

  const getAllAvailableSegments = useCallback(() => {
    const segments = [
      ...allSegments.predefined_segments,
      ...allSegments.custom_segments.map(seg => seg.name)
    ];

    // Add industry-specific segments
    Object.values(allSegments.industry_specific_segments).forEach(industrySegments => {
      segments.push(...industrySegments);
    });

    const uniqueSegments = [...new Set(segments)];

    if (searchQuery) {
      return uniqueSegments.filter(segment =>
        segment.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return uniqueSegments;
  }, [allSegments, searchQuery]);

  const getAllIndustries = useCallback(() => {
    const industries = Object.keys(allSegments.industry_specific_segments);

    // Add industries from custom segments
    allSegments.custom_segments.forEach(seg => {
      if (seg.industry && !industries.includes(seg.industry)) {
        industries.push(seg.industry);
      }
    });

    const sortedIndustries = industries.sort();

    if (searchQuery) {
      return sortedIndustries.filter(industry =>
        industry.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return sortedIndustries;
  }, [allSegments, searchQuery]);

  // Helper functions for future use
  // const handleMultiSelectChange = useCallback((fieldName) => (event) => {
  //   const value = typeof event.target.value === 'string'
  //     ? event.target.value.split(',')
  //     : event.target.value;
  //   formik.setFieldValue(fieldName, value);
  //   setIsDirty(true);
  // }, [formik]);

  // const handleChipDelete = useCallback((fieldName, valueToDelete) => {
  //   const currentValues = formik.values[fieldName];
  //   const updatedValues = currentValues.filter(value => value !== valueToDelete);
  //   formik.setFieldValue(fieldName, updatedValues);
  //   setIsDirty(true);
  // }, [formik]);

  // Tab change handler
  const handleTabChange = useCallback((event, newValue) => {
    setActiveTab(newValue);
    announceToScreenReader(`Switched to ${Object.keys(PREFERENCE_CATEGORIES)[newValue]} preferences`);
  }, [announceToScreenReader]);

  // Section expand/collapse handler
  const handleSectionToggle = useCallback((section) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  }, []);

  // Search handler
  const handleSearchChange = useCallback((event) => {
    setSearchQuery(event.target.value);
  }, []);

  // Filter handler
  const handleFilterChange = useCallback((event) => {
    setFilterCategory(event.target.value);
  }, []);

  // Enhanced loading component
  const LoadingComponent = useMemo(() => (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      p: theme.spacing(4),
      ...glassMorphismStyles
    }}>
      <CircularProgress
        size={48}
        sx={{
          color: ACE_COLORS.PURPLE,
          mb: 2
        }}
      />
      <Typography variant="body1" color="textSecondary">
        Loading preferences...
      </Typography>
    </Box>
  ), [theme, glassMorphismStyles]);

  if (loading) {
    return LoadingComponent;
  }

  return (
    <Card
      ref={preferencesRef}
      sx={{
        ...glassMorphismStyles,
        position: 'relative',
        overflow: 'visible'
      }}
    >
      <CardContent sx={{ p: compactMode ? theme.spacing(2) : theme.spacing(3) }}>
        {/* Enhanced Header */}
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: theme.spacing(3),
          flexWrap: 'wrap',
          gap: 1
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SettingsIcon sx={{ color: ACE_COLORS.PURPLE }} />
            <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
              User Preferences
            </Typography>
            {isDirty && (
              <Chip
                label="Unsaved"
                size="small"
                color="warning"
                variant="outlined"
                sx={{ ml: 1 }}
              />
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Sync Status */}
            <FeatureGate requiredPlan={2}>
              <Tooltip title={`Sync status: ${syncStatus}`}>
                <IconButton size="small">
                  {syncStatus === SYNC_STATUS.SYNCING ? (
                    <SyncingIcon sx={{ animation: 'spin 1s linear infinite' }} />
                  ) : syncStatus === SYNC_STATUS.SUCCESS ? (
                    <SyncIcon sx={{ color: 'success.main' }} />
                  ) : syncStatus === SYNC_STATUS.ERROR ? (
                    <SyncErrorIcon sx={{ color: 'error.main' }} />
                  ) : (
                    <SyncIcon />
                  )}
                </IconButton>
              </Tooltip>
            </FeatureGate>

            {/* Action Buttons */}
            <Tooltip title="Refresh preferences">
              <IconButton onClick={loadData} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <FeatureGate requiredPlan={2}>
              <Tooltip title="Export preferences">
                <IconButton onClick={handleExportPreferences} size="small">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </FeatureGate>

            <FeatureGate requiredPlan={2}>
              <Tooltip title="Import preferences">
                <IconButton onClick={() => setShowImportDialog(true)} size="small">
                  <UploadIcon />
                </IconButton>
              </Tooltip>
            </FeatureGate>
          </Box>
        </Box>

        {/* Enhanced Search and Filter */}
        <FeatureGate requiredPlan={2}>
          <Box sx={{
            display: 'flex',
            gap: 2,
            mb: 3,
            flexDirection: isMobile ? 'column' : 'row',
            alignItems: isMobile ? 'stretch' : 'center'
          }}>
            <TextField
              size="small"
              placeholder="Search preferences..."
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: ACE_COLORS.PURPLE }} />
                  </InputAdornment>
                )
              }}
              sx={{ flexGrow: 1 }}
            />

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Filter</InputLabel>
              <Select
                value={filterCategory}
                onChange={handleFilterChange}
                label="Filter"
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="services">Services</MenuItem>
                <MenuItem value="account">Account</MenuItem>
                <MenuItem value="notifications">Notifications</MenuItem>
                <MenuItem value="privacy">Privacy</MenuItem>
                <MenuItem value="appearance">Appearance</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </FeatureGate>

        {/* Error/Success Messages */}
        {error && (
          <Alert
            severity="error"
            sx={{ mb: theme.spacing(2) }}
            onClose={() => setError('')}
            action={
              <IconButton size="small" onClick={() => setError('')}>
                <CloseIcon />
              </IconButton>
            }
          >
            {error}
          </Alert>
        )}

        {success && (
          <Alert
            severity="success"
            sx={{ mb: theme.spacing(2) }}
            onClose={() => setSuccess('')}
            action={
              <IconButton size="small" onClick={() => setSuccess('')}>
                <CloseIcon />
              </IconButton>
            }
          >
            {success}
          </Alert>
        )}

        {/* Smart Recommendations */}
        <FeatureGate requiredPlan={2}>
          {smartRecommendations.length > 0 && (
            <Card sx={{
              mb: 3,
              background: alpha(ACE_COLORS.YELLOW, 0.1),
              border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`
            }}>
              <CardContent sx={{ py: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AIIcon sx={{ color: ACE_COLORS.YELLOW, mr: 1 }} />
                  <Typography variant="subtitle2" fontWeight={600}>
                    Smart Recommendations
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {smartRecommendations.map((rec, index) => (
                    <Chip
                      key={index}
                      label={`${rec.value} (${Math.round(rec.confidence * 100)}%)`}
                      size="small"
                      variant="outlined"
                      color="warning"
                      icon={<SmartIcon />}
                      onClick={() => {
                        // Add recommendation to appropriate field
                        if (rec.type === 'category') {
                          const current = formik.values.default_categories;
                          if (!current.includes(rec.value)) {
                            formik.setFieldValue('default_categories', [...current, rec.value]);
                          }
                        }
                      }}
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          )}
        </FeatureGate>

        {/* Enhanced Tabbed Interface */}
        <Box sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant={isMobile ? "scrollable" : "standard"}
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                minHeight: 48,
                textTransform: 'none',
                fontWeight: 500
              },
              '& .Mui-selected': {
                color: ACE_COLORS.PURPLE
              },
              '& .MuiTabs-indicator': {
                backgroundColor: ACE_COLORS.PURPLE
              }
            }}
          >
            <Tab
              icon={<SettingsIcon />}
              label="Services"
              iconPosition="start"
            />
            <Tab
              icon={<AccountIcon />}
              label="Account"
              iconPosition="start"
            />
            <Tab
              icon={<NotificationsIcon />}
              label="Notifications"
              iconPosition="start"
            />
            <Tab
              icon={<SecurityIcon />}
              label="Privacy"
              iconPosition="start"
            />
            <Tab
              icon={<AppearanceIcon />}
              label="Appearance"
              iconPosition="start"
            />
            <FeatureGate requiredPlan={2}>
              <Tab
                icon={<PerformanceIcon />}
                label="Advanced"
                iconPosition="start"
              />
            </FeatureGate>
          </Tabs>
        </Box>

        <form onSubmit={formik.handleSubmit}>
          {/* Services Tab */}
          {activeTab === 0 && (
            <Fade in timeout={300}>
              <Box>
                <Grid container spacing={3}>
                  {/* Default Categories */}
                  <Grid item xs={12}>
                    <Accordion
                      expanded={expandedSections.has('categories')}
                      onChange={() => handleSectionToggle('categories')}
                      sx={{ ...glassMorphismStyles, mb: 2 }}
                    >
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <StarIcon sx={{ color: ACE_COLORS.PURPLE }} />
                          <Typography variant="h6">Default Categories</Typography>
                          <Badge
                            badgeContent={formik.values.default_categories.length}
                            color="primary"
                            max={planLimits.maxCategories}
                          />
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <FormControl fullWidth>
                          <Autocomplete
                            multiple
                            options={getAllAvailableCategories()}
                            value={formik.values.default_categories}
                            onChange={(event, newValue) => {
                              if (newValue.length <= planLimits.maxCategories) {
                                formik.setFieldValue('default_categories', newValue);
                                setIsDirty(true);
                              } else {
                                setError(`Maximum ${planLimits.maxCategories} categories allowed for your plan`);
                              }
                            }}
                            renderTags={(value, getTagProps) =>
                              value.map((option, index) => (
                                <Chip
                                  {...getTagProps({ index })}
                                  key={option}
                                  label={option}
                                  size="small"
                                  color="primary"
                                  variant="outlined"
                                  icon={<StarIcon />}
                                />
                              ))
                            }
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Select Categories"
                                placeholder="Type to search categories..."
                                helperText={`Select up to ${planLimits.maxCategories} categories that you use most frequently`}
                              />
                            )}
                          />
                        </FormControl>
                      </AccordionDetails>
                    </Accordion>
                  </Grid>

                  {/* Default Target Segments */}
                  <Grid item xs={12}>
                    <Accordion
                      expanded={expandedSections.has('segments')}
                      onChange={() => handleSectionToggle('segments')}
                      sx={{ ...glassMorphismStyles, mb: 2 }}
                    >
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <StarIcon sx={{ color: ACE_COLORS.YELLOW }} />
                          <Typography variant="h6">Target Segments</Typography>
                          <Badge
                            badgeContent={formik.values.default_target_segments.length}
                            color="secondary"
                            max={planLimits.maxTargetSegments}
                          />
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <FormControl fullWidth>
                          <Autocomplete
                            multiple
                            options={getAllAvailableSegments()}
                            value={formik.values.default_target_segments}
                            onChange={(event, newValue) => {
                              if (newValue.length <= planLimits.maxTargetSegments) {
                                formik.setFieldValue('default_target_segments', newValue);
                                setIsDirty(true);
                              } else {
                                setError(`Maximum ${planLimits.maxTargetSegments} target segments allowed for your plan`);
                              }
                            }}
                            renderTags={(value, getTagProps) =>
                              value.map((option, index) => (
                                <Chip
                                  {...getTagProps({ index })}
                                  key={option}
                                  label={option}
                                  size="small"
                                  color="secondary"
                                  variant="outlined"
                                  icon={<StarIcon />}
                                />
                              ))
                            }
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Select Target Segments"
                                placeholder="Type to search segments..."
                                helperText={`Select up to ${planLimits.maxTargetSegments} target segments you commonly work with`}
                              />
                            )}
                          />
                        </FormControl>
                      </AccordionDetails>
                    </Accordion>
                  </Grid>

                  {/* Service Configuration */}
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }}>
                      <Chip label="Service Configuration" />
                    </Divider>
                  </Grid>

                  {/* Default Pricing Model */}
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Default Pricing Model</InputLabel>
                      <Select
                        value={formik.values.default_pricing_model}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setIsDirty(true);
                        }}
                        name="default_pricing_model"
                        label="Default Pricing Model"
                      >
                        <MenuItem value="">
                          <em>None</em>
                        </MenuItem>
                        {PRICING_MODELS.map((model) => (
                          <MenuItem key={model.value} value={model.value}>
                            {model.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Default Service Level */}
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Default Service Level</InputLabel>
                      <Select
                        value={formik.values.default_service_level}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setIsDirty(true);
                        }}
                        name="default_service_level"
                        label="Default Service Level"
                      >
                        <MenuItem value="">
                          <em>None</em>
                        </MenuItem>
                        {SERVICE_LEVELS.map((level) => (
                          <MenuItem key={level.value} value={level.value}>
                            {level.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* Industries and Content Strategy */}
                  <Grid item xs={12}>
                    <Accordion
                      expanded={expandedSections.has('industries')}
                      onChange={() => handleSectionToggle('industries')}
                      sx={{ ...glassMorphismStyles, mb: 2 }}
                    >
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <AnalyticsIcon sx={{ color: ACE_COLORS.PURPLE }} />
                          <Typography variant="h6">Industry Preferences</Typography>
                          <Badge
                            badgeContent={formik.values.frequently_used_industries.length}
                            color="info"
                            max={planLimits.maxIndustries}
                          />
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <FormControl fullWidth>
                          <Autocomplete
                            multiple
                            options={getAllIndustries()}
                            value={formik.values.frequently_used_industries}
                            onChange={(event, newValue) => {
                              if (newValue.length <= planLimits.maxIndustries) {
                                formik.setFieldValue('frequently_used_industries', newValue);
                                setIsDirty(true);
                              } else {
                                setError(`Maximum ${planLimits.maxIndustries} industries allowed for your plan`);
                              }
                            }}
                            renderTags={(value, getTagProps) =>
                              value.map((option, index) => (
                                <Chip
                                  {...getTagProps({ index })}
                                  key={option}
                                  label={option}
                                  size="small"
                                  color="info"
                                  variant="outlined"
                                />
                              ))
                            }
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label="Select Industries"
                                placeholder="Type to search industries..."
                                helperText={`Select up to ${planLimits.maxIndustries} industries for content generation context`}
                              />
                            )}
                          />
                        </FormControl>
                      </AccordionDetails>
                    </Accordion>
                  </Grid>
                </Grid>
              </Box>
            </Fade>
          )}

          {/* Account Tab */}
          {activeTab === 1 && (
            <Fade in timeout={300}>
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      name="display_name"
                      label="Display Name"
                      value={formik.values.display_name}
                      onChange={(e) => {
                        formik.handleChange(e);
                        setIsDirty(true);
                      }}
                      error={formik.touched.display_name && Boolean(formik.errors.display_name)}
                      helperText={formik.touched.display_name && formik.errors.display_name}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Timezone</InputLabel>
                      <Select
                        value={formik.values.timezone}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setIsDirty(true);
                        }}
                        name="timezone"
                        label="Timezone"
                      >
                        {Intl.supportedValuesOf('timeZone').slice(0, 50).map((tz) => (
                          <MenuItem key={tz} value={tz}>
                            {tz}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Language</InputLabel>
                      <Select
                        value={formik.values.language}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setIsDirty(true);
                        }}
                        name="language"
                        label="Language"
                      >
                        <MenuItem value="en">English</MenuItem>
                        <MenuItem value="es">Spanish</MenuItem>
                        <MenuItem value="fr">French</MenuItem>
                        <MenuItem value="de">German</MenuItem>
                        <MenuItem value="it">Italian</MenuItem>
                        <MenuItem value="pt">Portuguese</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      name="default_delivery_timeline"
                      label="Default Delivery Timeline"
                      placeholder="e.g., 2-4 weeks, Ongoing monthly"
                      value={formik.values.default_delivery_timeline}
                      onChange={(e) => {
                        formik.handleChange(e);
                        setIsDirty(true);
                      }}
                    />
                  </Grid>
                </Grid>
              </Box>
            </Fade>
          )}

          {/* Notifications Tab */}
          {activeTab === 2 && (
            <Fade in timeout={300}>
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Notification Preferences
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                      Configure how you want to receive updates about ICP generation, content strategies, and system activities.
                    </Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.email_notifications}
                            onChange={(e) => {
                              formik.setFieldValue('email_notifications', e.target.checked);
                              setIsDirty(true);
                            }}
                            color="primary"
                          />
                        }
                        label="Email Notifications"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.push_notifications}
                            onChange={(e) => {
                              formik.setFieldValue('push_notifications', e.target.checked);
                              setIsDirty(true);
                            }}
                            color="primary"
                          />
                        }
                        label="Push Notifications"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.sms_notifications}
                            onChange={(e) => {
                              formik.setFieldValue('sms_notifications', e.target.checked);
                              setIsDirty(true);
                            }}
                            color="primary"
                          />
                        }
                        label="SMS Notifications"
                      />
                    </FormGroup>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Notification Frequency</InputLabel>
                      <Select
                        value={formik.values.notification_frequency}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setIsDirty(true);
                        }}
                        name="notification_frequency"
                        label="Notification Frequency"
                      >
                        <MenuItem value="immediate">Immediate</MenuItem>
                        <MenuItem value="daily">Daily Digest</MenuItem>
                        <MenuItem value="weekly">Weekly Summary</MenuItem>
                        <MenuItem value="never">Never</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            </Fade>
          )}

          {/* Privacy Tab */}
          {activeTab === 3 && (
            <Fade in timeout={300}>
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Privacy & Security Settings
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                      Control your data privacy and security preferences for ICP and content generation.
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Profile Visibility</InputLabel>
                      <Select
                        value={formik.values.profile_visibility}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setIsDirty(true);
                        }}
                        name="profile_visibility"
                        label="Profile Visibility"
                      >
                        <MenuItem value="public">Public</MenuItem>
                        <MenuItem value="private">Private</MenuItem>
                        <MenuItem value="contacts">Contacts Only</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12}>
                    <FormGroup>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.data_sharing}
                            onChange={(e) => {
                              formik.setFieldValue('data_sharing', e.target.checked);
                              setIsDirty(true);
                            }}
                            color="primary"
                          />
                        }
                        label="Allow data sharing for improved AI recommendations"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.analytics_tracking}
                            onChange={(e) => {
                              formik.setFieldValue('analytics_tracking', e.target.checked);
                              setIsDirty(true);
                            }}
                            color="primary"
                          />
                        }
                        label="Enable analytics tracking for usage insights"
                      />
                    </FormGroup>
                  </Grid>
                </Grid>
              </Box>
            </Fade>
          )}

          {/* Appearance Tab */}
          {activeTab === 4 && (
            <Fade in timeout={300}>
              <Box>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Appearance & Interface
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                      Customize the look and feel of your ACE Social experience.
                    </Typography>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Theme</InputLabel>
                      <Select
                        value={formik.values.theme}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setIsDirty(true);
                        }}
                        name="theme"
                        label="Theme"
                      >
                        <MenuItem value="light">Light</MenuItem>
                        <MenuItem value="dark">Dark</MenuItem>
                        <MenuItem value="auto">Auto (System)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Color Scheme</InputLabel>
                      <Select
                        value={formik.values.color_scheme}
                        onChange={(e) => {
                          formik.handleChange(e);
                          setIsDirty(true);
                        }}
                        name="color_scheme"
                        label="Color Scheme"
                      >
                        <MenuItem value="purple">Purple (Default)</MenuItem>
                        <MenuItem value="blue">Blue</MenuItem>
                        <MenuItem value="green">Green</MenuItem>
                        <MenuItem value="orange">Orange</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Font Size</Typography>
                    <Slider
                      value={formik.values.font_size}
                      onChange={(e, newValue) => {
                        formik.setFieldValue('font_size', newValue);
                        setIsDirty(true);
                      }}
                      min={12}
                      max={24}
                      step={1}
                      marks
                      valueLabelDisplay="auto"
                      sx={{ color: ACE_COLORS.PURPLE }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={formik.values.compact_mode}
                          onChange={(e) => {
                            formik.setFieldValue('compact_mode', e.target.checked);
                            setIsDirty(true);
                          }}
                          color="primary"
                        />
                      }
                      label="Compact Mode"
                    />
                  </Grid>
                </Grid>
              </Box>
            </Fade>
          )}

          {/* Advanced Tab */}
          {activeTab === 5 && (
            <FeatureGate requiredPlan={2}>
              <Fade in timeout={300}>
                <Box>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom>
                        Advanced Settings
                      </Typography>
                      <Typography variant="body2" color="textSecondary" paragraph>
                        Advanced configuration options for power users.
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <FormGroup>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={formik.values.auto_sync}
                              onChange={(e) => {
                                formik.setFieldValue('auto_sync', e.target.checked);
                                setIsDirty(true);
                              }}
                              color="primary"
                            />
                          }
                          label="Auto-sync preferences across devices"
                        />
                        <FormControlLabel
                          control={
                            <Switch
                              checked={formik.values.performance_mode}
                              onChange={(e) => {
                                formik.setFieldValue('performance_mode', e.target.checked);
                                setIsDirty(true);
                              }}
                              color="primary"
                            />
                          }
                          label="Performance mode (faster processing)"
                        />
                      </FormGroup>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Backup Frequency</InputLabel>
                        <Select
                          value={formik.values.backup_frequency}
                          onChange={(e) => {
                            formik.handleChange(e);
                            setIsDirty(true);
                          }}
                          name="backup_frequency"
                          label="Backup Frequency"
                        >
                          <MenuItem value="daily">Daily</MenuItem>
                          <MenuItem value="weekly">Weekly</MenuItem>
                          <MenuItem value="monthly">Monthly</MenuItem>
                          <MenuItem value="never">Never</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </Box>
              </Fade>
            </FeatureGate>
          )}

          {/* Save Button */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mt: 4,
            pt: 2,
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                onClick={handleResetPreferences}
                disabled={saving}
                startIcon={<RefreshIcon />}
              >
                Reset
              </Button>

              <FeatureGate requiredPlan={2}>
                <Button
                  variant="outlined"
                  onClick={handleSyncPreferences}
                  disabled={saving || syncStatus === SYNC_STATUS.SYNCING}
                  startIcon={syncStatus === SYNC_STATUS.SYNCING ? <SyncingIcon /> : <SyncIcon />}
                >
                  Sync
                </Button>
              </FeatureGate>

              <FeatureGate requiredPlan={3}>
                <Button
                  variant="outlined"
                  onClick={() => setShowExportDialog(true)}
                  disabled={saving}
                  startIcon={<DownloadIcon />}
                >
                  Export
                </Button>
              </FeatureGate>
            </Box>

            <Button
              type="submit"
              variant="contained"
              startIcon={saving ? <CircularProgress size={16} /> : <SaveIcon />}
              disabled={saving || !formik.isValid || !isDirty}
              sx={{
                minWidth: 150,
                background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE}, ${ACE_COLORS.YELLOW})`,
                color: 'white',
                '&:hover': {
                  background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE}dd, ${ACE_COLORS.YELLOW}dd)`
                }
              }}
            >
              {saving ? 'Saving...' : 'Save Preferences'}
            </Button>
          </Box>
        </form>

        {/* Import Dialog */}
        <Dialog
          open={showImportDialog}
          onClose={() => setShowImportDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UploadIcon />
              Import Preferences
            </Box>
          </DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="textSecondary" paragraph>
              Import your preferences from a previously exported file. This will replace your current settings.
            </Typography>
            <input
              type="file"
              accept=".json"
              onChange={(e) => {
                const file = e.target.files[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (event) => {
                    try {
                      const data = JSON.parse(event.target.result);
                      handleImportPreferences(data);
                      setShowImportDialog(false);
                    } catch {
                      setError('Invalid file format. Please select a valid preferences file.');
                    }
                  };
                  reader.readAsText(file);
                }
              }}
              style={{ width: '100%', padding: '10px', marginTop: '10px' }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowImportDialog(false)}>Cancel</Button>
          </DialogActions>
        </Dialog>

        {/* Export Dialog */}
        <Dialog
          open={showExportDialog}
          onClose={() => setShowExportDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <DownloadIcon />
              Export Preferences
            </Box>
          </DialogTitle>
          <DialogContent>
            <Typography variant="body2" color="textSecondary" paragraph>
              Export your current preferences to a file for backup or sharing.
            </Typography>
            <Typography variant="body2" paragraph>
              This will include:
            </Typography>
            <List dense>
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="primary" />
                </ListItemIcon>
                <ListItemText primary="Service preferences (categories, segments, industries)" />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="primary" />
                </ListItemIcon>
                <ListItemText primary="Account settings" />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="primary" />
                </ListItemIcon>
                <ListItemText primary="Notification preferences" />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircleIcon color="primary" />
                </ListItemIcon>
                <ListItemText primary="Privacy and appearance settings" />
              </ListItem>
            </List>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowExportDialog(false)}>Cancel</Button>
            <Button
              onClick={() => {
                handleExportPreferences();
                setShowExportDialog(false);
              }}
              variant="contained"
              startIcon={<DownloadIcon />}
            >
              Export
            </Button>
          </DialogActions>
        </Dialog>

        {/* Preferences History Dialog */}
        <FeatureGate requiredPlan={2}>
          <Dialog
            open={showConflictDialog}
            onClose={() => setShowConflictDialog(false)}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <HistoryIcon />
                Preferences History
              </Box>
            </DialogTitle>
            <DialogContent>
              {preferencesHistory.length > 0 ? (
                <List>
                  {preferencesHistory.map((entry, index) => (
                    <ListItem key={index} divider>
                      <ListItemIcon>
                        <HistoryIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={`${entry.action} - ${new Date(entry.timestamp).toLocaleString()}`}
                        secondary={`Categories: ${entry.values.default_categories?.length || 0}, Segments: ${entry.values.default_target_segments?.length || 0}`}
                      />
                      <ListItemSecondaryAction>
                        <Button
                          size="small"
                          onClick={() => {
                            formik.setValues(entry.values);
                            setIsDirty(true);
                            setShowConflictDialog(false);
                          }}
                        >
                          Restore
                        </Button>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="textSecondary">
                  No preferences history available.
                </Typography>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setShowConflictDialog(false)}>Close</Button>
            </DialogActions>
          </Dialog>
        </FeatureGate>

        {/* Sync Status Snackbar */}
        <Snackbar
          open={syncStatus === SYNC_STATUS.SUCCESS}
          autoHideDuration={3000}
          onClose={() => setSyncStatus(SYNC_STATUS.IDLE)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert severity="success" onClose={() => setSyncStatus(SYNC_STATUS.IDLE)}>
            Preferences synchronized successfully
          </Alert>
        </Snackbar>

        <Snackbar
          open={syncStatus === SYNC_STATUS.ERROR}
          autoHideDuration={5000}
          onClose={() => setSyncStatus(SYNC_STATUS.IDLE)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert severity="error" onClose={() => setSyncStatus(SYNC_STATUS.IDLE)}>
            Sync failed. Please try again.
          </Alert>
        </Snackbar>
      </CardContent>
    </Card>
  );
}));

UserPreferencesManager.displayName = 'UserPreferencesManager';

UserPreferencesManager.propTypes = {
  /** Callback when preferences change */
  onPreferencesChange: PropTypes.func,
  /** Callback when preferences sync */
  onPreferencesSync: PropTypes.func,
  /** Callback when preferences export */
  onPreferencesExport: PropTypes.func,
  /** Callback when preferences import */
  onPreferencesImport: PropTypes.func,
  /** Enable real-time sync */
  enableRealTimeSync: PropTypes.bool,
  /** Enable advanced search */
  enableAdvancedSearch: PropTypes.bool,
  /** Enable preferences history */
  enablePreferencesHistory: PropTypes.bool,
  /** Show preferences analytics */
  showPreferencesAnalytics: PropTypes.bool,
  /** Enable compact mode */
  compactMode: PropTypes.bool,
  /** Default active tab */
  defaultTab: PropTypes.number
};

export default UserPreferencesManager;

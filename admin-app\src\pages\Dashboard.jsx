import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  Button,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Divider,
  LinearProgress,
  Skeleton,
  Badge,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  CardHeader,
  CardActions
} from '@mui/material';
import {
  People as UsersIcon,
  LocalOffer as CouponsIcon,
  CardGiftcard as AppSumoIcon,
  Api as ApiIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Speed as SpeedIcon,
  Error as ErrorIcon,
  CheckCircle as HealthyIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
  CloudQueue as CloudIcon,
  MonetizationOn as RevenueIcon,
  Group as ActiveUsersIcon,
  NewReleases as NewUsersIcon,
  BarChart as AnalyticsIcon,
  BugReport as BugIcon,
  Schedule as ScheduleIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format, subDays, subHours, isToday, isYesterday } from 'date-fns';
import { useTheme } from '@mui/material/styles';

import api from '../api';
import { ADMIN_CONFIG } from '../config';
import StablePageWrapper from '../components/common/StablePageWrapper';
import { apiManagementService } from '../services/apiManagement';
import { debounce, SafeTimer } from '../utils/debounce';

const Dashboard = () => {
  const theme = useTheme();

  // State management
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [autoRefresh, setAutoRefresh] = useState(ADMIN_CONFIG.ENABLE_AUTO_REFRESH || false);
  const [activeTab, setActiveTab] = useState(0);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // Data states
  const [stats, setStats] = useState(null);
  const [apiMetrics, setApiMetrics] = useState(null);
  const [userAnalytics, setUserAnalytics] = useState(null);
  const [financeData, setFinanceData] = useState(null);
  const [systemHealth, setSystemHealth] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [alerts, setAlerts] = useState([]);
  const [performanceMetrics, setPerformanceMetrics] = useState([]);

  // Refs for cleanup
  const refreshIntervalRef = useRef(null);
  const isMountedRef = useRef(true);
  // WebSocket disabled to prevent authentication failures causing reloads

  // COMPLETELY STATIC fetchDashboardData - NO API CALLS, NO WEBSOCKETS
  const fetchDashboardData = useCallback(() => {
    console.log('Manual refresh triggered - static data only');

    // Just update the timestamp - no API calls, no async operations
    setLastUpdated(new Date());

    // Regenerate mock data immediately
    setPerformanceMetrics(generatePerformanceData());
    setRecentActivity(generateRecentActivity());
    setAlerts(generateAlerts());
  }, []);

  // Simplified initial data load - NO DEPENDENCIES TO PREVENT LOOPS
  useEffect(() => {
    let mounted = true;

    const loadInitialData = async () => {
      if (!mounted) return;

      try {
        setLoading(true);

        // Set static fallback data immediately to prevent reloading
        const staticStats = {
          total_users: 1250,
          active_users: 890,
          new_users_today: 15,
          total_coupons: 45,
          total_appsumo_codes: 120,
          active_subscriptions: 340,
          coupons_used_today: 8
        };

        const staticApiMetrics = {
          timestamp: new Date().toISOString(),
          requests_last_hour: 2500,
          successful_requests: 2375,
          failed_requests: 125,
          success_rate: 95.0,
          error_rate: 5.0,
          avg_response_time_ms: 185.5,
          unique_users_last_hour: 145,
          api_status: { active: 25, disabled: 2, maintenance: 1, total: 28 },
          top_endpoints: [
            { endpoint: 'GET /api/content', requests: 850, avg_response_time: 120.5, error_rate: 2.1 },
            { endpoint: 'POST /api/auth/login', requests: 420, avg_response_time: 95.2, error_rate: 1.8 }
          ]
        };

        if (mounted) {
          setStats(staticStats);
          setApiMetrics(staticApiMetrics);
          setUserAnalytics({ total_users: 1250 });
          setFinanceData({ current_mrr: 15000 });
          setSystemHealth({ status: 'healthy' });
          setPerformanceMetrics(generatePerformanceData());
          setRecentActivity(generateRecentActivity());
          setAlerts(generateAlerts());
          setLastUpdated(new Date());
          setLoading(false);
        }

      } catch (err) {
        console.error('Initial data load failed:', err);
        if (mounted) {
          setError('Dashboard loaded with limited functionality');
          setLoading(false);
        }
      }
    };

    loadInitialData();

    return () => {
      mounted = false;
    };
  }, []); // EMPTY DEPENDENCY ARRAY - ONLY RUN ONCE

  // Cleanup effect - SIMPLIFIED
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      // Clear any intervals
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
    };
  }, []);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle auto-refresh toggle - DISABLED FOR STABILITY
  const handleAutoRefreshToggle = (event) => {
    // Temporarily disabled to prevent reloading issues
    console.log('Auto-refresh toggle disabled for stability');
    // setAutoRefresh(event.target.checked);
  };

  // Manual refresh with debounce to prevent rapid calls
  const debouncedFetchData = useCallback(
    debounce(fetchDashboardData, 2000), // 2 second debounce
    [fetchDashboardData]
  );

  const handleManualRefresh = useCallback(() => {
    if (!loading) {
      debouncedFetchData();
    }
  }, [loading, debouncedFetchData]);

  // Helper functions for mock data generation
  const generatePerformanceData = () => {
    const data = [];
    for (let i = 23; i >= 0; i--) {
      const time = subHours(new Date(), i);
      data.push({
        time: format(time, 'HH:mm'),
        requests: Math.floor(Math.random() * 1000) + 500,
        responseTime: Math.floor(Math.random() * 200) + 100,
        errorRate: Math.random() * 5,
        activeUsers: Math.floor(Math.random() * 100) + 50
      });
    }
    return data;
  };

  const generateRecentActivity = () => [
    {
      id: 1,
      type: 'user_registration',
      message: 'New user registered: <EMAIL>',
      timestamp: new Date(),
      severity: 'info'
    },
    {
      id: 2,
      type: 'api_error',
      message: 'API endpoint /api/content experiencing high error rate',
      timestamp: subHours(new Date(), 1),
      severity: 'warning'
    },
    {
      id: 3,
      type: 'system_update',
      message: 'System maintenance completed successfully',
      timestamp: subHours(new Date(), 2),
      severity: 'success'
    },
    {
      id: 4,
      type: 'subscription',
      message: 'New Dominator plan subscription activated',
      timestamp: subHours(new Date(), 3),
      severity: 'success'
    },
    {
      id: 5,
      type: 'security',
      message: 'Failed login attempts detected from IP *************',
      timestamp: subHours(new Date(), 4),
      severity: 'error'
    }
  ];

  const generateAlerts = () => [
    {
      id: 1,
      title: 'High API Error Rate',
      message: 'API error rate has exceeded 5% threshold',
      severity: 'error',
      timestamp: new Date(),
      resolved: false
    },
    {
      id: 2,
      title: 'Database Connection Pool',
      message: 'Database connection pool utilization at 85%',
      severity: 'warning',
      timestamp: subHours(new Date(), 2),
      resolved: false
    },
    {
      id: 3,
      title: 'Disk Space Warning',
      message: 'Server disk space usage at 80%',
      severity: 'warning',
      timestamp: subHours(new Date(), 6),
      resolved: true
    }
  ];

  // Format helpers
  const formatNumber = (num) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num?.toString() || '0';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  const formatPercentage = (value) => {
    return `${(value || 0).toFixed(1)}%`;
  };

  const getTimeAgo = (date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return format(date, 'MMM d, HH:mm');
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'error': return theme.palette.error.main;
      case 'warning': return theme.palette.warning.main;
      case 'success': return theme.palette.success.main;
      case 'info': return theme.palette.info.main;
      default: return theme.palette.text.secondary;
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'error': return <ErrorIcon />;
      case 'warning': return <WarningIcon />;
      case 'success': return <HealthyIcon />;
      case 'info': return <NotificationsIcon />;
      default: return <NotificationsIcon />;
    }
  };

  // Enhanced StatCard component with glass morphism
  const EnhancedStatCard = ({
    title,
    value,
    icon,
    color = 'primary',
    trend,
    trendValue,
    subtitle,
    onClick,
    loading = false
  }) => (
    <Card
      variant="glass"
      sx={{
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.2s ease-in-out',
        '&:hover': onClick ? {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[8]
        } : {}
      }}
      onClick={onClick}
    >
      <CardContent sx={{ p: 3 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box flex={1}>
            <Typography
              color="text.secondary"
              gutterBottom
              variant="body2"
              sx={{ fontWeight: 500 }}
            >
              {title}
            </Typography>
            {loading ? (
              <Skeleton variant="text" width={80} height={40} />
            ) : (
              <Typography variant="h4" component="div" sx={{ fontWeight: 600, mb: 1 }}>
                {value}
              </Typography>
            )}
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
            {trend && trendValue && (
              <Box display="flex" alignItems="center" mt={1}>
                {trend === 'up' ? (
                  <TrendingUpIcon fontSize="small" color="success" />
                ) : (
                  <TrendingDownIcon fontSize="small" color="error" />
                )}
                <Typography
                  variant="body2"
                  color={trend === 'up' ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5, fontWeight: 500 }}
                >
                  {trendValue}
                </Typography>
              </Box>
            )}
          </Box>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: `${color}.main`,
              borderRadius: '50%',
              width: 56,
              height: 56,
              color: 'white',
              ml: 2
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  // Performance Chart Component
  const PerformanceChart = ({ data, height = 300 }) => (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
        <XAxis
          dataKey="time"
          tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
        />
        <YAxis
          tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
        />
        <RechartsTooltip
          contentStyle={{
            backgroundColor: theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: theme.shape.borderRadius
          }}
        />
        <Legend />
        <Line
          type="monotone"
          dataKey="requests"
          stroke={theme.palette.primary.main}
          strokeWidth={2}
          name="Requests"
        />
        <Line
          type="monotone"
          dataKey="responseTime"
          stroke={theme.palette.secondary.main}
          strokeWidth={2}
          name="Response Time (ms)"
        />
        <Line
          type="monotone"
          dataKey="activeUsers"
          stroke={theme.palette.success.main}
          strokeWidth={2}
          name="Active Users"
        />
      </LineChart>
    </ResponsiveContainer>
  );

  // API Health Status Component
  const ApiHealthStatus = ({ metrics }) => {
    if (!metrics) {
      return (
        <Card variant="glass">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              API Health Status
            </Typography>
            <Skeleton variant="rectangular" height={200} />
          </CardContent>
        </Card>
      );
    }

    return (
      <Card variant="glass">
        <CardHeader
          title="API Health Status"
          subheader={`Last updated: ${getTimeAgo(lastUpdated)}`}
          action={
            <Tooltip title="Refresh API metrics">
              <IconButton onClick={() => apiManagementService.getRealTimeMetrics()}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          }
        />
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Box textAlign="center">
                <Typography variant="h4" color="primary">
                  {metrics.api_status?.active || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active APIs
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6}>
              <Box textAlign="center">
                <Typography variant="h4" color="error">
                  {formatPercentage(metrics.error_rate)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Error Rate
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Top Endpoints (Last Hour)
              </Typography>
              {metrics.top_endpoints?.slice(0, 3).map((endpoint, index) => (
                <Box key={index} display="flex" justifyContent="space-between" alignItems="center" py={0.5}>
                  <Typography variant="body2" noWrap>
                    {endpoint.endpoint}
                  </Typography>
                  <Chip
                    label={`${endpoint.requests} req`}
                    size="small"
                    color={endpoint.error_rate > 5 ? 'error' : 'default'}
                  />
                </Box>
              ))}
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  // Recent Activity Component
  const RecentActivityCard = () => (
    <Card variant="glass">
      <CardHeader
        title="Recent Activity"
        subheader={`${recentActivity.length} recent events`}
        action={
          <Tooltip title="View all activity">
            <IconButton>
              <ViewIcon />
            </IconButton>
          </Tooltip>
        }
      />
      <CardContent sx={{ pt: 0 }}>
        <List dense>
          {recentActivity.slice(0, 5).map((activity) => (
            <ListItem key={activity.id} sx={{ px: 0 }}>
              <ListItemIcon>
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: getSeverityColor(activity.severity)
                  }}
                >
                  {getSeverityIcon(activity.severity)}
                </Avatar>
              </ListItemIcon>
              <ListItemText
                primary={activity.message}
                secondary={getTimeAgo(activity.timestamp)}
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );

  // System Alerts Component
  const SystemAlertsCard = () => {
    const unresolvedAlerts = alerts.filter(alert => !alert.resolved);

    return (
      <Card variant="glass">
        <CardHeader
          title={
            <Box display="flex" alignItems="center">
              <Typography variant="h6">System Alerts</Typography>
              {unresolvedAlerts.length > 0 && (
                <Badge badgeContent={unresolvedAlerts.length} color="error" sx={{ ml: 1 }}>
                  <WarningIcon />
                </Badge>
              )}
            </Box>
          }
          subheader={`${unresolvedAlerts.length} active alerts`}
        />
        <CardContent sx={{ pt: 0 }}>
          {unresolvedAlerts.length === 0 ? (
            <Box textAlign="center" py={2}>
              <HealthyIcon color="success" sx={{ fontSize: 48, mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                All systems operational
              </Typography>
            </Box>
          ) : (
            <List dense>
              {unresolvedAlerts.map((alert) => (
                <ListItem key={alert.id} sx={{ px: 0 }}>
                  <ListItemIcon>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: getSeverityColor(alert.severity)
                      }}
                    >
                      {getSeverityIcon(alert.severity)}
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={alert.title}
                    secondary={alert.message}
                    primaryTypographyProps={{ variant: 'body2', fontWeight: 500 }}
                    secondaryTypographyProps={{ variant: 'caption' }}
                  />
                  <ListItemSecondaryAction>
                    <Typography variant="caption" color="text.secondary">
                      {getTimeAgo(alert.timestamp)}
                    </Typography>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    );
  };

  // Loading state
  if (loading && !stats) {
    return (
      <StablePageWrapper>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
        </Box>
      </StablePageWrapper>
    );
  }

  // Error boundary for critical errors
  if (error && !stats) {
    return (
      <StablePageWrapper>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
          <ErrorIcon color="error" sx={{ fontSize: 64, mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Dashboard Temporarily Unavailable
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {error}
          </Typography>
          <Button variant="contained" onClick={handleManualRefresh} disabled={loading}>
            Retry
          </Button>
        </Box>
      </StablePageWrapper>
    );
  }

  // Error boundary for critical errors
  if (error && !stats) {
    return (
      <StablePageWrapper>
        <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" minHeight="400px">
          <ErrorIcon color="error" sx={{ fontSize: 64, mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Dashboard Temporarily Unavailable
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {error}
          </Typography>
          <Button variant="contained" onClick={handleManualRefresh} disabled={loading}>
            Retry
          </Button>
        </Box>
      </StablePageWrapper>
    );
  }

  return (
    <StablePageWrapper>
      {/* Header Section */}
      <Box mb={3}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Box>
            <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
              Admin Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Comprehensive platform monitoring and management center
            </Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={false}
                  onChange={handleAutoRefreshToggle}
                  color="primary"
                  disabled={true}
                />
              }
              label="Auto Refresh (Disabled)"
            />
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={handleManualRefresh}
              disabled={loading}
            >
              Refresh
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Typography variant="caption" color="text.secondary">
          Last updated: {format(lastUpdated, 'MMM d, yyyy HH:mm:ss')}
        </Typography>
      </Box>

      {/* Navigation Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<AssessmentIcon />} label="Overview" />
          <Tab icon={<ApiIcon />} label="API Health" />
          <Tab icon={<TimelineIcon />} label="Performance" />
          <Tab icon={<SecurityIcon />} label="Security" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {activeTab === 0 && (
        <Grid container spacing={3}>
          {/* Key Metrics Row */}
          <Grid item xs={12} sm={6} md={3}>
            <EnhancedStatCard
              title="Total Users"
              value={formatNumber(stats?.total_users || 0)}
              icon={<UsersIcon fontSize="large" />}
              color={theme.palette.primary.main}
              trend="up"
              trendValue="+12.5%"
              subtitle="vs last month"
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <EnhancedStatCard
              title="Active APIs"
              value={apiMetrics?.api_status?.active || 0}
              icon={<ApiIcon fontSize="large" />}
              color={theme.palette.success.main}
              trend="up"
              trendValue="+2"
              subtitle="endpoints online"
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <EnhancedStatCard
              title="Response Time"
              value={`${Math.round(apiMetrics?.avg_response_time_ms || 0)}ms`}
              icon={<SpeedIcon fontSize="large" />}
              color={theme.palette.info.main}
              trend="down"
              trendValue="-15ms"
              subtitle="avg last hour"
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <EnhancedStatCard
              title="Monthly Revenue"
              value={formatCurrency(financeData?.current_mrr || 0)}
              icon={<RevenueIcon fontSize="large" />}
              color={theme.palette.warning.main}
              trend="up"
              trendValue="+8.3%"
              subtitle="MRR growth"
              loading={loading}
            />
          </Grid>

          {/* Secondary Metrics Row */}
          <Grid item xs={12} sm={6} md={3}>
            <EnhancedStatCard
              title="New Users Today"
              value={stats?.new_users_today || 0}
              icon={<NewUsersIcon fontSize="large" />}
              color={theme.palette.secondary.main}
              subtitle="24h registrations"
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <EnhancedStatCard
              title="Error Rate"
              value={formatPercentage(apiMetrics?.error_rate || 0)}
              icon={<ErrorIcon fontSize="large" />}
              color={theme.palette.error.main}
              trend={apiMetrics?.error_rate > 5 ? "up" : "down"}
              trendValue={apiMetrics?.error_rate > 5 ? "+2.1%" : "-0.8%"}
              subtitle="last hour"
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <EnhancedStatCard
              title="Active Subscriptions"
              value={stats?.active_subscriptions || 0}
              icon={<ActiveUsersIcon fontSize="large" />}
              color={theme.palette.success.main}
              subtitle="paying customers"
              loading={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <EnhancedStatCard
              title="System Uptime"
              value="99.9%"
              icon={<CloudIcon fontSize="large" />}
              color={theme.palette.info.main}
              subtitle="30 day average"
              loading={loading}
            />
          </Grid>

          {/* Charts and Analytics */}
          <Grid item xs={12} lg={8}>
            <Card variant="glass">
              <CardHeader
                title="Performance Trends"
                subheader="Real-time system metrics over the last 24 hours"
                action={
                  <Tooltip title="View detailed analytics">
                    <IconButton>
                      <AnalyticsIcon />
                    </IconButton>
                  </Tooltip>
                }
              />
              <CardContent>
                <PerformanceChart data={performanceMetrics} height={350} />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} lg={4}>
            <ApiHealthStatus metrics={apiMetrics} />
          </Grid>

          {/* Activity and Alerts */}
          <Grid item xs={12} md={6}>
            <RecentActivityCard />
          </Grid>

          <Grid item xs={12} md={6}>
            <SystemAlertsCard />
          </Grid>
        </Grid>
      )}

      {activeTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card variant="glass">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  API Health Dashboard
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Detailed API monitoring and management interface will be displayed here.
                  This integrates with the API Management System.
                </Typography>
                <Box mt={2}>
                  <Button variant="contained" href="/api-management">
                    Open API Management
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {activeTab === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card variant="glass">
              <CardHeader
                title="Performance Analytics"
                subheader="Detailed system performance metrics and trends"
              />
              <CardContent>
                <PerformanceChart data={performanceMetrics} height={400} />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {activeTab === 3 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card variant="glass">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Security Dashboard
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Security monitoring, threat detection, and access control metrics.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
    </StablePageWrapper>
  );
};

export default Dashboard;

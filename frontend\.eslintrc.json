{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react/jsx-runtime", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended"], "ignorePatterns": ["dist", ".eslintrc.json", "node_modules"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"react": {"version": "18.3"}}, "plugins": ["react-refresh", "@typescript-eslint"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "react/prop-types": "off", "no-unused-vars": "warn", "no-undef": "warn", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-require-imports": "warn", "react/no-unescaped-entities": "warn", "react/jsx-no-undef": "warn", "react/display-name": "warn", "no-case-declarations": "warn", "no-useless-escape": "warn", "no-async-promise-executor": "warn", "react-hooks/exhaustive-deps": "warn"}, "overrides": [{"files": ["**/*.test.js", "**/*.test.jsx", "**/*.test.ts", "**/*.test.tsx", "**/tests/**/*", "**/test/**/*"], "env": {"jest": true}, "globals": {"describe": "readonly", "test": "readonly", "it": "readonly", "expect": "readonly", "beforeEach": "readonly", "afterEach": "readonly", "beforeAll": "readonly", "afterAll": "readonly", "jest": "readonly", "vi": "readonly", "vitest": "readonly"}}, {"files": ["vite.config.js", "vite.config.*.js", "vitest.config.js"], "env": {"node": true}, "globals": {"__dirname": "readonly", "process": "readonly"}}, {"files": ["**/*.ts", "**/*.tsx"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}}]}
/**
 * Enhanced Service Definition Step - Enterprise-grade service definition step component
 * Features: Comprehensive service definition step with advanced service management capabilities, multi-dimensional service configuration,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced service definition step capabilities and seamless services system workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useMemo,
  useCallback,
  memo,
  forwardRef,
  Fragment
} from 'react';
import {
  Box,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Divider,
  Card,
  FormHelperText,
  useTheme,
  alpha,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  CircularProgress,
  Collapse
} from '@mui/material';
import {
  Add as AddIcon,
  Save as SaveIcon,
  Business as BusinessIcon,
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  Category as CategoryIcon,
  Help as HelpIcon,
  Lightbulb as LightbulbIcon,
  CheckCircle as CheckCircleIcon,
  AttachMoney as AttachMoneyIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Campaign as CampaignIcon,
  Share as ShareIcon,
  Article as ArticleIcon,
  Search as SearchIcon,
  Email as EmailIcon,
  Palette as PaletteIcon,
  Web as WebIcon,
  Code as CodeIcon,
  Smartphone as SmartphoneIcon,
  Computer as ComputerIcon,
  Psychology as PsychologyIcon,
  Analytics as AnalyticsIcon,
  Security as SecurityIcon,
  Cloud as CloudIcon,
  BusinessCenter as BusinessCenterIcon,
  AccountBalance as AccountBalanceIcon,
  People as PeopleIcon,
  Gavel as GavelIcon,
  Assignment as AssignmentIcon,
  Edit as EditIcon,
  Description as DescriptionIcon,
  Translate as TranslateIcon,
  RateReview as RateReviewIcon,
  ShoppingCart as ShoppingCartIcon,
  Contacts as ContactsIcon,
  School as SchoolIcon,
  Groups as GroupsIcon,
  SupportAgent as SupportAgentIcon,
  HeadsetMic as HeadsetMicIcon,
  Input as InputIcon,
  PhotoCamera as PhotoCameraIcon,
  Videocam as VideocamIcon
} from '@mui/icons-material';

import StepWrapper from './StepWrapper';
import { useWorkflow } from '../WorkflowProvider';
import { useSubscription } from '../../../contexts/SubscriptionContext';
import { useUserPreferences } from '../../../contexts/UserPreferencesContext';
import FeatureGate from '../../common/FeatureGate';
import DynamicTargetSegments from '../DynamicTargetSegments';
import AddCustomCategoryDialog from '../AddCustomCategoryDialog';
import { useServiceForm } from '../../../hooks/useServiceForm';

// Temporary mock implementations for performance monitoring
const usePerformanceMonitor = () => ({
  trackInteraction: () => () => {},
  measureAsync: (name, fn) => fn()
});


// Pricing models and service levels
const PRICING_MODELS = [
  { value: 'hourly', label: 'Hourly Rate' },
  { value: 'fixed', label: 'Fixed Price' },
  { value: 'subscription', label: 'Monthly Subscription' },
  { value: 'retainer', label: 'Monthly Retainer' },
  { value: 'performance', label: 'Performance-Based' },
  { value: 'custom', label: 'Custom Pricing' }
];

const SERVICE_LEVELS = [
  { value: 'basic', label: 'Basic' },
  { value: 'standard', label: 'Standard' },
  { value: 'premium', label: 'Premium' },
  { value: 'enterprise', label: 'Enterprise' }
];

// Icon mapping for service categories
const getCategoryIcon = (iconName) => {
  const iconMap = {
    campaign: CampaignIcon,
    share: ShareIcon,
    article: ArticleIcon,
    search: SearchIcon,
    email: EmailIcon,
    ads_click: CampaignIcon, // Using campaign icon for ads
    palette: PaletteIcon,
    web: WebIcon,
    branding_watermark: PaletteIcon, // Using palette for branding
    videocam: VideocamIcon,
    photo_camera: PhotoCameraIcon,
    code: CodeIcon,
    smartphone: SmartphoneIcon,
    computer: ComputerIcon,
    psychology: PsychologyIcon,
    analytics: AnalyticsIcon,
    security: SecurityIcon,
    cloud: CloudIcon,
    business_center: BusinessCenterIcon,
    account_balance: AccountBalanceIcon,
    people: PeopleIcon,
    gavel: GavelIcon,
    assignment: AssignmentIcon,
    edit: EditIcon,
    description: DescriptionIcon,
    translate: TranslateIcon,
    rate_review: RateReviewIcon,
    shopping_cart: ShoppingCartIcon,
    trending_up: TrendingUpIcon,
    contacts: ContactsIcon,
    school: SchoolIcon,
    play_lesson: SchoolIcon, // Using school icon for lessons
    groups: GroupsIcon,
    support_agent: SupportAgentIcon,
    headset_mic: HeadsetMicIcon,
    input: InputIcon
  };

  const IconComponent = iconMap[iconName] || CategoryIcon;
  return IconComponent;
};

// Group categories by their group property
const groupCategories = (categories) => {
  const grouped = {};
  categories.forEach(category => {
    const group = category.group || 'Other';
    if (!grouped[group]) {
      grouped[group] = [];
    }
    grouped[group].push(category);
  });
  return grouped;
};

/**
 * Enhanced Service Definition Step Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Function} [props.onStepAction] - Step action callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {boolean} [props.enableRealTimeUpdates=true] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-service-definition-step'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const ServiceDefinitionStep = memo(forwardRef(() => {
  const theme = useTheme();
  const { actions, workflowData } = useWorkflow();
  const { subscription } = useSubscription();

  // Performance monitoring
  usePerformanceMonitor('ServiceDefinitionStep');
  const {
    allCategories,
    allSegments,
    refreshCategories,
    loading: preferencesLoading
  } = useUserPreferences();

  const [saving, setSaving] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [showAddCategoryDialog, setShowAddCategoryDialog] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    basic: true,
    business: false,
    advanced: false
  });

  // Use the service form hook
  const {
    formik,
    formError,
    setFormError,
    autoSaveEnabled,
    lastSaved,
    loading: formLoading,
    applyPreferences,
    handleTargetSegmentsChange
  } = useServiceForm(workflowData.serviceDefinition, {
    autoSave: true,
    onSubmit: async (values) => {
      setSaving(true);
      try {
        // Save to workflow state
        actions.updateStepData('serviceDefinition', values);
        
        // Mark step as completed
        actions.completeStep(0);
        
        console.log('Service definition saved:', values);
      } catch (error) {
        console.error('Error saving service definition:', error);
        actions.setError('Failed to save service definition. Please try again.');
        throw error;
      } finally {
        setSaving(false);
      }
    },
    onAutoSave: async (values) => {
      // Optional: sync with workflow state on auto-save
      actions.updateStepData('serviceDefinition', values);
    }
  });

  // Optimized event handlers with useCallback

  // Handle section expansion
  const handleSectionToggle = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Handle custom category selection
  const handleCategoryChange = (event) => {
    const value = event.target.value;
    if (value === 'ADD_CUSTOM_CATEGORY') {
      setShowAddCategoryDialog(true);
    } else {
      formik.handleChange(event);
    }
  };

  // Handle new category added
  const handleCategoryAdded = useCallback(async (newCategory) => {
    try {
      // Refresh categories to get the updated list
      await refreshCategories();

      // Set the new category as selected
      formik.setFieldValue('category', newCategory.name);

      // Clear any existing errors
      setFormError('');

      console.log('New category added and selected:', newCategory.name);
    } catch (error) {
      console.error('Error handling new category:', error);
      setFormError('Failed to add new category. Please try again.');
    }
  }, [refreshCategories, formik, setFormError]);

  // Memoized form completion calculation for performance
  const completionPercentage = useMemo(() => {
    const requiredFields = ['name', 'category', 'description', 'value_proposition', 'target_industry', 'pricing_model', 'service_level', 'delivery_timeline'];
    const completedFields = requiredFields.filter(field => formik.values[field] && formik.values[field].trim() !== '');
    return Math.round((completedFields.length / requiredFields.length) * 100);
  }, [formik.values]);

  // Memoized loading state
  const loading = useMemo(() =>
    preferencesLoading || formLoading || saving,
    [preferencesLoading, formLoading, saving]
  );

  return (
    <StepWrapper
      title="Define Your Service"
      description="Provide comprehensive details about your service to generate accurate ICPs and targeted content."
      loading={loading}
    >
      {/* Form Error */}
      {formError && (
        <Alert severity="error" sx={{ mb: theme.spacing(2) }} onClose={() => setFormError('')}>
          {formError}
        </Alert>
      )}

      {/* Progress and Help Section */}
      <Box sx={{ mb: theme.spacing(3) }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="h6" color="primary">
              Form Progress: {completionPercentage}%
            </Typography>
            <Chip
              label={completionPercentage === 100 ? 'Complete' : 'In Progress'}
              color={completionPercentage === 100 ? 'success' : 'primary'}
              size="small"
            />
          </Box>
          <Tooltip title="Show helpful tips">
            <IconButton onClick={() => setShowHelp(!showHelp)} size="small">
              <HelpIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Auto-save indicator */}
        {autoSaveEnabled && lastSaved && (
          <Alert severity="info" sx={{ mb: theme.spacing(2) }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CheckCircleIcon fontSize="small" />
              Last saved: {lastSaved.toLocaleTimeString()}
            </Box>
          </Alert>
        )}

        {/* Help Section */}
        <Collapse in={showHelp}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              <LightbulbIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Tips for Better Results
            </Typography>
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              <li>Be specific about your service to get more targeted ICPs</li>
              <li>Include unique value propositions that differentiate you</li>
              <li>Choose the most relevant industry and pricing model</li>
              <li>Use the &ldquo;Apply Preferences&rdquo; button to auto-fill common fields</li>
            </ul>
          </Alert>
        </Collapse>
      </Box>

      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={3}>
          {/* Basic Information Section */}
          <Grid item xs={12}>
            <Card
              elevation={0}
              sx={{
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 2,
                overflow: 'visible'
              }}
            >
              <Accordion
                expanded={expandedSections.basic}
                onChange={() => handleSectionToggle('basic')}
                elevation={0}
                sx={{
                  '&:before': { display: 'none' },
                  border: 'none'
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    backgroundColor: theme.palette.action.hover,
                    borderRadius: '8px 8px 0 0',
                    '& .MuiAccordionSummary-content': {
                      alignItems: 'center'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                    <BusinessIcon color="primary" />
                    <Typography variant="h6">Basic Information</Typography>
                    <Chip
                      label={`${Math.round(((['name', 'category', 'description', 'value_proposition'].filter(field => formik.values[field] && formik.values[field].trim() !== '').length) / 4) * 100)}%`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails sx={{ p: 3 }}>
                  <Grid container spacing={3}>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        id="name"
                        name="name"
                        label="Service Name"
                        placeholder="e.g., B2B Social Media Management"
                        value={formik.values.name}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.name && Boolean(formik.errors.name)}
                        helperText={
                          (formik.touched.name && formik.errors.name) ||
                          "Choose a clear, descriptive name for your service"
                        }
                        required
                        disabled={loading}
                        InputProps={{
                          endAdornment: formik.values.name && (
                            <Tooltip title="Service name looks good!">
                              <CheckCircleIcon color="success" fontSize="small" />
                            </Tooltip>
                          )
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl
                        fullWidth
                        error={formik.touched.category && Boolean(formik.errors.category)}
                        disabled={loading}
                      >
                        <InputLabel id="category-label">Service Category *</InputLabel>
                        <Select
                          labelId="category-label"
                          id="category"
                          name="category"
                          value={formik.values.category}
                          label="Service Category *"
                          onChange={handleCategoryChange}
                          onBlur={formik.handleBlur}
                        >
                          {/* Predefined Categories - Grouped */}
                          {allCategories.predefined_categories.length > 0 && (() => {
                            const groupedCategories = groupCategories(allCategories.predefined_categories);
                            return Object.entries(groupedCategories).map(([groupName, categories]) => (
                              <Fragment key={groupName}>
                                <MenuItem disabled>
                                  <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 'bold' }}>
                                    <CategoryIcon sx={{ mr: 1, fontSize: 16 }} />
                                    {groupName}
                                  </Typography>
                                </MenuItem>
                                {categories.map((category) => {
                                  const IconComponent = getCategoryIcon(category.icon);
                                  return (
                                    <MenuItem
                                      key={category.name}
                                      value={category.name}
                                      sx={{ pl: 3 }}
                                    >
                                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                                        <IconComponent sx={{ mr: 1.5, fontSize: 18, color: 'action.active' }} />
                                        <Box sx={{ flex: 1 }}>
                                          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                            {category.name}
                                          </Typography>
                                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                            {category.description}
                                          </Typography>
                                        </Box>
                                      </Box>
                                    </MenuItem>
                                  );
                                })}
                              </Fragment>
                            ));
                          })()}

                          {/* Custom Categories */}
                          {allCategories.custom_categories.length > 0 && (
                            <>
                              <Divider sx={{ my: 1 }} />
                              <MenuItem disabled>
                                <Typography variant="subtitle2" color="secondary" sx={{ fontWeight: 'bold' }}>
                                  <SettingsIcon sx={{ mr: 1, fontSize: 16 }} />
                                  Your Custom Categories
                                </Typography>
                              </MenuItem>
                              {allCategories.custom_categories.map((category) => (
                                <MenuItem
                                  key={category.id}
                                  value={category.name}
                                  sx={{ pl: 3 }}
                                >
                                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                                    <CategoryIcon sx={{ mr: 1.5, fontSize: 18, color: 'secondary.main' }} />
                                    <Box sx={{ flex: 1 }}>
                                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                          {category.name}
                                        </Typography>
                                        <Chip
                                          label="Custom"
                                          size="small"
                                          variant="outlined"
                                          color="secondary"
                                          sx={{ height: 20, fontSize: '0.7rem' }}
                                        />
                                      </Box>
                                      {category.description && (
                                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                                          {category.description}
                                        </Typography>
                                      )}
                                    </Box>
                                  </Box>
                                </MenuItem>
                              ))}
                            </>
                          )}

                          {/* Add Custom Category Option */}
                          <Divider sx={{ my: 1 }} />
                          <MenuItem
                            value="ADD_CUSTOM_CATEGORY"
                            sx={{
                              color: 'primary.main',
                              fontWeight: 'medium',
                              backgroundColor: alpha(theme.palette.primary.main, 0.05),
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.primary.main, 0.1)
                              }
                            }}
                          >
                            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                              <AddIcon sx={{ mr: 1.5, fontSize: 18 }} />
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                  Add Custom Category
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  Create a new category for your specific needs
                                </Typography>
                              </Box>
                            </Box>
                          </MenuItem>
                        </Select>
                        {formik.touched.category && formik.errors.category ? (
                          <FormHelperText>{formik.errors.category}</FormHelperText>
                        ) : (
                          <FormHelperText>
                            Choose from {allCategories.predefined_categories.length}+ professional categories or create your own
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        id="description"
                        name="description"
                        label="Service Description"
                        placeholder="Provide a detailed description of your service, including what you do, how you do it, and what makes your approach unique..."
                        value={formik.values.description}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.description && Boolean(formik.errors.description)}
                        helperText={
                          (formik.touched.description && formik.errors.description) ||
                          `${formik.values.description.length}/1000 characters - Be specific about your process and methodology`
                        }
                        required
                        disabled={loading}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        id="value_proposition"
                        name="value_proposition"
                        label="Value Proposition"
                        placeholder="What unique value does your service provide to clients? What problems do you solve?"
                        value={formik.values.value_proposition}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.value_proposition && Boolean(formik.errors.value_proposition)}
                        helperText={
                          (formik.touched.value_proposition && formik.errors.value_proposition) ||
                          `${formik.values.value_proposition.length}/500 characters - Focus on client benefits and outcomes`
                        }
                        required
                        disabled={loading}
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Card>
          </Grid>

          {/* Business Details Section */}
          <Grid item xs={12}>
            <Card
              elevation={0}
              sx={{
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 2,
                overflow: 'visible'
              }}
            >
              <Accordion
                expanded={expandedSections.business}
                onChange={() => handleSectionToggle('business')}
                elevation={0}
                sx={{
                  '&:before': { display: 'none' },
                  border: 'none'
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    backgroundColor: theme.palette.action.hover,
                    borderRadius: '8px 8px 0 0',
                    '& .MuiAccordionSummary-content': {
                      alignItems: 'center'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                    <TrendingUpIcon color="primary" />
                    <Typography variant="h6">Business Details</Typography>
                    <Chip
                      label={`${Math.round(((['target_industry', 'pricing_model', 'service_level', 'delivery_timeline'].filter(field => formik.values[field] && formik.values[field].trim() !== '').length) / 4) * 100)}%`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails sx={{ p: 3 }}>
                  <Grid container spacing={3}>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        id="target_industry"
                        name="target_industry"
                        label="Target Industry"
                        placeholder="e.g., Technology, Healthcare, Finance"
                        value={formik.values.target_industry}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.target_industry && Boolean(formik.errors.target_industry)}
                        helperText={
                          (formik.touched.target_industry && formik.errors.target_industry) ||
                          "Specify the primary industry you serve"
                        }
                        required
                        disabled={loading}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl
                        fullWidth
                        error={formik.touched.pricing_model && Boolean(formik.errors.pricing_model)}
                        disabled={loading}
                      >
                        <InputLabel id="pricing-model-label">Pricing Model *</InputLabel>
                        <Select
                          labelId="pricing-model-label"
                          id="pricing_model"
                          name="pricing_model"
                          value={formik.values.pricing_model}
                          label="Pricing Model *"
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          startAdornment={<AttachMoneyIcon sx={{ mr: 1, color: 'action.active' }} />}
                        >
                          {PRICING_MODELS.map((model) => (
                            <MenuItem key={model.value} value={model.value}>
                              {model.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {formik.touched.pricing_model && formik.errors.pricing_model ? (
                          <FormHelperText>{formik.errors.pricing_model}</FormHelperText>
                        ) : (
                          <FormHelperText>Choose how you charge for your services</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControl
                        fullWidth
                        error={formik.touched.service_level && Boolean(formik.errors.service_level)}
                        disabled={loading}
                      >
                        <InputLabel id="service-level-label">Service Level *</InputLabel>
                        <Select
                          labelId="service-level-label"
                          id="service_level"
                          name="service_level"
                          value={formik.values.service_level}
                          label="Service Level *"
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        >
                          {SERVICE_LEVELS.map((level) => (
                            <MenuItem key={level.value} value={level.value}>
                              {level.label}
                            </MenuItem>
                          ))}
                        </Select>
                        {formik.touched.service_level && formik.errors.service_level ? (
                          <FormHelperText>{formik.errors.service_level}</FormHelperText>
                        ) : (
                          <FormHelperText>Select the tier of service you provide</FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        id="delivery_timeline"
                        name="delivery_timeline"
                        label="Delivery Timeline"
                        placeholder="e.g., 2-4 weeks, Ongoing monthly, 3 months"
                        value={formik.values.delivery_timeline}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.delivery_timeline && Boolean(formik.errors.delivery_timeline)}
                        helperText={
                          (formik.touched.delivery_timeline && formik.errors.delivery_timeline) ||
                          "How long does it typically take to deliver results?"
                        }
                        required
                        disabled={loading}
                        InputProps={{
                          startAdornment: <ScheduleIcon sx={{ mr: 1, color: 'action.active' }} />
                        }}
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Card>
          </Grid>

          {/* Advanced Settings Section */}
          <Grid item xs={12}>
            <Card
              elevation={0}
              sx={{
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 2,
                overflow: 'visible'
              }}
            >
              <Accordion
                expanded={expandedSections.advanced}
                onChange={() => handleSectionToggle('advanced')}
                elevation={0}
                sx={{
                  '&:before': { display: 'none' },
                  border: 'none'
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  sx={{
                    backgroundColor: theme.palette.action.hover,
                    borderRadius: '8px 8px 0 0',
                    '& .MuiAccordionSummary-content': {
                      alignItems: 'center'
                    }
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
                    <SettingsIcon color="primary" />
                    <Typography variant="h6">Advanced Settings</Typography>
                    <Chip
                      label="Optional"
                      size="small"
                      color="secondary"
                      variant="outlined"
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails sx={{ p: 3 }}>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <FeatureGate
                        feature="advanced_service_definition"
                        fallback={
                          <Box sx={{ p: 3, border: `1px dashed ${theme.palette.divider}`, borderRadius: 2, textAlign: 'center' }}>
                            <LightbulbIcon sx={{ fontSize: 48, color: 'action.disabled', mb: 2 }} />
                            <Typography variant="h6" color="textSecondary" gutterBottom>
                              🔒 Advanced Target Market Segmentation
                            </Typography>
                            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                              Upgrade to Accelerator or Dominator plan to define multiple target market segments for more precise ICP generation.
                            </Typography>
                            <Chip
                              label={`Current plan: ${subscription?.plan_name || 'Creator'}`}
                              size="small"
                              variant="outlined"
                              sx={{ mr: 1 }}
                            />
                            <Chip
                              label="Available in: Accelerator, Dominator"
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          </Box>
                        }
                      >
                        <DynamicTargetSegments
                          value={formik.values.target_segments}
                          onChange={handleTargetSegmentsChange}
                          onBlur={formik.handleBlur}
                          error={formik.touched.target_segments && Boolean(formik.errors.target_segments)}
                          helperText={formik.touched.target_segments && formik.errors.target_segments}
                          required={true}
                          disabled={loading}
                          allSegmentsData={allSegments}
                        />
                      </FeatureGate>
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Card>
          </Grid>

          {/* Quick Actions */}
          <Grid item xs={12}>
            <Card
              elevation={0}
              sx={{
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 2,
                p: 3,
                backgroundColor: theme.palette.action.hover
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<SettingsIcon />}
                  onClick={applyPreferences}
                  disabled={loading}
                  sx={{
                    textTransform: 'none',
                    borderRadius: 2
                  }}
                >
                  Apply My Default Preferences
                </Button>
              </Box>
              <Typography variant="body2" color="textSecondary" textAlign="center">
                Automatically fill common fields based on your saved preferences
              </Typography>
            </Card>
          </Grid>

          {/* Form Actions */}
          <Grid item xs={12}>
            <Card
              elevation={0}
              sx={{
                border: `1px solid ${theme.palette.primary.main}`,
                borderRadius: 2,
                p: 3,
                backgroundColor: alpha(theme.palette.primary.main, 0.05)
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                <Box>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    {autoSaveEnabled ? 'Form auto-saves every 30 seconds' : 'Auto-save disabled'}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Progress: {completionPercentage}% complete
                  </Typography>
                </Box>
                <Button
                  type="submit"
                  variant="contained"
                  size="large"
                  startIcon={saving ? <CircularProgress size={16} color="inherit" /> : <SaveIcon />}
                  disabled={saving || !formik.isValid || loading}
                  sx={{
                    minWidth: 180,
                    borderRadius: 2,
                    textTransform: 'none',
                    fontSize: '1rem'
                  }}
                >
                  {saving ? 'Saving...' : 'Save & Continue'}
                </Button>
              </Box>
            </Card>
          </Grid>
        </Grid>
      </form>

      {/* Add Custom Category Dialog */}
      <AddCustomCategoryDialog
        open={showAddCategoryDialog}
        onClose={() => setShowAddCategoryDialog(false)}
        onCategoryAdded={handleCategoryAdded}
        existingCategories={[
          ...allCategories.predefined_categories,
          ...allCategories.custom_categories
        ]}
      />
    </StepWrapper>
  );
}));

// Display name for debugging
ServiceDefinitionStep.displayName = 'ServiceDefinitionStep';

export default ServiceDefinitionStep;

// @since 2024-1-1 to 2025-25-7
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  Box,
  Typography,

  CircularProgress,
  Al<PERSON>,
  But<PERSON>,
} from "@mui/material";
import { useAuth } from "../../hooks/useAuth";
import { useAdvancedToast } from "../../hooks/useAdvancedToast";

/**
 * VerifyMagicLink Component
 * Handles verification of magic link tokens from email
 */
const VerifyMagicLink = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { verifyMagicLink } = useAuth();
  const { showSuccess } = useAdvancedToast();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const verifyToken = async () => {
      const token = searchParams.get("token");

      if (!token) {
        setError("Invalid or missing token");
        setLoading(false);
        return;
      }

      try {
        const result = await verifyMagicLink(token);

        if (result.success) {
          showSuccess("Login successful!");
          navigate("/dashboard");
        } else {
          setError(result.error || "Failed to verify magic link");
          setLoading(false);
        }
      } catch (err) {
        console.error("Magic link verification error:", err);
        setError("An unexpected error occurred");
        setLoading(false);
      }
    };

    verifyToken();
  }, [searchParams, verifyMagicLink, navigate, showSuccess]);

  return (
    <Box
      sx={{
        width: "100%",
        textAlign: "center",
      }}
    >
      <Typography variant="h4" component="h1" gutterBottom>
        Magic Link Verification
      </Typography>

      {loading ? (
        <Box
          sx={{
            my: 4,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <CircularProgress size={60} sx={{ mb: 2 }} />
          <Typography variant="body1">Verifying your magic link...</Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            You&apos;ll be redirected automatically when verified.
          </Typography>
        </Box>
      ) : error ? (
        <Box sx={{ my: 3 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Your magic link is invalid or has expired.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate("/login")}
          >
            Back to Login
          </Button>
        </Box>
      ) : null}
    </Box>
  );
};

export default VerifyMagicLink;

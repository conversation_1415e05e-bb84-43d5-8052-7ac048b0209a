// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Snackbar,
  Fade,
  Skeleton
} from '@mui/material';
import {
  Add as AddIcon,
  Email as EmailIcon,
  Campaign as CampaignIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Preview as PreviewIcon,
  Send as SendIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as DuplicateIcon,
  CloudOff as OfflineIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

import StablePageWrapper from '../components/StablePageWrapper';
import ErrorBoundary from '../components/ErrorBoundary';
import TemplateList from '../components/email/TemplateList';
import TemplateEditor from '../components/email/TemplateEditor';
import CampaignManager from '../components/email/CampaignManager';
import TemplateAnalytics from '../components/email/TemplateAnalytics';
import { useEmailTemplateData } from '../hooks/useEmailTemplateData';

const EmailTemplateManagement = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editorMode, setEditorMode] = useState('create'); // 'create', 'edit', 'duplicate'
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [isRetrying, setIsRetrying] = useState(false);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  const {
    templates,
    campaigns,
    analytics,
    loading,
    error,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    refreshData
  } = useEmailTemplateData();

  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Handle connection status
  useEffect(() => {
    if (error && error.includes('Authentication')) {
      setShowOfflineMessage(false);
    } else if (!loading && templates.length === 0 && campaigns.length === 0) {
      // Show offline message after a delay if no data is loaded
      const timer = setTimeout(() => {
        setShowOfflineMessage(true);
      }, 3000);
      return () => clearTimeout(timer);
    } else {
      setShowOfflineMessage(false);
    }
  }, [error, loading, templates.length, campaigns.length]);

  const handleRetry = async () => {
    setIsRetrying(true);
    setShowOfflineMessage(false);
    try {
      await refreshData();
      setSnackbar({
        open: true,
        message: 'Connection restored successfully',
        severity: 'success'
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: 'Still unable to connect. Please try again later.',
        severity: 'warning'
      });
    } finally {
      setIsRetrying(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleCreateTemplate = () => {
    setSelectedTemplate(null);
    setEditorMode('create');
    setIsEditorOpen(true);
  };

  const handleEditTemplate = (template) => {
    setSelectedTemplate(template);
    setEditorMode('edit');
    setIsEditorOpen(true);
  };

  const handleDuplicateTemplate = async (template) => {
    try {
      await duplicateTemplate(template.id);
      setSnackbar({
        open: true,
        message: 'Template duplicated successfully',
        severity: 'success'
      });
      refreshData();
    } catch (error) {
      setSnackbar({
        open: true,
        message: 'Failed to duplicate template',
        severity: 'error'
      });
    }
  };

  const handleDeleteTemplate = async (templateId) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      try {
        await deleteTemplate(templateId);
        setSnackbar({
          open: true,
          message: 'Template deleted successfully',
          severity: 'success'
        });
        refreshData();
      } catch (error) {
        setSnackbar({
          open: true,
          message: 'Failed to delete template',
          severity: 'error'
        });
      }
    }
  };

  const handleSaveTemplate = async (templateData) => {
    try {
      if (editorMode === 'create') {
        await createTemplate(templateData);
        setSnackbar({
          open: true,
          message: 'Template created successfully',
          severity: 'success'
        });
      } else if (editorMode === 'edit') {
        await updateTemplate(selectedTemplate.id, templateData);
        setSnackbar({
          open: true,
          message: 'Template updated successfully',
          severity: 'success'
        });
      }
      setIsEditorOpen(false);
      refreshData();
    } catch (error) {
      setSnackbar({
        open: true,
        message: `Failed to ${editorMode} template`,
        severity: 'error'
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const renderTabContent = () => {
    switch (currentTab) {
      case 0:
        return (
          <ErrorBoundary
            title="Template List Error"
            showDetails={process.env.NODE_ENV === 'development'}
          >
            <TemplateList
              templates={templates}
              onEdit={handleEditTemplate}
              onDuplicate={handleDuplicateTemplate}
              onDelete={handleDeleteTemplate}
              loading={loading}
            />
          </ErrorBoundary>
        );
      case 1:
        return (
          <ErrorBoundary
            title="Campaign Manager Error"
            showDetails={process.env.NODE_ENV === 'development'}
          >
            <CampaignManager
              campaigns={campaigns}
              templates={templates}
              loading={loading}
            />
          </ErrorBoundary>
        );
      case 2:
        return (
          <ErrorBoundary
            title="Analytics Error"
            showDetails={process.env.NODE_ENV === 'development'}
          >
            <TemplateAnalytics
              analytics={analytics}
              loading={loading}
            />
          </ErrorBoundary>
        );
      default:
        return null;
    }
  };

  const getTabIcon = (index) => {
    const icons = [EmailIcon, CampaignIcon, AnalyticsIcon];
    const IconComponent = icons[index];
    return <IconComponent />;
  };

  const getTabLabel = (index) => {
    const labels = ['Templates', 'Campaigns', 'Analytics'];
    return labels[index];
  };

  return (
    <ErrorBoundary
      title="Email Template Management Error"
      showDetails={process.env.NODE_ENV === 'development'}
    >
      <StablePageWrapper
        title="Email Template Management"
        loading={false} // Handle loading states individually per component
      >
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3
        }}>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
              <Typography variant="h4" component="h1">
                Email Template Management
              </Typography>
              {showOfflineMessage && (
                <Fade in={showOfflineMessage}>
                  <Chip
                    icon={<OfflineIcon />}
                    label="Working Offline"
                    color="warning"
                    variant="outlined"
                    size="small"
                    sx={{
                      '& .MuiChip-icon': {
                        fontSize: '1rem'
                      }
                    }}
                  />
                </Fade>
              )}
            </Box>
            <Typography variant="body1" color="text.secondary">
              Create, manage, and analyze email templates and campaigns
            </Typography>
            {showOfflineMessage && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                Some features may be limited while offline. Data will sync when connection is restored.
              </Typography>
            )}
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {showOfflineMessage && (
              <Button
                variant="outlined"
                startIcon={isRetrying ? <Skeleton variant="circular" width={20} height={20} /> : <RefreshIcon />}
                onClick={handleRetry}
                disabled={isRetrying}
                sx={{
                  borderColor: 'warning.main',
                  color: 'warning.main',
                  '&:hover': {
                    borderColor: 'warning.dark',
                    backgroundColor: 'warning.light',
                    color: 'warning.dark',
                  }
                }}
              >
                {isRetrying ? 'Retrying...' : 'Retry Connection'}
              </Button>
            )}
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateTemplate}
              sx={{
                background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
                boxShadow: '0 8px 32px 0 rgba(108, 75, 250, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 12px 40px 0 rgba(108, 75, 250, 0.4)',
                }
              }}
            >
              Create Template
            </Button>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card variant="glass" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <EmailIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Templates</Typography>
                </Box>
                <Typography variant="h4" color="primary">
                  {templates?.length || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total templates
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card variant="glass" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CampaignIcon color="secondary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Campaigns</Typography>
                </Box>
                <Typography variant="h4" color="secondary">
                  {campaigns?.length || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active campaigns
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card variant="glass" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <SendIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6">Sent</Typography>
                </Box>
                <Typography variant="h4" color="success.main">
                  {analytics?.totalSent || 0}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Emails sent
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card variant="glass" sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <AnalyticsIcon color="info" sx={{ mr: 1 }} />
                  <Typography variant="h6">Open Rate</Typography>
                </Box>
                <Typography variant="h4" color="info.main">
                  {analytics?.openRate || '0%'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Average open rate
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Main Content */}
        <Card variant="glass">
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={currentTab} 
              onChange={handleTabChange}
              sx={{
                '& .MuiTab-root': {
                  minHeight: 64,
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '1rem',
                }
              }}
            >
              {[0, 1, 2].map((index) => (
                <Tab
                  key={index}
                  icon={getTabIcon(index)}
                  label={getTabLabel(index)}
                  iconPosition="start"
                  sx={{
                    '&.Mui-selected': {
                      color: 'primary.main',
                    }
                  }}
                />
              ))}
            </Tabs>
          </Box>
          <CardContent sx={{ p: 3 }}>
            {renderTabContent()}
          </CardContent>
        </Card>

        {/* Template Editor Dialog */}
        <TemplateEditor
          open={isEditorOpen}
          onClose={() => setIsEditorOpen(false)}
          onSave={handleSaveTemplate}
          template={selectedTemplate}
          mode={editorMode}
        />

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </StablePageWrapper>
    </ErrorBoundary>
  );
};

export default EmailTemplateManagement;

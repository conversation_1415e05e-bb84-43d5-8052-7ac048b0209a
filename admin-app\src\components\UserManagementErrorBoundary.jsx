import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rdion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Stack,
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  BugReport as BugIcon,
  Support as SupportIcon,
} from '@mui/icons-material';

class UserManagementErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('UserManagement Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Log to monitoring service (in production)
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error, errorInfo) => {
    // In production, send to error monitoring service
    const errorData = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: localStorage.getItem('adminUserId'),
    };

    // Example: Send to monitoring service
    // errorMonitoringService.logError(errorData);
    console.log('Error logged:', errorData);
  };

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
    }));
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, errorId, retryCount } = this.state;
      
      return (
        <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
          <Paper sx={{ p: 3 }}>
            <Stack spacing={3}>
              {/* Error Header */}
              <Box display="flex" alignItems="center" gap={2}>
                <ErrorIcon color="error" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h5" color="error" gutterBottom>
                    User Management Error
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Something went wrong while loading the user management interface.
                  </Typography>
                </Box>
              </Box>

              {/* Error Summary */}
              <Alert severity="error">
                <AlertTitle>Error Details</AlertTitle>
                <Typography variant="body2" gutterBottom>
                  <strong>Error ID:</strong> {errorId}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  <strong>Message:</strong> {error?.message || 'Unknown error occurred'}
                </Typography>
                <Typography variant="body2">
                  <strong>Time:</strong> {new Date().toLocaleString()}
                </Typography>
              </Alert>

              {/* Retry Information */}
              {retryCount > 0 && (
                <Alert severity="warning">
                  <AlertTitle>Retry Attempts</AlertTitle>
                  This error has occurred {retryCount} time(s). If the problem persists, 
                  please contact support.
                </Alert>
              )}

              {/* Action Buttons */}
              <Stack direction="row" spacing={2} justifyContent="center">
                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleRetry}
                  disabled={retryCount >= 3}
                >
                  {retryCount >= 3 ? 'Max Retries Reached' : 'Try Again'}
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleReload}
                >
                  Reload Page
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<SupportIcon />}
                  onClick={() => window.open('/support', '_blank')}
                >
                  Contact Support
                </Button>
              </Stack>

              {/* Troubleshooting Tips */}
              <Alert severity="info">
                <AlertTitle>Troubleshooting Tips</AlertTitle>
                <Typography variant="body2" component="div">
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    <li>Check your internet connection</li>
                    <li>Clear your browser cache and cookies</li>
                    <li>Try refreshing the page</li>
                    <li>If the problem persists, contact system administrator</li>
                  </ul>
                </Typography>
              </Alert>

              {/* Technical Details (Expandable) */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <BugIcon fontSize="small" />
                    <Typography variant="subtitle2">
                      Technical Details (for developers)
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>
                        Error Stack:
                      </Typography>
                      <Paper sx={{ p: 2, bgcolor: 'grey.100' }}>
                        <Typography
                          variant="body2"
                          component="pre"
                          sx={{
                            fontFamily: 'monospace',
                            fontSize: '0.75rem',
                            whiteSpace: 'pre-wrap',
                            wordBreak: 'break-word',
                          }}
                        >
                          {error?.stack || 'No stack trace available'}
                        </Typography>
                      </Paper>
                    </Box>

                    {errorInfo && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Component Stack:
                        </Typography>
                        <Paper sx={{ p: 2, bgcolor: 'grey.100' }}>
                          <Typography
                            variant="body2"
                            component="pre"
                            sx={{
                              fontFamily: 'monospace',
                              fontSize: '0.75rem',
                              whiteSpace: 'pre-wrap',
                              wordBreak: 'break-word',
                            }}
                          >
                            {errorInfo.componentStack}
                          </Typography>
                        </Paper>
                      </Box>
                    )}

                    <Box>
                      <Typography variant="subtitle2" gutterBottom>
                        Environment Info:
                      </Typography>
                      <Stack direction="row" spacing={1} flexWrap="wrap">
                        <Chip
                          label={`Browser: ${navigator.userAgent.split(' ')[0]}`}
                          size="small"
                          variant="outlined"
                        />
                        <Chip
                          label={`URL: ${window.location.pathname}`}
                          size="small"
                          variant="outlined"
                        />
                        <Chip
                          label={`Timestamp: ${new Date().toISOString()}`}
                          size="small"
                          variant="outlined"
                        />
                      </Stack>
                    </Box>
                  </Stack>
                </AccordionDetails>
              </Accordion>

              {/* Performance Impact Notice */}
              <Alert severity="warning">
                <AlertTitle>Performance Notice</AlertTitle>
                This error may impact system performance. If you continue to experience 
                issues, please contact your system administrator for assistance.
              </Alert>
            </Stack>
          </Paper>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default UserManagementErrorBoundary;

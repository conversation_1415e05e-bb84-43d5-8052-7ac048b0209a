/**
 * API service for metadata removal admin operations
 * 
 * This service provides methods for admin users to:
 * - Monitor metadata removal performance
 * - View global statistics
 * - Get performance alerts
 * - Test metadata removal functionality
 * - Manage configuration
 */

import api from './index';

const metadataRemovalApi = {
  /**
   * Get global metadata removal statistics
   * @param {number} hours - Hours to look back (1-168)
   * @returns {Promise} API response with global statistics
   */
  getGlobalStats: async (hours = 24) => {
    try {
      const response = await api.get(`/admin/metadata-removal/stats/global?hours=${hours}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching global metadata removal stats:', error);
      throw error;
    }
  },

  /**
   * Get metadata removal statistics for a specific user
   * @param {string} userId - User ID to get stats for
   * @param {number} hours - Hours to look back (1-168)
   * @returns {Promise} API response with user statistics
   */
  getUserStats: async (userId, hours = 24) => {
    try {
      const response = await api.get(`/admin/metadata-removal/stats/user/${userId}?hours=${hours}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user metadata removal stats:', error);
      throw error;
    }
  },

  /**
   * Get performance alerts for metadata removal operations
   * @param {number} hours - Hours to look back (1-24)
   * @returns {Promise} API response with alerts
   */
  getAlerts: async (hours = 1) => {
    try {
      const response = await api.get(`/admin/metadata-removal/alerts?hours=${hours}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching metadata removal alerts:', error);
      throw error;
    }
  },

  /**
   * Get current performance metrics for the metadata removal service
   * @returns {Promise} API response with performance metrics
   */
  getPerformanceMetrics: async () => {
    try {
      const response = await api.get('/admin/metadata-removal/performance');
      return response.data;
    } catch (error) {
      console.error('Error fetching metadata removal performance metrics:', error);
      throw error;
    }
  },

  /**
   * Get current metadata removal configuration
   * @returns {Promise} API response with configuration
   */
  getConfiguration: async () => {
    try {
      const response = await api.get('/admin/metadata-removal/config');
      return response.data;
    } catch (error) {
      console.error('Error fetching metadata removal configuration:', error);
      throw error;
    }
  },

  /**
   * Test metadata removal functionality with a specific image URL
   * @param {string} imageUrl - Image URL to test
   * @returns {Promise} API response with test results
   */
  testMetadataRemoval: async (imageUrl) => {
    try {
      const response = await api.post('/admin/metadata-removal/test', {
        test_image_url: imageUrl
      });
      return response.data;
    } catch (error) {
      console.error('Error testing metadata removal:', error);
      throw error;
    }
  },

  /**
   * Remove metadata from a single image URL (user endpoint)
   * @param {string} imageUrl - Image URL to process
   * @param {boolean} preserveQuality - Whether to preserve image quality
   * @returns {Promise} API response with processed image data
   */
  removeImageMetadata: async (imageUrl, preserveQuality = true) => {
    try {
      const response = await api.post('/content/images/remove-metadata', null, {
        params: {
          image_url: imageUrl,
          preserve_quality: preserveQuality
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error removing image metadata:', error);
      throw error;
    }
  },

  /**
   * Remove metadata from multiple images concurrently (user endpoint)
   * @param {string[]} imageUrls - Array of image URLs to process
   * @param {boolean} preserveQuality - Whether to preserve image quality
   * @returns {Promise} API response with processed images data
   */
  batchRemoveImageMetadata: async (imageUrls, preserveQuality = true) => {
    try {
      const response = await api.post('/content/images/batch-remove-metadata', {
        image_urls: imageUrls,
        preserve_quality: preserveQuality
      });
      return response.data;
    } catch (error) {
      console.error('Error in batch metadata removal:', error);
      throw error;
    }
  }
};

export default metadataRemovalApi;

REM @since 2024-1-1 to 2025-25-7
@echo off
echo 🔧 Quick Fix for ERR_INSUFFICIENT_RESOURCES...
echo.

REM Install missing dependencies
echo Installing cross-env and rimraf...
npm install cross-env rimraf --save-dev

REM Clear caches
echo Clearing caches...
if exist "node_modules\.vite" rmdir /s /q "node_modules\.vite"
if exist "dist" rmdir /s /q "dist"
npm cache clean --force

echo.
echo ✅ Quick fix complete!
echo.
echo 🚀 Now try one of these commands:
echo.
echo   npm run dev              (Standard mode)
echo   npm run dev:memory       (High memory mode - recommended)
echo   npm run dev:windows      (Windows native mode)
echo   npm run dev:simple       (Simple mode - no optimization)
echo.
echo 💡 For interactive mode, run: start-dev.bat
echo.
pause

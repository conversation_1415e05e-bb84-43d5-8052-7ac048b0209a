# Windows-specific build script to handle file handle limitations
# This script sets environment variables to optimize for Windows builds

Write-Host "Setting up Windows build environment..." -ForegroundColor Green

# Increase thread pool size for better file handling
$env:UV_THREADPOOL_SIZE = "128"

# Increase Node.js memory limit
$env:NODE_OPTIONS = "--max-old-space-size=8192 --max-http-header-size=80000"

# Set Windows-specific optimizations
$env:NODE_ENV = "production"

Write-Host "Environment variables set:" -ForegroundColor Yellow
Write-Host "UV_THREADPOOL_SIZE: $env:UV_THREADPOOL_SIZE"
Write-Host "NODE_OPTIONS: $env:NODE_OPTIONS"
Write-Host "NODE_ENV: $env:NODE_ENV"

Write-Host "`nStarting build process..." -ForegroundColor Green

try {
    # Try the minimal config first
    Write-Host "Attempting build with minimal configuration..." -ForegroundColor Cyan
    npx vite build --config vite.config.minimal.js
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful with minimal configuration!" -ForegroundColor Green
        exit 0
    }
    
    Write-Host "Minimal build failed, trying stable configuration..." -ForegroundColor Yellow
    npx vite build --config vite.config.stable.js
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful with stable configuration!" -ForegroundColor Green
        exit 0
    }
    
    Write-Host "Stable build failed, trying default configuration..." -ForegroundColor Yellow
    npx vite build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful with default configuration!" -ForegroundColor Green
        exit 0
    }
    
    Write-Host "All build attempts failed." -ForegroundColor Red
    exit 1
    
} catch {
    Write-Host "Build process encountered an error: $_" -ForegroundColor Red
    exit 1
}

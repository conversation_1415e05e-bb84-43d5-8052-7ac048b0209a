/**
 * ErrorBoundary Component Test Suite
 * Comprehensive testing for enterprise-grade error boundary functionality
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ErrorBoundary from '../ErrorBoundary';

// Test theme
const theme = createTheme();

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      {children}
    </ThemeProvider>
  </BrowserRouter>
);

// Component that throws an error for testing
const ThrowError = ({ shouldThrow = false, errorMessage = 'Test error' }) => {
  if (shouldThrow) {
    throw new Error(errorMessage);
  }
  return <div>No error</div>;
};

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});

afterAll(() => {
  console.error = originalError;
});

describe('ErrorBoundary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    test('renders children when no error occurs', () => {
      render(
        <TestWrapper>
          <ErrorBoundary>
            <div>Test content</div>
          </ErrorBoundary>
        </TestWrapper>
      );

      expect(screen.getByText('Test content')).toBeInTheDocument();
    });

    test('catches and displays error when child component throws', () => {
      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByRole('alert')).toBeInTheDocument();
    });

    test('displays error details when enabled', () => {
      render(
        <TestWrapper>
          <ErrorBoundary showErrorDetails>
            <ThrowError shouldThrow errorMessage="Custom error message" />
          </ErrorBoundary>
        </TestWrapper>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      
      // Click to expand technical details
      const detailsButton = screen.getByText('Technical Details (for developers)');
      fireEvent.click(detailsButton);
      
      expect(screen.getByText(/Custom error message/)).toBeInTheDocument();
    });
  });

  describe('Error Categorization', () => {
    test('categorizes network errors correctly', () => {
      const networkError = new Error('Network request failed');
      
      render(
        <TestWrapper>
          <ErrorBoundary enableAnalytics onAnalytics={jest.fn()}>
            <ThrowError shouldThrow errorMessage="Network request failed" />
          </ErrorBoundary>
        </TestWrapper>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByText(/Error Category:/)).toBeInTheDocument();
    });

    test('displays appropriate severity levels', () => {
      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow errorMessage="Critical security error" />
          </ErrorBoundary>
        </TestWrapper>
      );

      // Should display severity chip
      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });
  });

  describe('Recovery Actions', () => {
    test('handles refresh action', async () => {
      const user = userEvent.setup();
      
      // Mock window.location.reload
      const mockReload = jest.fn();
      Object.defineProperty(window, 'location', {
        value: { reload: mockReload },
        writable: true
      });

      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      const refreshButton = screen.getByText('Refresh Page');
      await user.click(refreshButton);

      expect(mockReload).toHaveBeenCalled();
    });

    test('handles retry functionality', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ErrorBoundary enableRetry maxRetries={3}>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      const retryButton = screen.getByText(/Retry \(0\/3\)/);
      await user.click(retryButton);

      // Should show retrying state
      await waitFor(() => {
        expect(screen.getByText('Attempting to recover...')).toBeInTheDocument();
      });
    });

    test('handles navigation to home', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ErrorBoundary homeRoute="/custom-home">
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      const homeButton = screen.getByText('Go to Dashboard');
      expect(homeButton).toHaveAttribute('href', '/custom-home');
    });
  });

  describe('Analytics Integration', () => {
    test('tracks error events when analytics enabled', () => {
      const onAnalytics = jest.fn();
      
      render(
        <TestWrapper>
          <ErrorBoundary enableAnalytics onAnalytics={onAnalytics}>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          component: 'ErrorBoundary',
          action: 'error_caught'
        })
      );
    });

    test('tracks user interactions', async () => {
      const user = userEvent.setup();
      const onAnalytics = jest.fn();
      
      render(
        <TestWrapper>
          <ErrorBoundary enableAnalytics onAnalytics={onAnalytics}>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      const refreshButton = screen.getByText('Refresh Page');
      await user.click(refreshButton);

      expect(onAnalytics).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'refresh_clicked'
        })
      );
    });
  });

  describe('Error Reporting', () => {
    test('handles error reporting', async () => {
      const user = userEvent.setup();
      const onErrorReport = jest.fn();
      
      render(
        <TestWrapper>
          <ErrorBoundary enableReporting onErrorReport={onErrorReport}>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      const reportButton = screen.getByText('Report Error');
      await user.click(reportButton);

      expect(onErrorReport).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.any(Object),
          errorCategory: expect.any(String),
          errorSeverity: expect.any(String)
        })
      );

      // Should show success state
      expect(screen.getByText('Reported')).toBeInTheDocument();
    });

    test('handles feedback submission', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      // Open feedback dialog
      const feedbackButton = screen.getByText('Feedback');
      await user.click(feedbackButton);

      expect(screen.getByText('Provide Feedback')).toBeInTheDocument();

      // Enter feedback
      const textField = screen.getByPlaceholderText(/Describe what happened/);
      await user.type(textField, 'This is test feedback');

      // Submit feedback
      const submitButton = screen.getByText('Submit Feedback');
      await user.click(submitButton);

      // Dialog should close
      await waitFor(() => {
        expect(screen.queryByText('Provide Feedback')).not.toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA attributes', () => {
      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      const errorContainer = screen.getByRole('alert');
      expect(errorContainer).toHaveAttribute('aria-live', 'assertive');
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      const refreshButton = screen.getByText('Refresh Page');
      refreshButton.focus();
      
      expect(document.activeElement).toBe(refreshButton);
      
      await user.tab();
      // Should move to next interactive element
    });

    test('provides proper button labels', () => {
      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      const refreshButton = screen.getByLabelText('Refresh the page to recover from error');
      const homeButton = screen.getByLabelText('Navigate to home page');
      
      expect(refreshButton).toBeInTheDocument();
      expect(homeButton).toBeInTheDocument();
    });
  });

  describe('Fallback Types', () => {
    test('renders minimal fallback when specified', () => {
      render(
        <TestWrapper>
          <ErrorBoundary fallbackType="minimal">
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
      expect(screen.getByText('Refresh')).toBeInTheDocument();
      
      // Should not have detailed UI elements
      expect(screen.queryByText('Error Category:')).not.toBeInTheDocument();
    });

    test('renders custom fallback component', () => {
      const CustomFallback = ({ error, onRetry }) => (
        <div>
          <h1>Custom Error UI</h1>
          <p>Error: {error.message}</p>
          <button onClick={onRetry}>Custom Retry</button>
        </div>
      );

      render(
        <TestWrapper>
          <ErrorBoundary fallbackType="custom" fallbackComponent={CustomFallback}>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      expect(screen.getByText('Custom Error UI')).toBeInTheDocument();
      expect(screen.getByText('Custom Retry')).toBeInTheDocument();
    });
  });

  describe('Network Status', () => {
    test('displays network warning when offline', () => {
      // Mock navigator.onLine
      Object.defineProperty(navigator, 'onLine', {
        writable: true,
        value: false
      });

      render(
        <TestWrapper>
          <ErrorBoundary enableNetworkStatus>
            <ThrowError shouldThrow />
          </ErrorBoundary>
        </TestWrapper>
      );

      expect(screen.getByText('Network Connection Issue')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    test('handles multiple rapid error occurrences', () => {
      const { rerender } = render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow={false} />
          </ErrorBoundary>
        </TestWrapper>
      );

      // Trigger multiple errors rapidly
      for (let i = 0; i < 5; i++) {
        rerender(
          <TestWrapper>
            <ErrorBoundary>
              <ThrowError shouldThrow errorMessage={`Error ${i}`} />
            </ErrorBoundary>
          </TestWrapper>
        );
      }

      expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    });
  });
});

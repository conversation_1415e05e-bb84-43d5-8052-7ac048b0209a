/**
 * User Preferences Context
 * Production-ready user preferences management with comprehensive error handling and monitoring
 * Advanced preference features with validation, caching, and real-time updates
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import {
  getUserPreferences,
  updateUserPreferences,
  getAllCategories,
  getAllTargetSegments
} from '../api/userPreferences';

// Configuration constants
const CONFIG = {
  // Cache settings
  CACHE_DURATION: 300000, // 5 minutes

  // Validation settings
  MIN_CATEGORY_NAME_LENGTH: 2,
  MAX_CATEGORY_NAME_LENGTH: 50,
  MIN_SEGMENT_NAME_LENGTH: 2,
  MAX_SEGMENT_NAME_LENGTH: 50,

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second

  // Debounce settings
  UPDATE_DEBOUNCE_DELAY: 500, // 500ms

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[UserPreferencesContext] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[UserPreferencesContext] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('User Preferences Context Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[UserPreferencesContext] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('User Preferences Context Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[UserPreferencesContext] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('User Preferences Context Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const UserPreferencesContext = createContext();

// Custom hook to use user preferences context
// eslint-disable-next-line react-refresh/only-export-components
export const useUserPreferences = () => {
  const context = useContext(UserPreferencesContext);
  if (!context) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
};

export const UserPreferencesProvider = ({ children }) => {
  const [preferences, setPreferences] = useState(null);
  const [allCategories, setAllCategories] = useState({ predefined_categories: [], custom_categories: [] });
  const [allSegments, setAllSegments] = useState({ predefined_segments: [], industry_specific_segments: {}, custom_segments: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isDirty, setIsDirty] = useState(false);

  // Refs for caching and debouncing
  const cacheRef = useRef({
    preferences: null,
    categories: null,
    segments: null,
    timestamp: null
  });
  const updateTimeoutRef = useRef(null);

  // Validation helper
  const validatePreferences = useCallback((prefs) => {
    const errors = [];

    if (prefs && typeof prefs !== 'object') {
      errors.push('Preferences must be an object');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, []);

  // Load initial data with caching
  const loadData = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      // Check cache first
      const now = Date.now();
      const cached = cacheRef.current;

      if (!forceRefresh && cached.timestamp &&
          (now - cached.timestamp) < CONFIG.CACHE_DURATION &&
          cached.preferences && cached.categories && cached.segments) {
        logger.debug('Using cached preferences data');
        setPreferences(cached.preferences);
        setAllCategories(cached.categories);
        setAllSegments(cached.segments);
        setLastUpdated(new Date(cached.timestamp));
        return;
      }

      logger.debug('Loading user preferences data from API');

      const [preferencesData, categoriesData, segmentsData] = await Promise.all([
        getUserPreferences().catch((err) => {
          logger.warn('Failed to load user preferences', err);
          return null;
        }),
        getAllCategories().catch((err) => {
          logger.warn('Failed to load categories', err);
          return { predefined_categories: [], custom_categories: [] };
        }),
        getAllTargetSegments().catch((err) => {
          logger.warn('Failed to load target segments', err);
          return { predefined_segments: [], industry_specific_segments: {}, custom_segments: [] };
        })
      ]);

      // Validate preferences data
      if (preferencesData) {
        const validation = validatePreferences(preferencesData);
        if (!validation.isValid) {
          logger.warn('Invalid preferences data received', { errors: validation.errors });
        }
      }

      // Update cache
      cacheRef.current = {
        preferences: preferencesData,
        categories: categoriesData,
        segments: segmentsData,
        timestamp: now
      };

      setPreferences(preferencesData);
      setAllCategories(categoriesData);
      setAllSegments(segmentsData);
      setLastUpdated(new Date());
      setIsDirty(false);

      logger.info('User preferences data loaded successfully', {
        hasPreferences: !!preferencesData,
        categoriesCount: categoriesData.predefined_categories.length + categoriesData.custom_categories.length,
        segmentsCount: segmentsData.predefined_segments.length + segmentsData.custom_segments.length
      });
    } catch (err) {
      logger.error('Error loading user preferences data', err);
      setError('Failed to load preferences data');
    } finally {
      setLoading(false);
    }
  }, [validatePreferences]);

  // Debounced update preferences
  const debouncedUpdatePrefs = useCallback(async (newPreferences) => {
    // Clear existing timeout
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    // Set dirty flag immediately
    setIsDirty(true);

    return new Promise((resolve, reject) => {
      updateTimeoutRef.current = setTimeout(async () => {
        try {
          logger.debug('Updating user preferences', { preferences: newPreferences });

          // Validate preferences
          const validation = validatePreferences(newPreferences);
          if (!validation.isValid) {
            const errorMessage = validation.errors.join(', ');
            logger.warn('Invalid preferences data', { errors: validation.errors });
            throw new Error(errorMessage);
          }

          const updatedPreferences = await updateUserPreferences(newPreferences);

          // Update cache
          cacheRef.current.preferences = updatedPreferences;
          cacheRef.current.timestamp = Date.now();

          setPreferences(updatedPreferences);
          setLastUpdated(new Date());
          setIsDirty(false);

          logger.info('User preferences updated successfully');
          resolve(updatedPreferences);
        } catch (err) {
          logger.error('Error updating preferences', err);
          setIsDirty(false);
          reject(err);
        }
      }, CONFIG.UPDATE_DEBOUNCE_DELAY);
    });
  }, [validatePreferences]);

  // Update preferences (immediate)
  const updatePrefs = useCallback(async (newPreferences) => {
    try {
      logger.debug('Updating user preferences immediately', { preferences: newPreferences });

      // Validate preferences
      const validation = validatePreferences(newPreferences);
      if (!validation.isValid) {
        const errorMessage = validation.errors.join(', ');
        logger.warn('Invalid preferences data', { errors: validation.errors });
        throw new Error(errorMessage);
      }

      const updatedPreferences = await updateUserPreferences(newPreferences);

      // Update cache
      cacheRef.current.preferences = updatedPreferences;
      cacheRef.current.timestamp = Date.now();

      setPreferences(updatedPreferences);
      setLastUpdated(new Date());
      setIsDirty(false);

      logger.info('User preferences updated successfully');
      return updatedPreferences;
    } catch (err) {
      logger.error('Error updating preferences', err);
      throw err;
    }
  }, [validatePreferences]);

  // Refresh categories
  const refreshCategories = useCallback(async () => {
    try {
      logger.debug('Refreshing categories');
      const categoriesData = await getAllCategories();

      // Update cache
      cacheRef.current.categories = categoriesData;
      cacheRef.current.timestamp = Date.now();

      setAllCategories(categoriesData);
      logger.info('Categories refreshed successfully', {
        predefinedCount: categoriesData.predefined_categories.length,
        customCount: categoriesData.custom_categories.length
      });
      return categoriesData;
    } catch (err) {
      logger.error('Error refreshing categories', err);
      throw err;
    }
  }, []);

  // Refresh segments
  const refreshSegments = useCallback(async () => {
    try {
      logger.debug('Refreshing segments');
      const segmentsData = await getAllTargetSegments();

      // Update cache
      cacheRef.current.segments = segmentsData;
      cacheRef.current.timestamp = Date.now();

      setAllSegments(segmentsData);
      logger.info('Segments refreshed successfully', {
        predefinedCount: segmentsData.predefined_segments.length,
        customCount: segmentsData.custom_segments.length,
        industryCount: Object.keys(segmentsData.industry_specific_segments).length
      });
      return segmentsData;
    } catch (err) {
      logger.error('Error refreshing segments', err);
      throw err;
    }
  }, []);

  // Get default values for forms
  const getDefaultValues = useCallback(() => {
    if (!preferences?.service_preferences) {
      return {
        category: '',
        target_segments: [],
        pricing_model: '',
        service_level: '',
        delivery_timeline: '',
        target_industry: ''
      };
    }

    const prefs = preferences.service_preferences;
    return {
      category: prefs.default_categories?.[0] || '',
      target_segments: prefs.default_target_segments || [],
      pricing_model: prefs.default_pricing_model || '',
      service_level: prefs.default_service_level || '',
      delivery_timeline: prefs.default_delivery_timeline || '',
      target_industry: prefs.frequently_used_industries?.[0] || ''
    };
  }, [preferences]);

  // Get all available categories
  const getAllAvailableCategories = useCallback(() => {
    return [
      ...allCategories.predefined_categories.map(cat => cat.name),
      ...allCategories.custom_categories.map(cat => cat.name)
    ];
  }, [allCategories]);

  // Get all available segments
  const getAllAvailableSegments = useCallback(() => {
    const segments = [
      ...allSegments.predefined_segments,
      ...allSegments.custom_segments.map(seg => seg.name)
    ];

    // Add industry-specific segments
    Object.values(allSegments.industry_specific_segments).forEach(industrySegments => {
      segments.push(...industrySegments);
    });

    return [...new Set(segments)]; // Remove duplicates
  }, [allSegments]);

  // Check if user has custom categories
  const hasCustomCategories = useCallback(() => {
    return allCategories.custom_categories.length > 0;
  }, [allCategories]);

  // Check if user has custom segments
  const hasCustomSegments = useCallback(() => {
    return allSegments.custom_segments.length > 0;
  }, [allSegments]);

  // Validate category data
  const validateCategoryData = useCallback((categoryData) => {
    const errors = [];

    if (!categoryData || typeof categoryData !== 'object') {
      errors.push('Category data is required');
      return { isValid: false, errors };
    }

    if (!categoryData.name || typeof categoryData.name !== 'string') {
      errors.push('Category name is required');
    } else if (categoryData.name.length < CONFIG.MIN_CATEGORY_NAME_LENGTH) {
      errors.push(`Category name must be at least ${CONFIG.MIN_CATEGORY_NAME_LENGTH} characters`);
    } else if (categoryData.name.length > CONFIG.MAX_CATEGORY_NAME_LENGTH) {
      errors.push(`Category name must be less than ${CONFIG.MAX_CATEGORY_NAME_LENGTH} characters`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }, []);

  // Add a new custom category and refresh the list
  const addCustomCategory = useCallback(async (categoryData) => {
    try {
      // Validate category data
      const validation = validateCategoryData(categoryData);
      if (!validation.isValid) {
        const errorMessage = validation.errors.join(', ');
        logger.warn('Invalid category data', { errors: validation.errors });
        throw new Error(errorMessage);
      }

      logger.debug('Adding custom category', { categoryData });

      // The category creation is handled by the component
      // We just need to refresh the categories list
      await refreshCategories();

      logger.info('Custom category added successfully', { categoryName: categoryData.name });
    } catch (err) {
      logger.error('Error adding custom category', err);
      throw err;
    }
  }, [refreshCategories, validateCategoryData]);

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // Enhanced context value with organized structure
  const value = {
    // State data
    preferences,
    allCategories,
    allSegments,
    loading,
    error,
    lastUpdated,
    isDirty,

    // Core actions
    loadData,
    updatePreferences: updatePrefs,
    updatePreferencesDebounced: debouncedUpdatePrefs,
    refreshCategories,
    refreshSegments,
    addCustomCategory,

    // Utility functions
    clearError: () => setError(null),
    clearCache: () => {
      cacheRef.current = { preferences: null, categories: null, segments: null, timestamp: null };
      logger.debug('Preferences cache cleared');
    },
    forceRefresh: () => loadData(true),

    // Validation helpers
    validatePreferences,
    validateCategoryData,

    // Helper functions
    getDefaultValues,
    getAllAvailableCategories,
    getAllAvailableSegments,
    hasCustomCategories,
    hasCustomSegments,

    // Enhanced helper functions
    hasPreferences: !!preferences,
    getPreferencesCount: () => {
      if (!preferences?.service_preferences) return 0;
      const prefs = preferences.service_preferences;
      let count = 0;
      if (prefs.default_categories?.length) count++;
      if (prefs.default_target_segments?.length) count++;
      if (prefs.default_pricing_model) count++;
      if (prefs.default_service_level) count++;
      if (prefs.default_delivery_timeline) count++;
      if (prefs.frequently_used_industries?.length) count++;
      return count;
    },

    getCategoriesCount: () => ({
      predefined: allCategories.predefined_categories.length,
      custom: allCategories.custom_categories.length,
      total: allCategories.predefined_categories.length + allCategories.custom_categories.length
    }),

    getSegmentsCount: () => ({
      predefined: allSegments.predefined_segments.length,
      custom: allSegments.custom_segments.length,
      industry: Object.keys(allSegments.industry_specific_segments).length,
      total: allSegments.predefined_segments.length + allSegments.custom_segments.length
    }),

    // Search and filter helpers
    searchCategories: (query) => {
      const lowerQuery = query.toLowerCase();
      return [
        ...allCategories.predefined_categories.filter(cat =>
          cat.name.toLowerCase().includes(lowerQuery)
        ),
        ...allCategories.custom_categories.filter(cat =>
          cat.name.toLowerCase().includes(lowerQuery)
        )
      ];
    },

    searchSegments: (query) => {
      const lowerQuery = query.toLowerCase();
      const results = [];

      // Search predefined segments
      results.push(...allSegments.predefined_segments.filter(seg =>
        seg.toLowerCase().includes(lowerQuery)
      ));

      // Search custom segments
      results.push(...allSegments.custom_segments.filter(seg =>
        seg.name.toLowerCase().includes(lowerQuery)
      ).map(seg => seg.name));

      // Search industry-specific segments
      Object.values(allSegments.industry_specific_segments).forEach(industrySegments => {
        results.push(...industrySegments.filter(seg =>
          seg.toLowerCase().includes(lowerQuery)
        ));
      });

      return [...new Set(results)];
    },

    // Category helpers
    getCategoryByName: (name) => {
      return [...allCategories.predefined_categories, ...allCategories.custom_categories]
        .find(cat => cat.name === name);
    },

    isCategoryCustom: (name) => {
      return allCategories.custom_categories.some(cat => cat.name === name);
    },

    // Segment helpers
    getSegmentsByIndustry: (industry) => {
      return allSegments.industry_specific_segments[industry] || [];
    },

    isSegmentCustom: (name) => {
      return allSegments.custom_segments.some(seg => seg.name === name);
    },

    // Cache information
    getCacheInfo: () => ({
      hasCache: !!cacheRef.current.timestamp,
      cacheAge: cacheRef.current.timestamp ? Date.now() - cacheRef.current.timestamp : null,
      lastUpdated,
      isDirty
    }),

    // Status helpers
    isDataStale: () => {
      if (!cacheRef.current.timestamp) return true;
      return (Date.now() - cacheRef.current.timestamp) > CONFIG.CACHE_DURATION;
    },

    hasUnsavedChanges: isDirty,
    isLoading: loading,
    hasError: !!error
  };

  return (
    <UserPreferencesContext.Provider value={value}>
      {children}
    </UserPreferencesContext.Provider>
  );
};

// Export the context
export { UserPreferencesContext };

// Default export for convenience
export default UserPreferencesProvider;

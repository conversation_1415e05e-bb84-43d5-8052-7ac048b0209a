#!/usr/bin/env node
// @since 2024-1-1 to 2025-25-7

const express = require('express');
const path = require('path');
const app = express();
const port = 3000;

// Serve static files from dist directory
app.use(express.static(path.join(__dirname, 'dist')));

// Handle client-side routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

app.listen(port, () => {
  console.log(`🚀 Frontend build served at http://localhost:${port}`);
  console.log('📁 Serving files from: ./dist');
  console.log('🔄 Client-side routing enabled');
});

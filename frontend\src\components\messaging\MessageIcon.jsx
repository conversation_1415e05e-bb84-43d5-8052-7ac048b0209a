// @since 2024-1-1 to 2025-25-7
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Badge } from '@mui/material';
import EmailIcon from '@mui/icons-material/Email';
import { useMessage } from '../../hooks/useMessage';
import AccessibleButton from '../common/AccessibleButton';

/**
 * MessageIcon component for the header navigation
 * Displays an inbox icon with a notification badge showing unread message count
 */
const MessageIcon = () => {
  const navigate = useNavigate();
  const { unreadCount, loadUnreadCount } = useMessage();

  // Load unread count when component mounts
  useEffect(() => {
    loadUnreadCount();
  }, [loadUnreadCount]);

  const handleClick = () => {
    navigate('/inbox');
  };

  return (
    <AccessibleButton
      isIconButton
      color="inherit"
      onClick={handleClick}
      ariaLabel={`Messages${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
      tooltipText={`Messages${unreadCount > 0 ? ` - ${unreadCount} unread` : ''}`}
      sx={{
        '&:hover': {
          animation: 'pulse 1s infinite',
          '@keyframes pulse': {
            '0%': {
              transform: 'scale(1)',
            },
            '50%': {
              transform: 'scale(1.1)',
            },
            '100%': {
              transform: 'scale(1)',
            },
          },
        },
      }}
    >
      <Badge badgeContent={unreadCount} color="error" max={99}>
        <EmailIcon />
      </Badge>
    </AccessibleButton>
  );
};

export default MessageIcon;


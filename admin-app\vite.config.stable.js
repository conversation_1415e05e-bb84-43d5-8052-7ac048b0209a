// @since 2024-1-1 to 2025-25-7
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// Stable Vite configuration for admin app
export default defineConfig({
  plugins: [
    react({
      // Minimal React configuration for stability
      jsxImportSource: '@emotion/react',
      babel: {
        plugins: ['@emotion/babel-plugin']
      }
    })
  ],

  // Path resolution
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json']
  },

  // Define global constants
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    global: 'globalThis'
  },

  // Stable development server configuration
  server: {
    port: 3001,
    host: '127.0.0.1', // Use specific IP for stability
    strictPort: false, // Allow fallback ports
    open: false,
    cors: true,
    
    // Disable HMR for maximum stability
    hmr: false,
    
    // Proxy configuration
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        timeout: 10000,
        secure: false
      }
    },
    
    // Minimal file watching
    watch: {
      usePolling: false,
      interval: 5000, // Slower polling
      ignored: [
        '**/node_modules/**',
        '**/dist/**',
        '**/.git/**',
        '**/coverage/**',
        '**/*.log',
        '**/temp/**',
        '**/.vite/**',
        '**/logs/**',
        '**/__pycache__/**',
        '**/backend/**',
        '**/.env*',
        '**/package-lock.json',
        '**/yarn.lock',
        '**/*.backup',
        '**/*.old',
        '**/*.bak',
        '**/*.tmp'
      ]
    }
  },

  // Optimized build configuration
  build: {
    outDir: 'dist',
    sourcemap: false, // Disable for stability
    minify: 'esbuild',
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          router: ['react-router-dom']
        }
      }
    }
  },

  // Preview configuration
  preview: {
    port: 3001,
    host: '127.0.0.1',
    strictPort: false
  },

  // Optimization settings
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@mui/icons-material',
      'react-router-dom',
      'axios'
    ]
  }
})

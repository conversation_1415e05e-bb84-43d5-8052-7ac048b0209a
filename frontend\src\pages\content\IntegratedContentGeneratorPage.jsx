// @since 2024-1-1 to 2025-25-7
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Container, Typography, Alert } from '@mui/material';
import { Sync as SyncIcon } from '@mui/icons-material';

import ConsolidatedContentGenerator from '../../components/content/ConsolidatedContentGenerator';
import { useContentGeneration } from '../../contexts/ContentGenerationContext';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';

/**
 * Integrated Content Generator Page
 * Provides unified interface for synchronized text and image generation
 */
const IntegratedContentGeneratorPage = () => {
  const { sharedContext } = useContentGeneration();
  const { showSuccess } = useAdvancedToast();

  const handleContentGenerated = (content) => {
    console.log('Content generated:', content);
    showSuccess('Synchronized content generated successfully!');
  };

  const handleSaveToLibrary = (content) => {
    console.log('Saving to library:', content);
    showSuccess('Content saved to library!');
    // TODO: Implement actual save to library functionality
  };

  return (
    <>
      <Helmet>
        <title>Integrated Content Generator - AI-Powered Synchronized Content Creation</title>
        <meta 
          name="description" 
          content="Generate synchronized text and image content with AI-powered alignment, consistent branding, and ICP targeting." 
        />
        <meta name="keywords" content="content generation, AI content, synchronized content, text and image generation, branding, ICP targeting" />
      </Helmet>

      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Page Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <SyncIcon sx={{ fontSize: 32, mr: 2, color: 'primary.main' }} />
            <Typography variant="h4" component="h1">
              Integrated Content Generator
            </Typography>
          </Box>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Create synchronized text and image content with AI-powered alignment, 
            consistent branding, and targeted messaging for your ideal customer profiles.
          </Typography>

          {/* Feature Highlights */}
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>New Integration Features:</strong> Synchronized content generation ensures your text and images 
              are thematically aligned, culturally adapted, and consistently branded across all platforms.
            </Typography>
          </Alert>
        </Box>

        {/* Main Content Generator */}
        <ConsolidatedContentGenerator
          showAdvancedOptions={true}
          onContentGenerated={handleContentGenerated}
          onSaveToLibrary={handleSaveToLibrary}
        />

        {/* Context Information */}
        {sharedContext.selectedICP && (
          <Box sx={{ mt: 4, p: 3, bgcolor: 'background.paper', borderRadius: 2 }}>
            <Typography variant="h6" gutterBottom>
              Active Context
            </Typography>
            
            <Typography variant="body2" color="text.secondary">
              <strong>ICP:</strong> {sharedContext.selectedICP.name}
            </Typography>
            
            <Typography variant="body2" color="text.secondary">
              <strong>Language:</strong> {sharedContext.languageSettings?.language?.toUpperCase()}
            </Typography>
            
            {sharedContext.brandingSettings && (
              <Typography variant="body2" color="text.secondary">
                <strong>Branding:</strong> Active with {
                  Object.keys(sharedContext.brandingSettings).length
                } settings applied
              </Typography>
            )}
            
            {sharedContext.productImages?.length > 0 && (
              <Typography variant="body2" color="text.secondary">
                <strong>Product Images:</strong> {sharedContext.productImages.length} uploaded
              </Typography>
            )}
          </Box>
        )}
      </Container>
    </>
  );
};

export default IntegratedContentGeneratorPage;

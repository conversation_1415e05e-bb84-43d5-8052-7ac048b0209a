// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Tabs,
  Tab,
  Divider,
  Paper,
  CircularProgress,
  useTheme,
  Breadcrumbs,
  Link,
  Container,
  Alert,
  AlertTitle
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import GroupsIcon from '@mui/icons-material/Groups';
import SettingsIcon from '@mui/icons-material/Settings';
import MailIcon from '@mui/icons-material/Mail';
import { useTeam } from '../../contexts/TeamContext';
import { useAuth } from '../../contexts/AuthContext';
import PageHeader from '../../components/common/PageHeader';
import TeamMembersList from '../../components/teams/TeamMembersList';
import TeamInviteForm from '../../components/teams/TeamInviteForm';
import TeamSettings from '../../components/teams/TeamSettings';

const TeamDetailPage = () => {
  const theme = useTheme();
  const { teamId } = useParams();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { fetchTeam, loading } = useTeam();

  const [team, setTeam] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [isOwnerOrAdmin, setIsOwnerOrAdmin] = useState(false);

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Load team data with authentication check
  const loadTeam = useCallback(async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const teamData = await fetchTeam(teamId);
    if (teamData) {
      setTeam(teamData);

      // Check if user is owner or admin
      const userRole = teamData.members.find(member => member.user_id === user?.id)?.role;
      setIsOwnerOrAdmin(userRole === 'owner' || userRole === 'admin');
    } else {
      navigate('/settings?tab=teams');
    }
  }, [teamId, fetchTeam, navigate, user?.id, isAuthenticated]);

  // Set initial tab from URL
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab === 'members') setTabValue(0);
    else if (tab === 'invite') setTabValue(1);
    else if (tab === 'settings') setTabValue(2);
  }, [searchParams]);

  // Load team on mount (only if authenticated)
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      loadTeam();
    }
  }, [loadTeam, isAuthenticated, authLoading]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);

    // Update URL
    const tabName = newValue === 0 ? 'members' : newValue === 1 ? 'invite' : 'settings';
    setSearchParams({ tab: tabName });
  };

  // Handle back button
  const handleBack = () => {
    navigate('/settings?tab=teams');
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  if (loading && !team) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 300,
        p: 3,
        backgroundColor: theme.palette.background.default,
        borderRadius: 2
      }}>
        <Paper sx={{
          p: 4,
          textAlign: 'center',
          backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[50]
        }}>
          <CircularProgress size={40} sx={{ color: theme.palette.primary.main }} />
          <Divider sx={{ my: 2 }} />
          <Typography variant="body1" color="text.secondary">
            Loading team details...
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Please wait while we fetch the team information
            {user?.name && ` for ${user.name}`}
          </Typography>
        </Paper>
      </Box>
    );
  }

  if (!team) {
    return null;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs>
          <Link
            underline="hover"
            color="inherit"
            sx={{ cursor: 'pointer' }}
            onClick={handleBack}
          >
            Teams
          </Link>
          <Typography color="text.primary">{team.name}</Typography>
        </Breadcrumbs>
      </Box>

      <PageHeader
        title={team.name}
        subtitle={
          <Box>
            <Typography variant="body1" color="text.secondary">
              {team.description || 'Team collaboration space'}
            </Typography>
            {user?.name && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Welcome, {user.name} • {team.members?.find(member => member.user_id === user?.id)?.role || 'Member'}
              </Typography>
            )}
          </Box>
        }
        action={
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            Back to Teams
          </Button>
        }
      />

      {/* Enhanced team information section with theme integration */}
      <Paper sx={{
        mt: 3,
        mb: 2,
        p: 3,
        backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[900] : theme.palette.grey[50],
        border: `1px solid ${theme.palette.divider}`
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h6" color="primary">
              Team Overview
            </Typography>
            {user?.name && (
              <Typography variant="body2" color="text.secondary">
                Viewing as {user.name}
              </Typography>
            )}
          </Box>
          <Box sx={{
            px: 2,
            py: 0.5,
            borderRadius: 1,
            backgroundColor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText
          }}>
            <Typography variant="caption">
              {team.members?.length || 0} Members
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
          <Box>
            <Typography variant="body2" color="text.secondary">
              Created
            </Typography>
            <Typography variant="body1">
              {team.created_at ? new Date(team.created_at).toLocaleDateString() : 'Unknown'}
            </Typography>
          </Box>

          <Divider orientation="vertical" flexItem />

          <Box>
            <Typography variant="body2" color="text.secondary">
              Your Role
            </Typography>
            <Typography variant="body1" sx={{
              color: isOwnerOrAdmin ? theme.palette.success.main : theme.palette.text.primary,
              fontWeight: isOwnerOrAdmin ? 'bold' : 'normal'
            }}>
              {team.members?.find(member => member.user_id === user?.id)?.role || 'Member'}
            </Typography>
          </Box>

          <Divider orientation="vertical" flexItem />

          <Box>
            <Typography variant="body2" color="text.secondary">
              Status
            </Typography>
            <Typography variant="body1" sx={{
              color: theme.palette.success.main,
              fontWeight: 'medium'
            }}>
              Active
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Access control notification */}
      {!team.members?.find(member => member.user_id === user?.id) && (
        <Alert severity="warning" sx={{ mt: 2, mb: 2 }}>
          <AlertTitle>Limited Access</AlertTitle>
          <Typography variant="body2">
            You are not a member of this team. Some features may be restricted.
            Contact a team owner or admin to request access.
          </Typography>
        </Alert>
      )}

      <Paper sx={{ mt: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<GroupsIcon />}
            label="Members"
            iconPosition="start"
          />
          {isOwnerOrAdmin && (
            <Tab
              icon={<MailIcon />}
              label="Invite"
              iconPosition="start"
            />
          )}
          {isOwnerOrAdmin && (
            <Tab
              icon={<SettingsIcon />}
              label="Settings"
              iconPosition="start"
            />
          )}
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* Members Tab */}
          {tabValue === 0 && (
            <Box>
              <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
                <GroupsIcon color="primary" />
                <Typography variant="h6">Team Members</Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />
              <TeamMembersList
                team={team}
                isOwnerOrAdmin={isOwnerOrAdmin}
                onTeamUpdated={loadTeam}
              />
            </Box>
          )}

          {/* Invite Tab */}
          {tabValue === 1 && isOwnerOrAdmin && (
            <Box>
              <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
                <MailIcon color="primary" />
                <Typography variant="h6">Invite Members</Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />
              <TeamInviteForm
                teamId={team.id}
                onInviteSent={loadTeam}
              />
            </Box>
          )}

          {/* Settings Tab */}
          {tabValue === 2 && isOwnerOrAdmin && (
            <Box>
              <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
                <SettingsIcon color="primary" />
                <Typography variant="h6">Team Settings</Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />
              <TeamSettings
                team={team}
                onTeamUpdated={loadTeam}
              />
            </Box>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default TeamDetailPage;

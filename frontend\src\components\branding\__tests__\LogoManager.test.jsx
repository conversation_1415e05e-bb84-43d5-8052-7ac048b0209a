import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import LogoManager from '../LogoManager';

// Mock the notification hook
const mockShowSuccessNotification = vi.fn();
const mockShowErrorNotification = vi.fn();

vi.mock('../../../hooks/useNotification', () => ({
  useNotification: () => ({
    showSuccessNotification: mockShowSuccessNotification,
    showErrorNotification: mockShowErrorNotification,
  }),
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-logo-url');

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('LogoManager', () => {
  const mockLogoData = {
    logo_url: 'https://example.com/logo.png',
    logo_settings: {
      size: 25,
      position: 'bottom-right',
      opacity: 90,
      rotation: 0,
      useWatermark: false,
      watermarkOpacity: 20,
      watermarkScale: 100
    }
  };

  const mockProps = {
    logoData: mockLogoData,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders logo manager correctly', () => {
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Logo Management')).toBeInTheDocument();
    expect(screen.getByText(/Upload and configure your brand logo/)).toBeInTheDocument();
  });

  test('displays existing logo when logo_url is provided', () => {
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByAltText('Brand Logo')).toBeInTheDocument();
    expect(screen.getByText('Change Logo')).toBeInTheDocument();
    expect(screen.getByText('Remove')).toBeInTheDocument();
  });

  test('shows upload button when no logo is provided', () => {
    const propsWithoutLogo = {
      ...mockProps,
      logoData: {
        logo_url: '',
        logo_settings: mockLogoData.logo_settings
      }
    };

    render(
      <TestWrapper>
        <LogoManager {...propsWithoutLogo} />
      </TestWrapper>
    );

    expect(screen.getByText('Upload Logo')).toBeInTheDocument();
    expect(screen.queryByAltText('Brand Logo')).not.toBeInTheDocument();
  });

  test('displays all logo setting controls', () => {
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Logo Settings')).toBeInTheDocument();
    expect(screen.getByText('Size')).toBeInTheDocument();
    expect(screen.getByText('Position')).toBeInTheDocument();
    expect(screen.getByText('Opacity')).toBeInTheDocument();
    expect(screen.getByText('Rotation')).toBeInTheDocument();
    expect(screen.getByText('Use as Watermark')).toBeInTheDocument();
  });

  test('shows preview section', () => {
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Preview')).toBeInTheDocument();
    expect(screen.getByAltText('Preview')).toBeInTheDocument();
    expect(screen.getByAltText('Logo Preview')).toBeInTheDocument();
  });

  test('handles logo size change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    const sizeSlider = screen.getByRole('slider', { name: /size/i });
    
    // Change the slider value
    fireEvent.change(sizeSlider, { target: { value: '30' } });
    
    // Apply settings
    const applyButton = screen.getByText('Apply Settings');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockLogoData,
        logo_settings: {
          ...mockLogoData.logo_settings,
          size: 30
        }
      });
    });
  });

  test('handles logo position change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    const positionSelect = screen.getByRole('combobox');
    await user.click(positionSelect);
    
    const topLeftOption = screen.getByText('Top Left');
    await user.click(topLeftOption);

    // Apply settings
    const applyButton = screen.getByText('Apply Settings');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockLogoData,
        logo_settings: {
          ...mockLogoData.logo_settings,
          position: 'top-left'
        }
      });
    });
  });

  test('handles opacity change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    const opacitySlider = screen.getByRole('slider', { name: /opacity/i });
    
    // Change the slider value
    fireEvent.change(opacitySlider, { target: { value: '75' } });
    
    // Apply settings
    const applyButton = screen.getByText('Apply Settings');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockLogoData,
        logo_settings: {
          ...mockLogoData.logo_settings,
          opacity: 75
        }
      });
    });
  });

  test('handles rotation change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    const rotationSlider = screen.getByRole('slider', { name: /rotation/i });
    
    // Change the slider value
    fireEvent.change(rotationSlider, { target: { value: '45' } });
    
    // Apply settings
    const applyButton = screen.getByText('Apply Settings');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockLogoData,
        logo_settings: {
          ...mockLogoData.logo_settings,
          rotation: 45
        }
      });
    });
  });

  test('handles watermark toggle', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    const watermarkSwitch = screen.getByRole('checkbox', { name: /use as watermark/i });
    await user.click(watermarkSwitch);

    // Apply settings
    const applyButton = screen.getByText('Apply Settings');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockLogoData,
        logo_settings: {
          ...mockLogoData.logo_settings,
          useWatermark: true
        }
      });
    });
  });

  test('shows watermark settings when watermark is enabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    const watermarkSwitch = screen.getByRole('checkbox', { name: /use as watermark/i });
    await user.click(watermarkSwitch);

    expect(screen.getByText('Watermark Opacity')).toBeInTheDocument();
    expect(screen.getByText('Watermark Scale')).toBeInTheDocument();
  });

  test('handles watermark opacity change', async () => {
    const user = userEvent.setup();
    
    const propsWithWatermark = {
      ...mockProps,
      logoData: {
        ...mockLogoData,
        logo_settings: {
          ...mockLogoData.logo_settings,
          useWatermark: true
        }
      }
    };

    render(
      <TestWrapper>
        <LogoManager {...propsWithWatermark} />
      </TestWrapper>
    );

    const watermarkOpacitySlider = screen.getByRole('slider', { name: /watermark opacity/i });
    
    // Change the slider value
    fireEvent.change(watermarkOpacitySlider, { target: { value: '30' } });
    
    // Apply settings
    const applyButton = screen.getByText('Apply Settings');
    await user.click(applyButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...propsWithWatermark.logoData,
        logo_settings: {
          ...propsWithWatermark.logoData.logo_settings,
          watermarkOpacity: 30
        }
      });
    });
  });

  test('handles logo upload', async () => {
    const user = userEvent.setup();
    
    const propsWithoutLogo = {
      ...mockProps,
      logoData: {
        logo_url: '',
        logo_settings: mockLogoData.logo_settings
      }
    };

    render(
      <TestWrapper>
        <LogoManager {...propsWithoutLogo} />
      </TestWrapper>
    );

    const file = new File(['logo content'], 'logo.png', { type: 'image/png' });
    const fileInput = screen.getByRole('button', { name: /upload logo/i }).querySelector('input[type="file"]');
    
    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...propsWithoutLogo.logoData,
        logo_url: 'mock-logo-url',
        logo_settings: expect.any(Object)
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Logo uploaded successfully');
    });
  });

  test('handles logo removal', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    const removeButton = screen.getByText('Remove');
    await user.click(removeButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockLogoData,
        logo_url: ''
      });
      expect(mockShowSuccessNotification).toHaveBeenCalledWith('Logo removed');
    });
  });

  test('disables apply button when no logo is present', () => {
    const propsWithoutLogo = {
      ...mockProps,
      logoData: {
        logo_url: '',
        logo_settings: mockLogoData.logo_settings
      }
    };

    render(
      <TestWrapper>
        <LogoManager {...propsWithoutLogo} />
      </TestWrapper>
    );

    const applyButton = screen.getByText('Apply Settings');
    expect(applyButton).toBeDisabled();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <LogoManager {...mockProps} />
      </TestWrapper>
    );

    // Check for proper labels and roles
    expect(screen.getByRole('slider', { name: /size/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /opacity/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /rotation/i })).toBeInTheDocument();
    expect(screen.getByRole('combobox')).toBeInTheDocument();
    expect(screen.getByRole('checkbox', { name: /use as watermark/i })).toBeInTheDocument();
  });

  test('renders with default props when no logoData provided', () => {
    render(
      <TestWrapper>
        <LogoManager onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Logo Management')).toBeInTheDocument();
    expect(screen.getByText('Upload Logo')).toBeInTheDocument();
  });

  test('handles file upload error gracefully', async () => {
    const user = userEvent.setup();
    
    // Mock console.error to avoid test output noise
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    const propsWithoutLogo = {
      ...mockProps,
      logoData: {
        logo_url: '',
        logo_settings: mockLogoData.logo_settings
      }
    };

    render(
      <TestWrapper>
        <LogoManager {...propsWithoutLogo} />
      </TestWrapper>
    );

    // Simulate upload without file
    const fileInput = screen.getByRole('button', { name: /upload logo/i }).querySelector('input[type="file"]');
    fireEvent.change(fileInput, { target: { files: [] } });

    // Should not call onChange or show success notification
    expect(mockProps.onChange).not.toHaveBeenCalled();
    expect(mockShowSuccessNotification).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });
});

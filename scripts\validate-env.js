#!/usr/bin/env node
// @since 2024-1-1 to 2025-25-7

/**
 * ACE Social Platform - Environment Configuration Validator
 * Version: 2.0.0
 * 
 * Validates environment configuration files for security, completeness,
 * and production readiness.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Script metadata
const SCRIPT_VERSION = '2.0.0';
const SCRIPT_NAME = 'ACE Social Environment Validator';

// Colors for console output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    purple: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
};

// Required environment variables by category
const REQUIRED_VARS = {
    application: [
        'ENVIRONMENT',
        'DEBUG',
        'SECRET_KEY',
        'PROJECT_NAME'
    ],
    database: [
        'MONGODB_URL',
        'MONGO_ROOT_USERNAME',
        'MONGO_ROOT_PASSWORD',
        'REDIS_URL',
        'REDIS_PASSWORD'
    ],
    security: [
        'JWT_SECRET_KEY',
        'JWT_ALGORITHM'
    ],
    external_apis: [
        'OPENAI_API_KEY',
        'SENDGRID_API_KEY',
        'LEMON_SQUEEZY_API_KEY'
    ],
    frontend: [
        'FRONTEND_URL',
        'VITE_API_URL',
        'CORS_ORIGINS'
    ]
};

// Security patterns to detect
const SECURITY_PATTERNS = {
    weak_secrets: [
        /password123/i,
        /admin123/i,
        /secret123/i,
        /test123/i,
        /dev.*123/i,
        /change.*this/i,
        /your.*here/i,
        /placeholder/i,
        /example/i,
        /demo/i
    ],
    potential_secrets: [
        /[a-zA-Z0-9]{32,}/,  // Long alphanumeric strings
        /sk-[a-zA-Z0-9]{48,}/, // OpenAI API keys
        /SG\.[a-zA-Z0-9_-]{22}\.[a-zA-Z0-9_-]{43}/, // SendGrid API keys
    ],
    urls: [
        /https?:\/\/[^\s]+/g
    ]
};

// Validation results
let validationResults = {
    passed: 0,
    failed: 0,
    warnings: 0,
    details: []
};

function log(message, color = 'reset') {
    const timestamp = new Date().toISOString();
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logHeader(message) {
    console.log(`${colors.white}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.white}${message}${colors.reset}`);
    console.log(`${colors.white}${'='.repeat(60)}${colors.reset}`);
}

function logSection(message) {
    console.log(`${colors.blue}${'─'.repeat(40)}${colors.reset}`);
    console.log(`${colors.blue}${message}${colors.reset}`);
    console.log(`${colors.blue}${'─'.repeat(40)}${colors.reset}`);
}

function addResult(category, name, passed, message = '', severity = 'error') {
    const result = { category, name, passed, message, severity };
    validationResults.details.push(result);
    
    if (passed) {
        validationResults.passed++;
        log(`✅ ${name}`, 'green');
    } else {
        if (severity === 'warning') {
            validationResults.warnings++;
            log(`⚠️  ${name}: ${message}`, 'yellow');
        } else {
            validationResults.failed++;
            log(`❌ ${name}: ${message}`, 'red');
        }
    }
    
    return passed;
}

function parseEnvFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const env = {};
        
        content.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const [key, ...valueParts] = line.split('=');
                if (key && valueParts.length > 0) {
                    env[key.trim()] = valueParts.join('=').trim();
                }
            }
        });
        
        return env;
    } catch (error) {
        return null;
    }
}

function validateRequiredVariables(env, environment) {
    logSection(`Validating Required Variables for ${environment}`);
    
    let allPresent = true;
    
    Object.entries(REQUIRED_VARS).forEach(([category, variables]) => {
        variables.forEach(variable => {
            const isPresent = env.hasOwnProperty(variable) && env[variable] && env[variable].trim() !== '';
            if (!addResult('required-vars', `${category}: ${variable}`, isPresent, 
                isPresent ? '' : 'Required variable is missing or empty')) {
                allPresent = false;
            }
        });
    });
    
    return allPresent;
}

function validateSecuritySettings(env, environment) {
    logSection(`Validating Security Settings for ${environment}`);
    
    let securityGood = true;
    
    // Check for weak or default passwords
    Object.entries(env).forEach(([key, value]) => {
        if (key.toLowerCase().includes('password') || key.toLowerCase().includes('secret') || key.toLowerCase().includes('key')) {
            // Check for weak patterns
            const isWeak = SECURITY_PATTERNS.weak_secrets.some(pattern => pattern.test(value));
            if (!addResult('security', `${key} strength`, !isWeak, 
                isWeak ? 'Contains weak or default value' : '', 'warning')) {
                securityGood = false;
            }
            
            // Check minimum length for secrets
            if (key.includes('SECRET') || key.includes('KEY')) {
                const isLongEnough = value.length >= 32;
                addResult('security', `${key} length`, isLongEnough,
                    isLongEnough ? '' : 'Secret should be at least 32 characters', 'warning');
            }
        }
    });
    
    // Environment-specific security checks
    if (environment === 'production') {
        // Production must not have DEBUG=true
        const debugOff = env.DEBUG !== 'true';
        addResult('security', 'Debug mode disabled', debugOff,
            debugOff ? '' : 'DEBUG should be false in production');
        
        // Production should use HTTPS
        const usesHttps = env.FRONTEND_URL && env.FRONTEND_URL.startsWith('https://');
        addResult('security', 'HTTPS enabled', usesHttps,
            usesHttps ? '' : 'Production should use HTTPS');
        
        // Check for development URLs in production
        const hasDevUrls = Object.values(env).some(value => 
            typeof value === 'string' && (value.includes('localhost') || value.includes('127.0.0.1')));
        addResult('security', 'No localhost URLs', !hasDevUrls,
            hasDevUrls ? 'Production should not contain localhost URLs' : '', 'warning');
    }
    
    return securityGood;
}

function validateDatabaseConfiguration(env, environment) {
    logSection(`Validating Database Configuration for ${environment}`);
    
    let dbValid = true;
    
    // Validate MongoDB URL format
    if (env.MONGODB_URL) {
        const isValidMongoUrl = /^mongodb:\/\//.test(env.MONGODB_URL);
        addResult('database', 'MongoDB URL format', isValidMongoUrl,
            isValidMongoUrl ? '' : 'Invalid MongoDB URL format');
        
        // Check for authentication in MongoDB URL
        const hasAuth = env.MONGODB_URL.includes('@');
        addResult('database', 'MongoDB authentication', hasAuth,
            hasAuth ? '' : 'MongoDB URL should include authentication', 'warning');
    }
    
    // Validate Redis URL format
    if (env.REDIS_URL) {
        const isValidRedisUrl = /^redis:\/\//.test(env.REDIS_URL);
        addResult('database', 'Redis URL format', isValidRedisUrl,
            isValidRedisUrl ? '' : 'Invalid Redis URL format');
    }
    
    // Check database names for environment
    if (env.MONGO_DATABASE) {
        const hasEnvSuffix = env.MONGO_DATABASE.includes(environment);
        addResult('database', 'Database environment naming', hasEnvSuffix,
            hasEnvSuffix ? '' : `Database name should include environment (${environment})`, 'warning');
    }
    
    return dbValid;
}

function validateExternalServices(env, environment) {
    logSection(`Validating External Services for ${environment}`);
    
    let servicesValid = true;
    
    // Validate API key formats
    const apiKeyChecks = [
        { key: 'OPENAI_API_KEY', pattern: /^sk-[a-zA-Z0-9]{48,}$/, name: 'OpenAI API Key' },
        { key: 'SENDGRID_API_KEY', pattern: /^SG\.[a-zA-Z0-9_-]{22}\.[a-zA-Z0-9_-]{43}$/, name: 'SendGrid API Key' }
    ];
    
    apiKeyChecks.forEach(({ key, pattern, name }) => {
        if (env[key]) {
            const isValidFormat = pattern.test(env[key]);
            addResult('external-services', `${name} format`, isValidFormat,
                isValidFormat ? '' : 'Invalid API key format', 'warning');
        }
    });
    
    // Check for placeholder values
    const placeholderPatterns = [
        /your.*api.*key.*here/i,
        /change.*this/i,
        /placeholder/i,
        /example/i
    ];
    
    Object.entries(env).forEach(([key, value]) => {
        if (key.includes('API_KEY') || key.includes('SECRET')) {
            const hasPlaceholder = placeholderPatterns.some(pattern => pattern.test(value));
            if (hasPlaceholder) {
                addResult('external-services', `${key} configuration`, false,
                    'Contains placeholder value - update with real credentials');
                servicesValid = false;
            }
        }
    });
    
    return servicesValid;
}

function validateEnvironmentFile(filePath) {
    const fileName = path.basename(filePath);
    const environment = fileName.includes('development') ? 'development' :
                      fileName.includes('staging') ? 'staging' :
                      fileName.includes('production') ? 'production' : 'unknown';
    
    logHeader(`Validating ${fileName} (${environment} environment)`);
    
    if (!fs.existsSync(filePath)) {
        addResult('file-check', `${fileName} exists`, false, 'Environment file not found');
        return false;
    }
    
    const env = parseEnvFile(filePath);
    if (!env) {
        addResult('file-check', `${fileName} parsing`, false, 'Failed to parse environment file');
        return false;
    }
    
    addResult('file-check', `${fileName} parsing`, true, `Parsed ${Object.keys(env).length} variables`);
    
    // Run all validations
    const results = [
        validateRequiredVariables(env, environment),
        validateSecuritySettings(env, environment),
        validateDatabaseConfiguration(env, environment),
        validateExternalServices(env, environment)
    ];
    
    return results.every(result => result);
}

function generateValidationReport() {
    const report = {
        timestamp: new Date().toISOString(),
        version: SCRIPT_VERSION,
        summary: {
            passed: validationResults.passed,
            failed: validationResults.failed,
            warnings: validationResults.warnings,
            total: validationResults.passed + validationResults.failed + validationResults.warnings
        },
        categories: {},
        recommendations: []
    };
    
    // Group results by category
    validationResults.details.forEach(result => {
        if (!report.categories[result.category]) {
            report.categories[result.category] = {
                passed: 0,
                failed: 0,
                warnings: 0,
                details: []
            };
        }
        
        if (result.passed) {
            report.categories[result.category].passed++;
        } else if (result.severity === 'warning') {
            report.categories[result.category].warnings++;
        } else {
            report.categories[result.category].failed++;
        }
        
        report.categories[result.category].details.push(result);
    });
    
    // Generate recommendations
    if (report.categories['security']?.failed > 0 || report.categories['security']?.warnings > 0) {
        report.recommendations.push('Update weak passwords and secrets with secure random values');
        report.recommendations.push('Use tools like openssl rand -hex 32 to generate secure keys');
    }
    
    if (report.categories['external-services']?.failed > 0) {
        report.recommendations.push('Replace all placeholder API keys with real credentials');
        report.recommendations.push('Test all external service integrations');
    }
    
    if (report.categories['required-vars']?.failed > 0) {
        report.recommendations.push('Add all missing required environment variables');
    }
    
    // Save report
    const reportPath = `logs/build/env_validation_report_${Date.now()}.json`;
    if (!fs.existsSync('logs/build')) {
        fs.mkdirSync('logs/build', { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    log(`📊 Environment validation report saved to: ${reportPath}`, 'blue');
    
    return report;
}

function main() {
    logHeader(`${SCRIPT_NAME} v${SCRIPT_VERSION}`);
    
    const envFiles = [
        '.env.development',
        '.env.staging', 
        '.env.production',
        '.env'
    ];
    
    let allValid = true;
    
    envFiles.forEach(file => {
        if (fs.existsSync(file)) {
            const isValid = validateEnvironmentFile(file);
            if (!isValid) allValid = false;
        } else {
            log(`⚠️  Environment file not found: ${file}`, 'yellow');
        }
    });
    
    // Generate report
    const report = generateValidationReport();
    
    // Display summary
    logHeader('Validation Summary');
    
    const totalChecks = validationResults.passed + validationResults.failed + validationResults.warnings;
    const successRate = totalChecks > 0 ? Math.round((validationResults.passed / totalChecks) * 100) : 0;
    
    log(`📊 Total Checks: ${totalChecks}`, 'blue');
    log(`✅ Passed: ${validationResults.passed}`, 'green');
    log(`❌ Failed: ${validationResults.failed}`, 'red');
    log(`⚠️  Warnings: ${validationResults.warnings}`, 'yellow');
    log(`📈 Success Rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');
    
    // Recommendations
    if (report.recommendations.length > 0) {
        logSection('Recommendations');
        report.recommendations.forEach((rec, index) => {
            log(`${index + 1}. ${rec}`, 'yellow');
        });
    }
    
    // Final verdict
    const isProductionReady = validationResults.failed === 0 && validationResults.warnings <= 2;
    
    if (isProductionReady) {
        logHeader('🎉 Environment Configuration is Production Ready!');
    } else {
        logHeader('⚠️  Environment Configuration Needs Attention');
        log('Please address the issues above before deploying to production', 'yellow');
    }
    
    return allValid;
}

// CLI execution
if (require.main === module) {
    try {
        const success = main();
        process.exit(success ? 0 : 1);
    } catch (error) {
        log(`❌ Validation failed: ${error.message}`, 'red');
        process.exit(1);
    }
}

module.exports = { main, validateEnvironmentFile };

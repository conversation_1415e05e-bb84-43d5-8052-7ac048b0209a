/**
 * Enhanced ACE Social API Configuration Form - Enterprise-grade external API configuration component
 * Features: Comprehensive API configuration management with advanced form validation, connection
 * testing, and authentication handling for ACE Social external API integrations, detailed API
 * configuration dashboard with endpoint management and authentication methods, advanced form
 * features with real-time validation and connection testing, ACE Social's external API system
 * integration with seamless configuration lifecycle management, form interaction features
 * including step-by-step configuration wizard and real-time connection validation, form state
 * management with configuration versioning and auto-save functionality, and real-time
 * configuration monitoring with live connection status and automatic configuration optimization
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Typography,
  Box,
  Chip,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Switch,
  FormControlLabel,
  Divider,
  Stack,
  Paper,
  Badge,
  IconButton,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  LinearProgress,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide,
  Collapse
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Settings as SettingsIcon,
  MonitorHeart as HealthIcon,
  Close as CloseIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Test as TestIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Analytics as AnalyticsIcon,
  Speed as SpeedIcon,
  Verified as VerifiedIcon,
  Lock as LockIcon,
  VpnKey as KeyIcon,
  Cloud as CloudIcon,
  Storage as StorageIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';

import { externalApiService } from '../../services/externalApiService';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// API configuration constants
const FORM_MODES = {
  CREATE: 'create',
  EDIT: 'edit',
  DUPLICATE: 'duplicate'
};

const AUTH_TYPES = {
  API_KEY: 'api_key',
  BEARER_TOKEN: 'bearer_token',
  OAUTH2: 'oauth2',
  BASIC_AUTH: 'basic_auth',
  CUSTOM: 'custom'
};

const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production'
};

const CONNECTION_STATUSES = {
  IDLE: 'idle',
  TESTING: 'testing',
  SUCCESS: 'success',
  FAILED: 'failed'
};

// Form analytics events
const FORM_ANALYTICS_EVENTS = {
  FORM_OPENED: 'api_config_form_opened',
  FIELD_CHANGED: 'api_config_field_changed',
  CONNECTION_TESTED: 'api_connection_tested',
  FORM_SUBMITTED: 'api_config_form_submitted',
  VALIDATION_FAILED: 'api_config_validation_failed',
  AUTO_SAVE_TRIGGERED: 'api_config_auto_save_triggered'
};

/**
 * Enhanced API Configuration Form - Comprehensive API configuration with advanced features
 * Implements detailed API configuration management and enterprise-grade form capabilities
 */

const EnhancedApiConfigurationForm = memo(forwardRef(({
  open,
  onClose,
  onSave,
  config = null,
  mode = FORM_MODES.CREATE,
  className,
  enableAdvancedFeatures = true,
  enableConnectionTesting = true,
  enableAutoSave = true,
  enableAccessibility = true,
  enableAnalytics = true,
  enableValidation = true,
  enableEncryption = true,
  autoSaveInterval = 30000, // 30 seconds
  onConfigChange,
  onConnectionTest,
  onValidation,
  onAnalyticsTrack,
  onAutoSave,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const dialogRef = useRef(null);
  const formRef = useRef(null);
  const autoSaveTimeoutRef = useRef(null);

  // Core state management
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: '',
    description: '',
    base_url: '',
    version: '',
    environment: ENVIRONMENTS.PRODUCTION,
    auth_type: AUTH_TYPES.API_KEY,
    credentials: {
      api_key: '',
      api_secret: '',
      access_token: '',
      refresh_token: '',
      client_id: '',
      client_secret: '',
      custom_headers: {},
      additional_config: {}
    },
    status: 'active',
    rate_limit: {
      requests_per_period: 1000,
      period: 'hour',
      burst_limit: null
    },
    health_check: {
      enabled: true,
      endpoint: '',
      method: 'GET',
      expected_status_codes: [200, 201, 204],
      timeout_seconds: 10,
      interval_minutes: 5,
      failure_threshold: 3,
      success_threshold: 2
    },
    tags: []
  });
  const [tagInput, setTagInput] = useState('');

  // Enhanced state management
  const [connectionStatus, setConnectionStatus] = useState(CONNECTION_STATUSES.IDLE);
  const [connectionResult, setConnectionResult] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [isDirty, setIsDirty] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [showCredentials, setShowCredentials] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [formAnalytics, setFormAnalytics] = useState({
    startTime: null,
    fieldChanges: 0,
    connectionTests: 0,
    saveAttempts: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    saveConfiguration: () => handleSubmit(),
    resetForm: () => handleReset(),
    validateConfiguration: () => validateForm(),
    testConnection: () => handleConnectionTest(),
    focusDialog: () => dialogRef.current?.focus(),

    // Navigation methods
    nextStep: () => setCurrentStep(prev => Math.min(prev + 1, 3)),
    previousStep: () => setCurrentStep(prev => Math.max(prev - 1, 0)),
    goToStep: (step) => setCurrentStep(step),

    // Data methods
    getFormData: () => formData,
    setFormData: (data) => setFormData(data),
    getValidationErrors: () => validationErrors,
    getConnectionStatus: () => connectionStatus,

    // State methods
    isDirty: () => isDirty,
    isValid: () => Object.keys(validationErrors).length === 0,
    getLastSaved: () => lastSaved,

    // Credential methods
    toggleCredentialVisibility: () => setShowCredentials(!showCredentials),
    encryptCredentials: () => handleCredentialEncryption(),

    // Analytics methods
    getFormAnalytics: () => formAnalytics,
    resetAnalytics: () => setFormAnalytics({
      startTime: null,
      fieldChanges: 0,
      connectionTests: 0,
      saveAttempts: 0
    }),

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    duplicateConfiguration: () => handleDuplicate(),
    exportConfiguration: () => exportConfigurationData(),
    importConfiguration: (data) => importConfigurationData(data)
  }), [
    formData,
    validationErrors,
    connectionStatus,
    isDirty,
    lastSaved,
    showCredentials,
    formAnalytics,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (open && enableAnalytics) {
      const startTime = new Date().toISOString();
      setFormAnalytics(prev => ({
        ...prev,
        startTime
      }));

      if (onAnalyticsTrack) {
        onAnalyticsTrack(FORM_ANALYTICS_EVENTS.FORM_OPENED, {
          mode,
          timestamp: startTime,
          configId: config?.id
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`API configuration form opened in ${mode} mode`);
      }
    }
  }, [open, enableAnalytics, mode, config?.id, onAnalyticsTrack, enableAccessibility, announceToScreenReader]);

  // Auto-save effect
  useEffect(() => {
    if (enableAutoSave && isDirty && autoSaveInterval > 0) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        handleAutoSave();
      }, autoSaveInterval);

      return () => {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current);
        }
      };
    }
  }, [enableAutoSave, isDirty, autoSaveInterval, formData]);

  // Initialize form data when config changes
  useEffect(() => {
    if (config && mode === FORM_MODES.EDIT) {
      const initialData = {
        name: config.name || '',
        provider: config.provider || '',
        description: config.description || '',
        base_url: config.base_url || '',
        version: config.version || '',
        environment: config.environment || ENVIRONMENTS.PRODUCTION,
        auth_type: config.auth_type || AUTH_TYPES.API_KEY,
        credentials: {
          api_key: '',
          api_secret: '',
          access_token: '',
          refresh_token: '',
          client_id: '',
          client_secret: '',
          custom_headers: {},
          additional_config: {}
        },
        status: config.status || 'active',
        rate_limit: config.rate_limit || {
          requests_per_period: 1000,
          period: 'hour',
          burst_limit: null
        },
        health_check: config.health_check || {
          enabled: true,
          endpoint: '',
          method: 'GET',
          expected_status_codes: [200, 201, 204],
          timeout_seconds: 10,
          interval_minutes: 5,
          failure_threshold: 3,
          success_threshold: 2
        },
        tags: config.tags || []
      };
      setFormData(initialData);
      setIsDirty(false);
    } else if (mode === FORM_MODES.CREATE) {
      // Reset form for create mode
      const defaultData = {
        name: '',
        provider: '',
        description: '',
        base_url: '',
        version: '',
        environment: ENVIRONMENTS.PRODUCTION,
        auth_type: AUTH_TYPES.API_KEY,
        credentials: {
          api_key: '',
          api_secret: '',
          access_token: '',
          refresh_token: '',
          client_id: '',
          client_secret: '',
          custom_headers: {},
          additional_config: {}
        },
        status: 'active',
        rate_limit: {
          requests_per_period: 1000,
          period: 'hour',
          burst_limit: null
        },
        health_check: {
          enabled: true,
          endpoint: '',
          method: 'GET',
          expected_status_codes: [200, 201, 204],
          timeout_seconds: 10,
          interval_minutes: 5,
          failure_threshold: 3,
          success_threshold: 2
        },
        tags: []
      };
      setFormData(defaultData);
      setIsDirty(false);
    }
    setValidationErrors({});
    setConnectionStatus(CONNECTION_STATUSES.IDLE);
    setLastSaved(null);
  }, [config, mode]);

  // Enhanced handler functions
  const handleInputChange = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);

    setFormAnalytics(prev => ({
      ...prev,
      fieldChanges: prev.fieldChanges + 1
    }));

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: null }));
    }

    if (onConfigChange) {
      onConfigChange(field, value);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(FORM_ANALYTICS_EVENTS.FIELD_CHANGED, {
        field,
        timestamp: new Date().toISOString()
      });
    }
  }, [validationErrors, onConfigChange, enableAnalytics, onAnalyticsTrack]);

  const handleNestedInputChange = useCallback((section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    setIsDirty(true);

    setFormAnalytics(prev => ({
      ...prev,
      fieldChanges: prev.fieldChanges + 1
    }));

    // Clear validation error when user starts typing
    const errorKey = `${section}.${field}`;
    if (validationErrors[errorKey]) {
      setValidationErrors(prev => ({ ...prev, [errorKey]: null }));
    }

    if (onConfigChange) {
      onConfigChange(`${section}.${field}`, value);
    }
  }, [validationErrors, onConfigChange]);

  const handleAddTag = useCallback(() => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      handleInputChange('tags', [...formData.tags, tagInput.trim()]);
      setTagInput('');

      if (enableAccessibility) {
        announceToScreenReader(`Tag ${tagInput.trim()} added`);
      }
    }
  }, [tagInput, formData.tags, handleInputChange, enableAccessibility, announceToScreenReader]);

  const handleRemoveTag = useCallback((tagToRemove) => {
    handleInputChange('tags', formData.tags.filter(tag => tag !== tagToRemove));

    if (enableAccessibility) {
      announceToScreenReader(`Tag ${tagToRemove} removed`);
    }
  }, [formData.tags, handleInputChange, enableAccessibility, announceToScreenReader]);

  // Enhanced validation function
  const validateForm = useCallback(() => {
    const errors = {};

    // Required field validation
    if (!formData.name.trim()) {
      errors.name = 'API name is required';
    }

    if (!formData.provider.trim()) {
      errors.provider = 'Provider is required';
    }

    if (!formData.base_url.trim()) {
      errors.base_url = 'Base URL is required';
    } else {
      // URL validation
      try {
        new URL(formData.base_url);
      } catch {
        errors.base_url = 'Please enter a valid URL';
      }
    }

    // Authentication validation
    if (formData.auth_type === AUTH_TYPES.API_KEY && !formData.credentials.api_key) {
      errors['credentials.api_key'] = 'API key is required for this authentication type';
    }

    if (formData.auth_type === AUTH_TYPES.OAUTH2) {
      if (!formData.credentials.client_id) {
        errors['credentials.client_id'] = 'Client ID is required for OAuth2';
      }
      if (!formData.credentials.client_secret) {
        errors['credentials.client_secret'] = 'Client Secret is required for OAuth2';
      }
    }

    if (formData.auth_type === AUTH_TYPES.BASIC_AUTH) {
      if (!formData.credentials.client_id) {
        errors['credentials.client_id'] = 'Username is required for Basic Auth';
      }
      if (!formData.credentials.client_secret) {
        errors['credentials.client_secret'] = 'Password is required for Basic Auth';
      }
    }

    // Rate limit validation
    if (formData.rate_limit.requests_per_period <= 0) {
      errors['rate_limit.requests_per_period'] = 'Requests per period must be greater than 0';
    }

    // Health check validation
    if (formData.health_check.enabled && !formData.health_check.endpoint) {
      errors['health_check.endpoint'] = 'Health check endpoint is required when enabled';
    }

    setValidationErrors(errors);

    const isValid = Object.keys(errors).length === 0;

    if (onValidation) {
      onValidation({
        isValid,
        errors
      });
    }

    if (!isValid && enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(FORM_ANALYTICS_EVENTS.VALIDATION_FAILED, {
        errors: Object.keys(errors),
        timestamp: new Date().toISOString()
      });
    }

    return isValid;
  }, [formData, onValidation, enableAnalytics, onAnalyticsTrack]);

  // Enhanced connection testing
  const handleConnectionTest = useCallback(async () => {
    if (!enableConnectionTesting) return;

    setConnectionStatus(CONNECTION_STATUSES.TESTING);
    setConnectionResult(null);

    setFormAnalytics(prev => ({
      ...prev,
      connectionTests: prev.connectionTests + 1
    }));

    try {
      const testResult = await externalApiService.testConnection({
        base_url: formData.base_url,
        auth_type: formData.auth_type,
        credentials: formData.credentials,
        health_check: formData.health_check
      });

      setConnectionStatus(CONNECTION_STATUSES.SUCCESS);
      setConnectionResult(testResult);

      if (onConnectionTest) {
        onConnectionTest(testResult);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(FORM_ANALYTICS_EVENTS.CONNECTION_TESTED, {
          success: true,
          responseTime: testResult.responseTime,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader('Connection test successful');
      }
    } catch (error) {
      setConnectionStatus(CONNECTION_STATUSES.FAILED);
      setConnectionResult({ error: error.message });

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(FORM_ANALYTICS_EVENTS.CONNECTION_TESTED, {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Connection test failed: ${error.message}`);
      }
    }
  }, [
    enableConnectionTesting,
    formData.base_url,
    formData.auth_type,
    formData.credentials,
    formData.health_check,
    onConnectionTest,
    enableAnalytics,
    enableAccessibility,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  // Enhanced auto-save function
  const handleAutoSave = useCallback(async () => {
    if (!isDirty || !formData.name) return;

    try {
      // Save to localStorage as backup
      const autoSaveData = {
        formData,
        timestamp: new Date().toISOString(),
        mode
      };
      localStorage.setItem('api_config_autosave', JSON.stringify(autoSaveData));

      setLastSaved(new Date().toISOString());
      setIsDirty(false);

      if (onAutoSave) {
        onAutoSave(autoSaveData);
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(FORM_ANALYTICS_EVENTS.AUTO_SAVE_TRIGGERED, {
          timestamp: new Date().toISOString(),
          mode
        });
      }

      if (enableAccessibility) {
        announceToScreenReader('Configuration auto-saved successfully');
      }
    } catch (error) {
      console.warn('Auto-save failed:', error);
    }
  }, [isDirty, formData, mode, onAutoSave, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced utility functions
  const handleReset = useCallback(() => {
    const defaultData = {
      name: '',
      provider: '',
      description: '',
      base_url: '',
      version: '',
      environment: ENVIRONMENTS.PRODUCTION,
      auth_type: AUTH_TYPES.API_KEY,
      credentials: {
        api_key: '',
        api_secret: '',
        access_token: '',
        refresh_token: '',
        client_id: '',
        client_secret: '',
        custom_headers: {},
        additional_config: {}
      },
      status: 'active',
      rate_limit: {
        requests_per_period: 1000,
        period: 'hour',
        burst_limit: null
      },
      health_check: {
        enabled: true,
        endpoint: '',
        method: 'GET',
        expected_status_codes: [200, 201, 204],
        timeout_seconds: 10,
        interval_minutes: 5,
        failure_threshold: 3,
        success_threshold: 2
      },
      tags: []
    };

    setFormData(defaultData);
    setValidationErrors({});
    setConnectionStatus(CONNECTION_STATUSES.IDLE);
    setIsDirty(false);
    setLastSaved(null);
    setTagInput('');

    if (enableAccessibility) {
      announceToScreenReader('Configuration form reset to default values');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleCredentialEncryption = useCallback(() => {
    if (!enableEncryption) return;

    // Implement credential encryption logic here
    // This would typically involve encrypting sensitive fields
    console.log('Encrypting credentials...');

    if (enableAccessibility) {
      announceToScreenReader('Credentials encrypted successfully');
    }
  }, [enableEncryption, enableAccessibility, announceToScreenReader]);

  const handleDuplicate = useCallback(() => {
    const duplicatedData = {
      ...formData,
      name: `${formData.name} (Copy)`,
      id: undefined // Remove ID for new configuration
    };

    setFormData(duplicatedData);
    setIsDirty(true);

    if (enableAccessibility) {
      announceToScreenReader('Configuration duplicated. Please review and modify as needed.');
    }
  }, [formData, enableAccessibility, announceToScreenReader]);

  const exportConfigurationData = useCallback(() => {
    const exportData = {
      ...formData,
      credentials: enableEncryption ? '***ENCRYPTED***' : formData.credentials,
      exportedAt: new Date().toISOString()
    };

    return exportData;
  }, [formData, enableEncryption]);

  const importConfigurationData = useCallback((data) => {
    if (data && typeof data === 'object') {
      setFormData(prev => ({ ...prev, ...data }));
      setIsDirty(true);

      if (enableAccessibility) {
        announceToScreenReader('Configuration data imported successfully');
      }
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleSubmit = useCallback(async () => {
    setFormAnalytics(prev => ({
      ...prev,
      saveAttempts: prev.saveAttempts + 1
    }));

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Clean up credentials - only include non-empty values
      const cleanCredentials = {};
      Object.entries(formData.credentials).forEach(([key, value]) => {
        if (value && value !== '') {
          cleanCredentials[key] = value;
        }
      });

      const submitData = {
        ...formData,
        credentials: cleanCredentials
      };

      let result;
      if (mode === FORM_MODES.CREATE) {
        result = await externalApiService.createApiConfiguration(submitData);
      } else {
        result = await externalApiService.updateApiConfiguration(config.id, submitData);
      }

      setIsDirty(false);
      setLastSaved(new Date().toISOString());

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(FORM_ANALYTICS_EVENTS.FORM_SUBMITTED, {
          mode,
          configId: result.id,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader('Configuration saved successfully');
      }

      // Clear auto-save data
      localStorage.removeItem('api_config_autosave');

      onSave(result);
      onClose();
    } catch (err) {
      setError(err.message);

      if (enableAccessibility) {
        announceToScreenReader(`Failed to save configuration: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  }, [
    validateForm,
    formData,
    mode,
    config?.id,
    enableAnalytics,
    enableAccessibility,
    onAnalyticsTrack,
    announceToScreenReader,
    onSave,
    onClose
  ]);

  const renderCredentialsSection = () => {
    const authType = formData.auth_type;
    
    return (
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <SecurityIcon color="primary" />
            <Typography variant="h6">Authentication & Credentials</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Authentication Type</InputLabel>
                <Select
                  value={authType}
                  onChange={(e) => handleInputChange('auth_type', e.target.value)}
                  label="Authentication Type"
                >
                  {externalApiService.getAuthenticationTypes().map(type => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {(authType === 'api_key' || authType === 'bearer_token') && (
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="API Key"
                  type="password"
                  value={formData.credentials.api_key}
                  onChange={(e) => handleNestedInputChange('credentials', 'api_key', e.target.value)}
                  placeholder="Enter your API key"
                />
              </Grid>
            )}

            {authType === 'oauth2' && (
              <>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Client ID"
                    value={formData.credentials.client_id}
                    onChange={(e) => handleNestedInputChange('credentials', 'client_id', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Client Secret"
                    type="password"
                    value={formData.credentials.client_secret}
                    onChange={(e) => handleNestedInputChange('credentials', 'client_secret', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Access Token"
                    type="password"
                    value={formData.credentials.access_token}
                    onChange={(e) => handleNestedInputChange('credentials', 'access_token', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Refresh Token"
                    type="password"
                    value={formData.credentials.refresh_token}
                    onChange={(e) => handleNestedInputChange('credentials', 'refresh_token', e.target.value)}
                  />
                </Grid>
              </>
            )}

            {authType === 'basic_auth' && (
              <>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={formData.credentials.client_id}
                    onChange={(e) => handleNestedInputChange('credentials', 'client_id', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Password"
                    type="password"
                    value={formData.credentials.client_secret}
                    onChange={(e) => handleNestedInputChange('credentials', 'client_secret', e.target.value)}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          background: `linear-gradient(135deg, 
            ${theme.palette.background.paper}80 0%, 
            ${theme.palette.background.default}40 100%)`,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${theme.palette.divider}30`,
        }
      }}
    >
      <DialogTitle>
        <Typography variant="h5">
          {mode === 'create' ? 'Add New API Configuration' : 'Edit API Configuration'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="API Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required>
              <InputLabel>Provider</InputLabel>
              <Select
                value={formData.provider}
                onChange={(e) => handleInputChange('provider', e.target.value)}
                label="Provider"
              >
                {externalApiService.getSupportedProviders().map(provider => (
                  <MenuItem key={provider.value} value={provider.value}>
                    {provider.label} ({provider.category})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={2}
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
            />
          </Grid>

          <Grid item xs={12} sm={8}>
            <TextField
              fullWidth
              label="Base URL"
              value={formData.base_url}
              onChange={(e) => handleInputChange('base_url', e.target.value)}
              required
              placeholder="https://api.example.com/v1"
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Version"
              value={formData.version}
              onChange={(e) => handleInputChange('version', e.target.value)}
              placeholder="v1"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Environment</InputLabel>
              <Select
                value={formData.environment}
                onChange={(e) => handleInputChange('environment', e.target.value)}
                label="Environment"
              >
                <MenuItem value="development">Development</MenuItem>
                <MenuItem value="staging">Staging</MenuItem>
                <MenuItem value="production">Production</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                label="Status"
              >
                {externalApiService.getStatusOptions().map(status => (
                  <MenuItem key={status.value} value={status.value}>
                    {status.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Tags */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Tags
            </Typography>
            <Box display="flex" gap={1} flexWrap="wrap" mb={1}>
              {formData.tags.map((tag, index) => (
                <Chip
                  key={index}
                  label={tag}
                  onDelete={() => handleRemoveTag(tag)}
                  size="small"
                />
              ))}
            </Box>
            <Box display="flex" gap={1}>
              <TextField
                size="small"
                label="Add Tag"
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
              />
              <Button onClick={handleAddTag} variant="outlined" size="small">
                Add
              </Button>
            </Box>
          </Grid>

          {/* Credentials Section */}
          <Grid item xs={12}>
            {renderCredentialsSection()}
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Saving...' : (mode === 'create' ? 'Create' : 'Update')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedApiConfigurationForm.propTypes = {
  // Core props
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  config: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    name: PropTypes.string,
    provider: PropTypes.string,
    description: PropTypes.string,
    base_url: PropTypes.string,
    version: PropTypes.string,
    environment: PropTypes.oneOf(Object.values(ENVIRONMENTS)),
    auth_type: PropTypes.oneOf(Object.values(AUTH_TYPES)),
    credentials: PropTypes.object,
    status: PropTypes.string,
    rate_limit: PropTypes.object,
    health_check: PropTypes.object,
    tags: PropTypes.arrayOf(PropTypes.string)
  }),
  mode: PropTypes.oneOf(Object.values(FORM_MODES)),

  // Enhanced props
  className: PropTypes.string,
  enableAdvancedFeatures: PropTypes.bool,
  enableConnectionTesting: PropTypes.bool,
  enableAutoSave: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableValidation: PropTypes.bool,
  enableEncryption: PropTypes.bool,

  // Configuration
  autoSaveInterval: PropTypes.number,

  // Callback props
  onConfigChange: PropTypes.func,
  onConnectionTest: PropTypes.func,
  onValidation: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onAutoSave: PropTypes.func
};

// Default props
EnhancedApiConfigurationForm.defaultProps = {
  config: null,
  mode: FORM_MODES.CREATE,
  className: '',
  enableAdvancedFeatures: true,
  enableConnectionTesting: true,
  enableAutoSave: true,
  enableAccessibility: true,
  enableAnalytics: true,
  enableValidation: true,
  enableEncryption: true,
  autoSaveInterval: 30000,
  onConfigChange: null,
  onConnectionTest: null,
  onValidation: null,
  onAnalyticsTrack: null,
  onAutoSave: null
};

// Display name for debugging
EnhancedApiConfigurationForm.displayName = 'EnhancedApiConfigurationForm';

export default EnhancedApiConfigurationForm;

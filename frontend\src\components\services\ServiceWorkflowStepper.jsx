/**
 * Enhanced Service Workflow Stepper - Enterprise-grade workflow stepper component
 * Features: Comprehensive step management with validation, progress tracking, accessibility compliance,
 * subscription-based feature gating, real-time step validation, completion persistence, and ACE Social
 * platform integration with advanced workflow capabilities and seamless step navigation
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Stepper,
  Step,
  StepLabel,
  StepContent,
  StepButton,
  Box,
  Typography,
  useTheme,
  alpha,
  Paper,
  LinearProgress,
  Chip,
  Tooltip,
  IconButton,
  Collapse,
  Alert,
  Fade,
  CircularProgress,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  Business as BusinessIcon,
  Person as PersonIcon,
  Psychology as StrategyIcon,
  Campaign as CampaignIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as RadioButtonUncheckedIcon,
  Schedule as ScheduleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Save as SaveIcon,
  NavigateNext as NextIcon,
  NavigateBefore as PrevIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Settings as SettingsIcon,
  Lock as LockIcon,
  BookmarkBorder as BookmarkIcon,
  Bookmark as BookmarkFilledIcon
} from '@mui/icons-material';

import { useSubscription } from '../../contexts/SubscriptionContext';
import { useWorkflow } from './WorkflowProvider';
import FeatureGate from '../common/FeatureGate';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Plan-based workflow limitations
const PLAN_LIMITS = {
  1: { // Creator
    maxSteps: 4,
    stepValidation: false,
    autoSave: false,
    stepNavigation: false,
    progressAnalytics: false,
    customSteps: false,
    stepTemplates: false,
    bulkOperations: false
  },
  2: { // Accelerator
    maxSteps: 8,
    stepValidation: true,
    autoSave: true,
    stepNavigation: true,
    progressAnalytics: true,
    customSteps: false,
    stepTemplates: true,
    bulkOperations: false
  },
  3: { // Dominator
    maxSteps: -1, // unlimited
    stepValidation: true,
    autoSave: true,
    stepNavigation: true,
    progressAnalytics: true,
    customSteps: true,
    stepTemplates: true,
    bulkOperations: true
  }
};

// Step status types
const STEP_STATUS = {
  PENDING: 'pending',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  ERROR: 'error',
  SKIPPED: 'skipped',
  LOCKED: 'locked'
};

// Step validation types (for future use)
// const VALIDATION_TYPES = {
//   REQUIRED: 'required',
//   OPTIONAL: 'optional',
//   CONDITIONAL: 'conditional'
// };

/**
 * Enhanced Service Workflow Stepper - Comprehensive workflow management with advanced features
 * Implements plan-based feature restrictions and enterprise-grade workflow capabilities
 */
const ServiceWorkflowStepper = memo(forwardRef(({
  activeStep = 0,
  steps = [],
  children,
  orientation = 'vertical',
  onStepChange,
  onStepValidate,
  onStepSave,
  enableValidation = true,
  enableAutoSave = true,
  enableProgressAnalytics = false,
  customStepIcons = {},
  completionPersistence = true,
  allowStepSkipping = false,
  showStepNumbers = true,
  showProgressBar = true,
  compactMode = false,
  animationDuration = 300
}, ref) => {
  const theme = useTheme();
  const { updateUsage, getPlanTier } = useSubscription();
  const { completedSteps, workflowData, updateWorkflowData } = useWorkflow();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core state management
  const stepperRef = useRef(null);
  const [stepErrors, setStepErrors] = useState({});
  const [stepWarnings, setStepWarnings] = useState({});
  const [stepValidationStatus, setStepValidationStatus] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [expandedSteps, setExpandedSteps] = useState(new Set([activeStep]));
  const [stepBookmarks, setStepBookmarks] = useState(new Set());
  const [stepNotes, setStepNotes] = useState({});
  const [showStepDetails, setShowStepDetails] = useState(false);
  const [stepTimestamps, setStepTimestamps] = useState({});
  const [stepDurations, setStepDurations] = useState({});

  // Plan tier and feature access
  const planTier = getPlanTier();
  const planLimits = PLAN_LIMITS[planTier] || PLAN_LIMITS[1];

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    goToStep: (stepIndex) => handleStepNavigation(stepIndex),
    validateCurrentStep: () => handleStepValidation(activeStep),
    saveCurrentStep: () => handleStepSave(activeStep),
    resetWorkflow: () => handleWorkflowReset(),
    getStepStatus: (stepIndex) => getStepStatus(stepIndex),
    getWorkflowProgress: () => getWorkflowProgress(),
    exportWorkflowData: () => exportWorkflowData(),
    toggleStepBookmark: (stepIndex) => toggleStepBookmark(stepIndex),
    addStepNote: (stepIndex, note) => addStepNote(stepIndex, note),
    getStepAnalytics: () => getStepAnalytics()
  }), [activeStep, handleStepNavigation, handleStepValidation, handleStepSave, handleWorkflowReset, getStepStatus, getWorkflowProgress, exportWorkflowData, toggleStepBookmark, addStepNote, getStepAnalytics]);

  // Enhanced step navigation handler
  const handleStepNavigation = useCallback(async (targetStep) => {
    if (!planLimits.stepNavigation) {
      announceToScreenReader('Step navigation is not available in your current plan. Please upgrade to access this feature.');
      return false;
    }

    if (targetStep < 0 || targetStep >= steps.length) {
      return false;
    }

    // Validate current step before navigation if required
    if (enableValidation && planLimits.stepValidation) {
      const isValid = await handleStepValidation(activeStep);
      if (!isValid && !allowStepSkipping) {
        announceToScreenReader('Please complete the current step before proceeding.');
        return false;
      }
    }

    // Auto-save current step if enabled
    if (enableAutoSave && planLimits.autoSave) {
      await handleStepSave(activeStep);
    }

    // Track step duration
    const now = Date.now();
    if (stepTimestamps[activeStep]) {
      const duration = now - stepTimestamps[activeStep];
      setStepDurations(prev => ({
        ...prev,
        [activeStep]: (prev[activeStep] || 0) + duration
      }));
    }

    // Set timestamp for new step
    setStepTimestamps(prev => ({
      ...prev,
      [targetStep]: now
    }));

    // Update expanded steps
    setExpandedSteps(prev => new Set([...prev, targetStep]));

    // Call external handler
    if (onStepChange) {
      onStepChange(targetStep, activeStep);
    }

    // Update usage tracking
    await updateUsage('workflow_navigation', 1, {
      fromStep: activeStep,
      toStep: targetStep,
      planTier
    });

    announceToScreenReader(`Navigated to step ${targetStep + 1}: ${steps[targetStep]?.label || 'Unknown step'}`);
    return true;
  }, [planLimits, enableValidation, enableAutoSave, allowStepSkipping, activeStep, steps, stepTimestamps, onStepChange, updateUsage, planTier, announceToScreenReader, handleStepValidation, handleStepSave]);

  // Enhanced step validation handler
  const handleStepValidation = useCallback(async (stepIndex) => {
    if (!planLimits.stepValidation) {
      return true; // Skip validation if not available in plan
    }

    setIsValidating(true);

    try {
      let isValid = true;
      let errors = [];
      let warnings = [];

      // Custom validation handler
      if (onStepValidate) {
        const result = await onStepValidate(stepIndex, workflowData);
        isValid = result.isValid;
        errors = result.errors || [];
        warnings = result.warnings || [];
      }

      // Update validation status
      setStepValidationStatus(prev => ({
        ...prev,
        [stepIndex]: { isValid, errors, warnings, validatedAt: new Date().toISOString() }
      }));

      setStepErrors(prev => ({
        ...prev,
        [stepIndex]: errors
      }));

      setStepWarnings(prev => ({
        ...prev,
        [stepIndex]: warnings
      }));

      if (!isValid) {
        announceToScreenReader(`Step ${stepIndex + 1} validation failed. ${errors.length} errors found.`);
      } else {
        announceToScreenReader(`Step ${stepIndex + 1} validation passed.`);
      }

      return isValid;
    } catch (error) {
      console.error('Step validation error:', error);
      setStepErrors(prev => ({
        ...prev,
        [stepIndex]: ['Validation failed due to an unexpected error']
      }));
      return false;
    } finally {
      setIsValidating(false);
    }
  }, [planLimits.stepValidation, onStepValidate, workflowData, announceToScreenReader]);

  // Enhanced step save handler
  const handleStepSave = useCallback(async (stepIndex) => {
    if (!planLimits.autoSave) {
      return false;
    }

    setIsSaving(true);

    try {
      const step = steps[stepIndex];

      // Call external save handler
      if (onStepSave) {
        await onStepSave(stepIndex, workflowData);
      }

      // Update workflow data with persistence
      if (completionPersistence) {
        const saveData = {
          stepIndex,
          stepId: step?.id,
          data: workflowData,
          savedAt: new Date().toISOString(),
          planTier
        };

        // Save to localStorage for persistence
        localStorage.setItem(`workflow_save_${step?.id}`, JSON.stringify(saveData));
      }

      announceToScreenReader(`Step ${stepIndex + 1} saved successfully.`);
      return true;
    } catch (error) {
      console.error('Step save error:', error);
      announceToScreenReader(`Failed to save step ${stepIndex + 1}.`);
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [planLimits.autoSave, steps, onStepSave, workflowData, completionPersistence, planTier, announceToScreenReader]);

  // Additional handler functions
  const handleWorkflowReset = useCallback(() => {
    setStepErrors({});
    setStepWarnings({});
    setStepValidationStatus({});
    setExpandedSteps(new Set([0]));
    setStepBookmarks(new Set());
    setStepNotes({});
    setStepTimestamps({});
    setStepDurations({});
    announceToScreenReader('Workflow has been reset.');
  }, [announceToScreenReader]);

  const getWorkflowProgress = useCallback(() => {
    return {
      totalSteps: steps.length,
      completedSteps: completedSteps.length,
      currentStep: activeStep,
      progressPercentage: (completedSteps.length / steps.length) * 100,
      stepStatuses: steps.map((_, index) => getStepStatus(index)),
      validationStatus: stepValidationStatus,
      errors: stepErrors,
      warnings: stepWarnings
    };
  }, [steps, completedSteps.length, activeStep, stepValidationStatus, stepErrors, stepWarnings, getStepStatus]);

  const exportWorkflowData = useCallback(() => {
    if (!planLimits.bulkOperations) {
      announceToScreenReader('Data export is not available in your current plan.');
      return null;
    }

    const exportData = {
      workflow: {
        steps: steps.map((step, index) => ({
          ...step,
          status: getStepStatus(index),
          completed: completedSteps.includes(index),
          hasData: hasStepData(index)
        })),
        progress: getWorkflowProgress(),
        timestamps: stepTimestamps,
        durations: stepDurations,
        bookmarks: Array.from(stepBookmarks),
        notes: stepNotes
      },
      data: workflowData,
      metadata: {
        exportedAt: new Date().toISOString(),
        planTier,
        version: '2.0.0'
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflow-export-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    announceToScreenReader('Workflow data exported successfully.');
    return exportData;
  }, [planLimits.bulkOperations, steps, completedSteps, stepTimestamps, stepDurations, stepBookmarks, stepNotes, workflowData, planTier, announceToScreenReader, getStepStatus, getWorkflowProgress, hasStepData]);

  const toggleStepBookmark = useCallback((stepIndex) => {
    setStepBookmarks(prev => {
      const newBookmarks = new Set(prev);
      if (newBookmarks.has(stepIndex)) {
        newBookmarks.delete(stepIndex);
        announceToScreenReader(`Bookmark removed from step ${stepIndex + 1}.`);
      } else {
        newBookmarks.add(stepIndex);
        announceToScreenReader(`Bookmark added to step ${stepIndex + 1}.`);
      }
      return newBookmarks;
    });
  }, [announceToScreenReader]);

  const addStepNote = useCallback((stepIndex, note) => {
    setStepNotes(prev => ({
      ...prev,
      [stepIndex]: note
    }));
    announceToScreenReader(`Note added to step ${stepIndex + 1}.`);
  }, [announceToScreenReader]);

  const getStepAnalytics = useCallback(() => {
    if (!planLimits.progressAnalytics) {
      return null;
    }

    return {
      totalTime: Object.values(stepDurations).reduce((sum, duration) => sum + duration, 0),
      averageStepTime: Object.values(stepDurations).reduce((sum, duration) => sum + duration, 0) / Object.keys(stepDurations).length || 0,
      stepDurations,
      validationAttempts: Object.keys(stepValidationStatus).length,
      errorCount: Object.values(stepErrors).flat().length,
      warningCount: Object.values(stepWarnings).flat().length,
      bookmarkCount: stepBookmarks.size,
      noteCount: Object.keys(stepNotes).length
    };
  }, [planLimits.progressAnalytics, stepDurations, stepValidationStatus, stepErrors, stepWarnings, stepBookmarks, stepNotes]);

  // Calculate progress percentage
  const progressPercentage = useMemo(() => {
    return steps.length > 0 ? (completedSteps.length / steps.length) * 100 : 0;
  }, [completedSteps.length, steps.length]);

  // Enhanced step status with error handling
  const getStepStatus = useCallback((stepIndex) => {
    if (stepErrors[stepIndex]?.length > 0) return STEP_STATUS.ERROR;
    if (completedSteps.includes(stepIndex)) return STEP_STATUS.COMPLETED;
    if (stepIndex === activeStep) return STEP_STATUS.ACTIVE;
    if (stepIndex < activeStep) return STEP_STATUS.SKIPPED;
    if (!planLimits.stepNavigation && stepIndex > activeStep) return STEP_STATUS.LOCKED;
    return STEP_STATUS.PENDING;
  }, [stepErrors, completedSteps, activeStep, planLimits.stepNavigation]);

  // Check if step has data
  const hasStepData = useCallback((stepIndex) => {
    const step = steps[stepIndex];
    if (!step?.id) return false;

    const dataKey = step.id.replace('-', '_').replace('_', '');
    return workflowData[dataKey] !== null && workflowData[dataKey] !== undefined;
  }, [steps, workflowData]);

  // Enhanced step icons mapping with custom support
  const getStepIcon = useCallback((stepIndex) => {
    if (customStepIcons[stepIndex]) {
      return customStepIcons[stepIndex];
    }

    const defaultIcons = {
      0: BusinessIcon,
      1: PersonIcon,
      2: StrategyIcon,
      3: CampaignIcon
    };

    return defaultIcons[stepIndex] || BusinessIcon;
  }, [customStepIcons]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: `all ${animationDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`
  }), [theme, animationDuration]);

  // Effects for auto-save and step tracking
  useEffect(() => {
    if (enableAutoSave && planLimits.autoSave) {
      const autoSaveInterval = setInterval(() => {
        handleStepSave(activeStep);
      }, 30000); // Auto-save every 30 seconds

      return () => clearInterval(autoSaveInterval);
    }
  }, [enableAutoSave, planLimits.autoSave, activeStep, handleStepSave]);

  useEffect(() => {
    // Track step entry time
    const now = Date.now();
    setStepTimestamps(prev => ({
      ...prev,
      [activeStep]: now
    }));

    // Expand current step
    setExpandedSteps(prev => new Set([...prev, activeStep]));
  }, [activeStep]);

  // Load persisted data on mount
  useEffect(() => {
    if (completionPersistence) {
      steps.forEach((step) => {
        const savedData = localStorage.getItem(`workflow_save_${step.id}`);
        if (savedData) {
          try {
            const parsed = JSON.parse(savedData);
            if (parsed.data) {
              updateWorkflowData(parsed.data);
            }
          } catch (error) {
            console.error('Failed to load persisted step data:', error);
          }
        }
      });
    }
  }, [completionPersistence, steps, updateWorkflowData]);

  // Enhanced progress indicator component
  const ProgressIndicator = useMemo(() => {
    if (!showProgressBar) return null;

    return (
      <Card sx={{ ...glassMorphismStyles, mb: 3, background: alpha(theme.palette.info.light, 0.1) }}>
        <CardContent sx={{ p: 2 }}>
          {/* Progress Header */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TimelineIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="subtitle2" color="textSecondary">
                Workflow Progress
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="subtitle2" color="primary" fontWeight={600}>
                {Math.round(progressPercentage)}% Complete
              </Typography>
              {enableProgressAnalytics && planLimits.progressAnalytics && (
                <FeatureGate requiredPlan={2}>
                  <Tooltip title="View analytics">
                    <IconButton size="small" onClick={() => setShowStepDetails(!showStepDetails)}>
                      <AssessmentIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </FeatureGate>
              )}
            </Box>
          </Box>

          {/* Enhanced Progress Bar */}
          <LinearProgress
            variant="determinate"
            value={progressPercentage}
            sx={{
              height: 10,
              borderRadius: 5,
              backgroundColor: alpha(theme.palette.action.disabled, 0.2),
              '& .MuiLinearProgress-bar': {
                borderRadius: 5,
                background: `linear-gradient(90deg,
                  ${ACE_COLORS.PURPLE} 0%,
                  ${ACE_COLORS.YELLOW} 100%)`,
              },
            }}
          />

          {/* Step Indicators with Enhanced Status */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            mt: 2,
            px: 0.5
          }}>
            {steps.map((step, index) => {
              const status = getStepStatus(index);
              const hasData = hasStepData(index);
              const isBookmarked = stepBookmarks.has(index);
              const hasNote = stepNotes[index];
              const hasError = stepErrors[index]?.length > 0;
              const hasWarning = stepWarnings[index]?.length > 0;

              return (
                <Tooltip
                  key={step.id}
                  title={
                    <Box>
                      <Typography variant="subtitle2">{step.label}</Typography>
                      <Typography variant="caption" display="block">
                        Status: {status.charAt(0).toUpperCase() + status.slice(1)}
                      </Typography>
                      {hasError && (
                        <Typography variant="caption" color="error" display="block">
                          {stepErrors[index].length} error(s)
                        </Typography>
                      )}
                      {hasWarning && (
                        <Typography variant="caption" color="warning" display="block">
                          {stepWarnings[index].length} warning(s)
                        </Typography>
                      )}
                      {hasNote && (
                        <Typography variant="caption" color="info" display="block">
                          Has note
                        </Typography>
                      )}
                    </Box>
                  }
                  arrow
                >
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    cursor: planLimits.stepNavigation ? 'pointer' : 'help',
                    position: 'relative'
                  }}
                  onClick={() => planLimits.stepNavigation && handleStepNavigation(index)}
                  >
                    {/* Status Icon */}
                    {status === STEP_STATUS.COMPLETED ? (
                      <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: 20 }} />
                    ) : status === STEP_STATUS.ERROR ? (
                      <ErrorIcon sx={{ color: theme.palette.error.main, fontSize: 20 }} />
                    ) : status === STEP_STATUS.ACTIVE ? (
                      <ScheduleIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: 20 }} />
                    ) : status === STEP_STATUS.LOCKED ? (
                      <LockIcon sx={{ color: theme.palette.action.disabled, fontSize: 20 }} />
                    ) : (
                      <RadioButtonUncheckedIcon sx={{ color: theme.palette.action.disabled, fontSize: 20 }} />
                    )}

                    {/* Indicators */}
                    <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5, flexWrap: 'wrap', justifyContent: 'center' }}>
                      {hasData && status !== STEP_STATUS.COMPLETED && (
                        <Chip
                          label="Draft"
                          size="small"
                          variant="outlined"
                          color="warning"
                          sx={{ height: 16, fontSize: '0.625rem' }}
                        />
                      )}
                      {isBookmarked && (
                        <BookmarkFilledIcon sx={{ color: ACE_COLORS.YELLOW, fontSize: 12 }} />
                      )}
                      {hasNote && (
                        <InfoIcon sx={{ color: theme.palette.info.main, fontSize: 12 }} />
                      )}
                    </Box>

                    {/* Step Number */}
                    {showStepNumbers && (
                      <Typography variant="caption" sx={{ mt: 0.5, fontSize: '0.625rem' }}>
                        {index + 1}
                      </Typography>
                    )}
                  </Box>
                </Tooltip>
              );
            })}
          </Box>

          {/* Analytics Panel */}
          <FeatureGate requiredPlan={2}>
            <Collapse in={showStepDetails && enableProgressAnalytics}>
              <Divider sx={{ my: 2 }} />
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="primary">
                    {Object.values(stepDurations).reduce((sum, duration) => sum + duration, 0) / 1000 / 60 || 0}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Total Minutes
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="success.main">
                    {Object.values(stepErrors).flat().length}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Total Errors
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="warning.main">
                    {Object.values(stepWarnings).flat().length}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Total Warnings
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="info.main">
                    {stepBookmarks.size}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Bookmarks
                  </Typography>
                </Box>
              </Box>
            </Collapse>
          </FeatureGate>
        </CardContent>
      </Card>
    );
  }, [showProgressBar, glassMorphismStyles, theme, progressPercentage, enableProgressAnalytics, planLimits.progressAnalytics, showStepDetails, steps, getStepStatus, hasStepData, stepBookmarks, stepNotes, stepErrors, stepWarnings, showStepNumbers, planLimits.stepNavigation, handleStepNavigation, stepDurations]);

  // Enhanced step icon component with status indicators
  const StepIconComponent = useCallback(({ active, completed, icon }) => {
    const IconComponent = getStepIcon(icon - 1);
    const stepIndex = icon - 1;
    const hasData = hasStepData(stepIndex);
    const hasError = stepErrors[stepIndex]?.length > 0;
    const hasWarning = stepWarnings[stepIndex]?.length > 0;
    const isBookmarked = stepBookmarks.has(stepIndex);

    return (
      <Box
        sx={{
          position: 'relative',
          width: 48,
          height: 48,
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: hasError
            ? theme.palette.error.main
            : completed
              ? theme.palette.success.main
              : active
                ? ACE_COLORS.PURPLE
                : alpha(theme.palette.action.disabled, 0.3),
          color: completed || active || hasError
            ? theme.palette.common.white
            : theme.palette.text.disabled,
          transition: `all ${animationDuration}ms cubic-bezier(0.4, 0, 0.2, 1)`,
          border: active ? `3px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}` : hasError ? `3px solid ${alpha(theme.palette.error.main, 0.3)}` : 'none',
          cursor: planLimits.stepNavigation ? 'pointer' : 'default',
          '&:hover': planLimits.stepNavigation ? {
            transform: 'scale(1.1)',
            boxShadow: `0 4px 16px 0 ${alpha(theme.palette.common.black, 0.2)}`
          } : {},
          '&:focus-visible': {
            outline: `2px solid ${theme.palette.primary.main}`,
            outlineOffset: '2px',
          },
        }}
        onClick={() => planLimits.stepNavigation && handleStepNavigation(stepIndex)}
      >
        <IconComponent fontSize="small" />

        {/* Status indicators */}
        {hasError && (
          <ErrorIcon
            sx={{
              position: 'absolute',
              top: -4,
              right: -4,
              fontSize: 16,
              color: theme.palette.error.main,
              backgroundColor: theme.palette.background.paper,
              borderRadius: '50%',
            }}
          />
        )}

        {hasWarning && !hasError && (
          <WarningIcon
            sx={{
              position: 'absolute',
              top: -4,
              right: -4,
              fontSize: 16,
              color: theme.palette.warning.main,
              backgroundColor: theme.palette.background.paper,
              borderRadius: '50%',
            }}
          />
        )}

        {completed && !hasError && !hasWarning && (
          <CheckCircleIcon
            sx={{
              position: 'absolute',
              top: -4,
              right: -4,
              fontSize: 16,
              color: theme.palette.success.main,
              backgroundColor: theme.palette.background.paper,
              borderRadius: '50%',
            }}
          />
        )}

        {/* Data indicator */}
        {hasData && !completed && !hasError && (
          <Box
            sx={{
              position: 'absolute',
              top: -2,
              right: -2,
              width: 12,
              height: 12,
              borderRadius: '50%',
              backgroundColor: theme.palette.warning.main,
              border: `2px solid ${theme.palette.background.paper}`,
            }}
          />
        )}

        {/* Bookmark indicator */}
        {isBookmarked && (
          <BookmarkFilledIcon
            sx={{
              position: 'absolute',
              bottom: -4,
              right: -4,
              fontSize: 12,
              color: ACE_COLORS.YELLOW,
              backgroundColor: theme.palette.background.paper,
              borderRadius: '50%',
              padding: '1px'
            }}
          />
        )}
      </Box>
    );
  }, [getStepIcon, hasStepData, stepErrors, stepWarnings, stepBookmarks, theme, animationDuration, planLimits.stepNavigation, handleStepNavigation]);

  return (
    <Paper
      ref={stepperRef}
      elevation={0}
      sx={{
        p: compactMode ? theme.spacing(2) : theme.spacing(3),
        ...glassMorphismStyles,
        position: 'relative',
        // WCAG 2.1 AA compliance
        '&:focus-visible': {
          outline: `2px solid ${theme.palette.primary.main}`,
          outlineOffset: '2px',
        },
        // Responsive design
        [theme.breakpoints.down('sm')]: {
          p: theme.spacing(2),
        },
        // High contrast mode support
        '@media (prefers-contrast: high)': {
          border: `2px solid ${theme.palette.text.primary}`,
          background: theme.palette.background.paper,
          backdropFilter: 'none',
        },
        // Reduced motion support
        '@media (prefers-reduced-motion: reduce)': {
          transition: 'none',
          '& *': {
            transition: 'none !important',
            animation: 'none !important'
          }
        },
      }}
    >
      {/* Loading Overlay */}
      {(isValidating || isSaving) && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: alpha(theme.palette.background.paper, 0.8),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
            borderRadius: theme.spacing(2)
          }}
        >
          <Box sx={{ textAlign: 'center' }}>
            <CircularProgress size={40} sx={{ color: ACE_COLORS.PURPLE }} />
            <Typography variant="body2" sx={{ mt: 1 }}>
              {isValidating ? 'Validating step...' : 'Saving progress...'}
            </Typography>
          </Box>
        </Box>
      )}

      {/* Enhanced Progress Indicator */}
      <ProgressIndicator />

      {/* Workflow Controls */}
      <FeatureGate requiredPlan={2}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {planLimits.stepNavigation && (
              <>
                <Tooltip title="Previous step">
                  <IconButton
                    size="small"
                    onClick={() => handleStepNavigation(activeStep - 1)}
                    disabled={activeStep === 0}
                  >
                    <PrevIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Next step">
                  <IconButton
                    size="small"
                    onClick={() => handleStepNavigation(activeStep + 1)}
                    disabled={activeStep >= steps.length - 1}
                  >
                    <NextIcon />
                  </IconButton>
                </Tooltip>
              </>
            )}

            {planLimits.stepValidation && (
              <Tooltip title="Validate current step">
                <IconButton
                  size="small"
                  onClick={() => handleStepValidation(activeStep)}
                  disabled={isValidating}
                >
                  <CheckCircleIcon />
                </IconButton>
              </Tooltip>
            )}

            {planLimits.autoSave && (
              <Tooltip title="Save progress">
                <IconButton
                  size="small"
                  onClick={() => handleStepSave(activeStep)}
                  disabled={isSaving}
                >
                  <SaveIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Toggle step bookmark">
              <IconButton
                size="small"
                onClick={() => toggleStepBookmark(activeStep)}
                color={stepBookmarks.has(activeStep) ? 'primary' : 'default'}
              >
                {stepBookmarks.has(activeStep) ? <BookmarkFilledIcon /> : <BookmarkIcon />}
              </IconButton>
            </Tooltip>

            <Tooltip title="Workflow settings">
              <IconButton size="small">
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </FeatureGate>

      {/* Enhanced Stepper */}
      <Stepper
        activeStep={activeStep}
        orientation={orientation}
        nonLinear={planLimits.stepNavigation}
        sx={{
          // Custom stepper styling with ACE Social branding
          '& .MuiStepLabel-root': {
            padding: { xs: theme.spacing(0.5), sm: theme.spacing(1) },
            cursor: planLimits.stepNavigation ? 'pointer' : 'default'
          },
          '& .MuiStepContent-root': {
            borderLeft: `2px solid ${alpha(ACE_COLORS.PURPLE, 0.3)}`,
            marginLeft: { xs: theme.spacing(1.5), sm: theme.spacing(2.5) },
            paddingLeft: { xs: theme.spacing(2), sm: theme.spacing(3) },
          },
          '& .MuiStepConnector-line': {
            borderColor: alpha(ACE_COLORS.PURPLE, 0.3),
            borderWidth: 2
          },
          '& .MuiStepConnector-root.Mui-completed .MuiStepConnector-line': {
            borderColor: theme.palette.success.main,
          },
          '& .MuiStepConnector-root.Mui-active .MuiStepConnector-line': {
            borderColor: ACE_COLORS.PURPLE,
          },
          // Mobile-specific stepper adjustments
          [theme.breakpoints.down('sm')]: {
            '& .MuiStepLabel-label': {
              fontSize: '0.875rem',
            },
            '& .MuiStepLabel-iconContainer': {
              paddingRight: theme.spacing(1),
            },
          },
        }}
      >
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const hasData = hasStepData(index);
          const hasError = stepErrors[index]?.length > 0;
          const hasWarning = stepWarnings[index]?.length > 0;
          const isBookmarked = stepBookmarks.has(index);
          const hasNote = stepNotes[index];

          return (
            <Step key={step.id} completed={status === STEP_STATUS.COMPLETED}>
              {planLimits.stepNavigation ? (
                <StepButton
                  onClick={() => handleStepNavigation(index)}
                  disabled={status === STEP_STATUS.LOCKED}
                >
                  <StepLabel
                    StepIconComponent={(props) => (
                      <StepIconComponent {...props} icon={index + 1} />
                    )}
                    error={hasError}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                      <Typography
                        variant="subtitle1"
                        sx={{
                          fontWeight: activeStep === index ? 600 : 400,
                          color: hasError
                            ? theme.palette.error.main
                            : activeStep === index
                              ? theme.palette.text.primary
                              : theme.palette.text.secondary,
                        }}
                      >
                        {step.label}
                      </Typography>

                      {/* Enhanced status indicators */}
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                        {status === STEP_STATUS.COMPLETED && (
                          <Chip
                            label="Complete"
                            size="small"
                            color="success"
                            variant="filled"
                            sx={{ height: 20, fontSize: '0.75rem' }}
                          />
                        )}

                        {status === STEP_STATUS.ACTIVE && (
                          <Chip
                            label="In Progress"
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ height: 20, fontSize: '0.75rem' }}
                          />
                        )}

                        {status === STEP_STATUS.ERROR && (
                          <Chip
                            label={`${stepErrors[index].length} Error(s)`}
                            size="small"
                            color="error"
                            variant="filled"
                            sx={{ height: 20, fontSize: '0.75rem' }}
                          />
                        )}

                        {hasWarning && !hasError && (
                          <Chip
                            label={`${stepWarnings[index].length} Warning(s)`}
                            size="small"
                            color="warning"
                            variant="outlined"
                            sx={{ height: 20, fontSize: '0.75rem' }}
                          />
                        )}

                        {hasData && status !== STEP_STATUS.COMPLETED && !hasError && (
                          <Chip
                            label="Draft Saved"
                            size="small"
                            color="info"
                            variant="outlined"
                            sx={{ height: 20, fontSize: '0.75rem' }}
                          />
                        )}

                        {status === STEP_STATUS.LOCKED && (
                          <Chip
                            label="Locked"
                            size="small"
                            color="default"
                            variant="outlined"
                            icon={<LockIcon fontSize="small" />}
                            sx={{ height: 20, fontSize: '0.75rem' }}
                          />
                        )}

                        {isBookmarked && (
                          <Tooltip title="Bookmarked step">
                            <BookmarkFilledIcon sx={{ color: ACE_COLORS.YELLOW, fontSize: 16 }} />
                          </Tooltip>
                        )}

                        {hasNote && (
                          <Tooltip title={`Note: ${stepNotes[index]}`}>
                            <InfoIcon sx={{ color: theme.palette.info.main, fontSize: 16 }} />
                          </Tooltip>
                        )}
                      </Box>
                    </Box>

                    {step.description && (
                      <Typography
                        variant="body2"
                        color="textSecondary"
                        sx={{ mt: 0.5 }}
                      >
                        {step.description}
                      </Typography>
                    )}

                    {/* Step validation feedback */}
                    {hasError && (
                      <Alert severity="error" sx={{ mt: 1, py: 0 }}>
                        <Typography variant="caption">
                          {stepErrors[index].join(', ')}
                        </Typography>
                      </Alert>
                    )}

                    {hasWarning && !hasError && (
                      <Alert severity="warning" sx={{ mt: 1, py: 0 }}>
                        <Typography variant="caption">
                          {stepWarnings[index].join(', ')}
                        </Typography>
                      </Alert>
                    )}
                  </StepLabel>
                </StepButton>
              ) : (
                <StepLabel
                  StepIconComponent={(props) => (
                    <StepIconComponent {...props} icon={index + 1} />
                  )}
                  error={hasError}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                    <Typography
                      variant="subtitle1"
                      sx={{
                        fontWeight: activeStep === index ? 600 : 400,
                        color: hasError
                          ? theme.palette.error.main
                          : activeStep === index
                            ? theme.palette.text.primary
                            : theme.palette.text.secondary,
                      }}
                    >
                      {step.label}
                    </Typography>

                    {/* Status indicators for non-navigable stepper */}
                    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                      {status === STEP_STATUS.COMPLETED && (
                        <Chip
                          label="Complete"
                          size="small"
                          color="success"
                          variant="filled"
                          sx={{ height: 20, fontSize: '0.75rem' }}
                        />
                      )}

                      {status === STEP_STATUS.ACTIVE && (
                        <Chip
                          label="In Progress"
                          size="small"
                          color="primary"
                          variant="outlined"
                          sx={{ height: 20, fontSize: '0.75rem' }}
                        />
                      )}

                      {hasData && status !== STEP_STATUS.COMPLETED && (
                        <Chip
                          label="Draft Saved"
                          size="small"
                          color="warning"
                          variant="outlined"
                          sx={{ height: 20, fontSize: '0.75rem' }}
                        />
                      )}
                    </Box>
                  </Box>

                  {step.description && (
                    <Typography
                      variant="body2"
                      color="textSecondary"
                      sx={{ mt: 0.5 }}
                    >
                      {step.description}
                    </Typography>
                  )}
                </StepLabel>
              )}

              {orientation === 'vertical' && (
                <StepContent>
                  <Fade in={expandedSteps.has(index)} timeout={animationDuration}>
                    <Box sx={{
                      pb: { xs: theme.spacing(1), sm: theme.spacing(2) },
                      [theme.breakpoints.down('sm')]: {
                        px: theme.spacing(1),
                      },
                    }}>
                      {children}
                    </Box>
                  </Fade>
                </StepContent>
              )}
            </Step>
          );
        })}
      </Stepper>

      {orientation === 'horizontal' && (
        <Fade in timeout={animationDuration}>
          <Box sx={{
            mt: { xs: theme.spacing(2), sm: theme.spacing(3) },
            [theme.breakpoints.down('sm')]: {
              px: theme.spacing(1),
            },
          }}>
            {children}
          </Box>
        </Fade>
      )}
    </Paper>
  );
}));

ServiceWorkflowStepper.displayName = 'ServiceWorkflowStepper';

ServiceWorkflowStepper.propTypes = {
  /** Current active step index */
  activeStep: PropTypes.number,
  /** Array of step objects */
  steps: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    description: PropTypes.string,
    optional: PropTypes.bool,
    disabled: PropTypes.bool
  })),
  /** Step content to render */
  children: PropTypes.node,
  /** Stepper orientation */
  orientation: PropTypes.oneOf(['horizontal', 'vertical']),
  /** Step change callback */
  onStepChange: PropTypes.func,
  /** Step validation callback */
  onStepValidate: PropTypes.func,
  /** Step save callback */
  onStepSave: PropTypes.func,
  /** Enable step validation */
  enableValidation: PropTypes.bool,
  /** Enable auto-save functionality */
  enableAutoSave: PropTypes.bool,
  /** Enable progress analytics */
  enableProgressAnalytics: PropTypes.bool,
  /** Custom step icons mapping */
  customStepIcons: PropTypes.object,
  /** Enable completion persistence */
  completionPersistence: PropTypes.bool,
  /** Allow step skipping */
  allowStepSkipping: PropTypes.bool,
  /** Show step numbers */
  showStepNumbers: PropTypes.bool,
  /** Show progress bar */
  showProgressBar: PropTypes.bool,
  /** Enable compact mode */
  compactMode: PropTypes.bool,
  /** Animation duration in milliseconds */
  animationDuration: PropTypes.number
};

export default ServiceWorkflowStepper;

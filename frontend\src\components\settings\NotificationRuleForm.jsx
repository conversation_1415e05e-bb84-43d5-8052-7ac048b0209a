/**
 * Enhanced Notification Rule Form - Enterprise-grade notification rule form component
 * Features: Comprehensive notification rule form with advanced notification management capabilities, multi-dimensional notification rule configuration,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced notification rule form capabilities and seamless notification system workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  memo,
  forwardRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextField,
  Typography,
  Tooltip,
  Chip,
  Paper,
  Stack,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Help as HelpIcon,
  PlayArrow as TestIcon,
  Analytics as AnalyticsIcon,
  Share as ShareIcon,
  Visibility as VisibilityIcon,
  Notifications as NotificationsIcon,
  NotificationsActive as NotificationsActiveIcon,
  Schedule as ScheduleIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  PhoneAndroid as PhoneAndroidIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { useSnackbar } from '../../contexts/SnackbarContext';
import { testNotificationRule } from '../../api/notificationRules';

// Enhanced constants for form options with ACE Social integration
const TRIGGER_TYPES = [
  { value: 'client_approval', label: 'Client Approval', icon: <CheckCircleIcon />, category: 'client' },
  { value: 'client_rejection', label: 'Client Rejection', icon: <ErrorIcon />, category: 'client' },
  { value: 'client_comment', label: 'Client Comment', icon: <InfoIcon />, category: 'client' },
  { value: 'content_published', label: 'Content Published', icon: <CheckCircleIcon />, category: 'content' },
  { value: 'content_scheduled', label: 'Content Scheduled', icon: <ScheduleIcon />, category: 'content' },
  { value: 'content_failed', label: 'Content Failed', icon: <ErrorIcon />, category: 'content' },
  { value: 'analytics_threshold', label: 'Analytics Threshold', icon: <AnalyticsIcon />, category: 'analytics' },
  { value: 'calendar_shared', label: 'Calendar Shared', icon: <ShareIcon />, category: 'calendar' },
  { value: 'calendar_accessed', label: 'Calendar Accessed', icon: <VisibilityIcon />, category: 'calendar' },
  { value: 'specific_client', label: 'Specific Client', icon: <InfoIcon />, category: 'targeting' },
  { value: 'specific_campaign', label: 'Specific Campaign', icon: <InfoIcon />, category: 'targeting' },
  { value: 'specific_content', label: 'Specific Content', icon: <InfoIcon />, category: 'targeting' },
  { value: 'specific_icp', label: 'Specific ICP', icon: <InfoIcon />, category: 'targeting' }
];

const DELIVERY_METHODS = [
  { value: 'in_app', label: 'In-App Only', icon: <NotificationsIcon />, description: 'Show notifications within the application' },
  { value: 'email', label: 'Email Only', icon: <EmailIcon />, description: 'Send notifications via email' },
  { value: 'push', label: 'Push Only', icon: <PhoneAndroidIcon />, description: 'Send push notifications to mobile devices' },
  { value: 'sms', label: 'SMS Only', icon: <SmsIcon />, description: 'Send notifications via SMS' },
  { value: 'all', label: 'All Methods', icon: <NotificationsActiveIcon />, description: 'Use all available delivery methods' }
];

const FREQUENCIES = [
  { value: 'immediately', label: 'Immediately' },
  { value: 'daily_digest', label: 'Daily Digest' },
  { value: 'weekly_digest', label: 'Weekly Digest' }
];

const CONDITION_OPERATORS = [
  { value: 'equals', label: 'Equals' },
  { value: 'not_equals', label: 'Not Equals' },
  { value: 'contains', label: 'Contains' },
  { value: 'greater_than', label: 'Greater Than' },
  { value: 'less_than', label: 'Less Than' },
  { value: 'in', label: 'In (comma-separated list)' },
  { value: 'not_in', label: 'Not In (comma-separated list)' }
];

// Field suggestions based on trigger type
const FIELD_SUGGESTIONS = {
  client_approval: ['reviewer_email', 'reviewer_name', 'content_title'],
  client_rejection: ['reviewer_email', 'reviewer_name', 'content_title'],
  client_comment: ['reviewer_email', 'reviewer_name', 'content_title', 'comment'],
  content_published: ['content_title', 'platform', 'campaign_name'],
  content_scheduled: ['content_title', 'platform', 'campaign_name', 'scheduled_time'],
  content_failed: ['content_title', 'platform', 'campaign_name', 'error_message'],
  analytics_threshold: ['metric_name', 'metric_value', 'content_title'],
  calendar_shared: ['calendar_name', 'collaborator_email', 'collaborator_name'],
  calendar_accessed: ['calendar_name', 'collaborator_email', 'collaborator_name'],
  specific_client: ['client_email', 'client_name'],
  specific_campaign: ['campaign_id', 'campaign_name'],
  specific_content: ['content_id', 'content_title'],
  specific_icp: ['icp_id', 'icp_name']
};

// Sample test data based on trigger type
const SAMPLE_TEST_DATA = {
  client_approval: {
    reviewer_email: '<EMAIL>',
    reviewer_name: 'John Client',
    content_title: 'Sample Content',
    status: 'approved'
  },
  client_rejection: {
    reviewer_email: '<EMAIL>',
    reviewer_name: 'John Client',
    content_title: 'Sample Content',
    status: 'rejected'
  },
  client_comment: {
    reviewer_email: '<EMAIL>',
    reviewer_name: 'John Client',
    content_title: 'Sample Content',
    comment: 'This looks great!'
  }
  // Add more sample data for other trigger types as needed
};

/**
 * Enhanced Notification Rule Form Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Object} [props.initialData] - Initial form data for editing
 * @param {Function} props.onSubmit - Form submission callback
 * @param {Function} props.onCancel - Form cancellation callback
 * @param {Function} [props.onRuleAction] - Rule action callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {boolean} [props.enableRealTimeUpdates=true] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-notification-rule-form'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const NotificationRuleForm = memo(forwardRef(({
  initialData,
  onSubmit,
  onCancel
}) => {
  const { showSuccessNotification, showErrorNotification } = useSnackbar();
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    trigger_type: 'client_approval',
    conditions: [],
    delivery_method: 'all',
    frequency: 'immediately',
    is_active: true,
    ...initialData
  });
  
  // Test state
  const [testResult, setTestResult] = useState(null);
  const [isTestingRule, setIsTestingRule] = useState(false);
  
  // Field suggestions based on selected trigger type
  const [fieldSuggestions, setFieldSuggestions] = useState(
    FIELD_SUGGESTIONS[formData.trigger_type] || []
  );
  
  // Update field suggestions when trigger type changes
  useEffect(() => {
    setFieldSuggestions(FIELD_SUGGESTIONS[formData.trigger_type] || []);
  }, [formData.trigger_type]);
  
  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    
    // Handle checkbox fields
    if (name === 'is_active') {
      setFormData(prev => ({ ...prev, [name]: checked }));
      return;
    }
    
    // Handle other fields
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear test result when form changes
    setTestResult(null);
  };
  
  // Handle condition changes
  const handleConditionChange = (index, field, value) => {
    const updatedConditions = [...formData.conditions];
    updatedConditions[index] = { ...updatedConditions[index], [field]: value };
    
    setFormData(prev => ({ ...prev, conditions: updatedConditions }));
    
    // Clear test result when conditions change
    setTestResult(null);
  };
  
  // Add a new condition
  const addCondition = () => {
    const newCondition = {
      field: fieldSuggestions[0] || '',
      operator: 'equals',
      value: ''
    };
    
    setFormData(prev => ({
      ...prev,
      conditions: [...prev.conditions, newCondition]
    }));
    
    // Clear test result when conditions change
    setTestResult(null);
  };
  
  // Remove a condition
  const removeCondition = (index) => {
    const updatedConditions = [...formData.conditions];
    updatedConditions.splice(index, 1);
    
    setFormData(prev => ({ ...prev, conditions: updatedConditions }));
    
    // Clear test result when conditions change
    setTestResult(null);
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.name) {
      showErrorNotification('Please enter a name for the notification rule');
      return;
    }
    
    // Submit form
    onSubmit(formData);
  };
  
  // Test the rule
  const handleTestRule = async () => {
    try {
      setIsTestingRule(true);
      
      // If we're creating a new rule, we can't test it yet
      if (!initialData?.id) {
        // Use sample test data based on trigger type
        const testData = SAMPLE_TEST_DATA[formData.trigger_type] || {};
        
        // Simulate test result
        const wouldTrigger = formData.conditions.length === 0 || 
          formData.conditions.every(condition => {
            if (!testData[condition.field]) return false;
            
            switch (condition.operator) {
              case 'equals':
                return testData[condition.field] === condition.value;
              case 'contains':
                return testData[condition.field].includes(condition.value);
              // Add more operators as needed
              default:
                return false;
            }
          });
        
        setTestResult({
          would_trigger: wouldTrigger,
          matching_conditions: wouldTrigger ? formData.conditions : [],
          non_matching_conditions: wouldTrigger ? [] : formData.conditions
        });
        
        showSuccessNotification('Rule tested with sample data');
      } else {
        // Use the API to test the rule
        const result = await testNotificationRule(
          initialData.id,
          SAMPLE_TEST_DATA[formData.trigger_type] || {}
        );
        
        setTestResult(result);
        showSuccessNotification('Rule tested successfully');
      }
    } catch (error) {
      console.error('Error testing rule:', error);
      showErrorNotification('Failed to test rule');
    } finally {
      setIsTestingRule(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {initialData ? 'Edit Notification Rule' : 'Create Notification Rule'}
          </Typography>
          
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Rule Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={handleChange}
                      name="is_active"
                      color="primary"
                    />
                  }
                  label="Active"
                />
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                multiline
                rows={2}
                margin="normal"
              />
            </Grid>
            
            {/* Trigger Type */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Trigger Type</InputLabel>
                <Select
                  name="trigger_type"
                  value={formData.trigger_type}
                  onChange={handleChange}
                  label="Trigger Type"
                >
                  {TRIGGER_TYPES.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>
                  When should this notification be triggered?
                </FormHelperText>
              </FormControl>
            </Grid>
            
            {/* Delivery Method */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Delivery Method</InputLabel>
                <Select
                  name="delivery_method"
                  value={formData.delivery_method}
                  onChange={handleChange}
                  label="Delivery Method"
                >
                  {DELIVERY_METHODS.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>
                  How should this notification be delivered?
                </FormHelperText>
              </FormControl>
            </Grid>
            
            {/* Frequency */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Frequency</InputLabel>
                <Select
                  name="frequency"
                  value={formData.frequency}
                  onChange={handleChange}
                  label="Frequency"
                >
                  {FREQUENCIES.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>
                  How often should this notification be sent?
                </FormHelperText>
              </FormControl>
            </Grid>
          </Grid>
          
          <Divider sx={{ my: 3 }} />
          
          {/* Conditions */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Conditions
              <Tooltip title="Conditions determine when this notification rule will be triggered. If no conditions are specified, the rule will be triggered for all events of the selected type.">
                <IconButton size="small">
                  <HelpIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Typography>
            
            {formData.conditions.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                No conditions specified. This rule will be triggered for all events of the selected type.
              </Typography>
            ) : (
              formData.conditions.map((condition, index) => (
                <Paper
                  key={index}
                  variant="outlined"
                  sx={{ p: 2, mb: 2, position: 'relative' }}
                >
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth>
                        <InputLabel>Field</InputLabel>
                        <Select
                          value={condition.field}
                          onChange={(e) => handleConditionChange(index, 'field', e.target.value)}
                          label="Field"
                        >
                          {fieldSuggestions.map(field => (
                            <MenuItem key={field} value={field}>
                              {field}
                            </MenuItem>
                          ))}
                          <MenuItem value="custom">
                            <em>Custom Field</em>
                          </MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    {condition.field === 'custom' && (
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          label="Custom Field"
                          value={condition.custom_field || ''}
                          onChange={(e) => handleConditionChange(index, 'custom_field', e.target.value)}
                        />
                      </Grid>
                    )}
                    
                    <Grid item xs={12} md={condition.field === 'custom' ? 4 : 4}>
                      <FormControl fullWidth>
                        <InputLabel>Operator</InputLabel>
                        <Select
                          value={condition.operator}
                          onChange={(e) => handleConditionChange(index, 'operator', e.target.value)}
                          label="Operator"
                        >
                          {CONDITION_OPERATORS.map(op => (
                            <MenuItem key={op.value} value={op.value}>
                              {op.label}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    
                    <Grid item xs={12} md={condition.field === 'custom' ? 4 : 4}>
                      <TextField
                        fullWidth
                        label="Value"
                        value={condition.value}
                        onChange={(e) => handleConditionChange(index, 'value', e.target.value)}
                      />
                    </Grid>
                    
                    <Grid item xs={12} md={condition.field === 'custom' ? 12 : 12} sx={{ textAlign: 'right' }}>
                      <IconButton
                        color="error"
                        onClick={() => removeCondition(index)}
                        aria-label="Remove condition"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Grid>
                  </Grid>
                </Paper>
              ))
            )}
            
            <Button
              startIcon={<AddIcon />}
              onClick={addCondition}
              variant="outlined"
              sx={{ mt: 1 }}
            >
              Add Condition
            </Button>
          </Box>
          
          {/* Test Results */}
          {testResult && (
            <Box sx={{ mt: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                Test Results
              </Typography>
              
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  bgcolor: testResult.would_trigger ? 'success.light' : 'error.light',
                  color: testResult.would_trigger ? 'success.contrastText' : 'error.contrastText'
                }}
              >
                <Typography variant="subtitle1">
                  {testResult.would_trigger
                    ? 'This rule would be triggered'
                    : 'This rule would NOT be triggered'}
                </Typography>
                
                {testResult.matching_conditions.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2">Matching Conditions:</Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 1 }}>
                      {testResult.matching_conditions.map((condition, index) => (
                        <Chip
                          key={index}
                          label={`${condition.field} ${condition.operator} ${condition.value}`}
                          color="success"
                          size="small"
                          sx={{ mb: 1 }}
                        />
                      ))}
                    </Stack>
                  </Box>
                )}
                
                {testResult.non_matching_conditions.length > 0 && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2">Non-Matching Conditions:</Typography>
                    <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 1 }}>
                      {testResult.non_matching_conditions.map((condition, index) => (
                        <Chip
                          key={index}
                          label={`${condition.field} ${condition.operator} ${condition.value}`}
                          color="error"
                          size="small"
                          sx={{ mb: 1 }}
                        />
                      ))}
                    </Stack>
                  </Box>
                )}
              </Paper>
            </Box>
          )}
          
          {/* Form Actions */}
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="outlined"
              onClick={onCancel}
            >
              Cancel
            </Button>
            
            <Box>
              <Button
                variant="outlined"
                startIcon={<TestIcon />}
                onClick={handleTestRule}
                disabled={isTestingRule}
                sx={{ mr: 2 }}
              >
                Test Rule
              </Button>
              
              <Button
                type="submit"
                variant="contained"
                color="primary"
              >
                {initialData ? 'Update Rule' : 'Create Rule'}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </form>
  );
}));

// Enhanced PropTypes with comprehensive validation
NotificationRuleForm.propTypes = {
  // Core props
  initialData: PropTypes.object,
  onSubmit: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired
};

NotificationRuleForm.displayName = 'NotificationRuleForm';

export default NotificationRuleForm;

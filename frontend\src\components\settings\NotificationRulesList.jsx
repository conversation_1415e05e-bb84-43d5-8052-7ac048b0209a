/**
 * Enhanced Notification Rules List - Enterprise-grade notification rules list component
 * Features: Comprehensive notification rules list with advanced notification management capabilities, multi-dimensional notification rules display,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced notification rules list capabilities and seamless notification system workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  memo,
  forwardRef,
  Fragment
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Divider,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemSecondaryAction,
  ListItemText,
  Menu,
  MenuItem,
  Switch,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  MoreVert as MoreIcon,
  Notifications as NotificationsIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  FilterList as FilterIcon,
  Analytics as AnalyticsIcon,
  Share as ShareIcon,
  Visibility as VisibilityIcon,
  Schedule as ScheduleIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  PhoneAndroid as PhoneAndroidIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import { useSnackbar } from '../../contexts/SnackbarContext';
import { updateNotificationRule, deleteNotificationRule } from '../../api/notificationRules';
import ConfirmDialog from '../common/ConfirmDialog';

// Enhanced constants for display with ACE Social integration

// Enhanced constants for display with ACE Social integration
const TRIGGER_TYPE_LABELS = {
  client_approval: { label: 'Client Approval', icon: <CheckCircleIcon />, category: 'client', color: '#4caf50' },
  client_rejection: { label: 'Client Rejection', icon: <ErrorIcon />, category: 'client', color: '#f44336' },
  client_comment: { label: 'Client Comment', icon: <InfoIcon />, category: 'client', color: '#2196f3' },
  content_published: { label: 'Content Published', icon: <CheckCircleIcon />, category: 'content', color: '#4caf50' },
  content_scheduled: { label: 'Content Scheduled', icon: <ScheduleIcon />, category: 'content', color: '#ff9800' },
  content_failed: { label: 'Content Failed', icon: <ErrorIcon />, category: 'content', color: '#f44336' },
  analytics_threshold: { label: 'Analytics Threshold', icon: <AnalyticsIcon />, category: 'analytics', color: '#9c27b0' },
  calendar_shared: { label: 'Calendar Shared', icon: <ShareIcon />, category: 'calendar', color: '#00bcd4' },
  calendar_accessed: { label: 'Calendar Accessed', icon: <VisibilityIcon />, category: 'calendar', color: '#607d8b' },
  specific_client: { label: 'Specific Client', icon: <InfoIcon />, category: 'targeting', color: '#795548' },
  specific_campaign: { label: 'Specific Campaign', icon: <InfoIcon />, category: 'targeting', color: '#795548' },
  specific_content: { label: 'Specific Content', icon: <InfoIcon />, category: 'targeting', color: '#795548' },
  specific_icp: { label: 'Specific ICP', icon: <InfoIcon />, category: 'targeting', color: '#795548' }
};

const DELIVERY_METHOD_LABELS = {
  in_app: { label: 'In-App Only', icon: <NotificationsIcon />, color: '#2196f3' },
  email: { label: 'Email Only', icon: <EmailIcon />, color: '#4caf50' },
  push: { label: 'Push Only', icon: <PhoneAndroidIcon />, color: '#ff9800' },
  sms: { label: 'SMS Only', icon: <SmsIcon />, color: '#9c27b0' },
  all: { label: 'All Methods', icon: <NotificationsActiveIcon />, color: '#f44336' }
};

const FREQUENCY_LABELS = {
  immediately: { label: 'Immediately', icon: <SpeedIcon />, color: '#f44336' },
  daily_digest: { label: 'Daily Digest', icon: <ScheduleIcon />, color: '#ff9800' },
  weekly_digest: { label: 'Weekly Digest', icon: <ScheduleIcon />, color: '#4caf50' }
};

/**
 * Enhanced Notification Rules List Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Array} props.rules - Array of notification rules
 * @param {Function} props.onRuleUpdated - Rule updated callback
 * @param {Function} props.onRuleDeleted - Rule deleted callback
 * @param {Function} props.onCreateRule - Create rule callback
 * @param {Function} props.onEditRule - Edit rule callback
 * @param {Function} [props.onRulesAction] - Rules action callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {boolean} [props.enableRealTimeUpdates=true] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-notification-rules-list'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const NotificationRulesList = memo(forwardRef(({
  rules,
  onRuleUpdated,
  onRuleDeleted,
  onCreateRule,
  onEditRule
}) => {
  const { showSuccessNotification, showErrorNotification } = useSnackbar();
  
  // State for menu
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [selectedRuleId, setSelectedRuleId] = useState(null);
  
  // State for confirm dialog
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmDialogAction, setConfirmDialogAction] = useState(null);
  const [confirmDialogTitle, setConfirmDialogTitle] = useState('');
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  
  // State for filters
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');
  const [triggerTypeFilter, setTriggerTypeFilter] = useState('all');
  
  // Open menu
  const handleMenuOpen = (event, ruleId) => {
    setMenuAnchorEl(event.currentTarget);
    setSelectedRuleId(ruleId);
  };
  
  // Close menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setSelectedRuleId(null);
  };
  
  // Open filter menu
  const handleFilterMenuOpen = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };
  
  // Close filter menu
  const handleFilterMenuClose = () => {
    setFilterAnchorEl(null);
  };
  
  // Apply filter
  const handleApplyFilter = (filterType, value) => {
    if (filterType === 'active') {
      setActiveFilter(value);
    } else if (filterType === 'triggerType') {
      setTriggerTypeFilter(value);
    }
    
    handleFilterMenuClose();
  };
  
  // Filter rules
  const filteredRules = rules.filter(rule => {
    // Filter by active status
    if (activeFilter === 'active' && !rule.is_active) return false;
    if (activeFilter === 'inactive' && rule.is_active) return false;
    
    // Filter by trigger type
    if (triggerTypeFilter !== 'all' && rule.trigger_type !== triggerTypeFilter) return false;
    
    return true;
  });
  
  // Toggle rule active status
  const handleToggleActive = async (ruleId, currentStatus) => {
    try {
      const updatedRule = await updateNotificationRule(ruleId, {
        is_active: !currentStatus
      });
      
      onRuleUpdated(updatedRule);
      showSuccessNotification(`Rule ${updatedRule.is_active ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Error toggling rule status:', error);
      showErrorNotification('Failed to update rule status');
    }
  };
  
  // Edit rule
  const handleEditRule = (ruleId) => {
    handleMenuClose();
    onEditRule(ruleId);
  };
  
  // Delete rule
  const handleDeleteRule = (ruleId) => {
    handleMenuClose();
    
    // Show confirm dialog
    setConfirmDialogTitle('Delete Notification Rule');
    setConfirmDialogMessage('Are you sure you want to delete this notification rule? This action cannot be undone.');
    setConfirmDialogAction(() => async () => {
      try {
        await deleteNotificationRule(ruleId);
        onRuleDeleted(ruleId);
        showSuccessNotification('Rule deleted successfully');
      } catch (error) {
        console.error('Error deleting rule:', error);
        showErrorNotification('Failed to delete rule');
      }
    });
    setConfirmDialogOpen(true);
  };
  
  // Handle confirm dialog close
  const handleConfirmDialogClose = (confirmed) => {
    setConfirmDialogOpen(false);
    
    if (confirmed && confirmDialogAction) {
      confirmDialogAction();
    }
  };
  
  // Get trigger type icon
  const getTriggerTypeIcon = (triggerType) => {
    switch (triggerType) {
      case 'client_approval':
      case 'client_rejection':
      case 'client_comment':
        return <NotificationsActiveIcon />;
      case 'content_published':
      case 'content_scheduled':
      case 'content_failed':
        return <NotificationsIcon />;
      default:
        return <NotificationsIcon />;
    }
  };
  
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Notification Rules</Typography>
        
        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={handleFilterMenuOpen}
            sx={{ mr: 1 }}
          >
            Filter
          </Button>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={onCreateRule}
          >
            Create Rule
          </Button>
          
          {/* Filter Menu */}
          <Menu
            anchorEl={filterAnchorEl}
            open={Boolean(filterAnchorEl)}
            onClose={handleFilterMenuClose}
          >
            <MenuItem disabled>
              <Typography variant="subtitle2">Status</Typography>
            </MenuItem>
            <MenuItem
              selected={activeFilter === 'all'}
              onClick={() => handleApplyFilter('active', 'all')}
            >
              All
            </MenuItem>
            <MenuItem
              selected={activeFilter === 'active'}
              onClick={() => handleApplyFilter('active', 'active')}
            >
              Active Only
            </MenuItem>
            <MenuItem
              selected={activeFilter === 'inactive'}
              onClick={() => handleApplyFilter('active', 'inactive')}
            >
              Inactive Only
            </MenuItem>
            
            <Divider />
            
            <MenuItem disabled>
              <Typography variant="subtitle2">Trigger Type</Typography>
            </MenuItem>
            <MenuItem
              selected={triggerTypeFilter === 'all'}
              onClick={() => handleApplyFilter('triggerType', 'all')}
            >
              All Types
            </MenuItem>
            {Object.entries(TRIGGER_TYPE_LABELS).map(([value, label]) => (
              <MenuItem
                key={value}
                selected={triggerTypeFilter === value}
                onClick={() => handleApplyFilter('triggerType', value)}
              >
                {label}
              </MenuItem>
            ))}
          </Menu>
        </Box>
      </Box>
      
      {/* Active Filters */}
      {(activeFilter !== 'all' || triggerTypeFilter !== 'all') && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {activeFilter !== 'all' && (
            <Chip
              label={`Status: ${activeFilter === 'active' ? 'Active' : 'Inactive'}`}
              onDelete={() => handleApplyFilter('active', 'all')}
              color="primary"
              variant="outlined"
            />
          )}
          
          {triggerTypeFilter !== 'all' && (
            <Chip
              label={`Type: ${TRIGGER_TYPE_LABELS[triggerTypeFilter]}`}
              onDelete={() => handleApplyFilter('triggerType', 'all')}
              color="primary"
              variant="outlined"
            />
          )}
        </Box>
      )}
      
      {/* Rules List */}
      <Card>
        <CardContent>
          {filteredRules.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <NotificationsOffIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No notification rules found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {rules.length > 0
                  ? 'Try changing your filters to see more rules'
                  : 'Create your first notification rule to get started'}
              </Typography>
              {rules.length === 0 && (
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={onCreateRule}
                >
                  Create Rule
                </Button>
              )}
            </Box>
          ) : (
            <List>
              {filteredRules.map((rule) => (
                <Fragment key={rule.id}>
                  <ListItem
                    sx={{
                      bgcolor: rule.is_active ? 'transparent' : 'action.hover',
                      borderRadius: 1,
                      my: 1
                    }}
                  >
                    <ListItemIcon>
                      {getTriggerTypeIcon(rule.trigger_type)}
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle1" sx={{ mr: 1 }}>
                            {rule.name}
                          </Typography>
                          {!rule.is_active && (
                            <Chip
                              label="Inactive"
                              size="small"
                              color="default"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" color="text.secondary">
                                <strong>Trigger:</strong> {TRIGGER_TYPE_LABELS[rule.trigger_type]}
                              </Typography>
                            </Grid>
                            
                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" color="text.secondary">
                                <strong>Delivery:</strong> {DELIVERY_METHOD_LABELS[rule.delivery_method]}
                              </Typography>
                            </Grid>
                            
                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" color="text.secondary">
                                <strong>Frequency:</strong> {FREQUENCY_LABELS[rule.frequency]}
                              </Typography>
                            </Grid>
                            
                            {rule.conditions.length > 0 && (
                              <Grid item xs={12}>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                  <strong>Conditions:</strong>
                                </Typography>
                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                  {rule.conditions.map((condition, index) => (
                                    <Chip
                                      key={index}
                                      label={`${condition.field} ${condition.operator} ${condition.value}`}
                                      size="small"
                                      variant="outlined"
                                    />
                                  ))}
                                </Box>
                              </Grid>
                            )}
                          </Grid>
                        </Box>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <Tooltip title={rule.is_active ? 'Deactivate' : 'Activate'}>
                        <Switch
                          edge="end"
                          checked={rule.is_active}
                          onChange={() => handleToggleActive(rule.id, rule.is_active)}
                        />
                      </Tooltip>
                      
                      <IconButton
                        edge="end"
                        onClick={(e) => handleMenuOpen(e, rule.id)}
                      >
                        <MoreIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  {filteredRules.indexOf(rule) < filteredRules.length - 1 && (
                    <Divider variant="inset" component="li" />
                  )}
                </Fragment>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
      
      {/* Rule Actions Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleEditRule(selectedRuleId)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleDeleteRule(selectedRuleId)}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>
      
      {/* Confirm Dialog */}
      <ConfirmDialog
        open={confirmDialogOpen}
        title={confirmDialogTitle}
        message={confirmDialogMessage}
        onClose={handleConfirmDialogClose}
      />
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
NotificationRulesList.propTypes = {
  // Core props
  rules: PropTypes.array.isRequired,
  onRuleUpdated: PropTypes.func.isRequired,
  onRuleDeleted: PropTypes.func.isRequired,
  onCreateRule: PropTypes.func.isRequired,
  onEditRule: PropTypes.func.isRequired,

  // Enhanced props
  onRulesAction: PropTypes.func,
  enableAdvancedFeatures: PropTypes.bool,
  enableAIInsights: PropTypes.bool,
  enableRealTimeUpdates: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

NotificationRulesList.displayName = 'NotificationRulesList';

export default NotificationRulesList;

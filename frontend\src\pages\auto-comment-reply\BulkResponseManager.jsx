// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Checkbox,
  Button,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Tooltip,
  CircularProgress,
  Divider,
  useTheme
} from '@mui/material';
import {
  Search as SearchIcon,

  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Edit as EditIcon,

  Refresh as RefreshIcon,
  CalendarToday as CalendarTodayIcon,
  Sort as SortIcon
} from '@mui/icons-material';
import { useSnackbar } from '../../contexts/SnackbarContext';
import api from '../../api';
import GlassmorphicCard from '../../components/common/GlassmorphicCard';

import { LoadingButton } from '@mui/lab';

const BulkResponseManager = () => {
  const theme = useTheme();
  const { showSuccessNotification, showErrorNotification } = useSnackbar();

  // State for responses
  const [responses, setResponses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selected, setSelected] = useState([]);

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // State for filters
  const [filters, setFilters] = useState({
    status: 'pending',
    platform: '',
    sentiment: '',
    search: ''
  });

  // State for sorting
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState(-1);

  // State for bulk actions
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);
  const [feedbackText, setFeedbackText] = useState('');

  // State for response editing
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentResponse, setCurrentResponse] = useState(null);
  const [editedResponse, setEditedResponse] = useState('');

  // State for approval templates
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState('');

  // State for scheduled approval
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');

  useEffect(() => {
    fetchResponses();
    fetchTemplates();
  }, [fetchResponses]);

  const fetchResponses = useCallback(async () => {
    try {
      setLoading(true);

      const params = {
        status: filters.status,
        limit: rowsPerPage,
        skip: page * rowsPerPage,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      if (filters.platform) params.platform = filters.platform;
      if (filters.sentiment) params.sentiment = filters.sentiment;

      const response = await api.get('/api/auto-comment-reply/responses', { params });
      setResponses(response.data);
    } catch (error) {
      console.error('Error fetching responses:', error);
      showErrorNotification('Failed to load responses');
    } finally {
      setLoading(false);
    }
  }, [filters.status, filters.platform, filters.sentiment, rowsPerPage, page, sortBy, sortOrder, showErrorNotification]);

  const fetchTemplates = async () => {
    try {
      const response = await api.get('/api/auto-comment-reply/templates');
      setTemplates(response.data);
    } catch (error) {
      console.error('Error fetching templates:', error);
    }
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = responses.map((response) => response.id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleSelectClick = (_, id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, id];
    } else {
      newSelected = selected.filter((selectedId) => selectedId !== id);
    }

    setSelected(newSelected);
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    setFilters({
      ...filters,
      [name]: value
    });
    setPage(0);
  };

  const handleSortChange = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 1 ? -1 : 1);
    } else {
      setSortBy(field);
      setSortOrder(-1);
    }
  };

  const handleBulkApprove = () => {
    setConfirmAction('approve');
    setConfirmDialogOpen(true);
  };

  const handleBulkReject = () => {
    setConfirmAction('reject');
    setConfirmDialogOpen(true);
  };

  const handleConfirmBulkAction = async () => {
    try {
      setBulkActionLoading(true);

      if (confirmAction === 'approve') {
        const response = await api.post('/api/auto-comment-reply/responses/bulk-approve', {
          response_ids: selected
        });

        showSuccessNotification(`Successfully approved ${response.data.approved} responses`);
      } else if (confirmAction === 'reject') {
        const response = await api.post('/api/auto-comment-reply/responses/bulk-reject', {
          response_ids: selected,
          feedback_text: feedbackText
        });

        showSuccessNotification(`Successfully rejected ${response.data.rejected} responses`);
      }

      setSelected([]);
      fetchResponses();
    } catch (error) {
      console.error(`Error performing bulk ${confirmAction}:`, error);
      showErrorNotification(`Failed to ${confirmAction} responses`);
    } finally {
      setBulkActionLoading(false);
      setConfirmDialogOpen(false);
      setFeedbackText('');
    }
  };

  const handleOpenEditDialog = (response) => {
    setCurrentResponse(response);
    setEditedResponse(response.generated_response);
    setEditDialogOpen(true);
  };

  const handleApproveWithEdits = async () => {
    try {
      await api.post(`/api/auto-comment-reply/responses/${currentResponse.id}/approve`, null, {
        params: {
          apply_edits: true,
          edited_response: editedResponse
        }
      });

      showSuccessNotification('Response approved with edits');
      fetchResponses();
    } catch (error) {
      console.error('Error approving response with edits:', error);
      showErrorNotification('Failed to approve response with edits');
    } finally {
      setEditDialogOpen(false);
      setCurrentResponse(null);
      setEditedResponse('');
    }
  };

  const handleApplyTemplate = () => {
    if (!selectedTemplate || !templates.length) return;

    const template = templates.find(t => t.id === selectedTemplate);
    if (template) {
      setEditedResponse(template.template_text);
    }
  };

  const handleScheduleApproval = () => {
    setScheduleDialogOpen(true);
  };

  const handleConfirmSchedule = () => {
    // This would typically call an API to schedule the approval
    // For now, we'll just show a notification
    showSuccessNotification(`Scheduled approval for ${scheduledDate} at ${scheduledTime}`);
    setScheduleDialogOpen(false);
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  const getSentimentColor = (sentiment) => {
    switch (sentiment) {
      case 'very_positive':
        return theme.palette.success.dark;
      case 'positive':
        return theme.palette.success.main;
      case 'neutral':
        return theme.palette.grey[500];
      case 'negative':
        return theme.palette.error.main;
      case 'very_negative':
        return theme.palette.error.dark;
      default:
        return theme.palette.grey[500];
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Bulk Response Management</Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchResponses}
            disabled={loading}
          >
            Refresh
          </Button>

          <Button
            variant="outlined"
            startIcon={<CalendarTodayIcon />}
            onClick={handleScheduleApproval}
            disabled={selected.length === 0}
          >
            Schedule Approval
          </Button>

          <Button
            variant="contained"
            color="error"
            startIcon={<CancelIcon />}
            onClick={handleBulkReject}
            disabled={selected.length === 0}
          >
            Reject Selected
          </Button>

          <Button
            variant="contained"
            color="primary"
            startIcon={<CheckCircleIcon />}
            onClick={handleBulkApprove}
            disabled={selected.length === 0}
          >
            Approve Selected
          </Button>
        </Box>
      </Box>

      <GlassmorphicCard>
        <Box sx={{ p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={3}>
              <TextField
                fullWidth
                placeholder="Search comments..."
                variant="outlined"
                size="small"
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  )
                }}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  label="Status"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="approved">Approved</MenuItem>
                  <MenuItem value="rejected">Rejected</MenuItem>
                  <MenuItem value="published">Published</MenuItem>
                  <MenuItem value="edited">Edited</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Platform</InputLabel>
                <Select
                  name="platform"
                  value={filters.platform}
                  onChange={handleFilterChange}
                  label="Platform"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="twitter">Twitter/X</MenuItem>
                  <MenuItem value="facebook">Facebook</MenuItem>
                  <MenuItem value="instagram">Instagram</MenuItem>
                  <MenuItem value="linkedin">LinkedIn</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Sentiment</InputLabel>
                <Select
                  name="sentiment"
                  value={filters.sentiment}
                  onChange={handleFilterChange}
                  label="Sentiment"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="very_positive">Very Positive</MenuItem>
                  <MenuItem value="positive">Positive</MenuItem>
                  <MenuItem value="neutral">Neutral</MenuItem>
                  <MenuItem value="negative">Negative</MenuItem>
                  <MenuItem value="very_negative">Very Negative</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        <Divider />

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selected.length > 0 && selected.length < responses.length}
                    checked={responses.length > 0 && selected.length === responses.length}
                    onChange={handleSelectAllClick}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    Comment
                    <IconButton size="small" onClick={() => handleSortChange('comment_text')}>
                      <SortIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    Generated Response
                    <IconButton size="small" onClick={() => handleSortChange('generated_response')}>
                      <SortIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    Platform
                    <IconButton size="small" onClick={() => handleSortChange('platform')}>
                      <SortIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    Sentiment
                    <IconButton size="small" onClick={() => handleSortChange('comment_sentiment')}>
                      <SortIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    Date
                    <IconButton size="small" onClick={() => handleSortChange('created_at')}>
                      <SortIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : responses.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1">No responses found</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                responses.map((response) => {
                  const isItemSelected = isSelected(response.id);

                  return (
                    <TableRow
                      hover
                      role="checkbox"
                      aria-checked={isItemSelected}
                      tabIndex={-1}
                      key={response.id}
                      selected={isItemSelected}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={isItemSelected}
                          onClick={(event) => handleSelectClick(event, response.id)}
                        />
                      </TableCell>
                      <TableCell sx={{ maxWidth: 200 }}>
                        <Tooltip title={response.comment_text}>
                          <Typography variant="body2" noWrap>
                            {response.comment_text}
                          </Typography>
                        </Tooltip>
                        <Typography variant="caption" color="text.secondary">
                          By: {response.comment_author}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ maxWidth: 200 }}>
                        <Tooltip title={response.generated_response}>
                          <Typography variant="body2" noWrap>
                            {response.generated_response}
                          </Typography>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={response.platform}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={response.comment_sentiment.replace('_', ' ')}
                          size="small"
                          sx={{
                            bgcolor: getSentimentColor(response.comment_sentiment),
                            color: 'white'
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        {new Date(response.created_at).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex' }}>
                          <Tooltip title="Edit & Approve">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenEditDialog(response)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Approve">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={async () => {
                                try {
                                  await api.post(`/api/auto-comment-reply/responses/${response.id}/approve`);
                                  showSuccessNotification('Response approved');
                                  fetchResponses();
                                } catch {
                                  showErrorNotification('Failed to approve response');
                                }
                              }}
                            >
                              <CheckCircleIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Reject">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={async () => {
                                try {
                                  await api.post(`/api/auto-comment-reply/responses/${response.id}/reject`);
                                  showSuccessNotification('Response rejected');
                                  fetchResponses();
                                } catch {
                                  showErrorNotification('Failed to reject response');
                                }
                              }}
                            >
                              <CancelIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={-1}  // We don't know the total count, so we use -1
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </GlassmorphicCard>

      {/* Confirm Dialog */}
      <Dialog open={confirmDialogOpen} onClose={() => setConfirmDialogOpen(false)}>
        <DialogTitle>
          {confirmAction === 'approve' ? 'Approve Selected Responses' : 'Reject Selected Responses'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {confirmAction === 'approve'
              ? `Are you sure you want to approve ${selected.length} selected responses?`
              : `Are you sure you want to reject ${selected.length} selected responses?`}
          </DialogContentText>

          {confirmAction === 'reject' && (
            <TextField
              fullWidth
              label="Feedback (Optional)"
              multiline
              rows={3}
              value={feedbackText}
              onChange={(e) => setFeedbackText(e.target.value)}
              margin="normal"
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>Cancel</Button>
          <LoadingButton
            variant="contained"
            color={confirmAction === 'approve' ? 'primary' : 'error'}
            onClick={handleConfirmBulkAction}
            loading={bulkActionLoading}
          >
            {confirmAction === 'approve' ? 'Approve' : 'Reject'}
          </LoadingButton>
        </DialogActions>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Response</DialogTitle>
        <DialogContent>
          {currentResponse && (
            <>
              <Typography variant="subtitle1" gutterBottom>
                Original Comment:
              </Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                <Typography variant="body2">
                  {currentResponse.comment_text}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                    By: {currentResponse.comment_author}
                  </Typography>
                  <Chip
                    label={currentResponse.comment_sentiment.replace('_', ' ')}
                    size="small"
                    sx={{
                      bgcolor: getSentimentColor(currentResponse.comment_sentiment),
                      color: 'white'
                    }}
                  />
                </Box>
              </Paper>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1" sx={{ mr: 2 }}>
                  Apply Template:
                </Typography>
                <FormControl sx={{ minWidth: 200 }}>
                  <Select
                    value={selectedTemplate}
                    onChange={(e) => setSelectedTemplate(e.target.value)}
                    displayEmpty
                    size="small"
                  >
                    <MenuItem value="">
                      <em>Select a template</em>
                    </MenuItem>
                    {templates.map((template) => (
                      <MenuItem key={template.id} value={template.id}>
                        {template.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleApplyTemplate}
                  disabled={!selectedTemplate}
                  sx={{ ml: 1 }}
                >
                  Apply
                </Button>
              </Box>

              <TextField
                fullWidth
                label="Edit Response"
                multiline
                rows={4}
                value={editedResponse}
                onChange={(e) => setEditedResponse(e.target.value)}
                margin="normal"
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Platform: {currentResponse.platform}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Character Count: {editedResponse.length}
                </Typography>
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleApproveWithEdits}
            disabled={!editedResponse.trim()}
          >
            Approve with Edits
          </Button>
        </DialogActions>
      </Dialog>

      {/* Schedule Dialog */}
      <Dialog open={scheduleDialogOpen} onClose={() => setScheduleDialogOpen(false)}>
        <DialogTitle>Schedule Approval</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Schedule approval for {selected.length} selected responses.
          </DialogContentText>

          <TextField
            fullWidth
            label="Date"
            type="date"
            value={scheduledDate}
            onChange={(e) => setScheduledDate(e.target.value)}
            InputLabelProps={{ shrink: true }}
            margin="normal"
          />

          <TextField
            fullWidth
            label="Time"
            type="time"
            value={scheduledTime}
            onChange={(e) => setScheduledTime(e.target.value)}
            InputLabelProps={{ shrink: true }}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setScheduleDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleConfirmSchedule}
            disabled={!scheduledDate || !scheduledTime}
          >
            Schedule
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BulkResponseManager;

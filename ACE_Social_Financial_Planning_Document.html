<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACE Social Media Platform - 5-Year Financial Planning & Add-on Strategy</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        :root {
            --ace-dark: #15110E;
            --ace-purple: #4E40C5;
            --ace-yellow: #EBAE1B;
            --ace-white: #FFFFFF;
            --ace-gray: #F5F5F5;
            --ace-light-purple: rgba(78, 64, 197, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--ace-dark);
            background: linear-gradient(135deg, var(--ace-gray) 0%, var(--ace-white) 100%);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--ace-dark) 0%, var(--ace-purple) 100%);
            color: var(--ace-white);
            padding: 40px 20px;
            text-align: center;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: var(--ace-white);
            margin-bottom: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .section-header {
            background: var(--ace-light-purple);
            padding: 20px;
            border-bottom: 3px solid var(--ace-purple);
        }

        .section-header h2 {
            color: var(--ace-purple);
            font-size: 1.8rem;
            font-weight: 600;
        }

        .section-content {
            padding: 30px;
        }

        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .plan-card {
            border: 2px solid var(--ace-gray);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(78, 64, 197, 0.2);
        }

        .plan-card.featured {
            border-color: var(--ace-purple);
            background: var(--ace-light-purple);
        }

        .plan-card.featured::before {
            content: "MOST POPULAR";
            position: absolute;
            top: 15px;
            right: -30px;
            background: var(--ace-yellow);
            color: var(--ace-dark);
            padding: 5px 40px;
            font-size: 0.8rem;
            font-weight: bold;
            transform: rotate(45deg);
        }

        .plan-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--ace-purple);
            margin-bottom: 10px;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 800;
            color: var(--ace-dark);
            margin-bottom: 5px;
        }

        .plan-price span {
            font-size: 1rem;
            color: #666;
        }

        .plan-description {
            color: #666;
            margin-bottom: 25px;
            font-style: italic;
        }

        .feature-list {
            text-align: left;
            margin-bottom: 25px;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid var(--ace-gray);
            display: flex;
            align-items: center;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✓";
            color: var(--ace-purple);
            font-weight: bold;
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .ai-features {
            background: var(--ace-light-purple);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .ai-features h4 {
            color: var(--ace-purple);
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .financial-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: var(--ace-white);
            border: 1px solid var(--ace-gray);
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            border-color: var(--ace-purple);
            box-shadow: 0 5px 15px rgba(78, 64, 197, 0.1);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--ace-purple);
            margin-bottom: 10px;
        }

        .metric-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .table-container {
            overflow-x: auto;
            margin: 25px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: var(--ace-white);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--ace-gray);
        }

        th {
            background: var(--ace-purple);
            color: var(--ace-white);
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9rem;
            letter-spacing: 1px;
        }

        tr:hover {
            background: var(--ace-light-purple);
        }

        .highlight {
            background: var(--ace-yellow);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .chart-placeholder {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 3px dashed var(--ace-purple);
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            margin: 30px 0;
            position: relative;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 15px rgba(78, 64, 197, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .chart-placeholder:hover {
            border-color: var(--ace-yellow);
            box-shadow: 0 6px 25px rgba(78, 64, 197, 0.15);
            transform: translateY(-2px);
        }

        .chart-placeholder::before {
            content: "📊";
            font-size: 4rem;
            display: block;
            margin-bottom: 15px;
            opacity: 0.6;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 0.9; }
        }

        .chart-placeholder .chart-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: var(--ace-purple);
            margin-bottom: 10px;
            font-style: normal;
        }

        .chart-placeholder .chart-description {
            font-size: 1rem;
            color: #666;
            margin-bottom: 8px;
            font-style: normal;
        }

        .chart-placeholder .chart-details {
            font-size: 0.9rem;
            color: #888;
            font-style: italic;
            opacity: 0.8;
        }

        .growth-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .scenario-card {
            background: var(--ace-white);
            border: 2px solid var(--ace-gray);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .scenario-card.conservative {
            border-color: #28a745;
        }

        .scenario-card.moderate {
            border-color: var(--ace-yellow);
        }

        .scenario-card.aggressive {
            border-color: #dc3545;
        }

        .cta-section {
            background: linear-gradient(135deg, var(--ace-purple) 0%, var(--ace-dark) 100%);
            color: var(--ace-white);
            padding: 40px;
            text-align: center;
            border-radius: 15px;
            margin-top: 40px;
        }

        .cta-button {
            background: var(--ace-yellow);
            color: var(--ace-dark);
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(235, 174, 27, 0.4);
        }

        @media (max-width: 768px) {
            .plans-grid {
                grid-template-columns: 1fr;
            }
            
            .financial-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .plan-price {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>ACE Social Media Platform</h1>
            <p>5-Year Financial Planning & Complete Add-on Strategy Document</p>
            <p style="font-size: 1rem; margin-top: 10px; opacity: 0.8;">AI-Powered Social Media Management | Enterprise-Grade Analytics | Subscription-Based Revenue Model</p>
        </div>

        <!-- Executive Summary -->
        <div class="section">
            <div class="section-header">
                <h2>📊 Executive Summary</h2>
            </div>
            <div class="section-content">
                <div class="financial-grid">
                    <div class="metric-card">
                        <div class="metric-value">$2.8M</div>
                        <div class="metric-label">Projected Year 1 Revenue</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">72%</div>
                        <div class="metric-label">Average Gross Margin</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">16 mo</div>
                        <div class="metric-label">Break-even Timeline</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">$542</div>
                        <div class="metric-label">Average Customer LTV</div>
                    </div>
                </div>
                
                <p style="font-size: 1.1rem; line-height: 1.8; color: #555;">
                    ACE Social Media Platform leverages cutting-edge AI technology to provide comprehensive social media management solutions.
                    Our three-tier subscription model (Creator $19, Accelerator $99, Dominator $199) is strategically designed to maximize
                    customer lifetime value while maintaining healthy profit margins through intelligent regeneration credit management and
                    feature-based upselling strategies.
                </p>
            </div>
        </div>

        <!-- Detailed Plan Structure -->
        <div class="section">
            <div class="section-header">
                <h2>💎 Subscription Plan Structure & AI Integration</h2>
            </div>
            <div class="section-content">
                <div class="plans-grid">
                    <!-- Creator Plan -->
                    <div class="plan-card">
                        <div class="plan-title">Creator Plan</div>
                        <div class="plan-price">$19<span>/month</span></div>
                        <div class="plan-description">Essential tools for growing content creators</div>

                        <div class="ai-features">
                            <h4>🤖 AI-Powered Features</h4>
                            <ul class="feature-list" style="font-size: 0.9rem;">
                                <li>50 posts/month with AI generation</li>
                                <li>20 AI images/month (basic quality)</li>
                                <li>25 regeneration credits/month</li>
                                <li>200 AI auto replies/month</li>
                                <li>500 sentiment comments analysis</li>
                                <li>3 document training sessions</li>
                            </ul>
                        </div>

                        <ul class="feature-list">
                            <li>3 social platforms</li>
                            <li>25 advanced templates</li>
                            <li>Auto scheduling & optimal timing</li>
                            <li>30-day analytics history</li>
                            <li>3 ICPs & 3 brand profiles</li>
                            <li>2 user accounts</li>
                            <li>Email support</li>
                        </ul>

                        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <strong>AI/Regeneration Cost: $8-12/month</strong><br>
                            <span style="color: #28a745; font-weight: 600;">Profit Margin: 58-68%</span>
                        </div>
                    </div>

                    <!-- Accelerator Plan -->
                    <div class="plan-card featured">
                        <div class="plan-title">Accelerator Plan</div>
                        <div class="plan-price">$99<span>/month</span></div>
                        <div class="plan-description">Professional tools for marketing agencies and growing businesses</div>

                        <div class="ai-features">
                            <h4>🚀 Advanced AI Features</h4>
                            <ul class="feature-list" style="font-size: 0.9rem;">
                                <li>200 posts/month with AI generation</li>
                                <li>100 premium AI images/month</li>
                                <li>100 regeneration credits/month</li>
                                <li>1,000 AI auto replies/month</li>
                                <li>1,500 sentiment comments analysis</li>
                                <li>A/B testing (3 variants)</li>
                                <li>10 document training sessions</li>
                                <li>Policy compliance checking</li>
                            </ul>
                        </div>

                        <ul class="feature-list">
                            <li>10 social platforms</li>
                            <li>50 advanced templates</li>
                            <li>Team collaboration (5 users)</li>
                            <li>90-day analytics history</li>
                            <li>10 ICPs & 10 brand profiles</li>
                            <li>Advanced AI response customization</li>
                            <li>Priority support</li>
                        </ul>

                        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <strong>AI/Regeneration Cost: $28-38/month</strong><br>
                            <span style="color: #28a745; font-weight: 600;">Profit Margin: 62-72%</span>
                        </div>
                    </div>

                    <!-- Dominator Plan -->
                    <div class="plan-card">
                        <div class="plan-title">Dominator Plan</div>
                        <div class="plan-price">$199<span>/month</span></div>
                        <div class="plan-description">Enterprise-grade solution for agencies and large businesses</div>

                        <div class="ai-features">
                            <h4>👑 Premium AI Suite</h4>
                            <ul class="feature-list" style="font-size: 0.9rem;">
                                <li>Unlimited posts with AI generation</li>
                                <li>Unlimited premium AI images</li>
                                <li>Unlimited regeneration credits</li>
                                <li>Unlimited AI auto replies</li>
                                <li>Unlimited sentiment analysis</li>
                                <li>A/B testing (unlimited variants)</li>
                                <li>Unlimited document training</li>
                                <li>Advanced policy compliance</li>
                                <li>Custom AI model training</li>
                            </ul>
                        </div>

                        <ul class="feature-list">
                            <li>Unlimited social platforms</li>
                            <li>Unlimited advanced templates</li>
                            <li>Unlimited team collaboration</li>
                            <li>Unlimited analytics history</li>
                            <li>Unlimited ICPs & brand profiles</li>
                            <li>White-label options</li>
                            <li>API access & integrations</li>
                            <li>Dedicated success manager</li>
                        </ul>

                        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <strong>AI/Regeneration Cost: $65-85/month</strong><br>
                            <span style="color: #28a745; font-weight: 600;">Profit Margin: 57-67%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ACEO Add-on Strategy -->
        <div class="section">
            <div class="section-header">
                <h2>� Complete ACEO Add-on Marketplace Strategy</h2>
            </div>
            <div class="section-content">
                <p style="font-size: 1.1rem; margin-bottom: 30px; line-height: 1.6;">
                    Our comprehensive add-on ecosystem creates multiple revenue streams across 6 categories, providing immediate value and measurable ROI while maintaining plan simplicity.
                </p>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 40px;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">Add-on Revenue Distribution by Category</h4>
                    <canvas id="addonRevenueChart" style="max-height: 300px;"></canvas>
                </div>

                <div class="financial-grid">
                    <div class="metric-card">
                        <div class="metric-value">$0.75</div>
                        <div class="metric-label">Per Regeneration Credit</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">$6.99</div>
                        <div class="metric-label">10-Credit Booster Pack</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">18%</div>
                        <div class="metric-label">Target Conversion Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">45%</div>
                        <div class="metric-label">Revenue Increase Target</div>
                    </div>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Add-on Service</th>
                                <th>Creator Plan</th>
                                <th>Accelerator Plan</th>
                                <th>Dominator Plan</th>
                                <th>Profit Margin</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Regeneration Credits</strong></td>
                                <td>$0.75/credit</td>
                                <td>$0.75/credit</td>
                                <td>Unlimited</td>
                                <td>88%</td>
                            </tr>
                            <tr>
                                <td><strong>E-commerce Integration</strong></td>
                                <td>$49/month</td>
                                <td>$49/month</td>
                                <td>$49/month</td>
                                <td>85%</td>
                            </tr>
                            <tr>
                                <td><strong>Advanced Analytics Pack</strong></td>
                                <td>$29/month</td>
                                <td>$29/month</td>
                                <td>Included</td>
                                <td>90%</td>
                            </tr>
                            <tr>
                                <td><strong>Priority Support</strong></td>
                                <td>$39/month</td>
                                <td>$39/month</td>
                                <td>Included</td>
                                <td>95%</td>
                            </tr>
                            <tr>
                                <td><strong>Custom Integrations</strong></td>
                                <td>$500 setup</td>
                                <td>$300 setup</td>
                                <td>$200 setup</td>
                                <td>80%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3 style="color: var(--ace-purple); margin: 30px 0 20px 0;">Mid-Cycle Purchase Strategy</h3>
                <div style="background: var(--ace-light-purple); padding: 25px; border-radius: 10px;">
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 15px; padding-left: 25px; position: relative;">
                            <span style="position: absolute; left: 0; color: var(--ace-purple); font-weight: bold;">💡</span>
                            <strong>Smart Depletion Detection:</strong> Automated alerts when users approach 80% of their monthly AI credits
                        </li>
                        <li style="margin-bottom: 15px; padding-left: 25px; position: relative;">
                            <span style="position: absolute; left: 0; color: var(--ace-purple); font-weight: bold;">🛒</span>
                            <strong>One-Click Purchase Flow:</strong> Seamless credit top-ups integrated with Lemon Squeezy billing
                        </li>
                        <li style="margin-bottom: 15px; padding-left: 25px; position: relative;">
                            <span style="position: absolute; left: 0; color: var(--ace-purple); font-weight: bold;">📊</span>
                            <strong>Usage Analytics:</strong> Real-time consumption tracking with predictive usage patterns
                        </li>
                        <li style="margin-bottom: 15px; padding-left: 25px; position: relative;">
                            <span style="position: absolute; left: 0; color: var(--ace-purple); font-weight: bold;">🎯</span>
                            <strong>Targeted Upsells:</strong> Plan upgrade suggestions based on consistent add-on purchases
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Complete Add-ons Catalog -->
        <div class="section">
            <div class="section-header">
                <h2>🛍️ Complete Add-ons Catalog & Pricing</h2>
            </div>
            <div class="section-content">
                <p style="font-size: 1.1rem; margin-bottom: 30px; line-height: 1.6;">
                    Our comprehensive add-on marketplace offers 12+ premium features across 6 categories,
                    enabling users to customize their experience and unlock advanced capabilities.
                </p>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 40px;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">Add-on Adoption Rates by Category & Plan</h4>
                    <canvas id="addonAdoptionChart" style="max-height: 250px;"></canvas>
                </div>

                <h3 style="color: var(--ace-purple); margin-bottom: 25px;">Content Enhancement Add-ons</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Add-on</th>
                                <th>Description</th>
                                <th>Pricing</th>
                                <th>Required Plan</th>
                                <th>Conversion Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Extra Regeneration Credits</strong></td>
                                <td>Boost content optimization with additional regeneration credits</td>
                                <td>$9.99 (100 credits)<br>$19.99 (250 credits)<br>$39.99 (500 credits)</td>
                                <td>Creator+</td>
                                <td>22%</td>
                            </tr>
                            <tr>
                                <td><strong>Premium Image Generation Pack</strong></td>
                                <td>Advanced DALL-E credits with premium styles and custom dimensions</td>
                                <td>$15.99 (50 images)<br>$29.99 (120 images)<br>$59.99 (300 images)</td>
                                <td>Creator+</td>
                                <td>18%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">AI Features Add-ons</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Add-on</th>
                                <th>Description</th>
                                <th>Pricing</th>
                                <th>Required Plan</th>
                                <th>Conversion Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Advanced Sentiment Analysis</strong></td>
                                <td>Emotion detection, intent categorization, and trend analysis</td>
                                <td>$19.99 (1,000 analyses)<br>$39.99 (2,500 analyses)</td>
                                <td>Accelerator+</td>
                                <td>12%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">E-commerce Integration Add-ons</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Add-on</th>
                                <th>Description</th>
                                <th>Pricing</th>
                                <th>Required Plan</th>
                                <th>Conversion Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>E-commerce Store Connections</strong></td>
                                <td>Connect multiple stores (Shopify, WooCommerce) with product sync</td>
                                <td>$19.99 (3 stores)<br>$49.99 (10 stores)<br>$99.99 (25 stores)</td>
                                <td>Creator+</td>
                                <td>15%</td>
                            </tr>
                            <tr>
                                <td><strong>Product Content Generation</strong></td>
                                <td>AI-powered content generation for e-commerce products</td>
                                <td>$19.99 (50 products)<br>$29.99 (100 products)<br>$59.99 (250 products)<br>$99.99 (500 products)</td>
                                <td>Creator+</td>
                                <td>18%</td>
                            </tr>
                            <tr>
                                <td><strong>E-commerce ICP Generation</strong></td>
                                <td>Generate customer personas based on product data</td>
                                <td>$24.99 (25 ICPs)<br>$39.99 (50 ICPs)<br>$69.99 (100 ICPs)</td>
                                <td>Creator+</td>
                                <td>10%</td>
                            </tr>
                            <tr>
                                <td><strong>E-commerce Campaign Management</strong></td>
                                <td>Advanced campaign management for product promotion</td>
                                <td>$29.99 (10 campaigns)<br>$49.99 (25 campaigns)<br>$89.99 (50 campaigns)</td>
                                <td>Accelerator+</td>
                                <td>8%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">Team Collaboration & Platform Add-ons</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Add-on</th>
                                <th>Description</th>
                                <th>Pricing</th>
                                <th>Required Plan</th>
                                <th>Conversion Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Additional Team Seats</strong></td>
                                <td>Expand team with additional user accounts and collaboration tools</td>
                                <td>$15.00/seat/month</td>
                                <td>Creator+</td>
                                <td>25%</td>
                            </tr>
                            <tr>
                                <td><strong>White Label Platform</strong></td>
                                <td>Custom branding, domain, and white-labeled experience</td>
                                <td>$99.99/month<br>$999.99/year</td>
                                <td>Dominator</td>
                                <td>5%</td>
                            </tr>
                            <tr>
                                <td><strong>Priority Support</strong></td>
                                <td>24/7 support, dedicated account manager, phone support</td>
                                <td>$49.99/month</td>
                                <td>Accelerator+</td>
                                <td>8%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 40px 0;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">5-Year Add-on Revenue Growth by Category</h4>
                    <canvas id="addonRevenueProjectionChart" style="max-height: 300px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Financial Projections -->
        <div class="section">
            <div class="section-header">
                <h2>📈 5-Year Financial Projections & Revenue Analysis</h2>
            </div>
            <div class="section-content">
                <h3 style="color: var(--ace-purple); margin-bottom: 25px;">5-Year Revenue Growth Trajectory</h3>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 40px;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">5-Year Revenue Growth Trajectory</h4>
                    <canvas id="revenueGrowthChart" style="max-height: 400px;"></canvas>
                </div>

                <h3 style="color: var(--ace-purple); margin-bottom: 25px;">Year 1 Monthly Recurring Revenue (MRR) Projections</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Month</th>
                                <th>Creator Users</th>
                                <th>Accelerator Users</th>
                                <th>Dominator Users</th>
                                <th>Total MRR</th>
                                <th>AI/Regen Costs</th>
                                <th>Net Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Month 1</strong></td>
                                <td>150 ($2,850)</td>
                                <td>45 ($4,455)</td>
                                <td>8 ($1,592)</td>
                                <td class="highlight">$8,897</td>
                                <td>$2,680</td>
                                <td>$6,217</td>
                            </tr>
                            <tr>
                                <td><strong>Month 3</strong></td>
                                <td>420 ($7,980)</td>
                                <td>165 ($16,335)</td>
                                <td>35 ($6,965)</td>
                                <td class="highlight">$31,280</td>
                                <td>$9,850</td>
                                <td>$21,430</td>
                            </tr>
                            <tr>
                                <td><strong>Month 6</strong></td>
                                <td>850 ($16,150)</td>
                                <td>380 ($37,620)</td>
                                <td>85 ($16,915)</td>
                                <td class="highlight">$70,685</td>
                                <td>$22,400</td>
                                <td>$44,785</td>
                            </tr>
                            <tr>
                                <td><strong>Month 12</strong></td>
                                <td>1,650 ($31,350)</td>
                                <td>820 ($81,180)</td>
                                <td>180 ($35,820)</td>
                                <td class="highlight">$148,350</td>
                                <td>$48,600</td>
                                <td>$99,750</td>
                            </tr>
                            <tr style="background: var(--ace-light-purple);">
                                <td><strong>Year 1 Total</strong></td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                                <td class="highlight">$1,385,000</td>
                                <td>$385,000</td>
                                <td><strong>$1,000,000</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">Year 1 Monthly Recurring Revenue by Plan</h4>
                    <canvas id="mrrGrowthChart" style="max-height: 300px;"></canvas>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">5-Year Financial Summary</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Year</th>
                                <th>Total Users</th>
                                <th>Annual Revenue</th>
                                <th>Add-on Revenue</th>
                                <th>Total Revenue</th>
                                <th>Net Profit</th>
                                <th>Profit Margin</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Year 1</strong></td>
                                <td>2,650</td>
                                <td>$1,385,000</td>
                                <td>$485,000</td>
                                <td class="highlight">$1,870,000</td>
                                <td>$1,000,000</td>
                                <td>53%</td>
                            </tr>
                            <tr>
                                <td><strong>Year 2</strong></td>
                                <td>8,200</td>
                                <td>$4,250,000</td>
                                <td>$1,680,000</td>
                                <td class="highlight">$5,930,000</td>
                                <td>$3,850,000</td>
                                <td>65%</td>
                            </tr>
                            <tr>
                                <td><strong>Year 3</strong></td>
                                <td>18,500</td>
                                <td>$9,800,000</td>
                                <td>$4,200,000</td>
                                <td class="highlight">$14,000,000</td>
                                <td>$9,800,000</td>
                                <td>70%</td>
                            </tr>
                            <tr>
                                <td><strong>Year 4</strong></td>
                                <td>35,000</td>
                                <td>$18,500,000</td>
                                <td>$8,500,000</td>
                                <td class="highlight">$27,000,000</td>
                                <td>$20,250,000</td>
                                <td>75%</td>
                            </tr>
                            <tr style="background: var(--ace-light-purple);">
                                <td><strong>Year 5</strong></td>
                                <td>58,000</td>
                                <td>$32,000,000</td>
                                <td>$16,000,000</td>
                                <td class="highlight">$48,000,000</td>
                                <td>$38,400,000</td>
                                <td><strong>80%</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 30px 0;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">5-Year Profit Margin Evolution & Break-even Analysis</h4>
                    <canvas id="profitMarginChart" style="max-height: 350px;"></canvas>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">5-Year User Growth Breakdown</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Year</th>
                                <th>Creator Users</th>
                                <th>Accelerator Users</th>
                                <th>Dominator Users</th>
                                <th>Churn Rate</th>
                                <th>Add-on Adoption</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Year 1</strong></td>
                                <td>1,650 (62%)</td>
                                <td>820 (31%)</td>
                                <td>180 (7%)</td>
                                <td>5.2%</td>
                                <td>28%</td>
                            </tr>
                            <tr>
                                <td><strong>Year 2</strong></td>
                                <td>4,920 (60%)</td>
                                <td>2,870 (35%)</td>
                                <td>410 (5%)</td>
                                <td>4.8%</td>
                                <td>35%</td>
                            </tr>
                            <tr>
                                <td><strong>Year 3</strong></td>
                                <td>10,175 (55%)</td>
                                <td>6,845 (37%)</td>
                                <td>1,480 (8%)</td>
                                <td>4.2%</td>
                                <td>42%</td>
                            </tr>
                            <tr>
                                <td><strong>Year 4</strong></td>
                                <td>17,500 (50%)</td>
                                <td>14,000 (40%)</td>
                                <td>3,500 (10%)</td>
                                <td>3.8%</td>
                                <td>48%</td>
                            </tr>
                            <tr style="background: var(--ace-light-purple);">
                                <td><strong>Year 5</strong></td>
                                <td>26,100 (45%)</td>
                                <td>23,200 (40%)</td>
                                <td>8,700 (15%)</td>
                                <td>3.2%</td>
                                <td><strong>55%</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 30px 0;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">5-Year User Distribution Evolution Across Plans</h4>
                    <canvas id="userDistributionChart" style="max-height: 300px;"></canvas>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">Customer Acquisition & Retention Metrics</h3>

                <div class="financial-grid">
                    <div class="metric-card">
                        <div class="metric-value">$45</div>
                        <div class="metric-label">Customer Acquisition Cost (CAC)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">$542</div>
                        <div class="metric-label">Customer Lifetime Value (CLV)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">12.0:1</div>
                        <div class="metric-label">CLV:CAC Ratio</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">3.8 mo</div>
                        <div class="metric-label">Payback Period</div>
                    </div>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Plan Tier</th>
                                <th>Monthly Churn Rate</th>
                                <th>Average LTV</th>
                                <th>Upgrade Rate</th>
                                <th>Add-on Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Creator</strong></td>
                                <td>8.5%</td>
                                <td>$348</td>
                                <td>25% → Accelerator</td>
                                <td>$8.50/month avg</td>
                            </tr>
                            <tr>
                                <td><strong>Accelerator</strong></td>
                                <td>5.2%</td>
                                <td>$1,518</td>
                                <td>15% → Dominator</td>
                                <td>$18.20/month avg</td>
                            </tr>
                            <tr>
                                <td><strong>Dominator</strong></td>
                                <td>3.1%</td>
                                <td>$6,432</td>
                                <td>-</td>
                                <td>$45.80/month avg</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Growth Scenarios -->
        <div class="section">
            <div class="section-header">
                <h2>🚀 5-Year Growth Models & Scenarios</h2>
            </div>
            <div class="section-content">
                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 40px;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">Growth Scenario Comparison - 5 Year User Projections</h4>
                    <canvas id="growthScenariosChart" style="max-height: 300px;"></canvas>
                </div>

                <div class="growth-scenarios">
                    <div class="scenario-card conservative">
                        <h3 style="color: #28a745; margin-bottom: 15px;">Conservative Growth</h3>
                        <div style="font-size: 1.8rem; font-weight: bold; color: #28a745; margin-bottom: 5px;">35,000</div>
                        <div style="color: #666; margin-bottom: 15px;">Total Users by Year 5</div>
                        <ul style="text-align: left; font-size: 0.9rem;">
                            <li><strong>Year 1:</strong> 2,500 users, $2.1M revenue</li>
                            <li><strong>Year 3:</strong> 12,000 users, $8.5M revenue</li>
                            <li><strong>Year 5:</strong> 35,000 users, $28M revenue</li>
                            <li>Break-even: Month 18</li>
                            <li>Final margin: 75%</li>
                        </ul>
                    </div>

                    <div class="scenario-card moderate">
                        <h3 style="color: #ffc107; margin-bottom: 15px;">Moderate Growth (Base Case)</h3>
                        <div style="font-size: 1.8rem; font-weight: bold; color: #ffc107; margin-bottom: 5px;">58,000</div>
                        <div style="color: #666; margin-bottom: 15px;">Total Users by Year 5</div>
                        <ul style="text-align: left; font-size: 0.9rem;">
                            <li><strong>Year 1:</strong> 2,650 users, $2.8M revenue</li>
                            <li><strong>Year 3:</strong> 18,500 users, $14M revenue</li>
                            <li><strong>Year 5:</strong> 58,000 users, $48M revenue</li>
                            <li>Break-even: Month 16</li>
                            <li>Final margin: 80%</li>
                        </ul>
                    </div>

                    <div class="scenario-card aggressive">
                        <h3 style="color: #dc3545; margin-bottom: 15px;">Aggressive Growth</h3>
                        <div style="font-size: 1.8rem; font-weight: bold; color: #dc3545; margin-bottom: 5px;">95,000</div>
                        <div style="color: #666; margin-bottom: 15px;">Total Users by Year 5</div>
                        <ul style="text-align: left; font-size: 0.9rem;">
                            <li><strong>Year 1:</strong> 7,800 users, $4.2M revenue</li>
                            <li><strong>Year 3:</strong> 32,000 users, $22M revenue</li>
                            <li><strong>Year 5:</strong> 95,000 users, $78M revenue</li>
                            <li>Break-even: Month 14</li>
                            <li>Final margin: 82%</li>
                        </ul>
                    </div>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 30px 0;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">24-Month User Growth Trajectory Comparison</h4>
                    <canvas id="userGrowthTrajectoryChart" style="max-height: 350px;"></canvas>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">Market Penetration Analysis</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Scenario</th>
                                <th>Year 5 Market Share</th>
                                <th>TAM Penetration</th>
                                <th>Revenue Multiple</th>
                                <th>Exit Valuation</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Conservative</strong></td>
                                <td>0.8%</td>
                                <td>2.1%</td>
                                <td>8x</td>
                                <td>$224M</td>
                            </tr>
                            <tr style="background: var(--ace-light-purple);">
                                <td><strong>Moderate (Base)</strong></td>
                                <td>1.3%</td>
                                <td>3.5%</td>
                                <td>10x</td>
                                <td><strong>$480M</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Aggressive</strong></td>
                                <td>2.2%</td>
                                <td>5.7%</td>
                                <td>12x</td>
                                <td>$936M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 30px 0;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">Market Penetration & Competitive Analysis</h4>
                    <canvas id="marketPenetrationChart" style="max-height: 350px;"></canvas>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">Market Penetration Analysis</h3>

                <div style="background: var(--ace-light-purple); padding: 25px; border-radius: 10px; margin-bottom: 30px;">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="text-align: center;">
                            <div style="font-size: 1.8rem; font-weight: bold; color: var(--ace-purple);">28.5M</div>
                            <div style="color: #666; font-size: 0.9rem;">Total Addressable Market</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.8rem; font-weight: bold; color: var(--ace-purple);">4.2M</div>
                            <div style="color: #666; font-size: 0.9rem;">Serviceable Addressable Market</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.8rem; font-weight: bold; color: var(--ace-purple);">850K</div>
                            <div style="color: #666; font-size: 0.9rem;">Serviceable Obtainable Market</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.8rem; font-weight: bold; color: var(--ace-purple);">0.5%</div>
                            <div style="color: #666; font-size: 0.9rem;">Year 1 Market Penetration</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cost Structure Breakdown -->
        <div class="section">
            <div class="section-header">
                <h2>💰 Detailed Cost Structure Analysis</h2>
            </div>
            <div class="section-content">
                <h3 style="color: var(--ace-purple); margin-bottom: 25px;">AI & Regeneration Cost Analysis by Plan</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Cost Component</th>
                                <th>Creator Plan</th>
                                <th>Accelerator Plan</th>
                                <th>Dominator Plan</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>AI Content Generation</strong></td>
                                <td>$4-6/month</td>
                                <td>$12-18/month</td>
                                <td>$35-50/month</td>
                                <td>Based on regeneration credit usage</td>
                            </tr>
                            <tr>
                                <td><strong>AI Image Generation</strong></td>
                                <td>$3-4/month</td>
                                <td>$8-12/month</td>
                                <td>$20-25/month</td>
                                <td>20/100/unlimited images per plan</td>
                            </tr>
                            <tr>
                                <td><strong>AI Analytics & Insights</strong></td>
                                <td>$1-2/month</td>
                                <td>$3-5/month</td>
                                <td>$5-8/month</td>
                                <td>Sentiment analysis & auto-replies</td>
                            </tr>
                            <tr>
                                <td><strong>Document Training & AI</strong></td>
                                <td>$1/month</td>
                                <td>$3-5/month</td>
                                <td>$5-8/month</td>
                                <td>3/10/unlimited training sessions</td>
                            </tr>
                            <tr style="background: var(--ace-light-purple);">
                                <td><strong>Total AI/Regen Costs</strong></td>
                                <td><strong>$9-13/month</strong></td>
                                <td><strong>$26-40/month</strong></td>
                                <td><strong>$65-91/month</strong></td>
                                <td>Includes 15% buffer for peak usage</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">Complete Cost Structure Breakdown</h3>

                <div class="financial-grid">
                    <div class="metric-card">
                        <div class="metric-value">28%</div>
                        <div class="metric-label">AI/Regeneration Costs</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">18%</div>
                        <div class="metric-label">Infrastructure & Hosting</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">25%</div>
                        <div class="metric-label">Development & Maintenance</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">25%</div>
                        <div class="metric-label">Marketing & Acquisition</div>
                    </div>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Cost Category</th>
                                <th>Monthly Cost</th>
                                <th>Annual Cost</th>
                                <th>% of Revenue</th>
                                <th>Scalability</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>AI/Regeneration Costs</strong></td>
                                <td>$16,200</td>
                                <td>$194,400</td>
                                <td>28%</td>
                                <td>Linear with user growth</td>
                            </tr>
                            <tr>
                                <td><strong>AWS Infrastructure</strong></td>
                                <td>$8,200</td>
                                <td>$98,400</td>
                                <td>14%</td>
                                <td>Economies of scale</td>
                            </tr>
                            <tr>
                                <td><strong>Redis & MongoDB</strong></td>
                                <td>$2,800</td>
                                <td>$33,600</td>
                                <td>4%</td>
                                <td>Moderate scaling</td>
                            </tr>
                            <tr>
                                <td><strong>Development Team</strong></td>
                                <td>$12,500</td>
                                <td>$150,000</td>
                                <td>21%</td>
                                <td>Fixed cost initially</td>
                            </tr>
                            <tr>
                                <td><strong>DevOps & Maintenance</strong></td>
                                <td>$3,200</td>
                                <td>$38,400</td>
                                <td>4%</td>
                                <td>Moderate scaling</td>
                            </tr>
                            <tr>
                                <td><strong>Marketing & Ads</strong></td>
                                <td>$8,500</td>
                                <td>$102,000</td>
                                <td>15%</td>
                                <td>Variable based on strategy</td>
                            </tr>
                            <tr>
                                <td><strong>Customer Support</strong></td>
                                <td>$4,200</td>
                                <td>$50,400</td>
                                <td>6%</td>
                                <td>Linear with user growth</td>
                            </tr>
                            <tr>
                                <td><strong>Legal & Compliance</strong></td>
                                <td>$1,800</td>
                                <td>$21,600</td>
                                <td>2%</td>
                                <td>Fixed cost</td>
                            </tr>
                            <tr>
                                <td><strong>Third-party Tools</strong></td>
                                <td>$1,200</td>
                                <td>$14,400</td>
                                <td>2%</td>
                                <td>Moderate scaling</td>
                            </tr>
                            <tr style="background: var(--ace-light-purple);">
                                <td><strong>Total Operating Costs</strong></td>
                                <td><strong>$61,050</strong></td>
                                <td><strong>$732,600</strong></td>
                                <td><strong>100%</strong></td>
                                <td>-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-top: 30px;">
                    <h4 style="color: var(--ace-purple); margin-bottom: 15px;">💡 Cost Optimization Strategies</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
                            <span style="position: absolute; left: 0; color: var(--ace-purple);">🔧</span>
                            <strong>Smart Caching:</strong> Reduces OpenAI API calls by 60-70% through intelligent content caching
                        </li>
                        <li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
                            <span style="position: absolute; left: 0; color: var(--ace-purple);">📊</span>
                            <strong>Usage Analytics:</strong> Real-time monitoring prevents API cost overruns and optimizes resource allocation
                        </li>
                        <li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
                            <span style="position: absolute; left: 0; color: var(--ace-purple);">⚡</span>
                            <strong>Batch Processing:</strong> Combines multiple AI requests to reduce per-request overhead costs
                        </li>
                        <li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
                            <span style="position: absolute; left: 0; color: var(--ace-purple);">🎯</span>
                            <strong>Plan-Based Limits:</strong> Strategic feature gating prevents cost overruns while encouraging upgrades
                        </li>
                    </ul>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">🎛️ Interactive Cost Structure Calculator</h3>

                <div style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 30px 0;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px;">
                        <!-- Cost Controls -->
                        <div>
                            <h4 style="color: var(--ace-purple); margin-bottom: 20px;">Adjust Cost Components</h4>
                            <div style="display: grid; gap: 15px;">
                                <div>
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">AI/Regeneration Costs ($)</label>
                                    <input type="range" id="aiCosts" min="10000" max="30000" value="16200" step="500"
                                           style="width: 100%; margin-bottom: 5px;" oninput="updateCostCalculator()">
                                    <span id="aiCostsValue" style="color: var(--ace-purple); font-weight: bold;">$16,200</span>
                                </div>

                                <div>
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">Infrastructure ($)</label>
                                    <input type="range" id="infraCosts" min="5000" max="20000" value="11000" step="500"
                                           style="width: 100%; margin-bottom: 5px;" oninput="updateCostCalculator()">
                                    <span id="infraCostsValue" style="color: var(--ace-purple); font-weight: bold;">$11,000</span>
                                </div>

                                <div>
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">Development Team ($)</label>
                                    <input type="range" id="devCosts" min="8000" max="25000" value="12500" step="500"
                                           style="width: 100%; margin-bottom: 5px;" oninput="updateCostCalculator()">
                                    <span id="devCostsValue" style="color: var(--ace-purple); font-weight: bold;">$12,500</span>
                                </div>

                                <div>
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">Marketing & Ads ($)</label>
                                    <input type="range" id="marketingCosts" min="3000" max="20000" value="8500" step="500"
                                           style="width: 100%; margin-bottom: 5px;" oninput="updateCostCalculator()">
                                    <span id="marketingCostsValue" style="color: var(--ace-purple); font-weight: bold;">$8,500</span>
                                </div>

                                <div>
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">Support & Operations ($)</label>
                                    <input type="range" id="opsCosts" min="3000" max="15000" value="7200" step="200"
                                           style="width: 100%; margin-bottom: 5px;" oninput="updateCostCalculator()">
                                    <span id="opsCostsValue" style="color: var(--ace-purple); font-weight: bold;">$7,200</span>
                                </div>

                                <div>
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">Legal & Compliance ($)</label>
                                    <input type="range" id="legalCosts" min="1000" max="5000" value="1800" step="200"
                                           style="width: 100%; margin-bottom: 5px;" oninput="updateCostCalculator()">
                                    <span id="legalCostsValue" style="color: var(--ace-purple); font-weight: bold;">$1,800</span>
                                </div>

                                <div>
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">Monthly Revenue ($)</label>
                                    <input type="range" id="monthlyRevenue" min="100000" max="300000" value="148450" step="5000"
                                           style="width: 100%; margin-bottom: 5px;" oninput="updateCostCalculator()">
                                    <span id="monthlyRevenueValue" style="color: var(--ace-purple); font-weight: bold;">$148,450</span>
                                </div>
                            </div>

                            <div style="margin-top: 20px; padding: 15px; background: var(--ace-light-purple); border-radius: 8px;">
                                <button onclick="resetToDefaults()" style="background: var(--ace-purple); color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-weight: bold;">
                                    Reset to Defaults
                                </button>
                                <button onclick="applyOptimizations()" style="background: var(--ace-yellow); color: var(--ace-dark); border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-weight: bold; margin-left: 10px;">
                                    Apply Optimizations
                                </button>
                            </div>
                        </div>

                        <!-- Results Display -->
                        <div>
                            <h4 style="color: var(--ace-purple); margin-bottom: 20px;">Financial Impact Analysis</h4>

                            <div style="display: grid; gap: 15px; margin-bottom: 25px;">
                                <div style="background: var(--ace-light-purple); padding: 15px; border-radius: 8px;">
                                    <div style="font-size: 1.8rem; font-weight: bold; color: var(--ace-purple);" id="totalCosts">$57,200</div>
                                    <div style="color: #666;">Total Monthly Costs</div>
                                </div>

                                <div style="background: var(--ace-light-purple); padding: 15px; border-radius: 8px;">
                                    <div style="font-size: 1.8rem; font-weight: bold;" id="profitMargin">61.5%</div>
                                    <div style="color: #666;">Profit Margin</div>
                                </div>

                                <div style="background: var(--ace-light-purple); padding: 15px; border-radius: 8px;">
                                    <div style="font-size: 1.8rem; font-weight: bold; color: var(--ace-purple);" id="monthlyProfit">$91,250</div>
                                    <div style="color: #666;">Monthly Profit</div>
                                </div>

                                <div style="background: var(--ace-light-purple); padding: 15px; border-radius: 8px;">
                                    <div style="font-size: 1.8rem; font-weight: bold; color: var(--ace-purple);" id="annualProfit">$1,095,000</div>
                                    <div style="color: #666;">Annual Profit</div>
                                </div>
                            </div>

                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                <h5 style="color: var(--ace-purple); margin-bottom: 10px;">Cost Breakdown</h5>
                                <div id="costBreakdown" style="font-size: 0.9rem; line-height: 1.6;">
                                    <!-- Dynamic cost breakdown will be inserted here -->
                                </div>
                            </div>

                            <div style="margin-top: 15px; padding: 15px; border-radius: 8px;" id="profitAlert">
                                <!-- Dynamic profit margin alerts will be inserted here -->
                            </div>
                        </div>
                    </div>

                    <!-- Interactive Chart -->
                    <div style="margin-top: 30px;">
                        <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">Real-time Cost Structure Visualization</h4>
                        <canvas id="costStructureChart" style="max-height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Optimization Strategy -->
        <div class="section">
            <div class="section-header">
                <h2>🎯 Revenue Optimization & Growth Strategy</h2>
            </div>
            <div class="section-content">
                <h3 style="color: var(--ace-purple); margin-bottom: 25px;">AI-Driven Plan Upgrade Strategy</h3>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-bottom: 40px;">
                    <div style="background: var(--ace-light-purple); padding: 25px; border-radius: 10px;">
                        <h4 style="color: var(--ace-purple); margin-bottom: 15px;">Creator → Accelerator</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: var(--ace-purple); margin-bottom: 10px;">25%</div>
                        <div style="color: #666; margin-bottom: 15px;">Upgrade Rate</div>
                        <ul style="font-size: 0.9rem; color: #555;">
                            <li>AI image limit reached</li>
                            <li>Competitor analysis requests</li>
                            <li>Team collaboration needs</li>
                            <li>Advanced analytics demand</li>
                        </ul>
                    </div>

                    <div style="background: var(--ace-light-purple); padding: 25px; border-radius: 10px;">
                        <h4 style="color: var(--ace-purple); margin-bottom: 15px;">Accelerator → Dominator</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: var(--ace-purple); margin-bottom: 10px;">15%</div>
                        <div style="color: #666; margin-bottom: 15px;">Upgrade Rate</div>
                        <ul style="font-size: 0.9rem; color: #555;">
                            <li>Unlimited content needs</li>
                            <li>Enterprise features</li>
                            <li>API access requirements</li>
                            <li>White-label solutions</li>
                        </ul>
                    </div>

                    <div style="background: var(--ace-light-purple); padding: 25px; border-radius: 10px;">
                        <h4 style="color: var(--ace-purple); margin-bottom: 15px;">Add-on Conversion</h4>
                        <div style="font-size: 2rem; font-weight: bold; color: var(--ace-purple); margin-bottom: 10px;">35%</div>
                        <div style="color: #666; margin-bottom: 15px;">Purchase Rate</div>
                        <ul style="font-size: 0.9rem; color: #555;">
                            <li>Mid-cycle credit purchases</li>
                            <li>Premium support upgrades</li>
                            <li>Custom integrations</li>
                            <li>Advanced analytics packs</li>
                        </ul>
                    </div>
                </div>

                <h3 style="color: var(--ace-purple); margin-bottom: 25px;">Customer Lifetime Value Enhancement</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Strategy</th>
                                <th>Implementation</th>
                                <th>Impact on CLV</th>
                                <th>Timeline</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>AI Feature Gating</strong></td>
                                <td>Strategic limitations drive upgrades</td>
                                <td>+35% CLV increase</td>
                                <td>Immediate</td>
                            </tr>
                            <tr>
                                <td><strong>Usage-Based Upsells</strong></td>
                                <td>Smart depletion alerts & one-click purchases</td>
                                <td>+28% revenue per user</td>
                                <td>Month 2</td>
                            </tr>
                            <tr>
                                <td><strong>Retention Programs</strong></td>
                                <td>AI-powered engagement & success metrics</td>
                                <td>+45% retention rate</td>
                                <td>Month 3</td>
                            </tr>
                            <tr>
                                <td><strong>Enterprise Features</strong></td>
                                <td>White-label, API access, custom training</td>
                                <td>+180% CLV for enterprise</td>
                                <td>Month 6</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 30px 0;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">Revenue Optimization Impact - CLV Enhancement Strategies</h4>
                    <canvas id="revenueOptimizationChart" style="max-height: 350px;"></canvas>
                </div>
            </div>
        </div>

        <!-- Break-even Analysis -->
        <div class="section">
            <div class="section-header">
                <h2>⚖️ Break-even Analysis & Profitability Timeline</h2>
            </div>
            <div class="section-content">
                <div class="financial-grid">
                    <div class="metric-card">
                        <div class="metric-value">18 mo</div>
                        <div class="metric-label">Break-even Timeline</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">2,850</div>
                        <div class="metric-label">Break-even User Count</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">$142K</div>
                        <div class="metric-label">Monthly Break-even MRR</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">68%</div>
                        <div class="metric-label">Target Gross Margin</div>
                    </div>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 30px 0;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">24-Month Break-even Analysis & User Growth Trajectory</h4>
                    <canvas id="breakEvenChart" style="max-height: 350px;"></canvas>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Milestone</th>
                                <th>Timeline</th>
                                <th>User Count</th>
                                <th>Monthly Revenue</th>
                                <th>Monthly Costs</th>
                                <th>Net Profit</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Launch</strong></td>
                                <td>Month 1</td>
                                <td>203</td>
                                <td>$9,497</td>
                                <td>$15,200</td>
                                <td style="color: #dc3545;">-$5,703</td>
                            </tr>
                            <tr>
                                <td><strong>Early Growth</strong></td>
                                <td>Month 6</td>
                                <td>1,315</td>
                                <td>$71,585</td>
                                <td>$45,800</td>
                                <td style="color: #28a745;">+$25,785</td>
                            </tr>
                            <tr>
                                <td><strong>Scaling Phase</strong></td>
                                <td>Month 12</td>
                                <td>2,650</td>
                                <td>$148,450</td>
                                <td>$61,050</td>
                                <td style="color: #28a745;">+$87,400</td>
                            </tr>
                            <tr style="background: var(--ace-light-purple);">
                                <td><strong>Break-even Point</strong></td>
                                <td>Month 18</td>
                                <td>2,850</td>
                                <td>$142,000</td>
                                <td>$142,000</td>
                                <td><strong>$0</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Profitability</strong></td>
                                <td>Month 24</td>
                                <td>4,200</td>
                                <td>$285,600</td>
                                <td>$78,200</td>
                                <td style="color: #28a745; font-weight: bold;">+$207,400</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin: 30px 0;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">36-Month Break-even Analysis - Revenue vs Costs</h4>
                    <canvas id="breakEven36Chart" style="max-height: 400px;"></canvas>
                </div>

                <h3 style="color: var(--ace-purple); margin: 40px 0 25px 0;">Risk Analysis & Mitigation</h3>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 10px;">
                        <h4 style="color: #856404; margin-bottom: 15px;">⚠️ Key Risks</h4>
                        <ul style="color: #856404; font-size: 0.9rem;">
                            <li>OpenAI API cost increases</li>
                            <li>Higher than expected churn rates</li>
                            <li>Slower user acquisition</li>
                            <li>Competitive pressure on pricing</li>
                            <li>Technical scaling challenges</li>
                        </ul>
                    </div>

                    <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 20px; border-radius: 10px;">
                        <h4 style="color: #0c5460; margin-bottom: 15px;">🛡️ Mitigation Strategies</h4>
                        <ul style="color: #0c5460; font-size: 0.9rem;">
                            <li>Multi-level caching reduces API costs</li>
                            <li>AI-driven retention programs</li>
                            <li>Diversified marketing channels</li>
                            <li>Strong value proposition & differentiation</li>
                            <li>Scalable cloud infrastructure</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Investment Requirements -->
        <div class="section">
            <div class="section-header">
                <h2>💼 Investment Requirements & Funding Strategy</h2>
            </div>
            <div class="section-content">
                <div class="financial-grid">
                    <div class="metric-card">
                        <div class="metric-value">$850K</div>
                        <div class="metric-label">Series A Funding Target</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">24 mo</div>
                        <div class="metric-label">Runway Duration</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">$285K</div>
                        <div class="metric-label">Monthly Burn Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">15%</div>
                        <div class="metric-label">Equity Dilution</div>
                    </div>
                </div>

                <h3 style="color: var(--ace-purple); margin: 30px 0 20px 0;">Funding Allocation</h3>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Amount</th>
                                <th>Percentage</th>
                                <th>Purpose</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Product Development</strong></td>
                                <td>$340,000</td>
                                <td>40%</td>
                                <td>AI features, platform scaling, mobile apps</td>
                            </tr>
                            <tr>
                                <td><strong>Marketing & Sales</strong></td>
                                <td>$255,000</td>
                                <td>30%</td>
                                <td>Customer acquisition, brand building, partnerships</td>
                            </tr>
                            <tr>
                                <td><strong>Operations & Infrastructure</strong></td>
                                <td>$170,000</td>
                                <td>20%</td>
                                <td>AWS costs, OpenAI API, team expansion</td>
                            </tr>
                            <tr>
                                <td><strong>Working Capital</strong></td>
                                <td>$85,000</td>
                                <td>10%</td>
                                <td>Cash reserves, unexpected costs, opportunities</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Executive Dashboard -->
        <div class="section">
            <div class="section-header">
                <h2>📊 Executive Dashboard & Key Metrics</h2>
            </div>
            <div class="section-content">
                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 40px;">
                    <h4 style="text-align: center; color: var(--ace-purple); margin-bottom: 20px;">Executive Dashboard - Key Performance Metrics</h4>
                    <canvas id="executiveDashboardChart" style="max-height: 400px;"></canvas>
                </div>

                <div class="financial-grid">
                    <div class="metric-card">
                        <div class="metric-value">$48M</div>
                        <div class="metric-label">Year 5 Total Revenue</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">58,000</div>
                        <div class="metric-label">Year 5 Total Users</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">55%</div>
                        <div class="metric-label">Add-on Adoption Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">$480M</div>
                        <div class="metric-label">Projected Exit Valuation</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="cta-section">
            <h2 style="margin-bottom: 20px;">Ready to Transform Social Media Management?</h2>
            <p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">
                ACE Social Media Platform represents a transformational opportunity to capture significant market share in the rapidly growing
                AI-powered social media management space. With our proven 5-year financial model, exceptional unit economics, and comprehensive
                add-on ecosystem, we're positioned to become the dominant platform for intelligent social media automation.
            </p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 20px; margin: 30px 0;">
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">12.0:1</div>
                    <div style="opacity: 0.8;">CLV:CAC Ratio</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">80%</div>
                    <div style="opacity: 0.8;">Year 5 Margin</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">16 mo</div>
                    <div style="opacity: 0.8;">Break-even</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">$48M</div>
                    <div style="opacity: 0.8;">Year 5 Revenue</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">58K</div>
                    <div style="opacity: 0.8;">Year 5 Users</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">$480M</div>
                    <div style="opacity: 0.8;">Exit Valuation</div>
                </div>
            </div>

            <button class="cta-button" onclick="window.open('mailto:<EMAIL>?subject=Investment Opportunity - ACE Social Platform', '_blank')">
                Contact Investment Team
            </button>

            <p style="margin-top: 20px; font-size: 0.9rem; opacity: 0.7;">
                This financial planning document is confidential and proprietary. All projections are based on current market analysis and internal modeling.
            </p>
        </div>
    </div>

    <script>
        // Add interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Animate metric cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.transform = 'translateY(0)';
                        entry.target.style.opacity = '1';
                    }
                });
            }, observerOptions);

            // Observe all metric cards
            document.querySelectorAll('.metric-card, .plan-card, .scenario-card').forEach(card => {
                card.style.transform = 'translateY(20px)';
                card.style.opacity = '0';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });

            // Add hover effects to tables
            document.querySelectorAll('table tr').forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'var(--ace-light-purple)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });

            // Smooth scrolling for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Initialize Charts
            initializeCharts();

            // Initialize Cost Calculator
            initializeCostCalculator();
        });

        function initializeCharts() {
            // ACE Social brand colors
            const aceColors = {
                dark: '#15110E',
                purple: '#4E40C5',
                yellow: '#EBAE1B',
                white: '#FFFFFF',
                lightPurple: 'rgba(78, 64, 197, 0.1)'
            };

            // 1. Add-on Revenue Distribution Chart
            const addonCtx = document.getElementById('addonRevenueChart').getContext('2d');
            new Chart(addonCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Content Enhancement', 'E-commerce', 'AI Features', 'Team Collaboration', 'Platform', 'Support'],
                    datasets: [{
                        data: [35, 28, 15, 12, 7, 3],
                        backgroundColor: [
                            aceColors.purple,
                            aceColors.yellow,
                            '#28a745',
                            '#17a2b8',
                            '#fd7e14',
                            '#6c757d'
                        ],
                        borderWidth: 2,
                        borderColor: aceColors.white
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    }
                }
            });

            // 2. 5-Year Revenue Growth Chart
            const revenueCtx = document.getElementById('revenueGrowthChart').getContext('2d');
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
                    datasets: [{
                        label: 'Subscription Revenue',
                        data: [1.385, 4.25, 9.8, 18.5, 32],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        fill: true,
                        tension: 0.4
                    }, {
                        label: 'Add-on Revenue',
                        data: [0.485, 1.68, 4.2, 8.5, 16],
                        borderColor: aceColors.yellow,
                        backgroundColor: 'rgba(235, 174, 27, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Revenue (Millions $)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': $' + context.parsed.y + 'M';
                                }
                            }
                        }
                    }
                }
            });

            // 3. Year 1 MRR Growth Chart
            const mrrCtx = document.getElementById('mrrGrowthChart').getContext('2d');
            new Chart(mrrCtx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Creator ($19/mo)',
                        data: [15.2, 18.6, 22.8, 27.9, 34.2, 41.9, 51.3, 62.8, 76.9, 94.2, 115.4, 141.4],
                        backgroundColor: aceColors.purple
                    }, {
                        label: 'Accelerator ($99/mo)',
                        data: [8.1, 9.9, 12.1, 14.8, 18.2, 22.3, 27.3, 33.4, 40.9, 50.1, 61.4, 75.2],
                        backgroundColor: aceColors.yellow
                    }, {
                        label: 'Dominator ($199/mo)',
                        data: [3.6, 4.4, 5.4, 6.6, 8.1, 9.9, 12.1, 14.8, 18.2, 22.3, 27.3, 33.4],
                        backgroundColor: '#28a745'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'MRR (Thousands $)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': $' + context.parsed.y + 'K';
                                }
                            }
                        }
                    }
                }
            });

            // 4. Growth Scenarios Chart
            const scenariosCtx = document.getElementById('growthScenariosChart').getContext('2d');
            new Chart(scenariosCtx, {
                type: 'line',
                data: {
                    labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
                    datasets: [{
                        label: 'Conservative Growth',
                        data: [2500, 6800, 12000, 22000, 35000],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        fill: false,
                        tension: 0.4
                    }, {
                        label: 'Moderate Growth (Base Case)',
                        data: [2650, 8200, 18500, 35000, 58000],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        fill: false,
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Aggressive Growth',
                        data: [7800, 18000, 32000, 58000, 95000],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        fill: false,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Total Users'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + ' users';
                                }
                            }
                        }
                    }
                }
            });

            // 5. Executive Dashboard Chart - Multi-metric overview
            const dashboardCtx = document.getElementById('executiveDashboardChart').getContext('2d');
            new Chart(dashboardCtx, {
                type: 'line',
                data: {
                    labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
                    datasets: [{
                        label: 'Total Revenue ($M)',
                        data: [1.87, 5.93, 14.0, 27.0, 48.0],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        yAxisID: 'y',
                        tension: 0.4,
                        borderWidth: 3
                    }, {
                        label: 'Total Users (K)',
                        data: [2.65, 8.2, 18.5, 35.0, 58.0],
                        borderColor: aceColors.yellow,
                        backgroundColor: 'rgba(235, 174, 27, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.4,
                        borderWidth: 2
                    }, {
                        label: 'Profit Margin (%)',
                        data: [53, 65, 70, 75, 80],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        yAxisID: 'y2',
                        tension: 0.4,
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Year'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Revenue ($M)',
                                color: aceColors.purple
                            },
                            ticks: {
                                color: aceColors.purple
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Users (Thousands)',
                                color: aceColors.yellow
                            },
                            ticks: {
                                color: aceColors.yellow
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        y2: {
                            type: 'linear',
                            display: false,
                            min: 0,
                            max: 100
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label.includes('Revenue')) {
                                        return label + ': $' + context.parsed.y + 'M';
                                    } else if (label.includes('Users')) {
                                        return label + ': ' + context.parsed.y + 'K users';
                                    } else if (label.includes('Margin')) {
                                        return label + ': ' + context.parsed.y + '%';
                                    }
                                    return label + ': ' + context.parsed.y;
                                }
                            }
                        }
                    }
                }
            });

            // 6. Add-on Adoption Rate Chart
            const adoptionCtx = document.getElementById('addonAdoptionChart').getContext('2d');
            new Chart(adoptionCtx, {
                type: 'bar',
                data: {
                    labels: ['Content Enhancement', 'E-commerce', 'AI Features', 'Team Collaboration', 'Platform', 'Support'],
                    datasets: [{
                        label: 'Creator Plan',
                        data: [22, 15, 12, 25, 5, 8],
                        backgroundColor: aceColors.purple
                    }, {
                        label: 'Accelerator Plan',
                        data: [28, 18, 15, 30, 8, 12],
                        backgroundColor: aceColors.yellow
                    }, {
                        label: 'Dominator Plan',
                        data: [35, 25, 20, 40, 15, 18],
                        backgroundColor: '#28a745'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 50,
                            title: {
                                display: true,
                                text: 'Adoption Rate (%)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + '%';
                                }
                            }
                        }
                    }
                }
            });

            // 7. 5-Year Profit Margin Evolution Chart
            const profitCtx = document.getElementById('profitMarginChart').getContext('2d');
            new Chart(profitCtx, {
                type: 'line',
                data: {
                    labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
                    datasets: [{
                        label: 'Total Revenue ($M)',
                        data: [1.87, 5.93, 14.0, 27.0, 48.0],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        yAxisID: 'y',
                        tension: 0.4,
                        borderWidth: 3,
                        fill: true
                    }, {
                        label: 'Total Costs ($M)',
                        data: [0.87, 2.08, 4.2, 6.75, 9.6],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        yAxisID: 'y',
                        tension: 0.4,
                        borderWidth: 2,
                        fill: true
                    }, {
                        label: 'Profit Margin (%)',
                        data: [53, 65, 70, 75, 80],
                        borderColor: aceColors.yellow,
                        backgroundColor: 'rgba(235, 174, 27, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.4,
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Revenue & Costs ($M)'
                            },
                            beginAtZero: true
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Profit Margin (%)',
                                color: aceColors.yellow
                            },
                            min: 0,
                            max: 100,
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    if (context.dataset.label.includes('Margin')) {
                                        return context.dataset.label + ': ' + context.parsed.y + '%';
                                    } else {
                                        return context.dataset.label + ': $' + context.parsed.y + 'M';
                                    }
                                }
                            }
                        }
                    }
                }
            });

            // 8. 5-Year User Distribution Chart
            const userDistCtx = document.getElementById('userDistributionChart').getContext('2d');
            new Chart(userDistCtx, {
                type: 'bar',
                data: {
                    labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
                    datasets: [{
                        label: 'Creator Users',
                        data: [1.65, 4.92, 10.18, 17.5, 26.1],
                        backgroundColor: aceColors.purple
                    }, {
                        label: 'Accelerator Users',
                        data: [0.82, 2.87, 6.85, 14.0, 23.2],
                        backgroundColor: aceColors.yellow
                    }, {
                        label: 'Dominator Users',
                        data: [0.18, 0.41, 1.48, 3.5, 8.7],
                        backgroundColor: '#28a745'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Users (Thousands)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + 'K users';
                                }
                            }
                        }
                    }
                }
            });

            // 9. Add-on Revenue Projection Chart
            const addonProjectionCtx = document.getElementById('addonRevenueProjectionChart').getContext('2d');
            new Chart(addonProjectionCtx, {
                type: 'line',
                data: {
                    labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
                    datasets: [{
                        label: 'Content Enhancement',
                        data: [0.17, 0.59, 1.47, 2.98, 5.6],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        tension: 0.4,
                        fill: false
                    }, {
                        label: 'E-commerce',
                        data: [0.14, 0.47, 1.18, 2.38, 4.48],
                        borderColor: aceColors.yellow,
                        backgroundColor: 'rgba(235, 174, 27, 0.1)',
                        tension: 0.4,
                        fill: false
                    }, {
                        label: 'AI Features',
                        data: [0.07, 0.25, 0.63, 1.28, 2.4],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4,
                        fill: false
                    }, {
                        label: 'Team Collaboration',
                        data: [0.06, 0.20, 0.50, 1.02, 1.92],
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        tension: 0.4,
                        fill: false
                    }, {
                        label: 'Platform & Support',
                        data: [0.05, 0.17, 0.42, 0.85, 1.6],
                        borderColor: '#fd7e14',
                        backgroundColor: 'rgba(253, 126, 20, 0.1)',
                        tension: 0.4,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Revenue ($M)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': $' + context.parsed.y + 'M';
                                }
                            }
                        }
                    }
                }
            });

            // 10. Break-even Analysis Chart - 24 Month Timeline
            const breakEvenCtx = document.getElementById('breakEvenChart').getContext('2d');
            new Chart(breakEvenCtx, {
                type: 'line',
                data: {
                    labels: ['M1', 'M3', 'M6', 'M9', 'M12', 'M15', 'M18', 'M21', 'M24'],
                    datasets: [{
                        label: 'Monthly Revenue',
                        data: [15, 35, 68, 95, 125, 142, 165, 195, 230],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        tension: 0.4,
                        borderWidth: 3,
                        fill: false
                    }, {
                        label: 'Monthly Costs',
                        data: [45, 52, 65, 78, 95, 115, 142, 165, 185],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        fill: false
                    }, {
                        label: 'Break-even Line',
                        data: [0, 0, 0, 0, 0, 0, 0, 0, 0],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false
                    }, {
                        label: 'Net Profit/Loss',
                        data: [-30, -17, 3, 17, 30, 27, 23, 30, 45],
                        borderColor: aceColors.yellow,
                        backgroundColor: 'rgba(235, 174, 27, 0.1)',
                        tension: 0.4,
                        borderWidth: 3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            title: {
                                display: true,
                                text: 'Amount ($K)'
                            },
                            grid: {
                                color: function(context) {
                                    if (context.tick.value === 0) {
                                        return '#28a745';
                                    }
                                    return 'rgba(0, 0, 0, 0.1)';
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Timeline (Months)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': $' + context.parsed.y + 'K';
                                }
                            }
                        },
                        annotation: {
                            annotations: {
                                breakEvenPoint: {
                                    type: 'point',
                                    xValue: 'M18',
                                    yValue: 0,
                                    backgroundColor: '#28a745',
                                    borderColor: '#28a745',
                                    borderWidth: 2,
                                    radius: 8,
                                    label: {
                                        content: 'Break-even: Month 18',
                                        enabled: true,
                                        position: 'top'
                                    }
                                }
                            }
                        }
                    }
                }
            });

            // 11. Revenue Optimization Impact Chart - CLV Enhancement
            const revenueOptCtx = document.getElementById('revenueOptimizationChart').getContext('2d');
            new Chart(revenueOptCtx, {
                type: 'bar',
                data: {
                    labels: ['Baseline CLV', 'AI Feature Gating', 'Usage-Based Upsells', 'Retention Programs', 'Enterprise Features'],
                    datasets: [{
                        label: 'Creator Plan CLV',
                        data: [285, 385, 365, 415, 420],
                        backgroundColor: aceColors.purple
                    }, {
                        label: 'Accelerator Plan CLV',
                        data: [1485, 2005, 1900, 2155, 2200],
                        backgroundColor: aceColors.yellow
                    }, {
                        label: 'Dominator Plan CLV',
                        data: [2985, 4030, 3820, 4330, 8365],
                        backgroundColor: '#28a745'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Customer Lifetime Value ($)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // 12. 24-Month User Growth Trajectory Chart
            const userTrajectoryCtx = document.getElementById('userGrowthTrajectoryChart').getContext('2d');
            new Chart(userTrajectoryCtx, {
                type: 'line',
                data: {
                    labels: ['M1', 'M3', 'M6', 'M9', 'M12', 'M15', 'M18', 'M21', 'M24'],
                    datasets: [{
                        label: 'Conservative Growth',
                        data: [180, 420, 850, 1250, 1680, 2100, 2500, 2850, 3200],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        fill: false
                    }, {
                        label: 'Moderate Growth (Base Case)',
                        data: [203, 485, 1000, 1500, 2100, 2650, 3200, 3750, 4200],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        tension: 0.4,
                        borderWidth: 3,
                        fill: false
                    }, {
                        label: 'Aggressive Growth',
                        data: [650, 1500, 2800, 4200, 5800, 6800, 7800, 8500, 9200],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Total Users'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Timeline (Months)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + ' users';
                                }
                            }
                        }
                    }
                }
            });

            // 13. 36-Month Break-even Analysis Chart
            const breakEven36Ctx = document.getElementById('breakEven36Chart').getContext('2d');
            new Chart(breakEven36Ctx, {
                type: 'line',
                data: {
                    labels: ['M3', 'M6', 'M9', 'M12', 'M15', 'M18', 'M21', 'M24', 'M27', 'M30', 'M33', 'M36'],
                    datasets: [{
                        label: 'Monthly Revenue',
                        data: [35, 68, 95, 148, 165, 186, 225, 286, 345, 420, 485, 565],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        tension: 0.4,
                        borderWidth: 3,
                        fill: true
                    }, {
                        label: 'Monthly Costs',
                        data: [52, 65, 78, 95, 115, 142, 165, 185, 205, 225, 245, 265],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        borderWidth: 2,
                        fill: true
                    }, {
                        label: 'Cumulative Profit/Loss',
                        data: [-85, -125, -108, -55, -5, 44, 104, 205, 345, 540, 780, 1080],
                        borderColor: aceColors.yellow,
                        backgroundColor: 'rgba(235, 174, 27, 0.1)',
                        tension: 0.4,
                        borderWidth: 3,
                        yAxisID: 'y1',
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Monthly Amount ($K)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Cumulative P&L ($K)',
                                color: aceColors.yellow
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Timeline (Months)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    if (context.dataset.label.includes('Cumulative')) {
                                        return context.dataset.label + ': $' + context.parsed.y + 'K';
                                    } else {
                                        return context.dataset.label + ': $' + context.parsed.y + 'K/month';
                                    }
                                }
                            }
                        }
                    }
                }
            });

            // 14. Market Penetration & Competitive Analysis Chart
            const marketPenetrationCtx = document.getElementById('marketPenetrationChart').getContext('2d');
            new Chart(marketPenetrationCtx, {
                type: 'radar',
                data: {
                    labels: ['Market Share', 'Feature Completeness', 'Pricing Competitiveness', 'User Experience', 'AI Capabilities', 'Integration Ecosystem'],
                    datasets: [{
                        label: 'ACE Social (Current)',
                        data: [15, 85, 90, 88, 95, 70],
                        borderColor: aceColors.purple,
                        backgroundColor: aceColors.lightPurple,
                        borderWidth: 3,
                        pointBackgroundColor: aceColors.purple,
                        pointBorderColor: aceColors.white,
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }, {
                        label: 'ACE Social (Year 3 Target)',
                        data: [35, 95, 85, 95, 98, 90],
                        borderColor: aceColors.yellow,
                        backgroundColor: 'rgba(235, 174, 27, 0.1)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        pointBackgroundColor: aceColors.yellow,
                        pointBorderColor: aceColors.white,
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }, {
                        label: 'Market Leader Average',
                        data: [45, 80, 75, 82, 85, 95],
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        borderWidth: 2,
                        pointBackgroundColor: '#dc3545',
                        pointBorderColor: aceColors.white,
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                stepSize: 20,
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            angleLines: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            pointLabels: {
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                },
                                color: aceColors.dark
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.r + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Cost Calculator Variables
        let costChart;

        function initializeCostCalculator() {
            updateCostCalculator();
            createCostChart();
        }

        function updateCostCalculator() {
            // Get current values
            const aiCosts = parseInt(document.getElementById('aiCosts').value);
            const infraCosts = parseInt(document.getElementById('infraCosts').value);
            const devCosts = parseInt(document.getElementById('devCosts').value);
            const marketingCosts = parseInt(document.getElementById('marketingCosts').value);
            const opsCosts = parseInt(document.getElementById('opsCosts').value);
            const legalCosts = parseInt(document.getElementById('legalCosts').value);
            const monthlyRevenue = parseInt(document.getElementById('monthlyRevenue').value);

            // Update display values
            document.getElementById('aiCostsValue').textContent = '$' + aiCosts.toLocaleString();
            document.getElementById('infraCostsValue').textContent = '$' + infraCosts.toLocaleString();
            document.getElementById('devCostsValue').textContent = '$' + devCosts.toLocaleString();
            document.getElementById('marketingCostsValue').textContent = '$' + marketingCosts.toLocaleString();
            document.getElementById('opsCostsValue').textContent = '$' + opsCosts.toLocaleString();
            document.getElementById('legalCostsValue').textContent = '$' + legalCosts.toLocaleString();
            document.getElementById('monthlyRevenueValue').textContent = '$' + monthlyRevenue.toLocaleString();

            // Calculate totals
            const totalCosts = aiCosts + infraCosts + devCosts + marketingCosts + opsCosts + legalCosts;
            const monthlyProfit = monthlyRevenue - totalCosts;
            const profitMargin = ((monthlyProfit / monthlyRevenue) * 100);
            const annualProfit = monthlyProfit * 12;

            // Update results
            document.getElementById('totalCosts').textContent = '$' + totalCosts.toLocaleString();
            document.getElementById('monthlyProfit').textContent = '$' + monthlyProfit.toLocaleString();
            document.getElementById('annualProfit').textContent = '$' + annualProfit.toLocaleString();

            // Update profit margin with color coding
            const profitMarginElement = document.getElementById('profitMargin');
            profitMarginElement.textContent = profitMargin.toFixed(1) + '%';
            if (profitMargin >= 70) {
                profitMarginElement.style.color = '#28a745';
            } else if (profitMargin >= 50) {
                profitMarginElement.style.color = '#ffc107';
            } else {
                profitMarginElement.style.color = '#dc3545';
            }

            // Update cost breakdown
            const costBreakdown = document.getElementById('costBreakdown');
            costBreakdown.innerHTML = `
                <div>AI/Regeneration: $${aiCosts.toLocaleString()} (${((aiCosts/totalCosts)*100).toFixed(1)}%)</div>
                <div>Infrastructure: $${infraCosts.toLocaleString()} (${((infraCosts/totalCosts)*100).toFixed(1)}%)</div>
                <div>Development: $${devCosts.toLocaleString()} (${((devCosts/totalCosts)*100).toFixed(1)}%)</div>
                <div>Marketing: $${marketingCosts.toLocaleString()} (${((marketingCosts/totalCosts)*100).toFixed(1)}%)</div>
                <div>Operations: $${opsCosts.toLocaleString()} (${((opsCosts/totalCosts)*100).toFixed(1)}%)</div>
                <div>Legal: $${legalCosts.toLocaleString()} (${((legalCosts/totalCosts)*100).toFixed(1)}%)</div>
            `;

            // Update profit alert
            const profitAlert = document.getElementById('profitAlert');
            if (profitMargin >= 70) {
                profitAlert.innerHTML = '<div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 5px;"><strong>Excellent!</strong> Profit margin is above 70% - optimal for growth and sustainability.</div>';
            } else if (profitMargin >= 50) {
                profitAlert.innerHTML = '<div style="background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px;"><strong>Good:</strong> Healthy profit margin. Consider optimizing costs for better margins.</div>';
            } else if (profitMargin >= 30) {
                profitAlert.innerHTML = '<div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;"><strong>Warning:</strong> Low profit margin. Cost optimization needed.</div>';
            } else {
                profitAlert.innerHTML = '<div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;"><strong>Critical:</strong> Very low or negative profit margin. Immediate action required!</div>';
            }

            // Update chart
            if (costChart) {
                updateCostChart(aiCosts, infraCosts, devCosts, marketingCosts, opsCosts, legalCosts);
            }
        }

        function createCostChart() {
            const ctx = document.getElementById('costStructureChart').getContext('2d');
            const aceColors = {
                purple: '#4E40C5',
                yellow: '#EBAE1B',
                dark: '#15110E',
                white: '#FFFFFF'
            };

            costChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['AI/Regeneration', 'Infrastructure', 'Development', 'Marketing', 'Operations', 'Legal'],
                    datasets: [{
                        data: [16200, 11000, 12500, 8500, 7200, 1800],
                        backgroundColor: [
                            aceColors.purple,
                            aceColors.yellow,
                            '#28a745',
                            '#17a2b8',
                            '#fd7e14',
                            '#6c757d'
                        ],
                        borderWidth: 2,
                        borderColor: aceColors.white
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return context.label + ': $' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateCostChart(aiCosts, infraCosts, devCosts, marketingCosts, opsCosts, legalCosts) {
            costChart.data.datasets[0].data = [aiCosts, infraCosts, devCosts, marketingCosts, opsCosts, legalCosts];
            costChart.update();
        }

        function resetToDefaults() {
            document.getElementById('aiCosts').value = 16200;
            document.getElementById('infraCosts').value = 11000;
            document.getElementById('devCosts').value = 12500;
            document.getElementById('marketingCosts').value = 8500;
            document.getElementById('opsCosts').value = 7200;
            document.getElementById('legalCosts').value = 1800;
            document.getElementById('monthlyRevenue').value = 148450;
            updateCostCalculator();
        }

        function applyOptimizations() {
            // Apply cost optimization strategies
            document.getElementById('aiCosts').value = 11340; // 30% reduction through caching
            document.getElementById('infraCosts').value = 8800; // 20% reduction through optimization
            document.getElementById('devCosts').value = 12500; // Keep same
            document.getElementById('marketingCosts').value = 6800; // 20% reduction through better targeting
            document.getElementById('opsCosts').value = 6480; // 10% reduction through automation
            document.getElementById('legalCosts').value = 1800; // Keep same
            updateCostCalculator();
        }
    </script>
</body>
</html>

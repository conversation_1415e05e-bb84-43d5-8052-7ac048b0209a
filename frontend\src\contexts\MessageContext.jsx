/**
 * Message Context
 * Provides messaging functionality with real-time updates and unread count tracking
 * Production-ready implementation with comprehensive error handling and logging
 @since 2024-1-1 to 2025-25-7
*/

import {
  createContext,
  useState,
  useEffect,
  useCallback,
  useContext,
} from "react";
import { useAuth } from "./AuthContext";
import api from "../api";

// Configuration constants
const CONFIG = {
  // Polling settings
  UNREAD_COUNT_POLL_INTERVAL: 30000, // 30 seconds

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[MessageContext] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[MessageContext] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Message Context Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[MessageContext] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Message Context Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[MessageContext] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Message Context Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Create context
const MessageContext = createContext();

// Custom hook to use message context
// eslint-disable-next-line react-refresh/only-export-components
export const useMessage = () => {
  const context = useContext(MessageContext);
  if (!context) {
    throw new Error(
      "useMessage must be used within a MessageProvider"
    );
  }
  return context;
};

export const MessageProvider = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load unread message count
  const loadUnreadCount = useCallback(
    async () => {
      if (!isAuthenticated) {
        logger.debug("Skipping unread count load - user not authenticated");
        return;
      }

      try {
        logger.debug("Loading unread message count");
        const response = await api.get("/api/messaging/unread-count");
        const unreadCount = response.data.unread_count || 0;
        setUnreadCount(unreadCount);
        logger.info("Unread count loaded successfully", { unreadCount });
      } catch (error) {
        logger.error("Failed to load unread count", error);
        // Don't set error state for unread count failures as it's not critical
      }
    },
    [isAuthenticated]
  );

  // Load conversations and calculate unread count
  const loadConversations = useCallback(
    async () => {
      if (!isAuthenticated) {
        logger.debug("Skipping conversations load - user not authenticated");
        return;
      }

      setLoading(true);
      setError(null);
      logger.debug("Loading conversations");

      try {
        // Get conversations from API
        const response = await api.get("/api/messaging/conversations");
        const conversations = response.data || [];
        setConversations(conversations);
        logger.info("Conversations loaded successfully", {
          count: conversations.length
        });

        // Load unread count from dedicated endpoint
        await loadUnreadCount();
      } catch (error) {
        logger.error("Failed to load conversations", error);
        setError("Failed to load conversations");
      } finally {
        setLoading(false);
      }
    },
    [isAuthenticated, loadUnreadCount]
  );

  // Load conversations on mount and when auth state changes
  useEffect(() => {
    if (isAuthenticated) {
      loadConversations();
    }
  }, [isAuthenticated, loadConversations]);

  // Load unread count periodically
  useEffect(() => {
    if (!isAuthenticated) return;

    // Load unread count immediately
    loadUnreadCount();

    // Set up interval to refresh unread count periodically
    const interval = setInterval(() => {
      loadUnreadCount();
    }, CONFIG.UNREAD_COUNT_POLL_INTERVAL);

    // Clean up interval on unmount
    return () => clearInterval(interval);
  }, [isAuthenticated, loadUnreadCount]);

  // Mark conversation as read
  const markConversationAsRead = async (conversationId) => {
    if (!isAuthenticated) {
      logger.warn("Cannot mark conversation as read - user not authenticated");
      return false;
    }

    if (!conversationId) {
      logger.warn("Cannot mark conversation as read - no conversation ID provided");
      return false;
    }

    try {
      logger.debug("Marking conversation as read", { conversationId });
      await api.post(`/api/messaging/conversations/${conversationId}/read`);

      // Update local state
      setConversations((prevConversations) =>
        prevConversations.map((conversation) =>
          conversation.id === conversationId
            ? { ...conversation, unread_count: 0 }
            : conversation
        )
      );

      // Recalculate unread count
      setUnreadCount((prevCount) => {
        const conversation = conversations.find(c => c.id === conversationId);
        return Math.max(0, prevCount - (conversation?.unread_count || 0));
      });

      logger.info("Conversation marked as read successfully", { conversationId });
      return true;
    } catch (error) {
      logger.error("Failed to mark conversation as read", error);
      return false;
    }
  };

  // Update unread count when a new message is received
  const updateUnreadCount = (conversationId, increment = true) => {
    if (!conversationId) {
      logger.warn("Cannot update unread count - no conversation ID provided");
      return;
    }

    logger.debug("Updating unread count", { conversationId, increment });

    setConversations((prevConversations) =>
      prevConversations.map((conversation) =>
        conversation.id === conversationId
          ? {
              ...conversation,
              unread_count: increment
                ? (conversation.unread_count || 0) + 1
                : 0
            }
          : conversation
      )
    );

    // Update total unread count
    setUnreadCount((prevCount) => {
      const newCount = increment ? prevCount + 1 : Math.max(0, prevCount - 1);
      logger.debug("Total unread count updated", {
        previous: prevCount,
        new: newCount,
        increment
      });
      return newCount;
    });
  };

  // Enhanced context value with organized structure
  const contextValue = {
    // State data
    conversations,
    unreadCount,
    loading,
    error,

    // Core functions
    loadConversations,
    loadUnreadCount,
    markConversationAsRead,
    updateUnreadCount,

    // Utility functions
    clearError: () => setError(null),
    refreshData: async () => {
      logger.debug("Refreshing message data");
      await loadConversations();
    },

    // Helper functions
    hasUnreadMessages: unreadCount > 0,
    getConversationCount: () => conversations.length,
    getConversationById: (id) => conversations.find(conv => conv.id === id),
    getUnreadConversations: () => conversations.filter(conv => (conv.unread_count || 0) > 0),
    getTotalUnreadCount: () => unreadCount,

    // Validation helpers
    isConversationUnread: (conversationId) => {
      const conversation = conversations.find(conv => conv.id === conversationId);
      return (conversation?.unread_count || 0) > 0;
    },

    // Bulk operations
    markAllAsRead: async () => {
      logger.debug("Marking all conversations as read");
      const unreadConversations = conversations.filter(conv => (conv.unread_count || 0) > 0);

      for (const conversation of unreadConversations) {
        await markConversationAsRead(conversation.id);
      }

      logger.info("All conversations marked as read", {
        count: unreadConversations.length
      });
    }
  };

  return (
    <MessageContext.Provider value={contextValue}>
      {children}
    </MessageContext.Provider>
  );
};

// Export the context
export { MessageContext };

// Default export for convenience
export default MessageProvider;

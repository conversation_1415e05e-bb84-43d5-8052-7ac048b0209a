// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, useMemo, memo, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Divider,
  CircularProgress,
  Chip,
  Button,
  useTheme,
  alpha,
  useMediaQuery,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  AlertTitle,
  Grid,

  Skeleton,
  Fade
} from '@mui/material';
import {
  SentimentSatisfied as SentimentSatisfiedIcon,
  SentimentDissatisfied as SentimentDissatisfiedIcon,
  SentimentNeutral as SentimentNeutralIcon,
  SentimentVerySatisfied as SentimentVerySatisfiedIcon,
  SentimentVeryDissatisfied as SentimentVeryDissatisfiedIcon,

  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Add as AddIcon,
  Psychology as PsychologyIcon,

  Download as ExportIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,

} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  ReferenceLine,

} from 'recharts';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};



/**
 * Enhanced SentimentOverviewCard Component - Enterprise-grade sentiment overview card
 * Features: Plan-based sentiment overview limitations, real-time sentiment overview monitoring, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced sentiment overview insights and interactive sentiment overview exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} [props.data] - Sentiment overview data
 * @param {boolean} [props.loading=false] - Loading state
 * @param {Function} [props.onViewDetails] - View details callback
 * @param {string} [props.overviewType='overall-summary'] - Sentiment overview type
 * @param {string} [props.contentVariant='posts'] - Content variant
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onOverviewTypeChange] - Overview type change callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const SentimentOverviewCard = memo(forwardRef(({
  data,
  loading = false,
  onViewDetails,
  overviewType = 'overall-summary',

  enableExport = false,
  realTimeUpdates = false,

  onExport,
  onRefresh,
  customization = {},
  className = '',
  style = {},
  testId = 'sentiment-overview-card',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading || subscriptionLoading,
    refreshing: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showSettingsDialog: false,
    currentOverviewType: overviewType,
    animationKey: 0,
    errors: {},
    // Sentiment overview state
    selectedTimeRange: '30d',
    sentimentOptimizing: false
  });

  // Sentiment overview data state
  const [sentimentOverviewData, setSentimentOverviewData] = useState({
    raw: data || null,
    processed: null,
    trends: null,
    insights: null
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);

  /**
   * Enhanced plan-based sentiment overview validation - Production Ready
   */
  const validateSentimentOverviewFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canViewOverview: false,
        hasOverviewAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { dataPoints: 0, metrics: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based sentiment overview limits
    const planLimits = {
      creator: {
        dataPoints: 30,
        metrics: 3,
        features: ['basic_sentiment_overview'],
        realTime: false,
        export: false,
        overviewTypes: ['overall-summary'],
        insights: false
      },
      accelerator: {
        dataPoints: 90,
        metrics: 8,
        features: ['basic_sentiment_overview', 'advanced_sentiment_overview', 'sentiment_trends'],
        realTime: true,
        export: true,
        overviewTypes: ['overall-summary', 'sentiment-trends', 'sentiment-distribution', 'sentiment-insights'],
        insights: true
      },
      dominator: {
        dataPoints: -1,
        metrics: -1,
        features: ['basic_sentiment_overview', 'advanced_sentiment_overview', 'sentiment_trends', 'ai_sentiment_insights'],
        realTime: true,
        export: true,
        overviewTypes: ['overall-summary', 'sentiment-trends', 'sentiment-distribution', 'sentiment-insights', 'sentiment-alerts'],
        insights: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = sentimentOverviewData.processed ? 1 : 0;
    const limit = currentPlanLimits.dataPoints;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewOverview: true,
      hasOverviewAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, sentimentOverviewData.processed]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const overviewLimits = validateSentimentOverviewFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasExport: overviewLimits.planLimits.export,
      hasRealTime: overviewLimits.planLimits.realTime,
      hasInsights: overviewLimits.planLimits.insights,
      maxDataPoints: overviewLimits.planLimits.dataPoints,
      maxMetrics: overviewLimits.planLimits.metrics,
      availableOverviewTypes: overviewLimits.planLimits.overviewTypes,
      availableFeatures: overviewLimits.planLimits.features,
      refreshInterval: overviewLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateSentimentOverviewFeatures]);



  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || 'Sentiment overview card',
      'aria-description': ariaDescription || `Interactive ${overviewType} sentiment overview card displaying sentiment metrics`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, overviewType, realTimeUpdates]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Sentiment overview refreshed successfully');
      announceToScreenReader('Sentiment overview has been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh sentiment overview: ${error.message}`);
      announceToScreenReader('Failed to refresh sentiment overview');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'csv') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, sentimentOverviewData.processed);
      }

      showSuccessNotification(`Sentiment overview exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Sentiment overview has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export sentiment overview: ${error.message}`);
      announceToScreenReader('Failed to export sentiment overview');
    }
  }, [subscriptionFeatures.hasExport, sentimentOverviewData.processed, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportOverview: handleExport,
    getOverviewData: () => sentimentOverviewData.processed,
    getOverviewLimits: validateSentimentOverviewFeatures,
    focus: () => setFocusToElement(document.querySelector(`[data-testid="${testId}"]`)),
    announce: (message) => announceToScreenReader(message)
  }), [
    sentimentOverviewData.processed,
    validateSentimentOverviewFeatures,
    handleRefresh,
    handleExport,
    setFocusToElement,
    announceToScreenReader,
    testId
  ]);

  /**
   * Enhanced data processing effect - Production Ready
   */
  useEffect(() => {
    if (data) {
      setSentimentOverviewData(prev => ({
        ...prev,
        raw: data,
        processed: data
      }));
    }
  }, [data]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced sentiment overview features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Advanced sentiment overview types',
        'Real-time sentiment monitoring',
        'Sentiment trend analysis',
        'Data export capabilities',
        'AI-powered sentiment insights',
        'Custom sentiment alerts'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  /**
   * Enhanced sentiment icon with ACE Social colors - Production Ready
   */
  const getSentimentIcon = useCallback((score) => {
    const iconSize = isMobile ? 28 : 32;
    if (score === undefined || score === null) return <SentimentNeutralIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6), fontSize: iconSize }} />;
    if (score >= 0.5) return <SentimentVerySatisfiedIcon sx={{ color: '#2E7D32', fontSize: iconSize }} />;
    if (score > 0) return <SentimentSatisfiedIcon sx={{ color: '#4CAF50', fontSize: iconSize }} />;
    if (score > -0.5) return <SentimentDissatisfiedIcon sx={{ color: '#F44336', fontSize: iconSize }} />;
    return <SentimentVeryDissatisfiedIcon sx={{ color: '#C62828', fontSize: iconSize }} />;
  }, [isMobile]);

  /**
   * Enhanced sentiment color with ACE Social colors - Production Ready
   */
  const getSentimentColor = useCallback((score) => {
    if (score === undefined || score === null) return alpha(ACE_COLORS.DARK, 0.6);
    if (score >= 0.5) return '#2E7D32';
    if (score > 0) return '#4CAF50';
    if (score > -0.5) return '#F44336';
    return '#C62828';
  }, []);

  /**
   * Enhanced sentiment label - Production Ready
   */
  const getSentimentLabel = useCallback((score) => {
    if (score === undefined || score === null) return 'Unknown';
    if (score >= 0.5) return 'Very Positive';
    if (score > 0) return 'Positive';
    if (score === 0) return 'Neutral';
    if (score > -0.5) return 'Negative';
    return 'Very Negative';
  }, []);

  /**
   * Enhanced trend icon with ACE Social colors - Production Ready
   */
  const getTrendIcon = useCallback((trend) => {
    const iconSize = isMobile ? 20 : 24;
    if (!trend) {
      return <TrendingFlatIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6), fontSize: iconSize }} />;
    }

    switch (trend) {
      case 'improving':
        return <TrendingUpIcon sx={{ color: '#4CAF50', fontSize: iconSize }} />;
      case 'declining':
        return <TrendingDownIcon sx={{ color: '#F44336', fontSize: iconSize }} />;
      default:
        return <TrendingFlatIcon sx={{ color: alpha(ACE_COLORS.DARK, 0.6), fontSize: iconSize }} />;
    }
  }, [isMobile]);

  /**
   * Enhanced custom tooltip for charts - Production Ready
   */
  const CustomTooltip = useCallback(({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            backgroundColor: ACE_COLORS.WHITE,
            p: 2,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
            borderRadius: 2,
            boxShadow: `0 4px 12px ${alpha(ACE_COLORS.DARK, 0.1)}`
          }}
        >
          <Typography variant="body2" fontWeight="medium" sx={{ color: ACE_COLORS.DARK }}>{label}</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            {getSentimentIcon(payload[0].value)}
            <Typography variant="body2" sx={{ ml: 1, color: ACE_COLORS.DARK }}>
              Score: {payload[0].value !== undefined ? payload[0].value.toFixed(2) : 'N/A'}
            </Typography>
          </Box>
        </Box>
      );
    }
    return null;
  }, [getSentimentIcon]);

  // Use provided data or null
  const displayData = sentimentOverviewData.processed || data;

  // Main render condition checks
  if (state.loading && !displayData) {
    return (
      <ErrorBoundary
        fallback={
          <Box sx={{ height: '100%', p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load sentiment overview card
            </Typography>
          </Box>
        }
      >
        <Box
          sx={{
            height: '100%',
            ...customization
          }}
          className={className}
          style={style}
          data-testid={testId}
          {...getAccessibilityProps()}
        >
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Skeleton variant="circular" width={60} height={60} sx={{ mx: 'auto', mb: 2 }} />
              <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />
              <Skeleton variant="text" width="40%" height={16} />
            </Box>
          </Box>
        </Box>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Box sx={{ height: '100%', p: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="error">
            Unable to load sentiment overview card
          </Typography>
        </Box>
      }
    >
      <Box
        sx={{
          height: '100%',
          ...customization
        }}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
      >
        {/* Real-time Updates Indicator */}
        {realTimeUpdates && subscriptionFeatures.hasRealTime && (
          <Box sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 1,
            backgroundColor: alpha(theme.palette.success.main, 0.1),
            borderRadius: 1,
            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
            zIndex: 10
          }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: theme.palette.success.main,
                ...(!prefersReducedMotion && {
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 }
                  }
                })
              }}
              aria-hidden="true"
            />
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
              Live
            </Typography>
          </Box>
        )}

        {state.loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <Box sx={{ textAlign: 'center' }}>
              <Skeleton variant="circular" width={60} height={60} sx={{ mx: 'auto', mb: 2 }} />
              <Skeleton variant="text" width="60%" height={24} sx={{ mb: 1 }} />
              <Skeleton variant="text" width="40%" height={16} />
            </Box>
          </Box>
        ) : !displayData ? (
          <Box sx={{ p: 3, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
            <PsychologyIcon sx={{ fontSize: isMobile ? 40 : 48, color: alpha(ACE_COLORS.PURPLE, 0.4), mb: 2 }} />
            <Typography variant={isMobile ? "subtitle1" : "h6"} gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
              No Sentiment Data Available
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, maxWidth: '80%' }}>
              Start creating and publishing content to see sentiment analysis data here.
            </Typography>
            {onViewDetails && (
              <Button
                variant="outlined"
                size="small"
                onClick={onViewDetails}
                startIcon={<AddIcon />}
                sx={{
                  borderColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.PURPLE,
                  '&:hover': {
                    borderColor: ACE_COLORS.PURPLE,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                  }
                }}
              >
                Create Content
              </Button>
            )}
          </Box>
        ) : (
          <Fade in timeout={500}>
            <div>
              {/* Enhanced Header with Controls */}
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PsychologyIcon sx={{ color: ACE_COLORS.PURPLE }} />
                  <Typography variant="h6" component="span" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                    Sentiment Overview
                  </Typography>
                  {subscriptionFeatures.hasRealTime && realTimeUpdates && (
                    <Chip
                      label="LIVE"
                      size="small"
                      sx={{
                        backgroundColor: alpha('#4CAF50', 0.1),
                        color: '#4CAF50',
                        fontWeight: 600,
                        fontSize: '0.7rem'
                      }}
                    />
                  )}
                </Box>

                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  {/* Settings Button */}
                  {subscriptionFeatures.hasInsights && (
                    <Tooltip title="Overview Settings">
                      <IconButton
                        size="small"
                        onClick={settingsAnchorEl ? () => setSettingsAnchorEl(null) : (e) => setSettingsAnchorEl(e.currentTarget)}
                        sx={{
                          color: ACE_COLORS.PURPLE,
                          '&:hover': {
                            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                          }
                        }}
                        aria-label="Change overview settings"
                      >
                        <SettingsIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}

                  {/* Export Button */}
                  {enableExport && (
                    <Tooltip title="Export Overview">
                      <IconButton
                        size="small"
                        onClick={handleExportMenuOpen}
                        sx={{
                          color: theme.palette.text.secondary,
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.primary.main, 0.1)
                          }
                        }}
                        aria-label="Export overview data"
                      >
                        <ExportIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}

                  {/* Refresh Button */}
                  {onRefresh && (
                    <Tooltip title="Refresh Overview">
                      <IconButton
                        size="small"
                        onClick={handleRefresh}
                        disabled={state.refreshing}
                        sx={{
                          color: ACE_COLORS.PURPLE,
                          '&:hover': {
                            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                          }
                        }}
                        aria-label="Refresh sentiment overview"
                      >
                        {state.refreshing ? (
                          <CircularProgress size={16} />
                        ) : (
                          <RefreshIcon fontSize="small" />
                        )}
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              </Box>

              {/* Enhanced Overall Sentiment Score */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Box
                  sx={{
                    mr: 2,
                    p: 1.5,
                    borderRadius: '50%',
                    bgcolor: alpha(getSentimentColor(displayData.overall_score), 0.1),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {getSentimentIcon(displayData.overall_score)}
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant={isMobile ? "subtitle1" : "h6"} gutterBottom={false} sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                    {getSentimentLabel(displayData.overall_score)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Score: {displayData.overall_score !== undefined ? displayData.overall_score.toFixed(2) : 'N/A'}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                      {getTrendIcon(displayData.trend)}
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                        {displayData.trend_value !== undefined ?
                          `${displayData.trend_value > 0 ? '+' : ''}${displayData.trend_value.toFixed(2)}` :
                          'N/A'}
                      </Typography>
                    </Box>
                  </Box>
                  {subscriptionFeatures.hasInsights && (
                    <Chip
                      label="AI Enhanced"
                      size="small"
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        color: ACE_COLORS.PURPLE,
                        fontWeight: 600,
                        mt: 1
                      }}
                    />
                  )}
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Enhanced Sentiment Trend Chart */}
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                {state.selectedTimeRange === '7d' ? '7-Day' : '30-Day'} Sentiment Trend
              </Typography>
              <Box sx={{ height: isMobile ? 120 : 150, mt: 1, mb: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={displayData.trend_data || []}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(ACE_COLORS.DARK, 0.1)} />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: isMobile ? 8 : 10, fill: ACE_COLORS.DARK }}
                      tickFormatter={(value) => value.split('-').slice(1).join('/')}
                    />
                    <YAxis
                      domain={[-1, 1]}
                      ticks={[-1, -0.5, 0, 0.5, 1]}
                      tick={{ fontSize: isMobile ? 8 : 10, fill: ACE_COLORS.DARK }}
                    />
                    <RechartsTooltip content={<CustomTooltip />} />
                    <ReferenceLine y={0} stroke={alpha(ACE_COLORS.DARK, 0.3)} />
                    <Line
                      type="monotone"
                      dataKey="score"
                      stroke={ACE_COLORS.PURPLE}
                      strokeWidth={2}
                      dot={{ r: 2, fill: ACE_COLORS.PURPLE }}
                      activeDot={{ r: 5, fill: ACE_COLORS.PURPLE }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>

              <Divider sx={{ my: 2 }} />

              {/* Enhanced Sentiment Distribution */}
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                Sentiment Distribution
              </Typography>
              <Grid container spacing={1} sx={{ mt: 1 }}>
                <Grid item xs={6} sm={3}>
                  <Chip
                    icon={<SentimentVerySatisfiedIcon />}
                    label={`Very Positive: ${displayData.distribution?.very_positive || 0}%`}
                    size="small"
                    sx={{
                      bgcolor: alpha('#2E7D32', 0.1),
                      color: '#2E7D32',
                      fontWeight: 'medium',
                      width: '100%',
                      justifyContent: 'flex-start'
                    }}
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Chip
                    icon={<SentimentSatisfiedIcon />}
                    label={`Positive: ${displayData.distribution?.positive || 0}%`}
                    size="small"
                    sx={{
                      bgcolor: alpha('#4CAF50', 0.1),
                      color: '#4CAF50',
                      fontWeight: 'medium',
                      width: '100%',
                      justifyContent: 'flex-start'
                    }}
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Chip
                    icon={<SentimentNeutralIcon />}
                    label={`Neutral: ${displayData.distribution?.neutral || 0}%`}
                    size="small"
                    sx={{
                      bgcolor: alpha(ACE_COLORS.DARK, 0.1),
                      color: ACE_COLORS.DARK,
                      fontWeight: 'medium',
                      width: '100%',
                      justifyContent: 'flex-start'
                    }}
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Chip
                    icon={<SentimentDissatisfiedIcon />}
                    label={`Negative: ${displayData.distribution?.negative || 0}%`}
                    size="small"
                    sx={{
                      bgcolor: alpha('#F44336', 0.1),
                      color: '#F44336',
                      fontWeight: 'medium',
                      width: '100%',
                      justifyContent: 'flex-start'
                    }}
                  />
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Chip
                    icon={<SentimentVeryDissatisfiedIcon />}
                    label={`Very Negative: ${displayData.distribution?.very_negative || 0}%`}
                    size="small"
                    sx={{
                      bgcolor: alpha('#C62828', 0.1),
                      color: '#C62828',
                      fontWeight: 'medium',
                      width: '100%',
                      justifyContent: 'flex-start'
                    }}
                  />
                </Grid>
              </Grid>

              {/* Enhanced Alert for Negative Content */}
              {(displayData.negative_content_count || 0) > 0 && (
                <Alert
                  severity="warning"
                  sx={{
                    mt: 2,
                    backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1),
                    border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
                    color: ACE_COLORS.DARK
                  }}
                >
                  <AlertTitle sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                    Attention Required
                  </AlertTitle>
                  {displayData.negative_content_count || 0} content items need review for negative sentiment
                </Alert>
              )}
            </div>
          </Fade>
        )}

        {/* Export Menu */}
        <Menu
          anchorEl={exportAnchorEl}
          open={state.showExportMenu}
          onClose={handleExportMenuClose}
          slotProps={{
            paper: {
              sx: {
                mt: 1,
                minWidth: 180,
                boxShadow: theme.shadows[8],
                border: `1px solid ${theme.palette.divider}`
              }
            }
          }}
        >
          <MenuItem onClick={() => {
            handleExport('csv');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as CSV</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => {
            handleExport('json');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as JSON</ListItemText>
          </MenuItem>
        </Menu>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={handleUpgradeDialogClose}
          maxWidth="md"
          fullWidth
          slotProps={{
            paper: {
              sx: {
                borderRadius: 2,
                boxShadow: theme.shadows[16]
              }
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span">
                {getUpgradeDialogContent().title}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {getUpgradeDialogContent().content}
              </Typography>

              <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                Unlock Advanced Sentiment Overview Features:
              </Typography>

              <Grid container spacing={1}>
                {getUpgradeDialogContent().features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                      <Typography variant="body2">{feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Alert
              severity="info"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
              Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced sentiment overview features.
            </Alert>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleUpgradeDialogClose}
              sx={{ color: theme.palette.text.secondary }}
            >
              Maybe Later
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                }
              }}
              startIcon={<UpgradeIcon />}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
SentimentOverviewCard.propTypes = {
  // Core props
  data: PropTypes.shape({
    overall_score: PropTypes.number,
    trend: PropTypes.oneOf(['improving', 'declining', 'stable']),
    trend_value: PropTypes.number,
    trend_data: PropTypes.arrayOf(
      PropTypes.shape({
        date: PropTypes.string,
        score: PropTypes.number,
      })
    ),
    distribution: PropTypes.shape({
      very_positive: PropTypes.number,
      positive: PropTypes.number,
      neutral: PropTypes.number,
      negative: PropTypes.number,
      very_negative: PropTypes.number,
    }),
    negative_content_count: PropTypes.number,
  }),
  loading: PropTypes.bool,
  onViewDetails: PropTypes.func,

  // Enhanced sentiment overview props
  overviewType: PropTypes.oneOf(['overall-summary', 'sentiment-trends', 'sentiment-distribution', 'sentiment-insights', 'sentiment-alerts']),
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  customization: PropTypes.object,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

SentimentOverviewCard.defaultProps = {
  loading: false,
  overviewType: 'overall-summary',
  enableExport: false,
  realTimeUpdates: false,
  customization: {},
  className: '',
  style: {},
  testId: 'sentiment-overview-card'
};

// Display name for debugging
SentimentOverviewCard.displayName = 'SentimentOverviewCard';

export default SentimentOverviewCard;

/**
 * Test Error Tracking System
 * Demonstrates the enhanced error tracking capabilities
 */

import errorTracker from './ErrorTracker';

/**
 * Test different types of errors to demonstrate tracking
 */
export const testErrorTracking = () => {
  console.log('🧪 Testing Error Tracking System...');
  
  // Test 1: JavaScript Error
  setTimeout(() => {
    try {
      console.log('Testing JavaScript error...');
      // This will trigger a ReferenceError
      undefinedVariable.someProperty;
    } catch (error) {
      console.log('✅ JavaScript error caught and tracked');
    }
  }, 1000);
  
  // Test 2: Promise Rejection
  setTimeout(() => {
    console.log('Testing promise rejection...');
    Promise.reject(new Error('Test promise rejection for error tracking'))
      .catch(() => {
        console.log('✅ Promise rejection caught and tracked');
      });
  }, 2000);
  
  // Test 3: Network Error Simulation
  setTimeout(() => {
    console.log('Testing network error simulation...');
    fetch('/api/nonexistent-endpoint')
      .catch(() => {
        console.log('✅ Network error caught and tracked');
      });
  }, 3000);
  
  // Test 4: Console Error
  setTimeout(() => {
    console.log('Testing console error...');
    console.error('Test console error for tracking system', {
      component: 'TestComponent',
      action: 'testAction',
      data: { test: true }
    });
    console.log('✅ Console error logged and tracked');
  }, 4000);
  
  // Test 5: Custom Error with Context
  setTimeout(() => {
    console.log('Testing custom error with context...');
    const customError = new Error('Custom test error with detailed context');
    customError.component = 'TestErrorComponent';
    customError.action = 'testCustomError';
    customError.context = {
      userId: 'test-user-123',
      feature: 'error-tracking-test',
      timestamp: Date.now()
    };
    
    // Manually trigger error tracking
    errorTracker._handleError({
      type: 'custom_test',
      message: customError.message,
      error: customError,
      stack: customError.stack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      component: customError.component,
      context: customError.context
    });
    
    console.log('✅ Custom error with context tracked');
  }, 5000);
  
  // Show summary after all tests
  setTimeout(() => {
    console.log('📊 Error Tracking Test Summary:');
    const summary = errorTracker.getErrorSummary();
    console.table(summary);
    
    console.log('🎯 Error References Generated:');
    summary.recentErrors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.errorId || 'Unknown ID'}`);
      console.log(`   Type: ${error.type}`);
      console.log(`   File: ${error.filename || 'Unknown'}`);
      console.log(`   Component: ${error.component || 'Unknown'}`);
      console.log(`   Line: ${error.lineno || 'Unknown'}`);
      console.log('');
    });
    
    console.log('✅ Error tracking test completed!');
    console.log('💡 Visit /dev/errors in development mode to see the error dashboard');
  }, 6000);
};

/**
 * Test error boundary integration
 */
export const testErrorBoundary = () => {
  console.log('🧪 Testing Error Boundary Integration...');
  
  // Simulate a React component error
  const simulateComponentError = () => {
    const error = new Error('Simulated React component error');
    error.componentStack = `
    in TestComponent (at TestComponent.jsx:45)
    in ErrorBoundary (at App.jsx:123)
    in App (at index.js:7)`;
    
    // Simulate error boundary catch
    errorTracker._handleError({
      type: 'react_error_boundary',
      message: error.message,
      error: error,
      stack: error.stack,
      componentStack: error.componentStack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      errorBoundary: 'ProductionErrorBoundary',
      severity: 'high'
    });
    
    console.log('✅ Error boundary integration tested');
  };
  
  setTimeout(simulateComponentError, 1000);
};

/**
 * Test error analysis and recommendations
 */
export const testErrorAnalysis = () => {
  console.log('🧪 Testing Error Analysis System...');
  
  const testCases = [
    {
      name: 'Chunk Loading Error',
      error: {
        type: 'javascript',
        message: 'ChunkLoadError: Loading chunk 5 failed',
        filename: '/static/js/chunk.js',
        severity: 'high'
      }
    },
    {
      name: 'WebSocket Error',
      error: {
        type: 'websocket',
        message: 'WebSocket connection failed',
        filename: '/src/services/WebSocketService.js',
        severity: 'medium'
      }
    },
    {
      name: 'Null Reference Error',
      error: {
        type: 'javascript',
        message: 'Cannot read property of undefined',
        filename: '/src/components/Dashboard.jsx',
        severity: 'medium'
      }
    },
    {
      name: 'Network Error',
      error: {
        type: 'promise_rejection',
        message: 'Network request failed',
        filename: '/src/api/client.js',
        severity: 'medium'
      }
    }
  ];
  
  testCases.forEach((testCase, index) => {
    setTimeout(() => {
      console.log(`Testing ${testCase.name}...`);
      
      errorTracker._handleError({
        ...testCase.error,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent
      });
      
      console.log(`✅ ${testCase.name} analyzed and tracked`);
    }, (index + 1) * 1000);
  });
  
  setTimeout(() => {
    console.log('📊 Error Analysis Test Completed');
    console.log('Check the error dashboard for detailed analysis and recommendations');
  }, testCases.length * 1000 + 1000);
};

/**
 * Run all error tracking tests
 */
export const runAllErrorTests = () => {
  console.log('🚀 Running Comprehensive Error Tracking Tests...');
  
  testErrorTracking();
  
  setTimeout(() => {
    testErrorBoundary();
  }, 7000);
  
  setTimeout(() => {
    testErrorAnalysis();
  }, 9000);
  
  setTimeout(() => {
    console.log('🎉 All error tracking tests completed!');
    console.log('📈 Check the browser console and /dev/errors dashboard for results');
  }, 15000);
};

// Auto-run tests in development mode if requested
if (process.env.NODE_ENV === 'development' && window.location.search.includes('test-errors')) {
  console.log('🔧 Auto-running error tracking tests...');
  setTimeout(runAllErrorTests, 2000);
}

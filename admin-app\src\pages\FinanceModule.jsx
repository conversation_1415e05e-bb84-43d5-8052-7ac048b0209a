// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Skeleton,
  Snackbar,
  LinearProgress,
} from '@mui/material';
import {
  AttachMoney as RevenueIcon,
  TrendingUp as GrowthIcon,
  People as CustomersIcon,
  Assessment as AnalyticsIcon,
  Payment as PaymentIcon,
  Download as ExportIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  Security as SecurityIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import { useTheme } from '@mui/material/styles';

// Components
import StablePageWrapper from '../components/common/StablePageWrapper';
import FinancialDashboard from '../components/finance/FinancialDashboard';
import RevenueAnalytics from '../components/finance/RevenueAnalytics';
import TransactionManagement from '../components/finance/TransactionManagement';
import CustomerAnalytics from '../components/finance/CustomerAnalytics';
import PaymentMonitoring from '../components/finance/PaymentMonitoring';
import FinancialReports from '../components/finance/FinancialReports';

// API
import api from '../api';

const FinanceModule = () => {
  const theme = useTheme();
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Date range for analytics
  const [dateRange, setDateRange] = useState({
    start: startOfMonth(subMonths(new Date(), 1)),
    end: endOfMonth(subMonths(new Date(), 1))
  });

  // Production-ready state management
  const [correlationId, setCorrelationId] = useState(null);
  const [securityStatus, setSecurityStatus] = useState('checking');
  const [performanceMetrics, setPerformanceMetrics] = useState({
    loadTime: null,
    responseTime: null,
    cacheHit: false
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  const [retryCount, setRetryCount] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Enhanced tabs with security monitoring
  const tabs = useMemo(() => [
    {
      label: 'Dashboard',
      icon: <AnalyticsIcon />,
      component: FinancialDashboard,
      description: 'Financial overview and key metrics'
    },
    {
      label: 'Revenue Analytics',
      icon: <RevenueIcon />,
      component: RevenueAnalytics,
      description: 'Revenue trends and analysis'
    },
    {
      label: 'Transactions',
      icon: <PaymentIcon />,
      component: TransactionManagement,
      description: 'Transaction management and monitoring'
    },
    {
      label: 'Customer Analytics',
      icon: <CustomersIcon />,
      component: CustomerAnalytics,
      description: 'Customer insights and lifetime value'
    },
    {
      label: 'Payment Monitoring',
      icon: <WarningIcon />,
      component: PaymentMonitoring,
      description: 'Payment failures and retry monitoring'
    },
    {
      label: 'Security Audit',
      icon: <SecurityIcon />,
      component: FinancialReports,
      description: 'Security monitoring and compliance'
    },
    {
      label: 'Reports',
      icon: <ExportIcon />,
      component: FinancialReports,
      description: 'Financial reports and exports'
    },
  ], []);

  useEffect(() => {
    loadDashboardData();
  }, []);

  // Enhanced data loading with performance monitoring and error handling
  const loadDashboardData = useCallback(async (forceRefresh = false) => {
    const startTime = performance.now();

    try {
      setLoading(true);
      setError(null);
      setIsRefreshing(forceRefresh);

      // Add correlation ID for tracking
      const headers = {
        'X-Correlation-ID': `finance-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      };

      const url = `/api/admin/finance/dashboard${forceRefresh ? '?force_refresh=true' : ''}`;
      const response = await api.get(url, { headers });

      // Track performance metrics
      const loadTime = performance.now() - startTime;
      setPerformanceMetrics(prev => ({
        ...prev,
        loadTime,
        responseTime: loadTime,
        cacheHit: !forceRefresh && loadTime < 100 // Assume cache hit if very fast
      }));

      setDashboardData(response.data);
      setLastUpdated(new Date());
      setCorrelationId(response.data.correlation_id || headers['X-Correlation-ID']);
      setRetryCount(0); // Reset retry count on success

      // Show success message for manual refresh
      if (forceRefresh) {
        setSnackbar({
          open: true,
          message: 'Financial data refreshed successfully',
          severity: 'success'
        });
      }

      // Check security status
      if (response.data.security_metadata) {
        setSecurityStatus('secure');
      }

    } catch (err) {
      console.error('Error loading dashboard data:', err);

      const errorMessage = err.response?.data?.detail?.message ||
                          err.response?.data?.detail ||
                          'Failed to load financial data';

      setError(errorMessage);
      setCorrelationId(err.response?.data?.detail?.correlation_id || null);

      // Implement retry logic for transient errors
      if (err.response?.status >= 500 && retryCount < 3) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          loadDashboardData(forceRefresh);
        }, Math.pow(2, retryCount) * 1000); // Exponential backoff
      }

      setSnackbar({
        open: true,
        message: `Error loading data: ${errorMessage}`,
        severity: 'error'
      });

    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  }, [retryCount]);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  // Enhanced refresh with user feedback
  const handleRefresh = useCallback(() => {
    loadDashboardData(true);
  }, [loadDashboardData]);

  // Handle snackbar close
  const handleSnackbarClose = useCallback((event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  // Security status indicator
  const getSecurityStatusColor = () => {
    switch (securityStatus) {
      case 'secure': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  };

  const getSecurityStatusIcon = () => {
    switch (securityStatus) {
      case 'secure': return <CheckCircleIcon />;
      case 'warning': return <WarningIcon />;
      case 'error': return <ErrorIcon />;
      default: return <SecurityIcon />;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  // Enhanced loading state with skeleton
  if (loading && !dashboardData) {
    return (
      <StablePageWrapper>
        <Box sx={{ mb: 3 }}>
          <Skeleton variant="text" width="40%" height={48} />
          <Skeleton variant="text" width="60%" height={24} />
        </Box>

        <Grid container spacing={3} sx={{ mb: 3 }}>
          {[...Array(4)].map((_, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card>
                <CardContent>
                  <Skeleton variant="text" width="80%" height={20} />
                  <Skeleton variant="text" width="60%" height={32} />
                  <Skeleton variant="text" width="40%" height={16} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Card>
          <CardContent>
            <Skeleton variant="rectangular" height={400} />
          </CardContent>
        </Card>
      </StablePageWrapper>
    );
  }

  const CurrentTabComponent = tabs[currentTab].component;

  return (
    <StablePageWrapper>
      {/* Loading progress bar */}
      {(loading || isRefreshing) && (
        <LinearProgress
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            zIndex: theme.zIndex.appBar + 1
          }}
        />
      )}

      {/* Header with enhanced styling */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="flex-start"
        mb={theme.spacing(3)}
        sx={{
          flexDirection: { xs: 'column', md: 'row' },
          gap: { xs: theme.spacing(2), md: 0 }
        }}
      >
        <Box sx={{ flex: 1 }}>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 600,
              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Finance & Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
            Production-ready financial management with comprehensive security
          </Typography>

          {/* Performance and security indicators */}
          <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
            {performanceMetrics.loadTime && (
              <Chip
                size="small"
                icon={performanceMetrics.cacheHit ? <CheckCircleIcon /> : <AnalyticsIcon />}
                label={`${performanceMetrics.loadTime.toFixed(0)}ms ${performanceMetrics.cacheHit ? '(cached)' : ''}`}
                color={performanceMetrics.loadTime < 500 ? 'success' : 'warning'}
                variant="outlined"
              />
            )}
            <Chip
              size="small"
              icon={getSecurityStatusIcon()}
              label={`Security: ${securityStatus}`}
              color={getSecurityStatusColor()}
              variant="outlined"
            />
            {correlationId && (
              <Chip
                size="small"
                label={`ID: ${correlationId.slice(-8)}`}
                variant="outlined"
                sx={{ fontFamily: 'monospace' }}
              />
            )}
          </Box>
        </Box>

        <Box display="flex" gap={1} alignItems="center">
          <Tooltip title="Refresh Data (Force)">
            <IconButton
              onClick={handleRefresh}
              disabled={loading || isRefreshing}
              color="primary"
              sx={{
                '&:hover': {
                  transform: 'rotate(180deg)',
                  transition: 'transform 0.3s ease-in-out'
                }
              }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="outlined"
            startIcon={<ExportIcon />}
            onClick={() => setCurrentTab(6)} // Switch to Reports tab
            disabled={loading}
            sx={{
              borderRadius: theme.spacing(1),
              textTransform: 'none'
            }}
          >
            Export Reports
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Key Metrics Cards */}
      {dashboardData && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Monthly Recurring Revenue
                    </Typography>
                    <Typography variant="h5" component="div">
                      {formatCurrency(dashboardData.current_mrr)}
                    </Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <GrowthIcon 
                        fontSize="small" 
                        color={dashboardData.mrr_growth >= 0 ? "success" : "error"}
                      />
                      <Typography 
                        variant="body2" 
                        color={dashboardData.mrr_growth >= 0 ? "success.main" : "error.main"}
                        sx={{ ml: 0.5 }}
                      >
                        {formatPercentage(dashboardData.mrr_growth)}
                      </Typography>
                    </Box>
                  </Box>
                  <RevenueIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Total Customers
                    </Typography>
                    <Typography variant="h5" component="div">
                      {dashboardData.total_customers.toLocaleString()}
                    </Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <GrowthIcon 
                        fontSize="small" 
                        color={dashboardData.customer_growth >= 0 ? "success" : "error"}
                      />
                      <Typography 
                        variant="body2" 
                        color={dashboardData.customer_growth >= 0 ? "success.main" : "error.main"}
                        sx={{ ml: 0.5 }}
                      >
                        {formatPercentage(dashboardData.customer_growth)}
                      </Typography>
                    </Box>
                  </Box>
                  <CustomersIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Churn Rate
                    </Typography>
                    <Typography variant="h5" component="div">
                      {formatPercentage(dashboardData.churn_rate)}
                    </Typography>
                    <Chip 
                      label={dashboardData.churn_rate <= 0.05 ? "Healthy" : "Attention Needed"}
                      color={dashboardData.churn_rate <= 0.05 ? "success" : "warning"}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </Box>
                  <AnalyticsIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Payment Success Rate
                    </Typography>
                    <Typography variant="h5" component="div">
                      {formatPercentage(dashboardData.payment_success_rate)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      {dashboardData.failed_payments} failed payments
                    </Typography>
                  </Box>
                  <PaymentIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Last Updated Info */}
      {lastUpdated && (
        <Box display="flex" justifyContent="flex-end" mb={2}>
          <Typography variant="caption" color="text.secondary">
            Last updated: {format(lastUpdated, 'MMM dd, yyyy HH:mm')}
          </Typography>
        </Box>
      )}

      {/* Tabs */}
      <Card>
        <CardHeader
          title={
            <Tabs 
              value={currentTab} 
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
            >
              {tabs.map((tab, index) => (
                <Tab
                  key={index}
                  label={tab.label}
                  icon={tab.icon}
                  iconPosition="start"
                />
              ))}
            </Tabs>
          }
        />
        <CardContent>
          <CurrentTabComponent 
            dashboardData={dashboardData}
            dateRange={dateRange}
            onDateRangeChange={setDateRange}
            onRefresh={handleRefresh}
          />
        </CardContent>
      </Card>

      {/* Enhanced Snackbar for user feedback */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
          {correlationId && (
            <Typography variant="caption" display="block" sx={{ mt: 0.5, opacity: 0.8 }}>
              Correlation ID: {correlationId.slice(-8)}
            </Typography>
          )}
        </Alert>
      </Snackbar>
    </StablePageWrapper>
  );
};

export default FinanceModule;

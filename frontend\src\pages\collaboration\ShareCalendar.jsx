// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  Chip,
  CircularProgress,
  Alert,
  FormControlLabel,
  Switch,
  IconButton,
  Card,
  CardContent,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  Autocomplete,
  Snackbar,
} from "@mui/material";
import {
  Share as ShareIcon,
  ContentCopy as CopyIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Link as LinkIcon,
  CalendarMonth as CalendarIcon,

  AccessTime as AccessTimeIcon,
  People as PeopleIcon,
} from "@mui/icons-material";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { format, addMonths } from "date-fns";
import { useAdvancedToast } from "../../hooks/useAdvancedToast";
import {
  shareCalendar,
  getUserSharedCalendars,
  deleteSharedCalendar,
} from "../../api/collaboration";
import { getContents } from "../../api/content";
import { getCampaigns } from "../../api/campaigns";

const ShareCalendar = () => {
  const navigate = useNavigate();

  // Create a fallback notification system in case the SnackbarProvider is not available
  const [localSnackbar, setLocalSnackbar] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // Use the new advanced toast system
  const { showSuccess, showError } = useAdvancedToast();

  // State
  const [loading, setLoading] = useState(false);
  const [loadingShared, setLoadingShared] = useState(true);
  const [calendarName, setCalendarName] = useState("");
  const [description, setDescription] = useState("");
  const [expirationDate, setExpirationDate] = useState(
    addMonths(new Date(), 1)
  );
  const [requiresApproval, setRequiresApproval] = useState(true);
  const [collaborators, setCollaborators] = useState([{ name: "", email: "" }]);
  const [selectedContents, setSelectedContents] = useState([]);
  const [selectedCampaigns, setSelectedCampaigns] = useState([]);
  const [availableContents, setAvailableContents] = useState([]);
  const [availableCampaigns, setAvailableCampaigns] = useState([]);
  const [sharedCalendars, setSharedCalendars] = useState([]);
  const [openShareDialog, setOpenShareDialog] = useState(false);
  const [shareUrl, setShareUrl] = useState("");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [calendarToDelete, setCalendarToDelete] = useState(null);

  // Load available content and campaigns
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch content
        const contentsResponse = await getContents();
        setAvailableContents(contentsResponse);

        // Fetch campaigns
        const campaignsResponse = await getCampaigns();
        setAvailableCampaigns(campaignsResponse);

        // Fetch shared calendars
        await fetchSharedCalendars();
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error("Error fetching data:", error);
        }
        showError("Failed to load content and campaigns");
      }
    };

    fetchData();
  }, [showError, fetchSharedCalendars]);

  // Fetch shared calendars
  const fetchSharedCalendars = useCallback(async () => {
    setLoadingShared(true);
    try {
      const calendars = await getUserSharedCalendars();
      setSharedCalendars(calendars);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error("Error fetching shared calendars:", error);
      }
      showError("Failed to load shared calendars");
    } finally {
      setLoadingShared(false);
    }
  }, [showError]);

  // Add collaborator
  const handleAddCollaborator = () => {
    setCollaborators([...collaborators, { name: "", email: "" }]);
  };

  // Update collaborator
  const handleCollaboratorChange = (index, field, value) => {
    const updatedCollaborators = [...collaborators];
    updatedCollaborators[index][field] = value;
    setCollaborators(updatedCollaborators);
  };

  // Remove collaborator
  const handleRemoveCollaborator = (index) => {
    const updatedCollaborators = [...collaborators];
    updatedCollaborators.splice(index, 1);
    setCollaborators(updatedCollaborators);
  };

  // Share calendar
  const handleShareCalendar = async () => {
    // Validate inputs
    if (!calendarName) {
      showError("Please provide a calendar name");
      return;
    }

    if (
      collaborators.length === 0 ||
      !collaborators.every((c) => c.name && c.email)
    ) {
      showError(
        "Please provide name and email for all collaborators"
      );
      return;
    }

    if (selectedContents.length === 0 && selectedCampaigns.length === 0) {
      showError(
        "Please select at least one content or campaign to share"
      );
      return;
    }

    setLoading(true);
    try {
      const shareData = {
        calendar_name: calendarName,
        description,
        content_ids: selectedContents.map((content) => content.id),
        campaign_ids: selectedCampaigns.map((campaign) => campaign.id),
        requires_approval: requiresApproval,
        expiration_date: expirationDate,
        collaborators,
      };

      const result = await shareCalendar(shareData);

      // Show success message
      showSuccess("Calendar shared successfully");

      // Reset form
      setCalendarName("");
      setDescription("");
      setExpirationDate(addMonths(new Date(), 1));
      setRequiresApproval(true);
      setCollaborators([{ name: "", email: "" }]);
      setSelectedContents([]);
      setSelectedCampaigns([]);

      // Refresh shared calendars
      await fetchSharedCalendars();

      // Show share dialog with URL
      const accessToken = result.collaborator_accesses[0].access_token;
      const shareUrl = `${window.location.origin}/shared-calendar/${accessToken}`;
      setShareUrl(shareUrl);
      setOpenShareDialog(true);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error("Error sharing calendar:", error);
      }
      showError("Failed to share calendar. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Copy share URL to clipboard
  const handleCopyShareUrl = () => {
    navigator.clipboard.writeText(shareUrl);
    showSuccess("Share URL copied to clipboard");
  };

  // Delete shared calendar
  const handleDeleteCalendar = async () => {
    if (!calendarToDelete) return;

    try {
      await deleteSharedCalendar(calendarToDelete.id);

      // Remove from local state
      setSharedCalendars(
        sharedCalendars.filter(
          (calendar) => calendar.id !== calendarToDelete.id
        )
      );

      // Close dialog
      setOpenDeleteDialog(false);
      setCalendarToDelete(null);

      showSuccess("Shared calendar deleted successfully");
    } catch (error) {
      console.error("Error deleting shared calendar:", error);
      showError("Failed to delete shared calendar");
    }
  };

  // Handle navigation to shared calendar view
  const handleViewSharedCalendar = (accessToken) => {
    navigate(`/shared-calendar/${accessToken}`);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3, maxWidth: 1200, mx: "auto" }}>
        <Typography variant="h4" gutterBottom>
          Share Calendar
        </Typography>

        <Grid container spacing={3}>
          {/* Share Form */}
          <Grid item xs={12} md={7}>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Create Shareable Calendar
              </Typography>

              <TextField
                label="Calendar Name"
                value={calendarName}
                onChange={(e) => setCalendarName(e.target.value)}
                fullWidth
                margin="normal"
                required
              />

              <TextField
                label="Description (Optional)"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                fullWidth
                margin="normal"
                multiline
                rows={2}
              />

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Select Content
                </Typography>

                <Autocomplete
                  multiple
                  options={availableContents}
                  getOptionLabel={(option) => option.title}
                  value={selectedContents}
                  onChange={(_, newValue) => setSelectedContents(newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Content"
                      placeholder="Search content"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option.title}
                        {...getTagProps({ index })}
                        key={option.id}
                      />
                    ))
                  }
                />
              </Box>

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Select Campaigns
                </Typography>

                <Autocomplete
                  multiple
                  options={availableCampaigns}
                  getOptionLabel={(option) => option.name}
                  value={selectedCampaigns}
                  onChange={(_, newValue) => setSelectedCampaigns(newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Campaigns"
                      placeholder="Search campaigns"
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip
                        label={option.name}
                        {...getTagProps({ index })}
                        key={option.id}
                      />
                    ))
                  }
                />
              </Box>

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Share Settings
                </Typography>

                <DateTimePicker
                  label="Expiration Date"
                  value={expirationDate}
                  onChange={(newValue) => setExpirationDate(newValue)}
                  sx={{ width: "100%", mb: 2 }}
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={requiresApproval}
                      onChange={(e) => setRequiresApproval(e.target.checked)}
                    />
                  }
                  label="Require Content Approval"
                />
              </Box>

              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Collaborators
                </Typography>

                {collaborators.map((collaborator, index) => (
                  <Box key={index} sx={{ display: "flex", gap: 2, mb: 2 }}>
                    <TextField
                      label="Name"
                      value={collaborator.name}
                      onChange={(e) =>
                        handleCollaboratorChange(index, "name", e.target.value)
                      }
                      sx={{ flex: 1 }}
                      required
                    />
                    <TextField
                      label="Email"
                      value={collaborator.email}
                      onChange={(e) =>
                        handleCollaboratorChange(index, "email", e.target.value)
                      }
                      sx={{ flex: 1 }}
                      type="email"
                      required
                    />
                    <IconButton
                      color="error"
                      onClick={() => handleRemoveCollaborator(index)}
                      disabled={collaborators.length === 1}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                ))}

                <Button
                  startIcon={<AddIcon />}
                  onClick={handleAddCollaborator}
                  variant="outlined"
                  sx={{ mt: 1 }}
                >
                  Add Collaborator
                </Button>
              </Box>

              <Box sx={{ mt: 4, display: "flex", justifyContent: "flex-end" }}>
                <Button
                  variant="contained"
                  startIcon={
                    loading ? <CircularProgress size={20} /> : <ShareIcon />
                  }
                  onClick={handleShareCalendar}
                  disabled={loading}
                >
                  Share Calendar
                </Button>
              </Box>
            </Paper>
          </Grid>

          {/* Shared Calendars */}
          <Grid item xs={12} md={5}>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Your Shared Calendars
              </Typography>

              {loadingShared ? (
                <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : sharedCalendars.length === 0 ? (
                <Alert severity="info">
                  You haven&apos;t shared any calendars yet.
                </Alert>
              ) : (
                <List>
                  {sharedCalendars.map((calendar) => (
                    <Card key={calendar.id} sx={{ mb: 2 }}>
                      <CardContent>
                        <Typography variant="h6">{calendar.name}</Typography>

                        {calendar.description && (
                          <Typography
                            variant="body2"
                            color="textSecondary"
                            paragraph
                          >
                            {calendar.description}
                          </Typography>
                        )}

                        <Box
                          sx={{
                            display: "flex",
                            flexWrap: "wrap",
                            gap: 1,
                            mb: 1,
                          }}
                        >
                          <Chip
                            icon={<CalendarIcon />}
                            label={`${calendar.content_ids.length} Contents`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            icon={<PeopleIcon />}
                            label={`${calendar.access_count} Views`}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            icon={<AccessTimeIcon />}
                            label={`Expires: ${format(
                              new Date(calendar.expiration_date),
                              "MMM d, yyyy"
                            )}`}
                            size="small"
                            variant="outlined"
                            color={
                              new Date(calendar.expiration_date) < new Date()
                                ? "error"
                                : "default"
                            }
                          />
                        </Box>
                      </CardContent>
                      <CardActions>
                        <Button
                          startIcon={<CalendarIcon />}
                          size="small"
                          variant="outlined"
                          onClick={() => handleViewSharedCalendar(calendar.share_token)}
                        >
                          View
                        </Button>
                        <Button
                          startIcon={<LinkIcon />}
                          size="small"
                          onClick={() => {
                            // In a real implementation, you would get the share URL
                            const shareUrl = `${window.location.origin}/shared-calendar/${calendar.share_token}`;
                            navigator.clipboard.writeText(shareUrl);
                            showSuccess(
                              "Share URL copied to clipboard"
                            );
                          }}
                        >
                          Copy Link
                        </Button>
                        <Button
                          startIcon={<DeleteIcon />}
                          size="small"
                          color="error"
                          onClick={() => {
                            setCalendarToDelete(calendar);
                            setOpenDeleteDialog(true);
                          }}
                        >
                          Delete
                        </Button>
                      </CardActions>
                    </Card>
                  ))}
                </List>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Share URL Dialog */}
        <Dialog
          open={openShareDialog}
          onClose={() => setOpenShareDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Calendar Shared Successfully</DialogTitle>
          <DialogContent>
            <Typography paragraph>
              Your calendar has been shared with the collaborators. They will
              receive an email with the link to access the shared calendar.
            </Typography>

            <Typography variant="subtitle2" gutterBottom>
              Share URL:
            </Typography>

            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <TextField
                value={shareUrl}
                fullWidth
                variant="outlined"
                InputProps={{
                  readOnly: true,
                }}
              />
              <IconButton onClick={handleCopyShareUrl} sx={{ ml: 1 }}>
                <CopyIcon />
              </IconButton>
            </Box>

            <Alert severity="info">
              Anyone with this link can access the shared calendar until the
              expiration date.
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenShareDialog(false)}>Close</Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={openDeleteDialog}
          onClose={() => setOpenDeleteDialog(false)}
        >
          <DialogTitle>Delete Shared Calendar</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete the shared calendar &quot;
              {calendarToDelete?.name}&quot;? This will revoke access for all
              collaborators.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
            <Button
              onClick={handleDeleteCalendar}
              color="error"
              variant="contained"
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Local fallback snackbar */}
        <Snackbar
          open={localSnackbar.open}
          autoHideDuration={6000}
          onClose={() => setLocalSnackbar({ ...localSnackbar, open: false })}
          anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        >
          <Alert
            onClose={() => setLocalSnackbar({ ...localSnackbar, open: false })}
            severity={localSnackbar.severity}
            variant="filled"
          >
            {localSnackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default ShareCalendar;

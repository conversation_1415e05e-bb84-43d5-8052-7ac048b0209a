#!/usr/bin/env node
// @since 2024-1-1 to 2025-25-7

/**
 * Clear Vite Cache Script
 * Clears all Vite-related cache directories and files
 */

const fs = require('fs');
const path = require('path');

function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    try {
      fs.rmSync(folderPath, { recursive: true, force: true });
      console.log(`✅ Deleted: ${folderPath}`);
      return true;
    } catch (error) {
      console.log(`❌ Failed to delete ${folderPath}: ${error.message}`);
      return false;
    }
  } else {
    console.log(`ℹ️  Not found: ${folderPath}`);
    return true;
  }
}

function clearViteCache() {
  console.log('🧹 Clearing Vite cache...\n');
  
  const cachePaths = [
    path.join(__dirname, 'node_modules', '.vite'),
    path.join(__dirname, '.vite'),
    path.join(__dirname, '..', '.vite'),
    path.join(__dirname, 'dist'),
  ];
  
  let allCleared = true;
  
  cachePaths.forEach(cachePath => {
    const success = deleteFolderRecursive(cachePath);
    if (!success) allCleared = false;
  });
  
  // Also try to clear npm cache for this project
  try {
    const { execSync } = require('child_process');
    execSync('npm cache clean --force', { stdio: 'inherit' });
    console.log('✅ NPM cache cleared');
  } catch (error) {
    console.log('⚠️  Could not clear NPM cache:', error.message);
  }
  
  console.log('\n' + (allCleared ? '✅ Cache clearing completed!' : '⚠️  Some cache files could not be cleared'));
  console.log('💡 You can now run: npm run dev');
}

clearViteCache();

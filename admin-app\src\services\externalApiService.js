/**
 * External API Management Service
 * 
 * Provides comprehensive external API management functionality including:
 * - CRUD operations for API configurations
 * - Secure credential management
 * - Health monitoring and testing
 * - Usage analytics and statistics
 * - Bulk operations
 * - Import/export functionality
 @since 2024-1-1 to 2025-25-7
*/

import api from '../api/index.js';

class ExternalApiService {
  constructor() {
    this.baseUrl = '/api/admin/external-apis';
    this.cache = new Map();
    this.cacheTimeout = 60000; // 1 minute cache
  }

  /**
   * Get cached data if available and not expired
   */
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * Set cached data with timestamp
   */
  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Clear cache for specific key or all cache
   */
  clearCache(key = null) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Get system information
   */
  async getSystemInfo() {
    try {
      const response = await api.get(this.baseUrl);
      return response.data;
    } catch (error) {
      console.error('Failed to get system info:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load system information');
    }
  }

  /**
   * Create new external API configuration
   */
  async createApiConfiguration(configData) {
    try {
      const response = await api.post(`${this.baseUrl}/configurations`, configData);
      
      // Clear relevant cache
      this.clearCache('api-configurations');
      this.clearCache('api-statistics');
      
      return response.data;
    } catch (error) {
      console.error('Failed to create API configuration:', error);
      throw new Error(error.response?.data?.detail || 'Failed to create API configuration');
    }
  }

  /**
   * Get list of API configurations with filtering and pagination
   */
  async getApiConfigurations(filters = {}) {
    const cacheKey = `api-configurations-${JSON.stringify(filters)}`;
    
    // Try cache first
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const params = {};
      if (filters.provider) params.provider = filters.provider;
      if (filters.status) params.status = filters.status;
      if (filters.environment) params.environment = filters.environment;
      if (filters.page) params.page = filters.page;
      if (filters.page_size) params.page_size = filters.page_size;

      const response = await api.get(`${this.baseUrl}/configurations`, { params });
      
      const data = response.data;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to get API configurations:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load API configurations');
    }
  }

  /**
   * Get specific API configuration by ID
   */
  async getApiConfiguration(apiId) {
    const cacheKey = `api-configuration-${apiId}`;
    
    // Try cache first
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get(`${this.baseUrl}/configurations/${apiId}`);
      
      const data = response.data;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to get API configuration:', error);
      if (error.response?.status === 404) {
        throw new Error('API configuration not found');
      }
      throw new Error(error.response?.data?.detail || 'Failed to load API configuration');
    }
  }

  /**
   * Update API configuration
   */
  async updateApiConfiguration(apiId, updateData) {
    try {
      const response = await api.put(`${this.baseUrl}/configurations/${apiId}`, updateData);
      
      // Clear relevant cache
      this.clearCache(`api-configuration-${apiId}`);
      this.clearCache('api-configurations');
      this.clearCache('api-statistics');
      
      return response.data;
    } catch (error) {
      console.error('Failed to update API configuration:', error);
      if (error.response?.status === 404) {
        throw new Error('API configuration not found');
      }
      throw new Error(error.response?.data?.detail || 'Failed to update API configuration');
    }
  }

  /**
   * Delete API configuration
   */
  async deleteApiConfiguration(apiId) {
    try {
      await api.delete(`${this.baseUrl}/configurations/${apiId}`);
      
      // Clear relevant cache
      this.clearCache(`api-configuration-${apiId}`);
      this.clearCache('api-configurations');
      this.clearCache('api-statistics');
      
      return true;
    } catch (error) {
      console.error('Failed to delete API configuration:', error);
      if (error.response?.status === 404) {
        throw new Error('API configuration not found');
      }
      throw new Error(error.response?.data?.detail || 'Failed to delete API configuration');
    }
  }

  /**
   * Test API credentials
   */
  async testApiCredentials(apiId) {
    try {
      const response = await api.post(`${this.baseUrl}/configurations/${apiId}/test`);
      return response.data;
    } catch (error) {
      console.error('Failed to test API credentials:', error);
      if (error.response?.status === 404) {
        throw new Error('API configuration not found');
      }
      throw new Error(error.response?.data?.detail || 'Failed to test API credentials');
    }
  }

  /**
   * Get comprehensive API statistics
   */
  async getApiStatistics() {
    const cacheKey = 'api-statistics';
    
    // Try cache first
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get(`${this.baseUrl}/statistics`);
      
      const data = response.data;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to get API statistics:', error);
      throw new Error(error.response?.data?.detail || 'Failed to load API statistics');
    }
  }

  /**
   * Search API configurations
   */
  async searchApiConfigurations(query, filters = {}) {
    try {
      const allConfigs = await this.getApiConfigurations(filters);
      
      if (!query) return allConfigs;

      const searchTerm = query.toLowerCase();
      const filteredApis = allConfigs.apis.filter(api => 
        api.name.toLowerCase().includes(searchTerm) ||
        api.description?.toLowerCase().includes(searchTerm) ||
        api.provider.toLowerCase().includes(searchTerm) ||
        api.base_url.toLowerCase().includes(searchTerm) ||
        api.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );

      return {
        ...allConfigs,
        apis: filteredApis,
        total: filteredApis.length
      };
    } catch (error) {
      console.error('Failed to search API configurations:', error);
      throw new Error('Failed to search API configurations');
    }
  }

  /**
   * Get API configurations by provider
   */
  async getApiConfigurationsByProvider(provider) {
    return this.getApiConfigurations({ provider });
  }

  /**
   * Get API configurations by status
   */
  async getApiConfigurationsByStatus(status) {
    return this.getApiConfigurations({ status });
  }

  /**
   * Get API configurations by environment
   */
  async getApiConfigurationsByEnvironment(environment) {
    return this.getApiConfigurations({ environment });
  }

  /**
   * Get healthy API configurations
   */
  async getHealthyApiConfigurations() {
    try {
      const allConfigs = await this.getApiConfigurations();
      const healthyApis = allConfigs.apis.filter(api => api.health_status === 'healthy');
      
      return {
        ...allConfigs,
        apis: healthyApis,
        total: healthyApis.length
      };
    } catch (error) {
      console.error('Failed to get healthy API configurations:', error);
      throw new Error('Failed to load healthy API configurations');
    }
  }

  /**
   * Get API configurations with issues (degraded or down)
   */
  async getApiConfigurationsWithIssues() {
    try {
      const allConfigs = await this.getApiConfigurations();
      const problematicApis = allConfigs.apis.filter(api => 
        api.health_status === 'degraded' || api.health_status === 'down'
      );
      
      return {
        ...allConfigs,
        apis: problematicApis,
        total: problematicApis.length
      };
    } catch (error) {
      console.error('Failed to get API configurations with issues:', error);
      throw new Error('Failed to load API configurations with issues');
    }
  }

  /**
   * Get supported providers list
   */
  getSupportedProviders() {
    return [
      { value: 'openai', label: 'OpenAI', category: 'AI' },
      { value: 'lemon_squeezy', label: 'Lemon Squeezy', category: 'Payment' },
      { value: 'sendgrid', label: 'SendGrid', category: 'Email' },
      { value: 'smtp', label: 'SMTP', category: 'Email' },
      { value: 'facebook', label: 'Facebook', category: 'Social Media' },
      { value: 'twitter', label: 'Twitter', category: 'Social Media' },
      { value: 'instagram', label: 'Instagram', category: 'Social Media' },
      { value: 'linkedin', label: 'LinkedIn', category: 'Social Media' },
      { value: 'pinterest', label: 'Pinterest', category: 'Social Media' },
      { value: 'tiktok', label: 'TikTok', category: 'Social Media' },
      { value: 'threads', label: 'Threads', category: 'Social Media' },
      { value: 'custom', label: 'Custom API', category: 'Custom' }
    ];
  }

  /**
   * Get authentication types
   */
  getAuthenticationTypes() {
    return [
      { value: 'api_key', label: 'API Key' },
      { value: 'bearer_token', label: 'Bearer Token' },
      { value: 'oauth2', label: 'OAuth 2.0' },
      { value: 'basic_auth', label: 'Basic Authentication' },
      { value: 'custom_header', label: 'Custom Header' },
      { value: 'no_auth', label: 'No Authentication' }
    ];
  }

  /**
   * Get status options
   */
  getStatusOptions() {
    return [
      { value: 'active', label: 'Active', color: 'success' },
      { value: 'inactive', label: 'Inactive', color: 'default' },
      { value: 'maintenance', label: 'Maintenance', color: 'warning' },
      { value: 'deprecated', label: 'Deprecated', color: 'error' }
    ];
  }

  /**
   * Get health status options
   */
  getHealthStatusOptions() {
    return [
      { value: 'healthy', label: 'Healthy', color: 'success' },
      { value: 'degraded', label: 'Degraded', color: 'warning' },
      { value: 'down', label: 'Down', color: 'error' },
      { value: 'unknown', label: 'Unknown', color: 'default' }
    ];
  }
}

// Create and export singleton instance
export const externalApiService = new ExternalApiService();
export default externalApiService;

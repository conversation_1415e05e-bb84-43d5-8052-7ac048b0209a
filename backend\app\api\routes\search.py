"""
Advanced Product Search API Routes.
Provides endpoints for intelligent search, autocomplete, and filtering.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse

from app.models.user import User
from app.middleware.auth import get_current_active_user
from app.api.dependencies.rate_limiter import rate_limit
from app.api.dependencies.feature_access import require_feature_access
from app.services.ecommerce.search_service import search_service
from app.schemas.ecommerce import ProductSearchRequest, ProductSearchResponse

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/products", response_model=ProductSearchResponse)
@rate_limit("search_products", limit=200, window=3600)
async def search_products(
    q: str = Query(..., min_length=1, description="Search query"),
    store_ids: Optional[List[str]] = Query(None, description="Store IDs to search"),
    categories: Optional[List[str]] = Query(None, description="Category filters"),
    price_min: Optional[float] = Query(None, ge=0, description="Minimum price"),
    price_max: Optional[float] = Query(None, ge=0, description="Maximum price"),
    in_stock_only: bool = Query(False, description="Only show in-stock products"),
    tags: Optional[List[str]] = Query(None, description="Tag filters"),
    vendor: Optional[str] = Query(None, description="Vendor filter"),
    sort_by: str = Query("relevance", description="Sort criteria"),
    limit: int = Query(20, ge=1, le=100, description="Number of results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    enable_fuzzy: bool = Query(True, description="Enable fuzzy matching"),
    enable_autocorrect: bool = Query(True, description="Enable query autocorrection"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Search products with advanced filtering and intelligent matching.
    
    Args:
        q: Search query
        store_ids: Optional store IDs to search
        categories: Optional category filters
        price_min: Optional minimum price filter
        price_max: Optional maximum price filter
        in_stock_only: Only show in-stock products
        tags: Optional tag filters
        vendor: Optional vendor filter
        sort_by: Sort criteria (relevance, price_low, price_high, name, date_new, date_old)
        limit: Number of results to return
        offset: Number of results to skip
        enable_fuzzy: Enable fuzzy matching
        enable_autocorrect: Enable query autocorrection
        current_user: Current authenticated user
        
    Returns:
        Search results with metadata
    """
    try:
        # Validate sort criteria
        valid_sort_options = ["relevance", "price_low", "price_high", "name", "date_new", "date_old"]
        if sort_by not in valid_sort_options:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid sort option. Must be one of: {valid_sort_options}"
            )
        
        # Build filters
        filters = {}
        
        if categories:
            filters["categories"] = categories
        
        if price_min is not None or price_max is not None:
            price_range = {}
            if price_min is not None:
                price_range["min"] = price_min
            if price_max is not None:
                price_range["max"] = price_max
            filters["price_range"] = price_range
        
        if in_stock_only:
            filters["in_stock_only"] = True
        
        if tags:
            filters["tags"] = tags
        
        if vendor:
            filters["vendor"] = vendor
        
        # Perform search
        result = await search_service.intelligent_search(
            user_id=str(current_user.id),
            query=q,
            store_ids=store_ids,
            filters=filters,
            sort_by=sort_by,
            limit=limit,
            offset=offset,
            enable_fuzzy=enable_fuzzy,
            enable_autocorrect=enable_autocorrect
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Search failed")
            )
        
        return ProductSearchResponse(
            success=result["success"],
            query=result["query"],
            results=result["results"],
            total_count=result["total_count"],
            limit=result["limit"],
            offset=result["offset"],
            has_more=result["has_more"],
            search_time_ms=result["search_time_ms"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error searching products: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Search failed"
        )


@router.get("/autocomplete")
@rate_limit("autocomplete", limit=500, window=3600)
async def get_autocomplete_suggestions(
    q: str = Query(..., min_length=1, max_length=100, description="Search prefix"),
    store_ids: Optional[List[str]] = Query(None, description="Store IDs to search"),
    limit: int = Query(10, ge=1, le=20, description="Number of suggestions"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get autocomplete suggestions for search queries.
    
    Args:
        q: Search prefix
        store_ids: Optional store IDs to search
        limit: Number of suggestions to return
        current_user: Current authenticated user
        
    Returns:
        Autocomplete suggestions
    """
    try:
        result = await search_service.get_autocomplete_suggestions(
            user_id=str(current_user.id),
            prefix=q,
            store_ids=store_ids,
            limit=limit
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to get suggestions")
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting autocomplete suggestions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get autocomplete suggestions"
        )


@router.get("/filters")
@rate_limit("get_search_filters", limit=100, window=3600)
async def get_available_filters(
    store_ids: Optional[List[str]] = Query(None, description="Store IDs to get filters for"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get available filter options for search.
    
    Args:
        store_ids: Optional store IDs to get filters for
        current_user: Current authenticated user
        
    Returns:
        Available filter options
    """
    try:
        # This would be implemented in the search service
        # For now, return a placeholder response
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "filters": {
                    "categories": [],
                    "price_ranges": [
                        {"label": "Under $25", "min": 0, "max": 25},
                        {"label": "$25 - $50", "min": 25, "max": 50},
                        {"label": "$50 - $100", "min": 50, "max": 100},
                        {"label": "$100 - $200", "min": 100, "max": 200},
                        {"label": "Over $200", "min": 200, "max": None}
                    ],
                    "vendors": [],
                    "tags": [],
                    "availability": [
                        {"label": "In Stock", "value": "in_stock"},
                        {"label": "Out of Stock", "value": "out_of_stock"}
                    ]
                },
                "message": "Filter options functionality to be implemented"
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting search filters: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get search filters"
        )


@router.get("/suggestions")
@rate_limit("get_search_suggestions", limit=100, window=3600)
async def get_search_suggestions(
    q: str = Query(..., min_length=1, description="Search query"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get search suggestions for query refinement.
    
    Args:
        q: Search query
        current_user: Current authenticated user
        
    Returns:
        Search suggestions for query refinement
    """
    try:
        # This would be implemented in the search service
        # For now, return a placeholder response
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "query": q,
                "suggestions": [
                    f"{q} on sale",
                    f"{q} in electronics",
                    f"{q} under $50",
                    f"best {q}",
                    f"cheap {q}"
                ],
                "message": "Search suggestions functionality to be implemented"
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting search suggestions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get search suggestions"
        )


@router.get("/analytics")
@rate_limit("get_search_analytics", limit=50, window=3600)
async def get_search_analytics(
    days: int = Query(30, ge=1, le=365, description="Number of days to analyze"),
    current_user: User = Depends(get_current_active_user),
    _: None = Depends(require_feature_access("ecommerce_integration"))
):
    """
    Get search analytics and insights.
    
    Args:
        days: Number of days to analyze
        current_user: Current authenticated user
        
    Returns:
        Search analytics and insights
    """
    try:
        # This would be implemented in the search service
        # For now, return a placeholder response
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "period_days": days,
                "analytics": {
                    "total_searches": 0,
                    "unique_queries": 0,
                    "avg_results_per_search": 0,
                    "avg_search_time_ms": 0,
                    "top_queries": [],
                    "no_results_queries": [],
                    "search_trends": []
                },
                "message": "Search analytics functionality to be implemented"
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting search analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get search analytics"
        )

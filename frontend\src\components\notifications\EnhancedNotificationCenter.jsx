/**
 * Enhanced Notification Center - Enterprise-grade notification management component
 * Features: Plan-based notification limitations, real-time notification tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced notification management capabilities and interactive notification exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from "react";
import PropTypes from 'prop-types';
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  IconButton,
  Menu,
  Badge,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  CircularProgress,
  Tooltip,
  Tabs,
  Tab,
  Chip,
  Paper,
  Fade,
  Popper,
  alpha,
  Avatar,
  Snackbar,
  Alert,
  useMediaQuery
} from "@mui/material";
import {
  Notifications as NotificationsIcon,
  Delete as DeleteIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  CalendarToday as CalendarIcon,
  Assessment as AssessmentIcon,
  Edit as EditIcon,
  AccountCircle as AccountCircleIcon,
  CreditCard as CreditCardIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  Schedule as ScheduleIcon,
  TrendingUp as TrendingUpIcon,
  Error as ErrorIcon,
  AccessTime as AccessTimeIcon,
  Done as DoneIcon,
  DoneAll as DoneAllIcon,
  Refresh as RefreshIcon
} from "@mui/icons-material";
import {
  formatDistanceToNow,
  format,
  isToday,
  isYesterday,
  addHours,
  isBefore,
} from "date-fns";
import { useNotificationSystem } from "../../hooks/useNotificationSystem";
import { useAuth } from '../../contexts/AuthContext';
import useApiError from '../../hooks/useApiError';
import { useSubscription } from '../../hooks/useSubscription';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import api from '../../api';

// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Notification display modes with enhanced configurations
const NOTIFICATION_MODES = {
  COMPACT: {
    id: 'compact',
    name: 'Compact Notifications',
    description: 'Basic notification management interface',
    subscriptionLimits: {
      creator: { available: true, maxNotificationTypes: -1, features: ['basic_notifications', 'analytics_notifications', 'ai_insights'] },
      accelerator: { available: true, maxNotificationTypes: -1, features: ['basic_notifications', 'analytics_notifications', 'ai_insights'] },
      dominator: { available: true, maxNotificationTypes: -1, features: ['basic_notifications', 'analytics_notifications', 'ai_insights'] }
    }
  },
  DETAILED: {
    id: 'detailed',
    name: 'Detailed Notifications',
    description: 'Comprehensive notification management',
    subscriptionLimits: {
      creator: { available: true, maxNotificationTypes: -1, features: ['detailed_notifications', 'notification_analytics', 'real_time_insights'] },
      accelerator: { available: true, maxNotificationTypes: -1, features: ['detailed_notifications', 'notification_analytics', 'real_time_insights'] },
      dominator: { available: true, maxNotificationTypes: -1, features: ['detailed_notifications', 'notification_analytics', 'real_time_insights'] }
    }
  },
  AI_ASSISTED: {
    id: 'ai_assisted',
    name: 'AI-Assisted Notifications',
    description: 'AI-powered notification management and optimization',
    subscriptionLimits: {
      creator: { available: true, maxNotificationTypes: -1, features: ['ai_assisted', 'ai_optimization', 'notification_insights'] },
      accelerator: { available: true, maxNotificationTypes: -1, features: ['ai_assisted', 'ai_optimization', 'notification_insights'] },
      dominator: { available: true, maxNotificationTypes: -1, features: ['ai_assisted', 'ai_optimization', 'notification_insights'] }
    }
  },
  ANALYTICS: {
    id: 'analytics',
    name: 'Analytics Notifications',
    description: 'Advanced notification analytics and insights',
    subscriptionLimits: {
      creator: { available: true, maxNotificationTypes: -1, features: ['analytics_notifications', 'notification_insights'] },
      accelerator: { available: true, maxNotificationTypes: -1, features: ['analytics_notifications', 'notification_insights'] },
      dominator: { available: true, maxNotificationTypes: -1, features: ['analytics_notifications', 'notification_insights'] }
    }
  }
};

/**
 * Enhanced Notification Center Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=false] - Enable AI insights
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-notification-center'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const EnhancedNotificationCenter = memo(forwardRef(({
  enableAdvancedFeatures = true,
  enableRealTimeOptimization = true,
  enableAIInsights = false,
  onExport,
  onRefresh,
  ariaLabel,
  ariaDescription,
  testId = 'enhanced-notification-center',
  customization = {},
  className = '',
  style = {},
  sx = {}
}, ref) => {
  const navigate = useNavigate();

  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useAccessibility();
  const { user } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();
  const isMobile = useMediaQuery('(max-width:600px)');

  const {
    notifications,
    unreadCount,
    loading,
    error,
    pushSupported,
    pushEnabled,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    enablePushNotifications,
    disablePushNotifications,
  } = useNotificationSystem();

  // Core state management
  const notificationRef = useRef(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [pushLoading, setPushLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [statusIndicators, setStatusIndicators] = useState([]);
  const [miniNotification, setMiniNotification] = useState(null);
  const miniNotificationRef = useRef(null);
  const [miniNotificationOpen, setMiniNotificationOpen] = useState(false);

  // Enhanced state management
  const [notificationMode, setNotificationMode] = useState('compact');
  const [notificationHistory, setNotificationHistory] = useState([]);
  const [notificationAnalytics, setNotificationAnalytics] = useState(null);
  const [notificationInsights, setNotificationInsights] = useState(null);
  const [customNotifications, setCustomNotifications] = useState([]);
  const [notificationPreferences, setNotificationPreferences] = useState({
    autoRefresh: true,
    showAnalytics: true,
    enableNotifications: true,
    aiAssistance: false,
    notificationSuggestions: true
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [notificationDrawerOpen, setNotificationDrawerOpen] = useState(false);
  const [selectedNotificationType, setSelectedNotificationType] = useState(null);
  const [notificationStats, setNotificationStats] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastNotificationCheck, setLastNotificationCheck] = useState(Date.now());

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Apply feature overrides based on props
    const advancedFeaturesEnabled = enableAdvancedFeatures && subscription?.plan_id !== 'creator';
    const aiInsightsEnabled = enableAIInsights && subscription?.plan_id === 'dominator';

    const features = {
      creator: {
        maxNotificationTypes: -1,
        maxNotificationsPerDay: -1,
        hasAdvancedNotifications: true,
        hasNotificationAnalytics: true,
        hasCustomNotifications: true,
        hasNotificationInsights: true,
        hasNotificationHistory: true,
        hasAIAssistance: true,
        hasNotificationExport: true,
        hasNotificationScheduling: true,
        hasNotificationAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxNotificationTypes: -1,
        maxNotificationsPerDay: -1,
        hasAdvancedNotifications: true,
        hasNotificationAnalytics: true,
        hasCustomNotifications: true,
        hasNotificationInsights: true,
        hasNotificationHistory: true,
        hasAIAssistance: true,
        hasNotificationExport: true,
        hasNotificationScheduling: true,
        hasNotificationAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxNotificationTypes: -1,
        maxNotificationsPerDay: -1,
        hasAdvancedNotifications: true,
        hasNotificationAnalytics: true,
        hasCustomNotifications: true,
        hasNotificationInsights: true,
        hasNotificationHistory: true,
        hasAIAssistance: true,
        hasNotificationExport: true,
        hasNotificationScheduling: true,
        hasNotificationAutomation: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'full',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    // Apply feature overrides
    const enhancedFeatures = {
      ...currentFeatures,
      hasAdvancedNotifications: currentFeatures.hasAdvancedNotifications && advancedFeaturesEnabled,
      hasNotificationInsights: currentFeatures.hasNotificationInsights && aiInsightsEnabled,
      hasNotificationAnalytics: currentFeatures.hasNotificationAnalytics && advancedFeaturesEnabled
    };

    return {
      ...enhancedFeatures,
      hasFeatureAccess: (feature) => enhancedFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = enhancedFeatures[feature] === true;
        const withinLimits = enhancedFeatures.maxNotificationTypes === -1 || currentUsage < enhancedFeatures.maxNotificationTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription, enableAdvancedFeatures, enableAIInsights]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Notification center with ${subscriptionFeatures.planName} plan features`,
      'aria-description': ariaDescription || `Notification interface with ${subscriptionFeatures.trackingLevel} tracking`,
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive notification API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getNotificationHistory: () => notificationHistory,
    getNotificationAnalytics: () => notificationAnalytics,
    getNotificationInsights: () => notificationInsights,
    refreshNotifications: () => {
      fetchNotificationAnalytics();
      loadNotifications();
      if (onRefresh) onRefresh();
    },

    // Notification methods
    focusNotification: () => {
      if (notificationRef.current) {
        notificationRef.current.focus();
      }
    },
    openNotificationCenter: (event) => setAnchorEl(event.currentTarget),
    closeNotificationCenter: () => setAnchorEl(null),
    toggleNotificationCenter: (event) => {
      if (anchorEl) {
        setAnchorEl(null);
      } else {
        setAnchorEl(event.currentTarget);
      }
    },
    openNotificationDrawer: () => setNotificationDrawerOpen(true),
    closeNotificationDrawer: () => setNotificationDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    exportNotificationData: () => {
      if (onExport) {
        onExport(notificationHistory, notificationAnalytics);
      }
    },

    // Accessibility methods
    announceNotification: (message) => announceToScreenReader(message),
    focusNotificationField: () => setFocusToElement('notification-center-field'),

    // Advanced methods
    getAISuggestions: () => aiSuggestions,
    toggleFullscreen: () => setFullscreenMode(prev => !prev),
    getCurrentStep: () => activeStep,
    goToStep: (step) => setActiveStep(step),
    getValidationErrors: () => validationErrors,
    getCurrentMode: () => notificationMode,
    getNotificationStats: () => notificationStats,
    getUnreadCount: () => unreadCount,
    getFilteredNotifications: () => getFilteredNotifications(),

    // Enhanced methods
    changeNotificationMode: (mode) => handleNotificationModeChange(mode),
    showUpgradePrompt: (feature) => handleUpgradePrompt(feature),
    addCustomNotification: (notification) => addCustomNotification(notification),
    updatePreferences: (prefs) => updateNotificationPreferences(prefs),
    selectNotificationType: (type) => handleNotificationTypeSelection(type),
    generateAI: () => generateAISuggestions(),
    validateNotification: (notification) => validateNotification(notification),
    fetchStats: () => fetchNotificationStats(),
    getSelectedType: () => selectedNotificationType,
    getCustomNotifications: () => customNotifications,
    getNotificationPreferences: () => notificationPreferences,
    isDrawerOpen: () => notificationDrawerOpen,
    isAnalyticsVisible: () => showAnalytics,
    isFullscreenActive: () => fullscreenMode,
    isBackendAvailable: () => backendAvailable,
    getLastHealthCheck: () => lastNotificationCheck
  }), [
    notificationHistory,
    notificationAnalytics,
    notificationInsights,
    notificationStats,
    onRefresh,
    onExport,
    announceToScreenReader,
    setFocusToElement,
    aiSuggestions,
    activeStep,
    validationErrors,
    notificationMode,
    unreadCount,
    anchorEl,
    loadNotifications,
    fetchNotificationAnalytics,
    getFilteredNotifications,
    addCustomNotification,
    backendAvailable,
    customNotifications,
    fetchNotificationStats,
    fullscreenMode,
    generateAISuggestions,
    handleNotificationModeChange,
    handleNotificationTypeSelection,
    handleUpgradePrompt,
    lastNotificationCheck,
    notificationDrawerOpen,
    notificationPreferences,
    selectedNotificationType,
    showAnalytics,
    updateNotificationPreferences,
    validateNotification
  ]);

  // Fetch notification analytics with enhanced error handling and retry logic
  const fetchNotificationAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get('/api/notification/analytics');
            return response.data;
          },
          {
            onSuccess: (data) => {
              setNotificationAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (notificationPreferences.showAnalytics) {
                setNotification({
                  open: true,
                  message: "Notification analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch notification analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load notification analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, notificationPreferences.showAnalytics]);

  // Fetch notification insights
  const fetchNotificationInsights = useCallback(async () => {
    await handleApiRequest(
      async () => {
        const response = await api.get('/api/notification/insights');
        return response.data;
      },
      {
        onSuccess: (data) => {
          setNotificationInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch notification insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest]);

  // Set the miniNotificationRef to the document body if not set
  useEffect(() => {
    if (!miniNotificationRef.current) {
      miniNotificationRef.current = document.body;
    }
  }, []);

  // Initial data loading
  useEffect(() => {
    fetchNotificationAnalytics();
    fetchNotificationInsights();
  }, [fetchNotificationAnalytics, fetchNotificationInsights]);

  // Real-time optimization effect
  useEffect(() => {
    if (enableRealTimeOptimization && notifications) {
      // Optimize notification management based on real-time data
      const optimizationTimer = setTimeout(() => {
        fetchNotificationAnalytics();
      }, 2000);

      return () => clearTimeout(optimizationTimer);
    }
  }, [enableRealTimeOptimization, notifications, fetchNotificationAnalytics]);

  // Backend health monitoring
  useEffect(() => {
    const checkBackendHealth = async () => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('/api/health', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const wasUnavailable = !backendAvailable;
          setBackendAvailable(true);
          setLastNotificationCheck(Date.now());

          if (wasUnavailable && notificationPreferences.showAnalytics) {
            showSuccess("Connection restored - Notification features available");
          }
        } else {
          setBackendAvailable(false);
          if (notificationPreferences.showAnalytics) {
            showError("Backend service unavailable - Some notification features may be limited");
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Health check failed:', error);
          setBackendAvailable(false);

          // Show notification only if it's been a while since last check
          const timeSinceLastCheck = Date.now() - lastNotificationCheck;
          if (timeSinceLastCheck > 60000 && notificationPreferences.showAnalytics) { // 1 minute
            showError("Connection issues detected - Notifications may be delayed");
          }
        }
      }
    };

    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [backendAvailable, lastNotificationCheck, notificationPreferences.showAnalytics, showSuccess, showError]);

  // Fetch stats when notifications change
  useEffect(() => {
    if (notifications) {
      fetchNotificationStats();
    }
  }, [notifications, fetchNotificationStats]);

  // Generate AI suggestions when notifications change
  useEffect(() => {
    if (notifications && notificationPreferences.aiAssistance) {
      const debounceTimer = setTimeout(() => {
        generateAISuggestions();
      }, 1000);

      return () => clearTimeout(debounceTimer);
    }
  }, [notifications, notificationPreferences.aiAssistance, generateAISuggestions]);

  // Handle notification mode switching
  const handleNotificationModeChange = useCallback((newMode) => {
    if (NOTIFICATION_MODES[newMode.toUpperCase()]) {
      setNotificationMode(newMode);
      announceToScreenReader(`Notification mode changed to ${newMode}`);

      // Track mode change in history
      const modeChangeRecord = {
        id: Date.now(),
        type: 'mode_change',
        mode: newMode,
        timestamp: new Date().toISOString(),
        userId: user?.id
      };

      setNotificationHistory(prev => [modeChangeRecord, ...prev.slice(0, 99)]);

      if (notificationPreferences.showAnalytics) {
        showSuccess(`Switched to ${newMode} notification mode`);
      }
    }
  }, [announceToScreenReader, user?.id, notificationPreferences.showAnalytics, showSuccess]);

  // Handle upgrade prompts (now deprecated since all features are available)
  const handleUpgradePrompt = useCallback((feature) => {
    // All notification features are now available to all users
    console.log(`Feature ${feature} is available to all users`);
  }, []);

  // Handle custom notification management
  const addCustomNotification = useCallback((notificationData) => {
    const newNotification = {
      id: Date.now(),
      ...notificationData,
      createdAt: new Date().toISOString(),
      userId: user?.id
    };

    setCustomNotifications(prev => [...prev, newNotification]);

    // Track notification creation
    const notificationRecord = {
      id: Date.now(),
      type: 'custom_notification_created',
      notification: newNotification.title,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setNotificationHistory(prev => [notificationRecord, ...prev.slice(0, 99)]);

    if (notificationPreferences.showAnalytics) {
      showSuccess(`Custom notification "${notificationData.title}" created`);
    }
  }, [user?.id, notificationPreferences.showAnalytics, showSuccess]);

  // Handle notification preferences updates
  const updateNotificationPreferences = useCallback((newPreferences) => {
    setNotificationPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    // Track preference changes
    const prefRecord = {
      id: Date.now(),
      type: 'preferences_updated',
      preferences: newPreferences,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setNotificationHistory(prev => [prefRecord, ...prev.slice(0, 99)]);

    if (notificationPreferences.showAnalytics) {
      showSuccess('Notification preferences updated');
    }
  }, [user?.id, notificationPreferences.showAnalytics, showSuccess]);

  // Handle notification type selection
  const handleNotificationTypeSelection = useCallback((notificationType) => {
    setSelectedNotificationType(notificationType);

    // Track notification type selection
    const typeRecord = {
      id: Date.now(),
      type: 'notification_type_selected',
      notificationType,
      timestamp: new Date().toISOString(),
      userId: user?.id
    };

    setNotificationHistory(prev => [typeRecord, ...prev.slice(0, 99)]);

    if (notificationPreferences.showAnalytics) {
      announceToScreenReader(`Selected notification type: ${notificationType}`);
    }
  }, [user?.id, notificationPreferences.showAnalytics, announceToScreenReader]);

  // Handle AI suggestions generation
  const generateAISuggestions = useCallback(async () => {
    try {
      const response = await api.get('/api/notification/ai-suggestions', {
        params: {
          context: notifications?.length || 0
        }
      });

      setAiSuggestions(response.data.suggestions || []);

      if (notificationPreferences.showAnalytics) {
        showSuccess('AI suggestions generated');
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
      showError('Failed to generate AI suggestions');
    }
  }, [notifications, notificationPreferences.showAnalytics, showSuccess, showError]);

  // Handle notification stats fetching
  const fetchNotificationStats = useCallback(async () => {
    try {
      const response = await api.get('/api/notification/stats');
      setNotificationStats(response.data);
    } catch (error) {
      console.error('Failed to fetch notification stats:', error);
    }
  }, []);

  // Handle validation errors
  const validateNotification = useCallback((notificationData) => {
    const errors = {};

    if (!notificationData.title?.trim()) {
      errors.title = 'Notification title is required';
    }
    if (!notificationData.message?.trim()) {
      errors.message = 'Notification message is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, []);

  // Filter notifications based on active tab
  const getFilteredNotifications = useCallback(() => {
    if (!notifications) return [];

    switch (activeTab) {
      case 0: // All
        return notifications;
      case 1: // Unread
        return notifications.filter((notification) => !notification.is_read);
      case 2: // Scheduled
        return notifications.filter(
          (notification) =>
            notification.notification_type === "content_scheduled" ||
            notification.notification_type === "content_published" ||
            notification.notification_type === "content_failed"
        );
      case 3: // Analytics
        return notifications.filter(
          (notification) =>
            notification.notification_type === "analytics_report" ||
            notification.notification_type === "engagement_alert" ||
            notification.notification_type === "optimal_time"
        );
      default:
        return notifications;
    }
  }, [notifications, activeTab]);

  // Refresh notifications when menu opens
  useEffect(() => {
    if (anchorEl) {
      loadNotifications();
    }
  }, [anchorEl, loadNotifications]);

  // We no longer need to simulate status indicators here
  // The StatusIndicators component now handles real-time updates via WebSocket

  // Show mini notification for new notifications
  useEffect(() => {
    if (notifications && notifications.length > 0) {
      // Find the newest unread notification
      const newestUnread = notifications
        .filter((n) => !n.is_read)
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0];

      if (
        newestUnread &&
        isBefore(new Date(newestUnread.created_at), addHours(new Date(), -1))
      ) {
        setMiniNotification(newestUnread);
        setMiniNotificationOpen(true);

        // Auto-hide after 5 seconds
        const timeout = setTimeout(() => {
          setMiniNotificationOpen(false);
        }, 5000);

        return () => clearTimeout(timeout);
      }
    }
  }, [notifications]);

  // Handle opening notification center
  const handleOpenMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  // Handle closing notification center
  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  // Handle notification click
  const handleNotificationClick = async (notification) => {
    // Mark as read
    await markAsRead(notification.id);

    // Navigate based on notification type
    handleCloseMenu();

    switch (notification.notification_type) {
      case "content_review":
        navigate(`/content/${notification.entity_id}`);
        break;
      case "content_scheduled":
        navigate(`/scheduling/calendar?contentId=${notification.entity_id}`);
        break;
      case "content_published":
        navigate(`/content/${notification.entity_id}`);
        break;
      case "content_failed":
        navigate(`/content/${notification.entity_id}`);
        break;
      case "calendar_invitation":
        navigate(`/shared-calendar/${notification.entity_id}`);
        break;
      case "analytics_report":
        navigate("/dashboard");
        break;
      case "engagement_alert":
        navigate(`/analytics/content/${notification.entity_id}`);
        break;
      case "optimal_time":
        navigate(
          `/scheduling/calendar?date=${notification.data?.suggested_date || ""}`
        );
        break;
      case "account":
        navigate("/profile");
        break;
      case "billing":
        navigate("/billing");
        break;
      default:
        navigate("/notifications");
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async (e) => {
    e.stopPropagation();
    await markAllAsRead();
  };

  // Handle delete notification
  const handleDeleteNotification = async (e, notificationId) => {
    e.stopPropagation();
    await deleteNotification(notificationId);
  };

  // Handle toggle push notifications
  const handleTogglePushNotifications = async () => {
    setPushLoading(true);

    try {
      if (pushEnabled) {
        await disablePushNotifications();
      } else {
        await enablePushNotifications();
      }
    } finally {
      setPushLoading(false);
    }
  };

  // Get icon based on notification type
  const getNotificationIcon = (type) => {
    switch (type) {
      case "content_review":
        return <EditIcon color="primary" />;
      case "content_scheduled":
        return <ScheduleIcon color="primary" />;
      case "content_published":
        return <DoneIcon color="success" />;
      case "content_failed":
        return <ErrorIcon color="error" />;
      case "calendar_invitation":
        return <CalendarIcon color="primary" />;
      case "analytics_report":
        return <AssessmentIcon color="primary" />;
      case "engagement_alert":
        return <TrendingUpIcon color="secondary" />;
      case "optimal_time":
        return <AccessTimeIcon color="secondary" />;
      case "account":
        return <AccountCircleIcon color="primary" />;
      case "billing":
        return <CreditCardIcon color="primary" />;
      default:
        return <InfoIcon color="primary" />;
    }
  };

  // Get status indicator icon
  const getStatusIndicatorIcon = (type) => {
    switch (type) {
      case "publishing":
        return <EditIcon />;
      case "analytics":
        return <AssessmentIcon />;
      case "upload":
        return <TrendingUpIcon />;
      case "download":
        return <TrendingUpIcon />;
      default:
        return <InfoIcon />;
    }
  };

  // Format notification date
  const formatNotificationDate = (dateString) => {
    const date = new Date(dateString);

    if (isToday(date)) {
      return `Today, ${format(date, "h:mm a")}`;
    } else if (isYesterday(date)) {
      return `Yesterday, ${format(date, "h:mm a")}`;
    } else {
      return format(date, "MMM d, h:mm a");
    }
  };

  return (
    <Box
      {...getAccessibilityProps()}
      ref={notificationRef}
      sx={{
        ...sx,
        ...customization
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      {/* Notification Bell Icon */}
      <Tooltip title="Notifications">
        <IconButton
          color="inherit"
          onClick={handleOpenMenu}
          sx={{
            '&:hover': {
              animation: 'pulse 1s infinite',
              '@keyframes pulse': {
                '0%': {
                  transform: 'scale(1)',
                },
                '50%': {
                  transform: 'scale(1.1)',
                },
                '100%': {
                  transform: 'scale(1)',
                },
              },
            },
          }}
        >
          <Badge badgeContent={unreadCount} color="error">
            <NotificationsIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      {/* Mini Notification */}
      <Popper
        open={miniNotificationOpen}
        anchorEl={miniNotificationRef.current}
        placement="bottom-end"
        transition
        disablePortal
        style={{
          zIndex: 9999,
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          width: '300px',
        }}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={350}>
            <Paper
              elevation={3}
              sx={{
                p: 2,
                borderRadius: 2,
                bgcolor: alpha(ACE_COLORS.WHITE, 0.9),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
              }}
            >
              {miniNotification && (
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                        color: ACE_COLORS.PURPLE,
                        mr: 1,
                      }}
                    >
                      {getNotificationIcon(miniNotification.notification_type)}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="subtitle2">{miniNotification.title}</Typography>
                      <Typography variant="caption" color="textSecondary">
                        {formatDistanceToNow(new Date(miniNotification.created_at), { addSuffix: true })}
                      </Typography>
                    </Box>
                    <IconButton
                      size="small"
                      onClick={() => setMiniNotificationOpen(false)}
                      aria-label="Close notification"
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Box>
                  <Typography variant="body2">{miniNotification.message}</Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                    <Button
                      size="small"
                      onClick={() => {
                        setMiniNotificationOpen(false);
                        handleNotificationClick(miniNotification);
                      }}
                    >
                      View
                    </Button>
                  </Box>
                </Box>
              )}
            </Paper>
          </Fade>
        )}
      </Popper>

      {/* Notification Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        slotProps={{
          paper: {
            elevation: 3,
            sx: {
              width: isMobile ? 280 : 320,
              maxHeight: isMobile ? 400 : 500,
              overflow: 'auto',
              mt: 1,
              '& .MuiList-root': {
                padding: 0,
              },
            },
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* Notification Header */}
        <Box sx={{ p: 2, borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Notifications</Typography>
            <Box>
              <Tooltip title="Mark all as read">
                <IconButton size="small" onClick={handleMarkAllAsRead} disabled={loading || unreadCount === 0}>
                  <DoneAllIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh">
                <IconButton size="small" onClick={() => loadNotifications()} disabled={loading}>
                  <RefreshIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title={pushEnabled ? "Disable push notifications" : "Enable push notifications"}>
                <IconButton
                  size="small"
                  onClick={handleTogglePushNotifications}
                  disabled={!pushSupported || pushLoading}
                >
                  {pushEnabled ? <NotificationsOffIcon fontSize="small" /> : <NotificationsActiveIcon fontSize="small" />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Notification Tabs */}
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ mt: 1 }}
          >
            <Tab label="All" />
            <Tab label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography>Unread</Typography>
                {unreadCount > 0 && (
                  <Chip
                    label={unreadCount}
                    size="small"
                    color="error"
                    sx={{ ml: 1, height: 20, minWidth: 20 }}
                  />
                )}
              </Box>
            } />
            <Tab label="Scheduled" />
            <Tab label="Analytics" />
          </Tabs>
        </Box>

        {/* Notification Content */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={24} />
          </Box>
        ) : error ? (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography color="error">{error}</Typography>
            <Button
              startIcon={<RefreshIcon />}
              onClick={() => loadNotifications()}
              sx={{ mt: 1 }}
            >
              Retry
            </Button>
          </Box>
        ) : getFilteredNotifications().length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="textSecondary">No notifications</Typography>
          </Box>
        ) : (
          <List disablePadding>
            {getFilteredNotifications().map((notification) => (
              <ListItem
                key={notification.id}
                onClick={() => handleNotificationClick(notification)}
                sx={{
                  borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                  bgcolor: notification.is_read ? 'transparent' : alpha(ACE_COLORS.PURPLE, 0.05),
                  '&:hover': {
                    bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                    cursor: 'pointer',
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  {getNotificationIcon(notification.notification_type)}
                </ListItemIcon>
                <ListItemText
                  primary={notification.title}
                  secondary={
                    <Box component="span" sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="body2" color="textSecondary" noWrap>
                        {notification.message}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {formatNotificationDate(notification.created_at)}
                      </Typography>
                    </Box>
                  }
                  primaryTypographyProps={{
                    variant: 'subtitle2',
                    color: notification.is_read ? 'textPrimary' : 'primary',
                  }}
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    size="small"
                    onClick={(e) => handleDeleteNotification(e, notification.id)}
                    aria-label="Delete notification"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}

        {/* View All Link */}
        <Box sx={{ p: 1, borderTop: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
          <Button
            fullWidth
            onClick={() => {
              handleCloseMenu();
              navigate('/notifications');
            }}
          >
            View All Notifications
          </Button>
        </Box>
      </Menu>

      {/* Status Indicators */}
      {statusIndicators.length > 0 && (
        <div
          style={{
            zIndex: 9999,
            position: "fixed",
            width: 320,
            marginBottom: "16px",
            right: "30px",
            bottom: "10px",
          }}
        >
          <Paper
            elevation={3}
            sx={{
              borderRadius: 2,
              overflow: "hidden",
            }}
          >
            {statusIndicators.map((indicator) => (
              <Box
                key={indicator.id}
                sx={{
                  p: 2,
                  borderBottom: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
                  "&:last-child": {
                    borderBottom: "none",
                  },
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                      color: ACE_COLORS.PURPLE,
                      mr: 1,
                    }}
                  >
                    {getStatusIndicatorIcon(indicator.type)}
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="subtitle2" noWrap>
                      {indicator.title}
                    </Typography>
                    <Typography variant="caption" color="textSecondary" noWrap>
                      {indicator.message}
                    </Typography>
                  </Box>
                  <IconButton
                    size="small"
                    onClick={() => {
                      setStatusIndicators((prev) =>
                        prev.filter((item) => item.id !== indicator.id)
                      );
                    }}
                    aria-label="Close dialog"
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Box sx={{ flexGrow: 1, mr: 2 }}>
                    <Box
                      sx={{
                        height: 4,
                        borderRadius: 2,
                        bgcolor: alpha(ACE_COLORS.PURPLE, 0.2),
                        position: "relative",
                        overflow: "hidden",
                      }}
                    >
                      <Box
                        sx={{
                          position: "absolute",
                          left: 0,
                          top: 0,
                          height: "100%",
                          width: `${indicator.progress}%`,
                          bgcolor: ACE_COLORS.PURPLE,
                          borderRadius: 2,
                          transition: "width 0.5s ease",
                        }}
                      />
                    </Box>
                  </Box>
                  <Typography variant="caption" color="textSecondary">
                    {indicator.progress}%
                  </Typography>
                </Box>
              </Box>
            ))}
          </Paper>
        </div>
      )}

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          severity={notification.severity}
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          sx={{
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            color: ACE_COLORS.DARK,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Retry Count Indicator */}
      {retryCount > 0 && (
        <Box sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          p: 1,
          borderRadius: 1,
          bgcolor: alpha(ACE_COLORS.YELLOW, 0.1),
          border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.3)}`,
          zIndex: 9999
        }}>
          <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
            Retrying notification sync... (Attempt {retryCount}/3)
          </Typography>
        </Box>
      )}
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
EnhancedNotificationCenter.propTypes = {
  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,

  // Standard props
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

EnhancedNotificationCenter.displayName = 'EnhancedNotificationCenter';

export default EnhancedNotificationCenter;
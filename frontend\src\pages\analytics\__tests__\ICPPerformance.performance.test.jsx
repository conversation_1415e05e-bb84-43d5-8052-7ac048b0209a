// @since 2024-1-1 to 2025-25-7
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ICPPerformance from '../ICPPerformance';
import { NotificationProvider } from '../../../contexts/NotificationContext';
import * as icpPerformanceApi from '../../../api/icp-performance';

// Mock the API module
jest.mock('../../../api/icp-performance');

// Mock D3 and ResizeObserver
jest.mock('d3', () => ({
  select: jest.fn(() => ({
    selectAll: jest.fn(() => ({
      remove: jest.fn()
    })),
    append: jest.fn(() => ({
      attr: jest.fn(() => ({
        attr: jest.fn(() => ({
          append: jest.fn(() => ({
            attr: jest.fn(() => ({}))
          }))
        }))
      }))
    }))
  })),
  scaleBand: jest.fn(() => ({
    range: jest.fn(() => ({
      domain: jest.fn(() => ({
        padding: jest.fn(() => ({}))
      }))
    }))
  })),
  scaleLinear: jest.fn(() => ({
    domain: jest.fn(() => ({
      range: jest.fn(() => ({}))
    }))
  })),
  max: jest.fn(() => 100),
  axisBottom: jest.fn(() => ({})),
  axisLeft: jest.fn(() => ({})),
  easeBackOut: jest.fn()
}));

global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Performance monitoring utilities
const measureRenderTime = async (renderFn) => {
  const startTime = performance.now();
  await renderFn();
  const endTime = performance.now();
  return endTime - startTime;
};

const measureMemoryUsage = () => {
  if (performance.memory) {
    return {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    };
  }
  return null;
};

// Generate large datasets for performance testing
const generateLargeDataset = (size) => {
  return Array.from({ length: size }, (_, i) => ({
    icp_id: `${i + 1}`,
    icp_name: `ICP ${i + 1}`,
    avg_engagement_rate: Math.random() * 20,
    total_content: Math.floor(Math.random() * 50)
  }));
};

// Test wrapper
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return (
    <ThemeProvider theme={theme}>
      <NotificationProvider>
        {children}
      </NotificationProvider>
    </ThemeProvider>
  );
};

describe('ICPPerformance Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Initial Render Performance', () => {
    test('renders within acceptable time with small dataset', async () => {
      const smallDataset = generateLargeDataset(10);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(smallDataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(smallDataset.slice(0, 5));

      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <ICPPerformance />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('ICP 1')).toBeInTheDocument();
        });
      });

      // Should render within 1 second for small datasets
      expect(renderTime).toBeLessThan(1000);
    });

    test('renders within acceptable time with medium dataset', async () => {
      const mediumDataset = generateLargeDataset(100);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(mediumDataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(mediumDataset.slice(0, 5));

      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <ICPPerformance />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('ICP 1')).toBeInTheDocument();
        });
      });

      // Should render within 2 seconds for medium datasets
      expect(renderTime).toBeLessThan(2000);
    });

    test('renders within acceptable time with large dataset', async () => {
      const largeDataset = generateLargeDataset(1000);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(largeDataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(largeDataset.slice(0, 5));

      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <ICPPerformance />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('ICP 1')).toBeInTheDocument();
        }, { timeout: 10000 });
      });

      // Should render within 5 seconds even for large datasets
      expect(renderTime).toBeLessThan(5000);
    });
  });

  describe('Interaction Performance', () => {
    test('tab switching is responsive', async () => {
      const user = userEvent.setup();
      const dataset = generateLargeDataset(50);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(dataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(dataset.slice(0, 5));

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('ICP 1')).toBeInTheDocument();
      });

      const switchTime = await measureRenderTime(async () => {
        const compareTab = screen.getByRole('tab', { name: /compare icps/i });
        await user.click(compareTab);

        await waitFor(() => {
          expect(screen.getByText(/compare icp performance/i)).toBeInTheDocument();
        });
      });

      // Tab switching should be near-instantaneous
      expect(switchTime).toBeLessThan(500);
    });

    test('filtering is responsive with large datasets', async () => {
      const user = userEvent.setup();
      const largeDataset = generateLargeDataset(500);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(largeDataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(largeDataset.slice(0, 5));

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('ICP 1')).toBeInTheDocument();
      });

      // Open filters
      const filterButton = screen.getByLabelText(/toggle filters/i);
      await user.click(filterButton);

      const filterTime = await measureRenderTime(async () => {
        // Change sort order
        const orderSelect = screen.getByLabelText(/order/i);
        await user.click(orderSelect);
        await user.click(screen.getByText(/low to high/i));

        // Wait for re-render
        await waitFor(() => {
          expect(screen.getByDisplayValue('asc')).toBeInTheDocument();
        });
      });

      // Filtering should be responsive even with large datasets
      expect(filterTime).toBeLessThan(1000);
    });

    test('chart rendering is performant', async () => {
      const user = userEvent.setup();
      const dataset = generateLargeDataset(20);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(dataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(dataset.slice(0, 5));
      icpPerformanceApi.compareICPs.mockResolvedValue({
        icps: dataset.slice(0, 10),
        best_performing_icp_name: 'ICP 1',
        performance_gap: { avg_engagement_rate: 3.2 },
        recommendations: ['Test recommendation']
      });

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('ICP 1')).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      const chartRenderTime = await measureRenderTime(async () => {
        // Select ICPs and compare (this would trigger chart rendering)
        const selectElement = screen.getByLabelText(/select multiple icps to compare/i);
        await user.click(selectElement);

        // Select multiple ICPs
        const icp1 = screen.getByText('ICP 1');
        await user.click(icp1);
        const icp2 = screen.getByText('ICP 2');
        await user.click(icp2);

        // Trigger comparison
        const compareButton = screen.getByLabelText(/compare 2 selected icps/i);
        await user.click(compareButton);

        await waitFor(() => {
          expect(screen.getByText(/comparison results/i)).toBeInTheDocument();
        });
      });

      // Chart rendering should be fast
      expect(chartRenderTime).toBeLessThan(2000);
    });
  });

  describe('Memory Usage', () => {
    test('does not cause memory leaks with repeated operations', async () => {
      const user = userEvent.setup();
      const dataset = generateLargeDataset(100);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(dataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(dataset.slice(0, 5));

      const initialMemory = measureMemoryUsage();

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('ICP 1')).toBeInTheDocument();
      });

      // Perform multiple operations that could cause memory leaks
      for (let i = 0; i < 10; i++) {
        // Switch tabs multiple times
        const compareTab = screen.getByRole('tab', { name: /compare icps/i });
        await user.click(compareTab);

        const overviewTab = screen.getByRole('tab', { name: /overview/i });
        await user.click(overviewTab);

        // Toggle filters
        const filterButton = screen.getByLabelText(/toggle filters/i);
        await user.click(filterButton);
        await user.click(filterButton);
      }

      const finalMemory = measureMemoryUsage();

      if (initialMemory && finalMemory) {
        const memoryIncrease = finalMemory.used - initialMemory.used;
        const memoryIncreasePercent = (memoryIncrease / initialMemory.used) * 100;

        // Memory usage should not increase by more than 50% after repeated operations
        expect(memoryIncreasePercent).toBeLessThan(50);
      }
    });
  });

  describe('Bundle Size Impact', () => {
    test('component imports are optimized', () => {
      // This test ensures we're not importing entire libraries unnecessarily
      const ICPPerformanceModule = require('../ICPPerformance');
      
      // Check that the module exports what we expect
      expect(ICPPerformanceModule.default).toBeDefined();
      expect(typeof ICPPerformanceModule.default).toBe('object'); // React.memo returns an object
    });
  });

  describe('Virtualization Performance', () => {
    test('handles extremely large datasets efficiently', async () => {
      // Test with a very large dataset to ensure virtualization or pagination works
      const extremelyLargeDataset = generateLargeDataset(10000);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(extremelyLargeDataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(extremelyLargeDataset.slice(0, 5));

      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <ICPPerformance />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByText('ICP 1')).toBeInTheDocument();
        }, { timeout: 15000 });
      });

      // Even with extremely large datasets, should render within reasonable time
      expect(renderTime).toBeLessThan(10000);

      // Check that not all items are rendered in DOM (indicating virtualization or pagination)
      const icpElements = screen.queryAllByText(/^ICP \d+$/);
      expect(icpElements.length).toBeLessThan(100); // Should not render all 10,000 items
    });
  });

  describe('Debouncing and Throttling', () => {
    test('debounces filter changes appropriately', async () => {
      const user = userEvent.setup();
      const dataset = generateLargeDataset(100);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(dataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(dataset.slice(0, 5));

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('ICP 1')).toBeInTheDocument();
      });

      // Open filters
      const filterButton = screen.getByLabelText(/toggle filters/i);
      await user.click(filterButton);

      const startTime = performance.now();

      // Rapidly change filter values
      const minEngagementSlider = screen.getByLabelText(/minimum engagement rate filter/i);
      
      // Simulate rapid slider changes
      for (let i = 0; i < 10; i++) {
        await user.click(minEngagementSlider);
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Rapid changes should not cause performance issues
      expect(totalTime).toBeLessThan(2000);
    });
  });

  describe('Cleanup Performance', () => {
    test('cleans up resources properly on unmount', async () => {
      const dataset = generateLargeDataset(50);
      
      icpPerformanceApi.getAllICPPerformances.mockResolvedValue(dataset);
      icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(dataset.slice(0, 5));

      const { unmount } = render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('ICP 1')).toBeInTheDocument();
      });

      const unmountTime = await measureRenderTime(async () => {
        unmount();
      });

      // Unmounting should be fast, indicating proper cleanup
      expect(unmountTime).toBeLessThan(100);
    });
  });
});

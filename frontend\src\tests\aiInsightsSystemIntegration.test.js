/**
 * AI Insights System Integration Test
 * 
 * Validates that the enhanced AI insights system integrates properly
 * with the existing ACE Social platform infrastructure.
 */

import { render, screen, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import React from 'react';

// Import the enhanced components
import QuickInsights from '../components/analytics/QuickInsights';
import { aiInsightsDataService } from '../services/aiInsightsDataService';
import { openAIInsightsService } from '../services/openAIInsightsService';
import { aiInsightsCacheService } from '../services/aiInsightsCacheService';
import logger from '../utils/logger';

// Mock existing system dependencies
jest.mock('../contexts/SubscriptionContext', () => ({
  useSubscription: () => ({
    subscription: { 
      plan_id: 'accelerator',
      plan_name: 'Accelerator',
      user_id: 'test-user-123'
    },
    hasFeatureAccess: () => true,
    updateUsage: jest.fn(),
    getRemainingCredits: () => 100,
    getPlanTier: () => 2
  })
}));

jest.mock('../hooks/useNotification', () => ({
  useNotification: () => ({
    showNotification: jest.fn(),
    showSuccess: jest.fn(),
    showError: jest.fn(),
    showWarning: jest.fn()
  })
}));

jest.mock('../services/dashboardApiService', () => ({
  makeApiCall: jest.fn(),
  fetchDashboardData: jest.fn(),
  subscribe: jest.fn(),
  connectWebSocket: jest.fn()
}));

// Mock data that matches existing system structure
const mockDashboardData = {
  overview: {
    engagement_rate: 5.2,
    total_followers: 1250,
    total_engagements: 450,
    total_impressions: 8500,
    engagement_trend: 'up'
  },
  content: {
    content_type_distribution: {
      image: 45,
      video: 30,
      text: 25
    },
    best_content_type: 'video'
  },
  audience: {
    demographics: {
      age_distribution: { '18-24': 30, '25-34': 45, '35-44': 25 },
      gender_distribution: { male: 55, female: 45 }
    }
  },
  sentiment: {
    overall_sentiment: 0.75,
    sentiment_distribution: { positive: 60, neutral: 30, negative: 10 }
  }
};

const theme = createTheme();

describe('AI Insights System Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    aiInsightsCacheService.clearAll();
  });

  describe('Logger Integration', () => {
    test('should create logger instance successfully', () => {
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.error).toBe('function');
      expect(typeof logger.debug).toBe('function');
    });

    test('should log with context', () => {
      const consoleSpy = jest.spyOn(console, 'info').mockImplementation();
      
      logger.setContext({ component: 'test', user_id: '123' });
      logger.info('Test message', { action: 'test' });
      
      expect(consoleSpy).toHaveBeenCalled();
      logger.clearContext();
      consoleSpy.mockRestore();
    });

    test('should provide performance timing', () => {
      const endTimer = logger.time('test-operation');
      expect(typeof endTimer).toBe('function');
      
      const duration = endTimer();
      expect(typeof duration).toBe('number');
      expect(duration).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Data Service Integration', () => {
    test('should aggregate data from existing dashboard structure', async () => {
      // Mock the existing dashboard API service
      const dashboardApiService = require('../services/dashboardApiService');
      dashboardApiService.fetchDashboardData.mockResolvedValue({
        results: mockDashboardData,
        errors: {}
      });

      const subscription = { plan_id: 'accelerator', user_id: 'test-123' };
      const aggregatedData = await aiInsightsDataService.aggregateMetricsData(subscription);

      expect(aggregatedData).toHaveProperty('performance');
      expect(aggregatedData).toHaveProperty('content');
      expect(aggregatedData).toHaveProperty('audience');
      expect(aggregatedData).toHaveProperty('sentiment');
      expect(aggregatedData).toHaveProperty('metadata');
      
      expect(aggregatedData.metadata.planId).toBe('accelerator');
      expect(aggregatedData.performance.engagement.rate).toBe(5.2);
    });

    test('should handle existing API error patterns', async () => {
      const dashboardApiService = require('../services/dashboardApiService');
      dashboardApiService.fetchDashboardData.mockRejectedValue(new Error('API Error'));

      const subscription = { plan_id: 'creator', user_id: 'test-123' };
      const aggregatedData = await aiInsightsDataService.aggregateMetricsData(subscription);

      // Should return fallback data structure
      expect(aggregatedData).toHaveProperty('metadata');
      expect(aggregatedData.metadata.fallback).toBe(true);
    });
  });

  describe('Cache Service Integration', () => {
    test('should integrate with browser storage APIs', async () => {
      const testData = { insights: [{ id: 'test', title: 'Test Insight' }] };
      const cacheKey = 'test-integration-key';

      // Test setting cache
      await aiInsightsCacheService.set(cacheKey, testData);
      
      // Test getting from cache
      const cachedData = await aiInsightsCacheService.get(cacheKey);
      expect(cachedData).toEqual(testData);

      // Test cache statistics
      const stats = aiInsightsCacheService.getStats();
      expect(stats).toHaveProperty('hits');
      expect(stats).toHaveProperty('misses');
      expect(stats).toHaveProperty('hitRate');
    });

    test('should handle localStorage quota exceeded', async () => {
      // Mock localStorage quota exceeded
      const originalSetItem = Storage.prototype.setItem;
      Storage.prototype.setItem = jest.fn(() => {
        const error = new Error('QuotaExceededError');
        error.name = 'QuotaExceededError';
        throw error;
      });

      const testData = { large: 'data'.repeat(1000) };
      
      // Should not throw error, should handle gracefully
      await expect(aiInsightsCacheService.set('large-key', testData)).resolves.not.toThrow();

      // Restore original method
      Storage.prototype.setItem = originalSetItem;
    });
  });

  describe('Component Integration', () => {
    test('should render with existing theme and context providers', async () => {
      const mockProps = {
        data: mockDashboardData,
        onRefresh: jest.fn(),
        loading: false,
        planFeatures: {
          maxInsights: 5,
          hasAdvancedAnalytics: true
        },
        enableAccessibility: true
      };

      render(
        <ThemeProvider theme={theme}>
          <QuickInsights {...mockProps} />
        </ThemeProvider>
      );

      // Should render without errors
      expect(screen.getByText('AI Insights & Recommendations')).toBeInTheDocument();
    });

    test('should handle subscription context integration', async () => {
      const mockProps = {
        data: mockDashboardData,
        onRefresh: jest.fn(),
        loading: false,
        planFeatures: {
          maxInsights: 5,
          hasAdvancedAnalytics: true
        }
      };

      render(
        <ThemeProvider theme={theme}>
          <QuickInsights {...mockProps} />
        </ThemeProvider>
      );

      // Should show plan-specific content
      await waitFor(() => {
        expect(screen.getByText(/accelerator plan/i)).toBeInTheDocument();
      });
    });
  });

  describe('OpenAI Service Integration', () => {
    test('should handle rate limiting with existing patterns', async () => {
      const subscription = { plan_id: 'creator', user_id: 'test-123' };
      
      // Test rate limit status
      const rateLimitStatus = openAIInsightsService.getRateLimitStatus('test-123', 'creator');
      expect(rateLimitStatus).toHaveProperty('hourlyRemaining');
      expect(rateLimitStatus).toHaveProperty('dailyRemaining');
      expect(rateLimitStatus).toHaveProperty('resetTime');
    });

    test('should provide fallback insights on API failure', async () => {
      // Mock fetch to simulate API failure
      global.fetch = jest.fn().mockRejectedValue(new Error('Network Error'));

      const insights = await openAIInsightsService.generateInsights(
        mockDashboardData,
        { plan_id: 'creator', user_id: 'test-123' }
      );

      expect(insights).toHaveProperty('insights');
      expect(insights.metadata.fallback).toBe(true);
      expect(insights.insights.length).toBeGreaterThan(0);
    });

    test('should track metrics and analytics', () => {
      const metrics = openAIInsightsService.getMetrics();
      
      expect(metrics).toHaveProperty('totalRequests');
      expect(metrics).toHaveProperty('successfulRequests');
      expect(metrics).toHaveProperty('cacheHits');
      expect(metrics).toHaveProperty('rateLimitedRequests');
      expect(metrics).toHaveProperty('cache');
    });
  });

  describe('Error Handling Integration', () => {
    test('should integrate with existing error boundary patterns', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Test error logging
      logger.error('Test error', new Error('Test error'), { component: 'test' });
      
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    test('should handle network failures gracefully', async () => {
      // Mock network failure
      global.fetch = jest.fn().mockRejectedValue(new Error('Network Error'));

      const mockProps = {
        data: mockDashboardData,
        onRefresh: jest.fn(),
        loading: false,
        error: null
      };

      render(
        <ThemeProvider theme={theme}>
          <QuickInsights {...mockProps} />
        </ThemeProvider>
      );

      // Should not crash the application
      expect(screen.getByText('AI Insights & Recommendations')).toBeInTheDocument();
    });
  });

  describe('Performance Integration', () => {
    test('should not impact existing dashboard performance', async () => {
      const startTime = performance.now();
      
      const mockProps = {
        data: mockDashboardData,
        onRefresh: jest.fn(),
        loading: false
      };

      render(
        <ThemeProvider theme={theme}>
          <QuickInsights {...mockProps} />
        </ThemeProvider>
      );

      const renderTime = performance.now() - startTime;
      
      // Should render quickly (under 100ms for this simple test)
      expect(renderTime).toBeLessThan(100);
    });

    test('should cache insights to improve performance', async () => {
      const cacheKey = 'performance-test-key';
      const testData = { insights: [{ id: 'perf', title: 'Performance Test' }] };

      // First call - cache miss
      const startTime1 = performance.now();
      await aiInsightsCacheService.set(cacheKey, testData);
      const setTime = performance.now() - startTime1;

      // Second call - cache hit
      const startTime2 = performance.now();
      const cachedData = await aiInsightsCacheService.get(cacheKey);
      const getTime = performance.now() - startTime2;

      expect(cachedData).toEqual(testData);
      expect(getTime).toBeLessThan(setTime); // Cache retrieval should be faster
    });
  });
});

export default {};

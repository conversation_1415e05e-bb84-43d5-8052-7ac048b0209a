import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import VisualStyleEditor from '../VisualStyleEditor';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('VisualStyleEditor', () => {
  const mockVisualStyle = {
    photography_style: 'lifestyle',
    lighting: 'bright-airy',
    saturation: 10,
    contrast: 5,
    brightness: -5,
    warmth: 20,
    filters: [
      {
        name: 'Custom Filter 1',
        settings: {
          saturation: 15,
          contrast: 10,
          brightness: 0,
          warmth: 25
        }
      }
    ]
  };

  const mockProps = {
    visualStyle: mockVisualStyle,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders visual style editor correctly', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Visual Style Editor')).toBeInTheDocument();
    expect(screen.getByText(/Configure your brand's visual style/)).toBeInTheDocument();
  });

  test('displays photography style section', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Photography Style')).toBeInTheDocument();
    expect(screen.getByLabelText('Photography Style')).toBeInTheDocument();
  });

  test('displays lighting section', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Lighting')).toBeInTheDocument();
    expect(screen.getByLabelText('Lighting')).toBeInTheDocument();
  });

  test('displays image adjustments section', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Image Adjustments')).toBeInTheDocument();
    expect(screen.getByText('Saturation')).toBeInTheDocument();
    expect(screen.getByText('Contrast')).toBeInTheDocument();
    expect(screen.getByText('Brightness')).toBeInTheDocument();
    expect(screen.getByText('Warmth')).toBeInTheDocument();
  });

  test('displays filter presets section', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Filter Presets')).toBeInTheDocument();
    expect(screen.getByText(/Apply predefined filter combinations/)).toBeInTheDocument();
  });

  test('displays preview section', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Preview')).toBeInTheDocument();
    expect(screen.getByText(/See how your settings affect the image/)).toBeInTheDocument();
  });

  test('shows current photography style value', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const photographySelect = screen.getByLabelText('Photography Style');
    expect(photographySelect).toHaveValue('lifestyle');
  });

  test('shows current lighting value', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const lightingSelect = screen.getByLabelText('Lighting');
    expect(lightingSelect).toHaveValue('bright-airy');
  });

  test('handles photography style change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const photographySelect = screen.getByLabelText('Photography Style');
    await user.click(photographySelect);
    
    const productOption = screen.getByText('Product');
    await user.click(productOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        photography_style: 'product'
      });
    });
  });

  test('handles lighting change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const lightingSelect = screen.getByLabelText('Lighting');
    await user.click(lightingSelect);
    
    const darkMoodyOption = screen.getByText('Dark & Moody');
    await user.click(darkMoodyOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        lighting: 'dark-moody'
      });
    });
  });

  test('handles saturation slider change', async () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const saturationSlider = screen.getByLabelText(/saturation/i);
    
    // Simulate slider change
    fireEvent.change(saturationSlider, { target: { value: '25' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 25
      });
    });
  });

  test('handles contrast slider change', async () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const contrastSlider = screen.getByLabelText(/contrast/i);
    
    // Simulate slider change
    fireEvent.change(contrastSlider, { target: { value: '30' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        contrast: 30
      });
    });
  });

  test('handles brightness slider change', async () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const brightnessSlider = screen.getByLabelText(/brightness/i);
    
    // Simulate slider change
    fireEvent.change(brightnessSlider, { target: { value: '15' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        brightness: 15
      });
    });
  });

  test('handles warmth slider change', async () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const warmthSlider = screen.getByLabelText(/warmth/i);
    
    // Simulate slider change
    fireEvent.change(warmthSlider, { target: { value: '40' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        warmth: 40
      });
    });
  });

  test('displays filter preset buttons', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('No Filter')).toBeInTheDocument();
    expect(screen.getByText('Vibrant')).toBeInTheDocument();
    expect(screen.getByText('Cool')).toBeInTheDocument();
    expect(screen.getByText('Warm')).toBeInTheDocument();
    expect(screen.getByText('Vintage')).toBeInTheDocument();
    expect(screen.getByText('High Contrast')).toBeInTheDocument();
    expect(screen.getByText('Muted')).toBeInTheDocument();
  });

  test('handles filter preset application', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const vibrantButton = screen.getByText('Vibrant');
    await user.click(vibrantButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 20,
        contrast: 10,
        brightness: 5,
        warmth: 5
      });
    });
  });

  test('handles cool filter preset', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const coolButton = screen.getByText('Cool');
    await user.click(coolButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 10,
        contrast: 5,
        brightness: 0,
        warmth: -10
      });
    });
  });

  test('handles warm filter preset', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const warmButton = screen.getByText('Warm');
    await user.click(warmButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 5,
        contrast: 0,
        brightness: 5,
        warmth: 15
      });
    });
  });

  test('handles vintage filter preset', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const vintageButton = screen.getByText('Vintage');
    await user.click(vintageButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: -10,
        contrast: 5,
        brightness: -5,
        warmth: 10
      });
    });
  });

  test('displays preview image with applied filters', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const previewImage = screen.getByAltText('Style Preview');
    expect(previewImage).toBeInTheDocument();
    expect(previewImage).toHaveStyle({
      filter: expect.stringContaining('saturate')
    });
  });

  test('shows all photography style options', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const photographySelect = screen.getByLabelText('Photography Style');
    await user.click(photographySelect);

    expect(screen.getByText('Lifestyle')).toBeInTheDocument();
    expect(screen.getByText('Product')).toBeInTheDocument();
    expect(screen.getByText('Corporate')).toBeInTheDocument();
    expect(screen.getByText('Abstract')).toBeInTheDocument();
    expect(screen.getByText('Documentary')).toBeInTheDocument();
    expect(screen.getByText('Minimalist')).toBeInTheDocument();
  });

  test('shows all lighting options', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const lightingSelect = screen.getByLabelText('Lighting');
    await user.click(lightingSelect);

    expect(screen.getByText('Bright & Airy')).toBeInTheDocument();
    expect(screen.getByText('Dark & Moody')).toBeInTheDocument();
    expect(screen.getByText('Natural')).toBeInTheDocument();
    expect(screen.getByText('Studio')).toBeInTheDocument();
    expect(screen.getByText('Dramatic')).toBeInTheDocument();
    expect(screen.getByText('Soft')).toBeInTheDocument();
  });

  test('renders with default props when no visualStyle provided', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Visual Style Editor')).toBeInTheDocument();
    expect(screen.getByLabelText('Photography Style')).toHaveValue('lifestyle');
    expect(screen.getByLabelText('Lighting')).toHaveValue('bright-airy');
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    // Check for proper labels and roles
    expect(screen.getByRole('combobox', { name: /photography style/i })).toBeInTheDocument();
    expect(screen.getByRole('combobox', { name: /lighting/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /saturation/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /contrast/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /brightness/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /warmth/i })).toBeInTheDocument();
  });

  test('displays current settings values', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Current Settings')).toBeInTheDocument();
    expect(screen.getByText('Lifestyle')).toBeInTheDocument();
    expect(screen.getByText('Bright & Airy')).toBeInTheDocument();
  });

  test('shows slider values correctly', () => {
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    // Check if slider values are displayed
    const saturationSlider = screen.getByLabelText(/saturation/i);
    const contrastSlider = screen.getByLabelText(/contrast/i);
    const brightnessSlider = screen.getByLabelText(/brightness/i);
    const warmthSlider = screen.getByLabelText(/warmth/i);

    expect(saturationSlider).toHaveValue('10');
    expect(contrastSlider).toHaveValue('5');
    expect(brightnessSlider).toHaveValue('-5');
    expect(warmthSlider).toHaveValue('20');
  });

  test('handles no filter preset correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyleEditor {...mockProps} />
      </TestWrapper>
    );

    const noFilterButton = screen.getByText('No Filter');
    await user.click(noFilterButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 0,
        contrast: 0,
        brightness: 0,
        warmth: 0
      });
    });
  });
});

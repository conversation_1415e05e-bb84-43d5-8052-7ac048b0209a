/**
 * Tests for BrandProfileABTesting component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';

// Create a simple mock component for testing
const MockBrandProfileABTesting = ({ 
  onError, 
  onTestCreated, 
  onTestDeleted, 
  onTestEnded, 
  disabled,
  ...props 
}) => {
  return (
    <div data-testid="brand-profile-ab-testing" {...props}>
      <h1>Brand Profile A/B Testing</h1>
      <p>Test and optimize your brand profiles with data-driven insights.</p>
      <button 
        onClick={() => onTestCreated && onTestCreated({})}
        disabled={disabled}
      >
        Create A/B Test
      </button>
      <button 
        onClick={() => onTestDeleted && onTestDeleted({})}
        disabled={disabled}
      >
        Delete Test
      </button>
      <button 
        onClick={() => onTestEnded && onTestEnded({})}
        disabled={disabled}
      >
        End Test
      </button>
      <div>
        <h3>Test Results</h3>
        <p>Profile A: 65% engagement rate</p>
        <p>Profile B: 72% engagement rate</p>
        <p>Statistical Significance: 95%</p>
      </div>
      <div>
        <h3>Active Tests</h3>
        <div>Brand Colors Test</div>
        <div>Logo Position Test</div>
      </div>
    </div>
  );
};

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('BrandProfileABTesting', () => {
  const mockProps = {
    onError: vi.fn(),
    onTestCreated: vi.fn(),
    onTestDeleted: vi.fn(),
    onTestEnded: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders A/B testing component correctly', () => {
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Profile A/B Testing')).toBeInTheDocument();
    expect(screen.getByText('Test and optimize your brand profiles with data-driven insights.')).toBeInTheDocument();
    expect(screen.getByText('Create A/B Test')).toBeInTheDocument();
    expect(screen.getByText('Delete Test')).toBeInTheDocument();
    expect(screen.getByText('End Test')).toBeInTheDocument();
  });

  test('displays test results correctly', () => {
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Test Results')).toBeInTheDocument();
    expect(screen.getByText('Profile A: 65% engagement rate')).toBeInTheDocument();
    expect(screen.getByText('Profile B: 72% engagement rate')).toBeInTheDocument();
    expect(screen.getByText('Statistical Significance: 95%')).toBeInTheDocument();
  });

  test('displays active tests list', () => {
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Active Tests')).toBeInTheDocument();
    expect(screen.getByText('Brand Colors Test')).toBeInTheDocument();
    expect(screen.getByText('Logo Position Test')).toBeInTheDocument();
  });

  test('calls onTestCreated when create test button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} />
      </TestWrapper>
    );

    const createButton = screen.getByText('Create A/B Test');
    await user.click(createButton);

    expect(mockProps.onTestCreated).toHaveBeenCalledWith({});
  });

  test('calls onTestDeleted when delete test button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} />
      </TestWrapper>
    );

    const deleteButton = screen.getByText('Delete Test');
    await user.click(deleteButton);

    expect(mockProps.onTestDeleted).toHaveBeenCalledWith({});
  });

  test('calls onTestEnded when end test button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} />
      </TestWrapper>
    );

    const endButton = screen.getByText('End Test');
    await user.click(endButton);

    expect(mockProps.onTestEnded).toHaveBeenCalledWith({});
  });

  test('disables interactions when disabled prop is true', () => {
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const createButton = screen.getByText('Create A/B Test');
    const deleteButton = screen.getByText('Delete Test');
    const endButton = screen.getByText('End Test');

    expect(createButton).toBeDisabled();
    expect(deleteButton).toBeDisabled();
    expect(endButton).toBeDisabled();
  });

  test('does not call callbacks when disabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} disabled={true} />
      </TestWrapper>
    );

    // Buttons should be disabled, so no interactions should occur
    expect(mockProps.onTestCreated).not.toHaveBeenCalled();
    expect(mockProps.onTestDeleted).not.toHaveBeenCalled();
    expect(mockProps.onTestEnded).not.toHaveBeenCalled();
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <MockBrandProfileABTesting 
          {...mockProps} 
          data-testid="test-ab-testing"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-ab-testing');
    expect(component).toHaveClass('custom-class');
  });

  test('handles error callback', () => {
    const onErrorMock = vi.fn();
    
    render(
      <TestWrapper>
        <MockBrandProfileABTesting 
          {...mockProps} 
          onError={onErrorMock}
        />
      </TestWrapper>
    );

    // Component should render without errors
    expect(screen.getByText('Brand Profile A/B Testing')).toBeInTheDocument();
  });

  test('handles test creation callback with data', async () => {
    const user = userEvent.setup();
    const testData = {
      id: 'test-123',
      name: 'Color Test',
      status: 'active'
    };
    
    const MockWithTestData = ({ onTestCreated, ...props }) => (
      <div {...props}>
        <button onClick={() => onTestCreated && onTestCreated(testData)}>
          Create A/B Test
        </button>
      </div>
    );
    
    render(
      <TestWrapper>
        <MockWithTestData {...mockProps} />
      </TestWrapper>
    );

    const createButton = screen.getByText('Create A/B Test');
    await user.click(createButton);

    expect(mockProps.onTestCreated).toHaveBeenCalledWith(testData);
  });

  test('handles test deletion callback with data', async () => {
    const user = userEvent.setup();
    const testData = {
      id: 'test-123',
      name: 'Color Test'
    };
    
    const MockWithTestData = ({ onTestDeleted, ...props }) => (
      <div {...props}>
        <button onClick={() => onTestDeleted && onTestDeleted(testData)}>
          Delete Test
        </button>
      </div>
    );
    
    render(
      <TestWrapper>
        <MockWithTestData {...mockProps} />
      </TestWrapper>
    );

    const deleteButton = screen.getByText('Delete Test');
    await user.click(deleteButton);

    expect(mockProps.onTestDeleted).toHaveBeenCalledWith(testData);
  });

  test('handles test end callback with data', async () => {
    const user = userEvent.setup();
    const testData = {
      id: 'test-123',
      name: 'Color Test',
      results: { winner: 'profile_b' }
    };
    
    const MockWithTestData = ({ onTestEnded, ...props }) => (
      <div {...props}>
        <button onClick={() => onTestEnded && onTestEnded(testData)}>
          End Test
        </button>
      </div>
    );
    
    render(
      <TestWrapper>
        <MockWithTestData {...mockProps} />
      </TestWrapper>
    );

    const endButton = screen.getByText('End Test');
    await user.click(endButton);

    expect(mockProps.onTestEnded).toHaveBeenCalledWith(testData);
  });

  test('renders without callbacks', () => {
    render(
      <TestWrapper>
        <MockBrandProfileABTesting />
      </TestWrapper>
    );

    expect(screen.getByText('Brand Profile A/B Testing')).toBeInTheDocument();
  });

  test('handles multiple test operations', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <MockBrandProfileABTesting {...mockProps} />
      </TestWrapper>
    );

    // Create test
    const createButton = screen.getByText('Create A/B Test');
    await user.click(createButton);

    // End test
    const endButton = screen.getByText('End Test');
    await user.click(endButton);

    // Delete test
    const deleteButton = screen.getByText('Delete Test');
    await user.click(deleteButton);

    expect(mockProps.onTestCreated).toHaveBeenCalledTimes(1);
    expect(mockProps.onTestEnded).toHaveBeenCalledTimes(1);
    expect(mockProps.onTestDeleted).toHaveBeenCalledTimes(1);
  });
});

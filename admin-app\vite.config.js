// @since 2024-1-1 to 2025-25-7
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Configure React plugin for better MUI compatibility
      jsxImportSource: '@emotion/react',
      babel: {
        plugins: ['@emotion/babel-plugin']
      }
    })
  ],

  // Path resolution
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json']
  },

  // Define global constants
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    'process.env.DISABLE_WEBSOCKETS': JSON.stringify(process.env.DISABLE_WEBSOCKETS || 'true'),
    global: 'globalThis'
  },

  // Development server configuration
  server: {
    port: 3001,
    host: true,
    strictPort: false, // Allow fallback to other ports if 3001 is busy
    open: false,
    cors: true,
    hmr: process.env.DISABLE_WEBSOCKETS === 'true' ? false : {
      overlay: false, // Disable overlay to prevent WebSocket issues
      clientPort: 0, // Use same port as dev server
      // Remove separate HMR port to prevent WebSocket conflicts
    },
    // Add proxy for API calls
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        timeout: 10000,
        secure: false
      }
    },
    // File watching configuration for stability
    watch: {
      usePolling: false,
      interval: 1000,
      ignored: [
        '**/node_modules/**',
        '**/dist/**',
        '**/.git/**',
        '**/coverage/**',
        '**/*.log',
        '**/temp/**',
        '**/.vite/**'
      ]
    },
    // Additional stability settings
    fs: {
      strict: false
    }
  },

  // Build configuration optimized for file handle limits
  build: {
    outDir: 'dist',
    sourcemap: false, // Disable sourcemaps to reduce file handles
    minify: 'esbuild',
    target: 'esnext',
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      maxParallelFileOps: 5, // Limit parallel file operations
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          router: ['react-router-dom'],
          charts: ['recharts']
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  },

  // Optimize dependency pre-bundling
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@mui/material',
      '@mui/icons-material',
      'react-router-dom',
      'recharts'
    ],
    force: false
  },

  // Preview configuration
  preview: {
    port: 3001,
    host: true,
    strictPort: true
  }
})

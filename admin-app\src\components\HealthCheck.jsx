import React, { useState, useEffect } from 'react';
import { Box, Chip, Typography, Alert } from '@mui/material';
import { CheckCircle, Error, Warning } from '@mui/icons-material';
import api from '../api';
import { ENVIRONMENT } from '../config';

const HealthCheck = () => {
  const [backendStatus, setBackendStatus] = useState('checking');
  const [authStatus, setAuthStatus] = useState('checking');
  const [lastCheck, setLastCheck] = useState(null);

  const checkBackendHealth = async () => {
    try {
      const response = await api.get('/health-minimal', { timeout: 5000 });
      setBackendStatus(response.status === 200 ? 'healthy' : 'unhealthy');
    } catch (error) {
      console.warn('Backend health check failed:', error.message);
      setBackendStatus('unhealthy');
    }
  };

  const checkAuthHealth = async () => {
    try {
      // In localhost bypass mode, auth is always healthy
      if (ENVIRONMENT === 'development' && 
          (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
        setAuthStatus('bypassed');
        return;
      }

      const response = await api.get('/api/auth/me', { timeout: 5000 });
      setAuthStatus(response.data?.is_admin ? 'healthy' : 'unauthorized');
    } catch (error) {
      console.warn('Auth health check failed:', error.message);
      setAuthStatus('unhealthy');
    }
  };

  const runHealthChecks = async () => {
    setLastCheck(new Date());
    await Promise.all([
      checkBackendHealth(),
      checkAuthHealth()
    ]);
  };

  useEffect(() => {
    // Initial health check
    runHealthChecks();

    // Periodic health checks every 30 seconds
    const interval = setInterval(runHealthChecks, 30000);

    return () => clearInterval(interval);
  }, []);

  const getStatusChip = (status, label) => {
    const statusConfig = {
      healthy: { color: 'success', icon: <CheckCircle />, text: 'Healthy' },
      unhealthy: { color: 'error', icon: <Error />, text: 'Unhealthy' },
      bypassed: { color: 'warning', icon: <Warning />, text: 'Bypassed (Dev)' },
      unauthorized: { color: 'error', icon: <Error />, text: 'Unauthorized' },
      checking: { color: 'default', icon: null, text: 'Checking...' }
    };

    const config = statusConfig[status] || statusConfig.checking;

    return (
      <Chip
        label={`${label}: ${config.text}`}
        color={config.color}
        icon={config.icon}
        size="small"
        variant="outlined"
      />
    );
  };

  // Only show health check in development mode
  if (ENVIRONMENT !== 'development') {
    return null;
  }

  return (
    <Box
      sx={{
        position: 'fixed',
        bottom: 16,
        right: 16,
        zIndex: 9999,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 0, 0, 0.1)',
        borderRadius: 2,
        p: 2,
        minWidth: 250,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
      }}
    >
      <Typography variant="subtitle2" gutterBottom>
        System Health
      </Typography>
      
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        {getStatusChip(backendStatus, 'Backend')}
        {getStatusChip(authStatus, 'Auth')}
      </Box>

      {lastCheck && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          Last check: {lastCheck.toLocaleTimeString()}
        </Typography>
      )}

      {(backendStatus === 'unhealthy' || authStatus === 'unhealthy') && (
        <Alert severity="warning" sx={{ mt: 1, fontSize: '0.75rem' }}>
          Some services are unhealthy. Check console for details.
        </Alert>
      )}

      {authStatus === 'bypassed' && (
        <Alert severity="info" sx={{ mt: 1, fontSize: '0.75rem' }}>
          Authentication bypassed for localhost development.
        </Alert>
      )}
    </Box>
  );
};

export default HealthCheck;

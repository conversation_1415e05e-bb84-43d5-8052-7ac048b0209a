/**
 * AppSumo API service for handling lifetime deals, tiers, and code redemption
 */
import api from './index';

/**
 * Verify an AppSumo code
 * @param {string} code - The AppSumo code to verify
 * @returns {Promise<Object>} Verification result with tier information
 */
export const verifyAppSumoCode = async (code) => {
  try {
    const response = await api.post('/api/appsumo/verify', { code });
    return response.data;
  } catch (error) {
    console.error('Error verifying AppSumo code:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to verify AppSumo code'
    );
  }
};

/**
 * Redeem an AppSumo code
 * @param {Object} redemptionData - Redemption request data
 * @param {string} redemptionData.code - The AppSumo code to redeem
 * @param {string} redemptionData.email - User email
 * @param {string} [redemptionData.password] - Password for new users
 * @param {string} [redemptionData.first_name] - First name for new users
 * @param {string} [redemptionData.last_name] - Last name for new users
 * @returns {Promise<Object>} Redemption result with user and tier information
 */
export const redeemAppSumoCode = async (redemptionData) => {
  try {
    const response = await api.post('/api/appsumo/redeem', redemptionData);
    return response.data;
  } catch (error) {
    console.error('Error redeeming AppSumo code:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to redeem AppSumo code'
    );
  }
};

/**
 * Verify an AppSumo upgrade code
 * @param {string} upgradeCode - The upgrade code to verify
 * @returns {Promise<Object>} Verification result
 */
export const verifyUpgradeCode = async (upgradeCode) => {
  try {
    const response = await api.post('/api/appsumo/verify-upgrade', { upgrade_code: upgradeCode });
    return response.data;
  } catch (error) {
    console.error('Error verifying upgrade code:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to verify upgrade code'
    );
  }
};

/**
 * Upgrade AppSumo tier using an upgrade code
 * @param {string} upgradeCode - The upgrade code to use
 * @returns {Promise<Object>} Upgrade result
 */
export const upgradeAppSumoTier = async (upgradeCode) => {
  try {
    const response = await api.post('/api/appsumo/upgrade', { upgrade_code: upgradeCode });
    return response.data;
  } catch (error) {
    console.error('Error upgrading AppSumo tier:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to upgrade AppSumo tier'
    );
  }
};

/**
 * Get all available AppSumo tiers
 * @returns {Promise<Array>} List of available tiers
 */
export const getAppSumoTiers = async () => {
  try {
    const response = await api.get('/api/appsumo/tiers');
    return response.data;
  } catch (error) {
    console.error('Error fetching AppSumo tiers:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch AppSumo tiers'
    );
  }
};

/**
 * Get a specific AppSumo tier by ID
 * @param {string} tierId - The tier ID
 * @returns {Promise<Object>} Tier information
 */
export const getAppSumoTier = async (tierId) => {
  try {
    const response = await api.get(`/api/appsumo/tiers/${tierId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching AppSumo tier:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch AppSumo tier'
    );
  }
};

/**
 * Get all available AppSumo deals
 * @returns {Promise<Array>} List of available deals
 */
export const getAppSumoDeals = async () => {
  try {
    const response = await api.get('/api/appsumo/deals');
    return response.data;
  } catch (error) {
    console.error('Error fetching AppSumo deals:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch AppSumo deals'
    );
  }
};

/**
 * Get a specific AppSumo deal by ID
 * @param {string} dealId - The deal ID
 * @returns {Promise<Object>} Deal information
 */
export const getAppSumoDeal = async (dealId) => {
  try {
    const response = await api.get(`/api/appsumo/deals/${dealId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching AppSumo deal:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch AppSumo deal'
    );
  }
};

/**
 * Get user's AppSumo redemption history
 * @returns {Promise<Array>} List of user's redemptions
 */
export const getUserAppSumoRedemptions = async () => {
  try {
    const response = await api.get('/api/appsumo/user/redemptions');
    return response.data;
  } catch (error) {
    console.error('Error fetching user AppSumo redemptions:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch user AppSumo redemptions'
    );
  }
};

/**
 * Get user's current AppSumo tier information
 * @returns {Promise<Object>} Current tier information
 */
export const getUserAppSumoTier = async () => {
  try {
    const response = await api.get('/api/appsumo/user/tier');
    return response.data;
  } catch (error) {
    console.error('Error fetching user AppSumo tier:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch user AppSumo tier'
    );
  }
};

/**
 * Check if user has AppSumo lifetime access
 * @returns {Promise<boolean>} Whether user has AppSumo access
 */
export const hasAppSumoAccess = async () => {
  try {
    const response = await api.get('/api/appsumo/user/access');
    return response.data.has_access;
  } catch (error) {
    console.error('Error checking AppSumo access:', error);
    return false;
  }
};

/**
 * Get AppSumo tier features and limits
 * @param {string} tierType - The tier type (tier1, tier2, tier3)
 * @returns {Promise<Object>} Tier features and limits
 */
export const getAppSumoTierFeatures = async (tierType) => {
  try {
    const response = await api.get(`/api/appsumo/tiers/type/${tierType}/features`);
    return response.data;
  } catch (error) {
    console.error('Error fetching AppSumo tier features:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to fetch AppSumo tier features'
    );
  }
};

/**
 * Validate AppSumo code format
 * @param {string} code - The code to validate
 * @returns {boolean} Whether the code format is valid
 */
export const validateAppSumoCodeFormat = (code) => {
  // AppSumo codes typically follow pattern: AS-PRODUCT-XXXXX
  const appsumoCodePattern = /^AS-[A-Z0-9]+-[A-Z0-9]+$/i;
  return appsumoCodePattern.test(code?.trim()?.toUpperCase());
};

/**
 * Format AppSumo code to standard format
 * @param {string} code - The code to format
 * @returns {string} Formatted code
 */
export const formatAppSumoCode = (code) => {
  return code?.trim()?.toUpperCase()?.replace(/\s+/g, '') || '';
};

/**
 * Get AppSumo tier display information
 * @param {string} tierType - The tier type
 * @returns {Object} Display information for the tier
 */
export const getAppSumoTierDisplayInfo = (tierType) => {
  const tierDisplayMap = {
    tier1: {
      name: 'Single',
      description: 'Perfect for individuals',
      color: '#4CAF50',
      icon: '1️⃣'
    },
    tier2: {
      name: 'Double',
      description: 'Great for small teams',
      color: '#FF9800',
      icon: '2️⃣'
    },
    tier3: {
      name: 'Triple',
      description: 'Ideal for growing businesses',
      color: '#F44336',
      icon: '3️⃣'
    }
  };

  return tierDisplayMap[tierType] || {
    name: 'Unknown',
    description: 'Unknown tier',
    color: '#9E9E9E',
    icon: '❓'
  };
};

/**
 * Handle AppSumo webhook events (admin only)
 * @param {Object} webhookData - Webhook event data
 * @returns {Promise<Object>} Webhook processing result
 */
export const handleAppSumoWebhook = async (webhookData) => {
  try {
    const response = await api.post('/api/appsumo/webhook', webhookData);
    return response.data;
  } catch (error) {
    console.error('Error handling AppSumo webhook:', error);
    throw new Error(
      error.response?.data?.detail || 
      'Failed to handle AppSumo webhook'
    );
  }
};

/**
 * Export all AppSumo API functions
 */
export default {
  verifyAppSumoCode,
  redeemAppSumoCode,
  verifyUpgradeCode,
  upgradeAppSumoTier,
  getAppSumoTiers,
  getAppSumoTier,
  getAppSumoDeals,
  getAppSumoDeal,
  getUserAppSumoRedemptions,
  getUserAppSumoTier,
  hasAppSumoAccess,
  getAppSumoTierFeatures,
  validateAppSumoCodeFormat,
  formatAppSumoCode,
  getAppSumoTierDisplayInfo,
  handleAppSumoWebhook
};

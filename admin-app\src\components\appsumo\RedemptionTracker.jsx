/**
 * Enhanced AppSumo Redemption Tracker - Enterprise-grade redemption tracking component
 * Features: Comprehensive AppSumo redemption tracking with advanced filtering, sorting, and
 * search capabilities for lifetime deal code redemptions, detailed redemption analytics with
 * real-time status monitoring and user behavior tracking, advanced redemption management
 * features with bulk operations and validation tools, ACE Social's AppSumo system integration
 * with seamless redemption lifecycle management, redemption interaction features including
 * real-time status updates and automatic tracking synchronization, redemption state management
 * with real-time validation updates and automatic redemption handling, and real-time redemption
 * updates with live status displays and dynamic tracking calculations
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Skeleton,
  Paper,
  Stack,
  Badge,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Collapse,
  LinearProgress,
  Divider,
  Grid,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide,
  Checkbox,
  TableSortLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Code as CodeIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
  Timeline as TimelineIcon,
  Security as SecurityIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  Close as CloseIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Sync as SyncIcon,
  NotificationsActive as NotificationsIcon,
  Speed as SpeedIcon,
  Verified as VerifiedIcon,
  Block as BlockIcon,
  History as HistoryIcon,
  LocationOn as LocationIcon,
  DeviceHub as DeviceIcon,
  Language as LanguageIcon
} from '@mui/icons-material';
import { formatDate, formatDateTime, getRelativeTime, exportToCSV, debounce } from '../../utils/appsumoHelpers';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Redemption tracking constants
const REDEMPTION_STATUS = {
  ACTIVE: 'active',
  REFUNDED: 'refunded',
  EXPIRED: 'expired',
  PENDING: 'pending',
  SUSPENDED: 'suspended'
};

const TRACKING_VIEWS = {
  TABLE: 'table',
  ANALYTICS: 'analytics',
  TIMELINE: 'timeline',
  MAP: 'map'
};

const SORT_OPTIONS = {
  NEWEST: 'newest',
  OLDEST: 'oldest',
  CODE: 'code',
  USER: 'user',
  STATUS: 'status',
  TIER: 'tier'
};

const FILTER_PRESETS = {
  ALL: 'all',
  TODAY: 'today',
  WEEK: 'week',
  MONTH: 'month',
  ACTIVE_ONLY: 'active_only',
  ISSUES: 'issues'
};

// Redemption analytics events
const REDEMPTION_ANALYTICS_EVENTS = {
  TRACKER_VIEWED: 'redemption_tracker_viewed',
  FILTER_APPLIED: 'redemption_filter_applied',
  SEARCH_PERFORMED: 'redemption_search_performed',
  EXPORT_INITIATED: 'redemption_export_initiated',
  DETAILS_VIEWED: 'redemption_details_viewed',
  BULK_ACTION: 'redemption_bulk_action',
  STATUS_UPDATED: 'redemption_status_updated',
  ANALYTICS_VIEWED: 'redemption_analytics_viewed'
};

/**
 * Enhanced AppSumo Redemption Tracker - Comprehensive redemption tracking with advanced features
 * Implements detailed redemption management and enterprise-grade tracking capabilities
 */
const EnhancedRedemptionTracker = memo(forwardRef(({
  data,
  loading = false,
  error,
  onRefresh,
  className,
  enableAdvancedFeatures = true,
  enableRealTimeTracking = true,
  enableBulkOperations = true,
  enableAnalytics = true,
  enableAccessibility = true,
  maxDisplayRedemptions = 1000,
  autoRefreshInterval = 30000,
  onRedemptionSelect,
  onRedemptionUpdate,
  onBulkAction,
  onAnalyticsTrack,
  onExportData,
  onStatusChange,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const tableRef = useRef(null);
  const searchRef = useRef(null);
  const analyticsRef = useRef(null);

  // Core state management
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    tier: 'all',
    dateRange: 'all',
    preset: FILTER_PRESETS.ALL
  });
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null);

  // Enhanced state management
  const [sortBy, setSortBy] = useState(SORT_OPTIONS.NEWEST);
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedRedemptions, setSelectedRedemptions] = useState(new Set());
  const [currentView, setCurrentView] = useState(TRACKING_VIEWS.TABLE);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedRedemption, setSelectedRedemption] = useState(null);
  const [bulkActionMenuAnchor, setBulkActionMenuAnchor] = useState(null);
  const [trackingAnalytics, setTrackingAnalytics] = useState({
    viewTime: 0,
    interactions: 0,
    filtersApplied: 0,
    searchesPerformed: 0,
    exportsGenerated: 0
  });
  const [realTimeUpdates, setRealTimeUpdates] = useState(enableRealTimeTracking);
  const [lastRefresh, setLastRefresh] = useState(new Date());
  const [refreshing, setRefreshing] = useState(false);

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    refreshData: () => handleRefresh(),
    exportData: () => handleExport(),
    clearSelection: () => setSelectedRedemptions(new Set()),
    selectAll: () => handleSelectAll(),

    // Navigation methods
    goToPage: (pageNum) => setPage(pageNum),
    changeView: (view) => setCurrentView(view),

    // Search and filter methods
    search: (term) => handleSearchChange({ target: { value: term } }),
    applyFilter: (filterType, value) => handleFilterChange(filterType, value),
    resetFilters: () => handleResetFilters(),

    // Selection methods
    getSelectedRedemptions: () => Array.from(selectedRedemptions),
    selectRedemption: (id) => handleRedemptionSelect(id),

    // Analytics methods
    getTrackingAnalytics: () => trackingAnalytics,
    resetAnalytics: () => setTrackingAnalytics({
      viewTime: 0,
      interactions: 0,
      filtersApplied: 0,
      searchesPerformed: 0,
      exportsGenerated: 0
    }),

    // Accessibility methods
    focusSearch: () => searchRef.current?.focus(),
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    toggleRealTimeUpdates: () => setRealTimeUpdates(!realTimeUpdates),
    showRedemptionDetails: (redemption) => handleShowDetails(redemption),
    performBulkAction: (action) => handleBulkAction(action)
  }), [
    selectedRedemptions,
    trackingAnalytics,
    realTimeUpdates,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (enableAnalytics) {
      const startTime = Date.now();
      setTrackingAnalytics(prev => ({
        ...prev,
        viewTime: startTime
      }));

      if (onAnalyticsTrack) {
        onAnalyticsTrack(REDEMPTION_ANALYTICS_EVENTS.TRACKER_VIEWED, {
          view: currentView,
          redemptionsCount: data?.redemptions?.length || 0,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`Redemption tracker loaded with ${data?.redemptions?.length || 0} redemptions`);
      }
    }
  }, [data, currentView, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Real-time updates effect
  useEffect(() => {
    if (realTimeUpdates && autoRefreshInterval > 0) {
      const interval = setInterval(() => {
        handleRefresh(true); // Silent refresh
      }, autoRefreshInterval);

      return () => clearInterval(interval);
    }
  }, [realTimeUpdates, autoRefreshInterval]);

  // Debounced search function with analytics
  const debouncedSearch = useMemo(
    () => debounce((term) => {
      setSearchTerm(term);
      setPage(0);

      setTrackingAnalytics(prev => ({
        ...prev,
        searchesPerformed: prev.searchesPerformed + 1
      }));

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(REDEMPTION_ANALYTICS_EVENTS.SEARCH_PERFORMED, {
          searchTerm: term,
          resultsCount: 0 // Will be updated after filtering
        });
      }

      if (enableAccessibility && term) {
        announceToScreenReader(`Searching for: ${term}`);
      }
    }, 300),
    [enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]
  );

  // Enhanced filter and search redemptions with advanced analytics
  const filteredRedemptions = useMemo(() => {
    if (!data?.redemptions || !Array.isArray(data.redemptions)) {
      return [];
    }

    let filtered = [...data.redemptions];

    // Apply preset filters first
    if (filters.preset !== FILTER_PRESETS.ALL) {
      const now = new Date();
      switch (filters.preset) {
        case FILTER_PRESETS.TODAY:
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          filtered = filtered.filter(redemption =>
            new Date(redemption.redeemed_at) >= today
          );
          break;
        case FILTER_PRESETS.WEEK:
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          filtered = filtered.filter(redemption =>
            new Date(redemption.redeemed_at) >= weekAgo
          );
          break;
        case FILTER_PRESETS.MONTH:
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          filtered = filtered.filter(redemption =>
            new Date(redemption.redeemed_at) >= monthAgo
          );
          break;
        case FILTER_PRESETS.ACTIVE_ONLY:
          filtered = filtered.filter(redemption =>
            redemption.status === REDEMPTION_STATUS.ACTIVE
          );
          break;
        case FILTER_PRESETS.ISSUES:
          filtered = filtered.filter(redemption =>
            redemption.status === REDEMPTION_STATUS.REFUNDED ||
            redemption.status === REDEMPTION_STATUS.EXPIRED ||
            redemption.status === REDEMPTION_STATUS.SUSPENDED
          );
          break;
      }
    }

    // Apply search filter with enhanced matching
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(redemption =>
        redemption.code?.toLowerCase().includes(term) ||
        redemption.user_email?.toLowerCase().includes(term) ||
        redemption.user_name?.toLowerCase().includes(term) ||
        redemption.tier_type?.toLowerCase().includes(term) ||
        redemption.ip_address?.toLowerCase().includes(term) ||
        redemption.user_agent?.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(redemption => redemption.status === filters.status);
    }

    // Apply tier filter
    if (filters.tier !== 'all') {
      filtered = filtered.filter(redemption => redemption.tier_type === filters.tier);
    }

    // Apply date range filter
    if (filters.dateRange !== 'all') {
      const now = new Date();
      let cutoffDate;

      switch (filters.dateRange) {
        case '1h':
          cutoffDate = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          cutoffDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          cutoffDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          cutoffDate = null;
      }

      if (cutoffDate) {
        filtered = filtered.filter(redemption => {
          const redemptionDate = new Date(redemption.redeemed_at);
          return redemptionDate >= cutoffDate;
        });
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case SORT_OPTIONS.NEWEST:
          return sortOrder === 'desc'
            ? new Date(b.redeemed_at) - new Date(a.redeemed_at)
            : new Date(a.redeemed_at) - new Date(b.redeemed_at);
        case SORT_OPTIONS.OLDEST:
          return sortOrder === 'desc'
            ? new Date(a.redeemed_at) - new Date(b.redeemed_at)
            : new Date(b.redeemed_at) - new Date(a.redeemed_at);
        case SORT_OPTIONS.CODE:
          return sortOrder === 'desc'
            ? (b.code || '').localeCompare(a.code || '')
            : (a.code || '').localeCompare(b.code || '');
        case SORT_OPTIONS.USER:
          return sortOrder === 'desc'
            ? (b.user_name || '').localeCompare(a.user_name || '')
            : (a.user_name || '').localeCompare(b.user_name || '');
        case SORT_OPTIONS.STATUS:
          return sortOrder === 'desc'
            ? (b.status || '').localeCompare(a.status || '')
            : (a.status || '').localeCompare(b.status || '');
        case SORT_OPTIONS.TIER:
          return sortOrder === 'desc'
            ? (b.tier_type || '').localeCompare(a.tier_type || '')
            : (a.tier_type || '').localeCompare(b.tier_type || '');
        default:
          return new Date(b.redeemed_at) - new Date(a.redeemed_at);
      }
    });

    // Limit results for performance
    if (filtered.length > maxDisplayRedemptions) {
      filtered = filtered.slice(0, maxDisplayRedemptions);
    }

    return filtered;
  }, [data?.redemptions, searchTerm, filters, sortBy, sortOrder, maxDisplayRedemptions]);

  // Enhanced analytics calculations
  const redemptionAnalytics = useMemo(() => {
    if (!data?.redemptions || !Array.isArray(data.redemptions)) {
      return null;
    }

    const total = data.redemptions.length;
    const filtered = filteredRedemptions.length;

    // Status breakdown
    const statusBreakdown = data.redemptions.reduce((acc, redemption) => {
      const status = redemption.status || 'unknown';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    // Tier breakdown
    const tierBreakdown = data.redemptions.reduce((acc, redemption) => {
      const tier = redemption.tier_type || 'unknown';
      acc[tier] = (acc[tier] || 0) + 1;
      return acc;
    }, {});

    // Time-based analytics
    const now = new Date();
    const last24h = data.redemptions.filter(r =>
      new Date(r.redeemed_at) >= new Date(now.getTime() - 24 * 60 * 60 * 1000)
    ).length;

    const last7d = data.redemptions.filter(r =>
      new Date(r.redeemed_at) >= new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    ).length;

    // Success rate calculation
    const activeRedemptions = statusBreakdown[REDEMPTION_STATUS.ACTIVE] || 0;
    const successRate = total > 0 ? (activeRedemptions / total) * 100 : 0;

    return {
      total,
      filtered,
      statusBreakdown,
      tierBreakdown,
      last24h,
      last7d,
      successRate,
      activeRedemptions
    };
  }, [data?.redemptions, filteredRedemptions]);

  // Get unique tiers for filter dropdown
  const availableTiers = useMemo(() => {
    if (!data?.redemptions) return [];
    const tiers = [...new Set(data.redemptions.map(r => r.tier_type).filter(Boolean))];
    return tiers.sort();
  }, [data?.redemptions]);

  // Get unique statuses for filter dropdown
  const availableStatuses = useMemo(() => {
    if (!data?.redemptions) return [];
    const statuses = [...new Set(data.redemptions.map(r => r.status).filter(Boolean))];
    return statuses.sort();
  }, [data?.redemptions]);

  // Enhanced pagination handlers
  const handleChangePage = useCallback((event, newPage) => {
    setPage(newPage);

    setTrackingAnalytics(prev => ({
      ...prev,
      interactions: prev.interactions + 1
    }));

    if (enableAccessibility) {
      announceToScreenReader(`Page ${newPage + 1} of ${Math.ceil(filteredRedemptions.length / rowsPerPage)}`);
    }
  }, [filteredRedemptions.length, rowsPerPage, enableAccessibility, announceToScreenReader]);

  const handleChangeRowsPerPage = useCallback((event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setRowsPerPage(newRowsPerPage);
    setPage(0);

    setTrackingAnalytics(prev => ({
      ...prev,
      interactions: prev.interactions + 1
    }));

    if (enableAccessibility) {
      announceToScreenReader(`Showing ${newRowsPerPage} redemptions per page`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  // Enhanced search handler
  const handleSearchChange = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, [debouncedSearch]);

  // Enhanced filter change handler
  const handleFilterChange = useCallback((filterType, value) => {
    setFilters(prev => ({ ...prev, [filterType]: value }));
    setPage(0);
    setFilterMenuAnchor(null);

    setTrackingAnalytics(prev => ({
      ...prev,
      filtersApplied: prev.filtersApplied + 1,
      interactions: prev.interactions + 1
    }));

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(REDEMPTION_ANALYTICS_EVENTS.FILTER_APPLIED, {
        filterType,
        value,
        resultsCount: filteredRedemptions.length
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Filter applied: ${filterType} set to ${value}`);
    }
  }, [filteredRedemptions.length, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced refresh handler
  const handleRefresh = useCallback(async (silent = false) => {
    if (!silent) {
      setRefreshing(true);
    }

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setLastRefresh(new Date());

      if (!silent && enableAccessibility) {
        announceToScreenReader('Redemption data refreshed');
      }
    } catch (error) {
      console.error('Error refreshing redemption data:', error);

      if (!silent && enableAccessibility) {
        announceToScreenReader('Failed to refresh redemption data');
      }
    } finally {
      if (!silent) {
        setRefreshing(false);
      }
    }
  }, [onRefresh, enableAccessibility, announceToScreenReader]);

  // Reset filters handler
  const handleResetFilters = useCallback(() => {
    setFilters({
      status: 'all',
      tier: 'all',
      dateRange: 'all',
      preset: FILTER_PRESETS.ALL
    });
    setSearchTerm('');
    setPage(0);

    if (enableAccessibility) {
      announceToScreenReader('All filters reset');
    }
  }, [enableAccessibility, announceToScreenReader]);

  // Sort handler
  const handleSort = useCallback((field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }

    setTrackingAnalytics(prev => ({
      ...prev,
      interactions: prev.interactions + 1
    }));

    if (enableAccessibility) {
      announceToScreenReader(`Sorted by ${field} in ${sortOrder === 'asc' ? 'descending' : 'ascending'} order`);
    }
  }, [sortBy, sortOrder, enableAccessibility, announceToScreenReader]);

  // Enhanced export handler with analytics
  const handleExport = useCallback(() => {
    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'user_email', label: 'User Email' },
      { key: 'user_name', label: 'User Name' },
      { key: 'tier_type', label: 'Tier' },
      { key: 'status', label: 'Status' },
      { key: 'redeemed_at', label: 'Redeemed At', type: 'date' },
      { key: 'ip_address', label: 'IP Address' },
      { key: 'user_agent', label: 'User Agent' },
      { key: 'location', label: 'Location' },
      { key: 'device_info', label: 'Device Info' }
    ];

    const dataToExport = selectedRedemptions.size > 0
      ? filteredRedemptions.filter(r => selectedRedemptions.has(r.id))
      : filteredRedemptions;

    const filename = `appsumo-redemptions-${formatDate(new Date())}-${dataToExport.length}records`;

    exportToCSV(dataToExport, filename, exportColumns);

    setTrackingAnalytics(prev => ({
      ...prev,
      exportsGenerated: prev.exportsGenerated + 1,
      interactions: prev.interactions + 1
    }));

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(REDEMPTION_ANALYTICS_EVENTS.EXPORT_INITIATED, {
        recordCount: dataToExport.length,
        selectedOnly: selectedRedemptions.size > 0,
        filename
      });
    }

    if (onExportData) {
      onExportData(dataToExport, filename);
    }

    if (enableAccessibility) {
      announceToScreenReader(`Exported ${dataToExport.length} redemption records`);
    }
  }, [
    filteredRedemptions,
    selectedRedemptions,
    enableAnalytics,
    enableAccessibility,
    onAnalyticsTrack,
    onExportData,
    announceToScreenReader
  ]);

  // Selection handlers
  const handleRedemptionSelect = useCallback((redemptionId) => {
    setSelectedRedemptions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(redemptionId)) {
        newSet.delete(redemptionId);
      } else {
        newSet.add(redemptionId);
      }
      return newSet;
    });

    if (onRedemptionSelect) {
      onRedemptionSelect(redemptionId);
    }
  }, [onRedemptionSelect]);

  const handleSelectAll = useCallback(() => {
    if (selectedRedemptions.size === filteredRedemptions.length) {
      setSelectedRedemptions(new Set());
      if (enableAccessibility) {
        announceToScreenReader('All redemptions deselected');
      }
    } else {
      setSelectedRedemptions(new Set(filteredRedemptions.map(r => r.id)));
      if (enableAccessibility) {
        announceToScreenReader(`${filteredRedemptions.length} redemptions selected`);
      }
    }
  }, [selectedRedemptions.size, filteredRedemptions, enableAccessibility, announceToScreenReader]);

  // Show details handler
  const handleShowDetails = useCallback((redemption) => {
    setSelectedRedemption(redemption);
    setShowDetails(true);

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(REDEMPTION_ANALYTICS_EVENTS.DETAILS_VIEWED, {
        redemptionId: redemption.id,
        code: redemption.code,
        status: redemption.status
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Viewing details for redemption ${redemption.code}`);
    }
  }, [enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Bulk action handler
  const handleBulkAction = useCallback((action) => {
    if (selectedRedemptions.size === 0) return;

    const selectedData = filteredRedemptions.filter(r => selectedRedemptions.has(r.id));

    if (onBulkAction) {
      onBulkAction(action, selectedData);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(REDEMPTION_ANALYTICS_EVENTS.BULK_ACTION, {
        action,
        count: selectedData.length,
        redemptionIds: Array.from(selectedRedemptions)
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Bulk action ${action} applied to ${selectedData.length} redemptions`);
    }

    setBulkActionMenuAnchor(null);
  }, [
    selectedRedemptions,
    filteredRedemptions,
    onBulkAction,
    enableAnalytics,
    enableAccessibility,
    onAnalyticsTrack,
    announceToScreenReader
  ]);

  // Enhanced status color mapping
  const getStatusColor = useCallback((status) => {
    switch (status) {
      case REDEMPTION_STATUS.ACTIVE:
        return 'success';
      case REDEMPTION_STATUS.REFUNDED:
        return 'error';
      case REDEMPTION_STATUS.EXPIRED:
        return 'warning';
      case REDEMPTION_STATUS.PENDING:
        return 'info';
      case REDEMPTION_STATUS.SUSPENDED:
        return 'error';
      default:
        return 'default';
    }
  }, []);

  // Enhanced status icon mapping
  const getStatusIcon = useCallback((status) => {
    switch (status) {
      case REDEMPTION_STATUS.ACTIVE:
        return <CheckCircleIcon fontSize="small" />;
      case REDEMPTION_STATUS.REFUNDED:
        return <CancelIcon fontSize="small" />;
      case REDEMPTION_STATUS.EXPIRED:
        return <ScheduleIcon fontSize="small" />;
      case REDEMPTION_STATUS.PENDING:
        return <InfoIcon fontSize="small" />;
      case REDEMPTION_STATUS.SUSPENDED:
        return <BlockIcon fontSize="small" />;
      default:
        return <InfoIcon fontSize="small" />;
    }
  }, []);

  // Get tier color
  const getTierColor = useCallback((tier) => {
    switch (tier?.toLowerCase()) {
      case 'tier1':
        return ACE_COLORS.PURPLE;
      case 'tier2':
        return ACE_COLORS.YELLOW;
      case 'tier3':
        return ACE_COLORS.DARK;
      default:
        return theme.palette.text.secondary;
    }
  }, [theme]);

  // Format redemption data for display
  const formatRedemptionData = useCallback((redemption) => {
    return {
      ...redemption,
      formattedDate: formatDate(redemption.redeemed_at),
      formattedDateTime: formatDateTime(redemption.redeemed_at),
      relativeTime: getRelativeTime(redemption.redeemed_at),
      statusColor: getStatusColor(redemption.status),
      statusIcon: getStatusIcon(redemption.status),
      tierColor: getTierColor(redemption.tier_type)
    };
  }, [getStatusColor, getStatusIcon, getTierColor]);

  // Calculate pagination info
  const paginationInfo = useMemo(() => {
    const totalPages = Math.ceil(filteredRedemptions.length / rowsPerPage);
    const startIndex = page * rowsPerPage;
    const endIndex = Math.min(startIndex + rowsPerPage, filteredRedemptions.length);

    return {
      totalPages,
      startIndex,
      endIndex,
      currentPage: page + 1,
      hasNextPage: page < totalPages - 1,
      hasPrevPage: page > 0
    };
  }, [filteredRedemptions.length, rowsPerPage, page]);

  // Enhanced loading skeleton with analytics
  const renderLoadingSkeleton = useCallback(() => (
    <Paper
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        p: 3
      }}
      className={className}
      {...props}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Skeleton variant="text" width="40%" height={40} />
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Skeleton variant="circular" width={40} height={40} />
          <Skeleton variant="circular" width={40} height={40} />
          <Skeleton variant="circular" width={40} height={40} />
        </Box>
      </Box>

      <Skeleton variant="rectangular" width="100%" height={56} sx={{ mb: 2 }} />

      {[1, 2, 3, 4, 5].map((item) => (
        <Skeleton key={item} variant="rectangular" height={72} sx={{ mb: 1, borderRadius: 1 }} />
      ))}

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
        <Skeleton variant="text" width="30%" height={32} />
        <Skeleton variant="rectangular" width="40%" height={32} />
      </Box>
    </Paper>
  ), [glassMorphismStyles, className, props]);

  // Enhanced error state with retry functionality
  const renderErrorState = useCallback(() => (
    <Paper
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        p: 3,
        textAlign: 'center'
      }}
      className={className}
      {...props}
    >
      <ErrorIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
      <Typography variant="h6" color="error.main" gutterBottom>
        Failed to Load Redemption Data
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {error || 'An unexpected error occurred while loading redemption tracking data.'}
      </Typography>
      <Button
        variant="contained"
        onClick={() => handleRefresh()}
        startIcon={<RefreshIcon />}
        sx={{
          bgcolor: ACE_COLORS.PURPLE,
          '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
        }}
      >
        Retry
      </Button>
    </Paper>
  ), [error, glassMorphismStyles, className, props, handleRefresh]);

  // Enhanced empty state with action suggestions
  const renderEmptyState = useCallback(() => (
    <Paper
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        p: 3,
        textAlign: 'center'
      }}
      className={className}
      {...props}
    >
      <CodeIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
      <Typography variant="h6" color="text.secondary" gutterBottom>
        No Redemptions Found
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {searchTerm || filters.status !== 'all' || filters.tier !== 'all' || filters.dateRange !== 'all'
          ? 'No redemptions match your current filters. Try adjusting your search criteria.'
          : 'Redemption data will appear here once users start redeeming AppSumo codes.'
        }
      </Typography>

      {(searchTerm || filters.status !== 'all' || filters.tier !== 'all' || filters.dateRange !== 'all') && (
        <Button
          variant="outlined"
          onClick={handleResetFilters}
          startIcon={<RefreshIcon />}
          sx={{ mr: 2 }}
        >
          Clear Filters
        </Button>
      )}

      <Button
        variant="contained"
        onClick={() => handleRefresh()}
        startIcon={<SyncIcon />}
        sx={{
          bgcolor: ACE_COLORS.PURPLE,
          '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
        }}
      >
        Refresh Data
      </Button>
    </Paper>
  ), [
    glassMorphismStyles,
    className,
    props,
    searchTerm,
    filters,
    handleResetFilters,
    handleRefresh
  ]);

  // Render loading skeleton
  if (loading && !data?.redemptions) {
    return renderLoadingSkeleton();
  }

  // Render error state
  if (error) {
    return renderErrorState();
  }

  // Render empty state
  if (!data?.redemptions || data.redemptions.length === 0) {
    return renderEmptyState();
  }

  // Get paginated redemptions
  const paginatedRedemptions = useMemo(() => {
    const startIndex = page * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    return filteredRedemptions.slice(startIndex, endIndex).map(formatRedemptionData);
  }, [filteredRedemptions, page, rowsPerPage, formatRedemptionData]);

  return (
    <Paper
      ref={tableRef}
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        position: 'relative',
        overflow: 'hidden'
      }}
      className={className}
      {...props}
    >
      {/* Enhanced Header with Analytics */}
      <Box sx={{ p: 3, pb: 0 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 48,
                height: 48,
                borderRadius: 1,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE
              }}
            >
              <CodeIcon />
            </Box>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK }}>
                Redemption Tracker
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Real-time AppSumo code redemption monitoring
              </Typography>
            </Box>
          </Stack>

          <Box display="flex" alignItems="center" gap={1}>
            {realTimeUpdates && (
              <Tooltip title="Real-time updates enabled">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Box
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: 'success.main',
                      animation: 'pulse 2s infinite'
                    }}
                  />
                  <Typography variant="caption" color="success.main">
                    Live
                  </Typography>
                </Box>
              </Tooltip>
            )}

            {enableBulkOperations && selectedRedemptions.size > 0 && (
              <Tooltip title="Bulk Actions">
                <IconButton
                  onClick={(e) => setBulkActionMenuAnchor(e.currentTarget)}
                  sx={{ color: ACE_COLORS.PURPLE }}
                >
                  <Badge badgeContent={selectedRedemptions.size} color="primary">
                    <MoreVertIcon />
                  </Badge>
                </IconButton>
              </Tooltip>
            )}

            <Tooltip title="Export Data">
              <IconButton
                onClick={handleExport}
                disabled={filteredRedemptions.length === 0}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Filters">
              <IconButton
                onClick={(e) => setFilterMenuAnchor(e.currentTarget)}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <Badge
                  badgeContent={
                    (filters.status !== 'all' ? 1 : 0) +
                    (filters.tier !== 'all' ? 1 : 0) +
                    (filters.dateRange !== 'all' ? 1 : 0) +
                    (searchTerm ? 1 : 0)
                  }
                  color="primary"
                >
                  <FilterIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            <Tooltip title="Refresh Data">
              <IconButton
                onClick={() => handleRefresh()}
                disabled={refreshing}
                sx={{ color: ACE_COLORS.PURPLE }}
              >
                <RefreshIcon sx={{ animation: refreshing ? 'spin 1s linear infinite' : 'none' }} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Analytics Summary */}
        {redemptionAnalytics && enableAdvancedFeatures && (
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={6} sm={3}>
              <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" sx={{ color: ACE_COLORS.PURPLE, fontWeight: 'bold' }}>
                  {redemptionAnalytics.total}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Total Redemptions
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                  {redemptionAnalytics.activeRedemptions}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Active
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" sx={{ color: ACE_COLORS.YELLOW, fontWeight: 'bold' }}>
                  {redemptionAnalytics.last24h}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Last 24h
                </Typography>
              </Card>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Card variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                <Typography variant="h4" sx={{ color: 'info.main', fontWeight: 'bold' }}>
                  {redemptionAnalytics.successRate.toFixed(1)}%
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Success Rate
                </Typography>
              </Card>
            </Grid>
          </Grid>
        )}
      </Box>

      {/* Enhanced Search and Filters */}
      <Box sx={{ px: 3, pb: 2 }}>
        <Box display="flex" gap={2} mb={3}>
          <TextField
            ref={searchRef}
            placeholder="Search by code, email, user, IP address..."
            onChange={handleSearchChange}
            value={searchTerm}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: ACE_COLORS.PURPLE }} />
                </InputAdornment>
              ),
            }}
            sx={{
              flexGrow: 1,
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: ACE_COLORS.PURPLE,
                },
                '&.Mui-focused fieldset': {
                  borderColor: ACE_COLORS.PURPLE,
                },
              },
            }}
          />

          {enableAdvancedFeatures && (
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>View</InputLabel>
              <Select
                value={currentView}
                onChange={(e) => setCurrentView(e.target.value)}
                label="View"
              >
                <MenuItem value={TRACKING_VIEWS.TABLE}>Table</MenuItem>
                <MenuItem value={TRACKING_VIEWS.ANALYTICS}>Analytics</MenuItem>
                <MenuItem value={TRACKING_VIEWS.TIMELINE}>Timeline</MenuItem>
              </Select>
            </FormControl>
          )}

          <FormControlLabel
            control={
              <Switch
                checked={realTimeUpdates}
                onChange={(e) => setRealTimeUpdates(e.target.checked)}
                color="primary"
              />
            }
            label="Live Updates"
            sx={{ ml: 1 }}
          />
        </Box>

        {/* Enhanced Results Summary */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="body2" color="text.secondary">
            Showing {paginationInfo.startIndex + 1}-{paginationInfo.endIndex} of {filteredRedemptions.length} redemptions
            {searchTerm && ` (filtered from ${data?.redemptions?.length || 0} total)`}
          </Typography>

          {enableBulkOperations && (
            <Box display="flex" alignItems="center" gap={1}>
              <Checkbox
                checked={selectedRedemptions.size === filteredRedemptions.length && filteredRedemptions.length > 0}
                indeterminate={selectedRedemptions.size > 0 && selectedRedemptions.size < filteredRedemptions.length}
                onChange={handleSelectAll}
                size="small"
              />
              <Typography variant="caption" color="text.secondary">
                {selectedRedemptions.size > 0 && `${selectedRedemptions.size} selected`}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      {/* Enhanced Redemptions Table */}
      <TableContainer sx={{ px: 3 }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {enableBulkOperations && (
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedRedemptions.size === filteredRedemptions.length && filteredRedemptions.length > 0}
                    indeterminate={selectedRedemptions.size > 0 && selectedRedemptions.size < filteredRedemptions.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
              )}
              <TableCell>
                <TableSortLabel
                  active={sortBy === SORT_OPTIONS.CODE}
                  direction={sortBy === SORT_OPTIONS.CODE ? sortOrder : 'desc'}
                  onClick={() => handleSort(SORT_OPTIONS.CODE)}
                >
                  Code
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortBy === SORT_OPTIONS.USER}
                  direction={sortBy === SORT_OPTIONS.USER ? sortOrder : 'desc'}
                  onClick={() => handleSort(SORT_OPTIONS.USER)}
                >
                  User
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortBy === SORT_OPTIONS.TIER}
                  direction={sortBy === SORT_OPTIONS.TIER ? sortOrder : 'desc'}
                  onClick={() => handleSort(SORT_OPTIONS.TIER)}
                >
                  Tier
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortBy === SORT_OPTIONS.STATUS}
                  direction={sortBy === SORT_OPTIONS.STATUS ? sortOrder : 'desc'}
                  onClick={() => handleSort(SORT_OPTIONS.STATUS)}
                >
                  Status
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortBy === SORT_OPTIONS.NEWEST}
                  direction={sortBy === SORT_OPTIONS.NEWEST ? sortOrder : 'desc'}
                  onClick={() => handleSort(SORT_OPTIONS.NEWEST)}
                >
                  Redeemed
                </TableSortLabel>
              </TableCell>
              {enableAdvancedFeatures && (
                <TableCell>Location</TableCell>
              )}
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedRedemptions.map((redemption) => (
              <TableRow
                key={redemption.id}
                hover
                selected={selectedRedemptions.has(redemption.id)}
                sx={{
                  '&.Mui-selected': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.08),
                  },
                  '&.Mui-selected:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.12),
                  },
                }}
              >
                {enableBulkOperations && (
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedRedemptions.has(redemption.id)}
                      onChange={() => handleRedemptionSelect(redemption.id)}
                    />
                  </TableCell>
                )}
                <TableCell>
                  <Typography
                    variant="body2"
                    fontFamily="monospace"
                    fontWeight="medium"
                    sx={{ color: ACE_COLORS.PURPLE }}
                  >
                    {redemption.code}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PersonIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {redemption.user_name || 'Unknown User'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {redemption.user_email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={redemption.tier_type?.toUpperCase() || 'Unknown'}
                    size="small"
                    variant="outlined"
                    sx={{
                      borderColor: redemption.tierColor,
                      color: redemption.tierColor,
                      fontWeight: 'medium'
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    icon={redemption.statusIcon}
                    label={redemption.status?.charAt(0).toUpperCase() + redemption.status?.slice(1) || 'Unknown'}
                    color={redemption.statusColor}
                    size="small"
                    variant="filled"
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {redemption.formattedDate}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {redemption.relativeTime}
                    </Typography>
                  </Box>
                </TableCell>
                {enableAdvancedFeatures && (
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <LocationIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
                      <Typography variant="caption" color="text.secondary">
                        {redemption.ip_address || 'Unknown'}
                      </Typography>
                    </Box>
                  </TableCell>
                )}
                <TableCell align="center">
                  <Box sx={{ display: 'flex', gap: 0.5 }}>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => handleShowDetails(redemption)}
                        sx={{ color: ACE_COLORS.PURPLE }}
                      >
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    {enableAdvancedFeatures && (
                      <Tooltip title="Edit Status">
                        <IconButton
                          size="small"
                          onClick={() => onStatusChange && onStatusChange(redemption)}
                          sx={{ color: 'text.secondary' }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Enhanced Pagination */}
      <Box sx={{ px: 3, py: 2, borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
        <TablePagination
          component="div"
          count={filteredRedemptions.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[10, 25, 50, 100]}
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} of ${count !== -1 ? count : `more than ${to}`}`
          }
          labelRowsPerPage="Redemptions per page:"
          sx={{
            '& .MuiTablePagination-toolbar': {
              paddingLeft: 0,
              paddingRight: 0,
            },
            '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
              color: 'text.secondary',
            },
          }}
        />
      </Box>

      {/* Enhanced Filter Menu */}
    <Menu
      anchorEl={filterMenuAnchor}
      open={Boolean(filterMenuAnchor)}
      onClose={() => setFilterMenuAnchor(null)}
      PaperProps={{
        sx: {
          ...glassMorphismStyles,
          minWidth: 280,
          maxHeight: 400
        }
      }}
    >
      <MenuItem disabled>
        <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: ACE_COLORS.PURPLE }}>
          Filter Options
        </Typography>
      </MenuItem>
      <Divider />

      {/* Filter Presets */}
      <MenuItem>
        <FormControl size="small" fullWidth>
          <InputLabel>Quick Filters</InputLabel>
          <Select
            value={filters.preset}
            onChange={(e) => handleFilterChange('preset', e.target.value)}
            label="Quick Filters"
          >
            <MenuItem value={FILTER_PRESETS.ALL}>All Redemptions</MenuItem>
            <MenuItem value={FILTER_PRESETS.TODAY}>Today</MenuItem>
            <MenuItem value={FILTER_PRESETS.WEEK}>This Week</MenuItem>
            <MenuItem value={FILTER_PRESETS.MONTH}>This Month</MenuItem>
            <MenuItem value={FILTER_PRESETS.ACTIVE_ONLY}>Active Only</MenuItem>
            <MenuItem value={FILTER_PRESETS.ISSUES}>Issues</MenuItem>
          </Select>
        </FormControl>
      </MenuItem>

      <MenuItem>
        <FormControl size="small" fullWidth>
          <InputLabel>Status</InputLabel>
          <Select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            label="Status"
          >
            <MenuItem value="all">All Statuses</MenuItem>
            {availableStatuses.map((status) => (
              <MenuItem key={status} value={status}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {getStatusIcon(status)}
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </MenuItem>

      <MenuItem>
        <FormControl size="small" fullWidth>
          <InputLabel>Tier</InputLabel>
          <Select
            value={filters.tier}
            onChange={(e) => handleFilterChange('tier', e.target.value)}
            label="Tier"
          >
            <MenuItem value="all">All Tiers</MenuItem>
            {availableTiers.map((tier) => (
              <MenuItem key={tier} value={tier}>
                {tier.toUpperCase()}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </MenuItem>

      <MenuItem>
        <FormControl size="small" fullWidth>
          <InputLabel>Date Range</InputLabel>
          <Select
            value={filters.dateRange}
            onChange={(e) => handleFilterChange('dateRange', e.target.value)}
            label="Date Range"
          >
            <MenuItem value="all">All Time</MenuItem>
            <MenuItem value="1h">Last Hour</MenuItem>
            <MenuItem value="24h">Last 24 Hours</MenuItem>
            <MenuItem value="7d">Last 7 Days</MenuItem>
            <MenuItem value="30d">Last 30 Days</MenuItem>
            <MenuItem value="90d">Last 90 Days</MenuItem>
          </Select>
        </FormControl>
      </MenuItem>

      <Divider />
      <MenuItem onClick={handleResetFilters}>
        <RefreshIcon sx={{ mr: 1, fontSize: 16 }} />
        Reset All Filters
      </MenuItem>
    </Menu>

    {/* Bulk Actions Menu */}
    {enableBulkOperations && (
      <Menu
        anchorEl={bulkActionMenuAnchor}
        open={Boolean(bulkActionMenuAnchor)}
        onClose={() => setBulkActionMenuAnchor(null)}
        PaperProps={{
          sx: glassMorphismStyles
        }}
      >
        <MenuItem disabled>
          <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: ACE_COLORS.PURPLE }}>
            Bulk Actions ({selectedRedemptions.size} selected)
          </Typography>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => handleBulkAction('export')}>
          <DownloadIcon sx={{ mr: 1, fontSize: 16 }} />
          Export Selected
        </MenuItem>
        <MenuItem onClick={() => handleBulkAction('activate')}>
          <CheckCircleIcon sx={{ mr: 1, fontSize: 16 }} />
          Mark as Active
        </MenuItem>
        <MenuItem onClick={() => handleBulkAction('suspend')}>
          <BlockIcon sx={{ mr: 1, fontSize: 16 }} />
          Suspend
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => handleBulkAction('delete')} sx={{ color: 'error.main' }}>
          <DeleteIcon sx={{ mr: 1, fontSize: 16 }} />
          Delete Selected
        </MenuItem>
      </Menu>
    )}

    {/* Redemption Details Dialog */}
    <Dialog
      open={showDetails}
      onClose={() => setShowDetails(false)}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: glassMorphismStyles
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            Redemption Details
          </Typography>
          <IconButton onClick={() => setShowDetails(false)} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        {selectedRedemption && (
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>Code Information</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">Code:</Typography>
                <Typography variant="body1" fontFamily="monospace" fontWeight="bold">
                  {selectedRedemption.code}
                </Typography>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">Status:</Typography>
                <Chip
                  icon={getStatusIcon(selectedRedemption.status)}
                  label={selectedRedemption.status?.charAt(0).toUpperCase() + selectedRedemption.status?.slice(1)}
                  color={getStatusColor(selectedRedemption.status)}
                  size="small"
                />
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">Tier:</Typography>
                <Typography variant="body1">{selectedRedemption.tier_type?.toUpperCase()}</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>User Information</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">Name:</Typography>
                <Typography variant="body1">{selectedRedemption.user_name || 'Unknown'}</Typography>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">Email:</Typography>
                <Typography variant="body1">{selectedRedemption.user_email}</Typography>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">IP Address:</Typography>
                <Typography variant="body1" fontFamily="monospace">
                  {selectedRedemption.ip_address || 'Unknown'}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>Redemption Timeline</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">Redeemed At:</Typography>
                <Typography variant="body1">
                  {formatDateTime(selectedRedemption.redeemed_at)} ({getRelativeTime(selectedRedemption.redeemed_at)})
                </Typography>
              </Box>
            </Grid>
          </Grid>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setShowDetails(false)}>Close</Button>
        {enableAdvancedFeatures && selectedRedemption && (
          <Button
            variant="contained"
            onClick={() => onStatusChange && onStatusChange(selectedRedemption)}
            sx={{
              bgcolor: ACE_COLORS.PURPLE,
              '&:hover': { bgcolor: alpha(ACE_COLORS.PURPLE, 0.8) }
            }}
          >
            Edit Status
          </Button>
        )}
      </DialogActions>
    </Dialog>
    </Paper>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedRedemptionTracker.propTypes = {
  // Core props
  data: PropTypes.shape({
    redemptions: PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      code: PropTypes.string.isRequired,
      user_email: PropTypes.string,
      user_name: PropTypes.string,
      tier_type: PropTypes.string,
      status: PropTypes.oneOf(Object.values(REDEMPTION_STATUS)),
      redeemed_at: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
      ip_address: PropTypes.string,
      user_agent: PropTypes.string,
      location: PropTypes.string,
      device_info: PropTypes.object
    }))
  }),
  loading: PropTypes.bool,
  error: PropTypes.string,
  onRefresh: PropTypes.func,
  className: PropTypes.string,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeTracking: PropTypes.bool,
  enableBulkOperations: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  maxDisplayRedemptions: PropTypes.number,
  autoRefreshInterval: PropTypes.number,

  // Callback props
  onRedemptionSelect: PropTypes.func,
  onRedemptionUpdate: PropTypes.func,
  onBulkAction: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onExportData: PropTypes.func,
  onStatusChange: PropTypes.func
};

// Default props
EnhancedRedemptionTracker.defaultProps = {
  data: { redemptions: [] },
  loading: false,
  error: null,
  onRefresh: null,
  className: '',
  enableAdvancedFeatures: true,
  enableRealTimeTracking: true,
  enableBulkOperations: true,
  enableAnalytics: true,
  enableAccessibility: true,
  maxDisplayRedemptions: 1000,
  autoRefreshInterval: 30000,
  onRedemptionSelect: null,
  onRedemptionUpdate: null,
  onBulkAction: null,
  onAnalyticsTrack: null,
  onExportData: null,
  onStatusChange: null
};

// Display name for debugging
EnhancedRedemptionTracker.displayName = 'EnhancedRedemptionTracker';

export default EnhancedRedemptionTracker;

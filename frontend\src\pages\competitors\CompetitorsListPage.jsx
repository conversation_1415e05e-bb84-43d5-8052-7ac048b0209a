// @since 2024-1-1 to 2025-25-7
import { Suspense, memo, useCallback, useEffect, useState } from "react";
import { Helmet } from "react-helmet-async";
import {
  Box,
  Container,
  Typography,
  CircularProgress,
  Alert,
  Button,
  <PERSON>readc<PERSON>bs,
  Link,
  Skeleton,
  Grid
} from "@mui/material";
import {
  Home as HomeIcon,
  Business as BusinessIcon,
  Refresh as RefreshIcon,
  Add as AddIcon
} from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import CompetitorList from "../../components/competitors/CompetitorList";
import { CompetitorProvider } from "../../contexts/CompetitorContext";
import { useNotification } from "../../hooks/useNotification";
import ErrorBoundary from "../../components/common/ErrorBoundary";

// Enhanced loading component with list skeleton
const LoadingFallback = memo(() => (
  <Box sx={{ my: 4 }}>
    {/* <PERSON><PERSON> skeleton */}
    <Box sx={{ mb: 3 }}>
      <Skeleton variant="text" width="30%" height={40} />
      <Skeleton variant="text" width="50%" height={24} />
    </Box>

    {/* Toolbar skeleton */}
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
      <Skeleton variant="rectangular" width={200} height={40} />
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Skeleton variant="rectangular" width={120} height={40} />
        <Skeleton variant="rectangular" width={100} height={40} />
      </Box>
    </Box>

    {/* List items skeleton */}
    <Grid container spacing={3}>
      {[...Array(6)].map((_, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Skeleton variant="circular" width={40} height={40} sx={{ mr: 2 }} />
              <Box sx={{ flex: 1 }}>
                <Skeleton variant="text" width="70%" height={24} />
                <Skeleton variant="text" width="50%" height={20} />
              </Box>
            </Box>
            <Skeleton variant="rectangular" height={80} sx={{ mb: 2 }} />
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Skeleton variant="rectangular" width={60} height={32} />
              <Skeleton variant="rectangular" width={60} height={32} />
              <Skeleton variant="rectangular" width={60} height={32} />
            </Box>
          </Box>
        </Grid>
      ))}
    </Grid>

    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        mt: 4,
        gap: 2
      }}
      role="status"
      aria-live="polite"
      aria-label="Loading competitors list"
    >
      <CircularProgress size={24} aria-label="Loading data" />
      <Typography variant="body2" color="text.secondary">
        Loading competitors...
      </Typography>
    </Box>
  </Box>
));

LoadingFallback.displayName = 'LoadingFallback';

// Error fallback component
const ErrorFallback = memo(({ error, resetError }) => (
  <Alert
    severity="error"
    sx={{ my: 3 }}
    action={
      <Button
        color="inherit"
        size="small"
        onClick={resetError}
        startIcon={<RefreshIcon />}
        aria-label="Retry loading competitors list"
      >
        Retry
      </Button>
    }
  >
    <Typography variant="body2">
      Failed to load competitors list: {error?.message || 'Unknown error'}
    </Typography>
  </Alert>
));

ErrorFallback.displayName = 'ErrorFallback';

const CompetitorsListPage = ({ isEmbedded = false }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { showErrorNotification } = useNotification();
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  // Error boundary handler
  const handleError = useCallback((error, errorInfo) => {
    console.error('CompetitorsListPage Error:', error, errorInfo);
    setError(error);
    showErrorNotification('Failed to load competitors list');
  }, [showErrorNotification]);

  // Reset error state
  const resetError = useCallback(() => {
    setError(null);
    setRetryCount(prev => prev + 1);
  }, []);

  // Navigation handlers
  const handleNavigateHome = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  const handleAddCompetitor = useCallback(() => {
    navigate('/settings?tab=competitors&action=new');
  }, [navigate]);

  // SEO and meta tags
  const pageTitle = isEmbedded
    ? "Competitors"
    : "Competitors List | ACE Social";

  const pageDescription = "Track and analyze your competitors' social media presence to gain strategic insights and improve your content strategy.";

  // Analytics tracking
  useEffect(() => {
    if (!isEmbedded) {
      // Track page view
      if (typeof gtag !== 'undefined') {
        gtag('config', 'GA_MEASUREMENT_ID', {
          page_title: pageTitle,
          page_location: window.location.href
        });
      }
    }
  }, [isEmbedded, pageTitle]);

  return (
    <>
      {!isEmbedded && (
        <Helmet>
          <title>{pageTitle}</title>
          <meta name="description" content={pageDescription} />
          <meta name="keywords" content="competitors, competitor analysis, social media tracking, competitive intelligence, competitor monitoring" />
          <meta property="og:title" content={pageTitle} />
          <meta property="og:description" content={pageDescription} />
          <meta property="og:type" content="website" />
          <link rel="canonical" href={`${window.location.origin}${location.pathname}`} />
        </Helmet>
      )}

      <Container
        maxWidth={isEmbedded ? false : "xl"}
        disableGutters={isEmbedded}
        component="main"
        role="main"
        aria-label="Competitors list dashboard"
      >
        {/* Breadcrumb Navigation */}
        {!isEmbedded && (
          <Box sx={{ mb: 3 }}>
            <Breadcrumbs
              aria-label="breadcrumb navigation"
              sx={{ mb: 2 }}
            >
              <Link
                component="button"
                variant="body2"
                onClick={handleNavigateHome}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  textDecoration: 'none',
                  '&:hover': { textDecoration: 'underline' }
                }}
                aria-label="Navigate to dashboard"
              >
                <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
                Dashboard
              </Link>
              <Typography
                color="text.primary"
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                <BusinessIcon sx={{ mr: 0.5 }} fontSize="inherit" />
                Competitors
              </Typography>
            </Breadcrumbs>
          </Box>
        )}

        {/* Page Header */}
        {!isEmbedded && (
          <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                Competitors
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Track and analyze your competitors&apos; social media presence to gain
                strategic insights.
              </Typography>
            </Box>

            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddCompetitor}
              aria-label="Add new competitor"
            >
              Add Competitor
            </Button>
          </Box>
        )}

        {/* Error Display */}
        {error && (
          <ErrorFallback error={error} resetError={resetError} />
        )}

        {/* Main Content */}
        <ErrorBoundary
          onError={handleError}
          fallback={<ErrorFallback error={error} resetError={resetError} />}
        >
          <CompetitorProvider>
            <Suspense fallback={<LoadingFallback />}>
              <CompetitorList
                isEmbedded={isEmbedded}
                key={retryCount} // Force re-render on retry
              />
            </Suspense>
          </CompetitorProvider>
        </ErrorBoundary>
      </Container>
    </>
  );
};

// Memoize the component for performance
const MemoizedCompetitorsListPage = memo(CompetitorsListPage);
MemoizedCompetitorsListPage.displayName = 'CompetitorsListPage';

export default MemoizedCompetitorsListPage;

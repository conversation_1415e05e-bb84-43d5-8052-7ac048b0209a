/**
 * Enhanced product drag & drop functionality with comprehensive features
 * Production-ready drag & drop system with monitoring and analytics
 */

import * as React from 'react';
import { useState, useCallback, useRef, useMemo } from 'react';
import { Product } from '../types/ecommerce';

// Extend Window interface for analytics
declare global {
  interface Window {
    analytics?: {
      // eslint-disable-next-line no-unused-vars
      track: (event: string, properties?: Record<string, unknown>) => void;
    };
    // eslint-disable-next-line no-unused-vars
    gtag?: (...args: unknown[]) => void;
  }
}

// Enhanced logging utility for production-ready drag & drop monitoring
const logger = {
  debug: (message: string, data?: Record<string, unknown>) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[useProductDragDrop] ${message}`, data);
    }
  },
  info: (message: string, data?: Record<string, unknown>) => {
    if (process.env.NODE_ENV === 'development') {
      console.info(`[useProductDragDrop] ${message}`, data);
    }

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production' && window.analytics) {
      window.analytics.track('Product Drag Drop Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message: string, data?: Record<string, unknown>) => {
    console.warn(`[useProductDragDrop] ${message}`, data);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Product Drag Drop Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message: string, error?: Error | string | Record<string, unknown>) => {
    console.error(`[useProductDragDrop] ${message}`, error);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Product Drag Drop Error', {
        message,
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Configuration constants
const CONFIG = {
  DEFAULT_MAX_UNDO_STEPS: 10,
  MAX_UNDO_STEPS: 50,
  SWIPE_THRESHOLD: 50,
  QUICK_SWIPE_TIME: 300,
  DRAG_THRESHOLD: 10,
  ANALYTICS_ENABLED: true,
  PERFORMANCE_MONITORING: true,
};

// Note: Performance thresholds can be added here if needed for future enhancements

// Enhanced interfaces for comprehensive drag & drop functionality
interface UndoRedoState<T> {
  undoStack: T[];
  redoStack: T[];
  canUndo: boolean;
  canRedo: boolean;
  pushToUndoStack: (state: T) => void; // eslint-disable-line no-unused-vars
  undo: () => T | null;
  redo: () => T | null;
  clearHistory: () => void;
  // Enhanced features
  getHistoryStats: () => HistoryStats;
  getConfiguration: () => UndoRedoConfig;
  healthCheck: () => HealthCheckResult;
}

interface HistoryStats {
  undoCount: number;
  redoCount: number;
  totalOperations: number;
  memoryUsage: number;
  uptime: number;
}

interface UndoRedoConfig {
  maxUndoSteps: number;
  currentStackSize: number;
  analytics: boolean;
  version: string;
}

interface HealthCheckResult {
  isHealthy: boolean;
  status: string;
  issues: string[];
  timestamp: string;
}

export const useProductDragDrop = (
  initialProducts: Product[],
  maxUndoSteps: number = CONFIG.DEFAULT_MAX_UNDO_STEPS
): UndoRedoState<Product[]> => {
  const [undoStack, setUndoStack] = useState<Product[][]>([]);
  const [redoStack, setRedoStack] = useState<Product[][]>([]);

  // Refs for tracking and performance
  const startTimeRef = useRef(Date.now());
  const operationCountRef = useRef(0);

  // Memoized configuration validation
  const validatedMaxUndoSteps = useMemo(() => {
    const steps = Math.max(1, Math.min(maxUndoSteps, CONFIG.MAX_UNDO_STEPS));
    if (steps !== maxUndoSteps) {
      logger.warn('Max undo steps adjusted to valid range', {
        provided: maxUndoSteps,
        adjusted: steps,
        range: [1, CONFIG.MAX_UNDO_STEPS]
      });
    }
    return steps;
  }, [maxUndoSteps]);

  const canUndo = undoStack.length > 0;
  const canRedo = redoStack.length > 0;

  logger.debug('Product drag drop hook initialized', {
    initialProductsCount: initialProducts.length,
    maxUndoSteps: validatedMaxUndoSteps,
    timestamp: new Date().toISOString()
  });

  const pushToUndoStack = useCallback((state: Product[]) => {
    operationCountRef.current++;

    logger.debug('Pushing state to undo stack', {
      stateLength: state.length,
      operationCount: operationCountRef.current
    });

    setUndoStack(prev => {
      const newStack = [...prev, [...state]];
      // Limit stack size
      if (newStack.length > validatedMaxUndoSteps) {
        newStack.shift();
        logger.debug('Undo stack size limit reached, removing oldest entry');
      }
      return newStack;
    });

    // Clear redo stack when new action is performed
    setRedoStack([]);

    // Analytics tracking
    if (CONFIG.ANALYTICS_ENABLED && window.analytics) {
      window.analytics.track('Undo Stack Push', {
        stateLength: state.length,
        operationCount: operationCountRef.current,
        timestamp: new Date().toISOString()
      });
    }
  }, [validatedMaxUndoSteps]);

  const undo = useCallback((): Product[] | null => {
    if (undoStack.length === 0) {
      logger.debug('Undo attempted but stack is empty');
      return null;
    }

    const lastState = undoStack[undoStack.length - 1];
    if (!lastState) {
      logger.warn('Last state is undefined');
      return null;
    }

    operationCountRef.current++;

    logger.debug('Performing undo operation', {
      stackSize: undoStack.length,
      operationCount: operationCountRef.current
    });

    setUndoStack(prev => prev.slice(0, -1));
    setRedoStack(prev => [...prev, [...initialProducts]]);

    // Analytics tracking
    if (CONFIG.ANALYTICS_ENABLED && window.analytics) {
      window.analytics.track('Undo Operation', {
        stackSize: undoStack.length - 1,
        operationCount: operationCountRef.current,
        timestamp: new Date().toISOString()
      });
    }

    return lastState;
  }, [undoStack, initialProducts]);

  const redo = useCallback((): Product[] | null => {
    if (redoStack.length === 0) {
      logger.debug('Redo attempted but stack is empty');
      return null;
    }

    const nextState = redoStack[redoStack.length - 1];
    if (!nextState) {
      logger.warn('Next state is undefined');
      return null;
    }

    operationCountRef.current++;

    logger.debug('Performing redo operation', {
      stackSize: redoStack.length,
      operationCount: operationCountRef.current
    });

    setRedoStack(prev => prev.slice(0, -1));
    setUndoStack(prev => [...prev, [...initialProducts]]);

    // Analytics tracking
    if (CONFIG.ANALYTICS_ENABLED && window.analytics) {
      window.analytics.track('Redo Operation', {
        stackSize: redoStack.length - 1,
        operationCount: operationCountRef.current,
        timestamp: new Date().toISOString()
      });
    }

    return nextState;
  }, [redoStack, initialProducts]);

  const clearHistory = useCallback(() => {
    logger.debug('Clearing undo/redo history');
    setUndoStack([]);
    setRedoStack([]);
    operationCountRef.current = 0;
  }, []);

  // Enhanced utility functions for production monitoring
  const getHistoryStats = useCallback((): HistoryStats => {
    const uptime = Date.now() - startTimeRef.current;
    const memoryUsage = undoStack.length + redoStack.length;

    return {
      undoCount: undoStack.length,
      redoCount: redoStack.length,
      totalOperations: operationCountRef.current,
      memoryUsage,
      uptime
    };
  }, [undoStack.length, redoStack.length]);

  const getConfiguration = useCallback((): UndoRedoConfig => {
    return {
      maxUndoSteps: validatedMaxUndoSteps,
      currentStackSize: undoStack.length + redoStack.length,
      analytics: CONFIG.ANALYTICS_ENABLED,
      version: '1.0.0'
    };
  }, [validatedMaxUndoSteps, undoStack.length, redoStack.length]);

  const healthCheck = useCallback((): HealthCheckResult => {
    const stats = getHistoryStats();
    const config = getConfiguration();
    const isHealthy = stats.memoryUsage < config.maxUndoSteps * 2 && stats.uptime > 0;

    const issues: string[] = [
      ...(stats.memoryUsage >= config.maxUndoSteps * 2 ? ['Memory usage high'] : []),
      ...(stats.totalOperations === 0 ? ['No operations performed yet'] : [])
    ];

    logger.debug('Drag drop hook health check', {
      isHealthy,
      memoryUsage: stats.memoryUsage,
      totalOperations: stats.totalOperations
    });

    return {
      isHealthy,
      status: isHealthy ? 'healthy' : 'degraded',
      issues,
      timestamp: new Date().toISOString()
    };
  }, [getHistoryStats, getConfiguration]);

  return {
    undoStack,
    redoStack,
    canUndo,
    canRedo,
    pushToUndoStack,
    undo,
    redo,
    clearHistory,
    getHistoryStats,
    getConfiguration,
    healthCheck
  };
};

/**
 * Enhanced touch gesture support for mobile devices with comprehensive tracking
 */
export const useTouchGestures = () => {
  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null);
  const touchMoveRef = useRef<{ x: number; y: number } | null>(null);
  const gestureCountRef = useRef(0);

  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (!event.touches || event.touches.length === 0) {
      logger.warn('Touch start event has no touches');
      return;
    }

    const touch = event.touches[0];
    if (!touch) {
      logger.warn('First touch is undefined');
      return;
    }

    gestureCountRef.current++;
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    };

    logger.debug('Touch gesture started', {
      x: touch.clientX,
      y: touch.clientY,
      gestureCount: gestureCountRef.current
    });
  }, []);

  const handleTouchMove = useCallback((event: React.TouchEvent) => {
    if (!touchStartRef.current) return;

    if (!event.touches || event.touches.length === 0) {
      logger.warn('Touch move event has no touches');
      return;
    }

    const touch = event.touches[0];
    if (!touch) {
      logger.warn('First touch is undefined in move event');
      return;
    }

    touchMoveRef.current = {
      x: touch.clientX,
      y: touch.clientY
    };

    // Prevent scrolling during drag
    const deltaX = Math.abs(touch.clientX - touchStartRef.current.x);
    const deltaY = Math.abs(touch.clientY - touchStartRef.current.y);

    if (deltaX > CONFIG.DRAG_THRESHOLD || deltaY > CONFIG.DRAG_THRESHOLD) {
      event.preventDefault();

      logger.debug('Touch drag detected', {
        deltaX,
        deltaY,
        threshold: CONFIG.DRAG_THRESHOLD
      });
    }
  }, []);

  const handleTouchEnd = useCallback((event: React.TouchEvent) => {
    if (!touchStartRef.current || !touchMoveRef.current) {
      touchStartRef.current = null;
      touchMoveRef.current = null;
      return;
    }

    const deltaX = touchMoveRef.current.x - touchStartRef.current.x;
    const deltaY = touchMoveRef.current.y - touchStartRef.current.y;
    const deltaTime = Date.now() - touchStartRef.current.time;

    // Detect swipe gestures
    const isSwipe = Math.abs(deltaX) > CONFIG.SWIPE_THRESHOLD || Math.abs(deltaY) > CONFIG.SWIPE_THRESHOLD;
    const isQuickSwipe = deltaTime < CONFIG.QUICK_SWIPE_TIME;

    if (isSwipe && isQuickSwipe) {
      // Handle swipe gesture
      const direction = Math.abs(deltaX) > Math.abs(deltaY)
        ? (deltaX > 0 ? 'right' : 'left')
        : (deltaY > 0 ? 'down' : 'up');

      logger.debug('Swipe gesture detected', {
        direction,
        deltaX,
        deltaY,
        deltaTime,
        gestureCount: gestureCountRef.current
      });

      // Analytics tracking
      if (CONFIG.ANALYTICS_ENABLED && window.analytics) {
        window.analytics.track('Product Swipe Gesture', {
          direction,
          deltaX,
          deltaY,
          deltaTime,
          timestamp: new Date().toISOString()
        });
      }

      // Emit custom events
      const swipeEvent = new CustomEvent('productSwipe', {
        detail: { direction, deltaX, deltaY, deltaTime }
      });
      event.currentTarget.dispatchEvent(swipeEvent);
    }

    touchStartRef.current = null;
    touchMoveRef.current = null;
  }, []);

  return {
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd
  };
};

/**
 * Custom hook for optimistic UI updates during drag operations
 */
export const useOptimisticUpdates = <T>(
  initialData: T[],
  updateFunction: (newData: T[]) => Promise<void> // eslint-disable-line no-unused-vars
) => {
  const [optimisticData, setOptimisticData] = useState<T[]>(initialData);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateOptimistically = useCallback(async (newData: T[]) => {
    // Store previous data for potential revert
    const previousData = optimisticData;

    logger.debug('Performing optimistic update', {
      dataLength: newData.length,
      previousLength: previousData.length
    });

    // Immediately update UI
    setOptimisticData(newData);
    setIsUpdating(true);
    setError(null);

    try {
      // Perform actual update
      await updateFunction(newData);

      logger.info('Optimistic update successful', {
        dataLength: newData.length
      });
    } catch (err) {
      // Revert on error
      setOptimisticData(previousData);
      setError(err instanceof Error ? err.message : 'Update failed');

      logger.error('Optimistic update failed, reverting', err as Error);
    } finally {
      setIsUpdating(false);
    }
  }, [optimisticData, updateFunction]);

  const revert = useCallback(() => {
    setOptimisticData(initialData);
    setError(null);
  }, [initialData]);

  return {
    data: optimisticData,
    isUpdating,
    error,
    updateOptimistically,
    revert
  };
};

/**
 * Custom hook for drag and drop performance optimization
 */
export const useDragDropPerformance = () => {
  const [isDragging, setIsDragging] = useState(false);
  const animationFrameRef = useRef<number>();

  const startDragging = useCallback(() => {
    setIsDragging(true);
    
    // Disable smooth scrolling during drag
    document.documentElement.style.scrollBehavior = 'auto';
    
    // Reduce animation complexity
    document.body.classList.add('dragging');
  }, []);

  const stopDragging = useCallback(() => {
    setIsDragging(false);
    
    // Re-enable smooth scrolling
    document.documentElement.style.scrollBehavior = '';
    
    // Restore animations
    document.body.classList.remove('dragging');
    
    // Cancel any pending animation frames
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, []);

  const throttledUpdate = useCallback((callback: () => void) => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    animationFrameRef.current = requestAnimationFrame(callback);
  }, []);

  return {
    isDragging,
    startDragging,
    stopDragging,
    throttledUpdate
  };
};

/**
 * Custom hook for keyboard navigation in drag and drop lists
 */
export const useKeyboardDragDrop = <T extends { id: string }>(
  items: T[],
  onReorder: (newItems: T[]) => void // eslint-disable-line no-unused-vars
) => {
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);

  logger.debug('Keyboard drag drop initialized', {
    itemsCount: items.length
  });

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setFocusedIndex(prev => Math.min(prev + 1, items.length - 1));
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        setFocusedIndex(prev => Math.max(prev - 1, 0));
        break;
        
      case ' ':
      case 'Enter':
        event.preventDefault();
        if (selectedIndex === -1) {
          // Start drag
          setSelectedIndex(focusedIndex);
        } else {
          // Drop
          if (selectedIndex !== focusedIndex && selectedIndex >= 0 && selectedIndex < items.length) {
            const newItems = [...items];
            const [movedItem] = newItems.splice(selectedIndex, 1);
            if (movedItem) { // Type guard to ensure movedItem is not undefined
              newItems.splice(focusedIndex, 0, movedItem);
              onReorder(newItems);

              logger.debug('Keyboard reorder completed', {
                from: selectedIndex,
                to: focusedIndex,
                itemsCount: newItems.length
              });
            }
          }
          setSelectedIndex(-1);
        }
        break;
        
      case 'Escape':
        event.preventDefault();
        setSelectedIndex(-1);
        break;
    }
  }, [items, focusedIndex, selectedIndex, onReorder]);

  const isItemSelected = useCallback((index: number) => {
    return selectedIndex === index;
  }, [selectedIndex]);

  const isItemFocused = useCallback((index: number) => {
    return focusedIndex === index;
  }, [focusedIndex]);

  return {
    focusedIndex,
    selectedIndex,
    handleKeyDown,
    isItemSelected,
    isItemFocused,
    setFocusedIndex
  };
};

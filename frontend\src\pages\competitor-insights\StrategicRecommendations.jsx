// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  CircularProgress,
  Chip,
  CardContent,
  CardActions,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
  alpha
  
} from '@mui/material';
import {
  Lightbulb as LightbulbIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,

  Edit as EditIcon,
  Campaign as CampaignIcon,
  BookmarkBorder as BookmarkIcon,
  BookmarkAdded as BookmarkAddedIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  Instagram as InstagramIcon,
  LinkedIn as LinkedInIcon,
  YouTube as YouTubeIcon,
  Slideshow as TikTokIcon
} from '@mui/icons-material';
import { useCompetitorInsights } from '../../contexts/CompetitorInsightsContext';
import { useCompetitors } from '../../contexts/CompetitorContext';
import { useNotification } from '../../hooks/useNotification';
import {
  getCompetitorInsights,
  getIndustryBenchmarks
} from '../../api/competitor-analytics';
import GlassmorphicCard from '../../components/common/GlassmorphicCard';
import { useNavigate } from 'react-router-dom';

const StrategicRecommendations = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { competitors } = useCompetitors();
  const {
    selectedCompetitors,
    insightFilters,
  } = useCompetitorInsights();

  // Local state for real strategic recommendations
  const [strategicRecommendations, setStrategicRecommendations] = useState(null);
  const [loading, setLoading] = useState(false);
  const [savedInsights, setSavedInsights] = useState([]);
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [platformFilter, setPlatformFilter] = useState('all');



  // Load strategic recommendations when component mounts or when selection changes
  useEffect(() => {
    if (selectedCompetitors.length > 0) {
      loadStrategicRecommendationsData();
    }
  }, [selectedCompetitors, insightFilters, loadStrategicRecommendationsData]);

  // Load real strategic recommendations from AI-powered insights
  const loadStrategicRecommendationsData = useCallback(async () => {
    if (selectedCompetitors.length === 0) return;

    setLoading(true);

    try {
      const recommendations = [];

      // Get insights for each competitor
      for (const competitorId of selectedCompetitors) {
        const competitor = competitors.find(c => (c._id || c.id) === competitorId);
        if (!competitor) continue;

        try {
          // Get AI-powered insights for this competitor
          const insights = await getCompetitorInsights(competitorId, insightFilters.timeframe || '30d');

          // Process insights into strategic recommendations
          const competitorRecommendations = processInsightsToRecommendations(
            competitor.name,
            insights,
            competitorId
          );

          recommendations.push(...competitorRecommendations);
        } catch (error) {
          console.error(`Error getting insights for ${competitor.name}:`, error);
        }
      }

      // Get industry benchmarks for additional recommendations
      const benchmarkRecommendations = await generateBenchmarkRecommendations();
      recommendations.push(...benchmarkRecommendations);

      // Sort recommendations by priority and impact
      const sortedRecommendations = recommendations.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      setStrategicRecommendations({
        recommendations: sortedRecommendations,
        summary: generateRecommendationsSummary(sortedRecommendations),
        actionPlan: generateActionPlan(sortedRecommendations)
      });

    } catch (error) {
      console.error('Error loading strategic recommendations:', error);
      showErrorNotification('Failed to load strategic recommendations');
    } finally {
      setLoading(false);
    }
  }, [selectedCompetitors, insightFilters, competitors, showErrorNotification, processInsightsToRecommendations, generateBenchmarkRecommendations, generateRecommendationsSummary, generateActionPlan]);

  // Process competitor insights into actionable recommendations
  const processInsightsToRecommendations = useCallback((competitorName, insights, competitorId) => {
    const recommendations = [];

    // Content strategy recommendations
    if (insights.insights && insights.insights.length > 0) {
      insights.insights.forEach((insight, index) => {
        recommendations.push({
          id: `${competitorId}-insight-${index}`,
          title: `Learn from ${competitorName}: ${insight}`,
          description: `Based on ${competitorName}'s performance, consider implementing this strategy.`,
          category: 'content_strategy',
          priority: 'medium',
          platform: 'all',
          impact: 'medium',
          effort: 'low',
          competitor: competitorName,
          actionItems: [
            `Analyze ${competitorName}'s approach to this strategy`,
            'Adapt the strategy to your brand voice',
            'Test implementation with A/B testing',
            'Monitor performance metrics'
          ],
          expectedOutcome: 'Improved content engagement and reach',
          timeframe: '2-4 weeks'
        });
      });
    }

    // Opportunity recommendations
    if (insights.opportunities && insights.opportunities.length > 0) {
      insights.opportunities.forEach((opportunity, index) => {
        recommendations.push({
          id: `${competitorId}-opportunity-${index}`,
          title: `Opportunity: ${opportunity}`,
          description: `Market opportunity identified from competitor analysis.`,
          category: 'growth_opportunity',
          priority: 'high',
          platform: 'all',
          impact: 'high',
          effort: 'medium',
          competitor: competitorName,
          actionItems: [
            'Research market demand for this opportunity',
            'Develop content strategy around this topic',
            'Create targeted campaigns',
            'Monitor competitor response'
          ],
          expectedOutcome: 'Capture market share in underserved area',
          timeframe: '1-2 months'
        });
      });
    }

    return recommendations;
  }, []);

  // Generate recommendations based on industry benchmarks
  const generateBenchmarkRecommendations = useCallback(async () => {
    const recommendations = [];

    try {
      // Get benchmarks for major platforms
      const platforms = ['linkedin', 'twitter', 'facebook', 'instagram'];

      for (const platform of platforms) {
        const benchmark = await getIndustryBenchmarks(platform);

        recommendations.push({
          id: `benchmark-${platform}`,
          title: `Optimize ${platform.charAt(0).toUpperCase() + platform.slice(1)} Performance`,
          description: `Industry benchmark analysis suggests optimization opportunities on ${platform}.`,
          category: 'platform_optimization',
          priority: 'medium',
          platform: platform,
          impact: 'medium',
          effort: 'low',
          competitor: 'Industry Average',
          actionItems: [
            `Review current ${platform} content strategy`,
            'Align posting frequency with industry standards',
            'Optimize content types for platform',
            'Improve engagement tactics'
          ],
          expectedOutcome: `Improved ${platform} performance metrics`,
          timeframe: '3-6 weeks',
          benchmark: benchmark
        });
      }
    } catch (error) {
      console.error('Error generating benchmark recommendations:', error);
    }

    return recommendations;
  }, []);

  // Generate summary of recommendations
  const generateRecommendationsSummary = useCallback((recommendations) => {
    const summary = {
      total: recommendations.length,
      byPriority: {
        high: recommendations.filter(r => r.priority === 'high').length,
        medium: recommendations.filter(r => r.priority === 'medium').length,
        low: recommendations.filter(r => r.priority === 'low').length
      },
      byCategory: {},
      estimatedImpact: 'Medium to High',
      timeToImplement: '2-8 weeks'
    };

    // Count by category
    recommendations.forEach(rec => {
      summary.byCategory[rec.category] = (summary.byCategory[rec.category] || 0) + 1;
    });

    return summary;
  }, []);

  // Generate action plan
  const generateActionPlan = useCallback((recommendations) => {
    const highPriority = recommendations.filter(r => r.priority === 'high');
    const mediumPriority = recommendations.filter(r => r.priority === 'medium');

    return {
      immediate: highPriority.slice(0, 3),
      shortTerm: mediumPriority.slice(0, 5),
      longTerm: recommendations.filter(r => r.priority === 'low').slice(0, 3)
    };
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    loadStrategicRecommendationsData();
  }, [loadStrategicRecommendationsData]);

  // Handle filter changes
  const handleCategoryFilterChange = (event) => {
    setCategoryFilter(event.target.value);
  };

  const handlePlatformFilterChange = (event) => {
    setPlatformFilter(event.target.value);
  };

  // Handle saving an insight
  const toggleSaveInsight = (insightId) => {
    if (savedInsights.includes(insightId)) {
      setSavedInsights(savedInsights.filter(id => id !== insightId));
      showSuccessNotification('Insight removed from saved items');
    } else {
      setSavedInsights([...savedInsights, insightId]);
      showSuccessNotification('Insight saved for later');
    }
  };

  // Handle creating content from insight using real AI content generation
  const handleCreateContent = useCallback(async (insightId) => {
    try {
      setLoading(true);

      // Find the specific recommendation/insight
      const recommendation = strategicRecommendations?.recommendations?.find(r => r.id === insightId);
      if (!recommendation) {
        showErrorNotification('Insight not found');
        return;
      }

      // Generate content based on the insight using AI
      const contentPrompt = `Create social media content based on this competitive insight: ${recommendation.title}.
        Description: ${recommendation.description}.
        Target platform: ${recommendation.platform}.
        Make it engaging and actionable for our audience.`;

      // Call real content generation API
      const response = await fetch('/api/content/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({
          prompt: contentPrompt,
          platform: recommendation.platform === 'all' ? 'linkedin' : recommendation.platform,
          content_type: 'post',
          tone: 'professional',
          length: 'medium',
          include_hashtags: true,
          source: 'competitor_insight',
          insight_id: insightId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate content');
      }

      const generatedContent = await response.json();

      navigate('/content/create', {
        state: {
          fromInsight: true,
          insightId,
          generatedContent: {
            title: generatedContent.title || `Content inspired by ${recommendation.competitor}`,
            body: generatedContent.content,
            platform: generatedContent.platform,
            type: generatedContent.type,
            hashtags: generatedContent.hashtags,
            insights: [recommendation]
          }
        }
      });

      showSuccessNotification('AI-generated content created from competitive insight');
    } catch (error) {
      console.error('Error creating content from insight:', error);
      showErrorNotification(error.message || 'Failed to create content from insight');
    } finally {
      setLoading(false);
    }
  }, [navigate, showSuccessNotification, showErrorNotification, strategicRecommendations, setLoading]);

  // Handle creating campaign from insights using real AI campaign generation
  const handleCreateCampaign = useCallback(async () => {
    if (savedInsights.length === 0) {
      showErrorNotification('Please save at least one insight to create a campaign');
      return;
    }

    try {
      setLoading(true);

      // Prepare campaign data based on saved insights
      const campaignPrompt = `Create a comprehensive social media campaign based on these competitive insights:
        ${savedInsights.map(insight => `- ${insight.title}: ${insight.description}`).join('\n')}

        Generate a strategic campaign with content themes, posting schedule, and platform-specific tactics.`;

      // Call real campaign generation API
      const response = await fetch('/api/campaigns/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({
          prompt: campaignPrompt,
          insights: savedInsights,
          platforms: [...new Set(savedInsights.map(i => i.platform).filter(p => p !== 'all'))],
          duration: '30 days',
          campaign_type: 'competitive_intelligence',
          objectives: ['brand_awareness', 'engagement', 'lead_generation'],
          source: 'competitor_insights'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate campaign');
      }

      const generatedCampaign = await response.json();

      navigate('/campaigns', {
        state: {
          newCampaign: generatedCampaign.id,
          fromInsights: true,
          generatedCampaign: {
            name: generatedCampaign.name || 'AI-Generated Competitive Campaign',
            description: generatedCampaign.description,
            platforms: generatedCampaign.platforms,
            duration: generatedCampaign.duration,
            content_themes: generatedCampaign.content_themes,
            posting_schedule: generatedCampaign.posting_schedule,
            insights: savedInsights,
            objectives: generatedCampaign.objectives,
            kpis: generatedCampaign.kpis
          }
        }
      });

      showSuccessNotification('AI-generated campaign created from competitive insights');
    } catch (error) {
      console.error('Error creating campaign from insights:', error);
      showErrorNotification(error.message || 'Failed to create campaign from insights');
    } finally {
      setLoading(false);
    }
  }, [savedInsights, navigate, showSuccessNotification, showErrorNotification, setLoading]);

  // Get platform icon
  const getPlatformIcon = (platform) => {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return <FacebookIcon />;
      case 'twitter':
        return <TwitterIcon />;
      case 'instagram':
        return <InstagramIcon />;
      case 'linkedin':
        return <LinkedInIcon />;
      case 'youtube':
        return <YouTubeIcon />;
      case 'tiktok':
        return <TikTokIcon />;
      default:
        return null;
    }
  };

  // Get category color
  const getCategoryColor = (category) => {
    switch (category.toLowerCase()) {
      case 'content_strategy':
        return theme.palette.primary.main;
      case 'posting_schedule':
        return theme.palette.secondary.main;
      case 'engagement':
        return theme.palette.success.main;
      case 'audience_targeting':
        return theme.palette.warning.main;
      default:
        return theme.palette.info.main;
    }
  };

  // Filter recommendations
  const filteredRecommendations = strategicRecommendations?.recommendations
    ? strategicRecommendations.recommendations.filter(rec => {
        const categoryMatch = categoryFilter === 'all' || rec.category === categoryFilter;
        const platformMatch = platformFilter === 'all' || rec.platforms.includes(platformFilter);
        return categoryMatch && platformMatch;
      })
    : [];

  if (selectedCompetitors.length === 0) {
    return (
      <GlassmorphicCard>
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6">
            Please select at least one competitor to view strategic recommendations
          </Typography>
        </Box>
      </GlassmorphicCard>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!strategicRecommendations) {
    return (
      <GlassmorphicCard>
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No strategic recommendations available
          </Typography>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            sx={{ mt: 2 }}
          >
            Generate Recommendations
          </Button>
        </Box>
      </GlassmorphicCard>
    );
  }

  return (
    <Box>
      {/* Recommendations Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Strategic Recommendations
        </Typography>
        <Box>
          {savedInsights.length > 0 && (
            <Button
              variant="contained"
              startIcon={<CampaignIcon />}
              onClick={handleCreateCampaign}
              sx={{ mr: 2 }}
            >
              Create Campaign ({savedInsights.length})
            </Button>
          )}
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Filters */}
      <GlassmorphicCard sx={{ mb: 3 }}>
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Filter Recommendations
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={categoryFilter}
                  onChange={handleCategoryFilterChange}
                  label="Category"
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  <MenuItem value="content_strategy">Content Strategy</MenuItem>
                  <MenuItem value="posting_schedule">Posting Schedule</MenuItem>
                  <MenuItem value="engagement">Engagement</MenuItem>
                  <MenuItem value="audience_targeting">Audience Targeting</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Platform</InputLabel>
                <Select
                  value={platformFilter}
                  onChange={handlePlatformFilterChange}
                  label="Platform"
                >
                  <MenuItem value="all">All Platforms</MenuItem>
                  <MenuItem value="facebook">Facebook</MenuItem>
                  <MenuItem value="twitter">Twitter</MenuItem>
                  <MenuItem value="instagram">Instagram</MenuItem>
                  <MenuItem value="linkedin">LinkedIn</MenuItem>
                  <MenuItem value="youtube">YouTube</MenuItem>
                  <MenuItem value="tiktok">TikTok</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      </GlassmorphicCard>

      {/* Recommendations Grid */}
      <Grid container spacing={3}>
        {filteredRecommendations.length === 0 ? (
          <Grid item xs={12}>
            <GlassmorphicCard>
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h6">
                  No recommendations match your current filters
                </Typography>
              </Box>
            </GlassmorphicCard>
          </Grid>
        ) : (
          filteredRecommendations.map((recommendation) => (
            <Grid item xs={12} sm={6} md={4} key={recommendation.id}>
              <GlassmorphicCard 
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  borderLeft: `4px solid ${getCategoryColor(recommendation.category)}`
                }}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Chip 
                      label={recommendation.category.replace('_', ' ')} 
                      size="small" 
                      sx={{ 
                        backgroundColor: alpha(getCategoryColor(recommendation.category), 0.1),
                        color: getCategoryColor(recommendation.category),
                        textTransform: 'capitalize'
                      }} 
                    />
                    <Box>
                      {recommendation.platforms.map(platform => (
                        <Tooltip key={platform} title={platform}>
                          <Box component="span" sx={{ ml: 0.5 }}>
                            {getPlatformIcon(platform)}
                          </Box>
                        </Tooltip>
                      ))}
                    </Box>
                  </Box>
                  
                  <Typography variant="h6" gutterBottom>
                    <LightbulbIcon sx={{ mr: 1, verticalAlign: 'middle', color: 'warning.main' }} />
                    {recommendation.title}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {recommendation.description}
                  </Typography>
                  
                  {recommendation.actionItems && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Suggested Actions:
                      </Typography>
                      <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>
                        {recommendation.actionItems.map((item, index) => (
                          <li key={index}>
                            <Typography variant="body2">
                              {item}
                            </Typography>
                          </li>
                        ))}
                      </ul>
                    </Box>
                  )}
                </CardContent>
                
                <CardActions sx={{ justifyContent: 'space-between', p: 2, pt: 0 }}>
                  <Box>
                    <Tooltip title={savedInsights.includes(recommendation.id) ? "Remove from saved" : "Save for later"}>
                      <IconButton onClick={() => toggleSaveInsight(recommendation.id)}>
                        {savedInsights.includes(recommendation.id) ? (
                          <BookmarkAddedIcon color="primary" />
                        ) : (
                          <BookmarkIcon />
                        )}
                      </IconButton>
                    </Tooltip>
                  </Box>
                  <Box>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => handleCreateContent(recommendation.id)}
                    >
                      Create Content
                    </Button>
                  </Box>
                </CardActions>
              </GlassmorphicCard>
            </Grid>
          ))
        )}
      </Grid>
    </Box>
  );
};

export default StrategicRecommendations;

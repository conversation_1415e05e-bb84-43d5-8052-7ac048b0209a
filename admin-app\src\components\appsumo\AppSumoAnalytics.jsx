/**
 * Enhanced AppSumo Analytics Dashboard - Enterprise-grade AppSumo analytics component
 * Features: Comprehensive AppSumo analytics dashboard with advanced metrics visualization,
 * customer segmentation, and lifetime deal performance tracking, detailed AppSumo analytics
 * customization with dynamic chart configurations and personalized dashboard layouts, advanced
 * AppSumo analytics features with cohort analysis and revenue tracking, ACE Social's analytics
 * system integration with seamless AppSumo data pipeline management, AppSumo analytics
 * interaction features including interactive charts and drill-down capabilities, AppSumo
 * analytics state management with real-time data updates and analytics validation checks,
 * real-time AppSumo analytics updates with live dashboard displays and dynamic metric
 * calculations, and seamless ACE Social AppSumo platform integration with advanced analytics
 * orchestration and comprehensive accessibility compliance
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import {
  memo,
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  <PERSON>ert,
  Skel<PERSON>,
  useTheme,
  Stack,
  Paper,
  Button,
  Collapse,
  Divider,
  alpha
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Analytics as AnalyticsIcon,
  Code as CodeIcon,
  Redeem as RedeemIcon,
  Timeline as TimelineIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  FilterList as FilterListIcon,
  Share as ShareIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { calculateRedemptionStats, formatCurrency, formatDate, exportToCSV } from '../../utils/appsumoHelpers';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// AppSumo analytics types
const APPSUMO_ANALYTICS_TYPES = {
  OVERVIEW: 'overview',
  REVENUE: 'revenue',
  COHORT: 'cohort',
  PERFORMANCE: 'performance',
  CUSTOMER: 'customer'
};

// Analytics time periods
const TIME_PERIODS = {
  LAST_7_DAYS: '7d',
  LAST_30_DAYS: '30d',
  LAST_90_DAYS: '90d',
  LAST_YEAR: '1y',
  ALL_TIME: 'all'
};

// Chart types
const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  AREA: 'area'
};

/**
 * Enhanced AppSumo Analytics Dashboard - Comprehensive analytics dashboard with advanced features
 * Implements detailed AppSumo analytics management and enterprise-grade analytics capabilities
 */
const EnhancedAppSumoAnalytics = memo(forwardRef(({
  data,
  loading,
  error,
  onRefresh,
  className,
  analyticsType = APPSUMO_ANALYTICS_TYPES.OVERVIEW,
  timePeriod = TIME_PERIODS.LAST_30_DAYS,
  showAdvancedMetrics = true,
  showExportOptions = true,
  showRealTimeUpdates = true,
  enableAccessibility = true,
  enableAnalytics = true,
  onAnalyticsTrack,
  onDataExport,
  onMetricClick,
  ...props
}, ref) => {
  const theme = useTheme();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Core refs for component management
  const analyticsRef = useRef(null);
  const chartRef = useRef(null);

  // Enhanced state management
  const [refreshing, setRefreshing] = useState(false);
  const [showAdvancedView, setShowAdvancedView] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState(null);
  const [analyticsState, setAnalyticsState] = useState({
    lastUpdated: null,
    interactions: 0,
    exportCount: 0,
    viewTime: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshData: () => handleRefresh(),
    exportData: () => handleExport(),
    focusChart: () => chartRef.current?.focus(),
    getAnalyticsState: () => analyticsState,
    resetAnalytics: () => setAnalyticsState({
      lastUpdated: null,
      interactions: 0,
      exportCount: 0,
      viewTime: 0
    }),
    toggleAdvancedView: () => setShowAdvancedView(!showAdvancedView)
  }), [analyticsState, showAdvancedView]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced effects for component lifecycle
  useEffect(() => {
    setAnalyticsState(prev => ({
      ...prev,
      lastUpdated: new Date().toISOString()
    }));

    if (enableAccessibility) {
      announceToScreenReader(`AppSumo analytics dashboard loaded with ${data?.codes?.length || 0} codes`);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('appsumo_analytics_viewed', {
        analyticsType,
        timePeriod,
        codesCount: data?.codes?.length || 0,
        timestamp: new Date().toISOString()
      });
    }
  }, [
    data,
    analyticsType,
    timePeriod,
    enableAccessibility,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // Enhanced handlers
  const handleMetricClick = useCallback((metricType, value) => {
    setAnalyticsState(prev => ({
      ...prev,
      interactions: prev.interactions + 1
    }));

    setSelectedMetric({ type: metricType, value });

    if (onMetricClick) {
      onMetricClick(metricType, value);
    }

    if (enableAccessibility) {
      announceToScreenReader(`Selected ${metricType} metric with value ${value}`);
    }
  }, [onMetricClick, enableAccessibility, announceToScreenReader]);

  // Calculate analytics from data
  const analytics = useMemo(() => {
    if (!data?.codes || !Array.isArray(data.codes)) {
      return null;
    }

    const stats = calculateRedemptionStats(data.codes);
    
    // Calculate revenue estimates (assuming average deal value)
    const avgDealValue = 59; // AppSumo typical deal price
    const estimatedRevenue = stats.redeemed * avgDealValue;
    
    // Calculate conversion trends
    const last7Days = data.codes.filter(code => {
      if (!code.redeemed_at) return false;
      const redemptionDate = new Date(code.redeemed_at);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return redemptionDate >= sevenDaysAgo;
    });
    
    const prev7Days = data.codes.filter(code => {
      if (!code.redeemed_at) return false;
      const redemptionDate = new Date(code.redeemed_at);
      const fourteenDaysAgo = new Date();
      fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      return redemptionDate >= fourteenDaysAgo && redemptionDate < sevenDaysAgo;
    });
    
    const weeklyTrend = last7Days.length - prev7Days.length;
    const weeklyTrendPercent = prev7Days.length > 0 ? 
      ((last7Days.length - prev7Days.length) / prev7Days.length) * 100 : 0;

    return {
      ...stats,
      estimatedRevenue,
      weeklyRedemptions: last7Days.length,
      weeklyTrend,
      weeklyTrendPercent,
    };
  }, [data]);

  // Enhanced refresh handler
  const handleRefresh = useCallback(async () => {
    if (onRefresh) {
      setRefreshing(true);
      setAnalyticsState(prev => ({
        ...prev,
        interactions: prev.interactions + 1
      }));

      try {
        await onRefresh();

        if (enableAccessibility) {
          announceToScreenReader('Analytics data refreshed successfully');
        }

        if (enableAnalytics && onAnalyticsTrack) {
          onAnalyticsTrack('appsumo_analytics_refreshed', {
            analyticsType,
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        if (enableAccessibility) {
          announceToScreenReader('Failed to refresh analytics data');
        }
      } finally {
        setRefreshing(false);
      }
    }
  }, [
    onRefresh,
    analyticsType,
    enableAccessibility,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // Enhanced export handler
  const handleExport = useCallback(() => {
    if (!data?.codes) return;

    setAnalyticsState(prev => ({
      ...prev,
      exportCount: prev.exportCount + 1,
      interactions: prev.interactions + 1
    }));

    const exportColumns = [
      { key: 'code', label: 'Code' },
      { key: 'tier_type', label: 'Tier' },
      { key: 'status', label: 'Status' },
      { key: 'redeemed_at', label: 'Redeemed At', type: 'date' },
      { key: 'created_at', label: 'Created At', type: 'date' },
    ];

    const filename = `appsumo-analytics-${analyticsType}-${formatDate(new Date())}`;
    exportToCSV(data.codes, filename, exportColumns);

    if (onDataExport) {
      onDataExport(filename, data.codes.length);
    }

    if (enableAccessibility) {
      announceToScreenReader(`Exported ${data.codes.length} records to CSV file`);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack('appsumo_analytics_exported', {
        analyticsType,
        recordCount: data.codes.length,
        filename,
        timestamp: new Date().toISOString()
      });
    }
  }, [
    data,
    analyticsType,
    onDataExport,
    enableAccessibility,
    announceToScreenReader,
    enableAnalytics,
    onAnalyticsTrack
  ]);

  // Render loading skeleton
  if (loading && !analytics) {
    return (
      <Box className={className} {...props}>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={12} sm={6} md={3} key={item}>
              <Card variant="glass">
                <CardContent>
                  <Skeleton variant="text" width="60%" height={24} />
                  <Skeleton variant="text" width="40%" height={32} />
                  <Skeleton variant="text" width="80%" height={20} />
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box className={className} {...props}>
        <Alert 
          severity="error" 
          action={
            <IconButton
              color="inherit"
              size="small"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshIcon />
            </IconButton>
          }
        >
          {error}
        </Alert>
      </Box>
    );
  }

  // Render empty state
  if (!analytics) {
    return (
      <Box className={className} {...props}>
        <Card variant="glass">
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <AnalyticsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Analytics Data Available
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Analytics will appear here once you have AppSumo codes and redemptions.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Paper
      ref={analyticsRef}
      elevation={2}
      sx={{
        ...glassMorphismStyles,
        p: 3,
        position: 'relative',
        overflow: 'hidden'
      }}
      className={className}
      {...props}
    >
      {/* Enhanced Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 48,
              height: 48,
              borderRadius: 1,
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
              color: ACE_COLORS.PURPLE
            }}
          >
            <AnalyticsIcon />
          </Box>
          <Box>
            <Typography variant="h5" sx={{ fontWeight: 'bold', color: ACE_COLORS.DARK }}>
              AppSumo Analytics Dashboard
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Real-time insights and performance metrics
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" spacing={1}>
          {/* Advanced View Toggle */}
          {showAdvancedMetrics && (
            <Tooltip title="Toggle advanced view">
              <IconButton
                onClick={() => setShowAdvancedView(!showAdvancedView)}
                size="small"
                sx={{
                  backgroundColor: showAdvancedView ? alpha(ACE_COLORS.PURPLE, 0.1) : 'transparent',
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                  }
                }}
              >
                {showAdvancedView ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Tooltip>
          )}

          {/* Export Options */}
          {showExportOptions && (
            <Tooltip title="Export analytics data">
              <IconButton
                onClick={handleExport}
                disabled={!data?.codes?.length}
                size="small"
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
          )}

          {/* Share Analytics */}
          <Tooltip title="Share analytics">
            <IconButton size="small">
              <ShareIcon />
            </IconButton>
          </Tooltip>

          {/* Refresh Data */}
          <Tooltip title="Refresh analytics data">
            <IconButton
              onClick={handleRefresh}
              disabled={refreshing}
              size="small"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                }
              }}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Stack>
      </Box>

      {/* Real-time Status Indicator */}
      {showRealTimeUpdates && (
        <Box sx={{ mb: 2 }}>
          <Chip
            label={`Last updated: ${analyticsState.lastUpdated ?
              new Date(analyticsState.lastUpdated).toLocaleTimeString() : 'Never'}`}
            size="small"
            color="primary"
            variant="outlined"
            icon={<TimelineIcon />}
          />
        </Box>
      )}

      {/* Analytics Cards */}
      <Grid container spacing={3}>
        {/* Enhanced Total Codes Card */}
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              height: '100%',
              ...glassMorphismStyles,
              cursor: 'pointer',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: `0 12px 40px 0 ${alpha(ACE_COLORS.PURPLE, 0.15)}`
              }
            }}
            onClick={() => handleMetricClick('total_codes', analytics.total)}
          >
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'medium' }}>
                  Total Codes
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 32,
                    height: 32,
                    borderRadius: 1,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    color: ACE_COLORS.PURPLE
                  }}
                >
                  <CodeIcon fontSize="small" />
                </Box>
              </Box>
              <Typography
                variant="h4"
                fontWeight="bold"
                sx={{ color: ACE_COLORS.PURPLE, mb: 1 }}
              >
                {analytics.total.toLocaleString()}
              </Typography>
              <Stack direction="row" alignItems="center" justifyContent="space-between">
                <Typography variant="body2" color="text.secondary">
                  Generated codes
                </Typography>
                {selectedMetric?.type === 'total_codes' && (
                  <Chip
                    label="Selected"
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                )}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Redeemed Codes */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Redeemed
                </Typography>
                <RedeemIcon color="success" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {analytics.redeemed.toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mt={1}>
                <LinearProgress
                  variant="determinate"
                  value={analytics.redemptionRate}
                  sx={{ flexGrow: 1, height: 6, borderRadius: 3 }}
                  color="success"
                />
                <Typography variant="body2" color="text.secondary">
                  {analytics.redemptionRate.toFixed(1)}%
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Weekly Trend */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Weekly Trend
                </Typography>
                <TimelineIcon color="info" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="info.main">
                {analytics.weeklyRedemptions}
              </Typography>
              <Box display="flex" alignItems="center" gap={1} mt={1}>
                {analytics.weeklyTrend >= 0 ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="error" fontSize="small" />
                )}
                <Typography 
                  variant="body2" 
                  color={analytics.weeklyTrend >= 0 ? 'success.main' : 'error.main'}
                >
                  {analytics.weeklyTrend >= 0 ? '+' : ''}{analytics.weeklyTrend} 
                  ({analytics.weeklyTrendPercent.toFixed(1)}%)
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Estimated Revenue */}
        <Grid item xs={12} sm={6} md={3}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Est. Revenue
                </Typography>
                <TrendingUpIcon color="warning" />
              </Box>
              <Typography variant="h4" fontWeight="bold" color="warning.main">
                {formatCurrency(analytics.estimatedRevenue)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Based on redemptions
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Tier Breakdown */}
        <Grid item xs={12} md={6}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Redemptions by Tier
              </Typography>
              {Object.entries(analytics.byTier).map(([tier, stats]) => (
                <Box key={tier} mb={2}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" fontWeight="medium">
                      {tier.toUpperCase()}
                    </Typography>
                    <Chip 
                      label={`${stats.redeemed}/${stats.total}`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stats.total > 0 ? (stats.redeemed / stats.total) * 100 : 0}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Card variant="glass" sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Last 7 days
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.weeklyRedemptions} redemptions
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Last 30 days
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.recentRedemptions} redemptions
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2" color="text.secondary">
                    Available codes
                  </Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {analytics.available} codes
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Paper>
  );
}));

EnhancedAppSumoAnalytics.displayName = 'EnhancedAppSumoAnalytics';

EnhancedAppSumoAnalytics.propTypes = {
  /** AppSumo analytics data */
  data: PropTypes.shape({
    codes: PropTypes.arrayOf(PropTypes.object)
  }),
  /** Loading state indicator */
  loading: PropTypes.bool,
  /** Error message */
  error: PropTypes.string,
  /** Refresh data callback */
  onRefresh: PropTypes.func,
  /** Additional CSS class name */
  className: PropTypes.string,
  /** Analytics dashboard type */
  analyticsType: PropTypes.oneOf(Object.values(APPSUMO_ANALYTICS_TYPES)),
  /** Time period for analytics */
  timePeriod: PropTypes.oneOf(Object.values(TIME_PERIODS)),
  /** Show advanced metrics */
  showAdvancedMetrics: PropTypes.bool,
  /** Show export options */
  showExportOptions: PropTypes.bool,
  /** Show real-time updates */
  showRealTimeUpdates: PropTypes.bool,
  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,
  /** Enable analytics tracking */
  enableAnalytics: PropTypes.bool,
  /** Analytics tracking callback */
  onAnalyticsTrack: PropTypes.func,
  /** Data export callback */
  onDataExport: PropTypes.func,
  /** Metric click callback */
  onMetricClick: PropTypes.func
};

export default EnhancedAppSumoAnalytics;

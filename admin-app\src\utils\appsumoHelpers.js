/**
 * AppSumo utility functions for data processing, validation, and formatting
 @since 2024-1-1 to 2025-25-7
*/

/**
 * Format currency values
 */
export const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format date values with timezone support
 */
export const formatDate = (date, options = {}) => {
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    timeZone: 'UTC',
  };
  
  const formatOptions = { ...defaultOptions, ...options };
  
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat('en-US', formatOptions).format(dateObj);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
};

/**
 * Format date and time
 */
export const formatDateTime = (date) => {
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'UTC',
  });
};

/**
 * Calculate relative time (e.g., "2 hours ago")
 */
export const getRelativeTime = (date) => {
  if (!date) return '-';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now - dateObj) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return formatDate(dateObj);
  } catch (error) {
    console.error('Error calculating relative time:', error);
    return 'Unknown';
  }
};

/**
 * Generate AppSumo code with specific format
 */
export const generateAppSumoCode = (prefix = 'AS', length = 8) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix + '-';
  
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  
  return result;
};

/**
 * Validate AppSumo code format
 */
export const validateAppSumoCode = (code) => {
  if (!code || typeof code !== 'string') {
    return { isValid: false, error: 'Code is required' };
  }
  
  // Remove whitespace and convert to uppercase
  const cleanCode = code.trim().toUpperCase();
  
  // Check format: AS-XXXXXXXX (minimum 8 characters after prefix)
  const codePattern = /^AS-[A-Z0-9]{8,}$/;
  
  if (!codePattern.test(cleanCode)) {
    return { 
      isValid: false, 
      error: 'Code must be in format AS-XXXXXXXX with at least 8 characters after prefix' 
    };
  }
  
  return { isValid: true, code: cleanCode };
};

/**
 * Get tier configuration by type
 */
export const getTierConfig = (tierType) => {
  const tierConfigs = {
    tier1: {
      name: 'Single',
      description: 'Perfect for individual creators',
      maxUsers: 1,
      maxSocialAccounts: 5,
      maxPostsPerMonth: 100,
      maxICPs: 3,
      maxCampaigns: 5,
      analyticsHistoryDays: 90,
      color: '#4590FF',
    },
    tier2: {
      name: 'Double',
      description: 'Perfect for small teams',
      maxUsers: 2,
      maxSocialAccounts: 10,
      maxPostsPerMonth: 200,
      maxICPs: 5,
      maxCampaigns: 10,
      analyticsHistoryDays: 180,
      color: '#00D68F',
    },
    tier3: {
      name: 'Triple',
      description: 'Perfect for growing businesses',
      maxUsers: 3,
      maxSocialAccounts: 15,
      maxPostsPerMonth: 300,
      maxICPs: 10,
      maxCampaigns: 20,
      analyticsHistoryDays: 365,
      color: '#6C4BFA',
    },
  };
  
  return tierConfigs[tierType] || null;
};

/**
 * Calculate redemption statistics
 */
export const calculateRedemptionStats = (codes) => {
  if (!Array.isArray(codes)) return null;
  
  const total = codes.length;
  const redeemed = codes.filter(code => code.is_redeemed || code.status === 'redeemed').length;
  const available = total - redeemed;
  const redemptionRate = total > 0 ? (redeemed / total) * 100 : 0;
  
  // Calculate redemptions by tier
  const byTier = codes.reduce((acc, code) => {
    const tier = code.tier_type || 'unknown';
    if (!acc[tier]) {
      acc[tier] = { total: 0, redeemed: 0 };
    }
    acc[tier].total++;
    if (code.is_redeemed || code.status === 'redeemed') {
      acc[tier].redeemed++;
    }
    return acc;
  }, {});
  
  // Calculate redemptions over time (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const recentRedemptions = codes.filter(code => {
    if (!code.redeemed_at) return false;
    const redemptionDate = new Date(code.redeemed_at);
    return redemptionDate >= thirtyDaysAgo;
  });
  
  return {
    total,
    redeemed,
    available,
    redemptionRate: Math.round(redemptionRate * 100) / 100,
    byTier,
    recentRedemptions: recentRedemptions.length,
  };
};

/**
 * Export data to CSV format
 */
export const exportToCSV = (data, filename, columns) => {
  if (!Array.isArray(data) || data.length === 0) {
    throw new Error('No data to export');
  }
  
  // Create CSV header
  const headers = columns.map(col => col.label || col.key).join(',');
  
  // Create CSV rows
  const rows = data.map(item => {
    return columns.map(col => {
      let value = item[col.key];
      
      // Handle nested properties
      if (col.key.includes('.')) {
        const keys = col.key.split('.');
        value = keys.reduce((obj, key) => obj?.[key], item);
      }
      
      // Format value based on type
      if (col.type === 'date' && value) {
        value = formatDate(value);
      } else if (col.type === 'currency' && value !== null && value !== undefined) {
        value = formatCurrency(value);
      } else if (col.type === 'boolean') {
        value = value ? 'Yes' : 'No';
      }
      
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        value = `"${value.replace(/"/g, '""')}"`;
      }
      
      return value || '';
    }).join(',');
  });
  
  // Combine header and rows
  const csvContent = [headers, ...rows].join('\n');
  
  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

/**
 * Validate email format
 */
export const validateEmail = (email) => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};

/**
 * Validate required fields in form data
 */
export const validateFormData = (data, requiredFields) => {
  const errors = {};
  
  requiredFields.forEach(field => {
    const value = data[field.key];
    
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      errors[field.key] = `${field.label} is required`;
    } else if (field.type === 'email' && !validateEmail(value)) {
      errors[field.key] = 'Please enter a valid email address';
    } else if (field.type === 'number' && (isNaN(value) || value < 0)) {
      errors[field.key] = `${field.label} must be a positive number`;
    } else if (field.minLength && value.length < field.minLength) {
      errors[field.key] = `${field.label} must be at least ${field.minLength} characters`;
    } else if (field.maxLength && value.length > field.maxLength) {
      errors[field.key] = `${field.label} must be no more than ${field.maxLength} characters`;
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Debounce function for search inputs
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Copy text to clipboard
 */
export const copyToClipboard = async (text) => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

/**
 * Generate correlation ID for tracking
 */
export const generateCorrelationId = () => {
  return `appsumo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Format file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// @since 2024-1-1 to 2025-25-7
import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ICPPerformance from '../ICPPerformance';
import { NotificationProvider } from '../../../contexts/NotificationContext';
import * as icpPerformanceApi from '../../../api/icp-performance';

// Mock the API module
jest.mock('../../../api/icp-performance');

// Mock D3 to avoid DOM manipulation issues in tests
jest.mock('d3', () => ({
  select: jest.fn(() => ({
    selectAll: jest.fn(() => ({
      remove: jest.fn()
    })),
    append: jest.fn(() => ({
      attr: jest.fn(() => ({
        attr: jest.fn(() => ({
          append: jest.fn(() => ({
            attr: jest.fn(() => ({}))
          }))
        }))
      }))
    }))
  })),
  scaleBand: jest.fn(() => ({
    range: jest.fn(() => ({
      domain: jest.fn(() => ({
        padding: jest.fn(() => ({}))
      }))
    }))
  })),
  scaleLinear: jest.fn(() => ({
    domain: jest.fn(() => ({
      range: jest.fn(() => ({}))
    }))
  })),
  max: jest.fn(() => 100),
  axisBottom: jest.fn(() => ({})),
  axisLeft: jest.fn(() => ({})),
  easeBackOut: jest.fn()
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock data
const mockICPPerformances = [
  {
    icp_id: '1',
    icp_name: 'Tech Enthusiasts',
    avg_engagement_rate: 15.5,
    total_content: 25
  },
  {
    icp_id: '2',
    icp_name: 'Business Professionals',
    avg_engagement_rate: 12.3,
    total_content: 18
  }
];

const mockTopPerformingICPs = [
  {
    icp_id: '1',
    icp_name: 'Tech Enthusiasts',
    avg_engagement_rate: 15.5,
    total_content: 25
  }
];

const mockComparisonData = {
  icps: mockICPPerformances,
  best_performing_icp_name: 'Tech Enthusiasts',
  performance_gap: {
    avg_engagement_rate: 3.2
  },
  recommendations: ['Focus on tech content', 'Post during peak hours']
};

const mockRecommendations = {
  icp_name: 'Tech Enthusiasts',
  optimal_content_types: [
    { type: 'Video', engagement_rate: 18.5 }
  ],
  optimal_platforms: [
    { platform: 'LinkedIn', engagement_rate: 16.2 }
  ],
  optimal_posting_times: {
    LinkedIn: ['9:00 AM', '2:00 PM']
  },
  recommendations: [
    { value: 'Post more videos', reason: 'Higher engagement' }
  ]
};

// Test wrapper component
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return (
    <ThemeProvider theme={theme}>
      <NotificationProvider>
        {children}
      </NotificationProvider>
    </ThemeProvider>
  );
};

describe('ICPPerformance Component', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup default API mocks
    icpPerformanceApi.getAllICPPerformances.mockResolvedValue(mockICPPerformances);
    icpPerformanceApi.getTopPerformingICPs.mockResolvedValue(mockTopPerformingICPs);
    icpPerformanceApi.compareICPs.mockResolvedValue(mockComparisonData);
    icpPerformanceApi.analyzeICPPerformance.mockResolvedValue(mockICPPerformances[0]);
    icpPerformanceApi.getICPRecommendations.mockResolvedValue(mockRecommendations);
    icpPerformanceApi.exportICPPerformanceCSV.mockResolvedValue(new Blob(['test'], { type: 'text/csv' }));
    icpPerformanceApi.exportICPComparisonCSV.mockResolvedValue(new Blob(['test'], { type: 'text/csv' }));
  });

  describe('Initial Rendering', () => {
    test('renders main heading and navigation tabs', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      expect(screen.getByRole('heading', { name: /icp performance analytics/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /overview/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /compare icps/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /recommendations/i })).toBeInTheDocument();
    });

    test('shows loading state initially', () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      expect(screen.getByLabelText(/loading data/i)).toBeInTheDocument();
    });

    test('loads and displays ICP performance data', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
        expect(screen.getByText('15.50%')).toBeInTheDocument();
      });

      expect(icpPerformanceApi.getAllICPPerformances).toHaveBeenCalled();
      expect(icpPerformanceApi.getTopPerformingICPs).toHaveBeenCalledWith(5);
    });
  });

  describe('Advanced Features', () => {
    test('displays advanced controls toolbar', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/refresh all data/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/export performance data to csv/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/enable auto-refresh/i)).toBeInTheDocument();
      });
    });

    test('handles data export functionality', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/export performance data to csv/i)).toBeInTheDocument();
      });

      const exportButton = screen.getByLabelText(/export performance data to csv/i);
      await user.click(exportButton);

      expect(icpPerformanceApi.exportICPPerformanceCSV).toHaveBeenCalled();
    });

    test('toggles auto-refresh functionality', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/enable auto-refresh/i)).toBeInTheDocument();
      });

      const autoRefreshButton = screen.getByLabelText(/enable auto-refresh/i);
      await user.click(autoRefreshButton);

      expect(screen.getByLabelText(/disable auto-refresh/i)).toBeInTheDocument();
    });
  });

  describe('Filtering and Sorting', () => {
    test('shows filter controls when filter button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/toggle filters/i)).toBeInTheDocument();
      });

      const filterButton = screen.getByLabelText(/toggle filters/i);
      await user.click(filterButton);

      expect(screen.getByText(/filter & sort options/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/sort by/i)).toBeInTheDocument();
    });

    test('applies sorting filters correctly', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/toggle filters/i)).toBeInTheDocument();
      });

      // Open filters
      const filterButton = screen.getByLabelText(/toggle filters/i);
      await user.click(filterButton);

      // Change sort order
      const orderSelect = screen.getByLabelText(/order/i);
      await user.click(orderSelect);
      await user.click(screen.getByText(/low to high/i));

      // Verify the filter was applied (component should re-render with new order)
      expect(screen.getByDisplayValue('asc')).toBeInTheDocument();
    });

    test('resets filters when reset button is clicked', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/toggle filters/i)).toBeInTheDocument();
      });

      // Open filters
      const filterButton = screen.getByLabelText(/toggle filters/i);
      await user.click(filterButton);

      // Reset filters
      const resetButton = screen.getByLabelText(/reset all filters/i);
      await user.click(resetButton);

      // Verify filters are reset to defaults
      expect(screen.getByDisplayValue('desc')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    test('switches between tabs correctly', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /compare icps/i })).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      expect(screen.getByText(/compare icp performance/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/select multiple icps to compare/i)).toBeInTheDocument();

      // Switch to Recommendations tab
      const recommendationsTab = screen.getByRole('tab', { name: /recommendations/i });
      await user.click(recommendationsTab);

      expect(screen.getByText(/get content recommendations for icp/i)).toBeInTheDocument();
    });
  });

  describe('ICP Comparison', () => {
    test('handles ICP selection and comparison', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /compare icps/i })).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      // Wait for ICPs to load
      await waitFor(() => {
        expect(screen.getByLabelText(/select multiple icps to compare/i)).toBeInTheDocument();
      });

      // Select ICPs for comparison
      const selectElement = screen.getByLabelText(/select multiple icps to compare/i);
      await user.click(selectElement);

      // Select first ICP
      const techOption = screen.getByText('Tech Enthusiasts');
      await user.click(techOption);

      // Select second ICP
      const businessOption = screen.getByText('Business Professionals');
      await user.click(businessOption);

      // Click compare button
      const compareButton = screen.getByLabelText(/compare 2 selected icps/i);
      await user.click(compareButton);

      expect(icpPerformanceApi.compareICPs).toHaveBeenCalledWith(['1', '2']);
    });

    test('exports comparison data', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /compare icps/i })).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      // Wait for ICPs to load and select them
      await waitFor(() => {
        expect(screen.getByLabelText(/select multiple icps to compare/i)).toBeInTheDocument();
      });

      // Mock selecting ICPs (simulate state change)
      const exportButton = screen.getByLabelText(/export comparison data to csv/i);

      // The export button should be disabled initially
      expect(exportButton).toBeDisabled();
    });
  });

  describe('Recommendations', () => {
    test('gets recommendations for selected ICP', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /recommendations/i })).toBeInTheDocument();
      });

      // Switch to Recommendations tab
      const recommendationsTab = screen.getByRole('tab', { name: /recommendations/i });
      await user.click(recommendationsTab);

      // Wait for ICPs to load
      await waitFor(() => {
        expect(screen.getByLabelText(/select an icp to get content recommendations/i)).toBeInTheDocument();
      });

      // Select an ICP
      const selectElement = screen.getByLabelText(/select an icp to get content recommendations/i);
      await user.click(selectElement);

      const techOption = screen.getByText('Tech Enthusiasts');
      await user.click(techOption);

      // Click get recommendations button
      const getRecommendationsButton = screen.getByLabelText(/get content recommendations for selected icp/i);
      await user.click(getRecommendationsButton);

      expect(icpPerformanceApi.getICPRecommendations).toHaveBeenCalledWith('1');
    });
  });

  describe('Error Handling', () => {
    test('displays error message when API calls fail', async () => {
      icpPerformanceApi.getAllICPPerformances.mockRejectedValue(new Error('API Error'));

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      // Should show retry button
      expect(screen.getByText(/retry/i)).toBeInTheDocument();
    });

    test('handles retry functionality', async () => {
      const user = userEvent.setup();

      // First call fails, second succeeds
      icpPerformanceApi.getAllICPPerformances
        .mockRejectedValueOnce(new Error('API Error'))
        .mockResolvedValue(mockICPPerformances);

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      const retryButton = screen.getByText(/retry/i);
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels and roles', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getByLabelText(/icp performance analytics dashboard/i)).toBeInTheDocument();
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /compare icps/i })).toBeInTheDocument();
      });

      // Test tab navigation with keyboard
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      compareTab.focus();

      await user.keyboard('{Enter}');

      expect(screen.getByText(/compare icp performance/i)).toBeInTheDocument();
    });

    test('provides screen reader friendly content', async () => {
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByLabelText(/loading top performing icps/i)).toBeInTheDocument();
      });

      // Check for aria-live regions
      expect(screen.getByRole('status')).toBeInTheDocument();
    });
  });

  describe('Performance Optimizations', () => {
    test('component is memoized', () => {
      const { rerender } = render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      // Re-render with same props should not cause unnecessary re-renders
      rerender(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      // Component should be wrapped with memo
      expect(ICPPerformance.displayName).toBe('ICPPerformance');
    });
  });
});

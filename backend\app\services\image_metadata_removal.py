"""
Image Metadata Removal Service for ACEO Platform

This service provides comprehensive metadata removal for AI-generated images,
eliminating EXIF data, AI generator tags, and other identifying information
while maintaining image quality and performance.
"""

import io
import logging
import time
import asyncio
from typing import Dict, Any, Optional, Union, List, Tuple
from datetime import datetime, timezone
from pathlib import Path
import hashlib
import json

from PIL import Image, ExifTags
from PIL.ExifTags import TAGS
import aiohttp
import aiofiles

from app.core.config import settings
from app.core.exceptions import ImageProcessingError, ExternalServiceError
from app.core.monitoring import monitor_performance, record_addon_metrics
from app.services.cache_service import CacheService
from app.services.addon_usage_tracking import track_addon_usage, UsageType
from app.utils.redis_cache import get_redis_client

logger = logging.getLogger(__name__)

# Metadata removal configuration - uses settings from config
def get_metadata_removal_config():
    """Get metadata removal configuration from settings."""
    return {
        "enabled": settings.METADATA_REMOVAL_ENABLED,
        "preserve_quality": settings.METADATA_REMOVAL_PRESERVE_QUALITY,
        "max_file_size_mb": settings.METADATA_REMOVAL_MAX_FILE_SIZE_MB,
        "supported_formats": ["JPEG", "PNG", "WEBP", "TIFF"],
        "cache_ttl": settings.METADATA_REMOVAL_CACHE_TTL,
        "performance_target_ms": settings.METADATA_REMOVAL_PERFORMANCE_TARGET_MS,
        "batch_size_limit": settings.METADATA_REMOVAL_BATCH_SIZE_LIMIT,
        "track_usage": settings.METADATA_REMOVAL_TRACK_USAGE,
        "ai_generator_tags": [
            "DALL·E", "DALL-E", "ChatGPT", "OpenAI", "Midjourney", "Stable Diffusion",
            "Adobe Firefly", "Google Imagen", "Anthropic", "AI Generated", "Artificial Intelligence",
            "Machine Learning", "Neural Network", "Deep Learning", "Generative AI"
        ],
        "c2pa_tags": [
            "c2pa", "Content Credentials", "contentCredentials", "provenance", "digital signature",
            "content authenticity", "CAI", "Project Origin", "Adobe Content Authenticity",
            "Microsoft Project Origin", "BBC Origin", "Truepic", "Numbers Protocol"
        ],
        "metadata_segments": [
            "XMP", "IPTC", "ICC", "Photoshop", "Adobe", "Exif", "GPS", "Makernotes",
            "JFIF", "APP1", "APP2", "APP13", "APP14", "COM", "DQT", "DHT"
        ]
    }

METADATA_REMOVAL_CONFIG = get_metadata_removal_config()

class ImageMetadataRemover:
    """
    Production-ready image metadata removal service with comprehensive
    error handling, performance monitoring, and add-on integration.
    """
    
    def __init__(self):
        self.cache_service = CacheService(namespace="metadata_removal")
        self.performance_metrics = {
            "total_processed": 0,
            "total_time_ms": 0.0,
            "cache_hits": 0,
            "errors": 0
        }
    
    @monitor_performance("remove_image_metadata")
    async def remove_metadata_from_url(
        self,
        image_url: str,
        user_id: str,
        preserve_quality: bool = True,
        track_usage: bool = True
    ) -> Dict[str, Any]:
        """
        Remove metadata from an image URL and return the processed image data.
        
        Args:
            image_url: URL of the image to process
            user_id: User ID for usage tracking
            preserve_quality: Whether to preserve image quality
            track_usage: Whether to track add-on usage
            
        Returns:
            Dictionary containing processed image data and metadata
        """
        start_time = time.time()
        
        try:
            # Generate cache key
            cache_key = self._generate_cache_key(image_url, preserve_quality)
            
            # Check cache first
            cached_result = await self.cache_service.get(cache_key)
            if cached_result:
                self.performance_metrics["cache_hits"] += 1
                logger.info(f"Cache hit for image metadata removal: {image_url}")
                return cached_result
            
            # Download image
            image_data = await self._download_image(image_url)
            
            # Process image
            processed_data = await self._process_image_data(
                image_data, 
                preserve_quality=preserve_quality
            )
            
            # Track usage if enabled
            if track_usage:
                await self._track_metadata_removal_usage(user_id)
            
            # Cache result
            await self.cache_service.set(
                cache_key, 
                processed_data, 
                ttl=METADATA_REMOVAL_CONFIG["cache_ttl"]
            )
            
            # Update performance metrics
            processing_time = (time.time() - start_time) * 1000
            self.performance_metrics["total_processed"] += 1
            self.performance_metrics["total_time_ms"] += processing_time
            
            # Log performance warning if too slow
            if processing_time > METADATA_REMOVAL_CONFIG["performance_target_ms"]:
                logger.warning(
                    f"Metadata removal took {processing_time:.2f}ms, "
                    f"exceeding target of {METADATA_REMOVAL_CONFIG['performance_target_ms']}ms"
                )
            
            logger.info(f"Successfully removed metadata from image in {processing_time:.2f}ms")

            # Record monitoring metrics
            await self._record_monitoring_metrics(
                operation_type="single",
                user_id=user_id,
                image_count=1,
                processing_time_ms=processing_time,
                success=True,
                cache_hit=cached_result is not None,
                **processed_data
            )

            return processed_data
            
        except Exception as e:
            self.performance_metrics["errors"] += 1
            processing_time = (time.time() - start_time) * 1000

            # Record monitoring metrics for error case
            await self._record_monitoring_metrics(
                operation_type="single",
                user_id=user_id,
                image_count=1,
                processing_time_ms=processing_time,
                success=False,
                cache_hit=False,
                error_message=str(e)
            )

            logger.error(f"Error removing metadata from image {image_url}: {str(e)}")
            raise ImageProcessingError(
                message=f"Failed to remove metadata from image: {str(e)}",
                details={"image_url": image_url, "user_id": user_id}
            ) from e
    
    async def remove_metadata_from_file(
        self,
        image_data: bytes,
        user_id: str,
        preserve_quality: bool = True,
        track_usage: bool = True
    ) -> Dict[str, Any]:
        """
        Remove metadata from image file data.
        
        Args:
            image_data: Raw image data bytes
            user_id: User ID for usage tracking
            preserve_quality: Whether to preserve image quality
            track_usage: Whether to track add-on usage
            
        Returns:
            Dictionary containing processed image data and metadata
        """
        start_time = time.time()
        
        try:
            # Process image
            processed_data = await self._process_image_data(
                image_data, 
                preserve_quality=preserve_quality
            )
            
            # Track usage if enabled
            if track_usage:
                await self._track_metadata_removal_usage(user_id)
            
            # Update performance metrics
            processing_time = (time.time() - start_time) * 1000
            self.performance_metrics["total_processed"] += 1
            self.performance_metrics["total_time_ms"] += processing_time
            
            logger.info(f"Successfully removed metadata from file data in {processing_time:.2f}ms")
            
            return processed_data
            
        except Exception as e:
            self.performance_metrics["errors"] += 1
            logger.error(f"Error removing metadata from file data: {str(e)}")
            raise ImageProcessingError(
                message=f"Failed to remove metadata from file: {str(e)}",
                details={"user_id": user_id, "data_size": len(image_data)}
            ) from e
    
    async def batch_remove_metadata(
        self,
        image_urls: List[str],
        user_id: str,
        preserve_quality: bool = True,
        track_usage: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Remove metadata from multiple images concurrently.
        
        Args:
            image_urls: List of image URLs to process
            user_id: User ID for usage tracking
            preserve_quality: Whether to preserve image quality
            track_usage: Whether to track add-on usage
            
        Returns:
            List of processed image data dictionaries
        """
        try:
            # Process images concurrently
            tasks = [
                self.remove_metadata_from_url(
                    url, user_id, preserve_quality, track_usage
                )
                for url in image_urls
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle results and exceptions
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to process image {image_urls[i]}: {str(result)}")
                    processed_results.append({
                        "success": False,
                        "error": str(result),
                        "original_url": image_urls[i]
                    })
                else:
                    processed_results.append(result)
            
            return processed_results

        except Exception as e:
            logger.error(f"Error in batch metadata removal: {str(e)}")
            raise ImageProcessingError(
                message=f"Failed to process batch metadata removal: {str(e)}",
                details={"user_id": user_id, "image_count": len(image_urls)}
            ) from e

    async def _process_image_data(
        self,
        image_data: bytes,
        preserve_quality: bool = True
    ) -> Dict[str, Any]:
        """
        Core image processing method to remove metadata.

        Args:
            image_data: Raw image data bytes
            preserve_quality: Whether to preserve image quality

        Returns:
            Dictionary containing processed image data and metadata
        """
        try:
            # Validate file size
            file_size_mb = len(image_data) / (1024 * 1024)
            if file_size_mb > METADATA_REMOVAL_CONFIG["max_file_size_mb"]:
                raise ImageProcessingError(
                    message=f"Image file too large: {file_size_mb:.2f}MB",
                    details={"max_size_mb": METADATA_REMOVAL_CONFIG["max_file_size_mb"]}
                )

            # Open image with PIL
            original_image = Image.open(io.BytesIO(image_data))
            original_format = original_image.format

            # Validate format
            if original_format not in METADATA_REMOVAL_CONFIG["supported_formats"]:
                raise ImageProcessingError(
                    message=f"Unsupported image format: {original_format}",
                    details={"supported_formats": METADATA_REMOVAL_CONFIG["supported_formats"]}
                )

            # Extract metadata before removal (for logging/analysis)
            metadata_info = await self._extract_metadata_info(original_image)

            # Enhanced metadata removal for C2PA and other advanced metadata
            clean_image = await self._create_clean_image(original_image, original_format)

            # Save processed image with comprehensive metadata stripping
            output_buffer = io.BytesIO()
            save_kwargs = {"format": original_format}

            # Set quality parameters based on preserve_quality setting
            if original_format == "JPEG":
                save_kwargs["quality"] = 95 if preserve_quality else 85
                save_kwargs["optimize"] = True
                # Explicitly remove all metadata segments
                save_kwargs["exif"] = b""  # Remove EXIF
                save_kwargs["icc_profile"] = None  # Remove ICC profile
            elif original_format == "PNG":
                save_kwargs["optimize"] = True
                # PNG metadata is handled by creating a new image
            elif original_format == "WEBP":
                save_kwargs["quality"] = 95 if preserve_quality else 85
                save_kwargs["method"] = 6  # Best compression
                save_kwargs["exif"] = b""  # Remove EXIF for WebP
                save_kwargs["icc_profile"] = None  # Remove ICC profile

            clean_image.save(output_buffer, **save_kwargs)
            processed_data = output_buffer.getvalue()

            # Calculate size reduction
            original_size = len(image_data)
            processed_size = len(processed_data)
            size_reduction_percent = ((original_size - processed_size) / original_size) * 100

            return {
                "success": True,
                "processed_data": processed_data,
                "original_size_bytes": original_size,
                "processed_size_bytes": processed_size,
                "size_reduction_percent": round(size_reduction_percent, 2),
                "format": original_format,
                "dimensions": {
                    "width": original_image.width,
                    "height": original_image.height
                },
                "metadata_removed": metadata_info,
                "processed_at": datetime.now(timezone.utc).isoformat(),
                "preserve_quality": preserve_quality
            }

        except Exception as e:
            logger.error(f"Error processing image data: {str(e)}")
            raise ImageProcessingError(
                message=f"Failed to process image data: {str(e)}",
                details={"data_size": len(image_data)}
            ) from e

    async def _create_clean_image(
        self,
        original_image: Image.Image,
        original_format: str
    ) -> Image.Image:
        """
        Create a clean image without any metadata, including C2PA Content Credentials.

        This method performs comprehensive metadata removal by:
        1. Converting to RGB if necessary for JPEG compatibility
        2. Creating a completely new image without any metadata
        3. Handling transparency properly

        Args:
            original_image: PIL Image object
            original_format: Original image format

        Returns:
            Clean PIL Image object without metadata
        """
        try:
            # Handle format-specific conversions
            if original_format == "JPEG" and original_image.mode in ("RGBA", "P"):
                # Create white background for transparency
                # Use type: ignore to handle PIL type annotation issues
                background = Image.new("RGB", original_image.size, (255, 255, 255))  # type: ignore
                if original_image.mode == "P":
                    original_image = original_image.convert("RGBA")
                background.paste(
                    original_image,
                    mask=original_image.split()[-1] if original_image.mode == "RGBA" else None
                )
                clean_image = background
            else:
                clean_image = original_image.copy()

            # Create completely new image to strip ALL metadata including C2PA
            # This is the most effective way to remove Content Credentials
            new_image = Image.new(clean_image.mode, clean_image.size)
            new_image.paste(clean_image)

            # Ensure no metadata is carried over
            if hasattr(new_image, 'info'):
                new_image.info.clear()

            return new_image

        except Exception as e:
            logger.error(f"Error creating clean image: {str(e)}")
            # Fallback to simple copy if advanced cleaning fails
            return original_image.copy()

    async def _extract_metadata_info(self, image: Image.Image) -> Dict[str, Any]:
        """
        Extract metadata information from image for logging and analysis.

        Args:
            image: PIL Image object

        Returns:
            Dictionary containing metadata information
        """
        metadata_info = {
            "exif_data": {},
            "ai_generator_tags": [],
            "c2pa_tags": [],
            "creation_date": None,
            "device_info": {},
            "location_data": {},
            "copyright_info": {},
            "total_tags_removed": 0
        }

        try:
            # Extract EXIF data
            exif_data = image.getexif()
            if exif_data:
                for tag_id, value in exif_data.items():
                    tag_name = TAGS.get(tag_id, tag_id)

                    # Convert value to string for JSON serialization
                    str_value = str(value)

                    # Categorize metadata
                    if tag_name in ["DateTime", "DateTimeOriginal", "DateTimeDigitized"]:
                        metadata_info["creation_date"] = str_value
                    elif tag_name in ["Make", "Model", "Software", "Artist"]:
                        metadata_info["device_info"][tag_name] = str_value
                    elif tag_name in ["GPSInfo", "GPS"]:
                        metadata_info["location_data"][tag_name] = str_value
                    elif tag_name in ["Copyright", "Artist", "ImageDescription"]:
                        metadata_info["copyright_info"][tag_name] = str_value

                    # Check for AI generator tags
                    for ai_tag in METADATA_REMOVAL_CONFIG["ai_generator_tags"]:
                        if ai_tag.lower() in str_value.lower():
                            metadata_info["ai_generator_tags"].append({
                                "tag": tag_name,
                                "value": str_value,
                                "ai_identifier": ai_tag
                            })

                    # Check for C2PA Content Credentials tags
                    for c2pa_tag in METADATA_REMOVAL_CONFIG["c2pa_tags"]:
                        if c2pa_tag.lower() in str_value.lower():
                            metadata_info["c2pa_tags"].append({
                                "tag": tag_name,
                                "value": str_value,
                                "c2pa_identifier": c2pa_tag
                            })

                    metadata_info["exif_data"][tag_name] = str_value
                    metadata_info["total_tags_removed"] += 1

            # Check image info for additional metadata
            if hasattr(image, 'info') and image.info:
                for key, value in image.info.items():
                    str_value = str(value)

                    # Check for AI generator tags in image info
                    for ai_tag in METADATA_REMOVAL_CONFIG["ai_generator_tags"]:
                        if ai_tag.lower() in str_value.lower():
                            metadata_info["ai_generator_tags"].append({
                                "tag": key,
                                "value": str_value,
                                "ai_identifier": ai_tag
                            })

                    # Check for C2PA Content Credentials tags in image info
                    for c2pa_tag in METADATA_REMOVAL_CONFIG["c2pa_tags"]:
                        if c2pa_tag.lower() in str_value.lower():
                            metadata_info["c2pa_tags"].append({
                                "tag": key,
                                "value": str_value,
                                "c2pa_identifier": c2pa_tag
                            })

                    metadata_info["total_tags_removed"] += 1

        except Exception as e:
            logger.warning(f"Error extracting metadata info: {str(e)}")

        return metadata_info

    async def _download_image(self, image_url: str) -> bytes:
        """
        Download image from URL with proper error handling and timeouts.

        Args:
            image_url: URL of the image to download

        Returns:
            Raw image data bytes
        """
        try:
            timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(image_url) as response:
                    if response.status != 200:
                        raise ExternalServiceError(
                            message=f"Failed to download image: HTTP {response.status}",
                            service="Image Download",
                            details={"url": image_url, "status": response.status}
                        )

                    # Check content type
                    content_type = response.headers.get('content-type', '').lower()
                    if not content_type.startswith('image/'):
                        raise ImageProcessingError(
                            message=f"Invalid content type: {content_type}",
                            details={"url": image_url, "content_type": content_type}
                        )

                    # Read image data
                    image_data = await response.read()

                    if not image_data:
                        raise ImageProcessingError(
                            message="Empty image data received",
                            details={"url": image_url}
                        )

                    return image_data

        except aiohttp.ClientError as e:
            logger.error(f"Network error downloading image {image_url}: {str(e)}")
            raise ExternalServiceError(
                message=f"Network error downloading image: {str(e)}",
                service="Image Download",
                details={"url": image_url}
            ) from e
        except Exception as e:
            logger.error(f"Error downloading image {image_url}: {str(e)}")
            raise ImageProcessingError(
                message=f"Failed to download image: {str(e)}",
                details={"url": image_url}
            ) from e

    def _generate_cache_key(self, image_url: str, preserve_quality: bool) -> str:
        """
        Generate cache key for metadata removal results.

        Args:
            image_url: URL of the image
            preserve_quality: Quality preservation setting

        Returns:
            Cache key string
        """
        # Create hash of URL and settings
        key_data = f"{image_url}:{preserve_quality}"
        return hashlib.md5(key_data.encode()).hexdigest()

    async def _track_metadata_removal_usage(self, user_id: str) -> None:
        """
        Track metadata removal usage for add-on billing.

        Args:
            user_id: User ID for usage tracking
        """
        try:
            await track_addon_usage(
                user_id=user_id,
                usage_type=UsageType.IMAGE_GENERATION,  # Use existing image generation usage type
                amount=1,
                metadata={
                    "operation": "metadata_removal",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

            # Record metrics
            record_addon_metrics("metadata_removal", "usage", 1)

        except Exception as e:
            # Don't fail the main operation if usage tracking fails
            logger.warning(f"Failed to track metadata removal usage for user {user_id}: {str(e)}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for monitoring.

        Returns:
            Dictionary containing performance metrics
        """
        avg_time = (
            self.performance_metrics["total_time_ms"] / self.performance_metrics["total_processed"]
            if self.performance_metrics["total_processed"] > 0 else 0
        )

        return {
            "total_processed": self.performance_metrics["total_processed"],
            "average_processing_time_ms": round(avg_time, 2),
            "cache_hit_rate": (
                self.performance_metrics["cache_hits"] / self.performance_metrics["total_processed"]
                if self.performance_metrics["total_processed"] > 0 else 0
            ),
            "error_rate": (
                self.performance_metrics["errors"] / self.performance_metrics["total_processed"]
                if self.performance_metrics["total_processed"] > 0 else 0
            ),
            "performance_target_ms": METADATA_REMOVAL_CONFIG["performance_target_ms"],
            "meets_performance_target": avg_time <= METADATA_REMOVAL_CONFIG["performance_target_ms"]
        }

    async def _record_monitoring_metrics(
        self,
        operation_type: str,
        user_id: str,
        image_count: int,
        processing_time_ms: float,
        success: bool,
        cache_hit: bool = False,
        **kwargs
    ) -> None:
        """
        Record monitoring metrics for the operation.

        Args:
            operation_type: Type of operation (single, batch, job_handler)
            user_id: User ID
            image_count: Number of images processed
            processing_time_ms: Processing time in milliseconds
            success: Whether the operation was successful
            cache_hit: Whether this was a cache hit
            **kwargs: Additional metric data
        """
        try:
            # Import here to avoid circular imports
            from app.services.metadata_removal_monitoring import record_metadata_removal_operation

            await record_metadata_removal_operation(
                operation_type=operation_type,
                user_id=user_id,
                image_count=image_count,
                processing_time_ms=processing_time_ms,
                success=success,
                cache_hit=cache_hit,
                original_size_bytes=kwargs.get("original_size_bytes"),
                processed_size_bytes=kwargs.get("processed_size_bytes"),
                size_reduction_percent=kwargs.get("size_reduction_percent"),
                ai_tags_removed=len(kwargs.get("metadata_removed", {}).get("ai_generator_tags", [])) if kwargs.get("metadata_removed") else None,
                total_metadata_tags_removed=kwargs.get("metadata_removed", {}).get("total_tags_removed") if kwargs.get("metadata_removed") else None,
                error_message=kwargs.get("error_message")
            )
        except Exception as e:
            # Don't fail the main operation if monitoring fails
            logger.warning(f"Failed to record monitoring metrics: {str(e)}")


# Global instance
metadata_remover = ImageMetadataRemover()


async def remove_image_metadata(
    image_url: str,
    user_id: str,
    preserve_quality: bool = True,
    track_usage: bool = True
) -> Dict[str, Any]:
    """
    Convenience function to remove metadata from an image URL.

    Args:
        image_url: URL of the image to process
        user_id: User ID for usage tracking
        preserve_quality: Whether to preserve image quality
        track_usage: Whether to track add-on usage

    Returns:
        Dictionary containing processed image data and metadata
    """
    return await metadata_remover.remove_metadata_from_url(
        image_url, user_id, preserve_quality, track_usage
    )


async def remove_image_metadata_from_file(
    image_data: bytes,
    user_id: str,
    preserve_quality: bool = True,
    track_usage: bool = True
) -> Dict[str, Any]:
    """
    Convenience function to remove metadata from image file data.

    Args:
        image_data: Raw image data bytes
        user_id: User ID for usage tracking
        preserve_quality: Whether to preserve image quality
        track_usage: Whether to track add-on usage

    Returns:
        Dictionary containing processed image data and metadata
    """
    return await metadata_remover.remove_metadata_from_file(
        image_data, user_id, preserve_quality, track_usage
    )


async def batch_remove_image_metadata(
    image_urls: List[str],
    user_id: str,
    preserve_quality: bool = True,
    track_usage: bool = True
) -> List[Dict[str, Any]]:
    """
    Convenience function to remove metadata from multiple images.

    Args:
        image_urls: List of image URLs to process
        user_id: User ID for usage tracking
        preserve_quality: Whether to preserve image quality
        track_usage: Whether to track add-on usage

    Returns:
        List of processed image data dictionaries
    """
    return await metadata_remover.batch_remove_metadata(
        image_urls, user_id, preserve_quality, track_usage
    )


async def auto_remove_ai_metadata(
    image_urls: List[str],
    user_id: str,
    preserve_quality: bool = True
) -> List[str]:
    """
    Automatically remove metadata from AI-generated images and return clean URLs.

    This function is designed to be integrated into the image generation workflow
    to automatically clean AI-generated images of C2PA Content Credentials and
    other metadata.

    Args:
        image_urls: List of AI-generated image URLs
        user_id: User ID for tracking
        preserve_quality: Whether to preserve image quality

    Returns:
        List of clean image URLs (same URLs but with metadata removed)
    """
    try:
        if not image_urls:
            return []

        # Process all images to remove metadata
        results = await batch_remove_image_metadata(
            image_urls, user_id, preserve_quality, track_usage=True
        )

        # Log the metadata removal results
        total_ai_tags = sum(len(r.get("metadata_removed", {}).get("ai_generator_tags", [])) for r in results if r.get("success"))
        total_c2pa_tags = sum(len(r.get("metadata_removed", {}).get("c2pa_tags", [])) for r in results if r.get("success"))

        if total_ai_tags > 0 or total_c2pa_tags > 0:
            logger.info(f"Auto-removed metadata from {len(image_urls)} AI images: {total_ai_tags} AI tags, {total_c2pa_tags} C2PA tags")

        # Return original URLs (metadata removal is done in-place)
        return image_urls

    except Exception as e:
        logger.error(f"Error in auto metadata removal: {str(e)}")
        # Return original URLs if processing fails
        return image_urls

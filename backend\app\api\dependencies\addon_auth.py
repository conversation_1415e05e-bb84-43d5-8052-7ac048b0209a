"""
Authentication and authorization dependencies for ACE Social add-on system.
Provides secure access control for add-on features and purchases.
"""
import logging
from typing import Optional, List
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.db.database import get_database
from app.models.user import User, PurchasedAddon
from app.middleware.auth import get_current_user
# from app.services.addon_usage_tracking import usage_tracker
from app.core.redis import get_redis_client

logger = logging.getLogger(__name__)

security = HTTPBearer()


class AddonPermissionError(Exception):
    """Custom exception for add-on permission errors."""
    pass


async def require_addon_access(
    addon_id: str,
    current_user: User = Depends(get_current_user)
) -> PurchasedAddon:
    """
    Require user to have access to a specific add-on.
    Returns the user's addon record if they have access.
    """
    try:
        # Check if user has the add-on using the User model method
        user_addon = current_user.get_addon(addon_id)

        if not user_addon:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied: You don't have access to {addon_id} add-on"
            )

        # Check if add-on has expired
        if user_addon.is_expired():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied: Your {addon_id} add-on has expired"
            )

        # Check if add-on has credits remaining (for credit-based add-ons)
        if user_addon.remaining_uses is not None and user_addon.remaining_uses <= 0:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied: No credits remaining for {addon_id} add-on"
            )

        return user_addon

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking addon access for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error checking add-on access"
        )


async def require_plan_access(
    required_plans: List[str],
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Require user to have one of the specified subscription plans.
    """
    try:
        user_plan = current_user.subscription.plan_id if current_user.subscription else "creator"
        
        if user_plan not in required_plans:
            plan_names = {
                "creator": "Creator",
                "accelerator": "Accelerator", 
                "dominator": "Dominator"
            }
            
            required_plan_names = [plan_names.get(plan, plan) for plan in required_plans]
            
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied: Requires {' or '.join(required_plan_names)} plan"
            )
        
        return current_user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking plan access for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error checking plan access"
        )


async def require_credits(
    addon_id: str,
    credits_needed: int = 1,
    current_user: User = Depends(get_current_user)
) -> PurchasedAddon:
    """
    Require user to have sufficient credits for an add-on operation.
    """
    try:
        # Get user's add-on
        user_addon = await require_addon_access(addon_id, current_user)

        # Check if enough credits are available
        available_credits = user_addon.remaining_uses or 0
        if available_credits < credits_needed:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient credits: Need {credits_needed}, have {available_credits}"
            )

        return user_addon

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking credits for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error checking credits"
        )


async def consume_credits(
    user_addon: PurchasedAddon,
    credits_used: int,
    operation: str,
    metadata: Optional[dict] = None
) -> bool:
    """
    Consume credits from a user's add-on and track usage.
    """
    try:
        # Double-check credits are available
        available_credits = user_addon.remaining_uses or 0
        if available_credits < credits_used:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient credits for operation"
            )

        # Update credits using the addon's consume method
        success = user_addon.consume(credits_used)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Failed to consume credits"
            )

        # Track usage (simplified - would normally save to database)
        logger.info(f"Consumed {credits_used} credits for addon {user_addon.id}, operation: {operation}")

        # Check if credits are running low and trigger upsell
        if user_addon.remaining_uses is not None and user_addon.quantity > 0:
            usage_percentage = (user_addon.quantity - user_addon.remaining_uses) / user_addon.quantity
            if usage_percentage >= 0.75:  # 75% used
                logger.info(f"Credits running low for addon {user_addon.id}, triggering upsell")
                # Would normally trigger upsell service here

        return True

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error consuming credits: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing credit consumption"
        )


async def check_rate_limit(
    operation: str,
    limit_per_hour: int = 100,
    current_user: User = Depends(get_current_user)
) -> bool:
    """
    Check rate limits for add-on operations.
    """
    try:
        # Simplified rate limiting (would normally use Redis)
        # For now, just log and allow all operations
        logger.info(f"Rate limit check for user {current_user.id}, operation: {operation}")
        return True

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking rate limit: {str(e)}")
        return True  # Allow operation if rate limit check fails


async def require_feature_flag(
    flag_name: str,
    current_user: User = Depends(get_current_user)
) -> bool:
    """
    Require a feature flag to be enabled for the user.
    """
    try:
        from app.services.feature_flag_manager import is_feature_enabled
        
        is_enabled = await is_feature_enabled(flag_name, current_user)
        
        if not is_enabled:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Feature not available: {flag_name}"
            )
        
        return True
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking feature flag {flag_name}: {str(e)}")
        # Default to allowing access if feature flag check fails
        return True


# Convenience dependency combinations
async def require_regeneration_access(
    current_user: User = Depends(get_current_user)
) -> PurchasedAddon:
    """Require access to regeneration credits."""
    return await require_addon_access("regeneration_booster", current_user)


async def require_image_generation_access(
    current_user: User = Depends(get_current_user)
) -> PurchasedAddon:
    """Require access to image generation."""
    return await require_addon_access("image_pack_premium", current_user)


async def require_sentiment_analysis_access(
    current_user: User = Depends(get_current_user)
) -> PurchasedAddon:
    """Require access to advanced sentiment analysis."""
    return await require_addon_access("sentiment_analysis_pro", current_user)


async def require_accelerator_plan(
    current_user: User = Depends(get_current_user)
) -> User:
    """Require Accelerator or Dominator plan."""
    return await require_plan_access(["accelerator", "dominator"], current_user)


async def require_dominator_plan(
    current_user: User = Depends(get_current_user)
) -> User:
    """Require Dominator plan."""
    return await require_plan_access(["dominator"], current_user)


# Admin-only dependencies
async def require_admin_access(
    current_user: User = Depends(get_current_user)
) -> User:
    """Require admin access for add-on management."""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


# Usage tracking decorators
def track_addon_usage(addon_id: str, operation: str, credits: int = 1):
    """Decorator to automatically track add-on usage."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract user from dependencies
            current_user = None

            for arg in args:
                if isinstance(arg, User):
                    current_user = arg

            if not current_user:
                # If we can't find user, just execute function
                return await func(*args, **kwargs)

            try:
                # Check and consume credits
                user_addon = await require_credits(addon_id, credits, current_user)

                # Execute the function
                result = await func(*args, **kwargs)

                # Consume credits after successful execution
                await consume_credits(user_addon, credits, operation)

                return result

            except Exception as e:
                logger.error(f"Error in addon usage tracking: {str(e)}")
                raise

        return wrapper
    return decorator


# Error handlers for add-on specific errors
def handle_insufficient_credits(addon_id: str, credits_needed: int, credits_available: int):
    """Handle insufficient credits error."""
    return HTTPException(
        status_code=status.HTTP_402_PAYMENT_REQUIRED,
        detail={
            "error": "insufficient_credits",
            "addon_id": addon_id,
            "credits_needed": credits_needed,
            "credits_available": credits_available,
            "message": f"You need {credits_needed} credits but only have {credits_available}",
            "upgrade_url": f"/addons/{addon_id}"
        }
    )


def handle_expired_addon(addon_id: str, expired_date: str):
    """Handle expired add-on error."""
    return HTTPException(
        status_code=status.HTTP_402_PAYMENT_REQUIRED,
        detail={
            "error": "addon_expired",
            "addon_id": addon_id,
            "expired_date": expired_date,
            "message": f"Your {addon_id} add-on expired on {expired_date}",
            "renew_url": f"/addons/{addon_id}"
        }
    )


def handle_plan_upgrade_required(current_plan: str, required_plans: List[str]):
    """Handle plan upgrade required error."""
    return HTTPException(
        status_code=status.HTTP_402_PAYMENT_REQUIRED,
        detail={
            "error": "plan_upgrade_required",
            "current_plan": current_plan,
            "required_plans": required_plans,
            "message": f"This feature requires {' or '.join(required_plans)} plan",
            "upgrade_url": "/billing/plans"
        }
    )

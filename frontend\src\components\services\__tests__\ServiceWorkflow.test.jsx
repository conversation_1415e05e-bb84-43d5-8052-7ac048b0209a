// @since 2024-1-1 to 2025-25-7
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';

// Mock dependencies
jest.mock('../../../contexts/SubscriptionContext', () => ({
  useSubscription: () => ({
    subscription: { plan_name: 'Dominator' },
    hasFeatureAccess: () => true,
    updateUsage: jest.fn(),
    getRemainingCredits: () => 100,
    getPlanTier: () => 3
  })
}));

jest.mock('../../../utils/circuitBreaker', () => ({
  icpGenerationBreaker: {
    execute: jest.fn((operation) => operation())
  },
  withCircuitBreaker: jest.fn((breaker, operation, fallback) => operation())
}));

jest.mock('../../../utils/cache', () => ({
  aiResponseCache: {
    get: jest.fn(() => null),
    set: jest.fn()
  },
  workflowCache: {
    get: jest.fn(() => null),
    set: jest.fn()
  },
  cacheKey: {
    icpGeneration: jest.fn(() => 'test_key')
  }
}));

jest.mock('../../../utils/performance', () => ({
  measureApiCall: jest.fn(() => ({
    end: jest.fn()
  })),
  startTiming: jest.fn(() => 'timing_id'),
  endTiming: jest.fn()
}));

// Import components after mocks
import ServiceWorkflow from '../../../pages/services/ServiceWorkflow';
import WorkflowProvider from '../WorkflowProvider';
import ServiceDefinitionStep from '../steps/ServiceDefinitionStep';
import ICPSelectionStep from '../steps/ICPSelectionStep';

const theme = createTheme();

const renderWithTheme = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('Service Workflow Integration Tests', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
    jest.clearAllMocks();
  });

  describe('ServiceWorkflow Component', () => {
    test('renders without crashing', () => {
      renderWithTheme(<ServiceWorkflow />);
      expect(screen.getByText(/Create New Service|Edit Service/)).toBeInTheDocument();
    });

    test('displays workflow navigation', () => {
      renderWithTheme(<ServiceWorkflow />);
      expect(screen.getByText('Step 1 of 4')).toBeInTheDocument();
    });

    test('shows service definition step initially', () => {
      renderWithTheme(<ServiceWorkflow />);
      expect(screen.getByText('Define Your Service')).toBeInTheDocument();
    });
  });

  describe('WorkflowProvider', () => {
    test('provides workflow context', () => {
      const TestComponent = () => {
        const { currentStep, steps } = require('../WorkflowProvider').useWorkflow();
        return (
          <div>
            <span>Current Step: {currentStep}</span>
            <span>Total Steps: {steps.length}</span>
          </div>
        );
      };

      renderWithTheme(
        <WorkflowProvider>
          <TestComponent />
        </WorkflowProvider>
      );

      expect(screen.getByText('Current Step: 0')).toBeInTheDocument();
      expect(screen.getByText('Total Steps: 4')).toBeInTheDocument();
    });
  });

  describe('ServiceDefinitionStep', () => {
    test('renders form fields', () => {
      renderWithTheme(
        <WorkflowProvider>
          <ServiceDefinitionStep />
        </WorkflowProvider>
      );

      expect(screen.getByLabelText(/Service Name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/Service Category/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/Service Description/i)).toBeInTheDocument();
    });

    test('validates required fields', async () => {
      renderWithTheme(
        <WorkflowProvider>
          <ServiceDefinitionStep />
        </WorkflowProvider>
      );

      const submitButton = screen.getByText('Save & Continue');
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Service name is required')).toBeInTheDocument();
      });
    });

    test('saves form data on valid submission', async () => {
      renderWithTheme(
        <WorkflowProvider>
          <ServiceDefinitionStep />
        </WorkflowProvider>
      );

      // Fill out form
      fireEvent.change(screen.getByLabelText(/Service Name/i), {
        target: { value: 'Test Service' }
      });
      
      fireEvent.change(screen.getByLabelText(/Service Description/i), {
        target: { value: 'This is a comprehensive test service description that meets the minimum character requirements for validation.' }
      });

      fireEvent.change(screen.getByLabelText(/Value Proposition/i), {
        target: { value: 'Test value proposition that provides unique value to clients.' }
      });

      fireEvent.change(screen.getByLabelText(/Target Industry/i), {
        target: { value: 'Technology' }
      });

      fireEvent.change(screen.getByLabelText(/Delivery Timeline/i), {
        target: { value: '2-4 weeks' }
      });

      const submitButton = screen.getByText('Save & Continue');
      fireEvent.click(submitButton);

      await waitFor(() => {
        // Check if localStorage was called (mocked)
        expect(localStorage.setItem).toHaveBeenCalled();
      });
    });
  });

  describe('Performance and Accessibility', () => {
    test('components load within performance thresholds', async () => {
      const startTime = performance.now();

      renderWithTheme(<ServiceWorkflow />);

      await waitFor(() => {
        expect(screen.getByText(/Create New Service|Edit Service/)).toBeInTheDocument();
      });

      const loadTime = performance.now() - startTime;
      expect(loadTime).toBeLessThan(2000); // 2 second threshold
    });

    test('has proper ARIA labels', () => {
      renderWithTheme(<ServiceWorkflow />);

      // Check for proper ARIA attributes
      const navigation = screen.getByRole('navigation', { name: /workflow navigation/i });
      expect(navigation).toBeInTheDocument();
    });

    test('supports keyboard navigation', () => {
      renderWithTheme(<ServiceWorkflow />);

      const firstButton = screen.getAllByRole('button')[0];
      firstButton.focus();
      expect(document.activeElement).toBe(firstButton);
    });
  });

  describe('Error Handling', () => {
    test('displays error boundary on component crash', () => {
      const ThrowError = () => {
        throw new Error('Test error');
      };

      const ErrorBoundary = require('../ErrorBoundary').default;
      
      renderWithTheme(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
    });

    test('handles API failures gracefully', async () => {
      // Mock API failure
      const mockError = new Error('API Error');
      mockError.circuitBreakerOpen = true;

      jest.mocked(require('../../../utils/circuitBreaker').withCircuitBreaker)
        .mockRejectedValueOnce(mockError);

      renderWithTheme(
        <WorkflowProvider>
          <ICPSelectionStep />
        </WorkflowProvider>
      );

      await waitFor(() => {
        expect(screen.getByText(/AI service is temporarily unavailable/i)).toBeInTheDocument();
      });
    });
  });

  describe('Caching', () => {
    test('uses cached data when available', async () => {
      const mockCachedData = [
        {
          id: 'cached_icp',
          title: 'Cached ICP',
          demographics: { company_size: '1-10', industry: 'Tech' },
          pain_points: ['Test pain point'],
          goals: ['Test goal'],
          preferred_channels: ['LinkedIn'],
          confidence_score: 90
        }
      ];

      jest.mocked(require('../../../utils/cache').aiResponseCache.get)
        .mockReturnValueOnce(mockCachedData);

      renderWithTheme(
        <WorkflowProvider>
          <ICPSelectionStep />
        </WorkflowProvider>
      );

      await waitFor(() => {
        expect(screen.getByText('Cached ICP')).toBeInTheDocument();
      });
    });
  });
});

describe('Integration Test Suite Summary', () => {
  test('workflow completion end-to-end', async () => {
    // This test would simulate a complete workflow run
    // Due to complexity, this is a placeholder for full E2E testing
    expect(true).toBe(true);
  });
});

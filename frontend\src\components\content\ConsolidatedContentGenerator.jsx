/**
 * @fileoverview ConsolidatedContentGenerator - Enterprise-grade consolidated content generation component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @description Advanced consolidated content generator with multi-source aggregation, AI-powered synthesis,
 * and comprehensive content orchestration capabilities following ACE Social platform standards.
 */

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useImperativeHandle,
  forwardRef,
  memo
} from "react";
import PropTypes from 'prop-types';
import { useLocation } from "react-router-dom";
import {
  Box,
  Grid,
  Typography,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  TextField,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Paper,
  Card,
  CardContent,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from "@mui/material";
import { styled, alpha } from '@mui/material/styles';
import {
  AutoAwesome as AutoAwesomeIcon,
  TextFields as TextFieldsIcon,
  Settings as SettingsIcon,
  Keyboard as KeyboardIcon,
  Clear as ClearIcon,
  Analytics as AnalyticsIcon,
  SmartToy as SmartToyIcon,
  Psychology as PsychologyIcon,
  Insights as InsightsIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Visibility as VisibilityIcon,
  ExpandMore as ExpandMoreIcon
} from "@mui/icons-material";

// Enhanced context and hook imports
import { useAuth } from "../../contexts/AuthContext";
import { useNotification } from "../../contexts/NotificationContext";

// Enhanced common components
import {
  ErrorBoundary,
  EmptyState
} from "../common";
import ContentPreview from "./ContentPreview";
import AIContentSuggestions from "./AIContentSuggestions";
import CreditCounter from "../credits/CreditCounter";

// Import existing sentiment components instead of duplicate SentimentPreview
import SentimentOverviewCards from "../sentiment/SentimentOverviewCards";

// Enhanced utility imports
import {
  announceToScreenReader,
  debounce
} from "../../utils/helpers";

// Enhanced API imports
import { generateContent } from "../../api/content";
import api from "../../api";
import platformService from "../../services/platformService";

// Mock hooks for missing dependencies
const useAnalytics = () => ({
  trackEvent: () => {}
});

const useLocalStorage = (key, defaultValue) => {
  const [value, setValue] = useState(defaultValue);
  return [value, setValue];
};

const useDebounce = (callback, delay) => {
  return useCallback((...args) => debounce(callback, delay)(...args), [callback, delay]);
};

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Loading states
const LOADING_STATES = {
  IDLE: 'idle',
  GENERATING: 'generating',
  SYNTHESIZING: 'synthesizing',
  SUCCESS: 'success',
  ERROR: 'error'
};

// Content generation modes
const GENERATION_MODES = {
  AI_ASSISTED: 'ai_assisted',
  MANUAL: 'manual',
  BULK: 'bulk'
};

// Content types with enhanced metadata
const CONTENT_TYPES = {
  POST: {
    id: 'post',
    label: 'Social Media Post',
    description: 'Short-form content for social platforms',
    maxLength: 280,
    platforms: ['linkedin', 'twitter', 'facebook', 'instagram'],
    templates: ['announcement', 'question', 'tip', 'story']
  },
  ARTICLE: {
    id: 'article',
    label: 'Long-form Article',
    description: 'In-depth content for thought leadership',
    maxLength: 2000,
    platforms: ['linkedin', 'medium', 'blog'],
    templates: ['how-to', 'case-study', 'opinion', 'analysis']
  },
  VIDEO_SCRIPT: {
    id: 'video_script',
    label: 'Video Script',
    description: 'Script for video content creation',
    maxLength: 1500,
    platforms: ['youtube', 'tiktok', 'instagram', 'linkedin'],
    templates: ['tutorial', 'explainer', 'testimonial', 'demo']
  },
  CAROUSEL: {
    id: 'carousel',
    label: 'Carousel Content',
    description: 'Multi-slide visual content',
    maxLength: 150,
    platforms: ['instagram', 'linkedin', 'facebook'],
    templates: ['tips', 'steps', 'comparison', 'timeline']
  },
  THREAD: {
    id: 'thread',
    label: 'Thread Series',
    description: 'Connected series of posts',
    maxLength: 280,
    platforms: ['twitter', 'threads', 'linkedin'],
    templates: ['story', 'tutorial', 'analysis', 'tips']
  },
  STORY: {
    id: 'story',
    label: 'Story Content',
    description: 'Ephemeral content for stories',
    maxLength: 100,
    platforms: ['instagram', 'facebook', 'linkedin'],
    templates: ['behind-scenes', 'quick-tip', 'poll', 'announcement']
  }
};



// Component configuration
const COMPONENT_CONFIG = {
  MAX_CONTENT_HISTORY: 50,
  ANIMATION_DURATION: 200
};

// Content synthesis strategies
const SYNTHESIS_STRATEGIES = {
  MERGE: {
    id: 'merge',
    label: 'Merge Content',
    description: 'Combine multiple sources into unified content'
  },
  COMPARE: {
    id: 'compare',
    label: 'Compare & Contrast',
    description: 'Highlight differences and similarities'
  },
  SUMMARIZE: {
    id: 'summarize',
    label: 'Summarize',
    description: 'Create concise summary from multiple sources'
  },
  EXPAND: {
    id: 'expand',
    label: 'Expand & Elaborate',
    description: 'Add depth and detail to existing content'
  },
  OPTIMIZE: {
    id: 'optimize',
    label: 'Optimize',
    description: 'Enhance for platform-specific performance'
  }
};

// Generate platform options using centralized service
const platforms = platformService.getAllPlatforms()
  .filter(platform => ['linkedin', 'twitter', 'facebook', 'instagram', 'pinterest', 'threads', 'tiktok'].includes(platform.key))
  .map(platform => ({
    value: platform.key,
    label: platform.name,
    icon: platformService.getPlatformIcon(platform.key, { sx: { color: platform.color } }),
    color: platform.color
  }));

// Tone options
const tones = [
  { value: "professional", label: "Professional" },
  { value: "casual", label: "Casual" },
  { value: "friendly", label: "Friendly" },
  { value: "authoritative", label: "Authoritative" },
  { value: "humorous", label: "Humorous" },
  { value: "inspirational", label: "Inspirational" },
];



// ===========================
// STYLED COMPONENTS
// ===========================

// Enhanced content generator card
const ContentGeneratorCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.7)})`,
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: theme.shape.borderRadius * 3,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: COMPONENT_CONFIG.ANIMATION_DURATION,
  }),
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 8px 24px ${alpha(theme.palette.common.black, 0.12)}`,
  }
}));

// Content synthesis panel
const SynthesisPanel = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(2),
  borderLeft: `4px solid ${theme.palette.primary.main}`,
  borderRadius: theme.shape.borderRadius * 2,
  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.02)})`,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: COMPONENT_CONFIG.ANIMATION_DURATION,
  }),
  '&:hover': {
    transform: 'translateX(4px)',
    boxShadow: theme.shadows[4],
  }
}));

// ===========================
// MAIN COMPONENT
// ===========================

/**
 * ConsolidatedContentGenerator - Enterprise-grade consolidated content generation component
 *
 * @param {Object} props - Component props
 * @param {string} props.variant - Component variant (basic, advanced, enterprise, consolidated)
 * @param {boolean} props.enableAnalytics - Enable analytics tracking
 * @param {boolean} props.enableAccessibility - Enable accessibility features
 * @param {boolean} props.enableMultiSource - Enable multi-source content aggregation
 * @param {boolean} props.enableContentSynthesis - Enable AI-powered content synthesis
 * @param {boolean} props.enableBulkGeneration - Enable bulk content generation
 * @param {boolean} props.enableTemplateManagement - Enable content template management
 * @param {boolean} props.showContentHistory - Show content generation history
 * @param {boolean} props.showPerformanceMetrics - Show performance metrics
 * @param {boolean} props.showContentAnalytics - Show content analytics
 * @param {Function} props.onContentGenerated - Callback when content is generated
 * @param {Function} props.onContentSynthesized - Callback when content is synthesized
 * @param {Function} props.onTemplateApplied - Callback when template is applied
 * @param {Function} props.onError - Error callback
 * @param {string} props.userPlan - User subscription plan
 * @param {string} props.testId - Test identifier
 * @param {string} props.ariaLabel - Accessibility label
 * @param {boolean} props.announceChanges - Announce changes to screen readers
 * @param {Array} props.initialSources - Initial content sources
 * @param {Object} props.defaultSettings - Default generation settings
 */
const ConsolidatedContentGenerator = memo(forwardRef(({
  // Basic props
  variant = 'consolidated',

  // Enhanced props
  enableAnalytics = true,
  enableAccessibility = true,
  enableContentSynthesis = true,

  // Display props
  showPerformanceMetrics = true,
  showContentAnalytics = true,

  // Callback props
  onContentGenerated,
  onContentSynthesized,

  // User context props
  userPlan = 'creator',

  // Testing props
  testId = 'consolidated-content-generator',

  // Accessibility props
  ariaLabel,
  announceChanges = true,

  // Data props
  initialSources = [],
  defaultSettings = {}
}, ref) => {
  // ===========================
  // HOOKS & CONTEXT
  // ===========================

  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  // Enhanced hooks
  const { user, getFeatureLimit } = useAuth();
  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { trackEvent } = useAnalytics();

  // Local storage for preferences
  const [savedPreferences, setSavedPreferences] = useLocalStorage('consolidated-content-generator-preferences', {
    generationMode: defaultSettings.generationMode || GENERATION_MODES.AI_ASSISTED,
    synthesisStrategy: defaultSettings.synthesisStrategy || SYNTHESIS_STRATEGIES.MERGE.id,
    platform: defaultSettings.platform || 'linkedin',
    contentType: defaultSettings.contentType || 'post',
    tone: defaultSettings.tone || 'professional',
    enableBulkGeneration: defaultSettings.enableBulkGeneration || false,
    settingsOpen: !isMobile
  });

  // ===========================
  // STATE MANAGEMENT
  // ===========================

  // Core generation state
  const [loadingState, setLoadingState] = useState(LOADING_STATES.IDLE);
  const [generatedContent, setGeneratedContent] = useState(null);

  // Multi-source content state
  const [contentSources, setContentSources] = useState(initialSources || []);
  const [synthesisStrategy, setSynthesisStrategy] = useState(savedPreferences.synthesisStrategy);
  const [synthesizedContent, setSynthesizedContent] = useState(null);

  // Generation mode and settings
  const [generationMode, setGenerationMode] = useState(savedPreferences.generationMode);

  // Form state
  const [platform, setPlatform] = useState(savedPreferences.platform);
  const [contentType, setContentType] = useState(savedPreferences.contentType);
  const [tone, setTone] = useState(savedPreferences.tone);
  const [topic, setTopic] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [includeHashtags, setIncludeHashtags] = useState(true);
  const [generateImage, setGenerateImage] = useState(true);
  const [customPrompt, setCustomPrompt] = useState('');
  const [contentLength] = useState('medium');

  // UI state
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);

  // Analytics and performance state
  const [performanceMetrics, setPerformanceMetrics] = useState({
    generationTime: 0,
    synthesisTime: 0,
    successRate: 0,
    totalGenerated: 0
  });

  // Sentiment analysis state
  const [sentimentData] = useState(null);
  const [sentimentLoading] = useState(false);

  // Error handling
  const [lastError, setLastError] = useState(null);
  const [error, setError] = useState(null);

  // Usage tracking
  const [monthlyPostUsage, setMonthlyPostUsage] = useState(0);

  // Refs for performance and accessibility
  const topicInputRef = useRef(null);
  const generateButtonRef = useRef(null);

  // Debounced functions
  const debouncedTrackEvent = useDebounce(trackEvent, 300);
  const debouncedSavePreferences = useDebounce(setSavedPreferences, 1000);

  // ===========================
  // HELPER FUNCTIONS
  // ===========================

  /**
   * Handle clearing content
   */
  const handleClearContent = useCallback(() => {
    setGeneratedContent(null);
    setSynthesizedContent(null);
    setTopic('');
    setTargetAudience('');
    setCustomPrompt('');
    setLastError(null);
    setError(null);

    debouncedTrackEvent('content_cleared');

    if (topicInputRef.current) {
      topicInputRef.current.focus();
    }
  }, [debouncedTrackEvent]);

  /**
   * Handle content synthesis
   */
  const handleSynthesizeContent = useCallback(async () => {
    if (!isReadyToSynthesize) return;

    setLoadingState(LOADING_STATES.SYNTHESIZING);
    const startTime = Date.now();

    try {
      // Prepare synthesis request
      const synthesisRequest = {
        sources: contentSources,
        strategy: synthesisStrategy,
        platform: platform,
        tone: tone,
        target_length: contentLength
      };

      // Call synthesis API (mock implementation for now)
      const synthesizedResult = {
        content: `Synthesized content from ${contentSources.length} sources using ${synthesisStrategy} strategy for ${platform}`,
        sources_used: contentSources.length,
        strategy_applied: synthesisStrategy,
        confidence_score: 0.85,
        platform: platform,
        tone: tone
      };

      setSynthesizedContent(synthesizedResult);

      // Update performance metrics
      const synthesisTime = Date.now() - startTime;
      setPerformanceMetrics(prev => ({
        ...prev,
        synthesisTime,
        totalGenerated: prev.totalGenerated + 1
      }));

      setLoadingState(LOADING_STATES.SUCCESS);
      showSuccessNotification('Content synthesized successfully!');

      debouncedTrackEvent('content_synthesized', {
        synthesisTime,
        sourcesCount: contentSources.length,
        strategy: synthesisStrategy
      });

      onContentSynthesized?.(synthesizedResult, synthesisRequest);

    } catch (error) {
      console.error('Content synthesis error:', error);
      setError(error.message || 'Failed to synthesize content');
      setLoadingState(LOADING_STATES.ERROR);
      showErrorNotification(error.message || 'Failed to synthesize content');
    } finally {
      setLoadingState(LOADING_STATES.IDLE);
    }
  }, [
    isReadyToSynthesize, contentSources, synthesisStrategy, platform, tone,
    contentLength, showSuccessNotification, debouncedTrackEvent, onContentSynthesized,
    showErrorNotification
  ]);

  // Expose ref methods
  useImperativeHandle(ref, () => ({
    generateContent: () => handleSubmit(),
    synthesizeContent: () => handleSynthesizeContent(),
    clearContent: () => handleClearContent(),
    focus: () => topicInputRef.current?.focus()
  }), [handleSubmit, handleSynthesizeContent, handleClearContent]);

  // ===========================
  // COMPUTED VALUES
  // ===========================

  // Calculate content generation readiness
  const isReadyToGenerate = useMemo(() => {
    return topic.trim().length > 0 &&
           loadingState === LOADING_STATES.IDLE;
  }, [topic, loadingState]);

  // Get available content types for selected platform
  const availableContentTypes = useMemo(() => {
    return Object.values(CONTENT_TYPES).filter(type =>
      type.platforms.includes(platform)
    );
  }, [platform]);

  // Calculate synthesis readiness
  const isReadyToSynthesize = useMemo(() => {
    return enableContentSynthesis &&
           contentSources.length >= 2 &&
           loadingState === LOADING_STATES.IDLE;
  }, [enableContentSynthesis, contentSources.length, loadingState]);

  // ===========================
  // EFFECTS
  // ===========================

  // Load initial data and set up component
  useEffect(() => {
    if (initialSources.length > 0) {
      setContentSources(initialSources);
    }
  }, [initialSources]);

  // Check if we have a scheduled date from navigation state
  useEffect(() => {
    if (location.state?.isManualMode) {
      setGenerationMode(GENERATION_MODES.MANUAL);
    }

    if (location.state?.platform) {
      setPlatform(location.state.platform);
    }
  }, [location]);

  // Save preferences when they change
  useEffect(() => {
    const preferences = {
      generationMode,
      synthesisStrategy,
      platform,
      contentType,
      tone
    };

    debouncedSavePreferences(preferences);
  }, [generationMode, synthesisStrategy, platform, contentType, tone, debouncedSavePreferences]);

  // Fetch usage data when component mounts
  useEffect(() => {
    const fetchUsageData = async () => {
      try {
        const response = await api.get("/api/users/usage");
        if (response.data) {
          setMonthlyPostUsage(response.data.posts_used || 0);
        }
      } catch (error) {
        console.error("Error fetching usage data:", error);
      }
    };

    fetchUsageData();
  }, []);

  // Announce changes to screen readers
  useEffect(() => {
    if (announceChanges && enableAccessibility) {
      if (loadingState === LOADING_STATES.SUCCESS && generatedContent) {
        announceToScreenReader('Content generated successfully');
      } else if (loadingState === LOADING_STATES.ERROR && lastError) {
        announceToScreenReader(`Error: ${lastError.message}`);
      }
    }
  }, [loadingState, generatedContent, lastError, announceChanges, enableAccessibility]);

  // ===========================
  // EVENT HANDLERS
  // ===========================

  /**
   * Handle analytics tracking
   */
  const handleAnalytics = useCallback((eventName, data = {}) => {
    if (!enableAnalytics) return;

    const analyticsData = {
      ...data,
      component: 'ConsolidatedContentGenerator',
      variant,
      userPlan,
      generationMode,
      platform,
      contentType,
      tone,
      sourcesCount: contentSources.length,
      synthesisStrategy
    };

    debouncedTrackEvent(eventName, analyticsData);
  }, [enableAnalytics, variant, userPlan, generationMode, platform, contentType, tone, contentSources.length, synthesisStrategy, debouncedTrackEvent]);



  /**
   * Handle form submission for content generation
   */
  const handleSubmit = useCallback(async () => {
    setLoadingState(LOADING_STATES.GENERATING);
    setError(null);
    setGeneratedContent(null);
    setShowSuggestions(false);

    const startTime = Date.now();

    try {
      // Check if user has reached their monthly post limit
      const monthlyPostLimit = getFeatureLimit("monthly_posts");
      const currentUsage = await api
        .get("/api/users/usage")
        .then((res) => res.data?.posts_used || 0);

      if (currentUsage >= monthlyPostLimit) {
        const errorMessage = `You have reached your monthly limit of ${monthlyPostLimit} posts. Please upgrade your plan to create more content.`;
        setError(errorMessage);
        showErrorNotification("Monthly post limit reached");
        setLoadingState(LOADING_STATES.ERROR);
        return;
      }

      // Prepare request data
      const requestData = {
        topic: topic.trim(),
        tone: tone,
        platform: platform,
        content_type: contentType,
        include_hashtags: includeHashtags,
        target_audience: targetAudience || undefined,
        generate_image: generateImage,
        include_headline_on_image: true,
        custom_prompt: customPrompt || undefined,
        content_length: contentLength,
        user_preferences: {
          brand_voice: user?.branding_preferences?.brand_voice,
          brand_style: user?.branding_preferences?.style
        }
      };

      // Send request to API
      const response = await generateContent(requestData);

      // Update state with generated content
      setGeneratedContent(response);

      // Update performance metrics
      const generationTime = Date.now() - startTime;
      setPerformanceMetrics(prev => ({
        ...prev,
        generationTime,
        successRate: ((prev.successRate * 9) + 100) / 10, // Rolling average
        totalGenerated: prev.totalGenerated + 1
      }));

      setLoadingState(LOADING_STATES.SUCCESS);
      showSuccessNotification('Content generated successfully!');

      handleAnalytics('content_generated', {
        generationTime,
        contentType,
        platform,
        tone,
        hasCustomPrompt: !!customPrompt
      });

      onContentGenerated?.(response, requestData);

    } catch (error) {
      console.error('Content generation error:', error);
      setError(error.message || 'Failed to generate content');
      setLoadingState(LOADING_STATES.ERROR);
      showErrorNotification(error.message || 'Failed to generate content');
    } finally {
      setLoadingState(LOADING_STATES.IDLE);
    }
  }, [
    topic, tone, platform, contentType, includeHashtags, targetAudience,
    generateImage, customPrompt, contentLength, user,
    getFeatureLimit, showErrorNotification, showSuccessNotification,
    handleAnalytics, onContentGenerated
  ]);



  // ===========================
  // MAIN RENDER
  // ===========================

  return (
    <ErrorBoundary>
      <Box
        sx={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          bgcolor: 'background.default'
        }}
        data-testid={testId}
        aria-label={ariaLabel || "Consolidated Content Generator"}
      >
        {/* Header */}
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <SmartToyIcon color="primary" />
            Consolidated Content Generator
            {variant !== 'basic' && (
              <Chip
                label={variant.toUpperCase()}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Typography>

          {showPerformanceMetrics && (
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Chip
                icon={<TimelineIcon />}
                label={`Generated: ${performanceMetrics.totalGenerated}`}
                size="small"
                variant="outlined"
              />
              <Chip
                icon={<AssessmentIcon />}
                label={`Success Rate: ${performanceMetrics.successRate.toFixed(1)}%`}
                size="small"
                variant="outlined"
                color={performanceMetrics.successRate > 80 ? 'success' : 'warning'}
              />
              {performanceMetrics.generationTime > 0 && (
                <Chip
                  icon={<AnalyticsIcon />}
                  label={`Last Gen: ${performanceMetrics.generationTime}ms`}
                  size="small"
                  variant="outlined"
                />
              )}
            </Box>
          )}
        </Box>

        {/* Error Display */}
        {error && (
          <Alert
            severity="error"
            sx={{ mb: 2 }}
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        )}

        {/* Main Content Grid */}
        <Grid container spacing={3} sx={{ flexGrow: 1 }}>
          {/* Left Panel - Generation Settings */}
          <Grid item xs={12} md={4}>
            <ContentGeneratorCard>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SettingsIcon />
                  Generation Settings
                </Typography>

                {/* Generation Mode Selector */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Generation Mode</InputLabel>
                  <Select
                    value={generationMode}
                    onChange={(e) => setGenerationMode(e.target.value)}
                    label="Generation Mode"
                  >
                    <MenuItem value={GENERATION_MODES.AI_ASSISTED}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AutoAwesomeIcon />
                        AI Assisted
                      </Box>
                    </MenuItem>
                    <MenuItem value={GENERATION_MODES.MANUAL}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <KeyboardIcon />
                        Manual Entry
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>

                {/* Platform Selector */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Platform</InputLabel>
                  <Select
                    value={platform}
                    onChange={(e) => setPlatform(e.target.value)}
                    label="Platform"
                  >
                    {platforms.map((platformOption) => (
                      <MenuItem key={platformOption.value} value={platformOption.value}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {platformOption.icon}
                          {platformOption.label}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Content Type Selector */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Content Type</InputLabel>
                  <Select
                    value={contentType}
                    onChange={(e) => setContentType(e.target.value)}
                    label="Content Type"
                  >
                    {availableContentTypes.map((type) => (
                      <MenuItem key={type.id} value={type.id}>
                        <Box>
                          <Typography variant="body2">{type.label}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {type.description}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Tone Selector */}
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Tone</InputLabel>
                  <Select
                    value={tone}
                    onChange={(e) => setTone(e.target.value)}
                    label="Tone"
                  >
                    {tones.map((toneOption) => (
                      <MenuItem key={toneOption.value} value={toneOption.value}>
                        {toneOption.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Topic Input */}
                {generationMode === GENERATION_MODES.AI_ASSISTED && (
                  <TextField
                    ref={topicInputRef}
                    fullWidth
                    label="Topic"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    placeholder="Enter your content topic..."
                    multiline
                    rows={2}
                    sx={{ mb: 2 }}
                    helperText={`${topic.length}/500 characters`}
                    inputProps={{ maxLength: 500 }}
                  />
                )}

                {/* Target Audience */}
                <TextField
                  fullWidth
                  label="Target Audience (Optional)"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                  placeholder="e.g., Marketing professionals, Small business owners..."
                  sx={{ mb: 2 }}
                />

                {/* Options */}
                <Box sx={{ mb: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeHashtags}
                        onChange={(e) => setIncludeHashtags(e.target.checked)}
                      />
                    }
                    label="Include Hashtags"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={generateImage}
                        onChange={(e) => setGenerateImage(e.target.checked)}
                      />
                    }
                    label="Generate Image"
                  />
                </Box>

                {/* Generate Button */}
                <Button
                  ref={generateButtonRef}
                  fullWidth
                  variant="contained"
                  size="large"
                  onClick={() => handleSubmit()}
                  disabled={!isReadyToGenerate || loadingState === LOADING_STATES.GENERATING}
                  startIcon={
                    loadingState === LOADING_STATES.GENERATING ? (
                      <CircularProgress size={20} />
                    ) : (
                      <AutoAwesomeIcon />
                    )
                  }
                  sx={{ mb: 2 }}
                >
                  {loadingState === LOADING_STATES.GENERATING
                    ? 'Generating...'
                    : 'Generate Content'
                  }
                </Button>

                {/* Clear Button */}
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={handleClearContent}
                  startIcon={<ClearIcon />}
                  disabled={!generatedContent && !synthesizedContent}
                >
                  Clear Content
                </Button>
              </CardContent>
            </ContentGeneratorCard>
          </Grid>

          {/* Center Panel - Content Preview */}
          <Grid item xs={12} md={5}>
            <ContentGeneratorCard>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <VisibilityIcon />
                  Content Preview
                </Typography>

                {loadingState === LOADING_STATES.GENERATING && (
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
                    <CircularProgress size={40} sx={{ mb: 2 }} />
                    <Typography variant="body2" color="text.secondary">
                      Generating your content...
                    </Typography>
                  </Box>
                )}

                {generatedContent && (
                  <ContentPreview
                    content={generatedContent}
                    platform={platform}
                    showMetrics={showContentAnalytics}
                  />
                )}

                {synthesizedContent && (
                  <SynthesisPanel>
                    <Typography variant="subtitle1" gutterBottom>
                      Synthesized Content
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      {synthesizedContent.content}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Chip
                        size="small"
                        label={`${synthesizedContent.sources_used} sources`}
                      />
                      <Chip
                        size="small"
                        label={`${(synthesizedContent.confidence_score * 100).toFixed(0)}% confidence`}
                        color="primary"
                      />
                    </Box>
                  </SynthesisPanel>
                )}

                {!generatedContent && !synthesizedContent && loadingState === LOADING_STATES.IDLE && (
                  <EmptyState
                    icon={<TextFieldsIcon />}
                    title="No Content Generated"
                    description="Configure your settings and click 'Generate Content' to get started."
                  />
                )}
              </CardContent>
            </ContentGeneratorCard>
          </Grid>

          {/* Right Panel - Additional Features */}
          <Grid item xs={12} md={3}>
            <ContentGeneratorCard>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <InsightsIcon />
                  Insights & Tools
                </Typography>

                {/* Sentiment Analysis - Using existing enterprise component */}
                {sentimentData && (
                  <SentimentOverviewCards
                    data={sentimentData}
                    loading={sentimentLoading}
                    compact={true}
                    showOnlyOverview={true}
                    timeRange={1}
                  />
                )}

                {/* AI Suggestions */}
                {showSuggestions && (
                  <AIContentSuggestions
                    topic={topic}
                    platform={platform}
                    tone={tone}
                    onSuggestionSelect={(suggestion) => {
                      setTopic(suggestion.topic);
                      setShowSuggestions(false);
                    }}
                  />
                )}

                {/* Content Synthesis */}
                {enableContentSynthesis && (
                  <Accordion sx={{ mt: 2 }}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="subtitle2">Content Synthesis</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                        <InputLabel>Strategy</InputLabel>
                        <Select
                          value={synthesisStrategy}
                          onChange={(e) => setSynthesisStrategy(e.target.value)}
                          label="Strategy"
                        >
                          {Object.values(SYNTHESIS_STRATEGIES).map((strategy) => (
                            <MenuItem key={strategy.id} value={strategy.id}>
                              <Box>
                                <Typography variant="body2">{strategy.label}</Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {strategy.description}
                                </Typography>
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>

                      <Button
                        fullWidth
                        variant="outlined"
                        size="small"
                        onClick={handleSynthesizeContent}
                        disabled={!isReadyToSynthesize}
                        startIcon={<PsychologyIcon />}
                      >
                        Synthesize Content
                      </Button>
                    </AccordionDetails>
                  </Accordion>
                )}

                {/* Credit Counter */}
                <Box sx={{ mt: 2 }}>
                  <CreditCounter
                    currentUsage={monthlyPostUsage}
                    limit={getFeatureLimit("monthly_posts")}
                    feature="posts"
                  />
                </Box>
              </CardContent>
            </ContentGeneratorCard>
          </Grid>
        </Grid>

        {/* Keyboard Shortcuts Dialog */}
        <Dialog
          open={showKeyboardShortcuts}
          onClose={() => setShowKeyboardShortcuts(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Keyboard Shortcuts</DialogTitle>
          <DialogContent>
            <List>
              <ListItem>
                <ListItemText
                  primary="Ctrl/Cmd + Enter"
                  secondary="Generate content"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Ctrl/Cmd + K"
                  secondary="Clear content"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Ctrl/Cmd + S"
                  secondary="Save content"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Escape"
                  secondary="Close dialogs"
                />
              </ListItem>
            </List>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowKeyboardShortcuts(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ErrorBoundary>
  );
}));

// ===========================
// PROPTYPES & DISPLAY NAME
// ===========================

ConsolidatedContentGenerator.propTypes = {
  // Basic props
  variant: PropTypes.oneOf(['basic', 'advanced', 'enterprise', 'consolidated']),

  // Enhanced props
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableMultiSource: PropTypes.bool,
  enableContentSynthesis: PropTypes.bool,
  enableBulkGeneration: PropTypes.bool,
  enableTemplateManagement: PropTypes.bool,

  // Display props
  showContentHistory: PropTypes.bool,
  showPerformanceMetrics: PropTypes.bool,
  showContentAnalytics: PropTypes.bool,

  // Callback props
  onContentGenerated: PropTypes.func,
  onContentSynthesized: PropTypes.func,
  onTemplateApplied: PropTypes.func,
  onError: PropTypes.func,

  // User context props
  userPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),

  // Testing props
  testId: PropTypes.string,

  // Accessibility props
  ariaLabel: PropTypes.string,
  announceChanges: PropTypes.bool,

  // Data props
  initialSources: PropTypes.array,
  defaultSettings: PropTypes.object
};

ConsolidatedContentGenerator.defaultProps = {
  variant: 'consolidated',
  enableAnalytics: true,
  enableAccessibility: true,
  enableMultiSource: true,
  enableContentSynthesis: true,
  enableBulkGeneration: false,
  enableTemplateManagement: true,
  showContentHistory: true,
  showPerformanceMetrics: true,
  showContentAnalytics: true,
  userPlan: 'creator',
  testId: 'consolidated-content-generator',
  announceChanges: true,
  initialSources: [],
  defaultSettings: {}
};

ConsolidatedContentGenerator.displayName = 'ConsolidatedContentGenerator';

export default ConsolidatedContentGenerator;

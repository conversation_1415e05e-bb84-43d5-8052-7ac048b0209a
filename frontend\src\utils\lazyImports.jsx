import { lazy, Suspense } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

/**
 * Enhanced lazy loading utility with loading states and error boundaries
 * Implements performance optimization for bundle size reduction
 */

// Loading component with accessibility
const LoadingFallback = ({ message = 'Loading...' }) => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    minHeight="200px"
    gap={2}
    role="status"
    aria-live="polite"
    aria-label={message}
  >
    <CircularProgress size={40} />
    <Typography variant="body2" color="text.secondary">
      {message}
    </Typography>
  </Box>
);

// Enhanced lazy wrapper with error handling
export const createLazyComponent = (importFn, fallbackMessage) => {
  const LazyComponent = lazy(importFn);

  const WrappedComponent = (props) => (
    <Suspense fallback={<LoadingFallback message={fallbackMessage} />}>
      <LazyComponent {...props} />
    </Suspense>
  );

  WrappedComponent.displayName = 'LazyComponent';

  return WrappedComponent;
};

// Chart components (large libraries - lazy load)
// Note: Chart components are integrated into analytics components
// export const LazyBarChart = createLazyComponent(
//   () => import('../components/charts/BarChart'),
//   'Loading chart...'
// );

// export const LazyLineChart = createLazyComponent(
//   () => import('../components/charts/LineChart'),
//   'Loading chart...'
// );

// export const LazyPieChart = createLazyComponent(
//   () => import('../components/charts/PieChart'),
//   'Loading chart...'
// );

// export const LazyHeatmapChart = createLazyComponent(
//   () => import('../components/charts/HeatmapChart'),
//   'Loading heatmap...'
// );

// export const LazyNetworkChart = createLazyComponent(
//   () => import('../components/charts/NetworkChart'),
//   'Loading network chart...'
// );

// Analytics components
export const LazyAnalyticsDashboard = createLazyComponent(
  () => import('../pages/analytics/Overview'),
  'Loading analytics...'
);

export const LazyAdvancedAnalytics = createLazyComponent(
  () => import('../pages/analytics/AdvancedAnalytics'),
  'Loading advanced analytics...'
);

export const LazyCompetitorAnalysis = createLazyComponent(
  () => import('../pages/competitor-insights/CompetitorInsightsPage'),
  'Loading competitor analysis...'
);

// Content generation components
export const LazyContentGenerator = createLazyComponent(
  () => import('../pages/content/GeneralContentPage'),
  'Loading content generator...'
);

export const LazyImageStudio = createLazyComponent(
  () => import('../pages/content/UnifiedImageStudio'),
  'Loading image studio...'
);

export const LazyBrandIdentity = createLazyComponent(
  () => import('../pages/content/EnhancedBrandingPage'),
  'Loading brand identity...'
);

// Campaign components
export const LazyCampaignBuilder = createLazyComponent(
  () => import('../pages/campaigns/CampaignCreator'),
  'Loading campaign builder...'
);

export const LazyCampaignAnalytics = createLazyComponent(
  () => import('../pages/analytics/CampaignComparison'),
  'Loading campaign analytics...'
);

// Settings components
export const LazyAdvancedSettings = createLazyComponent(
  () => import('../pages/settings/UnifiedSettingsPage'),
  'Loading settings...'
);

export const LazyIntegrationSettings = createLazyComponent(
  () => import('../pages/settings/IntegrationSettings'),
  'Loading integrations...'
);

// Billing components
export const LazyBillingDashboard = createLazyComponent(
  () => import('../pages/billing/UnifiedBillingPage'),
  'Loading billing...'
);

export const LazyPaymentMethods = createLazyComponent(
  () => import('../pages/settings/PaymentMethods'),
  'Loading payment methods...'
);

// Team components
export const LazyTeamManagement = createLazyComponent(
  () => import('../pages/teams/TeamsPage'),
  'Loading team management...'
);

export const LazyTeamAnalytics = createLazyComponent(
  () => import('../pages/teams/TeamDetailPage'),
  'Loading team analytics...'
);

// ICP components
export const LazyICPGenerator = createLazyComponent(
  () => import('../pages/icp/ICPGenerator'),
  'Loading ICP generator...'
);

export const LazyICPAnalytics = createLazyComponent(
  () => import('../pages/analytics/ICPPerformance'),
  'Loading ICP analytics...'
);

// A/B Testing components
export const LazyABTestDashboard = createLazyComponent(
  () => import('../pages/analytics/UnifiedABTestingDashboard'),
  'Loading A/B testing...'
);

export const LazyBrandProfileTesting = createLazyComponent(
  () => import('../pages/campaigns/CreateABTest'),
  'Loading brand profile testing...'
);

// Utility function for dynamic icon loading
export const loadIcon = async (iconName) => {
  try {
    const iconModule = await import(`@mui/icons-material/${iconName}`);
    return iconModule.default;
  } catch (error) {
    console.warn(`Failed to load icon: ${iconName}`, error);
    // Return a default icon
    const { HelpOutline } = await import('@mui/icons-material');
    return HelpOutline;
  }
};

// Utility function for dynamic chart loading
// Note: Chart components are integrated into analytics components
// export const loadChartComponent = async (chartType) => {
//   const chartMap = {
//     bar: () => import('../components/charts/BarChart'),
//     line: () => import('../components/charts/LineChart'),
//     pie: () => import('../components/charts/PieChart'),
//     heatmap: () => import('../components/charts/HeatmapChart'),
//     network: () => import('../components/charts/NetworkChart'),
//   };

//   if (chartMap[chartType]) {
//     const module = await chartMap[chartType]();
//     return module.default;
//   }

//   throw new Error(`Unknown chart type: ${chartType}`);
// };

// Preload critical components
export const preloadCriticalComponents = () => {
  // Preload components that are likely to be used soon
  const criticalImports = [
    () => import('../pages/dashboard/ProductionDashboardPage'),
    () => import('../pages/content/GeneralContentPage'),
    () => import('../pages/analytics/Overview'),
  ];

  // Start preloading in the background
  criticalImports.forEach(importFn => {
    importFn().catch(error => {
      console.warn('Failed to preload component:', error);
    });
  });
};

// Bundle size monitoring
export const getBundleInfo = () => {
  if (process.env.NODE_ENV === 'development') {
    return {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      performance: {
        navigation: performance.getEntriesByType('navigation')[0],
        resources: performance.getEntriesByType('resource').length,
      },
    };
  }
  return null;
};

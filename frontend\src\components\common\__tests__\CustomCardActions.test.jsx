import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CustomCardActions from '../CustomCardActions';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
        dark: '#3A2E94',
        contrastText: '#FFFFFF',
      },
      secondary: {
        main: '#00E4BC',
        dark: '#00B89A',
        contrastText: '#000000',
      },
      error: {
        main: '#F44336',
      },
      warning: {
        main: '#FF9800',
      },
      text: {
        secondary: '#666666',
      },
      divider: '#E0E0E0',
      background: {
        paper: '#FFFFFF',
      },
      action: {
        hover: 'rgba(0, 0, 0, 0.04)',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
    transitions: {
      create: () => 'all 0.3s ease',
      duration: {
        short: 250,
      },
    },
    breakpoints: {
      down: (key) => `@media (max-width:${key === 'sm' ? 600 : 960}px)`,
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CustomCardActions', () => {
  const mockProps = {
    children: <button>Custom Action</button>
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders card actions with children correctly', () => {
    render(
      <TestWrapper>
        <CustomCardActions {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Custom Action')).toBeInTheDocument();
    expect(screen.getByRole('toolbar')).toBeInTheDocument();
  });

  test('shows divider by default', () => {
    render(
      <TestWrapper>
        <CustomCardActions {...mockProps} />
      </TestWrapper>
    );

    const divider = document.querySelector('hr');
    expect(divider).toBeInTheDocument();
  });

  test('hides divider when disabled', () => {
    render(
      <TestWrapper>
        <CustomCardActions {...mockProps} divider={false} />
      </TestWrapper>
    );

    const divider = document.querySelector('hr');
    expect(divider).not.toBeInTheDocument();
  });

  test('renders predefined actions correctly', () => {
    const actions = ['like', 'bookmark', 'share'];
    
    render(
      <TestWrapper>
        <CustomCardActions actions={actions} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Like')).toBeInTheDocument();
    expect(screen.getByLabelText('Bookmark')).toBeInTheDocument();
    expect(screen.getByLabelText('Share')).toBeInTheDocument();
  });

  test('handles custom actions with configuration', async () => {
    const user = userEvent.setup();
    const onLike = vi.fn();
    
    const actions = [
      {
        type: 'like',
        active: true,
        count: 42,
        onClick: onLike
      }
    ];

    render(
      <TestWrapper>
        <CustomCardActions actions={actions} showCounts={true} />
      </TestWrapper>
    );

    const likeButton = screen.getByLabelText('Unlike');
    expect(likeButton).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();

    await user.click(likeButton);
    expect(onLike).toHaveBeenCalled();
  });

  test('handles action clicks with analytics', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    const onClick = vi.fn();
    
    const actions = [
      {
        id: 'test-action',
        type: 'like',
        onClick
      }
    ];

    render(
      <TestWrapper>
        <CustomCardActions 
          actions={actions} 
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const actionButton = screen.getByLabelText('Like');
    await user.click(actionButton);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'CustomCardActions',
        action: 'action_click',
        actionType: 'like',
        actionId: 'test-action'
      })
    );
    expect(onClick).toHaveBeenCalled();
  });

  test('handles confirmation dialogs', async () => {
    const user = userEvent.setup();
    const onClick = vi.fn();
    
    // Mock window.confirm
    const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(true);
    
    const actions = [
      {
        type: 'delete',
        onClick,
        requiresConfirmation: true
      }
    ];

    render(
      <TestWrapper>
        <CustomCardActions 
          actions={actions} 
          enableConfirmation={true}
        />
      </TestWrapper>
    );

    const deleteButton = screen.getByLabelText('Delete');
    await user.click(deleteButton);

    expect(confirmSpy).toHaveBeenCalledWith('Are you sure you want to delete this item?');
    expect(onClick).toHaveBeenCalled();
    
    confirmSpy.mockRestore();
  });

  test('cancels action when confirmation is denied', async () => {
    const user = userEvent.setup();
    const onClick = vi.fn();
    
    // Mock window.confirm to return false
    const confirmSpy = vi.spyOn(window, 'confirm').mockReturnValue(false);
    
    const actions = [
      {
        type: 'delete',
        onClick,
        requiresConfirmation: true
      }
    ];

    render(
      <TestWrapper>
        <CustomCardActions 
          actions={actions} 
          enableConfirmation={true}
        />
      </TestWrapper>
    );

    const deleteButton = screen.getByLabelText('Delete');
    await user.click(deleteButton);

    expect(confirmSpy).toHaveBeenCalled();
    expect(onClick).not.toHaveBeenCalled();
    
    confirmSpy.mockRestore();
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <CustomCardActions loading={true} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('disables actions when disabled prop is true', async () => {
    const user = userEvent.setup();
    const onClick = vi.fn();
    
    const actions = [
      {
        type: 'like',
        onClick
      }
    ];

    render(
      <TestWrapper>
        <CustomCardActions actions={actions} disabled={true} />
      </TestWrapper>
    );

    const actionButton = screen.getByLabelText('Like');
    expect(actionButton).toBeDisabled();

    await user.click(actionButton);
    expect(onClick).not.toHaveBeenCalled();
  });

  test('handles keyboard shortcuts', async () => {
    const user = userEvent.setup();
    const onClick = vi.fn();
    
    const actions = [
      {
        type: 'like',
        onClick
      }
    ];

    render(
      <TestWrapper>
        <CustomCardActions 
          actions={actions} 
          enableKeyboardShortcuts={true}
        />
      </TestWrapper>
    );

    const toolbar = screen.getByRole('toolbar');
    toolbar.focus();
    
    // Press '1' to trigger first action
    await user.keyboard('1');
    expect(onClick).toHaveBeenCalled();
  });

  test('shows keyboard help', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CustomCardActions enableKeyboardShortcuts={true} />
      </TestWrapper>
    );

    const toolbar = screen.getByRole('toolbar');
    toolbar.focus();
    
    // Press '?' to show keyboard help
    await user.keyboard('?');

    expect(screen.getByText('Keyboard Shortcuts')).toBeInTheDocument();
    expect(screen.getByText('• 1-9: Execute actions')).toBeInTheDocument();
  });

  test('handles grouped actions', () => {
    const actions = [
      { type: 'like', group: 'primary' },
      { type: 'bookmark', group: 'secondary' },
      { type: 'share', group: 'overflow' }
    ];

    render(
      <TestWrapper>
        <CustomCardActions actions={actions} groupActions={true} />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Like')).toBeInTheDocument();
    expect(screen.getByLabelText('Bookmark')).toBeInTheDocument();
    expect(screen.getByLabelText('More actions')).toBeInTheDocument();
  });

  test('handles overflow menu', async () => {
    const user = userEvent.setup();
    
    const actions = Array.from({ length: 8 }, (_, i) => ({
      type: 'like',
      id: `action-${i}`,
      label: `Action ${i + 1}`
    }));

    render(
      <TestWrapper>
        <CustomCardActions actions={actions} maxVisibleActions={3} />
      </TestWrapper>
    );

    const overflowButton = screen.getByLabelText('More actions');
    await user.click(overflowButton);

    // Should show overflow menu
    expect(screen.getByRole('menu')).toBeInTheDocument();
  });

  test('applies different variants correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <CustomCardActions variant="elevated" />
      </TestWrapper>
    );

    let toolbar = screen.getByRole('toolbar');
    expect(toolbar).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCardActions variant="compact" />
      </TestWrapper>
    );

    toolbar = screen.getByRole('toolbar');
    expect(toolbar).toBeInTheDocument();
  });

  test('handles different justify options', () => {
    const { rerender } = render(
      <TestWrapper>
        <CustomCardActions justify="center" />
      </TestWrapper>
    );

    let toolbar = screen.getByRole('toolbar');
    expect(toolbar).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCardActions justify="flex-end" />
      </TestWrapper>
    );

    toolbar = screen.getByRole('toolbar');
    expect(toolbar).toBeInTheDocument();
  });

  test('shows tooltips when enabled', async () => {
    const user = userEvent.setup();
    
    const actions = [{ type: 'like' }];

    render(
      <TestWrapper>
        <CustomCardActions actions={actions} showTooltips={true} />
      </TestWrapper>
    );

    const actionButton = screen.getByLabelText('Like');
    await user.hover(actionButton);

    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
    });
  });

  test('handles action errors gracefully', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    const actions = [
      {
        type: 'like',
        onClick: () => {
          throw new Error('Test error');
        }
      }
    ];

    render(
      <TestWrapper>
        <CustomCardActions actions={actions} />
      </TestWrapper>
    );

    const actionButton = screen.getByLabelText('Like');
    await user.click(actionButton);

    expect(consoleSpy).toHaveBeenCalledWith('Action execution error:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CustomCardActions ariaLabel="Custom actions" role="group" />
      </TestWrapper>
    );

    const container = screen.getByRole('group');
    expect(container).toHaveAttribute('aria-label', 'Custom actions');
  });

  test('applies custom styles', () => {
    const customSx = { backgroundColor: 'red' };
    
    render(
      <TestWrapper>
        <CustomCardActions sx={customSx} />
      </TestWrapper>
    );

    const toolbar = screen.getByRole('toolbar');
    expect(toolbar).toBeInTheDocument();
  });

  test('disables spacing when configured', () => {
    render(
      <TestWrapper>
        <CustomCardActions disableSpacing={true} />
      </TestWrapper>
    );

    const toolbar = screen.getByRole('toolbar');
    expect(toolbar).toBeInTheDocument();
  });
});

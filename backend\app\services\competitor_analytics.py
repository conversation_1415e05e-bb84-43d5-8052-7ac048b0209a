"""
Competitor Social Media Analytics Service

This service fetches and analyzes competitor data from social media platforms
using the existing social media API integrations.
"""
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

import httpx
from bson import ObjectId

from app.core.config import settings
from app.models.competitor import Competitor, CompetitorSocialMedia
from app.services.social_media.factory import SocialMediaIntegrationFactory
from app.services.social_media.base import BaseSocialMediaIntegration
from app.core.database import get_database

logger = logging.getLogger(__name__)

@dataclass
class CompetitorMetrics:
    """Data class for competitor social media metrics."""
    platform: str
    account_name: str
    followers_count: int
    following_count: int
    posts_count: int
    engagement_rate: float
    avg_likes_per_post: float
    avg_comments_per_post: float
    avg_shares_per_post: float
    posting_frequency: str
    last_post_date: Optional[datetime]
    top_content_types: List[str]
    peak_posting_times: List[str]
    hashtag_usage: List[str]
    last_updated: datetime

@dataclass
class CompetitorComparison:
    """Data class for competitor comparison results."""
    competitor_id: str
    competitor_name: str
    metrics: List[CompetitorMetrics]
    overall_score: float
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]

class CompetitorAnalyticsService:
    """
    Service for fetching and analyzing competitor social media data.
    Includes methods for metrics fetching, caching, insights generation, and content analysis.
    """
    
    def __init__(self):
        # Database collections will be initialized when needed
        pass

    async def _get_db_collections(self):
        """Get database collections."""
        if not hasattr(self, '_db_initialized'):
            db = await get_database()
            self.competitors_collection = db.competitors
            self.analytics_collection = db.competitor_analytics
            self._db_initialized = True
        
    async def fetch_competitor_metrics(
        self, 
        competitor_id: str, 
        platforms: Optional[List[str]] = None
    ) -> List[CompetitorMetrics]:
        """
        Fetch social media metrics for a specific competitor.
        
        Args:
            competitor_id: The competitor's ID
            platforms: List of platforms to analyze (if None, analyze all)
            
        Returns:
            List of CompetitorMetrics for each platform
        """
        try:
            # Initialize database collections
            await self._get_db_collections()

            # Get competitor data
            competitor = await self.competitors_collection.find_one(
                {"_id": ObjectId(competitor_id)}
            )
            
            if not competitor:
                raise ValueError(f"Competitor {competitor_id} not found")
            
            metrics = []
            social_media_accounts = competitor.get("social_media", [])
            
            # Filter platforms if specified
            if platforms:
                social_media_accounts = [
                    acc for acc in social_media_accounts 
                    if acc.get("platform") in platforms
                ]
            
            # Fetch metrics for each platform
            for account in social_media_accounts:
                try:
                    platform_metrics = await self._fetch_platform_metrics(
                        account["platform"],
                        account["account_name"],
                        account["account_url"]
                    )
                    metrics.append(platform_metrics)
                except Exception as e:
                    logger.error(f"Failed to fetch metrics for {account['platform']}: {e}")
                    continue
            
            # Store metrics in database
            await self._store_competitor_metrics(competitor_id, metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error fetching competitor metrics: {e}")
            raise
    
    async def _fetch_platform_metrics(
        self, 
        platform: str, 
        account_name: str, 
        account_url: str
    ) -> CompetitorMetrics:
        """
        Fetch metrics for a specific platform account.
        
        This method uses public APIs and web scraping techniques to gather
        competitor data without requiring authentication.
        """
        try:
            if platform.lower() == "linkedin":
                return await self._fetch_linkedin_metrics(account_name, account_url)
            elif platform.lower() == "twitter":
                return await self._fetch_twitter_metrics(account_name, account_url)
            elif platform.lower() == "facebook":
                return await self._fetch_facebook_metrics(account_name, account_url)
            elif platform.lower() == "instagram":
                return await self._fetch_instagram_metrics(account_name, account_url)
            else:
                raise ValueError(f"Platform {platform} not supported")
                
        except Exception as e:
            logger.error(f"Error fetching {platform} metrics: {e}")
            # Return default metrics if API fails
            return CompetitorMetrics(
                platform=platform,
                account_name=account_name,
                followers_count=0,
                following_count=0,
                posts_count=0,
                engagement_rate=0.0,
                avg_likes_per_post=0.0,
                avg_comments_per_post=0.0,
                avg_shares_per_post=0.0,
                posting_frequency="unknown",
                last_post_date=None,
                top_content_types=[],
                peak_posting_times=[],
                hashtag_usage=[],
                last_updated=datetime.now(timezone.utc)
            )
    
    async def _fetch_linkedin_metrics(self, account_name: str, account_url: str) -> CompetitorMetrics:
        """Fetch LinkedIn company page metrics using LinkedIn API or web scraping."""
        try:
            # Use LinkedIn public API endpoints or web scraping
            # Note: This would require proper implementation based on LinkedIn's terms

            # For demo purposes, returning mock data
            # In production, implement actual LinkedIn data fetching
                return CompetitorMetrics(
                    platform="linkedin",
                    account_name=account_name,
                    followers_count=5000,  # Would be fetched from API
                    following_count=500,
                    posts_count=150,
                    engagement_rate=3.5,
                    avg_likes_per_post=25.0,
                    avg_comments_per_post=5.0,
                    avg_shares_per_post=3.0,
                    posting_frequency="weekly",
                    last_post_date=datetime.now(timezone.utc) - timedelta(days=2),
                    top_content_types=["article", "image", "video"],
                    peak_posting_times=["09:00", "13:00", "17:00"],
                    hashtag_usage=["#business", "#technology", "#innovation"],
                    last_updated=datetime.now(timezone.utc)
                )
        except Exception as e:
            logger.error(f"LinkedIn API error: {e}")
            raise
    
    async def _fetch_twitter_metrics(self, account_name: str, account_url: str) -> CompetitorMetrics:
        """Fetch Twitter metrics using Twitter API v2."""
        async with httpx.AsyncClient() as client:
            try:
                # Use Twitter API v2 with bearer token
                headers = {
                    "Authorization": f"Bearer {getattr(settings, 'TWITTER_BEARER_TOKEN', '')}",
                    "Content-Type": "application/json"
                }
                
                # Get user by username
                username = account_name.replace("@", "")
                user_response = await client.get(
                    f"https://api.twitter.com/2/users/by/username/{username}",
                    headers=headers,
                    params={
                        "user.fields": "public_metrics,created_at,description,verified"
                    }
                )
                
                if user_response.status_code != 200:
                    raise Exception(f"Twitter API error: {user_response.text}")
                
                user_data = user_response.json()["data"]
                metrics = user_data["public_metrics"]
                
                # Get recent tweets for engagement analysis
                tweets_response = await client.get(
                    f"https://api.twitter.com/2/users/{user_data['id']}/tweets",
                    headers=headers,
                    params={
                        "tweet.fields": "public_metrics,created_at,context_annotations",
                        "max_results": 100
                    }
                )
                
                tweets_data = tweets_response.json().get("data", [])
                
                # Calculate engagement metrics
                total_engagement = sum(
                    tweet["public_metrics"]["like_count"] + 
                    tweet["public_metrics"]["retweet_count"] + 
                    tweet["public_metrics"]["reply_count"]
                    for tweet in tweets_data
                )
                
                avg_engagement = total_engagement / len(tweets_data) if tweets_data else 0
                engagement_rate = (avg_engagement / metrics["followers_count"]) * 100 if metrics["followers_count"] > 0 else 0
                
                return CompetitorMetrics(
                    platform="twitter",
                    account_name=account_name,
                    followers_count=metrics["followers_count"],
                    following_count=metrics["following_count"],
                    posts_count=metrics["tweet_count"],
                    engagement_rate=engagement_rate,
                    avg_likes_per_post=sum(t["public_metrics"]["like_count"] for t in tweets_data) / len(tweets_data) if tweets_data else 0,
                    avg_comments_per_post=sum(t["public_metrics"]["reply_count"] for t in tweets_data) / len(tweets_data) if tweets_data else 0,
                    avg_shares_per_post=sum(t["public_metrics"]["retweet_count"] for t in tweets_data) / len(tweets_data) if tweets_data else 0,
                    posting_frequency=self._calculate_posting_frequency(tweets_data),
                    last_post_date=datetime.fromisoformat(tweets_data[0]["created_at"].replace("Z", "+00:00")) if tweets_data else None,
                    top_content_types=self._analyze_content_types(tweets_data),
                    peak_posting_times=self._analyze_posting_times(tweets_data),
                    hashtag_usage=self._extract_hashtags(tweets_data),
                    last_updated=datetime.now(timezone.utc)
                )
                
            except Exception as e:
                logger.error(f"Twitter API error: {e}")
                raise
    
    async def _fetch_facebook_metrics(self, account_name: str, account_url: str) -> CompetitorMetrics:
        """Fetch Facebook page metrics using Facebook Graph API."""
        # Implementation would use Facebook Graph API
        # For now, returning mock data
        return CompetitorMetrics(
            platform="facebook",
            account_name=account_name,
            followers_count=10000,
            following_count=0,  # Facebook pages don't have following
            posts_count=200,
            engagement_rate=4.2,
            avg_likes_per_post=50.0,
            avg_comments_per_post=8.0,
            avg_shares_per_post=5.0,
            posting_frequency="daily",
            last_post_date=datetime.now(timezone.utc) - timedelta(days=1),
            top_content_types=["image", "video", "link"],
            peak_posting_times=["10:00", "14:00", "19:00"],
            hashtag_usage=["#marketing", "#business", "#socialmedia"],
            last_updated=datetime.now(timezone.utc)
        )
    
    async def _fetch_instagram_metrics(self, account_name: str, account_url: str) -> CompetitorMetrics:
        """Fetch Instagram metrics using Instagram Basic Display API."""
        # Implementation would use Instagram Basic Display API
        # For now, returning mock data
        return CompetitorMetrics(
            platform="instagram",
            account_name=account_name,
            followers_count=15000,
            following_count=1000,
            posts_count=300,
            engagement_rate=5.8,
            avg_likes_per_post=150.0,
            avg_comments_per_post=20.0,
            avg_shares_per_post=10.0,
            posting_frequency="daily",
            last_post_date=datetime.now(timezone.utc) - timedelta(hours=12),
            top_content_types=["image", "video", "story"],
            peak_posting_times=["08:00", "12:00", "20:00"],
            hashtag_usage=["#lifestyle", "#brand", "#photography"],
            last_updated=datetime.now(timezone.utc)
        )
    
    def _calculate_posting_frequency(self, posts: List[Dict]) -> str:
        """Calculate posting frequency based on recent posts."""
        if not posts:
            return "unknown"
        
        # Analyze posting patterns
        post_dates = [datetime.fromisoformat(post["created_at"].replace("Z", "+00:00")) for post in posts]
        post_dates.sort(reverse=True)
        
        if len(post_dates) < 2:
            return "unknown"
        
        # Calculate average time between posts
        time_diffs = [(post_dates[i] - post_dates[i+1]).total_seconds() for i in range(len(post_dates)-1)]
        avg_diff = sum(time_diffs) / len(time_diffs)
        
        # Convert to frequency
        if avg_diff < 86400:  # Less than 1 day
            return "daily"
        elif avg_diff < 604800:  # Less than 1 week
            return "weekly"
        elif avg_diff < 2592000:  # Less than 1 month
            return "monthly"
        else:
            return "rarely"
    
    def _analyze_content_types(self, posts: List[Dict]) -> List[str]:
        """Analyze the types of content posted."""
        # This would analyze post content to determine types
        # For now, returning common types
        return ["text", "image", "video"]
    
    def _analyze_posting_times(self, posts: List[Dict]) -> List[str]:
        """Analyze peak posting times."""
        if not posts:
            return []
        
        hours = [datetime.fromisoformat(post["created_at"].replace("Z", "+00:00")).hour for post in posts]
        hour_counts = {}
        
        for hour in hours:
            hour_counts[hour] = hour_counts.get(hour, 0) + 1
        
        # Get top 3 posting hours
        top_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:3]
        return [f"{hour:02d}:00" for hour, _ in top_hours]
    
    def _extract_hashtags(self, posts: List[Dict]) -> List[str]:
        """Extract commonly used hashtags."""
        # This would analyze post text for hashtags
        # For now, returning mock hashtags
        return ["#business", "#marketing", "#socialmedia"]
    
    async def _store_competitor_metrics(self, competitor_id: str, metrics: List[CompetitorMetrics]):
        """Store competitor metrics in the database."""
        try:
            # Initialize database collections
            await self._get_db_collections()

            # Convert metrics to dict for storage
            metrics_data = [
                {
                    "platform": m.platform,
                    "account_name": m.account_name,
                    "followers_count": m.followers_count,
                    "following_count": m.following_count,
                    "posts_count": m.posts_count,
                    "engagement_rate": m.engagement_rate,
                    "avg_likes_per_post": m.avg_likes_per_post,
                    "avg_comments_per_post": m.avg_comments_per_post,
                    "avg_shares_per_post": m.avg_shares_per_post,
                    "posting_frequency": m.posting_frequency,
                    "last_post_date": m.last_post_date,
                    "top_content_types": m.top_content_types,
                    "peak_posting_times": m.peak_posting_times,
                    "hashtag_usage": m.hashtag_usage,
                    "last_updated": m.last_updated
                }
                for m in metrics
            ]
            
            # Upsert analytics data
            await self.analytics_collection.update_one(
                {"competitor_id": competitor_id},
                {
                    "$set": {
                        "competitor_id": competitor_id,
                        "metrics": metrics_data,
                        "last_updated": datetime.now(timezone.utc)
                    }
                },
                upsert=True
            )
            
        except Exception as e:
            logger.error(f"Error storing competitor metrics: {e}")
            raise
    
    async def compare_competitors(
        self, 
        competitor_ids: List[str], 
        platforms: Optional[List[str]] = None
    ) -> List[CompetitorComparison]:
        """
        Compare multiple competitors across platforms.
        
        Args:
            competitor_ids: List of competitor IDs to compare
            platforms: List of platforms to compare (if None, compare all)
            
        Returns:
            List of CompetitorComparison objects
        """
        try:
            # Initialize database collections
            await self._get_db_collections()

            comparisons = []

            for competitor_id in competitor_ids:
                # Fetch metrics for this competitor
                metrics = await self.fetch_competitor_metrics(competitor_id, platforms)

                # Get competitor name
                competitor = await self.competitors_collection.find_one(
                    {"_id": ObjectId(competitor_id)}
                )
                competitor_name = competitor.get("name", "Unknown") if competitor else "Unknown"
                
                # Calculate overall score and insights
                overall_score = self._calculate_overall_score(metrics)
                strengths, weaknesses = self._analyze_strengths_weaknesses(metrics)
                recommendations = self._generate_recommendations(metrics)
                
                comparison = CompetitorComparison(
                    competitor_id=competitor_id,
                    competitor_name=competitor_name,
                    metrics=metrics,
                    overall_score=overall_score,
                    strengths=strengths,
                    weaknesses=weaknesses,
                    recommendations=recommendations
                )
                
                comparisons.append(comparison)
            
            return comparisons
            
        except Exception as e:
            logger.error(f"Error comparing competitors: {e}")
            raise
    
    def _calculate_overall_score(self, metrics: List[CompetitorMetrics]) -> float:
        """Calculate an overall performance score for a competitor."""
        if not metrics:
            return 0.0
        
        total_score = 0.0
        for metric in metrics:
            # Weight different factors
            platform_score = (
                (metric.engagement_rate * 0.4) +
                (min(metric.followers_count / 10000, 10) * 0.3) +  # Normalize followers
                (metric.avg_likes_per_post / 100 * 0.2) +  # Normalize likes
                (len(metric.top_content_types) * 0.1)  # Content diversity
            )
            total_score += platform_score
        
        return min(total_score / len(metrics), 10.0)  # Cap at 10
    
    def _analyze_strengths_weaknesses(self, metrics: List[CompetitorMetrics]) -> Tuple[List[str], List[str]]:
        """Analyze competitor strengths and weaknesses."""
        strengths = []
        weaknesses = []
        
        for metric in metrics:
            if metric.engagement_rate > 5.0:
                strengths.append(f"High engagement rate on {metric.platform} ({metric.engagement_rate:.1f}%)")
            elif metric.engagement_rate < 2.0:
                weaknesses.append(f"Low engagement rate on {metric.platform} ({metric.engagement_rate:.1f}%)")
            
            if metric.followers_count > 10000:
                strengths.append(f"Large following on {metric.platform} ({metric.followers_count:,} followers)")
            elif metric.followers_count < 1000:
                weaknesses.append(f"Small following on {metric.platform} ({metric.followers_count:,} followers)")
            
            if metric.posting_frequency in ["daily", "weekly"]:
                strengths.append(f"Consistent posting on {metric.platform} ({metric.posting_frequency})")
            elif metric.posting_frequency in ["rarely", "unknown"]:
                weaknesses.append(f"Inconsistent posting on {metric.platform}")
        
        return strengths, weaknesses
    
    def _generate_recommendations(self, metrics: List[CompetitorMetrics]) -> List[str]:
        """Generate recommendations based on competitor analysis."""
        recommendations = []
        
        for metric in metrics:
            if metric.engagement_rate < 3.0:
                recommendations.append(f"Improve content quality on {metric.platform} to increase engagement")
            
            if metric.posting_frequency in ["rarely", "unknown"]:
                recommendations.append(f"Establish consistent posting schedule on {metric.platform}")
            
            if len(metric.top_content_types) < 2:
                recommendations.append(f"Diversify content types on {metric.platform}")
            
            if not metric.hashtag_usage:
                recommendations.append(f"Use relevant hashtags on {metric.platform} to increase discoverability")
        
        return recommendations

    async def _get_cached_metrics(
        self,
        competitor_id: str,
        platforms: Optional[List[str]] = None
    ) -> Optional[List[CompetitorMetrics]]:
        """
        Get cached competitor metrics from the database.

        Args:
            competitor_id: The competitor's ID
            platforms: List of platforms to get metrics for

        Returns:
            List of cached CompetitorMetrics or None if not found
        """
        try:
            # Initialize database collections
            await self._get_db_collections()

            # Get cached analytics data
            analytics_data = await self.analytics_collection.find_one(
                {"competitor_id": competitor_id}
            )

            if not analytics_data:
                return None

            metrics_data = analytics_data.get("metrics", [])

            # Filter by platforms if specified
            if platforms:
                metrics_data = [
                    m for m in metrics_data
                    if m.get("platform") in platforms
                ]

            # Convert to CompetitorMetrics objects
            metrics = []
            for m in metrics_data:
                metric = CompetitorMetrics(
                    platform=m.get("platform", ""),
                    account_name=m.get("account_name", ""),
                    followers_count=m.get("followers_count", 0),
                    following_count=m.get("following_count", 0),
                    posts_count=m.get("posts_count", 0),
                    engagement_rate=m.get("engagement_rate", 0.0),
                    avg_likes_per_post=m.get("avg_likes_per_post", 0.0),
                    avg_comments_per_post=m.get("avg_comments_per_post", 0.0),
                    avg_shares_per_post=m.get("avg_shares_per_post", 0.0),
                    posting_frequency=m.get("posting_frequency", "unknown"),
                    last_post_date=m.get("last_post_date"),
                    top_content_types=m.get("top_content_types", []),
                    peak_posting_times=m.get("peak_posting_times", []),
                    hashtag_usage=m.get("hashtag_usage", []),
                    last_updated=m.get("last_updated", datetime.now(timezone.utc))
                )
                metrics.append(metric)

            return metrics

        except Exception as e:
            logger.error(f"Error getting cached metrics: {e}")
            return None

    def _is_data_stale(self, metrics: List[CompetitorMetrics], max_age_hours: int = 24) -> bool:
        """
        Check if cached metrics data is stale.

        Args:
            metrics: List of CompetitorMetrics to check
            max_age_hours: Maximum age in hours before data is considered stale

        Returns:
            True if data is stale, False otherwise
        """
        if not metrics:
            return True

        # Check if any metric is older than max_age_hours
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)

        for metric in metrics:
            if metric.last_updated < cutoff_time:
                return True

        return False

    async def get_industry_benchmarks(
        self,
        platform: str,
        industry: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get industry benchmarks for a specific platform.

        Args:
            platform: The social media platform
            industry: Optional industry category for more specific benchmarks

        Returns:
            Dictionary containing industry benchmark data
        """
        try:
            # In a real implementation, this would query industry data
            # For now, returning mock benchmark data based on platform

            platform_benchmarks = {
                "linkedin": {
                    "avg_engagement": 2.5,
                    "avg_followers": 5000,
                    "avg_posting_frequency": "weekly",
                    "top_content_types": ["article", "image", "video"],
                    "recommended_posting_times": ["09:00", "13:00", "17:00"]
                },
                "twitter": {
                    "avg_engagement": 1.8,
                    "avg_followers": 3000,
                    "avg_posting_frequency": "daily",
                    "top_content_types": ["text", "image", "video"],
                    "recommended_posting_times": ["08:00", "12:00", "18:00"]
                },
                "facebook": {
                    "avg_engagement": 3.2,
                    "avg_followers": 8000,
                    "avg_posting_frequency": "daily",
                    "top_content_types": ["image", "video", "link"],
                    "recommended_posting_times": ["10:00", "14:00", "19:00"]
                },
                "instagram": {
                    "avg_engagement": 4.5,
                    "avg_followers": 12000,
                    "avg_posting_frequency": "daily",
                    "top_content_types": ["image", "video", "story"],
                    "recommended_posting_times": ["08:00", "12:00", "20:00"]
                }
            }

            benchmarks = platform_benchmarks.get(platform.lower(), {
                "avg_engagement": 2.0,
                "avg_followers": 5000,
                "avg_posting_frequency": "weekly",
                "top_content_types": ["text", "image"],
                "recommended_posting_times": ["09:00", "15:00"]
            })

            # Adjust benchmarks based on industry if provided
            if industry:
                # Industry-specific adjustments would go here
                # For now, just add industry info to response
                benchmarks["industry"] = industry

            return benchmarks

        except Exception as e:
            logger.error(f"Error getting industry benchmarks: {e}")
            raise

    async def generate_competitor_insights(
        self,
        competitor_id: str,
        timeframe: str = "30d"
    ) -> Dict[str, Any]:
        """
        Generate AI-powered insights for a specific competitor.

        Args:
            competitor_id: The competitor's ID
            timeframe: Timeframe for analysis (7d, 30d, 90d)

        Returns:
            Dictionary containing AI-generated insights and recommendations
        """
        try:
            # Get competitor metrics
            metrics = await self.fetch_competitor_metrics(competitor_id)

            if not metrics:
                raise ValueError(f"No metrics found for competitor {competitor_id}")

            # Initialize database collections
            await self._get_db_collections()

            # Get competitor info
            competitor = await self.competitors_collection.find_one(
                {"_id": ObjectId(competitor_id)}
            )

            if not competitor:
                raise ValueError(f"Competitor {competitor_id} not found")

            competitor_name = competitor.get("name", "Unknown")

            # Generate insights based on metrics
            insights = []
            trends = []
            opportunities = []
            threats = []
            action_items = []

            for metric in metrics:
                platform = metric.platform

                # Engagement insights
                if metric.engagement_rate > 5.0:
                    insights.append(f"{competitor_name} has exceptional engagement on {platform} ({metric.engagement_rate:.1f}%)")
                    threats.append(f"High competitor engagement on {platform} may attract your audience")
                elif metric.engagement_rate < 2.0:
                    opportunities.append(f"Low competitor engagement on {platform} presents opportunity for market share")

                # Follower insights
                if metric.followers_count > 50000:
                    insights.append(f"{competitor_name} has strong brand presence on {platform} with {metric.followers_count:,} followers")
                    threats.append(f"Large competitor following on {platform} indicates strong market position")

                # Content strategy insights
                if metric.posting_frequency == "daily":
                    insights.append(f"{competitor_name} maintains consistent daily posting on {platform}")
                    action_items.append(f"Consider increasing posting frequency on {platform} to match competitor")
                elif metric.posting_frequency in ["rarely", "unknown"]:
                    opportunities.append(f"Competitor posts infrequently on {platform} - opportunity to dominate")

                # Content type analysis
                if len(metric.top_content_types) > 3:
                    insights.append(f"{competitor_name} uses diverse content types on {platform}")
                    action_items.append(f"Diversify content strategy on {platform} to match competitor variety")

                # Hashtag strategy
                if metric.hashtag_usage:
                    insights.append(f"{competitor_name} actively uses hashtags on {platform}: {', '.join(metric.hashtag_usage[:3])}")
                    action_items.append(f"Analyze and adopt effective hashtag strategies on {platform}")

            # Generate trends based on timeframe
            if timeframe == "7d":
                trends.append("Short-term engagement patterns show recent activity spikes")
            elif timeframe == "30d":
                trends.append("Monthly analysis reveals consistent growth patterns")
            elif timeframe == "90d":
                trends.append("Quarterly trends indicate seasonal content strategies")

            return {
                "insights": insights,
                "trends": trends,
                "opportunities": opportunities,
                "threats": threats,
                "action_items": action_items,
                "competitor_name": competitor_name,
                "timeframe": timeframe,
                "metrics_analyzed": len(metrics)
            }

        except Exception as e:
            logger.error(f"Error generating competitor insights: {e}")
            raise

    async def analyze_competitor_content(
        self,
        competitor_id: str,
        platform: str,
        limit: int = 50
    ) -> Dict[str, Any]:
        """
        Analyze competitor's content strategy and performance.

        Args:
            competitor_id: The competitor's ID
            platform: Platform to analyze
            limit: Number of recent posts to analyze

        Returns:
            Dictionary containing content analysis results
        """
        try:
            # Initialize database collections
            await self._get_db_collections()

            # Get competitor info
            competitor = await self.competitors_collection.find_one(
                {"_id": ObjectId(competitor_id)}
            )

            if not competitor:
                raise ValueError(f"Competitor {competitor_id} not found")

            # Find the platform account
            social_media_accounts = competitor.get("social_media", [])
            platform_account = None

            for account in social_media_accounts:
                if account.get("platform", "").lower() == platform.lower():
                    platform_account = account
                    break

            if not platform_account:
                raise ValueError(f"No {platform} account found for competitor {competitor_id}")

            # In a real implementation, this would fetch and analyze actual posts
            # For now, returning mock analysis data

            content_themes = [
                "Product announcements",
                "Industry insights",
                "Company culture",
                "Customer testimonials",
                "Educational content"
            ]

            top_posts = [
                {
                    "id": "post_1",
                    "content": "Sample high-performing post content...",
                    "engagement_score": 95,
                    "likes": 150,
                    "comments": 25,
                    "shares": 12,
                    "post_type": "image",
                    "posted_at": "2024-01-15T10:00:00Z"
                },
                {
                    "id": "post_2",
                    "content": "Another successful post example...",
                    "engagement_score": 88,
                    "likes": 120,
                    "comments": 18,
                    "shares": 8,
                    "post_type": "video",
                    "posted_at": "2024-01-12T14:30:00Z"
                }
            ]

            calendar_patterns = {
                "best_days": ["Tuesday", "Wednesday", "Thursday"],
                "best_times": ["09:00", "13:00", "17:00"],
                "posting_frequency": "daily",
                "content_mix": {
                    "images": 40,
                    "videos": 30,
                    "text": 20,
                    "links": 10
                }
            }

            hashtag_strategy = {
                "most_used": ["#business", "#innovation", "#technology"],
                "avg_hashtags_per_post": 3.5,
                "hashtag_performance": {
                    "#business": {"usage": 45, "avg_engagement": 4.2},
                    "#innovation": {"usage": 32, "avg_engagement": 5.1},
                    "#technology": {"usage": 28, "avg_engagement": 3.8}
                }
            }

            engagement_patterns = {
                "avg_engagement_rate": 4.2,
                "peak_engagement_hours": ["09:00-11:00", "13:00-15:00", "17:00-19:00"],
                "engagement_by_content_type": {
                    "video": 6.5,
                    "image": 4.2,
                    "text": 2.8,
                    "link": 2.1
                }
            }

            recommendations = [
                f"Focus on video content for higher engagement on {platform}",
                f"Post during peak hours: {', '.join(calendar_patterns['best_times'])}",
                f"Use trending hashtags like {', '.join(hashtag_strategy['most_used'][:2])}",
                f"Maintain consistent posting schedule ({calendar_patterns['posting_frequency']})",
                "Engage with audience comments to boost algorithmic reach"
            ]

            return {
                "posts_count": limit,
                "content_themes": content_themes,
                "top_posts": top_posts,
                "calendar_patterns": calendar_patterns,
                "hashtag_strategy": hashtag_strategy,
                "engagement_patterns": engagement_patterns,
                "recommendations": recommendations,
                "analysis_period": f"Last {limit} posts",
                "competitor_name": competitor.get("name", "Unknown")
            }

        except Exception as e:
            logger.error(f"Error analyzing competitor content: {e}")
            raise



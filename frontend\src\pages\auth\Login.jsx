// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from "react";
import { Link as RouterLink, useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  TextField,
  Typography,
  Link,

  InputAdornment,
  IconButton,
  CircularProgress,
  Divider,
  Tabs,
  Tab,
  Chip,
  Alert,
  Card,
  CardContent,
  Fade,
  Slide,
  useTheme,
  alpha,
} from "@mui/material";
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  DeveloperMode,
  LoginRounded,
  AutoAwesome,

} from "@mui/icons-material";
import { useAuth } from "../../hooks/useAuth";
import { useAdvancedToast } from "../../hooks/useAdvancedToast";
import MagicLinkForm from "../../components/auth/MagicLinkForm";
import useWhitelabel from "../../hooks/useWhitelabel";
import Logo from "../../components/common/Logo";

const Login = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const { showSuccess, showError } = useAdvancedToast();
  const theme = useTheme();

  // Whitelabel configuration
  const {
    isEnabled: whitelabelEnabled,
    isDevelopment,

    getDevCredentials,
    getBrandingCSSVariables,
    status: whitelabelStatus,
  } = useWhitelabel();

  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Get preferred auth method from localStorage
  const [authMethod, setAuthMethod] = useState(() => {
    const savedMethod = localStorage.getItem("preferredAuthMethod");
    return savedMethod ? parseInt(savedMethod, 10) : 0; // Default to password
  });

  // Animation and UI state
  const [mounted, setMounted] = useState(false);
  const [generalError] = useState("");

  // Animation effect
  useEffect(() => {
    setMounted(true);
  }, []);

  // Auto-fill development credentials if whitelabel is enabled
  useEffect(() => {
    if (isDevelopment && whitelabelEnabled) {
      const devCreds = getDevCredentials();
      if (devCreds) {
        setFormData({
          email: devCreds.email,
          password: devCreds.password,
        });
      }
    }
  }, [isDevelopment, whitelabelEnabled, getDevCredentials]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const result = await login(formData.email, formData.password);

      if (result.success) {
        showSuccess("Login successful!");
        navigate("/dashboard");
      } else {
        // Handle different error types with user-friendly messages
        if (
          result.error &&
          result.error.includes("Connection to server timed out")
        ) {
          showError(
            "Connection to server timed out. Please check your internet connection and try again."
          );
        } else if (result.error && result.error.includes("Network error")) {
          showError(
            "Network error. The server might be down or unreachable. Please try again later."
          );
        } else {
          showError(
            result.error || "Login failed. Please check your credentials."
          );
        }
      }
    } catch (error) {
      console.error("Login error:", error);

      // Handle different error types
      if (error.name === "AbortError") {
        showError(
          "Connection to server timed out. Please try again later."
        );
      } else if (error.code === "ERR_NETWORK") {
        showError(
          "Network error. Please check your connection and try again."
        );
      } else {
        showError(
          "An unexpected error occurred. Please try again."
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // Update localStorage when auth method changes
  const handleAuthMethodChange = (e, newValue) => {
    setAuthMethod(newValue);
    localStorage.setItem("preferredAuthMethod", newValue.toString());
  };

  // Handle magic link success
  const handleMagicLinkSuccess = () => {
    showSuccess("Magic link sent! Please check your email.");
  };

  // Quick login with development credentials
  const handleQuickLogin = () => {
    const devCreds = getDevCredentials();
    if (devCreds) {
      setFormData({
        email: devCreds.email,
        password: devCreds.password,
      });
    }
  };

  // Apply branding styles
  const brandingStyles = getBrandingCSSVariables();

  return (
    <Fade in={mounted} timeout={800}>
      <Box
        sx={{
          width: "100%",
          position: "relative",
          ...brandingStyles,
        }}
      >
        {/* Modern Header with Logo and Gradient */}
        <Slide direction="down" in={mounted} timeout={600}>
          <Box sx={{ mb: 4, textAlign: "center", position: "relative" }}>
            {/* Background Gradient Effect */}
            <Box
              sx={{
                position: "absolute",
                top: -20,
                left: "50%",
                transform: "translateX(-50%)",
                width: 120,
                height: 120,
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                borderRadius: "50%",
                filter: "blur(40px)",
                zIndex: 0,
              }}
            />

            {/* Logo */}
            <Box sx={{ position: "relative", zIndex: 1, mb: 3 }}>
              <Logo size="large" />
            </Box>

            {/* Welcome Text */}
            <Typography
              variant="h3"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                mb: 1,
                position: "relative",
                zIndex: 1,
              }}
            >
              Welcome Back
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                position: "relative",
                zIndex: 1,
                maxWidth: 400,
                mx: "auto",
              }}
            >
              Sign in to your ACE Social account and continue your journey
            </Typography>
          </Box>
        </Slide>

        {/* Development Mode Indicator */}
        {isDevelopment && whitelabelEnabled && (
          <Slide direction="up" in={mounted} timeout={800}>
            <Card
              sx={{
                mb: 3,
                background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`,
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                borderRadius: 2,
              }}
            >
              <CardContent sx={{ py: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                  <Chip
                    icon={<DeveloperMode />}
                    label="Development Mode"
                    color="info"
                    variant="outlined"
                    size="small"
                  />
                  {whitelabelStatus?.admin_user_exists && (
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={handleQuickLogin}
                      sx={{ fontSize: "0.75rem" }}
                    >
                      Use Dev Credentials
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Slide>
        )}

        {/* Modern Auth Method Selector */}
        <Slide direction="up" in={mounted} timeout={1000}>
          <Card
            sx={{
              mb: 4,
              background: theme.palette.background.paper,
              borderRadius: 3,
              boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.1)}`,
              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            }}
          >
            <Tabs
              value={authMethod}
              onChange={handleAuthMethodChange}
              variant="fullWidth"
              sx={{
                "& .MuiTab-root": {
                  minHeight: 64,
                  textTransform: "none",
                  fontSize: "1rem",
                  fontWeight: 600,
                  transition: "all 0.3s ease",
                  "&:hover": {
                    background: alpha(theme.palette.primary.main, 0.05),
                  },
                },
                "& .Mui-selected": {
                  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
                  color: theme.palette.primary.main,
                },
                "& .MuiTabs-indicator": {
                  height: 3,
                  borderRadius: "3px 3px 0 0",
                  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                },
              }}
            >
              <Tab
                icon={<Lock fontSize="small" />}
                label="Password Login"
                iconPosition="start"
              />
              <Tab
                icon={<AutoAwesome fontSize="small" />}
                label="Magic Link"
                iconPosition="start"
              />
            </Tabs>
          </Card>
        </Slide>

        {/* Form Content */}
        <Slide direction="up" in={mounted} timeout={1200}>
          <Card
            sx={{
              background: theme.palette.background.paper,
              borderRadius: 3,
              boxShadow: `0 12px 40px ${alpha(theme.palette.primary.main, 0.15)}`,
              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              overflow: "hidden",
            }}
          >
            <CardContent sx={{ p: 4 }}>
              {/* General Error Alert */}
              {generalError && (
                <Fade in={!!generalError}>
                  <Alert
                    severity="error"
                    sx={{
                      mb: 3,
                      borderRadius: 2,
                      "& .MuiAlert-icon": {
                        fontSize: "1.5rem",
                      },
                    }}
                  >
                    {generalError}
                  </Alert>
                </Fade>
              )}

              {authMethod === 0 ? (
                <Box component="form" onSubmit={handleSubmit} sx={{ width: "100%" }}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    margin="normal"
                    error={!!errors.email}
                    helperText={errors.email}
                    required
                    autoFocus
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email color="action" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: 2,
                        minHeight: 56,
                        transition: "all 0.3s ease",
                        "&:hover": {
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: alpha(theme.palette.primary.main, 0.5),
                          },
                        },
                        "&.Mui-focused": {
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: theme.palette.primary.main,
                            borderWidth: 2,
                          },
                        },
                      },
                    }}
                  />

                  <TextField
                    fullWidth
                    label="Password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={handleChange}
                    margin="normal"
                    error={!!errors.password}
                    helperText={errors.password}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={handleTogglePasswordVisibility}
                            edge="end"
                            sx={{
                              color: "action.active",
                              "&:hover": {
                                color: theme.palette.primary.main,
                              },
                            }}
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        borderRadius: 2,
                        minHeight: 56,
                        transition: "all 0.3s ease",
                        "&:hover": {
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: alpha(theme.palette.primary.main, 0.5),
                          },
                        },
                        "&.Mui-focused": {
                          "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: theme.palette.primary.main,
                            borderWidth: 2,
                          },
                        },
                      },
                    }}
                  />

                  <Box sx={{ mt: 2, mb: 4, textAlign: "right" }}>
                    <Link
                      component={RouterLink}
                      to="/forgot-password"
                      variant="body2"
                      underline="hover"
                      sx={{
                        color: theme.palette.primary.main,
                        fontWeight: 500,
                        transition: "all 0.3s ease",
                        "&:hover": {
                          color: theme.palette.secondary.main,
                        },
                      }}
                    >
                      Forgot password?
                    </Link>
                  </Box>

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginRounded />}
                    sx={{
                      minHeight: 56,
                      borderRadius: 2,
                      fontSize: "1.1rem",
                      fontWeight: 600,
                      textTransform: "none",
                      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                      boxShadow: `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`,
                      transition: "all 0.3s ease",
                      "&:hover": {
                        background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
                        boxShadow: `0 12px 32px ${alpha(theme.palette.primary.main, 0.4)}`,
                        transform: "translateY(-2px)",
                      },
                      "&:disabled": {
                        background: alpha(theme.palette.action.disabled, 0.12),
                        color: theme.palette.action.disabled,
                        boxShadow: "none",
                      },
                    }}
                  >
                    {loading ? "Signing In..." : "Sign In"}
                  </Button>
                </Box>
              ) : (
                <Box sx={{ py: 2 }}>
                  <MagicLinkForm onSuccess={handleMagicLinkSuccess} />
                </Box>
              )}
            </CardContent>
          </Card>
        </Slide>

        {/* Divider with modern styling */}
        <Slide direction="up" in={mounted} timeout={1400}>
          <Box sx={{ my: 4, position: "relative" }}>
            <Divider
              sx={{
                "&::before, &::after": {
                  borderColor: alpha(theme.palette.divider, 0.3),
                },
              }}
            >
              <Chip
                label="OR"
                size="small"
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  color: theme.palette.text.secondary,
                  fontWeight: 600,
                  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                }}
              />
            </Divider>
          </Box>
        </Slide>

        {/* Sign Up Link */}
        <Slide direction="up" in={mounted} timeout={1600}>
          <Box sx={{ textAlign: "center" }}>
            <Typography variant="body1" color="text.secondary">
              Don&apos;t have an account?{" "}
              <Link
                component={RouterLink}
                to="/register"
                variant="body1"
                underline="hover"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.primary.main,
                  transition: "all 0.3s ease",
                  "&:hover": {
                    color: theme.palette.secondary.main,
                  },
                }}
              >
                Sign Up
              </Link>
            </Typography>
          </Box>
        </Slide>
      </Box>
    </Fade>
  );
};

export default Login;

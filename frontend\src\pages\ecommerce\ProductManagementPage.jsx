// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  Alert,
  CircularProgress,
  Checkbox,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  alpha,
  useTheme
} from '@mui/material';
import {
  Search as SearchIcon,
  ViewModule as GridViewIcon,
  ViewList as ListViewIcon,
  ShoppingCart as ProductIcon,
  Create as CreateContentIcon,
  Campaign as CampaignIcon,
  Psychology as ICPIcon,
  Inventory as InventoryIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../api';

const ProductManagementPage = () => {
  const theme = useTheme();
  const { storeId } = useParams();
  const { hasFeature } = useAuth();
  
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [viewMode, setViewMode] = useState('grid');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedProducts, setSelectedProducts] = useState(new Set());
  const [contentDialogOpen, setContentDialogOpen] = useState(false);
  const [icpDialogOpen, setIcpDialogOpen] = useState(false);
  const [campaignDialogOpen, setCampaignDialogOpen] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [generating, setGenerating] = useState(false);

  const itemsPerPage = 12;

  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      setError('');

      const params = new URLSearchParams({
        limit: itemsPerPage.toString(),
        offset: ((page - 1) * itemsPerPage).toString()
      });

      // Add filters to params
      if (searchTerm) params.append('search', searchTerm);
      if (categoryFilter) params.append('category', categoryFilter);
      if (statusFilter) params.append('status', statusFilter);

      const response = await api.get(`/api/ecommerce/stores/${storeId}/products?${params}`);

      // Handle different response formats
      const productsData = response.data?.products || response.data || [];
      const total = response.data?.total || productsData.length;

      setProducts(Array.isArray(productsData) ? productsData : []);
      setTotalPages(Math.ceil(total / itemsPerPage));

    } catch (error) {
      console.error('Error fetching products:', error);
      if (error.response?.status === 403) {
        setError('Product access not available in your plan');
      } else if (error.response?.status === 404) {
        setError('Store not found or no products available');
      } else {
        setError(error.response?.data?.detail || 'Failed to load products');
      }
    } finally {
      setLoading(false);
    }
  }, [storeId, page, searchTerm, categoryFilter, statusFilter, itemsPerPage]);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  const handleProductSelect = (productId) => {
    setSelectedProducts(prev => {
      const newSet = new Set(prev);
      if (newSet.has(productId)) {
        newSet.delete(productId);
      } else {
        newSet.add(productId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedProducts.size === products.length) {
      setSelectedProducts(new Set());
    } else {
      setSelectedProducts(new Set(products.map(p => p.id)));
    }
  };

  const handleGenerateContent = async (contentType = 'social_post') => {
    if (selectedProducts.size === 0) {
      setError('Please select at least one product');
      return;
    }

    setGenerating(true);
    setError('');

    try {
      // First create a campaign for the selected products
      const campaignData = {
        name: `Product Campaign - ${new Date().toLocaleDateString()}`,
        description: `Campaign for ${selectedProducts.size} selected products`,
        platforms: ['instagram', 'facebook'],
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
        budget: 1000,
        goals: [{
          type: 'engagement',
          target_value: 1000,
          metric: 'likes'
        }]
      };

      const campaignResponse = await api.post('/api/ecommerce/campaigns/product-campaign', {
        campaign_data: campaignData,
        product_ids: Array.from(selectedProducts).map(id =>
          products.find(p => p.id === id)?.external_product_id
        ),
        store_id: storeId
      });

      if (campaignResponse.data.success) {
        const campaignId = campaignResponse.data.campaign_id;

        // Now generate content for the campaign
        const contentRequests = Array.from(selectedProducts).map(productId => {
          const product = products.find(p => p.id === productId);
          return {
            product_id: productId,
            topic: `Promote ${product.title}`,
            platform: 'instagram',
            content_type: contentType,
            tone: 'engaging',
            include_pricing: true,
            call_to_action: 'Shop now'
          };
        });

        const contentResponse = await api.post(`/api/ecommerce/campaigns/${campaignId}/generate-content`, {
          content_requests: contentRequests
        });

        if (contentResponse.data.success) {
          setSuccess(`Generated ${contentResponse.data.count} pieces of content for campaign!`);
          setContentDialogOpen(false);
          setSelectedProducts(new Set());
        }
      }
    } catch (error) {
      console.error('Error generating content:', error);
      if (error.response?.status === 403) {
        setError('Content generation not available in your plan');
      } else {
        setError(error.response?.data?.detail || 'Failed to generate content');
      }
    } finally {
      setGenerating(false);
    }
  };

  const handleGenerateICPs = async () => {
    if (selectedProducts.size === 0) {
      setError('Please select at least one product');
      return;
    }

    setGenerating(true);
    setError('');

    try {
      const response = await api.post(`/api/ecommerce/stores/${storeId}/products/generate-icps`, {
        product_ids: Array.from(selectedProducts).map(id =>
          products.find(p => p.id === id)?.external_product_id
        ).filter(Boolean), // Remove any undefined values
        count: 3
      });

      if (response.data.success) {
        setSuccess(`Generated ${response.data.count} ICPs!`);
        setIcpDialogOpen(false);
        setSelectedProducts(new Set());
      }
    } catch (error) {
      console.error('Error generating ICPs:', error);
      if (error.response?.status === 403) {
        setError('ICP generation not available in your plan');
      } else {
        setError(error.response?.data?.detail || 'Failed to generate ICPs');
      }
    } finally {
      setGenerating(false);
    }
  };

  const handleCreateCampaign = async (campaignData) => {
    if (selectedProducts.size === 0) {
      setError('Please select at least one product');
      return;
    }

    setGenerating(true);
    setError('');

    try {
      const response = await api.post('/api/ecommerce/campaigns/product-campaign', {
        campaign_data: campaignData,
        product_ids: Array.from(selectedProducts).map(id =>
          products.find(p => p.id === id)?.external_product_id
        ).filter(Boolean),
        store_id: storeId
      });

      if (response.data.success) {
        setSuccess(`Campaign "${campaignData.name}" created successfully!`);
        setCampaignDialogOpen(false);
        setSelectedProducts(new Set());
      }
    } catch (error) {
      console.error('Error creating campaign:', error);
      if (error.response?.status === 403) {
        setError('Campaign creation not available in your plan');
      } else {
        setError(error.response?.data?.detail || 'Failed to create campaign');
      }
    } finally {
      setGenerating(false);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !categoryFilter || product.category === categoryFilter;
    const matchesStatus = !statusFilter || product.status === statusFilter;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const categories = [...new Set(products.map(p => p.category).filter(Boolean))];
  const statuses = [...new Set(products.map(p => p.status))];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 1, sm: 2 } }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          mb: 3,
          bgcolor: alpha(theme.palette.primary.main, 0.05),
          borderRadius: 2,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        }}
      >
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center" gap={2}>
            <ProductIcon color="primary" sx={{ fontSize: 32 }} />
            <Box>
              <Typography variant="h5" fontWeight="bold">
                Product Management
              </Typography>
              <Typography color="text.secondary">
                Manage your e-commerce products and generate content
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Bulk Actions */}
        {selectedProducts.size > 0 && (
          <Box display="flex" alignItems="center" gap={2} mt={2}>
            <Typography variant="body2" color="text.secondary">
              {selectedProducts.size} product(s) selected
            </Typography>
            <Button
              size="small"
              startIcon={generating ? <CircularProgress size={16} /> : <CreateContentIcon />}
              onClick={() => setContentDialogOpen(true)}
              disabled={!hasFeature('content_generation') || generating}
            >
              Generate Content
            </Button>
            <Button
              size="small"
              startIcon={generating ? <CircularProgress size={16} /> : <ICPIcon />}
              onClick={() => setIcpDialogOpen(true)}
              disabled={!hasFeature('icp_generation') || generating}
            >
              Generate ICPs
            </Button>
            <Button
              size="small"
              startIcon={generating ? <CircularProgress size={16} /> : <CampaignIcon />}
              onClick={() => setCampaignDialogOpen(true)}
              disabled={!hasFeature('campaign_management') || generating}
            >
              Create Campaign
            </Button>
          </Box>
        )}
      </Paper>

      {/* Alerts */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Filters and Search */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="Category"
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map(category => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="Status"
              >
                <MenuItem value="">All Statuses</MenuItem>
                {statuses.map(status => (
                  <MenuItem key={status} value={status}>
                    {status.replace('_', ' ')}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={selectedProducts.size === products.length && products.length > 0}
                    indeterminate={selectedProducts.size > 0 && selectedProducts.size < products.length}
                    onChange={handleSelectAll}
                  />
                }
                label="Select All"
              />
              <Box>
                <Button
                  variant={viewMode === 'grid' ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => setViewMode('grid')}
                  sx={{ mr: 1 }}
                >
                  <GridViewIcon />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => setViewMode('list')}
                >
                  <ListViewIcon />
                </Button>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Products Grid/List */}
      {filteredProducts.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <InventoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No products found
          </Typography>
          <Typography color="text.secondary">
            Try adjusting your search or filter criteria.
          </Typography>
        </Paper>
      ) : (
        <>
          <Grid container spacing={2}>
            {filteredProducts.map((product) => (
              <Grid item xs={12} sm={6} md={viewMode === 'grid' ? 4 : 12} key={product.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: viewMode === 'grid' ? 'column' : 'row',
                    cursor: 'pointer',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: theme.shadows[4],
                    },
                  }}
                  onClick={() => handleProductSelect(product.id)}
                >
                  <Checkbox
                    checked={selectedProducts.has(product.id)}
                    sx={{ position: 'absolute', top: 8, left: 8, zIndex: 1 }}
                  />
                  
                  {product.featured_image && (
                    <CardMedia
                      component="img"
                      sx={{
                        width: viewMode === 'grid' ? '100%' : 120,
                        height: viewMode === 'grid' ? 200 : 120,
                        objectFit: 'cover'
                      }}
                      image={product.featured_image}
                      alt={product.title}
                    />
                  )}
                  
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                      <Chip
                        label={(product.status || 'unknown').replace('_', ' ')}
                        color={product.status === 'active' ? 'success' : 'default'}
                        size="small"
                      />
                      <Typography variant="h6" color="primary">
                        ${product.price || '0.00'}
                      </Typography>
                    </Box>

                    <Typography variant="h6" gutterBottom noWrap title={product.title}>
                      {product.title || 'Untitled Product'}
                    </Typography>
                    
                    {product.description && (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                        }}
                      >
                        {product.description}
                      </Typography>
                    )}
                    
                    <Box mt={2}>
                      <Grid container spacing={1}>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">
                            SKU: {product.sku || 'N/A'}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="caption" color="text.secondary">
                            Stock: {product.inventory_quantity ?? 'N/A'}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                    
                    {product.tags && product.tags.length > 0 && (
                      <Box mt={1}>
                        {product.tags.slice(0, 3).map(tag => (
                          <Chip
                            key={tag}
                            label={tag}
                            size="small"
                            variant="outlined"
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        ))}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Pagination */}
          <Box display="flex" justifyContent="center" mt={4}>
            <Pagination
              count={totalPages}
              page={page}
              onChange={(e, value) => setPage(value)}
              color="primary"
            />
          </Box>
        </>
      )}

      {/* Content Generation Dialog */}
      <Dialog open={contentDialogOpen} onClose={() => !generating && setContentDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Generate Content</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            Generate social media content for {selectedProducts.size} selected product(s).
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This will create a campaign and generate engaging content for each selected product.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setContentDialogOpen(false)} disabled={generating}>
            Cancel
          </Button>
          <Button
            onClick={() => handleGenerateContent()}
            variant="contained"
            disabled={generating}
            startIcon={generating ? <CircularProgress size={20} /> : null}
          >
            {generating ? 'Generating...' : 'Generate Content'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* ICP Generation Dialog */}
      <Dialog open={icpDialogOpen} onClose={() => !generating && setIcpDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Generate ICPs</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            Generate Ideal Customer Profiles based on {selectedProducts.size} selected product(s).
          </Typography>
          <Typography variant="body2" color="text.secondary">
            AI will analyze your products to create detailed customer profiles for targeted marketing.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIcpDialogOpen(false)} disabled={generating}>
            Cancel
          </Button>
          <Button
            onClick={handleGenerateICPs}
            variant="contained"
            disabled={generating}
            startIcon={generating ? <CircularProgress size={20} /> : null}
          >
            {generating ? 'Generating...' : 'Generate ICPs'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Campaign Creation Dialog */}
      <CampaignCreationDialog
        open={campaignDialogOpen}
        onClose={() => !generating && setCampaignDialogOpen(false)}
        onSubmit={handleCreateCampaign}
        selectedProductsCount={selectedProducts.size}
        generating={generating}
      />
    </Box>
  );
};

// Campaign Creation Dialog Component
const CampaignCreationDialog = ({ open, onClose, onSubmit, selectedProductsCount, generating }) => {
  const [campaignName, setCampaignName] = useState('');
  const [campaignDescription, setCampaignDescription] = useState('');
  const [budget, setBudget] = useState(1000);
  const [duration, setDuration] = useState(30);
  const [platforms, setPlatforms] = useState(['instagram', 'facebook']);

  const handleSubmit = () => {
    if (!campaignName.trim()) {
      return;
    }

    const campaignData = {
      name: campaignName,
      description: campaignDescription || `Campaign for ${selectedProductsCount} selected products`,
      platforms,
      start_date: new Date().toISOString(),
      end_date: new Date(Date.now() + duration * 24 * 60 * 60 * 1000).toISOString(),
      budget,
      goals: [{
        type: 'engagement',
        target_value: Math.floor(budget * 0.1), // 10% of budget as engagement target
        metric: 'likes'
      }]
    };

    onSubmit(campaignData);
  };

  const handleClose = () => {
    if (!generating) {
      setCampaignName('');
      setCampaignDescription('');
      setBudget(1000);
      setDuration(30);
      setPlatforms(['instagram', 'facebook']);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Create Product Campaign</DialogTitle>
      <DialogContent>
        <Typography paragraph>
          Create a marketing campaign for {selectedProductsCount} selected product(s).
        </Typography>

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Campaign Name"
              value={campaignName}
              onChange={(e) => setCampaignName(e.target.value)}
              required
              disabled={generating}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              value={campaignDescription}
              onChange={(e) => setCampaignDescription(e.target.value)}
              multiline
              rows={3}
              disabled={generating}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Budget ($)"
              type="number"
              value={budget}
              onChange={(e) => setBudget(Number(e.target.value))}
              disabled={generating}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Duration (days)"
              type="number"
              value={duration}
              onChange={(e) => setDuration(Number(e.target.value))}
              disabled={generating}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth disabled={generating}>
              <InputLabel>Platforms</InputLabel>
              <Select
                multiple
                value={platforms}
                onChange={(e) => setPlatforms(e.target.value)}
                label="Platforms"
              >
                <MenuItem value="instagram">Instagram</MenuItem>
                <MenuItem value="facebook">Facebook</MenuItem>
                <MenuItem value="twitter">Twitter</MenuItem>
                <MenuItem value="linkedin">LinkedIn</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={generating}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={!campaignName.trim() || generating}
          startIcon={generating ? <CircularProgress size={20} /> : null}
        >
          {generating ? 'Creating...' : 'Create Campaign'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductManagementPage;

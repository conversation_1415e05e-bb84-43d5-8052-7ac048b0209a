/**
 * StablePageWrapper Component - Enterprise-grade stable page wrapper for ACE Social platform
 * Features: Advanced page stability patterns, intelligent layout handling, dynamic wrapper adaptation,
 * stable layout controls, smart content management, adaptive wrapper layouts, contextual page states, accessibility-focused wrapper controls, responsive wrapper patterns, and production-ready wrapper functionality
 @since 2024-1-1 to 2025-25-7
*/

import { memo, useState, useEffect, useCallback, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Box,
  useTheme,
  alpha,
  Typography,
  IconButton,
  Tooltip,
  Fab,
  Zoom,
  Fade,
  Alert,
  Snackbar,
  LinearProgress,
  useMediaQuery,
  Breadcrumbs,
  Link,
  Stack,
  Button,
  Menu,
  MenuItem,
  ListItemText
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import {
  KeyboardArrowUp as ScrollTopIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  Error as ErrorIcon,
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon
} from '@mui/icons-material';

// Enhanced animations for enterprise-grade stable page wrapper
const fadeInAnimation = keyframes`
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
`;

// Enhanced animations for enterprise-grade stable page wrapper (simplified for production)

const shimmerAnimation = keyframes`
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
`;

// Enhanced styled components for enterprise-grade stable page wrapper
const StyledPageContainer = styled(Container)(({ theme, variant, isAnimated, hasError }) => ({
  position: 'relative',
  transition: theme.transitions.create(['all'], {
    duration: theme.transitions.duration.standard
  }),
  ...(variant === 'glassmorphic' && {
    '&::before': {
      content: '""',
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `linear-gradient(135deg,
        ${alpha(theme.palette.primary.main, 0.05)} 0%,
        ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
      zIndex: -1,
      pointerEvents: 'none'
    }
  }),
  ...(hasError && {
    '&::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: alpha(theme.palette.error.main, 0.05),
      zIndex: 1,
      pointerEvents: 'none'
    }
  }),
  ...(isAnimated && {
    animation: `${fadeInAnimation} 0.6s ease-out`
  })
}));

const StyledPagePaper = styled(Paper)(({ theme, isFullscreen, hasGlassMorphism, isLoading }) => ({
  position: 'relative',
  transition: theme.transitions.create(['all'], {
    duration: theme.transitions.duration.standard
  }),
  ...(isFullscreen && {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: theme.zIndex.modal,
    borderRadius: 0,
    margin: 0
  }),
  ...(hasGlassMorphism && {
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.8)} 0%,
      ${alpha(theme.palette.background.default, 0.4)} 100%)`,
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.1)}`
  }),
  ...(isLoading && {
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.primary.main, 0.1)}, transparent)`,
      animation: `${shimmerAnimation} 2s infinite`,
      zIndex: 1
    }
  }),
  // Enhanced accessibility
  '&:focus-visible': {
    outline: `3px solid ${theme.palette.primary.main}`,
    outlineOffset: '2px'
  },
  // Enhanced responsive design
  [theme.breakpoints.down('sm')]: {
    margin: theme.spacing(0.5),
    borderRadius: theme.spacing(1)
  },
  // Enhanced high contrast mode
  '@media (prefers-contrast: high)': {
    border: `3px solid ${theme.palette.text.primary}`,
    background: theme.palette.background.paper,
    backdropFilter: 'none',
    boxShadow: `0 4px 8px 0 ${theme.palette.text.primary}`
  },
  // Enhanced print styles
  '@media print': {
    background: 'white !important',
    backdropFilter: 'none !important',
    boxShadow: 'none !important',
    border: '2px solid black !important',
    borderRadius: '0 !important',
    margin: '0 !important',
    padding: '20px !important'
  }
}));

const StyledScrollTopFab = styled(Fab)(({ theme, isVisible }) => ({
  position: 'fixed',
  bottom: theme.spacing(2),
  right: theme.spacing(2),
  zIndex: theme.zIndex.speedDial,
  transition: theme.transitions.create(['all'], {
    duration: theme.transitions.duration.standard
  }),
  transform: isVisible ? 'scale(1)' : 'scale(0)',
  opacity: isVisible ? 1 : 0
}));

// Stable page wrapper constants and configurations
const WRAPPER_VARIANTS = {
  STANDARD: 'standard',
  GLASSMORPHIC: 'glassmorphic',
  MINIMAL: 'minimal',
  ENHANCED: 'enhanced'
};

const WRAPPER_LAYOUTS = {
  FLUID: 'fluid',
  FIXED: 'fixed',
  CENTERED: 'centered',
  SIDEBAR: 'sidebar'
};

// Wrapper themes and configurations (simplified for production)

const PLAN_WRAPPER_FEATURES = {
  creator: {
    maxPages: 50,
    advancedLayouts: false,
    customization: false,
    analytics: false,
    fullscreen: true
  },
  accelerator: {
    maxPages: 200,
    advancedLayouts: true,
    customization: true,
    analytics: true,
    fullscreen: true
  },
  dominator: {
    maxPages: 1000,
    advancedLayouts: true,
    customization: true,
    analytics: true,
    fullscreen: true
  }
};

const WRAPPER_ANALYTICS_EVENTS = {
  PAGE_LOAD: 'stable_page_wrapper_load',
  PAGE_SCROLL: 'stable_page_wrapper_scroll',
  FULLSCREEN_TOGGLE: 'stable_page_wrapper_fullscreen',
  LAYOUT_CHANGE: 'stable_page_wrapper_layout_change',
  ERROR_BOUNDARY: 'stable_page_wrapper_error'
};

/**
 * Enhanced enterprise-grade stable page wrapper component with comprehensive page stability patterns,
 * intelligent layout handling, dynamic wrapper adaptation, and production-ready wrapper functionality
 */
const StablePageWrapper = memo(({
  children,
  maxWidth = 'xl',
  disableGutters = false,
  enableGlassMorphism = true,
  variant = WRAPPER_VARIANTS.STANDARD,
  layout = WRAPPER_LAYOUTS.FLUID,
  enableAnalytics = true,
  enableAccessibility = true,
  enableAnimations = true,
  enableScrollTop = true,
  enableBreadcrumbs = false,
  enableErrorBoundary = true,
  showLoadingState = false,
  title,
  breadcrumbs = [],
  onAnalytics,
  onLayoutChange,
  userPlan = 'creator',
  sx = {},
  testId
}) => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Enhanced state management
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isLoading] = useState(showLoadingState);
  const [error, setError] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('info');
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [, setPageMetrics] = useState({
    loadTime: null,
    scrollDepth: 0,
    interactions: 0
  });

  // Refs for performance optimization
  const containerRef = useRef(null);
  const contentRef = useRef(null);
  const scrollTimeoutRef = useRef(null);

  // Plan-based limits
  const planLimits = useMemo(() => {
    return PLAN_WRAPPER_FEATURES[userPlan] || PLAN_WRAPPER_FEATURES.creator;
  }, [userPlan]);

  // Analytics tracking
  const trackAnalytics = useCallback((action, data = {}) => {
    if (!enableAnalytics || !onAnalytics) return;

    onAnalytics({
      component: 'StablePageWrapper',
      action,
      variant,
      layout,
      timestamp: Date.now(),
      path: location.pathname,
      userPlan,
      isMobile,
      ...data
    });
  }, [enableAnalytics, onAnalytics, variant, layout, location.pathname, userPlan, isMobile]);

  // Enhanced scroll handling
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;

    // Show/hide scroll to top button
    setShowScrollTop(scrollTop > 300);

    // Calculate scroll depth
    const scrollDepth = Math.round((scrollTop / (documentHeight - windowHeight)) * 100);

    // Update metrics
    setPageMetrics(prev => ({
      ...prev,
      scrollDepth: Math.max(prev.scrollDepth, scrollDepth)
    }));

    // Throttled analytics tracking
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    scrollTimeoutRef.current = setTimeout(() => {
      trackAnalytics(WRAPPER_ANALYTICS_EVENTS.PAGE_SCROLL, {
        scrollDepth,
        scrollTop
      });
    }, 1000);
  }, [trackAnalytics]);

  // Enhanced fullscreen handling
  const handleFullscreenToggle = useCallback(() => {
    if (!planLimits.fullscreen) {
      setSnackbarMessage('Fullscreen mode requires a higher plan');
      setSnackbarSeverity('warning');
      setSnackbarOpen(true);
      return;
    }

    setIsFullscreen(prev => {
      const newState = !prev;
      trackAnalytics(WRAPPER_ANALYTICS_EVENTS.FULLSCREEN_TOGGLE, {
        isFullscreen: newState
      });
      return newState;
    });
  }, [planLimits.fullscreen, trackAnalytics]);

  // Enhanced scroll to top
  const handleScrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    trackAnalytics('scroll_to_top', {
      fromPosition: window.pageYOffset
    });
  }, [trackAnalytics]);

  // Enhanced error handling (simplified for production)

  // Enhanced layout change handler
  const handleLayoutChange = useCallback((newLayout) => {
    if (onLayoutChange) {
      onLayoutChange(newLayout);
    }

    trackAnalytics(WRAPPER_ANALYTICS_EVENTS.LAYOUT_CHANGE, {
      fromLayout: layout,
      toLayout: newLayout
    });
  }, [layout, onLayoutChange, trackAnalytics]);

  // Effects
  useEffect(() => {
    const startTime = Date.now();

    // Track page load
    trackAnalytics(WRAPPER_ANALYTICS_EVENTS.PAGE_LOAD, {
      path: location.pathname,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    });

    // Set load time
    setPageMetrics(prev => ({
      ...prev,
      loadTime: Date.now() - startTime
    }));

    // Add scroll listener
    if (enableScrollTop) {
      window.addEventListener('scroll', handleScroll, { passive: true });
    }

    return () => {
      if (enableScrollTop) {
        window.removeEventListener('scroll', handleScroll);
      }
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [location.pathname, enableScrollTop, handleScroll, trackAnalytics]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'f':
            if (planLimits.fullscreen) {
              e.preventDefault();
              handleFullscreenToggle();
            }
            break;
          case 'Home':
            e.preventDefault();
            handleScrollToTop();
            break;
        }
      }

      if (e.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (enableAccessibility) {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [enableAccessibility, planLimits.fullscreen, handleFullscreenToggle, handleScrollToTop, isFullscreen]);

  // Enhanced glass morphism styles
  const glassMorphismStyles = useMemo(() => {
    if (!enableGlassMorphism) return {};

    return {
      background: `linear-gradient(135deg,
        ${alpha(theme.palette.background.paper, 0.8)} 0%,
        ${alpha(theme.palette.background.default, 0.4)} 100%)`,
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
      borderRadius: theme.spacing(2),
      boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.1)}`
    };
  }, [enableGlassMorphism, theme]);

  // Error boundary wrapper
  if (error && enableErrorBoundary) {
    return (
      <StyledPageContainer
        maxWidth={maxWidth}
        disableGutters={disableGutters}
        variant={variant}
        hasError={true}
        ref={containerRef}
        data-testid={testId}
      >
        <StyledPagePaper
          elevation={0}
          variant={variant}
          hasGlassMorphism={enableGlassMorphism}
          sx={{
            flex: 1,
            p: theme.spacing(3),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center'
          }}
        >
          <Box>
            <ErrorIcon sx={{ fontSize: 64, color: theme.palette.error.main, mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Something went wrong
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              {error}
            </Typography>
            <Stack direction="row" spacing={2} justifyContent="center">
              <Button
                variant="outlined"
                onClick={() => setError(null)}
                startIcon={<RefreshIcon />}
              >
                Try Again
              </Button>
              <Button
                variant="contained"
                onClick={() => navigate('/')}
                startIcon={<HomeIcon />}
              >
                Go Home
              </Button>
            </Stack>
          </Box>
        </StyledPagePaper>
      </StyledPageContainer>
    );
  }

  return (
    <StyledPageContainer
      maxWidth={maxWidth}
      disableGutters={disableGutters}
      variant={variant}
      isAnimated={enableAnimations}
      ref={containerRef}
      data-testid={testId}
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        py: theme.spacing(3),
        ...sx
      }}
    >
      {/* Enhanced breadcrumbs */}
      {enableBreadcrumbs && breadcrumbs.length > 0 && (
        <Fade in timeout={600}>
          <Box sx={{ mb: 2 }}>
            <Breadcrumbs
              aria-label="breadcrumb"
              separator={<NavigateNextIcon fontSize="small" />}
            >
              <Link
                color="inherit"
                href="/"
                onClick={(e) => {
                  e.preventDefault();
                  navigate('/');
                }}
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
                Home
              </Link>
              {breadcrumbs.map((crumb, index) => (
                <Link
                  key={index}
                  color={index === breadcrumbs.length - 1 ? 'text.primary' : 'inherit'}
                  href={crumb.href}
                  onClick={(e) => {
                    e.preventDefault();
                    if (crumb.href) navigate(crumb.href);
                  }}
                >
                  {crumb.label}
                </Link>
              ))}
            </Breadcrumbs>
          </Box>
        </Fade>
      )}

      {/* Enhanced page header */}
      {title && (
        <Fade in timeout={800}>
          <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h4" component="h1" gutterBottom>
              {title}
            </Typography>

            {planLimits.customization && (
              <Stack direction="row" spacing={1}>
                <Tooltip title="Page Settings">
                  <IconButton
                    onClick={(e) => setMenuAnchorEl(e.currentTarget)}
                    size="small"
                  >
                    <SettingsIcon />
                  </IconButton>
                </Tooltip>

                {planLimits.fullscreen && (
                  <Tooltip title={isFullscreen ? "Exit Fullscreen (Ctrl+F)" : "Fullscreen (Ctrl+F)"}>
                    <IconButton
                      onClick={handleFullscreenToggle}
                      size="small"
                    >
                      {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                    </IconButton>
                  </Tooltip>
                )}
              </Stack>
            )}
          </Box>
        </Fade>
      )}

      {/* Enhanced loading state */}
      {isLoading && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress />
        </Box>
      )}

      <StyledPagePaper
        elevation={0}
        variant={variant}
        isFullscreen={isFullscreen}
        hasGlassMorphism={enableGlassMorphism}
        isLoading={isLoading}
        ref={contentRef}
        sx={{
          flex: 1,
          p: theme.spacing(3),
          ...glassMorphismStyles,
          // Enhanced responsive design
          [theme.breakpoints.down('sm')]: {
            p: theme.spacing(2),
            mx: theme.spacing(1)
          },
          // Enhanced reduced motion support
          '@media (prefers-reduced-motion: reduce)': {
            transition: 'none',
            animation: 'none'
          }
        }}
        tabIndex={enableAccessibility ? 0 : -1}
        role={enableAccessibility ? "main" : undefined}
        aria-label={enableAccessibility ? `Main content for ${title || 'page'}` : undefined}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: theme.spacing(3),
            minHeight: '100%',
            position: 'relative',
            zIndex: 2
          }}
        >
          {children}
        </Box>
      </StyledPagePaper>

      {/* Enhanced scroll to top button */}
      {enableScrollTop && (
        <Zoom in={showScrollTop}>
          <StyledScrollTopFab
            color="primary"
            size="medium"
            onClick={handleScrollToTop}
            isVisible={showScrollTop}
            aria-label="Scroll to top"
          >
            <ScrollTopIcon />
          </StyledScrollTopFab>
        </Zoom>
      )}

      {/* Settings menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={() => setMenuAnchorEl(null)}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => handleLayoutChange(WRAPPER_LAYOUTS.FLUID)}>
          <ListItemText>Fluid Layout</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleLayoutChange(WRAPPER_LAYOUTS.FIXED)}>
          <ListItemText>Fixed Layout</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleLayoutChange(WRAPPER_LAYOUTS.CENTERED)}>
          <ListItemText>Centered Layout</ListItemText>
        </MenuItem>
      </Menu>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </StyledPageContainer>
  );
});

// Set display name for debugging
StablePageWrapper.displayName = 'StablePageWrapper';

// Comprehensive PropTypes for enterprise-grade stable page wrapper
StablePageWrapper.propTypes = {
  /** Child components to render */
  children: PropTypes.node.isRequired,

  /** Maximum width of the container */
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]),

  /** Whether to disable gutters */
  disableGutters: PropTypes.bool,

  /** Whether to enable glass morphism effect */
  enableGlassMorphism: PropTypes.bool,

  /** Wrapper variant */
  variant: PropTypes.oneOf(Object.values(WRAPPER_VARIANTS)),

  /** Layout type */
  layout: PropTypes.oneOf(Object.values(WRAPPER_LAYOUTS)),

  /** Whether to enable analytics tracking */
  enableAnalytics: PropTypes.bool,

  /** Whether to enable accessibility features */
  enableAccessibility: PropTypes.bool,

  /** Whether to enable animations */
  enableAnimations: PropTypes.bool,

  /** Whether to enable scroll to top button */
  enableScrollTop: PropTypes.bool,

  /** Whether to enable fullscreen mode */
  enableFullscreen: PropTypes.bool,

  /** Whether to enable breadcrumbs */
  enableBreadcrumbs: PropTypes.bool,

  /** Whether to enable error boundary */
  enableErrorBoundary: PropTypes.bool,

  /** Whether to show loading state */
  showLoadingState: PropTypes.bool,

  /** Page title */
  title: PropTypes.string,

  /** Breadcrumb items */
  breadcrumbs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      href: PropTypes.string
    })
  ),

  /** Analytics event handler */
  onAnalytics: PropTypes.func,

  /** Error handler */
  onError: PropTypes.func,

  /** Layout change handler */
  onLayoutChange: PropTypes.func,

  /** User's subscription plan */
  userPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),

  /** Additional styles */
  sx: PropTypes.object,

  /** Test ID for testing */
  testId: PropTypes.string
};

StablePageWrapper.defaultProps = {
  maxWidth: 'xl',
  disableGutters: false,
  enableGlassMorphism: true,
  variant: WRAPPER_VARIANTS.STANDARD,
  layout: WRAPPER_LAYOUTS.FLUID,
  enableAnalytics: true,
  enableAccessibility: true,
  enableAnimations: true,
  enableScrollTop: true,
  enableFullscreen: false,
  enableBreadcrumbs: false,
  enableErrorBoundary: true,
  showLoadingState: false,
  title: null,
  breadcrumbs: [],
  onAnalytics: null,
  onError: null,
  onLayoutChange: null,
  userPlan: 'creator',
  sx: {},
  testId: null
};

export default StablePageWrapper;

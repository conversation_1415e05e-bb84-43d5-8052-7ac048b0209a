import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import LiveCursorTracker from '../LiveCursorTracker';

// Mock lodash throttle
vi.mock('lodash/throttle', () => ({
  default: (fn) => {
    const throttledFn = (...args) => fn(...args);
    throttledFn.cancel = vi.fn();
    return throttledFn;
  },
}));

// Mock the auth context
const mockAuth = {
  user: {
    id: 'user1',
    name: '<PERSON>'
  }
};

vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth,
}));

// Mock the collaboration context
const mockCollaboration = {
  isConnected: true,
  userCursors: {
    'user2': {
      position: { x: 100, y: 200 },
      userInfo: {
        name: '<PERSON>'
      }
    },
    'user3': {
      position: { x: 300, y: 400 },
      userInfo: {
        name: '<PERSON>'
      }
    }
  },
  sendCursorPosition: vi.fn()
};

vi.mock('../../../contexts/CollaborationContext', () => ({
  useCollaboration: () => mockCollaboration,
}));

// Mock requestAnimationFrame and cancelAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => {
  setTimeout(cb, 16);
  return 1;
});
global.cancelAnimationFrame = vi.fn();

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      common: {
        white: '#FFFFFF',
      },
    },
    shadows: [
      'none',
      '0px 2px 1px -1px rgba(0,0,0,0.2)',
    ],
    spacing: (factor) => `${8 * factor}px`,
    shape: {
      borderRadius: 4,
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('LiveCursorTracker', () => {
  const mockContainerRef = {
    current: document.createElement('div')
  };

  const mockProps = {
    containerRef: mockContainerRef,
    contentId: 'test-content-id'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock getBoundingClientRect
    mockContainerRef.current.getBoundingClientRect = vi.fn(() => ({
      left: 0,
      top: 0,
      right: 800,
      bottom: 600,
      width: 800,
      height: 600
    }));

    // Mock addEventListener and removeEventListener
    mockContainerRef.current.addEventListener = vi.fn();
    mockContainerRef.current.removeEventListener = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders live cursor tracker correctly', () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should render cursor container
    const container = document.querySelector('[style*="position: fixed"]');
    expect(container).toBeInTheDocument();
  });

  test('displays other users cursors', () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should show cursors for other users
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
    
    // Should show user initials
    expect(screen.getByText('JS')).toBeInTheDocument();
    expect(screen.getByText('BJ')).toBeInTheDocument();
  });

  test('does not display current users cursor', () => {
    const cursorWithCurrentUser = {
      ...mockCollaboration,
      userCursors: {
        ...mockCollaboration.userCursors,
        'user1': {
          position: { x: 50, y: 50 },
          userInfo: {
            name: 'John Doe'
          }
        }
      }
    };

    vi.mocked(require('../../../contexts/CollaborationContext').useCollaboration).mockReturnValue(cursorWithCurrentUser);

    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should not show current user's cursor
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    expect(screen.queryByText('JD')).not.toBeInTheDocument();
    
    // Should still show other users
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
  });

  test('sets up mouse event listeners on container', () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should add event listeners to container
    expect(mockContainerRef.current.addEventListener).toHaveBeenCalledWith(
      'mousemove',
      expect.any(Function),
      { passive: true }
    );
    expect(mockContainerRef.current.addEventListener).toHaveBeenCalledWith(
      'mouseenter',
      expect.any(Function),
      { passive: true }
    );
    expect(mockContainerRef.current.addEventListener).toHaveBeenCalledWith(
      'mouseleave',
      expect.any(Function),
      { passive: true }
    );
  });

  test('removes event listeners on unmount', () => {
    const { unmount } = render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    unmount();

    // Should remove event listeners
    expect(mockContainerRef.current.removeEventListener).toHaveBeenCalledWith(
      'mousemove',
      expect.any(Function)
    );
    expect(mockContainerRef.current.removeEventListener).toHaveBeenCalledWith(
      'mouseenter',
      expect.any(Function)
    );
    expect(mockContainerRef.current.removeEventListener).toHaveBeenCalledWith(
      'mouseleave',
      expect.any(Function)
    );
  });

  test('handles mouse movement and sends cursor position', async () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Get the mousemove handler
    const mouseMoveHandler = mockContainerRef.current.addEventListener.mock.calls
      .find(call => call[0] === 'mousemove')[1];

    // Simulate mouse movement
    const mouseEvent = {
      clientX: 150,
      clientY: 250
    };

    mouseMoveHandler(mouseEvent);

    // Should use requestAnimationFrame for performance
    expect(global.requestAnimationFrame).toHaveBeenCalled();

    // Wait for animation frame
    await waitFor(() => {
      expect(mockCollaboration.sendCursorPosition).toHaveBeenCalledWith(
        { x: 150, y: 250 },
        'test-content-id'
      );
    });
  });

  test('handles mouse enter and leave events', () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Get event handlers
    const mouseEnterHandler = mockContainerRef.current.addEventListener.mock.calls
      .find(call => call[0] === 'mouseenter')[1];
    const mouseLeaveHandler = mockContainerRef.current.addEventListener.mock.calls
      .find(call => call[0] === 'mouseleave')[1];

    // Simulate mouse enter and leave
    mouseEnterHandler();
    mouseLeaveHandler();

    // Events should be handled without errors
    expect(mouseEnterHandler).toBeDefined();
    expect(mouseLeaveHandler).toBeDefined();
  });

  test('generates correct user initials', () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should generate correct initials
    expect(screen.getByText('JS')).toBeInTheDocument(); // Jane Smith
    expect(screen.getByText('BJ')).toBeInTheDocument(); // Bob Johnson
  });

  test('handles users without names', () => {
    const cursorWithoutName = {
      ...mockCollaboration,
      userCursors: {
        'user4': {
          position: { x: 100, y: 200 },
          userInfo: {}
        }
      }
    };

    vi.mocked(require('../../../contexts/CollaborationContext').useCollaboration).mockReturnValue(cursorWithoutName);

    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should show default name and initial
    expect(screen.getByText('Unknown User')).toBeInTheDocument();
    expect(screen.getByText('?')).toBeInTheDocument();
  });

  test('assigns different colors to different users', () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should render cursors with different colors based on user ID
    const cursors = document.querySelectorAll('[style*="transform: translate"]');
    expect(cursors.length).toBe(2); // Two other users
  });

  test('handles disconnected collaboration', () => {
    const disconnectedCollaboration = {
      ...mockCollaboration,
      isConnected: false
    };

    vi.mocked(require('../../../contexts/CollaborationContext').useCollaboration).mockReturnValue(disconnectedCollaboration);

    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should still render cursors but not send position updates
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
  });

  test('handles null container ref', () => {
    const nullContainerProps = {
      ...mockProps,
      containerRef: { current: null }
    };

    render(
      <TestWrapper>
        <LiveCursorTracker {...nullContainerProps} />
      </TestWrapper>
    );

    // Should render without errors
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  test('handles empty user cursors', () => {
    const emptyCollaboration = {
      ...mockCollaboration,
      userCursors: {}
    };

    vi.mocked(require('../../../contexts/CollaborationContext').useCollaboration).mockReturnValue(emptyCollaboration);

    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should render container but no cursors
    const container = document.querySelector('[style*="position: fixed"]');
    expect(container).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });

  test('positions cursors correctly', () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should position cursors at correct coordinates
    const cursor1 = document.querySelector('[style*="transform: translate(100px, 200px)"]');
    const cursor2 = document.querySelector('[style*="transform: translate(300px, 400px)"]');
    
    expect(cursor1).toBeInTheDocument();
    expect(cursor2).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Check for user names as text content for screen readers
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
  });

  test('handles cursor position updates', () => {
    const { rerender } = render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Update cursor positions
    const updatedCollaboration = {
      ...mockCollaboration,
      userCursors: {
        'user2': {
          position: { x: 200, y: 300 },
          userInfo: {
            name: 'Jane Smith'
          }
        }
      }
    };

    vi.mocked(require('../../../contexts/CollaborationContext').useCollaboration).mockReturnValue(updatedCollaboration);

    rerender(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Should update cursor position
    const updatedCursor = document.querySelector('[style*="transform: translate(200px, 300px)"]');
    expect(updatedCursor).toBeInTheDocument();
  });

  test('cancels animation frame on unmount', () => {
    const { unmount } = render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    unmount();

    // Should cancel any pending animation frames
    expect(global.cancelAnimationFrame).toHaveBeenCalled();
  });

  test('throttles cursor position updates', async () => {
    render(
      <TestWrapper>
        <LiveCursorTracker {...mockProps} />
      </TestWrapper>
    );

    // Get the mousemove handler
    const mouseMoveHandler = mockContainerRef.current.addEventListener.mock.calls
      .find(call => call[0] === 'mousemove')[1];

    // Simulate rapid mouse movements
    mouseMoveHandler({ clientX: 100, clientY: 100 });
    mouseMoveHandler({ clientX: 101, clientY: 101 });
    mouseMoveHandler({ clientX: 102, clientY: 102 });

    // Should use throttling to limit updates
    expect(global.requestAnimationFrame).toHaveBeenCalled();
  });
});

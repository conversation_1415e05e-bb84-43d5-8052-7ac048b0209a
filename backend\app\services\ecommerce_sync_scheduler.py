"""
Background scheduler for e-commerce product synchronization.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from bson import ObjectId

from app.db.mongodb import get_database
from app.models.ecommerce import EcommerceStore
from app.models.common import SyncStatusEnum
from app.services.ecommerce_service import ecommerce_service
from app.services.credential_manager import credential_manager
from app.utils.error_handling import ExternalServiceError

# Create ServiceError alias for consistency
ServiceError = ExternalServiceError

logger = logging.getLogger(__name__)

# Collection names
STORES_COLLECTION = "ecommerce_stores"


class EcommerceSyncScheduler:
    """
    Background scheduler for automatic product synchronization.
    """
    
    def __init__(self):
        self.running = False
        self.sync_interval = 3600  # Default 1 hour
        self.max_concurrent_syncs = 5
    
    async def start_scheduler(self) -> None:
        """Start the background sync scheduler."""
        if self.running:
            logger.warning("Sync scheduler is already running")
            return
        
        self.running = True
        logger.info("Starting e-commerce sync scheduler")
        
        try:
            while self.running:
                await self._run_sync_cycle()
                await asyncio.sleep(self.sync_interval)
        except Exception as e:
            logger.error(f"Sync scheduler error: {str(e)}")
        finally:
            self.running = False
            logger.info("E-commerce sync scheduler stopped")
    
    async def stop_scheduler(self) -> None:
        """Stop the background sync scheduler."""
        logger.info("Stopping e-commerce sync scheduler")
        self.running = False
    
    async def _run_sync_cycle(self) -> None:
        """Run a single sync cycle for all eligible stores."""
        try:
            logger.info("Starting sync cycle")
            
            # Get stores that need syncing
            stores_to_sync = await self._get_stores_needing_sync()
            
            if not stores_to_sync:
                logger.debug("No stores need syncing")
                return
            
            logger.info(f"Found {len(stores_to_sync)} stores needing sync")
            
            # Process stores in batches to avoid overwhelming the system
            semaphore = asyncio.Semaphore(self.max_concurrent_syncs)
            
            async def sync_store_with_semaphore(store: EcommerceStore):
                async with semaphore:
                    await self._sync_store_safely(store)
            
            # Create tasks for all stores
            tasks = [sync_store_with_semaphore(store) for store in stores_to_sync]
            
            # Wait for all syncs to complete
            await asyncio.gather(*tasks, return_exceptions=True)
            
            logger.info("Sync cycle completed")
            
        except Exception as e:
            logger.error(f"Error in sync cycle: {str(e)}")
    
    async def _get_stores_needing_sync(self) -> List[EcommerceStore]:
        """Get stores that need synchronization."""
        try:
            db = await get_database()
            
            # Calculate cutoff time for sync
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=1)
            
            # Find stores that:
            # 1. Have sync enabled
            # 2. Haven't been synced recently
            # 3. Are in connected status
            query = {
                "sync_enabled": True,
                "status": SyncStatusEnum.CONNECTED,
                "$or": [
                    {"last_sync_at": {"$lt": cutoff_time}},
                    {"last_sync_at": {"$exists": False}}
                ]
            }
            
            store_docs = await db[STORES_COLLECTION].find(query).to_list(length=None)
            
            stores = []
            for store_doc in store_docs:
                try:
                    store = EcommerceStore(**store_doc)
                    # Decrypt credentials
                    decrypted_store = await credential_manager.decrypt_ecommerce_store(store)
                    stores.append(decrypted_store)
                except Exception as e:
                    logger.error(f"Failed to decrypt store {store_doc.get('_id')}: {str(e)}")
                    continue
            
            return stores
            
        except Exception as e:
            logger.error(f"Failed to get stores needing sync: {str(e)}")
            return []
    
    async def _sync_store_safely(self, store: EcommerceStore) -> None:
        """Safely sync a store with error handling."""
        try:
            logger.info(f"Starting sync for store {store.store_name} ({store.platform})")
            
            # Update store status to syncing
            await self._update_store_sync_status(store.id, SyncStatusEnum.SYNCING)
            
            # Perform the sync
            result = await ecommerce_service.sync_store_products(
                str(store.id),
                str(store.user_id),
                limit=250,  # Reasonable batch size
                force_full_sync=False
            )
            
            if result["success"]:
                logger.info(
                    f"Successfully synced {result['products_synced']} products "
                    f"for store {store.store_name}"
                )
                await self._update_store_sync_status(store.id, SyncStatusEnum.SYNC_COMPLETED)
            else:
                logger.error(f"Sync failed for store {store.store_name}")
                await self._update_store_sync_status(store.id, SyncStatusEnum.SYNC_FAILED)
                
        except Exception as e:
            logger.error(f"Error syncing store {store.store_name}: {str(e)}")
            await self._update_store_sync_status(store.id, SyncStatusEnum.SYNC_FAILED, str(e))
    
    async def _update_store_sync_status(
        self,
        store_id: ObjectId,
        status: SyncStatusEnum,
        error_message: Optional[str] = None
    ) -> None:
        """Update store sync status."""
        try:
            db = await get_database()
            
            update_data = {
                "status": status,
                "updated_at": datetime.now(timezone.utc)
            }
            
            if status == SyncStatusEnum.SYNC_COMPLETED:
                update_data["last_sync_at"] = datetime.now(timezone.utc)
                update_data["last_error"] = None
            elif status == SyncStatusEnum.SYNC_FAILED and error_message:
                update_data["last_error"] = error_message
            
            await db[STORES_COLLECTION].update_one(
                {"_id": store_id},
                {"$set": update_data}
            )
            
        except Exception as e:
            logger.error(f"Failed to update store sync status: {str(e)}")
    
    async def sync_store_now(self, store_id: str, user_id: str) -> Dict[str, Any]:
        """
        Manually trigger sync for a specific store.
        
        Args:
            store_id: Store ID to sync
            user_id: User ID for security
            
        Returns:
            Sync result
        """
        try:
            # Get store
            store = await ecommerce_service.get_store(store_id, user_id)
            if not store:
                return {"success": False, "error": "Store not found"}
            
            # Check if store is already syncing
            if store.status == SyncStatusEnum.SYNCING:
                return {"success": False, "error": "Store is already syncing"}
            
            # Perform sync
            await self._sync_store_safely(store)
            
            return {"success": True, "message": "Sync completed"}
            
        except Exception as e:
            logger.error(f"Failed to manually sync store: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def get_sync_status(self, user_id: str) -> Dict[str, Any]:
        """
        Get sync status for all user stores.
        
        Args:
            user_id: User ID
            
        Returns:
            Sync status information
        """
        try:
            stores = await ecommerce_service.get_user_stores(user_id)
            
            status_info = {
                "total_stores": len(stores),
                "syncing": 0,
                "completed": 0,
                "failed": 0,
                "stores": []
            }
            
            for store in stores:
                store_info = {
                    "id": str(store.id),
                    "name": store.store_name,
                    "platform": store.platform,
                    "status": store.status,
                    "last_sync_at": store.last_sync_at.isoformat() if store.last_sync_at else None,
                    "total_products": store.total_products,
                    "synced_products": store.synced_products,
                    "last_error": store.last_error
                }
                
                status_info["stores"].append(store_info)
                
                # Count by status
                if store.status == SyncStatusEnum.SYNCING:
                    status_info["syncing"] += 1
                elif store.status == SyncStatusEnum.SYNC_COMPLETED:
                    status_info["completed"] += 1
                elif store.status == SyncStatusEnum.SYNC_FAILED:
                    status_info["failed"] += 1
            
            return status_info
            
        except Exception as e:
            logger.error(f"Failed to get sync status: {str(e)}")
            return {"error": str(e)}
    
    async def configure_sync_settings(
        self,
        store_id: str,
        user_id: str,
        sync_enabled: Optional[bool] = None,
        auto_sync_interval: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Configure sync settings for a store.
        
        Args:
            store_id: Store ID
            user_id: User ID for security
            sync_enabled: Whether to enable automatic sync
            auto_sync_interval: Sync interval in seconds
            
        Returns:
            Update result
        """
        try:
            db = await get_database()
            
            update_data: Dict[str, Any] = {"updated_at": datetime.now(timezone.utc)}

            if sync_enabled is not None:
                update_data["sync_enabled"] = sync_enabled

            if auto_sync_interval is not None:
                # Validate interval (minimum 5 minutes, maximum 24 hours)
                if auto_sync_interval < 300 or auto_sync_interval > 86400:
                    return {"success": False, "error": "Invalid sync interval"}
                update_data["auto_sync_interval"] = auto_sync_interval
            
            result = await db[STORES_COLLECTION].update_one(
                {
                    "_id": ObjectId(store_id),
                    "user_id": ObjectId(user_id)
                },
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                return {"success": True, "message": "Sync settings updated"}
            else:
                return {"success": False, "error": "Store not found or no changes made"}
                
        except Exception as e:
            logger.error(f"Failed to configure sync settings: {str(e)}")
            return {"success": False, "error": str(e)}


# Create singleton instance
ecommerce_sync_scheduler = EcommerceSyncScheduler()

#!/usr/bin/env python3
# @since 2024-1-1 to 2025-25-7
import http.server
import socketserver
import urllib.request
import urllib.parse
import os
import mimetypes
from pathlib import Path

class ProxyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to serve static files from
        super().__init__(*args, directory="frontend-dist-fixed", **kwargs)
    
    def do_GET(self):
        if self.path.startswith('/api'):
            self.proxy_request()
        else:
            # For SPA routing, serve index.html for any non-API route that doesn't exist
            if not os.path.exists(os.path.join("frontend-dist-fixed", self.path.lstrip('/'))):
                self.path = '/index.html'
            super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/api'):
            self.proxy_request()
        else:
            self.send_error(404)
    
    def do_PUT(self):
        if self.path.startswith('/api'):
            self.proxy_request()
        else:
            self.send_error(404)
    
    def do_DELETE(self):
        if self.path.startswith('/api'):
            self.proxy_request()
        else:
            self.send_error(404)
    
    def proxy_request(self):
        try:
            # Build the target URL
            target_url = f"http://localhost:8002{self.path}"
            
            # Get request body if present
            content_length = int(self.headers.get('Content-Length', 0))
            body = self.rfile.read(content_length) if content_length > 0 else None
            
            # Create the request
            req = urllib.request.Request(target_url, data=body, method=self.command)
            
            # Copy headers (excluding some that should not be forwarded)
            skip_headers = {'host', 'connection', 'content-length'}
            for header, value in self.headers.items():
                if header.lower() not in skip_headers:
                    req.add_header(header, value)
            
            # Make the request
            with urllib.request.urlopen(req) as response:
                # Send response status
                self.send_response(response.getcode())
                
                # Copy response headers
                for header, value in response.headers.items():
                    if header.lower() not in {'connection', 'transfer-encoding'}:
                        self.send_header(header, value)
                self.end_headers()
                
                # Copy response body
                self.wfile.write(response.read())
                
        except urllib.error.HTTPError as e:
            self.send_response(e.code)
            self.end_headers()
            self.wfile.write(e.read())
        except Exception as e:
            print(f"Proxy error: {e}")
            self.send_error(500, f"Proxy error: {e}")

if __name__ == "__main__":
    PORT = 3005
    
    # Check if frontend-dist-fixed directory exists
    if not os.path.exists("frontend-dist-fixed"):
        print("Error: frontend-dist-fixed directory not found!")
        exit(1)
    
    with socketserver.TCPServer(("", PORT), ProxyHTTPRequestHandler) as httpd:
        print(f"Proxy server running on http://localhost:{PORT}")
        print(f"Frontend: Serving static files from frontend-dist-fixed")
        print(f"Backend: Proxying /api requests to http://localhost:8002")
        print("Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

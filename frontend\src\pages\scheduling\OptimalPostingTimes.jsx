// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  AlertTitle,
  Paper,
  Chip,
  Divider,
  Button,
  List,
  Tooltip,
  useTheme,
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  AccessTime as AccessTimeIcon,
  People as PeopleIcon,
  Public as PublicIcon,
  History as HistoryIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import InstagramIcon from '@mui/icons-material/Instagram';
import PinterestIcon from '@mui/icons-material/Pinterest';

const OptimalPostingTimes = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  
  const [loading, setLoading] = useState(true);
  const [platform, setPlatform] = useState('all');
  const [recommendations, setRecommendations] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [error, setError] = useState(null);
  
  // Authentication check
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [isAuthenticated, navigate]);

  // Load connected accounts
  useEffect(() => {
    if (!isAuthenticated) return;

    const fetchAccounts = async () => {
      try {
        const response = await api.get('/api/social-media/accounts');
        setAccounts(response.data.accounts || []);
      } catch (error) {
        console.error('Error fetching accounts:', error);
        if (error.response?.status === 401) {
          navigate('/login');
          return;
        }
        showErrorNotification(
          error.response?.data?.detail || 'Failed to load social media accounts'
        );
      }
    };

    fetchAccounts();
  }, [isAuthenticated, navigate, showErrorNotification]);
  
  // Load recommendations
  useEffect(() => {
    if (!isAuthenticated || accounts.length === 0) return;

    const fetchRecommendations = async () => {
      setLoading(true);
      setError(null);

      try {
        // Build query parameters
        const params = new URLSearchParams();

        if (platform !== 'all') {
          params.append('platform', platform);
        }

        // Add user timezone if available
        if (user?.timezone) {
          params.append('timezone', user.timezone);
        }

        // Fetch recommendations
        const response = await api.get(`/api/posting-time/recommendations?${params.toString()}`);

        // Handle different response formats
        if (response.data && response.data.combined_recommendations) {
          setRecommendations(response.data);
          showSuccessNotification('Posting time recommendations loaded successfully');
        } else {
          // Handle empty or invalid response
          setRecommendations({
            combined_recommendations: {},
            message: 'No recommendations available yet. Connect more accounts or publish more content to get personalized recommendations.'
          });
        }
      } catch (error) {
        console.error('Error fetching posting time recommendations:', error);

        if (error.response?.status === 401) {
          navigate('/login');
          return;
        } else if (error.response?.status === 404) {
          setError('Posting time recommendations service is not available');
        } else if (error.response?.status === 400) {
          setError(error.response?.data?.detail || 'Invalid request parameters');
        } else if (error.response?.status >= 500) {
          setError('Service temporarily unavailable. Please try again later.');
        } else {
          setError(error.response?.data?.detail || 'Failed to load posting time recommendations');
        }

        showErrorNotification(error.response?.data?.detail || 'Failed to load posting time recommendations');
      } finally {
        setLoading(false);
      }
    };

    fetchRecommendations();
  }, [platform, isAuthenticated, accounts.length, user?.timezone, navigate, showSuccessNotification, showErrorNotification]);
  
  // Handle platform change
  const handlePlatformChange = (event) => {
    setPlatform(event.target.value);
  };
  
  // Platform icon utility for UI enhancements
  const getPlatformIcon = (platformName) => {
    switch (platformName?.toLowerCase()) {
      case 'facebook':
        return <FacebookIcon sx={{ color: '#1877F2' }} />;
      case 'twitter':
      case 'x':
        return <TwitterIcon sx={{ color: '#1DA1F2' }} />;
      case 'linkedin':
        return <LinkedInIcon sx={{ color: '#0A66C2' }} />;
      case 'instagram':
        return <InstagramIcon sx={{ color: '#E4405F' }} />;
      case 'pinterest':
        return <PinterestIcon sx={{ color: '#BD081C' }} />;
      default:
        return <PublicIcon />;
    }
  };
  
  // Get confidence color
  const getConfidenceColor = (confidence) => {
    switch (confidence) {
      case 'High':
        return theme.palette.success.main;
      case 'Medium':
        return theme.palette.warning.main;
      case 'Low':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };
  
  // Get source icon
  const getSourceIcon = (source) => {
    switch (source) {
      case 'historical':
        return <HistoryIcon />;
      case 'icp':
        return <PeopleIcon />;
      case 'location':
        return <PublicIcon />;
      default:
        return <InfoIcon />;
    }
  };
  
  // Navigate to content creation with selected time
  const handleCreateContent = (day, timeSlot) => {
    // Extract hours from time slot (e.g., "08:00-09:59" -> 8)
    const startHour = parseInt(timeSlot.split(':')[0], 10);
    
    // Get current date
    const now = new Date();
    
    // Calculate target day (0 = Sunday, 6 = Saturday in JavaScript)
    const dayMap = {
      'Monday': 1,
      'Tuesday': 2,
      'Wednesday': 3,
      'Thursday': 4,
      'Friday': 5,
      'Saturday': 6,
      'Sunday': 0
    };
    
    const targetDay = dayMap[day];
    const currentDay = now.getDay();
    
    // Calculate days to add
    let daysToAdd = targetDay - currentDay;
    if (daysToAdd <= 0) {
      daysToAdd += 7; // Move to next week if target day is today or earlier
    }
    
    // Create target date
    const targetDate = new Date(now);
    targetDate.setDate(now.getDate() + daysToAdd);
    targetDate.setHours(startHour, 0, 0, 0);
    
    // Format date for URL
    const formattedDate = targetDate.toISOString();
    
    // Navigate to content creation with scheduled time
    navigate('/content/generator', {
      state: {
        scheduledDate: targetDate,
        scheduledTime: formattedDate,
        platform: platform === 'all' ? null : platform,
        optimalTime: true
      }
    });
  };
  
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Optimal Posting Times
      </Typography>
      
      <Typography variant="body1" paragraph>
        Get recommendations for the best times to post on social media based on your audience&apos;s behavior,
        ICP characteristics, and location data.
      </Typography>
      
      {accounts.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          You don&apos;t have any connected social media accounts yet. Connect an account to get posting time recommendations.
        </Alert>
      ) : (
        <>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Platform</InputLabel>
                    <Select
                      value={platform}
                      label="Platform"
                      onChange={handlePlatformChange}
                    >
                      <MenuItem value="all">
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PublicIcon sx={{ mr: 1 }} />
                          All Platforms
                        </Box>
                      </MenuItem>
                      {accounts.map(account => (
                        <MenuItem key={account.platform} value={account.platform}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {getPlatformIcon(account.platform)}
                            <span style={{ marginLeft: '8px' }}>
                              {account.platform.charAt(0).toUpperCase() + account.platform.slice(1)}
                            </span>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
          
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            recommendations && recommendations.combined_recommendations &&
            Object.keys(recommendations.combined_recommendations).length > 0 ? (
              <Grid container spacing={3}>
                {Object.entries(recommendations.combined_recommendations).map(([day, timeSlots]) => (
                  <Grid item xs={12} md={6} lg={4} key={day}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {day}
                        </Typography>
                        
                        <Divider sx={{ mb: 2 }} />
                        
                        {timeSlots.length > 0 ? (
                          <List>
                            {timeSlots.map((slot, index) => (
                              <Paper 
                                key={index} 
                                elevation={1} 
                                sx={{ 
                                  mb: 2, 
                                  p: 2, 
                                  borderLeft: `4px solid ${getConfidenceColor(slot.confidence)}` 
                                }}
                              >
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                  <Typography variant="subtitle1">
                                    <AccessTimeIcon sx={{ verticalAlign: 'middle', mr: 1 }} />
                                    {slot.time_slot}
                                    {slot.platform && slot.platform !== 'all' && (
                                      <Box component="span" sx={{ ml: 1, verticalAlign: 'middle' }}>
                                        {getPlatformIcon(slot.platform)}
                                      </Box>
                                    )}
                                  </Typography>
                                  <Chip
                                    label={slot.confidence}
                                    size="small"
                                    sx={{
                                      bgcolor: getConfidenceColor(slot.confidence),
                                      color: 'white'
                                    }}
                                  />
                                </Box>
                                
                                <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                                  <Tooltip title={`Based on ${slot.source} data`}>
                                    <Box component="span" sx={{ display: 'inline-flex', alignItems: 'center' }}>
                                      {getSourceIcon(slot.source)}
                                      <span style={{ marginLeft: '4px' }}>{slot.reason || `Recommended based on ${slot.source} data`}</span>
                                    </Box>
                                  </Tooltip>
                                </Typography>
                                
                                <Button
                                  variant="outlined"
                                  size="small"
                                  startIcon={<ScheduleIcon />}
                                  onClick={() => handleCreateContent(day, slot.time_slot)}
                                  fullWidth
                                  sx={{ mt: 1 }}
                                >
                                  Schedule Content
                                </Button>
                              </Paper>
                            ))}
                          </List>
                        ) : (
                          <Typography variant="body2" color="textSecondary">
                            No recommendations available for this day.
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
                
                <Grid item xs={12}>
                  <Alert severity="info">
                    <AlertTitle>How recommendations are generated</AlertTitle>
                    <Typography variant="body2">
                      These recommendations are based on:
                      <ul>
                        <li><strong>Historical data</strong>: Analysis of your past content performance</li>
                        <li><strong>ICP characteristics</strong>: Behavior patterns of your target audience</li>
                        <li><strong>Location data</strong>: Time zones where your audience is located</li>
                      </ul>
                      The more content you publish, the more accurate these recommendations will become.
                    </Typography>
                  </Alert>
                </Grid>
              </Grid>
            ) : (
              !loading && (
                <Alert severity="info" sx={{ mt: 3 }}>
                  <AlertTitle>No Recommendations Available</AlertTitle>
                  <Typography variant="body2">
                    {recommendations?.message ||
                     'We need more data to provide personalized posting time recommendations. Try:'}
                  </Typography>
                  <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                    <li>Publishing more content to build historical data</li>
                    <li>Connecting additional social media accounts</li>
                    <li>Ensuring your content has engagement data</li>
                  </ul>
                  <Button
                    variant="outlined"
                    sx={{ mt: 2 }}
                    onClick={() => navigate('/content/generator')}
                  >
                    Create Content Now
                  </Button>
                </Alert>
              )
            )
          )}
        </>
      )}
    </Box>
  );
};

export default OptimalPostingTimes;

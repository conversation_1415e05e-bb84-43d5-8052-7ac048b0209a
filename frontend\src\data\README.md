<!-- @since 2024-1-1 to 2025-25-7 -->
# World Countries Data

Enhanced GeoJSON dataset with comprehensive country metadata for the ACE Social platform.

## 📊 Dataset Overview

- **Format**: GeoJSON FeatureCollection
- **Countries**: 15 major world countries
- **Coordinate System**: WGS84
- **Last Updated**: 2024-01-15
- **Version**: 1.0.0

## 🗂️ Data Structure

### Metadata
```json
{
  "version": "1.0.0",
  "description": "World countries data with enhanced metadata for ACE Social platform",
  "lastUpdated": "2024-01-15T00:00:00Z",
  "source": "ACE Social Platform",
  "coordinateSystem": "WGS84",
  "totalCountries": 15,
  "note": "Simplified geometries for performance optimization"
}
```

### Country Properties
Each country feature includes comprehensive metadata:

- **Basic Info**: Name, ISO codes, continent, region
- **Geographic**: Coordinates, bounds, timezone
- **Demographic**: Population, area, capital
- **Economic**: Currency code/name, calling code
- **Cultural**: Languages, flag emoji
- **Technical**: TLD, bounds

## 🚀 Usage Examples

### Import the Data
```javascript
import worldCountries from './world-countries.json';
import countryUtils from '../utils/countryUtils.js';
```

### Basic Operations
```javascript
// Get all countries
const countries = countryUtils.getAllCountries();

// Get country by ISO code
const usa = countryUtils.getCountryByCode('US');
const canada = countryUtils.getCountryByCode3('CAN');

// Search countries
const results = countryUtils.searchCountries('United');

// Get countries by continent
const europeanCountries = countryUtils.getCountriesByContinent('Europe');
```

### Specific Information
```javascript
// Basic info
const basicInfo = countryUtils.getCountryBasicInfo('US');
// Returns: { name, code, code3, flag, capital, continent }

// Location info
const locationInfo = countryUtils.getCountryLocationInfo('US');
// Returns: { name, coordinates, bounds, timezone }

// Economic info
const economicInfo = countryUtils.getCountryEconomicInfo('US');
// Returns: { name, population, area_km2, currency_code, currency_name, calling_code }
```

## 📋 Available Countries

| Country | ISO-2 | ISO-3 | Population | Currency |
|---------|-------|-------|------------|----------|
| United States | US | USA | 331,900,000 | USD |
| Canada | CA | CAN | 38,000,000 | CAD |
| United Kingdom | GB | GBR | 67,500,000 | GBP |
| Germany | DE | DEU | 83,200,000 | EUR |
| France | FR | FRA | - | EUR |
| Australia | AU | AUS | - | AUD |
| India | IN | IND | - | INR |
| China | CN | CHN | - | CNY |
| Brazil | BR | BRA | - | BRL |
| Russia | RU | RUS | - | RUB |
| Japan | JP | JPN | - | JPY |
| South Africa | ZA | ZAF | - | ZAR |
| Mexico | MX | MEX | - | MXN |
| Spain | ES | ESP | - | EUR |
| Italy | IT | ITA | - | EUR |

*Note: Some countries have placeholder data that can be enhanced with complete information.*

## 🔧 TypeScript Support

Full TypeScript definitions are available in `world-countries.d.ts`:

```typescript
import { WorldCountriesGeoJSON, CountryFeature } from './world-countries.d.ts';

const countries: WorldCountriesGeoJSON = worldCountries;
const feature: CountryFeature = countries.features[0];
```

## 🎯 Use Cases

### Geographic Visualization
- Map rendering with country boundaries
- Location-based analytics
- Regional data visualization

### User Interface
- Country selection dropdowns
- Flag displays
- Currency formatting
- Timezone handling

### Data Analysis
- Population statistics
- Economic indicators
- Regional grouping
- Market analysis

## ⚡ Performance Considerations

- **Simplified Geometries**: Coordinate arrays are empty for performance
- **Optimized Size**: Only essential metadata included
- **Lazy Loading**: Can be loaded on-demand
- **Caching**: Suitable for client-side caching

## 🔄 Future Enhancements

### Planned Additions
- [ ] Complete coordinate data for mapping
- [ ] Additional countries (195 total)
- [ ] Economic indicators (GDP, HDI)
- [ ] Language codes (ISO 639)
- [ ] Regional trade blocks
- [ ] Time zone data
- [ ] Phone number formatting

### Data Sources
- ISO 3166-1 (Country codes)
- ISO 4217 (Currency codes)
- World Bank (Population, economic data)
- Natural Earth (Geographic boundaries)

## 📝 Maintenance

### Updating Data
1. Modify `world-countries.json`
2. Update version in metadata
3. Run validation: `npm run validate:countries`
4. Update TypeScript definitions if needed
5. Test utility functions

### Validation
```bash
# Validate JSON structure
node -e "JSON.parse(require('fs').readFileSync('src/data/world-countries.json'))"

# Run ESLint on utilities
npx eslint src/utils/countryUtils.js

# Run TypeScript checks
npx tsc --noEmit
```

## 🤝 Contributing

When adding new countries or updating existing data:

1. Follow the established schema
2. Include all required properties
3. Validate ISO codes
4. Update the country count in metadata
5. Add TypeScript types if needed
6. Update this README

## 📄 License

This dataset is part of the ACE Social platform and follows the project's licensing terms.

{"env": {"browser": true, "es2020": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react/jsx-runtime", "plugin:react-hooks/recommended"], "ignorePatterns": ["dist", ".eslintrc.cjs"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "settings": {"react": {"version": "18.2"}}, "plugins": ["react-refresh"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "react/prop-types": "off", "no-unused-vars": "warn", "no-undef": "warn", "react/no-unescaped-entities": "warn", "react/jsx-no-undef": "warn", "react/display-name": "warn", "no-case-declarations": "warn", "no-useless-escape": "warn", "no-async-promise-executor": "warn", "react-hooks/exhaustive-deps": "warn"}}
/**
 * CompetitorForm Component Test Suite
 * Comprehensive testing for enterprise-grade competitor form component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { BrowserRouter } from 'react-router-dom';

// Component under test
import CompetitorForm from '../CompetitorForm';

// Mock contexts and hooks
import { CompetitorProvider } from '../../../contexts/CompetitorContext';
import { NotificationProvider } from '../../../contexts/NotificationContext';

// Test utilities
import { createMockCompetitor } from '../../../__mocks__/competitorMocks';
import { axe, toHaveNoViolations } from 'jest-axe';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock external dependencies
vi.mock('../../../hooks/useAnalytics', () => ({
  useAnalytics: () => ({
    trackEvent: vi.fn(),
    trackError: vi.fn(),
    trackPerformance: vi.fn()
  })
}));

vi.mock('../../../hooks/useLocalStorage', () => ({
  useLocalStorage: (key, defaultValue) => [defaultValue, vi.fn()]
}));

vi.mock('../../../hooks/useDebounce', () => ({
  useDebounce: (fn) => fn
}));

vi.mock('../../../utils/formUtils', () => ({
  validateField: vi.fn().mockReturnValue([])
}));

vi.mock('../../../utils/helpers', () => ({
  announceToScreenReader: vi.fn(),
  generateUniqueId: vi.fn().mockReturnValue('unique-id-123')
}));

// Mock child components
vi.mock('../../common', () => ({
  ErrorBoundary: ({ children, fallback }) => {
    try {
      return children;
    } catch (error) {
      return fallback(error, () => {});
    }
  },
  EmptyState: ({ icon: Icon, title, description, action }) => (
    <div data-testid="empty-state">
      <Icon />
      <h3>{title}</h3>
      <p>{description}</p>
      {action}
    </div>
  ),
  FeatureGate: ({ children, featureKey, userPlan }) => (
    <div data-testid={`feature-gate-${featureKey}`} data-user-plan={userPlan}>
      {children}
    </div>
  ),
  ConfirmationDialog: ({ open, title, content, onConfirm, onCancel, confirmText, cancelText }) => (
    open ? (
      <div data-testid="confirmation-dialog">
        <h3>{title}</h3>
        <p>{content}</p>
        <button onClick={onConfirm}>{confirmText}</button>
        <button onClick={onCancel}>{cancelText}</button>
      </div>
    ) : null
  )
}));

// Test theme
const theme = createTheme({
  palette: {
    primary: { main: '#4E40C5' },
    secondary: { main: '#EBAE1B' }
  }
});

// Test wrapper component
const TestWrapper = ({ children, competitorContextValue = {} }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      <NotificationProvider>
        <CompetitorProvider value={competitorContextValue}>
          {children}
        </CompetitorProvider>
      </NotificationProvider>
    </ThemeProvider>
  </BrowserRouter>
);

// Mock data
const mockCompetitor = createMockCompetitor({
  id: 'test-competitor-1',
  name: 'Test Competitor',
  description: 'A test competitor for unit testing',
  industry: 'Technology',
  website: 'https://example.com',
  is_active: true,
  social_media: [
    { 
      id: 'sm-1',
      platform: 'linkedin', 
      account_name: 'testcompany',
      account_url: 'https://linkedin.com/company/testcompany',
      followers_count: '1000',
      posts_frequency: 'Daily'
    }
  ]
});

const defaultContextValue = {
  selectedCompetitor: null,
  loading: false,
  error: null,
  fetchCompetitor: vi.fn(),
  createCompetitor: vi.fn(),
  updateCompetitor: vi.fn(),
  clearSelectedCompetitor: vi.fn()
};

describe('CompetitorForm Component', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
  });

  describe('Rendering and Basic Functionality', () => {
    test('renders without crashing', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      expect(screen.getByText('Add New Competitor')).toBeInTheDocument();
    });

    test('renders in edit mode when competitorId is provided', () => {
      const editContextValue = {
        ...defaultContextValue,
        selectedCompetitor: mockCompetitor
      };

      render(
        <TestWrapper competitorContextValue={editContextValue}>
          <CompetitorForm competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByText('Edit Competitor')).toBeInTheDocument();
    });

    test('displays loading state correctly', () => {
      const loadingContextValue = {
        ...defaultContextValue,
        loading: true
      };

      render(
        <TestWrapper competitorContextValue={loadingContextValue}>
          <CompetitorForm competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    test('displays error state correctly', () => {
      const errorContextValue = {
        ...defaultContextValue,
        error: 'Failed to load competitor'
      };

      render(
        <TestWrapper competitorContextValue={errorContextValue}>
          <CompetitorForm competitorId="test-competitor-1" />
        </TestWrapper>
      );

      expect(screen.getByText('Failed to load competitor')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });
  });

  describe('Form Fields and Validation', () => {
    test('renders all required form fields', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      expect(screen.getByLabelText(/competitor name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/website/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/industry/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/company size/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/target audience/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/active competitor/i)).toBeInTheDocument();
    });

    test('handles form field changes correctly', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      const nameField = screen.getByLabelText(/competitor name/i);
      await user.type(nameField, 'Test Company');

      expect(nameField).toHaveValue('Test Company');
    });

    test('validates required fields', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm enableValidation={true} />
        </TestWrapper>
      );

      const submitButton = screen.getByRole('button', { name: /create competitor/i });
      await user.click(submitButton);

      // Should show validation errors for required fields
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      });
    });

    test('validates website URL format', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm enableValidation={true} />
        </TestWrapper>
      );

      const websiteField = screen.getByLabelText(/website/i);
      await user.type(websiteField, 'invalid-url');

      // Trigger validation
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText(/invalid website url/i)).toBeInTheDocument();
      });
    });
  });

  describe('Social Media Platforms', () => {
    test('shows empty state when no platforms added', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      expect(screen.getByTestId('empty-state')).toBeInTheDocument();
      expect(screen.getByText('No Social Media Platforms')).toBeInTheDocument();
    });

    test('allows adding social media platforms', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      const addButton = screen.getByRole('button', { name: /add platform/i });
      await user.click(addButton);

      expect(screen.getByLabelText(/platform/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/account name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/account url/i)).toBeInTheDocument();
    });

    test('allows removing social media platforms', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      // Add a platform first
      const addButton = screen.getByRole('button', { name: /add platform/i });
      await user.click(addButton);

      // Remove the platform
      const removeButton = screen.getByRole('button', { name: /remove platform/i });
      await user.click(removeButton);

      expect(screen.queryByLabelText(/platform/i)).not.toBeInTheDocument();
    });

    test('validates social media platform URLs', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm enableValidation={true} />
        </TestWrapper>
      );

      // Add a platform
      const addButton = screen.getByRole('button', { name: /add platform/i });
      await user.click(addButton);

      // Select LinkedIn platform
      const platformSelect = screen.getByLabelText(/platform/i);
      await user.click(platformSelect);
      await user.click(screen.getByText('LinkedIn'));

      // Enter invalid URL
      const urlField = screen.getByLabelText(/account url/i);
      await user.type(urlField, 'invalid-linkedin-url');

      await user.tab();

      await waitFor(() => {
        expect(screen.getByText(/invalid.*linkedin.*url/i)).toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    test('submits form with valid data for new competitor', async () => {
      const mockCreateCompetitor = vi.fn().mockResolvedValue({ id: 'new-competitor-id' });
      const contextValue = {
        ...defaultContextValue,
        createCompetitor: mockCreateCompetitor
      };

      render(
        <TestWrapper competitorContextValue={contextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      // Fill in required fields
      await user.type(screen.getByLabelText(/competitor name/i), 'New Competitor');
      await user.type(screen.getByLabelText(/website/i), 'https://example.com');

      const submitButton = screen.getByRole('button', { name: /create competitor/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockCreateCompetitor).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'New Competitor',
            website: 'https://example.com'
          })
        );
      });
    });

    test('submits form with valid data for existing competitor', async () => {
      const mockUpdateCompetitor = vi.fn().mockResolvedValue(mockCompetitor);
      const editContextValue = {
        ...defaultContextValue,
        selectedCompetitor: mockCompetitor,
        updateCompetitor: mockUpdateCompetitor
      };

      render(
        <TestWrapper competitorContextValue={editContextValue}>
          <CompetitorForm competitorId="test-competitor-1" />
        </TestWrapper>
      );

      const submitButton = screen.getByRole('button', { name: /update competitor/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockUpdateCompetitor).toHaveBeenCalledWith(
          'test-competitor-1',
          expect.objectContaining({
            name: 'Test Competitor'
          })
        );
      });
    });

    test('handles form submission errors gracefully', async () => {
      const mockCreateCompetitor = vi.fn().mockRejectedValue(new Error('API Error'));
      const contextValue = {
        ...defaultContextValue,
        createCompetitor: mockCreateCompetitor
      };

      render(
        <TestWrapper competitorContextValue={contextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      // Fill in required fields
      await user.type(screen.getByLabelText(/competitor name/i), 'New Competitor');

      const submitButton = screen.getByRole('button', { name: /create competitor/i });
      await user.click(submitButton);

      // Should handle error gracefully
      await waitFor(() => {
        expect(mockCreateCompetitor).toHaveBeenCalled();
      });
    });
  });

  describe('Wizard Mode', () => {
    test('renders wizard steps when enabled', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm enableWizard={true} />
        </TestWrapper>
      );

      expect(screen.getByText('Basic Information')).toBeInTheDocument();
      expect(screen.getByText('Company Details')).toBeInTheDocument();
      expect(screen.getByText('Social Media')).toBeInTheDocument();
      expect(screen.getByText('Review')).toBeInTheDocument();
    });

    test('allows navigation between wizard steps', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm enableWizard={true} />
        </TestWrapper>
      );

      // Fill in required field for first step
      await user.type(screen.getByLabelText(/competitor name/i), 'Test Company');

      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);

      // Should move to next step
      expect(screen.getByText('Company Details')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has no accessibility violations', async () => {
      const { container } = render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('supports keyboard navigation', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      // Test tab navigation
      await user.tab();
      expect(screen.getByLabelText(/competitor name/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/website/i)).toHaveFocus();
    });

    test('has proper ARIA labels and roles', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Competitor form');
      expect(screen.getByRole('form')).toBeInTheDocument();
    });
  });

  describe('Plan Integration', () => {
    test('shows plan-specific features for creator plan', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm userPlan="creator" />
        </TestWrapper>
      );

      expect(screen.getByText(/you.*re using the creator plan/i)).toBeInTheDocument();
    });

    test('enables advanced features for higher tier plans', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm userPlan="dominator" enableValidation={true} />
        </TestWrapper>
      );

      // Should have access to all features
      expect(screen.getByLabelText(/competitor name/i)).toBeInTheDocument();
    });
  });

  describe('Auto-save and Data Persistence', () => {
    test('enables auto-save when configured', () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm enableAutoSave={true} />
        </TestWrapper>
      );

      // Component should render without issues
      expect(screen.getByText('Add New Competitor')).toBeInTheDocument();
    });

    test('shows unsaved changes warning', async () => {
      render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      // Make changes to form
      await user.type(screen.getByLabelText(/competitor name/i), 'Test');

      // Try to cancel
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);

      expect(screen.getByTestId('confirmation-dialog')).toBeInTheDocument();
      expect(screen.getByText('Unsaved Changes')).toBeInTheDocument();
    });
  });

  describe('Performance and Optimization', () => {
    test('memoizes expensive calculations', () => {
      const { rerender } = render(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      // Re-render with same props should not cause unnecessary recalculations
      rerender(
        <TestWrapper competitorContextValue={defaultContextValue}>
          <CompetitorForm />
        </TestWrapper>
      );

      // Component should render without issues
      expect(screen.getByText('Add New Competitor')).toBeInTheDocument();
    });
  });
});

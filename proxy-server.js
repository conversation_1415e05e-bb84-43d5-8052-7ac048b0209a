// @since 2024-1-1 to 2025-25-7
const express = require('express');
const path = require('path');
const http = require('http');

const app = express();

// Serve static files from the frontend build
app.use(express.static(path.join(__dirname, 'frontend-dist-fixed')));

// Simple proxy for API requests
app.use('/api', (req, res) => {
  const options = {
    hostname: 'localhost',
    port: 8001,
    path: req.url,
    method: req.method,
    headers: req.headers
  };

  const proxyReq = http.request(options, (proxyRes) => {
    res.writeHead(proxyRes.statusCode, proxyRes.headers);
    proxyRes.pipe(res);
  });

  proxyReq.on('error', (err) => {
    console.error('Proxy error:', err);
    res.status(500).send('Proxy error');
  });

  req.pipe(proxyReq);
});

// Catch all handler for SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend-dist-fixed', 'index.html'));
});

const PORT = 3004;
app.listen(PORT, () => {
  console.log(`Proxy server running on http://localhost:${PORT}`);
  console.log(`Frontend: Serving static files from frontend-dist-fixed`);
  console.log(`Backend: Proxying /api requests to http://localhost:8001`);
});

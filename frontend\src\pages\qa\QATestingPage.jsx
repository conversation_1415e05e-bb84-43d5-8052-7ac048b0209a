/**
 * QA Testing Page
 * 
 * Comprehensive page for testing end-to-end workflows including:
 * - Form submission and validation
 * - API integration testing
 * - Data display and manipulation
 * - Error handling and user feedback
 @since 2024-1-1 to 2025-25-7
*/

import { useState, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Button,
  Alert,
  Chip,
  Divider,
  Paper
} from '@mui/material';
import {
  BugReport as BugReportIcon,
  DataUsage as DataUsageIcon,
  Api as ApiIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import QATestForm from '../../components/qa/QATestForm';
import QADataDisplay from '../../components/qa/QADataDisplay';
import { useAuth } from '../../hooks/useAuth';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

const QATestingPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const { user } = useAuth();
  const { showNotification } = useNotification();

  // Handle form submission for different data types
  const handleFormSubmit = useCallback(async (formData) => {
    try {
      let response;
      
      switch (formData.category) {
        case 'content':
          response = await api.content.create({
            title: formData.title,
            text_content: formData.description,
            platforms: formData.platforms,
            tags: formData.tags,
            status: 'draft',
            metadata: {
              priority: formData.priority,
              target_date: formData.targetDate,
              test_data: true
            }
          });
          break;
          
        case 'campaign': {
          // First create an ICP if needed
          const icpResponse = await api.icp.create({
            name: `Test ICP for ${formData.title}`,
            description: formData.description,
            demographics: {
              age_range: "25-45",
              location: "Global"
            },
            decision_maker: {
              title: "Manager",
              department: "Marketing"
            }
          });
          
          response = await api.campaigns.create({
            name: formData.title,
            description: formData.description,
            icp_id: icpResponse.id,
            platforms: formData.platforms,
            goals: [{
              type: 'engagement',
              target_value: 1000
            }],
            start_date: new Date().toISOString(),
            end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          });
          break;
        }

        case 'messaging': {
          // Create conversation first
          const conversationResponse = await api.messaging.createConversation({
            title: formData.title,
            platform: formData.platforms[0] || 'linkedin'
          });
          
          response = await api.messaging.sendMessage({
            conversation_id: conversationResponse.id,
            content: formData.description,
            sender_type: 'user'
          });
          break;
        }
          
        case 'collaboration':
          response = await api.collaboration.createWorkspace({
            name: formData.title,
            description: formData.description,
            requires_approval: formData.priority === 'critical',
            expiration_date: formData.targetDate
          });
          break;
          
        default:
          throw new Error(`Unsupported category: ${formData.category}`);
      }
      
      // Log test result
      const testResult = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        category: formData.category,
        action: 'create',
        status: 'success',
        data: formData,
        response: response
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]); // Keep last 10 results
      
      return response;
      
    } catch (error) {
      // Log test failure
      const testResult = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        category: formData.category,
        action: 'create',
        status: 'error',
        data: formData,
        error: error.message || 'Unknown error'
      };
      
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
      
      throw error;
    }
  }, []);

  // Data source functions for different data types
  const getContentData = useCallback(async () => {
    return await api.content.getAll();
  }, []);

  const getCampaignData = useCallback(async () => {
    return await api.campaigns.getAll();
  }, []);

  const getMessagingData = useCallback(async () => {
    return await api.messaging.getConversations();
  }, []);

  const getUserData = useCallback(async () => {
    return await api.users.getAll();
  }, []);

  // Handle item actions from data display
  const handleItemAction = useCallback(async (action, item) => {
    const testResult = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      category: 'data_action',
      action: action,
      status: 'success',
      data: { item_id: item.id, item_type: item.type || 'unknown' }
    };

    try {
      switch (action) {
        case 'view':
          showNotification(`Viewing item: ${item.title || item.name || item.id}`, 'info');
          break;
        case 'edit':
          showNotification(`Edit functionality would open for: ${item.title || item.name || item.id}`, 'info');
          break;
        case 'delete':
          // Simulate delete operation
          showNotification(`Item deleted: ${item.title || item.name || item.id}`, 'success');
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }
      
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
      
    } catch (error) {
      testResult.status = 'error';
      testResult.error = error.message;
      setTestResults(prev => [testResult, ...prev.slice(0, 9)]);
      throw error;
    }
  }, [showNotification]);

  // Run comprehensive automated tests
  const runAutomatedTests = async () => {
    setIsRunningTests(true);
    const testSuite = [
      {
        name: 'Content Creation Test',
        test: () => handleFormSubmit({
          title: 'Automated Test Content',
          description: 'This is an automated test for content creation workflow',
          category: 'content',
          platforms: ['linkedin', 'twitter'],
          tags: ['automated', 'test'],
          priority: 'medium',
          email: user?.email || '<EMAIL>',
          targetDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        })
      },
      {
        name: 'Data Retrieval Test',
        test: async () => {
          const data = await getContentData();
          if (!Array.isArray(data) && !data.items) {
            throw new Error('Invalid data format returned');
          }
          return { message: 'Data retrieval successful', count: data.length || data.items?.length || 0 };
        }
      },
      {
        name: 'Error Handling Test',
        test: async () => {
          try {
            await api.content.create({
              title: '', // Invalid empty title
              text_content: 'Test content'
            });
            throw new Error('Validation should have failed');
          } catch (error) {
            if (error.response?.status === 422 || error.response?.status === 400) {
              return { message: 'Error handling working correctly' };
            }
            throw error;
          }
        }
      }
    ];

    for (const test of testSuite) {
      try {
        const result = await test.test();
        setTestResults(prev => [{
          id: Date.now() + Math.random(),
          timestamp: new Date().toISOString(),
          category: 'automated_test',
          action: test.name,
          status: 'success',
          data: result
        }, ...prev.slice(0, 8)]);
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        setTestResults(prev => [{
          id: Date.now() + Math.random(),
          timestamp: new Date().toISOString(),
          category: 'automated_test',
          action: test.name,
          status: 'error',
          error: error.message
        }, ...prev.slice(0, 8)]);
      }
    }
    
    setIsRunningTests(false);
    showNotification('Automated test suite completed', 'info');
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const tabData = [
    { label: 'Form Testing', icon: <BugReportIcon /> },
    { label: 'Data Display', icon: <DataUsageIcon /> },
    { label: 'API Testing', icon: <ApiIcon /> },
    { label: 'Test Results', icon: <AssessmentIcon /> }
  ];

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        QA Testing Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Comprehensive testing environment for validating end-to-end workflows,
        API integrations, and data flow validation.
      </Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          {tabData.map((tab, index) => (
            <Tab
              key={index}
              label={tab.label}
              icon={tab.icon}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      {/* Form Testing Tab */}
      {activeTab === 0 && (
        <Box>
          <QATestForm
            onSubmit={handleFormSubmit}
            testMode="create"
          />
        </Box>
      )}

      {/* Data Display Tab */}
      {activeTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} lg={6}>
            <QADataDisplay
              dataSource={getContentData}
              dataType="content"
              onItemAction={handleItemAction}
              enableRealTimeUpdates={true}
            />
          </Grid>
          <Grid item xs={12} lg={6}>
            <QADataDisplay
              dataSource={getCampaignData}
              dataType="campaigns"
              onItemAction={handleItemAction}
            />
          </Grid>
          <Grid item xs={12} lg={6}>
            <QADataDisplay
              dataSource={getMessagingData}
              dataType="conversations"
              onItemAction={handleItemAction}
            />
          </Grid>
          <Grid item xs={12} lg={6}>
            <QADataDisplay
              dataSource={getUserData}
              dataType="users"
              onItemAction={handleItemAction}
            />
          </Grid>
        </Grid>
      )}

      {/* API Testing Tab */}
      {activeTab === 2 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Automated API Testing
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Run comprehensive automated tests to validate API endpoints,
              error handling, and data validation.
            </Typography>
            
            <Button
              variant="contained"
              onClick={runAutomatedTests}
              disabled={isRunningTests}
              startIcon={<ApiIcon />}
              sx={{ mb: 2 }}
            >
              {isRunningTests ? 'Running Tests...' : 'Run Automated Test Suite'}
            </Button>

            <Divider sx={{ my: 2 }} />

            <Typography variant="h6" gutterBottom>
              Manual API Testing
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => getContentData().then(data => 
                    showNotification(`Retrieved ${data.length || 0} content items`, 'success')
                  )}
                >
                  Test Content API
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => getCampaignData().then(data => 
                    showNotification(`Retrieved ${data.length || 0} campaigns`, 'success')
                  )}
                >
                  Test Campaign API
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => getMessagingData().then(data => 
                    showNotification(`Retrieved ${data.length || 0} conversations`, 'success')
                  )}
                >
                  Test Messaging API
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => getUserData().then(data => 
                    showNotification(`Retrieved ${data.length || 0} users`, 'success')
                  )}
                >
                  Test User API
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      {/* Test Results Tab */}
      {activeTab === 3 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Test Results History
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Recent test executions and their results
            </Typography>

            {testResults.length === 0 ? (
              <Alert severity="info">
                No test results yet. Run some tests to see results here.
              </Alert>
            ) : (
              <Box>
                {testResults.map((result) => (
                  <Paper key={result.id} sx={{ p: 2, mb: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="subtitle1">
                        {result.action} - {result.category}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Chip
                          label={result.status}
                          color={result.status === 'success' ? 'success' : 'error'}
                          size="small"
                        />
                        <Typography variant="caption" color="text.secondary">
                          {new Date(result.timestamp).toLocaleString()}
                        </Typography>
                      </Box>
                    </Box>
                    
                    {result.error && (
                      <Alert severity="error" sx={{ mb: 1 }}>
                        {result.error}
                      </Alert>
                    )}
                    
                    {result.response && (
                      <Box>
                        <Typography variant="body2" gutterBottom>Response:</Typography>
                        <pre style={{ 
                          fontSize: '0.8rem', 
                          backgroundColor: '#f5f5f5', 
                          padding: '8px', 
                          borderRadius: '4px',
                          overflow: 'auto',
                          maxHeight: '200px'
                        }}>
                          {JSON.stringify(result.response, null, 2)}
                        </pre>
                      </Box>
                    )}
                  </Paper>
                ))}
              </Box>
            )}
          </CardContent>
        </Card>
      )}
    </Container>
  );
};

export default QATestingPage;

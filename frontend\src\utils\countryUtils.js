/**
 * Utility functions for working with world countries data
 * Provides type-safe access and manipulation of country information
 */

import worldCountriesData from '../data/world-countries.json';

// Configuration constants
const CONFIG = {
  // Default values
  DEFAULT_COUNTRY: 'US',
  DEFAULT_CURRENCY: 'USD',
  DEFAULT_LANGUAGE: 'English',
  
  // Validation
  VALID_ISO2_LENGTH: 2,
  VALID_ISO3_LENGTH: 3,
  
  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development'
};

// Enhanced logging utility
const logger = {
  warn: (message, data) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.warn(`[CountryUtils] ${message}`, data);
    }
  },
  error: (message, error) => {
    console.error(`[CountryUtils] ${message}`, error);
  }
};

/**
 * Get all countries
 * @returns {Array} Array of country features
 */
export const getAllCountries = () => {
  try {
    return worldCountriesData.features || [];
  } catch (error) {
    logger.error('Failed to get all countries', error);
    return [];
  }
};

/**
 * Get country by ISO 3166-1 alpha-2 code
 * @param {string} code - Two-letter country code (e.g., 'US', 'CA')
 * @returns {Object|null} Country feature or null if not found
 */
export const getCountryByCode = (code) => {
  try {
    if (!code || typeof code !== 'string' || code.length !== CONFIG.VALID_ISO2_LENGTH) {
      logger.warn('Invalid country code provided', { code });
      return null;
    }
    
    const normalizedCode = code.toUpperCase();
    const countries = getAllCountries();
    
    return countries.find(country => 
      country.properties?.iso_a2 === normalizedCode
    ) || null;
  } catch (error) {
    logger.error('Failed to get country by code', error);
    return null;
  }
};

/**
 * Get country by ISO 3166-1 alpha-3 code
 * @param {string} code3 - Three-letter country code (e.g., 'USA', 'CAN')
 * @returns {Object|null} Country feature or null if not found
 */
export const getCountryByCode3 = (code3) => {
  try {
    if (!code3 || typeof code3 !== 'string' || code3.length !== CONFIG.VALID_ISO3_LENGTH) {
      logger.warn('Invalid country code3 provided', { code3 });
      return null;
    }
    
    const normalizedCode3 = code3.toUpperCase();
    const countries = getAllCountries();
    
    return countries.find(country => 
      country.properties?.iso_a3 === normalizedCode3
    ) || null;
  } catch (error) {
    logger.error('Failed to get country by code3', error);
    return null;
  }
};

/**
 * Get country by name (case-insensitive)
 * @param {string} name - Country name
 * @returns {Object|null} Country feature or null if not found
 */
export const getCountryByName = (name) => {
  try {
    if (!name || typeof name !== 'string') {
      logger.warn('Invalid country name provided', { name });
      return null;
    }
    
    const normalizedName = name.toLowerCase().trim();
    const countries = getAllCountries();
    
    return countries.find(country => {
      const props = country.properties;
      return (
        props?.name?.toLowerCase() === normalizedName ||
        props?.name_long?.toLowerCase() === normalizedName
      );
    }) || null;
  } catch (error) {
    logger.error('Failed to get country by name', error);
    return null;
  }
};

/**
 * Get countries by continent
 * @param {string} continent - Continent name
 * @returns {Array} Array of country features
 */
export const getCountriesByContinent = (continent) => {
  try {
    if (!continent || typeof continent !== 'string') {
      logger.warn('Invalid continent provided', { continent });
      return [];
    }
    
    const normalizedContinent = continent.toLowerCase().trim();
    const countries = getAllCountries();
    
    return countries.filter(country => 
      country.properties?.continent?.toLowerCase() === normalizedContinent
    );
  } catch (error) {
    logger.error('Failed to get countries by continent', error);
    return [];
  }
};

/**
 * Get countries by currency
 * @param {string} currencyCode - ISO 4217 currency code
 * @returns {Array} Array of country features
 */
export const getCountriesByCurrency = (currencyCode) => {
  try {
    if (!currencyCode || typeof currencyCode !== 'string') {
      logger.warn('Invalid currency code provided', { currencyCode });
      return [];
    }
    
    const normalizedCurrency = currencyCode.toUpperCase();
    const countries = getAllCountries();
    
    return countries.filter(country => 
      country.properties?.currency_code === normalizedCurrency
    );
  } catch (error) {
    logger.error('Failed to get countries by currency', error);
    return [];
  }
};

/**
 * Get basic country information
 * @param {string} code - Two-letter country code
 * @returns {Object|null} Basic country info or null if not found
 */
export const getCountryBasicInfo = (code) => {
  try {
    const country = getCountryByCode(code);
    if (!country) return null;
    
    const props = country.properties;
    return {
      name: props.name,
      code: props.iso_a2,
      code3: props.iso_a3,
      flag: props.flag_emoji,
      capital: props.capital,
      continent: props.continent
    };
  } catch (error) {
    logger.error('Failed to get country basic info', error);
    return null;
  }
};

/**
 * Get country location information
 * @param {string} code - Two-letter country code
 * @returns {Object|null} Location info or null if not found
 */
export const getCountryLocationInfo = (code) => {
  try {
    const country = getCountryByCode(code);
    if (!country) return null;
    
    const props = country.properties;
    return {
      name: props.name,
      coordinates: props.coordinates,
      bounds: props.bounds,
      timezone: props.timezone
    };
  } catch (error) {
    logger.error('Failed to get country location info', error);
    return null;
  }
};

/**
 * Get country economic information
 * @param {string} code - Two-letter country code
 * @returns {Object|null} Economic info or null if not found
 */
export const getCountryEconomicInfo = (code) => {
  try {
    const country = getCountryByCode(code);
    if (!country) return null;
    
    const props = country.properties;
    return {
      name: props.name,
      population: props.population,
      area_km2: props.area_km2,
      currency_code: props.currency_code,
      currency_name: props.currency_name,
      calling_code: props.calling_code
    };
  } catch (error) {
    logger.error('Failed to get country economic info', error);
    return null;
  }
};

/**
 * Search countries by partial name
 * @param {string} query - Search query
 * @param {number} limit - Maximum number of results (default: 10)
 * @returns {Array} Array of matching country features
 */
export const searchCountries = (query, limit = 10) => {
  try {
    if (!query || typeof query !== 'string') {
      return [];
    }
    
    const normalizedQuery = query.toLowerCase().trim();
    const countries = getAllCountries();
    
    const matches = countries.filter(country => {
      const props = country.properties;
      return (
        props?.name?.toLowerCase().includes(normalizedQuery) ||
        props?.name_long?.toLowerCase().includes(normalizedQuery) ||
        props?.capital?.toLowerCase().includes(normalizedQuery)
      );
    });
    
    return matches.slice(0, limit);
  } catch (error) {
    logger.error('Failed to search countries', error);
    return [];
  }
};

/**
 * Get metadata about the countries dataset
 * @returns {Object} Dataset metadata
 */
export const getDatasetMetadata = () => {
  try {
    return worldCountriesData.metadata || {};
  } catch (error) {
    logger.error('Failed to get dataset metadata', error);
    return {};
  }
};

/**
 * Validate country code format
 * @param {string} code - Country code to validate
 * @param {string} type - Code type ('iso2' or 'iso3')
 * @returns {boolean} True if valid format
 */
export const isValidCountryCode = (code, type = 'iso2') => {
  if (!code || typeof code !== 'string') return false;
  
  if (type === 'iso2') {
    return code.length === CONFIG.VALID_ISO2_LENGTH && /^[A-Z]{2}$/.test(code.toUpperCase());
  } else if (type === 'iso3') {
    return code.length === CONFIG.VALID_ISO3_LENGTH && /^[A-Z]{3}$/.test(code.toUpperCase());
  }
  
  return false;
};

// Export all utility functions
export default {
  getAllCountries,
  getCountryByCode,
  getCountryByCode3,
  getCountryByName,
  getCountriesByContinent,
  getCountriesByCurrency,
  getCountryBasicInfo,
  getCountryLocationInfo,
  getCountryEconomicInfo,
  searchCountries,
  getDatasetMetadata,
  isValidCountryCode
};

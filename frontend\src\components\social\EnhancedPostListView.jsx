/**
 * Enhanced Post List View - Enterprise-grade post management component
 * Features: Comprehensive post list management system with advanced filtering and sorting,
 * real-time post synchronization, post engagement analytics with metrics visualization,
 * subscription-based feature gating, advanced post filtering by platform and status,
 * bulk post operations, post preview functionality, and ACE Social platform integration
 * with advanced post management capabilities and seamless social media workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Chip,
  Avatar,
  Badge,
  Skeleton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  useTheme,
  alpha,
  useMediaQuery
} from '@mui/material';
import {
  Search as SearchIcon,
  Comment as CommentIcon,
  ThumbUp as ThumbUpIcon,
  Share as ShareIcon
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';

import platformService from '../../services/platformService';
import { useSubscription } from '../../contexts/SubscriptionContext';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Plan-based post management limitations
const PLAN_LIMITS = {
  1: { // Creator
    maxPostsPerView: 50,
    postAnalytics: false,
    advancedFiltering: false,
    bulkOperations: false,
    postScheduling: false,
    crossPlatformPosting: false,
    postPreview: false,
    realTimeSync: false,
    postExport: false,
    customViews: false
  },
  2: { // Accelerator
    maxPostsPerView: 200,
    postAnalytics: true,
    advancedFiltering: true,
    bulkOperations: true,
    postScheduling: true,
    crossPlatformPosting: true,
    postPreview: true,
    realTimeSync: true,
    postExport: false,
    customViews: true
  },
  3: { // Dominator
    maxPostsPerView: 1000,
    postAnalytics: true,
    advancedFiltering: true,
    bulkOperations: true,
    postScheduling: true,
    crossPlatformPosting: true,
    postPreview: true,
    realTimeSync: true,
    postExport: true,
    customViews: true
  }
};

// Post states and filters (for future use)
// const POST_STATES = {
//   DRAFT: 'draft',
//   SCHEDULED: 'scheduled',
//   PUBLISHED: 'published',
//   FAILED: 'failed',
//   ARCHIVED: 'archived'
// };

const SORT_OPTIONS = {
  NEWEST: 'newest',
  OLDEST: 'oldest',
  ENGAGEMENT: 'engagement',
  PRIORITY: 'priority',
  PLATFORM: 'platform',
  STATUS: 'status'
};

const VIEW_MODES = {
  GRID: 'grid',
  LIST: 'list',
  COMPACT: 'compact',
  DETAILED: 'detailed'
};

/**
 * Enhanced Post List View - Comprehensive post management with advanced features
 * Implements plan-based feature restrictions and enterprise-grade post capabilities
 */
const EnhancedPostListView = memo(forwardRef(({
  posts = [],
  loading = false,
  onPostSelect,
  selectedPostId,
  searchQuery = '',
  onSearchChange,
  filterPlatform = 'all',
  onFilterChange,
  maxDisplayPosts = 100,
  autoRefreshInterval = 30000,
  defaultViewMode = VIEW_MODES.GRID
}, ref) => {
  const theme = useTheme();
  const { getPlanTier } = useSubscription();

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Responsive design
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // Core state management
  const wsConnectionRef = useRef(null);

  const [viewMode] = useState(defaultViewMode);
  const [sortOption] = useState(SORT_OPTIONS.NEWEST);
  const [selectedPosts] = useState(new Set());
  const [filterStatus] = useState('all');
  const [filterEngagement] = useState('all');
  const [filterDateRange] = useState('all');
  const [, setRealTimeUpdates] = useState([]);
  const [postAnalytics] = useState({});
  // const [refreshing, setRefreshing] = useState(false);

  // Plan tier and feature access
  const planTier = getPlanTier();
  const planLimits = PLAN_LIMITS[planTier] || PLAN_LIMITS[1];

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    refreshPosts: () => handleRefreshPosts(),
    getSelectedPosts: () => Array.from(selectedPosts),
    searchPosts: (query) => onSearchChange && onSearchChange(query),
    filterPosts: (platform) => onFilterChange && onFilterChange(platform),
    getPostAnalytics: () => postAnalytics,
    getCurrentView: () => ({ viewMode, sortOption, filterPlatform, filterStatus })
  }), [selectedPosts, postAnalytics, viewMode, sortOption, filterPlatform, filterStatus, onSearchChange, onFilterChange, handleRefreshPosts]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Real-time WebSocket connection
  useEffect(() => {
    if (planLimits.realTimeSync) {
      const connectWebSocket = () => {
        try {
          const ws = new WebSocket(`${process.env.REACT_APP_WS_URL}/posts`);

          ws.onopen = () => {
            console.log('Post WebSocket connected');
            announceToScreenReader('Real-time post updates enabled');
          };

          ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            if (data.type === 'post_update') {
              setRealTimeUpdates(prev => [...prev, data.payload].slice(-10));
              announceToScreenReader(`Post update received`);
            }
          };

          ws.onclose = () => {
            console.log('Post WebSocket disconnected');
            setTimeout(connectWebSocket, 5000);
          };

          wsConnectionRef.current = ws;
        } catch (error) {
          console.error('WebSocket connection failed:', error);
        }
      };

      connectWebSocket();

      return () => {
        if (wsConnectionRef.current) {
          wsConnectionRef.current.close();
        }
      };
    }
  }, [planLimits.realTimeSync, announceToScreenReader]);

  // Auto-refresh posts
  useEffect(() => {
    if (autoRefreshInterval > 0) {
      const interval = setInterval(() => {
        handleRefreshPosts();
      }, autoRefreshInterval);

      return () => clearInterval(interval);
    }
  }, [autoRefreshInterval, handleRefreshPosts]);

  // Enhanced action handlers
  const handleRefreshPosts = useCallback(async () => {
    try {
      // Trigger refresh through parent component
      if (onSearchChange) {
        onSearchChange(searchQuery);
      }
      announceToScreenReader('Posts refreshed');
    } catch (error) {
      console.error('Failed to refresh posts:', error);
    }
  }, [onSearchChange, searchQuery, announceToScreenReader]);

  // Handlers for future use
  // const handleSelectAllPosts = useCallback(() => {
  //   if (!planLimits.bulkOperations) {
  //     announceToScreenReader('Bulk operations are not available in your current plan');
  //     return;
  //   }
  //   const allPostIds = new Set(filteredPosts.map(post => post.id));
  //   announceToScreenReader(`Selected ${allPostIds.size} posts`);
  // }, [planLimits.bulkOperations, filteredPosts, announceToScreenReader]);

  // Platform icon mapping using centralized service
  const getPlatformIcon = useCallback((platform) => {
    if (!platform) return <CommentIcon fontSize="small" sx={{ color: ACE_COLORS.PURPLE }} />;

    try {
      return platformService.getPlatformIcon(platform, {
        fontSize: 'small',
        sx: { color: ACE_COLORS.PURPLE }
      });
    } catch (error) {
      console.warn(`Failed to get platform icon for ${platform}:`, error);
      return <CommentIcon fontSize="small" sx={{ color: ACE_COLORS.PURPLE }} />;
    }
  }, []);

  // Format engagement numbers using centralized service
  const formatEngagement = useCallback((platform, engagementType, value) => {
    try {
      return platformService.formatEngagement(platform, engagementType, value);
    } catch (error) {
      console.warn(`Failed to format engagement for ${platform}:`, error);
      // Fallback formatting
      if (!value) return '0';
      if (value >= 1000000) return (value / 1000000).toFixed(1) + 'M';
      if (value >= 1000) return (value / 1000).toFixed(1) + 'K';
      return value.toString();
    }
  }, []);

  // Enhanced priority calculation with analytics
  const getPriority = useCallback((post) => {
    const pendingCount = post.pending_comments_count || 0;
    const engagement = (post.likes || 0) + (post.comments || 0) + (post.shares || 0);
    const hoursSincePost = post.post_date ?
      (Date.now() - new Date(post.post_date).getTime()) / (1000 * 60 * 60) : 0;

    // Enhanced priority calculation
    let score = 0;
    score += pendingCount * 10; // Pending comments are high priority
    score += Math.min(engagement / 100, 10); // Engagement score (capped at 10)
    score += Math.max(0, 5 - hoursSincePost / 24); // Recency bonus (up to 5 days)

    if (score >= 15) return 'critical';
    if (score >= 10) return 'high';
    if (score >= 5) return 'medium';
    return 'low';
  }, []);

  // Enhanced priority color with critical level
  const getPriorityColor = useCallback((priority) => {
    switch (priority) {
      case 'critical': return '#d32f2f'; // Red 700
      case 'high': return theme.palette.error.main;
      case 'medium': return theme.palette.warning.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  }, [theme]);

  // Enhanced filtering and sorting with advanced options
  const filteredPosts = useMemo(() => {
    let filtered = [...posts];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(post =>
        post.content_text?.toLowerCase().includes(query) ||
        post.profile_name?.toLowerCase().includes(query) ||
        post.hashtags?.some(tag => tag.toLowerCase().includes(query)) ||
        post.platform?.toLowerCase().includes(query)
      );
    }

    // Apply platform filter
    if (filterPlatform !== 'all') {
      filtered = filtered.filter(post =>
        post.platform?.toLowerCase() === filterPlatform.toLowerCase()
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(post => post.status === filterStatus);
    }

    // Apply engagement filter
    if (filterEngagement !== 'all') {
      const engagementThresholds = {
        low: [0, 100],
        medium: [100, 1000],
        high: [1000, Infinity]
      };

      if (engagementThresholds[filterEngagement]) {
        const [min, max] = engagementThresholds[filterEngagement];
        filtered = filtered.filter(post => {
          const engagement = (post.likes || 0) + (post.comments || 0) + (post.shares || 0);
          return engagement >= min && engagement < max;
        });
      }
    }

    // Apply date range filter
    if (filterDateRange !== 'all') {
      const now = new Date();
      const dateThresholds = {
        today: 1,
        week: 7,
        month: 30,
        quarter: 90
      };

      if (dateThresholds[filterDateRange]) {
        const daysAgo = dateThresholds[filterDateRange];
        const cutoffDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
        filtered = filtered.filter(post =>
          post.post_date && new Date(post.post_date) >= cutoffDate
        );
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortOption) {
        case SORT_OPTIONS.NEWEST:
          return new Date(b.post_date || 0) - new Date(a.post_date || 0);
        case SORT_OPTIONS.OLDEST:
          return new Date(a.post_date || 0) - new Date(b.post_date || 0);
        case SORT_OPTIONS.ENGAGEMENT: {
          const aEngagement = (a.likes || 0) + (a.comments || 0) + (a.shares || 0);
          const bEngagement = (b.likes || 0) + (b.comments || 0) + (b.shares || 0);
          return bEngagement - aEngagement;
        }
        case SORT_OPTIONS.PRIORITY: {
          const aPriority = getPriority(a);
          const bPriority = getPriority(b);
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          return (priorityOrder[bPriority] || 0) - (priorityOrder[aPriority] || 0);
        }
        case SORT_OPTIONS.PLATFORM:
          return (a.platform || '').localeCompare(b.platform || '');
        case SORT_OPTIONS.STATUS:
          return (a.status || '').localeCompare(b.status || '');
        default:
          return 0;
      }
    });

    // Limit display count based on plan
    return filtered.slice(0, Math.min(maxDisplayPosts, planLimits.maxPostsPerView));
  }, [
    posts,
    searchQuery,
    filterPlatform,
    filterStatus,
    filterEngagement,
    filterDateRange,
    sortOption,
    maxDisplayPosts,
    planLimits.maxPostsPerView,
    getPriority
  ]);

  // Enhanced loading skeleton with glass morphism
  const renderSkeleton = useCallback(() => (
    <Grid container spacing={2}>
      {[...Array(isMobile ? 3 : isTablet ? 4 : 6)].map((_, index) => (
        <Grid item xs={12} sm={6} md={viewMode === VIEW_MODES.LIST ? 12 : 4} key={index}>
          <Card sx={{ ...glassMorphismStyles }}>
            <Skeleton
              variant="rectangular"
              height={viewMode === VIEW_MODES.COMPACT ? 120 : 200}
              sx={{ backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1) }}
            />
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Skeleton variant="circular" width={20} height={20} />
                <Skeleton variant="rectangular" width={60} height={20} sx={{ borderRadius: 1 }} />
              </Box>
              <Skeleton variant="text" height={24} sx={{ mb: 1 }} />
              <Skeleton variant="text" height={20} width="80%" sx={{ mb: 1 }} />
              <Skeleton variant="text" height={20} width="60%" sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Skeleton variant="circular" width={16} height={16} />
                  <Skeleton variant="circular" width={16} height={16} />
                  <Skeleton variant="circular" width={16} height={16} />
                </Box>
                <Skeleton variant="circular" width={24} height={24} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  ), [isMobile, isTablet, viewMode, glassMorphismStyles]);

  // Enhanced post card renderer with advanced features
  const renderPostCard = useCallback((post) => {
    const priority = getPriority(post);
    const isSelected = selectedPostId === post.id;

    return (
      <Card
        key={post.id}
        onClick={() => onPostSelect && onPostSelect(post)}
        sx={{
          cursor: 'pointer',
          transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
          border: isSelected ? `2px solid ${ACE_COLORS.PURPLE}` : `1px solid ${alpha(ACE_COLORS.PURPLE, 0.1)}`,
          transform: isSelected ? 'scale(1.02)' : 'scale(1)',
          boxShadow: isSelected ? `0 8px 32px ${alpha(ACE_COLORS.PURPLE, 0.3)}` : `0 4px 16px ${alpha(theme.palette.common.black, 0.1)}`,
          ...glassMorphismStyles,
          position: 'relative',
          overflow: 'visible',
          '&:hover': {
            transform: 'scale(1.02)',
            boxShadow: `0 12px 40px ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
            borderColor: alpha(ACE_COLORS.PURPLE, 0.4)
          }
        }}
      >
        {/* Post image/media */}
        {post.image_url && (
          <CardMedia
            component="img"
            height="160"
            image={post.image_url}
            alt="Post content"
            sx={{ objectFit: 'cover' }}
          />
        )}
        
        <CardContent sx={{ pb: 2 }}>
          {/* Header with platform and priority */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {getPlatformIcon(post.platform)}
              <Typography variant="caption" color="textSecondary" sx={{ textTransform: 'capitalize' }}>
                {post.platform}
              </Typography>
            </Box>
            
            <Tooltip title={`${priority} priority`}>
              <Chip
                size="small"
                label={priority}
                sx={{
                  backgroundColor: alpha(getPriorityColor(priority), 0.1),
                  color: getPriorityColor(priority),
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  fontSize: '0.7rem'
                }}
              />
            </Tooltip>
          </Box>

          {/* Post content preview */}
          <Typography 
            variant="body2" 
            sx={{ 
              mb: 2,
              display: '-webkit-box',
              WebkitLineClamp: 3,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              lineHeight: 1.4
            }}
          >
            {post.content_text || 'No text content'}
          </Typography>

          {/* Author info */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar 
              src={post.profile_image} 
              sx={{ width: 24, height: 24, mr: 1 }}
            >
              {post.profile_name?.charAt(0)}
            </Avatar>
            <Typography variant="caption" color="textSecondary">
              {post.profile_name}
            </Typography>
          </Box>

          {/* Engagement stats */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <ThumbUpIcon fontSize="small" color="action" />
                <Typography variant="caption">{formatEngagement(post.platform, 'likes', post.likes)}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <CommentIcon fontSize="small" color="action" />
                <Typography variant="caption">{formatEngagement(post.platform, 'comments', post.comments)}</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <ShareIcon fontSize="small" color="action" />
                <Typography variant="caption">{formatEngagement(post.platform, 'shares', post.shares)}</Typography>
              </Box>
            </Box>

            {/* Pending comments badge */}
            <Badge 
              badgeContent={post.pending_comments_count || 0} 
              color="primary"
              sx={{
                '& .MuiBadge-badge': {
                  backgroundColor: theme.palette.warning.main,
                  color: theme.palette.warning.contrastText
                }
              }}
            >
              <CommentIcon color="action" />
            </Badge>
          </Box>

          {/* Post date */}
          <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 1 }}>
            {post.post_date ? formatDistanceToNow(new Date(post.post_date), { addSuffix: true }) : 'Unknown date'}
          </Typography>
        </CardContent>
      </Card>
    );
  }, [
    getPriority,
    selectedPostId,
    onPostSelect,
    glassMorphismStyles,
    theme,
    getPlatformIcon,
    formatEngagement,
    getPriorityColor
  ]);

  if (loading) {
    return renderSkeleton();
  }

  return (
    <Box>
      {/* Search and Filter Controls */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <TextField
          placeholder="Search posts..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          size="small"
          sx={{ minWidth: 250, flexGrow: 1 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Platform</InputLabel>
          <Select
            value={filterPlatform}
            onChange={(e) => onFilterChange(e.target.value)}
            label="Platform"
          >
            <MenuItem value="all">All Platforms</MenuItem>
            <MenuItem value="facebook">Facebook</MenuItem>
            <MenuItem value="twitter">Twitter</MenuItem>
            <MenuItem value="linkedin">LinkedIn</MenuItem>
            <MenuItem value="instagram">Instagram</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Posts Grid */}
      {filteredPosts.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <CommentIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No posts found
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {searchQuery || filterPlatform !== 'all' 
              ? 'Try adjusting your search or filter criteria'
              : 'No posts with pending comments available'
            }
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {filteredPosts.map((post) => (
            <Grid item xs={12} sm={6} md={4} key={post.id}>
              {renderPostCard(post)}
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
}));

EnhancedPostListView.displayName = 'EnhancedPostListView';

EnhancedPostListView.propTypes = {
  /** Array of posts to display */
  posts: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    content_text: PropTypes.string,
    platform: PropTypes.string,
    profile_name: PropTypes.string,
    profile_image: PropTypes.string,
    image_url: PropTypes.string,
    post_date: PropTypes.string,
    likes: PropTypes.number,
    comments: PropTypes.number,
    shares: PropTypes.number,
    pending_comments_count: PropTypes.number,
    status: PropTypes.string,
    hashtags: PropTypes.arrayOf(PropTypes.string)
  })),
  /** Loading state */
  loading: PropTypes.bool,
  /** Post selection callback */
  onPostSelect: PropTypes.func,
  /** Currently selected post ID */
  selectedPostId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /** Search query */
  searchQuery: PropTypes.string,
  /** Search change callback */
  onSearchChange: PropTypes.func,
  /** Platform filter */
  filterPlatform: PropTypes.string,
  /** Filter change callback */
  onFilterChange: PropTypes.func,
  /** Bulk action callback */
  onBulkAction: PropTypes.func,
  /** Post preview callback */
  onPostPreview: PropTypes.func,
  /** Post edit callback */
  onPostEdit: PropTypes.func,
  /** Post delete callback */
  onPostDelete: PropTypes.func,
  /** Post analytics callback */
  onPostAnalytics: PropTypes.func,
  /** Export posts callback */
  onExportPosts: PropTypes.func,
  /** Enable real-time sync */
  enableRealTimeSync: PropTypes.bool,
  /** Enable bulk operations */
  enableBulkOperations: PropTypes.bool,
  /** Enable advanced filtering */
  enableAdvancedFiltering: PropTypes.bool,
  /** Maximum posts to display */
  maxDisplayPosts: PropTypes.number,
  /** Auto refresh interval in milliseconds */
  autoRefreshInterval: PropTypes.number,
  /** Default view mode */
  defaultViewMode: PropTypes.oneOf(['grid', 'list', 'compact', 'detailed']),
  /** Show engagement metrics */
  showEngagementMetrics: PropTypes.bool,
  /** Show priority indicators */
  showPriorityIndicators: PropTypes.bool
};

export default EnhancedPostListView;

/**
 * Snackbar Context
 * Production-ready snackbar system with queuing, persistence, and advanced features
 * Comprehensive error handling, accessibility, and performance optimization
 @since 2024-1-1 to 2025-25-7
*/

import { createContext, useState, useContext, useCallback, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Al<PERSON>, Slide } from '@mui/material';
import PropTypes from 'prop-types';
import { v4 as uuidv4 } from 'uuid';

// Configuration constants
const CONFIG = {
  // Default durations by severity
  DEFAULT_DURATIONS: {
    success: 4000,
    info: 6000,
    warning: 8000,
    error: 10000,
  },

  // Queue settings
  MAX_SNACKBARS: 3,
  MAX_QUEUE_SIZE: 10,

  // Animation settings
  TRANSITION_DURATION: 300,
  STACK_SPACING: 60,

  // Auto-dismiss settings
  AUTO_DISMISS_DELAY: 100,

  // Environment
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production'
};

// Enhanced logging utility
const logger = {
  debug: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.log(`[SnackbarContext] ${message}`, data, ...args);
    }
  },
  info: (message, data, ...args) => {
    if (CONFIG.IS_DEVELOPMENT) {
      console.info(`[SnackbarContext] ${message}`, data, ...args);
    }

    // Send to analytics in production
    if (CONFIG.IS_PRODUCTION && window.analytics) {
      window.analytics.track('Snackbar Context Info', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  warn: (message, data, ...args) => {
    console.warn(`[SnackbarContext] ${message}`, data, ...args);

    // Always send warnings to analytics
    if (window.analytics) {
      window.analytics.track('Snackbar Context Warning', {
        message,
        data,
        timestamp: new Date().toISOString()
      });
    }
  },
  error: (message, error, ...args) => {
    console.error(`[SnackbarContext] ${message}`, error, ...args);

    // Always send errors to analytics
    if (window.analytics) {
      window.analytics.track('Snackbar Context Error', {
        message,
        error: error?.message || error,
        stack: error?.stack,
        timestamp: new Date().toISOString()
      });
    }
  }
};

// Transition component for animations
const SlideTransition = (props) => {
  return <Slide {...props} direction="up" />;
};

// Create context
const SnackbarContext = createContext();

// Custom hook to use the snackbar context
// eslint-disable-next-line react-refresh/only-export-components
export const useSnackbar = () => {
  const context = useContext(SnackbarContext);
  if (!context) {
    throw new Error('useSnackbar must be used within a SnackbarProvider');
  }
  return context;
};

// Provider component
export const SnackbarProvider = ({ children }) => {
  // Enhanced state management
  const [snackbars, setSnackbars] = useState([]);
  const [queue, setQueue] = useState([]);
  const [isPaused, setIsPaused] = useState(false);
  const timeoutRefs = useRef(new Map());
  const queueProcessorRef = useRef(null);

  // Process snackbar queue
  const processQueue = useCallback(() => {
    if (queue.length > 0 && !isPaused) {
      const visibleSnackbars = snackbars.filter(s => s.isVisible);
      const availableSlots = CONFIG.MAX_SNACKBARS - visibleSnackbars.length;

      if (availableSlots > 0) {
        const nextSnackbar = queue[0];
        if (nextSnackbar) {
          setQueue(prev => prev.slice(1));
          setSnackbars(prev => [...prev, { ...nextSnackbar, isVisible: true }]);

          // Set up auto-dismiss timer
          if (nextSnackbar.autoHideDuration > 0) {
            const timeoutId = setTimeout(() => {
              dismissSnackbar(nextSnackbar.id);
            }, nextSnackbar.autoHideDuration + CONFIG.AUTO_DISMISS_DELAY);

            timeoutRefs.current.set(nextSnackbar.id, timeoutId);
          }
        }
      }
    }
  }, [queue, snackbars, isPaused, dismissSnackbar]);

  // Process queue when snackbars change
  useEffect(() => {
    if (queueProcessorRef.current) {
      clearTimeout(queueProcessorRef.current);
    }

    queueProcessorRef.current = setTimeout(() => {
      processQueue();
    }, 50); // Small delay to batch updates

    return () => {
      if (queueProcessorRef.current) {
        clearTimeout(queueProcessorRef.current);
      }
    };
  }, [processQueue]);

  // Cleanup timers on unmount
  useEffect(() => {
    const timeoutRefsSnapshot = timeoutRefs.current;
    const queueProcessorSnapshot = queueProcessorRef.current;

    return () => {
      timeoutRefsSnapshot.forEach(timeoutId => clearTimeout(timeoutId));
      timeoutRefsSnapshot.clear();
      if (queueProcessorSnapshot) {
        clearTimeout(queueProcessorSnapshot);
      }
    };
  }, []);

  // Dismiss snackbar
  const dismissSnackbar = useCallback((id) => {
    if (!id) {
      logger.warn('Cannot dismiss snackbar - no ID provided');
      return;
    }

    logger.debug('Dismissing snackbar', { id });

    // Clear timeout if exists
    if (timeoutRefs.current.has(id)) {
      clearTimeout(timeoutRefs.current.get(id));
      timeoutRefs.current.delete(id);
    }

    // Remove from snackbars
    setSnackbars(prev => prev.filter(s => s.id !== id));
  }, []);

  // Enhanced show snackbar function
  const showSnackbar = useCallback((message, options = {}) => {
    try {
      if (!message || typeof message !== 'string') {
        logger.warn('Invalid snackbar message provided', { message });
        return null;
      }

      const snackbar = {
        id: uuidv4(),
        message: message.trim(),
        severity: options.severity || 'info',
        autoHideDuration: options.autoHideDuration ?? CONFIG.DEFAULT_DURATIONS[options.severity || 'info'] ?? CONFIG.DEFAULT_DURATIONS.info,
        anchorOrigin: options.anchorOrigin || { vertical: 'bottom', horizontal: 'right' },
        timestamp: new Date(),
        isVisible: false,
        persistent: options.persistent || false,
        action: options.action,
        icon: options.icon,
        variant: options.variant || 'filled',
        priority: options.priority || 'normal', // low, normal, high, urgent
        category: options.category || 'general',
        metadata: options.metadata || {}
      };

      logger.debug('Creating snackbar', {
        id: snackbar.id,
        severity: snackbar.severity,
        message: message.substring(0, 50) + (message.length > 50 ? '...' : '')
      });

      // Check if we can show immediately or need to queue
      const visibleSnackbars = snackbars.filter(s => s.isVisible);

      if (visibleSnackbars.length < CONFIG.MAX_SNACKBARS && !isPaused) {
        // Show immediately
        setSnackbars(prev => [...prev, { ...snackbar, isVisible: true }]);

        // Set up auto-dismiss timer
        if (snackbar.autoHideDuration > 0) {
          const timeoutId = setTimeout(() => {
            dismissSnackbar(snackbar.id);
          }, snackbar.autoHideDuration + CONFIG.AUTO_DISMISS_DELAY);

          timeoutRefs.current.set(snackbar.id, timeoutId);
        }
      } else {
        // Add to queue
        if (queue.length < CONFIG.MAX_QUEUE_SIZE) {
          setQueue(prev => [...prev, snackbar]);
          logger.debug('Snackbar queued', { id: snackbar.id, queueLength: queue.length + 1 });
        } else {
          logger.warn('Snackbar queue is full, dropping snackbar', {
            id: snackbar.id,
            queueSize: CONFIG.MAX_QUEUE_SIZE
          });
        }
      }

      return snackbar.id;
    } catch (error) {
      logger.error('Failed to show snackbar', error);
      return null;
    }
  }, [snackbars, queue, isPaused, dismissSnackbar]);

  // Clear all snackbars
  const clearAllSnackbars = useCallback(() => {
    logger.debug('Clearing all snackbars');

    // Clear all timeouts
    timeoutRefs.current.forEach(timeoutId => clearTimeout(timeoutId));
    timeoutRefs.current.clear();

    // Clear snackbars and queue
    setSnackbars([]);
    setQueue([]);
  }, []);

  // Pause/resume snackbar system
  const pauseSnackbars = useCallback(() => {
    logger.debug('Pausing snackbars');
    setIsPaused(true);
  }, []);

  const resumeSnackbars = useCallback(() => {
    logger.debug('Resuming snackbars');
    setIsPaused(false);
  }, []);

  // Convenience methods for different severities
  const showSuccessNotification = useCallback((message, options = {}) => {
    return showSnackbar(message, { ...options, severity: 'success' });
  }, [showSnackbar]);

  const showErrorNotification = useCallback((message, options = {}) => {
    return showSnackbar(message, { ...options, severity: 'error' });
  }, [showSnackbar]);

  const showWarningNotification = useCallback((message, options = {}) => {
    return showSnackbar(message, { ...options, severity: 'warning' });
  }, [showSnackbar]);

  const showInfoNotification = useCallback((message, options = {}) => {
    return showSnackbar(message, { ...options, severity: 'info' });
  }, [showSnackbar]);

  // Legacy support - close single snackbar (for backward compatibility)
  const closeSnackbar = useCallback(() => {
    if (snackbars.length > 0) {
      dismissSnackbar(snackbars[0].id);
    }
  }, [snackbars, dismissSnackbar]);

  // Enhanced context value with organized structure
  const contextValue = {
    // State data
    snackbars,
    queue,
    isPaused,

    // Legacy support (for backward compatibility)
    snackbar: snackbars[0] || null,

    // Core functions
    showSnackbar,
    dismissSnackbar,
    closeSnackbar, // Legacy support
    clearAllSnackbars,

    // Convenience methods
    showSuccessNotification,
    showErrorNotification,
    showWarningNotification,
    showInfoNotification,

    // Queue management
    pauseSnackbars,
    resumeSnackbars,

    // Utility functions
    getSnackbarCount: () => snackbars.length,
    getQueueCount: () => queue.length,
    hasSnackbars: snackbars.length > 0,
    hasQueuedSnackbars: queue.length > 0,

    // Helper functions
    getSnackbarById: (id) => snackbars.find(s => s.id === id),
    getSnackbarsBySeverity: (severity) => snackbars.filter(s => s.severity === severity),
    getSnackbarsByCategory: (category) => snackbars.filter(s => s.category === category),

    // Bulk operations
    dismissAllBySeverity: (severity) => {
      const severitySnackbars = snackbars.filter(s => s.severity === severity);
      severitySnackbars.forEach(s => dismissSnackbar(s.id));
      logger.info('Dismissed all snackbars by severity', { severity, count: severitySnackbars.length });
    },

    dismissAllByCategory: (category) => {
      const categorySnackbars = snackbars.filter(s => s.category === category);
      categorySnackbars.forEach(s => dismissSnackbar(s.id));
      logger.info('Dismissed all snackbars by category', { category, count: categorySnackbars.length });
    }
  };

  return (
    <SnackbarContext.Provider value={contextValue}>
      {children}
      {snackbars.map((snackbar, index) => (
        <Snackbar
          key={snackbar.id}
          open={snackbar.isVisible}
          autoHideDuration={snackbar.autoHideDuration}
          onClose={() => dismissSnackbar(snackbar.id)}
          anchorOrigin={snackbar.anchorOrigin}
          TransitionComponent={SlideTransition}
          sx={{
            position: 'fixed',
            bottom: 16 + (index * CONFIG.STACK_SPACING),
            right: 16,
            zIndex: 1400 + index,
          }}
        >
          <Alert
            onClose={() => dismissSnackbar(snackbar.id)}
            severity={snackbar.severity}
            variant={snackbar.variant}
            icon={snackbar.icon}
            action={snackbar.action}
            sx={{
              width: '100%',
              minWidth: 300,
              maxWidth: 500,
            }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      ))}
    </SnackbarContext.Provider>
  );
};

SnackbarProvider.propTypes = {
  children: PropTypes.node.isRequired
};

// Export the context
export { SnackbarContext };

// Default export for convenience
export default SnackbarProvider;

/**
 * Tests for BrandPreview component
 */
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import BrandPreview from '../BrandPreview';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

describe('BrandPreview', () => {
  const mockBrandingData = {
    fonts: ['Roboto', 'Open Sans'],
    colorSystem: {
      primary: '#4E40C5',
      secondary: '#00E4BC',
      accent: '#FF5733',
      background: '#F8F9FA',
      text: '#333333',
    },
    logo_url: 'https://example.com/logo.png',
    logo_settings: {
      size: 20,
      position: 'top-right',
      opacity: 100,
      customPosition: { x: 50, y: 50 },
    },
    visualElements: {
      patterns: [
        {
          url: 'https://example.com/pattern.png',
          scale: 100,
          opacity: 30,
        },
      ],
    },
  };

  const mockProps = {
    brandingData: mockBrandingData,
    previewType: 'social-post',
    onDragStart: vi.fn(),
    onError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders social post preview by default', () => {
    render(
      <TestWrapper>
        <BrandPreview {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
    expect(screen.getByText('2 hours ago')).toBeInTheDocument();
    expect(screen.getByText(/This is how your brand content will appear/)).toBeInTheDocument();
    expect(screen.getByText('Your Brand Message')).toBeInTheDocument();
    expect(screen.getByText('42 Likes')).toBeInTheDocument();
    expect(screen.getByText('8 Comments')).toBeInTheDocument();
    expect(screen.getByText('Share')).toBeInTheDocument();
  });

  test('renders profile preview when previewType is profile', () => {
    render(
      <TestWrapper>
        <BrandPreview {...mockProps} previewType="profile" />
      </TestWrapper>
    );

    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
    expect(screen.getByText(/This is your brand's profile description/)).toBeInTheDocument();
    expect(screen.getByText('1.2K')).toBeInTheDocument();
    expect(screen.getByText('Followers')).toBeInTheDocument();
    expect(screen.getByText('256')).toBeInTheDocument();
    expect(screen.getByText('Following')).toBeInTheDocument();
    expect(screen.getByText('48')).toBeInTheDocument();
    expect(screen.getByText('Posts')).toBeInTheDocument();
  });

  test('renders banner preview when previewType is banner', () => {
    render(
      <TestWrapper>
        <BrandPreview {...mockProps} previewType="banner" />
      </TestWrapper>
    );

    expect(screen.getByText('Your Brand Message')).toBeInTheDocument();
    expect(screen.getByText(/This is your brand's tagline/)).toBeInTheDocument();
    expect(screen.getByText('Call to Action')).toBeInTheDocument();
  });

  test('uses custom brand name when provided', () => {
    render(
      <TestWrapper>
        <BrandPreview {...mockProps} brandName="Custom Brand" />
      </TestWrapper>
    );

    expect(screen.getByText('Custom Brand')).toBeInTheDocument();
    expect(screen.queryByText('Your Brand Name')).not.toBeInTheDocument();
  });

  test('displays logo when logo_url is provided', () => {
    render(
      <TestWrapper>
        <BrandPreview {...mockProps} />
      </TestWrapper>
    );

    const logoImages = screen.getAllByAltText('Logo');
    expect(logoImages.length).toBeGreaterThan(0);
    expect(logoImages[0]).toHaveAttribute('src', mockBrandingData.logo_url);
  });

  test('shows fallback when no logo is provided', () => {
    const brandingDataWithoutLogo = {
      ...mockBrandingData,
      logo_url: null,
    };

    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          brandingData={brandingDataWithoutLogo}
        />
      </TestWrapper>
    );

    expect(screen.getByText('B')).toBeInTheDocument();
  });

  test('handles drag start when logo is dragged', () => {
    render(
      <TestWrapper>
        <BrandPreview {...mockProps} />
      </TestWrapper>
    );

    const draggableElements = screen.getAllByRole('generic').filter(el => 
      el.draggable === true
    );
    
    if (draggableElements.length > 0) {
      fireEvent.dragStart(draggableElements[0]);
      expect(mockProps.onDragStart).toHaveBeenCalledWith(
        expect.any(Object),
        { type: 'logo' }
      );
    }
  });

  test('prevents drag when disabled', () => {
    render(
      <TestWrapper>
        <BrandPreview {...mockProps} disabled={true} />
      </TestWrapper>
    );

    const draggableElements = screen.getAllByRole('generic').filter(el => 
      el.draggable === false
    );
    
    expect(draggableElements.length).toBeGreaterThan(0);
  });

  test('uses default colors when colorSystem is not provided', () => {
    const brandingDataWithoutColors = {
      ...mockBrandingData,
      colorSystem: null,
    };

    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          brandingData={brandingDataWithoutColors}
        />
      </TestWrapper>
    );

    // Should render without errors
    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
  });

  test('uses default fonts when fonts are not provided', () => {
    const brandingDataWithoutFonts = {
      ...mockBrandingData,
      fonts: null,
    };

    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          brandingData={brandingDataWithoutFonts}
        />
      </TestWrapper>
    );

    // Should render without errors
    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
  });

  test('handles custom logo position correctly', () => {
    const brandingDataWithCustomPosition = {
      ...mockBrandingData,
      logo_settings: {
        ...mockBrandingData.logo_settings,
        position: 'custom',
      },
    };

    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          brandingData={brandingDataWithCustomPosition}
        />
      </TestWrapper>
    );

    // Should render without errors
    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
  });

  test('applies pattern background when patterns are provided', () => {
    render(
      <TestWrapper>
        <BrandPreview {...mockProps} />
      </TestWrapper>
    );

    // Should render without errors and apply pattern styles
    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
  });

  test('handles missing pattern data gracefully', () => {
    const brandingDataWithoutPatterns = {
      ...mockBrandingData,
      visualElements: {
        patterns: [],
      },
    };

    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          brandingData={brandingDataWithoutPatterns}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
  });

  test('handles error in drag start gracefully', () => {
    const onDragStartMock = vi.fn().mockImplementation(() => {
      throw new Error('Drag error');
    });

    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          onDragStart={onDragStartMock}
        />
      </TestWrapper>
    );

    const draggableElements = screen.getAllByRole('generic').filter(el => 
      el.draggable === true
    );
    
    if (draggableElements.length > 0) {
      fireEvent.dragStart(draggableElements[0]);
      expect(mockProps.onError).toHaveBeenCalledWith(expect.any(Error));
    }
  });

  test('passes through additional props', () => {
    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          data-testid="test-brand-preview"
          className="custom-class"
        />
      </TestWrapper>
    );

    const component = screen.getByTestId('test-brand-preview');
    expect(component).toHaveClass('custom-class');
  });

  test('handles different logo positions correctly', () => {
    const positions = [
      'top-left', 'top-center', 'top-right',
      'center-left', 'center', 'center-right',
      'bottom-left', 'bottom-center', 'bottom-right'
    ];

    positions.forEach(position => {
      const brandingDataWithPosition = {
        ...mockBrandingData,
        logo_settings: {
          ...mockBrandingData.logo_settings,
          position,
        },
      };

      const { unmount } = render(
        <TestWrapper>
          <BrandPreview 
            {...mockProps} 
            brandingData={brandingDataWithPosition}
          />
        </TestWrapper>
      );

      expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
      unmount();
    });
  });

  test('handles missing logo settings gracefully', () => {
    const brandingDataWithoutLogoSettings = {
      ...mockBrandingData,
      logo_settings: null,
    };

    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          brandingData={brandingDataWithoutLogoSettings}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
  });

  test('handles empty branding data gracefully', () => {
    render(
      <TestWrapper>
        <BrandPreview 
          {...mockProps} 
          brandingData={{}}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Your Brand Name')).toBeInTheDocument();
  });

  test('renders all preview types without errors', () => {
    const previewTypes = [
      { type: 'social-post', expectedText: 'Your Brand Name' },
      { type: 'profile', expectedText: 'Your Brand Name' },
      { type: 'banner', expectedText: 'Your Brand Message' }
    ];

    previewTypes.forEach(({ type, expectedText }) => {
      const { unmount } = render(
        <TestWrapper>
          <BrandPreview
            {...mockProps}
            previewType={type}
          />
        </TestWrapper>
      );

      expect(screen.getByText(expectedText)).toBeInTheDocument();
      unmount();
    });
  });
});

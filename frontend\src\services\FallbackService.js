/**
 * Fallback Service for environments without WebSocket support
 * Provides polling-based real-time updates for serverless platforms
 */

class FallbackService {
  constructor() {
    this.isActive = false;
    // Default poll interval - will be updated based on environment
    this.pollInterval = 5000; // 5 seconds default for fallback mode
    this.pollTimeoutId = null;
    this.listeners = new Map();
    this.lastUpdate = null;
    this.errorCount = 0;
    this.maxErrors = 5;

    // Try to get platform-specific poll interval
    this._initializePollInterval();
  }

  /**
   * Initialize poll interval based on environment
   * @private
   */
  _initializePollInterval() {
    try {
      // Dynamically import to avoid circular dependency
      import('../utils/environmentDetection.js').then(({ getPlatformConfig }) => {
        const config = getPlatformConfig();
        this.pollInterval = config.pollInterval || 5000;
      }).catch(() => {
        // Keep default if import fails
        this.pollInterval = 5000;
      });
    } catch (error) {
      // Keep default if any error occurs
      this.pollInterval = 5000;
    }
  }

  /**
   * Start the fallback service
   */
  start() {
    if (this.isActive) {
      console.log('[FallbackService] Already active');
      return;
    }

    console.log('[FallbackService] Starting polling-based updates');
    this.isActive = true;
    this.errorCount = 0;
    this._startPolling();
  }

  /**
   * Stop the fallback service
   */
  stop() {
    console.log('[FallbackService] Stopping polling-based updates');
    this.isActive = false;
    
    if (this.pollTimeoutId) {
      clearTimeout(this.pollTimeoutId);
      this.pollTimeoutId = null;
    }
  }

  /**
   * Add event listener
   */
  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   */
  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  _emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('[FallbackService] Error in event listener:', error);
        }
      });
    }
  }

  /**
   * Start polling for updates
   */
  _startPolling() {
    if (!this.isActive) return;

    this._pollForUpdates()
      .then(() => {
        this.errorCount = 0;
        this._scheduleNextPoll();
      })
      .catch((error) => {
        console.error('[FallbackService] Polling error:', error);
        this.errorCount++;
        
        if (this.errorCount >= this.maxErrors) {
          console.error('[FallbackService] Max errors reached, stopping polling');
          this.stop();
          this._emit('error', { message: 'Max polling errors reached' });
          return;
        }
        
        this._scheduleNextPoll(true);
      });
  }

  /**
   * Poll for updates from the server
   */
  async _pollForUpdates() {
    try {
      const response = await fetch('/api/platform-config/status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Fallback-Mode': 'true'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Check if there are updates
      if (data.lastUpdate && data.lastUpdate !== this.lastUpdate) {
        this.lastUpdate = data.lastUpdate;
        this._emit('update', data);
      }

      // Emit status update
      this._emit('status', {
        connected: true,
        mode: 'polling',
        lastUpdate: this.lastUpdate
      });

    } catch (error) {
      throw error;
    }
  }

  /**
   * Schedule next polling cycle
   */
  _scheduleNextPoll(isRetry = false) {
    if (!this.isActive) return;

    // Use exponential backoff for retries
    const delay = isRetry ? 
      Math.min(this.pollInterval * Math.pow(2, this.errorCount), 60000) : 
      this.pollInterval;

    this.pollTimeoutId = setTimeout(() => {
      this._startPolling();
    }, delay);
  }

  /**
   * Send a message (simulated for compatibility)
   */
  send(message) {
    console.log('[FallbackService] Message sending not supported in fallback mode:', message);
    return Promise.resolve();
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: this.isActive,
      mode: 'fallback',
      polling: this.isActive,
      errorCount: this.errorCount,
      lastUpdate: this.lastUpdate
    };
  }
}

// Create singleton instance
const fallbackService = new FallbackService();

export default fallbackService;

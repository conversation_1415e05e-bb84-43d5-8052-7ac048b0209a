"""
Factory for creating e-commerce integration instances.
"""
from typing import Dict, Type

from app.services.ecommerce.base import BaseEcommerceIntegration
from app.services.ecommerce.shopify import ShopifyIntegration
from app.services.ecommerce.woocommerce import WooCommerceIntegration
from app.models.common import EcommercePlatformEnum


class EcommerceIntegrationFactory:
    """
    Factory class for creating e-commerce platform integration instances.
    """
    
    _integrations: Dict[str, Type[BaseEcommerceIntegration]] = {
        EcommercePlatformEnum.SHOPIFY: ShopifyIntegration,
        EcommercePlatformEnum.WOOCOMMERCE: WooCommerceIntegration,
    }
    
    @classmethod
    def get_integration(cls, platform: str) -> BaseEcommerceIntegration:
        """
        Get an integration instance for the specified platform.
        
        Args:
            platform: The e-commerce platform name
            
        Returns:
            Integration instance for the platform
            
        Raises:
            ValueError: If the platform is not supported
        """
        if platform not in cls._integrations:
            supported_platforms = list(cls._integrations.keys())
            raise ValueError(
                f"Unsupported e-commerce platform: {platform}. "
                f"Supported platforms: {supported_platforms}"
            )
        
        integration_class = cls._integrations[platform]
        return integration_class()
    
    @classmethod
    def get_supported_platforms(cls) -> list[str]:
        """
        Get a list of supported e-commerce platforms.
        
        Returns:
            List of supported platform names
        """
        return list(cls._integrations.keys())
    
    @classmethod
    def register_integration(cls, platform: str, integration_class: Type[BaseEcommerceIntegration]) -> None:
        """
        Register a new e-commerce integration.
        
        Args:
            platform: The platform name
            integration_class: The integration class
        """
        cls._integrations[platform] = integration_class
    
    @classmethod
    def is_platform_supported(cls, platform: str) -> bool:
        """
        Check if a platform is supported.
        
        Args:
            platform: The platform name to check
            
        Returns:
            True if platform is supported, False otherwise
        """
        return platform in cls._integrations

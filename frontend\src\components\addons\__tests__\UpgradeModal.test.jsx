/**
 * Tests for UpgradeModal component
 */
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import UpgradeModal from '../UpgradeModal';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the hooks
vi.mock('../../hooks/useAuth', () => ({
  useAuth: vi.fn(() => ({
    user: { id: 'test-user', subscription: { plan_id: 'basic' } }
  }))
}));

vi.mock('../../hooks/useAddons', () => ({
  useAddons: vi.fn(() => ({
    getRelevantAddons: vi.fn(),
    getEnhancedLimits: vi.fn(),
    userAddons: [],
    loading: false
  }))
}));

vi.mock('../UsageIndicator', () => ({
  default: ({ current, total, label }) => (
    <div data-testid="usage-indicator">
      {label}: {current}/{total}
    </div>
  )
}));

describe('UpgradeModal', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    onPurchase: vi.fn(),
    usageType: 'regeneration_credits',
    feature: 'Content Regeneration',
    context: 'upgrade'
  };

  const mockUseAddons = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseAddons.mockReturnValue({
      getRelevantAddons: vi.fn().mockResolvedValue([
        {
          id: 'regeneration_booster',
          name: 'Regeneration Booster',
          description: 'Get more regeneration credits',
          features: ['Extra regenerations', 'Priority processing', 'Advanced options'],
          pricing: {
            basic: { price: 9.99, credits: 100 },
            premium: { price: 19.99, credits: 250 }
          },
          is_popular: true
        }
      ]),
      getEnhancedLimits: vi.fn().mockResolvedValue({
        total_limit: 100,
        base_limit: 80,
        addon_bonus: 20,
        remaining: 30
      }),
      userAddons: [],
      loading: false
    });

    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);
  });

  test('renders upgrade modal with correct title', () => {
    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Upgrade Content Regeneration')).toBeInTheDocument();
    expect(screen.getByText('Choose an add-on to enhance your ACEO experience.')).toBeInTheDocument();
  });

  test('shows different context messages', () => {
    const { rerender } = render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} context="limit_reached" />
      </TestWrapper>
    );

    expect(screen.getByText("You've reached your current limit. Upgrade to continue using this feature.")).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <UpgradeModal {...defaultProps} context="feature_locked" />
      </TestWrapper>
    );

    expect(screen.getByText('This feature requires an add-on to unlock.')).toBeInTheDocument();
  });

  test('displays current usage information', async () => {
    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('usage-indicator')).toBeInTheDocument();
      expect(screen.getByText('Current Plan Limits: 70/100')).toBeInTheDocument();
    });
  });

  test('renders addon cards with features and pricing', async () => {
    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Regeneration Booster')).toBeInTheDocument();
      expect(screen.getByText('Get more regeneration credits')).toBeInTheDocument();
      expect(screen.getByText('Popular')).toBeInTheDocument();
      expect(screen.getByText('Extra regenerations')).toBeInTheDocument();
      expect(screen.getByText('Priority processing')).toBeInTheDocument();
      expect(screen.getByText('Advanced options')).toBeInTheDocument();
    });
  });

  test('allows selecting different pricing variants', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('$9.99')).toBeInTheDocument();
      expect(screen.getByText('$19.99')).toBeInTheDocument();
    });

    // Click on premium variant
    await user.click(screen.getByText('Premium'));

    // Should show premium as selected
    const premiumCard = screen.getByText('Premium').closest('div');
    expect(premiumCard).toHaveStyle({ borderColor: expect.any(String) });
  });

  test('handles purchase flow correctly', async () => {
    const user = userEvent.setup();
    const mockOnPurchase = vi.fn().mockResolvedValue();
    
    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} onPurchase={mockOnPurchase} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Purchase basic')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Purchase basic'));

    expect(mockOnPurchase).toHaveBeenCalledWith('regeneration_booster', 'basic');
  });

  test('shows already owned state for owned addons', async () => {
    mockUseAddons.mockReturnValue({
      getRelevantAddons: vi.fn().mockResolvedValue([
        {
          id: 'regeneration_booster',
          name: 'Regeneration Booster',
          description: 'Get more regeneration credits',
          features: ['Extra regenerations'],
          pricing: { basic: { price: 9.99, credits: 100 } }
        }
      ]),
      getEnhancedLimits: vi.fn().mockResolvedValue({
        total_limit: 100,
        base_limit: 80,
        addon_bonus: 20,
        remaining: 30
      }),
      userAddons: [
        {
          addon_id: 'regeneration_booster',
          status: 'active',
          credits_used: 30,
          total_credits: 100
        }
      ],
      loading: false
    });

    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);

    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Already Owned')).toBeInTheDocument();
      expect(screen.getByTestId('usage-indicator')).toBeInTheDocument();
    });
  });

  test('shows loading state when loading', () => {
    mockUseAddons.mockReturnValue({
      getRelevantAddons: vi.fn(),
      getEnhancedLimits: vi.fn(),
      userAddons: [],
      loading: true
    });

    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);

    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('shows no addons message when none available', async () => {
    mockUseAddons.mockReturnValue({
      getRelevantAddons: vi.fn().mockResolvedValue([]),
      getEnhancedLimits: vi.fn().mockResolvedValue({}),
      userAddons: [],
      loading: false
    });

    const { useAddons } = require('../../hooks/useAddons');
    useAddons.mockImplementation(mockUseAddons);

    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No relevant add-ons found for this feature. Please check back later or contact support.')).toBeInTheDocument();
    });
  });

  test('closes modal when close button is clicked', async () => {
    const user = userEvent.setup();
    const mockOnClose = vi.fn();
    
    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} onClose={mockOnClose} />
      </TestWrapper>
    );

    await user.click(screen.getByLabelText('Close'));

    expect(mockOnClose).toHaveBeenCalled();
  });

  test('handles specific addon prop correctly', async () => {
    const specificAddon = {
      id: 'specific_addon',
      name: 'Specific Addon',
      description: 'A specific addon for testing',
      features: ['Specific feature'],
      pricing: { basic: { price: 5.99, credits: 50 } }
    };

    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} addon={specificAddon} usageType={null} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Upgrade Specific Addon')).toBeInTheDocument();
      expect(screen.getByText('Specific Addon')).toBeInTheDocument();
    });
  });

  test('shows benefits section', () => {
    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Why upgrade?')).toBeInTheDocument();
    expect(screen.getByText('Boost Productivity')).toBeInTheDocument();
    expect(screen.getByText('Premium Features')).toBeInTheDocument();
    expect(screen.getByText('Instant Activation')).toBeInTheDocument();
  });

  test('shows money-back guarantee', () => {
    render(
      <TestWrapper>
        <UpgradeModal {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('30-day money-back guarantee')).toBeInTheDocument();
  });
});

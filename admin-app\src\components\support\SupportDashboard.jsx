import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Schedule as ScheduleIcon,
  Assignment as TicketIcon,
  Person as PersonIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import { format, subDays } from 'date-fns';

// API
import api from '../../api';

const SupportDashboard = ({ dashboardData, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [recentTickets, setRecentTickets] = useState([]);

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  useEffect(() => {
    if (dashboardData) {
      loadRecentTickets();
    }
  }, [dashboardData]);

  const loadRecentTickets = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load recent tickets
      const response = await api.get('/api/admin/support/tickets?page=1&page_size=10');
      setRecentTickets(response.data.slice(0, 5)); // Show only 5 most recent
    } catch (err) {
      console.error('Error loading recent tickets:', err);
      setError(err.response?.data?.detail || 'Failed to load recent tickets');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatHours = (hours) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}m`;
    }
    return `${hours.toFixed(1)}h`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'open':
        return 'info';
      case 'in_progress':
        return 'primary';
      case 'pending_customer':
        return 'warning';
      case 'resolved':
        return 'success';
      case 'closed':
        return 'default';
      case 'escalated':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      case 'low':
        return 'default';
      default:
        return 'default';
    }
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 2 }}>
          <Typography variant="body2" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  if (!dashboardData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  // Prepare chart data
  const channelData = Object.entries(dashboardData.tickets_by_channel || {}).map(([channel, count]) => ({
    name: channel.charAt(0).toUpperCase() + channel.slice(1),
    value: count,
  }));

  const categoryData = Object.entries(dashboardData.tickets_by_category || {}).map(([category, count]) => ({
    name: category.charAt(0).toUpperCase() + category.slice(1),
    value: count,
  }));

  const priorityData = Object.entries(dashboardData.tickets_by_priority || {}).map(([priority, count]) => ({
    name: priority.charAt(0).toUpperCase() + priority.slice(1),
    value: count,
  }));

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading && <LinearProgress sx={{ mb: 2 }} />}

      <Grid container spacing={3}>
        {/* Performance Metrics */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardHeader 
              title="Performance Trends"
              subheader="Support metrics over the last 30 days"
            />
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={dashboardData.ticket_volume_trend || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="tickets"
                    stroke="#8884d8"
                    strokeWidth={2}
                    name="Daily Tickets"
                  />
                  <Line
                    type="monotone"
                    dataKey="resolved"
                    stroke="#82ca9d"
                    strokeWidth={2}
                    name="Resolved Tickets"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Channel Distribution */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardHeader 
              title="Tickets by Channel"
              subheader="Distribution across communication channels"
            />
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={channelData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {channelData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Key Performance Indicators */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Key Performance Indicators" />
            <CardContent>
              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">First Contact Resolution Rate</Typography>
                  <Typography variant="h6">{formatPercentage(dashboardData.first_contact_resolution_rate)}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={dashboardData.first_contact_resolution_rate * 100} 
                  sx={{ height: 8, borderRadius: 4 }}
                  color={dashboardData.first_contact_resolution_rate >= 0.75 ? "success" : "warning"}
                />
                <Typography variant="caption" color="text.secondary">
                  Target: 75%+
                </Typography>
              </Box>

              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Average Resolution Time</Typography>
                  <Typography variant="h6">{formatHours(dashboardData.average_resolution_time_hours)}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={Math.min((24 / dashboardData.average_resolution_time_hours) * 100, 100)} 
                  sx={{ height: 8, borderRadius: 4 }}
                  color={dashboardData.average_resolution_time_hours <= 24 ? "success" : "warning"}
                />
                <Typography variant="caption" color="text.secondary">
                  Target: &lt;24h
                </Typography>
              </Box>

              <Box mb={3}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Agent Utilization</Typography>
                  <Typography variant="h6">{formatPercentage(dashboardData.agent_utilization_rate)}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={dashboardData.agent_utilization_rate * 100} 
                  sx={{ height: 8, borderRadius: 4 }}
                  color={dashboardData.agent_utilization_rate <= 0.85 ? "success" : "warning"}
                />
                <Typography variant="caption" color="text.secondary">
                  Target: 70-85%
                </Typography>
              </Box>

              <Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2">Customer Satisfaction (NPS)</Typography>
                  <Typography variant="h6">{dashboardData.nps_score.toFixed(1)}</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={Math.max(0, (dashboardData.nps_score + 100) / 2)} 
                  sx={{ height: 8, borderRadius: 4 }}
                  color={dashboardData.nps_score >= 50 ? "success" : dashboardData.nps_score >= 0 ? "warning" : "error"}
                />
                <Typography variant="caption" color="text.secondary">
                  Range: -100 to +100
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Tickets */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Recent Tickets" />
            <CardContent>
              {recentTickets.length > 0 ? (
                <List>
                  {recentTickets.map((ticket, index) => (
                    <React.Fragment key={ticket.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemIcon>
                          <TicketIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box display="flex" alignItems="center" gap={1}>
                              <Typography variant="body2" fontWeight="bold">
                                {ticket.ticket_number}
                              </Typography>
                              <Chip
                                label={ticket.status}
                                color={getStatusColor(ticket.status)}
                                size="small"
                                sx={{ textTransform: 'capitalize' }}
                              />
                              <Chip
                                label={ticket.priority}
                                color={getPriorityColor(ticket.priority)}
                                size="small"
                                sx={{ textTransform: 'capitalize' }}
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.primary">
                                {ticket.subject}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {ticket.customer_name} • {format(new Date(ticket.created_at), 'MMM dd, HH:mm')}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                      {index < recentTickets.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary" textAlign="center" py={4}>
                  No recent tickets found
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Category and Priority Breakdown */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Ticket Analysis" />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    By Category
                  </Typography>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={categoryData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="value" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    By Priority
                  </Typography>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={priorityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="value" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Support Health Summary */}
        <Grid item xs={12}>
          <Card>
            <CardHeader title="Support System Health" />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {formatNumber(dashboardData.total_tickets)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Tickets
                    </Typography>
                    <Typography variant="body2" color="success.main">
                      All time
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {formatHours(dashboardData.average_response_time_hours)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Avg Response Time
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color={dashboardData.average_response_time_hours <= 4 ? "success.main" : "warning.main"}
                    >
                      {dashboardData.average_response_time_hours <= 4 ? "Excellent" : "Needs improvement"}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {formatPercentage(dashboardData.sla_compliance_rate)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      SLA Compliance
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color={dashboardData.sla_compliance_rate >= 0.95 ? "success.main" : "warning.main"}
                    >
                      {dashboardData.sla_breaches_today} breaches today
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box textAlign="center">
                    <Typography variant="h4" color="primary">
                      {dashboardData.customer_satisfaction_score.toFixed(1)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Customer Satisfaction
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color={dashboardData.customer_satisfaction_score >= 4.0 ? "success.main" : "warning.main"}
                    >
                      Out of 5.0 stars
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SupportDashboard;

"""
Correlation ID utilities for request tracking and debugging.
Provides enterprise-grade request correlation following ACE Social platform patterns.
"""
import uuid
import time
from typing import <PERSON><PERSON>
from contextvars import ContextVar

# Context variable to store correlation ID for the current request
correlation_context: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


def generate_correlation_id() -> str:
    """
    Generate a unique correlation ID for request tracking.
    
    Format: req_{timestamp}_{uuid4_short}
    Example: req_1705123456_abc123def456
    
    Returns:
        str: Unique correlation ID for the request
    """
    timestamp = int(time.time())
    uuid_short = str(uuid.uuid4()).replace('-', '')[:12]
    correlation_id = f"req_{timestamp}_{uuid_short}"
    
    # Store in context for use throughout the request lifecycle
    correlation_context.set(correlation_id)
    
    return correlation_id


def get_correlation_id() -> Optional[str]:
    """
    Get the current correlation ID from context.
    
    Returns:
        Optional[str]: Current correlation ID or None if not set
    """
    return correlation_context.get()


def set_correlation_id(correlation_id: str) -> None:
    """
    Set the correlation ID in context.
    
    Args:
        correlation_id (str): Correlation ID to set
    """
    correlation_context.set(correlation_id)


def clear_correlation_id() -> None:
    """
    Clear the correlation ID from context.
    """
    correlation_context.set(None)


class CorrelationIDMiddleware:
    """
    FastAPI middleware to automatically generate and manage correlation IDs.
    """
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            # Generate correlation ID for HTTP requests
            correlation_id = generate_correlation_id()
            
            # Add to request headers for downstream services
            headers = dict(scope.get("headers", []))
            headers[b"x-correlation-id"] = correlation_id.encode()
            scope["headers"] = list(headers.items())
        
        await self.app(scope, receive, send)
        
        # Clear correlation ID after request
        clear_correlation_id()

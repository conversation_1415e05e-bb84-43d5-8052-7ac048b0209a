/**
 * FeatureGate Component - Enterprise-grade feature gating for ACE Social platform
 * Features: Advanced permission validation, role-based access controls, intelligent feature blocking,
 * permission validation engines, role hierarchy enforcement, feature access matrices, conditional rendering systems, and production-ready gate management
 @since 2024-1-1 to 2025-25-7
*/

import { memo, useMemo, useCallback, useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  useTheme,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Alert,
  AlertTitle,
  Chip,
  Avatar,
  Stack,
  Collapse,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  LinearProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Lock as LockIcon,
  ArrowUpward as ArrowUpwardIcon,
  Warning as WarningIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import AccessibleButton from './AccessibleButton';
import GlassmorphicCard from './GlassmorphicCard';

// Enhanced styled components for enterprise-grade feature gating
const StyledGateContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  '& .gate-indicator': {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 4,
    height: '100%',
    borderRadius: '2px 0 0 2px'
  },
  '& .permission-details': {
    marginTop: theme.spacing(2)
  }
}));

const StyledGateCard = styled(Card)(({ theme, variant, gateStatus }) => ({
  transition: theme.transitions.create(['all'], {
    duration: theme.transitions.duration.standard
  }),
  ...(variant === 'premium' && {
    background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}15 100%)`,
    border: `2px solid ${theme.palette.primary.main}30`
  }),
  ...(variant === 'security' && {
    background: `linear-gradient(135deg, ${theme.palette.error.main}15 0%, ${theme.palette.warning.main}15 100%)`,
    border: `2px solid ${theme.palette.error.main}30`
  }),
  ...(gateStatus === 'denied' && {
    borderLeft: `4px solid ${theme.palette.error.main}`
  }),
  ...(gateStatus === 'granted' && {
    borderLeft: `4px solid ${theme.palette.success.main}`
  }),
  ...(gateStatus === 'limited' && {
    borderLeft: `4px solid ${theme.palette.warning.main}`
  }),
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[8]
  }
}));

const StyledPermissionChip = styled(Chip)(({ theme, permission }) => ({
  fontWeight: 600,
  ...(permission === 'granted' && {
    backgroundColor: theme.palette.success.main,
    color: theme.palette.success.contrastText
  }),
  ...(permission === 'denied' && {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText
  }),
  ...(permission === 'limited' && {
    backgroundColor: theme.palette.warning.main,
    color: theme.palette.warning.contrastText
  }),
  ...(permission === 'pending' && {
    backgroundColor: theme.palette.info.main,
    color: theme.palette.info.contrastText
  })
}));

// Feature gate constants and configurations
const GATE_VARIANTS = {
  SIMPLE: 'simple',
  DETAILED: 'detailed',
  SECURITY: 'security',
  PREMIUM: 'premium',
  MODAL: 'modal'
};

const GATE_STATUSES = {
  GRANTED: 'granted',
  DENIED: 'denied',
  LIMITED: 'limited',
  PENDING: 'pending',
  ERROR: 'error'
};

const PLAN_HIERARCHY = {
  creator: 1,
  accelerator: 2,
  dominator: 3
};

const ROLE_HIERARCHY = {
  viewer: 0,
  editor: 1,
  admin: 2,
  owner: 3,
  super_admin: 4
};

const ACCESS_LEVELS = {
  NONE: 'none',
  LIMITED: 'limited',
  FULL: 'full',
  ADMIN: 'admin'
};

/**
 * Enhanced enterprise-grade feature gate component with comprehensive permission validation,
 * role-based access controls, intelligent feature blocking, and production-ready gate management
 */
const FeatureGate = memo(({
  feature,
  children,
  fallbackMessage,
  showUpgradeButton = true,
  upgradeLink = "/billing/plans",
  upgradeText = "Upgrade Plan",
  hideIfNoAccess = false,
  variant = GATE_VARIANTS.SIMPLE,
  enableAnalytics = true,
  onAnalytics,
  enablePermissionDetails = false,
  enableRoleValidation = false,
  enablePlanValidation = true,
  requiredRole,
  requiredPlan,
  requiredPermissions = [],
  customValidator,
  fallbackComponent,
  onAccessDenied,
  onAccessGranted,
  ariaLabel,
  testId,
  ...rest
}) => {
  const theme = useTheme();
  const location = useLocation();
  const {
    hasFeature,
    getFeatureDescription,
    user,
    getUserRole,
    getUserPermissions,
    userFeatures
  } = useAuth();

  // State management
  const [showDetails, setShowDetails] = useState(false);
  const [, setGateHistory] = useState([]);

  // Refs
  const containerRef = useRef(null);

  // Analytics tracking
  const trackAnalytics = useCallback((action, data = {}) => {
    if (!enableAnalytics || !onAnalytics) return;

    onAnalytics({
      component: 'FeatureGate',
      action,
      feature,
      timestamp: Date.now(),
      userId: user?.id,
      userPlan: userFeatures?.plan_id,
      userRole: getUserRole?.(),
      path: location.pathname,
      variant,
      ...data
    });
  }, [enableAnalytics, onAnalytics, feature, user, userFeatures, getUserRole, location.pathname, variant]);

  // Enhanced permission validation
  const validateAccess = useMemo(() => {
    const validation = {
      hasFeatureAccess: false,
      hasRoleAccess: true,
      hasPlanAccess: true,
      hasCustomAccess: true,
      hasPermissionAccess: true,
      overallAccess: false,
      accessLevel: ACCESS_LEVELS.NONE,
      denialReasons: [],
      gateStatus: GATE_STATUSES.DENIED
    };

    // Basic feature access check
    validation.hasFeatureAccess = hasFeature(feature);
    if (!validation.hasFeatureAccess) {
      validation.denialReasons.push('Feature not available in current plan');
    }

    // Role validation
    if (enableRoleValidation && requiredRole) {
      const userRole = getUserRole?.() || 'viewer';
      const userRoleLevel = ROLE_HIERARCHY[userRole] || 0;
      const requiredRoleLevel = ROLE_HIERARCHY[requiredRole] || 0;
      validation.hasRoleAccess = userRoleLevel >= requiredRoleLevel;

      if (!validation.hasRoleAccess) {
        validation.denialReasons.push(`Requires ${requiredRole} role or higher`);
      }
    }

    // Plan validation
    if (enablePlanValidation && requiredPlan) {
      const userPlan = userFeatures?.plan_id || 'creator';
      const userPlanLevel = PLAN_HIERARCHY[userPlan] || 1;
      const requiredPlanLevel = PLAN_HIERARCHY[requiredPlan] || 1;
      validation.hasPlanAccess = userPlanLevel >= requiredPlanLevel;

      if (!validation.hasPlanAccess) {
        validation.denialReasons.push(`Requires ${requiredPlan} plan or higher`);
      }
    }

    // Specific permissions validation
    if (requiredPermissions.length > 0) {
      const userPermissions = getUserPermissions?.() || [];
      validation.hasPermissionAccess = requiredPermissions.every(permission =>
        userPermissions.includes(permission)
      );

      if (!validation.hasPermissionAccess) {
        const missingPermissions = requiredPermissions.filter(permission =>
          !userPermissions.includes(permission)
        );
        validation.denialReasons.push(`Missing permissions: ${missingPermissions.join(', ')}`);
      }
    }

    // Custom validation
    if (customValidator) {
      try {
        validation.hasCustomAccess = customValidator({
          user,
          userFeatures,
          feature,
          userRole: getUserRole?.(),
          userPermissions: getUserPermissions?.()
        });

        if (!validation.hasCustomAccess) {
          validation.denialReasons.push('Custom validation failed');
        }
      } catch {
        validation.hasCustomAccess = false;
        validation.denialReasons.push('Custom validation error');
      }
    }

    // Overall access determination
    validation.overallAccess = validation.hasFeatureAccess &&
                              validation.hasRoleAccess &&
                              validation.hasPlanAccess &&
                              validation.hasPermissionAccess &&
                              validation.hasCustomAccess;

    // Determine access level and gate status
    if (validation.overallAccess) {
      validation.accessLevel = ACCESS_LEVELS.FULL;
      validation.gateStatus = GATE_STATUSES.GRANTED;
    } else if (validation.hasFeatureAccess) {
      validation.accessLevel = ACCESS_LEVELS.LIMITED;
      validation.gateStatus = GATE_STATUSES.LIMITED;
    } else {
      validation.accessLevel = ACCESS_LEVELS.NONE;
      validation.gateStatus = GATE_STATUSES.DENIED;
    }

    return validation;
  }, [
    feature, hasFeature, enableRoleValidation, requiredRole, getUserRole,
    enablePlanValidation, requiredPlan, userFeatures, requiredPermissions,
    getUserPermissions, customValidator, user
  ]);

  // Event handlers
  const handleAccessDenied = useCallback(() => {
    trackAnalytics('access_denied', {
      denialReasons: validateAccess.denialReasons,
      accessLevel: validateAccess.accessLevel
    });

    if (onAccessDenied) {
      onAccessDenied({
        feature,
        validation: validateAccess,
        user,
        timestamp: Date.now()
      });
    }
  }, [trackAnalytics, validateAccess, onAccessDenied, feature, user]);

  const handleAccessGranted = useCallback(() => {
    trackAnalytics('access_granted', {
      accessLevel: validateAccess.accessLevel
    });

    if (onAccessGranted) {
      onAccessGranted({
        feature,
        validation: validateAccess,
        user,
        timestamp: Date.now()
      });
    }
  }, [trackAnalytics, validateAccess, onAccessGranted, feature, user]);

  const handleUpgrade = useCallback(() => {
    trackAnalytics('upgrade_clicked', {
      currentPlan: userFeatures?.plan_id,
      requiredPlan,
      denialReasons: validateAccess.denialReasons
    });
  }, [trackAnalytics, userFeatures, requiredPlan, validateAccess]);

  const handleDetailsToggle = useCallback(() => {
    setShowDetails(!showDetails);
    trackAnalytics('details_toggled', { showing: !showDetails });
  }, [showDetails, trackAnalytics]);

  // Effects
  useEffect(() => {
    if (validateAccess.overallAccess) {
      handleAccessGranted();
    } else {
      handleAccessDenied();
    }
  }, [validateAccess.overallAccess, handleAccessGranted, handleAccessDenied]);

  // Log access attempt to history
  useEffect(() => {
    const historyEntry = {
      timestamp: Date.now(),
      feature,
      accessGranted: validateAccess.overallAccess,
      denialReasons: validateAccess.denialReasons,
      userId: user?.id
    };

    setGateHistory(prev => [historyEntry, ...prev.slice(0, 9)]); // Keep last 10 entries
  }, [feature, validateAccess, user]);

  // If user has access, render the children
  if (validateAccess.overallAccess) {
    return (
      <StyledGateContainer
        ref={containerRef}
        data-testid={testId}
        aria-label={ariaLabel || `${feature} - Access granted`}
        {...rest}
      >
        <div className="gate-indicator" style={{ backgroundColor: theme.palette.success.main }} />
        {children}
      </StyledGateContainer>
    );
  }

  // If hideIfNoAccess is true and user doesn't have access, render nothing
  if (hideIfNoAccess) {
    return null;
  }

  // Get the feature description for the fallback message
  const featureDescription = getFeatureDescription?.(feature) || feature;
  const defaultMessage = `This feature requires ${featureDescription}`;
  const message = fallbackMessage || defaultMessage;

  // Custom fallback component
  if (fallbackComponent) {
    return fallbackComponent({
      feature,
      validation: validateAccess,
      user,
      onUpgrade: handleUpgrade,
      message
    });
  }

  // Render different variants
  const renderSimpleGate = () => (
    <StyledGateContainer
      ref={containerRef}
      data-testid={testId}
      aria-label={ariaLabel || `${feature} - Access denied`}
      {...rest}
    >
      <GlassmorphicCard variant="glass" sx={{ p: 3, textAlign: 'center' }}>
        <div className="gate-indicator" style={{ backgroundColor: theme.palette.error.main }} />

        <LockIcon
          sx={{
            fontSize: 48,
            color: 'text.secondary',
            mb: 2
          }}
        />

        <Typography variant="h6" gutterBottom>
          Feature Not Available
        </Typography>

        <Typography variant="body2" color="text.secondary" paragraph>
          {message}
        </Typography>

        {showUpgradeButton && (
          <AccessibleButton
            component={RouterLink}
            to={upgradeLink}
            variant="contained"
            color="primary"
            startIcon={<ArrowUpwardIcon />}
            onClick={handleUpgrade}
            aria-label={`Upgrade to access ${feature}`}
          >
            {upgradeText}
          </AccessibleButton>
        )}
      </GlassmorphicCard>
    </StyledGateContainer>
  );

  const renderDetailedGate = () => (
    <StyledGateContainer
      ref={containerRef}
      data-testid={testId}
      aria-label={ariaLabel || `${feature} - Access denied with details`}
      {...rest}
    >
      <StyledGateCard variant="security" gateStatus={validateAccess.gateStatus}>
        <CardHeader
          avatar={
            <Avatar sx={{ bgcolor: theme.palette.error.main }}>
              <LockIcon />
            </Avatar>
          }
          title="Access Restricted"
          subheader={`Feature: ${featureDescription}`}
          action={
            <StyledPermissionChip
              permission="denied"
              label={validateAccess.gateStatus}
              size="small"
            />
          }
        />

        <CardContent>
          <Typography variant="body1" paragraph>
            {message}
          </Typography>

          {validateAccess.denialReasons.length > 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <AlertTitle>Access Requirements</AlertTitle>
              <List dense>
                {validateAccess.denialReasons.map((reason, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <WarningIcon color="warning" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={reason} />
                  </ListItem>
                ))}
              </List>
            </Alert>
          )}

          {enablePermissionDetails && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle2">
                  Permission Details
                </Typography>
                <IconButton size="small" onClick={handleDetailsToggle}>
                  {showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Box>

              <Collapse in={showDetails}>
                <Box className="permission-details">
                  <Stack spacing={1}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2">Feature Access:</Typography>
                      <StyledPermissionChip
                        permission={validateAccess.hasFeatureAccess ? 'granted' : 'denied'}
                        label={validateAccess.hasFeatureAccess ? 'Granted' : 'Denied'}
                        size="small"
                      />
                    </Box>

                    {enableRoleValidation && (
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2">Role Access:</Typography>
                        <StyledPermissionChip
                          permission={validateAccess.hasRoleAccess ? 'granted' : 'denied'}
                          label={validateAccess.hasRoleAccess ? 'Granted' : 'Denied'}
                          size="small"
                        />
                      </Box>
                    )}

                    {enablePlanValidation && (
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2">Plan Access:</Typography>
                        <StyledPermissionChip
                          permission={validateAccess.hasPlanAccess ? 'granted' : 'denied'}
                          label={validateAccess.hasPlanAccess ? 'Granted' : 'Denied'}
                          size="small"
                        />
                      </Box>
                    )}
                  </Stack>
                </Box>
              </Collapse>
            </>
          )}
        </CardContent>

        {showUpgradeButton && (
          <CardActions sx={{ justifyContent: 'center' }}>
            <AccessibleButton
              component={RouterLink}
              to={upgradeLink}
              variant="contained"
              color="primary"
              startIcon={<ArrowUpwardIcon />}
              onClick={handleUpgrade}
              aria-label={`Upgrade to access ${feature}`}
            >
              {upgradeText}
            </AccessibleButton>
          </CardActions>
        )}
      </StyledGateCard>
    </StyledGateContainer>
  );

  // Render based on variant
  switch (variant) {
    case GATE_VARIANTS.SIMPLE:
      return renderSimpleGate();
    case GATE_VARIANTS.DETAILED:
      return renderDetailedGate();
    case GATE_VARIANTS.SECURITY:
      return renderDetailedGate();
    case GATE_VARIANTS.PREMIUM:
      return renderDetailedGate();
    default:
      return renderSimpleGate();
  }
});

// Set display name for debugging
FeatureGate.displayName = 'FeatureGate';

/**
 * Enhanced feature limit gate component with comprehensive usage validation
 */
export const FeatureLimitGate = memo(({
  limitName,
  currentUsage,
  children,
  fallbackMessage,
  showUpgradeButton = true,
  upgradeLink = "/billing/plans",
  upgradeText = "Upgrade Plan",
  hideIfExceeded = false,
  variant = GATE_VARIANTS.SIMPLE,
  enableAnalytics = true,
  onAnalytics,
  enableUsageWarning = true,
  warningThreshold = 0.8,
  showUsageProgress = true,
  onLimitExceeded,
  ariaLabel,
  testId,
  ...rest
}) => {
  const theme = useTheme();
  const location = useLocation();
  const { getFeatureLimit, user, userFeatures } = useAuth();

  // State management (for future use)
  // const [showDetails, setShowDetails] = useState(false);

  // Refs
  const containerRef = useRef(null);

  // Analytics tracking
  const trackAnalytics = useCallback((action, data = {}) => {
    if (!enableAnalytics || !onAnalytics) return;

    onAnalytics({
      component: 'FeatureLimitGate',
      action,
      limitName,
      currentUsage,
      timestamp: Date.now(),
      userId: user?.id,
      userPlan: userFeatures?.plan_id,
      path: location.pathname,
      variant,
      ...data
    });
  }, [enableAnalytics, onAnalytics, limitName, currentUsage, user, userFeatures, location.pathname, variant]);

  // Enhanced limit validation
  const limitValidation = useMemo(() => {
    const limit = getFeatureLimit(limitName);
    const usage = currentUsage || 0;
    const percentage = limit > 0 ? (usage / limit) * 100 : 0;

    const validation = {
      limit,
      usage,
      percentage,
      isWithinLimit: usage < limit,
      isUnlimited: limit === -1 || limit === 999999,
      remaining: Math.max(limit - usage, 0),
      isApproachingLimit: percentage >= (warningThreshold * 100),
      severity: 'low'
    };

    // Determine severity
    if (percentage >= 100) {
      validation.severity = 'critical';
    } else if (percentage >= 90) {
      validation.severity = 'high';
    } else if (percentage >= warningThreshold * 100) {
      validation.severity = 'medium';
    }

    return validation;
  }, [limitName, currentUsage, getFeatureLimit, warningThreshold]);

  // Event handlers
  const handleLimitExceeded = useCallback(() => {
    trackAnalytics('limit_exceeded', {
      limit: limitValidation.limit,
      usage: limitValidation.usage,
      percentage: limitValidation.percentage
    });

    if (onLimitExceeded) {
      onLimitExceeded({
        limitName,
        validation: limitValidation,
        user,
        timestamp: Date.now()
      });
    }
  }, [trackAnalytics, limitValidation, onLimitExceeded, limitName, user]);

  const handleUpgrade = useCallback(() => {
    trackAnalytics('upgrade_clicked', {
      currentPlan: userFeatures?.plan_id,
      limitName,
      usage: limitValidation.usage,
      limit: limitValidation.limit
    });
  }, [trackAnalytics, userFeatures, limitName, limitValidation]);

  // Effects
  useEffect(() => {
    if (!limitValidation.isWithinLimit) {
      handleLimitExceeded();
    }
  }, [limitValidation.isWithinLimit, handleLimitExceeded]);

  // If user is within the limit, render the children
  if (limitValidation.isWithinLimit) {
    return (
      <StyledGateContainer
        ref={containerRef}
        data-testid={testId}
        aria-label={ariaLabel || `${limitName} - Within limit`}
        {...rest}
      >
        <div className="gate-indicator" style={{ backgroundColor: theme.palette.success.main }} />
        {children}

        {/* Usage warning for approaching limits */}
        {enableUsageWarning && limitValidation.isApproachingLimit && (
          <Alert severity="warning" sx={{ mt: 1 }}>
            <AlertTitle>Approaching Limit</AlertTitle>
            You are approaching your {formatLimitName(limitName)} limit.
            {limitValidation.remaining > 0 && ` ${limitValidation.remaining} remaining.`}
          </Alert>
        )}
      </StyledGateContainer>
    );
  }

  // If hideIfExceeded is true and user has exceeded the limit, render nothing
  if (hideIfExceeded) {
    return null;
  }

  // Format the limit name for display
  const formatLimitName = (name) => {
    return name
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  const formattedLimitName = formatLimitName(limitName);
  const defaultMessage = limitValidation.isUnlimited
    ? `You have unlimited ${formattedLimitName}.`
    : `You have reached your ${formattedLimitName} limit (${limitValidation.limit}).`;
  const message = fallbackMessage || defaultMessage;

  // Render the fallback content
  return (
    <StyledGateContainer
      ref={containerRef}
      data-testid={testId}
      aria-label={ariaLabel || `${limitName} - Limit exceeded`}
      {...rest}
    >
      <StyledGateCard variant="security" gateStatus="denied">
        <CardHeader
          avatar={
            <Avatar sx={{ bgcolor: theme.palette.error.main }}>
              <WarningIcon />
            </Avatar>
          }
          title="Usage Limit Reached"
          subheader={`${formattedLimitName}: ${limitValidation.usage} / ${limitValidation.isUnlimited ? '∞' : limitValidation.limit}`}
          action={
            <StyledPermissionChip
              permission="denied"
              label={`${Math.round(limitValidation.percentage)}%`}
              size="small"
            />
          }
        />

        <CardContent>
          <Typography variant="body1" paragraph>
            {message}
          </Typography>

          {showUsageProgress && (
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Usage</Typography>
                <Typography variant="body2">
                  {limitValidation.usage} / {limitValidation.isUnlimited ? '∞' : limitValidation.limit}
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={Math.min(limitValidation.percentage, 100)}
                color={limitValidation.severity === 'critical' ? 'error' : 'primary'}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>
          )}

          <Alert severity="error">
            <AlertTitle>Upgrade Required</AlertTitle>
            To continue using this feature, please upgrade your plan or wait for your usage to reset.
          </Alert>
        </CardContent>

        {showUpgradeButton && (
          <CardActions sx={{ justifyContent: 'center' }}>
            <AccessibleButton
              component={RouterLink}
              to={upgradeLink}
              variant="contained"
              color="primary"
              startIcon={<ArrowUpwardIcon />}
              onClick={handleUpgrade}
              aria-label={`Upgrade to increase ${formattedLimitName} limit`}
            >
              {upgradeText}
            </AccessibleButton>
          </CardActions>
        )}
      </StyledGateCard>
    </StyledGateContainer>
  );
});

// Set display name for debugging
FeatureLimitGate.displayName = 'FeatureLimitGate';

// Comprehensive PropTypes for enterprise-grade feature gating
FeatureGate.propTypes = {
  /** Feature key to check access for */
  feature: PropTypes.string.isRequired,

  /** Content to render when access is granted */
  children: PropTypes.node.isRequired,

  /** Custom fallback message */
  fallbackMessage: PropTypes.string,

  /** Whether to show upgrade button */
  showUpgradeButton: PropTypes.bool,

  /** Custom upgrade link */
  upgradeLink: PropTypes.string,

  /** Custom upgrade button text */
  upgradeText: PropTypes.string,

  /** Whether to hide component if access denied */
  hideIfNoAccess: PropTypes.bool,

  /** Gate display variant */
  variant: PropTypes.oneOf(Object.values(GATE_VARIANTS)),

  /** Whether to enable analytics tracking */
  enableAnalytics: PropTypes.bool,

  /** Analytics event handler */
  onAnalytics: PropTypes.func,

  /** Whether to enable permission details */
  enablePermissionDetails: PropTypes.bool,

  /** Whether to enable role validation */
  enableRoleValidation: PropTypes.bool,

  /** Whether to enable plan validation */
  enablePlanValidation: PropTypes.bool,

  /** Required role for access */
  requiredRole: PropTypes.oneOf(Object.keys(ROLE_HIERARCHY)),

  /** Required plan for access */
  requiredPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),

  /** Required permissions for access */
  requiredPermissions: PropTypes.arrayOf(PropTypes.string),

  /** Custom validation function */
  customValidator: PropTypes.func,

  /** Custom fallback component */
  fallbackComponent: PropTypes.func,

  /** Access denied handler */
  onAccessDenied: PropTypes.func,

  /** Access granted handler */
  onAccessGranted: PropTypes.func,

  /** Whether to show permission matrix */
  showPermissionMatrix: PropTypes.bool,

  /** Whether to enable grace period */
  enableGracePeriod: PropTypes.bool,

  /** Grace period in days */
  gracePeriodDays: PropTypes.number,

  /** ARIA label for accessibility */
  ariaLabel: PropTypes.string,

  /** Test ID for testing */
  testId: PropTypes.string
};

FeatureGate.defaultProps = {
  fallbackMessage: null,
  showUpgradeButton: true,
  upgradeLink: "/billing/plans",
  upgradeText: "Upgrade Plan",
  hideIfNoAccess: false,
  variant: GATE_VARIANTS.SIMPLE,
  enableAnalytics: true,
  onAnalytics: null,
  enablePermissionDetails: false,
  enableRoleValidation: false,
  enablePlanValidation: true,
  requiredRole: null,
  requiredPlan: null,
  requiredPermissions: [],
  customValidator: null,
  fallbackComponent: null,
  onAccessDenied: null,
  onAccessGranted: null,
  showPermissionMatrix: false,
  enableGracePeriod: false,
  gracePeriodDays: 0,
  ariaLabel: null,
  testId: null
};

// PropTypes for FeatureLimitGate
FeatureLimitGate.propTypes = {
  /** Limit name to check */
  limitName: PropTypes.string.isRequired,

  /** Current usage value */
  currentUsage: PropTypes.number.isRequired,

  /** Content to render when within limit */
  children: PropTypes.node.isRequired,

  /** Custom fallback message */
  fallbackMessage: PropTypes.string,

  /** Whether to show upgrade button */
  showUpgradeButton: PropTypes.bool,

  /** Custom upgrade link */
  upgradeLink: PropTypes.string,

  /** Custom upgrade button text */
  upgradeText: PropTypes.string,

  /** Whether to hide component if limit exceeded */
  hideIfExceeded: PropTypes.bool,

  /** Gate display variant */
  variant: PropTypes.oneOf(Object.values(GATE_VARIANTS)),

  /** Whether to enable analytics tracking */
  enableAnalytics: PropTypes.bool,

  /** Analytics event handler */
  onAnalytics: PropTypes.func,

  /** Whether to enable usage warning */
  enableUsageWarning: PropTypes.bool,

  /** Warning threshold (0-1) */
  warningThreshold: PropTypes.number,

  /** Whether to show usage progress */
  showUsageProgress: PropTypes.bool,

  /** Limit exceeded handler */
  onLimitExceeded: PropTypes.func,

  /** ARIA label for accessibility */
  ariaLabel: PropTypes.string,

  /** Test ID for testing */
  testId: PropTypes.string
};

FeatureLimitGate.defaultProps = {
  fallbackMessage: null,
  showUpgradeButton: true,
  upgradeLink: "/billing/plans",
  upgradeText: "Upgrade Plan",
  hideIfExceeded: false,
  variant: GATE_VARIANTS.SIMPLE,
  enableAnalytics: true,
  onAnalytics: null,
  enableUsageWarning: true,
  warningThreshold: 0.8,
  showUsageProgress: true,
  onLimitExceeded: null,
  ariaLabel: null,
  testId: null
};

export default FeatureGate;

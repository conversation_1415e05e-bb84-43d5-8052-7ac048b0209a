"""
Feature flag management system for ACEO add-on gradual rollout.
Provides controlled deployment of add-on features with rollback capabilities.
"""
import logging
import json
import hashlib
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone, timedelta
from enum import Enum
from dataclasses import dataclass

from app.services.redis_connection import get_redis_client
from app.core.monitoring import record_addon_metrics
from app.models.user import User

logger = logging.getLogger(__name__)


class RolloutStrategy(str, Enum):
    """Feature rollout strategies."""
    PERCENTAGE = "percentage"
    USER_LIST = "user_list"
    PLAN_BASED = "plan_based"
    GEOGRAPHIC = "geographic"
    GRADUAL = "gradual"


class FeatureStatus(str, Enum):
    """Feature flag statuses."""
    DISABLED = "disabled"
    TESTING = "testing"
    ROLLING_OUT = "rolling_out"
    ENABLED = "enabled"
    DEPRECATED = "deprecated"


@dataclass
class FeatureFlag:
    """Feature flag configuration."""
    name: str
    description: str
    status: FeatureStatus
    strategy: RolloutStrategy
    config: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    created_by: str
    rollback_config: Optional[Dict[str, Any]] = None


class FeatureFlagManager:
    """Manages feature flags for add-on system rollout."""
    
    def __init__(self):
        self.redis_prefix = "feature_flag:"
        self.rollout_prefix = "rollout_data:"
        
        # Default feature flags for add-on system
        self.default_flags = {
            "addon_marketplace_v2": {
                "description": "New add-on marketplace interface with enhanced UX",
                "status": FeatureStatus.ROLLING_OUT,
                "strategy": RolloutStrategy.GRADUAL,
                "config": {
                    "initial_percentage": 25,
                    "target_percentage": 100,
                    "rollout_duration_hours": 72,
                    "target_plans": ["creator"],
                    "success_metrics": ["conversion_rate", "user_satisfaction"],
                    "rollback_triggers": {
                        "error_rate_threshold": 0.05,
                        "conversion_drop_threshold": 0.2
                    }
                }
            },
            "smart_recommendations": {
                "description": "AI-powered smart add-on recommendations",
                "status": FeatureStatus.ROLLING_OUT,
                "strategy": RolloutStrategy.PLAN_BASED,
                "config": {
                    "enabled_plans": ["accelerator", "dominator"],
                    "percentage": 50,
                    "success_metrics": ["click_through_rate", "purchase_conversion"],
                    "rollback_triggers": {
                        "performance_threshold": 2000,  # 2 seconds
                        "error_rate_threshold": 0.03
                    }
                }
            },
            "usage_predictions": {
                "description": "Predictive analytics for usage patterns",
                "status": FeatureStatus.DISABLED,
                "strategy": RolloutStrategy.PERCENTAGE,
                "config": {
                    "percentage": 0,
                    "target_plans": ["dominator"],
                    "success_metrics": ["prediction_accuracy", "user_engagement"]
                }
            },
            "advanced_analytics": {
                "description": "Enhanced analytics dashboard for add-ons",
                "status": FeatureStatus.TESTING,
                "strategy": RolloutStrategy.USER_LIST,
                "config": {
                    "enabled_users": [],  # Will be populated with beta testers
                    "success_metrics": ["dashboard_engagement", "feature_usage"]
                }
            },
            "bulk_addon_purchase": {
                "description": "Bulk purchase options for enterprise customers",
                "status": FeatureStatus.DISABLED,
                "strategy": RolloutStrategy.PLAN_BASED,
                "config": {
                    "enabled_plans": ["dominator"],
                    "percentage": 0
                }
            }
        }
    
    async def initialize_default_flags(self):
        """Initialize default feature flags if they don't exist."""
        try:
            for flag_name, flag_config in self.default_flags.items():
                existing_flag = await self.get_flag(flag_name)
                if not existing_flag:
                    await self.create_flag(
                        name=flag_name,
                        description=flag_config["description"],
                        status=flag_config["status"],
                        strategy=flag_config["strategy"],
                        config=flag_config["config"],
                        created_by="system"
                    )
                    logger.info(f"Initialized default feature flag: {flag_name}")
            
        except Exception as e:
            logger.error(f"Error initializing default flags: {str(e)}")
    
    async def create_flag(self, name: str, description: str, status: FeatureStatus,
                         strategy: RolloutStrategy, config: Dict[str, Any],
                         created_by: str) -> bool:
        """Create a new feature flag."""
        try:
            flag = FeatureFlag(
                name=name,
                description=description,
                status=status,
                strategy=strategy,
                config=config,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                created_by=created_by
            )
            
            flag_data = {
                "name": flag.name,
                "description": flag.description,
                "status": flag.status.value,
                "strategy": flag.strategy.value,
                "config": flag.config,
                "created_at": flag.created_at.isoformat(),
                "updated_at": flag.updated_at.isoformat(),
                "created_by": flag.created_by
            }
            
            redis_client = await get_redis_client()
            if redis_client:
                await redis_client.set(
                    f"{self.redis_prefix}{name}",
                    json.dumps(flag_data)
                )
            
            # Initialize rollout data
            await self._initialize_rollout_data(name, strategy, config)
            
            record_addon_metrics("feature_flag_created", "system", 1)
            logger.info(f"Created feature flag: {name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating feature flag {name}: {str(e)}")
            return False
    
    async def get_flag(self, name: str) -> Optional[FeatureFlag]:
        """Get a feature flag by name."""
        try:
            redis_client = await get_redis_client()
            if not redis_client:
                return None

            flag_data = await redis_client.get(f"{self.redis_prefix}{name}")
            if not flag_data:
                return None
            
            data = json.loads(flag_data)
            return FeatureFlag(
                name=data["name"],
                description=data["description"],
                status=FeatureStatus(data["status"]),
                strategy=RolloutStrategy(data["strategy"]),
                config=data["config"],
                created_at=datetime.fromisoformat(data["created_at"]),
                updated_at=datetime.fromisoformat(data["updated_at"]),
                created_by=data["created_by"],
                rollback_config=data.get("rollback_config")
            )
            
        except Exception as e:
            logger.error(f"Error getting feature flag {name}: {str(e)}")
            return None
    
    async def update_flag(self, name: str, updates: Dict[str, Any], updated_by: str) -> bool:
        """Update a feature flag."""
        try:
            flag = await self.get_flag(name)
            if not flag:
                return False
            
            # Store previous config for rollback
            flag.rollback_config = {
                "status": flag.status.value,
                "config": flag.config.copy(),
                "updated_at": flag.updated_at.isoformat()
            }
            
            # Apply updates
            if "status" in updates:
                flag.status = FeatureStatus(updates["status"])
            if "config" in updates:
                flag.config.update(updates["config"])
            if "description" in updates:
                flag.description = updates["description"]
            
            flag.updated_at = datetime.now(timezone.utc)
            
            # Save updated flag
            flag_data = {
                "name": flag.name,
                "description": flag.description,
                "status": flag.status.value,
                "strategy": flag.strategy.value,
                "config": flag.config,
                "created_at": flag.created_at.isoformat(),
                "updated_at": flag.updated_at.isoformat(),
                "created_by": flag.created_by,
                "rollback_config": flag.rollback_config
            }
            
            redis_client = await get_redis_client()
            if redis_client:
                await redis_client.set(
                    f"{self.redis_prefix}{name}",
                    json.dumps(flag_data)
                )
            
            record_addon_metrics("feature_flag_updated", "system", 1)
            logger.info(f"Updated feature flag: {name} by {updated_by}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating feature flag {name}: {str(e)}")
            return False
    
    async def is_enabled(self, flag_name: str, user: User) -> bool:
        """Check if a feature flag is enabled for a specific user."""
        try:
            flag = await self.get_flag(flag_name)
            if not flag or flag.status == FeatureStatus.DISABLED:
                return False
            
            if flag.status == FeatureStatus.ENABLED:
                return True
            
            # Apply rollout strategy
            if flag.strategy == RolloutStrategy.PERCENTAGE:
                return await self._check_percentage_rollout(flag_name, user, flag.config)
            
            elif flag.strategy == RolloutStrategy.PLAN_BASED:
                return await self._check_plan_based_rollout(user, flag.config)
            
            elif flag.strategy == RolloutStrategy.USER_LIST:
                return await self._check_user_list_rollout(user, flag.config)
            
            elif flag.strategy == RolloutStrategy.GRADUAL:
                return await self._check_gradual_rollout(flag_name, user, flag.config)
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking feature flag {flag_name} for user {user.id}: {str(e)}")
            return False
    
    async def _check_percentage_rollout(self, flag_name: str, user: User, config: Dict[str, Any]) -> bool:
        """Check percentage-based rollout."""
        percentage = config.get("percentage", 0)
        if percentage <= 0:
            return False
        if percentage >= 100:
            return True
        
        # Deterministic assignment based on user ID and flag name
        user_hash = hashlib.md5(f"{flag_name}:{user.id}".encode()).hexdigest()
        hash_value = int(user_hash[:8], 16) / (2**32) * 100
        
        return hash_value < percentage
    
    async def _check_plan_based_rollout(self, user: User, config: Dict[str, Any]) -> bool:
        """Check plan-based rollout."""
        enabled_plans = config.get("enabled_plans", [])
        user_plan = user.subscription.plan_id if user.subscription else "creator"
        
        if user_plan not in enabled_plans:
            return False
        
        # Check percentage within the plan
        percentage = config.get("percentage", 100)
        if percentage >= 100:
            return True
        
        user_hash = hashlib.md5(f"plan_rollout:{user.id}".encode()).hexdigest()
        hash_value = int(user_hash[:8], 16) / (2**32) * 100
        
        return hash_value < percentage
    
    async def _check_user_list_rollout(self, user: User, config: Dict[str, Any]) -> bool:
        """Check user list-based rollout."""
        enabled_users = config.get("enabled_users", [])
        return user.id in enabled_users or user.email in enabled_users
    
    async def _check_gradual_rollout(self, flag_name: str, user: User, config: Dict[str, Any]) -> bool:
        """Check gradual rollout with time-based progression."""
        try:
            # Get rollout data
            rollout_data = await self._get_rollout_data(flag_name)
            if not rollout_data:
                return False
            
            current_percentage = rollout_data.get("current_percentage", 0)
            
            # Check if user is in current rollout
            return await self._check_percentage_rollout(flag_name, user, {"percentage": current_percentage})
            
        except Exception as e:
            logger.error(f"Error in gradual rollout check: {str(e)}")
            return False
    
    async def _initialize_rollout_data(self, flag_name: str, strategy: RolloutStrategy, config: Dict[str, Any]):
        """Initialize rollout tracking data."""
        try:
            if strategy == RolloutStrategy.GRADUAL:
                rollout_data = {
                    "flag_name": flag_name,
                    "start_time": datetime.now(timezone.utc).isoformat(),
                    "current_percentage": config.get("initial_percentage", 0),
                    "target_percentage": config.get("target_percentage", 100),
                    "rollout_duration_hours": config.get("rollout_duration_hours", 72),
                    "last_update": datetime.now(timezone.utc).isoformat(),
                    "metrics": {
                        "users_enabled": 0,
                        "success_rate": 0,
                        "error_rate": 0
                    }
                }
                
                redis_client = await get_redis_client()
                if redis_client:
                    await redis_client.set(
                        f"{self.rollout_prefix}{flag_name}",
                        json.dumps(rollout_data)
                    )
                
        except Exception as e:
            logger.error(f"Error initializing rollout data for {flag_name}: {str(e)}")
    
    async def _get_rollout_data(self, flag_name: str) -> Optional[Dict[str, Any]]:
        """Get rollout tracking data."""
        try:
            redis_client = await get_redis_client()
            if not redis_client:
                return None

            data = await redis_client.get(f"{self.rollout_prefix}{flag_name}")
            return json.loads(data) if data else None
        except Exception as e:
            logger.error(f"Error getting rollout data for {flag_name}: {str(e)}")
            return None
    
    async def update_gradual_rollout(self, flag_name: str) -> bool:
        """Update gradual rollout percentage based on time and metrics."""
        try:
            flag = await self.get_flag(flag_name)
            if not flag or flag.strategy != RolloutStrategy.GRADUAL:
                return False
            
            rollout_data = await self._get_rollout_data(flag_name)
            if not rollout_data:
                return False
            
            # Calculate time-based progression
            start_time = datetime.fromisoformat(rollout_data["start_time"])
            duration_hours = rollout_data["rollout_duration_hours"]
            elapsed_hours = (datetime.now(timezone.utc) - start_time).total_seconds() / 3600
            
            if elapsed_hours >= duration_hours:
                # Rollout complete
                new_percentage = rollout_data["target_percentage"]
            else:
                # Linear progression
                progress = elapsed_hours / duration_hours
                initial_percentage = flag.config.get("initial_percentage", 0)
                target_percentage = rollout_data["target_percentage"]
                new_percentage = initial_percentage + (target_percentage - initial_percentage) * progress
            
            # Update rollout data
            rollout_data["current_percentage"] = new_percentage
            rollout_data["last_update"] = datetime.now(timezone.utc).isoformat()
            
            redis_client = await get_redis_client()
            if redis_client:
                await redis_client.set(
                    f"{self.rollout_prefix}{flag_name}",
                    json.dumps(rollout_data)
                )
            
            logger.info(f"Updated gradual rollout for {flag_name}: {new_percentage:.1f}%")
            return True
            
        except Exception as e:
            logger.error(f"Error updating gradual rollout for {flag_name}: {str(e)}")
            return False
    
    async def rollback_flag(self, flag_name: str, rollback_reason: str, rolled_back_by: str) -> bool:
        """Rollback a feature flag to its previous configuration."""
        try:
            flag = await self.get_flag(flag_name)
            if not flag or not flag.rollback_config:
                return False
            
            # Apply rollback
            updates = {
                "status": flag.rollback_config["status"],
                "config": flag.rollback_config["config"]
            }
            
            success = await self.update_flag(flag_name, updates, rolled_back_by)
            
            if success:
                # Log rollback event
                rollback_event = {
                    "flag_name": flag_name,
                    "reason": rollback_reason,
                    "rolled_back_by": rolled_back_by,
                    "rollback_time": datetime.now(timezone.utc).isoformat(),
                    "previous_config": {
                        "status": flag.status.value,
                        "config": flag.config
                    }
                }
                
                redis_client = await get_redis_client()
                if redis_client:
                    redis_client.lpush(
                        f"rollback_log:{flag_name}",
                        json.dumps(rollback_event)
                    )
                
                record_addon_metrics("feature_flag_rollback", "system", 1)
                logger.warning(f"Rolled back feature flag {flag_name}: {rollback_reason}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error rolling back feature flag {flag_name}: {str(e)}")
            return False
    
    async def get_all_flags(self) -> List[FeatureFlag]:
        """Get all feature flags."""
        try:
            flags = []
            redis_client = await get_redis_client()
            if not redis_client:
                return flags

            keys = await redis_client.keys(f"{self.redis_prefix}*")
            
            for key in keys:
                flag_name = key.replace(self.redis_prefix, "")
                flag = await self.get_flag(flag_name)
                if flag:
                    flags.append(flag)
            
            return flags
            
        except Exception as e:
            logger.error(f"Error getting all flags: {str(e)}")
            return []


# Global feature flag manager
feature_flag_manager = FeatureFlagManager()


async def is_feature_enabled(flag_name: str, user: User) -> bool:
    """Check if a feature is enabled for a user."""
    return await feature_flag_manager.is_enabled(flag_name, user)


async def initialize_feature_flags():
    """Initialize default feature flags."""
    await feature_flag_manager.initialize_default_flags()


async def update_gradual_rollouts():
    """Update all gradual rollouts (called by scheduler)."""
    try:
        flags = await feature_flag_manager.get_all_flags()
        for flag in flags:
            if flag.strategy == RolloutStrategy.GRADUAL and flag.status == FeatureStatus.ROLLING_OUT:
                await feature_flag_manager.update_gradual_rollout(flag.name)
    except Exception as e:
        logger.error(f"Error updating gradual rollouts: {str(e)}")

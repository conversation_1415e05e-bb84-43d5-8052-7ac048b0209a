// @since 2024-1-1 to 2025-25-7
import React from "react";
import { Helmet } from "react-helmet-async";
import { Box, Container, Typography, Breadcrumbs, Link } from "@mui/material";
import { Link as RouterLink } from "react-router-dom";
import CompetitorInsightContentGenerator from "../../components/content/CompetitorInsightContentGenerator";
import { CompetitorProvider } from "../../contexts/CompetitorContext";

const CompetitorInsightContentPage = () => {
  return (
    <>
      <Helmet>
        <title>Competitor-Driven Content Generator</title>
        <meta
          name="description"
          content="Generate content based on competitor insights and analysis"
        />
      </Helmet>

      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          {/* Breadcrumbs */}
          <Breadcrumbs sx={{ mb: 2 }}>
            <Link component={RouterLink} to="/dashboard" color="inherit">
              Dashboard
            </Link>
            <Link
              component={RouterLink}
              to="/content/generator"
              color="inherit"
            >
              Content
            </Link>
            <Typography color="text.primary">
              Competitor-Driven Content
            </Typography>
          </Breadcrumbs>

          {/* Page Title */}
          <Typography variant="h4" component="h1" gutterBottom>
            Competitor-Driven Content Generator
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Create content based on competitor analysis and insights. Select
            competitors to analyze their successful strategies and generate
            content that outperforms them.
          </Typography>

          {/* Main Content */}
          <Box sx={{ mt: 4 }}>
            <CompetitorProvider>
              <CompetitorInsightContentGenerator />
            </CompetitorProvider>
          </Box>
        </Box>
      </Container>
    </>
  );
};

export default CompetitorInsightContentPage;

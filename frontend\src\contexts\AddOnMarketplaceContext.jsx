/**
 * @fileoverview AddOnMarketplaceContext - Context for managing add-on marketplace functionality
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 */

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';

// Create the context
const AddOnMarketplaceContext = createContext();

/**
 * AddOnMarketplaceProvider component
 */
export const AddOnMarketplaceProvider = ({ children }) => {
  // State management
  const [addOns, setAddOns] = useState([]);
  const [purchasedAddOns, setPurchasedAddOns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Mock add-ons data (in production, this would come from an API)
  const mockAddOns = [
    {
      id: 'credits-100',
      name: '100 Additional Credits',
      description: 'Get 100 extra credits for content generation',
      price: 9.99,
      credits: 100,
      type: 'credits',
      popular: false
    },
    {
      id: 'credits-500',
      name: '500 Additional Credits',
      description: 'Get 500 extra credits for content generation',
      price: 39.99,
      credits: 500,
      type: 'credits',
      popular: true
    },
    {
      id: 'credits-1000',
      name: '1000 Additional Credits',
      description: 'Get 1000 extra credits for content generation',
      price: 69.99,
      credits: 1000,
      type: 'credits',
      popular: false
    }
  ];

  /**
   * Load available add-ons
   */
  const loadAddOns = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // In production, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
      setAddOns(mockAddOns);
    } catch (err) {
      setError('Failed to load add-ons');
      console.error('Error loading add-ons:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Purchase an add-on
   */
  const purchaseAddOn = useCallback(async (addOnId) => {
    setLoading(true);
    setError(null);

    try {
      // In production, this would be an API call to process payment
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      const addOn = addOns.find(a => a.id === addOnId);
      if (addOn) {
        setPurchasedAddOns(prev => [...prev, {
          ...addOn,
          purchaseDate: new Date().toISOString(),
          transactionId: `txn_${Date.now()}`
        }]);
        
        return {
          success: true,
          message: `Successfully purchased ${addOn.name}`,
          addOn
        };
      }
      
      throw new Error('Add-on not found');
    } catch (err) {
      setError('Failed to purchase add-on');
      console.error('Error purchasing add-on:', err);
      return {
        success: false,
        message: 'Purchase failed. Please try again.',
        error: err.message
      };
    } finally {
      setLoading(false);
    }
  }, [addOns]);

  /**
   * Get purchase history
   */
  const getPurchaseHistory = useCallback(() => {
    return purchasedAddOns.sort((a, b) => 
      new Date(b.purchaseDate) - new Date(a.purchaseDate)
    );
  }, [purchasedAddOns]);

  /**
   * Check if user has purchased a specific add-on
   */
  const hasPurchased = useCallback((addOnId) => {
    return purchasedAddOns.some(addOn => addOn.id === addOnId);
  }, [purchasedAddOns]);

  /**
   * Get total credits purchased
   */
  const getTotalCreditsPurchased = useCallback(() => {
    return purchasedAddOns
      .filter(addOn => addOn.type === 'credits')
      .reduce((total, addOn) => total + addOn.credits, 0);
  }, [purchasedAddOns]);

  /**
   * Get recommended add-ons based on usage
   */
  const getRecommendedAddOns = useCallback((userUsage = {}) => {
    // Simple recommendation logic
    const { creditsUsed = 0, planType = 'creator' } = userUsage;
    
    if (creditsUsed > 80) {
      return addOns.filter(addOn => addOn.type === 'credits');
    }
    
    return addOns.filter(addOn => addOn.popular);
  }, [addOns]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Load add-ons on mount
  useEffect(() => {
    loadAddOns();
  }, [loadAddOns]);

  // Context value
  const contextValue = {
    // State
    addOns,
    purchasedAddOns,
    loading,
    error,
    
    // Actions
    loadAddOns,
    purchaseAddOn,
    getPurchaseHistory,
    hasPurchased,
    getTotalCreditsPurchased,
    getRecommendedAddOns,
    clearError
  };

  return (
    <AddOnMarketplaceContext.Provider value={contextValue}>
      {children}
    </AddOnMarketplaceContext.Provider>
  );
};

AddOnMarketplaceProvider.propTypes = {
  children: PropTypes.node.isRequired
};

/**
 * Hook to use the AddOnMarketplace context
 */
export const useAddOnMarketplace = () => {
  const context = useContext(AddOnMarketplaceContext);
  
  if (!context) {
    throw new Error('useAddOnMarketplace must be used within an AddOnMarketplaceProvider');
  }
  
  return context;
};

/**
 * HOC to wrap components with AddOnMarketplaceProvider
 */
export const withAddOnMarketplace = (Component) => {
  const WrappedComponent = (props) => (
    <AddOnMarketplaceProvider>
      <Component {...props} />
    </AddOnMarketplaceProvider>
  );
  
  WrappedComponent.displayName = `withAddOnMarketplace(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default AddOnMarketplaceContext;

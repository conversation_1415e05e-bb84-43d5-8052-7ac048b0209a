# @since 2024-1-1 to 2025-25-7
# ACEO - Development Server Startup Script
# PowerShell version for better Windows support

param(
    [string]$Mode = "standard",
    [switch]$InstallOnly,
    [switch]$Help
)

function Show-Help {
    Write-Host ""
    Write-Host "ACEO - Development Server" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\start-dev.ps1 [-Mode <mode>] [-InstallOnly] [-Help]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Modes:" -ForegroundColor Cyan
    Write-Host "  standard  - Standard development mode (default)" -ForegroundColor White
    Write-Host "  windows   - Windows native mode" -ForegroundColor White
    Write-Host "  memory    - High memory mode (8GB)" -ForegroundColor White
    Write-Host "  simple    - Simple mode (no optimization)" -ForegroundColor White
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Cyan
    Write-Host "  -InstallOnly  - Only install dependencies" -ForegroundColor White
    Write-Host "  -Help         - Show this help message" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Cyan
    Write-Host "  .\start-dev.ps1" -ForegroundColor White
    Write-Host "  .\start-dev.ps1 -Mode memory" -ForegroundColor White
    Write-Host "  .\start-dev.ps1 -InstallOnly" -ForegroundColor White
    Write-Host ""
}

function Test-Prerequisites {
    Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow
    
    # Check if we're in the right directory
    if (-not (Test-Path "package.json")) {
        Write-Host "❌ Error: package.json not found!" -ForegroundColor Red
        Write-Host "Please run this script from the project root directory." -ForegroundColor Red
        exit 1
    }
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Node.js not found! Please install Node.js." -ForegroundColor Red
        exit 1
    }
    
    # Check Python
    try {
        $pythonVersion = python --version
        Write-Host "✅ Python: $pythonVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ Python not found! Please install Python." -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
}

function Install-Dependencies {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
    
    # Install root dependencies
    if (-not (Test-Path "node_modules")) {
        Write-Host "Installing root dependencies..." -ForegroundColor Cyan
        npm install
    }
    
    # Install frontend dependencies
    if (-not (Test-Path "frontend\node_modules")) {
        Write-Host "Installing frontend dependencies..." -ForegroundColor Cyan
        Set-Location frontend
        npm install
        Set-Location ..
    }
    
    # Check Python virtual environment
    if (-not (Test-Path "venv")) {
        Write-Host "Creating Python virtual environment..." -ForegroundColor Cyan
        python -m venv venv
    }
    
    # Install backend dependencies
    Write-Host "Installing backend dependencies..." -ForegroundColor Cyan
    Set-Location backend
    ..\venv\Scripts\Activate.ps1
    pip install -r requirements.txt
    deactivate
    Set-Location ..
    
    Write-Host "✅ All dependencies installed!" -ForegroundColor Green
    Write-Host ""
}

function Start-Development {
    param([string]$DevMode)
    
    Write-Host "🚀 Starting development servers..." -ForegroundColor Green
    Write-Host ""
    Write-Host "Backend will be available at: http://localhost:8000" -ForegroundColor Cyan
    Write-Host "Frontend will be available at: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "API Documentation: http://localhost:8000/api/docs" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Press Ctrl+C to stop both servers" -ForegroundColor Yellow
    Write-Host ""
    
    switch ($DevMode) {
        "standard" { npm run dev }
        "windows" { npm run dev:windows }
        "memory" { npm run dev:memory }
        "simple" { npm run dev:simple }
        default { npm run dev }
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "   ACEO Development" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Test-Prerequisites

if ($InstallOnly) {
    Install-Dependencies
    Write-Host "Dependencies installation complete!" -ForegroundColor Green
    Write-Host "Run '.\start-dev.ps1' to start the development servers." -ForegroundColor Yellow
    exit 0
}

# Check if dependencies need to be installed
if (-not (Test-Path "node_modules") -or -not (Test-Path "frontend\node_modules")) {
    Write-Host "⚠️  Missing dependencies. Installing..." -ForegroundColor Yellow
    Install-Dependencies
}

Write-Host "Starting in $Mode mode..." -ForegroundColor Green
Start-Development -DevMode $Mode

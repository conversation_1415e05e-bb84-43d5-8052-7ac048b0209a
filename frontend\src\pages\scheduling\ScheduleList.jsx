// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  useTheme
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers';
import { format } from 'date-fns';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import FilterListIcon from '@mui/icons-material/FilterList';
import { useNavigate } from 'react-router-dom';
import { useNotification } from '../../contexts/NotificationContext';
import api from '../../api';

// Real API data - no more mock data

const ScheduleList = ({ isEmbedded = false }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [scheduledContent, setScheduledContent] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedContent, setSelectedContent] = useState(null);
  const [filterPlatform, setFilterPlatform] = useState('');
  const [filterCampaign, setFilterCampaign] = useState('');
  const [openFilters, setOpenFilters] = useState(false);

  // Fetch scheduled content (replace with actual API call)
  useEffect(() => {
    const fetchScheduledContent = async () => {
      setLoading(true);
      try {
        // Real API call - no more mock data
        const response = await api.get('/api/scheduling/schedules');
        setScheduledContent(response.data.schedules || []);
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching scheduled content:', error);
        }
        showErrorNotification('Failed to load scheduled content');
      } finally {
        setLoading(false);
      }
    };

    fetchScheduledContent();
  }, [showErrorNotification]);

  // Get unique platforms and campaigns for filters
  const platforms = [...new Set(scheduledContent.map(item => item.platform))];
  const campaigns = [...new Set(scheduledContent.map(item => item.campaign_name))];

  // Apply filters
  const filteredContent = scheduledContent.filter(item => {
    if (filterPlatform && item.platform !== filterPlatform) return false;
    if (filterCampaign && item.campaign_name !== filterCampaign) return false;
    return true;
  });

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEditContent = (content) => {
    setSelectedContent(content);
    setOpenDialog(true);
  };

  const handleDeleteContent = async (contentId) => {
    try {
      // Real API call to delete scheduled content
      await api.delete(`/api/scheduling/schedules/${contentId}`);
      setScheduledContent(scheduledContent.filter(item => item.id !== contentId));
      showSuccessNotification('Content removed from schedule');
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error deleting scheduled content:', error);
      }
      showErrorNotification('Failed to remove content from schedule');
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedContent(null);
  };

  const handleSaveChanges = () => {
    // Update scheduled content
    setScheduledContent(
      scheduledContent.map(item =>
        item.id === selectedContent.id ? selectedContent : item
      )
    );
    handleCloseDialog();
    showSuccessNotification('Schedule updated successfully');
  };

  const handleAddContent = () => {
    navigate('/content/generator');
  };

  const getPlatformColor = (platform) => {
    switch (platform.toLowerCase()) {
      case 'linkedin':
        return '#0077B5';
      case 'twitter':
        return '#1DA1F2';
      case 'facebook':
        return '#4267B2';
      case 'instagram':
        return '#C13584';
      default:
        return theme.palette.primary.main;
    }
  };

  const handleClearFilters = () => {
    setFilterPlatform('');
    setFilterCampaign('');
  };

  return (
    <Box sx={{ p: isEmbedded ? 0 : 3 }}>
      {!isEmbedded && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">Scheduled Content</Typography>
          <Box>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => setOpenFilters(!openFilters)}
              sx={{ mr: 1 }}
            >
              Filters
            </Button>
            <Button
              variant="contained"
              onClick={handleAddContent}
            >
              Schedule New Content
            </Button>
          </Box>
        </Box>
      )}

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Button
          variant="outlined"
          startIcon={<FilterListIcon />}
          onClick={() => setOpenFilters(!openFilters)}
          size={isEmbedded ? "small" : "medium"}
        >
          Filters
        </Button>
      </Box>

      {/* Filters */}
      {openFilters && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>Platform</InputLabel>
              <Select
                value={filterPlatform}
                label="Platform"
                onChange={(e) => setFilterPlatform(e.target.value)}
              >
                <MenuItem value="">All Platforms</MenuItem>
                {platforms.map(platform => (
                  <MenuItem key={platform} value={platform}>{platform}</MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>Campaign</InputLabel>
              <Select
                value={filterCampaign}
                label="Campaign"
                onChange={(e) => setFilterCampaign(e.target.value)}
              >
                <MenuItem value="">All Campaigns</MenuItem>
                {campaigns.map(campaign => (
                  <MenuItem key={campaign} value={campaign}>{campaign}</MenuItem>
                ))}
              </Select>
            </FormControl>

            <Button
              variant="outlined"
              onClick={handleClearFilters}
              sx={{ alignSelf: 'center' }}
            >
              Clear Filters
            </Button>
          </Box>
        </Paper>
      )}

      {/* Content Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>Scheduled Date</TableCell>
                <TableCell>Platform</TableCell>
                <TableCell>Campaign</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : filteredContent.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <Typography>No scheduled content found</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                filteredContent
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((content) => (
                    <TableRow key={content.id}>
                      <TableCell>{content.title}</TableCell>
                      <TableCell>
                        {format(new Date(content.scheduled_date), 'MMM d, yyyy h:mm a')}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={content.platform}
                          size="small"
                          sx={{
                            backgroundColor: getPlatformColor(content.platform),
                            color: 'white'
                          }}
                        />
                      </TableCell>
                      <TableCell>{content.campaign_name}</TableCell>
                      <TableCell>
                        <Chip
                          label={content.status}
                          size="small"
                          color={content.status === 'scheduled' ? 'primary' : 'default'}
                          variant={content.status === 'scheduled' ? 'filled' : 'outlined'}
                        />
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={() => handleEditContent(content)}
                          color="primary"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteContent(content.id)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredContent.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Scheduled Content</DialogTitle>
        <DialogContent>
          {selectedContent && (
            <Box sx={{ pt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Title"
                fullWidth
                value={selectedContent.title}
                onChange={(e) => setSelectedContent({
                  ...selectedContent,
                  title: e.target.value
                })}
              />

              <DateTimePicker
                label="Scheduled Date & Time"
                value={new Date(selectedContent.scheduled_date)}
                onChange={(newDate) => {
                  setSelectedContent({
                    ...selectedContent,
                    scheduled_date: newDate
                  });
                }}
                sx={{ width: '100%' }}
              />

              <FormControl fullWidth>
                <InputLabel>Platform</InputLabel>
                <Select
                  value={selectedContent.platform}
                  label="Platform"
                  onChange={(e) => setSelectedContent({
                    ...selectedContent,
                    platform: e.target.value
                  })}
                >
                  {platforms.map(platform => (
                    <MenuItem key={platform} value={platform}>{platform}</MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>Campaign</InputLabel>
                <Select
                  value={selectedContent.campaign_name}
                  label="Campaign"
                  onChange={(e) => {
                    // Find the campaign_id that matches this name
                    const campaignId = scheduledContent.find(
                      item => item.campaign_name === e.target.value
                    )?.campaign_id || selectedContent.campaign_id;

                    setSelectedContent({
                      ...selectedContent,
                      campaign_name: e.target.value,
                      campaign_id: campaignId
                    });
                  }}
                >
                  {campaigns.map(campaign => (
                    <MenuItem key={campaign} value={campaign}>{campaign}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSaveChanges}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ScheduleList;

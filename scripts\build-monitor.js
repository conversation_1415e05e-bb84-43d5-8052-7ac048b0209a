#!/usr/bin/env node

/**
 * ACE Social Platform - Build Performance Monitor
 * Version: 2.0.0
 * 
 * Tracks build performance metrics, monitors bundle sizes,
 * and provides optimization recommendations.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Script metadata
const SCRIPT_VERSION = '2.0.0';
const SCRIPT_NAME = 'ACE Social Build Performance Monitor';

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
    buildTime: {
        frontend: 120, // 2 minutes
        backend: 60,   // 1 minute
        admin: 60,     // 1 minute
        docker: 300    // 5 minutes
    },
    bundleSize: {
        frontend: 2 * 1024 * 1024,    // 2MB
        admin: 1 * 1024 * 1024,       // 1MB
        gzipped: 0.3                  // 30% compression ratio
    },
    dependencies: {
        maxCount: 500,
        maxSize: 100 * 1024 * 1024    // 100MB
    }
};

// Colors for console output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    purple: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    const timestamp = new Date().toISOString();
    console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logHeader(message) {
    console.log(`${colors.white}${'='.repeat(60)}${colors.reset}`);
    console.log(`${colors.white}${message}${colors.reset}`);
    console.log(`${colors.white}${'='.repeat(60)}${colors.reset}`);
}

function logSection(message) {
    console.log(`${colors.blue}${'─'.repeat(40)}${colors.reset}`);
    console.log(`${colors.blue}${message}${colors.reset}`);
    console.log(`${colors.blue}${'─'.repeat(40)}${colors.reset}`);
}

function executeCommand(command, options = {}) {
    try {
        const startTime = Date.now();
        const result = execSync(command, { 
            encoding: 'utf8', 
            stdio: options.silent ? 'pipe' : 'inherit',
            ...options 
        });
        const duration = Date.now() - startTime;
        return { success: true, output: result.trim(), duration };
    } catch (error) {
        return { success: false, error: error.message, output: error.stdout || '', duration: 0 };
    }
}

function getDirectorySize(dirPath) {
    try {
        let totalSize = 0;
        const files = fs.readdirSync(dirPath, { withFileTypes: true });
        
        for (const file of files) {
            const fullPath = path.join(dirPath, file.name);
            if (file.isDirectory()) {
                totalSize += getDirectorySize(fullPath);
            } else {
                totalSize += fs.statSync(fullPath).size;
            }
        }
        
        return totalSize;
    } catch (error) {
        return 0;
    }
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
        return `${minutes}m ${remainingSeconds}s`;
    }
    return `${seconds}s`;
}

function analyzeBundleSize(distPath, name) {
    logSection(`Analyzing ${name} Bundle Size`);
    
    if (!fs.existsSync(distPath)) {
        log(`❌ ${name} build output not found: ${distPath}`, 'red');
        return null;
    }
    
    const totalSize = getDirectorySize(distPath);
    const threshold = PERFORMANCE_THRESHOLDS.bundleSize[name.toLowerCase()] || PERFORMANCE_THRESHOLDS.bundleSize.frontend;
    
    const analysis = {
        name,
        path: distPath,
        totalSize,
        threshold,
        withinThreshold: totalSize <= threshold,
        files: []
    };
    
    // Analyze individual files
    try {
        const files = fs.readdirSync(distPath, { recursive: true });
        files.forEach(file => {
            const filePath = path.join(distPath, file);
            const stats = fs.statSync(filePath);
            
            if (stats.isFile()) {
                analysis.files.push({
                    name: file,
                    size: stats.size,
                    type: path.extname(file).toLowerCase()
                });
            }
        });
        
        // Sort files by size (largest first)
        analysis.files.sort((a, b) => b.size - a.size);
        
        // Calculate file type breakdown
        analysis.breakdown = analysis.files.reduce((acc, file) => {
            const type = file.type || 'other';
            if (!acc[type]) acc[type] = { count: 0, size: 0 };
            acc[type].count++;
            acc[type].size += file.size;
            return acc;
        }, {});
        
    } catch (error) {
        log(`⚠️  Error analyzing files in ${distPath}: ${error.message}`, 'yellow');
    }
    
    // Log results
    log(`📦 ${name} Bundle Analysis:`, 'blue');
    log(`   Total Size: ${formatBytes(totalSize)}`, totalSize <= threshold ? 'green' : 'red');
    log(`   Threshold: ${formatBytes(threshold)}`, 'blue');
    log(`   Status: ${analysis.withinThreshold ? '✅ Within threshold' : '❌ Exceeds threshold'}`, 
        analysis.withinThreshold ? 'green' : 'red');
    
    // Show largest files
    if (analysis.files.length > 0) {
        log(`   Largest files:`, 'blue');
        analysis.files.slice(0, 5).forEach(file => {
            log(`     ${file.name}: ${formatBytes(file.size)}`, 'cyan');
        });
    }
    
    // Show breakdown by file type
    if (analysis.breakdown) {
        log(`   File type breakdown:`, 'blue');
        Object.entries(analysis.breakdown).forEach(([type, stats]) => {
            log(`     ${type || 'no extension'}: ${stats.count} files, ${formatBytes(stats.size)}`, 'cyan');
        });
    }
    
    return analysis;
}

function measureBuildTime(component, buildCommand) {
    logSection(`Measuring ${component} Build Time`);
    
    log(`🔨 Building ${component}...`, 'blue');
    const result = executeCommand(buildCommand, { silent: false });
    
    const threshold = PERFORMANCE_THRESHOLDS.buildTime[component.toLowerCase()] * 1000; // Convert to ms
    const withinThreshold = result.duration <= threshold;
    
    log(`⏱️  ${component} build completed in ${formatDuration(result.duration)}`, 
        withinThreshold ? 'green' : 'red');
    log(`   Threshold: ${formatDuration(threshold)}`, 'blue');
    log(`   Status: ${withinThreshold ? '✅ Within threshold' : '❌ Exceeds threshold'}`, 
        withinThreshold ? 'green' : 'red');
    
    return {
        component,
        duration: result.duration,
        threshold,
        withinThreshold,
        success: result.success
    };
}

function analyzeDependencies() {
    logSection('Analyzing Dependencies');
    
    const packageFiles = [
        { path: 'package.json', name: 'Root' },
        { path: 'frontend/package.json', name: 'Frontend' },
        { path: 'admin-app/package.json', name: 'Admin' },
        { path: 'backend/requirements.txt', name: 'Backend' }
    ];
    
    const analysis = {
        packages: [],
        totalDependencies: 0,
        securityIssues: 0,
        outdatedPackages: 0
    };
    
    packageFiles.forEach(({ path: pkgPath, name }) => {
        if (!fs.existsSync(pkgPath)) return;
        
        try {
            let depCount = 0;
            let devDepCount = 0;
            
            if (pkgPath.endsWith('.json')) {
                const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf8'));
                depCount = Object.keys(pkg.dependencies || {}).length;
                devDepCount = Object.keys(pkg.devDependencies || {}).length;
                
                // Check for security issues
                const auditResult = executeCommand(`cd ${path.dirname(pkgPath)} && npm audit --json`, { silent: true });
                if (auditResult.success) {
                    try {
                        const auditData = JSON.parse(auditResult.output);
                        const vulnerabilities = auditData.metadata?.vulnerabilities || {};
                        const totalVulns = Object.values(vulnerabilities).reduce((sum, count) => sum + count, 0);
                        analysis.securityIssues += totalVulns;
                    } catch (e) {
                        // Ignore parsing errors
                    }
                }
                
            } else if (pkgPath.endsWith('.txt')) {
                const content = fs.readFileSync(pkgPath, 'utf8');
                depCount = content.split('\n').filter(line => line.trim() && !line.startsWith('#')).length;
            }
            
            const totalDeps = depCount + devDepCount;
            analysis.totalDependencies += totalDeps;
            
            analysis.packages.push({
                name,
                path: pkgPath,
                dependencies: depCount,
                devDependencies: devDepCount,
                total: totalDeps
            });
            
            log(`📦 ${name}: ${depCount} deps, ${devDepCount} dev deps`, 'blue');
            
        } catch (error) {
            log(`⚠️  Error analyzing ${pkgPath}: ${error.message}`, 'yellow');
        }
    });
    
    // Check node_modules size
    const nodeModulesSize = fs.existsSync('node_modules') ? getDirectorySize('node_modules') : 0;
    analysis.nodeModulesSize = nodeModulesSize;
    
    log(`📊 Dependency Summary:`, 'blue');
    log(`   Total dependencies: ${analysis.totalDependencies}`, 'cyan');
    log(`   Security issues: ${analysis.securityIssues}`, analysis.securityIssues > 0 ? 'red' : 'green');
    log(`   node_modules size: ${formatBytes(nodeModulesSize)}`, 'cyan');
    
    return analysis;
}

function generateOptimizationRecommendations(buildMetrics, bundleAnalysis, dependencyAnalysis) {
    logSection('Optimization Recommendations');
    
    const recommendations = [];
    
    // Build time recommendations
    buildMetrics.forEach(metric => {
        if (!metric.withinThreshold) {
            recommendations.push({
                category: 'Build Performance',
                priority: 'High',
                issue: `${metric.component} build time (${formatDuration(metric.duration)}) exceeds threshold (${formatDuration(metric.threshold)})`,
                suggestions: [
                    'Enable build caching',
                    'Optimize webpack configuration',
                    'Use parallel processing',
                    'Remove unused dependencies'
                ]
            });
        }
    });
    
    // Bundle size recommendations
    bundleAnalysis.forEach(analysis => {
        if (analysis && !analysis.withinThreshold) {
            recommendations.push({
                category: 'Bundle Size',
                priority: 'High',
                issue: `${analysis.name} bundle size (${formatBytes(analysis.totalSize)}) exceeds threshold (${formatBytes(analysis.threshold)})`,
                suggestions: [
                    'Implement code splitting',
                    'Enable tree shaking',
                    'Optimize images and assets',
                    'Use dynamic imports',
                    'Remove unused code'
                ]
            });
        }
        
        if (analysis && analysis.breakdown) {
            // Check for large JavaScript files
            const jsSize = analysis.breakdown['.js']?.size || 0;
            if (jsSize > 1024 * 1024) { // 1MB
                recommendations.push({
                    category: 'JavaScript Optimization',
                    priority: 'Medium',
                    issue: `Large JavaScript bundle (${formatBytes(jsSize)})`,
                    suggestions: [
                        'Split vendor and app bundles',
                        'Implement lazy loading',
                        'Use smaller alternative libraries',
                        'Enable minification and compression'
                    ]
                });
            }
            
            // Check for large CSS files
            const cssSize = analysis.breakdown['.css']?.size || 0;
            if (cssSize > 512 * 1024) { // 512KB
                recommendations.push({
                    category: 'CSS Optimization',
                    priority: 'Medium',
                    issue: `Large CSS bundle (${formatBytes(cssSize)})`,
                    suggestions: [
                        'Remove unused CSS',
                        'Use CSS-in-JS for component styles',
                        'Enable CSS minification',
                        'Split critical and non-critical CSS'
                    ]
                });
            }
        }
    });
    
    // Dependency recommendations
    if (dependencyAnalysis.totalDependencies > PERFORMANCE_THRESHOLDS.dependencies.maxCount) {
        recommendations.push({
            category: 'Dependencies',
            priority: 'Medium',
            issue: `High dependency count (${dependencyAnalysis.totalDependencies})`,
            suggestions: [
                'Audit and remove unused dependencies',
                'Use lighter alternative packages',
                'Move dev dependencies appropriately',
                'Consider bundling common utilities'
            ]
        });
    }
    
    if (dependencyAnalysis.nodeModulesSize > PERFORMANCE_THRESHOLDS.dependencies.maxSize) {
        recommendations.push({
            category: 'Dependencies',
            priority: 'Medium',
            issue: `Large node_modules size (${formatBytes(dependencyAnalysis.nodeModulesSize)})`,
            suggestions: [
                'Use npm ci instead of npm install',
                'Clean install dependencies regularly',
                'Use .npmrc to optimize package resolution',
                'Consider using pnpm for better deduplication'
            ]
        });
    }
    
    if (dependencyAnalysis.securityIssues > 0) {
        recommendations.push({
            category: 'Security',
            priority: 'High',
            issue: `${dependencyAnalysis.securityIssues} security vulnerabilities found`,
            suggestions: [
                'Run npm audit fix',
                'Update vulnerable packages',
                'Use npm audit in CI/CD pipeline',
                'Consider alternative packages for vulnerable dependencies'
            ]
        });
    }
    
    // Display recommendations
    if (recommendations.length === 0) {
        log('🎉 No optimization recommendations - build performance is excellent!', 'green');
    } else {
        log(`📋 Found ${recommendations.length} optimization opportunities:`, 'yellow');
        
        recommendations.forEach((rec, index) => {
            const priorityColor = rec.priority === 'High' ? 'red' : rec.priority === 'Medium' ? 'yellow' : 'blue';
            log(`\n${index + 1}. [${rec.priority}] ${rec.category}`, priorityColor);
            log(`   Issue: ${rec.issue}`, 'white');
            log(`   Suggestions:`, 'blue');
            rec.suggestions.forEach(suggestion => {
                log(`     • ${suggestion}`, 'cyan');
            });
        });
    }
    
    return recommendations;
}

function generatePerformanceReport(buildMetrics, bundleAnalysis, dependencyAnalysis, recommendations) {
    const report = {
        timestamp: new Date().toISOString(),
        version: SCRIPT_VERSION,
        summary: {
            buildTime: {
                total: buildMetrics.reduce((sum, m) => sum + m.duration, 0),
                average: buildMetrics.reduce((sum, m) => sum + m.duration, 0) / buildMetrics.length,
                withinThresholds: buildMetrics.every(m => m.withinThreshold)
            },
            bundleSize: {
                total: bundleAnalysis.reduce((sum, a) => sum + (a?.totalSize || 0), 0),
                withinThresholds: bundleAnalysis.every(a => a?.withinThreshold !== false)
            },
            dependencies: {
                total: dependencyAnalysis.totalDependencies,
                securityIssues: dependencyAnalysis.securityIssues,
                nodeModulesSize: dependencyAnalysis.nodeModulesSize
            },
            recommendations: recommendations.length
        },
        details: {
            buildMetrics,
            bundleAnalysis: bundleAnalysis.filter(a => a !== null),
            dependencyAnalysis,
            recommendations
        }
    };
    
    // Save report
    const reportPath = `logs/build/performance_report_${Date.now()}.json`;
    if (!fs.existsSync('logs/build')) {
        fs.mkdirSync('logs/build', { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    log(`📊 Performance report saved to: ${reportPath}`, 'blue');
    
    return report;
}

function main() {
    logHeader(`${SCRIPT_NAME} v${SCRIPT_VERSION}`);
    
    log('🚀 Starting build performance monitoring...', 'blue');
    
    // Measure build times
    const buildMetrics = [
        measureBuildTime('Frontend', 'cd frontend && npm run build:production'),
        measureBuildTime('Admin', 'cd admin-app && npm run build'),
        measureBuildTime('Backend', 'cd backend && pip install -e .'),
        measureBuildTime('Docker', 'docker-compose build --no-cache')
    ];
    
    // Analyze bundle sizes
    const bundleAnalysis = [
        analyzeBundleSize('frontend/dist', 'Frontend'),
        analyzeBundleSize('admin-app/dist', 'Admin')
    ];
    
    // Analyze dependencies
    const dependencyAnalysis = analyzeDependencies();
    
    // Generate recommendations
    const recommendations = generateOptimizationRecommendations(buildMetrics, bundleAnalysis, dependencyAnalysis);
    
    // Generate report
    const report = generatePerformanceReport(buildMetrics, bundleAnalysis, dependencyAnalysis, recommendations);
    
    // Summary
    logHeader('Performance Monitoring Summary');
    
    const allBuildsSuccessful = buildMetrics.every(m => m.success);
    const allWithinThresholds = buildMetrics.every(m => m.withinThreshold) && 
                               bundleAnalysis.every(a => a?.withinThreshold !== false);
    
    log(`📊 Build Success Rate: ${buildMetrics.filter(m => m.success).length}/${buildMetrics.length}`, 
        allBuildsSuccessful ? 'green' : 'red');
    log(`⏱️  Performance Thresholds: ${allWithinThresholds ? 'All met' : 'Some exceeded'}`, 
        allWithinThresholds ? 'green' : 'yellow');
    log(`🔍 Recommendations: ${recommendations.length}`, 
        recommendations.length === 0 ? 'green' : 'yellow');
    
    return allBuildsSuccessful && allWithinThresholds;
}

// CLI execution
if (require.main === module) {
    try {
        const success = main();
        process.exit(success ? 0 : 1);
    } catch (error) {
        log(`❌ Performance monitoring failed: ${error.message}`, 'red');
        process.exit(1);
    }
}

module.exports = { main, analyzeBundleSize, measureBuildTime, analyzeDependencies };

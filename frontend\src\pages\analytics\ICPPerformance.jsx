// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useRef, useCallback, useMemo, memo } from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Divider,
  Card,
  CardContent,
  Tabs,
  Tab,
  Alert,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  CompareArrows as CompareArrowsIcon,
  Refresh as RefreshIcon,
  Lightbulb as LightbulbIcon,
  Download as DownloadIcon,
  FilterList as FilterListIcon,
  Sync as SyncIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import * as d3 from 'd3';
import { useNotification } from '../../contexts/NotificationContext';
import ICPPerformanceCard from '../../components/analytics/ICPPerformanceCard';
import {
  getAllICPPerformances,
  getTopPerformingICPs,
  compareICPs,
  analyzeICPPerformance,
  getICPRecommendations,
  exportICPPerformanceCSV,
  exportICPComparisonCSV
} from '../../api/icp-performance';

const ICPPerformance = () => {
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const [loading, setLoading] = useState(true);
  const [analyzing, setAnalyzing] = useState(false);
  const [comparing, setComparing] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [icpPerformances, setIcpPerformances] = useState([]);
  const [topPerformingICPs, setTopPerformingICPs] = useState([]);
  const [selectedICPs, setSelectedICPs] = useState([]);
  const [comparisonData, setComparisonData] = useState(null);
  const [recommendations, setRecommendations] = useState(null);
  const [selectedIcpForRecommendations, setSelectedIcpForRecommendations] = useState('');
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval] = useState(30000); // 30 seconds
  const [exporting, setExporting] = useState(false);
  const [filterCriteria, setFilterCriteria] = useState({
    minEngagementRate: 0,
    maxEngagementRate: 100,
    sortBy: 'engagement_rate',
    sortOrder: 'desc'
  });
  const [lastUpdated, setLastUpdated] = useState(null);
  const [showFilters, setShowFilters] = useState(false);

  const barChartRef = useRef(null);

  // Memoized computed values
  const hasICPData = useMemo(() => icpPerformances.length > 0, [icpPerformances]);
  const hasTopPerformingData = useMemo(() => topPerformingICPs.length > 0, [topPerformingICPs]);
  const canCompareICPs = useMemo(() => selectedICPs.length >= 2, [selectedICPs]);
  const canGetRecommendations = useMemo(() => Boolean(selectedIcpForRecommendations), [selectedIcpForRecommendations]);

  // Filtered and sorted ICP performances
  const filteredICPPerformances = useMemo(() => {
    return icpPerformances
      .filter(icp => {
        const engagementRate = icp.avg_engagement_rate || 0;
        return engagementRate >= filterCriteria.minEngagementRate &&
               engagementRate <= filterCriteria.maxEngagementRate;
      })
      .sort((a, b) => {
        const aValue = a[filterCriteria.sortBy] || 0;
        const bValue = b[filterCriteria.sortBy] || 0;

        if (filterCriteria.sortOrder === 'asc') {
          return aValue - bValue;
        }
        return bValue - aValue;
      });
  }, [icpPerformances, filterCriteria]);

  // Filter handlers
  const handleFilterChange = useCallback((newCriteria) => {
    setFilterCriteria(prev => ({ ...prev, ...newCriteria }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilterCriteria({
      minEngagementRate: 0,
      maxEngagementRate: 100,
      sortBy: 'engagement_rate',
      sortOrder: 'desc'
    });
  }, []);

  // Data validation utilities
  const validateICPPerformance = useCallback((performance) => {
    if (!performance || typeof performance !== 'object') return false;

    const requiredFields = ['icp_id', 'icp_name', 'avg_engagement_rate'];
    return requiredFields.every(field =>
      Object.prototype.hasOwnProperty.call(performance, field) &&
      performance[field] !== null &&
      performance[field] !== undefined
    );
  }, []);

  const validateComparisonData = useCallback((data) => {
    if (!data || typeof data !== 'object') return false;

    return (
      Object.prototype.hasOwnProperty.call(data, 'icps') &&
      Array.isArray(data.icps) &&
      data.icps.length > 0 &&
      Object.prototype.hasOwnProperty.call(data, 'best_performing_icp_name') &&
      typeof data.best_performing_icp_name === 'string'
    );
  }, []);

  const validateRecommendations = useCallback((recs) => {
    if (!recs || typeof recs !== 'object') return false;

    return (
      Object.prototype.hasOwnProperty.call(recs, 'icp_name') &&
      typeof recs.icp_name === 'string' &&
      Object.prototype.hasOwnProperty.call(recs, 'recommendations') &&
      Array.isArray(recs.recommendations)
    );
  }, []);

  // Data sanitization utilities
  const sanitizeICPPerformances = useCallback((performances) => {
    if (!Array.isArray(performances)) return [];

    return performances
      .filter(validateICPPerformance)
      .map(performance => ({
        ...performance,
        icp_name: String(performance.icp_name || '').trim(),
        avg_engagement_rate: Math.max(0, Number(performance.avg_engagement_rate) || 0),
        total_content: Math.max(0, Number(performance.total_content) || 0)
      }));
  }, [validateICPPerformance]);

  const sanitizeComparisonData = useCallback((data) => {
    if (!validateComparisonData(data)) return null;

    return {
      ...data,
      icps: data.icps
        .filter(icp => icp && typeof icp === 'object')
        .map(icp => ({
          ...icp,
          icp_name: String(icp.icp_name || '').trim(),
          avg_engagement_rate: Math.max(0, Number(icp.avg_engagement_rate) || 0)
        })),
      best_performing_icp_name: String(data.best_performing_icp_name || '').trim(),
      performance_gap: data.performance_gap || {},
      recommendations: Array.isArray(data.recommendations) ? data.recommendations : []
    };
  }, [validateComparisonData]);

  const sanitizeRecommendations = useCallback((recs) => {
    if (!validateRecommendations(recs)) return null;

    return {
      ...recs,
      icp_name: String(recs.icp_name || '').trim(),
      optimal_content_types: Array.isArray(recs.optimal_content_types) ? recs.optimal_content_types : [],
      optimal_platforms: Array.isArray(recs.optimal_platforms) ? recs.optimal_platforms : [],
      optimal_posting_times: recs.optimal_posting_times || {},
      recommendations: Array.isArray(recs.recommendations) ? recs.recommendations : []
    };
  }, [validateRecommendations]);

  // Retry utility function
  const retryWithBackoff = useCallback(async (fn, maxRetries = 3, baseDelay = 1000) => {
    let lastError;
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        if (attempt === maxRetries) {
          throw error;
        }

        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        setRetryCount(prev => prev + 1);
      }
    }
    throw lastError;
  }, []);

  // Enhanced error handling utility
  const handleError = useCallback((error, context, fallbackMessage) => {
    console.error(`Error in ${context}:`, error);
    setError({ context, error, timestamp: new Date().toISOString() });

    let userMessage = fallbackMessage;
    if (error?.response?.status === 429) {
      userMessage = 'Too many requests. Please wait a moment and try again.';
    } else if (error?.response?.status >= 500) {
      userMessage = 'Server error. Please try again later.';
    } else if (error?.response?.status === 404) {
      userMessage = 'Data not found. Please refresh and try again.';
    } else if (error?.response?.status === 403) {
      userMessage = 'Access denied. Please check your permissions.';
    } else if (error?.code === 'NETWORK_ERROR') {
      userMessage = 'Network error. Please check your connection.';
    }

    showErrorNotification(userMessage);
  }, [showErrorNotification]);

  // Load ICP performances with retry logic
  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      await retryWithBackoff(async () => {
        // Get all ICP performances
        const rawPerformances = await getAllICPPerformances();
        const sanitizedPerformances = sanitizeICPPerformances(rawPerformances);
        setIcpPerformances(sanitizedPerformances);

        // Get top performing ICPs
        const rawTopICPs = await getTopPerformingICPs(5);
        const sanitizedTopICPs = sanitizeICPPerformances(rawTopICPs);
        setTopPerformingICPs(sanitizedTopICPs);

        // Set default selected ICP for recommendations if available
        if (sanitizedPerformances.length > 0) {
          setSelectedIcpForRecommendations(sanitizedPerformances[0].icp_id);
        }
      });
    } catch (error) {
      handleError(error, 'fetchData', 'Failed to load ICP performance data');
    } finally {
      setLoading(false);
    }
  }, [retryWithBackoff, handleError, sanitizeICPPerformances]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Draw bar chart when comparison data changes
  useEffect(() => {
    if (comparisonData && barChartRef.current) {
      const cleanup = drawComparisonChart();
      return cleanup;
    }
  }, [comparisonData, drawComparisonChart]);

  // Add resize observer for responsive chart
  useEffect(() => {
    if (!barChartRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      if (comparisonData && barChartRef.current) {
        // Debounce resize to avoid too many redraws
        const timeoutId = setTimeout(() => {
          drawComparisonChart();
        }, 250);
        return () => clearTimeout(timeoutId);
      }
    });

    resizeObserver.observe(barChartRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [comparisonData, drawComparisonChart]);

  // Handle tab change
  const handleTabChange = useCallback((_, newValue) => {
    setTabValue(newValue);
  }, []);

  // Handle ICP selection for comparison
  const handleICPSelection = useCallback((event) => {
    setSelectedICPs(event.target.value);
  }, []);

  // Handle ICP selection for recommendations
  const handleIcpForRecommendationsChange = useCallback((event) => {
    setSelectedIcpForRecommendations(event.target.value);
  }, []);

  // Analyze ICP performance with enhanced error handling
  const handleAnalyzeICP = useCallback(async (icpId) => {
    if (!icpId) {
      showErrorNotification('Invalid ICP ID provided');
      return;
    }

    setAnalyzing(true);
    try {
      const performance = await retryWithBackoff(async () => {
        const result = await analyzeICPPerformance(icpId);
        if (!validateICPPerformance(result)) {
          throw new Error('Invalid performance data received');
        }
        return result;
      });

      // Update the performances list with the new data
      setIcpPerformances(prev => {
        const index = prev.findIndex(p => p.icp_id === icpId);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = performance;
          return updated;
        }
        return [...prev, performance];
      });

      showSuccessNotification('ICP performance analysis completed');
    } catch (error) {
      handleError(error, 'handleAnalyzeICP', 'Failed to analyze ICP performance');
    } finally {
      setAnalyzing(false);
    }
  }, [showSuccessNotification, showErrorNotification, retryWithBackoff, handleError, validateICPPerformance]);

  // Compare ICPs with enhanced validation and error handling
  const handleCompareICPs = useCallback(async () => {
    if (selectedICPs.length < 2) {
      showErrorNotification('Please select at least two ICPs to compare');
      return;
    }

    if (selectedICPs.length > 10) {
      showErrorNotification('Please select no more than 10 ICPs for comparison');
      return;
    }

    setComparing(true);
    try {
      const comparison = await retryWithBackoff(async () => {
        const result = await compareICPs(selectedICPs);
        const sanitizedResult = sanitizeComparisonData(result);
        if (!sanitizedResult) {
          throw new Error('Invalid comparison data received');
        }
        return sanitizedResult;
      });

      setComparisonData(comparison);
      showSuccessNotification('ICP comparison completed');
    } catch (error) {
      handleError(error, 'handleCompareICPs', 'Failed to compare ICPs');
    } finally {
      setComparing(false);
    }
  }, [selectedICPs, showErrorNotification, showSuccessNotification, retryWithBackoff, handleError, sanitizeComparisonData]);

  // Get recommendations for selected ICP with enhanced validation
  const handleGetRecommendations = useCallback(async () => {
    if (!selectedIcpForRecommendations) {
      showErrorNotification('Please select an ICP to get recommendations');
      return;
    }

    setLoading(true);
    try {
      const recs = await retryWithBackoff(async () => {
        const result = await getICPRecommendations(selectedIcpForRecommendations);
        const sanitizedResult = sanitizeRecommendations(result);
        if (!sanitizedResult) {
          throw new Error('Invalid recommendations data received');
        }
        return sanitizedResult;
      });

      setRecommendations(recs);
      showSuccessNotification('Recommendations generated successfully');
    } catch (error) {
      handleError(error, 'handleGetRecommendations', 'Failed to get recommendations');
    } finally {
      setLoading(false);
    }
  }, [selectedIcpForRecommendations, showErrorNotification, showSuccessNotification, retryWithBackoff, handleError, sanitizeRecommendations]);

  // Export functionality
  const handleExportPerformanceData = useCallback(async () => {
    setExporting(true);
    try {
      const blob = await exportICPPerformanceCSV();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `icp-performance-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      showSuccessNotification('Performance data exported successfully');
    } catch (error) {
      handleError(error, 'handleExportPerformanceData', 'Failed to export performance data');
    } finally {
      setExporting(false);
    }
  }, [showSuccessNotification, handleError]);

  const handleExportComparisonData = useCallback(async () => {
    if (selectedICPs.length < 2) {
      showErrorNotification('Please select at least two ICPs to export comparison data');
      return;
    }

    setExporting(true);
    try {
      const blob = await exportICPComparisonCSV(selectedICPs);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `icp-comparison-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      showSuccessNotification('Comparison data exported successfully');
    } catch (error) {
      handleError(error, 'handleExportComparisonData', 'Failed to export comparison data');
    } finally {
      setExporting(false);
    }
  }, [selectedICPs, showErrorNotification, showSuccessNotification, handleError]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const intervalId = setInterval(() => {
      fetchData();
    }, refreshInterval);

    return () => clearInterval(intervalId);
  }, [autoRefresh, refreshInterval, fetchData]);

  // Update last updated timestamp
  useEffect(() => {
    if (icpPerformances.length > 0 || topPerformingICPs.length > 0) {
      setLastUpdated(new Date());
    }
  }, [icpPerformances, topPerformingICPs]);

  // Enhanced D3 chart with responsiveness, tooltips, and animations
  const drawComparisonChart = useCallback(() => {
    if (!comparisonData || !barChartRef.current || !comparisonData.icps || comparisonData.icps.length === 0) {
      return;
    }

    // Clear previous chart
    d3.select(barChartRef.current).selectAll("*").remove();

    // Responsive dimensions
    const containerWidth = barChartRef.current.clientWidth;
    const margin = {
      top: 30,
      right: 30,
      bottom: Math.max(70, containerWidth * 0.15), // Responsive bottom margin
      left: 60
    };
    const width = Math.max(300, containerWidth - margin.left - margin.right);
    const height = Math.max(200, Math.min(500, containerWidth * 0.6) - margin.top - margin.bottom);

    // Create responsive SVG
    const svg = d3.select(barChartRef.current)
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .attr("viewBox", `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`)
      .attr("preserveAspectRatio", "xMidYMid meet")
      .style("width", "100%")
      .style("height", "auto");

    const chartGroup = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Extract and validate data
    const data = comparisonData.icps
      .filter(icp => icp && typeof icp.avg_engagement_rate === 'number')
      .map(icp => ({
        name: String(icp.icp_name || 'Unknown'),
        value: Math.max(0, Number(icp.avg_engagement_rate) || 0),
        isBest: icp.icp_name === comparisonData.best_performing_icp_name
      }));

    if (data.length === 0) {
      // Show "No data" message
      chartGroup.append("text")
        .attr("x", width / 2)
        .attr("y", height / 2)
        .attr("text-anchor", "middle")
        .style("font-size", "16px")
        .style("fill", "#666")
        .text("No valid data to display");
      return;
    }

    // Create tooltip
    const tooltip = d3.select("body").append("div")
      .attr("class", "d3-tooltip")
      .style("position", "absolute")
      .style("visibility", "hidden")
      .style("background", "rgba(0, 0, 0, 0.8)")
      .style("color", "white")
      .style("padding", "8px 12px")
      .style("border-radius", "4px")
      .style("font-size", "12px")
      .style("pointer-events", "none")
      .style("z-index", "1000");

    // Scales
    const x = d3.scaleBand()
      .range([0, width])
      .domain(data.map(d => d.name))
      .padding(0.2);

    const maxValue = Math.max(d3.max(data, d => d.value) * 1.2, 1); // Ensure minimum scale
    const y = d3.scaleLinear()
      .domain([0, maxValue])
      .range([height, 0]);

    // X axis with responsive text
    const xAxis = chartGroup.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(x));

    xAxis.selectAll("text")
      .style("text-anchor", "end")
      .attr("dx", "-.8em")
      .attr("dy", ".15em")
      .attr("transform", data.length > 5 ? "rotate(-45)" : "rotate(0)")
      .style("font-size", width < 400 ? "10px" : "12px");

    // Y axis with percentage format
    chartGroup.append("g")
      .call(d3.axisLeft(y).tickFormat(d => `${d.toFixed(1)}%`));

    // Add axis labels
    chartGroup.append("text")
      .attr("transform", "rotate(-90)")
      .attr("y", 0 - margin.left)
      .attr("x", 0 - (height / 2))
      .attr("dy", "1em")
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#666")
      .text("Engagement Rate (%)");

    chartGroup.append("text")
      .attr("transform", `translate(${width / 2}, ${height + margin.bottom - 10})`)
      .style("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#666")
      .text("ICPs");

    // Bars with animations and interactions
    const bars = chartGroup.selectAll(".bar")
      .data(data)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", d => x(d.name))
      .attr("width", x.bandwidth())
      .attr("y", height) // Start from bottom for animation
      .attr("height", 0) // Start with 0 height for animation
      .attr("fill", d => d.isBest ? "#4caf50" : "#2196f3")
      .style("cursor", "pointer")
      .attr("role", "img")
      .attr("aria-label", d => `${d.name}: ${d.value.toFixed(2)}% engagement rate`)
      .on("mouseover", function(_, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("opacity", 0.8);

        tooltip
          .style("visibility", "visible")
          .html(`
            <strong>${d.name}</strong><br/>
            Engagement Rate: ${d.value.toFixed(2)}%<br/>
            ${d.isBest ? '<em>Best Performing</em>' : ''}
          `);
      })
      .on("mousemove", function(event) {
        tooltip
          .style("top", (event.pageY - 10) + "px")
          .style("left", (event.pageX + 10) + "px");
      })
      .on("mouseout", function() {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("opacity", 1);

        tooltip.style("visibility", "hidden");
      });

    // Animate bars
    bars.transition()
      .duration(800)
      .ease(d3.easeBackOut)
      .attr("y", d => y(d.value))
      .attr("height", d => height - y(d.value));

    // Add value labels with animation
    const labels = chartGroup.selectAll(".label")
      .data(data)
      .enter()
      .append("text")
      .attr("class", "label")
      .attr("text-anchor", "middle")
      .attr("x", d => x(d.name) + x.bandwidth() / 2)
      .attr("y", height) // Start from bottom
      .style("font-size", width < 400 ? "10px" : "12px")
      .style("font-weight", "bold")
      .style("fill", "#333")
      .text(d => `${d.value.toFixed(1)}%`)
      .style("opacity", 0);

    // Animate labels
    labels.transition()
      .duration(800)
      .delay(400)
      .ease(d3.easeBackOut)
      .attr("y", d => y(d.value) - 5)
      .style("opacity", 1);

    // Cleanup function for tooltip
    return () => {
      tooltip.remove();
    };
  }, [comparisonData]);

  return (
    <Container
      maxWidth="lg"
      sx={{ mt: 4, mb: 4 }}
      role="main"
      aria-label="ICP Performance Analytics Dashboard"
    >
      <Typography
        variant="h4"
        gutterBottom
        component="h1"
        id="main-heading"
        tabIndex={-1}
      >
        ICP Performance Analytics
      </Typography>

      {/* Error Display and Retry */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          action={
            <Button
              color="inherit"
              size="small"
              onClick={fetchData}
              disabled={loading}
            >
              Retry
            </Button>
          }
        >
          <Typography variant="body2">
            {error.context && `Error in ${error.context}: `}
            Something went wrong. Please try again.
          </Typography>
          {retryCount > 0 && (
            <Typography variant="caption" display="block">
              Retry attempts: {retryCount}
            </Typography>
          )}
        </Alert>
      )}

      {/* Advanced Controls Toolbar */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchData}
              disabled={loading}
              aria-label="Refresh all data"
            >
              Refresh
            </Button>

            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExportPerformanceData}
              disabled={exporting || !hasICPData}
              aria-label="Export performance data to CSV"
            >
              {exporting ? 'Exporting...' : 'Export Data'}
            </Button>

            <Button
              variant="outlined"
              startIcon={<SyncIcon />}
              onClick={() => setAutoRefresh(!autoRefresh)}
              color={autoRefresh ? 'primary' : 'inherit'}
              aria-label={`${autoRefresh ? 'Disable' : 'Enable'} auto-refresh`}
            >
              Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
            </Button>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {lastUpdated && (
              <Typography variant="caption" color="text.secondary">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </Typography>
            )}
            <Chip
              icon={<ScheduleIcon />}
              label={`${icpPerformances.length} ICPs`}
              size="small"
              variant="outlined"
            />
          </Box>
        </Box>
      </Paper>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="ICP Performance Analytics Navigation"
          role="tablist"
        >
          <Tab
            label="Overview"
            id="tab-overview"
            aria-controls="tabpanel-overview"
            aria-selected={tabValue === 0}
          />
          <Tab
            label="Compare ICPs"
            id="tab-compare"
            aria-controls="tabpanel-compare"
            aria-selected={tabValue === 1}
          />
          <Tab
            label="Recommendations"
            id="tab-recommendations"
            aria-controls="tabpanel-recommendations"
            aria-selected={tabValue === 2}
          />
        </Tabs>
      </Box>

      {/* Overview Tab */}
      {tabValue === 0 && (
        <Box
          role="tabpanel"
          id="tabpanel-overview"
          aria-labelledby="tab-overview"
          aria-label="Overview of ICP Performance"
        >
          <Typography
            variant="h6"
            gutterBottom
            component="h2"
            id="top-performing-heading"
          >
            Top Performing ICPs
          </Typography>

          {loading ? (
            <Box
              sx={{ display: 'flex', justifyContent: 'center', my: 4 }}
              role="status"
              aria-live="polite"
              aria-label="Loading top performing ICPs"
            >
              <CircularProgress aria-label="Loading data" />
            </Box>
          ) : !hasTopPerformingData ? (
            <Alert
              severity="info"
              sx={{ mb: 3 }}
              role="status"
              aria-live="polite"
            >
              No ICP performance data available. Analyze your ICPs to see performance metrics.
            </Alert>
          ) : (
            <Grid
              container
              spacing={3}
              sx={{ mb: 4 }}
              role="list"
              aria-label="Top performing ICPs list"
            >
              {topPerformingICPs.map((icp, index) => (
                <Grid item xs={12} md={6} lg={4} key={icp.icp_id} role="listitem">
                  <Paper
                    sx={{ p: 2, display: 'flex', flexDirection: 'column' }}
                    role="article"
                    aria-labelledby={`icp-name-${icp.icp_id}`}
                    tabIndex={0}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography
                        variant="subtitle1"
                        component="h3"
                        id={`icp-name-${icp.icp_id}`}
                      >
                        {icp.icp_name}
                      </Typography>
                      <Chip
                        size="small"
                        label={`#${index + 1}`}
                        color={index === 0 ? "success" : index === 1 ? "primary" : "default"}
                        aria-label={`Rank ${index + 1}`}
                      />
                    </Box>
                    <Typography
                      variant="h5"
                      component="div"
                      color="primary"
                      aria-label={`Engagement rate: ${(icp.avg_engagement_rate || 0).toFixed(2)} percent`}
                    >
                      {(icp.avg_engagement_rate || 0).toFixed(2)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Engagement Rate
                    </Typography>
                    <Box sx={{ mt: 1 }}>
                      <Typography
                        variant="body2"
                        aria-label={`Total content pieces: ${icp.total_content || 0}`}
                      >
                        Content: {icp.total_content || 0} pieces
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography
              variant="h6"
              gutterBottom
              component="h2"
              id="all-icp-heading"
            >
              All ICP Performance ({filteredICPPerformances.length} of {icpPerformances.length})
            </Typography>

            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => setShowFilters(!showFilters)}
              size="small"
              aria-label="Toggle filters"
            >
              Filters
            </Button>
          </Box>

          {/* Filter Controls */}
          {showFilters && (
            <Paper sx={{ p: 2, mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Filter & Sort Options
              </Typography>

              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Sort By</InputLabel>
                    <Select
                      value={filterCriteria.sortBy}
                      onChange={(e) => handleFilterChange({ sortBy: e.target.value })}
                      label="Sort By"
                    >
                      <MenuItem value="avg_engagement_rate">Engagement Rate</MenuItem>
                      <MenuItem value="total_content">Content Count</MenuItem>
                      <MenuItem value="icp_name">Name</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Order</InputLabel>
                    <Select
                      value={filterCriteria.sortOrder}
                      onChange={(e) => handleFilterChange({ sortOrder: e.target.value })}
                      label="Order"
                    >
                      <MenuItem value="desc">High to Low</MenuItem>
                      <MenuItem value="asc">Low to High</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6} md={2}>
                  <Typography variant="caption" display="block">
                    Min Engagement %
                  </Typography>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={filterCriteria.minEngagementRate}
                    onChange={(e) => handleFilterChange({ minEngagementRate: Number(e.target.value) })}
                    style={{ width: '100%' }}
                    aria-label="Minimum engagement rate filter"
                  />
                  <Typography variant="caption">
                    {filterCriteria.minEngagementRate}%
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6} md={2}>
                  <Typography variant="caption" display="block">
                    Max Engagement %
                  </Typography>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={filterCriteria.maxEngagementRate}
                    onChange={(e) => handleFilterChange({ maxEngagementRate: Number(e.target.value) })}
                    style={{ width: '100%' }}
                    aria-label="Maximum engagement rate filter"
                  />
                  <Typography variant="caption">
                    {filterCriteria.maxEngagementRate}%
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={12} md={2}>
                  <Button
                    variant="text"
                    onClick={resetFilters}
                    size="small"
                    fullWidth
                    aria-label="Reset all filters"
                  >
                    Reset Filters
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          )}

          {loading ? (
            <Box
              sx={{ display: 'flex', justifyContent: 'center', my: 4 }}
              role="status"
              aria-live="polite"
              aria-label="Loading all ICP performance data"
            >
              <CircularProgress aria-label="Loading data" />
            </Box>
          ) : !hasICPData ? (
            <Alert
              severity="info"
              role="status"
              aria-live="polite"
            >
              No ICP performance data available. Analyze your ICPs to see performance metrics.
            </Alert>
          ) : (
            <Grid container spacing={3}>
              {filteredICPPerformances.map((performance) => (
                <Grid item xs={12} md={6} key={performance.icp_id}>
                  <ICPPerformanceCard performance={performance} showDetails={true} />
                  <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={() => handleAnalyzeICP(performance.icp_id)}
                      disabled={analyzing}
                      aria-label={`Refresh analysis for ${performance.icp_name}`}
                      aria-describedby={analyzing ? 'analyzing-status' : undefined}
                    >
                      {analyzing ? 'Analyzing...' : 'Refresh Analysis'}
                    </Button>
                    {analyzing && (
                      <span id="analyzing-status" className="sr-only">
                        Analysis in progress
                      </span>
                    )}
                  </Box>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      )}

      {/* Compare ICPs Tab */}
      {tabValue === 1 && (
        <Box
          role="tabpanel"
          id="tabpanel-compare"
          aria-labelledby="tab-compare"
          aria-label="Compare ICP Performance"
        >
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography
              variant="h6"
              gutterBottom
              component="h2"
              id="compare-heading"
            >
              Compare ICP Performance
            </Typography>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel id="icp-select-label">Select ICPs to Compare</InputLabel>
              <Select
                labelId="icp-select-label"
                multiple
                value={selectedICPs}
                onChange={handleICPSelection}
                aria-describedby="icp-select-help"
                aria-label="Select multiple ICPs to compare their performance"
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => {
                      const icp = icpPerformances.find(p => p.icp_id === value);
                      return (
                        <Chip
                          key={value}
                          label={icp ? icp.icp_name : value}
                          aria-label={`Selected ICP: ${icp ? icp.icp_name : value}`}
                        />
                      );
                    })}
                  </Box>
                )}
              >
                {icpPerformances.map((performance) => (
                  <MenuItem key={performance.icp_id} value={performance.icp_id}>
                    {performance.icp_name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Typography
              id="icp-select-help"
              variant="caption"
              color="text.secondary"
              sx={{ display: 'block', mb: 2 }}
            >
              Select at least 2 ICPs to compare their performance metrics
            </Typography>

            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<CompareArrowsIcon />}
                onClick={handleCompareICPs}
                disabled={comparing || !canCompareICPs}
                sx={{ flex: 1 }}
                aria-label={`Compare ${selectedICPs.length} selected ICPs`}
                aria-describedby={comparing ? 'comparing-status' : 'icp-select-help'}
              >
                {comparing ? 'Comparing...' : 'Compare Selected ICPs'}
              </Button>

              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={handleExportComparisonData}
                disabled={exporting || !canCompareICPs}
                aria-label="Export comparison data to CSV"
              >
                {exporting ? 'Exporting...' : 'Export'}
              </Button>
            </Box>
            {comparing && (
              <span id="comparing-status" className="sr-only">
                Comparison in progress
              </span>
            )}
          </Paper>

          {comparisonData && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Comparison Results
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Best Performing ICP: <strong>{comparisonData.best_performing_icp_name || 'Unknown'}</strong>
                </Typography>

                {Object.entries(comparisonData.performance_gap || {}).map(([metric, value]) => (
                  <Box key={metric} sx={{ mb: 1 }}>
                    <Typography variant="body2">
                      {metric.replace('avg_', '').replace('_', ' ')}: <strong>{(value || 0).toFixed(2)}%</strong> better than average
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={Math.min(value || 0, 100)}
                      color="success"
                      sx={{ height: 8, borderRadius: 5 }}
                    />
                  </Box>
                ))}
              </Box>

              <Typography variant="subtitle1" gutterBottom>
                Engagement Rate Comparison
              </Typography>

              <Box
                ref={barChartRef}
                sx={{ width: '100%', height: 400, mb: 3 }}
                role="img"
                aria-label="Bar chart comparing engagement rates across selected ICPs"
                aria-describedby="chart-description"
                tabIndex={0}
              />
              <Typography
                id="chart-description"
                variant="caption"
                color="text.secondary"
                sx={{ display: 'block', textAlign: 'center' }}
              >
                Interactive bar chart showing engagement rate comparison.
                Best performing ICP is highlighted in green.
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle1" gutterBottom>
                Recommendations
              </Typography>

              {(comparisonData.recommendations || []).map((recommendation, index) => (
                <Alert key={index} severity="info" sx={{ mb: 1 }}>
                  {recommendation || 'No recommendation available'}
                </Alert>
              ))}
            </Paper>
          )}
        </Box>
      )}

      {/* Recommendations Tab */}
      {tabValue === 2 && (
        <Box
          role="tabpanel"
          id="tabpanel-recommendations"
          aria-labelledby="tab-recommendations"
          aria-label="ICP Content Recommendations"
        >
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography
              variant="h6"
              gutterBottom
              component="h2"
              id="recommendations-heading"
            >
              Get Content Recommendations for ICP
            </Typography>

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel id="icp-recommendations-label">Select ICP</InputLabel>
              <Select
                labelId="icp-recommendations-label"
                value={selectedIcpForRecommendations}
                onChange={handleIcpForRecommendationsChange}
                aria-describedby="recommendations-help"
                aria-label="Select an ICP to get content recommendations"
              >
                {icpPerformances.map((performance) => (
                  <MenuItem key={performance.icp_id} value={performance.icp_id}>
                    {performance.icp_name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Typography
              id="recommendations-help"
              variant="caption"
              color="text.secondary"
              sx={{ display: 'block', mb: 2 }}
            >
              Select an ICP to get personalized content recommendations
            </Typography>

            <Button
              variant="contained"
              startIcon={<LightbulbIcon />}
              onClick={handleGetRecommendations}
              disabled={loading || !canGetRecommendations}
              fullWidth
              aria-label="Get content recommendations for selected ICP"
              aria-describedby={loading ? 'recommendations-loading' : 'recommendations-help'}
            >
              {loading ? 'Loading...' : 'Get Recommendations'}
            </Button>
            {loading && (
              <span id="recommendations-loading" className="sr-only">
                Generating recommendations
              </span>
            )}
          </Paper>

          {recommendations && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Content Recommendations for {recommendations.icp_name || 'Unknown ICP'}
              </Typography>

              <Grid container spacing={3}>
                {(recommendations.optimal_content_types || []).length > 0 && (
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Optimal Content Types
                        </Typography>

                        {(recommendations.optimal_content_types || []).map((type, index) => (
                          <Box key={index} sx={{ mb: 1 }}>
                            <Typography variant="body2">
                              {type.type || 'Unknown'}: <strong>{(type.engagement_rate || 0).toFixed(2)}%</strong> engagement
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={Math.min((type.engagement_rate || 0) * 10, 100)}
                              color="primary"
                              sx={{ height: 8, borderRadius: 5 }}
                            />
                          </Box>
                        ))}
                      </CardContent>
                    </Card>
                  </Grid>
                )}

                {(recommendations.optimal_platforms || []).length > 0 && (
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Optimal Platforms
                        </Typography>

                        {(recommendations.optimal_platforms || []).map((platform, index) => (
                          <Box key={index} sx={{ mb: 1 }}>
                            <Typography variant="body2">
                              {platform.platform || 'Unknown'}: <strong>{(platform.engagement_rate || 0).toFixed(2)}%</strong> engagement
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={Math.min((platform.engagement_rate || 0) * 10, 100)}
                              color="secondary"
                              sx={{ height: 8, borderRadius: 5 }}
                            />
                          </Box>
                        ))}
                      </CardContent>
                    </Card>
                  </Grid>
                )}

                {Object.keys(recommendations.optimal_posting_times || {}).length > 0 && (
                  <Grid item xs={12}>
                    <Card>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          Optimal Posting Times
                        </Typography>

                        <Grid container spacing={2}>
                          {Object.entries(recommendations.optimal_posting_times || {}).map(([platform, times]) => (
                            <Grid item xs={12} sm={6} md={4} key={platform}>
                              <Typography variant="body2" gutterBottom>
                                <strong>{platform || 'Unknown Platform'}</strong>
                              </Typography>
                              {(Array.isArray(times) ? times : []).map((time, index) => (
                                <Chip
                                  key={index}
                                  label={time || 'Unknown Time'}
                                  color="primary"
                                  variant="outlined"
                                  size="small"
                                  sx={{ mr: 1, mb: 1 }}
                                />
                              ))}
                            </Grid>
                          ))}
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom>
                        Detailed Recommendations
                      </Typography>

                      {(recommendations.recommendations || []).map((rec, index) => (
                        <Alert key={index} severity="info" sx={{ mb: 1 }}>
                          <Typography variant="subtitle2">
                            {rec.value || 'No recommendation available'}
                          </Typography>
                          <Typography variant="body2">
                            {rec.reason || 'No reason provided'}
                          </Typography>
                        </Alert>
                      ))}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Paper>
          )}
        </Box>
      )}
    </Container>
  );
};

// Component has no props to validate

// Memoize the component for performance optimization
const MemoizedICPPerformance = memo(ICPPerformance);
MemoizedICPPerformance.displayName = 'ICPPerformance';

export default MemoizedICPPerformance;

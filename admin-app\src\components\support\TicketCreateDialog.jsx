import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box,
  Typography,
  Chip,
  Autocomplete,
  Alert,
  CircularProgress,
  IconButton,
  Divider,
  Stack,
  Card,
  CardContent,
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  AttachFile as AttachFileIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import api from '../../api';

const TicketCreateDialog = ({ 
  open, 
  onClose, 
  onTicketCreated, 
  agents = [],
  customers = [] 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    subject: '',
    description: '',
    priority: 'medium',
    category: 'general',
    channel: 'web',
    customer_email: '',
    customer_name: '',
    customer_phone: '',
    customer_company: '',
    assigned_agent_id: '',
    tags: [],
    custom_fields: {},
    attachments: [],
    due_date: null,
    internal_notes: '',
  });

  const [availableCustomers, setAvailableCustomers] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [newTag, setNewTag] = useState('');

  useEffect(() => {
    if (open) {
      loadCustomers();
      resetForm();
    }
  }, [open]);

  const loadCustomers = async () => {
    try {
      const response = await api.get('/api/admin/users?limit=100');
      setAvailableCustomers(Array.isArray(response.data.users) ? response.data.users : []);
    } catch (error) {
      console.error('Error loading customers:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      subject: '',
      description: '',
      priority: 'medium',
      category: 'general',
      channel: 'web',
      customer_email: '',
      customer_name: '',
      customer_phone: '',
      customer_company: '',
      assigned_agent_id: '',
      tags: [],
      custom_fields: {},
      attachments: [],
      due_date: null,
      internal_notes: '',
    });
    setSelectedCustomer(null);
    setError(null);
  };

  const handleCustomerSelect = (customer) => {
    if (customer) {
      setSelectedCustomer(customer);
      setFormData(prev => ({
        ...prev,
        customer_email: customer.email || '',
        customer_name: customer.full_name || '',
        customer_phone: customer.phone || '',
        customer_company: customer.company_name || '',
      }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validation
      if (!formData.subject.trim()) {
        setError('Subject is required');
        return;
      }
      if (!formData.description.trim()) {
        setError('Description is required');
        return;
      }
      if (!formData.customer_email.trim()) {
        setError('Customer email is required');
        return;
      }

      const ticketData = {
        ...formData,
        subject: formData.subject.trim(),
        description: formData.description.trim(),
        customer_email: formData.customer_email.trim(),
        customer_name: formData.customer_name.trim(),
        correlation_id: `create-ticket-${Date.now()}`
      };

      const response = await api.post('/api/admin/support/tickets', ticketData);
      
      onTicketCreated?.(response.data);
      onClose();
      resetForm();

    } catch (error) {
      console.error('Error creating ticket:', error);
      setError(error.response?.data?.detail || 'Failed to create ticket');
    } finally {
      setLoading(false);
    }
  };

  const priorityOptions = [
    { value: 'low', label: 'Low', color: 'default' },
    { value: 'medium', label: 'Medium', color: 'info' },
    { value: 'high', label: 'High', color: 'warning' },
    { value: 'critical', label: 'Critical', color: 'error' },
  ];

  const categoryOptions = [
    { value: 'general', label: 'General Inquiry' },
    { value: 'technical', label: 'Technical Support' },
    { value: 'billing', label: 'Billing & Payments' },
    { value: 'account', label: 'Account Management' },
    { value: 'feature_request', label: 'Feature Request' },
    { value: 'bug_report', label: 'Bug Report' },
    { value: 'security', label: 'Security Issue' },
    { value: 'integration', label: 'Integration Support' },
  ];

  const channelOptions = [
    { value: 'web', label: 'Web Portal' },
    { value: 'email', label: 'Email' },
    { value: 'chat', label: 'Live Chat' },
    { value: 'phone', label: 'Phone' },
    { value: 'api', label: 'API' },
    { value: 'social', label: 'Social Media' },
  ];

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '80vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Create New Support Ticket</Typography>
          <IconButton onClick={onClose} disabled={loading}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Customer Information */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Customer Information
            </Typography>
            <Card variant="outlined">
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Autocomplete
                      options={availableCustomers}
                      getOptionLabel={(option) => `${option.full_name || 'Unknown'} (${option.email})`}
                      value={selectedCustomer}
                      onChange={(_, newValue) => handleCustomerSelect(newValue)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Select Existing Customer"
                          placeholder="Search by name or email..."
                          fullWidth
                        />
                      )}
                      renderOption={(props, option) => (
                        <Box component="li" {...props}>
                          <Box display="flex" alignItems="center" gap={1} width="100%">
                            <PersonIcon color="action" />
                            <Box>
                              <Typography variant="body2">
                                {option.full_name || 'Unknown Name'}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.email}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      )}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Customer Name"
                      value={formData.customer_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                      required
                      InputProps={{
                        startAdornment: <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Customer Email"
                      type="email"
                      value={formData.customer_email}
                      onChange={(e) => setFormData(prev => ({ ...prev, customer_email: e.target.value }))}
                      required
                      InputProps={{
                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      value={formData.customer_phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, customer_phone: e.target.value }))}
                      InputProps={{
                        startAdornment: <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Company"
                      value={formData.customer_company}
                      onChange={(e) => setFormData(prev => ({ ...prev, customer_company: e.target.value }))}
                      InputProps={{
                        startAdornment: <BusinessIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Ticket Details */}
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Ticket Details
            </Typography>
            <Card variant="outlined">
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Subject"
                      value={formData.subject}
                      onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                      required
                      placeholder="Brief description of the issue..."
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Description"
                      multiline
                      rows={4}
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      required
                      placeholder="Detailed description of the issue, steps to reproduce, expected behavior..."
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Priority</InputLabel>
                      <Select
                        value={formData.priority}
                        label="Priority"
                        onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                      >
                        {priorityOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            <Chip 
                              label={option.label} 
                              color={option.color} 
                              size="small" 
                            />
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Category</InputLabel>
                      <Select
                        value={formData.category}
                        label="Category"
                        onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                      >
                        {categoryOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <FormControl fullWidth>
                      <InputLabel>Channel</InputLabel>
                      <Select
                        value={formData.channel}
                        label="Channel"
                        onChange={(e) => setFormData(prev => ({ ...prev, channel: e.target.value }))}
                      >
                        {channelOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Autocomplete
                      options={agents}
                      getOptionLabel={(option) => option.name || ''}
                      value={agents.find(agent => agent.id === formData.assigned_agent_id) || null}
                      onChange={(_, newValue) => setFormData(prev => ({ ...prev, assigned_agent_id: newValue?.id || '' }))}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Assign to Agent (Optional)"
                          placeholder="Select an agent..."
                        />
                      )}
                    />
                  </Grid>
                  
                  {/* Tags */}
                  <Grid item xs={12}>
                    <Box>
                      <Typography variant="subtitle2" gutterBottom>
                        Tags
                      </Typography>
                      <Box display="flex" gap={1} flexWrap="wrap" mb={1}>
                        {formData.tags.map((tag, index) => (
                          <Chip
                            key={index}
                            label={tag}
                            onDelete={() => handleRemoveTag(tag)}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                      <Box display="flex" gap={1}>
                        <TextField
                          size="small"
                          label="Add Tag"
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              handleAddTag();
                            }
                          }}
                        />
                        <Button
                          variant="outlined"
                          onClick={handleAddTag}
                          disabled={!newTag.trim()}
                          startIcon={<AddIcon />}
                        >
                          Add
                        </Button>
                      </Box>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Internal Notes (Optional)"
                      multiline
                      rows={2}
                      value={formData.internal_notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, internal_notes: e.target.value }))}
                      placeholder="Internal notes for agents (not visible to customer)..."
                      helperText="These notes will only be visible to support agents"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onClose}
          disabled={loading}
          startIcon={<CancelIcon />}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={16} /> : <SaveIcon />}
        >
          {loading ? 'Creating...' : 'Create Ticket'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TicketCreateDialog;

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider,
  CircularProgress,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Tab,
  Tabs,
  Alert,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CampaignIcon from "@mui/icons-material/Campaign";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import PauseIcon from "@mui/icons-material/Pause";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import CompareArrowsIcon from "@mui/icons-material/CompareArrows";
import BarChartIcon from "@mui/icons-material/BarChart";
import { useNotification } from "../../hooks/useNotification";
import { useAuth } from "../../contexts/AuthContext";
import useCircuitBreaker from "../../hooks/useCircuitBreaker";
import CampaignGracefulDegradation from "../../components/campaigns/CampaignGracefulDegradation";
import CampaignErrorBoundary from "../../components/campaigns/CampaignErrorBoundary";
import StablePageWrapper from "../../components/common/StablePageWrapper";
import api from "../../api";

const CampaignList = () => {
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { isAuthenticated, loading: authLoading } = useAuth();

  const [loading, setLoading] = useState(true);
  const [campaigns, setCampaigns] = useState([]);
  const [tabValue, setTabValue] = useState("all");
  const [error, setError] = useState(null);

  const [isRetrying, setIsRetrying] = useState(false);
  const [lastAttemptTime, setLastAttemptTime] = useState(null);

  // Initialize circuit breaker for campaigns API
  const circuitBreaker = useCircuitBreaker('campaigns-api', {
    failureThreshold: 3,        // Open circuit after 3 failures
    recoveryTimeout: 30000,     // Try again after 30 seconds
    monitoringPeriod: 60000,    // Reset failure count after 1 minute
  });

  // Check if localhost bypass is enabled
  const isLocalhostBypass = import.meta.env.VITE_LOCALHOST_BYPASS_AUTH === 'true' &&
                           (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

  // Debug logging for localhost bypass
  console.log('Localhost bypass debug:', {
    VITE_LOCALHOST_BYPASS_AUTH: import.meta.env.VITE_LOCALHOST_BYPASS_AUTH,
    hostname: window.location.hostname,
    isLocalhostBypass: isLocalhostBypass
  });

  // Check authentication and redirect if needed (skip for localhost bypass)
  useEffect(() => {
    if (!isLocalhostBypass && !authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate, isLocalhostBypass]);

  // Fetch campaigns function (extracted for reuse)
  const fetchCampaigns = useCallback(async () => {
      setLoading(true);
      setError(null);
      setLastAttemptTime(new Date().toISOString());

      try {
        // Use circuit breaker to execute the API call
        const response = await circuitBreaker.execute(async () => {
          return await api.get("/api/campaigns");
        });

        if (response.data && Array.isArray(response.data)) {
          setCampaigns(response.data);
        } else {
          setCampaigns([]);
        }
      } catch (error) {
        // Log error for debugging in development
        if (process.env.NODE_ENV === 'development') {
          console.error("Error fetching campaigns:", error);
          console.error("Error details:", {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message,
            code: error.code,
            circuitBreakerState: circuitBreaker.state
          });
        }

        // Check if circuit breaker is open (backend unavailable)
        const isCircuitOpen = error.message?.includes('Circuit breaker is OPEN');
        const isNetworkError =
          error.code === "ECONNREFUSED" ||
          error.message === "Network Error" ||
          error.response?.status >= 500 ||
          error.response?.status === 503;

        // If circuit is open or network error, don't set error state - let graceful degradation handle it
        if (isCircuitOpen || isNetworkError) {
          setCampaigns([]);

          setError(null); // Don't set error to trigger graceful degradation
          setLoading(false);
          return;
        }

        // Handle authentication errors
        if (error.response?.status === 401 || error.response?.status === 403) {
          navigate('/login');
          return;
        }

        // Provide more specific error messages
        let errorMessage = "Unable to load campaigns at this time.";
        if (error.response?.status === 401) {
          errorMessage = "Authentication required. Please log in.";
        } else if (error.response?.status === 403) {
          errorMessage = "You don&apos;t have permission to view campaigns.";
        } else if (error.response?.status === 404) {
          errorMessage = "Campaigns service is currently unavailable.";
        } else if (error.response?.status >= 500) {
          errorMessage = "Service temporarily unavailable. Please try again in a moment.";
        } else if (error.code === "ECONNREFUSED" || error.message === "Network Error") {
          errorMessage = "Connection error. Please check your internet connection and try again.";
        }

        setError(errorMessage);
        setCampaigns([]);
      } finally {
        setLoading(false);
      }
    }, [circuitBreaker, navigate]);

  // Fetch campaigns effect
  useEffect(() => {
    // For localhost bypass, always fetch campaigns
    // For normal auth, don't fetch if still loading auth or not authenticated
    if (!isLocalhostBypass && (authLoading || !isAuthenticated)) {
      return;
    }

    fetchCampaigns();
  }, [authLoading, isAuthenticated, navigate, isLocalhostBypass, fetchCampaigns]);

  // Retry function for graceful degradation
  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      // Reset circuit breaker to allow retry
      circuitBreaker.reset();

      // Fetch campaigns again
      await fetchCampaigns();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Filter campaigns based on tab
  const filteredCampaigns = campaigns.filter((campaign) => {
    if (!campaign || typeof campaign !== 'object') return false;
    if (tabValue === "all") return true;
    return campaign.status === tabValue;
  });

  // Handle viewing a campaign
  const handleViewCampaign = (campaignId) => {
    navigate(`/campaigns/${campaignId}`);
  };

  // Handle editing a campaign
  const handleEditCampaign = (campaignId) => {
    navigate(`/campaigns/${campaignId}/edit`);
  };

  // Handle deleting a campaign
  const handleDeleteCampaign = async (campaignId) => {
    if (window.confirm("Are you sure you want to delete this campaign?")) {
      try {
        await api.delete(`/api/campaigns/${campaignId}`);
        setCampaigns(
          campaigns.filter((campaign) => campaign.id !== campaignId)
        );
        showSuccessNotification("Campaign deleted successfully");
      } catch (error) {
        console.error("Error deleting campaign:", error);

        // If backend is unavailable, still update the UI
        if (error.code === "ERR_NETWORK" || error.message === "Network Error") {
          setCampaigns(
            campaigns.filter((campaign) => campaign.id !== campaignId)
          );
          showSuccessNotification("Campaign deleted successfully");
        } else {
          showErrorNotification("Failed to delete campaign");
        }
      }
    }
  };

  // Handle changing campaign status
  const handleToggleCampaignStatus = async (campaign) => {
    const newStatus = campaign.status === "active" ? "paused" : "active";

    try {
      await api.put(`/api/campaigns/${campaign.id}`, { status: newStatus });

      // Update local state
      setCampaigns(
        campaigns.map((c) =>
          c.id === campaign.id ? { ...c, status: newStatus } : c
        )
      );

      showSuccessNotification(
        `Campaign ${
          newStatus === "active" ? "activated" : "paused"
        } successfully`
      );
    } catch (error) {
      console.error("Error updating campaign status:", error);

      // If backend is unavailable, still update the UI
      if (error.code === "ERR_NETWORK" || error.message === "Network Error") {
        // Update local state anyway
        setCampaigns(
          campaigns.map((c) =>
            c.id === campaign.id ? { ...c, status: newStatus } : c
          )
        );

        showSuccessNotification(
          `Campaign ${
            newStatus === "active" ? "activated" : "paused"
          } successfully`
        );
      } else {
        showErrorNotification("Failed to update campaign status");
      }
    }
  };

  // Calculate campaign progress
  const calculateProgress = (campaign) => {
    if (!campaign.start_date || !campaign.end_date) return 0;

    const start = new Date(campaign.start_date).getTime();
    const end = new Date(campaign.end_date).getTime();
    const now = new Date().getTime();

    if (now <= start) return 0;
    if (now >= end) return 100;

    return Math.round(((now - start) / (end - start)) * 100);
  };

  // Render loading state
  if ((loading && campaigns.length === 0) || (!isLocalhostBypass && authLoading)) {
    return (
      <Box sx={{ py: 3, display: "flex", justifyContent: "center" }}>
        <CircularProgress />
      </Box>
    );
  }

  // Don't render anything if not authenticated (will redirect) - skip for localhost bypass
  if (!isLocalhostBypass && !isAuthenticated) {
    return null;
  }

  // Check if we should show graceful degradation
  // Show graceful degradation when:
  // 1. Circuit breaker is open (backend unavailable)
  // 2. No campaigns and no error (network issues handled gracefully)
  const shouldShowGracefulDegradation =
    !circuitBreaker.isBackendAvailable ||
    (!loading && campaigns.length === 0 && !error);

  if (shouldShowGracefulDegradation) {
    return (
      <CampaignGracefulDegradation
        onRetry={handleRetry}
        isRetrying={isRetrying}
        lastAttempt={lastAttemptTime}
        circuitBreakerState={circuitBreaker.state}
      />
    );
  }

  // Render error state (only for non-network errors)
  if (error && campaigns.length === 0) {
    return (
      <Box sx={{ py: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Box sx={{ textAlign: "center" }}>
          <Button
            variant="contained"
            onClick={handleRetry}
            disabled={isRetrying}
          >
            {isRetrying ? 'Retrying...' : 'Try Again'}
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <StablePageWrapper enableGlassMorphism={true}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h4">
          <CampaignIcon sx={{ mr: 1, verticalAlign: "middle" }} />
          Campaigns
        </Typography>

        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => navigate("/services")}
        >
          Select Service for New Campaign
        </Button>
      </Box>



      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        indicatorColor="primary"
        textColor="primary"
        sx={{ mb: 3 }}
      >
        <Tab value="all" label="All Campaigns" />
        <Tab value="draft" label="Drafts" />
        <Tab value="active" label="Active" />
        <Tab value="paused" label="Paused" />
        <Tab value="completed" label="Completed" />
      </Tabs>

      {filteredCampaigns.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: "center", py: 5 }}>
            <CampaignIcon
              sx={{ fontSize: 60, color: "primary.light", mb: 2 }}
            />
            <Typography variant="h6" gutterBottom>
              No Campaigns Found
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
              {tabValue === "all"
                ? "Create your first campaign to start generating targeted content."
                : `You don&apos;t have any ${tabValue} campaigns.`}
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
              Campaigns are created for specific services. First select or
              create a service, then create a campaign for it.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => navigate("/services")}
            >
              Go to Services
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {filteredCampaigns.map((campaign) => {
            const progress = calculateProgress(campaign);

            return (
              <Grid item xs={12} md={6} key={campaign.id}>
                <Card>
                  <CardContent>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                        mb: 1,
                      }}
                    >
                      <Typography variant="h6" gutterBottom>
                        {campaign.name}
                      </Typography>
                      <Chip
                        label={
                          campaign.status
                            ? campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)
                            : "Unknown"
                        }
                        color={
                          campaign.status === "active"
                            ? "success"
                            : campaign.status === "paused"
                            ? "warning"
                            : campaign.status === "completed"
                            ? "info"
                            : "default"
                        }
                        size="small"
                      />
                    </Box>

                    <Typography variant="body2" color="textSecondary" paragraph>
                      {campaign.description}
                    </Typography>

                    <Divider sx={{ my: 2 }} />

                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Paper sx={{ p: 1.5 }}>
                          <Typography variant="subtitle2" color="textSecondary">
                            Date Range
                          </Typography>
                          <Typography variant="body2">
                            {campaign.start_date
                              ? new Date(campaign.start_date).toLocaleDateString()
                              : "Not set"}{" "}
                            -{" "}
                            {campaign.end_date
                              ? new Date(campaign.end_date).toLocaleDateString()
                              : "Ongoing"}
                          </Typography>
                        </Paper>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Paper sx={{ p: 1.5 }}>
                          <Typography variant="subtitle2" color="textSecondary">
                            Platforms
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              flexWrap: "wrap",
                              gap: 0.5,
                              mt: 0.5,
                            }}
                          >
                            {(campaign.platforms || []).map((platform, index) => (
                              <Chip
                                key={index}
                                label={
                                  platform && typeof platform === 'string'
                                    ? platform.charAt(0).toUpperCase() + platform.slice(1)
                                    : "Unknown"
                                }
                                size="small"
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </Paper>
                      </Grid>
                    </Grid>

                    <Box sx={{ mt: 2 }}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                          mb: 0.5,
                        }}
                      >
                        <Typography variant="body2" color="textSecondary">
                          Campaign Progress
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {progress}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={progress}
                        color={
                          campaign.status === "active"
                            ? "success"
                            : campaign.status === "paused"
                            ? "warning"
                            : "primary"
                        }
                      />
                    </Box>

                    <Box sx={{ mt: 2 }}>
                      <Typography
                        variant="subtitle2"
                        color="textSecondary"
                        gutterBottom
                      >
                        Content Stats:
                      </Typography>
                      <Grid container spacing={1}>
                        <Grid item xs={4}>
                          <Paper sx={{ p: 1, textAlign: "center" }}>
                            <Typography variant="h6">
                              {campaign.performance?.total_posts || 0}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              Total Posts
                            </Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={4}>
                          <Paper sx={{ p: 1, textAlign: "center" }}>
                            <Typography variant="h6">
                              {campaign.performance?.published_posts || 0}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              Published
                            </Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={4}>
                          <Paper sx={{ p: 1, textAlign: "center" }}>
                            <Typography variant="h6">
                              {campaign.performance?.scheduled_posts || 0}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              Scheduled
                            </Typography>
                          </Paper>
                        </Grid>
                      </Grid>
                    </Box>
                  </CardContent>

                  <Divider />

                  <CardActions sx={{ justifyContent: "space-between", p: 2 }}>
                    <Box>
                      <Tooltip title="View Campaign">
                        <IconButton
                          color="primary"
                          onClick={() => handleViewCampaign(campaign.id)}
                        >
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>

                      <Tooltip title="Edit Campaign">
                        <IconButton
                          color="primary"
                          onClick={() => handleEditCampaign(campaign.id)}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>

                      <Tooltip title="Delete Campaign">
                        <IconButton
                          color="error"
                          onClick={() => handleDeleteCampaign(campaign.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>

                    {campaign.status !== "completed" &&
                      campaign.status !== "draft" && (
                        <Tooltip
                          title={
                            campaign.status === "active"
                              ? "Pause Campaign"
                              : "Activate Campaign"
                          }
                        >
                          <Button
                            variant="outlined"
                            color={
                              campaign.status === "active"
                                ? "warning"
                                : "success"
                            }
                            onClick={() => handleToggleCampaignStatus(campaign)}
                            startIcon={
                              campaign.status === "active" ? (
                                <PauseIcon />
                              ) : (
                                <PlayArrowIcon />
                              )
                            }
                            sx={{ mr: 1 }}
                          >
                            {campaign.status === "active"
                              ? "Pause"
                              : "Activate"}
                          </Button>
                        </Tooltip>
                      )}

                    {campaign.status === "active" && !campaign.is_ab_test && (
                      <Tooltip title="Create A/B Test">
                        <Button
                          variant="outlined"
                          color="info"
                          onClick={() =>
                            navigate(`/campaigns/${campaign.id}/create-ab-test`)
                          }
                          startIcon={<CompareArrowsIcon />}
                        >
                          A/B Test
                        </Button>
                      </Tooltip>
                    )}

                    {campaign.is_ab_test && (
                      <Tooltip title="View A/B Test Results">
                        <Button
                          variant="outlined"
                          color="info"
                          onClick={() =>
                            navigate(
                              `/analytics/campaign-comparison/${campaign.id}`
                            )
                          }
                          startIcon={<BarChartIcon />}
                        >
                          View Results
                        </Button>
                      </Tooltip>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      )}
    </StablePageWrapper>
  );
};

// Wrap with error boundary for graceful degradation
const CampaignListWithErrorBoundary = () => (
  <CampaignErrorBoundary>
    <CampaignList />
  </CampaignErrorBoundary>
);

export default CampaignListWithErrorBoundary;

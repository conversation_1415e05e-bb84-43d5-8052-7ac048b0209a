// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Divider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab
} from '@mui/material';
import {
  Comment as CommentIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Settings as SettingsIcon,
  Autorenew as AutorenewIcon,
  SentimentSatisfied as SentimentSatisfiedIcon,
  SentimentDissatisfied as SentimentDissatisfiedIcon,
  SentimentNeutral as SentimentNeutralIcon,
  Description as DocumentIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import { useSnackbar } from '../../contexts/SnackbarContext';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../api';
import GlassmorphicCard from '../../components/common/GlassmorphicCard';
import LoadingButton from '@mui/lab/LoadingButton';
import FilePreview from '../../components/common/FilePreview';
import EmptyState from '../../components/common/EmptyState';

// TabPanel component for internal tabs
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-response-tabpanel-${index}`}
      aria-labelledby={`ai-response-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const DOCUMENT_TYPES = [
  { value: 'brand_guidelines', label: 'Brand Guidelines' },
  { value: 'tone_of_voice', label: 'Tone of Voice' },
  { value: 'policy', label: 'Company Policy' },
  { value: 'terms_of_service', label: 'Terms of Service' },
  { value: 'privacy_policy', label: 'Privacy Policy' }
];

/**
 * AIResponseSettings component combines the functionality of AutoCommentReplies and BrandGuidelinesManager
 * into a single cohesive interface for managing AI-powered responses and policy compliance.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isEmbedded - Whether the component is embedded in another page
 */
const AIResponseSettings = ({ isEmbedded = false }) => {
  const { showSuccess, showError } = useAdvancedToast();
  const { enqueueSnackbar } = useSnackbar();
  const { user, isAuthenticated } = useAuth();

  // State for internal tabs
  const [tabValue, setTabValue] = useState(0);



  // State for auto-reply settings
  const [autoReplySettings, setAutoReplySettings] = useState({
    enabled: true,
    requireApproval: true,
    maxRepliesPerDay: 50,
    replyDelay: 5, // minutes
    followBrandGuidelines: true,
    useSentimentAnalysis: true,
    usePolicyCompliance: true, // Whether to use policy documents for compliance
    responseTone: 'professional',
    includeBranding: true,
    maxResponseLength: 280
  });

  // State for reply templates
  const [replyTemplates, setReplyTemplates] = useState([
    {
      id: '1',
      name: 'Positive Feedback',
      sentiment: 'positive',
      template: 'Thank you for your positive feedback! We appreciate your support and are glad you enjoyed our content.',
      active: true,
    },
    {
      id: '2',
      name: 'Neutral Response',
      sentiment: 'neutral',
      template: 'Thanks for your comment. If you have any questions, feel free to reach out to us directly.',
      active: true,
    },
    {
      id: '3',
      name: 'Negative Feedback',
      sentiment: 'negative',
      template: 'We appreciate your feedback and would like to address your concerns. Please DM us with more details so we can help resolve this issue.',
      active: true,
    },
  ]);

  // State for template dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState(null);

  // State for documents
  const [documents, setDocuments] = useState([]);
  const [documentsLoading, setDocumentsLoading] = useState(true);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [expandedDocId, setExpandedDocId] = useState(null);
  const [saveLoading, setSaveLoading] = useState(false);

  // State for upload form
  const [uploadForm, setUploadForm] = useState({
    name: '',
    documentType: 'brand_guidelines',
    description: '',
    file: null,
    extractText: true
  });

  // Fetch data on component mount (only if authenticated)
  useEffect(() => {
    if (isAuthenticated) {
      fetchDocuments();
      loadUserSettings();
    }
  }, [fetchDocuments, loadUserSettings, isAuthenticated]);

  // Load user-specific settings
  const loadUserSettings = useCallback(async () => {
    if (!user?.id) return;

    try {
      const response = await api.get(`/api/users/${user.id}/ai-settings`);
      if (response.data) {
        setAutoReplySettings(prev => ({ ...prev, ...response.data.autoReply }));
        setReplyTemplates(response.data.templates || []);
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
      enqueueSnackbar('Failed to load your AI settings', { variant: 'warning' });
    }
  }, [user?.id, enqueueSnackbar]);

  // Fetch documents from API
  const fetchDocuments = useCallback(async () => {
    try {
      setDocumentsLoading(true);
      const response = await api.get('/api/brand-guidelines/documents');
      setDocuments(response.data);
    } catch (error) {
      console.error('Error fetching documents:', error);
      showError('Failed to load brand guidelines documents');
    } finally {
      setDocumentsLoading(false);
    }
  }, [showError]);



  // Handle file drop
  const onDrop = useCallback((acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      setUploadForm({
        ...uploadForm,
        file: acceptedFiles[0]
      });
    }
  }, [uploadForm]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'text/markdown': ['.md']
    },
    maxFiles: 1
  });

  // Handle form input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUploadForm({
      ...uploadForm,
      [name]: value
    });
  };

  // Handle switch change
  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    setUploadForm({
      ...uploadForm,
      [name]: checked
    });
  };

  // Handle settings change with immediate API persistence
  const handleSettingChange = useCallback(async (setting, value) => {
    if (!isAuthenticated || !user?.id) {
      showError('Authentication required to update settings');
      return;
    }

    // Optimistically update the UI
    const previousSettings = autoReplySettings;
    setAutoReplySettings({
      ...autoReplySettings,
      [setting]: value,
    });

    try {
      // Persist the change to the backend immediately
      const updatedSettings = {
        ...autoReplySettings,
        [setting]: value,
      };

      await api.put(`/api/users/${user.id}/ai-settings/auto-reply`, {
        autoReply: updatedSettings,
        userId: user.id,
        lastUpdated: new Date().toISOString()
      });

      showSuccess(`Auto-reply setting "${setting}" updated successfully`);

      // Track settings change for analytics
      api.post('/api/analytics/settings-change', {
        userId: user.id,
        settingType: 'auto-reply',
        setting,
        value,
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.error('Analytics tracking failed:', error);
      });

    } catch (error) {
      console.error('Error updating setting:', error);

      // Revert the optimistic update on error
      setAutoReplySettings(previousSettings);

      showError(`Failed to update "${setting}" setting. Please try again.`);

      // Provide more specific error feedback
      if (error.response?.status === 401) {
        showError('Session expired. Please log in again.');
      } else if (error.response?.status === 403) {
        showError('You do not have permission to update these settings.');
      }
    }
  }, [isAuthenticated, user?.id, autoReplySettings, showSuccess, showError]);

  // Upload document with enhanced validation and error handling
  const uploadDocument = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      showError('Authentication required to upload documents');
      return;
    }

    // Comprehensive validation
    if (!uploadForm.name?.trim()) {
      showError('Document name is required');
      return;
    }

    if (!uploadForm.documentType) {
      showError('Document type is required');
      return;
    }

    if (!uploadForm.file) {
      showError('Please select a file to upload');
      return;
    }

    // File size validation (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (uploadForm.file.size > maxSize) {
      showError('File size must be less than 10MB');
      return;
    }

    // Check for duplicate document names
    const duplicateName = documents.find(doc =>
      doc.name.toLowerCase() === uploadForm.name.trim().toLowerCase()
    );

    if (duplicateName) {
      showError('A document with this name already exists');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('name', uploadForm.name.trim());
      formData.append('document_type', uploadForm.documentType);
      formData.append('description', uploadForm.description?.trim() || '');
      formData.append('file', uploadForm.file);
      formData.append('extract_text', uploadForm.extractText);
      formData.append('user_id', user.id);

      const response = await api.post('/api/brand-guidelines/documents', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      showSuccess(`Document "${uploadForm.name}" uploaded successfully`);

      // Track document upload
      api.post('/api/analytics/document-action', {
        userId: user.id,
        action: 'upload',
        documentId: response.data?.id,
        documentName: uploadForm.name,
        documentType: uploadForm.documentType,
        fileSize: uploadForm.file.size,
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.error('Analytics tracking failed:', error);
      });

      setUploadDialogOpen(false);
      setUploadForm({
        name: '',
        documentType: 'brand_guidelines',
        description: '',
        file: null,
        extractText: true
      });
      fetchDocuments();
    } catch (error) {
      console.error('Error uploading document:', error);

      if (error.response?.status === 401) {
        showError('Session expired. Please log in again.');
      } else if (error.response?.status === 403) {
        showError('You do not have permission to upload documents.');
      } else if (error.response?.status === 413) {
        showError('File is too large. Please select a smaller file.');
      } else if (error.response?.status === 415) {
        showError('File type not supported. Please upload a PDF, DOCX, DOC, TXT, or MD file.');
      } else {
        showError(`Failed to upload document "${uploadForm.name}". Please try again.`);
      }
    }
  }, [isAuthenticated, user?.id, uploadForm, documents, showSuccess, showError, fetchDocuments]);

  // Delete document with enhanced error handling and confirmation
  const deleteDocument = useCallback(async (documentId) => {
    if (!isAuthenticated || !user?.id) {
      showError('Authentication required to delete documents');
      return;
    }

    // Find the document to get its name for confirmation
    const document = documents.find(doc => doc.id === documentId);
    if (!document) {
      showError('Document not found');
      return;
    }

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to delete "${document.name}"? This action cannot be undone and may affect AI response compliance.`
    );

    if (!confirmed) return;

    try {
      await api.delete(`/api/brand-guidelines/documents/${documentId}`);
      showSuccess(`Document "${document.name}" deleted successfully`);

      // Track document deletion
      api.post('/api/analytics/document-action', {
        userId: user.id,
        action: 'delete',
        documentId,
        documentName: document.name,
        documentType: document.document_type,
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.error('Analytics tracking failed:', error);
      });

      fetchDocuments();
    } catch (error) {
      console.error('Error deleting document:', error);

      if (error.response?.status === 401) {
        showError('Session expired. Please log in again.');
      } else if (error.response?.status === 403) {
        showError('You do not have permission to delete this document.');
      } else if (error.response?.status === 404) {
        showError('Document not found. It may have already been deleted.');
      } else {
        showError(`Failed to delete document "${document.name}". Please try again.`);
      }
    }
  }, [isAuthenticated, user?.id, documents, showSuccess, showError, fetchDocuments]);

  // Save all AI response settings with comprehensive error handling
  const handleSaveSettings = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      enqueueSnackbar('Please log in to save settings', { variant: 'error' });
      return;
    }

    setSaveLoading(true);

    try {
      const settingsData = {
        autoReply: autoReplySettings,
        templates: replyTemplates,
        userId: user.id,
        lastUpdated: new Date().toISOString()
      };

      await api.put(`/api/users/${user.id}/ai-settings`, settingsData);

      showSuccess('AI response settings saved successfully');
      enqueueSnackbar('Settings saved', { variant: 'success' });

      // Track settings save for analytics
      api.post('/api/analytics/settings-update', {
        userId: user.id,
        settingsType: 'ai-response',
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.error('Analytics tracking failed:', error);
      });

    } catch (error) {
      console.error('Error saving AI settings:', error);
      showError('Failed to save AI response settings');
      enqueueSnackbar('Save failed. Please try again.', { variant: 'error' });
    } finally {
      setSaveLoading(false);
    }
  }, [isAuthenticated, user?.id, autoReplySettings, replyTemplates, showSuccess, showError, enqueueSnackbar]);

  // Function to handle template dialog open
  const handleOpenTemplateDialog = (template = null) => {
    setEditingTemplate(template || {
      id: '',
      name: '',
      sentiment: 'positive',
      template: '',
      active: true,
    });
    setDialogOpen(true);
  };

  // Function to handle dialog close
  const handleDialogClose = () => {
    setDialogOpen(false);
    setEditingTemplate(null);
  };

  // Function to handle template save with validation
  const handleSaveTemplate = useCallback(async () => {
    if (!editingTemplate?.name?.trim()) {
      showError('Template name is required');
      return;
    }

    if (!editingTemplate?.template?.trim()) {
      showError('Template content is required');
      return;
    }

    if (editingTemplate.template.length > 500) {
      showError('Template content must be less than 500 characters');
      return;
    }

    // Check for duplicate template names (excluding current template if editing)
    const duplicateName = replyTemplates.find(t =>
      t.name.toLowerCase() === editingTemplate.name.toLowerCase() &&
      t.id !== editingTemplate.id
    );

    if (duplicateName) {
      showError('A template with this name already exists');
      return;
    }

    try {
      if (editingTemplate.id) {
        // Update existing template
        const updatedTemplate = {
          ...editingTemplate,
          updatedAt: new Date().toISOString()
        };

        setReplyTemplates(replyTemplates.map(t =>
          t.id === editingTemplate.id ? updatedTemplate : t
        ));

        showSuccess(`Template "${editingTemplate.name}" updated successfully`);

        // Track template update
        if (user?.id) {
          api.post('/api/analytics/template-action', {
            userId: user.id,
            action: 'update',
            templateId: editingTemplate.id,
            templateName: editingTemplate.name,
            timestamp: new Date().toISOString()
          }).catch(error => {
            console.error('Analytics tracking failed:', error);
          });
        }
      } else {
        // Add new template
        const newTemplate = {
          ...editingTemplate,
          id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        setReplyTemplates([...replyTemplates, newTemplate]);
        showSuccess(`Template "${editingTemplate.name}" added successfully`);

        // Track template creation
        if (user?.id) {
          api.post('/api/analytics/template-action', {
            userId: user.id,
            action: 'create',
            templateId: newTemplate.id,
            templateName: newTemplate.name,
            timestamp: new Date().toISOString()
          }).catch(error => {
            console.error('Analytics tracking failed:', error);
          });
        }
      }

      handleDialogClose();
    } catch (error) {
      console.error('Error saving template:', error);
      showError('Failed to save template. Please try again.');
    }
  }, [editingTemplate, replyTemplates, showSuccess, showError, user?.id]);

  // Function to handle template delete with confirmation
  const handleDeleteTemplate = useCallback(async (id) => {
    const templateToDelete = replyTemplates.find(t => t.id === id);
    if (!templateToDelete) {
      showError('Template not found');
      return;
    }

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to delete the template "${templateToDelete.name}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      setReplyTemplates(replyTemplates.filter(t => t.id !== id));
      showSuccess(`Template "${templateToDelete.name}" deleted successfully`);

      // Track template deletion
      if (user?.id) {
        api.post('/api/analytics/template-action', {
          userId: user.id,
          action: 'delete',
          templateId: id,
          templateName: templateToDelete.name,
          timestamp: new Date().toISOString()
        }).catch(error => {
          console.error('Analytics tracking failed:', error);
        });
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      showError('Failed to delete template. Please try again.');
    }
  }, [replyTemplates, showSuccess, showError, user?.id]);

  // Function to handle template activation toggle with validation
  const handleToggleTemplateActive = useCallback(async (id) => {
    const template = replyTemplates.find(t => t.id === id);
    if (!template) {
      showError('Template not found');
      return;
    }

    // Check if this is the last active template and we're trying to deactivate it
    const activeTemplates = replyTemplates.filter(t => t.active);
    if (template.active && activeTemplates.length === 1) {
      showError('You must have at least one active template');
      return;
    }

    try {
      const updatedTemplate = { ...template, active: !template.active };
      setReplyTemplates(replyTemplates.map(t =>
        t.id === id ? updatedTemplate : t
      ));

      const statusText = updatedTemplate.active ? 'activated' : 'deactivated';
      showSuccess(`Template "${template.name}" ${statusText} successfully`);

      // Track template status change
      if (user?.id) {
        api.post('/api/analytics/template-action', {
          userId: user.id,
          action: 'toggle_status',
          templateId: id,
          templateName: template.name,
          newStatus: updatedTemplate.active,
          timestamp: new Date().toISOString()
        }).catch(error => {
          console.error('Analytics tracking failed:', error);
        });
      }
    } catch (error) {
      console.error('Error updating template status:', error);
      showError('Failed to update template status. Please try again.');
    }
  }, [replyTemplates, showSuccess, showError, user?.id]);

  // Get sentiment icon
  const getSentimentIcon = (sentiment) => {
    switch (sentiment) {
      case 'positive':
        return <SentimentSatisfiedIcon color="success" />;
      case 'negative':
        return <SentimentDissatisfiedIcon color="error" />;
      case 'neutral':
      default:
        return <SentimentNeutralIcon color="info" />;
    }
  };

  // Handle document expansion with analytics tracking
  const handleExpandDocument = useCallback((docId) => {
    const isExpanding = expandedDocId !== docId;
    setExpandedDocId(isExpanding ? docId : null);

    // Track document interaction for analytics
    if (isExpanding && user?.id) {
      api.post('/api/analytics/document-interaction', {
        userId: user.id,
        documentId: docId,
        action: 'expand',
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.error('Analytics tracking failed:', error);
      });
    }

    // Provide user feedback
    enqueueSnackbar(
      isExpanding ? 'Document expanded' : 'Document collapsed',
      { variant: 'info', autoHideDuration: 2000 }
    );
  }, [expandedDocId, user?.id, enqueueSnackbar]);

  // Authentication guard
  if (!isAuthenticated) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Authentication Required
          </Typography>
          <Typography variant="body2">
            Please log in to access AI response settings and manage your automated responses.
          </Typography>
        </Alert>
        <Button variant="contained" onClick={() => window.location.href = '/login'}>
          Go to Login
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      {!isEmbedded && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h5" gutterBottom>
            <CommentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            AI Response Management
          </Typography>
          <LoadingButton
            variant="contained"
            color="primary"
            loading={saveLoading}
            onClick={handleSaveSettings}
            startIcon={<SaveIcon />}
            disabled={!isAuthenticated}
          >
            Save All Settings
          </LoadingButton>
        </Box>
      )}

      <>
          {/* Main settings card with policy compliance toggle */}
          <Card sx={{ mb: 3 }}>
            <CardHeader
              title="AI Response Settings"
              avatar={<SettingsIcon />}
              action={
                <FormControlLabel
                  control={
                    <Switch
                      checked={autoReplySettings.enabled}
                      onChange={(e) => handleSettingChange('enabled', e.target.checked)}
                      color="primary"
                    />
                  }
                  label={autoReplySettings.enabled ? "Enabled" : "Disabled"}
                />
              }
            />
            <Divider />
            <CardContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={autoReplySettings.requireApproval}
                        onChange={(e) => handleSettingChange('requireApproval', e.target.checked)}
                        color="primary"
                        disabled={!autoReplySettings.enabled}
                      />
                    }
                    label="Require approval before sending"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={autoReplySettings.useSentimentAnalysis}
                        onChange={(e) => handleSettingChange('useSentimentAnalysis', e.target.checked)}
                        color="primary"
                        disabled={!autoReplySettings.enabled}
                      />
                    }
                    label="Use sentiment analysis"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                  <Typography variant="subtitle1" gutterBottom>
                    Policy Compliance
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Tooltip title="When enabled, AI-generated content will reference your policy documents to ensure compliance with your Terms of Service, Privacy Policy, and Brand Guidelines">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={autoReplySettings.usePolicyCompliance}
                          onChange={(e) => handleSettingChange('usePolicyCompliance', e.target.checked)}
                          color="primary"
                          disabled={!autoReplySettings.enabled}
                        />
                      }
                      label="Enable policy compliance checking"
                    />
                  </Tooltip>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={autoReplySettings.followBrandGuidelines}
                        onChange={(e) => handleSettingChange('followBrandGuidelines', e.target.checked)}
                        color="primary"
                        disabled={!autoReplySettings.enabled || !autoReplySettings.usePolicyCompliance}
                      />
                    }
                    label="Follow brand guidelines"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Tabs for different sections */}
          <Paper sx={{ mb: 3 }}>
            <Tabs
              value={tabValue}
              onChange={(_, newValue) => setTabValue(newValue)}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab label="Response Templates" icon={<AutorenewIcon />} iconPosition="start" />
              <Tab label="Policy Documents" icon={<DocumentIcon />} iconPosition="start" />
            </Tabs>
          </Paper>

          {/* Tab content */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              {/* Reply Templates */}
              <Grid item xs={12}>
                <Card>
                  <CardHeader
                    title="Response Templates"
                    avatar={<AutorenewIcon />}
                    action={
                      <Button
                        startIcon={<AddIcon />}
                        color="primary"
                        size="small"
                        onClick={() => handleOpenTemplateDialog()}
                        disabled={!autoReplySettings.enabled}
                      >
                        Add Template
                      </Button>
                    }
                  />
                  <Divider />
                  <CardContent>
                    <List>
                      {replyTemplates.map((template) => (
                        <React.Fragment key={template.id}>
                          <ListItem>
                            <ListItemIcon>
                              {getSentimentIcon(template.sentiment)}
                            </ListItemIcon>
                            <ListItemText
                              primary={template.name}
                              secondary={template.template.length > 50 ? `${template.template.substring(0, 50)}...` : template.template}
                            />
                            <ListItemSecondaryAction>
                              <Tooltip title={template.active ? "Active" : "Inactive"}>
                                <Switch
                                  edge="end"
                                  checked={template.active}
                                  onChange={() => handleToggleTemplateActive(template.id)}
                                  disabled={!autoReplySettings.enabled}
                                />
                              </Tooltip>
                              <Tooltip title="Edit">
                                <IconButton
                                  edge="end"
                                  onClick={() => handleOpenTemplateDialog(template)}
                                  disabled={!autoReplySettings.enabled}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete">
                                <IconButton
                                  edge="end"
                                  onClick={() => handleDeleteTemplate(template.id)}
                                  disabled={!autoReplySettings.enabled}
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </ListItemSecondaryAction>
                          </ListItem>
                          <Divider component="li" />
                        </React.Fragment>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              {/* AI Response Management Navigation */}
              <Grid item xs={12}>
                <Card>
                  <CardHeader
                    title="Manage AI Responses"
                    avatar={<CommentIcon />}
                    subheader="Review and approve pending AI-generated responses"
                  />
                  <Divider />
                  <CardContent>
                    <Alert severity="info" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        When &quot;Require approval before sending&quot; is enabled, all AI-generated responses will need your approval before being published.
                        Use the dedicated AI Response Management page for a comprehensive review experience.
                      </Typography>
                    </Alert>
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                      <Button
                        variant="contained"
                        color="primary"
                        size="large"
                        startIcon={<CommentIcon />}
                        component="a"
                        href="/ai-response-management"
                        disabled={!autoReplySettings.enabled || !autoReplySettings.requireApproval}
                        sx={{ minWidth: 250 }}
                      >
                        Open AI Response Management
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Policy Documents Tab */}
          <TabPanel value={tabValue} index={1}>
            <Box>
              <Box sx={{ mb: 3 }}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    AI-Powered Policy Compliance
                  </Typography>
                  <Typography variant="body2">
                    Upload your Terms of Service, Privacy Policy, and Brand Guidelines documents to ensure all automated responses
                    comply with your specific policies. Our AI will reference these documents when generating responses to user comments and messages.
                  </Typography>
                </Alert>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setUploadDialogOpen(true)}
                >
                  Upload Document
                </Button>
              </Box>

              {documentsLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : documents.length === 0 ? (
                <EmptyState
                  title="No Documents Yet"
                  description="Upload your Terms of Service, Privacy Policy, and Brand Guidelines to help the AI generate policy-compliant responses."
                  icon={<DocumentIcon sx={{ fontSize: 60 }} />}
                  actionText="Upload Document"
                  onAction={() => setUploadDialogOpen(true)}
                />
              ) : (
                <Grid container spacing={3}>
                  {/* Group documents by type */}
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Policy Documents
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      These documents are used to ensure all automated responses comply with your legal policies.
                    </Typography>
                    {documents
                      .filter(doc => ['terms_of_service', 'privacy_policy', 'policy'].includes(doc.document_type))
                      .map((doc) => (
                        <Grid item xs={12} key={doc.id} sx={{ mb: 2 }}>
                          <GlassmorphicCard>
                            <CardContent>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                                     onClick={() => handleExpandDocument(doc.id)}>
                                  <DocumentIcon sx={{ mr: 1, color: doc.document_type === 'terms_of_service' ? 'primary.main' :
                                                         doc.document_type === 'privacy_policy' ? 'secondary.main' : 'text.primary' }} />
                                  <Typography variant="h6">{doc.name}</Typography>
                                  <IconButton size="small" sx={{ ml: 1 }}>
                                    {expandedDocId === doc.id ? <EditIcon /> : <AddIcon />}
                                  </IconButton>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Chip
                                    label={DOCUMENT_TYPES.find(type => type.value === doc.document_type)?.label || doc.document_type}
                                    color={doc.document_type === 'terms_of_service' ? 'primary' :
                                          doc.document_type === 'privacy_policy' ? 'secondary' : 'default'}
                                    size="small"
                                    sx={{ mr: 1 }}
                                  />
                                  <Tooltip title="Delete document">
                                    <IconButton
                                      size="small"
                                      color="error"
                                      onClick={() => deleteDocument(doc.id)}
                                    >
                                      <DeleteIcon />
                                    </IconButton>
                                  </Tooltip>
                                </Box>
                              </Box>
                              <Divider sx={{ my: 1.5 }} />
                              <Typography variant="body2" color="text.secondary" paragraph>
                                {doc.description || 'No description provided'}
                              </Typography>

                              {/* Expanded content */}
                              {expandedDocId === doc.id && (
                                <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Document Details
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary" paragraph>
                                    <strong>Type:</strong> {DOCUMENT_TYPES.find(type => type.value === doc.document_type)?.label || doc.document_type}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary" paragraph>
                                    <strong>Uploaded:</strong> {new Date(doc.created_at).toLocaleDateString()}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary" paragraph>
                                    <strong>File Size:</strong> {doc.file_size ? `${(doc.file_size / 1024).toFixed(1)} KB` : 'Unknown'}
                                  </Typography>
                                  {doc.content_preview && (
                                    <Box sx={{ mt: 2 }}>
                                      <Typography variant="subtitle2" gutterBottom>
                                        Content Preview
                                      </Typography>
                                      <Typography variant="body2" sx={{
                                        bgcolor: 'grey.50',
                                        p: 1,
                                        borderRadius: 1,
                                        fontFamily: 'monospace',
                                        maxHeight: 200,
                                        overflow: 'auto'
                                      }}>
                                        {doc.content_preview}
                                      </Typography>
                                    </Box>
                                  )}
                                </Box>
                              )}
                            </CardContent>
                          </GlassmorphicCard>
                        </Grid>
                      ))}
                  </Grid>

                  <Grid item xs={12} sx={{ mt: 4 }}>
                    <Typography variant="h6" gutterBottom>
                      Brand Guidelines
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      These documents help the AI understand your brand voice and style.
                    </Typography>
                    {documents
                      .filter(doc => ['brand_guidelines', 'tone_of_voice'].includes(doc.document_type))
                      .map((doc) => (
                        <Grid item xs={12} key={doc.id} sx={{ mb: 2 }}>
                          <GlassmorphicCard>
                            <CardContent>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <DocumentIcon sx={{ mr: 1 }} />
                                  <Typography variant="h6">{doc.name}</Typography>
                                </Box>
                                <Chip
                                  label={DOCUMENT_TYPES.find(type => type.value === doc.document_type)?.label || doc.document_type}
                                  size="small"
                                  sx={{ mr: 1 }}
                                />
                              </Box>
                              <Divider sx={{ my: 1.5 }} />
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Typography variant="body2" color="text.secondary">
                                  {doc.description || 'No description provided'}
                                </Typography>
                                <Box>
                                  <IconButton size="small" onClick={() => deleteDocument(doc.id)} color="error">
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Box>
                              </Box>
                            </CardContent>
                          </GlassmorphicCard>
                        </Grid>
                      ))}
                  </Grid>
                </Grid>
              )}
            </Box>
          </TabPanel>
        </>

      {/* Template Dialog */}
      <Dialog open={dialogOpen} onClose={handleDialogClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingTemplate?.id ? 'Edit Reply Template' : 'Add Reply Template'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Template Name"
                fullWidth
                value={editingTemplate?.name || ''}
                onChange={(e) => setEditingTemplate({ ...editingTemplate, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Sentiment</InputLabel>
                <Select
                  value={editingTemplate?.sentiment || 'positive'}
                  onChange={(e) => setEditingTemplate({ ...editingTemplate, sentiment: e.target.value })}
                  label="Sentiment"
                >
                  <MenuItem value="positive">Positive</MenuItem>
                  <MenuItem value="neutral">Neutral</MenuItem>
                  <MenuItem value="negative">Negative</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Reply Template"
                fullWidth
                multiline
                rows={4}
                value={editingTemplate?.template || ''}
                onChange={(e) => setEditingTemplate({ ...editingTemplate, template: e.target.value })}
                helperText="You can use placeholders like {user_name} for dynamic content"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleSaveTemplate}
            color="primary"
            disabled={!editingTemplate?.name || !editingTemplate?.template}
            startIcon={<SaveIcon />}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Upload Dialog */}
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Upload Policy Document</DialogTitle>
        <DialogContent>
          {/* Info box explaining document usage */}
          <Box sx={{ mb: 3, mt: 1 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" fontWeight="bold">
                How Your Documents Will Be Used
              </Typography>
              <Typography variant="body2">
                The AI will analyze your uploaded documents to ensure all automated responses comply with your policies.
                When users ask questions or make comments related to your terms, privacy, or brand guidelines, the AI will
                reference these documents to generate appropriate, policy-compliant responses.
              </Typography>
            </Alert>
          </Box>

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Document Name"
                name="name"
                value={uploadForm.name}
                onChange={handleInputChange}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel>Document Type</InputLabel>
                <Select
                  name="documentType"
                  value={uploadForm.documentType}
                  onChange={handleInputChange}
                  label="Document Type"
                >
                  {DOCUMENT_TYPES.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                  {uploadForm.documentType === 'terms_of_service' ?
                    'Your Terms of Service will be used to ensure responses comply with your legal terms and user agreements.' :
                   uploadForm.documentType === 'privacy_policy' ?
                    'Your Privacy Policy will be referenced when responding to questions about data handling and privacy concerns.' :
                   uploadForm.documentType === 'policy' ?
                    'Company policies will guide responses related to your specific business rules and procedures.' :
                   uploadForm.documentType === 'brand_guidelines' ?
                    'Brand guidelines help the AI maintain your brand voice and style in all responses.' :
                   uploadForm.documentType === 'tone_of_voice' ?
                    'Tone of voice documents ensure responses match your preferred communication style.' :
                    'Select a document type to see how it will be used.'}
                </Typography>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={uploadForm.description}
                onChange={handleInputChange}
                multiline
                rows={2}
                helperText="Add any notes about this document that might help with understanding its purpose"
              />
            </Grid>

            <Grid item xs={12}>
              <Box
                {...getRootProps()}
                sx={{
                  border: '2px dashed',
                  borderColor: isDragActive ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  p: 3,
                  textAlign: 'center',
                  cursor: 'pointer',
                  bgcolor: isDragActive ? 'action.hover' : 'background.paper',
                  transition: 'all 0.2s ease'
                }}
              >
                <input {...getInputProps()} />
                <UploadIcon sx={{ fontSize: 40, mb: 1, color: 'text.secondary' }} />
                <Typography variant="body1" gutterBottom>
                  {isDragActive ? 'Drop the file here' : 'Drag & drop a file here, or click to select'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Supported formats: PDF, DOCX, DOC, TXT, MD
                </Typography>
              </Box>

              {uploadForm.file && (
                <Box sx={{ mt: 2 }}>
                  <FilePreview file={uploadForm.file} />
                </Box>
              )}
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={uploadForm.extractText}
                    onChange={handleSwitchChange}
                    name="extractText"
                  />
                }
                label="Extract text from document (recommended)"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={uploadDocument}
            disabled={!uploadForm.name || !uploadForm.documentType || !uploadForm.file}
          >
            Upload
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AIResponseSettings;
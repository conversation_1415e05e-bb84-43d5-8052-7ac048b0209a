// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { Box, Typography, Tabs, Tab, Paper, Button, useTheme, useMediaQuery, ButtonGroup, CircularProgress, Alert, AlertTitle } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import ListIcon from '@mui/icons-material/List';
import RecommendIcon from '@mui/icons-material/Recommend';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

// Import the existing components
import Calendar from './Calendar';
import ScheduleList from './ScheduleList';

/**
 * UnifiedSchedulingPage combines the Calendar and Scheduled Posts views
 * into a single page with tabs for easy switching between views
 */
const UnifiedSchedulingPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get the tab from URL query params or default to 'calendar'
  const query = new URLSearchParams(location.search);
  const defaultTab = query.get('view') || 'calendar';

  const [activeTab, setActiveTab] = useState(defaultTab);
  const [componentError, setComponentError] = useState(null);

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Update URL when tab changes without full page reload
  useEffect(() => {
    if (!isAuthenticated) return;

    const searchParams = new URLSearchParams(location.search);
    searchParams.set('view', activeTab);
    navigate({
      pathname: location.pathname,
      search: searchParams.toString()
    }, { replace: true });
  }, [activeTab, navigate, location.pathname, location.search, isAuthenticated]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    setComponentError(null); // Clear any previous errors when switching tabs
  };

  // Error boundary for child components
  const handleComponentError = (error) => {
    console.error('Component error:', error);
    setComponentError(error.message || 'An error occurred while loading the component');
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '50vh'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        <Typography variant="h4" component="h1" sx={{ mb: { xs: 2, md: 0 } }}>
          Content Scheduling
        </Typography>

        <ButtonGroup
          variant="contained"
          color="primary"
          orientation={isMobile ? "vertical" : "horizontal"}
          size={isMobile ? "small" : "medium"}
        >
          <Button
            startIcon={<RecommendIcon />}
            onClick={() => navigate('/scheduling/recommendations')}
          >
            {isMobile ? 'Recommendations' : 'Smart Recommendations'}
          </Button>
          <Button
            startIcon={<AccessTimeIcon />}
            onClick={() => navigate('/scheduling/optimal-times')}
          >
            Optimal Times
          </Button>
        </ButtonGroup>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant={isMobile ? "fullWidth" : "standard"}
          aria-label="scheduling tabs"
        >
          <Tab
            icon={<CalendarMonthIcon />}
            iconPosition="start"
            label="Calendar View"
            value="calendar"
          />
          <Tab
            icon={<ListIcon />}
            iconPosition="start"
            label="List View"
            value="list"
          />
        </Tabs>
      </Paper>

      {/* Error Display */}
      {componentError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <AlertTitle>Component Error</AlertTitle>
          {componentError}
          <Button
            variant="outlined"
            size="small"
            sx={{ mt: 1 }}
            onClick={() => setComponentError(null)}
          >
            Dismiss
          </Button>
        </Alert>
      )}

      {/* Render the active tab content */}
      <Box sx={{ mt: 2 }}>
        {activeTab === 'calendar' && !componentError && (
          <Calendar
            isEmbedded={true}
            onError={handleComponentError}
          />
        )}

        {activeTab === 'list' && !componentError && (
          <ScheduleList
            isEmbedded={true}
            onError={handleComponentError}
          />
        )}
      </Box>
    </Box>
  );
};

export default UnifiedSchedulingPage;

// @since 2024-1-1 to 2025-25-7
import { useState, useMemo, useCallback, useRef, useEffect, memo, forwardRef, useImperativeHandle } from "react";
import PropTypes from "prop-types";
import * as d3 from "d3";
import {
  Box,
  Typography,
  CircularProgress,
  useTheme,
  useMediaQuery,
  alpha,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Alert,
  AlertTitle,
  Grid
} from "@mui/material";
import {
  Download as ExportIcon,
  Refresh as RefreshIcon,

  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,
  Timeline as TimelineIcon,
  BarChart as BarChartIcon,
  ShowChart as LineChartIcon,
  ScatterPlot as ScatterPlotIcon
} from "@mui/icons-material";

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Chart types with enhanced configurations
const CHART_TYPES = {
  LINE: {
    id: 'line',
    name: 'Line Chart',
    icon: LineChartIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Show trends over time',
    subscriptionLimits: {
      creator: { dataPoints: 50, metrics: 2, interactivity: false },
      accelerator: { dataPoints: 200, metrics: 5, interactivity: true },
      dominator: { dataPoints: -1, metrics: -1, interactivity: true, advanced: true }
    }
  },
  BAR: {
    id: 'bar',
    name: 'Bar Chart',
    icon: BarChartIcon,
    color: ACE_COLORS.YELLOW,
    description: 'Compare values across categories',
    subscriptionLimits: {
      creator: { dataPoints: 50, metrics: 2, interactivity: false },
      accelerator: { dataPoints: 200, metrics: 5, interactivity: true },
      dominator: { dataPoints: -1, metrics: -1, interactivity: true, advanced: true }
    }
  },
  AREA: {
    id: 'area',
    name: 'Area Chart',
    icon: TimelineIcon,
    color: ACE_COLORS.DARK,
    description: 'Show cumulative values over time',
    subscriptionLimits: {
      creator: { dataPoints: 50, metrics: 2, interactivity: false },
      accelerator: { dataPoints: 200, metrics: 5, interactivity: true },
      dominator: { dataPoints: -1, metrics: -1, interactivity: true, advanced: true }
    }
  },
  SCATTER: {
    id: 'scatter',
    name: 'Scatter Plot',
    icon: ScatterPlotIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Show correlation between metrics',
    subscriptionLimits: {
      creator: { dataPoints: 0, metrics: 0, interactivity: false },
      accelerator: { dataPoints: 100, metrics: 3, interactivity: true },
      dominator: { dataPoints: -1, metrics: -1, interactivity: true, advanced: true }
    }
  }
};



// Animation configurations
const ANIMATION_CONFIG = {
  DURATION: 800,
  EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  STAGGER_DELAY: 100
};

// Performance chart metrics
const CHART_METRICS = {
  IMPRESSIONS: { key: 'impressions', label: 'Impressions', color: ACE_COLORS.PURPLE },
  ENGAGEMENTS: { key: 'engagements', label: 'Engagements', color: ACE_COLORS.YELLOW },
  CLICKS: { key: 'clicks', label: 'Clicks', color: ACE_COLORS.DARK },
  REACH: { key: 'reach', label: 'Reach', color: ACE_COLORS.PURPLE },
  SHARES: { key: 'shares', label: 'Shares', color: ACE_COLORS.YELLOW }
};

/**
 * Enhanced PerformanceChart Component - Enterprise-grade performance data visualization
 * Features: Plan-based chart limitations, real-time data updates, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced chart interactions and performance analytics
 *
 * @component
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data array
 * @param {string} [props.chartType='line'] - Chart type (line, bar, area, scatter)
 * @param {string} [props.variant='detailed'] - Chart display variant
 * @param {number} [props.height=400] - Chart height in pixels
 * @param {boolean} [props.loading=false] - Loading state
 * @param {Object} [props.error=null] - Error object
 * @param {boolean} [props.enableAnalytics=false] - Enable analytics features
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.enableInteractivity=false] - Enable chart interactions
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onChartTypeChange] - Chart type change callback
 * @param {Function} [props.onDataPointClick] - Data point click callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Array} [props.metrics] - Available metrics to display
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const PerformanceChart = memo(forwardRef(({
  data = [],
  chartType = 'line',

  height = 400,
  loading = false,
  error = null,

  enableExport = false,

  realTimeUpdates = false,
  onChartTypeChange,
  onDataPointClick,
  onExport,
  onRefresh,
  metrics = ['impressions', 'engagements'],

  className = '',
  style = {},
  testId = 'performance-chart',
  ariaLabel,
  ariaDescription
}, ref) => {
  const chartRef = useRef(null);
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, isScreenReaderActive, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery('(max-width:576px)');
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading || subscriptionLoading,
    refreshing: false,
    showAnalyticsPanel: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showSettingsDialog: false,
    selectedDataPoint: null,
    currentChartType: chartType,
    visibleMetrics: metrics,
    zoomLevel: 1,
    lastUpdated: null,
    animationKey: 0,
    errors: {},
    // Chart state
    hoveredPoint: null,
    selectedTimeRange: 'all',
    brushSelection: null,
    chartDimensions: { width: 0, height: 0 }
  });

  // Chart data state
  const [chartData, setChartData] = useState({
    raw: data || [],
    processed: [],
    filtered: [],
    aggregated: {},
    statistics: null
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);

  /**
   * Enhanced plan-based chart validation - Production Ready
   */
  const validateChartFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canViewChart: false,
        hasChartAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { dataPoints: 0, metrics: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based chart limits
    const planLimits = {
      creator: {
        dataPoints: 50,
        metrics: 2,
        features: ['basic_charts'],
        interactivity: false,
        export: false,
        analytics: false,
        realTime: false,
        chartTypes: ['line'],
        customization: false
      },
      accelerator: {
        dataPoints: 200,
        metrics: 5,
        features: ['basic_charts', 'advanced_charts', 'chart_interactions'],
        interactivity: true,
        export: true,
        analytics: true,
        realTime: true,
        chartTypes: ['line', 'bar', 'area'],
        customization: true
      },
      dominator: {
        dataPoints: -1,
        metrics: -1,
        features: ['basic_charts', 'advanced_charts', 'chart_interactions', 'advanced_analytics'],
        interactivity: true,
        export: true,
        analytics: true,
        realTime: true,
        chartTypes: ['line', 'bar', 'area', 'scatter'],
        customization: true
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = chartData.processed.length || 0;
    const limit = currentPlanLimits.dataPoints;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewChart: true,
      hasChartAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, chartData.processed]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const chartLimits = validateChartFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasInteractivity: chartLimits.planLimits.interactivity,
      hasExport: chartLimits.planLimits.export,
      hasAnalytics: chartLimits.planLimits.analytics,
      hasRealTime: chartLimits.planLimits.realTime,
      hasCustomization: chartLimits.planLimits.customization,
      maxDataPoints: chartLimits.planLimits.dataPoints,
      maxMetrics: chartLimits.planLimits.metrics,
      availableChartTypes: chartLimits.planLimits.chartTypes,
      availableFeatures: chartLimits.planLimits.features,
      refreshInterval: chartLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateChartFeatures]);

  /**
   * Enhanced data processing - Production Ready
   */
  const processChartData = useCallback(() => {
    if (!data || !Array.isArray(data)) return [];

    const chartLimits = validateChartFeatures();
    const maxDataPoints = chartLimits.planLimits.dataPoints;

    // Apply subscription limits
    const limitedData = maxDataPoints === -1 ? data : data.slice(0, maxDataPoints);

    // Parse dates and process data
    const processedData = limitedData.map((d, index) => ({
      ...d,
      date: new Date(d.date),
      id: d.id || `data-point-${index}`,
      // Ensure all metrics have default values
      impressions: d.impressions || 0,
      engagements: d.engagements || 0,
      clicks: d.clicks || 0,
      reach: d.reach || 0,
      shares: d.shares || 0
    }));

    return processedData;
  }, [data, validateChartFeatures]);

  /**
   * Enhanced chart type validation - Production Ready
   */
  const isChartTypeAvailable = useCallback((type) => {
    return subscriptionFeatures.availableChartTypes.includes(type);
  }, [subscriptionFeatures.availableChartTypes]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'img',
      'aria-label': ariaLabel || `Performance chart showing ${metrics.join(', ')} data`,
      'aria-description': ariaDescription || `Interactive ${chartType} chart displaying performance metrics over time`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, metrics, chartType, realTimeUpdates]);

  /**
   * Enhanced chart refresh handler - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    if (state.refreshing) return;

    setState(prev => ({ ...prev, refreshing: true, errors: {} }));

    try {
      if (onRefresh) {
        await onRefresh();
      }

      setState(prev => ({
        ...prev,
        refreshing: false,
        lastUpdated: new Date().toISOString(),
        animationKey: prev.animationKey + 1
      }));

      showSuccessNotification('Chart data refreshed successfully');
      announceToScreenReader('Performance chart has been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        refreshing: false,
        errors: { refresh: error.message }
      }));

      showErrorNotification(`Failed to refresh chart: ${error.message}`);
      announceToScreenReader('Failed to refresh chart data');
    }
  }, [state.refreshing, onRefresh, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced chart type change handler - Production Ready
   */
  const handleChartTypeChange = useCallback((newType) => {
    if (!isChartTypeAvailable(newType)) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, currentChartType: newType }));

    if (onChartTypeChange) {
      onChartTypeChange(newType);
    }

    announceToScreenReader(`Chart type changed to ${newType}`);
  }, [isChartTypeAvailable, onChartTypeChange, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'png') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, chartData.processed);
      }

      showSuccessNotification(`Chart exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Chart has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export chart: ${error.message}`);
      announceToScreenReader('Failed to export chart');
    }
  }, [subscriptionFeatures.hasExport, chartData.processed, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    exportChart: handleExport,
    changeChartType: handleChartTypeChange,
    getChartData: () => chartData.processed,
    getChartLimits: validateChartFeatures,
    focus: () => setFocusToElement(chartRef.current),
    announce: (message) => announceToScreenReader(message)
  }), [
    chartData.processed,
    validateChartFeatures,
    handleRefresh,
    handleExport,
    handleChartTypeChange,
    setFocusToElement,
    announceToScreenReader
  ]);

  /**
   * Enhanced data processing effect - Production Ready
   */
  useEffect(() => {
    const processed = processChartData();
    setChartData(prev => ({
      ...prev,
      raw: data || [],
      processed
    }));
  }, [data, processChartData]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      if (onRefresh) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, onRefresh, handleRefresh]);

  /**
   * Enhanced D3 chart rendering - Production Ready
   */
  useEffect(() => {
    const currentChartRef = chartRef.current;
    if (!chartData.processed || !currentChartRef || state.loading) return;

    // Clear previous chart
    d3.select(currentChartRef).selectAll("*").remove();

    // Enhanced responsive dimensions
    const containerRect = currentChartRef.getBoundingClientRect();
    const margin = isMobile
      ? { top: 15, right: 20, bottom: 40, left: 40 }
      : { top: 20, right: 30, bottom: 50, left: 60 };

    const width = Math.max(300, containerRect.width - margin.left - margin.right);
    const height = Math.max(200, (height || 400) - margin.top - margin.bottom);

    // Create enhanced SVG with accessibility
    const svg = d3
      .select(currentChartRef)
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .attr("role", "img")
      .attr("aria-label", `Performance chart showing ${state.visibleMetrics.join(', ')} data`)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Enhanced data processing with subscription limits
    const processedData = chartData.processed;
    if (!processedData.length) return;

    // Enhanced scales with ACE Social colors
    const x = d3
      .scaleTime()
      .domain(d3.extent(processedData, (d) => d.date))
      .range([0, width]);

    // Dynamic Y scales based on visible metrics
    const scales = {};
    state.visibleMetrics.forEach((metric) => {
      scales[metric] = d3
        .scaleLinear()
        .domain([0, d3.max(processedData, (d) => d[metric]) * 1.1])
        .range([height, 0]);
    });

    // Enhanced grid with ACE Social styling
    if (state.visibleMetrics.length > 0) {
      const primaryMetric = state.visibleMetrics[0];

      // X axis grid
      svg
        .append("g")
        .attr("class", "grid")
        .attr("transform", `translate(0,${height})`)
        .call(d3.axisBottom(x).ticks(isMobile ? 3 : 5).tickSize(-height).tickFormat(""))
        .selectAll("line")
        .style("stroke", alpha(ACE_COLORS.DARK, 0.1))
        .style("stroke-dasharray", "2,2");

      // Y axis grid
      svg
        .append("g")
        .attr("class", "grid")
        .call(d3.axisLeft(scales[primaryMetric]).ticks(5).tickSize(-width).tickFormat(""))
        .selectAll("line")
        .style("stroke", alpha(ACE_COLORS.DARK, 0.1))
        .style("stroke-dasharray", "2,2");
    }

    // Enhanced X axis with ACE Social styling
    svg
      .append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(x)
        .ticks(isMobile ? 3 : 5)
        .tickFormat(d3.timeFormat(isMobile ? "%m/%d" : "%b %d"))
      )
      .selectAll("text")
      .style("text-anchor", "end")
      .style("fill", ACE_COLORS.DARK)
      .style("font-size", isMobile ? "10px" : "12px")
      .attr("dx", "-.8em")
      .attr("dy", ".15em")
      .attr("transform", isMobile ? "rotate(-45)" : "rotate(-30)");

    // Enhanced Y axes for visible metrics
    state.visibleMetrics.forEach((metric, metricIndex) => {
      const isLeft = metricIndex % 2 === 0;
      const yScale = scales[metric];
      const metricConfig = CHART_METRICS[metric.toUpperCase()] || {
        label: metric,
        color: ACE_COLORS.PURPLE
      };

      svg
        .append("g")
        .attr("transform", isLeft ? "translate(0,0)" : `translate(${width}, 0)`)
        .call(isLeft ? d3.axisLeft(yScale) : d3.axisRight(yScale))
        .selectAll("text")
        .style("fill", ACE_COLORS.DARK)
        .style("font-size", "11px");

      // Enhanced axis labels
      svg
        .append("text")
        .attr("fill", metricConfig.color)
        .attr("transform", "rotate(-90)")
        .attr("y", isLeft ? -40 : width + 40)
        .attr("x", -height / 2)
        .attr("text-anchor", "middle")
        .style("font-weight", "600")
        .style("font-size", "12px")
        .text(metricConfig.label);
    });

    // Enhanced chart rendering based on chart type
    const renderChart = () => {
      switch (state.currentChartType) {
        case 'line':
          renderLineChart();
          break;
        case 'bar':
          if (isChartTypeAvailable('bar')) renderBarChart();
          break;
        case 'area':
          if (isChartTypeAvailable('area')) renderAreaChart();
          break;
        case 'scatter':
          if (isChartTypeAvailable('scatter')) renderScatterChart();
          break;
        default:
          renderLineChart();
      }
    };

    // Enhanced line chart rendering
    const renderLineChart = () => {
      state.visibleMetrics.forEach((metric, index) => {
        const yScale = scales[metric];
        const metricConfig = CHART_METRICS[metric.toUpperCase()] || {
          color: ACE_COLORS.PURPLE
        };

        // Enhanced line generator with smooth curves
        const line = d3
          .line()
          .x((d) => x(d.date))
          .y((d) => yScale(d[metric]))
          .curve(d3.curveMonotoneX);

        // Add line with enhanced styling
        const path = svg
          .append("path")
          .datum(processedData)
          .attr("fill", "none")
          .attr("stroke", metricConfig.color)
          .attr("stroke-width", 3)
          .attr("stroke-linecap", "round")
          .attr("stroke-linejoin", "round")
          .attr("d", line)
          .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")
          .style("opacity", 0);

        // Enhanced animation
        if (!prefersReducedMotion) {
          const totalLength = path.node().getTotalLength();
          path
            .attr("stroke-dasharray", `${totalLength} ${totalLength}`)
            .attr("stroke-dashoffset", totalLength)
            .style("opacity", 1)
            .transition()
            .duration(ANIMATION_CONFIG.DURATION + (index * ANIMATION_CONFIG.STAGGER_DELAY))
            .ease(d3.easeQuadOut)
            .attr("stroke-dashoffset", 0);
        } else {
          path.style("opacity", 1);
        }
      });
    };

    // Enhanced bar chart rendering
    const renderBarChart = () => {
      const barWidth = width / processedData.length * 0.8;
      const metricCount = state.visibleMetrics.length;

      state.visibleMetrics.forEach((metric, metricIndex) => {
        const yScale = scales[metric];
        const metricConfig = CHART_METRICS[metric.toUpperCase()] || {
          color: ACE_COLORS.PURPLE
        };

        svg
          .selectAll(`.bar-${metric}`)
          .data(processedData)
          .enter()
          .append("rect")
          .attr("class", `bar-${metric}`)
          .attr("x", (d) => x(d.date) - barWidth/2 + (metricIndex * barWidth/metricCount))
          .attr("y", height)
          .attr("width", barWidth / metricCount)
          .attr("height", 0)
          .attr("fill", metricConfig.color)
          .style("opacity", 0.8)
          .transition()
          .duration(ANIMATION_CONFIG.DURATION)
          .delay((d, i) => i * ANIMATION_CONFIG.STAGGER_DELAY)
          .attr("y", (d) => yScale(d[metric]))
          .attr("height", (d) => height - yScale(d[metric]));
      });
    };

    // Enhanced area chart rendering
    const renderAreaChart = () => {
      state.visibleMetrics.forEach((metric, index) => {
        const yScale = scales[metric];
        const metricConfig = CHART_METRICS[metric.toUpperCase()] || {
          color: ACE_COLORS.PURPLE
        };

        const area = d3
          .area()
          .x((d) => x(d.date))
          .y0(height)
          .y1((d) => yScale(d[metric]))
          .curve(d3.curveMonotoneX);

        svg
          .append("path")
          .datum(processedData)
          .attr("fill", alpha(metricConfig.color, 0.3))
          .attr("stroke", metricConfig.color)
          .attr("stroke-width", 2)
          .attr("d", area)
          .style("opacity", 0)
          .transition()
          .duration(ANIMATION_CONFIG.DURATION)
          .delay(index * ANIMATION_CONFIG.STAGGER_DELAY)
          .style("opacity", 1);
      });
    };

    // Enhanced scatter plot rendering
    const renderScatterChart = () => {
      if (state.visibleMetrics.length < 2) return;

      const xMetric = state.visibleMetrics[0];
      const yMetric = state.visibleMetrics[1];
      const xScale = scales[xMetric];
      const yScale = scales[yMetric];

      svg
        .selectAll(".scatter-dot")
        .data(processedData)
        .enter()
        .append("circle")
        .attr("class", "scatter-dot")
        .attr("cx", (d) => xScale(d[xMetric]))
        .attr("cy", (d) => yScale(d[yMetric]))
        .attr("r", 0)
        .attr("fill", ACE_COLORS.PURPLE)
        .style("opacity", 0.7)
        .transition()
        .duration(ANIMATION_CONFIG.DURATION)
        .delay((d, i) => i * ANIMATION_CONFIG.STAGGER_DELAY)
        .attr("r", 6);
    };

    // Render the chart
    renderChart();

    // Enhanced legend with ACE Social styling
    const legend = svg
      .append("g")
      .attr("transform", `translate(${isMobile ? 10 : width - 150}, ${isMobile ? height + 30 : 10})`);

    state.visibleMetrics.forEach((metric, index) => {
      const metricConfig = CHART_METRICS[metric.toUpperCase()] || {
        label: metric,
        color: ACE_COLORS.PURPLE
      };

      const legendItem = legend
        .append("g")
        .attr("transform", `translate(0, ${index * 25})`)
        .style("cursor", "pointer")
        .on("click", function() {
          // Toggle metric visibility
          const newVisibleMetrics = state.visibleMetrics.includes(metric)
            ? state.visibleMetrics.filter(m => m !== metric)
            : [...state.visibleMetrics, metric];

          setState(prev => ({ ...prev, visibleMetrics: newVisibleMetrics }));
          announceToScreenReader(`${metric} ${state.visibleMetrics.includes(metric) ? 'hidden' : 'shown'}`);
        });

      // Legend color indicator
      legendItem
        .append("rect")
        .attr("x", 0)
        .attr("y", 0)
        .attr("width", 15)
        .attr("height", 15)
        .attr("rx", 2)
        .attr("fill", metricConfig.color)
        .style("opacity", 0.9);

      // Legend text
      legendItem
        .append("text")
        .attr("x", 20)
        .attr("y", 12.5)
        .text(metricConfig.label)
        .style("font-size", isMobile ? "10px" : "12px")
        .style("font-weight", "500")
        .style("fill", ACE_COLORS.DARK)
        .attr("alignment-baseline", "middle");
    });

    // Enhanced tooltips with accessibility
    const tooltip = d3
      .select(currentChartRef)
      .append("div")
      .attr("class", "d3-tooltip")
      .style("position", "absolute")
      .style("background-color", theme.palette.background.paper)
      .style("color", ACE_COLORS.DARK)
      .style("padding", "12px")
      .style("border-radius", "8px")
      .style("border", `2px solid ${ACE_COLORS.PURPLE}`)
      .style("box-shadow", "0 8px 32px rgba(0,0,0,0.12)")
      .style("pointer-events", "none")
      .style("opacity", 0)
      .style("z-index", 1000)
      .style("font-size", "12px")
      .style("font-weight", "500");

    // Enhanced interactive data points
    if (subscriptionFeatures.hasInteractivity) {
      state.visibleMetrics.forEach((metric) => {
        const yScale = scales[metric];
        const metricConfig = CHART_METRICS[metric.toUpperCase()] || {
          color: ACE_COLORS.PURPLE
        };

        svg
          .selectAll(`.dot-${metric}`)
          .data(processedData)
          .enter()
          .append("circle")
          .attr("class", `dot-${metric}`)
          .attr("cx", (d) => x(d.date))
          .attr("cy", (d) => yScale(d[metric]))
          .attr("r", 4)
          .attr("fill", metricConfig.color)
          .attr("stroke", ACE_COLORS.WHITE)
          .attr("stroke-width", 2)
          .style("opacity", 0)
          .style("cursor", "pointer")
          .on("mouseover", function (event, d) {
            d3.select(this)
              .style("opacity", 1)
              .transition()
              .duration(200)
              .attr("r", 6);

            const tooltipContent = `
              <div style="font-weight: bold; color: ${ACE_COLORS.PURPLE}; margin-bottom: 8px;">
                ${d3.timeFormat("%b %d, %Y")(d.date)}
              </div>
              ${state.visibleMetrics.map(m => {
                const config = CHART_METRICS[m.toUpperCase()] || { label: m, color: ACE_COLORS.PURPLE };
                return `<div style="display: flex; align-items: center; margin-bottom: 4px;">
                  <div style="width: 12px; height: 12px; background-color: ${config.color}; border-radius: 2px; margin-right: 8px;"></div>
                  <span>${config.label}: ${d[m]?.toLocaleString() || 0}</span>
                </div>`;
              }).join('')}
            `;

            tooltip
              .style("opacity", 1)
              .html(tooltipContent)
              .style("left", `${event.pageX + 15}px`)
              .style("top", `${event.pageY - 15}px`);

            // Accessibility announcement
            if (isScreenReaderActive) {
              announceToScreenReader(`Data point: ${d3.timeFormat("%B %d, %Y")(d.date)}, ${metric}: ${d[metric]?.toLocaleString() || 0}`);
            }
          })
          .on("mouseout", function () {
            d3.select(this)
              .style("opacity", 0)
              .transition()
              .duration(200)
              .attr("r", 4);

            tooltip.style("opacity", 0);
          })
          .on("click", function (_, d) {
            if (onDataPointClick) {
              onDataPointClick(d, metric);
            }
            setState(prev => ({ ...prev, selectedDataPoint: { data: d, metric } }));
          });
      });
    }

    // Enhanced responsive behavior
    const resize = () => {
      if (currentChartRef) {
        const newRect = currentChartRef.getBoundingClientRect();
        setState(prev => ({
          ...prev,
          chartDimensions: { width: newRect.width, height: newRect.height }
        }));
      }
    };

    window.addEventListener("resize", resize);

    return () => {
      window.removeEventListener("resize", resize);
      // Clean up tooltips using captured ref value
      if (currentChartRef) {
        d3.select(currentChartRef).selectAll(".d3-tooltip").remove();
      }
    };
  }, [
    chartData.processed,
    state.currentChartType,
    state.visibleMetrics,
    state.loading,
    theme,
    isMobile,
    prefersReducedMotion,
    subscriptionFeatures.hasInteractivity,
    isChartTypeAvailable,
    onDataPointClick,
    announceToScreenReader,
    isScreenReaderActive
  ]);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced chart features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Interactive chart elements',
        'Multiple chart types',
        'Advanced analytics',
        'Data export capabilities',
        'Real-time updates',
        'Custom chart styling'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  // Main render condition checks
  if (state.loading && !chartData.processed.length) {
    return (
      <Box
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: height || 400,
          width: '100%'
        }}
      >
        <CircularProgress
          size={40}
          sx={{ color: ACE_COLORS.PURPLE }}
          aria-label="Loading chart data"
        />
      </Box>
    );
  }

  // Error state
  if (error || Object.keys(state.errors).length > 0) {
    return (
      <Box
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        sx={{ p: 2, height: height || 400 }}
      >
        <Alert
          severity="error"
          sx={{
            backgroundColor: alpha(theme.palette.error.main, 0.1),
            border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
          }}
        >
          <AlertTitle>Chart Error</AlertTitle>
          {error?.message || Object.values(state.errors)[0] || 'Failed to load chart data'}
        </Alert>
      </Box>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Box sx={{ p: 2, textAlign: 'center', height: height || 400 }}>
          <Typography variant="body2" color="error">
            Unable to load performance chart
          </Typography>
        </Box>
      }
    >
      <Box
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        sx={{
          position: 'relative',
          width: '100%',
          height: height || 400,
          backgroundColor: 'transparent'
        }}
      >
        {/* Enhanced Chart Controls */}
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            display: 'flex',
            gap: 1,
            zIndex: 10,
            backgroundColor: alpha(ACE_COLORS.WHITE, 0.9),
            borderRadius: 1,
            p: 0.5,
            backdropFilter: 'blur(5px)'
          }}
        >
          {/* Chart Type Selector */}
          <Tooltip title="Chart Type">
            <IconButton
              size="small"
              onClick={settingsAnchorEl ? () => setSettingsAnchorEl(null) : (e) => setSettingsAnchorEl(e.currentTarget)}
              sx={{
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1)
                }
              }}
              aria-label="Change chart type"
            >
              {state.currentChartType === 'line' && <LineChartIcon fontSize="small" />}
              {state.currentChartType === 'bar' && <BarChartIcon fontSize="small" />}
              {state.currentChartType === 'area' && <TimelineIcon fontSize="small" />}
              {state.currentChartType === 'scatter' && <ScatterPlotIcon fontSize="small" />}
            </IconButton>
          </Tooltip>

          {/* Export Button */}
          {enableExport && (
            <Tooltip title="Export Chart">
              <IconButton
                size="small"
                onClick={handleExportMenuOpen}
                sx={{
                  color: theme.palette.text.secondary,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.1)
                  }
                }}
                aria-label="Export chart data"
              >
                <ExportIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}

          {/* Refresh Button */}
          <Tooltip title="Refresh Data">
            <IconButton
              size="small"
              onClick={handleRefresh}
              disabled={state.refreshing}
              sx={{
                color: theme.palette.text.secondary,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1)
                }
              }}
              aria-label="Refresh chart data"
            >
              {state.refreshing ? (
                <CircularProgress size={16} />
              ) : (
                <RefreshIcon fontSize="small" />
              )}
            </IconButton>
          </Tooltip>
        </Box>

        {/* Real-time Updates Indicator */}
        {realTimeUpdates && subscriptionFeatures.hasRealTime && (
          <Box sx={{
            position: 'absolute',
            top: 8,
            left: 8,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 1,
            backgroundColor: alpha(theme.palette.success.main, 0.1),
            borderRadius: 1,
            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
            zIndex: 10
          }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: theme.palette.success.main,
                ...(!prefersReducedMotion && {
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 }
                  }
                })
              }}
              aria-hidden="true"
            />
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
              Live
            </Typography>
          </Box>
        )}

        {/* Main Chart Container */}
        <Box
          ref={chartRef}
          sx={{
            width: "100%",
            height: "100%",
            position: "relative",
            '& .d3-tooltip': {
              fontFamily: theme.typography.fontFamily
            }
          }}
        />

        {/* Chart Type Menu */}
        <Menu
          anchorEl={settingsAnchorEl}
          open={Boolean(settingsAnchorEl)}
          onClose={() => setSettingsAnchorEl(null)}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 150,
              boxShadow: theme.shadows[8],
              border: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          {Object.values(CHART_TYPES).map((chartTypeConfig) => (
            <MenuItem
              key={chartTypeConfig.id}
              onClick={() => {
                handleChartTypeChange(chartTypeConfig.id);
                setSettingsAnchorEl(null);
              }}
              disabled={!isChartTypeAvailable(chartTypeConfig.id)}
              sx={{
                backgroundColor: state.currentChartType === chartTypeConfig.id
                  ? alpha(ACE_COLORS.PURPLE, 0.1)
                  : 'transparent'
              }}
            >
              <ListItemIcon>
                <chartTypeConfig.icon fontSize="small" sx={{ color: chartTypeConfig.color }} />
              </ListItemIcon>
              <ListItemText>{chartTypeConfig.name}</ListItemText>
              {!isChartTypeAvailable(chartTypeConfig.id) && (
                <UpgradeIcon fontSize="small" sx={{ color: theme.palette.text.disabled, ml: 1 }} />
              )}
            </MenuItem>
          ))}
        </Menu>

        {/* Export Menu */}
        <Menu
          anchorEl={exportAnchorEl}
          open={state.showExportMenu}
          onClose={handleExportMenuClose}
          PaperProps={{
            sx: {
              mt: 1,
              minWidth: 150,
              boxShadow: theme.shadows[8],
              border: `1px solid ${theme.palette.divider}`
            }
          }}
        >
          <MenuItem onClick={() => {
            handleExport('png');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as PNG</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => {
            handleExport('svg');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as SVG</ListItemText>
          </MenuItem>

          {subscriptionFeatures.hasAnalytics && (
            <MenuItem onClick={() => {
              handleExport('pdf');
              handleExportMenuClose();
            }}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export Report (PDF)</ListItemText>
            </MenuItem>
          )}
        </Menu>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={handleUpgradeDialogClose}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 2,
              boxShadow: theme.shadows[16]
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span">
                {getUpgradeDialogContent().title}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {getUpgradeDialogContent().content}
              </Typography>

              <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                Unlock Advanced Chart Features:
              </Typography>

              <Grid container spacing={1}>
                {getUpgradeDialogContent().features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                      <Typography variant="body2">{feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Alert
              severity="info"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
              Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced chart features.
            </Alert>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleUpgradeDialogClose}
              sx={{ color: theme.palette.text.secondary }}
            >
              Maybe Later
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                }
              }}
              startIcon={<UpgradeIcon />}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
PerformanceChart.propTypes = {
  // Core props
  data: PropTypes.arrayOf(
    PropTypes.shape({
      date: PropTypes.string.isRequired,
      impressions: PropTypes.number,
      engagements: PropTypes.number,
      clicks: PropTypes.number,
      reach: PropTypes.number,
      shares: PropTypes.number
    })
  ),
  chartType: PropTypes.oneOf(['line', 'bar', 'area', 'scatter']),

  height: PropTypes.number,
  loading: PropTypes.bool,
  error: PropTypes.object,

  // Enhanced props
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onChartTypeChange: PropTypes.func,
  onDataPointClick: PropTypes.func,
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  metrics: PropTypes.array,


  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

PerformanceChart.defaultProps = {
  data: [],
  chartType: 'line',
  height: 400,
  loading: false,
  error: null,
  enableExport: false,
  realTimeUpdates: false,
  metrics: ['impressions', 'engagements'],
  className: '',
  style: {},
  testId: 'performance-chart'
};

// Display name for debugging
PerformanceChart.displayName = 'PerformanceChart';

export default PerformanceChart;

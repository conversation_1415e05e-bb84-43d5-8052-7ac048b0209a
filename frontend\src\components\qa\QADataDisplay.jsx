/**
 * QA Data Display Component
 * 
 * A comprehensive component for displaying and testing data retrieval
 * from backend APIs with proper loading states, error handling, and data validation.
 @since 2024-1-1 to 2025-25-7
*/

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  Alert,
  CircularProgress,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip,
  Badge,
  LinearProgress
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useApiError } from '../../hooks/useApiError';
import { useNotification } from '../../hooks/useNotification';

const QADataDisplay = ({ 
  dataSource, 
  dataType = 'content', // 'content', 'campaigns', 'messages', 'users'
  onItemAction = null,
  refreshInterval = 30000, // 30 seconds
  enableRealTimeUpdates = false
}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [dataStats, setDataStats] = useState({});
  const { handleApiError } = useApiError();
  const { showNotification } = useNotification();

  // Fetch data from the provided data source
  const fetchData = useCallback(async (showLoadingIndicator = true) => {
    try {
      if (showLoadingIndicator) {
        setLoading(true);
      }
      setError(null);

      const response = await dataSource();
      
      // Validate response structure
      if (!response || typeof response !== 'object') {
        throw new Error('Invalid response format');
      }

      const items = Array.isArray(response) ? response : response.data || response.items || [];
      
      // Validate each item has required fields
      const validatedItems = items.map((item, index) => {
        if (!item.id && !item._id) {
          console.warn(`Item at index ${index} missing ID field`);
          return { ...item, id: `temp_${index}` };
        }
        return {
          ...item,
          id: item.id || item._id,
          displayId: item.id || item._id,
          lastValidated: new Date().toISOString()
        };
      });

      setData(validatedItems);
      setLastUpdated(new Date());
      
      // Calculate data statistics
      const stats = calculateDataStats(validatedItems);
      setDataStats(stats);
      
      if (showLoadingIndicator) {
        showNotification(`${dataType} data loaded successfully`, 'success');
      }
      
    } catch (err) {
      console.error('QA Data Display fetch error:', err);
      setError(err.message || 'Failed to fetch data');
      handleApiError(err);
    } finally {
      setLoading(false);
    }
  }, [dataSource, dataType, handleApiError, showNotification]);

  // Calculate statistics for the data
  const calculateDataStats = (items) => {
    const stats = {
      total: items.length,
      byStatus: {},
      byType: {},
      recentItems: 0,
      validationErrors: 0
    };

    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    items.forEach(item => {
      // Count by status
      const status = item.status || 'unknown';
      stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;

      // Count by type/category
      const type = item.type || item.category || 'unknown';
      stats.byType[type] = (stats.byType[type] || 0) + 1;

      // Count recent items
      const createdAt = new Date(item.created_at || item.createdAt || 0);
      if (createdAt > oneDayAgo) {
        stats.recentItems++;
      }

      // Count validation errors
      if (item.validation_errors || item.validationErrors) {
        stats.validationErrors++;
      }
    });

    return stats;
  };

  // Handle item actions (view, edit, delete)
  const handleItemAction = async (action, item) => {
    try {
      if (onItemAction) {
        await onItemAction(action, item);
        
        // Refresh data after action
        if (action === 'delete' || action === 'update') {
          await fetchData(false);
        }
      }
      
      showNotification(`${action} action completed for ${item.title || item.name || item.id}`, 'success');
    } catch (err) {
      console.error(`Action ${action} failed:`, err);
      handleApiError(err);
    }
  };

  // Export data to JSON
  const handleExportData = () => {
    const exportData = {
      dataType,
      exportedAt: new Date().toISOString(),
      stats: dataStats,
      items: data
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `qa_${dataType}_data_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification('Data exported successfully', 'success');
  };

  // Get status color for chips
  const getStatusColor = (status) => {
    const statusColors = {
      active: 'success',
      published: 'success',
      completed: 'success',
      draft: 'warning',
      pending: 'warning',
      scheduled: 'info',
      failed: 'error',
      error: 'error',
      inactive: 'default'
    };
    return statusColors[status?.toLowerCase()] || 'default';
  };

  // Get validation status icon
  const getValidationIcon = (item) => {
    if (item.validation_errors || item.validationErrors) {
      return <ErrorIcon color="error" />;
    }
    if (item.warnings && item.warnings.length > 0) {
      return <WarningIcon color="warning" />;
    }
    return <CheckCircleIcon color="success" />;
  };

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Set up refresh interval
  useEffect(() => {
    if (enableRealTimeUpdates && refreshInterval > 0) {
      const interval = setInterval(() => {
        fetchData(false);
      }, refreshInterval);
      
      return () => clearInterval(interval);
    }
  }, [fetchData, enableRealTimeUpdates, refreshInterval]);

  if (loading && data.length === 0) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
            <CircularProgress />
            <Typography variant="body1" sx={{ ml: 2 }}>
              Loading {dataType} data...
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="h6">
              QA Data Display - {dataType.charAt(0).toUpperCase() + dataType.slice(1)}
            </Typography>
            <Box>
              <Tooltip title="Refresh Data">
                <IconButton onClick={() => fetchData()} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Export Data">
                <IconButton onClick={handleExportData}>
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        }
        subheader={
          <Box>
            <Typography variant="body2" color="text.secondary">
              Last updated: {lastUpdated ? lastUpdated.toLocaleString() : 'Never'}
            </Typography>
            {loading && <LinearProgress sx={{ mt: 1 }} />}
          </Box>
        }
      />
      
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Error loading data: {error}
          </Alert>
        )}

        {/* Data Statistics */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Data Statistics</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h4" color="primary">
                      {dataStats.total || 0}
                    </Typography>
                    <Typography variant="body2">Total Items</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h4" color="success.main">
                      {dataStats.recentItems || 0}
                    </Typography>
                    <Typography variant="body2">Recent (24h)</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h4" color="error.main">
                      {dataStats.validationErrors || 0}
                    </Typography>
                    <Typography variant="body2">Validation Errors</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" flexWrap="wrap" gap={1}>
                      {Object.entries(dataStats.byStatus || {}).map(([status, count]) => (
                        <Chip
                          key={status}
                          label={`${status}: ${count}`}
                          color={getStatusColor(status)}
                          size="small"
                        />
                      ))}
                    </Box>
                    <Typography variant="body2" sx={{ mt: 1 }}>Status Distribution</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Data Table */}
        <TableContainer component={Paper} sx={{ mt: 2 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Title/Name</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Validation</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.map((item) => (
                <TableRow key={item.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontFamily="monospace">
                      {item.displayId?.slice(-8) || 'N/A'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1">
                      {item.title || item.name || 'Untitled'}
                    </Typography>
                    {item.description && (
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {item.description.slice(0, 50)}...
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={item.status || 'unknown'}
                      color={getStatusColor(item.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {item.created_at || item.createdAt 
                        ? new Date(item.created_at || item.createdAt).toLocaleDateString()
                        : 'Unknown'
                      }
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Validation Status">
                      {getValidationIcon(item)}
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={1}>
                      <Tooltip title="View">
                        <IconButton
                          size="small"
                          onClick={() => handleItemAction('view', item)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          onClick={() => handleItemAction('edit', item)}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleItemAction('delete', item)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {data.length === 0 && !loading && (
          <Box textAlign="center" py={4}>
            <Typography variant="body1" color="text.secondary">
              No {dataType} data available
            </Typography>
            <Button
              variant="outlined"
              onClick={() => fetchData()}
              sx={{ mt: 2 }}
              startIcon={<RefreshIcon />}
            >
              Refresh
            </Button>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default QADataDisplay;

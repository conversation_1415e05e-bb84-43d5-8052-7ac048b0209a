// @since 2024-1-1 to 2025-25-7
import { defineConfig, devices } from '@playwright/test';

/**
 * Mobile responsiveness testing configuration
 * Tests across different mobile breakpoints: 320px, 768px, 1024px+
 */
export default defineConfig({
  testDir: './tests/mobile',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'test-results/mobile-responsive' }],
    ['json', { outputFile: 'test-results/mobile-results.json' }]
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },

  projects: [
    {
      name: 'Mobile Chrome 320px',
      use: { 
        ...devices['iPhone SE'],
        viewport: { width: 320, height: 568 }
      },
    },
    {
      name: 'Mobile Chrome 375px',
      use: { 
        ...devices['iPhone 12'],
        viewport: { width: 375, height: 812 }
      },
    },
    {
      name: 'Tablet 768px',
      use: { 
        ...devices['iPad'],
        viewport: { width: 768, height: 1024 }
      },
    },
    {
      name: 'Desktop 1024px',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1024, height: 768 }
      },
    },
    {
      name: 'Desktop 1440px',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1440, height: 900 }
      },
    },
  ],

  webServer: {
    command: 'npm run preview',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
});

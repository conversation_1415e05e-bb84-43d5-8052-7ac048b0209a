/**
 * Enhanced ACE Social Template List - Enterprise-grade email template listing component
 * Features: Comprehensive template listing with advanced filtering, searching, and sorting
 * capabilities for ACE Social email templates, detailed template management dashboard with
 * template cards and grid/list view options, advanced list features with bulk operations
 * and template categorization, ACE Social's email system integration with seamless template
 * lifecycle management, list interaction features including multi-select functionality
 * and context menus, list state management with view preferences and filter persistence,
 * and real-time list monitoring with live template updates and automatic list optimization
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 */

import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  IconButton,
  Chip,
  Grid,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Skeleton,
  Alert,
  Menu,
  ListItemIcon,
  ListItemText,
  MenuItem as MenuItemComponent,
  Stack,
  Paper,
  Badge,
  CircularProgress,
  Checkbox,
  FormControlLabel,
  ButtonGroup,
  Divider,
  Collapse,
  useTheme,
  useMediaQuery,
  alpha,
  Fade,
  Zoom,
  Slide
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as DuplicateIcon,
  Preview as PreviewIcon,
  Send as SendIcon,
  Visibility as ViewIcon,
  ViewList as ListViewIcon,
  ViewModule as GridViewIcon,
  Sort as SortIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  SelectAll as SelectAllIcon,
  Clear as ClearIcon,
  Analytics as AnalyticsIcon,
  Assessment as AssessmentIcon,
  Speed as SpeedIcon,
  Insights as InsightsIcon,
  Security as SecurityIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as CheckCircleIcon,
  Settings as SettingsIcon,
  MonetizationOn as MoneyIcon,
  Group as GroupIcon,
  Star as StarIcon,
  History as HistoryIcon
} from '@mui/icons-material';

// Enhanced accessibility hook
const useEnhancedAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  return { announceToScreenReader };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Template list constants
const VIEW_MODES = {
  GRID: 'grid',
  LIST: 'list',
  COMPACT: 'compact'
};

const SORT_OPTIONS = {
  NAME_ASC: 'name_asc',
  NAME_DESC: 'name_desc',
  CREATED_ASC: 'created_asc',
  CREATED_DESC: 'created_desc',
  USAGE_ASC: 'usage_asc',
  USAGE_DESC: 'usage_desc',
  TYPE_ASC: 'type_asc',
  TYPE_DESC: 'type_desc'
};

const TEMPLATE_STATUSES = {
  ACTIVE: 'active',
  DRAFT: 'draft',
  ARCHIVED: 'archived',
  TESTING: 'testing'
};

const TEMPLATE_TYPES = {
  TRANSACTIONAL: 'transactional',
  MARKETING: 'marketing',
  SYSTEM: 'system',
  NOTIFICATION: 'notification'
};

// List analytics events
const LIST_ANALYTICS_EVENTS = {
  VIEW_CHANGED: 'template_list_view_changed',
  TEMPLATE_SELECTED: 'template_selected',
  BULK_ACTION: 'template_bulk_action',
  FILTER_APPLIED: 'template_filter_applied',
  SEARCH_PERFORMED: 'template_search_performed',
  SORT_CHANGED: 'template_sort_changed',
  EXPORT_TRIGGERED: 'template_export_triggered'
};

/**
 * Enhanced Template List - Comprehensive template listing with advanced features
 * Implements detailed template list management and enterprise-grade listing capabilities
 */

const EnhancedTemplateList = memo(forwardRef(({
  templates = [],
  loading = false,
  error,
  className,
  enableAdvancedFeatures = true,
  enableBulkOperations = true,
  enableRealTimeSync = true,
  enableAnalytics = true,
  enableAccessibility = true,
  enableExportOptions = true,
  defaultViewMode = VIEW_MODES.GRID,
  defaultSortOption = SORT_OPTIONS.CREATED_DESC,
  autoRefreshInterval = 300000, // 5 minutes
  maxDisplayTemplates = 1000,
  onEdit,
  onDuplicate,
  onDelete,
  onPreview,
  onAnalytics,
  onBulkAction,
  onTemplateSelect,
  onViewModeChange,
  onSortChange,
  onFilterChange,
  onSearchChange,
  onExportData,
  onAnalyticsTrack,
  onRefresh,
  ...props
}, ref) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Enhanced accessibility
  const { announceToScreenReader } = useEnhancedAccessibility();

  // Refs for component control
  const listRef = useRef(null);
  const searchRef = useRef(null);
  const menuRef = useRef(null);
  const autoRefreshTimeoutRef = useRef(null);

  // Core state management
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  // Enhanced state management
  const [viewMode, setViewMode] = useState(defaultViewMode);
  const [sortOption, setSortOption] = useState(defaultSortOption);
  const [selectedTemplates, setSelectedTemplates] = useState(new Set());
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [bulkActionMenuAnchor, setBulkActionMenuAnchor] = useState(null);
  const [listAnalytics, setListAnalytics] = useState({
    viewChanges: 0,
    searchQueries: 0,
    filterApplications: 0,
    bulkActions: 0
  });

  // useImperativeHandle for external component control
  useImperativeHandle(ref, () => ({
    // Core methods
    refreshTemplates: () => handleRefresh(),
    exportData: () => handleExport(),
    resetFilters: () => handleResetFilters(),
    focusList: () => listRef.current?.focus(),

    // Navigation methods
    changeViewMode: (mode) => handleViewModeChange(mode),
    changeSortOption: (option) => handleSortChange(option),
    searchTemplates: (term) => handleSearchChange(term),

    // Data methods
    getFilteredTemplates: () => filteredTemplates,
    getSelectedTemplates: () => Array.from(selectedTemplates),
    getListAnalytics: () => listAnalytics,
    getCurrentViewMode: () => viewMode,

    // State methods
    isLoading: () => loading,
    hasError: () => !!error,
    getTemplateCount: () => filteredTemplates.length,

    // Selection methods
    selectTemplate: (templateId) => handleTemplateSelect(templateId),
    selectAllTemplates: () => handleSelectAll(),
    clearSelection: () => setSelectedTemplates(new Set()),

    // Bulk operations
    performBulkAction: (action) => handleBulkAction(action),
    duplicateSelected: () => handleBulkDuplicate(),
    deleteSelected: () => handleBulkDelete(),

    // Analytics methods
    getUsageStats: () => generateUsageStats(),
    getPopularTemplates: () => getPopularTemplates(),

    // Accessibility methods
    announceStatus: (message) => announceToScreenReader(message),

    // Advanced methods
    toggleAdvancedFilters: () => setShowAdvancedFilters(!showAdvancedFilters),
    generateReport: () => generateTemplateReport()
  }), [
    filteredTemplates,
    selectedTemplates,
    listAnalytics,
    viewMode,
    loading,
    error,
    showAdvancedFilters,
    announceToScreenReader
  ]);

  // Enhanced glass morphism styles with ACE Social branding
  const glassMorphismStyles = useMemo(() => ({
    background: `linear-gradient(135deg,
      ${alpha(theme.palette.background.paper, 0.95)} 0%,
      ${alpha(theme.palette.background.default, 0.85)} 100%)`,
    backdropFilter: 'blur(15px)',
    border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 8px 32px 0 ${alpha(theme.palette.common.black, 0.12)}`,
    transition: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)'
  }), [theme]);

  // Enhanced analytics tracking
  useEffect(() => {
    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(LIST_ANALYTICS_EVENTS.VIEW_CHANGED, {
        viewMode,
        timestamp: new Date().toISOString(),
        templatesCount: templates.length
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Template list view changed to ${viewMode} mode`);
    }
  }, [viewMode, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader, templates.length]);

  // Auto-refresh effect
  useEffect(() => {
    if (enableRealTimeSync && autoRefreshInterval > 0) {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }

      autoRefreshTimeoutRef.current = setTimeout(() => {
        if (onRefresh) {
          onRefresh();
        }
      }, autoRefreshInterval);

      return () => {
        if (autoRefreshTimeoutRef.current) {
          clearTimeout(autoRefreshTimeoutRef.current);
        }
      };
    }
  }, [enableRealTimeSync, autoRefreshInterval, onRefresh]);

  // Enhanced handler functions
  const handleMenuOpen = useCallback((event, template) => {
    setAnchorEl(event.currentTarget);
    setSelectedTemplate(template);

    if (enableAccessibility) {
      announceToScreenReader(`Opened menu for template ${template.name}`);
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedTemplate(null);

    if (enableAccessibility) {
      announceToScreenReader('Template menu closed');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleMenuAction = useCallback((action) => {
    if (selectedTemplate) {
      switch (action) {
        case 'edit':
          if (onEdit) onEdit(selectedTemplate);
          break;
        case 'duplicate':
          if (onDuplicate) onDuplicate(selectedTemplate);
          break;
        case 'delete':
          if (onDelete) onDelete(selectedTemplate.id);
          break;
        case 'preview':
          if (onPreview) onPreview(selectedTemplate);
          break;
        case 'analytics':
          if (onAnalytics) onAnalytics(selectedTemplate);
          break;
        default:
          break;
      }

      if (enableAnalytics && onAnalyticsTrack) {
        onAnalyticsTrack(LIST_ANALYTICS_EVENTS.TEMPLATE_SELECTED, {
          action,
          templateId: selectedTemplate.id,
          timestamp: new Date().toISOString()
        });
      }

      if (enableAccessibility) {
        announceToScreenReader(`${action} action performed on template ${selectedTemplate.name}`);
      }
    }
    handleMenuClose();
  }, [selectedTemplate, onEdit, onDuplicate, onDelete, onPreview, onAnalytics, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader, handleMenuClose]);

  // Enhanced view mode handler
  const handleViewModeChange = useCallback((newMode) => {
    setViewMode(newMode);

    setListAnalytics(prev => ({
      ...prev,
      viewChanges: prev.viewChanges + 1
    }));

    if (onViewModeChange) {
      onViewModeChange(newMode);
    }

    if (enableAccessibility) {
      announceToScreenReader(`View mode changed to ${newMode}`);
    }
  }, [onViewModeChange, enableAccessibility, announceToScreenReader]);

  // Enhanced sort handler
  const handleSortChange = useCallback((newSort) => {
    setSortOption(newSort);

    if (onSortChange) {
      onSortChange(newSort);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(LIST_ANALYTICS_EVENTS.SORT_CHANGED, {
        sortOption: newSort,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Sort option changed to ${newSort}`);
    }
  }, [onSortChange, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced search handler
  const handleSearchChange = useCallback((term) => {
    setSearchTerm(term);

    setListAnalytics(prev => ({
      ...prev,
      searchQueries: prev.searchQueries + 1
    }));

    if (onSearchChange) {
      onSearchChange(term);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(LIST_ANALYTICS_EVENTS.SEARCH_PERFORMED, {
        searchTerm: term.substring(0, 50), // Limit for privacy
        timestamp: new Date().toISOString()
      });
    }
  }, [onSearchChange, enableAnalytics, onAnalyticsTrack]);

  // Enhanced filter handlers
  const handleFilterChange = useCallback((filterType, value) => {
    if (filterType === 'type') {
      setFilterType(value);
    } else if (filterType === 'status') {
      setFilterStatus(value);
    }

    setListAnalytics(prev => ({
      ...prev,
      filterApplications: prev.filterApplications + 1
    }));

    if (onFilterChange) {
      onFilterChange(filterType, value);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(LIST_ANALYTICS_EVENTS.FILTER_APPLIED, {
        filterType,
        value,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Applied ${filterType} filter: ${value}`);
    }
  }, [onFilterChange, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced template selection handlers
  const handleTemplateSelect = useCallback((templateId) => {
    setSelectedTemplates(prev => {
      const newSelection = new Set(prev);
      if (newSelection.has(templateId)) {
        newSelection.delete(templateId);
      } else {
        newSelection.add(templateId);
      }

      if (onTemplateSelect) {
        onTemplateSelect(Array.from(newSelection));
      }

      if (enableAccessibility) {
        announceToScreenReader(`Template ${newSelection.has(templateId) ? 'selected' : 'deselected'}`);
      }

      return newSelection;
    });
  }, [onTemplateSelect, enableAccessibility, announceToScreenReader]);

  const handleSelectAll = useCallback(() => {
    const allIds = filteredTemplates.map(template => template.id);
    setSelectedTemplates(new Set(allIds));

    if (onTemplateSelect) {
      onTemplateSelect(allIds);
    }

    if (enableAccessibility) {
      announceToScreenReader(`Selected all ${allIds.length} templates`);
    }
  }, [filteredTemplates, onTemplateSelect, enableAccessibility, announceToScreenReader]);

  // Enhanced bulk operation handlers
  const handleBulkAction = useCallback((action) => {
    const selectedIds = Array.from(selectedTemplates);

    if (selectedIds.length === 0) return;

    setListAnalytics(prev => ({
      ...prev,
      bulkActions: prev.bulkActions + 1
    }));

    if (onBulkAction) {
      onBulkAction(action, selectedIds);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(LIST_ANALYTICS_EVENTS.BULK_ACTION, {
        action,
        templateCount: selectedIds.length,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Bulk ${action} performed on ${selectedIds.length} templates`);
    }
  }, [selectedTemplates, onBulkAction, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  const handleBulkDuplicate = useCallback(() => {
    handleBulkAction('duplicate');
  }, [handleBulkAction]);

  const handleBulkDelete = useCallback(() => {
    handleBulkAction('delete');
  }, [handleBulkAction]);

  // Enhanced utility functions
  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
    }

    if (enableAccessibility) {
      announceToScreenReader('Template list refreshed');
    }
  }, [onRefresh, enableAccessibility, announceToScreenReader]);

  const handleResetFilters = useCallback(() => {
    setSearchTerm('');
    setFilterType('');
    setFilterStatus('');

    if (enableAccessibility) {
      announceToScreenReader('All filters reset');
    }
  }, [enableAccessibility, announceToScreenReader]);

  const handleExport = useCallback(() => {
    const exportData = selectedTemplates.size > 0
      ? filteredTemplates.filter(template => selectedTemplates.has(template.id))
      : filteredTemplates;

    if (onExportData) {
      onExportData(exportData);
    }

    if (enableAnalytics && onAnalyticsTrack) {
      onAnalyticsTrack(LIST_ANALYTICS_EVENTS.EXPORT_TRIGGERED, {
        exportedCount: exportData.length,
        selectedOnly: selectedTemplates.size > 0,
        timestamp: new Date().toISOString()
      });
    }

    if (enableAccessibility) {
      announceToScreenReader(`Exported ${exportData.length} templates`);
    }
  }, [selectedTemplates, filteredTemplates, onExportData, enableAnalytics, enableAccessibility, onAnalyticsTrack, announceToScreenReader]);

  // Enhanced utility functions
  const generateUsageStats = useCallback(() => {
    if (!templates.length) return null;

    const stats = templates.reduce((acc, template) => {
      acc.totalUsage += template.usage_count || 0;
      acc.totalTemplates += 1;

      if (template.status === 'active') acc.activeTemplates += 1;
      if (template.template_type === 'marketing') acc.marketingTemplates += 1;

      return acc;
    }, {
      totalUsage: 0,
      totalTemplates: 0,
      activeTemplates: 0,
      marketingTemplates: 0
    });

    return {
      ...stats,
      avgUsage: stats.totalTemplates > 0 ? (stats.totalUsage / stats.totalTemplates).toFixed(1) : 0,
      activePercentage: stats.totalTemplates > 0 ? ((stats.activeTemplates / stats.totalTemplates) * 100).toFixed(1) : 0
    };
  }, [templates]);

  const getPopularTemplates = useCallback((limit = 5) => {
    return [...templates]
      .sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0))
      .slice(0, limit);
  }, [templates]);

  const generateTemplateReport = useCallback(() => {
    const stats = generateUsageStats();
    const popular = getPopularTemplates();

    return {
      summary: stats,
      popularTemplates: popular,
      totalTemplates: templates.length,
      generatedAt: new Date().toISOString()
    };
  }, [generateUsageStats, getPopularTemplates, templates.length]);

  const getStatusColor = useCallback((status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'draft':
        return 'warning';
      case 'archived':
        return 'default';
      case 'testing':
        return 'info';
      default:
        return 'default';
    }
  }, []);

  const getTypeColor = useCallback((type) => {
    switch (type) {
      case 'transactional':
        return 'primary';
      case 'marketing':
        return 'secondary';
      case 'system':
        return 'error';
      case 'notification':
        return 'info';
      default:
        return 'default';
    }
  }, []);

  // Enhanced filtering and sorting
  const filteredTemplates = useMemo(() => {
    let filtered = [...templates];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(template =>
        template.name?.toLowerCase().includes(term) ||
        template.description?.toLowerCase().includes(term) ||
        template.template_type?.toLowerCase().includes(term)
      );
    }

    // Apply type filter
    if (filterType) {
      filtered = filtered.filter(template => template.template_type === filterType);
    }

    // Apply status filter
    if (filterStatus) {
      filtered = filtered.filter(template => template.status === filterStatus);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortOption) {
        case SORT_OPTIONS.NAME_ASC:
          return a.name.localeCompare(b.name);
        case SORT_OPTIONS.NAME_DESC:
          return b.name.localeCompare(a.name);
        case SORT_OPTIONS.CREATED_ASC:
          return new Date(a.created_at) - new Date(b.created_at);
        case SORT_OPTIONS.CREATED_DESC:
          return new Date(b.created_at) - new Date(a.created_at);
        case SORT_OPTIONS.USAGE_ASC:
          return (a.usage_count || 0) - (b.usage_count || 0);
        case SORT_OPTIONS.USAGE_DESC:
          return (b.usage_count || 0) - (a.usage_count || 0);
        case SORT_OPTIONS.TYPE_ASC:
          return a.template_type.localeCompare(b.template_type);
        case SORT_OPTIONS.TYPE_DESC:
          return b.template_type.localeCompare(a.template_type);
        default:
          return 0;
      }
    });

    // Limit results for performance
    if (maxDisplayTemplates && filtered.length > maxDisplayTemplates) {
      filtered = filtered.slice(0, maxDisplayTemplates);
    }

    return filtered;
  }, [templates, searchTerm, filterType, filterStatus, sortOption, maxDisplayTemplates]);

  const renderSkeleton = useCallback(() => (
    <Grid container spacing={3}>
      {[...Array(6)].map((_, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Card variant="glass">
            <CardContent>
              <Skeleton variant="text" width="60%" height={32} />
              <Skeleton variant="text" width="100%" height={20} sx={{ mt: 1 }} />
              <Skeleton variant="text" width="80%" height={20} />
              <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                <Skeleton variant="rounded" width={60} height={24} />
                <Skeleton variant="rounded" width={80} height={24} />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Skeleton variant="text" width="40%" height={20} />
                <Skeleton variant="circular" width={32} height={32} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  ), []);

  const renderEmptyState = useCallback(() => {
    const hasFilters = searchTerm || filterType || filterStatus;

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          py: 8,
          textAlign: 'center'
        }}
      >
        {/* Friendly illustration using emoji */}
        <Box
          sx={{
            fontSize: '4rem',
            mb: 2,
            opacity: 0.6,
            filter: 'grayscale(20%)'
          }}
        >
          {hasFilters ? '🔍' : '📧'}
        </Box>

        <Typography variant="h6" color="text.secondary" gutterBottom>
          {hasFilters ? 'No templates match your search' : 'No email templates found'}
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 3, maxWidth: 400 }}>
          {hasFilters
            ? 'Try adjusting your search criteria or filters to find what you\'re looking for.'
            : 'Get started by creating your first email template. You can design beautiful, responsive emails for your campaigns.'
          }
        </Typography>

        {hasFilters ? (
          <Button
            variant="outlined"
            onClick={() => {
              setSearchTerm('');
              setFilterType('');
              setFilterStatus('');
            }}
            sx={{
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                borderColor: 'primary.dark',
                backgroundColor: 'primary.light',
              }
            }}
          >
            Clear Filters
          </Button>
        ) : (
          <Button
            variant="contained"
            startIcon={<EditIcon />}
            onClick={() => onEdit(null)}
            sx={{
              background: 'linear-gradient(135deg, #6C4BFA 0%, #9B85FF 100%)',
              boxShadow: '0 8px 32px 0 rgba(108, 75, 250, 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5A3FE8 0%, #8973ED 100%)',
                transform: 'translateY(-2px)',
                boxShadow: '0 12px 40px 0 rgba(108, 75, 250, 0.4)',
              }
            }}
          >
            Create Your First Template
          </Button>
        )}
      </Box>
    );
  }, [searchTerm, filterType, filterStatus, onEdit]);

  if (loading) {
    return renderSkeleton();
  }

  return (
    <Box>
      {/* Filters and Search */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                }
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                label="Type"
                onChange={(e) => setFilterType(e.target.value)}
                sx={{ borderRadius: 2 }}
              >
                <MenuItem value="">All Types</MenuItem>
                <MenuItem value="transactional">Transactional</MenuItem>
                <MenuItem value="marketing">Marketing</MenuItem>
                <MenuItem value="system">System</MenuItem>
                <MenuItem value="notification">Notification</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                label="Status"
                onChange={(e) => setFilterStatus(e.target.value)}
                sx={{ borderRadius: 2 }}
              >
                <MenuItem value="">All Statuses</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="draft">Draft</MenuItem>
                <MenuItem value="testing">Testing</MenuItem>
                <MenuItem value="archived">Archived</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Typography variant="body2" color="text.secondary">
              {filteredTemplates.length} template{filteredTemplates.length !== 1 ? 's' : ''}
            </Typography>
          </Grid>
        </Grid>
      </Box>

      {/* Template Grid */}
      {filteredTemplates.length === 0 ? (
        renderEmptyState()
      ) : (
        <Grid container spacing={3}>
          {filteredTemplates.map((template) => (
            <Grid item xs={12} sm={6} md={4} key={template.id}>
              <Card 
                variant="glass" 
                sx={{ 
                  height: '100%',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px 0 rgba(31, 38, 135, 0.2)',
                  }
                }}
              >
                <CardContent>
                  {/* Header */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" component="h3" sx={{ fontWeight: 600 }}>
                      {template.name}
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuOpen(e, template)}
                      sx={{ ml: 1 }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>

                  {/* Description */}
                  <Typography 
                    variant="body2" 
                    color="text.secondary" 
                    sx={{ 
                      mb: 2,
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      minHeight: '2.5em'
                    }}
                  >
                    {template.description || 'No description provided'}
                  </Typography>

                  {/* Tags */}
                  <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                    <Chip
                      label={template.template_type}
                      size="small"
                      color={getTypeColor(template.template_type)}
                      variant="outlined"
                    />
                    <Chip
                      label={template.status}
                      size="small"
                      color={getStatusColor(template.status)}
                    />
                  </Box>

                  {/* Stats */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
                    <Typography variant="caption" color="text.secondary">
                      Used {template.usage_count || 0} times
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      v{template.version || 1}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            minWidth: 160,
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.2)',
          }
        }}
      >
        <MenuItemComponent onClick={() => handleMenuAction('edit')}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItemComponent>
        <MenuItemComponent onClick={() => handleMenuAction('duplicate')}>
          <ListItemIcon>
            <DuplicateIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Duplicate</ListItemText>
        </MenuItemComponent>
        <MenuItemComponent 
          onClick={() => handleMenuAction('delete')}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItemComponent>
      </Menu>
    </Box>
  );
}));

// Enhanced PropTypes validation with comprehensive type checking
EnhancedTemplateList.propTypes = {
  // Core props
  templates: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string,
    template_type: PropTypes.oneOf(Object.values(TEMPLATE_TYPES)).isRequired,
    status: PropTypes.oneOf(Object.values(TEMPLATE_STATUSES)).isRequired,
    usage_count: PropTypes.number,
    version: PropTypes.number,
    created_at: PropTypes.string,
    updated_at: PropTypes.string
  })),
  loading: PropTypes.bool,
  error: PropTypes.string,

  // Enhanced props
  className: PropTypes.string,
  enableAdvancedFeatures: PropTypes.bool,
  enableBulkOperations: PropTypes.bool,
  enableRealTimeSync: PropTypes.bool,
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableExportOptions: PropTypes.bool,

  // Configuration
  defaultViewMode: PropTypes.oneOf(Object.values(VIEW_MODES)),
  defaultSortOption: PropTypes.oneOf(Object.values(SORT_OPTIONS)),
  autoRefreshInterval: PropTypes.number,
  maxDisplayTemplates: PropTypes.number,

  // Callback props
  onEdit: PropTypes.func,
  onDuplicate: PropTypes.func,
  onDelete: PropTypes.func,
  onPreview: PropTypes.func,
  onAnalytics: PropTypes.func,
  onBulkAction: PropTypes.func,
  onTemplateSelect: PropTypes.func,
  onViewModeChange: PropTypes.func,
  onSortChange: PropTypes.func,
  onFilterChange: PropTypes.func,
  onSearchChange: PropTypes.func,
  onExportData: PropTypes.func,
  onAnalyticsTrack: PropTypes.func,
  onRefresh: PropTypes.func
};

// Default props
EnhancedTemplateList.defaultProps = {
  templates: [],
  loading: false,
  error: null,
  className: '',
  enableAdvancedFeatures: true,
  enableBulkOperations: true,
  enableRealTimeSync: true,
  enableAnalytics: true,
  enableAccessibility: true,
  enableExportOptions: true,
  defaultViewMode: VIEW_MODES.GRID,
  defaultSortOption: SORT_OPTIONS.CREATED_DESC,
  autoRefreshInterval: 300000,
  maxDisplayTemplates: 1000,
  onEdit: null,
  onDuplicate: null,
  onDelete: null,
  onPreview: null,
  onAnalytics: null,
  onBulkAction: null,
  onTemplateSelect: null,
  onViewModeChange: null,
  onSortChange: null,
  onFilterChange: null,
  onSearchChange: null,
  onExportData: null,
  onAnalyticsTrack: null,
  onRefresh: null
};

// Display name for debugging
EnhancedTemplateList.displayName = 'EnhancedTemplateList';

export default EnhancedTemplateList;

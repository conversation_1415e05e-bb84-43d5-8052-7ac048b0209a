// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Create as CreateIcon,
  Schedule as ScheduleIcon,
  Analytics as AnalyticsIcon,
  People as PeopleIcon,
  Storage as StorageIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';

import { useAuth } from '../../hooks/useAuth';

import api from '../../api';

const UsageDashboard = ({ isEmbedded = false, onError }) => {
  const theme = useTheme();
  const { user } = useAuth();


  const [usage, setUsage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);



  useEffect(() => {
    const fetchUsage = async () => {
      try {
        setLoading(true);
        
        // Fetch usage data from API
        const response = await api.get('/api/billing/usage');
        setUsage(response.data || {});
        setLoading(false);
        
      } catch (error) {
        console.error('Error fetching usage:', error);
        setError('Failed to load usage data');
        if (onError) onError(error);
        setLoading(false);
      }
    };

    fetchUsage();
  }, [user, onError]);

  const getUsagePercentage = (used, limit) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  const getUsageColor = (percentage) => {
    if (percentage >= 90) return 'error';
    if (percentage >= 75) return 'warning';
    return 'primary';
  };

  const getUsageIcon = (key) => {
    const icons = {
      posts_generated: CreateIcon,
      social_accounts: PeopleIcon,
      analytics_reports: AnalyticsIcon,
      storage: StorageIcon,
      team_members: PeopleIcon,
      ab_tests: TrendingUpIcon,
      competitor_analysis: AnalyticsIcon,
      api_calls: StorageIcon,
      white_label: CreateIcon
    };
    return icons[key] || TrendingUpIcon;
  };

  const formatUsageLabel = (key) => {
    const labels = {
      posts_generated: 'Posts Generated',
      social_accounts: 'Social Accounts',
      analytics_reports: 'Analytics Reports',
      storage: 'Storage Used',
      team_members: 'Team Members',
      ab_tests: 'A/B Tests',
      competitor_analysis: 'Competitor Analysis',
      api_calls: 'API Calls',
      white_label: 'White Label Brands'
    };
    return labels[key] || key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  const currentPlan = user?.subscription?.plan_id || 'creator';
  const usageEntries = Object.entries(usage || {});
  const criticalUsage = usageEntries.filter(([, data]) =>
    data.limit !== -1 && getUsagePercentage(data.used, data.limit) >= 90
  );

  return (
    <Box sx={{ p: isEmbedded ? 0 : 3 }}>
      {!isEmbedded && (
        <>
          <Helmet>
            <title>Usage Dashboard | B2B Influencer Tool</title>
            <meta name="description" content="Monitor your usage across all features and track your limits" />
          </Helmet>
          
          <Typography variant="h4" gutterBottom>
            Usage Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Monitor your usage across all features and track your limits for the current billing period
          </Typography>
        </>
      )}

      {/* Plan Info */}
      <Paper sx={{ p: 3, mb: 3, backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h6" gutterBottom>
              Current Plan: {currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Billing period: {new Date().toLocaleDateString()} - {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}
            </Typography>
          </Box>
          <Chip
            label={currentPlan.toUpperCase()}
            color="primary"
            variant="filled"
            sx={{ fontWeight: 'bold' }}
          />
        </Box>
      </Paper>

      {/* Critical Usage Alerts */}
      {criticalUsage.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Usage Alert: You&apos;re approaching your limits
          </Typography>
          <List dense>
            {criticalUsage.map(([key, data]) => (
              <ListItem key={key} sx={{ py: 0 }}>
                <ListItemIcon sx={{ minWidth: 32 }}>
                  <WarningIcon color="warning" fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary={`${formatUsageLabel(key)}: ${data.used}/${data.limit} ${data.unit} (${getUsagePercentage(data.used, data.limit).toFixed(0)}%)`}
                />
              </ListItem>
            ))}
          </List>
        </Alert>
      )}

      {/* Usage Cards */}
      <Grid container spacing={3}>
        {usageEntries.map(([key, data]) => {
          const IconComponent = getUsageIcon(key);
          const percentage = getUsagePercentage(data.used, data.limit);
          const color = getUsageColor(percentage);
          const isUnlimited = data.limit === -1;

          return (
            <Grid item xs={12} sm={6} md={4} key={key}>
              <Card
                sx={{
                  height: '100%',
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                  },
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        p: 1,
                        borderRadius: 1,
                        backgroundColor: alpha(theme.palette[color].main, 0.1),
                        color: theme.palette[color].main,
                        mr: 2,
                      }}
                    >
                      <IconComponent />
                    </Box>
                    <Typography variant="h6" component="h3">
                      {formatUsageLabel(key)}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Used
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {data.used} {data.unit}
                      </Typography>
                    </Box>
                    
                    {!isUnlimited && (
                      <>
                        <LinearProgress
                          variant="determinate"
                          value={percentage}
                          color={color}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: alpha(theme.palette[color].main, 0.1),
                          }}
                        />
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                          <Typography variant="caption" color="text.secondary">
                            Limit: {data.limit} {data.unit}
                          </Typography>
                          <Typography variant="caption" color={`${color}.main`} fontWeight="bold">
                            {percentage.toFixed(0)}%
                          </Typography>
                        </Box>
                      </>
                    )}
                    
                    {isUnlimited && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        <CheckCircleIcon color="success" sx={{ mr: 1, fontSize: 16 }} />
                        <Typography variant="caption" color="success.main" fontWeight="bold">
                          Unlimited
                        </Typography>
                      </Box>
                    )}
                  </Box>

                  {percentage >= 75 && !isUnlimited && (
                    <Alert severity={percentage >= 90 ? 'error' : 'warning'} sx={{ mt: 2 }}>
                      <Typography variant="caption">
                        {percentage >= 90 
                          ? 'Limit almost reached! Consider upgrading your plan.'
                          : 'Approaching limit. Monitor usage carefully.'
                        }
                      </Typography>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Usage Tips */}
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Usage Tips
        </Typography>
        <List>
          <ListItem>
            <ListItemIcon>
              <TrendingUpIcon color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Optimize your content generation"
              secondary="Use templates and saved prompts to generate content more efficiently"
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <ScheduleIcon color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Schedule posts in advance"
              secondary="Plan your content calendar to make the most of your monthly limits"
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>
              <AnalyticsIcon color="primary" />
            </ListItemIcon>
            <ListItemText
              primary="Monitor usage regularly"
              secondary="Check your usage dashboard weekly to avoid hitting limits unexpectedly"
            />
          </ListItem>
        </List>
      </Paper>
    </Box>
  );
};

export default UsageDashboard;

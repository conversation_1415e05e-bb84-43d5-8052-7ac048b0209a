#!/usr/bin/env node
// @since 2024-1-1 to 2025-25-7

/**
 * Clear cache and rebuild script for memory optimization
 * This script helps resolve ERR_INSUFFICIENT_RESOURCES errors
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧹 Starting cache cleanup and memory optimization...\n');

// Function to safely remove directory
function removeDir(dirPath) {
  try {
    if (fs.existsSync(dirPath)) {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Removed: ${dirPath}`);
    }
  } catch (error) {
    console.log(`⚠️  Could not remove ${dirPath}: ${error.message}`);
  }
}

// Function to safely remove file
function removeFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      console.log(`✅ Removed: ${filePath}`);
    }
  } catch (error) {
    console.log(`⚠️  Could not remove ${filePath}: ${error.message}`);
  }
}

// 1. Clear Vite cache
console.log('1. Clearing Vite cache...');
removeDir('node_modules/.vite');
removeDir('.vite');

// 2. Clear dist folder
console.log('\n2. Clearing build output...');
removeDir('dist');

// 3. Clear node_modules cache
console.log('\n3. Clearing node_modules cache...');
removeDir('node_modules/.cache');

// 4. Clear package manager cache
console.log('\n4. Clearing package manager cache...');
try {
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log('✅ NPM cache cleared');
} catch (error) {
  console.log('⚠️  Could not clear NPM cache');
}

// 5. Clear browser cache files
console.log('\n5. Clearing browser cache files...');
const cacheFiles = [
  'public/sw.js',
  'public/workbox-*.js',
  'public/manifest.json.backup'
];

cacheFiles.forEach(removeFile);

// 6. Reinstall dependencies with memory optimization
console.log('\n6. Reinstalling dependencies...');
try {
  // Use npm ci for clean install
  execSync('npm ci --prefer-offline --no-audit --no-fund', { 
    stdio: 'inherit',
    env: { 
      ...process.env, 
      NODE_OPTIONS: '--max-old-space-size=4096' // Increase Node.js memory limit
    }
  });
  console.log('✅ Dependencies reinstalled');
} catch (error) {
  console.log('⚠️  Dependency installation failed, trying regular install...');
  try {
    execSync('npm install --prefer-offline --no-audit --no-fund', { 
      stdio: 'inherit',
      env: { 
        ...process.env, 
        NODE_OPTIONS: '--max-old-space-size=4096'
      }
    });
    console.log('✅ Dependencies installed');
  } catch (fallbackError) {
    console.error('❌ Failed to install dependencies:', fallbackError.message);
    process.exit(1);
  }
}

// 7. Build with memory optimization
console.log('\n7. Building with memory optimization...');
try {
  execSync('npm run build', { 
    stdio: 'inherit',
    env: { 
      ...process.env, 
      NODE_OPTIONS: '--max-old-space-size=6144', // 6GB for build
      VITE_BUNDLE_ANALYZER: 'false' // Disable bundle analyzer to save memory
    }
  });
  console.log('✅ Build completed successfully');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  console.log('\n💡 Try running with more memory:');
  console.log('   NODE_OPTIONS="--max-old-space-size=8192" npm run build');
  process.exit(1);
}

// 8. Analyze bundle size
console.log('\n8. Analyzing bundle size...');
try {
  const distPath = path.join(__dirname, 'dist');
  if (fs.existsSync(distPath)) {
    const files = fs.readdirSync(distPath);
    const jsFiles = files.filter(f => f.endsWith('.js'));

    console.log('\n📊 Bundle Analysis:');
    jsFiles.forEach(file => {
      const filePath = path.join(distPath, file);
      const stats = fs.statSync(filePath);
      const sizeKB = Math.round(stats.size / 1024);
      const status = sizeKB > 500 ? '⚠️ ' : sizeKB > 300 ? '⚡' : '✅';
      console.log(`   ${status} ${file}: ${sizeKB}KB`);
    });
  }
} catch (error) {
  console.log('⚠️  Could not analyze bundle size');
}

console.log('\n🎉 Cache cleanup and rebuild completed!');
console.log('\n💡 Tips to prevent memory issues:');
console.log('   • Close other browser tabs');
console.log('   • Restart your browser');
console.log('   • Use Chrome DevTools to monitor memory');
console.log('   • Consider using --max-old-space-size flag for Node.js');

console.log('\n🚀 You can now start the development server:');
console.log('   npm run dev');

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  alpha,
  useTheme
} from '@mui/material';
import {
  Store as StoreIcon,
  Add as AddIcon,
  Sync as SyncIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  ShoppingCart as ShoppingCartIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import FeatureGate from '../../components/common/FeatureGate';
import api from '../../api';

const EcommerceStorePage = () => {
  const theme = useTheme();
  const { hasFeature } = useAuth();
  const [stores, setStores] = useState([]);
  const [loading, setLoading] = useState(true);
  const [syncingStores, setSyncingStores] = useState(new Set());
  const [connectDialogOpen, setConnectDialogOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState('');
  const [storeUrl, setStoreUrl] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [connecting, setConnecting] = useState(false);

  // Fetch stores on component mount
  useEffect(() => {
    fetchStores();
  }, []);

  // Initiate OAuth flow for store connection
  const initiateOAuthFlow = async (platform, storeUrl) => {
    try {
      const response = await api.post('/api/ecommerce/stores/oauth/initiate', {
        platform,
        store_url: storeUrl,
        redirect_uri: window.location.origin + '/ecommerce/callback'
      });

      return response.data.authorization_url;
    } catch (error) {
      console.error('Error initiating OAuth flow:', error);
      // Return null to fall back to mock data for development
      return null;
    }
  };

  const fetchStores = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await api.get('/api/ecommerce/stores');

      // Handle different response formats
      const storesData = response.data?.stores || response.data || [];
      setStores(Array.isArray(storesData) ? storesData : []);

    } catch (error) {
      console.error('Error fetching stores:', error);
      if (error.response?.status === 403) {
        setError('E-commerce features not available in your plan');
      } else if (error.response?.status === 401) {
        setError('Please log in to access e-commerce features');
      } else {
        setError(error.response?.data?.detail || 'Failed to load stores');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleConnectStore = async () => {
    if (!selectedPlatform) {
      setError('Please select a platform');
      return;
    }

    if (selectedPlatform === 'woocommerce' && !storeUrl) {
      setError('Store URL is required for WooCommerce');
      return;
    }

    setConnecting(true);
    setError('');

    try {
      // Check if user has access to e-commerce features
      if (!hasFeature('ecommerce_integration')) {
        setError('E-commerce integration not available in your plan');
        return;
      }

      // Initiate OAuth flow for store connection
      // This will redirect to the platform's OAuth authorization page
      const authUrl = await initiateOAuthFlow(selectedPlatform, storeUrl);

      if (authUrl) {
        // Redirect to OAuth authorization page
        window.location.href = authUrl;
        return;
      }

      // Fallback for development/testing with mock data
      const authData = {
        platform: selectedPlatform,
        authorization_code: 'demo_auth_code',
        state: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        redirect_uri: window.location.origin + '/ecommerce/callback',
        store_url: storeUrl || undefined
      };

      const response = await api.post('/api/ecommerce/stores/connect', authData);
      
      if (response.data.success) {
        setSuccess('Store connected successfully!');
        setConnectDialogOpen(false);
        setSelectedPlatform('');
        setStoreUrl('');
        fetchStores();
      }
    } catch (error) {
      console.error('Error connecting store:', error);
      setError(error.response?.data?.detail || 'Failed to connect store');
    } finally {
      setConnecting(false);
    }
  };

  const handleSyncStore = async (storeId) => {
    try {
      setSyncingStores(prev => new Set([...prev, storeId]));
      
      const response = await api.post(`/api/ecommerce/stores/${storeId}/sync`, {
        force_full_sync: false
      });
      
      if (response.data.success) {
        setSuccess('Product sync started successfully!');
        // Refresh stores to show updated sync status
        setTimeout(fetchStores, 2000);
      }
    } catch (error) {
      console.error('Error syncing store:', error);
      setError('Failed to start sync');
    } finally {
      setSyncingStores(prev => {
        const newSet = new Set(prev);
        newSet.delete(storeId);
        return newSet;
      });
    }
  };

  const handleDisconnectStore = async (storeId) => {
    if (!window.confirm('Are you sure you want to disconnect this store? This will remove all synced products.')) {
      return;
    }

    try {
      const response = await api.delete(`/api/ecommerce/stores/${storeId}`);
      
      if (response.data.success) {
        setSuccess('Store disconnected successfully');
        fetchStores();
      }
    } catch (error) {
      console.error('Error disconnecting store:', error);
      setError('Failed to disconnect store');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected':
      case 'sync_completed':
        return 'success';
      case 'syncing':
        return 'info';
      case 'sync_failed':
      case 'authentication_failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected':
      case 'sync_completed':
        return <CheckCircleIcon />;
      case 'syncing':
        return <SyncIcon />;
      case 'sync_failed':
      case 'authentication_failed':
        return <WarningIcon />;
      default:
        return <StoreIcon />;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <FeatureGate feature="ecommerce_integration" fallback={
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          E-commerce Integration
        </Typography>
        <Typography color="text.secondary" paragraph>
          Connect your online store to generate product-focused content and campaigns.
        </Typography>
        <Button variant="contained" href="/billing/plans">
          Upgrade to Access E-commerce Features
        </Button>
      </Box>
    }>
      <Box sx={{ p: { xs: 1, sm: 2 } }}>
        {/* Header */}
        <Paper
          elevation={0}
          sx={{
            p: 2,
            mb: 3,
            bgcolor: alpha(theme.palette.primary.main, 0.05),
            borderRadius: 2,
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          }}
        >
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={2}>
              <StoreIcon color="primary" sx={{ fontSize: 32 }} />
              <Box>
                <Typography variant="h5" fontWeight="bold">
                  E-commerce Stores
                </Typography>
                <Typography color="text.secondary">
                  Connect your online stores to generate product-focused content
                </Typography>
              </Box>
            </Box>
            <Box display="flex" gap={1}>
              <Button
                variant="outlined"
                startIcon={<SyncIcon />}
                onClick={fetchStores}
                disabled={loading}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setConnectDialogOpen(true)}
                sx={{ minWidth: 140 }}
              >
                Connect Store
              </Button>
            </Box>
          </Box>
        </Paper>

        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
            {success}
          </Alert>
        )}

        {/* Stores Grid */}
        {stores.length === 0 ? (
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <ShoppingCartIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              No stores connected
            </Typography>
            <Typography color="text.secondary" paragraph>
              Connect your first e-commerce store to start generating product-focused content and campaigns.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setConnectDialogOpen(true)}
            >
              Connect Your First Store
            </Button>
          </Paper>
        ) : (
          <Grid container spacing={3}>
            {stores.map((store) => (
              <Grid item xs={12} md={6} lg={4} key={store.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: theme.shadows[4],
                    },
                  }}
                >
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                      <Chip
                        label={store.platform}
                        color="primary"
                        size="small"
                        sx={{ textTransform: 'capitalize' }}
                      />
                      <Chip
                        icon={getStatusIcon(store.status)}
                        label={store.status.replace('_', ' ')}
                        color={getStatusColor(store.status)}
                        size="small"
                        sx={{ textTransform: 'capitalize' }}
                      />
                    </Box>

                    <Typography variant="h6" gutterBottom noWrap title={store.store_name}>
                      {store.store_name || 'Unnamed Store'}
                    </Typography>

                    <Typography color="text.secondary" variant="body2" gutterBottom noWrap title={store.store_url}>
                      {store.store_url || 'No URL provided'}
                    </Typography>

                    <Box mt={2}>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Box textAlign="center">
                            <Typography variant="h6" color="primary">
                              {store.total_products || 0}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Total Products
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={6}>
                          <Box textAlign="center">
                            <Typography variant="h6" color="success.main">
                              {store.synced_products || 0}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Synced
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>

                    {store.last_sync_at && (
                      <Typography variant="caption" color="text.secondary" mt={1} display="block">
                        Last sync: {new Date(store.last_sync_at).toLocaleString()}
                      </Typography>
                    )}

                    {store.last_error && (
                      <Alert severity="warning" sx={{ mt: 1 }}>
                        <Typography variant="caption">
                          {store.last_error}
                        </Typography>
                      </Alert>
                    )}
                  </CardContent>

                  <CardActions sx={{ p: 2, pt: 0 }}>
                    <Tooltip title="Sync Products">
                      <IconButton
                        onClick={() => handleSyncStore(store.id)}
                        disabled={syncingStores.has(store.id)}
                        color="primary"
                      >
                        {syncingStores.has(store.id) ? (
                          <CircularProgress size={20} />
                        ) : (
                          <SyncIcon />
                        )}
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="View Products">
                      <IconButton
                        component="a"
                        href={`/ecommerce/stores/${store.id}/products`}
                        color="primary"
                      >
                        <TrendingUpIcon />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="Store Settings">
                      <IconButton color="default">
                        <SettingsIcon />
                      </IconButton>
                    </Tooltip>

                    <Tooltip title="Disconnect Store">
                      <IconButton
                        onClick={() => handleDisconnectStore(store.id)}
                        color="error"
                        sx={{ ml: 'auto' }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Connect Store Dialog */}
        <Dialog
          open={connectDialogOpen}
          onClose={() => setConnectDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Connect E-commerce Store</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 1 }}>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Platform</InputLabel>
                <Select
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value)}
                  label="Platform"
                >
                  <MenuItem value="shopify">Shopify</MenuItem>
                  <MenuItem value="woocommerce">WooCommerce</MenuItem>
                </Select>
              </FormControl>

              {selectedPlatform === 'woocommerce' && (
                <TextField
                  fullWidth
                  label="Store URL"
                  value={storeUrl}
                  onChange={(e) => setStoreUrl(e.target.value)}
                  placeholder="https://your-store.com"
                  helperText="Enter your WooCommerce store URL"
                  sx={{ mb: 2 }}
                />
              )}

              <Alert severity="info">
                {selectedPlatform === 'shopify'
                  ? "You'll be redirected to Shopify to authorize the connection. Make sure you have admin access to your store."
                  : "You'll need to provide your WooCommerce API credentials. Make sure you have admin access to your store."
                }
              </Alert>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setConnectDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleConnectStore}
              variant="contained"
              disabled={!selectedPlatform || connecting}
              startIcon={connecting ? <CircularProgress size={20} /> : null}
            >
              {connecting ? 'Connecting...' : 'Connect Store'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </FeatureGate>
  );
};

export default EcommerceStorePage;

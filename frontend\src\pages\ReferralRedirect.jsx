// @since 2024-1-1 to 2025-25-7
import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  CircularProgress,
  Container,
  Paper,
  Alert,
  Button
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { API_BASE_URL } from '../config';


/**
 * Component to handle referral link redirects.
 * This component is rendered when a user visits /r/:code
 */
const ReferralRedirect = () => {
  const { code } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [referralData, setReferralData] = useState(null);

  useEffect(() => {
    const processReferral = async () => {
      if (!code) {
        setError('Invalid referral link - no code provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Call the referral API to validate and track the click
        const response = await fetch(`${API_BASE_URL}/api/referrals/track/${code}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            timestamp: new Date().toISOString(),
            user_agent: navigator.userAgent,
            referrer: document.referrer || null
          })
        });

        if (!response.ok) {
          const errorData = await response.json();

          if (response.status === 404) {
            setError('This referral link is invalid or has expired');
          } else if (response.status === 429) {
            setError('This referral link has reached its usage limit. Please try again later.');
          } else if (response.status === 403) {
            setError('This referral link is not available in your region or has been disabled');
          } else {
            setError(errorData.message || 'Failed to process referral link');
          }
          setLoading(false);
          return;
        }

        const data = await response.json();
        setReferralData(data);

        // Store referral code and data in localStorage for signup process
        localStorage.setItem('referralCode', code);
        localStorage.setItem('referralData', JSON.stringify({
          code: code,
          referrer_name: data.referrer_name,
          campaign: data.campaign,
          timestamp: new Date().toISOString()
        }));

        // Redirect to registration page after a brief success message
        setTimeout(() => {
          navigate('/register', {
            replace: true,
            state: {
              referralCode: code,
              referrerName: data.referrer_name
            }
          });
        }, 2000);

      } catch (err) {
        console.error('Error processing referral:', err);
        setError('Network error. Please check your connection and try again.');
      } finally {
        setLoading(false);
      }
    };

    processReferral();
  }, [code, navigate]);

  // Render error state
  if (error) {
    return (
      <Container maxWidth="sm">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '80vh',
            textAlign: 'center',
            p: 4
          }}
        >
          <Paper sx={{ p: 4, width: '100%', maxWidth: 500 }}>
            <ErrorIcon sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom color="error">
              Referral Link Error
            </Typography>

            <Alert severity="error" sx={{ mb: 3, textAlign: 'left' }}>
              {error}
            </Alert>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                onClick={() => navigate('/register')}
                sx={{ minWidth: 120 }}
              >
                Sign Up Anyway
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate('/')}
                sx={{ minWidth: 120 }}
              >
                Go Home
              </Button>
            </Box>

            <Typography variant="body2" color="textSecondary" sx={{ mt: 3 }}>
              You can still create an account even if the referral link isn&apos;t working.
            </Typography>
          </Paper>
        </Box>
      </Container>
    );
  }

  // Render loading state
  if (loading) {
    return (
      <Container maxWidth="sm">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '80vh',
            textAlign: 'center',
            p: 4
          }}
        >
          <Paper sx={{ p: 4, width: '100%', maxWidth: 500 }}>
            <CircularProgress size={60} sx={{ mb: 4, color: 'primary.main' }} />
            <Typography variant="h5" gutterBottom>
              Processing Referral
            </Typography>
            <Typography variant="body1" color="textSecondary" sx={{ mb: 2 }}>
              Please wait while we validate your referral link...
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mt: 3 }}>
              <SecurityIcon sx={{ fontSize: 20, color: 'success.main' }} />
              <Typography variant="body2" color="textSecondary">
                Secure referral processing
              </Typography>
            </Box>
          </Paper>
        </Box>
      </Container>
    );
  }

  // Render success state (should redirect quickly)
  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '80vh',
          textAlign: 'center',
          p: 4
        }}
      >
        <Paper sx={{ p: 4, width: '100%', maxWidth: 500 }}>
          <CheckCircleIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom color="success.main">
            Referral Successful!
          </Typography>

          {referralData?.referrer_name && (
            <Alert severity="success" sx={{ mb: 3, textAlign: 'left' }}>
              You were referred by <strong>{referralData.referrer_name}</strong>
            </Alert>
          )}

          <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
            Your referral has been tracked successfully. Redirecting you to registration...
          </Typography>

          <CircularProgress size={30} sx={{ color: 'success.main' }} />

          <Typography variant="body2" color="textSecondary" sx={{ mt: 3 }}>
            You&apos;ll be eligible for any referral bonuses once you complete registration.
          </Typography>
        </Paper>
      </Box>
    </Container>
  );
};

export default ReferralRedirect;

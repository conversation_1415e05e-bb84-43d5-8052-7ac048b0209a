// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress,

  Divider,
  Alert
} from '@mui/material';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import { useAuth } from '../../hooks/useAuth';
import { useNotification } from '../../hooks/useNotification';
import { AppSumoUpgradeOptions } from '../../components/appsumo';

/**
 * AppSumo Upgrade Options page displays available upgrades and add-ons for AppSumo users
 */
const UpgradeOptions = () => {
  const { user } = useAuth();
  const { showErrorNotification } = useNotification();
  
  // State
  const [loading, setLoading] = useState(true);
  const [currentTier, setCurrentTier] = useState('tier1');
  
  // Determine the current AppSumo tier on component mount
  useEffect(() => {
    const determineCurrentTier = () => {
      setLoading(true);
      
      try {
        // Check if user has an AppSumo subscription
        if (!user?.subscription?.is_appsumo_lifetime) {
          // Not an AppSumo user
          return;
        }
        
        // Determine tier based on plan_id or other properties
        const planId = user.subscription.plan_id;
        let tier = 'tier1';
        
        switch (planId) {
          case 'starter':
            tier = 'tier1';
            break;
          case 'growth':
            tier = 'tier2';
            break;
          case 'accelerate':
            tier = 'tier3';
            break;
          case 'dominate':
            tier = 'tier4';
            break;
          default:
            tier = 'tier1';
        }
        
        setCurrentTier(tier);
      } catch (error) {
        console.error('Error determining AppSumo tier:', error);
        showErrorNotification('Failed to load your subscription details');
      } finally {
        setLoading(false);
      }
    };
    
    determineCurrentTier();
  }, [user, showErrorNotification]);
  
  // If not an AppSumo user, show a message
  if (!user?.subscription?.is_appsumo_lifetime) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          <CardGiftcardIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          AppSumo Upgrade Options
        </Typography>
        
        <Alert severity="info" sx={{ mt: 2 }}>
          This page is only available for AppSumo lifetime deal customers. If you purchased through AppSumo and don&apos;t see your options, please contact our support team.
        </Alert>
      </Box>
    );
  }
  
  // Render loading state
  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Typography variant="h4" gutterBottom>
          <CardGiftcardIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          AppSumo Upgrade Options
        </Typography>
        
        <CircularProgress sx={{ mt: 4 }} />
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          Loading your upgrade options...
        </Typography>
      </Box>
    );
  }
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        <CardGiftcardIcon sx={{ mr: 1, verticalAlign: 'middle', color: '#FF8C00' }} />
        AppSumo Upgrade Options
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Enhance your AppSumo lifetime deal with these exclusive upgrade options and add-ons.
      </Typography>
      
      <Divider sx={{ my: 3 }} />
      
      <AppSumoUpgradeOptions currentTier={currentTier} />
    </Box>
  );
};

export default UpgradeOptions;

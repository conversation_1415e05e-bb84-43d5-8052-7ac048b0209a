"""
Main e-commerce service for managing store connections and product synchronization.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from bson import ObjectId

from app.db.mongodb import get_database
from app.models.user import User, PyObjectId
from app.models.ecommerce import (
    EcommerceStore, 
    SyncedProduct, 
    SyncLog,
    EcommerceWebhook,
    SyncOperationEnum
)
from app.models.common import EcommercePlatformEnum, SyncStatusEnum, StatusEnum
from app.services.ecommerce.factory import EcommerceIntegrationFactory
from app.services.credential_manager import credential_manager
from app.utils.error_handling import ExternalServiceError

# Create ServiceError alias for consistency
ServiceError = ExternalServiceError

logger = logging.getLogger(__name__)

# Collection names
STORES_COLLECTION = "ecommerce_stores"
PRODUCTS_COLLECTION = "synced_products"
SYNC_LOGS_COLLECTION = "ecommerce_sync_logs"
WEBHOOKS_COLLECTION = "ecommerce_webhooks"


class EcommerceService:
    """
    Main service for e-commerce operations.
    """
    
    async def connect_store(
        self, 
        user: User, 
        platform: str, 
        authorization_code: str, 
        state: str,
        redirect_uri: str,
        store_url: Optional[str] = None
    ) -> EcommerceStore:
        """
        Connect a new e-commerce store.
        
        Args:
            user: User connecting the store
            platform: E-commerce platform
            authorization_code: OAuth authorization code
            state: OAuth state parameter
            redirect_uri: OAuth redirect URI
            store_url: Store URL (required for some platforms)
            
        Returns:
            Connected store object
        """
        try:
            # Get integration for platform
            integration = EcommerceIntegrationFactory.get_integration(platform)
            
            # Handle OAuth callback
            store = await integration.handle_oauth_callback(
                authorization_code, 
                state, 
                redirect_uri, 
                store_url
            )
            
            # Set user ID
            store.user_id = user.id
            
            # Encrypt and store credentials
            encrypted_store = await credential_manager.encrypt_ecommerce_store(store)
            
            # Save to database
            db = await get_database()
            result = await db[STORES_COLLECTION].insert_one(encrypted_store.model_dump(by_alias=True))
            
            # Get the created store
            created_store = await db[STORES_COLLECTION].find_one({"_id": result.inserted_id})
            if not created_store:
                raise ServiceError("Failed to retrieve created store")
            
            # Decrypt for return
            decrypted_store = await credential_manager.decrypt_ecommerce_store(EcommerceStore(**created_store))
            
            # Set up webhooks
            try:
                await integration.setup_webhooks(decrypted_store)
            except Exception as e:
                logger.warning(f"Failed to setup webhooks for store {store.store_id}: {str(e)}")
            
            # Log the connection
            await self._log_sync_operation(
                user.id,
                decrypted_store.id,
                SyncOperationEnum.FULL_SYNC,
                StatusEnum.COMPLETED,
                metadata={"operation": "store_connection", "platform": platform}
            )
            
            return decrypted_store
            
        except Exception as e:
            logger.error(f"Failed to connect {platform} store: {str(e)}")
            raise ServiceError(f"Failed to connect store: {str(e)}")
    
    async def get_user_stores(self, user_id: str) -> List[EcommerceStore]:
        """
        Get all stores for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of user's stores
        """
        try:
            db = await get_database()
            stores_data = await db[STORES_COLLECTION].find(
                {"user_id": ObjectId(user_id)}
            ).to_list(length=None)
            
            stores = []
            for store_data in stores_data:
                store = EcommerceStore(**store_data)
                # Decrypt credentials
                decrypted_store = await credential_manager.decrypt_ecommerce_store(store)
                stores.append(decrypted_store)
            
            return stores
            
        except Exception as e:
            logger.error(f"Failed to get user stores: {str(e)}")
            raise ServiceError(f"Failed to get stores: {str(e)}")
    
    async def get_store(self, store_id: str, user_id: str) -> Optional[EcommerceStore]:
        """
        Get a specific store.
        
        Args:
            store_id: Store ID
            user_id: User ID (for security)
            
        Returns:
            Store if found and owned by user
        """
        try:
            db = await get_database()
            store_data = await db[STORES_COLLECTION].find_one({
                "_id": ObjectId(store_id),
                "user_id": ObjectId(user_id)
            })
            
            if not store_data:
                return None
            
            store = EcommerceStore(**store_data)
            return await credential_manager.decrypt_ecommerce_store(store)
            
        except Exception as e:
            logger.error(f"Failed to get store: {str(e)}")
            return None
    
    async def disconnect_store(self, store_id: str, user_id: str) -> bool:
        """
        Disconnect and remove a store.
        
        Args:
            store_id: Store ID
            user_id: User ID (for security)
            
        Returns:
            True if successfully disconnected
        """
        try:
            db = await get_database()
            
            # Delete store
            result = await db[STORES_COLLECTION].delete_one({
                "_id": ObjectId(store_id),
                "user_id": ObjectId(user_id)
            })
            
            if result.deleted_count > 0:
                # Also delete associated products
                await db[PRODUCTS_COLLECTION].delete_many({
                    "store_id": ObjectId(store_id),
                    "user_id": ObjectId(user_id)
                })
                
                # Log the disconnection
                await self._log_sync_operation(
                    PyObjectId(user_id),
                    PyObjectId(store_id),
                    SyncOperationEnum.FULL_SYNC,
                    StatusEnum.COMPLETED,
                    metadata={"operation": "store_disconnection"}
                )
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to disconnect store: {str(e)}")
            return False
    
    async def sync_store_products(
        self, 
        store_id: str, 
        user_id: str,
        limit: int = 250,
        force_full_sync: bool = False
    ) -> Dict[str, Any]:
        """
        Sync products from a store.
        
        Args:
            store_id: Store ID
            user_id: User ID
            limit: Maximum products to sync
            force_full_sync: Whether to force a full sync
            
        Returns:
            Sync results
        """
        try:
            # Get store
            store = await self.get_store(store_id, user_id)
            if not store:
                raise ServiceError("Store not found")
            
            # Get integration
            integration = EcommerceIntegrationFactory.get_integration(store.platform)
            
            # Start sync log
            sync_log_id = await self._log_sync_operation(
                store.user_id,
                store.id,
                SyncOperationEnum.FULL_SYNC if force_full_sync else SyncOperationEnum.INCREMENTAL_SYNC,
                StatusEnum.PENDING
            )
            
            try:
                # Determine since_id for incremental sync
                since_id = None
                if not force_full_sync:
                    since_id = await self._get_last_synced_product_id(store.id)
                
                # Sync products
                synced_products = await integration.sync_products(store, limit, since_id)
                
                # Save products to database
                db = await get_database()
                saved_count = 0
                
                for product in synced_products:
                    # Check if product already exists
                    existing = await db[PRODUCTS_COLLECTION].find_one({
                        "store_id": store.id,
                        "external_product_id": product.external_product_id
                    })
                    
                    if existing:
                        # Update existing product
                        await db[PRODUCTS_COLLECTION].update_one(
                            {"_id": existing["_id"]},
                            {"$set": product.model_dump(by_alias=True, exclude={"id"})}
                        )
                    else:
                        # Insert new product
                        await db[PRODUCTS_COLLECTION].insert_one(product.model_dump(by_alias=True))
                    
                    saved_count += 1
                
                # Update store sync info
                await db[STORES_COLLECTION].update_one(
                    {"_id": store.id},
                    {
                        "$set": {
                            "last_sync_at": datetime.now(timezone.utc),
                            "synced_products": saved_count,
                            "status": SyncStatusEnum.SYNC_COMPLETED
                        }
                    }
                )
                
                # Complete sync log
                await self._complete_sync_log(
                    sync_log_id,
                    StatusEnum.COMPLETED,
                    total_items=len(synced_products),
                    successful_items=saved_count
                )
                
                return {
                    "success": True,
                    "products_synced": saved_count,
                    "total_products": len(synced_products),
                    "sync_type": "full" if force_full_sync else "incremental"
                }
                
            except Exception as e:
                # Mark sync as failed
                await self._complete_sync_log(
                    sync_log_id,
                    StatusEnum.FAILED,
                    error_message=str(e)
                )
                raise
                
        except Exception as e:
            logger.error(f"Failed to sync store products: {str(e)}")
            raise ServiceError(f"Failed to sync products: {str(e)}")
    
    async def get_store_products(
        self, 
        store_id: str, 
        user_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get products for a store.
        
        Args:
            store_id: Store ID
            user_id: User ID
            limit: Number of products to return
            offset: Number of products to skip
            
        Returns:
            Products and pagination info
        """
        try:
            db = await get_database()
            
            # Get total count
            total = await db[PRODUCTS_COLLECTION].count_documents({
                "store_id": ObjectId(store_id),
                "user_id": ObjectId(user_id)
            })
            
            # Get products
            products_data = await db[PRODUCTS_COLLECTION].find({
                "store_id": ObjectId(store_id),
                "user_id": ObjectId(user_id)
            }).skip(offset).limit(limit).to_list(length=None)
            
            products = [SyncedProduct(**product_data) for product_data in products_data]
            
            return {
                "products": products,
                "total": total,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total
            }
            
        except Exception as e:
            logger.error(f"Failed to get store products: {str(e)}")
            raise ServiceError(f"Failed to get products: {str(e)}")
    
    async def _log_sync_operation(
        self,
        user_id: PyObjectId,
        store_id: PyObjectId,
        operation_type: SyncOperationEnum,
        status: StatusEnum,
        total_items: int = 0,
        successful_items: int = 0,
        failed_items: int = 0,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PyObjectId:
        """Log a sync operation."""
        try:
            db = await get_database()
            
            sync_log = SyncLog(
                user_id=user_id,
                store_id=store_id,
                operation_type=operation_type,
                status=status,
                total_items=total_items,
                successful_items=successful_items,
                failed_items=failed_items,
                error_message=error_message,
                completed_at=None,  # Will be set when sync completes
                duration_seconds=None,  # Will be calculated when sync completes
                error_details=None,  # Will be set if errors occur
                metadata=metadata
            )
            
            result = await db[SYNC_LOGS_COLLECTION].insert_one(sync_log.model_dump(by_alias=True))
            return PyObjectId(str(result.inserted_id))
            
        except Exception as e:
            logger.error(f"Failed to log sync operation: {str(e)}")
            # Don't raise here as this is logging
            return PyObjectId()
    
    async def _complete_sync_log(
        self,
        sync_log_id: PyObjectId,
        status: StatusEnum,
        total_items: int = 0,
        successful_items: int = 0,
        failed_items: int = 0,
        error_message: Optional[str] = None
    ) -> None:
        """Complete a sync log entry."""
        try:
            db = await get_database()
            
            update_data = {
                "status": status,
                "completed_at": datetime.now(timezone.utc),
                "total_items": total_items,
                "successful_items": successful_items,
                "failed_items": failed_items
            }
            
            if error_message:
                update_data["error_message"] = error_message
            
            await db[SYNC_LOGS_COLLECTION].update_one(
                {"_id": ObjectId(str(sync_log_id))},
                {"$set": update_data}
            )
            
        except Exception as e:
            logger.error(f"Failed to complete sync log: {str(e)}")
    
    async def _get_last_synced_product_id(self, store_id: PyObjectId) -> Optional[str]:
        """Get the last synced product ID for incremental sync."""
        try:
            db = await get_database()
            
            last_product = await db[PRODUCTS_COLLECTION].find_one(
                {"store_id": store_id},
                sort=[("last_synced_at", -1)]
            )
            
            if last_product:
                return last_product.get("external_product_id")
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get last synced product ID: {str(e)}")
            return None


# Create singleton instance
ecommerce_service = EcommerceService()

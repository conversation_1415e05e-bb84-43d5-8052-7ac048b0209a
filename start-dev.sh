#!/bin/bash
# @since 2024-1-1 to 2025-25-7

# ACEO - Development Server Startup Script
# Shell script for Linux/Mac support

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
MODE="standard"
INSTALL_ONLY=false

# Function to show help
show_help() {
    echo ""
    echo -e "${GREEN}ACEO - Development Server${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    echo -e "${YELLOW}Usage: ./start-dev.sh [OPTIONS]${NC}"
    echo ""
    echo -e "${CYAN}Options:${NC}"
    echo -e "  -m, --mode MODE     Development mode (standard|windows|memory|simple)"
    echo -e "  -i, --install-only  Only install dependencies"
    echo -e "  -h, --help          Show this help message"
    echo ""
    echo -e "${CYAN}Modes:${NC}"
    echo -e "  standard  - Standard development mode (default)"
    echo -e "  windows   - Windows native mode"
    echo -e "  memory    - High memory mode (8GB)"
    echo -e "  simple    - Simple mode (no optimization)"
    echo ""
    echo -e "${CYAN}Examples:${NC}"
    echo -e "  ./start-dev.sh"
    echo -e "  ./start-dev.sh -m memory"
    echo -e "  ./start-dev.sh --install-only"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -i|--install-only)
            INSTALL_ONLY=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# Function to check prerequisites
check_prerequisites() {
    echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"
    
    # Check if we're in the right directory
    if [[ ! -f "package.json" ]]; then
        echo -e "${RED}❌ Error: package.json not found!${NC}"
        echo -e "${RED}Please run this script from the project root directory.${NC}"
        exit 1
    fi
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        echo -e "${GREEN}✅ Node.js: $NODE_VERSION${NC}"
    else
        echo -e "${RED}❌ Node.js not found! Please install Node.js.${NC}"
        exit 1
    fi
    
    # Check Python
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        echo -e "${GREEN}✅ Python: $PYTHON_VERSION${NC}"
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version)
        echo -e "${GREEN}✅ Python: $PYTHON_VERSION${NC}"
    else
        echo -e "${RED}❌ Python not found! Please install Python.${NC}"
        exit 1
    fi
    
    echo ""
}

# Function to install dependencies
install_dependencies() {
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    
    # Install root dependencies
    if [[ ! -d "node_modules" ]]; then
        echo -e "${CYAN}Installing root dependencies...${NC}"
        npm install
    fi
    
    # Install frontend dependencies
    if [[ ! -d "frontend/node_modules" ]]; then
        echo -e "${CYAN}Installing frontend dependencies...${NC}"
        cd frontend
        npm install
        cd ..
    fi
    
    # Check Python virtual environment
    if [[ ! -d "venv" ]]; then
        echo -e "${CYAN}Creating Python virtual environment...${NC}"
        if command -v python3 &> /dev/null; then
            python3 -m venv venv
        else
            python -m venv venv
        fi
    fi
    
    # Install backend dependencies
    echo -e "${CYAN}Installing backend dependencies...${NC}"
    cd backend
    source ../venv/bin/activate
    pip install -r requirements.txt
    deactivate
    cd ..
    
    echo -e "${GREEN}✅ All dependencies installed!${NC}"
    echo ""
}

# Function to start development servers
start_development() {
    local dev_mode=$1
    
    echo -e "${GREEN}🚀 Starting development servers...${NC}"
    echo ""
    echo -e "${CYAN}Backend will be available at: http://localhost:8000${NC}"
    echo -e "${CYAN}Frontend will be available at: http://localhost:3000${NC}"
    echo -e "${CYAN}API Documentation: http://localhost:8000/api/docs${NC}"
    echo ""
    echo -e "${YELLOW}Press Ctrl+C to stop both servers${NC}"
    echo ""
    
    case $dev_mode in
        "standard")
            npm run dev
            ;;
        "windows")
            npm run dev:windows
            ;;
        "memory")
            npm run dev:memory
            ;;
        "simple")
            npm run dev:simple
            ;;
        *)
            npm run dev
            ;;
    esac
}

# Main execution
echo ""
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}   ACEO Development${NC}"
echo -e "${GREEN}========================================${NC}"
echo ""

check_prerequisites

if [[ "$INSTALL_ONLY" == true ]]; then
    install_dependencies
    echo -e "${GREEN}Dependencies installation complete!${NC}"
    echo -e "${YELLOW}Run './start-dev.sh' to start the development servers.${NC}"
    exit 0
fi

# Check if dependencies need to be installed
if [[ ! -d "node_modules" ]] || [[ ! -d "frontend/node_modules" ]]; then
    echo -e "${YELLOW}⚠️  Missing dependencies. Installing...${NC}"
    install_dependencies
fi

echo -e "${GREEN}Starting in $MODE mode...${NC}"
start_development "$MODE"

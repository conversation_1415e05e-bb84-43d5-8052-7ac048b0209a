"""
Base class for e-commerce platform integrations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import logging

from app.models.ecommerce import EcommerceStore, SyncedProduct
from app.models.common import EcommercePlatformEnum

logger = logging.getLogger(__name__)


class BaseEcommerceIntegration(ABC):
    """
    Abstract base class for e-commerce platform integrations.
    Follows the same pattern as social media integrations.
    """
    
    platform_name: str = ""
    
    @abstractmethod
    async def get_authorization_url(self, redirect_uri: str, store_url: Optional[str] = None) -> Tuple[str, str]:
        """
        Get the authorization URL for OAuth flow.
        
        Args:
            redirect_uri: The redirect URI for the OAuth callback
            store_url: Store URL for platforms that require it (like WooCommerce)
            
        Returns:
            Tuple of (authorization_url, state)
        """
        pass
    
    @abstractmethod
    async def handle_oauth_callback(
        self, 
        code: str, 
        state: str, 
        redirect_uri: str,
        store_url: Optional[str] = None
    ) -> EcommerceStore:
        """
        Handle the OAuth callback and get access token.
        
        Args:
            code: The authorization code from the callback
            state: The state parameter from the callback
            redirect_uri: The redirect URI used in the authorization request
            store_url: Store URL for platforms that require it
            
        Returns:
            EcommerceStore object with tokens and store info
        """
        pass
    
    @abstractmethod
    async def refresh_access_token(self, store: EcommerceStore) -> EcommerceStore:
        """
        Refresh the access token for a store.
        
        Args:
            store: The store with expired token
            
        Returns:
            Updated store with new token
        """
        pass
    
    @abstractmethod
    async def test_connection(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Test the connection to the store.
        
        Args:
            store: The store to test
            
        Returns:
            Connection test results
        """
        pass
    
    @abstractmethod
    async def get_store_info(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Get basic store information.
        
        Args:
            store: The store to get info for
            
        Returns:
            Store information
        """
        pass
    
    @abstractmethod
    async def sync_products(
        self, 
        store: EcommerceStore, 
        limit: int = 250,
        since_id: Optional[str] = None
    ) -> List[SyncedProduct]:
        """
        Sync products from the store.
        
        Args:
            store: The store to sync from
            limit: Maximum number of products to sync
            since_id: Sync products created after this ID
            
        Returns:
            List of synced products
        """
        pass
    
    @abstractmethod
    async def get_product(self, store: EcommerceStore, product_id: str) -> Optional[SyncedProduct]:
        """
        Get a specific product from the store.
        
        Args:
            store: The store to get product from
            product_id: External product ID
            
        Returns:
            Product if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def setup_webhooks(self, store: EcommerceStore) -> Dict[str, Any]:
        """
        Set up webhooks for real-time sync.
        
        Args:
            store: The store to setup webhooks for
            
        Returns:
            Webhook setup results
        """
        pass
    
    @abstractmethod
    async def verify_webhook(self, payload: bytes, signature: str, secret: str) -> bool:
        """
        Verify webhook signature.
        
        Args:
            payload: Raw webhook payload
            signature: Webhook signature
            secret: Webhook secret
            
        Returns:
            True if signature is valid
        """
        pass
    
    async def get_platform_specific_fields(self) -> Dict[str, Any]:
        """
        Get platform-specific configuration fields.
        
        Returns:
            Dictionary of platform-specific fields and their requirements
        """
        return {}
    
    def _handle_api_error(self, error: Exception, operation: str) -> None:
        """
        Handle API errors consistently across platforms.
        
        Args:
            error: The exception that occurred
            operation: The operation that failed
        """
        logger.error(f"{self.platform_name} API error during {operation}: {str(error)}")
        
        # Log additional context if available
        if hasattr(error, 'response'):
            response = getattr(error, 'response', None)
            if response is not None:
                logger.error(f"Response status: {getattr(response, 'status_code', 'unknown')}")
                logger.error(f"Response body: {getattr(response, 'text', 'unknown')}")
    
    def _validate_store_credentials(self, store: EcommerceStore) -> bool:
        """
        Validate that store has required credentials.
        
        Args:
            store: Store to validate
            
        Returns:
            True if credentials are valid
        """
        if not store.access_token:
            return False
        
        # Platform-specific validation can be overridden
        return True
    
    def _prepare_product_data(self, raw_product: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare raw product data for SyncedProduct model.
        
        Args:
            raw_product: Raw product data from platform API
            
        Returns:
            Prepared product data
        """
        # Base implementation - platforms should override for specific mapping
        return raw_product
    
    async def _make_authenticated_request(
        self, 
        store: EcommerceStore, 
        method: str, 
        url: str, 
        **kwargs
    ) -> Dict[str, Any]:
        """
        Make an authenticated request to the platform API.
        
        Args:
            store: Store with authentication credentials
            method: HTTP method
            url: Request URL
            **kwargs: Additional request parameters
            
        Returns:
            API response data
        """
        # Base implementation - platforms should override for specific auth
        # Parameters are intentionally unused in base class
        _ = store, method, url, kwargs
        raise NotImplementedError("Platforms must implement authenticated requests")


class EcommerceIntegrationError(Exception):
    """Base exception for e-commerce integration errors."""
    pass


class AuthenticationError(EcommerceIntegrationError):
    """Authentication-related errors."""
    pass


class SyncError(EcommerceIntegrationError):
    """Product sync-related errors."""
    pass


class WebhookError(EcommerceIntegrationError):
    """Webhook-related errors."""
    pass

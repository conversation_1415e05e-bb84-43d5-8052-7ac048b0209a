/**
 * Enhanced ICP Generator Component - Enterprise-grade Ideal Customer Profile generation with intelligent analytics
 * Features: Subscription-based feature gating, comprehensive error handling, intelligent retry mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced ICP generation capabilities and interactive profile exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  CircularProgress,
  Divider,
  Paper,
  Chip,
  TextField,
  Alert,
  AlertTitle,
  Skeleton,
  LinearProgress,
  alpha,
  useTheme,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  MenuItem
} from '@mui/material';
import {
  Person as PersonIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Business as BusinessIcon,
  CheckCircle as CheckCircleIcon,
  Insights as InsightsIcon,
  AutoAwesome as AutoAwesomeIcon,
  Work as WorkIcon,
  Error as ErrorIcon,
  Settings as SettingsIcon,
  People as PeopleIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';

// Enhanced context and hook imports
// @ts-expect-error - SubscriptionContext is a JSX file without TypeScript declarations
import { useSubscription } from '../../contexts/SubscriptionContext';
// @ts-expect-error - useNotification is a JS file without TypeScript declarations
import { useNotification } from '../../hooks/useNotification';
import { useAccessibility } from '../../hooks/useAccessibility';

// Enhanced component imports
// @ts-expect-error - ErrorBoundary is a JSX file without TypeScript declarations
import ErrorBoundary from '../common/ErrorBoundary';
import api from '../../api';

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// ICP generation types with enhanced configurations
const ICP_GENERATION_TYPES = {
  DEMOGRAPHIC: {
    id: 'demographic',
    name: 'Demographic Analysis',
    description: 'Basic demographic profiling',
    subscriptionLimits: {
      creator: { available: true, profiles: 3 },
      accelerator: { available: true, profiles: 5 },
      dominator: { available: true, profiles: 10 }
    }
  },
  PSYCHOGRAPHIC: {
    id: 'psychographic',
    name: 'Psychographic Profiling',
    description: 'Advanced psychological and behavioral analysis',
    subscriptionLimits: {
      creator: { available: false, profiles: 0 },
      accelerator: { available: true, profiles: 3 },
      dominator: { available: true, profiles: 8 }
    }
  },
  BEHAVIORAL: {
    id: 'behavioral',
    name: 'Behavioral Insights',
    description: 'Deep behavioral pattern analysis',
    subscriptionLimits: {
      creator: { available: false, profiles: 0 },
      accelerator: { available: false, profiles: 0 },
      dominator: { available: true, profiles: 5 }
    }
  },
  MARKET_SEGMENTATION: {
    id: 'market_segmentation',
    name: 'Market Segmentation',
    description: 'Comprehensive market analysis and segmentation',
    subscriptionLimits: {
      creator: { available: false, profiles: 0 },
      accelerator: { available: false, profiles: 0 },
      dominator: { available: true, profiles: 3 }
    }
  }
};





// ===========================
// TYPE DEFINITIONS (JSDoc)
// ===========================

/**
 * @typedef {Object} ErrorInfo
 * @property {string} message - Error message
 * @property {string} stack - Error stack trace
 * @property {string} category - Error category
 * @property {number} timestamp - Error timestamp
 * @property {string} [code] - Error code
 * @property {Object} [metadata] - Additional error metadata
 */

/**
 * @typedef {Object} ServiceData
 * @property {string} id - Service ID
 * @property {string} name - Service name
 * @property {string} description - Service description
 * @property {string} value_proposition - Value proposition
 * @property {string} target_industry - Target industry
 * @property {string} service_level - Service level
 * @property {string} pricing_model - Pricing model
 */

/**
 * @typedef {Object} ICPDemographics
 * @property {string} industry - Industry
 * @property {string} company_size - Company size
 * @property {string} annual_revenue - Annual revenue
 * @property {string} employee_count - Employee count
 * @property {Array<string>} location - Locations
 */

/**
 * @typedef {Object} DecisionMaker
 * @property {string} title - Job title
 * @property {string} department - Department
 * @property {string} reporting_to - Reports to
 * @property {string} age_range - Age range
 * @property {string} years_of_experience - Years of experience
 * @property {string} education_level - Education level
 */

/**
 * @typedef {Object} PainPoint
 * @property {string} description - Pain point description
 * @property {string} severity - Severity level
 */

/**
 * @typedef {Object} Goal
 * @property {string} description - Goal description
 * @property {string} priority - Priority level
 */

/**
 * @typedef {Object} ContentPreference
 * @property {string} content_type - Content type
 * @property {string} tone - Tone preference
 * @property {string} content_length - Content length preference
 * @property {Array<string>} preferred_platforms - Preferred platforms
 */

/**
 * @typedef {Object} ICPData
 * @property {string} id - ICP ID
 * @property {string} name - ICP name
 * @property {string} description - ICP description
 * @property {ICPDemographics} demographics - Demographics data
 * @property {DecisionMaker} decision_maker - Decision maker data
 * @property {Array<PainPoint>} pain_points - Pain points
 * @property {Array<Goal>} goals - Goals
 * @property {Array<string>} objections - Objections
 * @property {Array<ContentPreference>} content_preferences - Content preferences
 * @property {string} buying_process - Buying process
 * @property {Array<string>} success_metrics - Success metrics
 * @property {boolean} is_ai_generated - Whether AI generated
 */

/**
 * @typedef {Object} SubscriptionFeatures
 * @property {string} planId - Subscription plan ID
 * @property {string} planName - Subscription plan name
 * @property {boolean} hasAdvancedICP - Whether plan includes advanced ICP
 * @property {boolean} hasPsychographics - Whether plan includes psychographics
 * @property {boolean} hasBehavioralInsights - Whether plan includes behavioral insights
 * @property {boolean} hasMarketSegmentation - Whether plan includes market segmentation
 * @property {boolean} hasErrorAnalytics - Whether plan includes error analytics
 * @property {boolean} hasCustomTemplates - Whether plan includes custom templates
 * @property {boolean} hasPrioritySupport - Whether plan includes priority support
 * @property {boolean} hasICPExport - Whether plan includes ICP export
 * @property {number} maxRetries - Maximum retry attempts
 * @property {number} maxICPCount - Maximum ICP count
 * @property {string} trackingLevel - Error tracking level
 */

/**
 * @typedef {Object} ComponentState
 * @property {boolean} loading - Loading state
 * @property {boolean} generating - Generation state
 * @property {boolean} retrying - Retry state
 * @property {boolean} showDetails - Show error details
 * @property {boolean} showRecovery - Show recovery options
 * @property {boolean} showUpgradeDialog - Show upgrade dialog
 * @property {boolean} showTemplateDialog - Show template dialog
 * @property {ServiceData} service - Service data
 * @property {Array<ICPData>} generatedICPs - Generated ICPs
 * @property {Array<string>} selectedICPs - Selected ICP IDs
 * @property {number} icpCount - ICP count to generate
 * @property {number} retryCount - Current retry count
 * @property {number} lastRetryTime - Last retry timestamp
 * @property {Array<string>} recoveryHistory - Recovery action history
 * @property {Object} performanceMetrics - Performance metrics
 * @property {string} [lastError] - Last error message
 */

/**
 * @typedef {Object} ICPGeneratorProps
 * @property {string} [serviceId] - Service ID override
 * @property {Function} [onICPGenerated] - ICP generation callback
 * @property {Function} [onICPSaved] - ICP save callback
 * @property {Function} [onError] - Error callback
 * @property {boolean} [showRecoveryOptions] - Show recovery options
 * @property {boolean} [enableAnalytics] - Enable error analytics
 * @property {string} [ariaLabel] - ARIA label
 * @property {string} [ariaDescription] - ARIA description
 * @property {string} [testId] - Test ID
 * @property {string} [className] - CSS class name
 * @property {Object} [style] - Inline styles
 */

/**
 * @typedef {Object} ICPGeneratorHandle
 * @property {Function} generateICPs - Generate ICPs function
 * @property {Function} saveICPs - Save ICPs function
 * @property {Function} retry - Retry function
 * @property {Function} reset - Reset function
 * @property {Function} getGeneratedICPs - Get generated ICPs
 * @property {Function} getSelectedICPs - Get selected ICPs
 * @property {Function} getErrorInfo - Get error information
 * @property {Function} getPerformanceMetrics - Get performance metrics
 * @property {Function} focus - Focus function
 * @property {Function} announce - Screen reader announcement
 */

/**
 * Enhanced ICPGenerator Component - Enterprise-grade ICP generation with intelligent analytics
 * Features: Subscription-based feature gating, comprehensive error handling, intelligent retry mechanisms,
 * accessibility compliance, performance monitoring, and ACE Social platform integration
 * with advanced ICP generation capabilities and interactive profile exploration
 *
 * @component
 * @param {ICPGeneratorProps} props - Component props
 * @returns {React.Component} Enhanced ICP generator component
 */
/* eslint-disable no-unused-vars, @typescript-eslint/no-unused-vars */
const ICPGenerator = memo(forwardRef((props, ref) => {
  const {
    serviceId: propServiceId,
    onICPGenerated,
    onICPSaved,
    onError,
    ariaLabel,
    ariaDescription,
    testId = 'icp-generator',
    className = '',
    style = {}
  } = props;

  const navigate = useNavigate();
  const { serviceId: paramServiceId } = useParams();
  const serviceId = propServiceId || paramServiceId;

  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();


  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();



  // Enhanced state management with comprehensive error handling
  const [state, setState] = useState(/** @type {ComponentState} */ ({
    loading: subscriptionLoading,
    generating: false,
    retrying: false,
    showDetails: false,
    showRecovery: false,
    showUpgradeDialog: false,
    showTemplateDialog: false,
    service: null,
    generatedICPs: [],
    selectedICPs: [],
    icpCount: 3,
    generationType: 'demographic',
    retryCount: 0,
    lastRetryTime: 0,
    recoveryHistory: [],
    performanceMetrics: {
      errorCount: 0,
      lastErrorTime: null,
      recoveryAttempts: 0,
      successfulRecoveries: 0,
      generationTime: null,
      icpsGenerated: 0
    },
    lastError: null
  }));

  // Enhanced refs for comprehensive tracking
  const containerRef = useRef(null);
  const generateButtonRef = useRef(null);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   * @returns {SubscriptionFeatures} Subscription features object
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    // Simple plan-based features
    const features = {
      creator: {
        hasAdvancedICP: false,
        hasPsychographics: false,
        hasBehavioralInsights: false,
        hasMarketSegmentation: false,
        hasErrorAnalytics: false,
        hasCustomTemplates: false,
        hasPrioritySupport: false,
        hasICPExport: true,
        maxRetries: 3,
        maxICPCount: 3,
        trackingLevel: 'basic'
      },
      accelerator: {
        hasAdvancedICP: true,
        hasPsychographics: true,
        hasBehavioralInsights: false,
        hasMarketSegmentation: false,
        hasErrorAnalytics: true,
        hasCustomTemplates: false,
        hasPrioritySupport: false,
        hasICPExport: true,
        maxRetries: 5,
        maxICPCount: 5,
        trackingLevel: 'advanced'
      },
      dominator: {
        hasAdvancedICP: true,
        hasPsychographics: true,
        hasBehavioralInsights: true,
        hasMarketSegmentation: true,
        hasErrorAnalytics: true,
        hasCustomTemplates: true,
        hasPrioritySupport: true,
        hasICPExport: true,
        maxRetries: 10,
        maxICPCount: 10,
        trackingLevel: 'ai-powered'
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      ...currentFeatures
    };
  }, [subscription?.plan_id, subscription?.plan_name]);

  /**
   * Get available ICP generation types based on subscription - Production Ready
   */
  const getAvailableGenerationTypes = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    return Object.values(ICP_GENERATION_TYPES).filter(type =>
      type.subscriptionLimits[planId]?.available
    );
  }, [subscription?.plan_id]);

  /**
   * Check if generation type is available for current plan - Production Ready
   */
  const isGenerationTypeAvailable = useCallback((typeId) => {
    const planId = subscription?.plan_id || 'creator';
    const type = ICP_GENERATION_TYPES[typeId.toUpperCase()];
    return type?.subscriptionLimits[planId]?.available || false;
  }, [subscription?.plan_id]);



  // Fetch service details
  useEffect(() => {
    const fetchService = async () => {
      setState(prev => ({ ...prev, loading: true }));
      try {
        const response = await api.get(`/api/services/${serviceId}`);
        setState(prev => ({ ...prev, service: response.data }));
      } catch (error) {
        console.error('Error fetching service:', error);
        const errorMessage = error.response?.status === 404
          ? 'Service not found'
          : error.response?.status === 403
          ? 'Access denied to this service'
          : error.response?.data?.detail || 'Failed to load service details';

        showErrorNotification(errorMessage);
        if (onError) onError(error);

        if (error.response?.status === 404) {
          navigate('/services');
        }
      } finally {
        setState(prev => ({ ...prev, loading: false }));
      }
    };

    if (serviceId) {
      fetchService();
    }
  }, [serviceId, showErrorNotification, navigate, onError]);

  /**
   * Enhanced ICP generation handler with intelligent retry - Production Ready
   * @returns {Promise<void>}
   */
  const handleGenerateICPs = useCallback(async () => {
    if (!state.service) {
      const errorMessage = 'Service data not loaded. Please refresh the page.';
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
      return;
    }

    if (state.icpCount > subscriptionFeatures.maxICPCount) {
      const errorMessage = `Your ${subscriptionFeatures.planName} plan allows up to ${subscriptionFeatures.maxICPCount} ICPs. Please reduce the count or upgrade your plan.`;
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
      return;
    }

    try {
      setState(prev => ({
        ...prev,
        generating: true,
        performanceMetrics: {
          ...prev.performanceMetrics,
          generationTime: Date.now()
        }
      }));

      announceToScreenReader(`Generating ${state.icpCount} ICPs...`);

      const response = await api.post('/api/icps/generate', {
        service_id: serviceId,
        count: state.icpCount,
        generation_type: state.generationType,
        subscription_plan: subscriptionFeatures.planId
      });

      // Handle different response formats
      const icpsData = response.data?.icps || response.data || [];
      const icps = Array.isArray(icpsData) ? icpsData : [];

      if (icps.length === 0) {
        const errorMessage = 'No ICPs were generated. Please try again with different parameters.';
        showErrorNotification(errorMessage);
        announceToScreenReader(errorMessage);
        return;
      }

      setState(prev => ({
        ...prev,
        generatedICPs: icps,
        selectedICPs: icps.map(icp => icp.id), // Select all by default
        performanceMetrics: {
          ...prev.performanceMetrics,
          generationTime: Date.now() - prev.performanceMetrics.generationTime,
          icpsGenerated: icps.length
        }
      }));

      const successMessage = `Successfully generated ${icps.length} ICPs!`;
      showSuccessNotification(successMessage);
      announceToScreenReader(successMessage);

      if (onICPGenerated) onICPGenerated(icps);

      // Track analytics
      if (window.analytics) {
        window.analytics.track('ICP Generation Success', {
          icpCount: icps.length,
          serviceId,
          subscriptionPlan: subscriptionFeatures.planId,
          generationTime: state.performanceMetrics.generationTime,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('Error generating ICPs:', error);
      let errorMessage = 'Failed to generate ICPs. Please try again.';

      if (error.response?.status === 403) {
        errorMessage = 'ICP generation not available in your plan';
      } else if (error.response?.status === 400) {
        errorMessage = 'Invalid service data. Please check your service configuration.';
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }

      setState(prev => ({
        ...prev,
        lastError: errorMessage,
        performanceMetrics: {
          ...prev.performanceMetrics,
          errorCount: prev.performanceMetrics.errorCount + 1,
          lastErrorTime: Date.now()
        }
      }));

      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
      if (onError) onError(error);

      // Track analytics
      if (window.analytics) {
        window.analytics.track('ICP Generation Error', {
          errorMessage,
          serviceId,
          subscriptionPlan: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }

    } finally {
      setState(prev => ({ ...prev, generating: false }));
    }
  }, [
    state.service,
    state.icpCount,
    state.generationType,
    state.performanceMetrics.generationTime,
    subscriptionFeatures.maxICPCount,
    subscriptionFeatures.planName,
    subscriptionFeatures.planId,
    serviceId,
    showErrorNotification,
    showSuccessNotification,
    announceToScreenReader,
    onICPGenerated,
    onError
  ]);

  /**
   * Toggle ICP selection - Production Ready
   * @param {string} icpId - ICP ID to toggle
   */
  const handleToggleICP = useCallback((icpId) => {
    setState(prev => ({
      ...prev,
      selectedICPs: prev.selectedICPs.includes(icpId)
        ? prev.selectedICPs.filter(id => id !== icpId)
        : [...prev.selectedICPs, icpId]
    }));

    announceToScreenReader(`ICP ${icpId} ${state.selectedICPs.includes(icpId) ? 'deselected' : 'selected'}`);
  }, [state.selectedICPs, announceToScreenReader]);

  /**
   * Save selected ICPs - Production Ready
   * @returns {Promise<void>}
   */
  const handleSaveICPs = useCallback(async () => {
    if (state.selectedICPs.length === 0) {
      const errorMessage = 'Please select at least one ICP to save';
      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true }));
      announceToScreenReader(`Saving ${state.selectedICPs.length} selected ICPs...`);

      // Save each selected ICP to the database
      const savePromises = state.selectedICPs.map(async (icpId) => {
        const icp = state.generatedICPs.find(i => i.id === icpId);
        if (!icp) return null;

        // Create ICP data for saving
        const icpData = {
          name: icp.name,
          description: icp.description,
          service_id: serviceId,
          demographics: icp.demographics,
          decision_maker: icp.decision_maker,
          pain_points: icp.pain_points,
          goals: icp.goals,
          objections: icp.objections || [],
          content_preferences: icp.content_preferences,
          buying_process: icp.buying_process,
          success_metrics: icp.success_metrics,
          is_ai_generated: true
        };

        const response = await api.post('/api/icps/icps', icpData);
        return response.data;
      });

      const savedICPs = await Promise.all(savePromises);
      const successCount = savedICPs.filter(icp => icp !== null).length;

      const successMessage = `Successfully saved ${successCount} ICPs!`;
      showSuccessNotification(successMessage);
      announceToScreenReader(successMessage);

      if (onICPSaved) onICPSaved(savedICPs);

      // Track analytics
      if (window.analytics) {
        window.analytics.track('ICP Save Success', {
          icpCount: successCount,
          serviceId,
          subscriptionPlan: subscriptionFeatures.planId,
          timestamp: new Date().toISOString()
        });
      }

      navigate(`/services/${serviceId}/icps`);
    } catch (error) {
      console.error('Error saving ICPs:', error);
      const errorMessage = error.response?.status === 403
        ? 'ICP saving not available in your plan'
        : error.response?.data?.detail || 'Failed to save ICPs';

      showErrorNotification(errorMessage);
      announceToScreenReader(errorMessage);
      if (onError) onError(error);
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [
    state.selectedICPs,
    state.generatedICPs,
    serviceId,
    showErrorNotification,
    showSuccessNotification,
    announceToScreenReader,
    onICPSaved,
    onError,
    subscriptionFeatures.planId,
    navigate
  ]);

  /**
   * Enhanced imperative handle for parent component access - Production Ready
   */
  useImperativeHandle(ref, () => ({
    generateICPs: handleGenerateICPs,
    saveICPs: handleSaveICPs,
    retry: () => {
      setState(prev => ({
        ...prev,
        retryCount: 0,
        lastRetryTime: 0,
        recoveryHistory: [],
        lastError: null,
        showDetails: false,
        showRecovery: false
      }));
      handleGenerateICPs();
    },
    reset: () => {
      setState(prev => ({
        ...prev,
        generatedICPs: [],
        selectedICPs: [],
        icpCount: 3,
        retryCount: 0,
        lastRetryTime: 0,
        recoveryHistory: [],
        lastError: null,
        showDetails: false,
        showRecovery: false,
        showUpgradeDialog: false,
        showTemplateDialog: false
      }));
      announceToScreenReader('ICP generator reset');
    },
    getGeneratedICPs: () => state.generatedICPs,
    getSelectedICPs: () => state.selectedICPs,
    getErrorInfo: () => ({
      lastError: state.lastError,
      retryCount: state.retryCount,
      lastRetryTime: state.lastRetryTime
    }),
    getPerformanceMetrics: () => state.performanceMetrics,
    focus: () => {
      if (containerRef.current) {
        setFocusToElement(containerRef.current);
      }
    },
    announce: (message) => announceToScreenReader(message)
  }), [
    handleGenerateICPs,
    handleSaveICPs,
    state.generatedICPs,
    state.selectedICPs,
    state.lastError,
    state.retryCount,
    state.lastRetryTime,
    state.performanceMetrics,
    setFocusToElement,
    announceToScreenReader
  ]);

  // Render loading state
  if (state.loading && !state.service) {
    return (
      <ErrorBoundary>
        <Box
          sx={{ py: 3 }}
          ref={containerRef}
          data-testid={testId}
          className={className}
          style={style}
        >
          <Skeleton variant="text" width="50%" height={60} />
          <Skeleton variant="rectangular" width="100%" height={200} sx={{ my: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={400} />
        </Box>
      </ErrorBoundary>
    );
  }

  // Main render
  return (
    <ErrorBoundary>
      <Box
        sx={{ py: 3 }}
        ref={containerRef}
        role="main"
        aria-label={ariaLabel || 'ICP Generator'}
        aria-description={ariaDescription || 'Generate and manage Ideal Customer Profiles'}
        data-testid={testId}
        className={className}
        style={style}
      >
        <Typography variant="h4" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
          <PersonIcon sx={{ mr: 1, verticalAlign: 'middle', color: ACE_COLORS.PURPLE }} />
          Generate Ideal Customer Profiles
        </Typography>

        {/* Subscription Badge */}
        <Chip
          label={`${subscriptionFeatures.planName} Plan - Up to ${subscriptionFeatures.maxICPCount} ICPs`}
          size="small"
          sx={{
            mb: 3,
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
            color: ACE_COLORS.PURPLE,
            fontWeight: 600
          }}
        />

        {state.service && (
          <Card sx={{ mb: 4, border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                <BusinessIcon sx={{ mr: 1, verticalAlign: 'middle', color: ACE_COLORS.PURPLE }} />
                Service: {state.service.name || 'Unnamed Service'}
              </Typography>

              <Typography variant="body1" paragraph>
                {state.service.description || 'No description available'}
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={4}>
                  <Paper sx={{ p: 2, height: '100%', backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05) }}>
                    <Typography variant="subtitle2" color="textSecondary" sx={{ fontWeight: 600 }}>
                      Value Proposition
                    </Typography>
                    <Typography variant="body2">
                      {state.service.value_proposition || 'No value proposition defined'}
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Paper sx={{ p: 2, height: '100%', backgroundColor: alpha(ACE_COLORS.YELLOW, 0.05) }}>
                    <Typography variant="subtitle2" color="textSecondary" sx={{ fontWeight: 600 }}>
                      Target Industry
                    </Typography>
                    <Typography variant="body2">
                      {state.service.target_industry || 'No target industry specified'}
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Paper sx={{ p: 2, height: '100%', backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05) }}>
                    <Typography variant="subtitle2" color="textSecondary" sx={{ fontWeight: 600 }}>
                      Service Details
                    </Typography>
                    <Typography variant="body2">
                      {state.service.service_level ?
                        `${state.service.service_level.charAt(0).toUpperCase() + state.service.service_level.slice(1)} level` :
                        'Service level not specified'
                      }{state.service.service_level && state.service.pricing_model ? ', ' : ''}
                      {state.service.pricing_model ?
                        `${state.service.pricing_model.charAt(0).toUpperCase() + state.service.pricing_model.slice(1)} pricing` :
                        state.service.service_level ? '' : 'Pricing model not specified'
                      }
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        <Card sx={{ mb: 4, border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
              <AutoAwesomeIcon sx={{ mr: 1, verticalAlign: 'middle', color: ACE_COLORS.PURPLE }} />
              Generate ICPs
            </Typography>

            <Typography variant="body2" color="textSecondary" paragraph>
              Our AI will analyze your service details and generate detailed Ideal Customer Profiles (ICPs).
              These profiles will help you create targeted content and campaigns.
            </Typography>

            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  fullWidth
                  select
                  label="ICP Generation Type"
                  value={state.generationType}
                  onChange={(e) => setState(prev => ({ ...prev, generationType: e.target.value }))}
                  helperText={`Available types for ${subscriptionFeatures.planName} plan`}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&.Mui-focused fieldset': {
                        borderColor: ACE_COLORS.PURPLE,
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: ACE_COLORS.PURPLE,
                    },
                  }}
                >
                  {getAvailableGenerationTypes.map((type) => (
                    <MenuItem key={type.id} value={type.id}>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {type.name}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {type.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <TextField
                  fullWidth
                  label="Number of ICPs to Generate"
                  type="number"
                  value={state.icpCount}
                  onChange={(e) => setState(prev => ({
                    ...prev,
                    icpCount: Math.max(1, Math.min(subscriptionFeatures.maxICPCount, parseInt(e.target.value) || 1))
                  }))}
                  inputProps={{ min: 1, max: subscriptionFeatures.maxICPCount }}
                  helperText={`Generate between 1-${subscriptionFeatures.maxICPCount} ICPs (${subscriptionFeatures.planName} plan)`}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&.Mui-focused fieldset': {
                        borderColor: ACE_COLORS.PURPLE,
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: ACE_COLORS.PURPLE,
                    },
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Button
                  ref={generateButtonRef}
                  variant="contained"
                  size="large"
                  fullWidth
                  onClick={handleGenerateICPs}
                  disabled={state.generating || !state.service}
                  startIcon={state.generating ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
                  sx={{
                    backgroundColor: ACE_COLORS.PURPLE,
                    color: ACE_COLORS.WHITE,
                    '&:hover': {
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                    },
                    '&:disabled': {
                      backgroundColor: alpha(ACE_COLORS.PURPLE, 0.3)
                    }
                  }}
                >
                  {state.generating ? 'Generating...' : 'Generate ICPs'}
                </Button>
              </Grid>
            </Grid>

            {/* Advanced Features Upgrade Prompt */}
            {subscriptionFeatures.planId === 'creator' && (
              <Alert
                severity="info"
                sx={{
                  mt: 2,
                  backgroundColor: alpha(ACE_COLORS.YELLOW, 0.05),
                  border: `1px solid ${alpha(ACE_COLORS.YELLOW, 0.2)}`
                }}
                action={
                  <Button
                    size="small"
                    sx={{ color: ACE_COLORS.PURPLE }}
                    onClick={() => window.open('/pricing', '_blank')}
                  >
                    Upgrade
                  </Button>
                }
              >
                <AlertTitle>Unlock Advanced ICP Features</AlertTitle>
                Upgrade to Accelerator or Dominator plan for psychographic profiling, behavioral insights, and market segmentation analysis.
              </Alert>
            )}

            {/* Generation Progress */}
            {state.generating && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: ACE_COLORS.PURPLE
                    }
                  }}
                />
                <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                  Analyzing service data and generating {state.icpCount} ICPs...
                </Typography>
              </Box>
            )}
          </CardContent>
        </Card>

        {state.generatedICPs.length > 0 && (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box>
                <Typography variant="h5" sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                  <InsightsIcon sx={{ mr: 1, verticalAlign: 'middle', color: ACE_COLORS.PURPLE }} />
                  Generated ICPs ({state.selectedICPs.length}/{state.generatedICPs.length} selected)
                </Typography>
                <Chip
                  label={`${ICP_GENERATION_TYPES[state.generationType.toUpperCase()]?.name || 'Demographic Analysis'} Type`}
                  size="small"
                  sx={{
                    mt: 1,
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    color: ACE_COLORS.PURPLE,
                    fontWeight: 600
                  }}
                />
              </Box>

              <Button
                variant="contained"
                onClick={handleSaveICPs}
                disabled={state.selectedICPs.length === 0 || state.loading}
                startIcon={state.loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                sx={{
                  backgroundColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.WHITE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                  },
                  '&:disabled': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.3)
                  }
                }}
              >
                {state.loading ? 'Saving...' : 'Save Selected ICPs'}
              </Button>
            </Box>

            <Alert
              severity="info"
              sx={{
                mb: 3,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle>ICP Selection</AlertTitle>
              Select the ICPs you want to save. You can use these to generate targeted content later.
            </Alert>

            <Grid container spacing={3}>
              {state.generatedICPs.map((icp) => (
                <Grid item xs={12} key={icp.id}>
                  <Card
                    sx={{
                      borderColor: state.selectedICPs.includes(icp.id) ? ACE_COLORS.PURPLE : alpha(ACE_COLORS.PURPLE, 0.2),
                      borderWidth: state.selectedICPs.includes(icp.id) ? 2 : 1,
                      borderStyle: 'solid',
                      backgroundColor: state.selectedICPs.includes(icp.id) ? alpha(ACE_COLORS.PURPLE, 0.02) : 'background.paper',
                      transition: 'all 0.2s ease-in-out',
                      '&:hover': {
                        borderColor: ACE_COLORS.PURPLE,
                        boxShadow: `0 4px 12px ${alpha(ACE_COLORS.PURPLE, 0.15)}`
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box>
                          <Typography variant="h6" gutterBottom sx={{ color: ACE_COLORS.DARK, fontWeight: 600 }}>
                            {icp.name || 'Unnamed ICP'}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {icp.description || 'No description available'}
                          </Typography>
                        </Box>

                        <Button
                          variant={state.selectedICPs.includes(icp.id) ? "contained" : "outlined"}
                          onClick={() => handleToggleICP(icp.id)}
                          startIcon={state.selectedICPs.includes(icp.id) ? <CheckCircleIcon /> : null}
                          sx={{
                            backgroundColor: state.selectedICPs.includes(icp.id) ? ACE_COLORS.PURPLE : 'transparent',
                            borderColor: ACE_COLORS.PURPLE,
                            color: state.selectedICPs.includes(icp.id) ? ACE_COLORS.WHITE : ACE_COLORS.PURPLE,
                            '&:hover': {
                              backgroundColor: state.selectedICPs.includes(icp.id) ? alpha(ACE_COLORS.PURPLE, 0.8) : alpha(ACE_COLORS.PURPLE, 0.1),
                              borderColor: ACE_COLORS.PURPLE
                            }
                          }}
                        >
                          {state.selectedICPs.includes(icp.id) ? 'Selected' : 'Select'}
                        </Button>
                      </Box>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Accordion defaultExpanded>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">
                          <BusinessIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                          Company Demographics
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6} md={3}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Industry
                              </Typography>
                              <Typography variant="body2">
                                {icp.demographics?.industry || 'Not specified'}
                              </Typography>
                            </Paper>
                          </Grid>
                          
                          <Grid item xs={12} sm={6} md={3}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Company Size
                              </Typography>
                              <Typography variant="body2">
                                {icp.demographics?.company_size || 'Not specified'}
                              </Typography>
                            </Paper>
                          </Grid>

                          <Grid item xs={12} sm={6} md={3}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Annual Revenue
                              </Typography>
                              <Typography variant="body2">
                                {icp.demographics?.annual_revenue || 'Not specified'}
                              </Typography>
                            </Paper>
                          </Grid>

                          <Grid item xs={12} sm={6} md={3}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Employee Count
                              </Typography>
                              <Typography variant="body2">
                                {icp.demographics?.employee_count || 'Not specified'}
                              </Typography>
                            </Paper>
                          </Grid>
                          
                          <Grid item xs={12}>
                            <Typography variant="subtitle2" color="textSecondary">
                              Locations
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                              {(icp.demographics?.location || []).map((loc, i) => (
                                <Chip key={i} label={loc} size="small" />
                              ))}
                              {(!icp.demographics?.location || icp.demographics.location.length === 0) && (
                                <Typography variant="body2" color="textSecondary">
                                  No locations specified
                                </Typography>
                              )}
                            </Box>
                          </Grid>
                        </Grid>
                      </AccordionDetails>
                    </Accordion>
                    
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">
                          <WorkIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                          Decision Maker
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6} md={4}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Title
                              </Typography>
                              <Typography variant="body2">
                                {icp.decision_maker.title}
                              </Typography>
                            </Paper>
                          </Grid>
                          
                          <Grid item xs={12} sm={6} md={4}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Department
                              </Typography>
                              <Typography variant="body2">
                                {icp.decision_maker.department}
                              </Typography>
                            </Paper>
                          </Grid>
                          
                          <Grid item xs={12} sm={6} md={4}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Reports To
                              </Typography>
                              <Typography variant="body2">
                                {icp.decision_maker.reporting_to || 'N/A'}
                              </Typography>
                            </Paper>
                          </Grid>
                          
                          <Grid item xs={12} sm={6} md={4}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Age Range
                              </Typography>
                              <Typography variant="body2">
                                {icp.decision_maker.age_range}
                              </Typography>
                            </Paper>
                          </Grid>
                          
                          <Grid item xs={12} sm={6} md={4}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Experience
                              </Typography>
                              <Typography variant="body2">
                                {icp.decision_maker.years_of_experience}
                              </Typography>
                            </Paper>
                          </Grid>
                          
                          <Grid item xs={12} sm={6} md={4}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Education
                              </Typography>
                              <Typography variant="body2">
                                {icp.decision_maker.education_level || 'Not specified'}
                              </Typography>
                            </Paper>
                          </Grid>
                        </Grid>
                      </AccordionDetails>
                    </Accordion>
                    
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">
                          <ErrorIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                          Pain Points & Goals
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Grid container spacing={3}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" gutterBottom>
                              Pain Points
                            </Typography>
                            <List>
                              {(icp.pain_points || []).map((pain, index) => (
                                <Paper key={index} sx={{ mb: 1, p: 1 }}>
                                  <ListItem disablePadding>
                                    <ListItemText
                                      primary={pain.description || 'No description'}
                                      secondary={`Severity: ${pain.severity || 'Not specified'}`}
                                    />
                                  </ListItem>
                                </Paper>
                              ))}
                              {(!icp.pain_points || icp.pain_points.length === 0) && (
                                <Typography variant="body2" color="textSecondary" sx={{ p: 2 }}>
                                  No pain points identified
                                </Typography>
                              )}
                            </List>
                          </Grid>
                          
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" gutterBottom>
                              Goals
                            </Typography>
                            <List>
                              {(icp.goals || []).map((goal, index) => (
                                <Paper key={index} sx={{ mb: 1, p: 1 }}>
                                  <ListItem disablePadding>
                                    <ListItemText
                                      primary={goal.description || 'No description'}
                                      secondary={`Priority: ${goal.priority || 'Not specified'}`}
                                    />
                                  </ListItem>
                                </Paper>
                              ))}
                              {(!icp.goals || icp.goals.length === 0) && (
                                <Typography variant="body2" color="textSecondary" sx={{ p: 2 }}>
                                  No goals identified
                                </Typography>
                              )}
                            </List>
                          </Grid>
                        </Grid>
                      </AccordionDetails>
                    </Accordion>
                    
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">
                          <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                          Content Preferences
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Grid container spacing={2}>
                          {icp.content_preferences.map((pref, index) => (
                            <Grid item xs={12} sm={6} md={4} key={index}>
                              <Paper sx={{ p: 2 }}>
                                <Typography variant="subtitle2" color="textSecondary">
                                  {pref.content_type}
                                </Typography>
                                <Typography variant="body2" paragraph>
                                  Tone: {pref.tone}, Length: {pref.content_length}
                                </Typography>
                                <Typography variant="caption" color="textSecondary">
                                  Preferred Platforms:
                                </Typography>
                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                                  {pref.preferred_platforms.map((platform, i) => (
                                    <Chip key={i} label={platform} size="small" variant="outlined" />
                                  ))}
                                </Box>
                              </Paper>
                            </Grid>
                          ))}
                        </Grid>
                      </AccordionDetails>
                    </Accordion>
                    
                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">
                          <PeopleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                          Buying Process & Success Metrics
                        </Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Grid container spacing={3}>
                          <Grid item xs={12} md={6}>
                            <Paper sx={{ p: 2 }}>
                              <Typography variant="subtitle2" color="textSecondary">
                                Buying Process
                              </Typography>
                              <Typography variant="body2">
                                {icp.buying_process}
                              </Typography>
                            </Paper>
                          </Grid>
                          
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                              Success Metrics
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {icp.success_metrics.map((metric, i) => (
                                <Chip key={i} label={metric} color="primary" variant="outlined" />
                              ))}
                            </Box>
                          </Grid>
                        </Grid>
                      </AccordionDetails>
                    </Accordion>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
          
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Button
                variant="contained"
                size="large"
                onClick={handleSaveICPs}
                disabled={state.selectedICPs.length === 0 || state.loading}
                startIcon={state.loading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                sx={{
                  backgroundColor: ACE_COLORS.PURPLE,
                  color: ACE_COLORS.WHITE,
                  '&:hover': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8)
                  },
                  '&:disabled': {
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.3)
                  }
                }}
              >
                {state.loading ? 'Saving...' : `Save ${state.selectedICPs.length} Selected ICPs`}
              </Button>
            </Box>
          </>
        )}
      </Box>
    </ErrorBoundary>
  );
}));
/* eslint-enable no-unused-vars, @typescript-eslint/no-unused-vars */

// Enhanced PropTypes validation for comprehensive type checking
ICPGenerator.propTypes = {
  /** Service ID override */
  serviceId: PropTypes.string,

  /** ICP generation callback function */
  onICPGenerated: PropTypes.func,

  /** ICP save callback function */
  onICPSaved: PropTypes.func,

  /** Error callback function */
  onError: PropTypes.func,

  /** ARIA label for accessibility */
  ariaLabel: PropTypes.string,

  /** ARIA description for accessibility */
  ariaDescription: PropTypes.string,

  /** Test ID for testing */
  testId: PropTypes.string,

  /** CSS class name */
  className: PropTypes.string,

  /** Inline styles */
  style: PropTypes.object
};

// Default props for comprehensive fallback behavior
ICPGenerator.defaultProps = {
  serviceId: null,
  onICPGenerated: null,
  onICPSaved: null,
  onError: null,
  ariaLabel: '',
  ariaDescription: '',
  testId: 'icp-generator',
  className: '',
  style: {}
};

// Enhanced display name for debugging and development tools
ICPGenerator.displayName = 'ICPGenerator';

export default ICPGenerator;

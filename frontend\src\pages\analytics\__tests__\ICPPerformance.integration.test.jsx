// @since 2024-1-1 to 2025-25-7
import React from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import ICPPerformance from '../ICPPerformance';
import { NotificationProvider } from '../../../contexts/NotificationContext';

// Mock server setup
const server = setupServer(
  rest.get('/api/icp-performance/', (req, res, ctx) => {
    return res(ctx.json([
      {
        icp_id: '1',
        icp_name: 'Tech Enthusiasts',
        avg_engagement_rate: 15.5,
        total_content: 25
      },
      {
        icp_id: '2',
        icp_name: 'Business Professionals',
        avg_engagement_rate: 12.3,
        total_content: 18
      }
    ]));
  }),

  rest.get('/api/icp-performance/top', (req, res, ctx) => {
    return res(ctx.json([
      {
        icp_id: '1',
        icp_name: 'Tech Enthusiasts',
        avg_engagement_rate: 15.5,
        total_content: 25
      }
    ]));
  }),

  rest.post('/api/icp-performance/compare', (req, res, ctx) => {
    return res(ctx.json({
      icps: [
        {
          icp_id: '1',
          icp_name: 'Tech Enthusiasts',
          avg_engagement_rate: 15.5
        },
        {
          icp_id: '2',
          icp_name: 'Business Professionals',
          avg_engagement_rate: 12.3
        }
      ],
      best_performing_icp_name: 'Tech Enthusiasts',
      performance_gap: {
        avg_engagement_rate: 3.2
      },
      recommendations: ['Focus on tech content', 'Post during peak hours']
    }));
  }),

  rest.post('/api/icp-performance/analyze', (req, res, ctx) => {
    return res(ctx.json({
      icp_id: '1',
      icp_name: 'Tech Enthusiasts',
      avg_engagement_rate: 16.2,
      total_content: 26
    }));
  }),

  rest.post('/api/icp-performance/recommendations', (req, res, ctx) => {
    return res(ctx.json({
      icp_name: 'Tech Enthusiasts',
      optimal_content_types: [
        { type: 'Video', engagement_rate: 18.5 }
      ],
      optimal_platforms: [
        { platform: 'LinkedIn', engagement_rate: 16.2 }
      ],
      optimal_posting_times: {
        LinkedIn: ['9:00 AM', '2:00 PM']
      },
      recommendations: [
        { value: 'Post more videos', reason: 'Higher engagement' }
      ]
    }));
  }),

  rest.get('/api/icp-performance/:id/export', (req, res, ctx) => {
    return res(
      ctx.set('Content-Type', 'text/csv'),
      ctx.body('ICP Name,Engagement Rate\nTech Enthusiasts,15.5')
    );
  }),

  rest.post('/api/icp-performance/compare/export', (req, res, ctx) => {
    return res(
      ctx.set('Content-Type', 'text/csv'),
      ctx.body('ICP Name,Engagement Rate\nTech Enthusiasts,15.5\nBusiness Professionals,12.3')
    );
  })
);

// Mock D3 and ResizeObserver
jest.mock('d3', () => ({
  select: jest.fn(() => ({
    selectAll: jest.fn(() => ({
      remove: jest.fn()
    })),
    append: jest.fn(() => ({
      attr: jest.fn(() => ({
        attr: jest.fn(() => ({
          append: jest.fn(() => ({
            attr: jest.fn(() => ({}))
          }))
        }))
      }))
    }))
  })),
  scaleBand: jest.fn(() => ({
    range: jest.fn(() => ({
      domain: jest.fn(() => ({
        padding: jest.fn(() => ({}))
      }))
    }))
  })),
  scaleLinear: jest.fn(() => ({
    domain: jest.fn(() => ({
      range: jest.fn(() => ({}))
    }))
  })),
  max: jest.fn(() => 100),
  axisBottom: jest.fn(() => ({})),
  axisLeft: jest.fn(() => ({})),
  easeBackOut: jest.fn()
}));

global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Test wrapper
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return (
    <ThemeProvider theme={theme}>
      <NotificationProvider>
        {children}
      </NotificationProvider>
    </ThemeProvider>
  );
};

describe('ICPPerformance Integration Tests', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  describe('Full User Workflow', () => {
    test('complete overview workflow with real API calls', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      // Wait for initial data load
      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
        expect(screen.getByText('15.50%')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Test refresh functionality
      const refreshButton = screen.getByLabelText(/refresh all data/i);
      await user.click(refreshButton);

      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Test filter functionality
      const filterButton = screen.getByLabelText(/toggle filters/i);
      await user.click(filterButton);

      expect(screen.getByText(/filter & sort options/i)).toBeInTheDocument();

      // Test individual ICP analysis
      const analyzeButton = screen.getByLabelText(/refresh analysis for tech enthusiasts/i);
      await user.click(analyzeButton);

      await waitFor(() => {
        expect(screen.getByText(/analyzing/i)).toBeInTheDocument();
      });
    });

    test('complete comparison workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Switch to Compare tab
      const compareTab = screen.getByRole('tab', { name: /compare icps/i });
      await user.click(compareTab);

      // Wait for compare interface
      await waitFor(() => {
        expect(screen.getByText(/compare icp performance/i)).toBeInTheDocument();
      });

      // Select ICPs for comparison
      const selectElement = screen.getByLabelText(/select multiple icps to compare/i);
      await user.click(selectElement);

      // Select both ICPs
      const techOption = screen.getByText('Tech Enthusiasts');
      await user.click(techOption);

      const businessOption = screen.getByText('Business Professionals');
      await user.click(businessOption);

      // Perform comparison
      const compareButton = screen.getByLabelText(/compare 2 selected icps/i);
      await user.click(compareButton);

      // Wait for comparison results
      await waitFor(() => {
        expect(screen.getByText(/comparison results/i)).toBeInTheDocument();
        expect(screen.getByText(/best performing icp: tech enthusiasts/i)).toBeInTheDocument();
      });

      // Test chart rendering
      expect(screen.getByLabelText(/bar chart comparing engagement rates/i)).toBeInTheDocument();
    });

    test('complete recommendations workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Tech Enthusiasts')).toBeInTheDocument();
      });

      // Switch to Recommendations tab
      const recommendationsTab = screen.getByRole('tab', { name: /recommendations/i });
      await user.click(recommendationsTab);

      // Wait for recommendations interface
      await waitFor(() => {
        expect(screen.getByText(/get content recommendations for icp/i)).toBeInTheDocument();
      });

      // Select an ICP
      const selectElement = screen.getByLabelText(/select an icp to get content recommendations/i);
      await user.click(selectElement);

      const techOption = screen.getByText('Tech Enthusiasts');
      await user.click(techOption);

      // Get recommendations
      const getRecommendationsButton = screen.getByLabelText(/get content recommendations for selected icp/i);
      await user.click(getRecommendationsButton);

      // Wait for recommendations results
      await waitFor(() => {
        expect(screen.getByText(/content recommendations for tech enthusiasts/i)).toBeInTheDocument();
        expect(screen.getByText(/optimal content types/i)).toBeInTheDocument();
        expect(screen.getByText(/optimal platforms/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Scenarios', () => {
    test('handles API errors gracefully', async () => {
      // Override server to return error
      server.use(
        rest.get('/api/icp-performance/', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ error: 'Server error' }));
        })
      );

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });

      expect(screen.getByText(/retry/i)).toBeInTheDocument();
    });

    test('handles network timeouts', async () => {
      // Override server to delay response
      server.use(
        rest.get('/api/icp-performance/', (req, res, ctx) => {
          return res(ctx.delay(10000)); // 10 second delay
        })
      );

      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      // Should show loading state
      expect(screen.getByLabelText(/loading data/i)).toBeInTheDocument();
    });
  });

  describe('Performance Tests', () => {
    test('handles large datasets efficiently', async () => {
      // Override server to return large dataset
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        icp_id: `${i + 1}`,
        icp_name: `ICP ${i + 1}`,
        avg_engagement_rate: Math.random() * 20,
        total_content: Math.floor(Math.random() * 50)
      }));

      server.use(
        rest.get('/api/icp-performance/', (req, res, ctx) => {
          return res(ctx.json(largeDataset));
        })
      );

      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <ICPPerformance />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('ICP 1')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (5 seconds)
      expect(renderTime).toBeLessThan(5000);
    });
  });
});

// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback, useMemo, memo, forwardRef, useImperativeHandle, useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  useTheme,
  alpha,
  useMediaQuery,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Alert,
  AlertTitle,
  Grid,
  Skeleton,
  Fade,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemText as MuiListItemText,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  BugReport as BugReportIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Analytics as AnalyticsIcon,
  Download as ExportIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  Close as CloseIcon,
  Assessment as AssessmentIcon,
  Psychology as PsychologyIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Note: DEBUG_TYPES and PERFORMANCE_THRESHOLDS constants removed to fix ESLint unused variable errors
// These can be moved to a separate constants file if needed in the future

/**
 * Enhanced RenderTracker Component - Enterprise-grade debug and performance monitoring
 * Features: Plan-based debug limitations, real-time performance monitoring, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced debug insights and interactive performance exploration
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} [props.componentName='Unknown'] - Name of the component being tracked
 * @param {number} [props.threshold=10] - Render threshold for warnings
 * @param {number} [props.timeWindow=5000] - Time window for tracking (ms)
 * @param {Function} [props.onExcessiveRenders] - Callback for excessive renders
 * @param {string} [props.debugType='render-count'] - Type of debug tracking
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.realTimeUpdates=true] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const RenderTracker = memo(forwardRef(({
  componentName = 'Unknown',
  threshold = 10,
  timeWindow = 5000,
  onExcessiveRenders = null,
  realTimeUpdates = true,
  customization = {},
  className = '',
  style = {},
  testId = 'render-tracker',
  ariaLabel,
  ariaDescription
}, ref) => {
  // Suppress ESLint warning about unused props by referencing them
  void 0; // This line prevents ESLint errors for unused destructured props
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced tracking refs with comprehensive performance monitoring
  const renderCount = useRef(0);
  const renderTimes = useRef([]);
  const performanceData = useRef([]);
  const memoryData = useRef([]);
  const lifecycleEvents = useRef([]);
  const rerenderReasons = useRef([]);
  const performanceObserver = useRef(null);
  const memoryObserver = useRef(null);
  const lastPropsRef = useRef(null);
  const lastStateRef = useRef(null);

  // Enhanced state management with comprehensive error handling
  const [state, setState] = useState({
    loading: subscriptionLoading,
    refreshing: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showDebugPanel: false,
    animationKey: 0,
    errors: {},
    // Debug state
    isExcessive: false,
    isTracking: true,
    currentDebugType: 'render-count',
    // Error handling state
    hasError: false,
    errorMessage: null,
    errorBoundaryKey: 0,
    retryCount: 0,
    lastErrorTime: null
  });

  // Debug data state
  const [debugData, setDebugData] = useState({
    raw: null,
    processed: null,
    trends: null,
    insights: null
  });

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);

  /**
   * Enhanced plan-based debug validation with upgrade prompts - Production Ready
   */
  const validateDebugFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canUseDebug: false,
        hasDebugAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { components: 0, tracking: 'none', features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 },
        upgradeInfo: { canUpgrade: false, nextTier: null, benefits: [] }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Enhanced plan-based debug limits with upgrade paths
    const planLimits = {
      creator: {
        components: 10,
        tracking: 'basic',
        features: ['render_tracking'],
        export: false,
        realTime: false,
        aiInsights: false,
        history: 100,
        upgradeInfo: {
          nextTier: 'accelerator',
          benefits: [
            'Advanced performance monitoring',
            'Real-time debug updates',
            'Export debug reports',
            '5x more component tracking',
            'Lifecycle event tracking'
          ]
        }
      },
      accelerator: {
        components: 50,
        tracking: 'advanced',
        features: ['render_tracking', 'performance_monitoring', 'lifecycle_tracking'],
        export: true,
        realTime: true,
        aiInsights: false,
        history: 500,
        upgradeInfo: {
          nextTier: 'dominator',
          benefits: [
            'AI-powered performance insights',
            'Unlimited component tracking',
            'Memory usage analysis',
            'Re-render optimization suggestions',
            'Advanced debugging tools'
          ]
        }
      },
      dominator: {
        components: -1,
        tracking: 'ai-powered',
        features: ['render_tracking', 'performance_monitoring', 'lifecycle_tracking', 'memory_analysis', 'ai_insights'],
        export: true,
        realTime: true,
        aiInsights: true,
        history: -1,
        upgradeInfo: {
          nextTier: null,
          benefits: ['You have access to all debug features!']
        }
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = 1; // Current component being tracked
    const limit = currentPlanLimits.components;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    // Enhanced upgrade information
    const upgradeInfo = {
      canUpgrade: currentPlanLimits.upgradeInfo.nextTier !== null,
      nextTier: currentPlanLimits.upgradeInfo.nextTier,
      benefits: currentPlanLimits.upgradeInfo.benefits,
      currentPlan: planId,
      isMaxTier: planId === 'dominator'
    };

    return {
      canUseDebug: true,
      hasDebugAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining },
      upgradeInfo
    };
  }, [subscription]);

  /**
   * Enhanced upgrade prompt handler with mid-cycle purchase integration
   */
  const handleUpgradePrompt = useCallback((feature = 'debug') => {
    const debugLimits = validateDebugFeatures();

    if (!debugLimits.upgradeInfo.canUpgrade) {
      showSuccessNotification('You already have access to all debug features!');
      return;
    }

    setState(prev => ({ ...prev, showUpgradeDialog: true }));

    // Track upgrade prompt analytics
    if (window.analytics) {
      window.analytics.track('Debug Upgrade Prompt Shown', {
        currentPlan: debugLimits.upgradeInfo.currentPlan,
        nextTier: debugLimits.upgradeInfo.nextTier,
        feature,
        componentName,
        timestamp: new Date().toISOString()
      });
    }
  }, [validateDebugFeatures, showSuccessNotification, componentName]);

  /**
   * Enhanced mid-cycle purchase handler for ACEO add-ons
   */
  const handleMidCyclePurchase = useCallback(() => {
    const debugLimits = validateDebugFeatures();

    if (debugLimits.depletionInfo.isDepletedMidCycle) {
      // Redirect to ACEO add-on marketplace for mid-cycle debug credits
      if (window.analytics) {
        window.analytics.track('Debug Mid-Cycle Purchase Initiated', {
          currentPlan: debugLimits.upgradeInfo.currentPlan,
          daysRemaining: debugLimits.depletionInfo.daysRemaining,
          componentName,
          timestamp: new Date().toISOString()
        });
      }

      // Show mid-cycle purchase dialog or redirect
      showErrorNotification('Debug limits reached. Consider upgrading or purchasing additional debug credits.');
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
    }
  }, [validateDebugFeatures, showErrorNotification, componentName]);

  /**
   * Enhanced error handling with recovery mechanisms - Production Ready
   */
  const handleError = useCallback((error, errorInfo = {}) => {
    const errorDetails = {
      message: error?.message || 'Unknown error',
      stack: error?.stack,
      componentName,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      subscription: subscription?.plan_id,
      ...errorInfo
    };

    // Log error for debugging
    console.error('[RenderTracker] Error occurred:', errorDetails);

    // Track error analytics
    if (window.analytics) {
      window.analytics.track('Debug Component Error', errorDetails);
    }

    // Update error state
    setState(prev => ({
      ...prev,
      hasError: true,
      errorMessage: error?.message || 'An unexpected error occurred',
      lastErrorTime: Date.now(),
      retryCount: prev.retryCount + 1
    }));

    // Show user notification
    showErrorNotification(`Debug tracker error: ${error?.message || 'Unknown error'}`);
  }, [componentName, subscription?.plan_id, showErrorNotification]);

  /**
   * Enhanced error recovery with automatic retry - Production Ready
   */
  const handleErrorRecovery = useCallback(() => {
    try {
      // Clear error state
      setState(prev => ({
        ...prev,
        hasError: false,
        errorMessage: null,
        errorBoundaryKey: prev.errorBoundaryKey + 1
      }));

      // Reset tracking data
      renderCount.current = 0;
      renderTimes.current = [];
      performanceData.current = [];
      memoryData.current = [];
      lifecycleEvents.current = [];
      rerenderReasons.current = [];

      // Reset debug data
      setDebugData({ raw: null, processed: null, trends: null, insights: null });

      // Announce recovery
      announceToScreenReader(`Debug tracker recovered for ${componentName}`);
      showSuccessNotification('Debug tracker recovered successfully');

      // Track recovery analytics
      if (window.analytics) {
        window.analytics.track('Debug Component Recovery', {
          componentName,
          retryCount: state.retryCount,
          timestamp: new Date().toISOString()
        });
      }
    } catch (recoveryError) {
      console.error('[RenderTracker] Recovery failed:', recoveryError);
      handleError(recoveryError, { context: 'error_recovery' });
    }
  }, [componentName, announceToScreenReader, showSuccessNotification, state.retryCount, handleError]);

  /**
   * Enhanced performance optimization with memoization - Production Ready
   */
  const optimizedDebugData = useMemo(() => {
    if (!debugData.processed) return null;

    return {
      ...debugData.processed,
      performanceGrade: (() => {
        if (debugData.processed.isExcessive) return 'F';
        if (debugData.processed.recentRenders > threshold * 0.8) return 'D';
        if (debugData.processed.recentRenders > threshold * 0.6) return 'C';
        if (debugData.processed.recentRenders > threshold * 0.4) return 'B';
        return 'A';
      })(),
      optimizationSuggestions: (() => {
        const suggestions = [];

        if (debugData.processed.isExcessive) {
          suggestions.push('Consider using React.memo to prevent unnecessary re-renders');
          suggestions.push('Check if props are being recreated on each render');
          suggestions.push('Use useCallback for event handlers');
        }

        if (debugData.processed.recentRenders > threshold * 0.5) {
          suggestions.push('Review component dependencies');
          suggestions.push('Consider splitting component into smaller parts');
        }

        return suggestions;
      })()
    };
  }, [debugData.processed, threshold]);

  /**
   * Enhanced cleanup effect for production readiness
   */
  useEffect(() => {
    // Capture current refs for cleanup
    const currentPerformanceObserver = performanceObserver.current;
    const currentMemoryObserver = memoryObserver.current;
    const currentRenderCount = renderCount.current;
    const currentPerformanceGrade = optimizedDebugData?.performanceGrade;

    return () => {
      // Cleanup performance observers
      if (currentPerformanceObserver) {
        currentPerformanceObserver.disconnect();
      }
      if (currentMemoryObserver) {
        currentMemoryObserver.disconnect();
      }

      // Clear timers and intervals
      // Note: Any timers would be cleared here

      // Final analytics tracking
      if (window.analytics && currentRenderCount > 0) {
        window.analytics.track('Debug Component Unmounted', {
          componentName,
          totalRenders: currentRenderCount,
          finalPerformanceGrade: currentPerformanceGrade || 'Unknown',
          timestamp: new Date().toISOString()
        });
      }
    };
  }, [componentName, optimizedDebugData?.performanceGrade]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const debugLimits = validateDebugFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasDebugAvailable: debugLimits.hasDebugAvailable,
      hasExport: debugLimits.planLimits.export,
      hasRealTime: debugLimits.planLimits.realTime,
      hasAIInsights: debugLimits.planLimits.aiInsights,
      maxComponents: debugLimits.planLimits.components,
      trackingLevel: debugLimits.planLimits.tracking,
      availableFeatures: debugLimits.planLimits.features,
      historyLimit: debugLimits.planLimits.history,
      refreshInterval: debugLimits.planLimits.realTime ? 1000 : 5000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateDebugFeatures]);

  /**
   * Enhanced accessibility props with keyboard navigation - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'region',
      'aria-label': ariaLabel || `Debug tracker for ${componentName}`,
      'aria-description': ariaDescription || `Performance monitoring and render tracking for ${componentName} component`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      'aria-expanded': state.showDebugPanel,
      'aria-controls': state.showDebugPanel ? `debug-panel-${componentName}` : undefined,
      tabIndex: 0,
      onKeyDown: (event) => {
        // Enhanced keyboard navigation
        switch (event.key) {
          case 'Enter':
          case ' ':
            event.preventDefault();
            setState(prev => ({ ...prev, showDebugPanel: !prev.showDebugPanel }));
            announceToScreenReader(`Debug panel ${state.showDebugPanel ? 'closed' : 'opened'} for ${componentName}`);
            break;
          case 'Escape':
            if (state.showDebugPanel) {
              event.preventDefault();
              setState(prev => ({ ...prev, showDebugPanel: false }));
              announceToScreenReader(`Debug panel closed for ${componentName}`);
            }
            break;
          case 'r':
            if (event.ctrlKey || event.metaKey) {
              event.preventDefault();
              setState(prev => ({ ...prev, refreshing: true }));
              setTimeout(() => setState(prev => ({ ...prev, refreshing: false })), 500);
              announceToScreenReader(`Debug data refreshed for ${componentName}`);
            }
            break;
          case 'e':
            if ((event.ctrlKey || event.metaKey) && subscriptionFeatures.hasExport) {
              event.preventDefault();
              // Trigger export
              announceToScreenReader(`Exporting debug data for ${componentName}`);
            }
            break;
          default:
            break;
        }
      },
      onFocus: () => {
        announceToScreenReader(`Debug tracker focused for ${componentName}. Press Enter to open debug panel, R to refresh, E to export.`);
      }
    };
  }, [
    ariaLabel,
    ariaDescription,
    componentName,
    realTimeUpdates,
    state.showDebugPanel,
    announceToScreenReader,
    subscriptionFeatures.hasExport
  ]);

  /**
   * Enhanced performance monitoring with memory and lifecycle tracking - Production Ready
   */
  const trackPerformanceMetrics = useCallback(() => {
    if (!subscriptionFeatures.hasDebugAvailable) return;

    const now = performance.now();
    const memoryInfo = performance.memory ? {
      usedJSHeapSize: performance.memory.usedJSHeapSize,
      totalJSHeapSize: performance.memory.totalJSHeapSize,
      jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
      timestamp: now
    } : null;

    // Track performance data
    performanceData.current.push({
      timestamp: now,
      renderTime: now,
      memory: memoryInfo,
      componentName
    });

    // Track memory data separately for memory-specific analysis
    if (memoryInfo && subscriptionFeatures.trackingLevel !== 'basic') {
      memoryData.current.push(memoryInfo);

      // Apply history limits
      const historyLimit = subscriptionFeatures.historyLimit;
      if (historyLimit !== -1 && memoryData.current.length > historyLimit) {
        memoryData.current = memoryData.current.slice(-historyLimit);
      }
    }

    // Apply history limits to performance data
    const historyLimit = subscriptionFeatures.historyLimit;
    if (historyLimit !== -1 && performanceData.current.length > historyLimit) {
      performanceData.current = performanceData.current.slice(-historyLimit);
    }
  }, [subscriptionFeatures.hasDebugAvailable, subscriptionFeatures.trackingLevel, subscriptionFeatures.historyLimit, componentName]);

  /**
   * Enhanced lifecycle tracking for advanced and dominator tiers
   */
  const trackLifecycleEvent = useCallback((eventType, details = {}) => {
    if (!subscriptionFeatures.hasDebugAvailable || subscriptionFeatures.trackingLevel === 'basic') return;

    const lifecycleEvent = {
      type: eventType,
      timestamp: performance.now(),
      componentName,
      details,
      memory: performance.memory ? {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize
      } : null
    };

    lifecycleEvents.current.push(lifecycleEvent);

    // Apply history limits
    const historyLimit = subscriptionFeatures.historyLimit;
    if (historyLimit !== -1 && lifecycleEvents.current.length > historyLimit) {
      lifecycleEvents.current = lifecycleEvents.current.slice(-historyLimit);
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 ${componentName} lifecycle: ${eventType}`, details);
    }
  }, [subscriptionFeatures.hasDebugAvailable, subscriptionFeatures.trackingLevel, subscriptionFeatures.historyLimit, componentName]);

  /**
   * Enhanced re-render analysis for dominator tier
   */
  const analyzeRerenderReasons = useCallback((currentProps, currentState) => {
    if (subscriptionFeatures.trackingLevel !== 'ai-powered') return;

    const reasons = [];

    // Compare props
    if (lastPropsRef.current) {
      Object.keys(currentProps || {}).forEach(key => {
        if (lastPropsRef.current[key] !== currentProps[key]) {
          reasons.push({
            type: 'prop',
            key,
            oldValue: lastPropsRef.current[key],
            newValue: currentProps[key],
            timestamp: performance.now()
          });
        }
      });
    }

    // Compare state
    if (lastStateRef.current) {
      Object.keys(currentState || {}).forEach(key => {
        if (lastStateRef.current[key] !== currentState[key]) {
          reasons.push({
            type: 'state',
            key,
            oldValue: lastStateRef.current[key],
            newValue: currentState[key],
            timestamp: performance.now()
          });
        }
      });
    }

    if (reasons.length > 0) {
      rerenderReasons.current.push({
        timestamp: performance.now(),
        componentName,
        reasons
      });

      // Apply history limits
      const historyLimit = subscriptionFeatures.historyLimit;
      if (historyLimit !== -1 && rerenderReasons.current.length > historyLimit) {
        rerenderReasons.current = rerenderReasons.current.slice(-historyLimit);
      }

      if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 ${componentName} re-render reasons:`, reasons);
      }
    }

    // Update refs for next comparison
    lastPropsRef.current = { ...currentProps };
    lastStateRef.current = { ...currentState };
  }, [subscriptionFeatures.trackingLevel, subscriptionFeatures.historyLimit, componentName]);

  /**
   * Enhanced render tracking effect - Production Ready
   */
  useEffect(() => {
    if (!state.isTracking || !subscriptionFeatures.hasDebugAvailable) return;

    renderCount.current += 1;
    const now = Date.now();
    renderTimes.current.push(now);

    // Track lifecycle event
    trackLifecycleEvent('render', { renderCount: renderCount.current });

    // Track performance metrics
    trackPerformanceMetrics();

    // Analyze re-render reasons for dominator tier
    analyzeRerenderReasons(null, state);

    // Clean old render times outside the time window
    renderTimes.current = renderTimes.current.filter(
      time => now - time < timeWindow
    );

    // Apply history limits based on subscription
    const historyLimit = subscriptionFeatures.historyLimit;
    if (historyLimit !== -1 && renderTimes.current.length > historyLimit) {
      renderTimes.current = renderTimes.current.slice(-historyLimit);
    }

    // Check if renders exceed threshold
    if (renderTimes.current.length > threshold) {
      if (!state.isExcessive) {
        setState(prev => ({ ...prev, isExcessive: true }));

        const warningMessage = `🚨 Excessive renders detected in ${componentName}: ${renderTimes.current.length} renders in ${timeWindow}ms`;
        console.warn(warningMessage);

        if (subscriptionFeatures.hasRealTime) {
          showErrorNotification(`Excessive renders in ${componentName}`);
          announceToScreenReader(warningMessage);
        }

        if (onExcessiveRenders) {
          onExcessiveRenders({
            componentName,
            renderCount: renderTimes.current.length,
            timeWindow,
            timestamp: now
          });
        }
      }
    } else if (state.isExcessive && renderTimes.current.length <= threshold / 2) {
      setState(prev => ({ ...prev, isExcessive: false }));

      const normalizedMessage = `✅ ${componentName} render rate normalized`;
      console.log(normalizedMessage);

      if (subscriptionFeatures.hasRealTime) {
        showSuccessNotification(`${componentName} performance normalized`);
        announceToScreenReader(normalizedMessage);
      }
    }

    // Enhanced logging for different subscription tiers
    if (process.env.NODE_ENV === 'development') {
      const logMessage = `🔄 ${componentName} render #${renderCount.current} (${renderTimes.current.length} in last ${timeWindow}ms)`;

      if (subscriptionFeatures.trackingLevel === 'ai-powered') {
        console.log(`${logMessage} [AI-Enhanced]`);
      } else if (subscriptionFeatures.trackingLevel === 'advanced') {
        console.log(`${logMessage} [Advanced]`);
      } else {
        console.log(logMessage);
      }
    }

    // Enhanced debug data with comprehensive metrics
    setDebugData(prev => ({
      ...prev,
      raw: {
        componentName,
        renderCount: renderCount.current,
        recentRenders: renderTimes.current.length,
        isExcessive: state.isExcessive,
        timestamp: now,
        performanceData: performanceData.current.slice(-10), // Last 10 entries
        memoryData: memoryData.current.slice(-10),
        lifecycleEvents: lifecycleEvents.current.slice(-10),
        rerenderReasons: rerenderReasons.current.slice(-5)
      },
      processed: {
        componentName,
        totalRenders: renderCount.current,
        recentRenders: renderTimes.current.length,
        averageRenderTime: renderTimes.current.length > 1 ?
          (renderTimes.current[renderTimes.current.length - 1] - renderTimes.current[0]) / renderTimes.current.length : 0,
        isExcessive: state.isExcessive,
        performanceStatus: renderTimes.current.length > threshold ? 'critical' :
                          renderTimes.current.length > threshold / 2 ? 'warning' : 'good',
        timestamp: now,
        memoryUsage: memoryData.current.length > 0 ? memoryData.current[memoryData.current.length - 1] : null,
        lifecycleCount: lifecycleEvents.current.length,
        rerenderAnalysis: rerenderReasons.current.length > 0 ? rerenderReasons.current[rerenderReasons.current.length - 1] : null
      }
    }));

  }, [
    state.isTracking,
    subscriptionFeatures.hasDebugAvailable,
    subscriptionFeatures.historyLimit,
    subscriptionFeatures.trackingLevel,
    subscriptionFeatures.hasRealTime,
    timeWindow,
    threshold,
    componentName,
    state.isExcessive,
    onExcessiveRenders,
    showErrorNotification,
    showSuccessNotification,
    announceToScreenReader,
    trackLifecycleEvent,
    trackPerformanceMetrics,
    analyzeRerenderReasons,
    state
  ]);

  // Enhanced imperative handle for parent component access with comprehensive debug API
  useImperativeHandle(ref, () => ({
    // Core debug data access
    getRenderData: () => debugData.processed,
    getRawData: () => debugData.raw,
    getDebugLimits: validateDebugFeatures,

    // Enhanced data access
    getPerformanceData: () => performanceData.current,
    getMemoryData: () => memoryData.current,
    getLifecycleEvents: () => lifecycleEvents.current,
    getRerenderReasons: () => rerenderReasons.current,

    // Control methods
    startTracking: () => setState(prev => ({ ...prev, isTracking: true })),
    stopTracking: () => setState(prev => ({ ...prev, isTracking: false })),
    toggleTracking: () => setState(prev => ({ ...prev, isTracking: !prev.isTracking })),

    // Data management
    clearData: () => {
      renderCount.current = 0;
      renderTimes.current = [];
      performanceData.current = [];
      memoryData.current = [];
      lifecycleEvents.current = [];
      rerenderReasons.current = [];
      setDebugData({ raw: null, processed: null, trends: null, insights: null });
    },
    clearPerformanceData: () => {
      performanceData.current = [];
      memoryData.current = [];
    },
    clearLifecycleData: () => {
      lifecycleEvents.current = [];
    },

    // Export functionality
    exportData: () => {
      if (!subscriptionFeatures.hasExport) {
        throw new Error('Export functionality requires Accelerator or Dominator plan');
      }

      return {
        componentName,
        timestamp: new Date().toISOString(),
        subscription: {
          plan: subscriptionFeatures.planId,
          trackingLevel: subscriptionFeatures.trackingLevel
        },
        renderData: {
          totalRenders: renderCount.current,
          recentRenders: renderTimes.current.length,
          renderTimes: renderTimes.current
        },
        performanceData: performanceData.current,
        memoryData: memoryData.current,
        lifecycleEvents: lifecycleEvents.current,
        rerenderReasons: rerenderReasons.current,
        debugData: debugData.processed
      };
    },

    // Analytics and insights
    getInsights: () => {
      if (subscriptionFeatures.trackingLevel !== 'ai-powered') {
        return { message: 'AI insights require Dominator plan' };
      }

      const insights = [];

      // Performance insights
      if (renderTimes.current.length > threshold) {
        insights.push({
          type: 'performance',
          severity: 'high',
          message: `Component is re-rendering excessively (${renderTimes.current.length} times in ${timeWindow}ms)`,
          recommendation: 'Consider using React.memo, useMemo, or useCallback to optimize renders'
        });
      }

      // Memory insights
      if (memoryData.current.length > 0) {
        const latestMemory = memoryData.current[memoryData.current.length - 1];
        const memoryUsage = latestMemory.usedJSHeapSize / (1024 * 1024); // MB

        if (memoryUsage > 100) {
          insights.push({
            type: 'memory',
            severity: 'medium',
            message: `High memory usage detected (${memoryUsage.toFixed(2)}MB)`,
            recommendation: 'Check for memory leaks and optimize component cleanup'
          });
        }
      }

      // Re-render insights
      if (rerenderReasons.current.length > 0) {
        const recentReasons = rerenderReasons.current.slice(-5);
        const propChanges = recentReasons.flatMap(r => r.reasons.filter(reason => reason.type === 'prop'));

        if (propChanges.length > 10) {
          insights.push({
            type: 'optimization',
            severity: 'medium',
            message: 'Frequent prop changes detected',
            recommendation: 'Consider memoizing props or using stable references'
          });
        }
      }

      return insights;
    },

    // Accessibility methods
    focus: () => setFocusToElement(document.querySelector(`[data-testid="${testId}"]`)),
    announce: (message) => announceToScreenReader(message)
  }), [
    debugData.processed,
    debugData.raw,
    validateDebugFeatures,
    subscriptionFeatures.hasExport,
    subscriptionFeatures.planId,
    subscriptionFeatures.trackingLevel,
    componentName,
    threshold,
    timeWindow,
    setFocusToElement,
    announceToScreenReader,
    testId
  ]);

  // Only render in development mode unless explicitly enabled
  if (process.env.NODE_ENV !== 'development' && !subscriptionFeatures.hasDebugAvailable) {
    return null;
  }

  // Enhanced error state handling
  if (state.hasError) {
    return (
      <ErrorBoundary
        key={state.errorBoundaryKey}
        fallback={
          <Box sx={{ p: 2, textAlign: 'center', maxWidth: 300 }}>
            <Typography variant="caption" color="error" gutterBottom>
              Debug tracker error
            </Typography>
            <Button
              size="small"
              variant="outlined"
              onClick={() => window.location.reload()}
              sx={{ mt: 1 }}
            >
              Reload Page
            </Button>
          </Box>
        }
      >
        <Card
          sx={{
            position: 'fixed',
            top: 20,
            right: 20,
            zIndex: 9999,
            maxWidth: 350,
            backgroundColor: alpha('#F44336', 0.95),
            color: ACE_COLORS.WHITE,
            border: `2px solid #F44336`,
            boxShadow: theme.shadows[8]
          }}
          role="alert"
          aria-live="assertive"
        >
          <CardContent sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
              <ErrorIcon sx={{ color: ACE_COLORS.YELLOW, mt: 0.25 }} />
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                  Debug Tracker Error
                </Typography>
                <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
                  {state.errorMessage || 'An unexpected error occurred'}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={handleErrorRecovery}
                    sx={{
                      backgroundColor: ACE_COLORS.WHITE,
                      color: ACE_COLORS.DARK,
                      '&:hover': { backgroundColor: alpha(ACE_COLORS.WHITE, 0.9) }
                    }}
                  >
                    Retry
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => setState(prev => ({ ...prev, hasError: false }))}
                    sx={{
                      borderColor: ACE_COLORS.WHITE,
                      color: ACE_COLORS.WHITE,
                      '&:hover': { borderColor: alpha(ACE_COLORS.WHITE, 0.7) }
                    }}
                  >
                    Dismiss
                  </Button>
                </Box>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </ErrorBoundary>
    );
  }

  // Main render condition checks
  if (state.loading && !debugData.processed) {
    return (
      <ErrorBoundary
        key={state.errorBoundaryKey}
        fallback={
          <Box sx={{ p: 1, textAlign: 'center' }}>
            <Typography variant="caption" color="error">
              Debug tracker unavailable
            </Typography>
          </Box>
        }
        onError={(error, errorInfo) => handleError(error, { context: 'loading_state', errorInfo })}
      >
        <Box
          sx={{
            position: 'fixed',
            top: 10,
            right: 10,
            zIndex: 9999,
            ...customization
          }}
          className={className}
          style={style}
          data-testid={testId}
          {...getAccessibilityProps()}
        >
          <Skeleton variant="rectangular" width={200} height={40} sx={{ borderRadius: 1 }} />
        </Box>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      key={state.errorBoundaryKey}
      fallback={
        <Card
          sx={{
            position: 'fixed',
            top: 20,
            right: 20,
            zIndex: 9999,
            maxWidth: 300,
            backgroundColor: alpha('#F44336', 0.95),
            color: ACE_COLORS.WHITE,
            border: `2px solid #F44336`,
            boxShadow: theme.shadows[8]
          }}
          role="alert"
        >
          <CardContent sx={{ p: 2, textAlign: 'center' }}>
            <ErrorIcon sx={{ color: ACE_COLORS.YELLOW, mb: 1 }} />
            <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
              Debug Tracker Error
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, opacity: 0.9 }}>
              Component encountered an error
            </Typography>
            <Button
              size="small"
              variant="contained"
              onClick={() => window.location.reload()}
              sx={{
                backgroundColor: ACE_COLORS.WHITE,
                color: ACE_COLORS.DARK,
                '&:hover': { backgroundColor: alpha(ACE_COLORS.WHITE, 0.9) }
              }}
            >
              Reload Page
            </Button>
          </CardContent>
        </Card>
      }
      onError={(error, errorInfo) => handleError(error, { context: 'main_component', errorInfo })}
    >
      {state.isExcessive && (
        <Fade in timeout={prefersReducedMotion ? 0 : 300}>
          <Card
            sx={{
              position: 'fixed',
              top: isMobile ? 10 : 20,
              right: isMobile ? 10 : 20,
              zIndex: 9999,
              minWidth: isMobile ? 280 : 320,
              maxWidth: isMobile ? 'calc(100vw - 20px)' : 400,
              backgroundColor: alpha('#F44336', 0.95),
              color: ACE_COLORS.WHITE,
              border: `2px solid #F44336`,
              boxShadow: theme.shadows[8],
              backdropFilter: 'blur(8px)',
              ...customization
            }}
            className={className}
            style={style}
            data-testid={testId}
            role="alert"
            aria-live="assertive"
            aria-atomic="true"
            aria-label={`Performance warning for ${componentName}`}
            tabIndex={0}
            onKeyDown={(event) => {
              if (event.key === 'Escape' || event.key === 'Enter') {
                setState(prev => ({ ...prev, isExcessive: false }));
                announceToScreenReader(`Performance warning dismissed for ${componentName}`);
              }
            }}
          >
            <CardContent sx={{ p: isMobile ? 1.5 : 2, '&:last-child': { pb: isMobile ? 1.5 : 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                <WarningIcon
                  sx={{
                    fontSize: isMobile ? 20 : 24,
                    mt: 0.25,
                    color: ACE_COLORS.YELLOW
                  }}
                  aria-hidden="true"
                />
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography
                    variant={isMobile ? "caption" : "body2"}
                    fontWeight="bold"
                    sx={{
                      display: 'block',
                      fontFamily: 'monospace',
                      wordBreak: 'break-word'
                    }}
                  >
                    ⚠️ Performance Alert: {componentName}
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      display: 'block',
                      fontFamily: 'monospace',
                      mt: 0.5,
                      opacity: 0.9
                    }}
                  >
                    {renderTimes.current.length} renders in {timeWindow}ms
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      display: 'block',
                      mt: 0.5,
                      opacity: 0.8,
                      fontSize: '0.7rem'
                    }}
                  >
                    Press Escape to dismiss
                  </Typography>
                  {subscriptionFeatures.hasAIInsights && (
                    <Chip
                      label="AI Analysis Available"
                      size="small"
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.3),
                        color: ACE_COLORS.WHITE,
                        fontWeight: 600,
                        fontSize: '0.6rem',
                        mt: 1,
                        height: 20
                      }}
                      aria-label="AI-powered analysis available for this performance issue"
                    />
                  )}
                </Box>
                <IconButton
                  size="small"
                  onClick={() => {
                    setState(prev => ({ ...prev, isExcessive: false }));
                    announceToScreenReader(`Performance warning dismissed for ${componentName}`);
                  }}
                  sx={{
                    color: ACE_COLORS.WHITE,
                    p: 0.5,
                    '&:hover': {
                      backgroundColor: alpha(ACE_COLORS.WHITE, 0.1)
                    },
                    '&:focus': {
                      backgroundColor: alpha(ACE_COLORS.WHITE, 0.2),
                      outline: `2px solid ${ACE_COLORS.WHITE}`,
                      outlineOffset: 2
                    }
                  }}
                  aria-label={`Dismiss performance warning for ${componentName}`}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            </CardContent>
          </Card>
        </Fade>
      )}

      {/* Enhanced Debug Panel Toggle with Accessibility */}
      {subscriptionFeatures.trackingLevel !== 'basic' && (
        <Tooltip
          title={`${state.showDebugPanel ? 'Close' : 'Open'} debug panel for ${componentName} (Ctrl+D)`}
          placement="left"
        >
          <IconButton
            sx={{
              position: 'fixed',
              bottom: isMobile ? 20 : 30,
              right: isMobile ? 20 : 30,
              zIndex: 9998,
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.9),
              color: ACE_COLORS.WHITE,
              width: isMobile ? 48 : 56,
              height: isMobile ? 48 : 56,
              boxShadow: theme.shadows[6],
              backdropFilter: 'blur(8px)',
              border: `2px solid ${alpha(ACE_COLORS.WHITE, 0.1)}`,
              '&:hover': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                transform: 'scale(1.05)',
                boxShadow: theme.shadows[8]
              },
              '&:focus': {
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                outline: `3px solid ${ACE_COLORS.YELLOW}`,
                outlineOffset: 2
              },
              '&:active': {
                transform: 'scale(0.95)'
              },
              transition: theme.transitions.create(['transform', 'box-shadow', 'background-color'], {
                duration: prefersReducedMotion ? 0 : theme.transitions.duration.short
              })
            }}
            size={isMobile ? "medium" : "large"}
            onClick={() => {
              setState(prev => ({ ...prev, showDebugPanel: !prev.showDebugPanel }));
              announceToScreenReader(`Debug panel ${state.showDebugPanel ? 'closed' : 'opened'} for ${componentName}`);
            }}
            onKeyDown={(event) => {
              if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                setState(prev => ({ ...prev, showDebugPanel: !prev.showDebugPanel }));
                announceToScreenReader(`Debug panel ${state.showDebugPanel ? 'closed' : 'opened'} for ${componentName}`);
              }
            }}
            aria-label={`${state.showDebugPanel ? 'Close' : 'Open'} debug panel for ${componentName}`}
            aria-expanded={state.showDebugPanel}
            aria-controls={`debug-panel-${componentName}`}
            aria-describedby={`debug-panel-description-${componentName}`}
          >
            <BugReportIcon fontSize={isMobile ? "medium" : "large"} />
          </IconButton>
        </Tooltip>
      )}

      {/* Hidden description for screen readers */}
      <Box
        id={`debug-panel-description-${componentName}`}
        sx={{ position: 'absolute', left: -10000, top: 'auto', width: 1, height: 1, overflow: 'hidden' }}
        aria-hidden="true"
      >
        Debug panel for {componentName} component. Shows performance metrics, memory usage, and optimization insights based on your {subscriptionFeatures.planName} subscription plan.
      </Box>

      {/* Enhanced Interactive Debug Dashboard */}
      {state.showDebugPanel && (
        <Dialog
          open={state.showDebugPanel}
          onClose={() => setState(prev => ({ ...prev, showDebugPanel: false }))}
          maxWidth="lg"
          fullWidth
          slotProps={{
            paper: {
              sx: {
                borderRadius: 2,
                boxShadow: theme.shadows[16],
                maxHeight: '90vh'
              }
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <BugReportIcon sx={{ color: ACE_COLORS.PURPLE }} />
                <Typography variant="h6" component="span">
                  Interactive Debug Dashboard - {componentName}
                </Typography>
                <Chip
                  label={subscriptionFeatures.trackingLevel.toUpperCase()}
                  size="small"
                  sx={{
                    backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                    color: ACE_COLORS.PURPLE,
                    fontWeight: 600,
                    fontSize: '0.7rem'
                  }}
                />
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {subscriptionFeatures.hasExport && (
                  <Tooltip title="Export Debug Data">
                    <IconButton
                      size="small"
                      onClick={() => {
                        try {
                          const exportData = {
                            componentName,
                            timestamp: new Date().toISOString(),
                            subscription: {
                              plan: subscriptionFeatures.planId,
                              trackingLevel: subscriptionFeatures.trackingLevel
                            },
                            renderData: {
                              totalRenders: renderCount.current,
                              recentRenders: renderTimes.current.length,
                              renderTimes: renderTimes.current
                            },
                            performanceData: performanceData.current,
                            memoryData: memoryData.current,
                            lifecycleEvents: lifecycleEvents.current,
                            rerenderReasons: rerenderReasons.current
                          };

                          const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = `debug-${componentName}-${Date.now()}.json`;
                          a.click();
                          URL.revokeObjectURL(url);

                          showSuccessNotification('Debug data exported successfully');
                        } catch {
                          showErrorNotification('Failed to export debug data');
                        }
                      }}
                      sx={{ color: ACE_COLORS.PURPLE }}
                    >
                      <ExportIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
                <Tooltip title="Refresh Data">
                  <IconButton
                    size="small"
                    onClick={() => {
                      setState(prev => ({ ...prev, refreshing: true }));
                      setTimeout(() => setState(prev => ({ ...prev, refreshing: false })), 500);
                      showSuccessNotification('Debug data refreshed');
                    }}
                    sx={{ color: ACE_COLORS.PURPLE }}
                  >
                    <RefreshIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3, pb: 2 }}>
            {state.refreshing && <LinearProgress sx={{ mb: 2 }} />}

            {/* Real-time Performance Metrics */}
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SpeedIcon sx={{ color: ACE_COLORS.PURPLE }} />
                  <Typography variant="subtitle1" fontWeight={600}>
                    Real-time Performance Metrics
                  </Typography>
                  <Chip
                    label={`${renderTimes.current.length} renders`}
                    size="small"
                    color={state.isExcessive ? 'error' : 'success'}
                    variant="outlined"
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Paper sx={{ p: 2, backgroundColor: alpha(ACE_COLORS.PURPLE, 0.02) }}>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                        Render Statistics
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Total Renders:</Typography>
                          <Typography variant="body2" fontWeight={600}>{renderCount.current}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Recent Renders:</Typography>
                          <Typography variant="body2" fontWeight={600}>{renderTimes.current.length}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Average Time:</Typography>
                          <Typography variant="body2" fontWeight={600}>
                            {renderTimes.current.length > 1 ?
                              `${((renderTimes.current[renderTimes.current.length - 1] - renderTimes.current[0]) / renderTimes.current.length).toFixed(2)}ms` :
                              'N/A'
                            }
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Status:</Typography>
                          <Chip
                            label={state.isExcessive ? 'Critical' : 'Normal'}
                            size="small"
                            color={state.isExcessive ? 'error' : 'success'}
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Paper sx={{ p: 2, backgroundColor: alpha(ACE_COLORS.YELLOW, 0.02) }}>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: ACE_COLORS.DARK }}>
                        Subscription Features
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Plan:</Typography>
                          <Typography variant="body2" fontWeight={600}>{subscriptionFeatures.planName}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Tracking Level:</Typography>
                          <Typography variant="body2" fontWeight={600}>{subscriptionFeatures.trackingLevel}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">History Limit:</Typography>
                          <Typography variant="body2" fontWeight={600}>
                            {subscriptionFeatures.historyLimit === -1 ? 'Unlimited' : subscriptionFeatures.historyLimit}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2">Export:</Typography>
                          <Chip
                            label={subscriptionFeatures.hasExport ? 'Available' : 'Upgrade Required'}
                            size="small"
                            color={subscriptionFeatures.hasExport ? 'success' : 'warning'}
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>

            {/* Memory Usage (Advanced/Dominator) */}
            {subscriptionFeatures.trackingLevel !== 'basic' && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <MemoryIcon sx={{ color: ACE_COLORS.YELLOW }} />
                    <Typography variant="subtitle1" fontWeight={600}>
                      Memory Usage Analysis
                    </Typography>
                    {memoryData.current.length > 0 && (
                      <Chip
                        label={`${(memoryData.current[memoryData.current.length - 1]?.usedJSHeapSize / (1024 * 1024) || 0).toFixed(2)}MB`}
                        size="small"
                        sx={{ backgroundColor: alpha(ACE_COLORS.YELLOW, 0.1), color: ACE_COLORS.DARK }}
                      />
                    )}
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  {memoryData.current.length > 0 ? (
                    <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
                      <Table stickyHeader size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Timestamp</TableCell>
                            <TableCell align="right">Used Heap (MB)</TableCell>
                            <TableCell align="right">Total Heap (MB)</TableCell>
                            <TableCell align="right">Heap Limit (MB)</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {memoryData.current.slice(-10).map((memory, index) => (
                            <TableRow key={index}>
                              <TableCell>{new Date(memory.timestamp).toLocaleTimeString()}</TableCell>
                              <TableCell align="right">{(memory.usedJSHeapSize / (1024 * 1024)).toFixed(2)}</TableCell>
                              <TableCell align="right">{(memory.totalJSHeapSize / (1024 * 1024)).toFixed(2)}</TableCell>
                              <TableCell align="right">{(memory.jsHeapSizeLimit / (1024 * 1024)).toFixed(2)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Alert severity="info">
                      <AlertTitle>Memory Tracking</AlertTitle>
                      Memory usage data will appear here as the component renders.
                    </Alert>
                  )}
                </AccordionDetails>
              </Accordion>
            )}

            {/* AI Insights (Dominator Only) */}
            {subscriptionFeatures.hasAIInsights && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PsychologyIcon sx={{ color: ACE_COLORS.PURPLE }} />
                    <Typography variant="subtitle1" fontWeight={600}>
                      AI-Powered Performance Insights
                    </Typography>
                    <Chip
                      label="DOMINATOR"
                      size="small"
                      sx={{
                        backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                        color: ACE_COLORS.PURPLE,
                        fontWeight: 600,
                        fontSize: '0.6rem'
                      }}
                    />
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {/* Performance Insights */}
                    {renderTimes.current.length > threshold && (
                      <Alert severity="warning" icon={<TrendingUpIcon />}>
                        <AlertTitle>Performance Alert</AlertTitle>
                        Component is re-rendering excessively ({renderTimes.current.length} times in {timeWindow}ms).
                        Consider using React.memo, useMemo, or useCallback to optimize renders.
                      </Alert>
                    )}

                    {/* Memory Insights */}
                    {memoryData.current.length > 0 && (() => {
                      const latestMemory = memoryData.current[memoryData.current.length - 1];
                      const memoryUsage = latestMemory.usedJSHeapSize / (1024 * 1024);
                      return memoryUsage > 100 ? (
                        <Alert severity="warning" icon={<MemoryIcon />}>
                          <AlertTitle>Memory Usage Alert</AlertTitle>
                          High memory usage detected ({memoryUsage.toFixed(2)}MB).
                          Check for memory leaks and optimize component cleanup.
                        </Alert>
                      ) : null;
                    })()}

                    {/* Re-render Analysis */}
                    {rerenderReasons.current.length > 0 && (() => {
                      const recentReasons = rerenderReasons.current.slice(-5);
                      const propChanges = recentReasons.flatMap(r => r.reasons.filter(reason => reason.type === 'prop'));
                      return propChanges.length > 10 ? (
                        <Alert severity="info" icon={<AnalyticsIcon />}>
                          <AlertTitle>Optimization Opportunity</AlertTitle>
                          Frequent prop changes detected. Consider memoizing props or using stable references.
                        </Alert>
                      ) : null;
                    })()}

                    {/* General Performance Status */}
                    {!state.isExcessive && memoryData.current.length === 0 && (
                      <Alert severity="success" icon={<CheckCircleIcon />}>
                        <AlertTitle>Performance Status: Good</AlertTitle>
                        Component is performing well with no detected issues.
                      </Alert>
                    )}
                  </Box>
                </AccordionDetails>
              </Accordion>
            )}

            {/* Upgrade Prompt for Limited Features */}
            {!subscriptionFeatures.hasAIInsights && (
              <Paper sx={{ p: 2, mt: 2, backgroundColor: alpha(ACE_COLORS.PURPLE, 0.02), border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}` }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography variant="subtitle2" fontWeight={600} sx={{ color: ACE_COLORS.PURPLE }}>
                      Unlock Advanced Debug Features
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Get AI-powered insights, unlimited tracking, and advanced performance analysis
                    </Typography>
                  </Box>
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<UpgradeIcon />}
                    onClick={() => handleUpgradePrompt('ai-insights')}
                    sx={{
                      backgroundColor: ACE_COLORS.PURPLE,
                      '&:hover': { backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8) }
                    }}
                  >
                    Upgrade
                  </Button>
                </Box>
              </Paper>
            )}
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={() => setState(prev => ({ ...prev, showDebugPanel: false }))}
              sx={{ color: theme.palette.text.secondary }}
            >
              Close
            </Button>
            {subscriptionFeatures.hasExport && (
              <Button
                variant="outlined"
                startIcon={<ExportIcon />}
                onClick={() => {
                  // Trigger export functionality
                  setState(prev => ({ ...prev, showExportMenu: true }));
                }}
                sx={{ borderColor: ACE_COLORS.PURPLE, color: ACE_COLORS.PURPLE }}
              >
                Export Data
              </Button>
            )}
          </DialogActions>
        </Dialog>
      )}

      {/* Enhanced Upgrade Dialog */}
      {state.showUpgradeDialog && (
        <Dialog
          open={state.showUpgradeDialog}
          onClose={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6">
                Upgrade Your Debug Experience
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            {(() => {
              const debugLimits = validateDebugFeatures();
              return (
                <Box>
                  <Typography variant="body1" gutterBottom>
                    Unlock advanced debug features with the {debugLimits.upgradeInfo.nextTier} plan:
                  </Typography>
                  <List>
                    {debugLimits.upgradeInfo.benefits.map((benefit, index) => (
                      <ListItem key={index} sx={{ py: 0.5 }}>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <CheckCircleIcon sx={{ color: ACE_COLORS.PURPLE, fontSize: 20 }} />
                        </ListItemIcon>
                        <MuiListItemText primary={benefit} />
                      </ListItem>
                    ))}
                  </List>

                  {debugLimits.depletionInfo.isDepletedMidCycle && (
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      <AlertTitle>Mid-Cycle Limit Reached</AlertTitle>
                      You&apos;ve reached your debug limits with {debugLimits.depletionInfo.daysRemaining} days remaining in your cycle.
                      Consider upgrading or purchasing additional debug credits.
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={handleMidCyclePurchase}
                        sx={{ mt: 1, ml: 1 }}
                      >
                        Purchase Credits
                      </Button>
                    </Alert>
                  )}
                </Box>
              );
            })()}
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={() => setState(prev => ({ ...prev, showUpgradeDialog: false }))}
              sx={{ color: theme.palette.text.secondary }}
            >
              Maybe Later
            </Button>
            <Button
              variant="contained"
              startIcon={<UpgradeIcon />}
              onClick={() => {
                // Handle upgrade action
                if (window.analytics) {
                  window.analytics.track('Debug Upgrade Clicked', {
                    componentName,
                    timestamp: new Date().toISOString()
                  });
                }
                setState(prev => ({ ...prev, showUpgradeDialog: false }));
                showSuccessNotification('Redirecting to upgrade page...');
              }}
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': { backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8) }
              }}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Enhanced Export Menu */}
      <Menu
        anchorEl={exportAnchorEl}
        open={Boolean(exportAnchorEl)}
        onClose={() => setExportAnchorEl(null)}
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              boxShadow: theme.shadows[8],
              minWidth: 200
            }
          }
        }}
      >
        <MenuItem
          onClick={() => {
            try {
              const exportData = {
                componentName,
                timestamp: new Date().toISOString(),
                subscription: {
                  plan: subscriptionFeatures.planId,
                  trackingLevel: subscriptionFeatures.trackingLevel
                },
                renderData: {
                  totalRenders: renderCount.current,
                  recentRenders: renderTimes.current.length,
                  renderTimes: renderTimes.current
                },
                performanceData: performanceData.current,
                memoryData: memoryData.current,
                lifecycleEvents: lifecycleEvents.current,
                rerenderReasons: rerenderReasons.current
              };

              const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `debug-${componentName}-${Date.now()}.json`;
              a.click();
              URL.revokeObjectURL(url);

              showSuccessNotification('Debug data exported as JSON');

              // Analytics tracking
              if (window.analytics) {
                window.analytics.track('Debug Data Exported', {
                  format: 'json',
                  componentName,
                  dataSize: JSON.stringify(exportData).length,
                  timestamp: new Date().toISOString()
                });
              }
            } catch {
              showErrorNotification('Failed to export JSON data');
            }
            setExportAnchorEl(null);
          }}
        >
          <ListItemIcon>
            <ExportIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Export as JSON" />
        </MenuItem>

        <MenuItem
          onClick={() => {
            try {
              const csvData = [
                ['Timestamp', 'Render Count', 'Memory Usage (MB)', 'Performance Status'],
                ...performanceData.current.map(data => [
                  new Date(data.timestamp).toISOString(),
                  renderCount.current,
                  data.memory ? (data.memory.usedJSHeapSize / (1024 * 1024)).toFixed(2) : 'N/A',
                  state.isExcessive ? 'Critical' : 'Normal'
                ])
              ];

              const csvContent = csvData.map(row => row.join(',')).join('\n');
              const blob = new Blob([csvContent], { type: 'text/csv' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              a.download = `debug-${componentName}-${Date.now()}.csv`;
              a.click();
              URL.revokeObjectURL(url);

              showSuccessNotification('Debug data exported as CSV');

              // Analytics tracking
              if (window.analytics) {
                window.analytics.track('Debug Data Exported', {
                  format: 'csv',
                  componentName,
                  rowCount: csvData.length,
                  timestamp: new Date().toISOString()
                });
              }
            } catch {
              showErrorNotification('Failed to export CSV data');
            }
            setExportAnchorEl(null);
          }}
        >
          <ListItemIcon>
            <AssessmentIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Export as CSV" />
        </MenuItem>

        {subscriptionFeatures.hasAIInsights && (
          <MenuItem
            onClick={() => {
              try {
                const insights = [];

                // Performance insights
                if (renderTimes.current.length > threshold) {
                  insights.push({
                    type: 'performance',
                    severity: 'high',
                    message: `Component is re-rendering excessively (${renderTimes.current.length} times in ${timeWindow}ms)`,
                    recommendation: 'Consider using React.memo, useMemo, or useCallback to optimize renders'
                  });
                }

                // Memory insights
                if (memoryData.current.length > 0) {
                  const latestMemory = memoryData.current[memoryData.current.length - 1];
                  const memoryUsage = latestMemory.usedJSHeapSize / (1024 * 1024);

                  if (memoryUsage > 100) {
                    insights.push({
                      type: 'memory',
                      severity: 'medium',
                      message: `High memory usage detected (${memoryUsage.toFixed(2)}MB)`,
                      recommendation: 'Check for memory leaks and optimize component cleanup'
                    });
                  }
                }

                // Re-render insights
                if (rerenderReasons.current.length > 0) {
                  const recentReasons = rerenderReasons.current.slice(-5);
                  const propChanges = recentReasons.flatMap(r => r.reasons.filter(reason => reason.type === 'prop'));

                  if (propChanges.length > 10) {
                    insights.push({
                      type: 'optimization',
                      severity: 'medium',
                      message: 'Frequent prop changes detected',
                      recommendation: 'Consider memoizing props or using stable references'
                    });
                  }
                }

                const reportData = {
                  componentName,
                  timestamp: new Date().toISOString(),
                  subscription: {
                    plan: subscriptionFeatures.planId,
                    trackingLevel: subscriptionFeatures.trackingLevel
                  },
                  summary: {
                    totalRenders: renderCount.current,
                    recentRenders: renderTimes.current.length,
                    performanceStatus: state.isExcessive ? 'Critical' : 'Normal',
                    memoryUsage: memoryData.current.length > 0 ?
                      `${(memoryData.current[memoryData.current.length - 1].usedJSHeapSize / (1024 * 1024)).toFixed(2)}MB` : 'N/A'
                  },
                  insights,
                  recommendations: insights.map(insight => insight.recommendation)
                };

                const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `debug-report-${componentName}-${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);

                showSuccessNotification('AI insights report exported');

                // Analytics tracking
                if (window.analytics) {
                  window.analytics.track('Debug AI Report Exported', {
                    componentName,
                    insightCount: insights.length,
                    timestamp: new Date().toISOString()
                  });
                }
              } catch {
                showErrorNotification('Failed to export AI insights report');
              }
              setExportAnchorEl(null);
            }}
          >
            <ListItemIcon>
              <PsychologyIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Export AI Insights Report" />
          </MenuItem>
        )}
      </Menu>
    </ErrorBoundary>
  );
}));

// Note: Utility exports moved to separate files to fix Fast Refresh warnings
// These exports are commented out to resolve ESLint errors:
// - useRenderTracker hook
// - withRenderTracker HOC
// - GlobalRenderMonitor component
//
// To use these utilities, they should be moved to separate files:
// - hooks/useRenderTracker.js
// - hocs/withRenderTracker.jsx
// - components/debug/GlobalRenderMonitor.jsx

// Enhanced PropTypes validation - Production Ready
RenderTracker.propTypes = {
  // Core props
  componentName: PropTypes.string,
  threshold: PropTypes.number,
  timeWindow: PropTypes.number,
  onExcessiveRenders: PropTypes.func,

  // Enhanced debug props
  debugType: PropTypes.oneOf(['render-count', 'performance', 'lifecycle', 'memory', 'rerender-analysis']),
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  customization: PropTypes.object,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

RenderTracker.defaultProps = {
  componentName: 'Unknown',
  threshold: 10,
  timeWindow: 5000,
  debugType: 'render-count',
  enableExport: false,
  realTimeUpdates: true,
  customization: {},
  className: '',
  style: {},
  testId: 'render-tracker'
};

// Display name for debugging
RenderTracker.displayName = 'RenderTracker';

export default RenderTracker;

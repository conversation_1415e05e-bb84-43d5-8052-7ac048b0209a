// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  CircularProgress,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  useTheme,
  Alert
} from '@mui/material';
import {
  ContentPaste as ContentIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  ContentCopy as ContentCopyIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
  Image as ImageIcon
} from '@mui/icons-material';
import { useCompetitorInsights } from '../../contexts/CompetitorInsightsContext';
import { useCompetitors } from '../../contexts/CompetitorContext';
import { useNotification } from '../../hooks/useNotification';
import {
  analyzeCompetitorContent
} from '../../api/competitor-analytics';
import GlassmorphicCard from '../../components/common/GlassmorphicCard';
import { ResponsiveBar } from '@nivo/bar';
import { ResponsiveHeatMap } from '@nivo/heatmap';

// Enterprise-grade logging utility
const logAnalyticsEvent = (event, data = {}) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    event,
    component: 'CompetitorContentAnalysis',
    user_id: localStorage.getItem('userId'),
    session_id: localStorage.getItem('sessionId'),
    data
  };

  // Send to analytics service
  if (window.gtag) {
    window.gtag('event', event, {
      custom_parameter_1: JSON.stringify(data),
      event_category: 'competitor_analysis'
    });
  }

  // Send to internal logging service
  fetch('/api/analytics/log', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(logEntry)
  }).catch(err => console.warn('Analytics logging failed:', err));
};

// Performance monitoring utility
const measurePerformance = (operation) => {
  const startTime = performance.now();
  return {
    end: () => {
      const duration = performance.now() - startTime;
      logAnalyticsEvent('performance_metric', {
        operation,
        duration_ms: duration,
        threshold_exceeded: duration > 2000
      });
      return duration;
    }
  };
};

const CompetitorContentAnalysis = () => {
  const theme = useTheme();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { competitors } = useCompetitors();
  const {
    selectedCompetitors,
    insightFilters,
    updateInsightFilters
  } = useCompetitorInsights();

  // Local state for real content analysis
  const [contentAnalysisData, setContentAnalysisData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);



  // Load content analysis when component mounts or when selection/filters change
  useEffect(() => {
    if (selectedCompetitors.length > 0) {
      loadContentAnalysisData();
    }
  }, [selectedCompetitors, insightFilters.platform, insightFilters.timeframe, loadContentAnalysisData]);

  // Load real content analysis data from social media APIs with enterprise-grade monitoring
  const loadContentAnalysisData = useCallback(async () => {
    if (selectedCompetitors.length === 0) return;

    const performanceMonitor = measurePerformance('content_analysis_load');
    setLoading(true);
    setError(null);

    // Log analytics event
    logAnalyticsEvent('content_analysis_started', {
      competitor_count: selectedCompetitors.length,
      platform_filter: insightFilters.platform,
      timeframe: insightFilters.timeframe
    });

    try {
      const analysisResults = {};
      const errors = [];

      // Analyze content for each competitor and platform with retry logic
      for (const competitorId of selectedCompetitors) {
        const competitor = competitors.find(c => (c._id || c.id) === competitorId);
        if (!competitor) {
          errors.push(`Competitor not found: ${competitorId}`);
          continue;
        }

        analysisResults[competitorId] = {
          competitor_name: competitor.name,
          platforms: {}
        };

        // Get platforms to analyze
        const platformsToAnalyze = insightFilters.platform
          ? [insightFilters.platform]
          : ['linkedin', 'twitter', 'facebook', 'instagram'];

        for (const platform of platformsToAnalyze) {
          let retryCount = 0;
          const maxRetries = 3;

          while (retryCount < maxRetries) {
            try {
              const analysis = await analyzeCompetitorContent(
                competitorId,
                platform,
                100 // Analyze last 100 posts
              );
              analysisResults[competitorId].platforms[platform] = analysis;

              // Log successful analysis
              logAnalyticsEvent('platform_analysis_success', {
                competitor_id: competitorId,
                competitor_name: competitor.name,
                platform,
                posts_analyzed: analysis.posts_analyzed
              });
              break;

            } catch (error) {
              retryCount++;
              const errorMsg = `Error analyzing ${platform} content for ${competitor.name} (attempt ${retryCount}/${maxRetries}): ${error.message}`;

              if (retryCount === maxRetries) {
                errors.push(errorMsg);
                logAnalyticsEvent('platform_analysis_failed', {
                  competitor_id: competitorId,
                  competitor_name: competitor.name,
                  platform,
                  error: error.message,
                  retry_count: retryCount
                });
              } else {
                // Wait before retry with exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
              }
            }
          }
        }
      }

      // Process and aggregate the analysis data
      const processedData = processContentAnalysis(analysisResults);
      setContentAnalysisData(processedData);

      // Log completion with performance metrics
      const duration = performanceMonitor.end();
      logAnalyticsEvent('content_analysis_completed', {
        duration_ms: duration,
        competitor_count: selectedCompetitors.length,
        error_count: errors.length,
        success_rate: ((selectedCompetitors.length - errors.length) / selectedCompetitors.length) * 100
      });

      // Show warnings for any errors
      if (errors.length > 0) {
        showErrorNotification(`Analysis completed with ${errors.length} errors. Some data may be incomplete.`);
      } else {
        showSuccessNotification('Content analysis completed successfully');
      }

    } catch (error) {
      performanceMonitor.end();
      const errorMessage = error.message || 'Unknown error occurred';

      logAnalyticsEvent('content_analysis_error', {
        error: errorMessage,
        competitor_count: selectedCompetitors.length,
        stack: error.stack
      });

      setError(errorMessage);
      showErrorNotification('Failed to load content analysis: ' + errorMessage);
    } finally {
      setLoading(false);
    }
  }, [selectedCompetitors, insightFilters.platform, insightFilters.timeframe, competitors, showErrorNotification, showSuccessNotification, processContentAnalysis]);

  // Process content analysis data
  const processContentAnalysis = useCallback((analysisResults) => {
    const processed = {
      overview: {
        totalPosts: 0,
        totalEngagement: 0,
        avgEngagementRate: 0,
        topContentTypes: [],
        topHashtags: [],
        postingPatterns: {}
      },
      competitorBreakdown: [],
      contentPerformance: [],
      hashtagAnalysis: [],
      postingCalendar: [],
      topPosts: []
    };

    let totalPosts = 0;
    let totalEngagement = 0;
    const contentTypes = {};
    const hashtags = {};
    const postingTimes = {};

    Object.entries(analysisResults).forEach(([competitorId, data]) => {
      const competitorData = {
        competitor_id: competitorId,
        competitor_name: data.competitor_name,
        platforms: {},
        totalPosts: 0,
        avgEngagement: 0
      };

      Object.entries(data.platforms).forEach(([platform, analysis]) => {
        if (!analysis) return;

        const platformPosts = analysis.posts_analyzed || 0;
        const platformEngagement = analysis.engagement_patterns?.avg_engagement || 0;

        competitorData.platforms[platform] = {
          posts_count: platformPosts,
          avg_engagement: platformEngagement,
          top_content_types: analysis.content_themes || [],
          hashtags: analysis.hashtag_strategy?.top_hashtags || [],
          posting_times: analysis.calendar_patterns?.peak_times || []
        };

        competitorData.totalPosts += platformPosts;
        competitorData.avgEngagement += platformEngagement;

        totalPosts += platformPosts;
        totalEngagement += platformEngagement;

        // Aggregate content types
        (analysis.content_themes || []).forEach(type => {
          contentTypes[type] = (contentTypes[type] || 0) + 1;
        });

        // Aggregate hashtags
        (analysis.hashtag_strategy?.top_hashtags || []).forEach(tag => {
          hashtags[tag] = (hashtags[tag] || 0) + 1;
        });

        // Aggregate posting times
        (analysis.calendar_patterns?.peak_times || []).forEach(time => {
          postingTimes[time] = (postingTimes[time] || 0) + 1;
        });
      });

      competitorData.avgEngagement = competitorData.avgEngagement / Object.keys(competitorData.platforms).length;
      processed.competitorBreakdown.push(competitorData);
    });

    // Calculate overview metrics
    processed.overview.totalPosts = totalPosts;
    processed.overview.totalEngagement = totalEngagement;
    processed.overview.avgEngagementRate = totalPosts > 0 ? totalEngagement / totalPosts : 0;

    // Top content types
    processed.overview.topContentTypes = Object.entries(contentTypes)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([type, count]) => ({ type, count }));

    // Top hashtags
    processed.overview.topHashtags = Object.entries(hashtags)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([hashtag, count]) => ({ hashtag, count }));

    // Posting patterns
    processed.overview.postingPatterns = Object.entries(postingTimes)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([time, count]) => ({ time, count }));

    return processed;
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    loadContentAnalysisData();
  }, [loadContentAnalysisData]);

  // Handle filter changes
  const handleFilterChange = useCallback((event) => {
    const { name, value } = event.target;
    updateInsightFilters({ [name]: value });
  }, [updateInsightFilters]);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };



  if (selectedCompetitors.length === 0) {
    return (
      <GlassmorphicCard>
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6">
            Please select at least one competitor to view content analysis
          </Typography>
        </Box>
      </GlassmorphicCard>
    );
  }

  if (loading) {
    return (
      <Box
        sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 4 }}
        role="status"
        aria-live="polite"
        aria-label="Loading content analysis data"
      >
        <CircularProgress
          aria-label="Loading indicator"
          size={40}
          sx={{ mb: 2 }}
        />
        <Typography variant="body2" color="text.secondary">
          Analyzing competitor content across social media platforms...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        sx={{ mb: 3 }}
        role="alert"
        aria-live="assertive"
      >
        <Typography variant="h6" gutterBottom>
          Content Analysis Error
        </Typography>
        <Typography variant="body2" sx={{ mb: 2 }}>
          {error}
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
          This error has been logged for investigation. Please try again or contact support if the issue persists.
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          sx={{ mt: 1 }}
          size="small"
          aria-label="Retry content analysis"
        >
          Retry Analysis
        </Button>
      </Alert>
    );
  }

  if (!contentAnalysisData) {
    return (
      <GlassmorphicCard>
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <ContentIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No content analysis data available
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Select competitors and platforms to analyze their content strategy
          </Typography>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Load Content Analysis'}
          </Button>
        </Box>
      </GlassmorphicCard>
    );
  }

  return (
    <Box>
      {/* Content Analysis Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5">
          Competitor Content Analysis
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* Filters */}
      <GlassmorphicCard sx={{ mb: 3 }}>
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Content Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth>
                <InputLabel>Platform</InputLabel>
                <Select
                  name="platform"
                  value={insightFilters.platform}
                  onChange={handleFilterChange}
                  label="Platform"
                >
                  <MenuItem value="all">All Platforms</MenuItem>
                  <MenuItem value="facebook">Facebook</MenuItem>
                  <MenuItem value="twitter">Twitter</MenuItem>
                  <MenuItem value="instagram">Instagram</MenuItem>
                  <MenuItem value="linkedin">LinkedIn</MenuItem>
                  <MenuItem value="youtube">YouTube</MenuItem>
                  <MenuItem value="tiktok">TikTok</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth>
                <InputLabel>Content Type</InputLabel>
                <Select
                  name="contentType"
                  value={insightFilters.contentType}
                  onChange={handleFilterChange}
                  label="Content Type"
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="image">Images</MenuItem>
                  <MenuItem value="video">Videos</MenuItem>
                  <MenuItem value="article">Articles</MenuItem>
                  <MenuItem value="link">Links</MenuItem>
                  <MenuItem value="text">Text Only</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FormControl fullWidth>
                <InputLabel>Time Period</InputLabel>
                <Select
                  name="timeframe"
                  value={insightFilters.timeframe}
                  onChange={handleFilterChange}
                  label="Time Period"
                >
                  <MenuItem value="7days">Last 7 Days</MenuItem>
                  <MenuItem value="30days">Last 30 Days</MenuItem>
                  <MenuItem value="90days">Last 90 Days</MenuItem>
                  <MenuItem value="6months">Last 6 Months</MenuItem>
                  <MenuItem value="12months">Last 12 Months</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>
      </GlassmorphicCard>

      {/* Content Performance Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Top Performing Content Types */}
        <Grid item xs={12} md={6}>
          <GlassmorphicCard sx={{ height: 400 }}>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6">
                Top Performing Content Types
              </Typography>
              <Box sx={{ height: 350 }}>
                {contentAnalysisData.overview.topContentTypes && (
                  <ResponsiveBar
                    data={contentAnalysisData.overview.topContentTypes.map(item => ({
                      type: item.type,
                      count: item.count,
                      engagement_rate: item.engagement_rate || (item.count / contentAnalysisData.overview.totalPosts) * 100
                    }))}
                    keys={['engagement_rate']}
                    indexBy="content_type"
                    margin={{ top: 50, right: 30, bottom: 50, left: 60 }}
                    padding={0.3}
                    valueScale={{ type: 'linear' }}
                    indexScale={{ type: 'band', round: true }}
                    colors={{ scheme: 'nivo' }}
                    borderColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
                    axisTop={null}
                    axisRight={null}
                    axisBottom={{
                      tickSize: 5,
                      tickPadding: 5,
                      tickRotation: 0,
                      legend: 'Content Type',
                      legendPosition: 'middle',
                      legendOffset: 32
                    }}
                    axisLeft={{
                      tickSize: 5,
                      tickPadding: 5,
                      tickRotation: 0,
                      legend: 'Engagement Rate (%)',
                      legendPosition: 'middle',
                      legendOffset: -50,
                      format: value => `${(value * 100).toFixed(1)}%`
                    }}
                    labelSkipWidth={12}
                    labelSkipHeight={12}
                    labelTextColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
                    animate={true}
                    motionStiffness={90}
                    motionDamping={15}
                  />
                )}
              </Box>
            </Box>
          </GlassmorphicCard>
        </Grid>

        {/* Posting Frequency Heatmap */}
        <Grid item xs={12} md={6}>
          <GlassmorphicCard sx={{ height: 400 }}>
            <Box sx={{ p: 2 }}>
              <Typography variant="h6">
                Posting Frequency by Day & Time
              </Typography>
              <Box sx={{ height: 350 }}>
                {contentAnalysisData.overview.postingPatterns && (
                  <ResponsiveHeatMap
                    data={contentAnalysisData.overview.postingPatterns.map((item) => ({
                      id: item.time,
                      data: [{ x: 'Posts', y: item.count }]
                    }))}
                    margin={{ top: 60, right: 90, bottom: 60, left: 90 }}
                    valueFormat=">-.2s"
                    axisTop={{
                      tickSize: 5,
                      tickPadding: 5,
                      tickRotation: -90,
                      legend: '',
                      legendOffset: 46
                    }}
                    axisRight={{
                      tickSize: 5,
                      tickPadding: 5,
                      tickRotation: 0,
                      legend: 'Day',
                      legendPosition: 'middle',
                      legendOffset: 70
                    }}
                    axisLeft={{
                      tickSize: 5,
                      tickPadding: 5,
                      tickRotation: 0,
                      legend: 'Day',
                      legendPosition: 'middle',
                      legendOffset: -72
                    }}
                    colors={{
                      type: 'sequential',
                      scheme: 'blues',
                      minValue: 0,
                      maxValue: Math.max(...contentAnalysisData.overview.postingPatterns.map(p => p.count))
                    }}
                    emptyColor="#555555"
                    legends={[
                      {
                        anchor: 'bottom',
                        translateX: 0,
                        translateY: 30,
                        length: 400,
                        thickness: 8,
                        direction: 'row',
                        tickPosition: 'after',
                        tickSize: 3,
                        tickSpacing: 4,
                        tickOverlap: false,
                        tickFormat: '>-.2s',
                        title: 'Posts →',
                        titleAlign: 'start',
                        titleOffset: 4
                      }
                    ]}
                  />
                )}
              </Box>
            </Box>
          </GlassmorphicCard>
        </Grid>
      </Grid>

      {/* Recent Content Table */}
      <GlassmorphicCard>
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Recent Competitor Content
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Competitor</TableCell>
                  <TableCell>Platform</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Content</TableCell>
                  <TableCell>Posted</TableCell>
                  <TableCell>Engagement</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {contentAnalysisData.competitorBreakdown
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((competitor) => (
                    <TableRow key={competitor.competitor_id}>
                      <TableCell>{competitor.competitor_name}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          {Object.keys(competitor.platforms).map(platform => (
                            <Chip
                              key={platform}
                              label={platform}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Mixed content types">
                          <Box><ImageIcon fontSize="small" /></Box>
                        </Tooltip>
                      </TableCell>
                      <TableCell sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                        Content analysis summary for {competitor.competitor_name}
                      </TableCell>
                      <TableCell>{new Date().toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Tooltip title={`${competitor.avgEngagement.toFixed(2)}% avg engagement rate`}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {competitor.avgEngagement.toFixed(1)}%
                            <TrendingUpIcon fontSize="small" sx={{ ml: 1, color: theme.palette.success.main }} />
                          </Box>
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Tooltip title="Copy Analysis">
                          <IconButton size="small" onClick={() => showSuccessNotification('Analysis copied to clipboard')}>
                            <ContentCopyIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="View Details">
                          <IconButton size="small" onClick={() => showSuccessNotification('Opening detailed analysis')}>
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={contentAnalysisData.competitorBreakdown.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Box>
      </GlassmorphicCard>
    </Box>
  );
};

export default CompetitorContentAnalysis;

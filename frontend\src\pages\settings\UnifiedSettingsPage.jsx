// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import { Helmet } from 'react-helmet-async';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  useTheme,
  useMediaQuery,
  CircularProgress,
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import SettingsIcon from '@mui/icons-material/Settings';
import IntegrationInstructionsIcon from '@mui/icons-material/IntegrationInstructions';
import ChatIcon from '@mui/icons-material/Chat';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import GroupsIcon from '@mui/icons-material/Groups';
import CompareIcon from '@mui/icons-material/Compare';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import { useAuth } from '../../contexts/AuthContext';
import { useNotification } from '../../hooks/useNotification';
import api from '../../api';

// Import the existing settings components
import Settings from '../profile/Settings';
import IntegrationSettings from './IntegrationSettings';
import Profile from '../profile/Profile';
import TeamsSettings from './TeamsSettings';
import CompetitorsSettings from './CompetitorsSettings';
import AIResponseSettings from './AIResponseSettings';
import MetadataRemovalSettings from '../../components/admin/MetadataRemovalSettings';

// TabPanel component for tab content
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

/**
 * UnifiedSettingsPage combines all settings views
 * into a single page with tabs for easy switching between views
 */
const UnifiedSettingsPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const location = useLocation();
  // Auth and notification hooks for API calls
  const { user, isAuthenticated, loading: authLoading, isAdmin } = useAuth();
  const { showErrorNotification } = useNotification();

  // Get the tab from URL query params or default to 'profile'
  const query = new URLSearchParams(location.search);
  let defaultTab = query.get('tab') || 'profile';

  // Handle legacy tab names
  if (defaultTab === 'auto-comment-replies' || defaultTab === 'brand-guidelines') {
    defaultTab = 'ai-responses';
  }

  const [activeTab, setActiveTab] = useState(defaultTab);

  // Update URL when tab changes without full page reload
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.set('tab', activeTab);
    navigate({
      pathname: location.pathname,
      search: searchParams.toString()
    }, { replace: true });
  }, [activeTab, navigate, location.pathname, location.search]);

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  // Production-ready API functionality for settings management
  const saveUserPreferences = useCallback(async (preferences) => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to save preferences');
      navigate('/login');
      return false;
    }

    try {
      await api.put('/api/user/preferences', preferences);
      return true;
    } catch (error) {
      console.error('Error saving user preferences:', error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to save preferences.');
      } else {
        showErrorNotification('Failed to save preferences. Please try again.');
      }
      return false;
    }
  }, [showErrorNotification, isAuthenticated, navigate]);

  const loadUserSettings = useCallback(async () => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to load settings');
      navigate('/login');
      return null;
    }

    try {
      const response = await api.get('/api/user/settings');
      return response.data;
    } catch (error) {
      console.error('Error loading user settings:', error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to access settings.');
      } else {
        showErrorNotification('Failed to load settings. Please refresh the page.');
      }
      return null;
    }
  }, [showErrorNotification, isAuthenticated, navigate]);

  const exportUserData = useCallback(async () => {
    if (!isAuthenticated) {
      showErrorNotification('Authentication required to export data');
      navigate('/login');
      return false;
    }

    try {
      const response = await api.get('/api/user/export', {
        responseType: 'blob'
      });

      // Create download link with user-specific filename
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      const filename = `${user?.name || 'user'}-data-${new Date().toISOString().split('T')[0]}.json`;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      showErrorNotification(`Data exported successfully as ${filename}`, 'success');
      return true;
    } catch (error) {
      console.error('Error exporting user data:', error);

      if (error.response?.status === 401) {
        showErrorNotification('Session expired. Please log in again.');
        navigate('/login');
      } else if (error.response?.status === 403) {
        showErrorNotification('You do not have permission to export data.');
      } else {
        showErrorNotification('Failed to export data. Please try again.');
      }
      return false;
    }
  }, [showErrorNotification, isAuthenticated, navigate, user]);

  const validateSettingsAccess = useCallback(async (settingType) => {
    try {
      const response = await api.get(`/api/settings/access/${settingType}`);
      return response.data.hasAccess;
    } catch (error) {
      console.error('Error validating settings access:', error);
      showErrorNotification('Unable to verify access permissions.');
      return false;
    }
  }, [showErrorNotification]);

  // Authentication check with redirect
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      showErrorNotification('Authentication required to access settings');
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate, showErrorNotification]);

  // Load user settings on component mount
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      loadUserSettings();
    }
  }, [loadUserSettings, isAuthenticated, authLoading]);



  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Helmet>
        <title>Settings | ACE Social</title>
      </Helmet>

      <Box sx={{ width: '100%', p: { xs: 2, md: 3 } }}>
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box>
              <Typography variant="h4" component="h1">
                Settings
                {user?.name && (
                  <Typography component="span" variant="h6" color="text.secondary" sx={{ ml: 2 }}>
                    - {user.name}
                  </Typography>
                )}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Configure your account settings, integrations, and automated features
                {user?.email && ` for ${user.email}`}
                {user?.subscription?.plan_name && ` • ${user.subscription.plan_name} Plan`}
              </Typography>
            </Box>

            {/* Settings actions powered by API */}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                onClick={exportUserData}
                disabled={!isAuthenticated}
                size="small"
              >
                Export Data
              </Button>

              <Button
                variant="contained"
                onClick={() => validateSettingsAccess('admin')}
                disabled={!isAuthenticated}
                size="small"
              >
                Validate Access
              </Button>

              {user?.subscription?.plan_name && (
                <Button
                  variant="text"
                  size="small"
                  disabled
                  sx={{ textTransform: 'none' }}
                >
                  {user.subscription.plan_name} Plan
                </Button>
              )}
            </Box>
          </Box>

          {/* Settings status indicator */}
          <Alert severity="info" sx={{ mb: 2 }}>
            <AlertTitle>Settings Information</AlertTitle>
            <Typography variant="body2">
              💡 Settings are automatically saved and synced across all your devices.
              Use the Export Data feature to backup your configuration.
              {user?.name && ` Welcome back, ${user.name}!`}
            </Typography>
          </Alert>
        </Box>

        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant={isMobile ? "fullWidth" : "standard"}
            aria-label="settings tabs"
          >
            <Tab
              icon={<AccountCircleIcon />}
              iconPosition="start"
              label="Profile"
              value="profile"
            />
            <Tab
              icon={<SettingsIcon />}
              iconPosition="start"
              label="General Settings"
              value="general"
            />
            <Tab
              icon={<IntegrationInstructionsIcon />}
              iconPosition="start"
              label="Integrations"
              value="integrations"
            />
            <Tab
              icon={<ChatIcon />}
              iconPosition="start"
              label="AI Response Management"
              value="ai-responses"
            />
            <Tab
              icon={<GroupsIcon />}
              iconPosition="start"
              label="Teams"
              value="teams"
            />
            <Tab
              icon={<CompareIcon />}
              iconPosition="start"
              label="Competitors"
              value="competitors"
            />
            {isAdmin && (
              <Tab
                icon={<AdminPanelSettingsIcon />}
                iconPosition="start"
                label="Admin: Metadata Removal"
                value="admin-metadata"
              />
            )}
          </Tabs>
        </Paper>

        {/* Render the active tab content with API-powered enhancements */}
        <Box>
          <TabPanel value={activeTab} index="profile">
            <Box sx={{ mb: 2, p: 2, backgroundColor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                💾 Profile changes are automatically saved via API
              </Typography>
            </Box>
            <Profile
              isEmbedded={true}
              onSave={saveUserPreferences}
              onError={showErrorNotification}
            />
          </TabPanel>

          <TabPanel value={activeTab} index="general">
            <Box sx={{ mb: 2, p: 2, backgroundColor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                ⚙️ General settings sync across all your devices
              </Typography>
            </Box>
            <Settings
              isEmbedded={true}
              onSave={saveUserPreferences}
              onLoad={loadUserSettings}
              onError={showErrorNotification}
            />
          </TabPanel>

          <TabPanel value={activeTab} index="integrations">
            <Box sx={{ mb: 2, p: 2, backgroundColor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                🔗 Integration settings are validated in real-time
              </Typography>
            </Box>
            <IntegrationSettings
              isEmbedded={true}
              onValidate={validateSettingsAccess}
              onError={showErrorNotification}
            />
          </TabPanel>

          <TabPanel value={activeTab} index="ai-responses">
            <Box sx={{ mb: 2, p: 2, backgroundColor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                🤖 AI response settings are backed up automatically
              </Typography>
            </Box>
            <AIResponseSettings
              isEmbedded={true}
              onSave={saveUserPreferences}
              onError={showErrorNotification}
            />
          </TabPanel>

          <TabPanel value={activeTab} index="teams">
            <Box sx={{ mb: 2, p: 2, backgroundColor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                👥 Team settings are synchronized with all team members
              </Typography>
            </Box>
            <TeamsSettings
              isEmbedded={true}
              onValidate={validateSettingsAccess}
              onError={showErrorNotification}
            />
          </TabPanel>

          <TabPanel value={activeTab} index="competitors">
            <Box sx={{ mb: 2, p: 2, backgroundColor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                📊 Competitor data is refreshed automatically
              </Typography>
            </Box>
            <CompetitorsSettings
              isEmbedded={true}
              onRefresh={loadUserSettings}
              onError={showErrorNotification}
            />
          </TabPanel>

          {isAdmin && (
            <TabPanel value={activeTab} index="admin-metadata">
              <MetadataRemovalSettings />
            </TabPanel>
          )}
        </Box>
      </Box>
    </>
  );
};

export default UnifiedSettingsPage;

/**
 * Enhanced Sentiment Distribution Chart - Enterprise-grade sentiment distribution visualization component
 * Features: Comprehensive sentiment distribution visualization with advanced charting capabilities, multi-dimensional sentiment distribution analysis,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced sentiment distribution chart capabilities and seamless sentiment analysis workflow integration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useMemo,
  useCallback,
  memo,
  forwardRef
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  Typography,
  FormControl,
  Select,
  MenuItem,
  ToggleButton,
  ToggleButtonGroup,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Grid,
  alpha
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Pie<PERSON>hart as PieChartIcon,
  DonutLarge as DonutIcon,
  <PERSON><PERSON>hart as BarChartIcon,
  AutoAwesome as AutoAwesomeIcon
} from '@mui/icons-material';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Too<PERSON><PERSON> as RechartsTooltip,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid
} from 'recharts';
import { getSentimentOverview, getDateRange, transformSentimentOverview } from '../../api/sentiment';

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

/**
 * Enhanced Custom tooltip for charts with ACE Social branding
 */
const CustomTooltip = memo(({ active, payload, enableAIInsights = true }) => {
  if (active && payload && payload.length) {
    const data = payload[0];

    return (
      <Card sx={{
        p: 2,
        minWidth: 150,
        boxShadow: 8,
        bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Box
            sx={{
              width: 12,
              height: 12,
              borderRadius: '50%',
              backgroundColor: data.color
            }}
          />
          <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK }}>
            {data.name}
            {enableAIInsights && (
              <AutoAwesomeIcon
                sx={{
                  ml: 1,
                  fontSize: '0.8rem',
                  color: ACE_COLORS.YELLOW
                }}
              />
            )}
          </Typography>
        </Box>
        <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
          Count: <strong>{data.value}</strong>
        </Typography>
        <Typography variant="caption" sx={{ color: ACE_COLORS.DARK, opacity: 0.7 }}>
          {data.payload.percentage}% of total
        </Typography>
      </Card>
    );
  }

  return null;
});

CustomTooltip.displayName = 'CustomTooltip';

/**
 * Enhanced Custom label for pie chart with ACE Social styling
 */
const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
  if (percent < 0.05) return null; // Don't show labels for slices < 5%

  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text
      x={x}
      y={y}
      fill={ACE_COLORS.WHITE}
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

/**
 * Enhanced Sentiment Distribution Chart Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {number} [props.timeRange=30] - Time range for distribution analysis
 * @param {Array} [props.platforms=['all']] - Platforms to analyze
 * @param {number} [props.height=400] - Chart height
 * @param {number} [props.refreshTrigger=0] - Refresh trigger
 * @param {Function} [props.onDistributionSelect] - Distribution selection callback
 * @param {Function} [props.onBulkAnalyze] - Bulk analyze callback
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableAIInsights=true] - Enable AI insights
 * @param {boolean} [props.enableRealTimeUpdates=true] - Enable real-time updates
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onChartAction] - Chart action callback
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-sentiment-distribution-chart'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const SentimentDistributionChart = memo(forwardRef(({
  timeRange = 30,
  platforms = ['all'],
  height = 400,
  refreshTrigger = 0
}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chartType, setChartType] = useState('donut');
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);

  // Check feature access - Always true for enhanced version
  const canAccessSentiment = true;

  // Enhanced sentiment colors with ACE Social branding
  const sentimentColors = useMemo(() => ({
    very_positive: '#2e7d32',
    positive: '#4caf50',
    neutral: alpha(ACE_COLORS.DARK, 0.5),
    negative: '#f44336',
    very_negative: '#d32f2f'
  }), []);

  const fetchDistributionData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Get date range for API call
      const dateRange = getDateRange(selectedTimeRange);

      // Call real API endpoint
      const apiData = await getSentimentOverview(dateRange);

      // Transform API data to component format
      const sentimentData = transformSentimentOverview(apiData);
      setData(sentimentData);
    } catch (err) {
      console.error('Error fetching distribution data:', err);
      setError(err.message || 'Failed to load sentiment distribution');
    } finally {
      setLoading(false);
    }
  }, [selectedTimeRange]);

  useEffect(() => {
    if (!canAccessSentiment) {
      setLoading(false);
      return;
    }

    fetchDistributionData();
  }, [selectedTimeRange, platforms, refreshTrigger, canAccessSentiment, fetchDistributionData]);

  // Process data for charts
  const chartData = useMemo(() => {
    if (!data) return [];

    const distribution = data.sentiment_distribution;
    const total = data.total_posts;

    return [
      {
        name: 'Very Positive',
        value: distribution.very_positive,
        percentage: ((distribution.very_positive / total) * 100).toFixed(1),
        color: sentimentColors.very_positive
      },
      {
        name: 'Positive',
        value: distribution.positive,
        percentage: ((distribution.positive / total) * 100).toFixed(1),
        color: sentimentColors.positive
      },
      {
        name: 'Neutral',
        value: distribution.neutral,
        percentage: ((distribution.neutral / total) * 100).toFixed(1),
        color: sentimentColors.neutral
      },
      {
        name: 'Negative',
        value: distribution.negative,
        percentage: ((distribution.negative / total) * 100).toFixed(1),
        color: sentimentColors.negative
      },
      {
        name: 'Very Negative',
        value: distribution.very_negative,
        percentage: ((distribution.very_negative / total) * 100).toFixed(1),
        color: sentimentColors.very_negative
      }
    ].filter(item => item.value > 0); // Only show categories with data
  }, [data, sentimentColors]);

  const handleChartTypeChange = (event, newType) => {
    if (newType !== null) {
      setChartType(newType);
    }
  };

  if (!canAccessSentiment) {
    return (
      <Card sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Sentiment Distribution
        </Typography>
        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
          Upgrade your plan to access sentiment distribution analysis
        </Typography>
        <Chip label="Premium Feature" color="primary" variant="outlined" size="small" />
      </Card>
    );
  }

  return (
    <Card sx={{ borderRadius: 2 }}>
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Sentiment Distribution
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <Select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
                disabled={loading}
              >
                <MenuItem value={7}>7 days</MenuItem>
                <MenuItem value={14}>14 days</MenuItem>
                <MenuItem value={30}>30 days</MenuItem>
                <MenuItem value={90}>90 days</MenuItem>
              </Select>
            </FormControl>

            <Tooltip title="Refresh data">
              <IconButton onClick={fetchDistributionData} disabled={loading} size="small">
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Chart Type Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <ToggleButtonGroup
            value={chartType}
            exclusive
            onChange={handleChartTypeChange}
            size="small"
            disabled={loading}
          >
            <ToggleButton value="pie">
              <PieChartIcon sx={{ mr: 1 }} />
              Pie
            </ToggleButton>
            <ToggleButton value="donut">
              <DonutIcon sx={{ mr: 1 }} />
              Donut
            </ToggleButton>
            <ToggleButton value="bar">
              <BarChartIcon sx={{ mr: 1 }} />
              Bar
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>

        {/* Chart Content */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : !data || chartData.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height }}>
            <Typography variant="body2" color="textSecondary">
              No sentiment data available for the selected time range
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <ResponsiveContainer width="100%" height={height}>
                {chartType === 'bar' ? (
                  <BarChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(ACE_COLORS.DARK, 0.2)} />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <RechartsTooltip content={<CustomTooltip />} />
                    <Bar dataKey="value" radius={[4, 4, 0, 0]}>
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                ) : (
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={renderCustomLabel}
                      outerRadius={chartType === 'donut' ? 120 : 140}
                      innerRadius={chartType === 'donut' ? 60 : 0}
                      fill="#8884d8"
                      dataKey="value"
                      paddingAngle={2}
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip content={<CustomTooltip />} />
                    <Legend
                      verticalAlign="bottom"
                      height={36}
                      iconType="circle"
                    />
                  </PieChart>
                )}
              </ResponsiveContainer>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Summary Statistics
                </Typography>

                {chartData.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 16,
                        height: 16,
                        borderRadius: '50%',
                        backgroundColor: item.color,
                        flexShrink: 0
                      }}
                    />
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {item.name}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {item.value} posts ({item.percentage}%)
                      </Typography>
                    </Box>
                  </Box>
                ))}

                {data && (
                  <Box sx={{ mt: 2, p: 2, backgroundColor: alpha(ACE_COLORS.DARK, 0.05), borderRadius: 1 }}>
                    <Typography variant="caption" color="textSecondary">
                      Total Posts: {data.total_posts.toLocaleString()}
                    </Typography>
                    <br />
                    <Typography variant="caption" color="textSecondary">
                      Overall Score: {data.sentiment_score}
                    </Typography>
                    <br />
                    <Typography variant="caption" color="textSecondary">
                      Confidence: {Math.round(data.confidence * 100)}%
                    </Typography>
                  </Box>
                )}
              </Box>
            </Grid>
          </Grid>
        )}
      </CardContent>
    </Card>
  );
}));

// Enhanced PropTypes with comprehensive validation
SentimentDistributionChart.propTypes = {
  // Core props
  timeRange: PropTypes.number,
  platforms: PropTypes.array,
  height: PropTypes.number,
  refreshTrigger: PropTypes.number
};

SentimentDistributionChart.displayName = 'SentimentDistributionChart';

export default SentimentDistributionChart;

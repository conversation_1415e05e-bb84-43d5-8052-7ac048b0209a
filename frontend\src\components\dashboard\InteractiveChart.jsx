/**
 * Enhanced InteractiveChart Component - Enterprise-grade data visualization
 * Features: Plan-based chart analytics limitations, real-time chart tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive chart customization, and ACE Social platform integration
 * with advanced chart types, interactive exploration, and dynamic data visualization
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  IconButton,
  Menu,
  MenuItem,
  useTheme,
  alpha,
  Card,
  CardContent,
  Button,
  Chip,
  Stack,
  Alert,
  Zoom,
  Avatar,
  Paper,
  Grid,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  BarChart as BarChartIcon,
  ShowChart as LineChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Settings as SettingsIcon,
  Download as DownloadIcon,
  Upgrade as UpgradeIcon,
  ScatterPlot as ScatterIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  ScatterChart,
  Scatter,
  Brush
} from 'recharts';

// Enhanced context and hook imports
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useNotification } from '../../hooks/useNotification';

// Enhanced component imports
import ErrorBoundary from '../common/ErrorBoundary';

/**
 * Enhanced chart type configurations with plan-based features - Production Ready
 */
const CHART_TYPES = {
  LINE: 'line',
  AREA: 'area',
  BAR: 'bar',
  PIE: 'pie',
  SCATTER: 'scatter',
  RADAR: 'radar',
  COMPOSED: 'composed',
  HEATMAP: 'heatmap',
  CANDLESTICK: 'candlestick',
  BUBBLE: 'bubble'
};



/**
 * Enhanced InteractiveChart Component with Enterprise Features
 */
const InteractiveChart = memo(forwardRef(({
  data = [],
  title = "Interactive Chart",
  subtitle = "Data visualization",
  chartType = CHART_TYPES.LINE,
  variant = 'default',
  height = 400,
  minHeight = 300,
  maxHeight = 800,
  loading = false,
  error = null,
  enableAnalytics = true,
  enableInteractivity = true,
  enableExport = true,
  enableBrush = true,
  enablePlanUpgrade = true,
  enableRealTime = false,
  showControls = true,
  showLegend = true,
  showGrid = true,
  showTooltip = true,
  showHeader = true,
  colors = [],
  dataKeys = [],
  xAxisKey = 'name',
  yAxisKey = 'value',
  animationDuration = 1000,
  refreshInterval = 0,
  onChartTypeChange = null,
  onExport = null,
  onDataPointClick = null,
  onAnalyticsEvent = null,
  customTooltip = null,
  className = '',
  'data-testid': testId = 'interactive-chart',
  ...props
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    usage,
    featureLimits,
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    currentChartType: chartType,
    loading: false,
    settingsAnchor: null,
    hoveredData: null,
    selectedData: null,
    zoomDomain: null,
    filterSettings: {
      dateRange: null,
      valueRange: null,
      categories: [],
      showOutliers: true
    },
    visualizationSettings: {
      showGrid: showGrid,
      showLegend: showLegend,
      showTooltip: showTooltip,
      animationEnabled: true,
      colorScheme: 'default',
      opacity: 1,
      strokeWidth: 2
    },
    analyticsData: {
      views: 0,
      interactions: 0,
      exports: 0,
      chartTypeChanges: 0,
      dataPointClicks: 0
    },
    chartInsights: {
      trend: null,
      correlation: null,
      outliers: [],
      patterns: []
    },
    showUpgradeDialog: false,
    showExportDialog: false,
    showSettingsDialog: false,
    isFullscreen: false,
    errors: {}
  });

  // Refs for enhanced functionality
  const chartRef = useRef(null);
  const containerRef = useRef(null);

  // ACE Social brand colors
  const aceColors = useMemo(() => ({
    primary: '#4E40C5',
    dark: '#15110E',
    yellow: '#EBAE1B',
    white: '#FFFFFF'
  }), []);

  /**
   * Enhanced plan-based chart analytics validation - Production Ready
   */
  const validateChartAnalytics = useCallback(() => {
    if (!usage || !featureLimits || !subscription) {
      return {
        canUseAdvancedCharts: false,
        hasAnalyticsAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { monthly: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based chart analytics limits
    const planLimits = {
      creator: {
        monthly: 50,
        features: ['basic_chart_types'],
        maxDataPoints: 100,
        chartTypes: [CHART_TYPES.LINE, CHART_TYPES.BAR, CHART_TYPES.PIE],
        interactiveFeatures: false,
        realTimeUpdates: false,
        customVisualization: false,
        exportFormats: ['png'],
        animationDuration: 500
      },
      accelerator: {
        monthly: 300,
        features: ['basic_chart_types', 'advanced_chart_types', 'interactive_features'],
        maxDataPoints: 1000,
        chartTypes: [CHART_TYPES.LINE, CHART_TYPES.BAR, CHART_TYPES.PIE, CHART_TYPES.AREA, CHART_TYPES.SCATTER],
        interactiveFeatures: true,
        realTimeUpdates: true,
        customVisualization: false,
        exportFormats: ['png', 'svg', 'pdf'],
        animationDuration: 1000
      },
      dominator: {
        monthly: Infinity,
        features: ['basic_chart_types', 'advanced_chart_types', 'interactive_features', 'custom_visualizations'],
        maxDataPoints: Infinity,
        chartTypes: Object.values(CHART_TYPES),
        interactiveFeatures: true,
        realTimeUpdates: true,
        customVisualization: true,
        exportFormats: ['png', 'svg', 'pdf', 'json', 'csv'],
        animationDuration: 2000
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    if (isUnlimited) {
      return {
        canUseAdvancedCharts: true,
        hasAnalyticsAvailable: true,
        remaining: Infinity,
        total: Infinity,
        used: 0,
        isUnlimited: true,
        status: 'unlimited',
        planLimits: currentPlanLimits,
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    // Get current chart analytics usage
    const analyticsUsed = usage.chart_analytics_used || 0;
    const analyticsLimit = currentPlanLimits.monthly;
    const remaining = Math.max(0, analyticsLimit - analyticsUsed);
    const hasAnalyticsAvailable = remaining > 0;
    const canUseAdvancedCharts = hasAnalyticsAvailable && currentPlanLimits.interactiveFeatures;

    // Calculate status based on usage
    let status = 'excellent';
    const percentage = analyticsLimit > 0 ? (analyticsUsed / analyticsLimit) * 100 : 0;
    if (percentage >= 95) status = 'critical';
    else if (percentage >= 85) status = 'warning';
    else if (percentage >= 70) status = 'caution';
    else if (percentage >= 50) status = 'moderate';
    else if (percentage >= 25) status = 'good';

    // Detect mid-cycle depletion
    const depletionInfo = detectPlanDepletion({
      used: analyticsUsed,
      total: analyticsLimit,
      remaining,
      percentage,
      status
    });

    return {
      canUseAdvancedCharts,
      hasAnalyticsAvailable,
      remaining,
      total: analyticsLimit,
      used: analyticsUsed,
      isUnlimited: false,
      status,
      percentage,
      planLimits: currentPlanLimits,
      depletionInfo
    };
  }, [usage, featureLimits, subscription, detectPlanDepletion]);

  /**
   * Detect plan limit depletion before monthly cycle ends - Production Ready
   */
  const detectPlanDepletion = useCallback((planInfo) => {
    if (!planInfo || !subscription) return { isDepletedMidCycle: false, daysRemaining: 0 };

    const now = new Date();
    const billingCycleStart = new Date(subscription.current_period_start * 1000);
    const billingCycleEnd = new Date(subscription.current_period_end * 1000);

    const totalCycleDays = Math.ceil((billingCycleEnd - billingCycleStart) / (1000 * 60 * 60 * 24));
    const daysElapsed = Math.ceil((now - billingCycleStart) / (1000 * 60 * 60 * 24));
    const daysRemaining = Math.max(0, totalCycleDays - daysElapsed);

    // Consider depleted if no analytics remaining with more than 3 days left in cycle
    const isDepletedMidCycle = planInfo.remaining === 0 && daysRemaining > 3;

    return {
      isDepletedMidCycle,
      daysRemaining,
      cycleProgress: (daysElapsed / totalCycleDays) * 100
    };
  }, [subscription]);

  /**
   * Enhanced feature availability check - Production Ready
   */
  const isFeatureAvailable = useCallback((feature) => {
    const analyticsLimits = validateChartAnalytics();
    return analyticsLimits.planLimits.features.includes(feature);
  }, [validateChartAnalytics]);

  /**
   * Enhanced chart type availability check - Production Ready
   */
  const isChartTypeAvailable = useCallback((type) => {
    const analyticsLimits = validateChartAnalytics();
    return analyticsLimits.planLimits.chartTypes.includes(type);
  }, [validateChartAnalytics]);

  // Enhanced default colors based on ACE Social theme
  const defaultColors = useMemo(() => [
    aceColors.primary,
    aceColors.yellow,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
    aceColors.dark,
    theme.palette.secondary.main
  ], [aceColors, theme]);

  const chartColors = colors.length > 0 ? colors : defaultColors;

  /**
   * Enhanced action handlers - Production Ready
   */
  const handleChartTypeChange = useCallback(async (event, newType) => {
    if (newType !== null && isChartTypeAvailable(newType)) {
      setState(prev => ({
        ...prev,
        currentChartType: newType,
        analyticsData: {
          ...prev.analyticsData,
          chartTypeChanges: prev.analyticsData.chartTypeChanges + 1
        }
      }));

      if (onChartTypeChange) {
        await onChartTypeChange(newType);
      }

      if (enableAnalytics && onAnalyticsEvent) {
        onAnalyticsEvent('chart_type_change', {
          oldType: state.currentChartType,
          newType,
          timestamp: new Date().toISOString()
        });
      }

      showSuccessNotification(`Chart type changed to ${newType}`);
    } else if (!isChartTypeAvailable(newType)) {
      showErrorNotification(`${newType} chart type not available in your plan`);
    }
  }, [state.currentChartType, isChartTypeAvailable, onChartTypeChange, enableAnalytics, onAnalyticsEvent, showSuccessNotification, showErrorNotification]);

  const handleExport = useCallback(async () => {
    const analyticsLimits = validateChartAnalytics();

    if (!analyticsLimits.hasAnalyticsAvailable) {
      showErrorNotification('Export limit reached for your plan');
      return;
    }

    setState(prev => ({
      ...prev,
      analyticsData: {
        ...prev.analyticsData,
        exports: prev.analyticsData.exports + 1
      }
    }));

    try {
      if (onExport) {
        await onExport({
          chartType: state.currentChartType,
          data,
          settings: state.visualizationSettings
        });
      }

      if (enableAnalytics && onAnalyticsEvent) {
        onAnalyticsEvent('chart_export', {
          chartType: state.currentChartType,
          dataPoints: data.length,
          format: 'png'
        });
      }

      showSuccessNotification('Chart exported successfully');
    } catch (error) {
      console.error('Error exporting chart:', error);
      showErrorNotification('Failed to export chart');
    }
  }, [validateChartAnalytics, onExport, state.currentChartType, data, state.visualizationSettings, enableAnalytics, onAnalyticsEvent, showSuccessNotification, showErrorNotification]);

  const handleDataPointClick = useCallback((data, index) => {
    setState(prev => ({
      ...prev,
      selectedData: { data, index },
      analyticsData: {
        ...prev.analyticsData,
        dataPointClicks: prev.analyticsData.dataPointClicks + 1
      }
    }));

    if (onDataPointClick) {
      onDataPointClick(data, index);
    }

    if (enableAnalytics && onAnalyticsEvent) {
      onAnalyticsEvent('chart_data_point_click', {
        chartType: state.currentChartType,
        dataPoint: data,
        index
      });
    }
  }, [state.currentChartType, onDataPointClick, enableAnalytics, onAnalyticsEvent]);

  const handlePlanUpgrade = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: true }));

    if (enableAnalytics && onAnalyticsEvent) {
      onAnalyticsEvent('chart_upgrade_click', {
        currentPlan: subscription?.plan_id || 'creator',
        chartType: state.currentChartType
      });
    }
  }, [enableAnalytics, onAnalyticsEvent, subscription, state.currentChartType]);

  const closeUpgradeDialog = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced effects for analytics and real-time updates - Production Ready
   */
  useEffect(() => {
    if (enableAnalytics) {
      setState(prev => ({
        ...prev,
        analyticsData: {
          ...prev.analyticsData,
          views: prev.analyticsData.views + 1
        }
      }));

      if (onAnalyticsEvent) {
        onAnalyticsEvent('chart_view', {
          chartType: state.currentChartType,
          variant
        });
      }
    }
  }, [enableAnalytics, onAnalyticsEvent, state.currentChartType, variant]);

  /**
   * Real-time data updates effect - Production Ready
   */
  useEffect(() => {
    if (enableRealTime && refreshInterval > 0) {
      const interval = setInterval(() => {
        // Trigger data refresh if callback provided
        if (onAnalyticsEvent) {
          onAnalyticsEvent('chart_auto_refresh', {
            chartType: state.currentChartType,
            interval: refreshInterval
          });
        }
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [enableRealTime, refreshInterval, onAnalyticsEvent, state.currentChartType]);

  /**
   * Expose component methods via ref - Production Ready
   */
  useImperativeHandle(ref, () => ({
    changeChartType: handleChartTypeChange,
    exportChart: handleExport,
    upgradePlan: handlePlanUpgrade,
    getAnalyticsData: () => state.analyticsData,
    getAnalyticsLimits: () => validateChartAnalytics(),
    focus: () => chartRef.current?.focus(),
    getElement: () => containerRef.current
  }), [
    handleChartTypeChange,
    handleExport,
    handlePlanUpgrade,
    state.analyticsData,
    validateChartAnalytics
  ]);

  // Memoized calculations
  const analyticsLimits = useMemo(() => validateChartAnalytics(), [validateChartAnalytics]);

  /**
   * Enhanced custom tooltip component - Production Ready
   */
  const CustomTooltip = useCallback(({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper
          elevation={8}
          sx={{
            background: `linear-gradient(135deg,
              ${alpha(aceColors.white, 0.95)} 0%,
              ${alpha(aceColors.white, 0.85)} 100%)`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha(aceColors.primary, 0.2)}`,
            borderRadius: 2,
            p: 2,
            maxWidth: 300
          }}
        >
          <Typography variant="subtitle2" sx={{ mb: 1, color: aceColors.dark, fontWeight: 600 }}>
            {label}
          </Typography>
          {payload.map((entry, index) => (
            <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1.5, mb: 0.5 }}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  backgroundColor: entry.color,
                  borderRadius: '50%',
                  border: `2px solid ${alpha(entry.color, 0.3)}`
                }}
              />
              <Typography variant="body2" sx={{ color: aceColors.dark }}>
                <strong>{entry.name}:</strong> {typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value}
              </Typography>
            </Box>
          ))}
          {/* Enhanced tooltip with additional insights */}
          {isFeatureAvailable('advanced_chart_types') && payload.length > 1 && (
            <Divider sx={{ my: 1 }} />
          )}
          {isFeatureAvailable('advanced_chart_types') && payload.length > 1 && (
            <Typography variant="caption" color="text.secondary">
              Total: {payload.reduce((sum, entry) => sum + (typeof entry.value === 'number' ? entry.value : 0), 0).toLocaleString()}
            </Typography>
          )}
        </Paper>
      );
    }
    return null;
  }, [aceColors, isFeatureAvailable]);

  /**
   * Render upgrade prompt for unavailable features - Production Ready
   */
  const renderUpgradePrompt = useCallback((message) => {
    return (
      <Box
        sx={{
          height,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center',
          p: 3,
          bgcolor: alpha(aceColors.yellow, 0.05),
          border: `1px solid ${alpha(aceColors.yellow, 0.2)}`,
          borderRadius: 2
        }}
      >
        <Avatar
          sx={{
            width: 56,
            height: 56,
            bgcolor: alpha(aceColors.yellow, 0.1),
            color: aceColors.dark,
            mb: 2
          }}
        >
          <UpgradeIcon sx={{ fontSize: 28 }} />
        </Avatar>
        <Typography variant="h6" sx={{ color: aceColors.dark, mb: 1, fontWeight: 600 }}>
          Feature Not Available
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {message}
        </Typography>
        {enablePlanUpgrade && (
          <Button
            variant="contained"
            size="small"
            startIcon={<UpgradeIcon />}
            onClick={handlePlanUpgrade}
            sx={{
              bgcolor: aceColors.yellow,
              color: aceColors.dark,
              '&:hover': { bgcolor: '#d97706' }
            }}
          >
            Upgrade Plan
          </Button>
        )}
      </Box>
    );
  }, [height, aceColors, enablePlanUpgrade, handlePlanUpgrade]);

  /**
   * Enhanced chart rendering with plan-based features - Production Ready
   */
  const renderChart = useCallback(() => {
    if (loading || state.loading || subscriptionLoading) {
      return (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height,
          flexDirection: 'column',
          gap: 2
        }}>
          <CircularProgress sx={{ color: aceColors.primary }} />
          <Typography variant="body2" sx={{ color: aceColors.dark }}>
            Loading chart data...
          </Typography>
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ m: 2, height: height - 32, display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2">
            Failed to load chart data: {error}
          </Typography>
        </Alert>
      );
    }

    if (!data || data.length === 0) {
      return (
        <Box
          sx={{
            height,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center',
            p: 3
          }}
        >
          <Avatar
            sx={{
              width: 64,
              height: 64,
              bgcolor: alpha(aceColors.primary, 0.1),
              color: aceColors.primary,
              mb: 2
            }}
          >
            <BarChartIcon sx={{ fontSize: 32 }} />
          </Avatar>
          <Typography variant="h6" sx={{ color: aceColors.dark, mb: 1, fontWeight: 600 }}>
            No Chart Data Available
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Chart will appear when data is provided
          </Typography>
          {enablePlanUpgrade && !analyticsLimits.isUnlimited && (
            <Button
              variant="outlined"
              size="small"
              startIcon={<UpgradeIcon />}
              onClick={handlePlanUpgrade}
              sx={{
                borderColor: aceColors.yellow,
                color: aceColors.dark,
                '&:hover': {
                  borderColor: '#d97706',
                  bgcolor: alpha(aceColors.yellow, 0.05)
                }
              }}
            >
              Upgrade for Advanced Charts
            </Button>
          )}
        </Box>
      );
    }

    // Apply plan-based data limitations
    const maxDataPoints = analyticsLimits.planLimits.maxDataPoints;
    const limitedData = maxDataPoints < Infinity ? data.slice(0, maxDataPoints) : data;

    const commonProps = {
      data: limitedData,
      margin: { top: 20, right: 30, left: 20, bottom: 20 },
      onClick: enableInteractivity ? handleDataPointClick : undefined
    };

    const animationProps = {
      animationDuration: Math.min(animationDuration, analyticsLimits.planLimits.animationDuration)
    };

    switch (state.currentChartType) {
      case CHART_TYPES.AREA:
        if (!isChartTypeAvailable(CHART_TYPES.AREA)) {
          return renderUpgradePrompt('Area charts are not available in your plan');
        }
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart {...commonProps}>
              {state.visualizationSettings.showGrid && (
                <CartesianGrid strokeDasharray="3 3" stroke={alpha(aceColors.primary, 0.2)} />
              )}
              <XAxis
                dataKey={xAxisKey}
                stroke={aceColors.dark}
                fontSize={12}
                tick={{ fill: aceColors.dark }}
              />
              <YAxis
                stroke={aceColors.dark}
                fontSize={12}
                tick={{ fill: aceColors.dark }}
              />
              {state.visualizationSettings.showTooltip && (
                <RechartsTooltip content={customTooltip || <CustomTooltip />} />
              )}
              {state.visualizationSettings.showLegend && <Legend />}
              {enableBrush && isFeatureAvailable('interactive_features') && (
                <Brush dataKey={xAxisKey} height={30} stroke={aceColors.primary} />
              )}
              {dataKeys.map((key, index) => (
                <Area
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={chartColors[index % chartColors.length]}
                  fill={alpha(chartColors[index % chartColors.length], 0.3)}
                  strokeWidth={state.visualizationSettings.strokeWidth}
                  {...animationProps}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );

      case CHART_TYPES.BAR:
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart {...commonProps}>
              {state.visualizationSettings.showGrid && (
                <CartesianGrid strokeDasharray="3 3" stroke={alpha(aceColors.primary, 0.2)} />
              )}
              <XAxis
                dataKey={xAxisKey}
                stroke={aceColors.dark}
                fontSize={12}
                tick={{ fill: aceColors.dark }}
              />
              <YAxis
                stroke={aceColors.dark}
                fontSize={12}
                tick={{ fill: aceColors.dark }}
              />
              {state.visualizationSettings.showTooltip && (
                <RechartsTooltip content={customTooltip || <CustomTooltip />} />
              )}
              {state.visualizationSettings.showLegend && <Legend />}
              {enableBrush && isFeatureAvailable('interactive_features') && (
                <Brush dataKey={xAxisKey} height={30} stroke={aceColors.primary} />
              )}
              {dataKeys.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={chartColors[index % chartColors.length]}
                  radius={[4, 4, 0, 0]}
                  {...animationProps}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );

      case CHART_TYPES.PIE:
        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={limitedData}
                cx="50%"
                cy="50%"
                outerRadius={Math.min(height * 0.35, 120)}
                innerRadius={isFeatureAvailable('advanced_chart_types') ? Math.min(height * 0.15, 40) : 0}
                dataKey={yAxisKey}
                {...animationProps}
              >
                {limitedData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={chartColors[index % chartColors.length]}
                    stroke={alpha(chartColors[index % chartColors.length], 0.8)}
                    strokeWidth={2}
                  />
                ))}
              </Pie>
              {state.visualizationSettings.showTooltip && (
                <RechartsTooltip content={customTooltip || <CustomTooltip />} />
              )}
              {state.visualizationSettings.showLegend && <Legend />}
            </PieChart>
          </ResponsiveContainer>
        );

      case CHART_TYPES.SCATTER:
        if (!isChartTypeAvailable(CHART_TYPES.SCATTER)) {
          return renderUpgradePrompt('Scatter charts are not available in your plan');
        }
        return (
          <ResponsiveContainer width="100%" height={height}>
            <ScatterChart {...commonProps}>
              {state.visualizationSettings.showGrid && (
                <CartesianGrid strokeDasharray="3 3" stroke={alpha(aceColors.primary, 0.2)} />
              )}
              <XAxis
                dataKey={xAxisKey}
                stroke={aceColors.dark}
                fontSize={12}
                tick={{ fill: aceColors.dark }}
              />
              <YAxis
                stroke={aceColors.dark}
                fontSize={12}
                tick={{ fill: aceColors.dark }}
              />
              {state.visualizationSettings.showTooltip && (
                <RechartsTooltip content={customTooltip || <CustomTooltip />} />
              )}
              {state.visualizationSettings.showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Scatter
                  key={key}
                  dataKey={key}
                  fill={chartColors[index % chartColors.length]}
                  {...animationProps}
                />
              ))}
            </ScatterChart>
          </ResponsiveContainer>
        );

      default: // line
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart {...commonProps}>
              {state.visualizationSettings.showGrid && (
                <CartesianGrid strokeDasharray="3 3" stroke={alpha(aceColors.primary, 0.2)} />
              )}
              <XAxis
                dataKey={xAxisKey}
                stroke={aceColors.dark}
                fontSize={12}
                tick={{ fill: aceColors.dark }}
              />
              <YAxis
                stroke={aceColors.dark}
                fontSize={12}
                tick={{ fill: aceColors.dark }}
              />
              {state.visualizationSettings.showTooltip && (
                <RechartsTooltip content={customTooltip || <CustomTooltip />} />
              )}
              {state.visualizationSettings.showLegend && <Legend />}
              {enableBrush && isFeatureAvailable('interactive_features') && (
                <Brush dataKey={xAxisKey} height={30} stroke={aceColors.primary} />
              )}
              {dataKeys.map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={chartColors[index % chartColors.length]}
                  strokeWidth={state.visualizationSettings.strokeWidth}
                  dot={{
                    fill: chartColors[index % chartColors.length],
                    strokeWidth: 2,
                    r: 4
                  }}
                  activeDot={{
                    r: 6,
                    stroke: chartColors[index % chartColors.length],
                    strokeWidth: 2,
                    fill: aceColors.white
                  }}
                  {...animationProps}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );
    }
  }, [
    state.currentChartType,
    state.loading,
    state.visualizationSettings,
    loading,
    subscriptionLoading,
    error,
    data,
    height,
    aceColors,
    analyticsLimits,
    enablePlanUpgrade,
    handlePlanUpgrade,
    enableInteractivity,
    handleDataPointClick,
    animationDuration,
    isChartTypeAvailable,
    renderUpgradePrompt,
    isFeatureAvailable,
    enableBrush,
    xAxisKey,
    yAxisKey,
    dataKeys,
    chartColors,
    customTooltip,
    CustomTooltip
  ]);

  return (
    <ErrorBoundary
      fallback={
        <Alert severity="error" sx={{ minHeight, display: 'flex', alignItems: 'center' }}>
          <Typography variant="body2">
            Chart unavailable
          </Typography>
        </Alert>
      }
    >
      <Zoom in={true} timeout={300}>
        <Card
          ref={containerRef}
          className={className}
          data-testid={testId}
          sx={{
            width: '100%',
            height: '100%',
            minHeight,
            maxHeight: variant === 'compact' ? minHeight : maxHeight,
            position: 'relative',
            background: `linear-gradient(135deg,
              ${alpha(aceColors.white, 0.95)} 0%,
              ${alpha(aceColors.white, 0.85)} 100%)`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha(aceColors.primary, 0.1)}`,
            borderRadius: 3,
            boxShadow: `0 8px 32px ${alpha(aceColors.primary, 0.1)}`,
            overflow: 'hidden'
          }}
          {...props}
        >
          {/* Enhanced Header */}
          {showHeader && (title || subtitle || showControls) && (
            <CardContent sx={{
              p: 3,
              pb: 1,
              background: `linear-gradient(135deg,
                ${alpha(aceColors.primary, 0.05)} 0%,
                ${alpha(aceColors.primary, 0.02)} 100%)`,
              borderBottom: `1px solid ${alpha(aceColors.primary, 0.1)}`
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: alpha(aceColors.primary, 0.1),
                      color: aceColors.primary,
                      width: 40,
                      height: 40
                    }}
                  >
                    <BarChartIcon />
                  </Avatar>
                  <Box>
                    {title && (
                      <Typography variant="h6" sx={{ fontWeight: 700, color: aceColors.dark }}>
                        {title}
                      </Typography>
                    )}
                    {subtitle && (
                      <Typography variant="caption" color="text.secondary">
                        {subtitle}
                      </Typography>
                    )}
                  </Box>
                </Box>

                {/* Enhanced Controls */}
                {showControls && (
                  <Stack direction="row" spacing={1} alignItems="center">
                    {/* Chart Type Toggle */}
                    <ToggleButtonGroup
                      value={state.currentChartType}
                      exclusive
                      onChange={handleChartTypeChange}
                      size="small"
                      sx={{
                        '& .MuiToggleButton-root': {
                          border: `1px solid ${alpha(aceColors.primary, 0.3)}`,
                          color: aceColors.dark,
                          '&.Mui-selected': {
                            backgroundColor: alpha(aceColors.primary, 0.1),
                            color: aceColors.primary,
                            borderColor: aceColors.primary
                          },
                          '&:hover': {
                            backgroundColor: alpha(aceColors.primary, 0.05)
                          }
                        }
                      }}
                    >
                      <ToggleButton value={CHART_TYPES.LINE} aria-label="line chart">
                        <Tooltip title="Line Chart">
                          <LineChartIcon fontSize="small" />
                        </Tooltip>
                      </ToggleButton>
                      <ToggleButton value={CHART_TYPES.BAR} aria-label="bar chart">
                        <Tooltip title="Bar Chart">
                          <BarChartIcon fontSize="small" />
                        </Tooltip>
                      </ToggleButton>
                      <ToggleButton value={CHART_TYPES.PIE} aria-label="pie chart">
                        <Tooltip title="Pie Chart">
                          <PieChartIcon fontSize="small" />
                        </Tooltip>
                      </ToggleButton>
                      {isChartTypeAvailable(CHART_TYPES.AREA) && (
                        <ToggleButton value={CHART_TYPES.AREA} aria-label="area chart">
                          <Tooltip title="Area Chart">
                            <TrendingUpIcon fontSize="small" />
                          </Tooltip>
                        </ToggleButton>
                      )}
                      {isChartTypeAvailable(CHART_TYPES.SCATTER) && (
                        <ToggleButton value={CHART_TYPES.SCATTER} aria-label="scatter chart">
                          <Tooltip title="Scatter Chart">
                            <ScatterIcon fontSize="small" />
                          </Tooltip>
                        </ToggleButton>
                      )}
                    </ToggleButtonGroup>

                    {/* Action Buttons */}
                    {enableExport && analyticsLimits.hasAnalyticsAvailable && (
                      <Tooltip title="Export chart">
                        <IconButton
                          size="small"
                          onClick={handleExport}
                          sx={{ color: aceColors.primary }}
                        >
                          <DownloadIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}

                    <Tooltip title="Chart settings">
                      <IconButton
                        size="small"
                        onClick={(e) => setState(prev => ({ ...prev, settingsAnchor: e.currentTarget }))}
                        sx={{ color: aceColors.primary }}
                      >
                        <SettingsIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>

                    {!analyticsLimits.isUnlimited && enablePlanUpgrade && (
                      <Tooltip title="Upgrade plan for more features">
                        <IconButton
                          size="small"
                          onClick={handlePlanUpgrade}
                          sx={{ color: aceColors.yellow }}
                        >
                          <UpgradeIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Stack>
                )}
              </Box>

              {/* Plan Status Indicator */}
              {!analyticsLimits.isUnlimited && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                  <Chip
                    size="small"
                    label={`${analyticsLimits.remaining} of ${analyticsLimits.total} charts remaining`}
                    color={analyticsLimits.status === 'critical' ? 'error' :
                           analyticsLimits.status === 'warning' ? 'warning' : 'primary'}
                    variant="outlined"
                  />
                  {analyticsLimits.planLimits.maxDataPoints < Infinity && (
                    <Chip
                      size="small"
                      label={`Max ${analyticsLimits.planLimits.maxDataPoints} data points`}
                      variant="outlined"
                      sx={{ borderColor: aceColors.yellow, color: aceColors.dark }}
                    />
                  )}
                </Box>
              )}
            </CardContent>
          )}

          {/* Chart Content */}
          <CardContent sx={{
            p: 3,
            height: showHeader ? `calc(100% - 140px)` : '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <Box ref={chartRef} sx={{ flexGrow: 1, minHeight: 200 }}>
              {renderChart()}
            </Box>
          </CardContent>

          {/* Settings Menu */}
          <Menu
            anchorEl={state.settingsAnchor}
            open={Boolean(state.settingsAnchor)}
            onClose={() => setState(prev => ({ ...prev, settingsAnchor: null }))}
            PaperProps={{
              sx: {
                background: `linear-gradient(135deg,
                  ${alpha(aceColors.white, 0.95)} 0%,
                  ${alpha(aceColors.white, 0.85)} 100%)`,
                backdropFilter: 'blur(20px)',
                border: `1px solid ${alpha(aceColors.primary, 0.2)}`
              }
            }}
          >
            <MenuItem onClick={() => {
              setState(prev => ({
                ...prev,
                visualizationSettings: {
                  ...prev.visualizationSettings,
                  showGrid: !prev.visualizationSettings.showGrid
                },
                settingsAnchor: null
              }));
            }}>
              {state.visualizationSettings.showGrid ? 'Hide' : 'Show'} Grid
            </MenuItem>
            <MenuItem onClick={() => {
              setState(prev => ({
                ...prev,
                visualizationSettings: {
                  ...prev.visualizationSettings,
                  showLegend: !prev.visualizationSettings.showLegend
                },
                settingsAnchor: null
              }));
            }}>
              {state.visualizationSettings.showLegend ? 'Hide' : 'Show'} Legend
            </MenuItem>
            <MenuItem onClick={() => {
              setState(prev => ({
                ...prev,
                visualizationSettings: {
                  ...prev.visualizationSettings,
                  animationEnabled: !prev.visualizationSettings.animationEnabled
                },
                settingsAnchor: null
              }));
            }}>
              {state.visualizationSettings.animationEnabled ? 'Disable' : 'Enable'} Animations
            </MenuItem>
          </Menu>

          {/* Upgrade Dialog */}
          <Dialog
            open={state.showUpgradeDialog}
            onClose={closeUpgradeDialog}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              <Typography variant="h6" sx={{ fontWeight: 600, color: aceColors.dark }}>
                Upgrade Your Plan
              </Typography>
            </DialogTitle>
            <DialogContent>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Unlock advanced chart features with a higher tier plan:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="body2">• Advanced chart types (scatter, radar, heatmap)</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2">• Interactive features and real-time updates</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2">• Unlimited data points and customization</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2">• Multiple export formats</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2">• Advanced analytics and insights</Typography>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={closeUpgradeDialog}>
                Cancel
              </Button>
              <Button
                variant="contained"
                sx={{ bgcolor: aceColors.primary, '&:hover': { bgcolor: '#3d2f9f' } }}
                onClick={() => {
                  closeUpgradeDialog();
                  // Handle upgrade logic
                }}
              >
                Upgrade Now
              </Button>
            </DialogActions>
          </Dialog>
        </Card>
      </Zoom>
    </ErrorBoundary>
  );
}));

/**
 * Comprehensive PropTypes validation - Production Ready
 */
InteractiveChart.propTypes = {
  /** Chart data array */
  data: PropTypes.array,

  /** Chart title */
  title: PropTypes.string,

  /** Chart subtitle */
  subtitle: PropTypes.string,

  /** Chart type */
  chartType: PropTypes.oneOf(Object.values(CHART_TYPES)),

  /** Visual variant of the component */
  variant: PropTypes.oneOf(['default', 'compact', 'detailed']),

  /** Chart height */
  height: PropTypes.number,

  /** Minimum height of the component */
  minHeight: PropTypes.number,

  /** Maximum height of the component */
  maxHeight: PropTypes.number,

  /** Loading state */
  loading: PropTypes.bool,

  /** Error state */
  error: PropTypes.string,

  /** Enable analytics functionality */
  enableAnalytics: PropTypes.bool,

  /** Enable interactivity functionality */
  enableInteractivity: PropTypes.bool,

  /** Enable export functionality */
  enableExport: PropTypes.bool,

  /** Enable plan upgrade functionality */
  enablePlanUpgrade: PropTypes.bool,

  /** Whether to show controls */
  showControls: PropTypes.bool,

  /** Whether to show legend */
  showLegend: PropTypes.bool,

  /** Whether to show grid */
  showGrid: PropTypes.bool,

  /** Whether to show header */
  showHeader: PropTypes.bool,

  /** Chart colors array */
  colors: PropTypes.array,

  /** Data keys for chart series */
  dataKeys: PropTypes.array,

  /** X-axis data key */
  xAxisKey: PropTypes.string,

  /** Y-axis data key */
  yAxisKey: PropTypes.string,

  /** Animation duration */
  animationDuration: PropTypes.number,

  /** Callback when chart type changes */
  onChartTypeChange: PropTypes.func,

  /** Callback when export is triggered */
  onExport: PropTypes.func,

  /** Callback when analytics event occurs */
  onAnalyticsEvent: PropTypes.func,

  /** Custom tooltip component */
  customTooltip: PropTypes.node,

  /** Additional CSS class name */
  className: PropTypes.string,

  /** Test ID for testing */
  'data-testid': PropTypes.string
};

/**
 * Default props with enterprise-grade defaults - Production Ready
 */
InteractiveChart.defaultProps = {
  data: [],
  title: 'Interactive Chart',
  subtitle: 'Data visualization',
  chartType: CHART_TYPES.LINE,
  variant: 'default',
  height: 400,
  minHeight: 300,
  maxHeight: 800,
  loading: false,
  error: null,
  enableAnalytics: true,
  enableInteractivity: true,
  enableExport: true,
  enablePlanUpgrade: true,
  showControls: true,
  showLegend: true,
  showGrid: true,
  showHeader: true,
  colors: [],
  dataKeys: [],
  xAxisKey: 'name',
  yAxisKey: 'value',
  animationDuration: 1000,
  onChartTypeChange: null,
  onExport: null,
  onAnalyticsEvent: null,
  customTooltip: null,
  className: '',
  'data-testid': 'interactive-chart'
};

/**
 * Display name for debugging - Production Ready
 */
InteractiveChart.displayName = 'InteractiveChart';

export default InteractiveChart;

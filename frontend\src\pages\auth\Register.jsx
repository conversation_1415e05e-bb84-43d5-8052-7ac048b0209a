// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from "react";
import { Link as RouterLink, useNavigate, useSearchParams } from "react-router-dom";
import {
  Box,
  Button,
  TextField,
  Typography,
  Link,
  InputAdornment,
  IconButton,
  CircularProgress,
  Divider,
  Grid,
  Alert,
  Card,
  CardContent,
  Fade,
  Slide,
  useTheme,
  alpha,
  LinearProgress,
  Chip,
  Avatar,
} from "@mui/material";
import {
  Visibility,
  VisibilityOff,
  HowToReg,
  Person,
  Email,
  Lock,
  Security,
  Group,
  CheckCircle,
} from "@mui/icons-material";
import { useAuth } from "../../hooks/useAuth";
import { useNotification } from "../../hooks/useNotification";
import Logo from "../../components/common/Logo";
import api from "../../api";

const Register = () => {
  const navigate = useNavigate();
  const { register } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const theme = useTheme();
  const [searchParams] = useSearchParams();

  // Get invitation token from URL
  const invitationToken = searchParams.get('invitation');

  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    fullName: "",
    companyName: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [mounted, setMounted] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  // Invitation-related state
  const [invitationData, setInvitationData] = useState(null);
  const [invitationLoading, setInvitationLoading] = useState(false);
  const [invitationError, setInvitationError] = useState(null);

  // Animation effect
  useEffect(() => {
    setMounted(true);
  }, []);

  // Verify invitation token if present
  useEffect(() => {
    const verifyInvitation = async () => {
      if (!invitationToken) return;

      setInvitationLoading(true);
      setInvitationError(null);

      try {
        const response = await api.get(`/api/teams/invitations/verify/${invitationToken}`);

        if (response.data && response.data.valid) {
          setInvitationData(response.data);

          // Pre-fill email from invitation
          setFormData(prev => ({
            ...prev,
            email: response.data.email
          }));

          showSuccessNotification(`You're invited to join ${response.data.team_name}!`);
        } else {
          setInvitationError('Invalid invitation link');
        }
      } catch (error) {
        console.error('Error verifying invitation:', error);
        setInvitationError(
          error.response?.data?.detail ||
          'Invalid or expired invitation link'
        );
      } finally {
        setInvitationLoading(false);
      }
    };

    verifyInvitation();
  }, [invitationToken, showSuccessNotification]);

  // Password strength checker
  const calculatePasswordStrength = (password) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    return strength;
  };

  const getPasswordStrengthColor = (strength) => {
    if (strength < 50) return theme.palette.error.main;
    if (strength < 75) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getPasswordStrengthText = (strength) => {
    if (strength < 25) return "Very Weak";
    if (strength < 50) return "Weak";
    if (strength < 75) return "Good";
    return "Strong";
  };
  const [generalError, setGeneralError] = useState("");
  const [referralCode, setReferralCode] = useState("");
  const [referrerId, setReferrerId] = useState("");

  // Check for referral code in localStorage
  useEffect(() => {
    const storedReferralCode = localStorage.getItem("referralCode");
    const storedReferrerId = localStorage.getItem("referrerId");

    if (storedReferralCode) {
      setReferralCode(storedReferralCode);
    }

    if (storedReferrerId) {
      setReferrerId(storedReferrerId);
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Update password strength for password field
    if (name === "password") {
      setPasswordStrength(calculatePasswordStrength(value));
    }

    // Clear error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }

    // Clear general error when user makes any change
    if (generalError) {
      setGeneralError("");
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters long";
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    // Full name validation
    if (!formData.fullName) {
      newErrors.fullName = "Full name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setGeneralError("");

    try {
      const userData = {
        email: formData.email,
        password: formData.password,
        full_name: formData.fullName,
        company_name: formData.companyName || undefined,
        referral_code: referralCode || undefined,
        referrer_id: referrerId || undefined,
        invitation_token: invitationToken || undefined,
      };

      const result = await register(userData);

      if (result.success) {
        // Clear referral data from localStorage after successful registration
        localStorage.removeItem("referralCode");
        localStorage.removeItem("referrerId");

        // Check if user joined a team through invitation
        if (result.data?.team_joined) {
          const teamInfo = result.data.team_joined;
          showSuccessNotification(
            `Welcome! You've successfully registered and joined ${teamInfo.team_name} as a ${teamInfo.role}.`
          );

          // Redirect to team dashboard after a short delay
          setTimeout(() => {
            navigate(`/settings/teams/${teamInfo.team_id}`);
          }, 2000);
        } else {
          showSuccessNotification("Registration successful! You can now log in.");
          navigate("/login");
        }
      } else {
        setGeneralError(
          result.error || "Registration failed. Please try again."
        );
        showErrorNotification(
          result.error || "Registration failed. Please try again."
        );
      }
    } catch (error) {
      console.error("Registration error:", error);
      setGeneralError("An unexpected error occurred. Please try again.");
      showErrorNotification("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Fade in={mounted} timeout={800}>
      <Box
        sx={{
          width: "100%",
          position: "relative",
        }}
      >
        {/* Modern Header with Logo and Gradient */}
        <Slide direction="down" in={mounted} timeout={600}>
          <Box sx={{ mb: 4, textAlign: "center", position: "relative" }}>
            {/* Background Gradient Effect */}
            <Box
              sx={{
                position: "absolute",
                top: -20,
                left: "50%",
                transform: "translateX(-50%)",
                width: 120,
                height: 120,
                background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.main, 0.1)} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`,
                borderRadius: "50%",
                filter: "blur(40px)",
                zIndex: 0,
              }}
            />

            {/* Logo */}
            <Box sx={{ position: "relative", zIndex: 1, mb: 3 }}>
              <Logo size="large" />
            </Box>

            {/* Welcome Text */}
            <Typography
              variant="h3"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 700,
                background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.primary.main} 100%)`,
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                mb: 1,
                position: "relative",
                zIndex: 1,
              }}
            >
              Join ACE Social
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                position: "relative",
                zIndex: 1,
                maxWidth: 400,
                mx: "auto",
              }}
            >
              {invitationData
                ? `You're invited to join ${invitationData.team_name}! Complete your registration below.`
                : "Create your account and start building your social media presence"
              }
            </Typography>

            {/* Invitation Context Display */}
            {invitationData && (
              <Fade in={true}>
                <Card
                  sx={{
                    mt: 3,
                    mb: 2,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    borderRadius: 2,
                    position: "relative",
                    zIndex: 1,
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: theme.palette.primary.main,
                          mr: 2,
                          width: 48,
                          height: 48,
                        }}
                      >
                        <Group />
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                          Team Invitation
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          You're invited to join as a {invitationData.role}
                        </Typography>
                      </Box>
                      <Chip
                        icon={<CheckCircle />}
                        label="Verified"
                        color="success"
                        size="small"
                        variant="outlined"
                      />
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Team Name
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {invitationData.team_name}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Invited by
                        </Typography>
                        <Typography variant="body1" sx={{ fontWeight: 500 }}>
                          {invitationData.invited_by_name}
                        </Typography>
                      </Grid>
                      {invitationData.message && (
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Message
                          </Typography>
                          <Typography variant="body2" sx={{
                            fontStyle: 'italic',
                            p: 1.5,
                            bgcolor: alpha(theme.palette.background.default, 0.5),
                            borderRadius: 1,
                            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                          }}>
                            "{invitationData.message}"
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>
              </Fade>
            )}

            {/* Invitation Error Display */}
            {invitationError && (
              <Alert
                severity="error"
                sx={{
                  mt: 2,
                  mb: 2,
                  position: "relative",
                  zIndex: 1,
                }}
              >
                {invitationError}
              </Alert>
            )}
          </Box>
        </Slide>

        {/* Alerts */}
        {generalError && (
          <Slide direction="up" in={!!generalError} timeout={400}>
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                "& .MuiAlert-icon": {
                  fontSize: "1.5rem",
                },
              }}
            >
              {generalError}
            </Alert>
          </Slide>
        )}

        {referralCode && (
          <Slide direction="up" in={!!referralCode} timeout={600}>
            <Alert
              severity="info"
              sx={{
                mb: 3,
                borderRadius: 2,
                background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.1)} 0%, ${alpha(theme.palette.info.main, 0.05)} 100%)`,
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
              }}
            >
              You&apos;re signing up with a referral code. You&apos;ll be connected to the
              person who referred you!
            </Alert>
          </Slide>
        )}

        {/* Form Content */}
        <Slide direction="up" in={mounted} timeout={1000}>
          <Card
            sx={{
              background: theme.palette.background.paper,
              borderRadius: 3,
              boxShadow: `0 12px 40px ${alpha(theme.palette.primary.main, 0.15)}`,
              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              overflow: "hidden",
            }}
          >
            <CardContent sx={{ p: 4 }}>
              <Box component="form" onSubmit={handleSubmit} sx={{ width: "100%" }}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleChange}
                      error={!!errors.fullName}
                      helperText={errors.fullName}
                      required
                      autoFocus
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Person color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: 2,
                          minHeight: 56,
                          transition: "all 0.3s ease",
                          "&:hover": {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: alpha(theme.palette.primary.main, 0.5),
                            },
                          },
                          "&.Mui-focused": {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: theme.palette.primary.main,
                              borderWidth: 2,
                            },
                          },
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      error={!!errors.email}
                      helperText={errors.email}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Email color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: 2,
                          minHeight: 56,
                          transition: "all 0.3s ease",
                          "&:hover": {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: alpha(theme.palette.primary.main, 0.5),
                            },
                          },
                          "&.Mui-focused": {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: theme.palette.primary.main,
                              borderWidth: 2,
                            },
                          },
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Company Name (Optional)"
                      name="companyName"
                      value={formData.companyName}
                      onChange={handleChange}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Security color="action" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: 2,
                          minHeight: 56,
                          transition: "all 0.3s ease",
                          "&:hover": {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: alpha(theme.palette.primary.main, 0.5),
                            },
                          },
                          "&.Mui-focused": {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: theme.palette.primary.main,
                              borderWidth: 2,
                            },
                          },
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={handleChange}
                      error={!!errors.password}
                      helperText={errors.password}
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Lock color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={handleTogglePasswordVisibility}
                              edge="end"
                              sx={{
                                color: "action.active",
                                "&:hover": {
                                  color: theme.palette.primary.main,
                                },
                              }}
                            >
                              {showPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        "& .MuiOutlinedInput-root": {
                          borderRadius: 2,
                          minHeight: 56,
                          transition: "all 0.3s ease",
                          "&:hover": {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: alpha(theme.palette.primary.main, 0.5),
                            },
                          },
                          "&.Mui-focused": {
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: theme.palette.primary.main,
                              borderWidth: 2,
                            },
                          },
                        },
                      }}
                    />

                    {/* Password Strength Indicator */}
                    {formData.password && (
                      <Box sx={{ mt: 1 }}>
                        <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                          <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                            Password Strength:
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{
                              color: getPasswordStrengthColor(passwordStrength),
                              fontWeight: 600,
                            }}
                          >
                            {getPasswordStrengthText(passwordStrength)}
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={passwordStrength}
                          sx={{
                            height: 4,
                            borderRadius: 2,
                            backgroundColor: alpha(theme.palette.action.disabled, 0.2),
                            "& .MuiLinearProgress-bar": {
                              backgroundColor: getPasswordStrengthColor(passwordStrength),
                              borderRadius: 2,
                            },
                          }}
                        />
                      </Box>
                    )}
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Confirm Password"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      error={!!errors.confirmPassword}
                      helperText={errors.confirmPassword}
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={handleToggleConfirmPasswordVisibility}
                              edge="end"
                            >
                              {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                </Grid>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  color="primary"
                  size="large"
                  disabled={loading}
                  startIcon={<HowToReg />}
                  sx={{ mt: 3, mb: 3 }}
                >
                  {loading ? <CircularProgress size={24} /> : "Sign Up"}
                </Button>
              </Box>

              <Divider sx={{ my: 2 }}>
                <Typography variant="body2" color="textSecondary">
                  OR
                </Typography>
              </Divider>

              <Box sx={{ textAlign: "center" }}>
                <Typography variant="body2">
                  Already have an account?{" "}
                  <Link
                    component={RouterLink}
                    to="/login"
                    variant="body2"
                    underline="hover"
                    fontWeight="bold"
                  >
                    Sign In
                  </Link>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Slide>
      </Box>
    </Fade>
  );
};

export default Register;

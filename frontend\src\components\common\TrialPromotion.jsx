/**
 * TrialPromotion Component - Enterprise-grade trial promotion for ACE Social platform
 * Features: Advanced promotional patterns, intelligent conversion strategies, dynamic promotion adaptation,
 * advanced promotion controls, smart conversion optimization, adaptive promotional layouts, contextual promotion states, accessibility-focused promotional messaging, responsive promotion patterns, and production-ready trial promotion functionality
 @since 2024-1-1 to 2025-25-7
*/

import { memo, useState, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import {
  Box,
  Button,
  Typography,
  Paper,
  Grid,
  Link,
  Stack,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent,
  Avatar,
  Chip,
  Alert,
  AlertTitle,
  Snackbar,
  IconButton,
  Fade,
  Slide,
  useTheme,
  alpha,
  useMediaQuery,
  CircularProgress
} from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';
import {
  CheckCircleOutline as CheckCircleOutlineIcon,
  Close as CloseIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Timer as TimerIcon,
  Rocket as RocketIcon,
  AutoAwesome as AutoAwesomeIcon,
  Celebration as CelebrationIcon,
  LocalOffer as LocalOfferIcon,
  Favorite as FavoriteIcon
} from '@mui/icons-material';
import { useTrial } from '../../contexts/TrialContext';
import { useAuth } from '../../contexts/AuthContext';
import { useConfirmation } from '../../contexts/ConfirmationContext';

// Enhanced animations for enterprise-grade trial promotion
const pulseAnimation = keyframes`
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
`;

// Enhanced animations for enterprise-grade trial promotion (simplified for production)

// Enhanced styled components for enterprise-grade trial promotion
const StyledPromotionCard = styled(Paper)(({ theme, variant, isAnimated, urgencyLevel }) => ({
  position: 'relative',
  overflow: 'hidden',
  transition: theme.transitions.create(['all'], {
    duration: theme.transitions.duration.standard
  }),
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[8]
  },
  ...(variant === 'premium' && {
    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,
    border: `2px solid ${alpha(theme.palette.primary.main, 0.3)}`,
    boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.2)}`
  }),
  ...(variant === 'featured' && {
    background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)}, ${alpha(theme.palette.info.main, 0.1)})`,
    border: `2px solid ${alpha(theme.palette.success.main, 0.3)}`
  }),
  ...(urgencyLevel === 'high' && {
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '3px',
      background: `linear-gradient(90deg, ${theme.palette.error.main}, ${theme.palette.warning.main})`,
      animation: isAnimated ? `${pulseAnimation} 2s ease-in-out infinite` : 'none'
    }
  }),
  // Enhanced animations (simplified for production)
  // Enhanced accessibility
  '&:focus-visible': {
    outline: `3px solid ${theme.palette.primary.main}`,
    outlineOffset: '2px'
  },
  // Enhanced responsive design
  [theme.breakpoints.down('sm')]: {
    margin: theme.spacing(0.5),
    borderRadius: theme.spacing(1)
  },
  // Enhanced high contrast mode
  '@media (prefers-contrast: high)': {
    border: `3px solid ${theme.palette.text.primary}`,
    background: theme.palette.background.paper
  },
  // Enhanced reduced motion support
  '@media (prefers-reduced-motion: reduce)': {
    transition: 'none',
    animation: 'none',
    '&:hover': {
      transform: 'none'
    },
    '&::before': {
      animation: 'none'
    }
  }
}));

const StyledFeatureList = styled(List)(({ theme }) => ({
  '& .MuiListItem-root': {
    paddingLeft: 0,
    paddingRight: 0,
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.04),
      borderRadius: theme.spacing(1)
    }
  }
}));

const StyledCTAButton = styled(Button)(({ theme, variant }) => ({
  fontWeight: 'bold',
  textTransform: 'none',
  borderRadius: theme.spacing(1),
  boxShadow: theme.shadows[3],
  '&:hover': {
    boxShadow: theme.shadows[6],
    transform: 'translateY(-1px)'
  },
  ...(variant === 'primary' && {
    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
    '&:hover': {
      background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`
    }
  }),
  ...(variant === 'premium' && {
    background: `linear-gradient(45deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
    color: theme.palette.secondary.contrastText,
    '&:hover': {
      background: `linear-gradient(45deg, ${theme.palette.secondary.dark}, ${theme.palette.secondary.main})`
    }
  }),
  // Enhanced animations (simplified for production)
  // Enhanced accessibility
  '&:focus-visible': {
    outline: `3px solid ${theme.palette.primary.main}`,
    outlineOffset: '2px'
  },
  // Enhanced reduced motion support
  '@media (prefers-reduced-motion: reduce)': {
    animation: 'none',
    '&:hover': {
      transform: 'none'
    }
  }
}));

// Trial promotion constants and configurations
const PROMOTION_VARIANTS = {
  FULL: 'full',
  COMPACT: 'compact',
  BANNER: 'banner',
  MODAL: 'modal',
  SIDEBAR: 'sidebar'
};

const URGENCY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

const PLAN_PROMOTION_FEATURES = {
  creator: {
    trialDays: 14,
    features: [
      { title: 'Create 25 posts per month', subtitle: '5x more than Free plan' },
      { title: 'Basic analytics dashboard', subtitle: 'Track your performance' },
      { title: 'Post scheduling', subtitle: 'Plan your content ahead' },
      { title: 'Email support', subtitle: 'Get help when you need it' }
    ],
    testimonial: {
      quote: "ACE Social's Creator plan helped me organize my content strategy and grow my audience by 150% in just 2 months!",
      author: "Alex Chen",
      role: "Content Creator & Influencer"
    },
    stats: ['2x increase in engagement', '3+ hours saved per week', '25% more consistent posting'],
    upgradePrice: '$9.99/month',
    savings: '20%'
  },
  accelerator: {
    trialDays: 14,
    features: [
      { title: 'Create 100 posts per month', subtitle: '20x more than Free plan' },
      { title: 'Connect up to 5 social platforms', subtitle: 'Manage all accounts in one place' },
      { title: 'Advanced analytics & insights', subtitle: 'Make data-driven decisions' },
      { title: 'Team collaboration features', subtitle: 'Work together seamlessly' },
      { title: 'AI-powered content suggestions', subtitle: 'Never run out of ideas' },
      { title: 'Priority support', subtitle: '24/7 chat and email support' }
    ],
    testimonial: {
      quote: "The Accelerator plan's team features and advanced analytics transformed our social media strategy. We saw 300% growth in engagement!",
      author: "Sarah Johnson",
      role: "Marketing Director, TechSolutions Inc."
    },
    stats: ['5x increase in engagement', '8+ hours saved per week', '60% more consistent posting'],
    upgradePrice: '$29.99/month',
    savings: '30%'
  },
  dominator: {
    trialDays: 14,
    features: [
      { title: 'Unlimited posts per month', subtitle: 'No limits on your creativity' },
      { title: 'Connect unlimited social platforms', subtitle: 'Manage your entire presence' },
      { title: 'Enterprise analytics suite', subtitle: 'Advanced reporting & insights' },
      { title: 'White-label solutions', subtitle: 'Brand it as your own' },
      { title: 'Custom AI training', subtitle: 'Personalized content generation' },
      { title: 'Dedicated account manager', subtitle: 'Personal support specialist' }
    ],
    testimonial: {
      quote: "The Dominator plan's enterprise features helped us scale our social media operations across 50+ brands with incredible efficiency.",
      author: "Michael Rodriguez",
      role: "VP of Digital Marketing, Global Brands Corp"
    },
    stats: ['10x increase in efficiency', '20+ hours saved per week', '95+ automation rate'],
    upgradePrice: '$99.99/month',
    savings: '40%'
  }
};

const PROMOTION_ANALYTICS_EVENTS = {
  PROMOTION_VIEW: 'trial_promotion_view',
  PROMOTION_DISMISS: 'trial_promotion_dismiss',
  TRIAL_START: 'trial_promotion_start',
  FEATURE_EXPAND: 'trial_promotion_feature_expand',
  TESTIMONIAL_VIEW: 'trial_promotion_testimonial_view',
  COMPARE_PLANS: 'trial_promotion_compare_plans'
};

/**
 * Enhanced enterprise-grade trial promotion component with comprehensive promotional patterns,
 * intelligent conversion strategies, dynamic promotion adaptation, and production-ready trial promotion functionality
 */

const TrialPromotion = memo(({
  variant = PROMOTION_VARIANTS.FULL,
  targetPlan = 'accelerator',
  enableAnalytics = true,
  enableAccessibility = true,
  enableAnimations = true,
  enableDismiss = true,
  enableTestimonials = true,
  showFeatures = true,
  showStats = true,
  showUrgency = false,
  urgencyLevel = URGENCY_LEVELS.LOW,
  autoHide = false,
  autoHideDelay = 30000,
  onAnalytics,
  onTrialStart,
  onDismiss,
  testId
}) => {
  const theme = useTheme();
  const location = useLocation();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Enhanced state management
  const [isDismissed, setIsDismissed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('info');
  const [conversionMetrics, setConversionMetrics] = useState({
    views: 0,
    interactions: 0,
    conversions: 0
  });

  // Context hooks
  const { startTrial } = useTrial();
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();

  // Plan features
  const planFeatures = useMemo(() => {
    return PLAN_PROMOTION_FEATURES[targetPlan] || PLAN_PROMOTION_FEATURES.accelerator;
  }, [targetPlan]);

  // Check if user should see promotion
  const shouldShowPromotion = useMemo(() => {
    if (isDismissed) return false;

    // Don't show if user is already on a paid plan or trial
    if (user?.subscription?.plan_id !== 'creator' && user?.subscription?.plan_id !== 'free') {
      return false;
    }

    if (user?.subscription?.status === 'trialing') {
      return false;
    }

    return true;
  }, [user, isDismissed]);

  // Analytics tracking
  const trackAnalytics = useCallback((action, data = {}) => {
    if (!enableAnalytics || !onAnalytics) return;

    onAnalytics({
      component: 'TrialPromotion',
      action,
      variant,
      targetPlan,
      urgencyLevel,
      timestamp: Date.now(),
      path: location.pathname,
      isMobile,
      ...data
    });
  }, [enableAnalytics, onAnalytics, variant, targetPlan, urgencyLevel, location.pathname, isMobile]);

  // Enhanced trial start handler
  const handleStartTrial = useCallback(async () => {
    setIsLoading(true);

    try {
      // Track trial start attempt
      trackAnalytics(PROMOTION_ANALYTICS_EVENTS.TRIAL_START, {
        targetPlan,
        conversionMetrics
      });

      // Update conversion metrics
      setConversionMetrics(prev => ({
        ...prev,
        interactions: prev.interactions + 1
      }));

      const result = await showConfirmation({
        title: `Start Your ${planFeatures.trialDays}-Day Free Trial`,
        content: `Ready to experience all the premium features of the ${targetPlan.charAt(0).toUpperCase() + targetPlan.slice(1)} plan? Start your ${planFeatures.trialDays}-day free trial now with no credit card required. Save ${planFeatures.savings} on your first year!`,
        confirmText: 'Start Free Trial',
        cancelText: 'Not Now',
        confirmColor: 'primary',
        onConfirm: async () => {
          await startTrial(targetPlan);

          // Track successful conversion
          trackAnalytics('trial_conversion_success', {
            targetPlan,
            conversionTime: Date.now()
          });

          setConversionMetrics(prev => ({
            ...prev,
            conversions: prev.conversions + 1
          }));

          if (onTrialStart) {
            onTrialStart(targetPlan);
          }
        }
      });

      if (result) {
        setSnackbarMessage(`Welcome to your ${targetPlan} trial! Enjoy all premium features.`);
        setSnackbarSeverity('success');
        setSnackbarOpen(true);
      }
    } catch (trialError) {
      setError(trialError.message);
      setSnackbarMessage(`Trial start failed: ${trialError.message}`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);

      trackAnalytics('trial_start_error', {
        error: trialError.message
      });
    } finally {
      setIsLoading(false);
    }
  }, [showConfirmation, startTrial, trackAnalytics, targetPlan, planFeatures, conversionMetrics, onTrialStart]);

  // Enhanced dismiss handler
  const handleDismiss = useCallback(() => {
    setIsDismissed(true);

    trackAnalytics(PROMOTION_ANALYTICS_EVENTS.PROMOTION_DISMISS, {
      dismissTime: Date.now(),
      viewDuration: Date.now() - (conversionMetrics.views * 1000)
    });

    if (onDismiss) {
      onDismiss();
    }
  }, [trackAnalytics, conversionMetrics.views, onDismiss]);

  // Enhanced feature management (simplified for production)

  // Effects
  useEffect(() => {
    if (shouldShowPromotion) {
      // Track promotion view
      trackAnalytics(PROMOTION_ANALYTICS_EVENTS.PROMOTION_VIEW, {
        initialView: true
      });

      // Update view metrics
      setConversionMetrics(prev => ({
        ...prev,
        views: prev.views + 1
      }));
    }
  }, [shouldShowPromotion, trackAnalytics]);

  useEffect(() => {
    if (autoHide && autoHideDelay > 0 && shouldShowPromotion) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, shouldShowPromotion, handleDismiss]);

  // Don't render if user shouldn't see promotion
  if (!shouldShowPromotion) return null;

  // Enhanced compact variant (for sidebar or small spaces)
  if (variant === PROMOTION_VARIANTS.COMPACT) {
    return (
      <Fade in timeout={600}>
        <StyledPromotionCard
          elevation={0}
          variant="featured"
          isAnimated={enableAnimations}
          urgencyLevel={urgencyLevel}
          data-testid={testId}
          sx={{
            p: 2,
            mb: 3,
            borderRadius: 2
          }}
        >
          {enableDismiss && (
            <IconButton
              size="small"
              onClick={handleDismiss}
              sx={{ position: 'absolute', top: 8, right: 8 }}
              aria-label="Dismiss promotion"
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          )}

          <Stack spacing={2}>
            <Stack direction="row" spacing={1} alignItems="center">
              <StarIcon color="primary" />
              <Typography variant="subtitle2" fontWeight="bold">
                Try {targetPlan.charAt(0).toUpperCase() + targetPlan.slice(1)} Free
              </Typography>
              {showUrgency && (
                <Chip
                  label="Limited Time"
                  size="small"
                  color="error"
                  variant="filled"
                />
              )}
            </Stack>

            <Typography variant="caption" color="text.secondary">
              Unlock premium features with our {planFeatures.trialDays}-day no-risk trial.
            </Typography>

            <StyledCTAButton
              variant="primary"
              size="small"
              fullWidth
              onClick={handleStartTrial}
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={16} /> : <RocketIcon />}
              isAnimated={enableAnimations}
            >
              {isLoading ? 'Starting...' : 'Start Free Trial'}
            </StyledCTAButton>

            <Typography variant="caption" color="text.secondary" align="center">
              No credit card required • Cancel anytime
            </Typography>
          </Stack>
        </StyledPromotionCard>
      </Fade>
    );
  }
  
  // Enhanced banner variant (for top of page)
  if (variant === PROMOTION_VARIANTS.BANNER) {
    return (
      <Slide direction="down" in timeout={600}>
        <StyledPromotionCard
          elevation={0}
          variant="featured"
          isAnimated={enableAnimations}
          urgencyLevel={urgencyLevel}
          data-testid={testId}
          sx={{
            p: 2,
            mb: 3,
            borderRadius: 2
          }}
        >
          {enableDismiss && (
            <IconButton
              size="small"
              onClick={handleDismiss}
              sx={{ position: 'absolute', top: 8, right: 8 }}
              aria-label="Dismiss promotion"
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          )}

          <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems="center" justifyContent="space-between">
            <Stack direction="row" spacing={2} alignItems="center">
              <Avatar sx={{ bgcolor: 'primary.main' }}>
                <StarIcon />
              </Avatar>
              <Box>
                <Typography variant="subtitle1" fontWeight="bold">
                  Unlock All Premium Features for {planFeatures.trialDays} Days
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Try the {targetPlan.charAt(0).toUpperCase() + targetPlan.slice(1)} plan free. No credit card required.
                </Typography>
              </Box>
            </Stack>

            <StyledCTAButton
              variant="primary"
              onClick={handleStartTrial}
              disabled={isLoading}
              startIcon={isLoading ? <CircularProgress size={16} /> : <RocketIcon />}
              isAnimated={enableAnimations}
            >
              {isLoading ? 'Starting...' : 'Start Free Trial'}
            </StyledCTAButton>
          </Stack>
        </StyledPromotionCard>
      </Slide>
    );
  }
  
  // Enhanced full variant (default, for dedicated sections)
  return (
    <Fade in timeout={800}>
      <StyledPromotionCard
        elevation={2}
        variant="premium"
        isAnimated={enableAnimations}
        urgencyLevel={urgencyLevel}
        data-testid={testId}
        tabIndex={enableAccessibility ? 0 : -1}
        role={enableAccessibility ? "region" : undefined}
        aria-label={enableAccessibility ? `Trial promotion for ${targetPlan} plan` : undefined}
        sx={{
          p: isMobile ? 2 : 4,
          mb: 4,
          borderRadius: 3
        }}
      >
        {/* Enhanced header with dismiss */}
        {enableDismiss && (
          <IconButton
            size="small"
            onClick={handleDismiss}
            sx={{ position: 'absolute', top: 16, right: 16, zIndex: 1 }}
            aria-label="Dismiss promotion"
          >
            <CloseIcon />
          </IconButton>
        )}

        <Grid container spacing={4}>
          {/* Enhanced content section */}
          <Grid item xs={12} md={7}>
            <Stack spacing={3}>
              {/* Enhanced title */}
              <Box>
                <Stack direction="row" spacing={2} alignItems="center" mb={2}>
                  <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                    <CelebrationIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h4" fontWeight="bold" gutterBottom>
                      Try {targetPlan.charAt(0).toUpperCase() + targetPlan.slice(1)} Free for {planFeatures.trialDays} Days
                    </Typography>
                    <Stack direction="row" spacing={1} alignItems="center" flexWrap="wrap">
                      <Chip
                        icon={<TimerIcon />}
                        label={`${planFeatures.trialDays}-Day Trial`}
                        color="primary"
                        variant="filled"
                      />
                      <Chip
                        icon={<LocalOfferIcon />}
                        label={`Save ${planFeatures.savings}`}
                        color="success"
                        variant="outlined"
                      />
                    </Stack>
                  </Box>
                </Stack>

                <Typography variant="body1" color="text.secondary" paragraph>
                  Experience all the premium features that can transform your social media marketing strategy.
                </Typography>
              </Box>

              {/* Enhanced features list */}
              {showFeatures && (
                <Box>
                  <Typography variant="h6" fontWeight="bold" mb={2}>
                    What&apos;s Included:
                  </Typography>

                  <StyledFeatureList>
                    {planFeatures.features.slice(0, 4).map((feature, index) => (
                      <ListItem key={index} sx={{ py: 1 }}>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          {feature.icon || <CheckCircleOutlineIcon color="primary" />}
                        </ListItemIcon>
                        <ListItemText
                          primary={feature.title}
                          secondary={feature.subtitle}
                          primaryTypographyProps={{ fontWeight: 'medium' }}
                        />
                      </ListItem>
                    ))}
                  </StyledFeatureList>
                </Box>
              )}

              {/* Enhanced CTA */}
              <Box>
                <StyledCTAButton
                  variant="premium"
                  size="large"
                  onClick={handleStartTrial}
                  disabled={isLoading}
                  startIcon={isLoading ? <CircularProgress size={20} /> : <AutoAwesomeIcon />}
                  isAnimated={enableAnimations}
                  sx={{ mr: 2, py: 1.5 }}
                >
                  {isLoading ? 'Starting Trial...' : 'Start Your Free Trial'}
                </StyledCTAButton>

                <Link
                  component={RouterLink}
                  to="/pricing"
                  color="primary"
                  onClick={() => trackAnalytics(PROMOTION_ANALYTICS_EVENTS.COMPARE_PLANS)}
                >
                  Compare all plans
                </Link>
              </Box>

              <Typography variant="caption" color="text.secondary">
                No credit card required. Automatically downgrades to Creator plan after {planFeatures.trialDays} days.
              </Typography>
            </Stack>
          </Grid>

          {/* Enhanced testimonial section */}
          <Grid item xs={12} md={5}>
            <Card
              sx={{
                background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.1)}, ${alpha(theme.palette.info.main, 0.1)})`,
                border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                borderRadius: 3,
                height: '100%'
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Stack spacing={3}>
                  <Typography variant="h6" gutterBottom>
                    What our users say
                  </Typography>

                  {enableTestimonials && planFeatures.testimonial && (
                    <Box>
                      <Typography variant="body2" paragraph sx={{ fontStyle: 'italic' }}>
                      </Typography>

                      <Stack direction="row" spacing={2} alignItems="center">
                        <Avatar sx={{ bgcolor: 'success.main' }}>
                          <FavoriteIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle2">
                            {planFeatures.testimonial.author}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {planFeatures.testimonial.role}
                          </Typography>
                        </Box>
                      </Stack>
                    </Box>
                  )}

                  <Divider />

                  {showStats && planFeatures.stats && (
                    <Box>
                      <Typography variant="body2" paragraph fontWeight="bold">
                        Average results with {targetPlan.charAt(0).toUpperCase() + targetPlan.slice(1)}:
                      </Typography>

                      <Stack spacing={1}>
                        {planFeatures.stats.map((stat, index) => (
                          <Stack key={index} direction="row" spacing={1} alignItems="center">
                            <TrendingUpIcon color="success" fontSize="small" />
                            <Typography variant="body2">{stat}</Typography>
                          </Stack>
                        ))}
                      </Stack>
                    </Box>
                  )}
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Error state */}
        {error && (
          <Alert
            severity="error"
            sx={{ mt: 3 }}
            onClose={() => setError(null)}
          >
            <AlertTitle>Error</AlertTitle>
            {error}
          </Alert>
        )}

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={() => setSnackbarOpen(false)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert
            onClose={() => setSnackbarOpen(false)}
            severity={snackbarSeverity}
            sx={{ width: '100%' }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </StyledPromotionCard>
    </Fade>
  );
});

// Set display name for debugging
TrialPromotion.displayName = 'TrialPromotion';

// Comprehensive PropTypes for enterprise-grade trial promotion
TrialPromotion.propTypes = {
  /** Promotion variant */
  variant: PropTypes.oneOf(Object.values(PROMOTION_VARIANTS)),

  /** Target plan for trial */
  targetPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),

  /** Whether to enable analytics tracking */
  enableAnalytics: PropTypes.bool,

  /** Whether to enable accessibility features */
  enableAccessibility: PropTypes.bool,

  /** Whether to enable animations */
  enableAnimations: PropTypes.bool,

  /** Whether to enable dismiss functionality */
  enableDismiss: PropTypes.bool,

  /** Whether to enable testimonials */
  enableTestimonials: PropTypes.bool,

  /** Whether to show features */
  showFeatures: PropTypes.bool,

  /** Whether to show stats */
  showStats: PropTypes.bool,

  /** Whether to show urgency indicators */
  showUrgency: PropTypes.bool,

  /** Urgency level */
  urgencyLevel: PropTypes.oneOf(Object.values(URGENCY_LEVELS)),

  /** Whether to auto-hide promotion */
  autoHide: PropTypes.bool,

  /** Auto-hide delay in milliseconds */
  autoHideDelay: PropTypes.number,

  /** Analytics event handler */
  onAnalytics: PropTypes.func,

  /** Trial start handler */
  onTrialStart: PropTypes.func,

  /** Dismiss handler */
  onDismiss: PropTypes.func,

  /** Test ID for testing */
  testId: PropTypes.string
};

TrialPromotion.defaultProps = {
  variant: PROMOTION_VARIANTS.FULL,
  targetPlan: 'accelerator',
  enableAnalytics: true,
  enableAccessibility: true,
  enableAnimations: true,
  enableDismiss: true,
  enableTestimonials: true,
  showFeatures: true,
  showStats: true,
  showUrgency: false,
  urgencyLevel: URGENCY_LEVELS.LOW,
  autoHide: false,
  autoHideDelay: 30000,
  onAnalytics: null,
  onTrialStart: null,
  onDismiss: null,
  testId: null
};

export default TrialPromotion;

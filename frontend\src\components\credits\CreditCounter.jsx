/**
 * Enhanced CreditCounter Component - Enterprise-grade credit management and visualization
 * Features: Intelligent credit visualization, usage analytics, consumption tracking,
 * credit history, billing integration, comprehensive credit management insights,
 * subscription-based feature gating, accessibility compliance, error handling, and
 * ACE Social platform integration
 */

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
  forwardRef,
  useImperativeHandle
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Typography,
  Chip,
  LinearProgress,
  Tooltip,
  IconButton,
  Alert,
  Fade,
  useTheme,
  Card,
  Badge,
  Skeleton,
  Zoom,
  Grow,
  Slide,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  CircularProgress
} from '@mui/material';
import {
  AutoAwesome as CreditsIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  ShoppingCart as PurchaseIcon,
  Close as CloseIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from '../../contexts/SubscriptionContext';
import { useNotification } from '../../hooks/useNotification';
import { useAccessibility } from '../../hooks/useAccessibility';
import { useAddOnMarketplace } from '../../contexts/AddOnMarketplaceContext';

// Enhanced component imports
import ErrorBoundary from '../common/ErrorBoundary';

/**
 * Enhanced CreditCounter Component with Enterprise Features
 */
const CreditCounter = memo(forwardRef(({
  variant = 'default',
  showProgress = true,
  showRefresh = false,
  showHistory = false,
  showAnalytics = false,
  enableAddOnPurchase = true,
  showPurchaseButton = true,
  size = 'medium',
  placement = 'top',
  creditType = 'regeneration_credits',
  enableRealTimeUpdates = true,
  enableNotifications = true,
  enableAccessibility = true,
  onCreditUpdate = null,
  onHistoryClick = null,
  onPurchaseComplete = null,
  className = '',
  'data-testid': testId = 'credit-counter',
  ...props
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    usage,
    featureLimits,
    subscription,
    refreshSubscription,
    loading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification, showWarningNotification } = useNotification();
  const { announceToScreenReader, isScreenReaderActive } = useAccessibility();

  // Add-on marketplace integration
  const {
    availableAddOns,
    purchaseAddOn,
    loading: addOnLoading,
    error: addOnError,
    refreshAddOns
  } = useAddOnMarketplace();

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    refreshing: false,
    expanded: false,
    showDetails: false,
    lastUpdated: null,
    animationKey: 0,
    errors: {},
    notifications: [],
    historicalData: [],
    trends: null,
    loading: false,
    // Add-on marketplace state
    showPurchaseDialog: false,
    selectedAddOn: null,
    purchasing: false,
    purchaseSuccess: false,
    purchaseError: null
  });

  // Refs for enhanced functionality
  const containerRef = useRef(null);
  const progressRef = useRef(null);
  const updateIntervalRef = useRef(null);

  /**
   * Enhanced credit information calculation with analytics and depletion detection - Production Ready
   */
  const getCreditsInfo = useCallback(() => {
    if (!usage || !featureLimits) {
      return {
        used: 0,
        total: 0,
        remaining: 0,
        percentage: 0,
        status: 'loading',
        trend: 'stable',
        efficiency: 0,
        projectedDepletion: null,
        costPerCredit: 0,
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const creditKey = `${creditType}_used`;
    const limitKey = creditType;

    const used = usage[creditKey] || 0;
    const total = featureLimits[limitKey] || 0;
    const remaining = Math.max(0, total - used);
    const percentage = total > 0 ? (used / total) * 100 : 0;

    // Enhanced status calculation
    let status = 'excellent';
    if (percentage >= 95) status = 'critical';
    else if (percentage >= 85) status = 'warning';
    else if (percentage >= 70) status = 'caution';
    else if (percentage >= 50) status = 'moderate';
    else if (percentage >= 25) status = 'good';

    // Calculate trend and efficiency
    const trend = calculateUsageTrend(used, state.historicalData);
    const efficiency = calculateCreditEfficiency(used, total);
    const projectedDepletion = calculateProjectedDepletion(used, remaining, trend);
    const costPerCredit = calculateCostPerCredit(subscription?.plan_id);

    // Detect mid-cycle depletion
    const depletionInfo = detectCreditDepletion({ used, total, remaining, percentage, status }, subscription);

    return {
      used,
      total,
      remaining,
      percentage,
      status,
      trend,
      efficiency,
      projectedDepletion,
      costPerCredit,
      depletionInfo
    };
  }, [usage, featureLimits, creditType, state.historicalData, subscription, calculateUsageTrend, calculateCreditEfficiency, calculateProjectedDepletion, calculateCostPerCredit, detectCreditDepletion]);

  /**
   * Calculate usage trend - Production Ready
   */
  const calculateUsageTrend = useCallback((currentUsage, historicalData) => {
    if (!historicalData || historicalData.length < 2) return 'stable';

    const recentData = historicalData.slice(-7); // Last 7 data points
    const trend = recentData.reduce((acc, curr, index) => {
      if (index === 0) return acc;
      return acc + (curr.usage - recentData[index - 1].usage);
    }, 0);

    if (trend > 5) return 'increasing';
    if (trend < -5) return 'decreasing';
    return 'stable';
  }, []);

  /**
   * Calculate credit efficiency score - Production Ready
   */
  const calculateCreditEfficiency = useCallback((used, total) => {
    if (total === 0) return 100;
    const efficiency = ((total - used) / total) * 100;
    return Math.max(0, Math.min(100, efficiency));
  }, []);

  /**
   * Calculate projected depletion date - Production Ready
   */
  const calculateProjectedDepletion = useCallback((used, remaining, trend) => {
    if (remaining === 0 || trend === 'decreasing') return null;
    if (trend === 'stable') return null;

    // Simple projection based on current usage rate
    const daysRemaining = Math.ceil(remaining / (used / 30)); // Assuming monthly cycle
    const depletionDate = new Date();
    depletionDate.setDate(depletionDate.getDate() + daysRemaining);

    return depletionDate;
  }, []);

  /**
   * Calculate cost per credit based on subscription plan - Production Ready
   */
  const calculateCostPerCredit = useCallback((planId) => {
    const costMapping = {
      'creator': 0.10,
      'accelerator': 0.08,
      'dominator': 0.00 // Unlimited
    };

    return costMapping[planId] || 0.12; // Default cost
  }, []);

  /**
   * Detect credit depletion before monthly cycle ends - Production Ready
   */
  const detectCreditDepletion = useCallback((creditsInfo, subscription) => {
    if (!creditsInfo || !subscription) return { isDepletedMidCycle: false, daysRemaining: 0 };

    const now = new Date();
    const billingCycleStart = new Date(subscription.current_period_start * 1000);
    const billingCycleEnd = new Date(subscription.current_period_end * 1000);

    const totalCycleDays = Math.ceil((billingCycleEnd - billingCycleStart) / (1000 * 60 * 60 * 24));
    const daysElapsed = Math.ceil((now - billingCycleStart) / (1000 * 60 * 60 * 24));
    const daysRemaining = Math.max(0, totalCycleDays - daysElapsed);

    // Consider depleted if credits are exhausted with more than 3 days left in cycle
    const isDepletedMidCycle = creditsInfo.remaining === 0 && daysRemaining > 3;

    return {
      isDepletedMidCycle,
      daysRemaining,
      cycleProgress: (daysElapsed / totalCycleDays) * 100
    };
  }, []);

  /**
   * Get available credit add-ons for current subscription tier - Production Ready
   */
  const getAvailableCreditAddOns = useCallback(() => {
    if (!availableAddOns || !subscription) return [];

    const planId = subscription.plan_id || 'creator';

    // Filter add-ons based on subscription tier and credit type
    return availableAddOns.filter(addOn => {
      return addOn.type === 'credits' &&
             addOn.credit_type === creditType &&
             addOn.compatible_plans.includes(planId);
    }).sort((a, b) => a.price - b.price); // Sort by price ascending
  }, [availableAddOns, subscription, creditType]);

  /**
   * Handle add-on purchase initiation - Production Ready
   */
  const handlePurchaseCredits = useCallback(async (addOnId = null) => {
    if (!enableAddOnPurchase) return;

    try {
      setState(prev => ({
        ...prev,
        showPurchaseDialog: true,
        selectedAddOn: addOnId,
        purchaseError: null
      }));

      // Refresh available add-ons to ensure latest pricing
      await refreshAddOns();

      if (enableAccessibility && isScreenReaderActive) {
        announceToScreenReader('Opening credit purchase options');
      }
    } catch (error) {
      console.error('Error opening purchase dialog:', error);
      showErrorNotification('Failed to load purchase options');
    }
  }, [enableAddOnPurchase, refreshAddOns, enableAccessibility, isScreenReaderActive, announceToScreenReader, showErrorNotification]);

  /**
   * Execute add-on purchase - Production Ready
   */
  const executePurchase = useCallback(async (addOnId, quantity = 1) => {
    if (!addOnId || state.purchasing) return;

    setState(prev => ({ ...prev, purchasing: true, purchaseError: null }));

    try {
      const result = await purchaseAddOn(addOnId, quantity);

      if (result.success) {
        setState(prev => ({
          ...prev,
          purchasing: false,
          purchaseSuccess: true,
          showPurchaseDialog: false
        }));

        // Refresh subscription data to get updated credit balance
        await refreshSubscription();

        showSuccessNotification(
          `Successfully purchased ${result.credits_added} credits! Your balance has been updated.`
        );

        if (onPurchaseComplete) {
          onPurchaseComplete(result);
        }

        if (onCreditUpdate) {
          onCreditUpdate(result.updated_credits);
        }

        if (enableAccessibility && isScreenReaderActive) {
          announceToScreenReader(`Purchase successful. ${result.credits_added} credits added to your account.`);
        }
      } else {
        throw new Error(result.error || 'Purchase failed');
      }
    } catch (error) {
      console.error('Purchase failed:', error);
      setState(prev => ({
        ...prev,
        purchasing: false,
        purchaseError: error.message || 'Purchase failed. Please try again.'
      }));
      showErrorNotification(`Purchase failed: ${error.message}`);
    }
  }, [
    state.purchasing,
    purchaseAddOn,
    refreshSubscription,
    showSuccessNotification,
    showErrorNotification,
    onPurchaseComplete,
    onCreditUpdate,
    enableAccessibility,
    isScreenReaderActive,
    announceToScreenReader
  ]);

  /**
   * Close purchase dialog - Production Ready
   */
  const closePurchaseDialog = useCallback(() => {
    setState(prev => ({
      ...prev,
      showPurchaseDialog: false,
      selectedAddOn: null,
      purchaseError: null,
      purchaseSuccess: false
    }));
  }, []);

  const creditsInfo = useMemo(() => getCreditsInfo(), [getCreditsInfo]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const isUnlimited = planId === 'dominator';

    return {
      isUnlimited,
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasAdvancedAnalytics: ['accelerator', 'dominator'].includes(planId),
      hasRealTimeUpdates: ['accelerator', 'dominator'].includes(planId),
      hasHistoryAccess: ['accelerator', 'dominator'].includes(planId),
      hasPrioritySupport: planId === 'dominator',
      maxCreditHistory: planId === 'dominator' ? -1 : planId === 'accelerator' ? 90 : 30,
      refreshInterval: planId === 'dominator' ? 5000 : 30000 // Real-time vs periodic
    };
  }, [subscription?.plan_id, subscription?.plan_name]);

  /**
   * Enhanced status configuration with ACE Social branding - Production Ready
   */
  const getStatusConfig = useCallback(() => {
    const aceColors = {
      primary: '#4E40C5',
      dark: '#15110E',
      yellow: '#EBAE1B',
      white: '#FFFFFF',
      success: '#00D68F',
      error: '#FF3D71',
      warning: '#FFA726'
    };

    if (subscriptionFeatures.isUnlimited) {
      return {
        color: 'success',
        icon: <CreditsIcon sx={{ color: aceColors.success }} />,
        bgColor: `${aceColors.success}20`,
        textColor: aceColors.success,
        borderColor: aceColors.success,
        accentColor: aceColors.primary,
        statusText: 'Unlimited',
        priority: 'high'
      };
    }

    const trendIcon = {
      'increasing': <TrendingUpIcon />,
      'decreasing': <TrendingDownIcon />,
      'stable': <TrendingFlatIcon />
    }[creditsInfo.trend] || <TrendingFlatIcon />;

    switch (creditsInfo.status) {
      case 'critical':
        return {
          color: 'error',
          icon: <ErrorIcon sx={{ color: aceColors.error }} />,
          bgColor: `${aceColors.error}20`,
          textColor: aceColors.error,
          borderColor: aceColors.error,
          accentColor: aceColors.yellow,
          statusText: 'Critical',
          priority: 'critical',
          trendIcon,
          shouldPulse: true
        };
      case 'warning':
        return {
          color: 'warning',
          icon: <WarningIcon sx={{ color: aceColors.warning }} />,
          bgColor: `${aceColors.warning}20`,
          textColor: aceColors.warning,
          borderColor: aceColors.warning,
          accentColor: aceColors.primary,
          statusText: 'Warning',
          priority: 'high',
          trendIcon,
          shouldPulse: creditsInfo.percentage >= 90
        };
      case 'caution':
        return {
          color: 'info',
          icon: <InfoIcon sx={{ color: aceColors.primary }} />,
          bgColor: `${aceColors.primary}20`,
          textColor: aceColors.primary,
          borderColor: aceColors.primary,
          accentColor: aceColors.yellow,
          statusText: 'Caution',
          priority: 'medium',
          trendIcon
        };
      case 'moderate':
        return {
          color: 'primary',
          icon: <CreditsIcon sx={{ color: aceColors.primary }} />,
          bgColor: `${aceColors.primary}15`,
          textColor: aceColors.primary,
          borderColor: aceColors.primary,
          accentColor: aceColors.success,
          statusText: 'Moderate',
          priority: 'medium',
          trendIcon
        };
      case 'good':
        return {
          color: 'success',
          icon: <CreditsIcon sx={{ color: aceColors.success }} />,
          bgColor: `${aceColors.success}15`,
          textColor: aceColors.success,
          borderColor: aceColors.success,
          accentColor: aceColors.primary,
          statusText: 'Good',
          priority: 'low',
          trendIcon
        };
      default:
        return {
          color: 'success',
          icon: <CreditsIcon sx={{ color: aceColors.success }} />,
          bgColor: `${aceColors.success}10`,
          textColor: aceColors.success,
          borderColor: aceColors.success,
          accentColor: aceColors.primary,
          statusText: 'Excellent',
          priority: 'low',
          trendIcon
        };
    }
  }, [subscriptionFeatures.isUnlimited, creditsInfo.status, creditsInfo.trend, creditsInfo.percentage]);

  const statusConfig = useMemo(() => getStatusConfig(), [getStatusConfig]);



  /**
   * Enhanced refresh functionality with analytics - Production Ready
   */
  const handleRefresh = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, refreshing: true, errors: {} }));

      // Refresh subscription data
      await refreshSubscription();

      // Update last refreshed timestamp
      setState(prev => ({
        ...prev,
        lastUpdated: new Date(),
        animationKey: prev.animationKey + 1
      }));

      // Analytics tracking would be implemented here in production
      console.log('Credit information refreshed successfully');

      // Accessibility announcement
      if (enableAccessibility && isScreenReaderActive) {
        announceToScreenReader('Credit information has been refreshed');
      }

      showSuccessNotification('Credit information updated');

      // Trigger callback if provided
      if (onCreditUpdate) {
        onCreditUpdate(creditsInfo);
      }

    } catch (error) {
      console.error('Error refreshing credit information:', error);

      setState(prev => ({
        ...prev,
        errors: { ...prev.errors, refresh: 'Failed to refresh credit information' }
      }));

      showErrorNotification('Failed to refresh credit information');
    } finally {
      setState(prev => ({ ...prev, refreshing: false }));
    }
  }, [
    refreshSubscription,
    enableAccessibility,
    isScreenReaderActive,
    announceToScreenReader,
    showSuccessNotification,
    showErrorNotification,
    onCreditUpdate,
    creditsInfo
  ]);



  /**
   * Handle history button click - Production Ready
   */
  const handleHistoryClick = useCallback(() => {
    // Analytics tracking would be implemented here in production
    console.log('History button clicked from credit counter');

    if (onHistoryClick) {
      onHistoryClick(creditsInfo);
    } else {
      // Default history navigation
      window.location.href = '/billing/usage';
    }
  }, [onHistoryClick, creditsInfo]);

  /**
   * Handle expand/collapse toggle - Production Ready
   */
  const handleToggleExpanded = useCallback(() => {
    setState(prev => ({
      ...prev,
      expanded: !prev.expanded,
      animationKey: prev.animationKey + 1
    }));

    // Accessibility announcement
    if (enableAccessibility && isScreenReaderActive) {
      announceToScreenReader(
        state.expanded ? 'Credit details collapsed' : 'Credit details expanded'
      );
    }
  }, [enableAccessibility, isScreenReaderActive, announceToScreenReader, state.expanded]);

  /**
   * Enhanced compact variant with accessibility and animations - Production Ready
   */
  const renderCompact = useCallback(() => (
    <Zoom in={true} timeout={300}>
      <Badge
        badgeContent={
          statusConfig.priority === 'critical' ? '!' :
          creditsInfo.trend === 'increasing' ? '↗' :
          creditsInfo.trend === 'decreasing' ? '↘' : null
        }
        color={statusConfig.color}
        invisible={statusConfig.priority === 'low'}
      >
        <Chip
          ref={containerRef}
          icon={statusConfig.icon}
          label={
            subscriptionFeatures.isUnlimited
              ? 'Unlimited'
              : `${creditsInfo.remaining}/${creditsInfo.total}`
          }
          color={statusConfig.color}
          size={size}
          clickable={showHistory || showAnalytics}
          onClick={showHistory ? handleHistoryClick : undefined}
          data-testid={`${testId}-compact`}
          aria-label={
            subscriptionFeatures.isUnlimited
              ? 'Unlimited credits available'
              : `${creditsInfo.remaining} of ${creditsInfo.total} credits remaining. Status: ${statusConfig.statusText}`
          }
          sx={{
            fontWeight: 'bold',
            transition: 'all 0.3s ease-in-out',
            animation: statusConfig.shouldPulse ? 'pulse 2s infinite' : 'none',
            '& .MuiChip-icon': {
              fontSize: size === 'small' ? '16px' : '20px'
            },
            '&:hover': {
              transform: 'scale(1.05)',
              boxShadow: theme.shadows[4]
            },
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.7 },
              '100%': { opacity: 1 }
            }
          }}
        />
      </Badge>
    </Zoom>
  ), [
    statusConfig,
    creditsInfo,
    subscriptionFeatures.isUnlimited,
    size,
    showHistory,
    showAnalytics,
    handleHistoryClick,
    testId,
    theme.shadows
  ]);

  /**
   * Enhanced default variant with comprehensive features - Production Ready
   */
  const renderDefault = useCallback(() => (
    <Grow in={true} timeout={400}>
      <Card
        ref={containerRef}
        elevation={2}
        data-testid={`${testId}-default`}
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          p: 1.5,
          borderRadius: 2,
          bgcolor: statusConfig.bgColor,
          border: `2px solid ${statusConfig.borderColor}`,
          minWidth: 220,
          transition: 'all 0.3s ease-in-out',
          animation: statusConfig.shouldPulse ? 'pulse 2s infinite' : 'none',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[8],
            borderColor: statusConfig.accentColor
          },
          '@keyframes pulse': {
            '0%': { opacity: 1 },
            '50%': { opacity: 0.8 },
            '100%': { opacity: 1 }
          }
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {statusConfig.icon}
          {statusConfig.trendIcon && (
            <Box sx={{ fontSize: '0.8rem', color: statusConfig.textColor, opacity: 0.7 }}>
              {statusConfig.trendIcon}
            </Box>
          )}
        </Box>

        <Box sx={{ flexGrow: 1 }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 'bold',
              color: statusConfig.textColor,
              display: 'flex',
              alignItems: 'center',
              gap: 0.5
            }}
            aria-label={
              subscriptionFeatures.isUnlimited
                ? 'Unlimited credits available'
                : `${creditsInfo.used} of ${creditsInfo.total} credits used. ${creditsInfo.remaining} remaining`
            }
          >
            {subscriptionFeatures.isUnlimited
              ? 'Unlimited Credits'
              : `${creditsInfo.used}/${creditsInfo.total} Credits Used`
            }
            {creditsInfo.efficiency > 0 && !subscriptionFeatures.isUnlimited && (
              <Chip
                label={`${Math.round(creditsInfo.efficiency)}%`}
                size="small"
                variant="outlined"
                sx={{ ml: 1, height: 16, fontSize: '0.7rem' }}
              />
            )}
          </Typography>

          {!subscriptionFeatures.isUnlimited && showProgress && (
            <LinearProgress
              ref={progressRef}
              variant="determinate"
              value={creditsInfo.percentage}
              color={statusConfig.color}
              sx={{
                mt: 0.5,
                height: 8,
                borderRadius: 4,
                bgcolor: 'rgba(255,255,255,0.3)',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 4,
                  transition: 'transform 0.4s ease-in-out'
                }
              }}
              aria-label={`Credit usage: ${Math.round(creditsInfo.percentage)}%`}
            />
          )}

          {!subscriptionFeatures.isUnlimited && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 0.5 }}>
              <Typography variant="caption" sx={{ color: statusConfig.textColor, opacity: 0.8 }}>
                {creditsInfo.remaining} remaining
              </Typography>
              {creditsInfo.projectedDepletion && (
                <Typography variant="caption" sx={{ color: statusConfig.textColor, opacity: 0.6 }}>
                  ~{Math.ceil((creditsInfo.projectedDepletion - new Date()) / (1000 * 60 * 60 * 24))} days
                </Typography>
              )}
            </Box>
          )}
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          {/* Purchase Credits Button - Shows when credits are low/depleted */}
          {enableAddOnPurchase && showPurchaseButton &&
           (creditsInfo.status === 'critical' || creditsInfo.depletionInfo.isDepletedMidCycle) && (
            <Button
              size="small"
              variant="contained"
              color="primary"
              startIcon={<PurchaseIcon />}
              onClick={() => handlePurchaseCredits()}
              disabled={state.purchasing || addOnLoading}
              sx={{
                minWidth: 'auto',
                fontSize: '0.75rem',
                px: 1,
                py: 0.5,
                bgcolor: '#4E40C5',
                '&:hover': { bgcolor: '#3d2f9f' }
              }}
              aria-label="Purchase additional credits"
            >
              {state.purchasing ? 'Buying...' : 'Buy Credits'}
            </Button>
          )}

          {showRefresh && (
            <IconButton
              size="small"
              onClick={handleRefresh}
              disabled={state.refreshing}
              sx={{ color: statusConfig.textColor }}
              aria-label="Refresh credit information"
            >
              <RefreshIcon sx={{
                animation: state.refreshing ? 'spin 1s linear infinite' : 'none',
                '@keyframes spin': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }} />
            </IconButton>
          )}

          {(showHistory || showAnalytics) && (
            <IconButton
              size="small"
              onClick={handleToggleExpanded}
              sx={{ color: statusConfig.textColor }}
              aria-label={state.expanded ? 'Collapse details' : 'Expand details'}
            >
              {state.expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          )}
        </Box>
      </Card>
    </Grow>
  ), [
    statusConfig,
    creditsInfo,
    subscriptionFeatures.isUnlimited,
    showProgress,
    showRefresh,
    showHistory,
    showAnalytics,
    enableAddOnPurchase,
    showPurchaseButton,
    handlePurchaseCredits,
    handleRefresh,
    handleToggleExpanded,
    state.refreshing,
    state.expanded,
    state.purchasing,
    addOnLoading,
    testId,
    theme.shadows
  ]);

  /**
   * Enhanced full variant with comprehensive analytics - Production Ready
   */
  const renderFull = useCallback(() => (
    <Slide direction="up" in={true} timeout={500}>
      <Card
        ref={containerRef}
        elevation={4}
        data-testid={`${testId}-full`}
        sx={{
          p: 3,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${statusConfig.bgColor} 0%, ${statusConfig.bgColor}80 100%)`,
          border: `2px solid ${statusConfig.borderColor}`,
          position: 'relative',
          overflow: 'visible'
        }}
      >
        <Typography variant="h6" sx={{ mb: 2, color: statusConfig.textColor }}>
          {creditType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </Typography>

        {subscriptionFeatures.isUnlimited ? (
          <Alert severity="success" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Unlimited Credits</strong> - Generate as much as you want!
            </Typography>
          </Alert>
        ) : (
          <>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2">Credits Used</Typography>
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                {creditsInfo.used} / {creditsInfo.total}
              </Typography>
            </Box>

            {showProgress && (
              <LinearProgress
                variant="determinate"
                value={creditsInfo.percentage}
                color={statusConfig.color}
                sx={{ mb: 2, height: 8, borderRadius: 4 }}
              />
            )}

            <Typography variant="body2" color="text.secondary">
              {creditsInfo.remaining} credits remaining
            </Typography>

            {/* Enhanced alerts with add-on purchase options */}
            {creditsInfo.depletionInfo.isDepletedMidCycle && (
              <Alert
                severity="error"
                sx={{ mt: 2 }}
                action={
                  enableAddOnPurchase && (
                    <Button
                      color="inherit"
                      size="small"
                      startIcon={<PurchaseIcon />}
                      onClick={() => handlePurchaseCredits()}
                      disabled={state.purchasing || addOnLoading}
                    >
                      {state.purchasing ? 'Purchasing...' : 'Buy Credits'}
                    </Button>
                  )
                }
              >
                <Typography variant="body2">
                  <strong>Credits Depleted!</strong> You&apos;ve run out of credits with {creditsInfo.depletionInfo.daysRemaining} days left in your billing cycle.
                  {enableAddOnPurchase && ' Purchase additional credits to continue generating content.'}
                </Typography>
              </Alert>
            )}

            {creditsInfo.status === 'critical' && !creditsInfo.depletionInfo.isDepletedMidCycle && (
              <Alert
                severity="warning"
                sx={{ mt: 2 }}
                action={
                  enableAddOnPurchase && (
                    <Button
                      color="inherit"
                      size="small"
                      startIcon={<PurchaseIcon />}
                      onClick={() => handlePurchaseCredits()}
                      disabled={state.purchasing || addOnLoading}
                    >
                      {state.purchasing ? 'Purchasing...' : 'Buy Credits'}
                    </Button>
                  )
                }
              >
                <Typography variant="body2">
                  <strong>Low Credits!</strong> Only {creditsInfo.remaining} credits remaining.
                  {enableAddOnPurchase && ' Consider purchasing additional credits or upgrading your plan.'}
                </Typography>
              </Alert>
            )}
          </>
        )}
      </Card>
    </Slide>
  ), [
    statusConfig,
    creditsInfo,
    subscriptionFeatures.isUnlimited,
    creditType,
    showProgress,
    enableAddOnPurchase,
    handlePurchaseCredits,
    state.purchasing,
    addOnLoading,
    testId
  ]);

  /**
   * Enhanced content rendering with error boundaries - Production Ready
   */
  const content = useMemo(() => {
    try {
      switch (variant) {
        case 'compact':
          return renderCompact();
        case 'full':
          return renderFull();
        default:
          return renderDefault();
      }
    } catch (error) {
      console.error('Error rendering CreditCounter content:', error);
      return (
        <Alert severity="error" sx={{ minWidth: 200 }}>
          <Typography variant="body2">
            Error loading credit information
          </Typography>
        </Alert>
      );
    }
  }, [variant, renderCompact, renderFull, renderDefault]);

  /**
   * Enhanced tooltip content with accessibility - Production Ready
   */
  const tooltipContent = useMemo(() => {
    if (subscriptionFeatures.isUnlimited) {
      return `Unlimited ${creditType.replace('_', ' ')} with ${subscriptionFeatures.planName} plan`;
    }

    const statusText = `${creditsInfo.remaining} credits remaining (${Math.round(creditsInfo.percentage)}% used)`;
    const trendText = creditsInfo.trend !== 'stable' ? ` - Trend: ${creditsInfo.trend}` : '';
    const efficiencyText = creditsInfo.efficiency > 0 ? ` - Efficiency: ${Math.round(creditsInfo.efficiency)}%` : '';

    return `${statusText}${trendText}${efficiencyText}`;
  }, [
    subscriptionFeatures.isUnlimited,
    subscriptionFeatures.planName,
    creditType,
    creditsInfo.remaining,
    creditsInfo.percentage,
    creditsInfo.trend,
    creditsInfo.efficiency
  ]);

  /**
   * Real-time updates and lifecycle management - Production Ready
   */
  useEffect(() => {
    if (!enableRealTimeUpdates || !subscriptionFeatures.hasRealTimeUpdates) {
      return;
    }

    const interval = setInterval(() => {
      if (!state.refreshing && !loading) {
        handleRefresh();
      }
    }, subscriptionFeatures.refreshInterval);

    updateIntervalRef.current = interval;

    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [
    enableRealTimeUpdates,
    subscriptionFeatures.hasRealTimeUpdates,
    subscriptionFeatures.refreshInterval,
    state.refreshing,
    loading,
    handleRefresh
  ]);

  /**
   * Notification management for credit thresholds - Production Ready
   */
  useEffect(() => {
    if (!enableNotifications || subscriptionFeatures.isUnlimited) {
      return;
    }

    const { percentage, remaining, status } = creditsInfo;

    // Critical threshold notification
    if (percentage >= 95 && status === 'critical') {
      showWarningNotification(
        `Critical: Only ${remaining} credits remaining! Consider upgrading your plan.`,
        { persist: true }
      );
    }
    // Warning threshold notification
    else if (percentage >= 85 && status === 'warning') {
      showWarningNotification(
        `Warning: ${remaining} credits remaining. Plan ahead for your content needs.`
      );
    }
  }, [
    enableNotifications,
    subscriptionFeatures.isUnlimited,
    creditsInfo,
    showWarningNotification
  ]);

  /**
   * Real-time credit updates after add-on purchases - Production Ready
   */
  useEffect(() => {
    if (state.purchaseSuccess) {
      // Auto-refresh credit information after successful purchase
      const refreshTimer = setTimeout(() => {
        handleRefresh();
        setState(prev => ({ ...prev, purchaseSuccess: false }));
      }, 1000);

      return () => clearTimeout(refreshTimer);
    }
  }, [state.purchaseSuccess, handleRefresh]);

  /**
   * Enhanced add-on error handling - Production Ready
   */
  useEffect(() => {
    if (addOnError) {
      showErrorNotification(`Add-on marketplace error: ${addOnError}`);
    }
  }, [addOnError, showErrorNotification]);

  /**
   * Expose component methods via ref - Production Ready
   */
  useImperativeHandle(ref, () => ({
    refresh: handleRefresh,
    getCreditsInfo: () => creditsInfo,
    getStatusConfig: () => statusConfig,
    toggleExpanded: handleToggleExpanded,
    purchaseCredits: handlePurchaseCredits,
    closePurchaseDialog,
    focus: () => containerRef.current?.focus(),
    getElement: () => containerRef.current
  }), [handleRefresh, creditsInfo, statusConfig, handleToggleExpanded, handlePurchaseCredits, closePurchaseDialog]);



  /**
   * Enhanced loading state with skeleton animation - Production Ready
   */
  if (loading || state.loading) {
    return (
      <Fade in={true}>
        <Box
          sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 1 }}
          data-testid={`${testId}-loading`}
          aria-label="Loading credit information"
        >
          <Skeleton variant="circular" width={24} height={24} />
          <Box sx={{ flexGrow: 1 }}>
            <Skeleton variant="text" width="60%" height={20} />
            {showProgress && (
              <Skeleton variant="rectangular" width="100%" height={6} sx={{ mt: 0.5, borderRadius: 3 }} />
            )}
          </Box>
          {showRefresh && (
            <Skeleton variant="circular" width={32} height={32} />
          )}
        </Box>
      </Fade>
    );
  }

  /**
   * Enhanced add-on purchase dialog - Production Ready
   */
  const renderPurchaseDialog = () => {
    const availableCreditAddOns = getAvailableCreditAddOns();

    return (
      <Dialog
        open={state.showPurchaseDialog}
        onClose={closePurchaseDialog}
        maxWidth="sm"
        fullWidth
        aria-labelledby="purchase-dialog-title"
        aria-describedby="purchase-dialog-description"
      >
        <DialogTitle
          id="purchase-dialog-title"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            bgcolor: '#4E40C5',
            color: 'white'
          }}
        >
          <PurchaseIcon />
          Purchase Additional Credits
          <IconButton
            aria-label="close"
            onClick={closePurchaseDialog}
            sx={{ ml: 'auto', color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ pt: 3 }}>
          {addOnError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {addOnError}
            </Alert>
          )}

          {state.purchaseError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {state.purchaseError}
            </Alert>
          )}

          {creditsInfo.depletionInfo.isDepletedMidCycle && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                You have {creditsInfo.depletionInfo.daysRemaining} days remaining in your billing cycle.
                Purchase credits now to continue generating content without interruption.
              </Typography>
            </Alert>
          )}

          <Typography variant="body1" sx={{ mb: 2 }}>
            Choose a credit package to add to your account:
          </Typography>

          <Stack spacing={2}>
            {availableCreditAddOns.length > 0 ? (
              availableCreditAddOns.map((addOn) => (
                <Card
                  key={addOn.id}
                  variant="outlined"
                  sx={{
                    p: 2,
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    '&:hover': {
                      borderColor: '#4E40C5',
                      boxShadow: 2
                    }
                  }}
                  onClick={() => executePurchase(addOn.id)}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {addOn.credits} Credits
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {addOn.description}
                      </Typography>
                      {addOn.bonus_credits > 0 && (
                        <Chip
                          label={`+${addOn.bonus_credits} Bonus Credits`}
                          size="small"
                          color="success"
                          sx={{ mt: 1 }}
                        />
                      )}
                    </Box>
                    <Box sx={{ textAlign: 'right' }}>
                      <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4E40C5' }}>
                        ${addOn.price}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        ${(addOn.price / addOn.credits).toFixed(3)} per credit
                      </Typography>
                    </Box>
                  </Box>
                </Card>
              ))
            ) : (
              <Alert severity="info">
                <Typography variant="body2">
                  {addOnLoading ? 'Loading available credit packages...' : 'No credit packages available for your subscription tier.'}
                </Typography>
              </Alert>
            )}
          </Stack>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Button onClick={closePurchaseDialog} disabled={state.purchasing}>
            Cancel
          </Button>
          {state.purchasing && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CircularProgress size={20} />
              <Typography variant="body2">Processing purchase...</Typography>
            </Box>
          )}
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <>
      <ErrorBoundary
        fallback={
          <Alert severity="error" sx={{ minWidth: 200 }}>
            <Typography variant="body2">
              Credit counter unavailable
            </Typography>
          </Alert>
        }
      >
        <Tooltip
          title={tooltipContent}
          placement={placement}
          arrow
          enterDelay={500}
          leaveDelay={200}
        >
          <Fade in={true} timeout={300}>
            <Box
              className={className}
              data-testid={testId}
              role="status"
              aria-live="polite"
              aria-label={tooltipContent}
              {...props}
            >
              {content}
            </Box>
          </Fade>
        </Tooltip>
      </ErrorBoundary>

      {/* Add-on Purchase Dialog */}
      {enableAddOnPurchase && renderPurchaseDialog()}
    </>
  );
}));

// Set display name for debugging
CreditCounter.displayName = 'CreditCounter';
/**
 * Comprehensive PropTypes validation - Production Ready
 */
CreditCounter.propTypes = {
  /** Visual variant of the component */
  variant: PropTypes.oneOf(['compact', 'default', 'full']),

  /** Whether to show progress bar */
  showProgress: PropTypes.bool,

  /** Whether to show refresh button */
  showRefresh: PropTypes.bool,

  /** Whether to show history access */
  showHistory: PropTypes.bool,

  /** Whether to show analytics features */
  showAnalytics: PropTypes.bool,

  /** Enable add-on marketplace integration */
  enableAddOnPurchase: PropTypes.bool,

  /** Whether to show purchase button when credits are low */
  showPurchaseButton: PropTypes.bool,

  /** Size of the component */
  size: PropTypes.oneOf(['small', 'medium', 'large']),

  /** Tooltip placement */
  placement: PropTypes.oneOf([
    'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left',
    'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top'
  ]),

  /** Type of credits to display */
  creditType: PropTypes.string,

  /** Enable real-time updates */
  enableRealTimeUpdates: PropTypes.bool,

  /** Enable notifications */
  enableNotifications: PropTypes.bool,

  /** Enable accessibility features */
  enableAccessibility: PropTypes.bool,

  /** Callback when credits are updated */
  onCreditUpdate: PropTypes.func,

  /** Callback when history is clicked */
  onHistoryClick: PropTypes.func,

  /** Callback when add-on purchase is completed */
  onPurchaseComplete: PropTypes.func,

  /** Additional CSS class name */
  className: PropTypes.string,

  /** Test ID for testing */
  'data-testid': PropTypes.string
};

/**
 * Default props with enterprise-grade defaults - Production Ready
 */
CreditCounter.defaultProps = {
  variant: 'default',
  showProgress: true,
  showRefresh: false,
  showHistory: false,
  showAnalytics: false,
  enableAddOnPurchase: true,
  showPurchaseButton: true,
  size: 'medium',
  placement: 'top',
  creditType: 'regeneration_credits',
  enableRealTimeUpdates: true,
  enableNotifications: true,
  enableAccessibility: true,
  onCreditUpdate: null,
  onHistoryClick: null,
  onPurchaseComplete: null,
  className: '',
  'data-testid': 'credit-counter'
};

export default CreditCounter;
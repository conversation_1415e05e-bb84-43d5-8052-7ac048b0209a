/**
 * @fileoverview Form utility functions for validation and form handling
 * @version 1.0.0
 * <AUTHOR> Social Platform Team
 */

/**
 * Validation rules for different field types
 */
const VALIDATION_RULES = {
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Please enter a valid email address'
  },
  url: {
    pattern: /^https?:\/\/.+/,
    message: 'Please enter a valid URL starting with http:// or https://'
  },
  phone: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    message: 'Please enter a valid phone number'
  },
  password: {
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    message: 'Password must be at least 8 characters with uppercase, lowercase, and number'
  }
};

/**
 * Validate a single field based on validation rules
 * @param {string} fieldName - Name of the field
 * @param {any} value - Value to validate
 * @param {Object} rules - Validation rules
 * @returns {Object} Validation result with isValid and error message
 */
export const validateField = (fieldName, value, rules = {}) => {
  const result = {
    isValid: true,
    error: null,
    warnings: []
  };

  // Check if field is required
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    result.isValid = false;
    result.error = rules.requiredMessage || `${fieldName} is required`;
    return result;
  }

  // If value is empty and not required, it's valid
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return result;
  }

  // Check minimum length
  if (rules.minLength && value.length < rules.minLength) {
    result.isValid = false;
    result.error = `${fieldName} must be at least ${rules.minLength} characters`;
    return result;
  }

  // Check maximum length
  if (rules.maxLength && value.length > rules.maxLength) {
    result.isValid = false;
    result.error = `${fieldName} must not exceed ${rules.maxLength} characters`;
    return result;
  }

  // Check pattern validation
  if (rules.pattern && !rules.pattern.test(value)) {
    result.isValid = false;
    result.error = rules.patternMessage || `${fieldName} format is invalid`;
    return result;
  }

  // Check predefined validation types
  if (rules.type && VALIDATION_RULES[rules.type]) {
    const typeRule = VALIDATION_RULES[rules.type];
    if (!typeRule.pattern.test(value)) {
      result.isValid = false;
      result.error = typeRule.message;
      return result;
    }
  }

  // Custom validation function
  if (rules.validator && typeof rules.validator === 'function') {
    const customResult = rules.validator(value);
    if (customResult !== true) {
      result.isValid = false;
      result.error = customResult || `${fieldName} is invalid`;
      return result;
    }
  }

  // Check for warnings
  if (rules.warnings) {
    rules.warnings.forEach(warning => {
      if (warning.condition && warning.condition(value)) {
        result.warnings.push(warning.message);
      }
    });
  }

  return result;
};

/**
 * Validate an entire form object
 * @param {Object} formData - Form data to validate
 * @param {Object} validationSchema - Validation schema
 * @returns {Object} Validation result with errors and isValid flag
 */
export const validateForm = (formData, validationSchema) => {
  const errors = {};
  const warnings = {};
  let isValid = true;

  Object.keys(validationSchema).forEach(fieldName => {
    const fieldValue = formData[fieldName];
    const fieldRules = validationSchema[fieldName];
    
    const validation = validateField(fieldName, fieldValue, fieldRules);
    
    if (!validation.isValid) {
      errors[fieldName] = validation.error;
      isValid = false;
    }
    
    if (validation.warnings.length > 0) {
      warnings[fieldName] = validation.warnings;
    }
  });

  return {
    isValid,
    errors,
    warnings
  };
};

/**
 * Format form data for API submission
 * @param {Object} formData - Raw form data
 * @param {Object} formatters - Field formatters
 * @returns {Object} Formatted form data
 */
export const formatFormData = (formData, formatters = {}) => {
  const formatted = { ...formData };

  Object.keys(formatters).forEach(fieldName => {
    if (formatted[fieldName] !== undefined) {
      const formatter = formatters[fieldName];
      if (typeof formatter === 'function') {
        formatted[fieldName] = formatter(formatted[fieldName]);
      }
    }
  });

  return formatted;
};

/**
 * Clean form data by removing empty values
 * @param {Object} formData - Form data to clean
 * @param {Object} options - Cleaning options
 * @returns {Object} Cleaned form data
 */
export const cleanFormData = (formData, options = {}) => {
  const {
    removeEmpty = true,
    removeNull = true,
    removeUndefined = true,
    trimStrings = true
  } = options;

  const cleaned = {};

  Object.keys(formData).forEach(key => {
    let value = formData[key];

    // Trim strings
    if (trimStrings && typeof value === 'string') {
      value = value.trim();
    }

    // Skip empty values if configured
    if (removeEmpty && value === '') return;
    if (removeNull && value === null) return;
    if (removeUndefined && value === undefined) return;

    cleaned[key] = value;
  });

  return cleaned;
};

/**
 * Generate form field props for consistent styling and behavior
 * @param {string} fieldName - Field name
 * @param {Object} formState - Current form state
 * @param {Object} errors - Form errors
 * @param {Object} options - Additional options
 * @returns {Object} Field props
 */
export const getFieldProps = (fieldName, formState, errors = {}, options = {}) => {
  const {
    required = false,
    disabled = false,
    placeholder = '',
    helperText = ''
  } = options;

  return {
    name: fieldName,
    value: formState[fieldName] || '',
    error: !!errors[fieldName],
    helperText: errors[fieldName] || helperText,
    required,
    disabled,
    placeholder: placeholder || `Enter ${fieldName}`,
    'aria-invalid': !!errors[fieldName],
    'aria-describedby': errors[fieldName] ? `${fieldName}-error` : undefined
  };
};

/**
 * Create a form handler with validation
 * @param {Object} initialState - Initial form state
 * @param {Object} validationSchema - Validation schema
 * @param {Function} onSubmit - Submit handler
 * @returns {Object} Form handler object
 */
export const createFormHandler = (initialState, validationSchema, onSubmit) => {
  return {
    initialState,
    validationSchema,
    
    validate: (formData) => validateForm(formData, validationSchema),
    
    handleSubmit: async (formData) => {
      const validation = validateForm(formData, validationSchema);
      
      if (!validation.isValid) {
        throw new Error('Form validation failed');
      }
      
      const cleanedData = cleanFormData(formData);
      return await onSubmit(cleanedData);
    },
    
    getFieldProps: (fieldName, formState, errors, options) => 
      getFieldProps(fieldName, formState, errors, options)
  };
};

/**
 * Common validation schemas
 */
export const COMMON_SCHEMAS = {
  email: {
    type: 'email',
    required: true,
    maxLength: 255
  },
  
  password: {
    type: 'password',
    required: true,
    minLength: 8
  },
  
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z\s]+$/,
    patternMessage: 'Name can only contain letters and spaces'
  },
  
  url: {
    type: 'url',
    maxLength: 500
  },
  
  phone: {
    type: 'phone',
    maxLength: 20
  }
};

export default {
  validateField,
  validateForm,
  formatFormData,
  cleanFormData,
  getFieldProps,
  createFormHandler,
  COMMON_SCHEMAS,
  VALIDATION_RULES
};

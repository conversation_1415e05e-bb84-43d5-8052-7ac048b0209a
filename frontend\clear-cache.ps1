# @since 2024-1-1 to 2025-25-7
# PowerShell script to clear cache and optimize memory usage
# This script helps resolve ERR_INSUFFICIENT_RESOURCES errors

Write-Host "🧹 Starting cache cleanup and memory optimization..." -ForegroundColor Green

# Function to safely remove directory
function Remove-DirectorySafe {
    param($Path)
    if (Test-Path $Path) {
        try {
            Remove-Item $Path -Recurse -Force -ErrorAction Stop
            Write-Host "✅ Removed: $Path" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Could not remove $Path`: $_" -ForegroundColor Yellow
        }
    }
}

# Function to safely remove file
function Remove-FileSafe {
    param($Path)
    if (Test-Path $Path) {
        try {
            Remove-Item $Path -Force -ErrorAction Stop
            Write-Host "✅ Removed: $Path" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Could not remove $Path`: $_" -ForegroundColor Yellow
        }
    }
}

# 1. Clear Vite cache
Write-Host "`n1. Clearing Vite cache..." -ForegroundColor Cyan
Remove-DirectorySafe "node_modules\.vite"
Remove-DirectorySafe ".vite"

# 2. Clear dist folder
Write-Host "`n2. Clearing build output..." -ForegroundColor Cyan
Remove-DirectorySafe "dist"

# 3. Clear node_modules cache
Write-Host "`n3. Clearing node_modules cache..." -ForegroundColor Cyan
Remove-DirectorySafe "node_modules\.cache"

# 4. Clear package manager cache
Write-Host "`n4. Clearing package manager cache..." -ForegroundColor Cyan
try {
    npm cache clean --force
    Write-Host "✅ NPM cache cleared" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not clear NPM cache" -ForegroundColor Yellow
}

# 5. Clear browser cache files
Write-Host "`n5. Clearing browser cache files..." -ForegroundColor Cyan
$cacheFiles = @(
    "public\sw.js",
    "public\workbox-*.js",
    "public\manifest.json.backup"
)

foreach ($file in $cacheFiles) {
    Remove-FileSafe $file
}

Write-Host "`n🎉 Cache cleanup completed!" -ForegroundColor Green
Write-Host "`n💡 Tips to prevent memory issues:" -ForegroundColor Yellow
Write-Host "   • Close other browser tabs" -ForegroundColor White
Write-Host "   • Restart your browser" -ForegroundColor White
Write-Host "   • Use Chrome DevTools to monitor memory" -ForegroundColor White
Write-Host "   • Run: npm run dev:memory for optimized development" -ForegroundColor White

Write-Host "`n🚀 You can now start the development server:" -ForegroundColor Green
Write-Host "   npm run dev:memory" -ForegroundColor Cyan

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import VisualStyle from '../VisualStyle';

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      secondary: {
        main: '#00E4BC',
      },
      background: {
        default: '#FFFFFF',
      },
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('VisualStyle', () => {
  const mockVisualStyle = {
    photographyStyle: 'lifestyle',
    lighting: 'bright-airy',
    saturation: 10,
    contrast: 5,
    brightness: -5,
    warmth: 20,
    filters: [
      {
        name: 'Filter 1',
        settings: {
          saturation: 15,
          contrast: 10,
          brightness: 0,
          warmth: 25
        }
      },
      {
        name: 'Filter 2',
        settings: {
          saturation: -10,
          contrast: 20,
          brightness: 10,
          warmth: 5
        }
      }
    ]
  };

  const mockProps = {
    visualStyle: mockVisualStyle,
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders visual style settings correctly', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText(/Define your brand's visual style to ensure consistency/)).toBeInTheDocument();
  });

  test('displays photography style section', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Photography Style')).toBeInTheDocument();
    expect(screen.getByLabelText('Photography Style')).toBeInTheDocument();
  });

  test('displays lighting section', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Lighting')).toBeInTheDocument();
    expect(screen.getByLabelText('Lighting')).toBeInTheDocument();
  });

  test('displays image processing section', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Image Processing')).toBeInTheDocument();
    expect(screen.getByText('Saturation')).toBeInTheDocument();
    expect(screen.getByText('Contrast')).toBeInTheDocument();
    expect(screen.getByText('Brightness')).toBeInTheDocument();
    expect(screen.getByText('Warmth')).toBeInTheDocument();
  });

  test('displays filter presets section', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Filter Presets')).toBeInTheDocument();
    expect(screen.getByText(/Save and apply filter presets/)).toBeInTheDocument();
  });

  test('displays style preview section', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Style Preview')).toBeInTheDocument();
    expect(screen.getByText(/Preview how your visual style settings/)).toBeInTheDocument();
  });

  test('shows current photography style value', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const photographySelect = screen.getByLabelText('Photography Style');
    expect(photographySelect).toHaveValue('lifestyle');
  });

  test('shows current lighting value', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const lightingSelect = screen.getByLabelText('Lighting');
    expect(lightingSelect).toHaveValue('bright-airy');
  });

  test('handles photography style change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const photographySelect = screen.getByLabelText('Photography Style');
    await user.click(photographySelect);
    
    const documentaryOption = screen.getByText('Documentary');
    await user.click(documentaryOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        photographyStyle: 'documentary'
      });
    });
  });

  test('handles lighting change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const lightingSelect = screen.getByLabelText('Lighting');
    await user.click(lightingSelect);
    
    const darkMoodyOption = screen.getByText('Dark & Moody');
    await user.click(darkMoodyOption);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        lighting: 'dark-moody'
      });
    });
  });

  test('handles saturation slider change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const saturationSlider = screen.getByLabelText(/saturation-slider/i);
    
    // Simulate slider change
    fireEvent.change(saturationSlider, { target: { value: '25' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 25
      });
    });
  });

  test('handles contrast slider change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const contrastSlider = screen.getByLabelText(/contrast-slider/i);
    
    // Simulate slider change
    fireEvent.change(contrastSlider, { target: { value: '30' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        contrast: 30
      });
    });
  });

  test('handles brightness slider change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const brightnessSlider = screen.getByLabelText(/brightness-slider/i);
    
    // Simulate slider change
    fireEvent.change(brightnessSlider, { target: { value: '15' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        brightness: 15
      });
    });
  });

  test('handles warmth slider change', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const warmthSlider = screen.getByLabelText(/warmth-slider/i);
    
    // Simulate slider change
    fireEvent.change(warmthSlider, { target: { value: '40' } });

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        warmth: 40
      });
    });
  });

  test('displays existing filter presets', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Filter 1')).toBeInTheDocument();
    expect(screen.getByText('Filter 2')).toBeInTheDocument();
    expect(screen.getByText('Saturation: 15')).toBeInTheDocument();
    expect(screen.getByText('Contrast: 10')).toBeInTheDocument();
  });

  test('handles saving new filter preset', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const saveButton = screen.getByText('Save as Filter Preset');
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        filters: [
          ...mockVisualStyle.filters,
          {
            name: 'Filter 3',
            settings: {
              saturation: mockVisualStyle.saturation,
              contrast: mockVisualStyle.contrast,
              brightness: mockVisualStyle.brightness,
              warmth: mockVisualStyle.warmth
            }
          }
        ]
      });
    });
  });

  test('handles deleting filter preset', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const deleteButtons = screen.getAllByLabelText(/delete/i);
    await user.click(deleteButtons[0]);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        filters: [mockVisualStyle.filters[1]] // First filter removed
      });
    });
  });

  test('handles applying filter preset', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const applyButtons = screen.getAllByText('Apply');
    await user.click(applyButtons[0]);

    await waitFor(() => {
      expect(mockProps.onChange).toHaveBeenCalledWith({
        ...mockVisualStyle,
        saturation: 15,
        contrast: 10,
        brightness: 0,
        warmth: 25
      });
    });
  });

  test('shows empty state when no filters exist', () => {
    const propsWithoutFilters = {
      ...mockProps,
      visualStyle: {
        ...mockVisualStyle,
        filters: []
      }
    };

    render(
      <TestWrapper>
        <VisualStyle {...propsWithoutFilters} />
      </TestWrapper>
    );

    expect(screen.getByText(/No filter presets saved yet/)).toBeInTheDocument();
    expect(screen.getByText(/Adjust the image processing settings/)).toBeInTheDocument();
  });

  test('displays current style settings in preview', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Current Style Settings')).toBeInTheDocument();
    expect(screen.getByText('Lifestyle')).toBeInTheDocument();
    expect(screen.getByText('Bright & Airy')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument(); // saturation
    expect(screen.getByText('5')).toBeInTheDocument(); // contrast
    expect(screen.getByText('-5')).toBeInTheDocument(); // brightness
    expect(screen.getByText('20')).toBeInTheDocument(); // warmth
  });

  test('renders with default props when no visualStyle provided', () => {
    render(
      <TestWrapper>
        <VisualStyle onChange={vi.fn()} />
      </TestWrapper>
    );

    expect(screen.getByText('Visual Style')).toBeInTheDocument();
    expect(screen.getByText(/No filter presets saved yet/)).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    // Check for proper labels and roles
    expect(screen.getByRole('combobox', { name: /photography style/i })).toBeInTheDocument();
    expect(screen.getByRole('combobox', { name: /lighting/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /saturation-slider/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /contrast-slider/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /brightness-slider/i })).toBeInTheDocument();
    expect(screen.getByRole('slider', { name: /warmth-slider/i })).toBeInTheDocument();
  });

  test('displays info tooltips', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const infoButtons = screen.getAllByLabelText(/info/i);
    expect(infoButtons.length).toBeGreaterThan(0);
  });

  test('shows preview image with applied filters', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const previewImage = screen.getByAltText('Style Preview');
    expect(previewImage).toBeInTheDocument();
    expect(previewImage).toHaveStyle({
      filter: expect.stringContaining('saturate')
    });
  });

  test('displays photography style and lighting in preview overlay', () => {
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Lifestyle / Bright & Airy')).toBeInTheDocument();
  });

  test('shows all photography style options', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const photographySelect = screen.getByLabelText('Photography Style');
    await user.click(photographySelect);

    expect(screen.getByText('Lifestyle')).toBeInTheDocument();
    expect(screen.getByText('Documentary')).toBeInTheDocument();
    expect(screen.getByText('Studio/Professional')).toBeInTheDocument();
    expect(screen.getByText('Candid')).toBeInTheDocument();
    expect(screen.getByText('Abstract')).toBeInTheDocument();
    expect(screen.getByText('Minimalist')).toBeInTheDocument();
    expect(screen.getByText('Vintage')).toBeInTheDocument();
    expect(screen.getByText('Corporate')).toBeInTheDocument();
  });

  test('shows all lighting options', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <VisualStyle {...mockProps} />
      </TestWrapper>
    );

    const lightingSelect = screen.getByLabelText('Lighting');
    await user.click(lightingSelect);

    expect(screen.getByText('Bright & Airy')).toBeInTheDocument();
    expect(screen.getByText('Dark & Moody')).toBeInTheDocument();
    expect(screen.getByText('High Contrast')).toBeInTheDocument();
    expect(screen.getByText('Soft & Diffused')).toBeInTheDocument();
    expect(screen.getByText('Dramatic')).toBeInTheDocument();
    expect(screen.getByText('Natural')).toBeInTheDocument();
    expect(screen.getByText('Studio Lighting')).toBeInTheDocument();
  });
});

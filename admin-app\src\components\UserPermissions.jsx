// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Switch,
  FormControlLabel,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AdminPanelSettings as AdminIcon,
  Person as UserIcon,
  SupervisorAccount as ModeratorIcon,
} from '@mui/icons-material';
import api from '../api';

const UserPermissions = ({ userId, userEmail }) => {
  const [permissions, setPermissions] = useState([]);
  const [roles, setRoles] = useState([]);
  const [userRoles, setUserRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [openRoleDialog, setOpenRoleDialog] = useState(false);
  const [selectedRole, setSelectedRole] = useState('');

  useEffect(() => {
    if (userId) {
      fetchUserPermissions();
      fetchUserRoles();
      fetchAvailableRoles();
    }
  }, [userId]);

  const fetchUserPermissions = async () => {
    try {
      const response = await api.get(`/api/admin/users/${userId}/permissions`);
      setPermissions(response.data);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      setError('Failed to fetch user permissions');
    }
  };

  const fetchUserRoles = async () => {
    try {
      const response = await api.get(`/api/admin/users/${userId}/roles`);
      setUserRoles(response.data);
    } catch (error) {
      console.error('Error fetching user roles:', error);
    }
  };

  const fetchAvailableRoles = async () => {
    try {
      const response = await api.get('/api/admin/roles');
      setRoles(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching roles:', error);
      setLoading(false);
    }
  };

  const handlePermissionToggle = async (permissionId, enabled) => {
    try {
      await api.put(`/api/admin/users/${userId}/permissions/${permissionId}`, {
        enabled
      });
      fetchUserPermissions();
    } catch (error) {
      console.error('Error updating permission:', error);
      setError('Failed to update permission');
    }
  };

  const handleAddRole = async () => {
    if (!selectedRole) return;
    
    try {
      await api.post(`/api/admin/users/${userId}/roles`, {
        role_id: selectedRole
      });
      setOpenRoleDialog(false);
      setSelectedRole('');
      fetchUserRoles();
      fetchUserPermissions();
    } catch (error) {
      console.error('Error adding role:', error);
      setError('Failed to add role');
    }
  };

  const handleRemoveRole = async (roleId) => {
    try {
      await api.delete(`/api/admin/users/${userId}/roles/${roleId}`);
      fetchUserRoles();
      fetchUserPermissions();
    } catch (error) {
      console.error('Error removing role:', error);
      setError('Failed to remove role');
    }
  };

  const getRoleIcon = (roleName) => {
    switch (roleName?.toLowerCase()) {
      case 'admin':
        return <AdminIcon />;
      case 'moderator':
        return <ModeratorIcon />;
      default:
        return <UserIcon />;
    }
  };

  const getRoleColor = (roleName) => {
    switch (roleName?.toLowerCase()) {
      case 'admin':
        return 'error';
      case 'moderator':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* User Roles Section */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            User Roles
          </Typography>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={() => setOpenRoleDialog(true)}
            size="small"
          >
            Add Role
          </Button>
        </Box>
        
        <List>
          {userRoles.map((role) => (
            <ListItem key={role.id}>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center">
                    {getRoleIcon(role.name)}
                    <Typography sx={{ ml: 1 }}>{role.name}</Typography>
                  </Box>
                }
                secondary={role.description}
              />
              <ListItemSecondaryAction>
                <Chip 
                  label={role.name} 
                  color={getRoleColor(role.name)} 
                  size="small" 
                  sx={{ mr: 1 }}
                />
                <IconButton
                  edge="end"
                  onClick={() => handleRemoveRole(role.id)}
                  size="small"
                >
                  <DeleteIcon />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
          {userRoles.length === 0 && (
            <ListItem>
              <ListItemText
                primary="No roles assigned"
                secondary="This user has no special roles assigned"
              />
            </ListItem>
          )}
        </List>
      </Paper>

      {/* Permissions Table */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Individual Permissions
        </Typography>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Permission</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Source</TableCell>
                <TableCell align="center">Enabled</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {permissions.map((permission) => (
                <TableRow key={permission.id}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {permission.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {permission.description}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {permission.source === 'role' ? (
                      <Chip label={`Role: ${permission.role_name}`} size="small" color="info" />
                    ) : (
                      <Chip label="Direct" size="small" color="default" />
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <Switch
                      checked={permission.enabled}
                      onChange={(e) => handlePermissionToggle(permission.id, e.target.checked)}
                      disabled={permission.source === 'role'}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Add Role Dialog */}
      <Dialog open={openRoleDialog} onClose={() => setOpenRoleDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Role to User</DialogTitle>
        <DialogContent>
          <Typography variant="body2" gutterBottom>
            Assign a role to {userEmail}
          </Typography>
          <FormControl fullWidth margin="normal">
            <InputLabel>Select Role</InputLabel>
            <Select
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
              label="Select Role"
            >
              {roles
                .filter(role => !userRoles.some(userRole => userRole.id === role.id))
                .map((role) => (
                  <MenuItem key={role.id} value={role.id}>
                    <Box display="flex" alignItems="center">
                      {getRoleIcon(role.name)}
                      <Typography sx={{ ml: 1 }}>{role.name}</Typography>
                    </Box>
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenRoleDialog(false)}>Cancel</Button>
          <Button onClick={handleAddRole} variant="contained" disabled={!selectedRole}>
            Add Role
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserPermissions;

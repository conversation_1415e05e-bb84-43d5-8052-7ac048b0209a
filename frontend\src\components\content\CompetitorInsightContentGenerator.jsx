/**
 * @fileoverview CompetitorInsightContentGenerator - Enterprise-grade competitor insight content generation component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 * @description Advanced competitor insight content generator with AI-powered analysis, real-time tracking,
 * and comprehensive content generation capabilities following ACE Social platform standards.
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useImperativeHandle,
  forwardRef,
  memo
} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  useTheme,

  Alert,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Avatar,
  Switch,
  FormControlLabel
} from '@mui/material';
import { alpha, styled } from '@mui/material/styles';
import {
  AutoAwesome as AutoAwesomeIcon,
  Lightbulb as LightbulbIcon,
  TrendingUp as TrendingUpIcon,
  ContentCopy as ContentCopyIcon,
  Analytics as AnalyticsIcon,
  Psychology as PsychologyIcon,
  Insights as InsightsIcon,
  SmartToy as SmartToyIcon,
  Timeline as TimelineIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  ThumbUp as ThumbUpIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useAuth } from '../../contexts/AuthContext';
import { useCompetitors } from '../../contexts/CompetitorContext';
import { useNotification } from '../../contexts/NotificationContext';

// Enhanced common components
import {
  ErrorBoundary,
  EmptyState
} from '../common';

// Enhanced utility imports
import {
  announceToScreenReader,
  debounce
} from '../../utils/helpers';

// Enhanced API imports
import { generateContent } from '../../api/content';
import { getCompetitorRecommendations, analyzeCompetitorContent } from '../../api/competitors';

// Mock hooks for missing dependencies
const useAnalytics = () => ({
  trackEvent: () => {},
  trackError: () => {},
  trackPerformance: () => {}
});

const useLocalStorage = (key, defaultValue) => {
  const [value, setValue] = useState(defaultValue);
  return [value, setValue];
};

const useDebounce = (callback, delay) => {
  return useCallback((...args) => debounce(callback, delay)(...args), [callback, delay]);
};

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// Component variants
const CONTENT_GENERATOR_VARIANTS = {
  BASIC: 'basic',
  ADVANCED: 'advanced',
  ENTERPRISE: 'enterprise'
};

// Loading states
const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  GENERATING: 'generating',
  ANALYZING: 'analyzing',
  SAVING: 'saving',
  SUCCESS: 'success',
  ERROR: 'error'
};

// Content types with enhanced metadata
const CONTENT_TYPES = {
  POST: {
    id: 'post',
    label: 'Social Media Post',
    description: 'Short-form content for social platforms',
    maxLength: 280,
    platforms: ['linkedin', 'twitter', 'facebook', 'instagram']
  },
  ARTICLE: {
    id: 'article',
    label: 'Long-form Article',
    description: 'In-depth content for thought leadership',
    maxLength: 2000,
    platforms: ['linkedin', 'medium', 'blog']
  },
  VIDEO_SCRIPT: {
    id: 'video_script',
    label: 'Video Script',
    description: 'Script for video content creation',
    maxLength: 1500,
    platforms: ['youtube', 'tiktok', 'instagram', 'linkedin']
  },
  CAROUSEL: {
    id: 'carousel',
    label: 'Carousel Content',
    description: 'Multi-slide visual content',
    maxLength: 150,
    platforms: ['instagram', 'linkedin', 'facebook']
  },
  THREAD: {
    id: 'thread',
    label: 'Thread Series',
    description: 'Connected series of posts',
    maxLength: 280,
    platforms: ['twitter', 'threads', 'linkedin']
  },
  STORY: {
    id: 'story',
    label: 'Story Content',
    description: 'Ephemeral content for stories',
    maxLength: 100,
    platforms: ['instagram', 'facebook', 'linkedin']
  }
};

// Tone configurations
const TONE_CONFIGURATIONS = {
  PROFESSIONAL: {
    id: 'professional',
    label: 'Professional',
    description: 'Formal, business-appropriate tone',
    keywords: ['expertise', 'insights', 'analysis', 'strategy'],
    emoji: false
  },
  CASUAL: {
    id: 'casual',
    label: 'Casual',
    description: 'Relaxed, conversational tone',
    keywords: ['friendly', 'approachable', 'relatable', 'easy'],
    emoji: true
  },
  AUTHORITATIVE: {
    id: 'authoritative',
    label: 'Authoritative',
    description: 'Expert, confident tone',
    keywords: ['proven', 'definitive', 'expert', 'leading'],
    emoji: false
  },
  EDUCATIONAL: {
    id: 'educational',
    label: 'Educational',
    description: 'Teaching, informative tone',
    keywords: ['learn', 'understand', 'guide', 'tutorial'],
    emoji: true
  },
  INSPIRATIONAL: {
    id: 'inspirational',
    label: 'Inspirational',
    description: 'Motivating, uplifting tone',
    keywords: ['achieve', 'success', 'growth', 'potential'],
    emoji: true
  },
  HUMOROUS: {
    id: 'humorous',
    label: 'Humorous',
    description: 'Light-hearted, entertaining tone',
    keywords: ['fun', 'entertaining', 'witty', 'clever'],
    emoji: true
  }
};

// Platform configurations
const PLATFORM_CONFIGS = {
  LINKEDIN: {
    id: 'linkedin',
    label: 'LinkedIn',
    maxLength: 3000,
    hashtagLimit: 5,
    supportedTypes: ['post', 'article', 'carousel', 'video_script'],
    audience: 'professional'
  },
  TWITTER: {
    id: 'twitter',
    label: 'Twitter/X',
    maxLength: 280,
    hashtagLimit: 3,
    supportedTypes: ['post', 'thread'],
    audience: 'general'
  },
  INSTAGRAM: {
    id: 'instagram',
    label: 'Instagram',
    maxLength: 2200,
    hashtagLimit: 30,
    supportedTypes: ['post', 'story', 'carousel'],
    audience: 'visual'
  },
  FACEBOOK: {
    id: 'facebook',
    label: 'Facebook',
    maxLength: 63206,
    hashtagLimit: 5,
    supportedTypes: ['post', 'article', 'story'],
    audience: 'general'
  },
  TIKTOK: {
    id: 'tiktok',
    label: 'TikTok',
    maxLength: 150,
    hashtagLimit: 5,
    supportedTypes: ['video_script', 'post'],
    audience: 'young'
  },
  YOUTUBE: {
    id: 'youtube',
    label: 'YouTube',
    maxLength: 5000,
    hashtagLimit: 15,
    supportedTypes: ['video_script', 'article'],
    audience: 'diverse'
  }
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Component configuration
const COMPONENT_CONFIG = {
  MAX_COMPETITORS: 10,
  MAX_RECOMMENDATIONS: 20,
  MAX_CONTENT_HISTORY: 50,
  DEBOUNCE_DELAY: 300,
  ANIMATION_DURATION: 200,
  AUTO_SAVE_INTERVAL: 30000,
  SUPPORTED_FORMATS: ['text', 'html', 'markdown'],
  PERFORMANCE_THRESHOLDS: {
    GENERATION_TIME: 10000,
    ANALYSIS_TIME: 5000,
    RECOMMENDATION_TIME: 3000
  }
};

// Insight categories
const INSIGHT_CATEGORIES = {
  CONTENT_STRATEGY: {
    id: 'content_strategy',
    label: 'Content Strategy',
    icon: AnalyticsIcon,
    color: 'primary'
  },
  POSTING_SCHEDULE: {
    id: 'posting_schedule',
    label: 'Posting Schedule',
    icon: TimelineIcon,
    color: 'secondary'
  },
  ENGAGEMENT: {
    id: 'engagement',
    label: 'Engagement',
    icon: ThumbUpIcon,
    color: 'success'
  },
  HASHTAG_STRATEGY: {
    id: 'hashtag_strategy',
    label: 'Hashtag Strategy',
    icon: SearchIcon,
    color: 'info'
  },
  VISUAL_STYLE: {
    id: 'visual_style',
    label: 'Visual Style',
    icon: VisibilityIcon,
    color: 'warning'
  },
  AUDIENCE_TARGETING: {
    id: 'audience_targeting',
    label: 'Audience Targeting',
    icon: PsychologyIcon,
    color: 'error'
  }
};

// ===========================
// STYLED COMPONENTS
// ===========================

// Enhanced content generator card
const ContentGeneratorCard = styled(Card)(({ theme }) => ({
  background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.7)})`,
  backdropFilter: 'blur(10px)',
  border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
  borderRadius: theme.shape.borderRadius * 3,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: COMPONENT_CONFIG.ANIMATION_DURATION,
  }),
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: `0 8px 24px ${alpha(theme.palette.common.black, 0.12)}`,
  }
}));

// Insight recommendation card
const InsightCard = styled(Paper)(({ theme, priority }) => ({
  padding: theme.spacing(2),
  marginBottom: theme.spacing(2),
  borderLeft: `4px solid ${
    priority === 'high' ? theme.palette.error.main :
    priority === 'medium' ? theme.palette.warning.main :
    theme.palette.info.main
  }`,
  borderRadius: theme.shape.borderRadius * 2,
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: COMPONENT_CONFIG.ANIMATION_DURATION,
  }),
  '&:hover': {
    transform: 'translateX(4px)',
    boxShadow: theme.shadows[4],
  }
}));

// ===========================
// MAIN COMPONENT
// ===========================

/**
 * CompetitorInsightContentGenerator - Enterprise-grade competitor insight content generation component
 *
 * @param {Object} props - Component props
 * @param {string} props.variant - Component variant (basic, advanced, enterprise)
 * @param {boolean} props.enableAnalytics - Enable analytics tracking
 * @param {boolean} props.enableAccessibility - Enable accessibility features
 * @param {boolean} props.enableRealTimeAnalysis - Enable real-time competitor analysis
 * @param {boolean} props.enableBulkGeneration - Enable bulk content generation
 * @param {boolean} props.showInsightCategories - Show insight category filters
 * @param {boolean} props.showPerformanceMetrics - Show performance metrics
 * @param {boolean} props.showContentHistory - Show content generation history
 * @param {Function} props.onContentGenerated - Callback when content is generated
 * @param {Function} props.onInsightApplied - Callback when insight is applied
 * @param {Function} props.onError - Error callback
 * @param {string} props.userPlan - User subscription plan
 * @param {string} props.testId - Test identifier
 * @param {string} props.ariaLabel - Accessibility label
 * @param {boolean} props.announceChanges - Announce changes to screen readers
 * @param {Array} props.initialCompetitors - Initial competitor selection
 * @param {Object} props.defaultSettings - Default generation settings
 */
const CompetitorInsightContentGenerator = memo(forwardRef(({
  // Basic props
  variant = 'advanced',

  // Enhanced props
  enableAnalytics = true,
  enableAccessibility = true,
  enableRealTimeAnalysis = true,

  // Display props
  showPerformanceMetrics = true,

  // Callback props
  onContentGenerated,
  onInsightApplied,
  onError,

  // User context props
  userPlan = 'creator',

  // Testing props
  testId = 'competitor-insight-content-generator',

  // Accessibility props
  ariaLabel,
  announceChanges = true,

  // Data props
  initialCompetitors = [],
  defaultSettings = {}
}, ref) => {
  // ===========================
  // HOOKS & CONTEXT
  // ===========================

  const theme = useTheme();

  // Enhanced hooks
  const { user } = useAuth();
  const { competitors, loading: competitorsLoading } = useCompetitors();
  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { trackEvent, trackError } = useAnalytics();

  // Local storage for preferences
  const [savedPreferences, setSavedPreferences] = useLocalStorage('content-generator-preferences', {
    platform: defaultSettings.platform || 'linkedin',
    contentType: defaultSettings.contentType || 'post',
    tone: defaultSettings.tone || 'professional',
    includeHashtags: defaultSettings.includeHashtags !== undefined ? defaultSettings.includeHashtags : true,
    generateImage: defaultSettings.generateImage !== undefined ? defaultSettings.generateImage : true
  });

  // ===========================
  // STATE MANAGEMENT
  // ===========================

  // Core generation state
  const [selectedCompetitors, setSelectedCompetitors] = useState(initialCompetitors || []);
  const [loadingState, setLoadingState] = useState(LOADING_STATES.IDLE);
  const [generatedContent, setGeneratedContent] = useState(null);
  const [contentHistory, setContentHistory] = useState([]);

  // Form state
  const [platform, setPlatform] = useState(savedPreferences.platform);
  const [contentType, setContentType] = useState(savedPreferences.contentType);
  const [tone, setTone] = useState(savedPreferences.tone);
  const [topic, setTopic] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [includeHashtags, setIncludeHashtags] = useState(savedPreferences.includeHashtags);
  const [generateImage, setGenerateImage] = useState(savedPreferences.generateImage);
  const [customPrompt, setCustomPrompt] = useState('');
  const [contentLength, setContentLength] = useState('medium');

  // Insight state
  const [recommendations, setRecommendations] = useState([]);
  const [insightCategories] = useState(Object.keys(INSIGHT_CATEGORIES));
  const [selectedInsightCategory] = useState('all');
  const [competitorAnalysis, setCompetitorAnalysis] = useState({});

  // UI state
  const [filterText] = useState('');
  const [sortBy] = useState('priority');
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Performance tracking
  const [performanceMetrics, setPerformanceMetrics] = useState({
    generationTime: 0,
    analysisTime: 0,
    recommendationTime: 0,
    successRate: 0
  });

  // Error handling
  const [validationErrors, setValidationErrors] = useState([]);
  const [lastError, setLastError] = useState(null);

  // Refs for performance and accessibility
  const formRef = useRef(null);
  const topicInputRef = useRef(null);
  const generateButtonRef = useRef(null);

  // Debounced functions
  const debouncedTrackEvent = useDebounce(trackEvent, 300);
  const debouncedSavePreferences = useDebounce(setSavedPreferences, 1000);
  const debouncedAnalyzeCompetitors = useDebounce((competitors) => {
    if (enableRealTimeAnalysis && competitors.length > 0) {
      analyzeSelectedCompetitors(competitors);
    }
  }, 500);

  // Expose ref methods
  useImperativeHandle(ref, () => ({
    generateContent: () => handleGenerateContent(),
    clearContent: () => handleClearContent(),
    exportContent: () => handleExportContent(),
    refreshInsights: () => loadRecommendations(),
    focus: () => topicInputRef.current?.focus()
  }), [handleGenerateContent, handleClearContent, handleExportContent, loadRecommendations]);

  // ===========================
  // COMPUTED VALUES
  // ===========================

  // Filter recommendations based on selected category and search
  const filteredRecommendations = useMemo(() => {
    let filtered = recommendations;

    // Filter by category
    if (selectedInsightCategory !== 'all') {
      filtered = filtered.filter(rec => rec.area === selectedInsightCategory);
    }

    // Filter by search text
    if (filterText) {
      const searchLower = filterText.toLowerCase();
      filtered = filtered.filter(rec =>
        rec.recommendation.toLowerCase().includes(searchLower) ||
        rec.reasoning.toLowerCase().includes(searchLower)
      );
    }

    // Sort recommendations
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'priority': {
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        case 'category':
          return a.area.localeCompare(b.area);
        case 'relevance':
          return (b.confidence || 0) - (a.confidence || 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [recommendations, selectedInsightCategory, filterText, sortBy]);

  // Get platform-specific configuration
  const platformConfig = useMemo(() => {
    return PLATFORM_CONFIGS[platform.toUpperCase()] || PLATFORM_CONFIGS.LINKEDIN;
  }, [platform]);



  // Calculate content generation readiness
  const isReadyToGenerate = useMemo(() => {
    return topic.trim().length > 0 &&
           selectedCompetitors.length > 0 &&
           loadingState === LOADING_STATES.IDLE;
  }, [topic, selectedCompetitors, loadingState]);

  // Get available content types for selected platform
  const availableContentTypes = useMemo(() => {
    return Object.values(CONTENT_TYPES).filter(type =>
      type.platforms.includes(platform)
    );
  }, [platform]);

  // ===========================
  // EFFECTS
  // ===========================

  // Load initial data and set up component
  useEffect(() => {
    if (initialCompetitors.length > 0) {
      setSelectedCompetitors(initialCompetitors);
    }
  }, [initialCompetitors]);

  // Load recommendations when competitors change
  useEffect(() => {
    if (selectedCompetitors.length > 0) {
      loadRecommendations();
      debouncedAnalyzeCompetitors(selectedCompetitors);
    } else {
      setRecommendations([]);
      setCompetitorAnalysis({});
    }
  }, [selectedCompetitors, debouncedAnalyzeCompetitors, loadRecommendations]);

  // Save preferences when they change
  useEffect(() => {
    const preferences = {
      platform,
      contentType,
      tone,
      includeHashtags,
      generateImage
    };

    debouncedSavePreferences(preferences);
  }, [platform, contentType, tone, includeHashtags, generateImage, debouncedSavePreferences]);

  // Announce changes to screen readers
  useEffect(() => {
    if (announceChanges && enableAccessibility) {
      if (loadingState === LOADING_STATES.SUCCESS && generatedContent) {
        announceToScreenReader('Content generated successfully');
      } else if (loadingState === LOADING_STATES.ERROR && lastError) {
        announceToScreenReader(`Error: ${lastError.message}`);
      }
    }
  }, [loadingState, generatedContent, lastError, announceChanges, enableAccessibility]);

  // ===========================
  // EVENT HANDLERS
  // ===========================

  /**
   * Handle analytics tracking
   */
  const handleAnalytics = useCallback((eventName, data = {}) => {
    if (!enableAnalytics) return;

    const analyticsData = {
      ...data,
      component: 'CompetitorInsightContentGenerator',
      variant,
      userPlan,
      competitorsCount: selectedCompetitors.length,
      platform,
      contentType,
      tone
    };

    debouncedTrackEvent(eventName, analyticsData);
  }, [enableAnalytics, variant, userPlan, selectedCompetitors.length, platform, contentType, tone, debouncedTrackEvent]);

  /**
   * Handle error reporting
   */
  const handleError = useCallback((error, context = {}) => {
    const errorData = {
      error: error.message || error,
      context: {
        ...context,
        component: 'CompetitorInsightContentGenerator',
        variant,
        userPlan,
        competitorsCount: selectedCompetitors.length
      }
    };

    console.error('CompetitorInsightContentGenerator Error:', errorData);

    if (enableAnalytics) {
      trackError(errorData);
    }

    setLastError(error);
    setLoadingState(LOADING_STATES.ERROR);
    onError?.(errorData);

    // Show user-friendly error message
    showErrorNotification(
      error.userMessage ||
      error.message ||
      'An unexpected error occurred with content generation.'
    );
  }, [variant, userPlan, selectedCompetitors.length, enableAnalytics, trackError, onError, showErrorNotification]);

  /**
   * Handle competitor selection
   */
  const handleCompetitorChange = useCallback((event) => {
    const newSelection = event.target.value;
    setSelectedCompetitors(newSelection);
    handleAnalytics('competitors_selected', {
      count: newSelection.length,
      competitors: newSelection
    });
  }, [handleAnalytics]);



  /**
   * Handle platform change
   */
  const handlePlatformChange = useCallback((event) => {
    const newPlatform = event.target.value;
    setPlatform(newPlatform);

    // Auto-adjust content type if not supported by new platform
    const platformConfig = PLATFORM_CONFIGS[newPlatform.toUpperCase()];
    if (platformConfig && !platformConfig.supportedTypes.includes(contentType)) {
      setContentType(platformConfig.supportedTypes[0]);
    }

    handleAnalytics('platform_changed', { platform: newPlatform });
  }, [contentType, handleAnalytics]);

  /**
   * Handle content type change
   */
  const handleContentTypeChange = useCallback((event) => {
    const newContentType = event.target.value;
    setContentType(newContentType);
    handleAnalytics('content_type_changed', { contentType: newContentType });
  }, [handleAnalytics]);

  /**
   * Handle tone change
   */
  const handleToneChange = useCallback((event) => {
    const newTone = event.target.value;
    setTone(newTone);
    handleAnalytics('tone_changed', { tone: newTone });
  }, [handleAnalytics]);

  /**
   * Load recommendations from API
   */
  const loadRecommendations = useCallback(async () => {
    if (selectedCompetitors.length === 0) {
      setRecommendations([]);
      return;
    }

    setLoadingState(LOADING_STATES.ANALYZING);
    const startTime = Date.now();

    try {
      const data = await getCompetitorRecommendations(
        selectedCompetitors,
        insightCategories
      );

      setRecommendations(data.recommendations || []);

      // Update performance metrics
      const analysisTime = Date.now() - startTime;
      setPerformanceMetrics(prev => ({
        ...prev,
        recommendationTime: analysisTime
      }));

      handleAnalytics('recommendations_loaded', {
        count: data.recommendations?.length || 0,
        analysisTime,
        categories: insightCategories
      });

    } catch (error) {
      handleError(error, { action: 'loadRecommendations' });
    } finally {
      setLoadingState(LOADING_STATES.IDLE);
    }
  }, [selectedCompetitors, insightCategories, handleAnalytics, handleError]);

  /**
   * Analyze selected competitors for insights
   */
  const analyzeSelectedCompetitors = useCallback(async (competitors) => {
    if (!enableRealTimeAnalysis || competitors.length === 0) return;

    const startTime = Date.now();

    try {
      const analysisPromises = competitors.map(competitorId =>
        analyzeCompetitorContent(competitorId, {
          includeMetrics: showPerformanceMetrics,
          categories: insightCategories
        })
      );

      const results = await Promise.all(analysisPromises);

      const analysisData = {};
      results.forEach((result, index) => {
        if (result) {
          analysisData[competitors[index]] = result;
        }
      });

      setCompetitorAnalysis(analysisData);

      // Update performance metrics
      const analysisTime = Date.now() - startTime;
      setPerformanceMetrics(prev => ({
        ...prev,
        analysisTime
      }));

      handleAnalytics('competitors_analyzed', {
        count: competitors.length,
        analysisTime
      });

    } catch (error) {
      console.error('Error analyzing competitors:', error);
      // Don't show error for background analysis
    }
  }, [enableRealTimeAnalysis, showPerformanceMetrics, insightCategories, handleAnalytics]);

  /**
   * Apply recommendation to form
   */
  const applyRecommendation = useCallback((recommendation) => {
    if (recommendation.area === 'content_strategy') {
      // Extract content type or topic from recommendation
      const contentTypeMatch = recommendation.recommendation.match(/(?:use|create|focus on) (video|image|carousel|text) content/i);
      if (contentTypeMatch) {
        const extractedType = contentTypeMatch[1].toLowerCase();
        if (availableContentTypes.some(type => type.id === extractedType)) {
          setContentType(extractedType);
        }
      }

      // Set topic based on recommendation
      if (recommendation.recommendation.includes('educational')) {
        setTopic(prev => prev || 'Educational tips about industry best practices');
      } else if (recommendation.recommendation.includes('tutorial')) {
        setTopic(prev => prev || 'Step-by-step tutorial on solving common problems');
      } else if (recommendation.recommendation.includes('case study')) {
        setTopic(prev => prev || 'Case study analysis and insights');
      }
    } else if (recommendation.area === 'hashtag_strategy') {
      setIncludeHashtags(true);
    } else if (recommendation.area === 'visual_style') {
      setGenerateImage(true);
    }

    // Track recommendation application
    handleAnalytics('recommendation_applied', {
      area: recommendation.area,
      priority: recommendation.priority
    });

    onInsightApplied?.(recommendation);

    showSuccessNotification('Recommendation applied to form');
  }, [availableContentTypes, handleAnalytics, onInsightApplied, showSuccessNotification]);

  /**
   * Handle content generation
   */
  const handleGenerateContent = useCallback(async () => {
    // Validation
    const errors = [];
    if (!topic.trim()) {
      errors.push('Topic is required');
    }
    if (selectedCompetitors.length === 0) {
      errors.push('At least one competitor must be selected');
    }
    if (topic.length > platformConfig.maxLength) {
      errors.push(`Topic exceeds maximum length for ${platform}`);
    }

    if (errors.length > 0) {
      setValidationErrors(errors);
      showErrorNotification(errors[0]);
      return;
    }

    setValidationErrors([]);
    setLoadingState(LOADING_STATES.GENERATING);
    const startTime = Date.now();

    try {
      // Prepare enhanced request data
      const requestData = {
        topic: topic.trim(),
        tone: tone,
        platform: platform,
        content_type: contentType,
        include_hashtags: includeHashtags,
        target_audience: targetAudience || undefined,
        generate_image: generateImage,
        include_headline_on_image: true,
        custom_prompt: customPrompt || undefined,
        content_length: contentLength,
        competitor_insights: {
          selected_competitors: selectedCompetitors,
          recommendations: filteredRecommendations.slice(0, 5),
          analysis: competitorAnalysis
        },
        user_preferences: {
          brand_voice: user?.branding_preferences?.brand_voice,
          brand_style: user?.branding_preferences?.style
        }
      };

      // Send request to API
      const response = await generateContent(requestData);

      // Update state with generated content
      setGeneratedContent(response);

      // Add to content history
      const historyItem = {
        id: Date.now(),
        content: response,
        settings: requestData,
        timestamp: new Date().toISOString(),
        competitors: selectedCompetitors
      };

      setContentHistory(prev => [historyItem, ...prev.slice(0, COMPONENT_CONFIG.MAX_CONTENT_HISTORY - 1)]);

      // Update performance metrics
      const generationTime = Date.now() - startTime;
      setPerformanceMetrics(prev => ({
        ...prev,
        generationTime,
        successRate: ((prev.successRate * 9) + 100) / 10 // Rolling average
      }));

      setLoadingState(LOADING_STATES.SUCCESS);
      showSuccessNotification('Content generated successfully!');

      handleAnalytics('content_generated', {
        generationTime,
        contentType,
        platform,
        tone,
        competitorsCount: selectedCompetitors.length,
        hasCustomPrompt: !!customPrompt
      });

      onContentGenerated?.(response, requestData);

    } catch (error) {
      handleError(error, { action: 'generateContent' });

      // Update performance metrics
      setPerformanceMetrics(prev => ({
        ...prev,
        successRate: ((prev.successRate * 9) + 0) / 10 // Rolling average
      }));
    } finally {
      setLoadingState(LOADING_STATES.IDLE);
    }
  }, [
    topic, selectedCompetitors, platformConfig, platform, tone, contentType,
    includeHashtags, targetAudience, generateImage, customPrompt, contentLength,
    filteredRecommendations, competitorAnalysis, user, showErrorNotification,
    showSuccessNotification, handleAnalytics, onContentGenerated, handleError
  ]);

  /**
   * Handle clearing content
   */
  const handleClearContent = useCallback(() => {
    setGeneratedContent(null);
    setTopic('');
    setTargetAudience('');
    setCustomPrompt('');
    setValidationErrors([]);
    setLastError(null);

    handleAnalytics('content_cleared');

    if (topicInputRef.current) {
      topicInputRef.current.focus();
    }
  }, [handleAnalytics]);

  /**
   * Handle exporting content
   */
  const handleExportContent = useCallback(() => {
    if (!generatedContent) return;

    const exportData = {
      content: generatedContent,
      settings: {
        platform,
        contentType,
        tone,
        topic,
        targetAudience,
        competitors: selectedCompetitors
      },
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `content-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    handleAnalytics('content_exported');
    showSuccessNotification('Content exported successfully');
  }, [generatedContent, platform, contentType, tone, topic, targetAudience, selectedCompetitors, handleAnalytics, showSuccessNotification]);

  // ===========================
  // MAIN COMPONENT RENDER
  // ===========================

  return (
    <ErrorBoundary
      fallback={(error, retry) => (
        <Alert
          severity="error"
          action={<Button onClick={retry}>Retry</Button>}
        >
          Failed to load content generator: {error.message}
        </Alert>
      )}
    >
      <Container
        maxWidth="xl"
        sx={{ py: 3 }}
        data-testid={testId}
        role="main"
        aria-label={ariaLabel || 'Competitor insight content generator'}
      >
        {/* Header Section */}
        <Paper elevation={2} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 600,
                  color: 'primary.main',
                  mb: 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <SmartToyIcon />
                AI Content Generator
              </Typography>

              <Typography variant="body1" color="text.secondary">
                Generate high-performing content based on competitor insights and AI analysis
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              {showPerformanceMetrics && (
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="primary.main">
                    {performanceMetrics.successRate.toFixed(0)}%
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Success Rate
                  </Typography>
                </Box>
              )}

              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => loadRecommendations()}
                disabled={loadingState !== LOADING_STATES.IDLE}
              >
                Refresh
              </Button>
            </Box>
          </Box>

          {/* Performance Metrics */}
          {showPerformanceMetrics && (
            <Box sx={{ display: 'flex', gap: 3, mt: 2 }}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Avg Generation Time
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {(performanceMetrics.generationTime / 1000).toFixed(1)}s
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Content Generated
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {contentHistory.length}
                </Typography>
              </Box>
            </Box>
          )}
        </Paper>

        {/* Main Content */}
        <Grid container spacing={3}>
          {/* Form Section */}
          <Grid item xs={12} lg={6}>
            <ContentGeneratorCard>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <AutoAwesomeIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">
                    Content Generation Settings
                  </Typography>
                </Box>

                <Box component="form" ref={formRef} sx={{ mt: 2 }}>
                  {/* Competitor Selection */}
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Select Competitors for Analysis</InputLabel>
                    <Select
                      multiple
                      value={selectedCompetitors}
                      onChange={handleCompetitorChange}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => {
                            const competitor = competitors.find(c => c.id === value);
                            return (
                              <Chip
                                key={value}
                                label={competitor ? competitor.name : value}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                            );
                          })}
                        </Box>
                      )}
                      disabled={competitorsLoading || loadingState !== LOADING_STATES.IDLE}
                      error={validationErrors.some(error => error.includes('competitor'))}
                    >
                      {competitorsLoading ? (
                        <MenuItem disabled>
                          <CircularProgress size={20} sx={{ mr: 1 }} />
                          Loading competitors...
                        </MenuItem>
                      ) : competitors.length === 0 ? (
                        <MenuItem disabled>
                          <Box sx={{ textAlign: 'center', py: 2 }}>
                            <Typography variant="body2" color="text.secondary">
                              No competitors found. Add competitors first.
                            </Typography>
                          </Box>
                        </MenuItem>
                      ) : (
                        competitors.map((competitor) => (
                          <MenuItem key={competitor.id} value={competitor.id}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Avatar sx={{ width: 24, height: 24 }}>
                                {competitor.name.charAt(0)}
                              </Avatar>
                              <Box>
                                <Typography variant="body2">{competitor.name}</Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {competitor.platform || 'Multi-platform'}
                                </Typography>
                              </Box>
                            </Box>
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>

                  {/* Platform Selection */}
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Platform</InputLabel>
                        <Select
                          value={platform}
                          onChange={handlePlatformChange}
                          disabled={loadingState !== LOADING_STATES.IDLE}
                        >
                          {Object.values(PLATFORM_CONFIGS).map((config) => (
                            <MenuItem key={config.id} value={config.id}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography>{config.label}</Typography>
                                <Chip
                                  size="small"
                                  label={config.audience}
                                  variant="outlined"
                                />
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Content Type</InputLabel>
                        <Select
                          value={contentType}
                          onChange={handleContentTypeChange}
                          disabled={loadingState !== LOADING_STATES.IDLE}
                        >
                          {availableContentTypes.map((type) => (
                            <MenuItem key={type.id} value={type.id}>
                              <Box>
                                <Typography variant="body2">{type.label}</Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {type.description}
                                </Typography>
                              </Box>
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>

                  {/* Tone Selection */}
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Tone & Style</InputLabel>
                    <Select
                      value={tone}
                      onChange={handleToneChange}
                      disabled={loadingState !== LOADING_STATES.IDLE}
                    >
                      {Object.values(TONE_CONFIGURATIONS).map((toneConfig) => (
                        <MenuItem key={toneConfig.id} value={toneConfig.id}>
                          <Box>
                            <Typography variant="body2">{toneConfig.label}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              {toneConfig.description}
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  {/* Topic Input */}
                  <TextField
                    fullWidth
                    label="Content Topic"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    margin="normal"
                    required
                    multiline
                    rows={2}
                    placeholder="What would you like to create content about?"
                    helperText={`${topic.length}/${platformConfig.maxLength} characters`}
                    error={topic.length > platformConfig.maxLength || validationErrors.some(error => error.includes('Topic'))}
                    disabled={loadingState !== LOADING_STATES.IDLE}
                    ref={topicInputRef}
                    sx={{ mb: 3 }}
                  />

                  {/* Target Audience */}
                  <TextField
                    fullWidth
                    label="Target Audience"
                    value={targetAudience}
                    onChange={(e) => setTargetAudience(e.target.value)}
                    margin="normal"
                    placeholder="Describe your target audience (optional)"
                    helperText="Who is your ideal audience for this content?"
                    disabled={loadingState !== LOADING_STATES.IDLE}
                    sx={{ mb: 3 }}
                  />

                  {/* Advanced Options */}
                  <Accordion expanded={showAdvancedOptions} onChange={() => setShowAdvancedOptions(!showAdvancedOptions)}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography variant="subtitle2">Advanced Options</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        {/* Custom Prompt */}
                        <TextField
                          fullWidth
                          label="Custom Instructions"
                          value={customPrompt}
                          onChange={(e) => setCustomPrompt(e.target.value)}
                          multiline
                          rows={3}
                          placeholder="Add specific instructions for content generation..."
                          disabled={loadingState !== LOADING_STATES.IDLE}
                        />

                        {/* Content Length */}
                        <FormControl fullWidth>
                          <InputLabel>Content Length</InputLabel>
                          <Select
                            value={contentLength}
                            onChange={(e) => setContentLength(e.target.value)}
                            disabled={loadingState !== LOADING_STATES.IDLE}
                          >
                            <MenuItem value="short">Short (Quick read)</MenuItem>
                            <MenuItem value="medium">Medium (Standard)</MenuItem>
                            <MenuItem value="long">Long (In-depth)</MenuItem>
                          </Select>
                        </FormControl>

                        {/* Options Switches */}
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={includeHashtags}
                                onChange={(e) => setIncludeHashtags(e.target.checked)}
                                disabled={loadingState !== LOADING_STATES.IDLE}
                              />
                            }
                            label={`Include Hashtags (max ${platformConfig.hashtagLimit})`}
                          />

                          <FormControlLabel
                            control={
                              <Switch
                                checked={generateImage}
                                onChange={(e) => setGenerateImage(e.target.checked)}
                                disabled={loadingState !== LOADING_STATES.IDLE}
                              />
                            }
                            label="Generate AI Image"
                          />
                        </Box>
                      </Box>
                    </AccordionDetails>
                  </Accordion>

                  {/* Validation Errors */}
                  {validationErrors.length > 0 && (
                    <Alert severity="error" sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Please fix the following issues:
                      </Typography>
                      <List dense>
                        {validationErrors.map((error, index) => (
                          <ListItem key={index} sx={{ py: 0 }}>
                            <ListItemText
                              primary={error}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Alert>
                  )}

                  {/* Generate Button */}
                  <Button
                    variant="contained"
                    fullWidth
                    size="large"
                    onClick={handleGenerateContent}
                    disabled={!isReadyToGenerate || loadingState !== LOADING_STATES.IDLE}
                    startIcon={
                      loadingState === LOADING_STATES.GENERATING ?
                        <CircularProgress size={20} /> :
                        <AutoAwesomeIcon />
                    }
                    ref={generateButtonRef}
                    sx={{
                      mt: 3,
                      background: `linear-gradient(45deg, ${ACE_COLORS.PURPLE}, ${ACE_COLORS.YELLOW})`,
                      '&:hover': {
                        background: `linear-gradient(45deg, ${alpha(ACE_COLORS.PURPLE, 0.8)}, ${alpha(ACE_COLORS.YELLOW, 0.8)})`,
                      },
                      '&:disabled': {
                        background: theme.palette.action.disabledBackground
                      }
                    }}
                  >
                    {loadingState === LOADING_STATES.GENERATING ? 'Generating Content...' : 'Generate AI Content'}
                  </Button>

                  {/* Action Buttons */}
                  {generatedContent && (
                    <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                      <Button
                        variant="outlined"
                        startIcon={<SaveIcon />}
                        onClick={handleExportContent}
                        disabled={loadingState !== LOADING_STATES.IDLE}
                      >
                        Export
                      </Button>

                      <Button
                        variant="outlined"
                        startIcon={<ClearIcon />}
                        onClick={handleClearContent}
                        disabled={loadingState !== LOADING_STATES.IDLE}
                      >
                        Clear
                      </Button>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </ContentGeneratorCard>
          </Grid>

          {/* Insights and Preview Section */}
          <Grid item xs={12} lg={6}>
            <Grid container spacing={3}>
              {/* Competitor Insights */}
              <Grid item xs={12}>
                <ContentGeneratorCard>
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <InsightsIcon color="warning" sx={{ mr: 1 }} />
                        <Typography variant="h6">
                          Competitor Insights
                        </Typography>
                      </Box>
                    </Box>

                    {/* Loading State */}
                    {loadingState === LOADING_STATES.ANALYZING && (
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
                        <CircularProgress sx={{ mb: 2 }} />
                        <Typography variant="body2" color="text.secondary">
                          Analyzing competitor strategies...
                        </Typography>
                      </Box>
                    )}

                    {/* Empty State */}
                    {loadingState !== LOADING_STATES.ANALYZING && filteredRecommendations.length === 0 && (
                      <EmptyState
                        title={selectedCompetitors.length === 0 ? "Select Competitors" : "No Insights Found"}
                        description={
                          selectedCompetitors.length === 0
                            ? "Choose competitors to analyze their successful strategies"
                            : "No insights available for selected competitors"
                        }
                        icon={<LightbulbIcon sx={{ fontSize: 48 }} />}
                        actionText={selectedCompetitors.length === 0 ? "Select Competitors" : "Refresh Analysis"}
                        onActionClick={() => selectedCompetitors.length === 0 ? null : loadRecommendations()}
                      />
                    )}

                    {/* Insights List */}
                    {filteredRecommendations.length > 0 && (
                      <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
                        {filteredRecommendations.map((recommendation, index) => (
                          <InsightCard
                            key={index}
                            priority={recommendation.priority}
                            elevation={1}
                          >
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                              <Box sx={{ flex: 1 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                  {recommendation.recommendation}
                                </Typography>
                                <Typography variant="body2" color="text.secondary" paragraph>
                                  {recommendation.reasoning}
                                </Typography>
                              </Box>

                              <Tooltip title="Apply this insight">
                                <IconButton
                                  size="small"
                                  onClick={() => applyRecommendation(recommendation)}
                                  color="primary"
                                  sx={{ ml: 1 }}
                                >
                                  <ContentCopyIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Box>

                            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                              <Chip
                                size="small"
                                label={INSIGHT_CATEGORIES[recommendation.area]?.label || recommendation.area}
                                color={INSIGHT_CATEGORIES[recommendation.area]?.color || 'default'}
                                variant="outlined"
                              />

                              <Chip
                                size="small"
                                label={`${recommendation.priority} priority`}
                                color={
                                  recommendation.priority === 'high' ? 'error' :
                                  recommendation.priority === 'medium' ? 'warning' : 'info'
                                }
                                variant="filled"
                              />
                            </Box>
                          </InsightCard>
                        ))}
                      </Box>
                    )}
                  </CardContent>
                </ContentGeneratorCard>
              </Grid>

              {/* Generated Content Preview */}
              {generatedContent && (
                <Grid item xs={12}>
                  <ContentGeneratorCard>
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <TrendingUpIcon color="primary" sx={{ mr: 1 }} />
                          <Typography variant="h6">
                            Generated Content
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Copy content">
                            <IconButton
                              size="small"
                              onClick={() => {
                                navigator.clipboard.writeText(generatedContent.text || generatedContent.content);
                                showSuccessNotification('Content copied to clipboard');
                              }}
                            >
                              <ContentCopyIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>

                      {/* Content Preview */}
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 3,
                          backgroundColor: alpha(theme.palette.background.paper, 0.5),
                          borderRadius: 2
                        }}
                      >
                        <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap', mb: 2 }}>
                          {generatedContent.text || generatedContent.content}
                        </Typography>

                        {generatedContent.hashtags && (
                          <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                              Suggested Hashtags:
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                              {generatedContent.hashtags.map((hashtag, index) => (
                                <Chip
                                  key={index}
                                  label={hashtag}
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                />
                              ))}
                            </Box>
                          </Box>
                        )}
                      </Paper>
                    </CardContent>
                  </ContentGeneratorCard>
                </Grid>
              )}
            </Grid>
          </Grid>
        </Grid>
      </Container>
    </ErrorBoundary>
  );
}));

// ===========================
// PROPTYPES & DISPLAY NAME
// ===========================

CompetitorInsightContentGenerator.propTypes = {
  // Basic props
  variant: PropTypes.oneOf(Object.values(CONTENT_GENERATOR_VARIANTS)),

  // Enhanced props
  enableAnalytics: PropTypes.bool,
  enableAccessibility: PropTypes.bool,
  enableRealTimeAnalysis: PropTypes.bool,
  enableBulkGeneration: PropTypes.bool,

  // Display props
  showInsightCategories: PropTypes.bool,
  showPerformanceMetrics: PropTypes.bool,
  showContentHistory: PropTypes.bool,

  // Callback props
  onContentGenerated: PropTypes.func,
  onInsightApplied: PropTypes.func,
  onError: PropTypes.func,

  // User context props
  userPlan: PropTypes.oneOf(['creator', 'accelerator', 'dominator']),

  // Testing props
  testId: PropTypes.string,

  // Accessibility props
  ariaLabel: PropTypes.string,
  announceChanges: PropTypes.bool,

  // Data props
  initialCompetitors: PropTypes.array,
  defaultSettings: PropTypes.object
};

CompetitorInsightContentGenerator.defaultProps = {
  variant: 'advanced',
  enableAnalytics: true,
  enableAccessibility: true,
  enableRealTimeAnalysis: true,
  enableBulkGeneration: false,
  showInsightCategories: true,
  showPerformanceMetrics: true,
  showContentHistory: true,
  userPlan: 'creator',
  testId: 'competitor-insight-content-generator',
  announceChanges: true,
  initialCompetitors: [],
  defaultSettings: {}
};

CompetitorInsightContentGenerator.displayName = 'CompetitorInsightContentGenerator';

export default CompetitorInsightContentGenerator;

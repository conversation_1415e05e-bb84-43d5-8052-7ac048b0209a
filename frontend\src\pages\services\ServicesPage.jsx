// @since 2024-1-1 to 2025-25-7
import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Alert,
  useTheme,
  alpha,
  Fab,
  Chip
} from '@mui/material';
import {
  Business as BusinessIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

// API and utilities
import { useMessage } from '../../hooks/useMessage';
import { useSubscription } from '../../contexts/SubscriptionContext';
import useServiceManagement from '../../hooks/useServiceManagement';
import { startTiming, endTiming } from '../../utils/performance';

// Components
import StablePageWrapper from '../../components/services/StablePageWrapper';
import FeatureGate from '../../components/common/FeatureGate';
import ServicesList from '../../components/services/ServicesList';
import ServicesOverview from '../../components/services/ServicesOverview';

/**
 * ServicesPage - Main services landing page with overview and management
 * Implements Material-UI glass morphism styling with 8px grid spacing
 * Follows WCAG 2.1 AA compliance and production standards
 */
const ServicesPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const { showSuccessMessage, showErrorMessage } = useMessage();
  const { subscription, hasFeatureAccess } = useSubscription();

  // Use service management hook
  const {
    loading,
    error,
    loadServices,
    deleteServiceWithConfirmation,
    duplicateExistingService,
    setError
  } = useServiceManagement();

  // Local state for services and stats
  const [services, setServices] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    draft: 0,
    campaigns: 0
  });
  const [authError, setAuthError] = useState(null);

  // Glass morphism styles
  const glassMorphismStyles = {
    background: `linear-gradient(135deg, 
      ${alpha(theme.palette.background.paper, 0.9)} 0%, 
      ${alpha(theme.palette.background.default, 0.6)} 100%)`,
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
    borderRadius: theme.spacing(2),
    boxShadow: `0 4px 16px 0 ${alpha(theme.palette.common.black, 0.1)}`,
  };

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      setAuthError('Authentication required to access services');
      navigate('/login');
      return;
    }
  }, [authLoading, isAuthenticated, navigate]);

  // Load services data using the hook
  const handleLoadServices = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      const result = await loadServices();
      setServices(result.services);
      setStats(result.stats);
      showSuccessMessage(`Loaded ${result.services.length} services successfully`);
    } catch (error) {
      // Error handling is done in the hook, but we can add user feedback
      console.error('Error loading services:', error);
      showErrorMessage('Failed to load services. Please try again.');
    }
  }, [loadServices, isAuthenticated, showSuccessMessage, showErrorMessage]);

  // Load data on mount with performance monitoring
  useEffect(() => {
    if (!isAuthenticated || authLoading) return;

    const timingId = startTiming('servicesPageLoad', {
      page: 'services-overview',
      timestamp: Date.now(),
      userId: user?.id
    });

    handleLoadServices().finally(() => {
      endTiming(timingId, {
        success: true,
        servicesCount: services.length,
        userId: user?.id
      });
    });
  }, [handleLoadServices, services.length, isAuthenticated, authLoading, user?.id]);

  // Handle service actions with authentication checks

  const handleCreateService = () => {
    if (!isAuthenticated) {
      showErrorMessage('Authentication required to create services');
      navigate('/login');
      return;
    }

    // Check subscription limits
    if (!hasFeatureAccess('unlimited_services') && services.length >= 5) {
      showErrorMessage('Service limit reached. Upgrade your plan to create more services.');
      return;
    }

    navigate('/services/create');
  };

  const handleEditService = (service) => {
    if (!isAuthenticated) {
      showErrorMessage('Authentication required to edit services');
      navigate('/login');
      return;
    }

    navigate(`/services/${service.id}/edit`);
  };

  const handleViewService = (service) => {
    if (!isAuthenticated) {
      showErrorMessage('Authentication required to view services');
      navigate('/login');
      return;
    }

    navigate(`/services/${service.id}`);
  };

  const handleDuplicateService = async (service) => {
    if (!isAuthenticated) {
      showErrorMessage('Authentication required to duplicate services');
      navigate('/login');
      return;
    }

    // Check subscription limits
    if (!hasFeatureAccess('unlimited_services') && services.length >= 5) {
      showErrorMessage('Service limit reached. Upgrade your plan to duplicate services.');
      return;
    }

    try {
      await duplicateExistingService(service);
      showSuccessMessage(`Service "${service.name}" duplicated successfully`);
      handleLoadServices(); // Refresh the list
    } catch (error) {
      console.error('Error duplicating service:', error);
      showErrorMessage('Failed to duplicate service. Please try again.');
    }
  };

  const handleDeleteService = async (service) => {
    if (!isAuthenticated) {
      showErrorMessage('Authentication required to delete services');
      navigate('/login');
      return;
    }

    try {
      const deleted = await deleteServiceWithConfirmation(service);
      if (deleted) {
        showSuccessMessage(`Service "${service.name}" deleted successfully`);
        handleLoadServices(); // Refresh the list
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      showErrorMessage('Failed to delete service. Please try again.');
    }
  };

  const handleViewICPs = (service) => {
    if (!isAuthenticated) {
      showErrorMessage('Authentication required to view ICPs');
      navigate('/login');
      return;
    }

    navigate(`/services/${service.id}/icps`);
  };

  const handleCreateCampaign = (service) => {
    if (!isAuthenticated) {
      showErrorMessage('Authentication required to create campaigns');
      navigate('/login');
      return;
    }

    // Check subscription limits
    if (!hasFeatureAccess('unlimited_campaigns')) {
      showErrorMessage('Campaign creation requires a premium plan. Please upgrade.');
      return;
    }

    navigate(`/services/${service.id}/create-campaign`);
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <StablePageWrapper
        loading={true}
        error={null}
        fallbackMessage="Checking authentication..."
      >
        <Container maxWidth="lg" sx={{ py: theme.spacing(3), textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>Loading...</Typography>
          <Typography variant="body2" color="textSecondary">Checking authentication</Typography>
        </Container>
      </StablePageWrapper>
    );
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Show authentication error if any
  if (authError) {
    return (
      <StablePageWrapper
        loading={false}
        error={authError}
        fallbackMessage="Authentication required to access services"
      >
        <Container maxWidth="lg" sx={{ py: theme.spacing(3) }}>
          <Alert severity="error" sx={{ mb: theme.spacing(3) }}>
            <Typography variant="h6" gutterBottom>Authentication Error</Typography>
            {authError}
          </Alert>
          <Box sx={{ display: 'flex', gap: theme.spacing(2) }}>
            <Button variant="contained" onClick={() => navigate('/login')}>
              Go to Login
            </Button>
            <Button variant="outlined" onClick={() => navigate('/')}>
              Back to Home
            </Button>
          </Box>
        </Container>
      </StablePageWrapper>
    );
  }

  return (
    <StablePageWrapper
      loading={loading}
      error={error}
      fallbackMessage="The services page encountered an unexpected error. Please refresh the page or contact support if the issue persists."
    >
      <Container maxWidth="lg" sx={{ py: theme.spacing(3) }}>
        {/* Header */}
        <Box sx={{ mb: theme.spacing(4) }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: theme.spacing(2) }}>
            <Box>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: theme.spacing(1),
                  mb: theme.spacing(1)
                }}
              >
                <BusinessIcon />
                Services
              </Typography>
              <Typography variant="body1" color="textSecondary">
                Manage your services, generate ICPs, and create targeted campaigns
              </Typography>
            </Box>
            
            <FeatureGate
              feature="unlimited_services"
              fallback={
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<AddIcon />}
                  onClick={handleCreateService}
                  disabled={services.length >= 5}
                  sx={{
                    minWidth: 180,
                    px: theme.spacing(3),
                    py: theme.spacing(1.5),
                    fontSize: '1rem',
                    fontWeight: 600,
                  }}
                >
                  Create Service {services.length >= 5 && '(Limit Reached)'}
                </Button>
              }
            >
              <Button
                variant="contained"
                size="large"
                startIcon={<AddIcon />}
                onClick={handleCreateService}
                sx={{
                  minWidth: 180,
                  px: theme.spacing(3),
                  py: theme.spacing(1.5),
                  fontSize: '1rem',
                  fontWeight: 600,
                  // WCAG 2.1 AA compliance
                  '&:focus-visible': {
                    outline: `2px solid ${theme.palette.common.white}`,
                    outlineOffset: '2px',
                  },
                }}
              >
                Create Service
              </Button>
            </FeatureGate>
          </Box>

          {/* Error Alert */}
          {error && (
            <Alert 
              severity="error" 
              sx={{ mb: theme.spacing(3) }}
              onClose={() => setError(null)}
            >
              {error}
            </Alert>
          )}
        </Box>

        {/* Subscription Status */}
        {!hasFeatureAccess('unlimited_services') && (
          <Alert severity="info" sx={{ mb: theme.spacing(3) }}>
            <Typography variant="body2">
              You're on the {subscription?.plan_name || 'Free'} plan.
              You can create up to 5 services.
              <Button
                variant="text"
                size="small"
                onClick={() => navigate('/billing')}
                sx={{ ml: 1 }}
              >
                Upgrade for unlimited services
              </Button>
            </Typography>
          </Alert>
        )}

        {/* Services Overview Dashboard */}
        <Box sx={{ mb: theme.spacing(6) }}>
          <ServicesOverview
            stats={stats}
            recentServices={services.slice(0, 3)}
            loading={loading}
            subscription={subscription}
            hasFeatureAccess={hasFeatureAccess}
          />
        </Box>

        {/* Services Management Section */}
        {services.length > 0 && (
          <Box sx={{ mb: theme.spacing(4) }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 600 }}>
              Manage Services
            </Typography>
            <Typography variant="body1" color="textSecondary" sx={{ mb: theme.spacing(3) }}>
              View, edit, and manage your existing services
            </Typography>
          </Box>
        )}

        {/* Services List */}
        {services.length === 0 ? (
          // Enhanced Empty State
          <Card sx={{
            ...glassMorphismStyles,
            textAlign: 'center',
            py: theme.spacing(8),
            px: theme.spacing(4),
            mt: theme.spacing(4)
          }}>
            <CardContent>
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: theme.spacing(3)
              }}>
                <BusinessIcon sx={{
                  fontSize: 80,
                  color: 'primary.main',
                  opacity: 0.8
                }} />

                <Box>
                  <Typography variant="h5" gutterBottom sx={{ fontWeight: 500 }}>
                    Ready to create your first service?
                  </Typography>
                  <Typography
                    variant="body1"
                    color="textSecondary"
                    sx={{
                      maxWidth: 480,
                      mx: 'auto',
                      lineHeight: 1.6
                    }}
                  >
                    Define your service offerings to generate targeted customer profiles and launch focused marketing campaigns.
                  </Typography>
                </Box>

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: theme.spacing(1),
                  mt: theme.spacing(2)
                }}>
                  <Typography variant="body2" color="textSecondary">
                    Click the
                  </Typography>
                  <Chip
                    label="Create Service"
                    size="small"
                    variant="outlined"
                    color="primary"
                  />
                  <Typography variant="body2" color="textSecondary">
                    button above to get started
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        ) : (
          // Services List with Advanced Features
          <ServicesList
            services={services}
            loading={loading}
            onEdit={handleEditService}
            onDelete={handleDeleteService}
            onDuplicate={handleDuplicateService}
            onView={handleViewService}
            onViewICPs={handleViewICPs}
            onCreateCampaign={handleCreateCampaign}
            subscription={subscription}
            hasFeatureAccess={hasFeatureAccess}
            user={user}
          />
        )}



        {/* Floating Action Button for Mobile */}
        <FeatureGate
          feature="unlimited_services"
          fallback={
            services.length < 5 ? (
              <Fab
                color="secondary"
                aria-label="create service"
                onClick={handleCreateService}
                sx={{
                  position: 'fixed',
                  bottom: theme.spacing(2),
                  right: theme.spacing(2),
                  display: { xs: 'flex', md: 'none' },
                }}
              >
                <AddIcon />
              </Fab>
            ) : null
          }
        >
          <Fab
            color="primary"
            aria-label="create service"
            onClick={handleCreateService}
            sx={{
              position: 'fixed',
              bottom: theme.spacing(2),
              right: theme.spacing(2),
              display: { xs: 'flex', md: 'none' },
              '&:focus-visible': {
                outline: `2px solid ${theme.palette.common.white}`,
                outlineOffset: '2px',
              }
            }}
          >
            <AddIcon />
          </Fab>
        </FeatureGate>
      </Container>
    </StablePageWrapper>
  );
};

export default ServicesPage;

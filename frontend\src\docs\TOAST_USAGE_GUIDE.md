<!-- @since 2024-1-1 to 2025-25-7 -->
# Production-Ready Toast Notification System

A comprehensive, accessible, and production-ready toast notification system for the ACE Social platform.

## Features

### 🚀 Production-Ready
- Comprehensive error handling and fallback mechanisms
- Network failure handling with automatic retries
- Rate limiting to prevent notification spam
- Deduplication to avoid duplicate notifications
- Memory leak prevention with automatic cleanup

### ♿ Accessibility First
- WCAG 2.1 AA compliant
- Full screen reader support with proper ARIA labels
- Complete keyboard navigation
- High contrast mode support
- Reduced motion support
- Focus management and restoration

### 🎨 Modern Design
- Responsive design for mobile, tablet, and desktop
- Smooth animations with respect for user preferences
- Material-UI integration with theme support
- Modern visual design with proper spacing and typography
- Priority indicators and visual hierarchy

### 🔧 Advanced Features
- Smart queuing system with priority handling
- Toast stacking with collision detection
- Persistence across page refreshes
- Configurable auto-dismiss timers
- Action buttons with undo functionality
- Progress toasts for long operations

## Quick Start

### Basic Usage

```javascript
import { useAdvancedToast } from '../hooks/useAdvancedToast';

function MyComponent() {
  const { showSuccess, showError, showWarning, showInfo } = useAdvancedToast();

  const handleSave = () => {
    showSuccess('Data saved successfully!');
  };

  const handleError = () => {
    showError('Something went wrong. Please try again.');
  };

  return (
    <div>
      <button onClick={handleSave}>Save</button>
      <button onClick={handleError}>Trigger Error</button>
    </div>
  );
}
```

### Advanced Usage

```javascript
import { useAdvancedToast } from '../hooks/useAdvancedToast';

function AdvancedComponent() {
  const toast = useAdvancedToast();

  const handleComplexOperation = async () => {
    // Progress toast for long operations
    try {
      await toast.showProgress(
        () => api.complexOperation(),
        {
          loadingMessage: 'Processing your request...',
          successMessage: 'Operation completed successfully!',
          errorMessage: 'Operation failed. Please try again.',
          title: 'Complex Operation'
        }
      );
    } catch (error) {
      // Error is automatically handled by showProgress
    }
  };

  const handleWithUndo = () => {
    const itemId = deleteItem();
    
    toast.showSuccess('Item deleted', {
      onUndo: () => restoreItem(itemId),
      duration: 10000,
      actions: [
        {
          label: 'View Trash',
          onClick: () => navigate('/trash'),
          variant: 'outlined'
        }
      ]
    });
  };

  const handleNetworkOperation = () => {
    // Automatic network error handling with retries
    toast.showNetworkToast(
      () => api.saveData(data),
      {
        type: 'success',
        message: 'Data synchronized successfully'
      }
    );
  };

  const handleBatchNotifications = () => {
    const notifications = [
      { type: 'info', message: 'Starting batch process...' },
      { type: 'success', message: 'Step 1 completed' },
      { type: 'success', message: 'Step 2 completed' },
      { type: 'warning', message: 'Step 3 had warnings' },
      { type: 'success', message: 'Batch process completed' }
    ];

    toast.showBatch(notifications, 1000); // 1 second delay between each
  };

  return (
    <div>
      <button onClick={handleComplexOperation}>Complex Operation</button>
      <button onClick={handleWithUndo}>Delete with Undo</button>
      <button onClick={handleNetworkOperation}>Network Operation</button>
      <button onClick={handleBatchNotifications}>Batch Process</button>
    </div>
  );
}
```

## API Reference

### useAdvancedToast Hook

#### Basic Methods
```javascript
const {
  showSuccess,    // (message, options?) => string
  showError,      // (message, options?) => string  
  showWarning,    // (message, options?) => string
  showInfo,       // (message, options?) => string
  showToast,      // (config) => string
} = useAdvancedToast();
```

#### Advanced Methods
```javascript
const {
  showNetworkToast,  // (operation, config, operationId?) => Promise<string>
  showProgress,      // (operation, config) => Promise<any>
  showBatch,         // (configs, delay?) => string[]
  dismissToast,      // (id) => void
  dismissAll,        // () => void
  updateToast,       // (id, updates) => void
} = useAdvancedToast();
```

#### Utility Methods
```javascript
const {
  resetRateLimit,      // (message?) => void
  clearDeduplication,  // () => void
  getStats,           // () => { activeOperations, maxConcurrent }
} = useAdvancedToast();
```

### Configuration Options

#### Toast Configuration
```typescript
interface ToastConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  title?: string;
  duration?: number;
  priority?: 'low' | 'normal' | 'high' | 'critical';
  persistent?: boolean;
  dismissible?: boolean;
  actions?: ToastAction[];
  onClose?: () => void;
  onAction?: () => void;
  onUndo?: () => void;
  metadata?: Record<string, any>;
}
```

#### Action Configuration
```typescript
interface ToastAction {
  label: string;
  onClick: () => void;
  variant?: 'text' | 'outlined' | 'contained';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
}
```

## Best Practices

### 1. Message Content
```javascript
// ✅ Good: Clear, actionable messages
showSuccess('Document saved successfully');
showError('Failed to save document. Please check your connection.');

// ❌ Avoid: Vague or technical messages
showError('Error 500');
showSuccess('OK');
```

### 2. Duration Guidelines
```javascript
// ✅ Good: Appropriate durations
showSuccess('Saved', { duration: 3000 });        // Quick confirmation
showError('Network error', { duration: 8000 });   // Important error
showInfo('Tip: Use Ctrl+S', { duration: 6000 }); // Helpful info

// ❌ Avoid: Too short or too long
showError('Critical error', { duration: 1000 });  // Too short for errors
showInfo('Info', { duration: 30000 });            // Too long for info
```

### 3. Priority Usage
```javascript
// ✅ Good: Appropriate priorities
showError('System failure', { priority: 'critical' });
showWarning('Disk space low', { priority: 'high' });
showInfo('New feature available', { priority: 'low' });

// ❌ Avoid: Everything as critical
showInfo('File saved', { priority: 'critical' });
```

### 4. Actions and Undo
```javascript
// ✅ Good: Meaningful actions
showSuccess('Email sent', {
  actions: [
    { label: 'View Sent Items', onClick: () => navigate('/sent') }
  ]
});

showSuccess('Item deleted', {
  onUndo: () => restoreItem(id),
  duration: 10000 // Give time for undo
});

// ❌ Avoid: Too many actions
showInfo('Welcome', {
  actions: [
    { label: 'Tour' },
    { label: 'Settings' },
    { label: 'Help' },
    { label: 'Feedback' },
    { label: 'Docs' }
  ]
});
```

## Accessibility Guidelines

### Screen Reader Support
- Messages are automatically announced to screen readers
- Error toasts use `aria-live="assertive"` for immediate attention
- Info/success toasts use `aria-live="polite"` for non-intrusive announcements

### Keyboard Navigation
- `Tab` to focus toasts
- `Escape` to dismiss focused toast
- `Enter` or `Space` to trigger primary action
- `Ctrl+U` to trigger undo action
- Arrow keys to navigate between multiple toasts

### Visual Accessibility
- High contrast mode automatically detected and supported
- Reduced motion preferences respected
- Sufficient color contrast ratios (4.5:1 minimum)
- Focus indicators clearly visible

## Performance Considerations

### Rate Limiting
```javascript
// Automatic rate limiting prevents spam
for (let i = 0; i < 10; i++) {
  showSuccess('Spam message'); // Only first 3 will show (configurable)
}
```

### Memory Management
- Toasts are automatically cleaned up after dismissal
- Queue size limits prevent memory leaks
- Efficient React rendering with proper memoization

### Network Optimization
```javascript
// Automatic retry with exponential backoff
toast.showNetworkToast(
  () => api.uploadFile(file),
  { type: 'success', message: 'File uploaded' }
);
```

## Testing

### Unit Testing
```javascript
import { renderWithToast } from '../tests/toast/testUtils';

test('shows success toast on save', async () => {
  const { showSuccess } = renderWithToast(<MyComponent />);
  
  fireEvent.click(screen.getByText('Save'));
  
  await waitFor(() => {
    expect(screen.getByText('Saved successfully')).toBeInTheDocument();
  });
});
```

### Accessibility Testing
```javascript
import { axe } from 'jest-axe';

test('toast has no accessibility violations', async () => {
  const { container } = renderWithToast(<ToastComponent />);
  const results = await axe(container);
  expect(results).toHaveNoViolations();
});
```

## Troubleshooting

### Common Issues

1. **Toasts not appearing**
   - Check that `EnhancedToastProvider` wraps your component
   - Verify rate limiting isn't blocking the toast
   - Check browser console for errors

2. **Performance issues**
   - Reduce `maxVisible` if showing too many toasts
   - Enable rate limiting to prevent spam
   - Check for memory leaks in development tools

3. **Accessibility warnings**
   - Ensure proper message content
   - Check color contrast in different themes
   - Test with screen readers

### Debug Mode
```javascript
const toast = useAdvancedToast({
  debug: true // Enables console logging
});
```

This comprehensive toast system provides a production-ready, accessible, and user-friendly notification experience for the ACE Social platform.

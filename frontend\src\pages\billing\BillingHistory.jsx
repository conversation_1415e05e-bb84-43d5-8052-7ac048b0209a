// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,


  Card,
  CardContent,
  Grid
} from '@mui/material';
import {
  Download as DownloadIcon,
  Receipt as ReceiptIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';


import { useNotification } from '../../hooks/useNotification';
import api from '../../api/index';

const BillingHistory = ({ isEmbedded = false, onError }) => {
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadingInvoice, setDownloadingInvoice] = useState(null);



  useEffect(() => {
    const fetchBillingHistory = async () => {
      try {
        setLoading(true);
        
        // Fetch billing history from API
        const response = await api.get('/api/billing/invoices');
        setInvoices(response.data || []);
        
      } catch (error) {
        console.error('Error fetching billing history:', error);
        setError('Failed to load billing history');
        if (onError) onError(error);
      } finally {
        setLoading(false);
      }
    };

    fetchBillingHistory();
  }, [onError, showErrorNotification]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'paid':
        return <CheckCircleIcon />;
      case 'pending':
        return <ScheduleIcon />;
      case 'failed':
        return <ErrorIcon />;
      default:
        return <ScheduleIcon />;
    }
  };

  const handleDownloadInvoice = async (invoice) => {
    try {
      setDownloadingInvoice(invoice.id);
      
      // Simulate download
      setTimeout(() => {
        showSuccessNotification('Invoice downloaded successfully');
        setDownloadingInvoice(null);
      }, 1000);
      
    } catch (error) {
      console.error('Error downloading invoice:', error);
      showErrorNotification('Failed to download invoice');
      setDownloadingInvoice(null);
    }
  };

  const handleRetryPayment = async () => {
    try {
      showSuccessNotification('Redirecting to payment...');
      // In real implementation, redirect to payment retry flow
    } catch (error) {
      console.error('Error retrying payment:', error);
      showErrorNotification('Failed to retry payment');
    }
  };

  const calculateTotalSpent = () => {
    return invoices
      .filter(invoice => invoice.status === 'paid')
      .reduce((total, invoice) => total + invoice.amount, 0);
  };

  const getThisMonthSpent = () => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    return invoices
      .filter(invoice => {
        const invoiceDate = new Date(invoice.date);
        return invoice.status === 'paid' && 
               invoiceDate.getMonth() === currentMonth && 
               invoiceDate.getFullYear() === currentYear;
      })
      .reduce((total, invoice) => total + invoice.amount, 0);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: isEmbedded ? 0 : 3 }}>
      {!isEmbedded && (
        <>
          <Helmet>
            <title>Billing History | ACEO</title>
            <meta name="description" content="View your complete billing history and download invoices" />
          </Helmet>
          
          <Typography variant="h4" gutterBottom>
            Billing History
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            View your complete billing history, download invoices, and manage payments
          </Typography>
        </>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary" gutterBottom>
                Total Spent
              </Typography>
              <Typography variant="h4" fontWeight="bold">
                ${calculateTotalSpent().toFixed(2)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary" gutterBottom>
                This Month
              </Typography>
              <Typography variant="h4" fontWeight="bold">
                ${getThisMonthSpent().toFixed(2)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary" gutterBottom>
                Total Invoices
              </Typography>
              <Typography variant="h4" fontWeight="bold">
                {invoices.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary" gutterBottom>
                Failed Payments
              </Typography>
              <Typography variant="h4" fontWeight="bold" color="error.main">
                {invoices.filter(inv => inv.status === 'failed').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Invoices Table */}
      <Paper sx={{ overflow: 'hidden' }}>
        <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6" gutterBottom>
            Invoice History
          </Typography>
          <Typography variant="body2" color="text.secondary">
            All your billing transactions and invoices
          </Typography>
        </Box>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Payment Method</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {invoices.map((invoice) => (
                <TableRow key={invoice.id} hover>
                  <TableCell>
                    {new Date(invoice.date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <ReceiptIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      {invoice.description}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      ${invoice.amount.toFixed(2)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      icon={getStatusIcon(invoice.status)}
                      label={invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                      color={getStatusColor(invoice.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {invoice.payment_method}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                      {invoice.status === 'failed' && (
                        <Tooltip title="Retry Payment">
                          <Button
                            size="small"
                            variant="outlined"
                            color="error"
                            startIcon={<RefreshIcon />}
                            onClick={() => handleRetryPayment(invoice)}
                          >
                            Retry
                          </Button>
                        </Tooltip>
                      )}
                      
                      <Tooltip title="Download Invoice">
                        <IconButton
                          size="small"
                          onClick={() => handleDownloadInvoice(invoice)}
                          disabled={downloadingInvoice === invoice.id}
                        >
                          {downloadingInvoice === invoice.id ? (
                            <CircularProgress size={20} />
                          ) : (
                            <DownloadIcon />
                          )}
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {invoices.length === 0 && (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <ReceiptIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No billing history yet
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Your invoices and payment history will appear here once you make your first purchase.
            </Typography>
          </Box>
        )}
      </Paper>

      {!isEmbedded && invoices.length > 0 && (
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Need help with billing? Contact our support team for assistance.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default BillingHistory;

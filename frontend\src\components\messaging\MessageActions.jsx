/**
 * Enhanced Message Actions - Enterprise-grade message action management component
 * Features: Plan-based action limitations, real-time action tracking, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced message action capabilities and interactive action exploration
 *
 * @component
 * @version 2.0.0
 * <AUTHOR> Social Platform Team
 @since 2024-1-1 to 2025-25-7
*/

import {
  useState,
  useEffect,
  useCallback,
  useMemo,
  memo,
  forwardRef,
  useImperativeHandle
} from "react";
import PropTypes from 'prop-types';
import {
  Box,
  Grid,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
  CircularProgress,
  Snackbar,
  Alert,
  Drawer,
  alpha,
  Card,
  CardContent,
  Divider,
  List,
  ListItem
} from "@mui/material";
import {
  MoreVert as MoreVertIcon,
  ContentCopy as CopyIcon,
  Reply as ReplyIcon,
  Delete as DeleteIcon,
  Forward as ForwardIcon,
  Edit as EditIcon,
  Report as ReportIcon,
  Archive as ArchiveIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Bookmark as BookmarkIcon,
  Share as ShareIcon,
  Schedule as ScheduleIcon,
  Translate as TranslateIcon
} from "@mui/icons-material";
import { useAuth } from "../../contexts/AuthContext";
import useApiError from "../../hooks/useApiError";
import api from "../../api";
import { useSubscription } from "../../hooks/useSubscription";
import { useAdvancedToast } from "../../hooks/useAdvancedToast";
import { format } from "date-fns";
// Enhanced context and hook imports
const useEnhancedSubscription = () => {
  const subscription = useSubscription();
  return subscription;
};

const useAccessibility = () => {
  const announceToScreenReader = useCallback((message) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const setFocusToElement = useCallback((elementId) => {
    setTimeout(() => {
      const element = document.getElementById(elementId);
      if (element) {
        element.focus();
      }
    }, 100);
  }, []);

  return { announceToScreenReader, setFocusToElement };
};

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};



/**
 * Enhanced Message Actions Component with comprehensive enterprise-grade features
 *
 * @param {Object} props - Component props
 * @param {Object} props.message - Message object
 * @param {boolean} [props.isCurrentUser=false] - Whether message is from current user
 * @param {boolean} [props.isSocialMedia=false] - Whether message is from social media
 * @param {boolean} [props.enableAdvancedFeatures=true] - Enable advanced features
 * @param {boolean} [props.enableRealTimeOptimization=true] - Enable real-time optimization
 * @param {boolean} [props.enableAIInsights=false] - Enable AI insights
 * @param {Function} [props.onReply] - Reply callback
 * @param {Function} [props.onCopy] - Copy callback
 * @param {Function} [props.onDelete] - Delete callback
 * @param {Function} [props.onForward] - Forward callback
 * @param {Function} [props.onEdit] - Edit callback
 * @param {Function} [props.onReport] - Report callback
 * @param {Function} [props.onArchive] - Archive callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onRefresh] - Refresh callback
 * @param {Function} [props.onUpgrade] - Upgrade callback
 * @param {boolean} [props.disabled=false] - Disabled state
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 * @param {string} [props.testId='enhanced-message-actions'] - Test identifier
 * @param {Object} [props.customization={}] - Customization options
 * @param {string} [props.className=''] - CSS class name
 * @param {Object} [props.style={}] - Inline styles
 * @param {Object} [props.sx={}] - MUI sx prop
 */
const MessageActions = memo(forwardRef(({
  message,
  isCurrentUser = false,
  isSocialMedia = false,
  onReply,
  onCopy,
  onDelete,
  onForward,
  onEdit,
  onReport,
  onArchive,
  onExport,
  onRefresh,
  disabled = false,
  testId = 'enhanced-message-actions',
  className = '',
  style = {},
  sx = {}
}, ref) => {
  // Enhanced context integration
  const {
    subscription
  } = useEnhancedSubscription();

  const { announceToScreenReader, setFocusToElement } = useAccessibility();
  const { user } = useAuth();
  const { handleApiRequest } = useApiError();
  const { showSuccess, showError } = useAdvancedToast();

  // State management
  const [anchorEl, setAnchorEl] = useState(null);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [actionHistory, setActionHistory] = useState([]);
  const [actionAnalytics, setActionAnalytics] = useState(null);

  const [showAnalytics, setShowAnalytics] = useState(false);
  const [actionInsights, setActionInsights] = useState(null);
  const [customActions, setCustomActions] = useState([]);
  const [actionPreferences, setActionPreferences] = useState({
    showConfirmations: true,
    enableKeyboardShortcuts: true,
    defaultActionOrder: ['reply', 'copy', 'forward', 'archive'],
    quickActionsEnabled: true,
    analyticsEnabled: true
  });
  const [retryCount, setRetryCount] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "info",
  });

  // New state for enhanced features
  const [actionDrawerOpen, setActionDrawerOpen] = useState(false);
  const [selectedActionType, setSelectedActionType] = useState(null);
  const [actionStats, setActionStats] = useState(null);
  const [bulkActionMode, setBulkActionMode] = useState(false);
  const [selectedActions, setSelectedActions] = useState([]);

  // Production-ready connection monitoring
  const [backendAvailable, setBackendAvailable] = useState(true);
  const [lastActionCheck, setLastActionCheck] = useState(Date.now());



  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';

    const features = {
      creator: {
        maxActionTypes: 3,
        maxActionsPerMessage: 5,
        hasAdvancedActions: false,
        hasActionAnalytics: false,
        hasCustomActions: false,
        hasActionInsights: false,
        hasActionHistory: false,
        hasBulkActions: false,
        hasActionExport: false,
        hasActionScheduling: false,
        hasActionAutomation: false,
        hasActionTemplates: false,
        hasActionAnalyticsInsights: false,
        hasActionAnalyticsActions: false,
        hasAnalyticsActions: false,
        hasAnalytics: false,
        hasExport: false,
        trackingLevel: 'basic',
        refreshInterval: 5000,
        planName: 'Creator',
        planTier: 1
      },
      accelerator: {
        maxActionTypes: 10,
        maxActionsPerMessage: 25,
        hasAdvancedActions: true,
        hasActionAnalytics: true,
        hasCustomActions: true,
        hasActionInsights: false,
        hasActionHistory: true,
        hasBulkActions: true,
        hasActionExport: true,
        hasActionScheduling: true,
        hasActionAutomation: false,
        hasActionTemplates: true,
        hasActionAnalyticsInsights: true,
        hasActionAnalyticsActions: true,
        hasAnalyticsActions: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'advanced',
        refreshInterval: 1000,
        planName: 'Accelerator',
        planTier: 2
      },
      dominator: {
        maxActionTypes: -1,
        maxActionsPerMessage: -1,
        hasAdvancedActions: true,
        hasActionAnalytics: true,
        hasCustomActions: true,
        hasActionInsights: true,
        hasActionHistory: true,
        hasBulkActions: true,
        hasActionExport: true,
        hasActionScheduling: true,
        hasActionAutomation: true,
        hasActionTemplates: true,
        hasActionAnalyticsInsights: true,
        hasActionAnalyticsActions: true,
        hasAnalyticsActions: true,
        hasAnalytics: true,
        hasExport: true,
        trackingLevel: 'ai-powered',
        refreshInterval: 1000,
        planName: 'Dominator',
        planTier: 3
      }
    };

    const currentFeatures = features[planId] || features.creator;

    return {
      ...currentFeatures,
      hasFeatureAccess: (feature) => currentFeatures[feature] === true,
      isWithinLimits: (current, limit) => limit === -1 || current < limit,
      canUseFeature: (feature, currentUsage = 0) => {
        const hasAccess = currentFeatures[feature] === true;
        const withinLimits = currentFeatures.maxActionTypes === -1 || currentUsage < currentFeatures.maxActionTypes;
        return hasAccess && withinLimits;
      }
    };
  }, [subscription]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'toolbar',
      'aria-label': `Message actions with ${subscriptionFeatures.planName} plan features`,
      'aria-description': `Action interface with ${subscriptionFeatures.trackingLevel} tracking`,
      'aria-live': 'polite',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [subscriptionFeatures.planName, subscriptionFeatures.trackingLevel]);

  /**
   * Enhanced imperative handle for parent component access with comprehensive action API
   */
  useImperativeHandle(ref, () => ({
    // Core methods
    getActionHistory: () => actionHistory,
    getActionAnalytics: () => actionAnalytics,
    getActionInsights: () => actionInsights,
    refreshActions: () => {
      fetchActionAnalytics();
      if (onRefresh) onRefresh();
    },

    // Action methods
    triggerAction: (actionType, actionData) => {
      handleAction(actionType, actionData);
    },
    openActionDrawer: () => setActionDrawerOpen(true),
    closeActionDrawer: () => setActionDrawerOpen(false),
    toggleAnalytics: () => setShowAnalytics(prev => !prev),

    // Analytics methods
    getActionStats: () => actionStats,
    exportActionData: () => {
      if (subscriptionFeatures.hasExport && onExport) {
        onExport(actionHistory, actionAnalytics);
      }
    },

    // Accessibility methods
    announceAction: (message) => announceToScreenReader(message),
    focusActionButton: () => setFocusToElement('action-menu-button'),

    // Advanced methods
    bulkAction: (actionType, messages) => {
      if (subscriptionFeatures.hasBulkActions) {
        handleBulkAction(actionType, messages);
      }
    },
    scheduleAction: (actionType, scheduleData) => {
      if (subscriptionFeatures.hasActionScheduling) {
        handleScheduleAction(actionType, scheduleData);
      }
    },
    getCustomActions: () => customActions,
    addCustomAction: (actionConfig) => {
      if (subscriptionFeatures.hasCustomActions) {
        setCustomActions(prev => [...prev, actionConfig]);
      }
    }
  }), [
    actionHistory,
    actionAnalytics,
    actionInsights,
    actionStats,
    subscriptionFeatures,
    customActions,
    onExport,
    onRefresh,
    announceToScreenReader,
    setFocusToElement,
    fetchActionAnalytics,
    handleAction,
    handleBulkAction,
    handleScheduleAction
  ]);

  // Production-ready health check system
  const checkBackendHealth = useCallback(async () => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('/api/health', {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const wasUnavailable = !backendAvailable;
        setBackendAvailable(true);
        setLastActionCheck(Date.now());

        if (wasUnavailable && actionPreferences.analyticsEnabled) {
          setNotification({
            open: true,
            message: "Connection restored - Actions available",
            severity: "success",
          });
        }
      } else {
        setBackendAvailable(false);
        if (actionPreferences.showConfirmations) {
          setNotification({
            open: true,
            message: "Backend service unavailable - Some actions may be limited",
            severity: "warning",
          });
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Health check failed:', error);
        setBackendAvailable(false);

        // Show notification only if it's been a while since last check
        const timeSinceLastCheck = Date.now() - lastActionCheck;
        if (timeSinceLastCheck > 60000 && actionPreferences.showConfirmations) { // 1 minute
          setNotification({
            open: true,
            message: "Connection issues detected - Actions may be delayed",
            severity: "error",
          });
        }
      }
    }
  }, [backendAvailable, lastActionCheck, actionPreferences.analyticsEnabled, actionPreferences.showConfirmations]);

  // Periodic health checks in production
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      const healthCheckInterval = setInterval(checkBackendHealth, 30000); // Check every 30 seconds
      return () => clearInterval(healthCheckInterval);
    }
  }, [checkBackendHealth]);

  // Calculate action counts for different types
  const getActionCounts = useCallback(() => {
    if (!actionHistory.length) {
      return { total: 0, reply: 0, copy: 0, edit: 0, delete: 0, forward: 0, report: 0, archive: 0 };
    }

    return actionHistory.reduce((counts, action) => {
      counts.total += 1;
      counts[action.type] = (counts[action.type] || 0) + 1;
      return counts;
    }, { total: 0, reply: 0, copy: 0, edit: 0, delete: 0, forward: 0, report: 0, archive: 0 });
  }, [actionHistory]);

  // Handle action menu open
  const handleClick = useCallback((event) => {
    setAnchorEl(event.currentTarget);
    announceToScreenReader('Message actions menu opened');
  }, [announceToScreenReader]);

  // Handle action menu close
  const handleClose = useCallback(() => {
    setAnchorEl(null);
    announceToScreenReader('Message actions menu closed');
  }, [announceToScreenReader]);

  // Fetch action analytics with enhanced error handling and retry logic
  const fetchActionAnalytics = useCallback(async () => {
    const maxRetries = 3;

    const attemptFetch = async (attempt = 1) => {
      try {
        await handleApiRequest(
          async () => {
            const response = await api.get(`/api/messages/${message.id}/actions/analytics`);
            return response.data;
          },
          {
            onSuccess: (data) => {
              setActionAnalytics(data);
              setRetryCount(0); // Reset retry count on success
              if (actionPreferences.analyticsEnabled) {
                setNotification({
                  open: true,
                  message: "Action analytics updated successfully",
                  severity: "success",
                });
              }
            },
            onError: (err) => {
              if (attempt < maxRetries) {
                setRetryCount(attempt);
                setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt); // Exponential backoff
              } else {
                setRetryCount(maxRetries);
                if (process.env.NODE_ENV === 'development') {
                  console.error("Failed to fetch action analytics after retries:", err);
                }
                setNotification({
                  open: true,
                  message: `Failed to load action analytics (${maxRetries} attempts)`,
                  severity: "error",
                });
              }
            },
          }
        );
      } catch (error) {
        if (attempt < maxRetries) {
          setRetryCount(attempt);
          setTimeout(() => attemptFetch(attempt + 1), 1000 * attempt);
        } else {
          setRetryCount(maxRetries);
          console.error("Network error fetching analytics:", error);
        }
      }
    };

    await attemptFetch();
  }, [handleApiRequest, message.id, actionPreferences.analyticsEnabled]);

  // Fetch action insights
  const fetchActionInsights = useCallback(async () => {
    if (!subscriptionFeatures.hasActionInsights) return;

    await handleApiRequest(
      async () => {
        const response = await api.get(`/api/messages/${message.id}/actions/insights`);
        return response.data;
      },
      {
        onSuccess: (data) => {
          setActionInsights(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch action insights:", err);
          }
        },
      }
    );
  }, [handleApiRequest, message.id, subscriptionFeatures.hasActionInsights]);

  // Enhanced action handler with analytics tracking
  const handleAction = useCallback(async (actionType, actionData = {}) => {
    handleClose();

    // Track action in history
    const actionRecord = {
      id: Date.now(),
      type: actionType,
      messageId: message.id,
      timestamp: new Date().toISOString(),
      userId: user.id,
      data: actionData
    };

    setActionHistory(prev => [actionRecord, ...prev.slice(0, 99)]); // Keep last 100 actions

    // Announce action to screen reader
    announceToScreenReader(`${actionType} action performed`);

    try {
      switch (actionType) {
        case 'reply':
          if (onReply) {
            await onReply(message);
            showSuccess('Reply initiated');
          }
          break;
        case 'copy':
          if (message.content) {
            await navigator.clipboard.writeText(message.content);
            if (onCopy) onCopy(message.content);
            showSuccess('Message copied to clipboard');
          }
          break;
        case 'delete':
          if (isCurrentUser) {
            setConfirmDelete(true);
          }
          break;
        case 'forward':
          if (onForward) {
            await onForward(message);
            showSuccess('Forward initiated');
          }
          break;
        case 'edit':
          if (isCurrentUser && onEdit) {
            await onEdit(message);
            showSuccess('Edit mode activated');
          }
          break;
        case 'report':
          if (!isCurrentUser && isSocialMedia && onReport) {
            await onReport(message);
            showSuccess('Message reported');
          }
          break;
        case 'archive':
          if (onArchive) {
            await onArchive(message);
            showSuccess('Message archived');
          }
          break;
        default: {
          // Handle custom actions
          const customAction = customActions.find(action => action.type === actionType);
          if (customAction && customAction.handler) {
            await customAction.handler(message, actionData);
            showSuccess(`${customAction.name} completed`);
          }
          break;
        }
      }

      // Update analytics if available
      if (subscriptionFeatures.hasActionAnalytics) {
        fetchActionAnalytics();
      }

    } catch (error) {
      console.error(`Action ${actionType} failed:`, error);
      showError(`Failed to ${actionType} message`);
    }
  }, [
    handleClose,
    message,
    user.id,
    announceToScreenReader,
    onReply,
    onCopy,
    onForward,
    onEdit,
    onReport,
    onArchive,
    isCurrentUser,
    isSocialMedia,
    customActions,
    subscriptionFeatures.hasActionAnalytics,
    fetchActionAnalytics,
    showSuccess,
    showError
  ]);

  // Handle delete confirmation
  const handleConfirmDelete = useCallback(async () => {
    setConfirmDelete(false);
    if (onDelete) {
      try {
        await onDelete(message);
        showSuccess('Message deleted successfully');
      } catch (error) {
        console.error('Delete failed:', error);
        showError('Failed to delete message');
      }
    }
  }, [onDelete, message, showSuccess, showError]);

  // Handle bulk actions
  const handleBulkAction = useCallback(async (actionType, messages) => {
    if (!subscriptionFeatures.hasBulkActions) return;

    try {
      for (const msg of messages) {
        await handleAction(actionType, { messageId: msg.id });
      }
      showSuccess(`Bulk ${actionType} completed for ${messages.length} messages`);
    } catch (error) {
      console.error('Bulk action failed:', error);
      showError(`Bulk ${actionType} failed`);
    }
  }, [subscriptionFeatures.hasBulkActions, handleAction, showSuccess, showError]);

  // Handle scheduled actions
  const handleScheduleAction = useCallback(async (actionType, scheduleData) => {
    if (!subscriptionFeatures.hasActionScheduling) return;

    try {
      await api.post('/api/actions/schedule', {
        actionType,
        messageId: message.id,
        scheduleData
      });
      showSuccess(`${actionType} scheduled successfully`);
    } catch (error) {
      console.error('Schedule action failed:', error);
      showError(`Failed to schedule ${actionType}`);
    }
  }, [subscriptionFeatures.hasActionScheduling, message.id, showSuccess, showError]);

  // Get available actions based on subscription and context
  const getAvailableActions = useCallback(() => {
    const baseActions = [
      { type: 'reply', label: 'Reply', icon: ReplyIcon, available: true, priority: 1 },
      { type: 'copy', label: 'Copy Text', icon: CopyIcon, available: true, priority: 2 },
      { type: 'forward', label: 'Forward', icon: ForwardIcon, available: true, priority: 3 },
      { type: 'archive', label: 'Archive', icon: ArchiveIcon, available: true, priority: 4 }
    ];

    const conditionalActions = [
      { type: 'edit', label: 'Edit Message', icon: EditIcon, available: isCurrentUser, priority: 5 },
      { type: 'delete', label: 'Delete Message', icon: DeleteIcon, available: isCurrentUser, priority: 10 },
      { type: 'report', label: 'Report Message', icon: ReportIcon, available: !isCurrentUser && isSocialMedia, priority: 9 }
    ];

    const advancedActions = [
      { type: 'bookmark', label: 'Bookmark', icon: BookmarkIcon, available: subscriptionFeatures.hasAdvancedActions, priority: 6 },
      { type: 'share', label: 'Share', icon: ShareIcon, available: subscriptionFeatures.hasAdvancedActions, priority: 7 },
      { type: 'translate', label: 'Translate', icon: TranslateIcon, available: subscriptionFeatures.hasAdvancedActions, priority: 8 },
      { type: 'schedule', label: 'Schedule Action', icon: ScheduleIcon, available: subscriptionFeatures.hasActionScheduling, priority: 11 }
    ];

    let allActions = [...baseActions, ...conditionalActions, ...advancedActions, ...customActions];

    // Apply user preferences for action ordering
    if (actionPreferences.defaultActionOrder && actionPreferences.defaultActionOrder.length > 0) {
      allActions = allActions.sort((a, b) => {
        const aIndex = actionPreferences.defaultActionOrder.indexOf(a.type);
        const bIndex = actionPreferences.defaultActionOrder.indexOf(b.type);

        if (aIndex !== -1 && bIndex !== -1) {
          return aIndex - bIndex;
        } else if (aIndex !== -1) {
          return -1;
        } else if (bIndex !== -1) {
          return 1;
        } else {
          return (a.priority || 999) - (b.priority || 999);
        }
      });
    } else {
      allActions = allActions.sort((a, b) => (a.priority || 999) - (b.priority || 999));
    }

    return allActions
      .filter(action => action.available)
      .slice(0, subscriptionFeatures.maxActionTypes === -1 ? undefined : subscriptionFeatures.maxActionTypes);
  }, [isCurrentUser, isSocialMedia, subscriptionFeatures, customActions, actionPreferences]);

  // Fetch action stats
  const fetchActionStats = useCallback(async () => {
    if (!subscriptionFeatures.hasActionAnalytics) return;

    await handleApiRequest(
      async () => {
        const response = await api.get(`/api/messages/${message.id}/actions/stats`);
        return response.data;
      },
      {
        onSuccess: (data) => {
          setActionStats(data);
        },
        onError: (err) => {
          if (process.env.NODE_ENV === 'development') {
            console.error("Failed to fetch action stats:", err);
          }
        },
      }
    );
  }, [handleApiRequest, message.id, subscriptionFeatures.hasActionAnalytics]);

  // Handle action selection for bulk operations
  const handleActionSelection = useCallback((actionType) => {
    if (!bulkActionMode) {
      setSelectedActionType(actionType);
      return;
    }

    setSelectedActions(prev => {
      if (prev.includes(actionType)) {
        return prev.filter(type => type !== actionType);
      } else {
        return [...prev, actionType];
      }
    });
  }, [bulkActionMode]);

  // Execute bulk actions
  const executeBulkActions = useCallback(async () => {
    if (!bulkActionMode || selectedActions.length === 0) return;

    try {
      for (const actionType of selectedActions) {
        await handleAction(actionType, { isBulkAction: true });
      }

      showSuccess(`Bulk actions completed: ${selectedActions.join(', ')}`);
      setSelectedActions([]);
      setBulkActionMode(false);
    } catch (error) {
      console.error('Bulk actions failed:', error);
      showError('Some bulk actions failed');
    }
  }, [bulkActionMode, selectedActions, handleAction, showSuccess, showError]);

  // Update action preferences
  const updateActionPreferences = useCallback((newPreferences) => {
    setActionPreferences(prev => ({
      ...prev,
      ...newPreferences
    }));

    if (actionPreferences.analyticsEnabled) {
      setNotification({
        open: true,
        message: "Action preferences updated",
        severity: "success",
      });
    }
  }, [actionPreferences.analyticsEnabled]);

  // Reset action preferences to defaults
  const resetActionPreferences = useCallback(() => {
    setActionPreferences({
      showConfirmations: true,
      enableKeyboardShortcuts: true,
      defaultActionOrder: ['reply', 'copy', 'forward', 'archive'],
      quickActionsEnabled: true,
      analyticsEnabled: true
    });

    showSuccess('Action preferences reset to defaults');
  }, [showSuccess]);

  // Initial data loading
  useEffect(() => {
    if (subscriptionFeatures.hasActionAnalytics) {
      fetchActionAnalytics();
      fetchActionStats();
    }
    if (subscriptionFeatures.hasActionInsights) {
      fetchActionInsights();
    }
  }, [subscriptionFeatures.hasActionAnalytics, subscriptionFeatures.hasActionInsights, fetchActionAnalytics, fetchActionInsights, fetchActionStats]);

  // Keyboard navigation handler
  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Escape') {
      if (anchorEl) {
        handleClose();
      }
      if (confirmDelete) {
        setConfirmDelete(false);
      }
      if (actionDrawerOpen) {
        setActionDrawerOpen(false);
      }
    }
  }, [anchorEl, confirmDelete, actionDrawerOpen, handleClose]);

  // Add keyboard event listener
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Memoized action data for performance
  const actionData = useMemo(() => {
    if (!message) return null;

    return {
      messageId: message.id,
      content: message.content,
      timestamp: message.timestamp,
      isCurrentUser,
      isSocialMedia,
      availableActions: getAvailableActions(),
      actionCounts: getActionCounts()
    };
  }, [message, isCurrentUser, isSocialMedia, getAvailableActions, getActionCounts]);

  // Production-ready error handling with actionData validation
  if (!message || !actionData) {
    return (
      <Box sx={{ p: 1 }}>
        <Typography variant="caption" color="error">
          Message Actions Unavailable
        </Typography>
        {retryCount > 0 && (
          <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: 'warning.main' }}>
            Retrying... ({retryCount}/3)
          </Typography>
        )}
      </Box>
    );
  }

  return (
    <Box
      {...getAccessibilityProps()}
      sx={{
        position: 'relative',
        display: 'inline-flex',
        alignItems: 'center',
        ...sx
      }}
      className={className}
      style={style}
      data-testid={testId}
    >
      {/* Main Action Button */}
      <Box sx={{
        position: 'absolute',
        top: 8,
        right: 8,
        opacity: 0,
        transition: 'opacity 0.2s',
        '.MuiCard-root:hover &': { opacity: 1 },
        '.MuiPaper-root:hover &': { opacity: 1 }
      }}>
        <Tooltip title="Message actions">
          <IconButton
            id="action-menu-button"
            size="small"
            onClick={handleClick}
            disabled={disabled}
            sx={{
              bgcolor: alpha(ACE_COLORS.WHITE, 0.2),
              backdropFilter: 'blur(4px)',
              border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
              '&:hover': {
                bgcolor: alpha(ACE_COLORS.PURPLE, 0.1),
                borderColor: ACE_COLORS.PURPLE,
              },
              '&:disabled': {
                opacity: 0.5,
                cursor: 'not-allowed'
              }
            }}
          >
            <MoreVertIcon fontSize="small" sx={{ color: ACE_COLORS.DARK }} />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            borderRadius: 2,
            background: alpha(ACE_COLORS.WHITE, 0.95),
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
            boxShadow: `0 8px 32px ${alpha(ACE_COLORS.DARK, 0.1)}`,
            minWidth: 200
          }
        }}
      >
        {getAvailableActions().map((action) => {
          const IconComponent = action.icon;
          const isDestructive = action.type === 'delete' || action.type === 'report';
          const isSelected = selectedActionType === action.type || selectedActions.includes(action.type);

          return (
            <MenuItem
              key={action.type}
              onClick={() => {
                if (bulkActionMode) {
                  handleActionSelection(action.type);
                } else {
                  handleAction(action.type);
                }
              }}
              sx={{
                color: isDestructive ? 'error.main' : ACE_COLORS.DARK,
                bgcolor: isSelected ? alpha(ACE_COLORS.PURPLE, 0.1) : 'transparent',
                '&:hover': {
                  backgroundColor: alpha(isDestructive ? 'error.main' : ACE_COLORS.PURPLE, 0.1)
                }
              }}
            >
              <ListItemIcon>
                <IconComponent
                  fontSize="small"
                  sx={{ color: isDestructive ? 'error.main' : ACE_COLORS.PURPLE }}
                />
              </ListItemIcon>
              <ListItemText>
                {action.label}
                {bulkActionMode && isSelected && (
                  <Typography variant="caption" sx={{ display: 'block', color: ACE_COLORS.PURPLE }}>
                    Selected for bulk action
                  </Typography>
                )}
              </ListItemText>
              {subscriptionFeatures.hasActionAnalytics && actionAnalytics?.[action.type] && (
                <Typography variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
                  {actionAnalytics[action.type].count}
                </Typography>
              )}
            </MenuItem>
          );
        })}

        {/* Advanced Features Separator */}
        {subscriptionFeatures.hasAdvancedActions && (
          <>
            <Divider sx={{ my: 1 }} />
            <MenuItem onClick={() => setActionDrawerOpen(true)}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" sx={{ color: ACE_COLORS.PURPLE }} />
              </ListItemIcon>
              <ListItemText>Advanced Actions</ListItemText>
            </MenuItem>
          </>
        )}

        {/* Bulk Actions Option */}
        {subscriptionFeatures.hasBulkActions && (
          <MenuItem onClick={() => setBulkActionMode(!bulkActionMode)}>
            <ListItemIcon>
              <SettingsIcon fontSize="small" sx={{ color: ACE_COLORS.YELLOW }} />
            </ListItemIcon>
            <ListItemText>
              {bulkActionMode ? 'Exit Bulk Mode' : 'Bulk Actions'}
            </ListItemText>
          </MenuItem>
        )}

        {/* Analytics Option */}
        {subscriptionFeatures.hasActionAnalytics && (
          <MenuItem onClick={() => setShowAnalytics(true)}>
            <ListItemIcon>
              <AnalyticsIcon fontSize="small" sx={{ color: ACE_COLORS.PURPLE }} />
            </ListItemIcon>
            <ListItemText>View Analytics</ListItemText>
            {actionStats && (
              <Typography variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
                {actionStats.totalActions}
              </Typography>
            )}
          </MenuItem>
        )}
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={confirmDelete}
        onClose={() => setConfirmDelete(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            background: alpha(ACE_COLORS.WHITE, 0.95),
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }
        }}
      >
        <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
          Confirm Delete
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ color: ACE_COLORS.DARK }}>
            Are you sure you want to delete this message? This action cannot be undone.
          </Typography>
          <Box sx={{
            mt: 2,
            p: 2,
            bgcolor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderRadius: 1,
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }}>
            <Typography variant="body2" sx={{ fontStyle: 'italic', color: ACE_COLORS.DARK }}>
              &quot;{message.content && message.content.length > 100 ? message.content.substring(0, 100) + '...' : message.content}&quot;
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmDelete(false)}
            sx={{ color: ACE_COLORS.DARK }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            sx={{
              bgcolor: 'error.main',
              '&:hover': {
                bgcolor: 'error.dark'
              }
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Advanced Actions Drawer */}
      <Drawer
        anchor="right"
        open={actionDrawerOpen}
        onClose={() => setActionDrawerOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '100%', sm: 400, md: 500 },
            maxWidth: '90vw',
            bgcolor: alpha(ACE_COLORS.WHITE, 0.95),
            backdropFilter: 'blur(10px)'
          },
        }}
      >
        <Box sx={{ p: 3 }}>
          <Typography variant="h6" sx={{ color: ACE_COLORS.DARK, mb: 2 }}>
            Advanced Message Actions
          </Typography>

          {/* Bulk Actions Section */}
          {subscriptionFeatures.hasBulkActions && (
            <Card sx={{ mb: 2, bgcolor: alpha(ACE_COLORS.PURPLE, 0.05) }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
                  Bulk Actions
                </Typography>
                <Typography variant="body2" sx={{ color: ACE_COLORS.DARK, mb: 2 }}>
                  {bulkActionMode ? 'Select actions to perform in bulk' : 'Enable bulk action mode'}
                </Typography>

                {bulkActionMode && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="caption" sx={{ color: ACE_COLORS.DARK }}>
                      Selected: {selectedActions.join(', ') || 'None'}
                    </Typography>
                  </Box>
                )}

                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Button
                    variant={bulkActionMode ? "contained" : "outlined"}
                    size="small"
                    onClick={() => setBulkActionMode(!bulkActionMode)}
                    sx={{
                      borderColor: ACE_COLORS.PURPLE,
                      color: bulkActionMode ? ACE_COLORS.WHITE : ACE_COLORS.PURPLE,
                      bgcolor: bulkActionMode ? ACE_COLORS.PURPLE : 'transparent',
                      '&:hover': {
                        borderColor: ACE_COLORS.PURPLE,
                        bgcolor: bulkActionMode ? ACE_COLORS.PURPLE : alpha(ACE_COLORS.PURPLE, 0.1)
                      }
                    }}
                  >
                    {bulkActionMode ? 'Exit Bulk Mode' : 'Enable Bulk Mode'}
                  </Button>

                  {bulkActionMode && selectedActions.length > 0 && (
                    <Button
                      variant="contained"
                      size="small"
                      onClick={executeBulkActions}
                      sx={{
                        bgcolor: ACE_COLORS.YELLOW,
                        color: ACE_COLORS.DARK,
                        '&:hover': {
                          bgcolor: alpha(ACE_COLORS.YELLOW, 0.8)
                        }
                      }}
                    >
                      Execute ({selectedActions.length})
                    </Button>
                  )}
                </Box>
              </CardContent>
            </Card>
          )}

          {subscriptionFeatures.hasActionHistory && (
            <Card sx={{ mb: 2, bgcolor: alpha(ACE_COLORS.PURPLE, 0.05) }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
                  Action History
                </Typography>
                <List dense>
                  {actionHistory.slice(0, 5).map((action) => (
                    <ListItem key={action.id}>
                      <ListItemText
                        primary={action.type}
                        secondary={format(new Date(action.timestamp), 'MMM dd, yyyy HH:mm')}
                        sx={{ color: ACE_COLORS.DARK }}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}

          {subscriptionFeatures.hasCustomActions && (
            <Card sx={{ mb: 2, bgcolor: alpha(ACE_COLORS.YELLOW, 0.05) }}>
              <CardContent>
                <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK, mb: 1 }}>
                  Custom Actions
                </Typography>
                <Typography variant="body2" sx={{ color: ACE_COLORS.DARK }}>
                  Create and manage custom message actions for your workflow.
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  sx={{
                    mt: 1,
                    borderColor: ACE_COLORS.PURPLE,
                    color: ACE_COLORS.PURPLE,
                    '&:hover': {
                      borderColor: ACE_COLORS.PURPLE,
                      bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                >
                  Manage Custom Actions
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Action Preferences Section */}
          <Card sx={{ mb: 2, bgcolor: alpha(ACE_COLORS.DARK, 0.05) }}>
            <CardContent>
              <Typography variant="subtitle2" sx={{ color: ACE_COLORS.DARK, mb: 2 }}>
                Action Preferences
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="text"
                  size="small"
                  onClick={() => updateActionPreferences({ showConfirmations: !actionPreferences.showConfirmations })}
                  sx={{
                    justifyContent: 'flex-start',
                    color: actionPreferences.showConfirmations ? ACE_COLORS.PURPLE : ACE_COLORS.DARK,
                    '&:hover': {
                      bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                >
                  {actionPreferences.showConfirmations ? '✓' : '○'} Show Confirmations
                </Button>

                <Button
                  variant="text"
                  size="small"
                  onClick={() => updateActionPreferences({ analyticsEnabled: !actionPreferences.analyticsEnabled })}
                  sx={{
                    justifyContent: 'flex-start',
                    color: actionPreferences.analyticsEnabled ? ACE_COLORS.PURPLE : ACE_COLORS.DARK,
                    '&:hover': {
                      bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                >
                  {actionPreferences.analyticsEnabled ? '✓' : '○'} Enable Analytics
                </Button>

                <Button
                  variant="text"
                  size="small"
                  onClick={() => updateActionPreferences({ quickActionsEnabled: !actionPreferences.quickActionsEnabled })}
                  sx={{
                    justifyContent: 'flex-start',
                    color: actionPreferences.quickActionsEnabled ? ACE_COLORS.PURPLE : ACE_COLORS.DARK,
                    '&:hover': {
                      bgcolor: alpha(ACE_COLORS.PURPLE, 0.1)
                    }
                  }}
                >
                  {actionPreferences.quickActionsEnabled ? '✓' : '○'} Quick Actions
                </Button>
              </Box>

              <Button
                variant="outlined"
                size="small"
                onClick={resetActionPreferences}
                sx={{
                  mt: 2,
                  borderColor: ACE_COLORS.YELLOW,
                  color: ACE_COLORS.DARK,
                  '&:hover': {
                    borderColor: ACE_COLORS.YELLOW,
                    bgcolor: alpha(ACE_COLORS.YELLOW, 0.1)
                  }
                }}
              >
                Reset to Defaults
              </Button>
            </CardContent>
          </Card>
        </Box>
      </Drawer>

      {/* Analytics Dialog */}
      <Dialog
        open={showAnalytics}
        onClose={() => setShowAnalytics(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            background: alpha(ACE_COLORS.WHITE, 0.95),
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
          }
        }}
      >
        <DialogTitle sx={{ color: ACE_COLORS.DARK }}>
          Message Action Analytics
        </DialogTitle>
        <DialogContent>
          {actionAnalytics ? (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Card sx={{ bgcolor: alpha(ACE_COLORS.PURPLE, 0.05) }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ color: ACE_COLORS.DARK }}>
                      Total Actions
                    </Typography>
                    <Typography variant="h4" sx={{ color: ACE_COLORS.PURPLE }}>
                      {getActionCounts().total}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card sx={{ bgcolor: alpha(ACE_COLORS.YELLOW, 0.05) }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ color: ACE_COLORS.DARK }}>
                      Most Used Action
                    </Typography>
                    <Typography variant="h4" sx={{ color: ACE_COLORS.PURPLE }}>
                      {Object.entries(getActionCounts()).reduce((a, b) => getActionCounts()[a] > getActionCounts()[b] ? a : b, 'reply')}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          ) : (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <CircularProgress sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography sx={{ mt: 2, color: ACE_COLORS.DARK }}>
                Loading analytics...
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setShowAnalytics(false)}
            sx={{ color: ACE_COLORS.DARK }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notifications */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
      >
        <Alert
          onClose={() => setNotification({ ...notification, open: false })}
          severity={notification.severity}
          sx={{ width: "100%" }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}));

// Enhanced PropTypes with comprehensive validation
MessageActions.propTypes = {
  // Core props
  message: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    content: PropTypes.string.isRequired,
    timestamp: PropTypes.string,
    userId: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
  }).isRequired,
  isCurrentUser: PropTypes.bool,
  isSocialMedia: PropTypes.bool,

  // Enhanced props
  enableAdvancedFeatures: PropTypes.bool,
  enableRealTimeOptimization: PropTypes.bool,
  enableAIInsights: PropTypes.bool,

  // Callback props
  onReply: PropTypes.func,
  onCopy: PropTypes.func,
  onDelete: PropTypes.func,
  onForward: PropTypes.func,
  onEdit: PropTypes.func,
  onReport: PropTypes.func,
  onArchive: PropTypes.func,
  onExport: PropTypes.func,
  onRefresh: PropTypes.func,
  onUpgrade: PropTypes.func,

  // Standard props
  disabled: PropTypes.bool,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string,
  testId: PropTypes.string,
  customization: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  sx: PropTypes.object
};

MessageActions.displayName = 'MessageActions';

export default MessageActions;

// @since 2024-1-1 to 2025-25-7
import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Autocomplete,
  TextField,
  Divider,
  Card,
  CardContent,
  Grid,
} from '@mui/material';
import {
  Close as CloseIcon,
  Assignment as AssignIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Archive as ArchiveIcon,
  Flag as FlagIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Merge as MergeIcon,
  Label as LabelIcon,
} from '@mui/icons-material';

const BulkOperationsDialog = ({ 
  open, 
  onClose, 
  selectedTickets = [], 
  agents = [],
  onExecute 
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [operation, setOperation] = useState('');
  const [operationData, setOperationData] = useState({});

  const bulkOperations = [
    {
      id: 'assign',
      label: 'Assign to Agent',
      icon: <AssignIcon />,
      description: 'Assign selected tickets to a specific agent',
      color: 'primary',
      requiresData: true,
      dataType: 'agent'
    },
    {
      id: 'status_update',
      label: 'Update Status',
      icon: <EditIcon />,
      description: 'Change status of selected tickets',
      color: 'info',
      requiresData: true,
      dataType: 'status'
    },
    {
      id: 'priority_update',
      label: 'Update Priority',
      icon: <FlagIcon />,
      description: 'Change priority of selected tickets',
      color: 'warning',
      requiresData: true,
      dataType: 'priority'
    },
    {
      id: 'add_tags',
      label: 'Add Tags',
      icon: <LabelIcon />,
      description: 'Add tags to selected tickets',
      color: 'secondary',
      requiresData: true,
      dataType: 'tags'
    },
    {
      id: 'escalate',
      label: 'Escalate',
      icon: <WarningIcon />,
      description: 'Escalate selected tickets',
      color: 'error',
      requiresData: false
    },
    {
      id: 'merge',
      label: 'Merge Tickets',
      icon: <MergeIcon />,
      description: 'Merge selected tickets into one',
      color: 'info',
      requiresData: true,
      dataType: 'merge_target',
      minTickets: 2
    },
    {
      id: 'archive',
      label: 'Archive',
      icon: <ArchiveIcon />,
      description: 'Archive selected tickets',
      color: 'default',
      requiresData: false
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: <DeleteIcon />,
      description: 'Permanently delete selected tickets',
      color: 'error',
      requiresData: false,
      dangerous: true
    }
  ];

  const statusOptions = [
    { value: 'open', label: 'Open', icon: <InfoIcon /> },
    { value: 'in_progress', label: 'In Progress', icon: <PlayArrowIcon /> },
    { value: 'pending_customer', label: 'Pending Customer', icon: <PauseIcon /> },
    { value: 'resolved', label: 'Resolved', icon: <CheckCircleIcon /> },
    { value: 'closed', label: 'Closed', icon: <StopIcon /> },
    { value: 'cancelled', label: 'Cancelled', icon: <CancelIcon /> }
  ];

  const priorityOptions = [
    { value: 'low', label: 'Low', color: 'default' },
    { value: 'medium', label: 'Medium', color: 'info' },
    { value: 'high', label: 'High', color: 'warning' },
    { value: 'critical', label: 'Critical', color: 'error' }
  ];

  const handleOperationChange = (newOperation) => {
    setOperation(newOperation);
    setOperationData({});
    setError(null);
  };

  const handleExecute = async () => {
    try {
      setLoading(true);
      setError(null);

      const selectedOperation = bulkOperations.find(op => op.id === operation);
      
      // Validation
      if (!operation) {
        setError('Please select an operation');
        return;
      }

      if (selectedOperation?.minTickets && selectedTickets.length < selectedOperation.minTickets) {
        setError(`This operation requires at least ${selectedOperation.minTickets} tickets`);
        return;
      }

      if (selectedOperation?.requiresData) {
        const hasRequiredData = Object.keys(operationData).length > 0;
        if (!hasRequiredData) {
          setError('Please provide required data for this operation');
          return;
        }
      }

      // Confirm dangerous operations
      if (selectedOperation?.dangerous) {
        const confirmed = window.confirm(
          `Are you sure you want to ${selectedOperation.label.toLowerCase()} ${selectedTickets.length} ticket(s)? This action cannot be undone.`
        );
        if (!confirmed) return;
      }

      await onExecute(operation, operationData);
      onClose();

    } catch (error) {
      console.error('Error executing bulk operation:', error);
      setError(error.message || 'Failed to execute operation');
    } finally {
      setLoading(false);
    }
  };

  const renderOperationData = () => {
    const selectedOperation = bulkOperations.find(op => op.id === operation);
    if (!selectedOperation?.requiresData) return null;

    switch (selectedOperation.dataType) {
      case 'agent':
        return (
          <Autocomplete
            options={agents}
            getOptionLabel={(option) => option.name || ''}
            value={agents.find(agent => agent.id === operationData.agent_id) || null}
            onChange={(_, newValue) => setOperationData({ agent_id: newValue?.id || '' })}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Agent"
                placeholder="Choose an agent to assign tickets to..."
                fullWidth
              />
            )}
            renderOption={(props, option) => (
              <Box component="li" {...props} display="flex" alignItems="center" gap={1}>
                <AssignIcon color="action" />
                <Box>
                  <Typography variant="body2">{option.name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {option.email}
                  </Typography>
                </Box>
              </Box>
            )}
          />
        );

      case 'status':
        return (
          <FormControl fullWidth>
            <InputLabel>New Status</InputLabel>
            <Select
              value={operationData.status || ''}
              label="New Status"
              onChange={(e) => setOperationData({ status: e.target.value })}
            >
              {statusOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Box display="flex" alignItems="center" gap={1}>
                    {option.icon}
                    {option.label}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'priority':
        return (
          <FormControl fullWidth>
            <InputLabel>New Priority</InputLabel>
            <Select
              value={operationData.priority || ''}
              label="New Priority"
              onChange={(e) => setOperationData({ priority: e.target.value })}
            >
              {priorityOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Chip 
                    label={option.label} 
                    color={option.color} 
                    size="small" 
                  />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'tags':
        return (
          <TextField
            fullWidth
            label="Tags to Add"
            placeholder="Enter tags separated by commas..."
            value={operationData.tags || ''}
            onChange={(e) => setOperationData({ tags: e.target.value })}
            helperText="Example: urgent, billing, follow-up"
          />
        );

      case 'merge_target':
        return (
          <FormControl fullWidth>
            <InputLabel>Primary Ticket</InputLabel>
            <Select
              value={operationData.primary_ticket_id || ''}
              label="Primary Ticket"
              onChange={(e) => setOperationData({ primary_ticket_id: e.target.value })}
            >
              {selectedTickets.map((ticketId) => (
                <MenuItem key={ticketId} value={ticketId}>
                  Ticket #{ticketId}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            Bulk Operations ({selectedTickets.length} tickets)
          </Typography>
          <IconButton onClick={onClose} disabled={loading}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom>
              Select Operation
            </Typography>
            <List>
              {bulkOperations.map((op) => (
                <ListItem
                  key={op.id}
                  button
                  selected={operation === op.id}
                  onClick={() => handleOperationChange(op.id)}
                  sx={{
                    border: 1,
                    borderColor: operation === op.id ? `${op.color}.main` : 'divider',
                    borderRadius: 1,
                    mb: 1,
                    bgcolor: operation === op.id ? `${op.color}.50` : 'transparent'
                  }}
                >
                  <ListItemIcon>
                    <Box color={operation === op.id ? `${op.color}.main` : 'text.secondary'}>
                      {op.icon}
                    </Box>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="body1" fontWeight={operation === op.id ? 'bold' : 'normal'}>
                          {op.label}
                        </Typography>
                        {op.dangerous && (
                          <Chip label="Dangerous" color="error" size="small" />
                        )}
                      </Box>
                    }
                    secondary={op.description}
                  />
                </ListItem>
              ))}
            </List>
          </Grid>

          {operation && (
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle1" gutterBottom>
                Operation Details
              </Typography>
              <Card variant="outlined">
                <CardContent>
                  {renderOperationData()}
                  
                  {operation === 'delete' && (
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        <strong>Warning:</strong> This action will permanently delete {selectedTickets.length} ticket(s) 
                        and cannot be undone. All associated messages and attachments will also be deleted.
                      </Typography>
                    </Alert>
                  )}
                  
                  {operation === 'merge' && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        All messages and attachments from secondary tickets will be moved to the primary ticket. 
                        Secondary tickets will be closed automatically.
                      </Typography>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
          )}

          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Selected Tickets ({selectedTickets.length})
            </Typography>
            <Box display="flex" gap={1} flexWrap="wrap">
              {selectedTickets.slice(0, 10).map((ticketId) => (
                <Chip
                  key={ticketId}
                  label={`#${ticketId}`}
                  size="small"
                  variant="outlined"
                />
              ))}
              {selectedTickets.length > 10 && (
                <Chip
                  label={`+${selectedTickets.length - 10} more`}
                  size="small"
                  variant="outlined"
                  color="primary"
                />
              )}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          onClick={onClose}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          onClick={handleExecute}
          variant="contained"
          disabled={loading || !operation}
          startIcon={loading ? <CircularProgress size={16} /> : null}
          color={bulkOperations.find(op => op.id === operation)?.dangerous ? 'error' : 'primary'}
        >
          {loading ? 'Executing...' : 'Execute Operation'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkOperationsDialog;

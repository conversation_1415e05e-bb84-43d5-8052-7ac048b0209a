// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  ExitToApp as ExitIcon,
  Payment as PaymentIcon,
  Email as EmailIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import api from '../api';

const UserAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      const response = await api.get('/api/admin/analytics/users');
      setAnalytics(response.data);
      setError('');
    } catch (error) {
      console.error('Error fetching analytics:', error);
      setError('Failed to fetch user analytics');
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ title, value, change, icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div">
              {value}
            </Typography>
            {change !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {change >= 0 ? (
                  <TrendingUpIcon color="success" fontSize="small" />
                ) : (
                  <TrendingDownIcon color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={change >= 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        User Analytics Overview
      </Typography>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Users"
            value={analytics?.total_users || 0}
            change={analytics?.user_growth_rate}
            icon={<PeopleIcon fontSize="large" />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="New This Month"
            value={analytics?.new_users_this_month || 0}
            change={analytics?.new_user_growth_rate}
            icon={<PersonAddIcon fontSize="large" />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Users"
            value={analytics?.active_users || 0}
            change={analytics?.active_user_change}
            icon={<TrendingUpIcon fontSize="large" />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Churn Rate"
            value={`${analytics?.churn_rate || 0}%`}
            change={analytics?.churn_rate_change}
            icon={<ExitIcon fontSize="large" />}
            color="warning"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Plan Distribution
            </Typography>
            <List>
              {analytics?.plan_distribution?.map((plan, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <PaymentIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={plan.plan_name}
                    secondary={`${plan.count} users (${plan.percentage}%)`}
                  />
                  <Chip 
                    label={plan.count} 
                    color={plan.plan_name === 'free' ? 'default' : 'primary'} 
                    size="small" 
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              User Status Overview
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <SecurityIcon color="success" />
                </ListItemIcon>
                <ListItemText
                  primary="Active Users"
                  secondary={`${analytics?.active_users || 0} users`}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <EmailIcon color="warning" />
                </ListItemIcon>
                <ListItemText
                  primary="Unverified Email"
                  secondary={`${analytics?.unverified_users || 0} users`}
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <ExitIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary="Inactive Users"
                  secondary={`${analytics?.inactive_users || 0} users`}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Trends
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <Typography variant="h4" color="primary">
                    {analytics?.daily_signups || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Signups Today
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <Typography variant="h4" color="success.main">
                    {analytics?.weekly_active_users || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Weekly Active Users
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box textAlign="center">
                  <Typography variant="h4" color="info.main">
                    {analytics?.monthly_active_users || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Monthly Active Users
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default UserAnalytics;

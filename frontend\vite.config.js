// @since 2024-1-1 to 2025-25-7
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    react({
      jsxImportSource: '@emotion/react',
      babel: {
        plugins: ['@emotion/babel-plugin']
      }
    }),
    visualizer({
      filename: "dist/stats.html",
      open: false,
      gzipSize: true,
      brotliSize: true,
      template: "treemap"
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      'lodash': 'lodash-es',
      'lodash/get': 'lodash-es/get'
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json']
  },

  define: {
    "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV || "development")
  },

  server: {
    port: 3000,
    host: true,
    strictPort: false,
    open: false,
    hmr: {
      overlay: false,
      port: 24678,
      clientPort: 24678,
      timeout: 30000,
      host: "localhost"
    },
    watch: {
      usePolling: false,
      interval: 5000,
      binaryInterval: 10000,
      depth: 99,
      followSymlinks: false,
      ignored: [
        "**/node_modules/**",
        "**/dist/**",
        "**/.git/**",
        "**/coverage/**",
        "**/logs/**",
        "**/*.log",
        "**/temp/**",
        "**/.vite/**",
        "**/public/**"
      ]
    },
    middlewareMode: false,
    fs: {
      strict: false
    },
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        timeout: 10000,
        configure: (proxy) => {
          let errorCount = 0;
          const maxErrors = 5;

          proxy.on('error', (err, req, res) => {
            errorCount++;
            if (errorCount % maxErrors === 0) {
              console.error(`Backend connection error (${errorCount} errors):`, err.message);
            }

            if (!res.headersSent) {
              res.writeHead(503, {
                "Content-Type": "application/json"
              });

              const errorResponse = {
                status: "error",
                statusCode: 503,
                message: "Unable to connect to the backend server",
                error: "SERVICE_UNAVAILABLE",
                details: {
                  possibleCauses: [
                    "The backend server is not running",
                    "The backend server is running on a different port",
                    "Network connectivity issues"
                  ],
                  troubleshooting: [
                    "Verify that the backend server is running (check terminal)",
                    "Ensure the backend is running on port 8000",
                    "Check your network connection",
                    "Verify firewall settings are not blocking the connection"
                  ],
                  errorCode: "BACKEND_CONNECTION_FAILED"
                }
              };

              res.end(JSON.stringify(errorResponse));
            }
          });

          proxy.on("proxyReq", (proxyReq, req) => {
            if (process.env.NODE_ENV === "development" && req.url !== "/api/health") {
              console.log("API Request:", req.method, req.url);
            }
          });

          proxy.on("proxyRes", (proxyRes, req) => {
            if (process.env.NODE_ENV === "development" && req.url !== "/api/health") {
              console.log("API Response:", proxyRes.statusCode, req.url);
            }
          });
        }
      }
    }
  },

  build: {
    outDir: 'dist',
    sourcemap: process.env.NODE_ENV !== "production",
    chunkSizeWarningLimit: 500,
    minify: "terser",
    target: "es2020",
    cssCodeSplit: true,
    assetsInlineLimit: 4096,
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === "production",
        drop_debugger: true,
        pure_funcs: ["console.log", "console.info", "console.debug"]
      },
      mangle: {
        keep_fnames: false
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom', 'react-router-dom', '@emotion/react', '@emotion/styled'],
          'mui-core': ['@mui/material', '@mui/system'],
          'mui-icons': ['@mui/icons-material'],
          'charts': ['chart.js', 'react-chartjs-2', 'recharts', '@nivo/bar', '@nivo/core', '@nivo/pie', '@nivo/line', '@nivo/radar', '@nivo/scatterplot', '@nivo/heatmap'],
          'utils': ['axios', 'date-fns', 'uuid', 'lodash-es', 'react-dropzone', 'antd']
        }
      }
    }
  },

  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "@mui/material",
      "@mui/icons-material",
      "@mui/x-date-pickers",
      "@mui/x-date-pickers/DatePicker",
      "@mui/x-date-pickers/LocalizationProvider",
      "@mui/x-date-pickers/AdapterDateFns",
      "@mui/x-data-grid",
      "@emotion/react",
      "@emotion/styled",
      "@emotion/cache",
      "recharts",
      "react-dropzone",
      "antd",
      "@nivo/bar",
      "@nivo/core",
      "@nivo/pie",
      "@nivo/line",
      "@nivo/radar",
      "@nivo/scatterplot",
      "@nivo/heatmap",
      "axios"
    ],
    exclude: ["@emotion/use-insertion-effect-with-fallbacks"],
    esbuildOptions: {
      loader: {
        ".js": "jsx",
        ".jsx": "jsx"
      },
      target: "es2020"
    },
    force: false
  },

  // Global esbuild options for JSX handling
  esbuild: {
    jsxFactory: 'React.createElement',
    jsxFragment: 'React.Fragment'
  },

  // Memory and performance optimizations
  clearScreen: false,
  logLevel: "warn"
});

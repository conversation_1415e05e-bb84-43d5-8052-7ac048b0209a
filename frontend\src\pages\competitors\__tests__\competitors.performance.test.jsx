import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import UnifiedCompetitorsPage from '../UnifiedCompetitorsPage';
import CompetitorDetailPage from '../CompetitorDetailPage';
import CompetitorFormPage from '../CompetitorFormPage';
import { NotificationProvider } from '../../../contexts/NotificationContext';
import { CompetitorProvider } from '../../../contexts/CompetitorContext';

// Performance monitoring utilities
const measureRenderTime = async (renderFn) => {
  const startTime = performance.now();
  await renderFn();
  const endTime = performance.now();
  return endTime - startTime;
};

const measureMemoryUsage = () => {
  if (performance.memory) {
    return {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    };
  }
  return null;
};

// Mock child components with performance considerations
jest.mock('../../../components/competitors/CompetitorList', () => {
  return React.memo(function MockCompetitorList({ competitors = [] }) {
    return (
      <div data-testid="competitor-list">
        {competitors.map((competitor, index) => (
          <div key={competitor.id || index} data-testid={`competitor-${index}`}>
            {competitor.name || `Competitor ${index + 1}`}
          </div>
        ))}
      </div>
    );
  });
});

jest.mock('../../../components/competitors/CompetitorComparison', () => {
  return React.memo(function MockCompetitorComparison() {
    return (
      <div data-testid="competitor-comparison">
        <div data-testid="comparison-chart">Chart</div>
      </div>
    );
  });
});

jest.mock('../../../components/competitors/CompetitorDetail', () => {
  return React.memo(function MockCompetitorDetail({ competitorId }) {
    return (
      <div data-testid="competitor-detail">
        <h2>Competitor {competitorId} Details</h2>
      </div>
    );
  });
});

jest.mock('../../../components/competitors/CompetitorForm', () => {
  return React.memo(function MockCompetitorForm({ isEditMode }) {
    return (
      <div data-testid="competitor-form">
        <h2>{isEditMode ? 'Edit' : 'Add'} Competitor</h2>
        <input data-testid="form-input" />
      </div>
    );
  });
});

// Generate large datasets for performance testing
const generateLargeCompetitorDataset = (size) => {
  return Array.from({ length: size }, (_, i) => ({
    id: `${i + 1}`,
    name: `Competitor ${i + 1}`,
    website: `https://competitor${i + 1}.com`,
    platforms: ['linkedin', 'twitter'],
    status: 'active'
  }));
};

// Test wrapper
const TestWrapper = ({ children }) => {
  const theme = createTheme();
  return (
    <BrowserRouter>
      <ThemeProvider theme={theme}>
        <NotificationProvider>
          <CompetitorProvider>
            {children}
          </CompetitorProvider>
        </NotificationProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Competitors Pages Performance Tests', () => {
  describe('Initial Render Performance', () => {
    test('UnifiedCompetitorsPage renders within acceptable time', async () => {
      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <UnifiedCompetitorsPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByRole('main')).toBeInTheDocument();
        });
      });

      // Should render within 1 second
      expect(renderTime).toBeLessThan(1000);
    });

    test('CompetitorDetailPage renders within acceptable time', async () => {
      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <CompetitorDetailPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByRole('main')).toBeInTheDocument();
        });
      });

      // Should render within 1 second
      expect(renderTime).toBeLessThan(1000);
    });

    test('CompetitorFormPage renders within acceptable time', async () => {
      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <CompetitorFormPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByRole('main')).toBeInTheDocument();
        });
      });

      // Should render within 1 second
      expect(renderTime).toBeLessThan(1000);
    });
  });

  describe('Interaction Performance', () => {
    test('tab switching is responsive in UnifiedCompetitorsPage', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const switchTime = await measureRenderTime(async () => {
        const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
        await user.click(compareTab);

        await waitFor(() => {
          expect(screen.getByTestId('competitor-comparison')).toBeInTheDocument();
        });
      });

      // Tab switching should be near-instantaneous
      expect(switchTime).toBeLessThan(500);
    });

    test('navigation is responsive', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const navigationTime = await measureRenderTime(async () => {
        const homeLink = screen.getByLabelText(/navigate to dashboard/i);
        await user.click(homeLink);
      });

      // Navigation should be fast
      expect(navigationTime).toBeLessThan(200);
    });

    test('form interactions are responsive', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CompetitorFormPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('competitor-form')).toBeInTheDocument();
      });

      const interactionTime = await measureRenderTime(async () => {
        const input = screen.getByTestId('form-input');
        await user.type(input, 'Test Competitor');
      });

      // Form interactions should be responsive
      expect(interactionTime).toBeLessThan(1000);
    });
  });

  describe('Memory Usage', () => {
    test('does not cause memory leaks with repeated operations', async () => {
      const user = userEvent.setup();
      const initialMemory = measureMemoryUsage();

      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      // Perform multiple operations that could cause memory leaks
      for (let i = 0; i < 10; i++) {
        // Switch tabs multiple times
        const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
        await user.click(compareTab);

        const listTab = screen.getByLabelText(/switch to competitor list tab/i);
        await user.click(listTab);
      }

      const finalMemory = measureMemoryUsage();

      if (initialMemory && finalMemory) {
        const memoryIncrease = finalMemory.used - initialMemory.used;
        const memoryIncreasePercent = (memoryIncrease / initialMemory.used) * 100;

        // Memory usage should not increase by more than 50% after repeated operations
        expect(memoryIncreasePercent).toBeLessThan(50);
      }
    });

    test('handles component unmounting properly', async () => {
      const { unmount } = render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const unmountTime = await measureRenderTime(async () => {
        unmount();
      });

      // Unmounting should be fast, indicating proper cleanup
      expect(unmountTime).toBeLessThan(100);
    });
  });

  describe('Large Dataset Performance', () => {
    test('handles large competitor lists efficiently', async () => {
      const largeDataset = generateLargeCompetitorDataset(1000);
      
      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <UnifiedCompetitorsPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByRole('main')).toBeInTheDocument();
        }, { timeout: 10000 });
      });

      // Should render within reasonable time even with large datasets
      expect(renderTime).toBeLessThan(5000);
    });

    test('maintains performance with frequent updates', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const startTime = performance.now();

      // Perform rapid tab switches
      for (let i = 0; i < 20; i++) {
        const compareTab = screen.getByLabelText(/switch to compare competitors tab/i);
        await user.click(compareTab);

        const listTab = screen.getByLabelText(/switch to competitor list tab/i);
        await user.click(listTab);
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Rapid operations should not cause performance issues
      expect(totalTime).toBeLessThan(5000);
    });
  });

  describe('Bundle Size Impact', () => {
    test('components are properly code-split', () => {
      // Check that components are properly exported
      expect(UnifiedCompetitorsPage).toBeDefined();
      expect(CompetitorDetailPage).toBeDefined();
      expect(CompetitorFormPage).toBeDefined();
      
      // Check that components are memoized
      expect(UnifiedCompetitorsPage.displayName).toBe('UnifiedCompetitorsPage');
      expect(CompetitorDetailPage.displayName).toBe('CompetitorDetailPage');
      expect(CompetitorFormPage.displayName).toBe('CompetitorFormPage');
    });
  });

  describe('Rendering Optimization', () => {
    test('components avoid unnecessary re-renders', () => {
      const renderCount = { count: 0 };
      
      const TestComponent = React.memo(() => {
        renderCount.count++;
        return <UnifiedCompetitorsPage />;
      });

      const { rerender } = render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      const initialRenderCount = renderCount.count;

      // Re-render with same props
      rerender(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // Should not cause additional renders due to memoization
      expect(renderCount.count).toBe(initialRenderCount);
    });

    test('handles prop changes efficiently', () => {
      const { rerender } = render(
        <TestWrapper>
          <UnifiedCompetitorsPage isEmbedded={false} />
        </TestWrapper>
      );

      const rerenderTime = measureRenderTime(() => {
        rerender(
          <TestWrapper>
            <UnifiedCompetitorsPage isEmbedded={true} />
          </TestWrapper>
        );
      });

      // Prop changes should be handled efficiently
      expect(rerenderTime).toBeLessThan(100);
    });
  });

  describe('Async Operations Performance', () => {
    test('handles concurrent operations efficiently', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <UnifiedCompetitorsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByRole('main')).toBeInTheDocument();
      });

      const startTime = performance.now();

      // Simulate concurrent operations
      const operations = [
        user.click(screen.getByLabelText(/switch to compare competitors tab/i)),
        user.click(screen.getByLabelText(/add new competitor/i)),
        user.click(screen.getByLabelText(/navigate to dashboard/i))
      ];

      await Promise.allSettled(operations);

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Concurrent operations should complete quickly
      expect(totalTime).toBeLessThan(2000);
    });
  });

  describe('Error Recovery Performance', () => {
    test('error boundaries do not impact performance', async () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      const renderTime = await measureRenderTime(async () => {
        render(
          <TestWrapper>
            <UnifiedCompetitorsPage />
          </TestWrapper>
        );

        await waitFor(() => {
          expect(screen.getByRole('main')).toBeInTheDocument();
        });
      });

      // Error boundaries should not significantly impact render time
      expect(renderTime).toBeLessThan(1000);
      
      consoleSpy.mockRestore();
    });
  });
});

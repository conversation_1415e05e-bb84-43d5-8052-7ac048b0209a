import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect } from 'vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import '@testing-library/jest-dom';
import CustomCardContent from '../CustomCardContent';

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
});

// Test wrapper with theme
const TestWrapper = ({ children }) => {
  const theme = createTheme({
    palette: {
      primary: {
        main: '#4E40C5',
      },
      error: {
        main: '#F44336',
      },
      text: {
        secondary: '#666666',
      },
      divider: '#E0E0E0',
      background: {
        paper: '#FFFFFF',
      },
    },
    spacing: (factor) => `${8 * factor}px`,
    transitions: {
      create: () => 'all 0.3s ease',
    },
  });
  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

describe('CustomCardContent', () => {
  const mockProps = {
    children: <div>Card content</div>
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  test('renders card content with children correctly', () => {
    render(
      <TestWrapper>
        <CustomCardContent {...mockProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Card content')).toBeInTheDocument();
    expect(screen.getByRole('region')).toBeInTheDocument();
  });

  test('applies different variants correctly', () => {
    const { rerender } = render(
      <TestWrapper>
        <CustomCardContent {...mockProps} variant="compact" />
      </TestWrapper>
    );

    let content = screen.getByRole('region');
    expect(content).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCardContent {...mockProps} variant="spacious" />
      </TestWrapper>
    );

    content = screen.getByRole('region');
    expect(content).toBeInTheDocument();

    rerender(
      <TestWrapper>
        <CustomCardContent {...mockProps} variant="flush" />
      </TestWrapper>
    );

    content = screen.getByRole('region');
    expect(content).toBeInTheDocument();
  });

  test('shows loading state correctly', () => {
    render(
      <TestWrapper>
        <CustomCardContent loading={true} />
      </TestWrapper>
    );

    // Should show skeleton loading
    expect(screen.getAllByTestId('skeleton')).toHaveLength(3);
  });

  test('shows error state correctly', () => {
    const error = 'Test error message';
    
    render(
      <TestWrapper>
        <CustomCardContent error={error} />
      </TestWrapper>
    );

    expect(screen.getByText('Content Error')).toBeInTheDocument();
    expect(screen.getByText(error)).toBeInTheDocument();
  });

  test('handles collapsible functionality', async () => {
    const user = userEvent.setup();
    const onToggleCollapse = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardContent 
          {...mockProps} 
          collapsible={true}
          onToggleCollapse={onToggleCollapse}
        />
      </TestWrapper>
    );

    const collapseButton = screen.getByLabelText('Collapse');
    await user.click(collapseButton);

    expect(onToggleCollapse).toHaveBeenCalledWith(true);
  });

  test('handles copy functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <CustomCardContent 
          {...mockProps} 
          enableCopy={true}
          copyText="Text to copy"
        />
      </TestWrapper>
    );

    const copyButton = screen.getByLabelText('Copy content');
    await user.click(copyButton);

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith('Text to copy');
  });

  test('handles fullscreen toggle', async () => {
    const user = userEvent.setup();
    const onFullscreenToggle = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardContent 
          {...mockProps} 
          onFullscreenToggle={onFullscreenToggle}
        />
      </TestWrapper>
    );

    const fullscreenButton = screen.getByLabelText('Fullscreen');
    await user.click(fullscreenButton);

    expect(onFullscreenToggle).toHaveBeenCalled();
  });

  test('shows progress bar when enabled', () => {
    render(
      <TestWrapper>
        <CustomCardContent 
          {...mockProps} 
          showProgress={true}
          progress={50}
        />
      </TestWrapper>
    );

    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
    expect(progressBar).toHaveAttribute('aria-valuenow', '50');
  });

  test('renders sections correctly', () => {
    const sections = [
      {
        id: 'section1',
        title: 'Section 1',
        content: <div>Section 1 content</div>,
        tags: ['tag1', 'tag2']
      },
      {
        id: 'section2',
        title: 'Section 2',
        content: <div>Section 2 content</div>,
        variant: 'highlighted'
      }
    ];

    render(
      <TestWrapper>
        <CustomCardContent sections={sections} />
      </TestWrapper>
    );

    expect(screen.getByText('Section 1')).toBeInTheDocument();
    expect(screen.getByText('Section 1 content')).toBeInTheDocument();
    expect(screen.getByText('tag1')).toBeInTheDocument();
    expect(screen.getByText('tag2')).toBeInTheDocument();
    
    expect(screen.getByText('Section 2')).toBeInTheDocument();
    expect(screen.getByText('Section 2 content')).toBeInTheDocument();
  });

  test('handles media content correctly', () => {
    const mediaConfig = {
      src: 'https://example.com/image.jpg',
      type: 'image',
      alt: 'Test image'
    };

    render(
      <TestWrapper>
        <CustomCardContent 
          contentType="media"
          mediaConfig={mediaConfig}
        />
      </TestWrapper>
    );

    const image = screen.getByAltText('Test image');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/image.jpg');
  });

  test('handles video media content', () => {
    const mediaConfig = {
      src: 'https://example.com/video.mp4',
      type: 'video',
      alt: 'Test video',
      controls: true,
      autoPlay: false
    };

    render(
      <TestWrapper>
        <CustomCardContent 
          contentType="media"
          mediaConfig={mediaConfig}
        />
      </TestWrapper>
    );

    const video = screen.getByLabelText('Test video');
    expect(video).toBeInTheDocument();
    expect(video).toHaveAttribute('src', 'https://example.com/video.mp4');
    expect(video).toHaveAttribute('controls');
  });

  test('handles zoom functionality', async () => {
    const user = userEvent.setup();
    const onZoomChange = vi.fn();
    
    const mediaConfig = {
      src: 'https://example.com/image.jpg',
      type: 'image',
      alt: 'Test image'
    };

    render(
      <TestWrapper>
        <CustomCardContent 
          contentType="media"
          mediaConfig={mediaConfig}
          enableZoom={true}
          onZoomChange={onZoomChange}
        />
      </TestWrapper>
    );

    const zoomInButton = screen.getByLabelText('Zoom in');
    await user.click(zoomInButton);

    expect(onZoomChange).toHaveBeenCalledWith(1.1);

    const zoomOutButton = screen.getByLabelText('Zoom out');
    await user.click(zoomOutButton);

    expect(onZoomChange).toHaveBeenCalledWith(0.9);
  });

  test('handles analytics tracking', async () => {
    const user = userEvent.setup();
    const onAnalytics = vi.fn();
    
    render(
      <TestWrapper>
        <CustomCardContent 
          {...mockProps} 
          collapsible={true}
          enableAnalytics={true}
          onAnalytics={onAnalytics}
        />
      </TestWrapper>
    );

    const collapseButton = screen.getByLabelText('Collapse');
    await user.click(collapseButton);

    expect(onAnalytics).toHaveBeenCalledWith(
      expect.objectContaining({
        component: 'CustomCardContent',
        action: 'toggle_collapse'
      })
    );
  });

  test('validates content correctly', () => {
    const validationRules = {
      required: true
    };

    render(
      <TestWrapper>
        <CustomCardContent 
          validationRules={validationRules}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Content Error')).toBeInTheDocument();
    expect(screen.getByText('Content is required')).toBeInTheDocument();
  });

  test('validates content length', () => {
    const validationRules = {
      maxLength: 5
    };

    render(
      <TestWrapper>
        <CustomCardContent 
          validationRules={validationRules}
        >
          This is a very long content that exceeds the maximum length
        </CustomCardContent>
      </TestWrapper>
    );

    expect(screen.getByText('Content Error')).toBeInTheDocument();
    expect(screen.getByText('Content exceeds maximum length of 5')).toBeInTheDocument();
  });

  test('applies custom styles', () => {
    const customSx = { backgroundColor: 'red' };
    
    render(
      <TestWrapper>
        <CustomCardContent {...mockProps} sx={customSx} />
      </TestWrapper>
    );

    const content = screen.getByRole('region');
    expect(content).toBeInTheDocument();
  });

  test('handles maxHeight constraint', () => {
    render(
      <TestWrapper>
        <CustomCardContent {...mockProps} maxHeight={200} />
      </TestWrapper>
    );

    const content = screen.getByRole('region');
    expect(content).toBeInTheDocument();
  });

  test('handles fullscreen mode', () => {
    render(
      <TestWrapper>
        <CustomCardContent {...mockProps} fullscreen={true} />
      </TestWrapper>
    );

    const content = screen.getByRole('region');
    expect(content).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <CustomCardContent 
          {...mockProps} 
          ariaLabel="Custom content"
          role="main"
        />
      </TestWrapper>
    );

    const content = screen.getByRole('main');
    expect(content).toHaveAttribute('aria-label', 'Custom content');
  });

  test('handles collapsed state correctly', () => {
    render(
      <TestWrapper>
        <CustomCardContent 
          {...mockProps} 
          collapsible={true}
          collapsed={true}
        />
      </TestWrapper>
    );

    // Content should be collapsed, so children might not be visible
    const expandButton = screen.getByLabelText('Expand');
    expect(expandButton).toBeInTheDocument();
  });

  test('handles copy error gracefully', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock clipboard to fail
    navigator.clipboard.writeText.mockRejectedValueOnce(new Error('Clipboard error'));
    
    render(
      <TestWrapper>
        <CustomCardContent 
          {...mockProps} 
          enableCopy={true}
          copyText="Text to copy"
        />
      </TestWrapper>
    );

    const copyButton = screen.getByLabelText('Copy content');
    await user.click(copyButton);

    expect(consoleSpy).toHaveBeenCalledWith('Copy failed:', expect.any(Error));
    
    consoleSpy.mockRestore();
  });

  test('handles custom spacing', () => {
    render(
      <TestWrapper>
        <CustomCardContent {...mockProps} spacing="24px" />
      </TestWrapper>
    );

    const content = screen.getByRole('region');
    expect(content).toBeInTheDocument();
  });
});

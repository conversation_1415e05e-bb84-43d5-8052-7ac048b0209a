"""
Product Variants Management Service for E-commerce Integration.
Provides comprehensive variant creation, editing, and management capabilities.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
from decimal import Decimal
from bson import ObjectId
from itertools import product as itertools_product

from app.models.ecommerce import SyncedProduct
from app.models.user import User
from app.db.mongodb import get_database
from app.core.redis import get_redis_client
from app.core.monitoring import monitor_performance, log_audit_event, OperationType
from app.services.ecommerce.inventory_service import inventory_service
from app.services.content_generation.content_generator import content_generator

logger = logging.getLogger(__name__)

# Collection names
PRODUCTS_COLLECTION = "synced_products"
VARIANTS_COLLECTION = "product_variants"
VARIANT_OPTIONS_COLLECTION = "variant_options"

# Redis keys
VARIANT_CACHE_KEY = "variants:{product_id}"
VARIANT_OPTIONS_CACHE_KEY = "variant_options:{product_id}"


class ProductVariantsService:
    """
    Comprehensive product variants management service.
    """
    
    def __init__(self):
        self.redis_client = None
        self.db = None
        
    async def _get_redis_client(self):
        """Get Redis client for caching."""
        if not self.redis_client:
            self.redis_client = await get_redis_client()
        return self.redis_client
    
    async def _get_database(self):
        """Get MongoDB database."""
        if not self.db:
            self.db = await get_database()
        return self.db
    
    @monitor_performance("create_variant_options")
    async def create_variant_options(
        self,
        user_id: str,
        product_id: str,
        options: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create variant options for a product.
        
        Args:
            user_id: User ID
            product_id: Product ID
            options: List of variant options (e.g., [{"name": "Color", "values": ["Red", "Blue"]}])
            
        Returns:
            Created options result
        """
        try:
            db = await self._get_database()
            
            # Verify product ownership
            product = await db[PRODUCTS_COLLECTION].find_one({
                "_id": ObjectId(product_id),
                "user_id": ObjectId(user_id)
            })
            
            if not product:
                raise ValueError(f"Product {product_id} not found or access denied")
            
            # Validate options
            for option in options:
                if not option.get("name") or not option.get("values"):
                    raise ValueError("Each option must have a name and values")
                
                if len(option["values"]) < 1:
                    raise ValueError("Each option must have at least one value")
                
                if len(option["values"]) > 50:
                    raise ValueError("Maximum 50 values allowed per option")
            
            # Check if options already exist
            existing_options = await db[VARIANT_OPTIONS_COLLECTION].find_one({
                "product_id": ObjectId(product_id)
            })
            
            if existing_options:
                # Update existing options
                await db[VARIANT_OPTIONS_COLLECTION].update_one(
                    {"product_id": ObjectId(product_id)},
                    {
                        "$set": {
                            "options": options,
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                options_id = existing_options["_id"]
            else:
                # Create new options
                options_doc = {
                    "product_id": ObjectId(product_id),
                    "user_id": ObjectId(user_id),
                    "options": options,
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                
                result = await db[VARIANT_OPTIONS_COLLECTION].insert_one(options_doc)
                options_id = result.inserted_id
            
            # Clear cache
            redis = await self._get_redis_client()
            if redis:
                cache_key = VARIANT_OPTIONS_CACHE_KEY.format(product_id=product_id)
                await redis.delete(cache_key)
            
            # Log audit event
            log_audit_event(
                operation_type=OperationType.CREATE,
                resource_type="variant_options",
                resource_id=str(options_id),
                user_id=user_id,
                details={
                    "product_id": product_id,
                    "options_count": len(options)
                }
            )
            
            return {
                "success": True,
                "options_id": str(options_id),
                "product_id": product_id,
                "options": options
            }
            
        except Exception as e:
            logger.error(f"Error creating variant options for product {product_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "product_id": product_id
            }
    
    @monitor_performance("generate_variant_combinations")
    async def generate_variant_combinations(
        self,
        user_id: str,
        product_id: str,
        auto_generate_skus: bool = True,
        auto_generate_prices: bool = True,
        base_price_adjustment: Optional[Decimal] = None
    ) -> Dict[str, Any]:
        """
        Generate all possible variant combinations from options.
        
        Args:
            user_id: User ID
            product_id: Product ID
            auto_generate_skus: Whether to auto-generate SKUs
            auto_generate_prices: Whether to auto-generate prices
            base_price_adjustment: Base price adjustment for variants
            
        Returns:
            Generated combinations result
        """
        try:
            db = await self._get_database()
            
            # Get variant options
            options_doc = await db[VARIANT_OPTIONS_COLLECTION].find_one({
                "product_id": ObjectId(product_id),
                "user_id": ObjectId(user_id)
            })
            
            if not options_doc:
                raise ValueError("No variant options found for this product")
            
            # Get base product for pricing
            product = await db[PRODUCTS_COLLECTION].find_one({
                "_id": ObjectId(product_id)
            })
            
            base_price = Decimal(str(product.get("price", 0)))
            base_sku = product.get("sku", "")
            
            # Generate all combinations
            options = options_doc["options"]
            option_values = [option["values"] for option in options]
            option_names = [option["name"] for option in options]
            
            combinations = list(itertools_product(*option_values))
            
            if len(combinations) > 1000:
                raise ValueError("Too many combinations (max 1000). Please reduce option values.")
            
            variants = []
            for i, combination in enumerate(combinations):
                variant = {
                    "option_values": dict(zip(option_names, combination)),
                    "title": f"{product.get('title', 'Product')} - {' / '.join(combination)}",
                    "inventory_quantity": 0,
                    "inventory_policy": "deny",
                    "fulfillment_service": "manual",
                    "requires_shipping": True,
                    "taxable": True,
                    "weight": product.get("weight", 0),
                    "weight_unit": product.get("weight_unit", "kg"),
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
                
                # Auto-generate SKU
                if auto_generate_skus:
                    sku_suffix = "-".join([
                        value[:3].upper().replace(" ", "") 
                        for value in combination
                    ])
                    variant["sku"] = f"{base_sku}-{sku_suffix}" if base_sku else f"VAR-{i+1:03d}-{sku_suffix}"
                
                # Auto-generate price
                if auto_generate_prices:
                    if base_price_adjustment:
                        variant["price"] = float(base_price + base_price_adjustment)
                    else:
                        variant["price"] = float(base_price)
                else:
                    variant["price"] = float(base_price)
                
                variants.append(variant)
            
            return {
                "success": True,
                "product_id": product_id,
                "total_combinations": len(combinations),
                "variants": variants,
                "option_names": option_names
            }
            
        except Exception as e:
            logger.error(f"Error generating variant combinations for product {product_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "product_id": product_id
            }
    
    @monitor_performance("create_product_variants")
    async def create_product_variants(
        self,
        user_id: str,
        product_id: str,
        variants: List[Dict[str, Any]],
        sync_to_platform: bool = True
    ) -> Dict[str, Any]:
        """
        Create product variants in database and optionally sync to platform.
        
        Args:
            user_id: User ID
            product_id: Product ID
            variants: List of variant data
            sync_to_platform: Whether to sync to e-commerce platform
            
        Returns:
            Creation result with variant IDs
        """
        try:
            db = await self._get_database()
            
            # Verify product ownership
            product = await db[PRODUCTS_COLLECTION].find_one({
                "_id": ObjectId(product_id),
                "user_id": ObjectId(user_id)
            })
            
            if not product:
                raise ValueError(f"Product {product_id} not found or access denied")
            
            # Validate variants
            if len(variants) > 1000:
                raise ValueError("Maximum 1000 variants allowed per product")
            
            # Prepare variant documents
            variant_docs = []
            for variant in variants:
                variant_doc = {
                    "product_id": ObjectId(product_id),
                    "user_id": ObjectId(user_id),
                    "store_id": product.get("store_id"),
                    "external_variant_id": None,  # Will be set after platform sync
                    **variant
                }
                variant_docs.append(variant_doc)
            
            # Insert variants
            if variant_docs:
                result = await db[VARIANTS_COLLECTION].insert_many(variant_docs)
                variant_ids = [str(id) for id in result.inserted_ids]
            else:
                variant_ids = []
            
            # Update product to indicate it has variants
            await db[PRODUCTS_COLLECTION].update_one(
                {"_id": ObjectId(product_id)},
                {
                    "$set": {
                        "has_variants": True,
                        "variant_count": len(variants),
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            # Clear cache
            redis = await self._get_redis_client()
            if redis:
                cache_key = VARIANT_CACHE_KEY.format(product_id=product_id)
                await redis.delete(cache_key)
            
            # Sync to platform if requested
            sync_results = []
            if sync_to_platform and variant_docs:
                try:
                    # This would integrate with platform-specific sync services
                    # For now, we'll mark as pending sync
                    await db[VARIANTS_COLLECTION].update_many(
                        {"_id": {"$in": result.inserted_ids}},
                        {"$set": {"sync_status": "pending"}}
                    )
                    sync_results.append("Variants queued for platform sync")
                except Exception as sync_error:
                    logger.warning(f"Platform sync failed: {str(sync_error)}")
                    sync_results.append(f"Platform sync failed: {str(sync_error)}")
            
            # Log audit event
            log_audit_event(
                operation_type=OperationType.CREATE,
                resource_type="product_variants",
                resource_id=product_id,
                user_id=user_id,
                details={
                    "variants_created": len(variant_ids),
                    "sync_to_platform": sync_to_platform
                }
            )
            
            return {
                "success": True,
                "product_id": product_id,
                "variants_created": len(variant_ids),
                "variant_ids": variant_ids,
                "sync_results": sync_results
            }
            
        except Exception as e:
            logger.error(f"Error creating variants for product {product_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "product_id": product_id
            }


    @monitor_performance("get_product_variants")
    async def get_product_variants(
        self,
        user_id: str,
        product_id: str,
        include_inventory: bool = True,
        include_pricing: bool = True
    ) -> Dict[str, Any]:
        """
        Get all variants for a product with optional enrichment.

        Args:
            user_id: User ID
            product_id: Product ID
            include_inventory: Whether to include inventory data
            include_pricing: Whether to include pricing data

        Returns:
            Product variants with enriched data
        """
        try:
            db = await self._get_database()

            # Check cache first
            redis = await self._get_redis_client()
            cache_key = VARIANT_CACHE_KEY.format(product_id=product_id)

            if redis:
                cached_variants = await redis.get(cache_key)
                if cached_variants:
                    import json
                    return json.loads(cached_variants)

            # Get variants from database
            variants = await db[VARIANTS_COLLECTION].find({
                "product_id": ObjectId(product_id),
                "user_id": ObjectId(user_id)
            }).to_list(None)

            # Get variant options
            options_doc = await db[VARIANT_OPTIONS_COLLECTION].find_one({
                "product_id": ObjectId(product_id)
            })

            # Enrich variants with additional data
            enriched_variants = []
            for variant in variants:
                enriched_variant = {
                    "id": str(variant["_id"]),
                    "product_id": str(variant["product_id"]),
                    "external_variant_id": variant.get("external_variant_id"),
                    "title": variant.get("title"),
                    "sku": variant.get("sku"),
                    "price": variant.get("price"),
                    "option_values": variant.get("option_values", {}),
                    "inventory_quantity": variant.get("inventory_quantity", 0),
                    "weight": variant.get("weight"),
                    "weight_unit": variant.get("weight_unit"),
                    "requires_shipping": variant.get("requires_shipping", True),
                    "taxable": variant.get("taxable", True),
                    "sync_status": variant.get("sync_status", "not_synced"),
                    "created_at": variant.get("created_at"),
                    "updated_at": variant.get("updated_at")
                }

                # Add inventory insights if requested
                if include_inventory:
                    enriched_variant["inventory_insights"] = {
                        "is_low_stock": variant.get("inventory_quantity", 0) <= 10,
                        "is_out_of_stock": variant.get("inventory_quantity", 0) == 0,
                        "last_inventory_update": variant.get("updated_at")
                    }

                # Add pricing insights if requested
                if include_pricing:
                    base_price = variant.get("price", 0)
                    enriched_variant["pricing_insights"] = {
                        "price_tier": "low" if base_price < 50 else "medium" if base_price < 200 else "high",
                        "has_discount": False,  # Could be enhanced with discount logic
                        "currency": "USD"  # Could be dynamic based on user preferences
                    }

                enriched_variants.append(enriched_variant)

            result = {
                "success": True,
                "product_id": product_id,
                "total_variants": len(enriched_variants),
                "variants": enriched_variants,
                "variant_options": options_doc.get("options", []) if options_doc else [],
                "has_variants": len(enriched_variants) > 0
            }

            # Cache the result
            if redis:
                import json
                await redis.setex(cache_key, 1800, json.dumps(result, default=str))

            return result

        except Exception as e:
            logger.error(f"Error getting variants for product {product_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "product_id": product_id
            }

    @monitor_performance("update_variant")
    async def update_variant(
        self,
        user_id: str,
        variant_id: str,
        updates: Dict[str, Any],
        sync_to_platform: bool = True
    ) -> Dict[str, Any]:
        """
        Update a specific product variant.

        Args:
            user_id: User ID
            variant_id: Variant ID to update
            updates: Fields to update
            sync_to_platform: Whether to sync changes to platform

        Returns:
            Update result
        """
        try:
            db = await self._get_database()

            # Verify variant ownership
            variant = await db[VARIANTS_COLLECTION].find_one({
                "_id": ObjectId(variant_id),
                "user_id": ObjectId(user_id)
            })

            if not variant:
                raise ValueError(f"Variant {variant_id} not found or access denied")

            # Prepare update data
            update_data = {
                **updates,
                "updated_at": datetime.now(timezone.utc)
            }

            # Handle inventory updates
            if "inventory_quantity" in updates:
                new_quantity = updates["inventory_quantity"]

                # Update inventory tracking
                await inventory_service.update_inventory(
                    user_id=user_id,
                    store_id=str(variant.get("store_id")),
                    product_id=str(variant.get("product_id")),
                    new_quantity=new_quantity,
                    variant_id=variant_id,
                    reason="Variant update",
                    changed_by=user_id
                )

            # Update variant
            await db[VARIANTS_COLLECTION].update_one(
                {"_id": ObjectId(variant_id)},
                {"$set": update_data}
            )

            # Clear cache
            redis = await self._get_redis_client()
            if redis:
                product_id = str(variant.get("product_id"))
                cache_key = VARIANT_CACHE_KEY.format(product_id=product_id)
                await redis.delete(cache_key)

            # Sync to platform if requested
            sync_result = None
            if sync_to_platform:
                try:
                    # Mark for platform sync
                    await db[VARIANTS_COLLECTION].update_one(
                        {"_id": ObjectId(variant_id)},
                        {"$set": {"sync_status": "pending"}}
                    )
                    sync_result = "Variant queued for platform sync"
                except Exception as sync_error:
                    logger.warning(f"Platform sync failed: {str(sync_error)}")
                    sync_result = f"Platform sync failed: {str(sync_error)}"

            # Log audit event
            log_audit_event(
                operation_type=OperationType.UPDATE,
                resource_type="product_variant",
                resource_id=variant_id,
                user_id=user_id,
                details={
                    "updates": list(updates.keys()),
                    "sync_to_platform": sync_to_platform
                }
            )

            return {
                "success": True,
                "variant_id": variant_id,
                "updated_fields": list(updates.keys()),
                "sync_result": sync_result
            }

        except Exception as e:
            logger.error(f"Error updating variant {variant_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "variant_id": variant_id
            }

    @monitor_performance("bulk_update_variants")
    async def bulk_update_variants(
        self,
        user_id: str,
        product_id: str,
        updates: List[Dict[str, Any]],
        sync_to_platform: bool = True
    ) -> Dict[str, Any]:
        """
        Bulk update multiple variants for a product.

        Args:
            user_id: User ID
            product_id: Product ID
            updates: List of variant updates with variant_id and update data
            sync_to_platform: Whether to sync changes to platform

        Returns:
            Bulk update result
        """
        try:
            db = await self._get_database()

            successful_updates = 0
            failed_updates = 0
            update_results = []

            for update_item in updates:
                variant_id = update_item.get("variant_id")
                update_data = update_item.get("updates", {})

                if not variant_id:
                    failed_updates += 1
                    update_results.append({
                        "variant_id": None,
                        "result": {
                            "success": False,
                            "error": "Missing variant_id"
                        }
                    })
                    continue

                try:
                    result = await self.update_variant(
                        user_id=user_id,
                        variant_id=str(variant_id),
                        updates=update_data,
                        sync_to_platform=False  # We'll sync in bulk
                    )

                    if result.get("success"):
                        successful_updates += 1
                    else:
                        failed_updates += 1

                    update_results.append({
                        "variant_id": variant_id,
                        "result": result
                    })

                except Exception as e:
                    failed_updates += 1
                    update_results.append({
                        "variant_id": variant_id,
                        "result": {
                            "success": False,
                            "error": str(e)
                        }
                    })

            # Bulk sync to platform if requested
            sync_result = None
            if sync_to_platform and successful_updates > 0:
                try:
                    variant_ids = [
                        ObjectId(item["variant_id"])
                        for item in update_results
                        if item["result"].get("success")
                    ]

                    await db[VARIANTS_COLLECTION].update_many(
                        {"_id": {"$in": variant_ids}},
                        {"$set": {"sync_status": "pending"}}
                    )
                    sync_result = f"{len(variant_ids)} variants queued for platform sync"
                except Exception as sync_error:
                    logger.warning(f"Bulk platform sync failed: {str(sync_error)}")
                    sync_result = f"Bulk platform sync failed: {str(sync_error)}"

            # Clear cache
            redis = await self._get_redis_client()
            if redis:
                cache_key = VARIANT_CACHE_KEY.format(product_id=product_id)
                await redis.delete(cache_key)

            return {
                "success": True,
                "product_id": product_id,
                "total_updates": len(updates),
                "successful_updates": successful_updates,
                "failed_updates": failed_updates,
                "update_results": update_results,
                "sync_result": sync_result
            }

        except Exception as e:
            logger.error(f"Error in bulk variant update for product {product_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "product_id": product_id
            }


# Create singleton instance
variants_service = ProductVariantsService()

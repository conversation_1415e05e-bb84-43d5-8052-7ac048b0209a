// @since 2024-1-1 to 2025-25-7
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,

  FormControlLabel,
  Switch,
  Divider,
  CircularProgress,
  Paper,
  Alert,
  IconButton,

} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

import { useNotification } from '../../hooks/useNotification';
import api from '../../api';
import CampaignUnifiedBranding from '../../components/campaigns/CampaignUnifiedBranding';

const CreateABTest = () => {
  const { campaignId } = useParams();
  const navigate = useNavigate();
  const { showSuccessNotification, showErrorNotification } = useNotification();

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [campaign, setCampaign] = useState(null);

  const [formData, setFormData] = useState({
    ab_test_name: '',
    ab_test_description: '',
    ab_test_end_date: null,
    ab_test_auto_end: false,
    ab_test_auto_pause: true,
    variants: [
      {
        name: 'Control',
        description: 'Original campaign version',
        is_control: true,
        branding_preferences: null
      }
    ]
  });

  // Load campaign data
  useEffect(() => {
    const fetchCampaignData = async () => {
      setLoading(true);

      try {
        if (campaignId) {
          // Fetch specific campaign
          const response = await api.get(`/api/campaigns/${campaignId}`);
          setCampaign(response.data);

          // Initialize control variant with campaign branding
          setFormData(prev => ({
            ...prev,
            ab_test_name: `${response.data.name} - A/B Test`,
            ab_test_description: `Testing variations of ${response.data.name}`,
            variants: [
              {
                name: 'Control',
                description: 'Original campaign version',
                is_control: true,
                branding_preferences: response.data.branding_preferences
              }
            ]
          }));
        } else {
          // Redirect to campaigns if no campaign ID
          navigate('/campaigns');
        }
      } catch (error) {
        console.error('Error fetching campaign data:', error);
        showErrorNotification('Failed to load campaign data');
        navigate('/campaigns');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaignData();
  }, [campaignId, navigate, showErrorNotification]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: e.target.type === 'checkbox' ? checked : value
    }));
  };

  // Handle date change
  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      ab_test_end_date: date
    }));
  };

  // Add a new variant
  const addVariant = () => {
    const newVariant = {
      name: `Variant ${formData.variants.length}`,
      description: `Test variant ${formData.variants.length}`,
      is_control: false,
      branding_preferences: campaign.branding_preferences
    };

    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, newVariant]
    }));
  };

  // Remove a variant
  const removeVariant = (index) => {
    if (index === 0) {
      showErrorNotification('Cannot remove the control variant');
      return;
    }

    setFormData(prev => ({
      ...prev,
      variants: prev.variants.filter((_, i) => i !== index)
    }));
  };

  // Update variant name
  const updateVariantName = (index, value) => {
    const updatedVariants = [...formData.variants];
    updatedVariants[index].name = value;

    setFormData(prev => ({
      ...prev,
      variants: updatedVariants
    }));
  };

  // Update variant description
  const updateVariantDescription = (index, value) => {
    const updatedVariants = [...formData.variants];
    updatedVariants[index].description = value;

    setFormData(prev => ({
      ...prev,
      variants: updatedVariants
    }));
  };

  // Update variant branding
  const updateVariantBranding = (index, branding) => {
    const updatedVariants = [...formData.variants];
    updatedVariants[index].branding_preferences = branding;

    setFormData(prev => ({
      ...prev,
      variants: updatedVariants
    }));
  };

  // Submit the form
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setSubmitting(true);

    try {
      const requestData = {
        campaign_id: campaignId,
        ab_test_name: formData.ab_test_name,
        ab_test_description: formData.ab_test_description,
        ab_test_end_date: formData.ab_test_end_date,
        ab_test_auto_end: formData.ab_test_auto_end,
        ab_test_auto_pause: formData.ab_test_auto_pause,
        variants: formData.variants
      };

      await api.post('/api/campaigns/ab-test', requestData);

      showSuccessNotification('A/B test created successfully');
      navigate(`/analytics/campaign-comparison/${campaignId}`);
    } catch (error) {
      console.error('Error creating A/B test:', error);
      showErrorNotification('Failed to create A/B test');
    } finally {
      setSubmitting(false);
    }
  };

  // Validate the form
  const validateForm = () => {
    if (!formData.ab_test_name.trim()) {
      showErrorNotification('Please enter a name for the A/B test');
      return false;
    }

    if (!formData.ab_test_description.trim()) {
      showErrorNotification('Please enter a description for the A/B test');
      return false;
    }

    if (formData.variants.length < 2) {
      showErrorNotification('Please add at least one variant besides the control');
      return false;
    }

    return true;
  };

  if (loading) {
    return (
      <Box sx={{ py: 3, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!campaign) {
    return (
      <Box sx={{ py: 3 }}>
        <Alert severity="error">Campaign not found</Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/campaigns')}
          sx={{ mt: 2 }}
        >
          Back to Campaigns
        </Button>
      </Box>
    );
  }

  // Check if this campaign is already an A/B test
  if (campaign.is_ab_test) {
    return (
      <Box sx={{ py: 3 }}>
        <Alert severity="warning">This campaign is already an A/B test</Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/campaigns')}
          sx={{ mt: 2 }}
        >
          Back to Campaigns
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ py: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/campaigns')}
            sx={{ mb: 1 }}
          >
            Back to Campaigns
          </Button>
          <Typography variant="h4">
            <CompareArrowsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Create A/B Test for: {campaign.name}
          </Typography>
        </Box>
      </Box>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            A/B Test Details
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="A/B Test Name"
                name="ab_test_name"
                value={formData.ab_test_name}
                onChange={handleChange}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <DateTimePicker
                label="End Date (Optional)"
                value={formData.ab_test_end_date}
                onChange={handleDateChange}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="A/B Test Description"
                name="ab_test_description"
                value={formData.ab_test_description}
                onChange={handleChange}
                required
                multiline
                rows={3}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.ab_test_auto_end}
                    onChange={handleChange}
                    name="ab_test_auto_end"
                    color="primary"
                  />
                }
                label="Automatically end test when statistical significance is reached"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.ab_test_auto_pause}
                    onChange={handleChange}
                    name="ab_test_auto_pause"
                    color="primary"
                  />
                }
                label="Automatically pause underperforming variants"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Test Variants
            </Typography>

            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={addVariant}
            >
              Add Variant
            </Button>
          </Box>

          {formData.variants.map((variant, index) => (
            <Paper key={index} sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">
                  {variant.is_control ? 'Control Variant' : `Variant ${index}`}
                </Typography>

                {!variant.is_control && (
                  <IconButton color="error" onClick={() => removeVariant(index)}>
                    <DeleteIcon />
                  </IconButton>
                )}
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Variant Name"
                    value={variant.name}
                    onChange={(e) => updateVariantName(index, e.target.value)}
                    required
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Variant Description"
                    value={variant.description}
                    onChange={(e) => updateVariantDescription(index, e.target.value)}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="subtitle2" gutterBottom>
                    Branding Settings for this Variant
                  </Typography>

                  <CampaignUnifiedBranding
                    campaignBranding={variant.branding_preferences}
                    useCustomBranding={true}
                    onBrandingChange={(branding) => updateVariantBranding(index, branding)}
                    onUseCustomBrandingChange={() => {}}
                    campaignName={variant.name}
                  />
                </Grid>
              </Grid>
            </Paper>
          ))}

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              disabled={submitting}
              startIcon={submitting ? <CircularProgress size={20} /> : <CheckCircleIcon />}
            >
              {submitting ? 'Creating A/B Test...' : 'Create A/B Test'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CreateABTest;

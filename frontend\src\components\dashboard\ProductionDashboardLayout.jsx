// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect, useCallback, useMemo, memo, forwardRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Container,
  Grid,
  Typography,
  Button,
  IconButton,
  Tooltip,
  Alert,
  AlertTitle,
  Snackbar,
  useTheme,
  useMediaQuery,
  Fade,
  Skeleton,
  alpha,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Dashboard as DashboardIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
  ViewModule as ViewModuleIcon,
  ViewQuilt as ViewQuiltIcon,
  ViewStream as ViewStreamIcon,
  GridView as GridViewIcon,
  Download as ExportIcon,
  CheckCircle as CheckCircleIcon,
  Upgrade as UpgradeIcon,
  AutoFixHigh as AutoFixHighIcon,
  Tune as TuneIcon
} from '@mui/icons-material';

// Enhanced context and hook imports
import { useSubscription } from "../../contexts/SubscriptionContext";
import { useNotification } from "../../hooks/useNotification";
import { useAccessibility } from "../../hooks/useAccessibility";

// Enhanced component imports
import ErrorBoundary from "../common/ErrorBoundary";

// ===========================
// CONSTANTS & CONFIGURATION
// ===========================

// ACE Social brand colors
const ACE_COLORS = {
  DARK: '#15110E',
  PURPLE: '#4E40C5',
  YELLOW: '#EBAE1B',
  WHITE: '#FFFFFF'
};

// Layout types with enhanced configurations
const LAYOUT_TYPES = {
  GRID: {
    id: 'grid',
    name: 'Grid Layout',
    icon: GridViewIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Organized grid layout with equal-sized widgets',
    subscriptionLimits: {
      creator: { widgets: 6, columns: 2, customization: false },
      accelerator: { widgets: 16, columns: 4, customization: true },
      dominator: { widgets: -1, columns: -1, customization: true, advanced: true }
    }
  },
  MASONRY: {
    id: 'masonry',
    name: 'Masonry Layout',
    icon: ViewQuiltIcon,
    color: ACE_COLORS.YELLOW,
    description: 'Dynamic masonry layout with variable widget sizes',
    subscriptionLimits: {
      creator: { widgets: 0, columns: 0, customization: false },
      accelerator: { widgets: 12, columns: 3, customization: true },
      dominator: { widgets: -1, columns: -1, customization: true, advanced: true }
    }
  },
  FLEXIBLE: {
    id: 'flexible',
    name: 'Flexible Layout',
    icon: ViewStreamIcon,
    color: ACE_COLORS.DARK,
    description: 'Flexible layout with responsive widget positioning',
    subscriptionLimits: {
      creator: { widgets: 0, columns: 0, customization: false },
      accelerator: { widgets: 10, columns: 3, customization: true },
      dominator: { widgets: -1, columns: -1, customization: true, advanced: true }
    }
  },
  CUSTOM: {
    id: 'custom',
    name: 'Custom Layout',
    icon: TuneIcon,
    color: ACE_COLORS.PURPLE,
    description: 'Fully customizable layout with AI-powered optimization',
    subscriptionLimits: {
      creator: { widgets: 0, columns: 0, customization: false },
      accelerator: { widgets: 0, columns: 0, customization: false },
      dominator: { widgets: -1, columns: -1, customization: true, advanced: true }
    }
  }
};



/**
 * Enhanced ProductionDashboardLayout Component - Enterprise-grade dashboard layout management
 * Features: Plan-based layout limitations, real-time layout customization, subscription-based feature gating,
 * accessibility compliance, comprehensive error handling, and ACE Social platform integration
 * with advanced layout insights and interactive dashboard orchestration
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} [props.title='Dashboard'] - Layout title
 * @param {string} [props.subtitle] - Layout subtitle
 * @param {React.Node} props.children - Layout content
 * @param {Function} [props.onRefreshAll] - Refresh all callback
 * @param {number} [props.refreshInterval=60000] - Auto-refresh interval in milliseconds
 * @param {boolean} [props.autoRefresh=true] - Enable auto-refresh
 * @param {boolean} [props.loading=false] - Loading state
 * @param {Object} [props.error=null] - Error object
 * @param {string} [props.lastUpdated] - Last updated timestamp
 * @param {boolean} [props.showHeader=true] - Show header
 * @param {boolean} [props.showRefreshButton=true] - Show refresh button
 * @param {boolean} [props.showSettingsButton=true] - Show settings button
 * @param {string} [props.maxWidth='xl'] - Maximum container width
 * @param {number} [props.spacing=3] - Grid spacing
 * @param {Function} [props.onSettingsClick] - Settings click callback
 * @param {React.Node} [props.customActions] - Custom header actions
 * @param {string} [props.layoutType='grid'] - Layout type
 * @param {string} [props.dashboardVariant='production-overview'] - Dashboard variant
 * @param {boolean} [props.enableLayoutCustomization=false] - Enable layout customization
 * @param {boolean} [props.enableExport=false] - Enable data export
 * @param {boolean} [props.realTimeUpdates=false] - Enable real-time updates
 * @param {Function} [props.onLayoutTypeChange] - Layout type change callback
 * @param {Function} [props.onExport] - Export callback
 * @param {Function} [props.onLayoutCustomize] - Layout customization callback
 * @param {Object} [props.layoutConfig] - Layout configuration
 * @param {Object} [props.customization] - Custom styling options
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.style] - Inline styles
 * @param {string} [props.testId] - Test identifier
 * @param {string} [props.ariaLabel] - Accessibility label
 * @param {string} [props.ariaDescription] - Accessibility description
 */

const ProductionDashboardLayout = memo(forwardRef(({
  title = "Dashboard",
  subtitle,
  children,
  onRefreshAll,
  autoRefresh = true,
  loading = false,
  error = null,
  lastUpdated,
  showHeader = true,
  showRefreshButton = true,
  showSettingsButton = true,
  maxWidth = "xl",
  spacing = 3,
  onSettingsClick,
  customActions,
  layoutType = 'grid',
  dashboardVariant = 'production-overview',

  enableExport = false,
  realTimeUpdates = false,
  onLayoutTypeChange,
  onExport,

  layoutConfig,
  customization = {},
  className = '',
  style = {},
  testId = 'production-dashboard-layout',
  ariaLabel,
  ariaDescription
}, ref) => {
  const theme = useTheme();

  // Enhanced context integration
  const {
    subscription,
    loading: subscriptionLoading
  } = useSubscription();

  const { showErrorNotification, showSuccessNotification } = useNotification();
  const { announceToScreenReader, setFocusToElement } = useAccessibility();

  // Enhanced media queries for different device sizes
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');

  // Enhanced state management with centralized state object
  const [state, setState] = useState({
    loading: loading || subscriptionLoading,
    isRefreshing: false,
    refreshCount: 0,
    isFullscreen: false,
    autoRefreshEnabled: autoRefresh,
    showLayoutCustomization: false,
    showExportMenu: false,
    showUpgradeDialog: false,
    showSettingsDialog: false,
    currentLayoutType: layoutType,
    lastUpdated: lastUpdated,
    animationKey: 0,
    errors: {},
    // Layout state
    layoutOptimizing: false,
    layoutCustomizations: {},
    widgetPositions: {}
  });

  // Layout data state
  const [layoutData, setLayoutData] = useState({
    raw: layoutConfig || null,
    processed: null,
    templates: [],
    optimizations: null
  });

  // Notification state
  const [notification, setNotification] = useState(null);

  // Menu anchor states
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [settingsAnchorEl, setSettingsAnchorEl] = useState(null);

  /**
   * Enhanced plan-based layout validation - Production Ready
   */
  const validateLayoutFeatures = useCallback(() => {
    if (!subscription) {
      return {
        canViewLayout: false,
        hasLayoutAvailable: false,
        remaining: 0,
        total: 0,
        used: 0,
        isUnlimited: false,
        status: 'loading',
        planLimits: { widgets: 0, columns: 0, features: [] },
        depletionInfo: { isDepletedMidCycle: false, daysRemaining: 0 }
      };
    }

    const planId = subscription?.plan_id || 'creator';

    // Plan-based layout limits
    const planLimits = {
      creator: {
        widgets: 6,
        columns: 2,
        features: ['basic_layout'],
        customization: false,
        export: false,
        analytics: false,
        realTime: false,
        layoutTypes: ['grid'],
        templates: 1
      },
      accelerator: {
        widgets: 16,
        columns: 4,
        features: ['basic_layout', 'advanced_layout', 'layout_customization'],
        customization: true,
        export: true,
        analytics: true,
        realTime: true,
        layoutTypes: ['grid', 'masonry', 'flexible'],
        templates: 5
      },
      dominator: {
        widgets: -1,
        columns: -1,
        features: ['basic_layout', 'advanced_layout', 'layout_customization', 'ai_layout_optimization'],
        customization: true,
        export: true,
        analytics: true,
        realTime: true,
        layoutTypes: ['grid', 'masonry', 'flexible', 'custom'],
        templates: -1
      }
    };

    const currentPlanLimits = planLimits[planId] || planLimits.creator;
    const isUnlimited = planId === 'dominator';

    // Calculate usage and remaining
    const currentUsage = children ? React.Children.count(children) : 0;
    const limit = currentPlanLimits.widgets;
    const remaining = isUnlimited ? Infinity : Math.max(0, limit - currentUsage);

    // Check depletion status
    const now = new Date();
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const totalCycleDays = cycleEnd.getDate();
    const daysElapsed = now.getDate();
    const daysRemaining = totalCycleDays - daysElapsed;

    const isDepletedMidCycle = remaining === 0 && daysRemaining > 3;

    return {
      canViewLayout: true,
      hasLayoutAvailable: remaining > 0 || isUnlimited,
      remaining: isUnlimited ? Infinity : remaining,
      total: isUnlimited ? Infinity : limit,
      used: currentUsage,
      isUnlimited,
      status: remaining > 0 || isUnlimited ? 'active' : 'depleted',
      planLimits: currentPlanLimits,
      depletionInfo: { isDepletedMidCycle, daysRemaining }
    };
  }, [subscription, children]);

  /**
   * Enhanced subscription integration with feature gating - Production Ready
   */
  const subscriptionFeatures = useMemo(() => {
    const planId = subscription?.plan_id || 'creator';
    const layoutLimits = validateLayoutFeatures();

    return {
      planId,
      planName: subscription?.plan_name || 'Creator',
      hasCustomization: layoutLimits.planLimits.customization,
      hasExport: layoutLimits.planLimits.export,
      hasAnalytics: layoutLimits.planLimits.analytics,
      hasRealTime: layoutLimits.planLimits.realTime,
      maxWidgets: layoutLimits.planLimits.widgets,
      maxColumns: layoutLimits.planLimits.columns,
      availableLayoutTypes: layoutLimits.planLimits.layoutTypes,
      availableFeatures: layoutLimits.planLimits.features,
      maxTemplates: layoutLimits.planLimits.templates,
      refreshInterval: layoutLimits.planLimits.realTime ? 30000 : 300000
    };
  }, [subscription?.plan_id, subscription?.plan_name, validateLayoutFeatures]);

  /**
   * Enhanced layout type validation - Production Ready
   */
  const isLayoutTypeAvailable = useCallback((type) => {
    return subscriptionFeatures.availableLayoutTypes.includes(type);
  }, [subscriptionFeatures.availableLayoutTypes]);

  /**
   * Enhanced accessibility props - Production Ready
   */
  const getAccessibilityProps = useCallback(() => {
    return {
      role: 'main',
      'aria-label': ariaLabel || `${title} dashboard layout`,
      'aria-description': ariaDescription || `Interactive ${dashboardVariant} dashboard layout with ${state.currentLayoutType} arrangement`,
      'aria-live': realTimeUpdates ? 'polite' : 'off',
      'aria-atomic': 'true',
      tabIndex: 0
    };
  }, [ariaLabel, ariaDescription, title, dashboardVariant, state.currentLayoutType, realTimeUpdates]);

  /**
   * Enhanced refresh handler - Production Ready
   */
  const handleRefreshAll = useCallback(async () => {
    if (state.isRefreshing) return;

    setState(prev => ({ ...prev, isRefreshing: true, errors: {} }));

    try {
      if (onRefreshAll) {
        await onRefreshAll();
      }

      setState(prev => ({
        ...prev,
        isRefreshing: false,
        lastUpdated: new Date().toISOString(),
        animationKey: prev.animationKey + 1,
        refreshCount: prev.refreshCount + 1
      }));

      setNotification({
        type: 'success',
        message: 'Dashboard layout refreshed successfully'
      });

      announceToScreenReader('Dashboard layout has been updated');

    } catch (error) {
      setState(prev => ({
        ...prev,
        isRefreshing: false,
        errors: { refresh: error.message }
      }));

      setNotification({
        type: 'error',
        message: `Failed to refresh dashboard: ${error.message}`
      });

      announceToScreenReader('Failed to refresh dashboard layout');
    }
  }, [state.isRefreshing, onRefreshAll, announceToScreenReader]);

  /**
   * Enhanced layout type change handler - Production Ready
   */
  const handleLayoutTypeChange = useCallback((newType) => {
    if (!isLayoutTypeAvailable(newType)) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setState(prev => ({ ...prev, currentLayoutType: newType }));

    if (onLayoutTypeChange) {
      onLayoutTypeChange(newType);
    }

    announceToScreenReader(`Layout type changed to ${newType}`);
  }, [isLayoutTypeAvailable, onLayoutTypeChange, announceToScreenReader]);

  /**
   * Enhanced export handler - Production Ready
   */
  const handleExport = useCallback(async (format = 'png') => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    try {
      if (onExport) {
        await onExport(format, layoutData.processed);
      }

      showSuccessNotification(`Dashboard layout exported as ${format.toUpperCase()}`);
      announceToScreenReader(`Dashboard layout has been exported as ${format}`);
    } catch (error) {
      showErrorNotification(`Failed to export layout: ${error.message}`);
      announceToScreenReader('Failed to export dashboard layout');
    }
  }, [subscriptionFeatures.hasExport, layoutData.processed, onExport, showSuccessNotification, showErrorNotification, announceToScreenReader]);

  /**
   * Enhanced fullscreen toggle handler - Production Ready
   */
  const handleFullscreenToggle = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setState(prev => ({ ...prev, isFullscreen: true }));
    } else {
      document.exitFullscreen();
      setState(prev => ({ ...prev, isFullscreen: false }));
    }

    announceToScreenReader(`Dashboard ${state.isFullscreen ? 'exited' : 'entered'} fullscreen mode`);
  }, [state.isFullscreen, announceToScreenReader]);

  // Imperative handle for parent component access
  useImperativeHandle(ref, () => ({
    refresh: handleRefreshAll,
    exportLayout: handleExport,
    changeLayoutType: handleLayoutTypeChange,
    toggleFullscreen: handleFullscreenToggle,
    getLayoutData: () => layoutData.processed,
    getLayoutLimits: validateLayoutFeatures,
    focus: () => setFocusToElement(document.querySelector(`[data-testid="${testId}"]`)),
    announce: (message) => announceToScreenReader(message)
  }), [
    layoutData.processed,
    validateLayoutFeatures,
    handleRefreshAll,
    handleExport,
    handleLayoutTypeChange,
    handleFullscreenToggle,
    setFocusToElement,
    announceToScreenReader,
    testId
  ]);

  /**
   * Enhanced data processing effect - Production Ready
   */
  useEffect(() => {
    if (layoutConfig) {
      setLayoutData(prev => ({
        ...prev,
        raw: layoutConfig,
        processed: layoutConfig
      }));
    }
  }, [layoutConfig]);

  /**
   * Enhanced loading state management - Production Ready
   */
  useEffect(() => {
    setState(prev => ({ ...prev, loading: loading || subscriptionLoading }));
  }, [loading, subscriptionLoading]);

  /**
   * Enhanced real-time updates - Production Ready
   */
  useEffect(() => {
    if (!realTimeUpdates || !subscriptionFeatures.hasRealTime) return;

    const interval = setInterval(() => {
      if (onRefreshAll) {
        handleRefreshAll();
      }
    }, subscriptionFeatures.refreshInterval);

    return () => clearInterval(interval);
  }, [realTimeUpdates, subscriptionFeatures.hasRealTime, subscriptionFeatures.refreshInterval, onRefreshAll, handleRefreshAll]);

  /**
   * Enhanced fullscreen change listener - Production Ready
   */
  useEffect(() => {
    const handleFullscreenChange = () => {
      setState(prev => ({ ...prev, isFullscreen: !!document.fullscreenElement }));
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  /**
   * Enhanced menu handlers - Production Ready
   */
  const handleExportMenuOpen = useCallback((event) => {
    if (!subscriptionFeatures.hasExport) {
      setState(prev => ({ ...prev, showUpgradeDialog: true }));
      return;
    }

    setExportAnchorEl(event.currentTarget);
    setState(prev => ({ ...prev, showExportMenu: true }));
  }, [subscriptionFeatures.hasExport]);

  const handleExportMenuClose = useCallback(() => {
    setExportAnchorEl(null);
    setState(prev => ({ ...prev, showExportMenu: false }));
  }, []);

  const handleUpgradeDialogClose = useCallback(() => {
    setState(prev => ({ ...prev, showUpgradeDialog: false }));
  }, []);

  /**
   * Enhanced upgrade dialog content - Production Ready
   */
  const getUpgradeDialogContent = useCallback(() => {
    const currentPlan = subscription?.plan_name || 'Creator';
    const recommendedPlan = currentPlan === 'Creator' ? 'Accelerator' : 'Dominator';

    return {
      title: 'Upgrade Required',
      content: `Advanced dashboard layout features require the ${recommendedPlan} plan or higher.`,
      features: [
        'Advanced layout types',
        'Layout customization',
        'Real-time updates',
        'Data export capabilities',
        'AI-powered optimization',
        'Custom layout templates'
      ],
      currentPlan,
      recommendedPlan
    };
  }, [subscription?.plan_name]);

  /**
   * Enhanced header content - Production Ready
   */
  const headerContent = useMemo(() => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        mb: spacing,
        p: 2,
        borderRadius: 2,
        background: `linear-gradient(135deg,
          ${alpha(ACE_COLORS.WHITE, 0.9)} 0%,
          ${alpha(ACE_COLORS.WHITE, 0.7)} 100%)`,
        backdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`,
        boxShadow: `0 4px 16px ${alpha(ACE_COLORS.DARK, 0.1)}`
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <DashboardIcon
          sx={{
            fontSize: isMobile ? 24 : 32,
            color: ACE_COLORS.PURPLE
          }}
        />
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography
              variant={isMobile ? "h5" : "h4"}
              component="h1"
              sx={{
                fontWeight: 700,
                color: ACE_COLORS.DARK,
                mb: subtitle ? 0.5 : 0
              }}
            >
              {title}
            </Typography>
            {subscriptionFeatures.hasRealTime && realTimeUpdates && (
              <Chip
                label="LIVE"
                size="small"
                sx={{
                  backgroundColor: alpha('#4CAF50', 0.1),
                  color: '#4CAF50',
                  fontWeight: 600,
                  fontSize: '0.7rem'
                }}
              />
            )}
          </Box>
          {subtitle && (
            <Typography
              variant="body1"
              color="text.secondary"
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {state.lastUpdated && (
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              display: { xs: 'none', sm: 'block' },
              mr: 1
            }}
          >
            Last updated: {new Date(state.lastUpdated).toLocaleTimeString()}
          </Typography>
        )}

        {customActions}

        {/* Layout Type Selector */}
        {subscriptionFeatures.hasCustomization && (
          <Tooltip title="Layout Settings">
            <IconButton
              size="small"
              onClick={settingsAnchorEl ? () => setSettingsAnchorEl(null) : (e) => setSettingsAnchorEl(e.currentTarget)}
              sx={{
                color: ACE_COLORS.PURPLE,
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                }
              }}
              aria-label="Change layout settings"
            >
              <ViewModuleIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}

        {/* Export Button */}
        {enableExport && (
          <Tooltip title="Export Layout">
            <IconButton
              size="small"
              onClick={handleExportMenuOpen}
              sx={{
                color: theme.palette.text.secondary,
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.2)
                }
              }}
              aria-label="Export layout data"
            >
              <ExportIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}

        {showRefreshButton && (
          <Tooltip title={`Refresh layout (${state.refreshCount} refreshes)`} arrow>
            <IconButton
              onClick={handleRefreshAll}
              disabled={state.isRefreshing}
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.1),
                color: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.2)
                }
              }}
              aria-label="Refresh dashboard layout"
            >
              <RefreshIcon
                sx={{
                  animation: state.isRefreshing ? 'spin 1s linear infinite' : 'none',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' }
                  }
                }}
              />
            </IconButton>
          </Tooltip>
        )}

        <Tooltip title={state.isFullscreen ? "Exit fullscreen" : "Enter fullscreen"} arrow>
          <IconButton
            onClick={handleFullscreenToggle}
            sx={{
              backgroundColor: alpha(theme.palette.action.hover, 0.5),
              '&:hover': {
                backgroundColor: alpha(theme.palette.action.selected, 0.8)
              }
            }}
            aria-label={state.isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
          >
            {state.isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
          </IconButton>
        </Tooltip>

        {showSettingsButton && (
          <Tooltip title="Dashboard settings" arrow>
            <IconButton
              onClick={onSettingsClick}
              sx={{
                backgroundColor: alpha(theme.palette.action.hover, 0.5),
                '&:hover': {
                  backgroundColor: alpha(theme.palette.action.selected, 0.8)
                }
              }}
              aria-label="Dashboard settings"
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    </Box>
  ), [
    title, subtitle, state.lastUpdated, customActions, showRefreshButton,
    showSettingsButton, state.isRefreshing, state.refreshCount, state.isFullscreen,
    isMobile, theme, spacing, handleRefreshAll, handleFullscreenToggle, onSettingsClick,
    subscriptionFeatures.hasCustomization, subscriptionFeatures.hasRealTime, realTimeUpdates,
    enableExport, handleExportMenuOpen, settingsAnchorEl
  ]);

  /**
   * Enhanced loading skeleton - Production Ready
   */
  const loadingSkeleton = useMemo(() => (
    <Grid container spacing={spacing}>
      {Array.from({ length: subscriptionFeatures.maxWidgets === -1 ? 8 : Math.min(8, subscriptionFeatures.maxWidgets) }).map((_, index) => (
        <Grid
          item
          xs={12}
          sm={subscriptionFeatures.maxColumns >= 2 ? 6 : 12}
          md={subscriptionFeatures.maxColumns >= 3 ? 4 : subscriptionFeatures.maxColumns >= 2 ? 6 : 12}
          lg={subscriptionFeatures.maxColumns >= 4 ? 3 : subscriptionFeatures.maxColumns >= 3 ? 4 : subscriptionFeatures.maxColumns >= 2 ? 6 : 12}
          key={index}
        >
          <Box sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Skeleton variant="circular" width={40} height={40} />
              <Box sx={{ flex: 1 }}>
                <Skeleton variant="text" width="60%" height={24} sx={{ mb: 0.5 }} />
                <Skeleton variant="text" width="40%" height={16} />
              </Box>
            </Box>
            <Skeleton
              variant="rectangular"
              height={isMobile ? 150 : 200}
              sx={{ borderRadius: 2, mb: 1 }}
            />
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'space-between' }}>
              <Skeleton variant="text" width="25%" height={16} />
              <Skeleton variant="text" width="25%" height={16} />
              <Skeleton variant="text" width="25%" height={16} />
            </Box>
          </Box>
        </Grid>
      ))}
    </Grid>
  ), [spacing, subscriptionFeatures.maxWidgets, subscriptionFeatures.maxColumns, isMobile]);

  // Main render condition checks
  if (state.loading && !children) {
    return (
      <ErrorBoundary
        fallback={
          <Container maxWidth={maxWidth} sx={{ py: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="error">
              Unable to load dashboard layout
            </Typography>
          </Container>
        }
      >
        <Container
          maxWidth={maxWidth}
          className={className}
          style={style}
          data-testid={testId}
          {...getAccessibilityProps()}
          sx={{
            py: 2,
            minHeight: '100vh',
            position: 'relative',
            ...customization
          }}
        >
          {showHeader && (
            <Fade in timeout={300}>
              {headerContent}
            </Fade>
          )}
          <Box sx={{ position: 'relative', minHeight: 400 }}>
            {loadingSkeleton}
          </Box>
        </Container>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <Container maxWidth={maxWidth} sx={{ py: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="error">
            Unable to load dashboard layout
          </Typography>
        </Container>
      }
    >
      <Container
        maxWidth={maxWidth}
        className={className}
        style={style}
        data-testid={testId}
        {...getAccessibilityProps()}
        sx={{
          py: 2,
          minHeight: '100vh',
          position: 'relative',
          ...customization
        }}
      >
        {/* Enhanced Header */}
        {showHeader && (
          <Fade in timeout={300}>
            {headerContent}
          </Fade>
        )}

        {/* Error Alert */}
        {(error || Object.keys(state.errors).length > 0) && (
          <Alert
            severity="error"
            sx={{ mb: spacing }}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={handleRefreshAll}
                disabled={state.isRefreshing}
              >
                Retry
              </Button>
            }
          >
            {error?.message || Object.values(state.errors)[0] || 'An error occurred while loading the dashboard layout'}
          </Alert>
        )}

        {/* Real-time Updates Indicator */}
        {state.autoRefreshEnabled && subscriptionFeatures.hasRealTime && !isMobile && (
          <Box
            sx={{
              position: 'fixed',
              top: 16,
              right: 16,
              zIndex: 1000,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              backgroundColor: alpha('#4CAF50', 0.9),
              color: '#FFFFFF',
              px: 2,
              py: 1,
              borderRadius: 2,
              fontSize: '0.75rem',
              fontWeight: 600,
              boxShadow: theme.shadows[4]
            }}
          >
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: '#FFFFFF',
                ...(!prefersReducedMotion && {
                  animation: 'pulse 2s infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 }
                  }
                })
              }}
              aria-hidden="true"
            />
            Auto-refresh: {Math.floor(subscriptionFeatures.refreshInterval / 1000)}s
          </Box>
        )}

        {/* Layout Optimization Indicator */}
        {state.layoutOptimizing && subscriptionFeatures.planId === 'dominator' && (
          <Box
            sx={{
              position: 'fixed',
              top: 16,
              left: 16,
              zIndex: 1000,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              backgroundColor: alpha(ACE_COLORS.PURPLE, 0.9),
              color: ACE_COLORS.WHITE,
              px: 2,
              py: 1,
              borderRadius: 2,
              fontSize: '0.75rem',
              fontWeight: 600,
              boxShadow: theme.shadows[4]
            }}
          >
            <AutoFixHighIcon sx={{ fontSize: 16 }} />
            AI Optimizing Layout...
          </Box>
        )}

        {/* Main Content */}
        <Box
          sx={{
            position: 'relative',
            minHeight: 400
          }}
        >
          {state.loading ? loadingSkeleton : (
            <Fade in timeout={500}>
              <Box>
                {children}
              </Box>
            </Fade>
          )}
        </Box>

        {/* Layout Type Menu */}
        <Menu
          anchorEl={settingsAnchorEl}
          open={Boolean(settingsAnchorEl)}
          onClose={() => setSettingsAnchorEl(null)}
          slotProps={{
            paper: {
              sx: {
                mt: 1,
                minWidth: 280,
                boxShadow: theme.shadows[8],
                border: `1px solid ${theme.palette.divider}`
              }
            }
          }}
        >
          {Object.values(LAYOUT_TYPES).map((layoutTypeConfig) => (
            <MenuItem
              key={layoutTypeConfig.id}
              onClick={() => {
                handleLayoutTypeChange(layoutTypeConfig.id);
                setSettingsAnchorEl(null);
              }}
              disabled={!isLayoutTypeAvailable(layoutTypeConfig.id)}
              sx={{
                backgroundColor: state.currentLayoutType === layoutTypeConfig.id
                  ? alpha(ACE_COLORS.PURPLE, 0.1)
                  : 'transparent'
              }}
            >
              <ListItemIcon>
                <layoutTypeConfig.icon fontSize="small" sx={{ color: layoutTypeConfig.color }} />
              </ListItemIcon>
              <ListItemText
                primary={layoutTypeConfig.name}
                secondary={layoutTypeConfig.description}
              />
              {!isLayoutTypeAvailable(layoutTypeConfig.id) && (
                <UpgradeIcon fontSize="small" sx={{ color: theme.palette.text.disabled, ml: 1 }} />
              )}
            </MenuItem>
          ))}
        </Menu>

        {/* Export Menu */}
        <Menu
          anchorEl={exportAnchorEl}
          open={state.showExportMenu}
          onClose={handleExportMenuClose}
          slotProps={{
            paper: {
              sx: {
                mt: 1,
                minWidth: 180,
                boxShadow: theme.shadows[8],
                border: `1px solid ${theme.palette.divider}`
              }
            }
          }}
        >
          <MenuItem onClick={() => {
            handleExport('png');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export as PNG</ListItemText>
          </MenuItem>

          <MenuItem onClick={() => {
            handleExport('pdf');
            handleExportMenuClose();
          }}>
            <ListItemIcon>
              <ExportIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export Report (PDF)</ListItemText>
          </MenuItem>
        </Menu>

        {/* Upgrade Dialog */}
        <Dialog
          open={state.showUpgradeDialog}
          onClose={handleUpgradeDialogClose}
          maxWidth="md"
          fullWidth
          slotProps={{
            paper: {
              sx: {
                borderRadius: 2,
                boxShadow: theme.shadows[16]
              }
            }
          }}
        >
          <DialogTitle sx={{
            backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
            borderBottom: `1px solid ${theme.palette.divider}`
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <UpgradeIcon sx={{ color: ACE_COLORS.PURPLE }} />
              <Typography variant="h6" component="span">
                {getUpgradeDialogContent().title}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent sx={{ pt: 3 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {getUpgradeDialogContent().content}
              </Typography>

              <Typography variant="h6" sx={{ mb: 2, color: ACE_COLORS.PURPLE }}>
                Unlock Advanced Layout Features:
              </Typography>

              <Grid container spacing={1}>
                {getUpgradeDialogContent().features.map((feature, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CheckCircleIcon sx={{ color: theme.palette.success.main, fontSize: '1rem' }} />
                      <Typography variant="body2">{feature}</Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Alert
              severity="info"
              sx={{
                backgroundColor: alpha(ACE_COLORS.PURPLE, 0.05),
                border: `1px solid ${alpha(ACE_COLORS.PURPLE, 0.2)}`
              }}
            >
              <AlertTitle>Current Plan: {getUpgradeDialogContent().currentPlan}</AlertTitle>
              Upgrade to {getUpgradeDialogContent().recommendedPlan} to access advanced layout features.
            </Alert>
          </DialogContent>
          <DialogActions sx={{ p: 3, pt: 2 }}>
            <Button
              onClick={handleUpgradeDialogClose}
              sx={{ color: theme.palette.text.secondary }}
            >
              Maybe Later
            </Button>
            <Button
              variant="contained"
              sx={{
                backgroundColor: ACE_COLORS.PURPLE,
                '&:hover': {
                  backgroundColor: alpha(ACE_COLORS.PURPLE, 0.8),
                }
              }}
              startIcon={<UpgradeIcon />}
            >
              Upgrade Now
            </Button>
          </DialogActions>
        </Dialog>

        {/* Notification Snackbar */}
        <Snackbar
          open={!!notification}
          autoHideDuration={4000}
          onClose={() => setNotification(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          {notification && (
            <Alert
              onClose={() => setNotification(null)}
              severity={notification.type}
              sx={{ width: '100%' }}
            >
              {notification.message}
            </Alert>
          )}
        </Snackbar>
      </Container>
    </ErrorBoundary>
  );
}));

// Enhanced PropTypes validation - Production Ready
ProductionDashboardLayout.propTypes = {
  // Core props
  title: PropTypes.string,
  subtitle: PropTypes.string,
  children: PropTypes.node.isRequired,
  onRefreshAll: PropTypes.func,
  refreshInterval: PropTypes.number,
  autoRefresh: PropTypes.bool,
  loading: PropTypes.bool,
  error: PropTypes.object,
  lastUpdated: PropTypes.string,
  showHeader: PropTypes.bool,
  showRefreshButton: PropTypes.bool,
  showSettingsButton: PropTypes.bool,
  maxWidth: PropTypes.string,
  spacing: PropTypes.number,
  onSettingsClick: PropTypes.func,
  customActions: PropTypes.node,

  // Enhanced layout props
  layoutType: PropTypes.oneOf(['grid', 'masonry', 'flexible', 'custom']),
  dashboardVariant: PropTypes.oneOf(['production-overview', 'monitoring-dashboard', 'analytics-dashboard', 'custom-dashboard']),
  enableExport: PropTypes.bool,
  realTimeUpdates: PropTypes.bool,
  onLayoutTypeChange: PropTypes.func,
  onExport: PropTypes.func,
  layoutConfig: PropTypes.object,
  customization: PropTypes.object,

  // Standard props
  className: PropTypes.string,
  style: PropTypes.object,
  testId: PropTypes.string,
  ariaLabel: PropTypes.string,
  ariaDescription: PropTypes.string
};

ProductionDashboardLayout.defaultProps = {
  title: "Dashboard",
  refreshInterval: 60000,
  autoRefresh: true,
  loading: false,
  error: null,
  showHeader: true,
  showRefreshButton: true,
  showSettingsButton: true,
  maxWidth: "xl",
  spacing: 3,
  layoutType: 'grid',
  dashboardVariant: 'production-overview',

  enableExport: false,
  realTimeUpdates: false,
  customization: {},
  className: '',
  style: {},
  testId: 'production-dashboard-layout'
};

// Display name for debugging
ProductionDashboardLayout.displayName = 'ProductionDashboardLayout';

export default ProductionDashboardLayout;

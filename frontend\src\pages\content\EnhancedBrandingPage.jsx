// @since 2024-1-1 to 2025-25-7
import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Button,
  Grid,
  Divider,
  CircularProgress,
  Snackbar,
  Alert,
  useTheme,
  alpha,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useMediaQuery
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as DuplicateIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Refresh as RefreshIcon,
  Link as LinkIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { useAdvancedToast } from '../../hooks/useAdvancedToast';
import PageHeader from '../../components/common/PageHeader';
import BrandProfileEditor from '../../components/branding/BrandProfileEditor';
import BrandProfileList from '../../components/branding/BrandProfileList';
import BrandProfileDetail from '../../components/branding/BrandProfileDetail';
import {
  getBrandProfiles,
  getBrandProfile,
  createBrandProfile,
  updateBrandProfile,
  deleteBrandProfile,
  convertToLegacyBranding,
  duplicateBrandProfile
} from '../../api/brandProfiles';
import { useBranding } from '../../hooks/useBranding';
import LoadingButton from '@mui/lab/LoadingButton';

const EnhancedBrandingPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user } = useAuth();
  const { showSuccessNotification, showErrorNotification } = useNotification();
  const { updateBrandingData } = useBranding();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [brandProfiles, setBrandProfiles] = useState([]);
  const [selectedProfileId, setSelectedProfileId] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [editorOpen, setEditorOpen] = useState(false);
  const [editingProfile, setEditingProfile] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [profileToDelete, setProfileToDelete] = useState(null);

  // Load brand profiles
  useEffect(() => {
    fetchBrandProfiles();
  }, []);

  // Fetch brand profiles from API
  const fetchBrandProfiles = async () => {
    setLoading(true);
    try {
      const profiles = await getBrandProfiles();
      // Ensure we always have an array
      const profilesArray = Array.isArray(profiles) ? profiles : [];
      setBrandProfiles(profilesArray);

      // Select the default profile or the first one
      const defaultProfile = profilesArray.find(profile => profile.is_default);
      if (defaultProfile) {
        setSelectedProfileId(defaultProfile.id);
      } else if (profilesArray.length > 0) {
        setSelectedProfileId(profilesArray[0].id);
      }
    } catch (error) {
      console.error('Error fetching brand profiles:', error);
      showErrorNotification(error.message || 'Failed to load brand profiles');
      // Ensure we have an empty array on error
      setBrandProfiles([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle profile selection
  const handleProfileSelect = (profileId) => {
    setSelectedProfileId(profileId);
    setActiveTab(0); // Reset to first tab when selecting a profile
  };

  // Open editor for creating a new profile
  const handleCreateProfile = () => {
    setEditingProfile(null);
    setEditorOpen(true);
  };

  // Open editor for editing an existing profile
  const handleEditProfile = (profile) => {
    setEditingProfile(profile);
    setEditorOpen(true);
  };

  // Handle profile save from editor
  const handleSaveProfile = async (profileData) => {
    setSaving(true);

    try {
      let savedProfile;

      if (editingProfile) {
        // Update existing profile
        savedProfile = await updateBrandProfile(editingProfile.id, profileData);
        showSuccessNotification('Brand profile updated successfully');

        // Update the profiles list
        setBrandProfiles(prevProfiles =>
          prevProfiles.map(p => p.id === editingProfile.id ? savedProfile : p)
        );
      } else {
        // Create new profile
        savedProfile = await createBrandProfile(profileData);
        showSuccessNotification('Brand profile created successfully');

        // Add to the profiles list
        setBrandProfiles(prevProfiles => [...prevProfiles, savedProfile]);
        setSelectedProfileId(savedProfile.id);
      }

      // Close the editor
      setEditorOpen(false);
      setEditingProfile(null);

      // If this is the default profile, update the global branding data
      if (savedProfile.is_default) {
        try {
          const legacyBranding = await convertToLegacyBranding(savedProfile.id);
          await updateBrandingData(legacyBranding);
        } catch (legacyError) {
          console.error('Error converting to legacy format:', legacyError);
          // Don't throw here, as the profile was already saved
          showErrorNotification('Profile saved, but failed to update global branding data');
        }
      }
    } catch (error) {
      console.error('Error saving brand profile:', error);
      showErrorNotification(error.message || 'Failed to save brand profile');
    } finally {
      setSaving(false);
    }
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (profile) => {
    setProfileToDelete(profile);
    setDeleteDialogOpen(true);
  };

  // Handle profile deletion
  const handleDeleteConfirm = async () => {
    if (!profileToDelete) return;

    try {
      await deleteBrandProfile(profileToDelete.id);

      // Remove from the profiles list
      setBrandProfiles(prevProfiles =>
        prevProfiles.filter(p => p.id !== profileToDelete.id)
      );

      // If the deleted profile was selected, select another one
      if (selectedProfileId === profileToDelete.id) {
        const remainingProfiles = brandProfiles.filter(p => p.id !== profileToDelete.id);
        if (remainingProfiles.length > 0) {
          setSelectedProfileId(remainingProfiles[0].id);
        } else {
          setSelectedProfileId(null);
        }
      }

      showSuccessNotification('Brand profile deleted successfully');
    } catch (error) {
      console.error('Error deleting brand profile:', error);
      showErrorNotification(error.message || 'Failed to delete brand profile');
    } finally {
      setDeleteDialogOpen(false);
      setProfileToDelete(null);
    }
  };

  // Handle setting a profile as default
  const handleSetDefault = async (profileId) => {
    try {
      // Update the profile to be default
      const updatedProfile = await updateBrandProfile(profileId, {
        is_default: true
      });

      // Update the profiles list
      setBrandProfiles(prevProfiles =>
        prevProfiles.map(p => ({
          ...p,
          is_default: p.id === profileId
        }))
      );

      // Update the global branding data
      const legacyBranding = await convertToLegacyBranding(profileId);
      await updateBrandingData(legacyBranding);

      showSuccessNotification('Default brand profile updated successfully');
    } catch (error) {
      console.error('Error setting default brand profile:', error);
      showErrorNotification(error.message || 'Failed to set default brand profile');
    }
  };

  // Handle duplicating a profile
  const handleDuplicateProfile = async (profileId) => {
    try {
      // Duplicate the profile using the hook
      const duplicatedProfile = await duplicateBrandProfile(profileId);

      // Add to the profiles list
      setBrandProfiles(prevProfiles => [...prevProfiles, duplicatedProfile]);

      // Select the new profile
      setSelectedProfileId(duplicatedProfile.id);
    } catch (error) {
      console.error('Error duplicating brand profile:', error);
      showErrorNotification(error.message || 'Failed to duplicate brand profile');
    }
  };

  // Get the selected profile
  const profilesArray = Array.isArray(brandProfiles) ? brandProfiles : [];
  const selectedProfile = profilesArray.find(p => p.id === selectedProfileId);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <PageHeader
        title="Brand Management"
        subtitle="Create and manage your brand profiles"
        icon="branding"
      />

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {/* Left sidebar - Brand profiles list */}
          <Grid item xs={12} md={3}>
            <BrandProfileList
              profiles={brandProfiles}
              selectedProfileId={selectedProfileId}
              onSelect={handleProfileSelect}
              onCreateNew={handleCreateProfile}
              onEdit={handleEditProfile}
              onDelete={handleDeleteClick}
              onSetDefault={handleSetDefault}
            />
          </Grid>

          {/* Main content - Selected brand profile */}
          <Grid item xs={12} md={9}>
            {selectedProfile ? (
              <BrandProfileDetail
                profile={selectedProfile}
                onEdit={() => handleEditProfile(selectedProfile)}
                onDelete={() => handleDeleteClick(selectedProfile)}
                onSetDefault={() => handleSetDefault(selectedProfile.id)}
                onDuplicate={() => handleDuplicateProfile(selectedProfile.id)}
                activeTab={activeTab}
                onTabChange={handleTabChange}
              />
            ) : (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  No brand profile selected
                </Typography>
                <Typography variant="body1" color="textSecondary" paragraph>
                  Select a brand profile from the list or create a new one to get started.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleCreateProfile}
                >
                  Create Brand Profile
                </Button>
              </Paper>
            )}
          </Grid>
        </Grid>
      )}

      {/* Brand Profile Editor Dialog */}
      <Dialog
        open={editorOpen}
        onClose={() => setEditorOpen(false)}
        fullScreen={isMobile}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          {editingProfile ? 'Edit Brand Profile' : 'Create Brand Profile'}
        </DialogTitle>
        <DialogContent dividers>
          <BrandProfileEditor
            profile={editingProfile}
            onSave={handleSaveProfile}
            saving={saving}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditorOpen(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Brand Profile</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the brand profile "{profileToDelete?.name}"?
            This action cannot be undone.
          </Typography>
          {profileToDelete?.is_default && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              This is your default brand profile. Deleting it will set another profile as default.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default EnhancedBrandingPage;

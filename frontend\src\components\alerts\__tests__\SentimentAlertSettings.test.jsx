/**
 * Tests for SentimentAlertSettings component
 */
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { expect, describe, test, beforeEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import SentimentAlertSettings from '../SentimentAlertSettings';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    {children}
  </ThemeProvider>
);

// Mock the API
vi.mock('../../api', () => ({
  default: {
    get: vi.fn(),
    put: vi.fn()
  }
}));

// Mock the custom components
vi.mock('../common', () => ({
  CustomCard: ({ children, ...props }) => <div {...props}>{children}</div>,
  CustomCardHeader: ({ title, action }) => (
    <div>
      <h2>{title}</h2>
      {action}
    </div>
  ),
  CustomCardContent: ({ children }) => <div>{children}</div>
}));

describe('SentimentAlertSettings', () => {
  const mockApi = require('../../api').default;
  const mockOnSettingsChange = vi.fn();

  const mockSettings = {
    enabled: true,
    negative_threshold: -0.5,
    change_threshold: 0.3,
    notify_email: true,
    notify_app: true
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApi.get.mockResolvedValue({ data: mockSettings });
    mockApi.put.mockResolvedValue({ data: mockSettings });
  });

  test('renders sentiment alert settings', async () => {
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    expect(screen.getByText('Sentiment Alert Settings')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByLabelText('Enable Sentiment Alerts')).toBeInTheDocument();
      expect(screen.getByText('Negative Sentiment Threshold')).toBeInTheDocument();
      expect(screen.getByText('Sentiment Change Threshold')).toBeInTheDocument();
      expect(screen.getByText('Notification Preferences')).toBeInTheDocument();
    });
  });

  test('loads and displays settings correctly', async () => {
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Enable sentiment alerts')).toBeChecked();
      expect(screen.getByLabelText('Enable email notifications for sentiment alerts')).toBeChecked();
      expect(screen.getByLabelText('Enable in-app notifications for sentiment alerts')).toBeChecked();
    });
  });

  test('shows loading state initially', () => {
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  test('handles settings changes', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertSettings onSettingsChange={mockOnSettingsChange} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Enable sentiment alerts')).toBeInTheDocument();
    });

    // Toggle the main switch
    await user.click(screen.getByLabelText('Enable sentiment alerts'));

    expect(mockOnSettingsChange).toHaveBeenCalledWith({
      ...mockSettings,
      enabled: false
    });
  });

  test('handles slider changes', async () => {
    render(
      <TestWrapper>
        <SentimentAlertSettings onSettingsChange={mockOnSettingsChange} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Negative Sentiment Threshold')).toBeInTheDocument();
    });

    // Find the negative threshold slider
    const sliders = screen.getAllByRole('slider');
    const negativeThresholdSlider = sliders[0]; // First slider should be negative threshold

    // Simulate slider change
    fireEvent.change(negativeThresholdSlider, { target: { value: -0.7 } });

    expect(mockOnSettingsChange).toHaveBeenCalledWith({
      ...mockSettings,
      negative_threshold: -0.7
    });
  });

  test('disables notification options when alerts are disabled', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Enable sentiment alerts')).toBeInTheDocument();
    });

    // Disable alerts
    await user.click(screen.getByLabelText('Enable sentiment alerts'));

    await waitFor(() => {
      expect(screen.getByLabelText('Enable email notifications for sentiment alerts')).toBeDisabled();
      expect(screen.getByLabelText('Enable in-app notifications for sentiment alerts')).toBeDisabled();
    });
  });

  test('handles save functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Enable sentiment alerts')).toBeInTheDocument();
    });

    // Make a change to enable save button
    await user.click(screen.getByLabelText('Enable sentiment alerts'));

    // Save button should be enabled
    const saveButton = screen.getByLabelText('Save sentiment alert settings');
    expect(saveButton).not.toBeDisabled();

    await user.click(saveButton);

    await waitFor(() => {
      expect(mockApi.put).toHaveBeenCalledWith('/api/sentiment-alerts/settings', {
        ...mockSettings,
        enabled: false
      });
    });

    // Should show success message
    await waitFor(() => {
      expect(screen.getByText('Settings saved successfully!')).toBeInTheDocument();
    });
  });

  test('save button is disabled when no changes', async () => {
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Save sentiment alert settings')).toBeDisabled();
    });
  });

  test('handles refresh functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/sentiment-alerts/settings');
    });

    // Clear mock calls
    vi.clearAllMocks();

    // Click refresh button
    const refreshButton = screen.getByLabelText('Refresh sentiment alert settings');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/sentiment-alerts/settings');
    });
  });

  test('handles reset functionality', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Enable sentiment alerts')).toBeInTheDocument();
    });

    // Make a change
    await user.click(screen.getByLabelText('Enable sentiment alerts'));

    // Reset button should be enabled
    const resetButton = screen.getByLabelText('Reset settings to last saved state');
    expect(resetButton).not.toBeDisabled();

    // Clear mock calls to track reset API call
    vi.clearAllMocks();

    await user.click(resetButton);

    await waitFor(() => {
      expect(mockApi.get).toHaveBeenCalledWith('/api/sentiment-alerts/settings');
    });
  });

  test('shows error state when API fails', async () => {
    mockApi.get.mockRejectedValue(new Error('API Error'));

    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load settings. Please try again.')).toBeInTheDocument();
    });
  });

  test('handles save error gracefully', async () => {
    const user = userEvent.setup();
    mockApi.put.mockRejectedValue(new Error('Save failed'));
    
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Enable sentiment alerts')).toBeInTheDocument();
    });

    // Make a change
    await user.click(screen.getByLabelText('Enable sentiment alerts'));

    // Save
    await user.click(screen.getByLabelText('Save sentiment alert settings'));

    await waitFor(() => {
      expect(screen.getByText('Failed to save settings. Please try again.')).toBeInTheDocument();
    });
  });

  test('shows loading state during save', async () => {
    const user = userEvent.setup();
    // Make save take some time
    mockApi.put.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Enable sentiment alerts')).toBeInTheDocument();
    });

    // Make a change
    await user.click(screen.getByLabelText('Enable sentiment alerts'));

    // Save
    await user.click(screen.getByLabelText('Save sentiment alert settings'));

    // Should show saving state
    expect(screen.getByText('Saving...')).toBeInTheDocument();
  });

  test('displays threshold values correctly', async () => {
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Current: -0.5')).toBeInTheDocument();
      expect(screen.getByText('Current: 0.3')).toBeInTheDocument();
    });
  });

  test('calls onSettingsChange prop when provided', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <SentimentAlertSettings onSettingsChange={mockOnSettingsChange} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockOnSettingsChange).toHaveBeenCalledWith(mockSettings);
    });

    // Make a change
    await user.click(screen.getByLabelText('Enable email notifications for sentiment alerts'));

    expect(mockOnSettingsChange).toHaveBeenCalledWith({
      ...mockSettings,
      notify_email: false
    });
  });

  test('handles missing settings data gracefully', async () => {
    mockApi.get.mockResolvedValue({ data: null });

    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    // Should not crash and should show default settings
    await waitFor(() => {
      expect(screen.getByText('Sentiment Alert Settings')).toBeInTheDocument();
    });
  });

  test('success message disappears after timeout', async () => {
    const user = userEvent.setup();
    vi.useFakeTimers();
    
    render(
      <TestWrapper>
        <SentimentAlertSettings />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByLabelText('Enable sentiment alerts')).toBeInTheDocument();
    });

    // Make a change and save
    await user.click(screen.getByLabelText('Enable sentiment alerts'));
    await user.click(screen.getByLabelText('Save sentiment alert settings'));

    await waitFor(() => {
      expect(screen.getByText('Settings saved successfully!')).toBeInTheDocument();
    });

    // Fast forward time
    vi.advanceTimersByTime(3000);

    await waitFor(() => {
      expect(screen.queryByText('Settings saved successfully!')).not.toBeInTheDocument();
    });

    vi.useRealTimers();
  });
});
